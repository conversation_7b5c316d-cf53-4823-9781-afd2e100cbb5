/**
 * @file jrsaoiImgsmanager.h AOI图像管理器
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2024-08-15
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#ifndef JRSAOIIMGSMANAGER_H
#define JRSAOIIMGSMANAGER_H

#pragma once
#include <vector>
#include <map>

#pragma warning(push,3)
#pragma warning(disable: 4127)
#include <opencv2/opencv.hpp>
#pragma warning(pop)

#include "image.hpp"
#include "stictchimages.h"
using std::vector;
using std::map;
using cv ::Mat;
using cv ::Point2f;

namespace jrslogic
{

    class JrsAoiImgsManager
    {
    public:

        /**
         * @brief 构造函数
         * 
         * @param _resolution 镜头分辨率
         * @param _fov_w      fov宽度
         * @param _fov_h      fov高度
         * @param _x_axis_neg 机械轴x方向是否与图像相反
         * @param _y_axis_neg 机械轴y方向是否与图像相反
         */
        JrsAoiImgsManager(const float& _resolution, const int& _fov_w, const int& _fov_h, const bool& _x_axis_neg, const bool& _y_axis_neg);
        ~JrsAoiImgsManager();

    private:
        int board_w = 0;                     /**< 整版图像宽度*/
        int board_h = 0;                     /**< 整版图像高度*/
        StictchImagesTool stitch_tool;       /**< 图像合并工具*/
        vector<jrsdata::OneFovImgs> fov_imgs;         /**< 所有fov图像信息*/
        map<jrsdata::LightImageType, Mat> board_imgs;        /**< 整版图像信息*/
        map<jrsdata::LightImageType, Mat> board_mask;        /**< 整版图像掩码信息*/

    public: 
        /**
         * @brief 清除所有fov图像信息
         * 
         * @return int 错误码
         */
        int ClearFovImgs();
        int ClearBoardImgs();
        
        /**
         * @brief 设置系统参数
         * 
         * @param _resolution 镜头分辨率
         * @param _fov_w      fov宽度
         * @param _fov_h      fov高度
         * @param _x_axis_neg 机械轴x方向是否与图像相反
         * @param _y_axis_neg 机械轴y方向是否与图像相反
         * @return int        错误码
         */
        int SetSystemParam(float& _resolution, const int& _fov_w, const int& _fov_h, const bool& _x_axis_neg, const bool& _y_axis_neg);
        
        /**
         * @brief 设置所有Fov位置信息，用来计算大图的宽高
         * 
         * @param pos 所有fov位置信息
         * @return int 错误码
         */
        int SetAllFovPos(const vector<Point2f>& pos);

        /**
         * @brief 添加fov数据
         * 
         * @param img_info 单个fov图像信息
         * @param stitch_flag 是否往大图上拼接
         * @return int 错误码
         */
        int AddFovImg(const jrsdata::OneFovImgs& img_info, const bool& stitch_flag = true);  
        
        /**
         * @brief 拼接所有fov图像
         * 
         * @return int 错误码
         */
        int StichFovImgs();

        /**
         * @brief 获取所有fov图像信息
         * 
         * @return const vector<OneFovImgs>* 所有fov图像信息
         */
        const vector<jrsdata::OneFovImgs>* GetFovImgs();

        /**
         * @brief 获取整版图像信息
         * 
         * @return const map<LightImageType, Mat>* 所有类型的整版图
         */
        const map<jrsdata::LightImageType, Mat>*  GetBoardImgs();

        /**
         * @brief 获取特定类型的整版图
         * 
         * @param light_type 图像类型
         * @return const Mat* 整版图像
         */
        const Mat* GetBoardImg(const jrsdata::LightImageType& light_type);
    };
}

#endif