<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EditView</class>
 <widget class="QWidget" name="EditView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>776</width>
    <height>179</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>EditView</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Ignored" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabWidget::pane {border:0px solid;}
</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="component_rect_info">
      <attribute name="title">
       <string>元件框信息</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <property name="leftMargin">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>3</number>
       </property>
       <property name="rightMargin">
        <number>3</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>3</number>
       </property>
       <item row="1" column="1">
        <widget class="QDoubleSpinBox" name="body_width">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_4">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>角度:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="label_5">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>Y:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="4">
        <widget class="QDoubleSpinBox" name="body_height">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QCheckBox" name="component_is_detect">
         <property name="text">
          <string>不测</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_19">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>宽:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_21">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>状态:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QDoubleSpinBox" name="body_x">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="label_20">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>高:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="2">
        <widget class="QPushButton" name="body_add_90_degrees">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>+90</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="label_2">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="layoutDirection">
          <enum>Qt::LeftToRight</enum>
         </property>
         <property name="text">
          <string>X:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QDoubleSpinBox" name="body_y">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5" rowspan="5">
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="body_angle_value"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="pad_rect_info">
      <attribute name="title">
       <string>焊盘框信息</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <property name="leftMargin">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>3</number>
       </property>
       <property name="rightMargin">
        <number>3</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>3</number>
       </property>
       <item row="0" column="0">
        <widget class="QLabel" name="label_6">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="layoutDirection">
          <enum>Qt::LeftToRight</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="text">
          <string>添加:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QComboBox" name="combo_add_pad_type"/>
       </item>
       <item row="0" column="2">
        <widget class="QPushButton" name="pb_add_pad_manual">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>添加</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_7">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>方向:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="1" colspan="3">
        <widget class="QWidget" name="widget" native="true">
         <layout class="QGridLayout" name="gridLayout_5">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="horizontalSpacing">
           <number>3</number>
          </property>
          <property name="verticalSpacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="QRadioButton" name="radio_btn_pad_direct_up">
            <property name="text">
             <string>上</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QRadioButton" name="radio_btn_pad_direct_down">
            <property name="text">
             <string>下</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QRadioButton" name="radio_btn_pad_direct_left">
            <property name="text">
             <string>左</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QRadioButton" name="radio_btn_pad_direct_right">
            <property name="text">
             <string>右</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QRadioButton" name="radio_btn_pad_direct_center">
            <property name="text">
             <string>中</string>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QRadioButton" name="radio_btn_pad_direct_none">
            <property name="text">
             <string>无</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_25">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>X:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="pad_x">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="label_26">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>Y:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QDoubleSpinBox" name="pad_y">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_8">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>宽:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QDoubleSpinBox" name="pad_width">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="QLabel" name="label_23">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>高:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="3">
        <widget class="QDoubleSpinBox" name="pad_height">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_28">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>列:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QDoubleSpinBox" name="pad_col">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="QLabel" name="label_29">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>行:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="4" column="3">
        <widget class="QDoubleSpinBox" name="pad_row">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="prefix">
          <string/>
         </property>
         <property name="maximum">
          <double>100000000000000000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_30">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>状态:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QCheckBox" name="pad_is_detect">
         <property name="text">
          <string>不测</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0" colspan="4">
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QGridLayout" name="gridLayout_6">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="horizontalSpacing">
           <number>3</number>
          </property>
          <property name="verticalSpacing">
           <number>0</number>
          </property>
          <item row="0" column="1">
           <widget class="QPushButton" name="btn_pad_alter">
            <property name="text">
             <string>pad 修改</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_32">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>23</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>23</height>
             </size>
            </property>
            <property name="text">
             <string>*备注：焊盘坐标是本体中心的相对坐标</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="detect_rect_info">
      <attribute name="title">
       <string>检测框信息</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <property name="leftMargin">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>3</number>
       </property>
       <property name="rightMargin">
        <number>3</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>3</number>
       </property>
       <item row="0" column="1">
        <widget class="QDoubleSpinBox" name="detect_window_x">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QToolButton" name="equal_btn_x">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>=</string>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QToolButton" name="equal_btn_w">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>=</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="label_37">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>高:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="label_34">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>Y:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="5">
        <widget class="QToolButton" name="equal_btn_h">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>=</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="label_33">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="layoutDirection">
          <enum>Qt::LeftToRight</enum>
         </property>
         <property name="text">
          <string>X:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QDoubleSpinBox" name="detect_window_width">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QToolButton" name="equal_btn_y">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>=</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_36">
         <property name="minimumSize">
          <size>
           <width>45</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>45</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>宽:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QDoubleSpinBox" name="detect_window_y">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="1" column="4">
        <widget class="QDoubleSpinBox" name="detect_window_height">
         <property name="minimumSize">
          <size>
           <width>95</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>95</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="suffix">
          <string>pix</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0" colspan="3">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>3</number>
         </property>
         <item>
          <widget class="QLabel" name="label_38">
           <property name="minimumSize">
            <size>
             <width>45</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>45</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>角度:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDoubleSpinBox" name="detect_window_angle_value"/>
         </item>
         <item>
          <widget class="QPushButton" name="detect_window_add_90_degrees">
           <property name="minimumSize">
            <size>
             <width>30</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>30</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>+90</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="3" column="0" colspan="2">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_39">
           <property name="minimumSize">
            <size>
             <width>45</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>45</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>状态:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="detect_window_is_detect">
           <property name="text">
            <string>不测</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="4" column="1">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="5" column="0" colspan="5">
        <widget class="QLabel" name="label_40">
         <property name="text">
          <string>*备注：检测框坐标是所属中心组的相对坐标。</string>
         </property>
        </widget>
       </item>
       <item row="0" column="6" rowspan="5">
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
