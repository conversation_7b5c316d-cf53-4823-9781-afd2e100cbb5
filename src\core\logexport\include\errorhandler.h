/*****************************************************************//**
 * @file   errorhandler.h
 * @brief  错误处理工具类
 * @details 用于定义错误工具接口类   
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>YYZhang      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __ERRORHANDLER_H__
#define __ERRORHANDLER_H__

//STD
#include <string>
#include <memory>
#include <queue>
#include <stack>
#include <map>
#include <functional>
#include <mutex>

//Custom
#include "pluginexport.hpp"

namespace jrscore
{

    /**
         * @brief 核心库错误码/通用错误码
         * @detail
         *    规格约束
         *     一 错误码名称：<错误标识>_<软件名称>_<模块名称>_<错误类型描述>_<错误类型描述>_<错误类型描述>
         *            1.1 错误标识通常为固定标识 E
         *            1.2 软件名称通常为固定标识 AOI
         *            1.3 模块名称与错误码描述文件一一对应，如CORE库模块名称为 CORE
         *            1.4 错误类型描述，此处较宽泛，但最多不建议超过长度3
         *               可为形容词（UNKNOWN）、动词+名词+形容词（LOAD_FILE_FAILED）等形式
         *     二 错误码位排布
         *        考虑Int对应最大16进制数为 0x7FFFFFFF  自16进制高地址位起，取1~2位代表软件标识, 即对于JRSAOI，视为 10，
         *                                                                  取3~4位代表模块标识, 则对于核心库模块    ，视为 01,
         *                                                                  第5位暂为保留位,默认为0
         *                                                                  取6~8位代表错误码，  则对于核心库未知错误，视为001，
         *
         *      一般核心库错误码可视为通用错误码
         *
         */
    using AOIErrorCode = int;
    constexpr int AOI_OK = 0;
    /*** @brief 错误描述信息 YYZhang 2024.1.23*/
    struct  ErrorInfo
    {
        //AOIErrorCode error_code;      /**< 错误码         */
        std::string err_string;         /**< 错误码字符信息 */
        std::string err_description;    /**< 错误码描述信息 */
        std::string module_name;        /**< 模块名称 */
        std::string what;               /**< 调用端详细描述 */

    };
    /** 定义错误码宏 */
    #define DEFINE_ERROR_CODE(name, value) \
    static const int name = value; 


    /*** @brief  核心库模块错误码，模块标识为:01 YYZhang 2024.1.23*/
    class J3DError
    {

        public :
     
            DEFINE_ERROR_CODE(E_3D_ALOG_UNKNOWN,0x11010001);/**< 未知错误  */
            
            static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& Get3DErrorMap()
            {
                return error_3D_map;
            }

        private:
       
            static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_3D_map =
            {
                {E_3D_ALOG_UNKNOWN, {"E_3D_ALOG_UNKNOWN","未知错误","J3D",""}},
        
            };

            static_assert( 
                (static_cast<int>( E_3D_ALOG_UNKNOWN ) - static_cast<int>(E_3D_ALOG_UNKNOWN)) == 0,
                "J3D ErrorCount not equal !"
            );

    };

    


    struct ErrorImplData;


    class JRS_AOI_PLUGIN_API ErrorHandler
    {
    public :
        ErrorHandler ();
        ~ErrorHandler ();

        /*** @brief 错误输出模式 YYZhang 2024.1.23*/
        enum FlushStrategy
        {
            FLUSHNONE = 0x0000, /**< 无处理 */
            FLUSHTOMESSAGEBOX = 0x0001, /**< 弹窗显示 */
            FLUSHTOERRORVIEW = 0x0002, /**< 输出到错误显示界面 */
            FLUSHNORMAL = FLUSHTOMESSAGEBOX | FLUSHTOERRORVIEW
        };

        /**
         * @fun PushStackError 
         * @brief 错误信息推送到错误收集器中
         * @param err 错误码
         * @param what 对于错误的详细描述
         * @date 2024.1.23
         * <AUTHOR>
         */
        void PushStackError (const AOIErrorCode err, const std::string& what);

        /**
         * @fun FlushStackError 
         * @brief 
         * @param straegy
         * @date 2024.1.23
         * <AUTHOR>
         */
        void FlushStackError (FlushStrategy straegy = FLUSHNORMAL);

        /*** 生成错误，并触发回调 YYZhang 2024.1.23*/
        using ErrorHandleGenerateCallBack = std::function<void (const ErrorInfo info)>;

        /*** @brief 刷新处理缓存区域所有错误 YYZhang 2024.1.23*/
        using ErrorHandleFlushCallBack = std::function<void (FlushStrategy flush_type_)>;

        /**
         * @fun SetErrorGenerateCallBack 
         * @brief 设置错误处理回调函数
         * @param cb 回调函数
         * @date 2024.1.23
         * <AUTHOR>
         */
        void SetErrorGenerateCallBack (ErrorHandleGenerateCallBack cb_);
   
        /**
         * @fun SetErrorFlushCallBack 
         * @brief 设置错误处理回调函数
         * @param cb_ 
         * @date 2024.1.23
         * <AUTHOR>
         */
        void SetErrorFlushCallBack (ErrorHandleFlushCallBack cb_);
    private:
        ErrorImplData* p_data;

    };
    using ErrorHandlerPtr = std::shared_ptr<ErrorHandler>;

}
#endif // !__ERRORHANDLER_H__
