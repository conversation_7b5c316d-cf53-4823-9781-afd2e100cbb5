/*********************************************************************
 * @brief  测试渲染.
 *
 * @file   renderfore.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include "renderabstract.hpp"   // RenderAbstractPtr
#include "graphicsabstract.hpp" // GraphicsPtrVec

class RenderTest : public RenderAbstract
{
public:
    RenderTest();
    ~RenderTest();

    void Render() override;
    void Destroy() override;

private:
    GraphicsPtrVec ghs;
    GraphicsPtrVec ghs_text;
};
