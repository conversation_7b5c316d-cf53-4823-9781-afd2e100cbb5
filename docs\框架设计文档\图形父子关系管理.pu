@startuml 图形父子关系管理

actor User as "用户"
participant ParentShape as "父图形"
participant ChildShape as "子图形"

== 添加子图形 ==
User -> ParentShape : 添加子图形(子图形实例)
ParentShape -> ParentShape : 将子图形添加到子图形容器
== 移除子图形 ==
User -> ParentShape : 删除子图形(子图形实例)
ParentShape -> ParentShape : 从子图形容器移除
ParentShape -> ChildShape : 通知移除父图形
ChildShape -> ChildShape : 移除父图形弱指针
== 添加父图形 ==
User -> ChildShape : 添加父图形(父图形实例)
ChildShape -> ChildShape : 如果已存在父图形,移除父图形
ChildShape -> ChildShape : 设置父图形弱指针
ChildShape -> ChildShape : 将自身坐标系转换到父图形坐标系
ChildShape -> ParentShape : 通知添加自身为子图形
== 移除父图形 ==
User -> ChildShape : 移除父图形
ChildShape -> ChildShape : 将自身坐标系转换到全局坐标系
ChildShape -> ChildShape : 移除父图形弱指针

@enduml