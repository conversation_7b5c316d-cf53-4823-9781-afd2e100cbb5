//QT 
#include <QVariant>
#include "onlinedebugmodel.h"
namespace jrsaoi
{
    OnLineDebugModel::OnLineDebugModel(const std::string& name)
        : ModelBase(name)
    {
    }
    OnLineDebugModel::~OnLineDebugModel()
    {
    }
    int OnLineDebugModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("指针转换失败");
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        else if (param_->event_name == jrsaoi::DETECT_RESULT_ONLINE_DEBUG_INFO_EVENT_NAME)
        {

            if (is_waitting_debug_info.load())
            {
                auto online_debug_info = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
                project_param_instance.GetProjectDataProcessInstance()->UpdateProjectBoardInfo(online_debug_info->online_debug_param->work_flow_board_info);
                is_waitting_debug_info.store(false);
            }
        }
        
        else if (param_->event_name == jrsaoi::ONLINEDEBUG_DEBUG_FINISHED_SEND_EVNET_NAME)
        {
            auto online_debug_info = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
            is_waitting_debug_info.store(online_debug_info->is_waitting_debug_info);
        }
        
        return jrscore::AOI_OK;
        
    }
    int OnLineDebugModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        
        (void)param_;
        return jrscore::AOI_OK;
    }
}