/*****************************************************************//**
 * @file   logshowview.h
 * @brief  日志显示界面类
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __LOGSHOWVIEW_H__
#define __LOGSHOWVIEW_H__
 //STD
//Custom
#include "viewbase.h" 
//Third
QT_FORWARD_DECLARE_CLASS(QPlainTextEdit)

namespace jrsaoi
{
    constexpr int MAX_LINE = 1000;
    class LogShowView :public ViewBase
    {
    public:
        LogShowView(const std::string& name, QWidget* parent = nullptr);
        ~LogShowView();
        virtual int Init() override;
        virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_)override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
    private:
        void InitView();
        void AddMessage(const std::string& msg_);

        QPlainTextEdit* show_log_message_edit;


    };
}
#endif // !__LOGSHOWVIEW_H__
