/*********************************************************************
 * @brief  父子关系管理类.
 *
 * @file   hierarchicalobject.hpp
 *
 * @date   2024.09.30
 * <AUTHOR>
 *********************************************************************/
#include <memory>
#include <vector>

class HierarchicalObject : public std::enable_shared_from_this<HierarchicalObject>
{
public:
    // 浅拷贝
    HierarchicalObject& operator=(const HierarchicalObject& other)
    {
        if (this != &other)
        {
            parent_ = other.parent_;
            children_ = other.children_;
        }
        return *this;
    }

    virtual ~HierarchicalObject() = default;

    // 添加子节点
    virtual void AddChild(std::shared_ptr<HierarchicalObject> child) {
        // 检查子节点是否重复
        if (IsChildExists(child)) {
            return;
        }

        // 检查是否是父节点或子节点的子节点
        if (IsDescendant(child)) {
            return;
        }

        children_.emplace_back(child);
        child->parent_ = shared_from_this();  // 更新子对象的父节点
    }

    // 删除子节点
    virtual void RemoveChild(std::shared_ptr<HierarchicalObject> child) {
        child->parent_.reset();
        children_.erase(std::remove_if(children_.begin(), children_.end(),
            [&](const std::weak_ptr<HierarchicalObject>& weakChild) {
                return weakChild.lock() == child;
            }), children_.end());
    }

    // 获取子节点
    virtual const std::vector<std::weak_ptr<HierarchicalObject>>& GetChildren() const {
        return children_;
    }

    // 添加父节点
    virtual void AddParent(std::shared_ptr<HierarchicalObject> parent) {
        // 检查是否是自身
        if (shared_from_this() == parent) {
            return;
        }

        // 检查是否是子节点或子节点的子节点
        if (IsDescendant(parent)) {
            return;
        }

        parent_ = parent;
    }

    virtual void RemoveParent() {
        if (parent_.expired())
            return;
        parent_.lock()->RemoveChild(shared_from_this());
        parent_.reset();
    }

    virtual const std::weak_ptr<HierarchicalObject>& GetParent() const {
        return parent_;
    }

    virtual std::shared_ptr<HierarchicalObject> Clone() const = 0;

    virtual std::vector<std::weak_ptr<HierarchicalObject>> CloneChildren(std::shared_ptr<HierarchicalObject> object) const {
        std::vector<std::weak_ptr<HierarchicalObject>> clonedChildren;
        for (const auto& child : object->children_) {
            auto child_clone = child.lock()->Clone();
            child_clone->AddParent(object);
            clonedChildren.push_back(child_clone);
        }
        return clonedChildren;
    }

protected:
    std::weak_ptr<HierarchicalObject> parent_; // 父节点
    std::vector<std::weak_ptr<HierarchicalObject>> children_; // 子节点

private:
    // 检查子节点是否已存在
    bool IsChildExists(const std::shared_ptr<HierarchicalObject>& child) const {
        for (const auto& weakChild : children_) {
            if (weakChild.lock() == child) {
                return true;
            }
        }
        return false;
    }

    // 检查是否为后代
    bool IsDescendant(std::shared_ptr<HierarchicalObject> node) const {
        // 遍历子节点，检查后代
        for (const auto& weakChild : children_) {
            auto child = weakChild.lock();
            if (child == node || (child && child->IsDescendant(node))) {
                return true;
            }
        }
        return false;
    }
};
