﻿//CUSTOM
#include "barcodedevicehuarui.h"
//STL
#include <iostream>
namespace jrsdevice
{


    BarcodeDeviceHuaRui* BarcodeDeviceHuaRui::instance = nullptr;

    BarcodeDeviceHuaRui::BarcodeDeviceHuaRui() :
        BarcodeDevice(), m_eid_camera(nullptr), m_barcode_callback(nullptr), m_save_image(false), file_path(""), m_pre_barcode("")
    {
        instance = this; // 设置静态实例指针
    }

    BarcodeDeviceHuaRui::~BarcodeDeviceHuaRui()
    {
    }

    void BarcodeDeviceHuaRui::SetBarcodeCallback(BarcodeCallback callback)
    {
        m_barcode_callback = std::move(callback);
    }

    bool BarcodeDeviceHuaRui::StartGrabing()
    {
        m_pre_barcode = "";
        int ret = StartGrab(m_eid_camera);
        if (ret == 0)
        {
            return true;
        }
        return false;
    }

    bool BarcodeDeviceHuaRui::StopGrabing()
    {
        int ret = StopGrab(m_eid_camera);
        if (ret == 0)
        {
            return true;
        }
        return false;
    }

    bool BarcodeDeviceHuaRui::IsOpenDevice()
    {
        if (m_eid_camera == 0)
        {
            return false;
        }
        return eidIsDeviceOpen(m_eid_camera);
    }

    bool BarcodeDeviceHuaRui::IsGrabing()
    {
        if (m_eid_camera == 0)
        {
            return false;
        }
        return eidIsGrabbing(m_eid_camera);
    }

    void BarcodeDeviceHuaRui::ParsingQRCode(const EidFrameInfo* info)
    {
        std::lock_guard<std::mutex> lock(m_wait_mutex);
        if (int(info->readState) == EidReadState::eidReadStateComplete || int(info->readState) == EidReadState::eidReadStatePartial ||
            int(info->readState) == EidReadState::eidReadStatePhaseComplete || int(info->readState) == EidReadState::eidReadStatePhasePartial)
        {
            if (info->codeNum > 0)
            {
                const EidCodeInfo* c = info->codeList;
                std::string code = c->data;
                if (m_pre_barcode.compare(code) != 0)
                {
                    if (m_barcode_callback)// 调用回调函数
                    {
                        m_barcode_callback(code);
                    }
                }
                m_pre_barcode = code;
            }
        }
        if (m_save_image)
        {
            std::string image_file_name = file_path;
            image_file_name += "/image.";
            image_file_name += info->isJpeg ? "jpg" : "raw";
            FILE* file = fopen(image_file_name.c_str(), "wb");
            if (file)
            {
                fwrite(info->imageData, 1, info->imageDataLen, file);
                fclose(file);
            }
        }
    }

    bool BarcodeDeviceHuaRui::ConnectCamera(std::string& serial_numbers)
    {
        std::cout << __FUNCTION__ << " serial_numbers " << serial_numbers << std::endl;
        if (serial_numbers.empty())
        {
            return false;
        }
        m_eid_camera = eidCreateDevice(serial_numbers.c_str(), eidDeviceDataTypeSN);
        if (m_eid_camera == 0)
        {
            return false;
        }
        int ret = eidOpenDevice(m_eid_camera);
        if (ret != eidErrorOK)
        {
            eidReleaseHandle(m_eid_camera);
            return false;
        }
        return true;
    }

    void EASYID_CALL BarcodeDeviceHuaRui::AsyncFrameCallback(const EidFrameInfo* frame, void* userData)
    {
        (void)userData;
        if (instance)
        {
            instance->ParsingQRCode(frame);
        }
    }

    int BarcodeDeviceHuaRui::StartGrab(EidCamera cam)
    {
        int frame_count = 0;
        eidRegisterFrameCallback(cam, AsyncFrameCallback, &frame_count);
        if (eidIsGrabbing(cam))
        {
            return 0;
        }
        int ret = eidStartGrabbing(cam, 0);
        if (ret != eidErrorOK)
        {
            return ret;
        }
        return 0;
    }

    int BarcodeDeviceHuaRui::StopGrab(EidCamera cam)
    {
        eidStopGrabbing(cam);
        eidRegisterFrameCallback(cam, NULL, NULL);
        return 0;
    }

    bool BarcodeDeviceHuaRui::OpenDevice()
    {
        std::vector<std::string> list_serial_number;
        if (EnumDeviceLists(list_serial_number))
        {
            if (list_serial_number.size() > 0)
            {
                if (ConnectCamera(list_serial_number.at(0)))
                {
                    eidSetEnumFeatureSymbol(m_eid_camera, "TriggerType", "FreeRun");//硬触发
                    return true;
                }
            }
        }
        return false;
    }

    bool BarcodeDeviceHuaRui::EnumDeviceLists(std::vector<std::string>& serial_numbers)
    {
        EidDeviceList dev_list = { 0 };
        int ret = eidEnumDevices(&dev_list, 0);
        if (ret != eidErrorOK)
        {
            return false;
        }
        if (dev_list.num == 0)
        {
            return false;
        }
        for (int i = 0; i < int(dev_list.num); i++)
        {
            std::string serial_number(dev_list.infos[i].serialNumber);
            serial_numbers.push_back(serial_number);
        }
        return true;
    }

    bool BarcodeDeviceHuaRui::OpenDeviceBySerialNumber(std::string& serial_numbers)
    {
        std::vector<std::string> list_serial_number;
        if (EnumDeviceLists(list_serial_number))
        {
            if (list_serial_number.size() > 0)
            {
                if (ConnectCamera(serial_numbers))
                {
                    eidSetEnumFeatureSymbol(m_eid_camera, "TriggerType", "FreeRun");//硬触发
                    return true;
                }
            }
        }
        return false;
    }

    bool BarcodeDeviceHuaRui::CloseDevice()
    {
        if (m_eid_camera == nullptr)
        {
            return false;
        }
        eidCloseDevice(m_eid_camera);
        eidReleaseHandle(m_eid_camera);
        return true;
    }

    void BarcodeDeviceHuaRui::SetEnableSaveImage(bool save_image)
    {
        if (instance)
        {
            instance->m_save_image = save_image;
        }
    }

    void BarcodeDeviceHuaRui::SetBarcodeImagePath(std::string image_path)
    {
        if (instance)
        {
            instance->file_path = image_path;
        }
    }
}

