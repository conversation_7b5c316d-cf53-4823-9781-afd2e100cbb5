@echo off
echo ========================================
echo JRSAOI UML图表导出脚本
echo ========================================
echo.

REM 检查是否安装了mermaid-cli
where mmdc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到mermaid-cli工具
    echo 请先安装: npm install -g @mermaid-js/mermaid-cli
    echo.
    pause
    exit /b 1
)

REM 创建images目录
if not exist "images" mkdir images

echo 开始导出UML图表...
echo.

REM 导出类图
echo [1/4] 导出整体架构UML类图...
mmdc -i class-diagram.md -o images/class-diagram.png -w 1920 -H 1080
if %errorlevel% equ 0 (
    echo ✓ 类图导出成功: images/class-diagram.png
) else (
    echo ✗ 类图导出失败
)

REM 导出时序图
echo [2/4] 导出检测流程时序图...
mmdc -i sequence-diagram.md -o images/sequence-diagram.png -w 1920 -H 1080
if %errorlevel% equ 0 (
    echo ✓ 时序图导出成功: images/sequence-diagram.png
) else (
    echo ✗ 时序图导出失败
)

REM 导出架构图
echo [3/4] 导出系统分层架构图...
mmdc -i architecture-diagram.md -o images/architecture-diagram.png -w 1920 -H 1080
if %errorlevel% equ 0 (
    echo ✓ 架构图导出成功: images/architecture-diagram.png
) else (
    echo ✗ 架构图导出失败
)

REM 导出ER图
echo [4/4] 导出核心数据模型ER图...
mmdc -i er-diagram.md -o images/er-diagram.png -w 1920 -H 1080
if %errorlevel% equ 0 (
    echo ✓ ER图导出成功: images/er-diagram.png
) else (
    echo ✗ ER图导出失败
)

echo.
echo ========================================
echo 导出完成！
echo 图片文件保存在 images/ 目录下
echo ========================================
echo.

REM 询问是否打开图片目录
set /p choice="是否打开图片目录? (y/n): "
if /i "%choice%"=="y" (
    start explorer images
)

pause
