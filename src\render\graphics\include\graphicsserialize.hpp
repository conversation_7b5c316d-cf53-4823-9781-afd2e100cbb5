/*********************************************************************
 * @brief  进行图形的序列化/反序列化,目前采用cereal.
 *
 * @file   graphicsserialize.hpp
 *
 * @date   2024.01.01
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef GRAPHICS_SERIALIZE_H
#define GRAPHICS_SERIALIZE_H

#include "graphicsobject.h"
#include "customgraphicsobject.h"

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "cereal/archives/json.hpp" // 支持json
#include "cereal/types/memory.hpp"  // 支持智能指针
#include "cereal/types/vector.hpp"  // 支持标准库类型
#include "opencv2/core/types.hpp"
#pragma warning(pop)

CEREAL_REGISTER_TYPE_WITH_NAME(RectGraphics, "RectGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(CircleGraphics, "CircleGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(PolygonGraphics, "PolygonGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(TextGraphics, "TextGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(BezierGraphics, "BezierGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(LineGraphics, "LineGraphics");
CEREAL_REGISTER_TYPE_WITH_NAME(SGGraphics, "SGGraphics");
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, RectGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, CircleGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, PolygonGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, TextGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, BezierGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, LineGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAbstract, SGGraphics)
CEREAL_REGISTER_POLYMORPHIC_RELATION(GraphicsAttributes<float>, GraphicsAbstract)
CEREAL_REGISTER_ARCHIVE(cv::Point)
CEREAL_REGISTER_ARCHIVE(Vec2)

namespace cv
{
    template <class Archive>
    void serialize(Archive& archive,
        Point& m)
    {
        archive(
            cereal::make_nvp("x", m.x),
            cereal::make_nvp("y", m.y));
    }
}
template <class Archive>
void serialize(Archive& archive,
    Vec2& m)
{
    archive(
        cereal::make_nvp("x", m.x),
        cereal::make_nvp("y", m.y));
}

template <class Archive, typename _T>
void serialize(Archive& archive,
    GraphicsAttributes<_T>& m)
{
    if (Archive::is_saving::value)
    {
        archive(
            cereal::make_nvp("x", m.x()),
            cereal::make_nvp("y", m.y()),
            cereal::make_nvp("w", m.w()),
            cereal::make_nvp("h", m.h()),
            cereal::make_nvp("a", m.a()));
    }
    else
    {
        _T x, y, w, h, a;
        archive(
            cereal::make_nvp("x", x),
            cereal::make_nvp("y", y),
            cereal::make_nvp("w", w),
            cereal::make_nvp("h", h),
            cereal::make_nvp("a", a));
        m.SetValue(x, y, w, h, a);
    }
}

template <class Archive>
void serialize(Archive& archive,
    GraphicsAbstract& m)
{
    std::string id;
    if (Archive::is_saving::value) {
        id = m.GetId().GetString();
        archive(
            cereal::make_nvp("attr", cereal::base_class<GraphicsAttributes<float>>(&m)),
            CEREAL_NVP(id));
    }
    else {
        archive(
            cereal::make_nvp("attr", cereal::base_class<GraphicsAttributes<float>>(&m)),
            CEREAL_NVP(id));
        m.SetId(id);
    }
}

template <class Archive>
void serialize(Archive& archive,
    RectGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m))
    );
}

template <class Archive>
void serialize(Archive& archive,
    CircleGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m))
    );
}

template <class Archive>
void serialize(Archive& archive,
    LineGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m)),
        cereal::make_nvp("start", m.start),
        cereal::make_nvp("end", m.end));
}

template <class Archive>
void serialize(Archive& archive,
    PolygonGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m)),
        cereal::make_nvp("contours", m.contours)
    );
}

template <class Archive>
void serialize(Archive& archive,
    TextGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m)),
        cereal::make_nvp("text", m.text)
    );
}

template <class Archive>
void serialize(Archive& archive,
    BezierGraphics& m)
{
    archive(
        cereal::make_nvp("abstract", cereal::base_class<GraphicsAbstract>(&m)),
        cereal::make_nvp("start", m.start),
        cereal::make_nvp("end", m.end),
        cereal::make_nvp("control", m.controls));
}

template <class Archive>
void serialize(Archive& archive,
    SGGraphics& m)
{
    archive(
        cereal::base_class<GraphicsAbstract>(&m),
        cereal::make_nvp("start", m.GetStart()),
        cereal::make_nvp("end", m.GetEnd()),
        cereal::make_nvp("size", m.terminal_size),
        cereal::make_nvp("type", m.terminal_type),
        cereal::make_nvp("nodes", m.GetPoint()));
}

#endif // !GRAPHICS_SERIALIZE_H