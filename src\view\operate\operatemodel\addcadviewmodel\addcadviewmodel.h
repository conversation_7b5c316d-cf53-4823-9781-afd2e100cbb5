/*****************************************************************//**
 * @file   AddCadViewModel.h
 * @brief 添加CAD TableView的Model
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef AddCadViewModel_H
#define AddCadViewModel_H
 //STD
#include <string>
#include <vector>
//QT
#include <QObject>
#include <QStandardItemModel>
#include <QStringList>
#include <QVariant>
#include <QModelIndex>
//CUSTOM
#include "datadefine.hpp"

class AddCadViewModel : public QStandardItemModel
{
    Q_OBJECT
public:
    /**
     * @fun index
     * @brief 获取模型索引
     * @param row 行号
     * @param column 列号
     * @param parent 父索引
     * @return QModelIndex 模型索引
     * @date 2025.02.25
     * <AUTHOR>
     */
    QModelIndex index(int row, int column, const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun parent
     * @brief 获取父索引
     * @param child 子索引
     * @return QModelIndex 父索引
     * @date 2025.02.25
     * <AUTHOR>
     */
    QModelIndex parent(const QModelIndex& child) const override;
    /**
     * @fun rowCount
     * @brief 获取行数
     * @param parent 父索引
     * @return int 行数
     * @date 2025.02.25
     * <AUTHOR>
     */
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun columnCount
     * @brief 获取列数
     * @param parent 父索引
     * @return int 列数
     * @date 2025.02.25
     * <AUTHOR>
     */
    int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun data
     * @brief 获取单元格数据
     * @param index 模型索引
     * @param role 角色
     * @return QVariant 包含单元格数据的 QVariant
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    /**
     * @fun headerData
     * @brief 获取表头数据
     * @param section 表头部分
     * @param orientation 方向
     * @param role 角色
     * @return QVariant 包含表头数据的 QVariant
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    /**
     * @fun SetDataModel
     * @brief 设置数据模型
     * @details 更新内部数据结构并通知视图更新
     * @param vector_data 二维字符串向量，包含表格数据
     * @param column_count 列数
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void SetDataModel(std::vector<std::vector<std::string>>& vector_data, int column_count);
    /**
     * @fun GetDataModel
     * @brief 获取当前数据模型
     * @param vector_data 用于存储当前数据模型的二维字符串向量
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void GetDataModel(std::vector<std::vector<std::string>>& vector_data);
    /**
     * @fun GetHeaderLabels
     * @brief 获取表头标签
     * @details 根据列数生成表头标签
     * @return QStringList 包含表头标签的字符串列表
     * @date 2025.02.25
     * <AUTHOR>
     */
    QStringList GetHeaderLabels();
public:
    /**
     * @fun AddCadViewModel
     * @brief 构造函数，初始化 AddCadViewModel
     * @param parent 父对象
     * @date 2025.02.25
     * <AUTHOR>
     */
    AddCadViewModel(QObject* parent);
    /**
     * @fun ~AddCadViewModel
     * @brief 析构函数，释放资源
     * @date 2025.02.25
     * <AUTHOR>
     */
    ~AddCadViewModel();
private:
    /// 列数，表示表格的列数。
    int m_column_count;

    /// 标题栏数据，存储表头的字符串列表。
    QStringList m_header_list;

    /// 表格数据，二维字符串向量，存储表格的每一行和每一列的数据。
    std::vector<std::vector<std::string>> m_vec_data;
};
#endif