/*****************************************************************//**
 * @file   controllerbase.h
 * @brief  界面控制器基类
 * @details    
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Description
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __CONTROLLERBASE_H__
#define __CONTROLLERBASE_H__
// prebuild
#include "pch.h"
#pragma warning(push, 1)
#include <QObject>
#pragma warning(push)
//#include "viewparam.hpp"
namespace jrsaoi
{
    class ViewBase;
    class ModelBase;
    using ModelBasePtr = std::shared_ptr<ModelBase>;
    class ControllerBase;
    using ControllerBasePtr = std::shared_ptr<ControllerBase>;
    class ControllerBase :
        public QObject
    {
    public:
        virtual ~ControllerBase ()= default;
        /**
         * @fun Update 
         * @brief 更新参数
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual int Update (const jrsdata::ViewParamBasePtr& param_) = 0;
        /**
         * @fun Save 
         * @brief 保存参数
         * @param param_
         * @return 
         * @date 2024.1.31
         * <AUTHOR>
         */
        virtual int Save (const jrsdata::ViewParamBasePtr& param_) = 0;
        /**
         * @fun SetView 
         * @brief 传入view实例
         * @param view_param view实例
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual void SetView (ViewBase* view_param) = 0;
        /**
         * @fun SetModel 
         * @brief 传入model实例
         * @param model_param model 实例
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual void SetModel (ModelBasePtr model_param) = 0;
    protected:
    
        explicit ControllerBase (const std::string& name);
    private:
        std::string type_name; /** < */
    
    };

}

#endif // !__CONTROLLERBASE_H__

