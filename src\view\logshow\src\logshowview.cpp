//QT
#include <QGridLayout>
#include <QPlainTextEdit>
//Custom
#include "logshowview.h"

namespace jrsaoi
{
    LogShowView::LogShowView(const std::string& name, QWidget* parent) :ViewBase(name, parent)
    {
        Init();
    }
    LogShowView::~LogShowView()
    {

    }
    int LogShowView::Init()
    {
        InitView();
        return jrscore::AOI_OK;

    }
    int LogShowView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    void LogShowView::InitView()
    {
        QGridLayout* main_layout = new QGridLayout(this);

        show_log_message_edit = new QPlainTextEdit(this);
        show_log_message_edit->setMaximumBlockCount(MAX_LINE);
        main_layout->addWidget(show_log_message_edit);
        AddMessage("Init...");
        main_layout->setContentsMargins(0, 0, 0, 0);




    }
    void LogShowView::AddMessage(const std::string& msg_)
    {
        QTextCursor cursor = show_log_message_edit->textCursor();
        QTextBlockFormat blockFormat;
        QTextCharFormat charFormat;

        /*  for (const auto& pair : _levelColors)
          {

              if (IsContainsSubString (msg, pair.first))
              {
                  if (_currentShowLevel != pair.first && _currentShowLevel != "All")
                  {
                      return;
                  }
                  charFormat.setForeground (pair.second);
                  break;
              }
          }*/

        cursor.movePosition(QTextCursor::End);
        cursor.insertBlock(blockFormat); // 在光标处插入一个新的文本块
        cursor.movePosition(QTextCursor::EndOfBlock);
        cursor.insertText(QString::fromStdString(msg_), charFormat);
        show_log_message_edit->setTextCursor(cursor);
        show_log_message_edit->ensureCursorVisible();
    }
    int LogShowView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
}
