project(devicemanager)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#导出宏
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)
 
# 扫码枪部分
set(barcodescanner_src
    barcodescanner/src/barcodedevice.cpp
    barcodescanner/src/barcodedevicehuarui.cpp
)

set(barcodescanner_head
    barcodescanner/include/barcodedevice.h
    barcodescanner/include/barcodedevicehuarui.h
)

# 管理部分
set(devicemanager_src
    devicemanager/src/devicemanager.cpp
)

set(devicemanager_head
 
    devicemanager/include/devicemanager.h
)

# 结构光部分
set(structlight_src

    structlight/src/structlight.cpp
)
set(structlight_head

    structlight/include/structlight.h
)


source_group("barcodescanner/src" FILES ${barcodescanner_src})
source_group("barcodescanner/head" FILES ${barcodescanner_head})

source_group("devicemanager/src" FILES ${devicemanager_src}) 
source_group("devicemanager/head" FILES ${devicemanager_head})

source_group("structlight/src" FILES ${structlight_src}) 
source_group("structlight/head" FILES ${structlight_head})



add_library(${PROJECT_NAME} SHARED
    ${barcodescanner_src}
    ${barcodescanner_head}
    ${devicemanager_src}
    ${devicemanager_head}
    ${structlight_src}
    ${structlight_head}
    ${JRS_VERSIONINFO_RC} 
)

#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

target_link_directories(${PROJECT_NAME} 
    PRIVATE
    #OPENCV
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<CONFIG:Release>:${OPENCV_RELEASE_DIR}>
    #EASYID
    $<$<CONFIG:Debug>:${EASYID_DEBUG_DIR}>
    $<$<CONFIG:Release>:${EASYID_RELEASE_DIR}>
    # 成像模块
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/debug>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>
    $<$<CONFIG:RelWithDebInfo>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/relwithdebinfo>

    
    #FOV基准校正
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/heightbasecorrect/lib/debug>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/heightbasecorrect/lib/release>
    
    
)

 
#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}
    ormpp
    parametermanager
    #成像模块
    $<$<CONFIG:Debug>:jrsstructlightcontrlmodule>
    $<$<CONFIG:Release>:jrsstructlightcontrlmodule>
    $<$<CONFIG:RelWithDebInfo>:jrsstructlightcontrlmodule>
    
    #基准校正
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}/thirdparty/heightbasecorrect/lib/debug/BasePlaneProject.lib>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}/thirdparty/heightbasecorrect/lib/release/BasePlaneProject.lib>
    
    #EASYID 华瑞扫码枪
    $<$<CONFIG:Debug>:${EASYID_DEBUG_DIR}EasyID.lib>
    $<$<CONFIG:Release>:${EASYID_RELEASE_DIR}EasyID.lib>
)

# 引入头文件
target_include_directories(${PROJECT_NAME} PUBLIC
                            ${DIR_PROJECT_CURRENT}/src/parametermanager/define/dataparam
                            ${DIR_PROJECT_CURRENT}/thirdparty/jrsstructlightcamera/include
                            ${DIR_PROJECT_CURRENT}/src/devicemanager/barcodescanner/include
                            ${DIR_PROJECT_CURRENT}/src/devicemanager/devicemanager/include
                            ${DIR_PROJECT_CURRENT}/src/devicemanager/structlight/include
                            ${DIR_PROJECT_CURRENT}/src/core/common/include
                            ${DIR_PROJECT_CURRENT}/src/parametermanager/define/image
                            ${DIR_PROJECT_CURRENT}/src/parametermanager/define/projectparam
                            ${DIR_PROJECT_CURRENT}src/parametermanager/define/viewparam
                            ${DIR_PROJECT_CURRENT}/thirdparty/json/include
                            ${DIR_PROJECT_CURRENT}/thirdparty/asio-1.30.0/include
                            ${DIR_PROJECT_CURRENT}/thirdparty/heightbasecorrect/include
                            ${DIR_PROJECT_CURRENT}/src/core/common/include
                            #华瑞扫码枪头文件
                            ${EASYID_INCLUDE_DIR}
                            #${OPENCV_INCLUDE_DIR}
                            )

#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
