<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TrackDebug</class>
 <widget class="QWidget" name="TrackDebug">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>280</width>
    <height>178</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    margin-top: 10px;
    border: 1px solid black;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 5px;
}

</string>
     </property>
     <property name="title">
      <string>轨道调试</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_17">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="1" column="0">
       <widget class="QFrame" name="frame_11">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_15">
         <property name="topMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>3</number>
         </property>
         <item row="0" column="1">
          <widget class="QLabel" name="cylinder_down">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>20</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="shield_up">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>20</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QLabel" name="shield_down">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>20</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="cylinder_up">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>20</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QFrame" name="frame_14">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_12">
         <property name="topMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>3</number>
         </property>
         <item row="0" column="0">
          <widget class="QLabel" name="label_13">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="text">
            <string>动作调试</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="forward_rotation">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>正转</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="reverse_rotation">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>反转</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QPushButton" name="stop_axis">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>停止</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QFrame" name="frame_13">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <property name="topMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>3</number>
         </property>
         <item row="0" column="0">
          <widget class="QLabel" name="label_12">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="text">
            <string>上下料调试</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="loading">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>上料</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="unloading">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>下料</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QPushButton" name="startdebug">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>上下料</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QFrame" name="frame_15">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_11">
         <property name="topMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>3</number>
         </property>
         <item row="0" column="3">
          <widget class="QPushButton" name="shield_off">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>挡板下降</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QPushButton" name="cylinder_on">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>夹板上升</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="cylinder_off">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>夹板下降</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="shield_on">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>挡板顶升</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QFrame" name="frame_9">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_16">
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>3</number>
         </property>
         <item row="0" column="1">
          <widget class="QComboBox" name="track">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <item>
            <property name="text">
             <string>轨道1</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>轨道2</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_4">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>25</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>25</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="text">
            <string>轨道名称:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
