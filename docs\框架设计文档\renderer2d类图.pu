@startuml 渲染类图

class StateManager {
    -VisionMode m_state;
    -CreateGraphicsMode m_createmode;
    -WheelMode m_wheelmode;
    -Event callback_regionselected;
}

class 图形模块.GraphicsManager {
}
class 图形模块.GraphicsAttributes {
}
class 图形模块.GraphicsID {
}
class 图形模块.GraphicsIDTempData {
}

class 命令模块.CommandManager {
}
class 命令模块.CommandManager {
}

class ui模块.Renderer2DManager {
}
class ui模块.RulerWidget {
}
class ui模块.ThumbnailNavigationWidget {
}
class ui模块.Renderer2DWidget {
}
class ui模块.GLWindow {
}
class ui模块.WindowSignalEmitter {
}

class gl模块.Windowinterface {
}
class gl模块.Renderer {
}
class gl模块.VisualCameraAbstract {
}

namespace gl模块 {        
    RenderAbstract --> RenderAbstract : Parent-Child
    RenderAbstract -- Renderer

    ShaderProgram ..> ROpenGLFunctions
    ShaderProgram *--> QOpenGLShaderProgram

    VisualCameraAbstract *-->QVector3D
    VisualCameraAbstract *-->QVector4D
    VisualCameraAbstract o-->QMatrix4x4

    VisualCameraAbstract <|-- VisualCameraTopDown

    Renderer o--> QOpenGLContext
    Renderer o--> QSurface
    Renderer o--> QPaintDevice
    Renderer o--> ShaderProgram
    Renderer o--> VisualCameraAbstract
    Renderer o--> Windowinterface
    Renderer *--> RenderAbstract

    ShaderProgramManager *--> ShaderProgram

    Windowinterface o--> QRect
    Windowinterface o--> QPoint
    Windowinterface o--> WindowSignalEmitter
    Windowinterface o--> VisualCameraAbstract
    Windowinterface *--> Renderer
    Windowinterface *--> ShaderProgramManager

    RenderAbstract <|-- GraphicsManager
}

namespace 图形模块 {
    GraphicsID *--> GraphicsIDTempData

    GraphicsAttributes --> GraphicsAttributes : Parent-Child
    GraphicsAttributes *--> GraphicsID 

    GraphicsAttributes <|-- GraphicsAbstract
    GraphicsAbstract <|-- RectGraphics
    GraphicsAbstract <|-- PolygonGraphics
    GraphicsAbstract <|-- CircleGraphics
    TransientObjectControl <|-- RectGraphics
    TransientObjectControl <|-- PolygonGraphics
    TransientObjectControl <|-- CircleGraphics

    ControlPointAbstract --> Renderer
    ControlPointAbstract --> Painter
    ControlPointAbstract --> LayerConfig
    ControlPointAbstract *--> ControlAttributes
    ControlPointAbstract *--> ControlPointDraw

    ControlPointFactory o--> ControlPointAbstract

    GraphicsAbstract ..> ControlPointFactory 
    GraphicsAbstract *--> ControlPointAbstract 
    GraphicsAbstract *--> DrawSettings
        
    GraphicsManager *--> GraphicsAbstract
    GraphicsManager *--> LayerConfig
    GraphicsManager *--> Event
}

namespace 命令模块 {
    CommandAbstract <|-- CommandCreateGraphics
    CommandAbstract <|-- CommandDeleteGraphics
    CommandAbstract <|-- CommandEditGraphics
    CommandManager *--> CommandAbstract
    CommandManager --> GraphicsManager
}

namespace ui模块 {
    QWidget <|-- ThumbnailNavigationWidget 
    QWidget <|-- RulerWidget 
    QWidget <|-- Renderer2DWidget 
    QObject <|-- WindowSignalEmitter 
    ThumbnailNavigation *--> qt.QImage
    ThumbnailNavigation *--> cv.Mat
    ThumbnailNavigation <|-- ThumbnailNavigationWidget
    RulerWidget *--> qt.QColor
    RulerWidget *--> qt.QFont

    Windowinterface <|-- GLWindow
    QOpenGLWidget <|-- GLWindow
    GLWindow --> QOpenGLContext
    GLWindow --> QPaintDevice

    Renderer2DWidget o--> GLWindow
    Renderer2DWidget o--> RulerWidget
    Renderer2DWidget *--> ui.Renderer2DWidgetClass

    Renderer2DManager --> WindowSignalEmitter
    Renderer2DManager o--> GraphicsManager
    Renderer2DManager o--> RenderAbstract
    Renderer2DManager o--> QTimer
    Renderer2DManager *--> Renderer2DWidget
    Renderer2DManager *--> ThumbnailNavigationWidget
    Renderer2DManager *--> GLWindow
    Renderer2DManager *--> RulerWidget
    Renderer2DManager *--> VisualCameraAbstract
    Renderer2DManager *--> Event

    CustomCursorManager *--> QCursor
}



StateManager --> QObject
StateManager o--> GraphicsManager
StateManager *--> Renderer2DManager
StateManager *--> CommandManager
StateManager *--> CustomCursorManager
StateManager *--> Event

Renderer2D *--> StateManager

@enduml