﻿#include "renderer2d.h"
#include "statemanager.h" // StateManager

Renderer2D::Renderer2D()
    : manager(new StateManager())
{
}

Renderer2D::~Renderer2D()
{
    if (manager)
    {
        delete manager;
        manager = nullptr;
    }
}

QWidget* Renderer2D::GetWidget()
{
    return manager->GetWidget();
}

std::shared_ptr<GraphicsManager> Renderer2D::GetGraphicsManager() const
{
    return manager->GetGraphicsManager();
}

void Renderer2D::Undo()
{
    manager->Undo();
}

void Renderer2D::Redo()
{
    manager->Redo();
}

void Renderer2D::Update()
{
    manager->Update();
}

void Renderer2D::Clear()
{
    manager->Clear();
}

void Renderer2D::ClearGraphics(bool invoke_callback)
{
    manager->ClearGraphics(invoke_callback);
}

void Renderer2D::ClearGraphics(const std::string& except_layer_, bool invoke_callback)
{
    manager->ClearGraphics(except_layer_, invoke_callback);
}

void Renderer2D::ClearPadGroups()
{

    manager->DeletePadGroups();
}


void Renderer2D::DeleteGraphicsWithLayer(const std::string& layer, bool invoke_callback_)
{
    manager->DeleteGraphicsWithLayer(layer, invoke_callback_);
}

int Renderer2D::GetAllGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs) const
{
    return manager->GetAllGraphics(ghs);
}

int Renderer2D::GetAllGraphics(std::string& str) const
{
    return manager->GetAllGraphics(str);
}

int Renderer2D::GetLayerGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer) const
{
    return manager->GetLayerGraphics(ghs, layer);
}

int Renderer2D::GetCurrentSelectedGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer)
{
    return manager->GetCurrentSelectedGraphics(ghs, layer);
}

int Renderer2D::GetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer)
{
    return manager->GetCurrentSelectedGraphicsSingle(gh, layer);
}

int Renderer2D::GetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids) const
{
    return manager->GetGraphics(ghs, ids);
}

int Renderer2D::GetGraphics(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id) const
{
    return manager->GetGraphics(gh, id);
}

int Renderer2D::DeleteGraphics(const std::vector<GraphicsID>& ids)
{
    return manager->DeleteGraphics(ids);
}

int Renderer2D::AddGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback)
{
    return manager->AddGraphics(ghs, layer, invoke_callback);
}

int Renderer2D::AddGraphics(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer, bool invoke_callback)
{
    return manager->AddGraphics(gh, layer, invoke_callback);
}

int Renderer2D::AddGraphics(const std::string& str, bool invoke_callback)
{
    return manager->AddGraphics(str, invoke_callback);
}

int Renderer2D::CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, std::shared_ptr<GraphicsAbstract>& gh, float x, float y)
{
    return manager->CopyGraphics(new_gh, gh, x, y);
}

int Renderer2D::CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, const GraphicsID& id, float x, float y)
{
    return manager->CopyGraphics(new_gh, id, x, y);
}

void Renderer2D::SetGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state)
{
    manager->SetGraphicsSelected(ghs, state);
}

void Renderer2D::SetGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback)
{
    manager->SetGraphicsSelectedSingle(gh, invoke_callback);
}

std::shared_ptr<GraphicsAbstract> Renderer2D::CreateGraphicsTool(int graphics_type, const std::string& layer,
    const std::string& group_name_, const GraphicsPtr& father_graphics_)
{
    return manager->CreateGraphicsTool(graphics_type, layer, group_name_, father_graphics_);
}

const std::unordered_map<std::string, std::shared_ptr<LayerConfig>>& Renderer2D::GetAllLayerConfig() const
{
    return manager->GetAllLayerConfig();
}

void Renderer2D::AddGraphicsLayerConfig(const std::string& layer, std::shared_ptr<LayerConfig> config)
{
    manager->AddGraphicsLayerConfig(layer, config);
}

int Renderer2D::GetLayerConfig(std::weak_ptr<LayerConfig>& result, const std::string& layer) const
{
    return manager->GetLayerConfig(result, layer);
}

void Renderer2D::ClearLayerConfig()
{
    manager->ClearLayerConfig();
}

int Renderer2D::SetCurrentLayer(const std::string& layer)
{
    return manager->SetCurrentLayer(layer);
}

std::string Renderer2D::GetCurrentLayer() const
{
    return manager->GetCurrentLayer();
}

void Renderer2D::SetManualDefaultDrawAngle(float angle)
{
    manager->SetManualDefaultDrawAngle(angle);
}

float Renderer2D::GetManualDefaultDrawAngle()
{
    return manager->GetManualDefaultDrawAngle();
}

void Renderer2D::ClearTexture(int z /*= 0*/)
{
    manager->ClearTexture(z);
}

int Renderer2D::CreateTexture(unsigned int& id, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center)
{
    return manager->CreateTexture(id, image, x, y, z, angle, is_draw_center);
}


int Renderer2D::CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center, int current_show_image_key_)
{
    return manager->CreateImage(key_, image, x, y, z, angle, is_draw_center, current_show_image_key_);
}

int Renderer2D::CreateImages(const GraphicsImage& graphics_img_)
{
    return manager->CreateImages(graphics_img_);
}

int Renderer2D::AddGraphicsShapes(const GraphicsShape& graphics_shape_)
{
    return manager->AddGraphicsShapes(graphics_shape_);
}

int Renderer2D::ClearGraphicsShapes()
{
    return manager->ClearGraphicsShapes();
}

int Renderer2D::ClearImage(const int& set_key_, int key_)
{
    return manager->ClearImage(set_key_, key_);
}



bool Renderer2D::ShowImage(const uint8_t& set_key_, int key_)
{
    return manager->ShowImage(set_key_, key_);
}

int Renderer2D::SetTextureZ(unsigned int id, int z, bool make_unique)
{
    return manager->SetTextureZ(id, z, make_unique);
}

int Renderer2D::SetCanvasSize(int width, int height)
{
    return manager->SetCanvasSize(width, height);
}

int Renderer2D::GetCanvasSize(int& width, int& height)
{
    return manager->GetCanvasSize(width, height);
}

void Renderer2D::MoveCamera(float xoffset, float yoffset)
{
    manager->MoveCamera(xoffset, yoffset);
}

void Renderer2D::MoveCamera(int direction)
{
    manager->MoveCamera(direction);
}

void Renderer2D::MoveCameraTo(float x, float y)
{
    manager->MoveCameraTo(x, y);
}

void Renderer2D::MoveCameraTo(std::shared_ptr<GraphicsAbstract> gh)
{
    manager->MoveCameraTo(gh);
}

void Renderer2D::MoveCameraTo(const std::string& graphicsid)
{
    manager->MoveCameraTo(graphicsid);
}

void Renderer2D::ResetCamera(int type)
{
    manager->ResetCamera(type);
}

void Renderer2D::SetZoom(float zoom)
{
    manager->SetZoom(zoom);
}

void Renderer2D::SetZoomState(int state)
{
    manager->SetZoomState(state);
}

int Renderer2D::SetLimitViewByCanvas(bool state)
{
    return manager->SetLimitViewByCanvas(state);
}

void Renderer2D::SetState(const int& state)
{
    manager->SetState(state);
}

int Renderer2D::GetState()
{
    return static_cast<int>(manager->GetState());
}

void Renderer2D::SetCreateGraphicsMode(int mode)
{
    manager->SetCreateGraphicsMode(static_cast<CreateGraphicsMode>(mode));
}

void Renderer2D::SetSelectGraphicsMode(const int& mode)
{
    manager->SetSelectGraphicsMode(mode);
}

int Renderer2D::GetSelectGraphicsMode() const
{
    return manager->GetSelectGraphicsMode();
}

void Renderer2D::SetShowDebugInfo(bool state)
{
    manager->SetShowDebugInfo(state);
}
void Renderer2D::SetShowCenterCrossLine(bool state)
{
    manager->SetShowCenterCrossLine(state);
}
void Renderer2D::SetRulerDisplayScale(double scale)
{
    return manager->SetRulerDisplayScale(scale);
}

void Renderer2D::SetRulerPrecision(int precision)
{
    return manager->SetRulerPrecision(precision);
}

int Renderer2D::SetThumbnailNavigation(const cv::Mat& image, int image_true_w, int image_true_h)
{
    return manager->SetThumbnailNavigation(image, image_true_w, image_true_h);
}

int Renderer2D::SetThumbnailShow()
{
    return manager->SetThumbnailShow();
}

void Renderer2D::SetCallbackGraphicscreate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback)
{
    manager->SetCallbackGraphicscreate(callback);
}

void Renderer2D::SetCallbackGraphicsupdate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&, bool)> callback)
{
    manager->SetCallbackGraphicsupdate(callback);
}

void Renderer2D::SetCallbackGraphicsdelete(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback)
{
    manager->SetCallbackGraphicsdelete(callback);
}

void Renderer2D::SetCallbackGraphicsselected(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback)
{
    manager->SetCallbackGraphicsselected(callback);
}

void Renderer2D::SetCallbackRegionselected(std::function<void(float x, float y, float w, float h)> callback)
{
    manager->SetCallbackRegionselected(callback);
}
