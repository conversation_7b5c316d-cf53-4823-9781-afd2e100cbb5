/*****************************************************************//**
 * @file   axismove.h
 * @brief  轴运动调试模块
 * @details
 * <AUTHOR>
 * @date 2024.8.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.22         <td>V1.0              <td>zhaokunlong      <td><td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef AXISMOVE_H
#define AXISMOVE_H
// prebuild
#include "pch.h"
// QT
//#include <QWidget>
#include <QMutex>
#include <QDialog>  
#include <QVBoxLayout>
#include <QComboBox>
#include <QGroupBox>
#pragma warning(push, 3)
#include "ui_axismove.h"
#pragma warning(pop)
 //CUSTOM
//#include <viewparam.hpp>
#include <algorithm>

QT_BEGIN_NAMESPACE
namespace Ui {
    class AxisMove;
}
QT_END_NAMESPACE

class AxisMove : public QWidget
{
    Q_OBJECT
public:
    explicit AxisMove(QWidget* parent = nullptr);
    ~AxisMove();
public slots:
    /**
     * @fun SlotPushButtonTrigger
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SlotPushButtonTrigger();
    /**
     * @fun UpdateView 
     * @brief 更新界面参数
     * @param ptr
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateView(const jrsdata::OperateViewParamPtr ptr);
    /**
     * @fun SlotUpdateAxisPos 
     * @brief
     * @param text_
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SlotUpdateAxisPos(const QString& text_);
signals:
    /**
     * @fun SigMotionDebugTrigger
     * @brief
     * @param operateparam
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigMotionDebugTrigger(jrsdata::OperateViewParamPtr operateparam);
    /**
     * @fun SigCurrentAxisPos 
     * @brief
     * @param curren_pos_
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigCurrentAxisPos(const jrsdata::CurrentAxisPos& curren_pos_);
private:
    /**
     * @fun InitView 
     * @brief 初始化界面
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitView();
    /**
     * @fun InitConnect 
     * @brief 初始信号链接
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitConnect();
    /**
     * @fun CreateMovParam 
     * @brief 构建单轴相对运动参数
     * @param axis
     * @param direction
     * @return 
     * @date 2024.9.24
     * <AUTHOR>
     */
    jrsdata::MoveParam CreateMovParam(int axis, int direction);
    /**
     * @fun CreateLopToParam 
     * @brief 构建双轴运动参数
     * @param xdirection
     * @param ydirection
     * @return 
     * @date 2024.9.24
     * <AUTHOR>
     */
    jrsdata::MoveParam CreateLopToParam(int xdirection, int ydirection);
private:
    Ui::AxisMove* ui;
    QMutex track_mutex;
    jrsdata::OperateViewParamPtr axis_move_view_ptr;
    bool move_lock;// 移动锁
};
#endif // AXISMOVE_H
