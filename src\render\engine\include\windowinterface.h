/*********************************************************************
 * @brief  GL窗口接口类.
 *
 * @file   windowinterface.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef __WINDOWINTERFACE_0708_H__
#define __WINDOWINTERFACE_0708_H__

#include "qrect.h"            // QRect
#include "renderabstract.hpp" // RenderAbstractPtr
#include "engineconstants.hpp" // RenderType RefreshMode
#include "ropenglfunctions.hpp" // ROpenGLFunctions


class ShaderProgram;
class Renderer;
class VisualCameraAbstract;
class WindowSignalEmitter;
class ShaderProgramManager;

class QMouseEvent;
class QWheelEvent;
class QKeyEvent;
class QResizeEvent;
class QPaintDevice;

class WindowInterface
{
public:
    WindowInterface(QObject* parent = nullptr);
    virtual ~WindowInterface();

    virtual QOpenGLContext* GetOpenGLContext() const = 0;
    virtual void DoMakeCurrent() = 0;
    virtual void DoDoneCurrent() = 0;
    // virtual void GetContext(DrawContext &context);

    virtual void Update();

    virtual qreal GetDevicePixelRatio() const = 0;
    virtual int qtWidth() const = 0;
    virtual int qtHeight() const = 0;
    virtual QSize qtSize() const = 0;
    virtual QPoint qtPos() const = 0;
    virtual QWidget* asWidget() { return nullptr; }
    virtual QPaintDevice* GetPaintDevice() { return nullptr; }
    virtual void RequestUpdate() = 0;

    const WindowSignalEmitter* signalEmitter() const { return emitter; }
    WindowSignalEmitter* signalEmitter() { return emitter; }

    /**
     * @brief 设置主视图相机
     */
    void SetMainVisualCamera(VisualCameraAbstract* camera);

    // void MoveCamera(int type);
    // void MoveCamera(float xoffset, float yoffset);
    // void ResetCamera(int type);
    // void MoveCameraTo(float x, float y);
    // void SetZoom(float zoom);
    // void SetZoomState(int state);
    // bool SetCanvasSize(int width, int height);
    // void SetLimitViewByCanvas(bool state);

    void AddObject(RenderAbstractPtr ro, const RenderType& type);
    void RemoveObject(RenderAbstractPtr ro, const RenderType& type);

    void RenderSetProgram(Renderer*, const ProgramType& type);
    /**
     * @brief 设置界面刷新模式
     */
    void SetRefreshMode(RefreshMode mode) { refresh_mode = mode; }

protected:
    virtual ROpenGLFunctions* functions() const = 0;
    // TODO fbo离屏渲染
    virtual void swapGLBuffers() {}
    void onResizeGL(int w, int h);

    void setGLViewport(const QRect& rect);
    inline void setGLViewport(int x, int y, int w, int h) { setGLViewport(QRect(x, y, w, h)); }

    virtual int width() const = 0;
    virtual int height() const = 0;
    virtual QSize size() const = 0;

    /**
     * @brief 初始化OpenGL上下文
     */
    bool initialize();
    /**
     * @brief 释放OpenGL上下文
     */
    void uninitializeGL();
    /**
     * @brief 绘制
     */
    void doPaintGL();
    /**
     * @brief 初始化绘制
     * @note  由qt界面内部完成所以直接返回true
     */
    virtual bool initPaintGL() { return true; }

protected:
    void processMousePressEvent(QMouseEvent* event);
    void processMouseDoubleClickEvent(QMouseEvent* event);
    void processMouseMoveEvent(QMouseEvent* event);
    void processMouseReleaseEvent(QMouseEvent* event);
    void processWheelEvent(int degree, int x, int y);
    void processKeyPressEvent(int key, bool isAutoRepeat);
    void processKeyReleaseEvent(int key, bool isAutoRepeat);
    // void processWheelEvent(QWheelEvent* event);
    // void processKeyPressEvent(QKeyEvent* event);
    // void processKeyReleaseEvent(QKeyEvent* event);
    void processResizeEvent(int w, int h);
    void processEnterEvent();
    void processLeaveEvent();

    bool m_initialized;        ///< 是否初始化完成
    bool mouse_moved;          ///< 鼠标是否移动
    bool mouse_pressed;        ///< 鼠标是否按下
    RefreshMode refresh_mode;  ///< 刷新模式

    WindowSignalEmitter* emitter;         ///< 信号转发器
    VisualCameraAbstract* main_camera;    ///< 主虚拟相机,用于控制主视图
    std::vector<Renderer*> renderers;     ///< 渲染器,分成前景,图形,背景分别渲染,方便区分渲染层级
    ShaderProgramManager* shader_manager; ///< 着色器管理器

    QRect m_gl_viewport;         ///< 视图
    QPoint m_point_mouse_press; ///< 鼠标按下位置
    QPoint m_point_mouse_last;  ///< 鼠标上次移动位置
    // QPoint m_point_mouse_current;  ///< 鼠标当前位置
};

#endif // __WINDOWINTERFACE_0708_H__