/*****************************************************************//**
 * @file   algogeometrydata.h
 * @brief  算法几何数据结构定义文件
 * @details
 * <AUTHOR>
 * @date  2024.11.27
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.27         <td>V1.0              <td>xailor      <td><EMAIL> <td>
 * <tr><td>2024.12.18         <td>V1.1              <td>xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
**********************************************************************/
#ifndef __ALGOGEOMETRYDATA_H__
#define __ALGOGEOMETRYDATA_H__
#pragma warning(push, 1)
#pragma warning(disable : 4003 4514 4365 4244 4800 4820 26495)
// std
#include <iostream>
#include <string>
// opencv
#include <opencv2/opencv.hpp>
// thirdparty
#include <iguana/iguana.hpp>
#pragma warning(pop)
// 三维数据结构
struct JrsVec3f
{
    float x = -1.0f;
    float y = -1.0f;
    float z = 0.0f;
    int id = -1; // 编号

    JrsVec3f() : x(-1.0f), y(-1.0f), z(0), id(-1) {}
    JrsVec3f(float x, float y, float z = 0.0f, int id = -1) : x(x), y(y), z(z), id(id) {}
    // 析构函数
    ~JrsVec3f() = default;
    // 重载赋值运算符
    JrsVec3f& operator=(const JrsVec3f& vec)
    {
        if (this == &vec)
        {
            return *this;
        }
        x = vec.x;
        y = vec.y;
        z = vec.z;
        id = vec.id;
        return *this;
    }


    // 空判断
    bool Empty() const
    {
        return x == -1.0f && y == -1.0f;
    }
    // 转换为cv::Point2f
    cv::Point2f ToCvPoint2f() const
    {
        return cv::Point2f(x, y);
    }
    // 从cv::Point2f转换
    static JrsVec3f FromCvPoint2f(const cv::Point2f& point, float z = 0.0f, int id = -1)
    {
        return JrsVec3f(point.x, point.y, z, id);
    }
    // 转换为cv::Point
    cv::Point ToCvPoint() const
    {
        return cv::Point(static_cast<int>(x), static_cast<int>(y));
    }
    // 从cv::Point转换
    static JrsVec3f FromCvPoint(const cv::Point& point, float z = 0.0f, int id = -1)
    {
        return JrsVec3f(static_cast<float>(point.x), static_cast<float>(point.y), z, id);
    }

    YLT_REFL(
        JrsVec3f,
        x,
        y,
        z,
        id
    )
};

/**
* @struct  JrsRect
* @brief  矩形和旋转矩形
* @param  x 矩形和旋转矩形的中心坐标x
* @param  y 矩形和旋转矩形的中心坐标y
* @param  width 宽度
* @param  height 高度
* @param  angle 矩形和旋转矩形的中心坐标
* @param  id 矩形的编号
* @date   2024.11.21
* <AUTHOR>
*/
struct JrsRect
{
    float cx = 0.0f; // 矩形和旋转矩形的中心坐标
    float cy = 0.0f; // 矩形和旋转矩形的中心坐标
    float width = 0.0f; // 宽度
    float height = 0.0f; // 高度
    float angle = 0.0f; // 旋转角度 非旋转矩形时为0
    int id = -1; // 矩形的编号
    bool status = false; // 检测框结果true/false
    YLT_REFL(
        JrsRect,
        cx,
        cy,
        width,
        height,
        angle,
        id,
        status
    )
        JrsRect() : cx(0), cy(0), width(0), height(0), angle(0), id(-1) {}
    // angle=0的构造函数
    JrsRect(float x, float y, float width, float height, int id_ = -1, bool status_ = false) : cx(x), cy(y), width(width), height(height), angle(0), id(id_), status(status_) {}
    // 带角度构造函数
    JrsRect(float x, float y, float width, float height, float angle, int id_ = -1, bool status_ = false) : cx(x), cy(y), width(width), height(height), angle(angle), id(id_), status(status_) {}
    // 拷贝构造函数
    JrsRect(const JrsRect& rect) : cx(rect.cx), cy(rect.cy), width(rect.width), height(rect.height), angle(rect.angle), id(rect.id), status(rect.status) {}
    // 移动构造函数
    JrsRect(JrsRect&& rect) noexcept : cx(rect.cx), cy(rect.cy), width(rect.width), height(rect.height), angle(rect.angle), id(rect.id), status(rect.status) {}
    // 析构函数
    ~JrsRect() = default;
    // 重载赋值运算符
    JrsRect& operator=(const JrsRect& rect)
    {
        if (this == &rect)
        {
            return *this;
        }
        cx = rect.cx;
        cy = rect.cy;
        width = rect.width;
        height = rect.height;
        angle = rect.angle;
        id = rect.id;
        status = rect.status;
        return *this;
    }
    // 重载移动赋值运算符
    JrsRect& operator=(JrsRect&& rect) noexcept
    {
        if (this == &rect)
        {
            return *this;
        }
        cx = rect.cx;
        cy = rect.cy;
        width = rect.width;
        height = rect.height;
        angle = rect.angle;
        id = rect.id;
        status = rect.status;
        return *this;
    }
    // 重载==运算符
    bool operator==(const JrsRect& rect) const
    {
        return cx == rect.cx && cy == rect.cy && width == rect.width && height == rect.height && angle == rect.angle && id == rect.id && status == rect.status;
    }
    // 重载!=运算符
    bool operator!=(const JrsRect& rect) const
    {
        return cx != rect.cx || cy != rect.cy || width != rect.width || height != rect.height || angle != rect.angle || id != rect.id || status != rect.status;
    }
    void SetStatus(bool status_)
    {
        status = status_;
    }
    // 空判断
    bool Empty() const
    {
        return cx == 0 && cy == 0 && width == 0 && height == 0 && angle == 0;
    }
    // 转换为cv::Rect float安全转换为int
    cv::Rect ToCvRect() const
    {
        return cv::Rect(static_cast<int>(cx - width / 2), static_cast<int>(cy - height / 2), static_cast<int>(width), static_cast<int>(height));
    }
    // 转为 cv::Rect2f
    cv::Rect2f ToCvRect2f() const
    {
        return cv::Rect2f(cx - width / 2, cy - height / 2, width, height);
    }

    //转为 cv::Rect2f带角度
    cv::Rect2f ToCvRect2fWithAngle() const
    {
        cv::RotatedRect rotate_rect(cv::Point2f(cx, cy), cv::Size2f(width, height), angle);
        cv::Point2f rotate_points[4];
        rotate_rect.points(rotate_points);
        return cv::Rect2f(rotate_points[0].x, rotate_points[0].y, rotate_points[2].x - rotate_points[0].x, rotate_points[2].y - rotate_points[0].y);
    }

    // 转换为cv::RotatedRect 
    cv::RotatedRect ToCvRotatedRect() const
    {
        return cv::RotatedRect(cv::Point2f(cx, cy), cv::Size2f(width, height), angle);
    }
    // 从cv::Rect转换
    static JrsRect FromCvRect(const cv::Rect& rect, int id = -1, bool status = false)
    {
        return JrsRect(rect.x + rect.width / 2.f, rect.y + rect.height / 2.f, (float)rect.width, (float)rect.height, id, status);
    }
    // 从cv::Rect2f转换
    static JrsRect FromCvRect2f(const cv::Rect2f& rect, int id = -1, bool status = false)
    {
        return JrsRect(rect.x + rect.width / 2, rect.y + rect.height / 2, (float)rect.width, (float)rect.height, id, status);
    }
    // 从cv::RotatedRect转换
    static JrsRect FromCvRotatedRect(const cv::RotatedRect& rect, int id = -1, bool status = false)
    {
        return JrsRect(rect.center.x, rect.center.y, rect.size.width, rect.size.height, rect.angle, id, status);
    }
};
using JrsRects = std::vector<JrsRect>;
using JrsPoints = std::vector<JrsVec3f>;


#endif // __ALGOGEOMETRYDATA_H__
