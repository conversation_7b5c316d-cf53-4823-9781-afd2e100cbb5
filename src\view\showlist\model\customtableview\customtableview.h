/*****************************************************************//**
 * @file   customtableview.h
 * @brief  自定义TableView
 *
 * <AUTHOR>
 * @date   2024.9.25
 *********************************************************************/
#ifndef CUSTOMTABLEVIEW_H
#define CUSTOMTABLEVIEW_H

#include <QTableView>
#include <QMouseEvent>
#include <QModelIndex>
#include <QWidget>
#include <QKeyEvent>

class CustomTableView : public QTableView
{
    Q_OBJECT

public:
    /**
     * @fun CustomTableView::CustomTableView
     * @brief 构造函数，初始化 CustomTableView。
     * @param parent 父窗口部件。
     * @date 2025.02.25
     * <AUTHOR>
     */
    CustomTableView(QWidget* parent);
    /**
     * @fun CustomTableView::~CustomTableView
     * @brief 析构函数，释放 CustomTableView 相关资源。
     * @date 2025.02.25
     * <AUTHOR>
     */
    ~CustomTableView();
signals:
    /**
     * @fun CustomLeftClicked
     * @brief 点击左键信号
     * @param
     * @date 2024.9.24
     * <AUTHOR>
     */
    void CustomLeftClicked(const QModelIndex&);
    /**
     * @fun CustomRightClicked
     * @brief 点击右键信号
     * @param
     * @date 2024.9.24
     * <AUTHOR>
     */
    void CustomRightClicked(const QModelIndex&);
    /**
     * @fun CustomUpClicked
     * @brief 向上信号
     * @date 2024.9.24
     * <AUTHOR>
     */
    void CustomUpClicked();
    /**
     * @fun CustomDownClicked
     * @brief 向下信号
     * @date 2024.9.24
     * <AUTHOR>
     */
    void CustomDownClicked();
protected:

    /**
     * @fun CustomTableView::mousePressEvent
     * @brief 处理鼠标点击事件。
     * @details 当用户点击表格时，根据点击位置和鼠标按钮类型触发自定义信号。
     *          如果点击空白区域，则不触发任何信号。
     * @param event 鼠标事件对象。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void mousePressEvent(QMouseEvent* event);
    /**
     * @fun CustomTableView::keyPressEvent
     * @brief 处理键盘按键事件。
     * @details 当用户按下上下箭头键时，触发自定义信号。
     * @param event 键盘事件对象。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void keyPressEvent(QKeyEvent* event);
};
#endif