<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TrackStatus</class>
 <widget class="QWidget" name="TrackStatus">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>495</width>
    <height>110</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>110</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>110</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true">border-color: rgb(0, 85, 255);</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="track_name">
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>60</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>轨道1</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_mode">
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>60</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>在线模式</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_direction">
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>60</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>左进右出</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,4,1">
        <property name="spacing">
         <number>3</number>
        </property>
        <item>
         <widget class="QLabel" name="label">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>35</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>前站</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <property name="spacing">
           <number>3</number>
          </property>
          <item>
           <widget class="QLabel" name="track_cylinder_top">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>6</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>6</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(194, 194, 194);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="spacing">
             <number>3</number>
            </property>
            <item>
             <widget class="QLabel" name="track_import">
              <property name="font">
               <font>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color:transparent;</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="track_shield_left">
              <property name="maximumSize">
               <size>
                <width>12</width>
                <height>25</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(194, 194, 194);</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="track_slow">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color:transparent;</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="track_stop">
              <property name="font">
               <font>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color:transparent;</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="track_shield_right">
              <property name="maximumSize">
               <size>
                <width>12</width>
                <height>25</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(194, 194, 194);</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="track_export">
              <property name="font">
               <font>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color:transparent;</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="track_cylinder_bottom">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>6</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>6</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(194, 194, 194);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QLabel" name="label_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>35</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>后站</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,1,1,1,1">
        <property name="spacing">
         <number>3</number>
        </property>
        <property name="sizeConstraint">
         <enum>QLayout::SetDefaultConstraint</enum>
        </property>
        <item>
         <widget class="QLabel" name="track_askboard_front">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>有板</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_ask_board">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>要板</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_10">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>板宽:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_board_width">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="text">
           <string>300mm</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_has_board">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>出板</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="track_askboard_back">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>20</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>要板</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
