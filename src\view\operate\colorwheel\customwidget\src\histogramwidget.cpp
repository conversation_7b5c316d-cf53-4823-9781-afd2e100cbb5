#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "histogramwidget.h"
#pragma warning(pop)


CustomPlotWidget::CustomPlotWidget(int hist_bin,
    QColor histogram_color, QWidget* parent)
    : QCustomPlot(parent), histogram_color_(histogram_color),
    hist_bin_(hist_bin)
{
    addGraph();
    this->yAxis->ticker()->setTickCount(1);
    this->setContentsMargins(QMargins(0, 0, 0, 0));

    lower_limit_line = new QCPItemStraightLine(this);
    upper_limit_line = new QCPItemStraightLine(this);

    lower_limit_line->point1->setCoords(min_thre_, 0);
    lower_limit_line->point2->setCoords(min_thre_, 1);
    lower_limit_line->setPen(QPen(Qt::darkYellow));

    upper_limit_line->point1->setCoords(max_thre_, 0);
    upper_limit_line->point2->setCoords(max_thre_, 1);
    upper_limit_line->setPen(QPen(Qt::darkYellow));

    fillRect = new QCPItemRect(this);
    fillRect->topLeft->setCoords(min_thre_, 0);
    fillRect->bottomRight->setCoords(max_thre_, 0);
    fillRect->setBrush(QBrush(QColor(0, 0, 255, 100)));

    // Replot to show changes
    replot();
}

int CustomPlotWidget::SetHistValue(const std::vector<float>& hist)
{
    if (hist.empty()) return -1;
    hist_ = hist;
    QVector<double> x(hist_bin_), y(hist_bin_);
    if (hist_.empty())return -1;
    for (int i = 0; i < hist_bin_ - 1; ++i)
    {
        if (i >= hist_.size()) break;
        x[i] = i;
        y[i] = hist_[i];
    }


    graph(0)->setData(x, y);
    graph(0)->setPen(QPen(histogram_color_));
    rescaleAxes();
    replot();

    return 0;
}



int CustomPlotWidget::SetHistThre(int min_thre, int max_thre)
{
    min_thre_ = min_thre;
    max_thre_ = max_thre;
    if (min_max_value_is_init)
    {
        if (min_thre_ < 0)
        {
            min_thre_copy_ = min_thre_;
        }
        if (max_thre_copy_ * 5 < max_thre_)
        {
            max_thre_copy_ = max_thre_;
        }
        min_max_value_is_init = false;
    }

    lower_limit_line->point1->setCoords(min_thre_, lower_limit_line->point1->coords().y());
    lower_limit_line->point2->setCoords(min_thre_, lower_limit_line->point2->coords().y());
    upper_limit_line->point1->setCoords(max_thre_, upper_limit_line->point1->coords().y());
    upper_limit_line->point2->setCoords(max_thre_, upper_limit_line->point2->coords().y());
    replot();
    return 0;
}

int CustomPlotWidget::SetHistBin(int bin)
{
    hist_bin_ = bin;
    return 0;
}

void CustomPlotWidget::mousePressEvent(QMouseEvent* event)
{
    double x = xAxis->pixelToCoord(event->pos().x());
    bool mouse_is_closer_to_lower = (x - min_thre_) < (max_thre_ - x) ?
        true : false;
    if (mouse_is_closer_to_lower)
    {
        dragging_lower_line = true;
        min_thre_ = int(x);
        if (min_thre_ > max_thre_) return;
        lower_limit_line->point1->setCoords(min_thre_, lower_limit_line->point1->coords().y());
        lower_limit_line->point2->setCoords(min_thre_, lower_limit_line->point2->coords().y());
    }
    else
    {
        dragging_upper_line = true;
        max_thre_ = int(x);
        if (min_thre_ > max_thre_) return;
        upper_limit_line->point1->setCoords(max_thre_, upper_limit_line->point1->coords().y());
        upper_limit_line->point2->setCoords(max_thre_, upper_limit_line->point2->coords().y());
    }
    replot();
    emit UpdateThre(min_thre_, max_thre_);
}

void CustomPlotWidget::mouseMoveEvent(QMouseEvent* event)
{
    if (dragging_lower_line)
    {
        min_thre_ = int(xAxis->pixelToCoord(event->pos().x()));
        if (min_thre_ > max_thre_) return;
        lower_limit_line->point1->setCoords(min_thre_, lower_limit_line->point1->coords().y());
        lower_limit_line->point2->setCoords(min_thre_, lower_limit_line->point2->coords().y());
        replot();
        emit UpdateThre(min_thre_, max_thre_);
    }
    else if (dragging_upper_line)
    {
        max_thre_ = int(xAxis->pixelToCoord(event->pos().x()));
        if (min_thre_ > max_thre_) return;
        upper_limit_line->point1->setCoords(max_thre_, upper_limit_line->point1->coords().y());
        upper_limit_line->point2->setCoords(max_thre_, upper_limit_line->point2->coords().y());
        replot();
        emit UpdateThre(min_thre_, max_thre_);
    }

}

void CustomPlotWidget::mouseReleaseEvent(QMouseEvent* event)
{
    Q_UNUSED(event);
    dragging_lower_line = false;
    dragging_upper_line = false;
}

void CustomPlotWidget::mouseDoubleClickEvent(QMouseEvent* /*event*/)
{
    min_thre_ = min_thre_copy_;
    max_thre_ = max_thre_copy_;
    lower_limit_line->point1->setCoords(min_thre_copy_, lower_limit_line->point1->coords().y());
    lower_limit_line->point2->setCoords(min_thre_copy_, lower_limit_line->point2->coords().y());
    upper_limit_line->point1->setCoords(max_thre_copy_, upper_limit_line->point1->coords().y());
    upper_limit_line->point2->setCoords(max_thre_copy_, upper_limit_line->point2->coords().y());
    replot();
    emit UpdateThre(min_thre_, max_thre_);

}
