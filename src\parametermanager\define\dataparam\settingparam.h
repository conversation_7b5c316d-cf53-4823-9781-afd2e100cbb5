﻿
#pragma once
#include "parambase.hpp"
#include "dbparam.h"
#include "fileoperation.h"
namespace jrsdata
{
    struct SystemStateParam : public ViewParamBase
    {
        enum class SystemItem
        {
            PARAM_STATE = 0x0001,
            MOTION_TRACK_STATE, /**< 运控轨道状态  */
            MOTION_AXIS_STATE,  /**< 运控轴状态    */
            STRUCT_LIGHT_STATE, /**< 结构光状态    */
        };
        enum class StateLevel
        {
            OK = 0x00,
            WARNING = 0x02,
            ERR = 0x01,
        };
        struct StateLevelInfo
        {
            StateLevel level;   /**<状态级别*/
            std::string description; /**<状态描述*/
        };
        std::unordered_map<SystemItem, StateLevelInfo> current_system_states;
    };
    //JRSREFLECTION(SystemStateParam, current_system_states);
    using SystemStateParamPtr = std::shared_ptr<SystemStateParam>;
    /**< 机台检测项检测参数 */
    struct MachineCheckParamInfo
    {
        enum class CheckState
        {
            OK = 0,
            CHECKING = 1,
            FIALED = 2,
            WAITTING = 3,
        };
        std::string item_name;
        CheckState check_state;
        int code;/**<返回错误码：OK => jrscore::AOI_OK*/
        std::string err_info; /**code =-1, 则启用该字段*/
        std::string description;/**<详细信息*/
        MachineCheckParamInfo(std::string item_name_ = "", CheckState check_state_ = CheckState::WAITTING, const int code_ = jrscore::AOI_OK,
            const std::string& err_info_ = "", const std::string& description_ = "")
            :item_name(item_name_), check_state(check_state_), code(code_), err_info(err_info_), description(description_)
        {
        }
    };
    using SystemStateMap = std::unordered_map<std::string, jrsdata::MachineCheckParamInfo>;
    /** 参数等级 */
    enum class ParamLevel
    {
        SYSTEM = 0,/**< 系统级参数 需要重启*/
        MACHINE, /**< 机台及参数-热修改*/
    };
    /**< 参数 */
    struct SettingParam
    {
        std::string param_name;  /*< 参数名称 */
        std::string param_type;  /*< 参数类型 */
        std::string param_value; /*< 参数值   */
        std::string param_exp;   /*< 参数解释 */
        int param_level; /*< 参数等级 */
        SettingParam()
            : param_name(""), param_type(""), param_value(""), param_exp(""), param_level(0)
        {
        }
        SettingParam(std::string param_name_, std::string param_type_, std::string param_value_, std::string param_exp_, int param_level_)
            : param_name(param_name_), param_type(param_type_), param_value(param_value_), param_exp(param_exp_), param_level(param_level_)
        {
        }
    };
    using SettingParamMap = std::unordered_map<std::string, SettingParam>;
    using AllSettingParamMap = std::unordered_map<ParamLevel, SettingParamMap>;
    using SettingParamVec = std::vector<SettingParam>;
    JRSREFLECTION(SettingParam, param_name, param_type, param_value, param_exp);

    struct SystemParam :public DataBase /**< 系统参数*/
    {
        SettingParamMap sys_params;
        //SettingParamVec sys_params;     /**< 系统参数 */
        SystemParam()
            :sys_params({})
        {
            this->file_param.file_name = "system_param_setting.json";
            this->file_param.file_path = jtools::FileOperation::GetCurrentWorkingDirectory()
                + "/config/paramsetting/systemparam/";
            this->file_param.file_type = jrsdata::FileType::JSON;
            this->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        }
    };
    JRSREFLECTION(SystemParam, sys_params);

    struct CommonParam :public DataBase
    {
        SettingParamMap comm_params;     /**< 项目参数指针 */
        CommonParam()
            :comm_params({})
        {
            this->file_param.file_name = "common_param_setting.json";
            this->file_param.file_path = jtools::FileOperation::GetCurrentWorkingDirectory()
                + "/config/paramsetting/commonparam/";
            this->file_param.file_type = jrsdata::FileType::JSON;
            this->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        }
    };
    JRSREFLECTION(CommonParam, comm_params);
    /**< 机台参数 */
    struct MachineParam :public DataBase
    {
        std::string event_name; /**<机台参数事件*/
        //SettingParamMap machine_params_seting;
        SettingParamMap machine_params_data;
        SystemStateParam sys_state; /**<系统状态参数*/
        MachineParam()
            :machine_params_data({})
        {
            this->file_param.file_name = "machine_param_setting.json";
            this->file_param.file_path = jtools::FileOperation::GetCurrentWorkingDirectory()
                + "/config/paramsetting/machineparam/";
            this->file_param.file_type = jrsdata::FileType::JSON;
            this->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        }
    };
    JRSREFLECTION(MachineParam, /*machine_params_seting,*/ machine_params_data);

}
namespace jrscheckitem
{
    const char CHECK_ALL_ITEMS[] = "checke_all_items";
    const char TRACK_CHECK_ITEM[] = "track_reset_item";
    const char MOTION_CHECK_ITEM[] = "motion_reset_item";
    const char CAMERA_CHECK_ITEM[] = "camera_reset_item";
    const char LIGHT_SOURCE_ITEM[] = "light_source_reset_item";
    const char CALIBRATION_CHECK_ITEM[] = "calibration_load_item";
    const char TWO_DIMESION_IMAGE_CHECK_ITEM[] = "two_dimesion_image_item";
    const char TREE_DIMETION_IMAGE_CHECK_ITEM[] = "tree_dimetion_image_item";
    const char DATABASE_CHECK_ITEM[] = "database_check_item";
    const char SYSTEM_PARAM_CHECK_ITEM[] = "system_param_check_item";
}

namespace jrssettingparam
{
    namespace jrssysparam
    {
        /** Warning 加完之后须在断言中添加，为了检测是否有重复字符串命名 */
        constexpr char SYSTEM_PARAM_DEMO[] = "sys_param_demo";   /**< 系统参数demo*/
        constexpr char SYSTEM_MOTION_PATH[] = "sys_motion_path"; /**< 运控配置文件地址*/
    };
    namespace jrscommonparam
    {
        constexpr char COMMON_PARAM_DEMO[] = "common_param_demo";     /**< 普通参数demo*/
        constexpr char COMMON_PROJECT_PATH[] = "common_project_path"; /**< 项目路径*/
        constexpr char COMMON_DB_IP[] = "common_db_ip";                   /**< 数据库ip*/
        constexpr char COMMON_DB_NAME[] = "common_db_name";               /**< 数据库名称*/
        constexpr char COMMON_DB_USER[] = "common_db_user";               /**< 数据库用户名*/
        constexpr char COMMON_DB_PASSWORD[] = "common_db_password";       /**< 数据库密码*/
        constexpr char COMMON_DB_CONNECT_NUM[] = "common_db_connect_num"; /**< 数据库链接数*/
    }
    namespace jrsmachineparam
    {
        constexpr char MACHINE_PARAM_PROJECT_PATH[] = "machine_param_project_path";                             /**< 工程路径    */
        constexpr char MACHINE_PARAM_ENTIRTY_IMAGE_PATH[] = "machine_param_entirty_image_parh";                 /**< 整板图片路径*/
        constexpr char MACHINE_PARAM_COMPONENTS_PATH[] = "machine_param_components_parh";                       /**< 元件库路径  */
        constexpr char MACHINE_PARAM_REPAIR_PATH[] = "machine_param_repair_path";                               /**< 维修站路径  */
        constexpr char MACHINE_PARAM_TEST_BIG_IMAGE_PATH[] = "machine_param_test_big_image_path";               /**< 测试大图路径*/
        constexpr char MACHINE_PARAM_LOCATION_POINT_IMAGE_PATH[] = "machine_param_location_point_image_path";   /**< 定位点图片  */
        constexpr char MACHINE_PARAM_ALGORITHM_RESULT_PATH[] = "machine_param_algorithm_result_path";           /**< 算法结果路径*/
        constexpr char MACHINE_PARAM_REAL_TIME_TAKE_IMAGE_PATH[] = "machine_param_real_time_take_image_path";   /**< 实时彩图路径*/
        constexpr char MACHINE_PARAM_ORIGINAL_IMAGE_PATH[] = "machine_param_original_image_path";               /**< 原始图片路径*/
        constexpr char MACHINE_PARAM_REPAIR_IP[] = "machine_param_repair_ip";                                   /**< 复盘站ip*/
        constexpr char MACHINE_PARAM_REPAIR_USER[] = "machine_param_repair_user";                               /**< 复盘站用户名*/
        constexpr char MACHINE_PARAM_REPAIR_PASSWORD[] = "machine_param_repair_password";                       /**< 复盘站密码*/
        constexpr char MACHINE_PARAM_REPAIR_DATABASE_NAME[] = "machine_param_repair_database_name";             /**< 复盘站密码*/
        //constexpr char MACHINE_PARAM_IMAGE_INDEX_REPAIR[] = "machine_param_image_index_repair";               /**< 复盘站图片*/
        //constexpr char MACHINE_PARAM_IMAGE_INDEX_TEST[] = "machine_param_image_index_test";                   /**< */
        constexpr char MACHINE_PARAM_INIT_RESULAT[] = "machine_param_system_state";                             /**< 系统状态 */
        constexpr char MACHINE_PARAM_COMPRESS_IMG_RATE[] = "machine_param_compress_img_rate";                   /**< 缩略图压缩比例 */
        constexpr char MACHINE_PARAM_COMPRESS_IMG_TYPE[] = "machine_param_compress_img_type";                   /**< 缩略图保存格式 */
        constexpr char MACHINE_PARAM_ENTIRETY_IMG_TYPE[] = "machine_param_entirety_img_type";                   /**< 大图保存类型 */

        constexpr char MACHINE_PARAM_MULTIALGO_LIST[] = "machine_param_multialgo_list";                         /**< 自动加框算法集合 */

        constexpr char MACHINE_PARAM_ALGO_RATIO_LIST[] = "machine_param_algo_ratio_list";                         /**< 算法参数比例系数 */

        /***<类型模板---HJC 2025/5/7*/
        constexpr char MACHINE_PARAM_NAME_TYPE[] = "machine_param_name_type";                                   /**< 名字模板 */
        constexpr char MACHINE_PARAM_TEST_INT[] = "machine_param_test_int";                                     /**< 名字模板 */

        constexpr char MACHINE_PARAM_PATH_PLANNING_INT[] = "machine_param_path_planning_int";                                       /**< 路径规划 标准模式=0 优化路径=1 */
        constexpr char MACHINE_PARAM_PATH_PATTERN_INT[] = "machine_param_path_pattern_int";                                         /**< 轨迹模式 X轴优先-S型 =0 Y轴优先-S型=1 X轴优先Z型 =2 Y轴优先Z型 = 3*/
        constexpr char MACHINE_PARAM_BARCODE_INT[] = "machine_param_barcode_int";                                                   /**< 条码 优先识别=0 同元件识别=1 */
        constexpr char MACHINE_PARAM_BAD_BOARD_MARK_INT[] = "machine_param_bad_board_mark_int";                                     /**< 坏板标记 优先识别=0 同元件识别=1 */
        constexpr char MACHINE_PARAM_SUB_BOARD_POSITIONING_POINT_INT[] = "machine_param_sub_board_positioning_point_int";           /**< 子板定位点 优先识别=0 同元件识别=1 */
        constexpr char MACHINE_PARAM_INSPECTION_AREA_INT[] = "machine_param_inspection_area_int";                                   /**< 检测区域 扫图区域=0 子板区域=1 手动设置区域=2 */
        constexpr char MACHINE_PARAM_BAD_BOARD_SETTING_INT[] = "machine_param_bad_board_setting_int";                               /**< 坏板设置 百分比动态屏=0 手动指定=1 坏板标记=2 */
        constexpr char MACHINE_PARAM_ENABLE_REJECTION_TEST_INT[] = "machine_param_enable_rejection_test_int";                       /**< 启用抛料测试 checkbox true = 1 false = 0 */
        constexpr char MACHINE_PARAM_MINIMUM_HEIGHT_FLOAT[] = "machine_param_minimum_height_float";                                 /**< 最小高度 float数值*/
        constexpr char MACHINE_PARAM_MINIMUM_AREA_FLOAT[] = "machine_param_minimum_area_float";                                     /**< 最小面积 float数值*/
        constexpr char MACHINE_PARAM_MASK_EXPANSION_FLOAT[] = "machine_param_mask_expansion_float";                                 /**< 遮罩外扩 float数值*/
        constexpr char MACHINE_PARAM_PRODUCT_SWITCH_INT[] = "machine_param_product_switch_int";                                     /**< 产品切换 绿板=0 红板=1 篮板=2*/
        constexpr char MACHINE_PARAM_PERCENTAGE_DYNAMIC_SHIELDING_FLOAT[] = "machine_param_percentage_dynamic_shielding_float";     /**< 百分比动态屏蔽 float数值*/


        constexpr char MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL[] = "machine_param_is_save_all_detect_data_bool"; /**< 是否保存所有检测的数据 */



        constexpr char MACHINE_PARAM_COMPANY_NO[] = "machine_param_company_no";                                     /**< 工单信息 */
        constexpr char MACHINE_PARAM_MACHINE_NO[] = "machine_param_machine_no";                                     /**< 站点编号 */
        constexpr char MACHINE_PARAM_SITE_NO[] = "machine_param_site_no";                                           /**< 设备ID */
        constexpr char MACHINE_PARAM_THREAD_NO[] = "machine_param_thread_no";                                       /**< 线体ID */




    }

    template<typename T, typename... Ts>
    constexpr bool CheckUnique([[maybe_unused]] T&& t, Ts&&... ts)
    {
        if constexpr (sizeof...(ts) == 0)
        {
            return true;
        }
        else
        {
            return ((!std::is_same_v<std::remove_reference_t<T>, std::remove_reference_t<Ts>> && ...) && CheckUnique(std::forward<Ts>(ts)...));
        }
    }
    //static_assert(CheckUnique(
    //    jrssysparam::SYSTEM_PARAM_DEMO,
    //    jrssysparam::SYSTEM_MOTION_PATH,
    //    jrscommonparam::COMMON_PARAM_DEMO,
    //    jrscommonparam::COMMON_PROJECT_PATH
    //), "Constants in jrssystemparam  must be unique！");
};