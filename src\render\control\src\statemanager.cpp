﻿#include "statemanager.h"

#include "commandmanager.h"
#include "customcursormanager.h"
#include "graphicsmanager.h"
#include "renderer2dmanager.h"

#include "commandobject.h"
#include "customgraphicsobject.h"
#include "eventparam.hpp"
#include "graphicsobject.h"
#include "renderer.h"        // Renderer
// #include "windowinterface.h" // RenderType
#include "engineconstants.hpp" // RenderType
#include "log.h"             // printinfo
#include "systemmonitor.hpp" // key

//#include <QObject>   // connect
#include <QShortcut> // QShortcut
#include <string>
#include "graphicsalgorithm.h"

//#include <QDebug>
StateManager::StateManager()
    : m_graphicsmanager(std::make_shared<GraphicsManager>())
    , m_renderer2dmanager(new Renderer2DManager())
    , m_cmdmanager(new CommandManager())
    , m_cursormanager(new CustomCursorManager())
    // ,m_camerastate(0)
    , m_state(VisionMode::NONE)
    , _last_state(VisionMode::NONE)
    , m_createmode(CreateGraphicsMode::NONE)
    , m_wheelmode(WheelMode::VIEW_CENTER)
    , _is_update_select_object(true)
{
    RegisterSlot();
    RegisterShortcut();
    InitDefaultAttriute();
    ResetState(); // 这里调用一次是为了执行回调
}

StateManager::~StateManager()
{
    if (m_renderer2dmanager)
    {
        delete m_renderer2dmanager;
        m_renderer2dmanager = nullptr;
    }
    if (m_cmdmanager)
    {
        delete m_cmdmanager;
        m_cmdmanager = nullptr;
    }
    if (m_cursormanager)
    {
        delete m_cursormanager;
        m_cursormanager = nullptr;
    }
}

QWidget* StateManager::GetWidget()
{
    return GetRenderer2DManager()->GetWidget();
}

void StateManager::Undo()
{
    GetCommandManager()->Undo();
    Update();
}

void StateManager::Redo()
{
    GetCommandManager()->Redo();
    Update();
}

void StateManager::Update()
{
    // GetGraphicsManager()->Update();
    GetRenderer2DManager()->Update(); // 更新渲染,图形管理器属于渲染中的一个渲染对象
}

void StateManager::Clear()
{
    ClearGraphics(true);
    ClearTexture(0);
}

void StateManager::SetState(const int& state)
{
    SetState(VisionMode(state));
}

void StateManager::SetState(const VisionMode& state)
{
    if (GetState() == state)
    {
        return;
    }
    auto current_state = GetState();
    if (state == VisionMode::RESPONSE_GRAPHICS || state == VisionMode::MANUAL_CREATE_GRAPHICS || state == VisionMode::HOVER)
    {
        _last_state = state;
    }
    //qDebug() << "[" << __FUNCTION__ << "][" << __LINE__ << "]:" << QString::fromStdString(VisionModeToString(current_state)) << " To " << QString::fromStdString(VisionModeToString(state));
    OutState(current_state);
    InState(state);
}

void StateManager::ResetState()
{
    SetState(VisionMode::HOVER);
}

void StateManager::BackLastState()
{
    SetState(_last_state);
    //_last_state = VisionMode::NONE;
}

void StateManager::SetCreateGraphicsMode(const CreateGraphicsMode& mode)
{
    m_createmode = mode;
    if (mode != CreateGraphicsMode::NONE)
    {
        SetState(VisionMode::CREATE_GRAPHICS);
    }
}

void StateManager::SetSelectGraphicsMode(const int& mode)
{
    GetGraphicsManager()->SetSelectGraphicsMode(mode);
}

int StateManager::GetSelectGraphicsMode() const
{
    return GetGraphicsManager()->GetSelectGraphicsMode();
}

void StateManager::SetShowDebugInfo(bool state)
{
    return GetRenderer2DManager()->SetShowDebugInfo(state);
}

void StateManager::SetShowCenterCrossLine(bool state)
{
    return GetRenderer2DManager()->SetShowCenterCrossLine(state);
}

int StateManager::SetThumbnailShow()
{
    const auto& renderer2dmanager = GetRenderer2DManager();
    renderer2dmanager->SetThumbnailShow(!renderer2dmanager->GetThumbnailShow());
    return 0;
}

int StateManager::SetThumbnailNavigation(const cv::Mat& image, int image_true_w, int image_true_h)
{
    return GetRenderer2DManager()->SetThumbnailNavigation(image, image_true_w, image_true_h) ? 0 : 1;
}

int StateManager::SetCanvasSize(int width, int height)
{
    return GetRenderer2DManager()->SetCanvasSize(width, height) ? 0 : 1;
}

int StateManager::GetCanvasSize(int& width, int& height)
{
    return GetRenderer2DManager()->GetCanvasSize(width, height) ? 0 : 1;
}

void StateManager::SetRulerDisplayScale(double scale)
{
    GetRenderer2DManager()->SetRulerScale(scale);
}

void StateManager::SetRulerPrecision(int precision)
{
    GetRenderer2DManager()->SetRulerPrecision(precision);
}

void StateManager::ClearTexture(int z)
{
    GetRenderer2DManager()->ClearTexture(z);
}

int StateManager::CreateTexture(unsigned int& id, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center)
{
    id = GetRenderer2DManager()->CreateTexture(image, x, y, z, angle, is_draw_center);
    return id != 0 ? 0 : 1;
}

int StateManager::CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center, int current_show_image_key_)
{
    return GetRenderer2DManager()->CreateImage(key_, image, x, y, z, angle, is_draw_center, current_show_image_key_);
}

int StateManager::CreateImages(const GraphicsImage& graphics_img_)
{
    return GetRenderer2DManager()->CreateImages(graphics_img_);
}

int StateManager::AddGraphicsShapes(const GraphicsShape& graphics_shape_)
{
    return GetRenderer2DManager()->AddGraphicsShapes(graphics_shape_);
}

int StateManager::ClearGraphicsShapes()
{
    return GetRenderer2DManager()->ClearGraphicsShapes();
}

int StateManager::ClearImage(const int& set_key_, int key_)
{
    return GetRenderer2DManager()->ClearImage(set_key_, key_);
}

int StateManager::ShowImage(const uint8_t& set_key_, int key_)
{
    return GetRenderer2DManager()->ShowImage(set_key_, key_);
}

int StateManager::SetTextureZ(unsigned int id, int z, bool make_unique)
{
    auto renderer2dmanager = GetRenderer2DManager();

    if (make_unique)
    {
        int cz = 0;
        auto state = renderer2dmanager->FindTextureZ(cz, id); // 查找目标纹理的z值
        if (state != 0 || cz == z)
        {
            return state;
        }
        std::vector<unsigned int> ids;
        state = renderer2dmanager->FindZTexture(ids, z); // 查找目标z值下的纹理
        if (state == 0)
        {
            state = renderer2dmanager->SetTextureZ(cz, ids); // 将目标z值下的纹理移动到目标纹理的z值
            if (state != 0)
            {
                return state;
            }
        }
    }
    auto state = renderer2dmanager->SetTextureZ(z, { id }); // 将目标纹理移动到目标z值
    if (state != 0)
    {
        return state;
    }

    return 0;
}

void StateManager::SetManualDefaultDrawAngle(float angle)
{
    GetGraphicsManager()->SetDrawAngle(angle);
}

float StateManager::GetManualDefaultDrawAngle()
{
    return GetGraphicsManager()->GetDrawAngle();
}

void StateManager::ClearGraphics(bool invoke_callback)
{
    GetGraphicsManager()->DeleteGraphics(invoke_callback);
}

void StateManager::ClearGraphics(const std::string& except_layer, bool invoke_callback)
{
    GetGraphicsManager()->DeleteGraphicsExceptLayer(except_layer, invoke_callback);
}

void StateManager::DeleteGraphicsWithLayer(const std::string& layer, bool invoke_callback_)
{
    GetGraphicsManager()->DeleteGraphics(layer, invoke_callback_);
}

void StateManager::DeletePadGroups()
{
    GetGraphicsManager()->DeletePadGroups();
}

int StateManager::GetAllGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs) const
{
    return GetGraphicsManager()->ReadGraphics(ghs);
}

int StateManager::GetAllGraphics(std::string& str) const
{
    return GetGraphicsManager()->ReadGraphics(str);
}

int StateManager::GetLayerGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer) const
{
    return GetGraphicsManager()->ReadGraphics(ghs, layer);
}

int StateManager::GetCurrentSelectedGraphics(
    std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer)
{
    (void)layer; // TODO
    return GetGraphicsManager()->ReadGraphicsSelected(ghs);
}

int StateManager::GetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer)
{
    (void)layer; // TODO
    return GetGraphicsManager()->ReadSelectedSingleGraphics(gh);
}

void StateManager::SetGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state)
{
    //// TODO 触发选中设置的是temp因此不关联其他事件,需要手动切换状态
    GetGraphicsManager()->SelectGraphics(ghs, state, false);

    if (GetGraphicsManager()->IsHaveSelected())
    {
        SetState(VisionMode::RESPONSE_GRAPHICS);
    }
    else
    {
        // GetGraphicsManager()->ClearSelected();
        ResetState();
    }

    if (ghs.size() == 1 && state)
    {
        MoveCameraTo(ghs[0]);
    }
}

void StateManager::SetGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback)
{
    //GetGraphicsManager()->ClearSelected();
    GetGraphicsManager()->SelectGraphics(gh, true, invoke_callback);
    SetState(VisionMode::RESPONSE_GRAPHICS);

    UpdateLayerShowMessage();
    // MoveCameraTo(gh);  //By :改变位置和大小后，不要刷新居中； 2024/12/13 HJC
}

std::shared_ptr<GraphicsAbstract> StateManager::CreateGraphicsTool(int graphics_type, const std::string& layer,
    const std::string& group_name_, const  std::shared_ptr<GraphicsAbstract>& father_graphics_)
{
    return GetGraphicsManager()->CreateGraphics(static_cast<GraphicsFlag>(graphics_type), layer, group_name_, father_graphics_, false);
}


int StateManager::GetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids) const
{
    if (ids.empty())
        return 1;

    return GetGraphicsManager()->ReadGraphics(ghs, ids);
}

int StateManager::GetGraphics(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id) const
{
    return GetGraphicsManager()->ReadGraphics(gh, id);
}

int StateManager::DeleteGraphics(const std::vector<GraphicsID>& ids)
{
    if (ids.empty())
        return 1;

    GetGraphicsManager()->DeleteGraphics(ids);
    return 0;
}

int StateManager::AddGraphics(
    const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback)
{
    return GetGraphicsManager()->UpdateGraphics(ghs, layer, invoke_callback);
}

int StateManager::AddGraphics(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer, bool invoke_callback)
{
    if (!layer.empty())
    {
        gh->settings.SetLayer(layer);
    }
    // if (layer.empty() && gh->settings.GetLayer().empty())
    // {
    //     gh->settings.SetLayer(GetGraphicsManager()->GetCurrentLayer());
    // }
    // else
    // {
    //     gh->settings.SetLayer(layer);
    // }
    return GetGraphicsManager()->UpdateGraphics(gh, invoke_callback);
}

int StateManager::AddGraphics(const std::string& str, bool invoke_callback)
{
    (void)invoke_callback;
    std::vector<GraphicsPtr> ghs = GetGraphicsManager()->CreateGraphics(str, invoke_callback);
    return !ghs.empty() ? 0 : 1;
}

int StateManager::CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, std::shared_ptr<GraphicsAbstract>& gh, float x, float y)
{
    return GetGraphicsManager()->CopyGraphicsTo(new_gh, gh, x, y);
}

int StateManager::CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, const GraphicsID& id, float x, float y)
{
    std::shared_ptr<GraphicsAbstract> gh;
    auto state = GetGraphicsManager()->ReadGraphics(gh, id);
    if (state != 0)
    {
        return state;
    }
    return GetGraphicsManager()->CopyGraphicsTo(new_gh, gh, x, y);
}

void StateManager::MoveCamera(float xoffset, float yoffset)
{
    GetRenderer2DManager()->MoveCamera(xoffset, yoffset);
}

void StateManager::MoveCamera(int direction)
{
    GetRenderer2DManager()->MoveCamera(direction);
}

void StateManager::ResetCamera(int type)
{
    GetRenderer2DManager()->ResetCamera(type);
}

void StateManager::MoveCameraTo(float x, float y)
{
    GetRenderer2DManager()->MoveCameraTo(x, y);
}

void StateManager::MoveCameraTo(std::shared_ptr<GraphicsAbstract> gh)
{
    if (!gh)
        return;

    GetRenderer2DManager()->MoveCameraTo(gh->x(), gh->y());
}

void StateManager::MoveCameraTo(const std::string& graphicsid)
{
    GraphicsPtr gh;
    auto state = GetGraphicsManager()->ReadGraphics(gh, graphicsid);
    if (state != 0)
    {
        return;
    }
    return MoveCameraTo(gh);
}

void StateManager::SetZoom(float zoom)
{
    GetRenderer2DManager()->SetZoom(zoom);
}

void StateManager::SetZoomState(int state)
{
    GetRenderer2DManager()->SetZoomState(state);
}

int StateManager::SetLimitViewByCanvas(bool state)
{
    return GetRenderer2DManager()->SetLimitViewByCanvas(state) ? 0 : 1;
}

const std::unordered_map<std::string, std::shared_ptr<LayerConfig>>& StateManager::GetAllLayerConfig() const
{
    return GetGraphicsManager()->ReadLayerConfig();
}

void StateManager::AddGraphicsLayerConfig(const std::string& layer,
    std::shared_ptr<LayerConfig> config)
{
    GetGraphicsManager()->UpdateLayerConfig(config, layer);
}

int StateManager::GetLayerConfig(std::weak_ptr<LayerConfig>& result, const std::string& layer) const
{
    result = GetGraphicsManager()->ReadLayerConfig(layer);
    return !result.expired() ? 0 : 1;
}

void StateManager::ClearLayerConfig()
{
    GetGraphicsManager()->DeleteLayerConfig();
}

int StateManager::SetCurrentLayer(const std::string& layer)
{
    if (auto state = GetGraphicsManager()->SetCurrentLayer(layer); state != 0)
    {
        return state;
    }
    GetRenderer2DManager()->SetShowMessage("当前图层: " + layer, -1);
    //SetState(VisionMode::HOVER);
    return 0;
}

std::string StateManager::GetCurrentLayer() const
{
    return GetGraphicsManager()->GetCurrentLayer();
}

void StateManager::UpdateLayerShowMessage() const
{
    GetRenderer2DManager()->SetShowMessage("当前图层: " + GetCurrentLayer(), -1);
}

void StateManager::SetCallbackGraphicscreate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback)
{
    GetGraphicsManager()->SetCallbackGraphicsadd(callback);
}

void StateManager::SetCallbackGraphicsupdate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&, bool)>  callback)
{
    GetGraphicsManager()->SetCallbackGraphicsupdate(callback);
}

void StateManager::SetCallbackGraphicsdelete(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback)
{
    GetGraphicsManager()->SetCallbackGraphicsdelete(callback);
}

void StateManager::SetCallbackGraphicsselected(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)>    callback)
{
    GetGraphicsManager()->SetCallbackGraphicsselected(callback);
}

void StateManager::SetCallbackRegionselected(std::function<void(float x, float y, float w, float h)> callback)
{
    callback_regionselected += callback;
    // GetGraphicsManager()->SetCallbackRegionselected(callback);
}

void StateManager::HandlerMouseGraphicsResponse(const MouseEventValue& value)
{
    GetGraphicsManager()->ResponseEvent(value);
}

void StateManager::HandlerMouseGraphicsAdd(const MouseEventValue& value)
{
    switch (m_createmode)
    {
    case CreateGraphicsMode::RECT:
        AddRect(value);
        break;
    case CreateGraphicsMode::CIRCLE:
        AddCircle(value);
        break;
    case CreateGraphicsMode::POLYGON:
        AddPolygon(value);
        break;
    case CreateGraphicsMode::BEZIER:
        AddBezier(value);
        break;
    case CreateGraphicsMode::SG:
        AddSG(value);
        break;
    case CreateGraphicsMode::MULTI_REGION:
        AddMR(value);
        break;
    case CreateGraphicsMode::PAD:
        AddPad(value);
        break;
    case CreateGraphicsMode::NONE:
        return;
    default:
        printInfo((std::stringstream() << "unknown graphics add"));
    }
    Update();
}

void StateManager::AddRect(const MouseEventValue& value)
{
    [[maybe_unused]] auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;
    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        std::shared_ptr<GraphicsAbstract> gh = std::make_shared<RectGraphics>();
        GetGraphicsManager()->BeginCreate(gh);
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh)
        {
            GetGraphicsManager()->UpdateGraphicsValue(gh, px, py, cx, cy, GetGraphicsManager()->GetDrawAngle());
            // gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
            // gh->SetWH(abs(px - cx), abs(py - cy));
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        GetGraphicsManager()->CommitCreate();
        ResetState();
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
    }
    break;
    default:
        break;
    }
}

void StateManager::AddCircle(const MouseEventValue& value)
{
    [[maybe_unused]] auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;
    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        std::shared_ptr<GraphicsAbstract> gh = std::make_shared<CircleGraphics>();
        // gh->SetValue(0, 0, 0, 0, GetGraphicsManager()->GetDrawAngle());
        GetGraphicsManager()->BeginCreate(gh);
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh)
        {
            gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
            gh->SetWH(abs(px - cx), abs(py - cy));
            // 新增按住shift时，绘制正圆
            if (GetKeyDown(VK_SHIFT))
            {
                auto r = std::max(abs(px - cx), abs(py - cy));
                gh->SetWH(r, r);
            }
            // GetGraphicsManager()->Draw();
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        GetGraphicsManager()->CommitCreate();
        ResetState();
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
    }
    break;
    default:
        break;
    }
}

void StateManager::AddPolygon(const MouseEventValue& value)
{
    const auto should_type = GraphicsFlag::polygon;
    auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    [[maybe_unused]] auto& px = value.px;
    [[maybe_unused]] auto& py = value.py;
    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
        }
        else
        {
            std::shared_ptr<GraphicsAbstract> tg =
                std::make_shared<PolygonGraphics>();
            GetGraphicsManager()->BeginCreate(tg);
        }
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
            if (!(type & Qt::MouseButton::LeftButton) && tg->contours.size() > 1)
            {
                tg->DeletePoint();
            }

            tg->AddPoint(cx, cy);
            // GetGraphicsManager()->Draw();

          /*  printInfo((std::stringstream() << "temp point " << tg->contours.size())
                .str()
                .c_str());*/
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        if (type & Qt::MouseButton::RightButton)
        {
            // GetGraphicsManager()->CommitCreate();
            ResetState();
        }
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (!gh || gh->GetFlag() != should_type)
        {
            break;
        }

        auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
        if (type & Qt::MouseButton::RightButton)
        {
            tg->DeletePoint();
            tg->ComfirmPoint();
            GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else
        {
            tg->AddPoint(cx, cy);

            /* printInfo(
                 (std::stringstream() << &tg << "add point:" << tg->contours.size())
                 .str()
                 .c_str());*/
        }
    }
    break;
    default:
        break;
    }
}

void StateManager::AddBezier(const MouseEventValue& value)
{
    const auto should_type = GraphicsFlag::Bezier;
    auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;
    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            // GetGraphicsManager()->CancelCreate();
        }
        else
        {
            std::shared_ptr<BezierGraphics> tg = std::make_shared<BezierGraphics>();
            // auto ttg = std::dynamic_pointer_cast<BezierGraphics>(gh);
            tg->SetStart(px, py);
            GetGraphicsManager()->BeginCreate(tg);
        }
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
            if (!(type & Qt::MouseButton::LeftButton))
            {
                // tg->DeletePoint();
                // tg->AddPoint(cx, cy);
            }
            else
            {
                tg->SetEnd(cx, cy, false);
            }

            // GetGraphicsManager()->Draw();

       /*     printInfo((std::stringstream()
                << "control point size:" << tg->controls.size())
                .str()
                .c_str());*/
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        if (type & Qt::MouseButton::RightButton)
        {
            // GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else if (type & Qt::MouseButton::LeftButton)
        {
            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
                tg->SetEnd(cx, cy, true);
            }
        }
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (!gh || gh->GetFlag() != should_type)
        {
            break;
        }

        auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
        if (type & Qt::MouseButton::RightButton)
        {
            tg->ComfirmPoint();
            GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else
        {
            tg->AddPoint(cx, cy);

            // GetGraphicsManager()->Draw();
            // printInfo(
            //     (std::stringstream() << &tg << "add point:" <<
            //     tg->contour.size()) .str() .c_str());
        }
    }
    break;
    default:
        break;
    }
}

void StateManager::AddSG(const MouseEventValue& value)
{
    using GTYPE = SGGraphics;
    const auto should_type = GraphicsFlag::SG;
    auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;

    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            // GetGraphicsManager()->CancelCreate();
        }
        else
        {
            auto tg = std::make_shared<GTYPE>();
            tg->SetStart(px, py);
            GetGraphicsManager()->BeginCreate(tg);
        }
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        if (type & Qt::MouseButton::LeftButton)
        {
            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
                if (tg->ConnectNum() == 0)
                {
                    tg->SetEnd(cx, cy);
                }

                /*printInfo((std::stringstream()
                    << "control point size:" << tg->GetPoint().size()));*/
            }
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        if (type & Qt::MouseButton::RightButton)
        {
            ResetState();
        }
        else if (type & Qt::MouseButton::LeftButton)
        {
            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
                if (tg->ConnectNum() == 0)
                {
                    tg->SetEnd(cx, cy);
                }
            }
        }
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (!gh || gh->GetFlag() != should_type)
        {
            break;
        }

        auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
        if (type & Qt::MouseButton::RightButton)
        {
            tg->ComfirmPoint();
            // tg->ComfirmPoint(tg.get());
            GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else
        {
            tg->AddConnect(cx, cy, false);
        }
    }
    break;
    default:
        break;
    }
}

void StateManager::AddMR(const MouseEventValue& value)
{
    using GTYPE = MultiRegionGraphics;
    const auto should_type = GraphicsFlag::multiregion;
    auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;

    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            // GetGraphicsManager()->CancelCreate();
        }
        else
        {
            auto tg = std::make_shared<GTYPE>();
            tg->region_num = 1;
            GetGraphicsManager()->BeginCreate(tg);
        }
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
            gh->SetWH(abs(px - cx), abs(py - cy));
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        if (type & Qt::MouseButton::LeftButton)
        {
            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
                tg->region_cx = -tg->w() * 0.25f;
                tg->region_cy = 0;
                tg->region_w = tg->w() * 0.25f;
                tg->region_h = tg->h() * 0.5f;
                tg->region_angle = gh->a();
                tg->region_num = 2;
                tg->region_type = 1;
                GetGraphicsManager()->CommitCreate();
                ResetState();
            }
        }
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
    }
    break;
    default:
        break;
    }
}

void StateManager::AddPad(const MouseEventValue& value)
{
   /* using GTYPE = PadGraphics;
    const auto should_type = GraphicsFlag::pad;
    auto& type = value.type;
    auto& cx = value.cx;
    auto& cy = value.cy;
    auto& px = value.px;
    auto& py = value.py;

    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (gh && gh->GetFlag() == should_type)
        {
            // GetGraphicsManager()->CancelCreate();
        }
        else
        {
            auto tg = std::make_shared<GTYPE>();
            GetGraphicsManager()->BeginCreate(tg);
        }
    }
    break;
    case MouseEventValue::MouseState::move:
    {
        if (type & Qt::MouseButton::LeftButton)
        {
            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                gh->SetXY((cx + px) / 2.f, (cy + py) / 2.f);
                gh->SetWH(abs(px - cx), abs(py - cy));
            }
        }
    }
    break;
    case MouseEventValue::MouseState::release:
    {
        if (type & Qt::MouseButton::RightButton)
        {
            GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else if (type & Qt::MouseButton::LeftButton)
        {
            //auto gh = GetGraphicsManager()->GetCreate();
            //if (gh && gh->GetFlag() == should_type)
            //{
            //}
        }
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (!gh || gh->GetFlag() != should_type)
        {
            break;
        }

        auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
        if (type & Qt::MouseButton::RightButton)
        {
            GetGraphicsManager()->CommitCreate();
            ResetState();
        }
        else if (type & Qt::MouseButton::LeftButton)
        {
            tg->array_point = { cx - tg->x() , cy - tg->y() };
        }
    }
    break;
    case MouseEventValue::MouseState::wheel:
    {
        auto gh = GetGraphicsManager()->GetCreate();
        if (!gh || gh->GetFlag() != should_type)
        {
            break;
        }

        auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
        switch (type)
        {
        case 1:
        {
            tg->subnum += 1;
        }
        break;
        case -1:
        {
            tg->subnum = std::max(0, tg->subnum - 1);
        }
        break;
        }
    }
    break;
    default:
        break;
    }
	*/
	    (void)value;
}

void StateManager::AddSelectBatchPolygon(const MouseEventValue& value)
{
    //printInfo("");
    const auto should_type = GraphicsFlag::polygon;
    switch (static_cast<MouseEventValue::MouseState>(value.state))
    {
    case MouseEventValue::MouseState::press:
    case MouseEventValue::MouseState::move:
    case MouseEventValue::MouseState::release:
    {
        AddPolygon(value);
        return;
    }
    break;
    case MouseEventValue::MouseState::clicked:
    {
        if (value.type & Qt::MouseButton::RightButton)
        {

            auto gh = GetGraphicsManager()->GetCreate();
            if (gh && gh->GetFlag() == should_type)
            {
                auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
                GetGraphicsManager()->CancelCreate();
                GetGraphicsManager()->TrySelectGraphics(tg->contours, false);
            }
        }
        else
        {
            AddPolygon(value);
            return;
        }
    }
    break;
    default:
        break;
    }
}

void StateManager::HandlerRendermouseclicked(int type, int x, int y)
{
    //printInfo((std::stringstream() << GetState() << "," << x << "," << y));
    auto r = GetGraphicsManager()->GetRenderer();
    float cx = x;
    float cy = y;
    r->MouseToWorld(cx, cy);
    switch (GetState())
    {
    case VisionMode::CREATE_GRAPHICS:
    {
        if (m_createmode != CreateGraphicsMode::NONE)
        {
            HandlerMouseGraphicsAdd(
                MouseEventValue(static_cast<WheelType>(type), cx, cy, cx, cy, 0,
                    MouseEventValue::MouseState::clicked));
        }
    }
    break;
    case VisionMode::MOVE_ALL_GRAPHICS:
    case VisionMode::MOVE_CAMERA:
        ResetState();
        break;
        //case VisionMode::EDIT_GRAPHICS:
        //    SetState(VisionMode::RESPONSE_GRAPHICS);
        //    break;
        //case VisionMode::RESPONSE_GRAPHICS:
        //    break;

    default:
        break;
    }
    /*在这里处理一部分按键响应*/
    if (type == Qt::LeftButton)   //左键 就是选择和切换元件的
    {
        if (GetKeyDown(VK_CONTROL))
        {
            //SetState(VisionMode::SELECT_GRAPHICS_BATCH);
            //if (GetGraphicsManager()->TrySelectGraphics(cx, cy, false, false))
            //{
            //    //GetRenderer2DManager()->SetShowMessage("当前图层: " + GetGraphicsManager()->GetCurrentLayer(), -1);
            //    SetState(VisionMode::RESPONSE_GRAPHICS);
            //}
        }
        else /*if (GetKeyDown(VK_SHIFT))*/
        {
            SetState(VisionMode::SELECT_GRAPHICS);
            if (GetGraphicsManager()->TrySelectGraphics(cx, cy, false, true))
            {
                //GetRenderer2DManager()->SetShowMessage("当前图层: " + GetGraphicsManager()->GetCurrentLayer(), -1);
                SetState(VisionMode::RESPONSE_GRAPHICS);
            }
            BackLastState();
        }
    }
    else if (type == Qt::MidButton)
    {
        GetGraphicsManager()->ClearSelected();//取消所有选中
        ResetState();
    }

}

void StateManager::HandlerRendermousepress(int type, int x, int y)
{
    //printInfo((std::stringstream() << GetState() << "," << x << "," << y));

    auto r = GetGraphicsManager()->GetRenderer();
    float cx = x;
    float cy = y;
    r->MouseToWorld(cx, cy);
    (void)type;
}

static auto pre_vision_mode = VisionMode::NONE;
void StateManager::HandlerRendermousemove(int type,
    int icx, int icy, int ilx, int ily, int ipx, int ipy)
{

    auto r = GetGraphicsManager()->GetRenderer();

    float lx = ilx;  //最新的坐标点
    float ly = ily;
    r->MouseToWorld(lx, ly);
    float cx = icx;
    float cy = icy;
    r->MouseToWorld(cx, cy);
    float px = ipx;
    float py = ipy;
    r->MouseToWorld(px, py);
    if (pre_vision_mode != GetState())
    {
        /*      printInfo(std::stringstream() << "previous vision_mode:" << pre_vision_mode
                  << "change vision_mdoe:" << GetState());*/
        pre_vision_mode = GetState();
    }

    // printInfo((std::stringstream()
    //            << GetState() << "," << "type:" << type << ","
    //            << "ilx " << ilx << "," << "ily " << ily << ","
    //            << "icx " << icx << "," << "icy " << icy << ","
    //            << "ipx " << ipx << "," << "ipy " << ipy << ",")
    //               .str()
    //               .c_str());
    // printInfo((std::stringstream()
    //     << GetState() << "," << "type:" << type << ","
    //     << "lx " << lx << "," << "ly " << ly << ","
    //     << "cx " << cx << "," << "cy " << cy << ","
    //     << "px " << px << "," << "py " << py << ","));

    GetRenderer2DManager()->HoverColor(icx, icy);
    //GetGraphicsManager()->TrySelectGraphics(cx, cy, false);
    auto current_state = GetState();
    switch (current_state)
    {
    case VisionMode::RESPONSE_GRAPHICS://!编辑图像
    {
        if (!GetKeyDown(VK_CONTROL) && !GetKeyDown(VK_MENU))
        {
            GetCustomCursorManager()->ResetCursor();
            break;
        }
        if (GetGraphicsManager()->TryResponseGraphics(cx, cy, true) == 0 && type == Qt::LeftButton)
        {
            SetState(VisionMode::EDIT_GRAPHICS);
        };
    }
    break;
    case VisionMode::EDIT_GRAPHICS:
        //SetState(VisionMode::RESPONSE_GRAPHICS);
        if (!GetKeyDown(VK_CONTROL) && !GetKeyDown(VK_MENU))
        {
            GetCustomCursorManager()->ResetCursor();
            break;
        }
        if (GetKeyDown(VK_MENU))
        {
            /**<移动元件本体*/
            _is_update_select_object = false;
            GetGraphicsManager()->ResponseParentGraphics(px, py, cx, cy, true);
        }
        else
        {
            /**<移动目标*/
            _is_update_select_object = true;
            GetGraphicsManager()->ResponseGraphics(px, py, cx, cy, true);
        }

        break;
    case VisionMode::CREATE_GRAPHICS:
    {
        HandlerMouseGraphicsAdd(
            MouseEventValue(static_cast<WheelType>(type), cx, cy, px, py, 0,
                MouseEventValue::MouseState::move));

    }
    break;
    case VisionMode::MOVE_ALL_GRAPHICS:
    {
        GetGraphicsManager()->UpdateGraphicsMoveAll(cx - lx, cy - ly, false);
        return;
    }
    break;
    }
    if (type == Qt::LeftButton && !GetKeyDown(VK_MENU) && !GetKeyDown(VK_CONTROL))
    {
        HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
        GetGraphicsManager()->ClearTemporary();
        GetGraphicsManager()->UpdateTemporary(px, py, cx, cy, false);
        if (current_state != VisionMode::CREATE_GRAPHICS
            && current_state != VisionMode::MANUAL_CREATE_GRAPHICS
            //&& current_state != VisionMode::RESPONSE_GRAPHICS
            && current_state != VisionMode::EDIT_GRAPHICS)
        {
            SetState(VisionMode::SELECT_GRAPHICS_BATCH);
        }
    }
    //if (type == Qt::LeftButton /*&& !GetKeyDown(VK_MENU) && !GetKeyDown(VK_CONTROL)*/ /*&& current_state == VisionMode::CREATE_GRAPHICS*/)
    //{

    //}
    //else if (type == Qt::LeftButton /*&& GetKeyDown(VK_CONTROL) && !GetKeyDown(VK_MENU)*/ && current_state != VisionMode::CREATE_GRAPHICS)
    //{

    //    HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
    //    GetGraphicsManager()->ClearTemporary();
    //    GetGraphicsManager()->UpdateTemporary(px, py, cx, cy, true);
    //}
    else if (type == Qt::RightButton) // ! 鼠标右键  默认移动镜头
    {
        MoveCamera(lx - cx, ly - cy);  //! 移动相机
        HandlerCursorchange(static_cast<int>(CustomCursorType::HandClose));
        SetState(VisionMode::MOVE_CAMERA);
    }
}

void StateManager::HandlerRendermouserelease(int type, int icx, int icy,
    int ipx, int ipy)
{
    auto r = GetGraphicsManager()->GetRenderer();

    float cx = icx;
    float cy = icy;
    r->MouseToWorld(cx, cy);
    float px = ipx;
    float py = ipy;
    r->MouseToWorld(px, py);

    //printInfo((std::stringstream()
    //    << GetState() << "," << "type:" << type << ","
    //    << "cx " << cx << ",cy " << cy << "," << "px " << px << ",py "
    //    << py << ","));

    switch (GetState())
    {
    case VisionMode::MOVE_CAMERA:
        //ResetState();
        BackLastState();
        break;
    case VisionMode::SELECT_GRAPHICS:
    {
        callback_regionselected(std::min(px, cx), std::min(py, cy), abs(cx - px), abs(cy - py));
        if (GetGraphicsManager()->IsHaveSelected())
        {
            SetState(VisionMode::RESPONSE_GRAPHICS);

        }
        else
        {
            GetGraphicsManager()->ClearSelected();
            ResetState();
        }
    }
    break;
    case VisionMode::SELECT_GRAPHICS_BATCH:
    {
        callback_regionselected(std::min(px, cx), std::min(py, cy), abs(cx - px), abs(cy - py));
        auto state = GetGraphicsManager()->TrySelectGraphics(px, py, cx, cy, false);
        if (state)
        {
            SetState(VisionMode::RESPONSE_GRAPHICS);
        }
        else
        {
            //GetGraphicsManager()->ClearSelected();
            ResetState();
        }
    }
    break;
    //case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    //{
    //    AddSelectBatchPolygon(
    //        MouseEventValue(type, cx, cy, cx, cy,
    //            MouseEventValue::MouseState::release));
    //}
    //break;
    case VisionMode::EDIT_GRAPHICS:   //编辑事件 to RESPONSE_GRAPHICS
        //if (GetKeyDown(VK_MENU))
        //{
        //    GetGraphicsManager()->ResponseParentGraphics(0, 0, 0, 0, false);
        //}
        //else if (GetKeyDown(VK_CONTROL))
        //{
        //    GetGraphicsManager()->ResponseGraphics(0, 0, 0, 0, false);
        //}
        if (_is_update_select_object)
        {
            GetGraphicsManager()->ResponseGraphics(0, 0, 0, 0, false); /**<更新选中目标*/
        }
        else
        {
            GetGraphicsManager()->ResponseParentGraphics(0, 0, 0, 0, false);  /**<更新选中父级目标*/
        }
        SetState(VisionMode::RESPONSE_GRAPHICS);
        break;
    case VisionMode::CREATE_GRAPHICS:
    {
        HandlerMouseGraphicsAdd(
            MouseEventValue(static_cast<WheelType>(type), cx, cy, px, py, 0,
                MouseEventValue::MouseState::release));
    }
    break;
    case VisionMode::MOVE_ALL_GRAPHICS:
    {
        GetGraphicsManager()->UpdateGraphicsMoveAll(0, 0, true);
        ResetState();
    }
    break;
    case VisionMode::MANUAL_CREATE_GRAPHICS:
    {
        /***< 当前是否有角度，如果有角度，起始点和终点并不一样*/
        auto draw_angle = GetGraphicsManager()->GetDrawAngle();
        auto rotate_rect = GetEffectiveRotateRect(px, py, cx, cy, draw_angle);
        auto start_point_x = rotate_rect.center.x - (rotate_rect.size.width / 2);
        auto start_point_y = rotate_rect.center.y - (rotate_rect.size.height / 2);
        callback_regionselected(/*std::min(px, cx)*/start_point_x, /*std::min(py, cy)*/start_point_y, rotate_rect.size.width, rotate_rect.size.height);
    }
    break;
    default:
        break;
    }

    GetGraphicsManager()->ClearTemporary();
    GetCustomCursorManager()->ResetCursor();

}

void StateManager::HandlerRenderwheeldelta(int delta, int x, int y)
{
    // 滚轮模式

    WheelType wheel_type = (delta >= 120) ? WheelType::ZoomIn
        : (delta <= -120) ? WheelType::ZoomOut
        : WheelType::None;

    // 特殊处理逻辑 TODO
    if (IsAddGraphicsState(GetState()))
    {
        HandlerMouseGraphicsAdd(
            MouseEventValue(wheel_type, x, y, x, y, 0,
                MouseEventValue::MouseState::wheel));
        return;
    }
    else if (GetState() == VisionMode::RESPONSE_GRAPHICS
        && GetKeyDown(VK_CONTROL))
    {
        HandlerMouseGraphicsResponse(
            MouseEventValue(wheel_type, x, y, x, y, 1,
                MouseEventValue::MouseState::wheel));
        return;
    }
    else if (GetState() == VisionMode::RESPONSE_GRAPHICS
        && GetKeyDown(VK_SHIFT))
    {
        HandlerMouseGraphicsResponse(
            MouseEventValue(wheel_type, x, y, x, y, 10,
                MouseEventValue::MouseState::wheel));
        return;
    }


    auto r = GetGraphicsManager()->GetRenderer();

    // 按住ctrl 中心缩放
    bool is_zoom_center = GetKeyDown(VK_CONTROL);

    float origin_x = static_cast<float>(x);
    float origin_y = static_cast<float>(y);

    // 如果是中心缩放模式，转换鼠标到世界坐标系
    if (is_zoom_center && !r->MouseToWorld(origin_x, origin_y))
        return;

    switch (wheel_type)
    {
    case WheelType::ZoomIn:
        MoveCamera(static_cast<int>(CameraDirection::Rear));
        break;
    case WheelType::ZoomOut:
        MoveCamera(static_cast<int>(CameraDirection::Front));
        break;
    case WheelType::None:
        return;
    }

    // 如果在中心缩放模式，根据鼠标坐标的变化调整摄像机位置
    if (is_zoom_center)
    {
        float result_x = static_cast<float>(x);
        float result_y = static_cast<float>(y);

        // 转换鼠标到世界坐标系
        if (!r->MouseToWorld(result_x, result_y))
            return;

        // 调整摄像机位置以实现平移效果
        MoveCamera(origin_x - result_x, origin_y - result_y);
    }
}

void StateManager::HandlerThumbnailmousemove(int type, float x, float y)
{
    (void)type; // TODO
    MoveCameraTo(x, y);
}

void StateManager::HandlerWindowsizechange([[maybe_unused]] int w, [[maybe_unused]] int h)
{
    //printInfo((std::stringstream() << "w:" << w << ",h:" << h));
    HandlerCameramove();
}

void StateManager::HandlerGraphicsdraw()
{
    GetRenderer2DManager()->Update();
}

// void StateManager::HandlerGraphicsupdate(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
// {
//     auto cmd = new CommandEditGraphics(ghs, GetGraphicsManager());
//     GetCommandManager()->AddCommand(cmd);
//     HandlerGraphicsdraw();
// }

void StateManager::HandlerGraphicschange(
    const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
{
    auto cmd = new CommandEditGraphics(ghs, GetGraphicsManager().get());
    GetCommandManager()->AddCommand(cmd);
}

void StateManager::HandlerGraphicsselected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
{
    if (ghs.empty())
        ResetState();
    else
        SetState(VisionMode::RESPONSE_GRAPHICS);
}

void StateManager::HandlerGraphicsadd(
    const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
{
    auto cmd = new CommandCreateGraphics(ghs, GetGraphicsManager().get());

    GetCommandManager()->AddCommand(cmd);
}

void StateManager::HandlerGraphicsdelete(
    const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
{
    auto cmd = new CommandDeleteGraphics(ghs, GetGraphicsManager().get());
    GetCommandManager()->AddCommand(cmd);
}

void StateManager::HandlerCursorchange(int type)
{
    GetCustomCursorManager()->SetCursor(static_cast<CustomCursorType>(type));
}

void StateManager::HandlerCameramove()
{
    int w, h;
    GetRenderer2DManager()->GetGLWindowSize(w, h);

    auto r = GetGraphicsManager()->GetRenderer();
    float tlx = 0; //top left x
    float tly = 0;  //top left y
    if (!r->MouseToWorld(tlx, tly))
        return;
    float brx = w; //top right x
    float bry = h; //top right y
    if (!r->MouseToWorld(brx, bry))
        return;

    GetRenderer2DManager()->SetRulerValue(tlx, brx, tly, bry);

    // float image_x = (float)((brx + tlx) * 0.5f);
    // float image_y = (float)((bry + tly) * 0.5f);
    // float image_w = (float)abs(brx - tlx);
    // float image_h = (float)abs(bry - tly);

    // printInfo((std::stringstream()
    //            << "image_x: " << image_x << " image_y: " << image_y
    //            << " image_w: " << image_w << " image_h: " << image_h)
    //               .str()
    //               .c_str());

    GetRenderer2DManager()->SetThumbnailViewport(tlx, tly, brx, bry);
    GetRenderer2DManager()->Update();
    // GetRenderer2DManager()->SetThumbnailRegion(image_x, image_y, image_w, image_h);
}

void StateManager::RegisterSlot()
{
    const auto& renderer2dmanager = GetRenderer2DManager();
    renderer2dmanager->SetCallbackRendermouseclicked(std::bind(
        &StateManager::HandlerRendermouseclicked, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3));
    renderer2dmanager->SetCallbackRendermousepress(std::bind(
        &StateManager::HandlerRendermousepress, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3));
    renderer2dmanager->SetCallbackRendermouserelease(std::bind(
        &StateManager::HandlerRendermouserelease, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3, std::placeholders::_4,
        std::placeholders::_5));
    renderer2dmanager->SetCallbackRendermousemove(std::bind(
        &StateManager::HandlerRendermousemove, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3, std::placeholders::_4,
        std::placeholders::_5, std::placeholders::_6, std::placeholders::_7));
    renderer2dmanager->SetCallbackRenderwheeldelta(std::bind(
        &StateManager::HandlerRenderwheeldelta, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3));
    renderer2dmanager->SetCallbackWindowsizechange(
        std::bind(&StateManager::HandlerWindowsizechange, this,
            std::placeholders::_1, std::placeholders::_2));
    renderer2dmanager->SetCallbackCameramove(
        std::bind(&StateManager::HandlerCameramove, this));
    renderer2dmanager->SetCallbackThumbnailmousemove(std::bind(
        &StateManager::HandlerThumbnailmousemove, this, std::placeholders::_1,
        std::placeholders::_2, std::placeholders::_3));
    renderer2dmanager->SetCallbackThumbnailmouseenter([=]()
        {
            this->HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPosition));
        });
    renderer2dmanager->SetCallbackThumbnailmouseleave([=]()
        {
            this->HandlerCursorchange(static_cast<int>(CustomCursorType::Default));
        });
    renderer2dmanager->SetCallbackCursorchange(std::bind(
        &StateManager::HandlerCursorchange, this, std::placeholders::_1));

    const auto& graphicsmanager = GetGraphicsManager();
    graphicsmanager->SetCallbackGraphicsdraw(std::bind(
        &StateManager::HandlerGraphicsdraw, this));
    // graphicsmanager->SetCallbackGraphicsupdate(std::bind(
    //     &StateManager::HandlerGraphicsupdate, this, std::placeholders::_1));
    graphicsmanager->SetCallbackGraphicsselected(std::bind(
        &StateManager::HandlerGraphicsselected, this, std::placeholders::_1));

    /**< 临时屏蔽，可能导致运行变慢 */
    //graphicsmanager->SetCallbackGraphicschange(std::bind(
    //    &StateManager::HandlerGraphicschange, this, std::placeholders::_1));
    //graphicsmanager->SetCallbackGraphicsadd(std::bind(
    //    &StateManager::HandlerGraphicsadd, this, std::placeholders::_1));
    //graphicsmanager->SetCallbackGraphicsdelete(std::bind(
    //    &StateManager::HandlerGraphicsdelete, this, std::placeholders::_1));
     /**< 临时屏蔽，可能导致运行变慢 */


    graphicsmanager->SetCallbackCursorchange(std::bind(
        &StateManager::HandlerCursorchange, this, std::placeholders::_1));
}

void StateManager::RegisterShortcut()
{
    const auto& renderer2dmanager = GetRenderer2DManager();
    auto widget = renderer2dmanager->GetWidget();
#pragma region 图形操作

    // 撤销
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_Z), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("undo");
                Undo();
                // UpdateScene();
            });
    }
    // 恢复
    {
        auto* sc =
            new QShortcut(QKeySequence(Qt::CTRL + Qt::SHIFT + Qt::Key_Z), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("redo");
                Redo();
                // UpdateScene();
            });
    }
    // 设置父图形
    {
        auto* sc =
            new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_F), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("set parent");
                GraphicsPtr gh;
                GetGraphicsManager()->ReadSelectedSingleGraphics(gh);
                GetGraphicsManager()->RecordParentGraphics(gh);
                //auto gh = GetGraphicsManager()->GetCurrentSelectedGraphicsSingle(GetGraphicsManager()->GetCurrentLayer());
                //if (!gh)
                //    return;
                //GetGraphicsManager()->SetParent(gh->GetId());
                // UpdateScene();
            });
    }
    // 应用父图形
    {
        auto* sc =
            new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_G), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("do parent");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                GraphicsPtr parent;
                GetGraphicsManager()->GetParentGraphics(parent);
                GetGraphicsManager()->UpdateGraphics(ghs, parent);
                //auto ghs = GetGraphicsManager()->GetCurrentSelectedGraphics(GetGraphicsManager()->GetCurrentLayer());
                //GetGraphicsManager()->AddToParent(ghs);
                // UpdateScene();
            });
    }
    // 创建图形
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_1), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                SetCreateGraphicsMode(CreateGraphicsMode::RECT);
                printInfo("add rect");
                // UpdateScene();
            });
    }
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_2), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                SetCreateGraphicsMode(CreateGraphicsMode::CIRCLE);
                printInfo("add circle");
                // UpdateScene();
            });
    }
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_3), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                SetCreateGraphicsMode(CreateGraphicsMode::POLYGON);
                printInfo("add polygon");
                // UpdateScene();
            });
    }
    // 复制
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_C), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("copy");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                m_graphicsmanager->CopyGraphics(ghs);
                //m_graphicsmanager->SetCopy();
                // UpdateScene();
            });
    }
    // 粘贴
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_V), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("paste");
                auto glw = renderer2dmanager->GetGLWindow();
                //  float x =
                //  glw->mapFromParent(widget->mapFromGlobal(QCursor().pos())).x();
                //  float y =
                //  glw->mapFromParent(widget->mapFromGlobal(QCursor().pos())).y();
                float x = glw->mapFromGlobal(QCursor().pos()).x();
                float y = glw->mapFromGlobal(QCursor().pos()).y();
                auto r = GetGraphicsManager()->GetRenderer();
                r->MouseToWorld(x, y);
                // mousePosToGraphic(x, y);
                m_graphicsmanager->PasteGraphics(x, y);
                //m_graphicsmanager->CreateCopy(x, y);
                // UpdateScene();
            });
    }
    // 移动全部图形
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_N), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("MOVE_ALL_GRAPHICS");
                SetState(VisionMode::MOVE_ALL_GRAPHICS);
            });
    }
    // 删除
    {
        auto* sc = new QShortcut(QKeySequence(Qt::Key_Delete), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("delete");
                //auto ghs = m_graphicsmanager->GetCurrentSelectedGraphics("");
                //std::vector<GraphicsID> ids;
                //for (auto& gh : ghs)
                //{
                //    ids.push_back(gh->GetId());
                //}
                m_graphicsmanager->DeleteSelectedGraphics();
            });
    }
    // 选中图形居中
    {
        auto* sc = new QShortcut(QKeySequence(Qt::Key_Backspace), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("reset view select graphics center");
                GraphicsPtr gh;
                GetGraphicsManager()->ReadSelectedSingleGraphics(gh);
                //auto gh = GetGraphicsManager()->GetCurrentSelectedGraphicsSingle(GetGraphicsManager()->GetCurrentLayer());
                if (!gh)
                    return;
                MoveCameraTo(gh);
                // UpdateScene();
            });
    }

#pragma endregion 图形操作

#pragma region 图形移动
    {
        auto* sc = new QShortcut(QKeySequence(/*Qt::CTRL +*/ Qt::Key_Left), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("move left");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                m_graphicsmanager->UpdateGraphics(ghs,
                    GraphicsManager::MoveDirection::LEFT);
                // UpdateScene();
            });
    }
    {
        auto* sc = new QShortcut(QKeySequence(/*Qt::CTRL + */Qt::Key_Right), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("move right");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                m_graphicsmanager->UpdateGraphics(ghs,
                    GraphicsManager::MoveDirection::RIGHT);
                // UpdateScene();
            });
    }
    {
        auto* sc = new QShortcut(QKeySequence(/*Qt::CTRL +*/ Qt::Key_Up), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("move up");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                m_graphicsmanager->UpdateGraphics(ghs,
                    GraphicsManager::MoveDirection::UP);
                // UpdateScene();
            });
    }
    {
        auto* sc = new QShortcut(QKeySequence(/*Qt::CTRL +*/ Qt::Key_Down), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("move down");
                std::vector<GraphicsPtr> ghs;
                m_graphicsmanager->ReadGraphicsSelected(ghs);
                m_graphicsmanager->UpdateGraphics(ghs,
                    GraphicsManager::MoveDirection::DOWN);
                // UpdateScene();
            });
    }
    // {
    //     auto *sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_R),
    //     widget); QObject::connect(sc, &QShortcut::activated, widget, [=] {
    //         m_graphicsmanager->KeyMoveGraphics(GraphicsMoveFlag::ROTATION);
    //         // UpdateScene();
    //     });
    // }
    // {
    //     auto *sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::SHIFT +
    //     Qt::Key_R), widget); QObject::connect(sc, &QShortcut::activated,
    //     widget, [=] {
    //         m_graphicsmanager->KeyMoveGraphics(GraphicsMoveFlag::REVERSE_TOTATION);
    //         UpdateScene();
    //     });
    // }


#pragma endregion 图形移动

#pragma region 其他操作
// 重置视野
    {
        auto* sc = new QShortcut(QKeySequence(Qt::Key_R), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("reset view center");
                ResetCamera(0);
                // UpdateScene();
            });
    }
    // 开/关视野移动限制
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_B), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("limit view to canvas");
                renderer2dmanager->SetLimitViewByCanvas(!renderer2dmanager->GetLimitViewByCanvas());
                // UpdateScene();
            });
    }
    // 开/关缩略图导航
    {
        auto* sc = new QShortcut(QKeySequence(Qt::CTRL + Qt::Key_M), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("thumbnail show");
                renderer2dmanager->SetThumbnailShow(!renderer2dmanager->GetThumbnailShow());
                // UpdateScene();
            });
    }
    // 开/关中心线
    {
        auto* sc = new QShortcut(QKeySequence(Qt::SHIFT + Qt::Key_A), widget);
        QObject::connect(sc, &QShortcut::activated, widget, [=]
            {
                printInfo("thumbnail show");
                renderer2dmanager->SetShowCenterCrossLine(!renderer2dmanager->GetIsShowCenterCrossLine());
                // UpdateScene();
            });
    }
#pragma endregion 其他操作
}

void StateManager::InitDefaultAttriute()
{
    m_renderer2dmanager->SetRendererObject(m_graphicsmanager, RenderType::RT_Graphics);
    m_renderer2dmanager->SetCanvasSize(4096, 3072);
}

void StateManager::OutState(const VisionMode& state)
{
    //printInfo((std::stringstream() << GetState()).str().c_str());

    switch (state)
    {
    case VisionMode::HOVER:
        GetRenderer2DManager()->SetMouseTracking(false);
        break;
    case VisionMode::SELECT_GRAPHICS:
    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    case VisionMode::SELECT_GRAPHICS_BATCH:
        HandlerCursorchange(static_cast<int>(CustomCursorType::Default));
        GetGraphicsManager()->ClearTemporary();
        break;
    case VisionMode::RESPONSE_GRAPHICS:
        GetRenderer2DManager()->SetMouseTracking(false);
        break;
    case VisionMode::EDIT_GRAPHICS:
        //GetGraphicsManager()->ClearResponse();
        //GetCustomCursorManager()->ResetCursor();
        break;
    case VisionMode::CREATE_GRAPHICS:
        GetGraphicsManager()->ClearTemporary();
        GetRenderer2DManager()->SetMouseTracking(false);
        m_createmode = CreateGraphicsMode::NONE;
        break;
    case VisionMode::MOVE_ALL_GRAPHICS:
        GetCustomCursorManager()->ResetCursor();
        break;
    case VisionMode::MANUAL_CREATE_GRAPHICS:
    {
        GetGraphicsManager()->ClearTemporary();
        GetCustomCursorManager()->ResetCursor();
        GetRenderer2DManager()->SetMouseTracking(false);
    }
    break;
    default:
        break;
    }
}

void StateManager::InState(const VisionMode& state)
{
    //printInfo((std::stringstream() << VisionMode(state)).str().c_str());

    switch (state)
    {
    case VisionMode::HOVER:
        //GetGraphicsManager()->ClearResponse();
        //GetGraphicsManager()->ClearSelected();
        GetGraphicsManager()->ClearTemporary();
        GetCustomCursorManager()->ResetCursor();
        GetRenderer2DManager()->SetMouseTracking(true);
        break;
    case VisionMode::MOVE_CAMERA:
        //GetGraphicsManager()->ClearResponse();
        //GetGraphicsManager()->ClearSelected();
        GetGraphicsManager()->ClearTemporary();
        GetCustomCursorManager()->ResetCursor();
        GetRenderer2DManager()->SetMouseTracking(false);
        break;
    case VisionMode::VIEW_ALL:
        //GetGraphicsManager()->ClearResponse();
        GetGraphicsManager()->ClearSelected();
        GetGraphicsManager()->ClearTemporary();
        GetCustomCursorManager()->ResetCursor();
        GetRenderer2DManager()->SetMouseTracking(false);
        SetZoomState(static_cast<int>(CameraScaleMode::AUTO_SCALE));
        break;
    case VisionMode::RESPONSE_GRAPHICS:
        GetRenderer2DManager()->SetMouseTracking(true);
        break;
    case VisionMode::SELECT_GRAPHICS:
    case VisionMode::SELECT_GRAPHICS_BATCH:
        HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
        break;
    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    {
        HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
        //GetGraphicsManager()->ClearResponse();
        GetGraphicsManager()->ClearSelected();
        GetRenderer2DManager()->SetMouseTracking(true);
    }
    break;
    case VisionMode::CREATE_GRAPHICS:
    {
        HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
        //GetGraphicsManager()->ClearResponse();
        GetGraphicsManager()->ClearSelected();
        GetRenderer2DManager()->SetMouseTracking(true);
    }
    break;
    case VisionMode::MOVE_ALL_GRAPHICS:
    {
        GetGraphicsManager()->ClearSelected();
        GetGraphicsManager()->ClearTemporary();
        HandlerCursorchange(static_cast<int>(CustomCursorType::MoveAll));
    }
    break;

    case VisionMode::EDIT_GRAPHICS:
        break;
    case VisionMode::MANUAL_CREATE_GRAPHICS:
    {
        HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
        GetGraphicsManager()->ClearTemporary();
        //GetCustomCursorManager()->ResetCursor();
       //GetGraphicsManager()->ClearSelected();
        GetRenderer2DManager()->SetMouseTracking(true);
    }
    break;
    default:
        break;
    }
    m_state = VisionMode(state);
}

bool StateManager::IsAddGraphicsState(const VisionMode& state)
{
    return state == VisionMode::CREATE_GRAPHICS && m_createmode != CreateGraphicsMode::NONE;
}
