/*********************************************************************
 * @brief  临时对象控制模板类.
 *
 * @file   transientobjectcontrol.hpp
 *
 * @date   2024.03.11
 * <AUTHOR>
**********************************************************************/
#pragma once

#ifndef TRANSIENTOBJECTCONTROL_H
#define TRANSIENTOBJECTCONTROL_H

/**
 * @brief 临时对象控制模板类
 * 该类用于管理和操作对象的临时拷贝
 * @tparam _T 类型模板参数
 * <AUTHOR>
 */
template <typename _T>
class TransientObjectControl
{
public:
    TransientObjectControl() = default;
    /**
     * @brief 采用默认构造函数
     */
    TransientObjectControl(const TransientObjectControl&)
        :TransientObjectControl()
    {}
    TransientObjectControl& operator=(const TransientObjectControl& other)
    {
        if (this != &other)
        {
            this->DeleteTemp();
        }
        return *this;
    }
    /**
     * @brief 析构函数
     * <AUTHOR>
     */
    ~TransientObjectControl()
    {
        DeleteTemp();
    }

    /**
     * @brief 获取临时对象
     *
     * @param current 当前对象指针
     * @param use_temp 是否使用临时对象
     * @return 返回当前对象或临时对象的指针
     * <AUTHOR>
     */
    _T* GetTemp(_T* current, bool use_temp) const
    {
        if (use_temp && is_have_temp)
        {
            return temp;
        }
        else
        {
            return current;
        }
    }

    /**
     * @brief 获取临时对象（常量版本）
     *
     * @param current 当前对象指针
     * @param use_temp 是否使用临时对象
     * @return 返回当前对象或临时对象的常量指针
     * <AUTHOR>
     */
    const _T* GetTemp(const _T* current, bool use_temp) const
    {
        if (use_temp && is_have_temp)
        {
            return temp;
        }
        else
        {
            return current;
        }
    }

    /**
     * @brief 获取并更新临时对象
     *
     * @param current 当前对象指针
     * @param use_temp 是否使用临时对象
     * @return use_temp 为true时 返回更新后的临时对象指针
     *         use_temp 为false时 如果存在临时对象则返回空，否则返回当前对象指针
     * @note  返回空的原因是,直接用临时对象覆盖当前对象,
     *        因此不用再对当前对象进行处理
     * <AUTHOR>
     */
    _T* GetAndUpdateTemp(_T* current, bool use_temp)
    {
        if (use_temp)
        {
            UpdateTemp(current);
            return temp;
        }

        if (UpdateByTemp(current))
        {
            return nullptr;
        }
        return current;
    }

    /**
     * @brief 用临时对象更新当前对象
     * @param current 当前对象指针
     * <AUTHOR>
     */
    bool UpdateByTemp(_T* current)
    {
        if (!is_have_temp)
            return false;

        *current = std::move(*temp); // 反向赋值 更高效
        // *current = *temp; // 反向赋值
        is_have_temp = false; // 删除临时对象,这里只更改标记
        // DeleteTemp();
        return true;
    }

    /**
     * @brief 更新临时对象
     * @param current 当前对象指针
     * @note  要求对象实现了拷贝赋值运算符
     * <AUTHOR>
     */
    void UpdateTemp(_T* current)
    {
        if (!is_have_temp)
        {
            CreateTemp(current);
        }
        else
        {
            *temp = *current;
        }
    }

    /**
     * @brief 创建临时对象
     * @param current 当前对象指针
     * @note  要求对象实现了拷贝构造函数
     * <AUTHOR>
     */
    void CreateTemp(_T* current)
    {
        if (!temp)
        {
            temp = new _T(*current);
        }
        else
        {
            *temp = *current;
        }
        is_have_temp = true;
    }

    /**
     * @brief 删除临时对象
     * <AUTHOR>
     */
    void DeleteTemp()
    {
        if (temp)
        {
            delete temp;
            temp = nullptr;
        }
        is_have_temp = false;
    }

    bool is_have_temp = false; ///< 是否有临时对象,用于减少临时对象的新建和删除
    _T* temp = nullptr; ///< 临时对象指针

private:
    // 显式删除移动构造函数
    TransientObjectControl(TransientObjectControl&&) = delete;
    // 显式删除移动赋值运算符
    TransientObjectControl& operator=(TransientObjectControl&&) = delete;
};

#endif // !TRANSIENTOBJECTCONTROL_H