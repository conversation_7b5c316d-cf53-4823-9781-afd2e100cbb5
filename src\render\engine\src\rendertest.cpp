﻿#include "rendertest.h"
#include "graphicsobject.h"
#include "painter.h"

#include "randomhelper.hpp"


const int region_x_min = 0;
const int region_y_min = 0;
const int region_x_max = 10000;
const int region_y_max = 10000;
std::vector<std::pair<float, float>> move;
std::vector<std::pair<float, float>> move_text;
std::vector<LayerConfig> configs;
LayerConfig textconfig({ 66, 66, 66, 255 ,1 }, { 66, 66, 66, 255,1 });
RenderTest::RenderTest()
    : RenderAbstract()
{
    //const int gsize = 10000; /*debug下2000,release下10000可保持30帧*/
    //ghs.reserve(gsize);
    //move.reserve(gsize);
    //for (int i = 0; i < gsize - 2; ++i)
    //{
    //    GraphicsPtr gh;
    //    if (i % 2 == 0)
    //        gh = std::make_shared<CircleGraphics>();
    //    else
    //        gh = std::make_shared<RectGraphics>();

    //    gh->SetX((float)rand_a_b(region_x_min, region_x_max));
    //    gh->SetY((float)rand_a_b(region_y_min, region_y_max));
    //    float size = (float)rand_a_b(30, 70);
    //    gh->SetWH(size, size);
    //    ghs.emplace_back(gh);

    //    move.emplace_back(std::make_pair<float, float>((float)rand_a_b(-5, 5), (float)rand_a_b(-5, 5)));
    //}
    //const int tsize = 10;
    //ghs_text.reserve(tsize);
    //move_text.reserve(tsize);
    //for (int i = 0; i < tsize; ++i)
    //{
    //    std::shared_ptr<TextGraphics> gh = std::make_shared<TextGraphics>();
    //    std::string text;
    //    for (int j = 0; j < 15; ++j)
    //    {
    //        text.push_back((char)rand_a_b(33, 124));
    //    }
    //    gh->text = text;
    //    gh->SetXY((float)rand_a_b(region_x_min, region_x_max), (float)rand_a_b(region_y_min, region_y_max));
    //    ghs_text.emplace_back(gh);
    //    move_text.emplace_back(std::make_pair<float, float>((float)rand_a_b(-5, 5), (float)rand_a_b(-5, 5)));
    //}
    //configs.emplace_back(LayerConfig(255, 255, 255, 35, 20));
    //configs.emplace_back(LayerConfig(255, 255, 0, 255));
    //configs.emplace_back(LayerConfig(255, 0, 255, 35));
    //configs.emplace_back(LayerConfig(0, 255, 255, 255, 60));
    //configs.emplace_back(LayerConfig(0, 0, 0, 255));
    //configs.emplace_back(LayerConfig(255, 0, 0, 35));
}

RenderTest::~RenderTest()
{
}

void RenderTest::Render()
{
    if (!IsRendered())
        return;
    if (!IsHaveRenerer())
        return;

    Painter p(renderer);
    int config_size = (int)configs.size();
    for (size_t i = 0; i < ghs.size(); ++i)
    {
        auto& gh = ghs[i];
        gh->Draw(renderer, &p, &configs[(i + 1) % config_size]);
        gh->SetXY(gh->x() + move[i].first, gh->y() + move[i].second);
        if (gh->x() > region_x_max || gh->x() < region_x_min)
        {
            move[i].first *= -1;
        }
        if (gh->y() > region_y_max || gh->y() < region_y_min)
        {
            move[i].second *= -1;
        }
    }
    for (size_t i = 0; i < ghs_text.size(); ++i)
    {
        auto& gh = ghs_text[i];
        gh->Draw(renderer, &p, &textconfig);
        gh->SetXY(gh->x() + move_text[i].first, gh->y() + move_text[i].second);
        if (gh->x() > region_x_max || gh->x() < region_x_min)
        {
            move_text[i].first *= -1;
        }
        if (gh->y() > region_y_max || gh->y() < region_y_min)
        {
            move_text[i].second *= -1;
        }
    }
}

void RenderTest::Destroy()
{
}
