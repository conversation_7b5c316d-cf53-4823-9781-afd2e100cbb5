// Custom
#include "motiontrack.h"

namespace jrsdevice
{
    MotionTrack::MotionTrack(std::shared_ptr<Motion> &motion, bool &running): TrackBase(motion,running)
    {
        MotionFunctionBind();
    }

    MotionTrack::~MotionTrack()
    {
    }

    void MotionTrack::Excute<PERSON><PERSON>mond(const jrsdata::DeviceParamPtr &device_param_)
    {
        if (motion_func_map.find(device_param_->event_name) != motion_func_map.end())
        {
            motion_func_map[device_param_->event_name](device_param_);
        }
    }

    void MotionTrack::SetMotionSetting(jrsdata::MotionSetting motion)
    {
        motion_setting = motion;
    }

    void MotionTrack::MotionFunctionBind()
    {
        motion_func_map["AskInitial"] = std::bind(&MotionTrack::AskInitial, this, std::placeholders::_1);
        motion_func_map["AskStart"] = std::bind(&MotionTrack::AskStart, this, std::placeholders::_1);
        motion_func_map["AskStop"] = std::bind(&MotionTrack::AskStop, this, std::placeholders::_1);
    }

    void MotionTrack::AskInitial(const jrsdata::DeviceParamPtr &param_)
    {
        if (process_running)
        {
            Log_ERROR("程序正在运行中，请先停止流程 ");
            return;
        }

        (void)param_;
        int res = motion_ptr->AskInitial();
        if (res != 0)
        {
            Log_ERROR("回零失败 ");
        }
    }

    void MotionTrack::AskStart(const jrsdata::DeviceParamPtr &param_)
    {
        if (process_running)
        {
            Log_ERROR("程序正在运行中，请先停止流程 ");
            return;
        }

        (void)param_;

        // 轨道数量
        int TrackCout = motion_setting.trackCout;
        if (TrackCout > motion_setting.transportMode.size())
        {
            return;
        }

        // 在线模式0、单机模式1、老化模式2(有老化次数)
        int TrackMode = motion_setting.trackMode;

        // 老化次数
        long long CycleCount = motion_setting.cycleCount;

        // 轨道参数初始化(去除传输模式)
        RemoveAllTrack();
        for (int i = 0; i < TrackCout; i++)
        {
            if (!motion_setting.transportMode[i])
            {
                jrsdevice::TrackIndex index = (jrsdevice::TrackIndex)(i + 1);
                jrsdevice::TrackProcess process;
                process.track_load_state = TrackLoadState::UnLoadSuccess;
                process.track_photo_state = PhotoState::NotStart;
                process.track_station = WorkStation::Station1;
                process.cycle_count = TrackMode == 2 ? CycleCount : (long long)1E16;
                process.track_trans_mode = false;
                process.track_mode = TrackMode;
                AddTrackProcess(index, process);
            }
            else
            {
            }
        }
        process_running = true;
    }
    
    void MotionTrack::AskStop(const jrsdata::DeviceParamPtr &param_)
    {
        process_running = false;
        (void)param_;
        int res = motion_ptr->AskStop();
        if (res != 0)
        {
            Log_ERROR(motion_ptr->GetLastError());
        }
    }
}
