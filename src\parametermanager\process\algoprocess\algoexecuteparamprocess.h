///*****************************************************************//**
// * @file   algoexecuteparamprocess.h
// * @brief  算法执行结果参数处理
// * @details
// * <AUTHOR>
// * @date 2025.1.9
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.2.26         <td>V1.0              <td>YYZhang      <td><EMAIL> <td>
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/
//#ifndef __ALGOEXECUTEPARAMPROCESS_H__
//#define __ALGOEXECUTEPARAMPROCESS_H__
//
// //STD
//#include <memory>
//
////Custom
//#include "pluginexport.hpp"
//namespace jrsoperator
//{
//    struct OperatorParamBase;
//}
//namespace jrsparam
//{
//
//    class JRS_AOI_PLUGIN_API AlgoExecuteParamProcess
//    {
//    public:
//        static bool GetAlgoExecuteResultStatus(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_res_);
//        static bool GetAlgoExecuteResultStatusTemp(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_res_, float& mark_socre_);
//
//    };
//}
//#endif // __ALGOEXECUTEPARAMPROCESS_H__