﻿// STD
#include <thread>
#include <future>

// Custom
#include "devicemanager.h"
#include "coreapplication.h"
#include "structlight.h"
#include "barcodedevicehuarui.h"
#include "fileoperation.h"
// #include "settingparam.h"

namespace jrsdevice
{
    DeviceManager::DeviceManager()
        : struct_light_ptr(std::make_shared<StructLight>())
        , device_param(std::make_shared<jrsdata::DeviceParam>())
        , barcode_device_ptr(std::make_shared<BarcodeDeviceHuaRui>())
    {
    }
    DeviceManager::~DeviceManager()
    {

    }
    void DeviceManager::EventHandler(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT)
        {
            std::thread t(&DeviceManager::InitSystem, this, param_);
            t.detach();
        }
        else if (param_->event_name == "release_structlight_device")
        {
            struct_light_ptr.reset();
        }
        else
        {

        }
    }

    void DeviceManager::InitSystem(const jrsdata::ViewParamBasePtr& param_ptr_)/**< 进行函数拆分 */
    {
        Log_INFO("进入devicemanager初始化流程");
        auto system_param = std::static_pointer_cast<jrsdata::SystemStateViewParam>(param_ptr_);
        {
            std::lock_guard<std::mutex> lock(_mtxsys_state_param);
            sys_state_param = system_param;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        auto struct_light_future = std::async(std::launch::async, [&]()
            {
                std::string struct_light_init_error_info;
                Log_INFO("进入结构光初始化流程！");
                ChangeSystemState(jrscheckitem::LIGHT_SOURCE_ITEM, jrsdata::MachineCheckParamInfo::CheckState::CHECKING);
                auto res = struct_light_ptr->Init(struct_light_init_error_info);
                if (!res)
                {

                    //if (motion_ptr.get() != nullptr)
                    //{
                    //    auto motion_trigger = [this](const int& io_id, int& erro_code)
                    //        {
                    //            if (this->motion_ptr->PhotoTook((jrsdevice::OutputIndex)io_id) == jrscore::AOI_OK)
                    //            {
                    //                erro_code = 0;
                    //            }
                    //            else
                    //            {
                    //                erro_code = -1;
                    //            }
                    //        };
                    //    struct_light_ptr->SetIOTriggerFuc(motion_trigger);
                    //}

                    ChangeSystemState(jrscheckitem::LIGHT_SOURCE_ITEM, jrsdata::MachineCheckParamInfo::CheckState::OK);
                }
                else
                {
                    Log_ERROR("结构光初始化失败！");
                    ChangeSystemState(jrscheckitem::LIGHT_SOURCE_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED,
                        { jrscore::DeviceError::E_AOI_DEVICE_STRUCT_LIGHT_RESET_FAILURE,"",struct_light_init_error_info });
                }
                return res;
            });
       // std::string motion_init_error_info = "";

        //auto motion_future = std::async(std::launch::async, [&]()
        //    {
        //        ChangeSystemState(jrscheckitem::MOTION_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::CHECKING);
        //        //auto res = motion_ptr->AskInitial(60 * 1000);
        //        //if (res != jrscore::AOI_OK)
        //        //{
        //        //    if (res == jrscore::MotionError::E_CONTROL_Motion_UNKNOWN)
        //        //    {
        //        //        motion_init_error_info = "运控初始化失败，请检查运控软件是否打开!\n";
        //        //    }
        //        //    ChangeSystemState(jrscheckitem::MOTION_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED,
        //        //        { res,"",motion_init_error_info });
        //        //}
        //        return 0;
        //    });

        //motion_future.get();

        struct_light_future.get();

        // ! 轴回零结束后将Z轴移动到清晰拍照点位置
        //device_param->struct_light_param = struct_light_ptr->GetStructLightParam();
        //auto res_move_z = motion_ptr->Mova(Axis::Axis03, device_param->struct_light_param.z_focus_pos, 100);
        //if (res_move_z != jrscore::AOI_OK)
        //{
        //    motion_init_error_info += "Z轴移动到清晰拍照点位失败！\n";
        //}

       // if (!motion_init_error_info.empty())
       // {
       ///*     ChangeSystemState(jrscheckitem::MOTION_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED,
       //         { res_move_z,"",motion_init_error_info });*/
       // }
       // else
       // {
       //     ChangeSystemState(jrscheckitem::MOTION_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::OK);
       // }
        //{
        //    ChangeSystemState(jrscheckitem::LIGHT_SOURCE_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED);
        //    ChangeSystemState(jrscheckitem::TRACK_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED);
        //    ChangeSystemState(jrscheckitem::MOTION_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED);
        //}
        if (init_callback)
        {
            init_callback(device_param);

        }
    }

    void DeviceManager::SetInitCallBack(InitCallBack cb_)
    {
        init_callback = cb_;
    }

    void DeviceManager::SetSystemStateCallBack(jrsdata::InvokeSystemStateParamFun callback_)
    {
        sys_state_callback = callback_;
    }

    void DeviceManager::ChangeSystemState(const std::string& check_name_, const jrsdata::MachineCheckParamInfo::CheckState& state_,
        const std::tuple<int, std::string, std::string>& code_and_info_/*= std::make_pair(jrscore::AOI_OK, "")*/,
        const std::string& event_name_ /*= jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT*/)
    {
        sys_state_param->event_name = event_name_;
        auto it = sys_state_param->check_items.find(check_name_);
        bool callback_needed = false;
        {
            std::lock_guard<std::mutex> lock(_mtxsys_state_param);
            if (it != sys_state_param->check_items.end()) {
                it->second.check_state = state_;
                auto [code, error_info, description] = code_and_info_;
                it->second.code = code;
                it->second.err_info = error_info;
                it->second.description = description;
                callback_needed = true;
            }
        }
        if (callback_needed && sys_state_callback) {
            sys_state_callback(sys_state_param);
        }
    }

    void DeviceManager::ReleaseStructLight()
    {
        struct_light_ptr.reset();
    }

    std::vector<std::string> DeviceManager::SplitString(const std::string& s, char delimiter)
    {
        std::vector<std::string> tokens;
        std::string token;
        std::istringstream tokenStream(s);
        while (std::getline(tokenStream, token, delimiter))
        {
            tokens.push_back(token);
        }
        return tokens;
    }

    JSON DeviceManager::GetObjFromName(JSON Obj, std::string name)
    {
        if (Obj.is_array())
        {
            for (int i = 0; i < (int)Obj.size(); i++)
            {
                if (Obj[i]["name"].get<std::string>() == name)
                {
                    return Obj[i];
                }
            }
        }
        return JSON();
    }
    StructLightPtr DeviceManager::GetStructLightInstance()
    {
        return struct_light_ptr;
    }

    std::shared_ptr<BarcodeDevice> DeviceManager::GetBarcodeInstance()
    {
        return barcode_device_ptr;
    }
}
