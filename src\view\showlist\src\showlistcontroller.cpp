﻿#include "showlistcontroller.h"
#include "showlistmodel.h"
#include "showlistview.h"
#include "editview.h"
#include "editdetectmodelview.h"
#include <QTimer>
#include <QDebug>

namespace jrsaoi
{
    ShowListController::ShowListController(const std::string& name) :ControllerB<PERSON>(name)
    {
    }
    ShowListController::~ShowListController()
    {
    }

    int ShowListController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name.compare(jrsaoi::SHOWLIST_SELECT_COMPONENT_UPDATE) == 0)
        {
            return jrscore::AOI_OK;
        }
        auto res = model->Update(param_);
        if (res == jrscore::AOI_OK)
        {
            if (param_->event_name.compare(jrsaoi::SHOWLIST_CLEAN_EVENT_NAME) == 0)//文件列表清除
            {
                model->CleanDatas();
                show_list_view->CleanDatas();
            }
            else if ((param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME) == 0) ||
                (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME) == 0) ||
                (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME) == 0) ||
                (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME) == 0) ||
                (param_->event_name.compare(jrsaoi::OPERATE_UPDATE_COMPONENT_RESULT_STATUS_EVENT_NAME) == 0))
            {
                show_list_view->UpdateSubboardDatas(model->GetGraphicsParam());
            }
            else
            {
                res = show_list_view->UpdateView(model->GetModelData());
            }
        }
        //_edit_view->UpdateView(model->GetEditViewData());

        return res;
    }

    int ShowListController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    void ShowListController::SetView(ViewBase* view_param)
    {
        show_list_view = static_cast<ShowListView*>(view_param);
        connect(show_list_view, &ShowListView::ShowListRenderTrigger, this, &ShowListController::SlotViewUpdateData);
        connect(show_list_view, &ShowListView::FindTrigger, this, &ShowListController::FindTriggerSlots);
        //_edit_view = new EditView();
        //show_list_view->SetWindowView(_edit_view);
        //connect(_edit_view, &EditView::SigUpdateView, this, &ShowListController::SlotViewUpdateData);

        //TODO 修改编辑算法的界面改到左侧中  by baron zhang 2025-07-10
        edit_detect_model_view = new EditDetectModelView();

        // 设置EditDetectModelView的显示属性
        edit_detect_model_view->setVisible(true);
        edit_detect_model_view->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        edit_detect_model_view->setMinimumSize(200, 200);

        // 连接信号
        connect(edit_detect_model_view, &EditDetectModelView::SigUpdateOperator, this, &ShowListController::SlotViewUpdateData);

        // 调试：先尝试直接显示
        qDebug() << "Creating EditDetectModelView, size:" << edit_detect_model_view->size();

        show_list_view->SetWindowView(edit_detect_model_view);

        // 强制更新显示
        QTimer::singleShot(100, [this]() {
            if (edit_detect_model_view) {
                edit_detect_model_view->update();
                edit_detect_model_view->repaint();
                qDebug() << "EditDetectModelView forced update";
            }
        });
        //TODO end

        connect(show_list_view, &ShowListView::ChangeShowListViewParam, this, &ShowListController::UpdateShowListViewParamSlots);
    }

    void ShowListController::SetModel(ModelBasePtr model_param)
    {
        model = std::dynamic_pointer_cast<ShowListModel>(model_param);
    }

    void ShowListController::UpdateShowListViewParamSlots(ShowListViewParam param)
    {
        if (model != nullptr)
        {
            model->UpdateShowListView(param);
        }
    }

    void ShowListController::SlotViewUpdateData(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            return;
        }
        if (param_->event_name.compare(jrsaoi::SHOWLIST_SELECT_COMPONENT_UPDATE) == 0)
        {
            auto component_param = std::dynamic_pointer_cast<jrsdata::ComponentListViewParam>(param_);
            model->SetSelectComponentUpdateData(component_param->component_name);
        }
        if (param_->event_name == jrsaoi::EDIT_UPDATE_RENDER_EVENT_NAME)
        {
            model->UpdateEditViewDataToProject(param_);
            show_list_view->UpdateSubboardDatas(model->GetGraphicsParam());
        }
        emit ShowListRenderTrigger(param_);
    }

    void ShowListController::FindTriggerSlots(const jrsdata::QueryListViewParam param)
    {
        model->Query(param);
        show_list_view->UpdateSubboardDatas(model->GetGraphicsParam());
        show_list_view->QueryConditionFocus();
    }
}