/*********************************************************************
 * @brief  fov规划算法.
 *
 * @file   calcfov.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#ifndef CALCFOV_H
#define CALCFOV_H
#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "opencv2/opencv.hpp"
#pragma warning(pop)

#include <algorithm>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <limits>
#undef min
#undef max
class CalcFov
{
public:
    struct ObjectInput
    {
        std::string id;/**< 需要被分配的信息id*/
        cv::RotatedRect rr; /**< 外接矩形*/
    };
    struct ObjectOutput
    {
        std::string id;/**<需要被分配的信息id，即元件的名称 */
        int fovid;/**< 被分配的FOV的 ID*/
        std::vector<int> fovids; /**<被分配目标被分配到的FOV集合 */
    };

    /**< 独立的fov规划路径*/
    struct SeparateFovPath
    {
        int fovid = -1; /**< 分配出来的FOV的ID信息*/
        cv::Point2f center; /**< 中心坐标*/
    };
    struct Fov
    {
        int fovid; /**< FOV ID */
        cv::Point2f center; /**< FOV 中心坐标*/
        std::vector<ObjectOutput> coveredRectangles;
        bool haveBig; // 包含了一个超出最大范围的矩形,因此位置不能动

        Fov() :fovid(-1), haveBig(false) {};
        Fov(const int& fovid_, const cv::Point2f& _center, const std::vector<ObjectOutput>& _coveredRectangles, const bool& _haveBig)
            :fovid(fovid_), center(_center), coveredRectangles(_coveredRectangles), haveBig(_haveBig)
        {
        }
        bool operator==(const Fov& other) const
        {
            return center == other.center;
        }
    };

    enum GridSortMode
    {
        FROM_TOP_TO_BOTTOM,
        FROM_LEFT_TO_RIGHT,
        FROM_BOTTOM_TO_TOP,
        FROM_RIGHT_TO_LEFT,
    };

    /**
     * @brief  限制值在区间内.
     */
    template <class T>
    static constexpr T clamp(const T& v, const T& lo, const T& hi)
    {
        assert(!(hi < lo));
        return v < lo ? lo : hi < v ? hi
            : v;
    }
    template <typename T>
    static int manhattanlength(const cv::Point_<T>& p1, const cv::Point_<T>& p2)
    {
        return abs(p1.x - p2.x) + abs(p1.y - p2.y);
    }

    static std::vector<cv::Rect> makeRectMapRect(const cv::Rect& bigRect, int fovWidth, int fovHeight)
    {
        if (bigRect.width < fovWidth && bigRect.height < fovHeight)
            return std::vector<cv::Rect>{
            cv::Rect((int)(bigRect.x + bigRect.width / 2.f - fovWidth / 2.f), (int)(bigRect.y + bigRect.height / 2.f - fovHeight / 2.f),
                fovWidth, fovHeight)};

        int cols = (int)ceil((float)bigRect.width / fovWidth);
        int rows = (int)ceil((float)bigRect.height / fovHeight);

        // int colsOffset = bigRect.width % fovWidth;
        // int rowsOffset = bigRect.height % fovHeight;

        // int xMargin = ceil((float)colsOffset / cols) ;
        // int yMargin = ceil((float)rowsOffset / rows );

        // int xStart = bigRect.x + xMargin;
        // int yStart = bigRect.y + yMargin;
        int xStart = (int)(bigRect.x + (bigRect.width - (cols * fovWidth)) / 2.f);
        int yStart = (int)(bigRect.y + (bigRect.height - (rows * fovHeight)) / 2.f);

        std::vector<cv::Rect> trRects;

        for (int r = 0; r < rows; r++)
        {
            for (int c = 0; c < cols; c++)
            {
                int x = xStart + c * fovWidth;
                int y = yStart + r * fovHeight;
                cv::Rect trRect(x, y, fovWidth, fovHeight);
                trRects.emplace_back(trRect);
            }
        }
        return trRects;
    }

    template <typename T>
    static bool isRectInRect(const cv::Rect_<T>& rect1, const cv::Rect_<T>& rect2)
    {
        return (rect1.x >= rect2.x &&
            rect1.y >= rect2.y &&
            rect1.x + rect1.width <= rect2.x + rect2.width &&
            rect1.y + rect1.height <= rect2.y + rect2.height);
    }

    template <typename T>
    static bool isRectIntersecting(const cv::Rect_<T>& rect1, const cv::Rect_<T>& rect2)
    {
        return rect1.x < rect2.x + rect2.width &&
            rect1.x + rect1.width > rect2.x &&
            rect1.y < rect2.y + rect2.height &&
            rect1.y + rect1.height > rect2.y;
    }

    static void drawRect(cv::Mat& image, const cv::Rect& rect, const cv::Scalar& color, const int& thickness)
    {
        cv::rectangle(image, rect, color, thickness, cv::LINE_4);
    }

    static void drawRotatedRect(cv::Mat& image, const cv::RotatedRect& rect, const cv::Scalar& color, const int& thickness)
    {
        cv::Point2f P[4];
        rect.points(P); // 获取四顶点坐标
        for (int j = 0; j < 4; j++)
        {
            cv::line(image, P[j], P[(j + 1) % 4], color, thickness, cv::LINE_AA); // 依次连线
        }
    }
    static void resizeImagePreserveAspectRatio(const cv::Mat& src, cv::Mat& dst, int width, int height)
    {
        // 获取图像的宽度和高度
        int imageWidth = src.cols;
        int imageHeight = src.rows;

        // 判断输入图像是否需要缩放
        if (imageWidth <= width && imageHeight <= height)
        {
            dst = src.clone();
            return; // 图像宽高都小于区域大小，无需缩放
        }
         
        // 计算宽度和高度的缩放比例
        float scaleWidth = static_cast<float>(width) / imageWidth;
        float scaleHeight = static_cast<float>(height) / imageHeight;

        // 选择较小的缩放比例作为最终的缩放比例，以保持图像的长宽比不变
        float scaleFactor = std::min(scaleWidth, scaleHeight);

        //// 计算缩放后的图像尺寸
        // int newWidth = static_cast<int>(imageWidth * scaleFactor);
        // int newHeight = static_cast<int>(imageHeight * scaleFactor);

        cv::resize(src, dst, {}, scaleFactor, scaleFactor);
    }

    // 计算限制后的矩形的左上角坐标
    static cv::Rect limitRectTLInBoundary(const cv::Rect& rect, const cv::Rect& boundary)
    {

        int x = std::max(rect.x, boundary.x);
        int y = std::max(rect.y, boundary.y);
        x = std::min(x, boundary.x + boundary.width - rect.width);
        y = std::min(y, boundary.y + boundary.height - rect.height);

        return cv::Rect(x, y, rect.width, rect.height);
    }

    /**
     * @brief 使用贪心算法解决集合覆盖问题
     * @param universe 需要覆盖的元素集合
     * @param subsets 子集集合
     * @return 最小的子集索引集合
     */
    static std::vector<int> greedySetCover(const std::unordered_set<int>& universe, const std::vector<std::unordered_set<int>>& subsets)
    {
        std::unordered_set<int> uncovered = universe; // 未覆盖的元素集合
        std::vector<int> selectedSubsets;             // 选择的子集索引集合

        while (!uncovered.empty())
        {
            int bestSubset = -1; // 当前选择的最优子集索引
            int maxCovered = 0;  // 当前最优子集能覆盖的未覆盖元素数量

            // 找到能覆盖最多未覆盖元素的子集
            for (size_t i = 0; i < subsets.size(); ++i)
            {
                const std::unordered_set<int>& subset = subsets[i];
                int coveredCount = 0;

                // 计算该子集能覆盖的未覆盖元素数量
                for (const int& elem : subset)
                {
                    if (uncovered.find(elem) != uncovered.end())
                    {
                        coveredCount++;
                    }
                }

                // 更新最优子集
                if (coveredCount > maxCovered)
                {
                    maxCovered = coveredCount;
                    bestSubset = (int)i;
                }
            }

            // 将最优子集添加到选择的子集集合中
            if (bestSubset != -1)
            {
                selectedSubsets.push_back(bestSubset);
                // 从未覆盖元素集合中移除已覆盖的元素
                for (int elem : subsets[bestSubset])
                {
                    uncovered.erase(elem);
                }
            }
            else
            {
                // 如果没有子集可以覆盖剩余的元素，退出循环
                break;
            }
        }

        return selectedSubsets;
    }

    static bool FindRectRecoverAllGrid(std::vector<cv::Point2f>& dst, const cv::Rect& region, int fov_w, int fov_h, int gap_w, int gap_h)
    {
        /*
         * gap = ow - (w-ow)/(n-1)
         *
         * n = (w-ow)/(ow-gap) + 1
         */
        const int& w = region.width;
        const int& h = region.height;
        const float half_fov_w = fov_w * 0.5f;
        const float half_fov_h = fov_h * 0.5f;
        /*中心移动限制*/
        const int c_x_min = (int)half_fov_w;
        const int c_x_max = w > fov_w ? w - (int)half_fov_w : w;
        const int c_y_min = (int)half_fov_h;
        const int c_y_max = h > fov_h ? h - (int)half_fov_h : h;

        int cols = (int)std::floor((float)w / fov_w) + 1;
        int rows = (int)std::floor((float)h / fov_h) + 1;
        float current_gap_col = fov_w - (float)(w - fov_w) / (cols - 1);
        float current_gap_row = fov_h - (float)(h - fov_h) / (rows - 1);
        if (current_gap_col < gap_w)
        {
            cols = (int)std::floor((float)(w - fov_w) / (fov_w - current_gap_col)) + 1;
        }
        if (current_gap_row < gap_h)
        {
            rows = (int)std::floor((float)(h - fov_h) / (fov_h - current_gap_row)) + 1;
        }
        float step_w = (float)w / cols;
        float step_h = (float)h / rows;

        std::vector<cv::Point2f> res;
        for (int i = 0; i < cols; ++i)
        {
            for (int j = 0; j < rows; ++j)
            {
                float x = c_x_min + i * step_w;
                float y = c_y_min + j * step_h;

                // 确保坐标不会超出边界
                x = std::min(static_cast<float>(c_x_max), std::max(static_cast<float>(c_x_min), x));
                y = std::min(static_cast<float>(c_y_max), std::max(static_cast<float>(c_y_min), y));

                res.emplace_back(region.x + x, region.y + y);
            }
        }
        res.swap(dst);
        return true;
    }

    static bool FindMinRectRecoverAll0(std::vector<Fov>& fovs, std::vector<ObjectOutput> objects,
        const std::vector<ObjectInput>& rrs,
        //  const std::vector<cv::RotatedRect>& rrs, 
        const cv::Rect& region, int fov_w, int fov_h)
    {
        using IndexType = std::string;
        const int min_gap = 200; // 重合部分的最小间隔

        std::vector<Fov> curRC;

        // const int& tlx = region.x;
        // const int& tly = region.y;
        const int& w = region.width;
        const int& h = region.height;

        const float half_fov_w = fov_w * 0.5f;
        const float half_fov_h = fov_h * 0.5f;
        /*中心移动限制*/
        const int c_x_min = (int)half_fov_w;
        const int c_x_max = w - (int)half_fov_w;
        const int c_y_min = (int)half_fov_h;
        const int c_y_max = h - (int)half_fov_h;
        (void)c_x_min;
        (void)c_x_max;
        (void)c_x_min;
        (void)c_y_min;
        (void)c_y_max;
        std::unordered_map<IndexType, cv::Rect> es, ebig;
        // std::vector<cv::Rect> ebig;
        // std::vector<cv::Rect> es; // 所有有效的元素集合
        es.reserve(rrs.size());
        /*转成外接矩形*/
        for (auto& oi : rrs)
        {
            cv::Rect br = oi.rr.boundingRect();

            if (br.width > fov_w || br.height > fov_h)
            {
                // ebig.emplace_back(br);
                ebig.emplace(oi.id, br);
                continue;
            }
            else if (!isRectInRect(br, region))
            {
                /*不在区域内的无效元素*/
                continue;
            }
            // es.emplace_back(br);
            es.emplace(oi.id, br);
        }
        int size = (int)es.size();
        if (size <= 0)
        {
            std::cout << "no valid rect\n";
            return false;
        }

        /*边界限制*/
        // float r_x_min = FLT_MAX;
        // float r_y_min = FLT_MAX;
        // float r_x_max = -FLT_MAX;
        // float r_y_max = -FLT_MAX;
        // for (auto& br : es)
        // {
        //     if (r_x_min > br.x)
        //         r_x_min = (float)br.x;
        //     if (r_x_max < br.x + br.width)
        //         r_x_max = (float)(br.x + br.width);
        //     if (r_y_min > br.y)
        //         r_y_min = (float)br.y;
        //     if (r_y_max < br.y + br.height)
        //         r_y_max = (float)(br.y + br.height);
        // }

        std::vector<IndexType> es_visit; // 保留原始索引
        std::unordered_map<IndexType, bool> bool_visit;
        // std::vector<bool> bool_visit(size, false);
        es_visit.reserve(size);
        for (auto& e : es)
        {
            es_visit.emplace_back(e.first);
        }
        // for (int i = 0; i < size; ++i)
        // {
        //     es_visit[i] = i;
        // }

        int max_count = 0;
        /*为大的先构造不可变FOV*/
        for (auto& e : ebig)
            // for (int i = 0; i < ebig.size(); ++i)
        {
            auto& br = e.second;
            auto rmrs = makeRectMapRect(br, fov_w - min_gap, fov_h - min_gap);

            std::vector<int> fovids;
            fovids.reserve(rmrs.size());

            for (const auto& rect : rmrs)
            {
                cv::Point2f center(rect.x + rect.width * 0.5f, rect.y + rect.height * 0.5f);
                // TODO 优化位置
                // /*将移动限制在可移动区间内部*/
                // new_center.x = std::min((float)c_x_max, std::max((float)c_x_min, new_center.x));
                // new_center.y = std::min((float)c_y_max, std::max((float)c_y_min, new_center.y));
                // cv::Rect c_fov((int)(new_center.x - half_fov_w), (int)(new_center.y - half_fov_h), fov_w, fov_h);
                // std::vector<cv::Rect> trects;
                // for (int j = 0; j < es_visit.size(); ++j)
                // {
                //     if (bool_visit[es_visit[j]])
                //         continue;
                //     if (isRectInRect(es[j], c_fov))
                //     {
                //         bool_visit[es_visit[j]] = false;
                //         trects.emplace_back(es[j]);
                //         max_count++;
                //     }
                // }
                int fovid = (int)curRC.size();
                fovids.emplace_back(fovid);
                curRC.emplace_back(Fov(fovid, center, {}, true));
            }
            ObjectOutput oo;
            oo.fovid = -1;
            oo.fovids.swap(fovids);
            oo.id = e.first;
            objects.emplace_back(oo);
        }

        std::vector<cv::Rect> fov_rects;                // fov的矩形
        std::vector<std::vector<IndexType>> e_i_in_fovs;      // 每个fov拥有的元素索引

        while (max_count < size)
        {
            // 从左上角开始
            int min_x = w;
            int min_y = h;
            for (size_t i = 0; i < es_visit.size(); ++i)
            {
                // if (bool_visit[es_visit[i]])
                //     continue;
                if (bool_visit.find(es_visit[i]) != bool_visit.end())
                    continue;
                auto& te = es[es_visit[i]];
                if (min_x > te.x)
                {
                    min_x = te.x;
                }
                if (min_y > te.y)
                {
                    min_y = te.y;
                }
            }

            cv::Rect l_long(min_x, min_y, w, fov_h); // 行搜索区域

            std::vector<IndexType> cur_contain; // 当前行包含的元素索引
            for (size_t i = 0; i < es_visit.size(); ++i)
            {
                // if (bool_visit[es_visit[i]])
                //     continue;
                if (bool_visit.find(es_visit[i]) != bool_visit.end())
                    continue;
                auto& te = es[es_visit[i]];
                // auto& te = es[es_visit[i]];
                if (isRectInRect(te, l_long))
                {
                    cur_contain.emplace_back(es_visit[i]);
                }
            }

            int count = 0;
            while (count < cur_contain.size())
            {
                int make_x = w; // 获取左上角坐标
                int make_y = h;
                for (int i = 0; i < cur_contain.size(); ++i)
                {
                    // if (bool_visit[cur_contain[i]])
                    //     continue;
                    if (bool_visit.find(cur_contain[i]) != bool_visit.end())
                        continue;

                    auto& te = es[cur_contain[i]];
                    if (make_x > te.x)
                    {
                        make_x = te.x;
                    }
                }
                cv::Rect l_h(make_x, min_y, fov_w, fov_h); // 先固定x
                for (int i = 0; i < cur_contain.size(); ++i)
                {
                    // if (bool_visit[cur_contain[i]])
                    //     continue;  
                    if (bool_visit.find(cur_contain[i]) != bool_visit.end())
                        continue;

                    auto& te = es[cur_contain[i]];
                    if (isRectInRect(te, l_h) && make_y > te.y)
                    {
                        make_y = te.y;
                    }
                }
                cv::Rect l_fov(make_x, make_y, fov_w, fov_h); //再固定y

                std::vector<IndexType> e_i_in_fov;
                for (auto& e : es)
                {
                    auto& id = e.first;
                    // if (bool_visit[es_visit[i]])
                    //     continue;
                    if (bool_visit.find(id) != bool_visit.end())
                        continue;
                    auto& te = e.second;
                    if (isRectInRect(te, l_fov))
                    {
                        e_i_in_fov.emplace_back(id);
                    }
                }

                if (e_i_in_fov.empty()) // 报错
                {
                    //{
                    //    for (int i = 0; i < fovs.size(); ++i)
                    //    {
                    //        Fov rc;
                    //        //rc.center = cv::Point2f(fovs[i].x + fovs[i].width * 0.5f, fovs[i].y + fovs[i].height * 0.5f);
                    //        rc.center = fovs[i].center;
                    //        rc.haveBig = false;
                    //        for (int j = 0; j < e_i_in_fovs[i].size(); ++j)
                    //        {
                    //            rc.coveredRectangles.emplace_back(es[e_i_in_fovs[i][j]]);
                    //        }
                    //        curRC.emplace_back(rc);
                    //    }
                    //    fovs.swap(curRC);
                    //    //dst = curRC;
                    //}
                    std::cout << "error:1\n";
                    return false;
                }

                for (int i = 0; i < e_i_in_fov.size(); ++i) // 标记为访问
                {
                    bool_visit[e_i_in_fov[i]] = true;
                }
                count += (int)e_i_in_fov.size();
                e_i_in_fovs.emplace_back(std::move(e_i_in_fov));

                fov_rects.emplace_back(l_fov);
            }
            max_count += count;
        }

        /*修正到重心位置*/

        {
            int fov_size = (int)fov_rects.size();
            std::vector<cv::Rect> tfovs(fov_size);
            for (int i = 0; i < fov_size; ++i)
            {
                auto& new_e_i_in_fov = e_i_in_fovs[i];
                float fx = 0;
                float fy = 0;
                // 计算重心
                {
                    float x_q = 0;
                    float y_q = 0;
                    for (auto& e_i : new_e_i_in_fov)
                    {
                        auto& e = es[e_i];
                        float e_r_c_x = e.x + e.width * 0.5f;
                        float e_r_c_y = e.y + e.height * 0.5f;
                        auto area = e.area();
                        x_q += area;
                        y_q += area;
                        fx += e_r_c_x * area;
                        fy += e_r_c_y * area;
                    }
                    fx /= x_q;
                    fy /= y_q;
                }
                int min_x, min_y, max_x, max_y;
                // 计算移动区间
                {
                    min_x = w + 1;
                    min_y = h + 1;
                    max_x = -1;
                    max_y = -1;
                    for (auto& e_i : new_e_i_in_fov)
                    {
                        auto& e = es[e_i];
                        if (min_x > e.x)
                            min_x = e.x;
                        if (max_x < e.x + e.width)
                            max_x = e.x + e.width;
                        if (min_y > e.y)
                            min_y = e.y;
                        if (max_y < e.y + e.height)
                            max_y = e.y + e.height;
                    }
                }
                cv::Rect new_tr(static_cast<int>(fx - half_fov_w), static_cast<int>(fy - half_fov_h),
                    fov_w, fov_h);

                new_tr = limitRectTLInBoundary(new_tr, { max_x - fov_w, max_y - fov_h, (min_x - max_x + fov_w * 2), (min_y - max_y + fov_h * 2) }); // 满足移动区间的最小fov移动范围
                new_tr = limitRectTLInBoundary(new_tr, { 0,0,w,h });
                tfovs[i] = new_tr;
            }

            // 刷新包含元素
            {
                std::vector<IndexType> new_es_visit;
                std::unordered_map<IndexType, bool> new_bool_visit;
                new_es_visit.reserve(size);
                for (auto& e : es)
                {
                    new_es_visit.emplace_back(e.first);
                }
                //std::vector<bool> visit(size);
                std::vector<std::vector<IndexType>> t_e_i_in_fovs(fov_size);
                for (int i = 0; i < fov_size; ++i)
                {
                    // auto& r = es[i];
                    //  当前fov覆盖元素
                    for (int j = 0; j < size; ++j)
                    {
                        auto& id = new_es_visit[j];
                        if (new_bool_visit.find(id) != new_bool_visit.end())
                            continue;
                        if (isRectInRect(es[id], tfovs[i]))
                        {
                            new_bool_visit[id] = true;
                            t_e_i_in_fovs[i].emplace_back(id);
                        }
                    }
                }

                tfovs.swap(fov_rects);
                t_e_i_in_fovs.swap(e_i_in_fovs);
            }
        }
        /*生成结果数据*/
        {
            for (int i = 0; i < fov_rects.size(); ++i)
            {
                Fov rc;
                rc.fovid = (int)curRC.size();
                rc.center = cv::Point2f(fov_rects[i].x + fov_rects[i].width * 0.5f, fov_rects[i].y + fov_rects[i].height * 0.5f);
                rc.haveBig = false;
                for (int j = 0; j < e_i_in_fovs[i].size(); ++j)
                {
                    ObjectOutput oo;
                    oo.id = e_i_in_fovs[i][j];
                    oo.fovid = rc.fovid;
                    objects.emplace_back(oo);

                    rc.coveredRectangles.emplace_back(oo);
                }
                curRC.emplace_back(rc);
            }
            fovs.swap(curRC);

            // dst = curRC;
        }
        return true;
    }

    // static bool FindMinRectRecoverAll4(std::vector<Fov>& dst, const std::vector<cv::RotatedRect>& rrs, const cv::Rect& region, int fov_w, int fov_h)
    //{
    //     std::vector<Fov> curRC;

    //    const int& w = region.width;
    //    const int& h = region.height;

    //    const float half_fov_w = fov_w * 0.5f;
    //    const float half_fov_h = fov_h * 0.5f;
    //    /*中心移动限制*/
    //    const int c_x_min = (int)half_fov_w;
    //    const int c_x_max = w - (int)half_fov_w;
    //    const int c_y_min = (int)half_fov_h;
    //    const int c_y_max = h - (int)half_fov_h;

    //    std::vector<cv::Rect> es; // 所有有效的元素集合
    //    es.reserve(rrs.size());
    //    /*转成外接矩形*/
    //    for (auto& rr : rrs) {
    //        cv::Rect br = rr.boundingRect();

    //        if (br.width > fov_w || br.height > fov_h)
    //        {
    //            /*为大的先构造不可变FOV*/
    //            auto rmrs = makeRectMapRect(br, fov_w, fov_h);
    //            for (const auto& rect : rmrs) {
    //                cv::Point new_center(rect.x + rect.width * 0.5f, rect.y + rect.height * 0.5f);
    //                /*将移动限制在可移动区间内部*/
    //                new_center.x = std::min(c_x_max, std::max(c_x_min, new_center.x));
    //                new_center.y = std::min(c_y_max, std::max(c_y_min, new_center.y));

    //                curRC.emplace_back(Fov(
    //                    new_center, {}, true));
    //            }
    //            continue;
    //        }
    //        else if (!isRectInRect(br, region))
    //        {
    //            /*不在区域内的无效元素*/
    //            continue;
    //        }
    //        es.emplace_back(br);
    //    }

    //    int size = (int)es.size();
    //    if (size <= 0)
    //    {
    //        std::cout << "no valid rect\n";
    //        return false;
    //    }

    //    /*边界限制*/
    //    float r_x_min = FLT_MAX;
    //    float r_y_min = FLT_MAX;
    //    float r_x_max = -FLT_MAX;
    //    float r_y_max = -FLT_MAX;
    //    for (auto& br : es)
    //    {
    //        if (r_x_min > br.x)
    //            r_x_min = (float)br.x;
    //        if (r_x_max < br.x + br.width)
    //            r_x_max = (float)(br.x + br.width);
    //        if (r_y_min > br.y)
    //            r_y_min = (float)br.y;
    //        if (r_y_max < br.y + br.height)
    //            r_y_max = (float)(br.y + br.height);
    //    }
    //    std::unordered_set<int> all_e_set;
    //    for (int i = 0; i < es.size(); ++i)
    //    {
    //        all_e_set.insert(i);
    //    }
    //    cv::Rect boundary((int)r_x_min, (int)r_y_min, (int)(r_x_max - r_x_min), (int)(r_y_max - r_y_min));
    //    std::vector<cv::Rect> fovs; //fov坐标
    //    std::vector<std::vector<int>> e_i_in_fovs; //每个fov拥有的元素索引
    //    for (int i = 0; i < size; ++i)
    //    {
    //        auto& r = es[i];
    //        std::vector<cv::Rect> trs
    //        {
    //            cv::Rect((int)(r.x + r.width * 0.5f - half_fov_w), (int)(r.y + r.height * 0.5f - half_fov_h), fov_w, fov_h),//中心
    //            cv::Rect((int)(r.x + half_fov_w),(int)(r.y + half_fov_h), fov_w, fov_h),//右下
    //            cv::Rect((int)(r.x + r.width - half_fov_w), (int)(r.y + half_fov_h),fov_w, fov_h),//左下
    //            cv::Rect((int)(r.x + half_fov_w), (int)(r.y + r.height * 0.5f - half_fov_h),fov_w, fov_h),//右上
    //            cv::Rect((int)(r.x + r.width - half_fov_w), (int)(r.y + r.height * 0.5f - half_fov_h),fov_w, fov_h),//右下
    //        };

    //        for (auto& tr : trs)
    //        {
    //            std::vector<int> t_e_i_in_fov;
    //            tr = limitRectTLInBoundary(tr, boundary);
    //            fovs.emplace_back(tr);
    //            //fovs[i] = tr;
    //            // 当前fov覆盖元素
    //            for (int j = 0; j < size; ++j)
    //            {
    //                if (isRectInRect(es[j], tr))
    //                {
    //                    t_e_i_in_fov.emplace_back(j);
    //                }
    //            }
    //            e_i_in_fovs.emplace_back(std::move(t_e_i_in_fov));
    //        }
    //    }

    //    /*最小集合覆盖*/
    //    {
    //        std::vector<std::unordered_set<int>> sub_sets;
    //        for (auto& e_i : e_i_in_fovs)
    //        {
    //            sub_sets.emplace_back(std::unordered_set<int>(e_i.begin(), e_i.end()));
    //        }
    //        std::vector<int> min_sub_set = greedySetCover(all_e_set, sub_sets);

    //        std::vector<cv::Rect> new_fov2;
    //        std::vector<std::vector<int>> new_e_i_in_fovs2;
    //        for (auto& i : min_sub_set)
    //        {
    //            new_fov2.emplace_back(fovs[i]);
    //            new_e_i_in_fovs2.emplace_back(e_i_in_fovs[i]);
    //        }
    //        new_fov2.swap(fovs);
    //        new_e_i_in_fovs2.swap(e_i_in_fovs);
    //    }

    //    /*修正到重心位置*/
    //    {
    //        int fov_size = fovs.size();
    //        std::vector<cv::Rect> tfovs(fov_size);
    //        for (int i = 0; i < fov_size; ++i)
    //        {
    //            auto& new_e_i_in_fov = e_i_in_fovs[i];
    //            float fx = 0;
    //            float fy = 0;
    //            //计算重心
    //            {
    //                float x_q = 0;
    //                float y_q = 0;
    //                for (auto& e_i : new_e_i_in_fov)
    //                {
    //                    auto& e = es[e_i];
    //                    float e_r_c_x = e.x + e.width * 0.5f;
    //                    float e_r_c_y = e.y + e.height * 0.5f;
    //                    auto area = e.area();
    //                    x_q += area;
    //                    y_q += area;
    //                    fx += e_r_c_x * area;
    //                    fy += e_r_c_y * area;
    //                }
    //                fx /= x_q;
    //                fy /= y_q;
    //            }
    //            int min_x, min_y, max_x, max_y;
    //            //计算移动区间
    //            {
    //                min_x = w + 1;
    //                min_y = h + 1;
    //                max_x = -1;
    //                max_y = -1;
    //                for (auto& e_i : new_e_i_in_fov)
    //                {
    //                    auto& e = es[e_i];
    //                    if (min_x > e.x)
    //                        min_x = e.x;
    //                    if (max_x < e.x + e.width)
    //                        max_x = e.x + e.width;
    //                    if (min_y > e.y)
    //                        min_y = e.y;
    //                    if (max_y < e.y + e.height)
    //                        max_y = e.y + e.height;
    //                }
    //            }
    //            cv::Rect new_tr(static_cast<int>(fx - half_fov_w), static_cast<int>(fy - half_fov_h),
    //                fov_w, fov_h);

    //            //new_tr = limitRectTLInBoundary(new_tr, {
    //            //    cv::Point(
    //            //        std::max((int)r_x_min,max_x - fov_w),
    //            //                std::max((int)r_y_min,max_y - fov_h)
    //            //                ),
    //            //    cv::Point(
    //            //                std::min((int)r_x_max,(min_x - max_x + fov_w * 2)),
    //            //             std::min((int)r_y_max,(min_y - max_y + fov_h * 2))
    //            //    )
    //            //    });
    //            //new_tr = limitRectTLInBoundary(new_tr,boundary);
    //            //tr = limitRectTLInBoundary(tr, { 0,0,w,h });
    //            new_tr = limitRectTLInBoundary(new_tr, { max_x - fov_w,max_y - fov_h,(min_x - max_x + fov_w * 2),(min_y - max_y + fov_h * 2) }); // 满足移动区间的最小fov移动范围

    //            tfovs[i] = new_tr;
    //        }

    //        {
    //            std::vector<std::vector<int>> t_e_i_in_fovs(fov_size);
    //            for (int i = 0; i < fov_size; ++i)
    //            {
    //                auto& r = es[i];
    //                // 当前fov覆盖元素
    //                for (int j = 0; j < size; ++j)
    //                {
    //                    if (isRectInRect(es[j], tfovs[i]))
    //                    {
    //                        t_e_i_in_fovs[i].emplace_back(j);
    //                    }
    //                }
    //            }

    //            //fovs.insert(fovs.end(), tfovs.begin(), tfovs.end());
    //            //e_i_in_fovs.insert(e_i_in_fovs.end(), t_e_i_in_fovs.begin(), t_e_i_in_fovs.end());

    //            tfovs.swap(fovs);
    //            t_e_i_in_fovs.swap(e_i_in_fovs);
    //        }
    //    }
    //    /*最小集合覆盖*/

    //    {
    //        std::vector<std::unordered_set<int>> sub_sets;
    //        for (auto& e_i : e_i_in_fovs)
    //        {
    //            sub_sets.emplace_back(std::unordered_set<int>(e_i.begin(), e_i.end()));
    //        }
    //        std::vector<int> min_sub_set = greedySetCover(all_e_set, sub_sets);

    //        std::vector<cv::Rect> new_fov2;
    //        std::vector<std::vector<int>> new_e_i_in_fovs2;
    //        for (auto& i : min_sub_set)
    //        {
    //            new_fov2.emplace_back(fovs[i]);
    //            new_e_i_in_fovs2.emplace_back(e_i_in_fovs[i]);
    //        }
    //        {
    //            //std::vector<bool> visit(size);
    //            //for (int i = 0; i < min_sub_set.size(); ++i)
    //            //{
    //            //    std::vector<int> new_e_i_in_fovs;
    //            //    for (int j = 0; j < e_i_in_fovs[i].size(); ++j)
    //            //    {
    //            //        if (!visit[e_i_in_fovs[i][j]])
    //            //        {
    //            //            visit[e_i_in_fovs[i][j]] = true;
    //            //            new_e_i_in_fovs.emplace_back(e_i_in_fovs[i][j]);
    //            //        }
    //            //    }
    //            //    if (new_e_i_in_fovs.empty()) continue;
    //            //    new_e_i_in_fovs2.emplace_back(std::move(new_e_i_in_fovs));
    //            //    new_fov2.emplace_back(fovs[min_sub_set[i]]);
    //            //}
    //        }
    //        new_fov2.swap(fovs);
    //        new_e_i_in_fovs2.swap(e_i_in_fovs);

    //    }

    //    /*生成结果数据*/
    //    {
    //        for (int i = 0; i < fovs.size(); ++i)
    //        {
    //            Fov rc;
    //            rc.center = cv::Point2f(fovs[i].x + fovs[i].width * 0.5f, fovs[i].y + fovs[i].height * 0.5f);
    //            rc.haveBig = false;
    //            for (int j = 0; j < e_i_in_fovs[i].size(); ++j)
    //            {
    //                rc.coveredRectangles.emplace_back(es[e_i_in_fovs[i][j]]);
    //            }
    //            curRC.emplace_back(rc);
    //        }
    //        dst = curRC;
    //    }
    //    return true;
    //}

    /**
     * @brief  点集排序.
     *
     * @fun    SortPoints
     * @param  points
     * @param  mode 排序模式 参见 GridSortMode
     * @param  gap 组间距
     * @return
     *
     * @date   2024.07.26
     * <AUTHOR>
     */
    template <typename T>
    static bool SortPoints(std::vector<cv::Point_<T>>& points, int mode, int gap)
    {
        using SortFunc = std::function<bool(const cv::Point_<T>&, const cv::Point_<T>&)>;
        auto sort_desc_x = [](const cv::Point_<T>& a, const cv::Point_<T>& b) -> bool
            {
                return a.x > b.x;
            };
        auto sort_asc_x = [](const cv::Point_<T>& a, const cv::Point_<T>& b) -> bool
            {
                return a.x < b.x;
            };
        auto sort_desc_y = [](const cv::Point_<T>& a, const cv::Point_<T>& b) -> bool
            {
                return a.y > b.y;
            };
        auto sort_asc_y = [](const cv::Point_<T>& a, const cv::Point_<T>& b) -> bool
            {
                return a.y < b.y;
            };

        if (points.empty())
            return false;

        std::vector<cv::Point_<T>> dst = points;
        int n = (int)dst.size();

        SortFunc sort_1 = sort_desc_x;
        SortFunc sort_2 = sort_desc_y;

        switch (GridSortMode(mode))
        {
        case FROM_TOP_TO_BOTTOM:
            sort_1 = sort_asc_y;
            sort_2 = sort_desc_x;
            break;
        case FROM_LEFT_TO_RIGHT:
            sort_1 = sort_asc_x;
            sort_2 = sort_asc_y;
            break;
        case FROM_BOTTOM_TO_TOP:
            sort_1 = sort_desc_y;
            sort_2 = sort_desc_x;
            break;
        case FROM_RIGHT_TO_LEFT:
            sort_1 = sort_desc_x;
            sort_2 = sort_asc_y;
            break;
        default:
            std::cout << "Unknown sorting mode" << std::endl;
            return false;
        }

        std::sort(dst.begin(), dst.end(), sort_1);

        std::vector<std::vector<cv::Point_<T>>> grid;
        {
            int j = -1;
            int i = 0;
            while (i < n)
            {
                j = i;
                std::vector<cv::Point_<T>> tg;
                for (; i < n; ++i)
                {
                    int tgap = 999;
                    switch (GridSortMode(mode))
                    {
                    case FROM_TOP_TO_BOTTOM:
                    case FROM_BOTTOM_TO_TOP:
                        tgap = (int)abs(dst[i].y - dst[j].y);
                        break;
                    case FROM_LEFT_TO_RIGHT:
                    case FROM_RIGHT_TO_LEFT:
                        tgap = (int)abs(dst[i].x - dst[j].x);
                        break;
                    }
                    if (tgap < gap)
                    {
                        tg.emplace_back(dst[i]);
                    }
                    else
                    {
                        j = -1;
                        break;
                    }
                }
                if (!tg.empty())
                {
                    grid.emplace_back(tg);
                }
            }
        }
        for (auto& tg : grid)
        {
            std::sort(tg.begin(), tg.end(), sort_2);
        }

        std::vector<cv::Point_<T>> tpoints;
        tpoints.reserve(n);
        for (int i = 0; i < grid.size(); ++i)
        {
            if (i % 2 == 0)
            {
                for (int j = 0; j < grid[i].size(); ++j)
                {
                    tpoints.emplace_back(grid[i][j]);
                }
            }
            else
            {
                for (int j = (int)grid[i].size() - 1; j >= 0; --j)
                {
                    tpoints.emplace_back(grid[i][j]);
                }
            }
        }
        tpoints.swap(points);

        return true;
    }

    static bool SortFovs(std::vector<Fov>& fovs, int mode, int gap)
    {
        using SortFunc = std::function<bool(const Fov&, const Fov&)>;

        auto sort_desc_x = [](const Fov& a, const Fov& b) -> bool
            {
                return a.center.x > b.center.x;
            };
        auto sort_asc_x = [](const Fov& a, const Fov& b) -> bool
            {
                return a.center.x < b.center.x;
            };
        auto sort_desc_y = [](const Fov& a, const Fov& b) -> bool
            {
                return a.center.y > b.center.y;
            };
        auto sort_asc_y = [](const Fov& a, const Fov& b) -> bool
            {
                return a.center.y < b.center.y;
            };

        if (fovs.empty())
            return false;

        std::vector<Fov> dst = fovs;
        int n = static_cast<int>(dst.size());

        SortFunc sort_1 = sort_desc_x;
        SortFunc sort_2 = sort_desc_y;

        switch (GridSortMode(mode))
        {
        case FROM_TOP_TO_BOTTOM:
            sort_1 = sort_asc_y;
            sort_2 = sort_desc_x;
            break;
        case FROM_LEFT_TO_RIGHT:
            sort_1 = sort_asc_x;
            sort_2 = sort_asc_y;
            break;
        case FROM_BOTTOM_TO_TOP:
            sort_1 = sort_desc_y;
            sort_2 = sort_desc_x;
            break;
        case FROM_RIGHT_TO_LEFT:
            sort_1 = sort_desc_x;
            sort_2 = sort_asc_y;
            break;
        default:
            std::cout << "Unknown sorting mode" << std::endl;
            return false;
        }

        std::sort(dst.begin(), dst.end(), sort_1);

        std::vector<std::vector<Fov>> grid;
        {
            int j = -1;
            int i = 0;
            while (i < n)
            {
                j = i;
                std::vector<Fov> tg;
                for (; i < n; ++i)
                {
                    int tgap = 999;
                    switch (GridSortMode(mode))
                    {
                    case FROM_TOP_TO_BOTTOM:
                    case FROM_BOTTOM_TO_TOP:
                        tgap = static_cast<int>(std::abs(dst[i].center.y - dst[j].center.y));
                        break;
                    case FROM_LEFT_TO_RIGHT:
                    case FROM_RIGHT_TO_LEFT:
                        tgap = static_cast<int>(std::abs(dst[i].center.x - dst[j].center.x));
                        break;
                    }
                    if (tgap < gap)
                    {
                        tg.emplace_back(dst[i]);
                    }
                    else
                    {
                        j = -1;
                        break;
                    }
                }
                if (!tg.empty())
                {
                    grid.emplace_back(tg);
                }
            }
        }

        for (auto& tg : grid)
        {
            std::sort(tg.begin(), tg.end(), sort_2);
        }

        std::vector<Fov> tpoints;
        tpoints.reserve(n);
        for (int i = 0; i < static_cast<int>(grid.size()); ++i)
        {
            if (i % 2 == 0)
            {
                for (const auto& fov : grid[i])
                {
                    tpoints.emplace_back(fov);
                }
            }
            else
            {
                for (auto it = grid[i].rbegin(); it != grid[i].rend(); ++it)
                {
                    tpoints.emplace_back(*it);
                }
            }
        }
        tpoints.swap(fovs);

        return true;
    }

    template <typename T>
    static bool SortPointsGreedyNearestNeighbor(std::vector<cv::Point_<T>>& points, const cv::Point_<T>& start)
    {
        std::vector<bool> visited(points.size(), false);
        std::vector<cv::Point_<T>> sortedPoints;
        cv::Point_<T> cur = start;
        while (sortedPoints.size() < points.size())
        {
            long long mind = std::numeric_limits<long long>::max();
            int idx = -1;
            for (size_t i = 0; i < points.size(); ++i)
            {
                if (visited[i])
                    continue;
                auto d = manhattanlength(points[i], cur);
                if (mind > d)
                {
                    mind = d;
                    idx = (int)i;
                }
            }
            if (idx < 0)
            {
                break;
            }
            visited[idx] = true;
            cur = points[idx];
            sortedPoints.push_back(cur);
        }
        points.swap(sortedPoints);
        return true;
    }
};

#endif //! CALCFOV_H