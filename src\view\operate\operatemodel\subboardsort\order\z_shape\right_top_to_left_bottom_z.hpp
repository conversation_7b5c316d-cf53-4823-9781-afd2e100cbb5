/*****************************************************************
 * @file   right_top_to_left_bottom_z.hpp
 * @brief  从右上到左下  z字形排序
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class ZRightTopToLeftBottom :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int subboard_id = 1;
            for (auto& subboards : subboards_)
            {
                for (auto& subboard : subboards)
                {
                    UpdateSubboard(subboard, subboard_id++);
                }
            }
            return jrscore::AOI_OK;
        }
    };
}
