/*****************************************************************//**
 * @file   callbackhelper.hpp
 * @brief  订阅回调函数
 * @details    
 * <AUTHOR>
 * @date 2024.1.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.18         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef _CALLBACKHELPER_H__
#define _CALLBACKHELPER_H__
#include <iostream>
#include <any>
#include <functional>
#include <mutex>

namespace jrscore
{
    template<typename ...Args>
    using CallBackFunction = std::function<int(Args ...)>;

    //用于管理每个回调
    class  SubscriberCallbackHelper
    {
    public:
        virtual ~SubscriberCallbackHelper ()
        {
        }
        /**
         * @fun CallBack 
         * @brief 执行回调函数
         * @param params
         * @return 返回执行后状态，后期改为错误码
         * @date 2024.1.18
         * <AUTHOR>
         */
        
        virtual int CallBack (const std::vector<std::any>& args) = 0;
        /**
         * @fun GetModuleName 
         * @brief 获取模块名称
         * @return 模块名称
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual const std::string& GetModuleName ()const = 0;
        /**
       * @fun GetTopicName
       * @brief 获取主题名称
       * @return 主题名称
       * @date 2024.1.18
       * <AUTHOR>
       */
        virtual const std::string& GetTopicName ()const = 0;
        /**
       * @fun GetSubName
       * @brief 获取订阅者名称
       * @return 订阅者名称
       * @date 2024.1.18
       * <AUTHOR>
       */
        virtual const std::string& GetSubName ()const = 0;
    };

    template <typename ...Args>
    class SubScriberCallBackHelperImpl :public SubscriberCallbackHelper
    {
    public:

        SubScriberCallBackHelperImpl (const CallBackFunction<Args...> call, const std::string& module_name_, const std::string& topic_name_,const std::string& sub_name_)
            :callback (call)
            , module_name (module_name_)
            , topic_name (topic_name_)
            , sub_name (sub_name_)
        {
        }

        int CallBack (const std::vector<std::any>& args)override
        {
            try {
                if (args.size() != sizeof...(Args)) {
                    std::cerr << "Error: Size mismatch in CallBack. Expected " << sizeof...(Args)
                        << " arguments, but got " << args.size() << " arguments." << std::endl;
                    return -1;
                }
                return CallBackImpl(args, std::index_sequence_for<Args...>{});
            }
            catch (const std::bad_any_cast& e) {
                std::cerr << "Error in CallBack: " << e.what() << std::endl;
                return -1; // 后期改成返回错误码
            }
           
        }

        const std::string& GetModuleName ()const override
        {
            return module_name;
        }
        const std::string& GetTopicName ()const override
        {
            return topic_name;
        }
        const std::string& GetSubName ()const override
        {
            return sub_name;
        }

    private:
        template<std::size_t...I>
        int CallBackImpl(const std::vector<std::any>& args, std::index_sequence<I...>)
        {
          
            if (callback)
            {
                //TODO:后期可能改成std::variant进行参数转换
                std::lock_guard<std::recursive_mutex> lock(call_back_mutex_);

                return callback(std::any_cast<typename std::tuple_element <I, std::tuple<Args...>>::type>(args[I])...);

            }
            return -1;
        }
        CallBackFunction<Args ...> callback;
        std::string module_name;
        std::string topic_name;
        std::string sub_name;
        std::recursive_mutex call_back_mutex_;

    };

    using SubscribeCallbackHelperPtr = std::shared_ptr<SubscriberCallbackHelper>;


    template <typename ...Args>
    class InvokeCallBackHelperImpl :public SubscriberCallbackHelper
    {
        public:

        InvokeCallBackHelperImpl ( const CallBackFunction<Args...> call )
            :callback ( call )
        {
        }

        int CallBack ( const std::vector<std::any>& args )override
        {
            try
            {
                if (args.size () != sizeof...( Args ))
                {
                    std::cerr << "Error: Size mismatch in CallBack. Expected " << sizeof...( Args )
                        << " arguments, but got " << args.size () << " arguments." << std::endl;
                    return -1;
                }
                return CallBackImpl ( args , std::index_sequence_for<Args...>{} );
            }
            catch (const std::bad_any_cast& e)
            {
                std::cerr << "Error in CallBack: " << e.what () << std::endl;
                return -1; // 后期改成返回错误码
            }

        }

        const std::string& GetModuleName ()const override
        {
            return "";
        }
        const std::string& GetTopicName ()const override
        {
            return "";
        }
        const std::string& GetSubName ()const override
        {
            return "";
        }

        private:
        template<std::size_t...I>
        int CallBackImpl ( const std::vector<std::any>& args , std::index_sequence<I...> )
        {

            if (callback)
            {
                //TODO:后期可能改成std::variant进行参数转换
                return callback ( std::any_cast< typename std::tuple_element <I , std::tuple<Args...>>::type >( args[I] )... );

            }
            return -1;
        }
        CallBackFunction<Args ...> callback;
    };

}


#endif // !_CALLBACKHELPER_H__
