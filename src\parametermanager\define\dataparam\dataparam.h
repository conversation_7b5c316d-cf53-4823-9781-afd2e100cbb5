﻿/*****************************************************************
* @file   dataparam.hpp
* @brief  存入文件的数据，定义等
* @details
* <AUTHOR>
* @date 2024.8.18
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
* <tr><td>2024.8.8          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/
#ifndef __JRSDATAPARAM_H__
#define __JRSDATAPARAM_H__

//PREBUILD
//#include "pch.h"
#include "dbparam.h"
#include "parambase.hpp"
#include "projectparam.hpp"
#include "detectresultparam.hpp"
//thirdparty
#pragma warning(push, 1)
#include "opencv2/opencv.hpp"
#pragma warning(pop)

namespace jrsdata
{
    namespace jrsconfig {
        static constexpr char DEMO_SETING[] = "demo_setting";
        static constexpr char MOTION_SETING[] = "motion_setting";
    }

    /**< 一个检测框数据 */
    struct DetectWindowResult
    {
        jrsdatabase::jrstable::TDetectWindow t_detect_window; /**< 检测框信息*/
        cv::Mat algorithm_image;
        int  template_image_id;

        DetectWindowResult() :t_detect_window({}) {}
        DetectWindowResult(const jrsdatabase::jrstable::TDetectWindow& t_detect_window_)
            : t_detect_window(t_detect_window_) {
        }
        DetectWindowResult(const DetectWindowResult& other)
            : t_detect_window(other.t_detect_window), algorithm_image(other.algorithm_image), template_image_id(other.template_image_id) {
        }
        /**< 除数据表字段之外的数据 */
    };
    JRSREFLECTION(DetectWindowResult, t_detect_window);
    /**< 一个算法组数据 */
    struct GroupResult
    {
        jrsdatabase::jrstable::TGroup t_group;      /**< 组表数据 */
        std::vector<DetectWindowResult> detect_windows;  /**< 检测框表 */

        GroupResult()
            : t_group(), detect_windows() {
        }
        GroupResult(const jrsdatabase::jrstable::TGroup& t_group_,
            const std::vector<DetectWindowResult>& detect_windows_)
            : t_group(t_group_), detect_windows(detect_windows_) {
        }
        GroupResult(const GroupResult& other)
            : t_group(other.t_group), detect_windows(other.detect_windows) {
        }
    };
    JRSREFLECTION(GroupResult, t_group, detect_windows);

    /**@brief 元件运行或者建模时用于保存的信息 如标准图片 ，检测时的算法参数等 zhangyuyu 2025.1.9*/
    struct TemplateInfo
    {
        cv::Mat template_image;
        std::string template_color_param;
        int light_id;
    };
    struct ComponentSaveInfo :ViewParamBase
    {
        std::string barcode; //! 当前执行算法所属板子的二维码信息
        std::string current_time; //! 当前算法执行时的时间
        std::string project_name; //! 当前算法所执行时工程名称
        std::string component_name; //! 当前检测框对应的元件名称
        std::string subboard_name; //! 当前检测框检测的原件对应的子板名称
        std::string algo_name; //! 当前执行的算法名称
        //！输入参数
        std::string algo_execute_rect_param;//!当前算法执行时的检测框信息
        std::string algo_param;//!当前算法执行时的算参数信息
        std::unordered_map<int, cv::Mat> input_img; //! 算法执行时输入的图片
        double resolution_x; //! x方向分辨率
        double resolution_y; //! y方向分辨率

        //！输出参数
        bool algo_result_status;
        std::vector<TemplateInfo> template_info_vector;//!当前算法执行时的模板信息
        std::unordered_map<int, cv::Mat> hom_matrix; //! 当前算法执行时的变换矩阵
        cv::Mat output_mask_image;//!当前算法执行时输出的mask图片
        std::unordered_map<int, cv::Mat> result_img;

        ComponentSaveInfo()
            : barcode{}
            , current_time{}
            , project_name{}
            , component_name{}
            , subboard_name{}
            , algo_name{}
            , algo_execute_rect_param{}
            , algo_param{}
            , resolution_x{}
            , resolution_y{}
            , algo_result_status{ true }
            , hom_matrix({})
            , input_img({})
            , result_img({})

        {
        }
        ComponentSaveInfo(const ComponentSaveInfo& other)
            : barcode(other.barcode),
            current_time(other.current_time),
            project_name(other.project_name),
            component_name(other.component_name),
            subboard_name(other.subboard_name),
            algo_name(other.algo_name),
            algo_execute_rect_param(other.algo_execute_rect_param),
            algo_param(other.algo_param),
            resolution_x(other.resolution_x),
            resolution_y(other.resolution_y),
            algo_result_status(other.algo_result_status),
            hom_matrix(other.hom_matrix),
            input_img(other.input_img),
            result_img(other.result_img)
        {
        }
    };
    JRSREFLECTION(ComponentSaveInfo, algo_execute_rect_param, algo_param, resolution_x, resolution_y);
    using ComponentSaveInfoPtr = std::shared_ptr<ComponentSaveInfo>;

    /**<一个元件数据*/
    struct DeviceResult {
        std::unordered_map<int, cv::Mat> device_images;
        std::vector<ComponentSaveInfoPtr> component_algo_info;/**< 元件算法信息*/

        jrsdatabase::jrstable::TDevice t_device; /* 元件表数据 */
        std::vector<GroupResult> groups;               /**< 一个元件可能有多个检测框组 */

        DeviceResult()
            : t_device(), groups(), component_algo_info() {
        }
        DeviceResult(const jrsdatabase::jrstable::TDevice& t_device_,
            const std::vector<GroupResult>& groups_, const std::vector<ComponentSaveInfoPtr>& component_algo_info_)
            : t_device(t_device_), groups(groups_), component_algo_info(component_algo_info_) {
        }
        DeviceResult(const DeviceResult& other)
            : t_device(other.t_device), groups(other.groups), device_images(other.device_images), component_algo_info() {
            for (const auto& component : other.component_algo_info) {
                component_algo_info.push_back(std::make_shared<ComponentSaveInfo>(*component));
            }
        }
    };
    JRSREFLECTION(DeviceResult, t_device, groups);
    /**<一块子板数据*/
    struct SubboardResult
    {
        jrsdatabase::jrstable::TSubboard t_subboard; /**< 子板表数据 */
        std::vector<DeviceResult> devices;             /**< 对应多个元件*/

        SubboardResult()
            : t_subboard(), devices() {
        }
        SubboardResult(const jrsdatabase::jrstable::TSubboard& t_subboard_,
            const std::vector<DeviceResult>& devices_)
            : t_subboard(t_subboard_), devices(devices_) {
        }
        SubboardResult(const SubboardResult& other)
            : t_subboard(other.t_subboard), devices(other.devices) {
        }
    };
    JRSREFLECTION(SubboardResult, t_subboard, devices);
    /**<一块整版数据*/
    struct EntiretyBoardResult :ViewParamBase, DataBase
    {
        jrsdatabase::jrstable::TBoard board_info;                   /**< 板子数据 */
        std::vector<SubboardResult> subboards;                      /**< 子板数据 */
        jrsdatabase::jrstable::TUser user;                          /**< 用户信息 */
        jrsdatabase::jrstable::TProject project;                    /**< 项目信息 */
        std::optional<jrsdatabase::jrstable::TAOIMachine> aoi_sys;                  /**< AOI系统信息 */
        jrsdatabase::jrstable::TAOIMachineStatistics aoi_machien_statistics; /**< AOI机台统计信息*/
        EntiretyBoardResult()
            : board_info(), subboards(), user(), project(), aoi_sys(std::nullopt), aoi_machien_statistics()
        {
        }
        EntiretyBoardResult(const jrsdatabase::jrstable::TBoard& board_info_,
            const std::vector<SubboardResult>& subboards_,
            const jrsdatabase::jrstable::TUser& user_,
            const jrsdatabase::jrstable::TProject& project_,
            const jrsdatabase::jrstable::TAOIMachine& aoi_sys_,
            const jrsdatabase::jrstable::TAOIMachineStatistics& aoi_machien_statistics_)
            : board_info(board_info_), subboards(subboards_), user(user_),
            project(project_), aoi_sys(aoi_sys_), aoi_machien_statistics(aoi_machien_statistics_)
        {
        }
        //EntiretyBoardResult(const EntiretyBoardResult& other)
        //    : board_info(other.board_info), subboards(other.subboards),
        //    user(other.user), project(other.project), aoi_sys(other.aoi_sys)
        //
        //{}
    };
    using EntiretyBoardResultPtr = std::shared_ptr<EntiretyBoardResult>;
    JRSREFLECTION(EntiretyBoardResult, board_info, subboards, user, project, aoi_sys);

    /** 板子检测状态 */
    enum class BoardDetectStatus :int
    {

        NORMAL = 0, /**< 正常检测结束 */
        MARK_FAILURE = 1 /**< mark 异常导致整板未检测*/
    };
    //! 整板检测结果
    struct DetectResultParam :ViewParamBase
    {
        BoardDetectStatus board_detect_status;                          /**< 检测状态 */
        bool detect_result;                                             /**< 检测结果 */
        int track_id;                                                   /**< 轨道ID*/
        std::string board_code;                                         /**< 整板条码*/
        std::string start_detect_time;                                  /**< 开始检测时间*/
        std::string finish_detect_time;                                 /**< 完成检测时间*/
        std::string fov_start_time;                                   /**< fov拍照开始时间*/
        std::string fov_end_time;                                     /**< fov拍照结束时间*/
        std::string one_fov_take_time;                                  /**< 单个fov 耗费时间*/
        std::string link_project_barcode;                               /**< 关联工程的某一个条码，可能是子板，可能是整板 by zhangyuyu 2025.6.8*/
        std::unordered_map<int, std::string> subboard_id_and_barcode;   /**< 子板barcode*/
        std::unordered_map<int, cv::Mat> entirety_board_imags;          /**< 检测到的整板大图*/
        std::unordered_map<int/*子板id*/, bool/*坏板状态 true/false*/> subboard_bad_info;         /*< 坏子板信息*/
        std::vector<DeviceResult> component_result_vector;              /**< 整板所有元件检测详细结果，包括各类算法检测结果，检测时间等等....*/
        std::vector<jrsdata::ComponentDetectResult> component_status_result; /**< 元件检测结果,只要状态结果：OK/NG 主要用于更新主界面上render界面*/
        std::vector<ComponentSaveInfoPtr> workflow_component_algo_info_vector; /**< 自动流程中，算法执行参数信息*/
        OnlineDebugParmPtr online_debug_param_ptr; /**< 在线调试参数*/
        DetectResultParam()
            : board_detect_status(BoardDetectStatus::NORMAL)
            , detect_result(true)
            , track_id(1)
            , board_code{}
            , start_detect_time{}
            , finish_detect_time{}
            , fov_start_time{}
            , fov_end_time{}
            , one_fov_take_time{}
            , subboard_id_and_barcode{}
            , entirety_board_imags{}
            , subboard_bad_info{}
            , component_result_vector{}
            , component_status_result{}
            , workflow_component_algo_info_vector{}
            , online_debug_param_ptr(std::make_shared<OnlineDebugParm>())
        {
        }
    };
    using DetectResultParamPtr = std::shared_ptr<DetectResultParam>;

    /** 供界面使用， */
    struct NgStatistics
    {
        std::unordered_map<std::string/**< 名称*/, int/**<数量*/ > component_ng_statistics;/**<进行递减排序*/
        std::unordered_map<std::string/**< 错误原因*/, int/**<数量*/ > flaw_type_statistics;/**<进行递减排序*/
        long ng_compoent_number;    /**< ng 元件的总数*/
    };
    JRSREFLECTION(NgStatistics, component_ng_statistics, flaw_type_statistics, ng_compoent_number);
}
#endif // !__JRSDATAPARAM_H__
