#include "onlinedebugcontroller.h"
#include "onlinedebugview.h"
#include "onlinedebugmodel.h"
namespace jrsaoi
{
    OnLineDebugController::OnLineDebugController(const std::string& name)
        :ControllerBase(name)
        , is_debugging(false)
    {
    }
    OnLineDebugController::~OnLineDebugController()
    {
    }
    int OnLineDebugController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            is_debugging.store(false);
            online_debug_view->UpdateView(param_);

        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            is_debugging.store(true);
        }
        if (!is_debugging.load())
        {
            return jrscore::AOI_OK;
        }
        online_debug_model->Update(param_);

        QMetaObject::invokeMethod(online_debug_view,
            "UpdateView",
            Qt::QueuedConnection,
            Q_ARG(const jrsdata::ViewParamBasePtr&, param_)
        );
        return jrscore::AOI_OK;
    }
    int OnLineDebugController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    void OnLineDebugController::SetView(ViewBase* view_param)
    {
        online_debug_view = static_cast<OnLineDebugView*>(view_param);
        connect(online_debug_view,&OnLineDebugView::SigUpdateOnline,this,&OnLineDebugController::SlotUpdateOnline);
    }
    void OnLineDebugController::SetModel(ModelBasePtr model_param)
    {
        online_debug_model = std::dynamic_pointer_cast<OnLineDebugModel>(model_param);
    } 
    void OnLineDebugController::SlotUpdateOnline(const jrsdata::ViewParamBasePtr& param_)
    {
        online_debug_model->Update(param_);
        if (param_->topic_name.empty())
        {
            return;
        }
        emit SigUpdateOnline(param_);
    }
}