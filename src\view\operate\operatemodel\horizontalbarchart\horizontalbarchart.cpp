// CUSTOM
#include "horizontalbarchart.h"
// QT
// STL

HorizontalBarChart::HorizontalBarChart(QWidget* parent) : QWidget(parent)
{
    Init();
    SetPercent(true);
    SetShowCount(true);
}

HorizontalBarChart::~HorizontalBarChart()
{
}

void HorizontalBarChart::Init()
{
    m_colors =
    {
        QColor(0, 255, 0),
        QColor(255, 0, 0),
        QColor(255, 113, 247),
        QColor(164, 145, 224),
        QColor(255, 134, 116),
        QColor(254, 136, 0),
        QColor(194, 102, 87),
        QColor(186, 156, 148),
        QColor(55, 173, 107),
        QColor(0, 0, 0)
    };
    m_alignment =
    {
        Qt::AlignRight,
        Qt::AlignLeft,
        Qt::AlignLeft,
        Qt::AlignLeft,
        Qt::AlignLeft,
        Qt::AlignLeft
    };
}

void HorizontalBarChart::SetPercent(bool percent)
{
    m_show_percent = percent;
    update();
}

void HorizontalBarChart::SetShowCount(bool show_count)
{
    m_show_count = show_count;
}

void HorizontalBarChart::SetDataInfo(const std::vector<RingData>& data_)
{
    m_ring_data = data_;
    update();
}

void HorizontalBarChart::ClearHorizontalBarChart()
{
    m_ring_data.clear();
    repaint();
}
void HorizontalBarChart::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    QPainter painter(this);

    const int width = this->width();
    const int height = this->height();

    // 设置字体和字体度量
    QFont font;
    font.setFamily("宋体");
    font.setPointSize(9);
    painter.setFont(font);
    QFontMetrics fm(font);

    // 计算总值和最大标签宽度
    double sum_count = std::accumulate(m_ring_data.begin(), m_ring_data.end(), 0.0,
        [](double sum, const RingData& ring_data)
        { return sum + ring_data.m_value; });
    int max_label_width = CalculateMaxLabelWidth(fm);

    // 计算百分比和颜色索引
    int id = 0;
    for (auto& item : m_ring_data)
    {
        item.m_percent = item.m_value / sum_count * 100.0;
        item.m_color_idx = id++ % m_colors.size();
    }

    // 绘制横向轴和竖向轴
    int x_start = max_label_width + 10;
    int x_end = width - 15;
    int y_start = height - 20;
    int y_end = 0;
    DrawAxes(painter, x_start, x_end, y_start, y_end, fm);

    // 绘制百分比标尺
    DrawPercentageScale(painter, x_start, x_end, y_start, fm);

    DrawVerticalLabels(painter, x_start, y_start, y_end, fm);

    // 绘制条形图
    DrawBars(painter, x_start, x_end, y_start, y_end, fm, max_label_width);
}

int HorizontalBarChart::CalculateMaxLabelWidth(const QFontMetrics& fm)
{
    int max_label_width = 0;
    for (const auto& item : m_ring_data)
    {
        QString label = QString::fromStdString(item.m_label);
        max_label_width = qMax(max_label_width, fm.width(label));
    }
    return max_label_width;
}

void HorizontalBarChart::DrawAxes(QPainter& painter, int x_start, int x_end, int y_start, int y_end, const QFontMetrics& fm)
{
    Q_UNUSED(fm);
    // 绘制横向轴
    painter.drawLine(x_start, y_start, x_end, y_start);

    // 绘制竖向轴
    painter.drawLine(x_start, y_end, x_start, y_start);

    // 绘制竖向轴刻度
    for (int i = 0; i < int(m_ring_data.size()); ++i)
    {
        int y = y_end + i * (y_start - y_end) / int(m_ring_data.size());
        painter.drawLine(x_start, y, x_start - 5, y);
    }
}

void HorizontalBarChart::DrawBars(QPainter& painter, double x_start, double x_end, double y_start, double y_end, const QFontMetrics& fm, double max_label_width)
{
    Q_UNUSED(max_label_width);
    for (double i = 0; i < double(m_ring_data.size()); ++i)
    {
        const auto& item = m_ring_data[i];
        double bar_width = static_cast<double>(item.m_percent * (x_end - x_start) / 100);
        double bar_x = x_start + 1;
        double bar_y = i * (y_start - y_end) / double(m_ring_data.size());
        double bar_height = (y_start - y_end) / double(m_ring_data.size());

        // 获取对应的颜色
        QColor color = m_colors[item.m_color_idx];
        painter.fillRect(bar_x, bar_y + bar_height * 0.05, bar_width, bar_height * 0.9, color);

        // 在柱子右边绘制数字和百分比
        QString value_text = QString::number(item.m_value);
        QString percent_text = QString::number(item.m_percent, 'f', 1) + "%";
        QString combined_text = value_text + "(" + percent_text + ")";
        double text_x = bar_x + bar_width + 1;
        double text_y = bar_y + bar_height / 2 + fm.height() / 2;
        if ((text_x + this->fontMetrics().horizontalAdvance(combined_text)) > this->width())
        {
            text_x = bar_x + bar_width / 2 - this->fontMetrics().horizontalAdvance(combined_text) / 2;
        }
        painter.drawText(text_x, text_y, combined_text);
    }
}

void HorizontalBarChart::DrawPercentageScale(QPainter& painter, int x_start, int x_end, int y_start, const QFontMetrics& fm)
{
    for (int i = 0; i <= 5; ++i)
    {
        int x = x_start + i * (x_end - x_start) / 5;
        QString text = QString("%1%").arg(i * 20);
        painter.setPen(QPen(Qt::black, 1, Qt::SolidLine));
        painter.drawLine(x, y_start, x, y_start + 5);
        painter.drawText(x - fm.width(text) / 2, y_start + 20, text);
        if (i != 0)
        {
            painter.setPen(QPen(Qt::gray, 1, Qt::DashLine));
            painter.drawLine(x, y_start, x, 0); // 横轴上的竖向虚线
        }
    }
    painter.setPen(QPen(Qt::black, 1, Qt::SolidLine));
}

void HorizontalBarChart::DrawVerticalLabels(QPainter& painter, int x_start, int y_start, int y_end, const QFontMetrics& fm)
{
    for (int i = 0; i < int(m_ring_data.size()); ++i)
    {
        const auto& item = m_ring_data[i];
        int one_height = (y_start - y_end) / int(m_ring_data.size());
        int y = y_end + i * one_height;
        QString label = QString::fromStdString(item.m_label);
        int label_width = fm.width(label);
        painter.drawText(x_start - label_width - 10, y + fm.height() / 2 + one_height / 2.0, label); // 标签向右对齐
    }
}