﻿/*********************************************************************
 * @brief  除标准图形以外的其他图形.
 *
 * @file   customgraphicsobject.h
 *
 * @date   2024.10.22
 * <AUTHOR>
**********************************************************************/
#ifndef CUSTOMGRAPHICSOBJECT_H
#define CUSTOMGRAPHICSOBJECT_H

#include "graphicsobject.h"

/**
 * @brief 金线
 */
class GRAPHICS_API SGGraphics : public GraphicsAbstract, public TransientObjectControl<SGGraphics>
{
public:
    SGGraphics() : GraphicsAbstract() {}
    SGGraphics(const std::string& id_) : GraphicsAbstract(id_) {}

    SGGraphics(float size_, int type_, Vec2 start_, Vec2 end_, const std::vector<Vec2>& connect_)
        : GraphicsAbstract(), terminal_size(size_), terminal_type(type_)
        , start_terminal(start_), end_terminal(end_), connect_nodes(connect_)
    {
        ComfirmPoint(); ///< 将输入的绝对坐标转成相对坐标
    }

    SGGraphics& operator=(const SGGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        iscomfirmend = other.iscomfirmend;
        connect_nodes = other.connect_nodes;
        start_terminal = other.start_terminal;
        end_terminal = other.end_terminal;
        terminal_size = other.terminal_size;
        terminal_type = other.terminal_type;
        // TODO
        return *this;
    }

    GraphicsFlag GetFlag() const override { return GraphicsFlag::SG; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    void Update() override;
    void UpdateDrawBuffer() override;
    void UpdateControlPoint()  override;
    void DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam&) override;
    int ResponseControlPoint(const ResponseEventParam&) override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;

    std::shared_ptr<GraphicsAbstract> Clone() const override;
    std::string GetSerializedData() override;
    /**
     * @brief 获取起点(相对坐标)
     */
    Vec2 GetStart() const { return start_terminal; }
    /**
     * @brief 获取终点(相对坐标)
     */
    Vec2 GetEnd() const { return end_terminal; }
    /**
     * @brief 获取中间点(相对坐标)
     */
    std::vector<Vec2> GetPoint() const { return connect_nodes; }

    /*设置起点*/
    void SetStart(float x, float y, bool isrelative = false);
    /*设置终点*/
    void SetEnd(float x, float y, bool isrelative = false);
    void EndComfirm();
    /*设置中间点*/
    void SetPoint(const std::vector<Vec2>&, bool isrelative = false);

    /**
     * @brief 在末尾添加一个连接点
     * @param isrelative 是否为相对坐标
     */
    void AddConnect(float x, float y, bool isrelative = false);
    /**
     * @brief 删除末尾连接点
     */
    void DeleteConnect();
    size_t ConnectNum() const { return connect_nodes.size(); }

    /**
     * @brief 确认点-生成外接矩形
     */
    void ComfirmPoint();

    float terminal_size = 15; ///< 端点大小
    int terminal_type = 0;    ///< 端点类型 0:矩形 1:圆

private:
    /*生成外接矩形*/
    void CreateBoundingBox(SGGraphics* obj);
    void DrawInfo(SGGraphics* obj, Renderer* r, Painter* p, const LayerConfig* config);

    bool iscomfirmend = false;  ///< 是否已经确认
    Vec2 start_terminal;  ///< 起点
    Vec2 end_terminal;    ///< 终点

    std::vector<Vec2> connect_nodes; ///< 中间节点

private:
    std::vector<Vec2> paths;  ///< 顶点缓存
    std::vector<Vec2> paths_boundingbox;  ///< 顶点缓存(外接矩形)
};

/**
 * @brief 多内框
 */
class GRAPHICS_API MultiRegionGraphics : public GraphicsAbstract, public TransientObjectControl<MultiRegionGraphics>
{
public:
    MultiRegionGraphics() : GraphicsAbstract() {}
    MultiRegionGraphics(const std::string& id_) : GraphicsAbstract(id_) {}
    MultiRegionGraphics(float x, float y, float width, float height, float angle)
        : GraphicsAbstract(x, y, width, height, angle)
        , region_cx(-width * 0.25f), region_cy(0), region_w(width * 0.25f), region_h(height * 0.5f)
        , region_angle(angle), region_num(2), region_type(1)
    {
    }
    MultiRegionGraphics& operator=(const MultiRegionGraphics& other)
    {
        if (this != &other)
        {
            GraphicsAbstract::operator=(other);
            this->region_cx = other.region_cx;
            this->region_cy = other.region_cy;
            this->region_w = other.region_w;
            this->region_h = other.region_h;
            this->region_angle = other.region_angle;
            this->region_num = other.region_num;
            this->region_type = other.region_type;
            this->controls = other.controls;
        }
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<MultiRegionGraphics>(*this);
    }

    GraphicsFlag GetFlag() const override { return GraphicsFlag::multiregion; }
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;

    float region_cx = 0; ///< 子区域相对中心点x
    float region_cy = 0; ///< 子区域相对中心点y
    float region_w = 0;  ///< 子区域宽度
    float region_h = 0;  ///< 子区域高度
    float region_angle = 0; ///< 子区域旋转角度
    int region_num = 0; ///< 子区域数量
    int region_type = 0; ///< 子区域类型

private:
    /*更新绘制路径*/
    void UpdateDrawPath(MultiRegionGraphics* obj);

private:
    std::vector<Vec2> controls;
    std::vector<Vec2> paths;
};
#endif //! CUSTOMGRAPHICSOBJECT_H