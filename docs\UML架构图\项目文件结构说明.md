# JRSAOI 2.0 项目文件结构说明

## 项目概述
JRSAOI 2.0 是一个基于Qt和OpenCV的AOI（自动光学检测）系统，主要用于PCB板的自动检测。项目采用模块化架构设计，使用C++17开发，支持Visual Studio 2022编译。

## 项目架构
项目采用分层架构设计，主要分为以下几层：
- **视图层（View）**: 用户界面和交互
- **逻辑层（Logic）**: 业务逻辑处理
- **插件层（Plugin）**: 算法引擎和设备管理
- **核心层（Core）**: 基础功能和工具
- **数据层（Data）**: 数据管理和存储

## 主要目录结构

### 根目录文件
- `CMakeLists.txt` - 主CMake配置文件，定义整个项目的构建规则
- `更新目录.txt` - 项目更新记录

### 源代码目录 (`src/`)

#### 视图层 (View Layer)
- **`src/view/`** - 主界面模块
  - `mainwindow/` - 主窗口实现，基于SARibbonMainWindow
  - `controlpanel/` - 控制面板界面
  - `customwidget/` - 自定义控件
  - `logshow/` - 日志显示界面
  - `operate/` - 操作界面，包含检测、项目管理等功能
  - `render2d/` - 2D渲染显示界面
  - `settingview/` - 设置界面
  - `toolbar/` - 工具栏界面

- **`src/render/`** - 渲染引擎模块
  - `core/` - 渲染核心功能
  - `engine/` - 渲染引擎实现
  - `graphics/` - 图形绘制功能
  - `control/` - 渲染控制逻辑

#### 逻辑层 (Logic Layer)
- **`src/logicmanager/`** - 逻辑管理器
  - 负责协调各个模块间的业务逻辑
  - 处理检测流程控制
  - 管理事件分发和处理

- **`src/project/`** - 项目管理模块
  - 工程文件的创建、保存、加载
  - 项目参数管理

- **`src/workflow/`** - 工作流管理模块
  - 检测流程控制
  - 自动化工作流执行

#### 插件层 (Plugin Layer)
- **`src/algorithmengine/`** - 算法引擎模块
  - 算法加载和管理
  - 各种检测算法的执行
  - 支持的算法类型：
    - 位置检测 (Position)
    - OCR/OCV识别
    - Mark点检测
    - 高度测量
    - 桥接检测
    - 焊点检测等

- **`src/devicemanager/`** - 设备管理模块
  - `motion/` - 运动控制设备
  - `structlight/` - 结构光相机设备
  - `barcodescanner/` - 条码扫描设备

#### 核心层 (Core Layer)
- **`src/core/`** - 核心功能模块
  - `common/` - 通用工具和基础类
  - `coordinatecenter/` - 坐标转换中心
  - `database/` - 数据库操作
  - `logexport/` - 日志导出功能
  - `pubsub/` - 发布订阅模式实现

- **`src/parametermanager/`** - 参数管理模块
  - 系统参数管理
  - 工程参数管理
  - 设备参数管理

#### 数据层 (Data Layer)
- **`src/datamanager/`** - 数据管理模块
  - `database/` - 数据库操作
  - `filesystem/` - 文件系统操作
  - `exportfile/` - 文件导出功能

### 第三方库目录 (`thirdparty/`)
- `Eigen3/` - 线性代数库
- `opencv/` - 图像处理库（通过cmake查找）
- `Qt/` - GUI框架（通过cmake查找）
- `mysql/` - 数据库连接
- `json/` - JSON处理
- `spdlog/` - 日志库
- `googletest/` - 单元测试框架
- `cereal/` - 序列化库
- `ormpp/` - ORM框架
- `ylt/` - 网络库

### 配置目录 (`config/`)
- `structlightconfig/` - 结构光相机配置

### 文档目录 (`docs/`)
- `readme/` - 项目说明文档
- `框架设计文档/` - 架构设计文档
- `数据库/` - 数据库设计文档
- `运控/` - 运动控制相关文档

### 构建目录
- `build/` - CMake构建输出目录
- `bin/` - 可执行文件输出目录
- `out/` - 其他构建输出

### 资源目录 (`jrsresource/`)
- `icon/` - 应用程序图标和界面资源
  - `MainWindowResource.qrc` - Qt资源文件
  - `image/` - 界面图片资源
  - `renderimage/` - 渲染相关图片资源

### 工具目录 (`tools/`)
- 开发和部署相关工具（当前为空）

### 单元测试 (`unittest/`)
- `coretest/` - 核心模块测试
- `datatest/` - 数据模块测试

## 主要功能模块说明

### 1. 检测流程
系统支持完整的PCB检测流程：
1. 工程加载和参数配置
2. Mark点定位和坐标校正
3. FOV（视野）规划和图像采集
4. 算法检测和结果分析
5. 检测报告生成

### 2. 用户界面
- 基于Qt的现代化Ribbon界面
- 支持2D图形渲染和交互
- 实时日志显示
- 参数设置和调试界面

### 3. 设备集成
- 支持多种相机设备
- 运动控制系统集成
- 条码扫描功能
- 结构光3D检测

### 4. 数据管理
- MySQL数据库存储
- 文件系统管理
- 检测结果导出
- 工程参数序列化

## 编译和运行要求
- Visual Studio 2022
- Qt 5.15.2+
- OpenCV 4.8.1
- CMake 3.12+
- C++17标准

## 关键文件详细说明

### 主要可执行文件
- `src/view/mainwindow/src/main.cpp` - 程序入口点，初始化Qt应用和主窗口
- `src/view/mainwindow/src/mainwindow.cpp` - 主窗口实现，集成所有功能模块

### 核心管理器类
- `src/logicmanager/logicmanager/src/logicmanager.cpp` - 核心业务逻辑管理器
- `src/algorithmengine/src/algorithmenginemanager.cpp` - 算法引擎管理器
- `src/devicemanager/devicemanager/src/devicemanager.cpp` - 设备管理器
- `src/datamanager/src/datamanager.cpp` - 数据管理器

### 重要配置文件
- `cmake/` 目录下的各种cmake配置文件
  - `findopencv.cmake` - OpenCV库查找配置
  - `findqt.cmake` - Qt库查找配置
  - `versions.cmake` - 版本信息配置

### 参数定义文件
- `src/parametermanager/define/projectparam/projectparam.hpp` - 工程参数定义
- `src/parametermanager/define/dataparam/settingparam.h` - 设置参数定义

## 模块间通信机制
项目采用事件驱动架构：
1. **事件参数系统** - 通过ViewParamBase基类实现统一的事件参数传递
2. **发布订阅模式** - 模块间通过事件中心进行松耦合通信
3. **回调机制** - 设备状态和检测结果通过回调函数传递

## 检测算法支持
系统支持多种检测算法：
- **位置检测** - 元件位置和角度检测
- **OCR/OCV** - 字符识别和验证
- **Mark检测** - 基准点定位
- **高度测量** - 3D高度检测
- **桥接检测** - 焊点桥接检测
- **极性检测** - 元件极性检测
- **边缘检测** - 边缘轮廓检测

## 数据流向
1. **图像采集** - 相机设备 → 图像缓冲区
2. **坐标转换** - 像素坐标 ↔ 物理坐标
3. **算法处理** - 图像数据 → 算法引擎 → 检测结果
4. **结果存储** - 检测结果 → 数据库/文件系统
5. **界面显示** - 结果数据 → 渲染引擎 → 用户界面

## 注意事项
1. 项目使用UTF-8编码
2. 支持多处理器编译
3. 包含完整的错误处理和日志系统
4. 采用插件化架构，便于功能扩展
5. 支持离线调试模式
6. 集成了崩溃转储功能用于调试
