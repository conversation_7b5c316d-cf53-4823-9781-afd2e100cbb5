/*****************************************************************//**
 * @file   operatorviewbase.h
 * @brief  算子界面基类
 * @details
 * <AUTHOR>
 * @date 2024.2.26
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.26         <td>V1.0              <td>YYZhang      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSOPERATORVIEWBASE_H__
#define __JRSOPERATORVIEWBASE_H__

 //STD
#include <iostream>
#include <functional>

//QT
#include <QWidget>
//Custom
#include "operatorparambase.h"
#include "pluginexport.hpp" 
namespace jrsoperator
{
    class   JRS_AOI_PLUGIN_API OperatorViewBase :public QWidget
    {
        Q_OBJECT
        public:
        explicit OperatorViewBase(const std::string& operator_name_, QWidget* parent = nullptr);
        virtual ~OperatorViewBase() = default;
        using ExecuteCallBack = std::function<bool(OperatorParamBasePtr execute_param_)>;
        using SaveCallBack = std::function<bool(OperatorParamBasePtr execute_param_)>;

        /**
         * @fun GetOperatorName
         * @brief 返回算子名称
         * @return  算子名称字符串
         * @date 2024.2.28
         * <AUTHOR>
         */
        const std::string& GetOperatorName()const;
        /**
         * @fun SetOperatorName
         * @brief 设置算子名称
         * @param operatpr_name_ 算子名称
         * @date 2024.2.28
         * <AUTHOR>
         */
         //void SetOperatorName(const std::string& operatpr_name_);
         /**
          * @fun SetExecuteCallBack
          * @brief 设置执行回调
          * @param cb_ 执行回调函数
          * @date 2024.2.28
          * <AUTHOR>
          */
        void SetExecuteCallBack(ExecuteCallBack cb_);

        /**
         * @fun SetSaveCallBack
         * @brief 设置保存参数回调
         * @param cb_
         * @date 2024.2.28
         * <AUTHOR>
         */
        void SetSaveCallBack(SaveCallBack cb_);

        /**
         * @fun Clone
         * @brief 返回对象的拷贝构造
         * @return 返回对象指针
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual OperatorViewBase* Clone() = 0;

        /**
         * @fun GetView
         * @brief 获取算子界面指针
         * @return 返回算子界面指针
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual QWidget* GetView() = 0;
        /**
         * @fun UpdateViewParam
         * @brief 更新界面参数
         * @param param_ 界面参数
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual void UpdateViewParam(OperatorParamBasePtr param_) = 0;

        /**
         * @fun GetViewParam
         * @brief 获取界面参数
         * @return  返回界面参数GetViewParam
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual OperatorParamBasePtr GetViewParam() = 0;

        /**
         * @brief 切换规格界面显示的结果信息id
         * @param id 子检测框id
         */
        virtual bool ChangeSpecShowID(int id) {
            (void)id; return true;
        }

        /**
         * @fun OnExecute
         * @brief 执行算子
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual void OnExecute() = 0;

        /**
         * @fun OnConfirmSave
         * @brief 确认参数保存
         * @date 2024.2.28
         * <AUTHOR>
         */
        virtual void OnConfirmSave() = 0;

        /**
        * @fun FastUpdateSpecParam
        * @brief 快速更新规格值
        * @date 2025.5.13
        * <AUTHOR>
        */
        virtual void FastUpdateSpecParam(const std::map<std::string, FastSetSpecParamInfo>& spec_param_map) = 0;

        protected:
        ExecuteCallBack execute_cb; /**< 执行回调 */
        SaveCallBack save_cb; /**< 保存回调 */

        private:

        std::string operator_name; /**< 算子名称 */



    };

}

#endif // !__JRSOPERATORVIEWBASE_H__
