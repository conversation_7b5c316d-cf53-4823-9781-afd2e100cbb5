﻿#include "coreapplication.h"
namespace jrscore
{
    struct CoreAppImplData
    {
        ErrorHandlerPtr p_handler;
        LogManagerPtr p_log_manager;
        MessageManagerPtr p_message_ptr;
    };
    CoreApplication::CoreApplication() :p_data(new CoreAppImplData)
    {
        p_data->p_handler = std::make_shared<ErrorHandler>();
        p_data->p_log_manager = std::make_shared<LogManager>();
        p_data->p_message_ptr = std::make_shared<jrscore::MessageManager>();
    }
    CoreApplication::~CoreApplication()
    {
    }
    CoreApplication* CoreApplication::GetInstance()
    {
        static std::shared_ptr<CoreApplication> core_app = std::make_shared<CoreApplication>();

        return core_app.get();
    }
    ErrorHandlerPtr CoreApplication::GetErrorHandler()
    {

        return p_data->p_handler;
    }
    LogManagerPtr CoreApplication::GetLogManager()
    {

        return p_data->p_log_manager;
    }

    jrscore::MessageManagerPtr CoreApplication::GetMessageManager()
    {
        return p_data->p_message_ptr;
    }

}