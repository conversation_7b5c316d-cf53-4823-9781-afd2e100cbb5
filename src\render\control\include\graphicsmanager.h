﻿/**********************************************************************
 * @brief  图形管理类.
 *
 * @file   graphicsmanager.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef GRAPHICSMANAGER_H
#define GRAPHICSMANAGER_H

#include "graphicsid.h"              // GraphicsID
#include "renderabstract.hpp"        // RenderAbstract
#include "graphicsabstract.hpp"      // GraphicsPtr LayerConfigPtr
#include "controlpointconstants.hpp" // ControlAttributes ControlPoint
 //#include "resourceguard.hpp"         // ResourceVector
#include "delegate.hpp"              // Event

#include <memory>        // std::shared_ptr
#include <shared_mutex>  // shared_mutex shared_lock
#include <unordered_map> // std::unordered_map
 //#include <optional>      // std::optional

struct MouseEventValue;
class PadManager;
class GraphicsManager : public RenderAbstract
{
public:
    enum ErrorCode
    {
        SUCCESS = 0,
        INVALID_PARAM,
        INVALID_GRAPHICS,
        INVALID_GRAPHICS_ID,
        INVALID_LAYER,
        NOT_FOUND,
        CONTAINER_IS_NULL,
        NOT_SUPPORT,
        NOT_EXIST,
        NOT_INIT,
        NOT_FOUND_GRAPHICS,
        NOT_FOUND_CONTROL_POINT,
        NOT_FOUND_CONTROL_ATTRIBUTE,
        NOT_FOUND_CONTROL_POINT_ATTRIBUTE,
    };
    explicit GraphicsManager();
    ~GraphicsManager() {}
    /**
     * @brief 渲染
     */
    void Render() override;
    /**
     * @brief 资源销毁
     */
    void Destroy() override {}

    // 清除操作
    void Clear();

    /**
     * @brief 键盘控制图形移动方向
     */
    enum MoveDirection
    {
        LEFT = 1 << 0,
        RIGHT = 1 << 1,
        UP = 1 << 2,
        DOWN = 1 << 3,
    };

    /**
     * @brief 创建图形
     * @param layer 非空时采用指定图层，否则采用默认图层
     */
    GraphicsPtr CreateGraphics(const GraphicsFlag& flag = GraphicsFlag::rect,
        const std::string& layer = "", const std::string& group_name = "", const GraphicsPtr& father_graphics_ = nullptr,
        bool invoke_callback = true);

    /**
     * @brief 创建图形
     * @param layer 非空时采用指定图层，否则采用默认图层
     */
    GraphicsPtr CreateGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const float& angle,
        const GraphicsFlag& flag = GraphicsFlag::rect, const std::string& layer = "");
    /**
     * @brief 通过json配置文件生成图形
     */
    std::vector<GraphicsPtr> CreateGraphics(const std::string& json, bool invoke_callback = true);

    /**
     * @brief 更新图形
     */
    int UpdateGraphics(const GraphicsPtr& gh, bool invoke_callback = true);
    /**
     * @fun UpdateGraphics
     * @brief  更新图形到工程
     * @param ghs
     * @param invoke_callback
     * @return
     * <AUTHOR>
     * @date 2025.3.13
     */
    int UpdateGraphics(const GraphicsPtrVec& ghs, bool invoke_callback);
    /**
     * @brief 更新图形
     */
    int UpdateGraphics(const std::vector<GraphicsPtr>& ghs, const std::string& layer = "", bool invoke_callback = true);
    /**
     * @brief 根据id查找并更新图形
     */
    int UpdateGraphics(const GraphicsID& id,
        const float& xstart, const float& ystart, const float& xend, const float& yend, bool invoke_callback = true);
    /**
     * @brief 图形向指定方向移动一个单位
     */
    int UpdateGraphics(const std::vector<GraphicsPtr>& ghs, const MoveDirection& direction, bool invoke_callback = true);
    /**
     * @brief 为图形设置父图形
     * @param ghs 图形组
     * @param parent 父图形
     */
    int UpdateGraphics(const std::vector<GraphicsPtr>& ghs, const GraphicsPtr& parent, bool invoke_callback = true);
    /**
     * @brief  移动全部图形
     */
    int UpdateGraphicsMoveAll(const float& xoffset, const float& yOffset, bool invoke_callback = true);

    /**
     * @brief 更新图形的值
     * @note  仅修改输入的图形,不对管理器造成影响
     *        如果需要更新管理器中的图形,请在之后使用UpdateGraphics
     */
    static void UpdateGraphicsValue(const GraphicsPtr& gh, const float& xstart, const float& ystart, const float& xend, const float& yend, const float& angle);

    /**
     * @brief 更新图形所在层
     * @param ghs 图形组
     * @param layer 图层,如果为空使用当前图层
     * @note  仅修改输入的图形,不对管理器造成影响
     *        如果需要更新管理器中的图形,请在之后使用UpdateGraphics
     */
    int UpdateGraphicsValue(const std::vector<GraphicsPtr>& ghs, const std::string& layer = "");


    /**
     * @brief 获取所有图形
     */
    int ReadGraphics(std::vector<GraphicsPtr>& ghs) const;
    int ReadGraphics(std::vector<GraphicsPtr>& ghs, const std::string& layer) const;
    int ReadGraphics(std::vector<GraphicsPtr>& ghs, const std::vector<GraphicsID>& ids) const;
    int ReadGraphics(GraphicsPtr& gh, const GraphicsID& id) const;
    /**
     * @brief 获取图形序列化字符串
     */
    int ReadGraphics(std::string& json) const;
    /**
     * @brief 获取所有选中图形
     */
    int ReadGraphicsSelected(std::vector<GraphicsPtr>& ghs) const;
    /**
     * @brief 获取选中的单个图形
     * @note  如果选中的图形数量大于1,也返回空
     */
    int ReadSelectedSingleGraphics(GraphicsPtr& gh) const;
    /**
     * @brief 删除所有图形
     */
    int DeleteGraphics(bool invoke_callback = true);

    int DeleteGraphicsExceptLayer(const std::string& except_layer_, bool invoke_callback_ = true);
    int DeleteGraphics(const GraphicsID& id, bool invoke_callback = true);
    int DeleteGraphics(const std::vector<GraphicsID>& ids, bool invoke_callback = true);
    int DeleteGraphics(const std::string& layer, bool invoke_callback = true);
    int DeletePadGroups();
    /**
     * @brief  删除选中图形
     */
    int DeleteSelectedGraphics();

    /**
     * @brief 选择图形
     * @param ghs 图形组
     * @param selected 是否选中 true 选中 false 取消选中
     * @return
     */
    int SelectGraphics(const std::vector<GraphicsPtr>& ghs, const bool& selected, bool invoke_callback = true);
    int SelectGraphics(const std::vector<GraphicsID>& ids, const bool& selected, bool invoke_callback = true);
    int SelectGraphics(const std::string& layer, const bool& selected, bool invoke_callback = true);
    int SelectGraphics(const GraphicsPtr& gh, const bool& selected, bool invoke_callback = true);
    int SelectGraphics(const GraphicsID& id, const bool& selected, bool invoke_callback = true);
    /**
     * @brief   选择图形
     * @param   选择区域
     * @param   temp 是否临时选中 true 临时选中 false 永久选中
     * @return  是否选中 true 选中 false 未选中
     */
    bool TrySelectGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp);
    /**
     * @brief   选择图形
     * @param   x y 选择点
     * @param   temp 是否临时选中 true 临时选中 false 永久选中
     * @return  是否选中 true 选中 false 未选中
     */
    bool TrySelectGraphics(const float& x, const float& y, const bool& temp, const bool& is_invoke = true);
    /**
     * @brief   选择图形
     * @param   paths 多边形路径点
     * @param   temp 是否临时选中 true 临时选中 false 永久选中
     * @return  是否选中 true 选中 false 未选中
     */
    bool TrySelectGraphics(const std::vector<Vec2>& paths, const bool& temp);
    /**
     * @brief  判断图形是否能够被一个矩形区域选中
     */
    bool IsSelectGraphics(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const;
    /**
     * @brief  清除所有图形的选中状态
     * @note   不是删除选中图形
     */
    void ClearSelected();
    /**
     * @brief  是否存在选中图形
     * @note   目前未储存选中图形,所以需要遍历所有图形,应减少调用次数
     * @return 是否存在选中 true 存在选中 false 不存在选中
     */
    bool IsHaveSelected();

    /**
     * @brief  响应事件
     */
    void ResponseEvent(const MouseEventValue& value);
    /**
     * @brief  尝试响应图形
     * @return 返回0 表示响应,否则未响应
     */
    int TryResponseGraphics(const float& x, const float& y, const bool& temp);
    int ResponseGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp);
    int ResponseParentGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp);



    /**
     * @brief  获取响应属性
     */
    const ControlAttributes& GetResponseAttributes() const;
    void SetResponseAttributes(const ControlAttributes&);
    /**
     * @brief  记录复制图形
     */
    int CopyGraphics(const std::vector<GraphicsPtr>& ghs);
    /**
     * @brief  将复制的图形粘贴到指定位置
     */
    int PasteGraphics(const float& x, const float& y);
    /**
     * @brief  将图形粘贴到指定位置
     * @note   TODO 这个函数未实现
     *         存在问题如果存在父子关系会进行多个复制,无法区分哪个才是与输入同级的图形
     */
    int CopyGraphicsTo(std::shared_ptr<GraphicsAbstract>& new_gh, const std::shared_ptr<GraphicsAbstract>& gh, float x, float y);

    int BeginCreate(GraphicsPtr gh);
    GraphicsPtr GetCreate();
    /**
     * @brief  确认创建,将临时图形添加到图形容器中
     */
    int CommitCreate();
    int CancelCreate();

    /**
     * @brief  更新临时图形
     */
    int UpdateTemporary(const float& xstart, const float& ystart, const float& xend, const float& yend, bool not_rotate);
    /**
     * @brief  清除临时图形
     */
    int ClearTemporary();

    /**
     * @brief  记录父级图形
     */
    int RecordParentGraphics(const GraphicsPtr& gh);
    /**
     * @brief  获取父级图形
     */
    int GetParentGraphics(GraphicsPtr& gh);

    /**
     * @brief 生成图形配置
     */
    std::shared_ptr<LayerConfig> CreateLayerConfig(const std::string& layer, RenderConfig display_style, RenderConfig focus_style);
    void UpdateLayerConfig(std::shared_ptr<LayerConfig> config, const std::string& layer);
    std::weak_ptr<LayerConfig> ReadLayerConfig(const std::string& layer);
    const LayerConfigMap& ReadLayerConfig();
    void DeleteLayerConfig(const std::string& layer);
    void DeleteLayerConfig();

    /**
     * @brief 设置当前操作图层
     */
    int SetCurrentLayer(const std::string& layer);
    inline const std::string& GetCurrentLayer() const { return m_current_layer; }
    /**
     * @brief 判断图层是否存在
     */
    bool IsLayerExist(const std::string& layer) const;

    /**
     * @brief 图形选择模式
     * @note  仅在框选时生效
     */
    enum SelectGraphicsMode
    {
        FullContainment,    ///< 完全框选住图形才算选中
        IntersectingArea,   ///< 只要框选区域与图形相交就算选中
        CenterPoint,        ///< 框选区域包含图形中心点即选中
    };

    /**
     * @brief 设置图形选择模式
     * @param mode 参考SelectGraphicsMode
     */
    void SetSelectGraphicsMode(const int& mode) { m_selection_mode = static_cast<SelectGraphicsMode>(mode); }
    void SetSelectGraphicsMode(const SelectGraphicsMode& mode) { m_selection_mode = mode; }
    inline int GetSelectGraphicsMode() const { return m_selection_mode; }

    // 图形绘制角度
    inline float GetDrawAngle() const { return m_angle; }
    void SetDrawAngle(float val);

    // 回调函数
    void SetCallbackGraphicsdraw(std::function<void()> callback);
    void SetCallbackGraphicsupdate(std::function<void(const std::vector<GraphicsPtr>&, bool)> callback);
    void SetCallbackGraphicschange(std::function<void(const std::vector<GraphicsPtr>&)> callback);
    void SetCallbackGraphicsadd(std::function<void(const std::vector<GraphicsPtr>&)> callback);
    void SetCallbackGraphicsdelete(std::function<void(const std::vector<GraphicsPtr>&)> callback);
    void SetCallbackGraphicsselected(std::function<void(const std::vector<GraphicsPtr>&)> callback);
    void SetCallbackCursorchange(std::function<void(int)> callback);

protected:
    /*触发回调*/
    void TriggerGraphicsdraw();
    void TriggerGraphicsupdate(const std::vector<GraphicsPtr>&, bool is_update_graphics_ = true);
    void TriggerGraphicsupdate(const GraphicsPtr&);
    void TriggerGraphicschange(const std::vector<GraphicsPtr>&);
    void TriggerGraphicsadd(const std::vector<GraphicsPtr>&);
    void TriggerGraphicsadd(const GraphicsPtr&);
    void TriggerGraphicsdelete(const std::vector<GraphicsPtr>&);
    void TriggerGraphicsselected(const std::vector<GraphicsPtr>&);
    void TriggerCursorchange(int cursor_type);

private:
    /**
    < 自动生成graphics_id  TODO:这个需要放到外面做
    */
    void SetAutoGenerateIDs(const GraphicsPtrVec& gh_ptrs_);

    /**
     * @brief 生成哈希表,用于提升批量操作效率
     * @note  经过测试,需要批量操作的次数大于100次时才会显著提升效率,目前暂不使用
     */
    std::unordered_map<GraphicsID, GraphicsPtr, GraphicsIDHash, GraphicsIDEqual>
        CreateGraphicsTempHash(const std::vector<GraphicsPtr>& vg) const;

    void SortGraphics();
    /**
     * @brief  移除图形
     * @note   这里将图形的父子关系解绑,然后重置当前图形指针
     *         需要先将图形从容器中移除,否则该函数无效
     */
    void RemoveGraphics(GraphicsPtr& gh);
    /**
     * @brief  图形克隆
     * @param  other 被克隆的图形
     * @note   进行克隆时会递归克隆子图形
     * @return 克隆后的图形
     */
    std::vector<GraphicsPtr> GraphicsClone(const GraphicsAbstract* other);
    int KeyMoveGraphics(const GraphicsPtr& gh, const MoveDirection& direction);
    bool IsSelectedByFullContainment(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const;
    bool IsSelectedByIntersectingArea(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const;
    bool IsSelectedByCenterPoint(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const;

    int SelectGraphicsByBoundaryAndContainment(GraphicsPtr& gh, const std::vector<GraphicsPtr>& ghs, float x, float y) const;
    /**
     * @brief 生成默认图层
     */
    void AddDefaultLayerConfig();

    /**
     * @fun AddGroupPadsIfNeeded
     * @brief Pad 同组内的元件添加
     * @param ghs
     * <AUTHOR>
     * @date 2025.2.27
     */
    int AddGroupPadsIfNeeded(std::vector<GraphicsPtr>& ghs, const std::string& command_ = "update");

    /**
     * @fun TestPrintGraphicsInfo
     * @brief  测试使用
     * <AUTHOR>
     * @date 2025.3.20
     */
    void TestPrintGraphicsInfo();

private:
    SelectGraphicsMode m_selection_mode;          ///< 图形选择模式
    float m_angle;                                ///< 图形绘制角度
    mutable std::shared_mutex mutex;              ///< 保护图形容器的互斥锁
    std::string m_current_layer;                  ///< 当前图层
    ControlAttributes m_attr_controlpoint;        ///< 当前触发控制点的属性
    GraphicsID m_parent_graphics_id;              ///< 待添加子图形的父图形id
    GraphicsPtr m_add_graphics;                   ///< 待添加图形
    LayerConfigMap m_layer_configs;               ///< 图层配置容器
    // GraphicsID m_copy_graphics_id;                ///< 待复制图形的id
    std::vector<GraphicsPtr> m_copy_graphics;     ///< 待复制图形
    std::vector<GraphicsPtr> m_graphics;          ///< 图形容器
    std::shared_ptr<PadManager> m_pad_manager;                    ///< pad_manager


    // ResourceVector<ControlPoint> m_controlpoints; ///< 控制点容器

    Event<> callback_graphicsdraw;                                   ///< 整体刷新
    Event<int> callback_cursorchange;                                ///< 切换鼠标指针
    Event<const std::vector<GraphicsPtr>&, bool> callback_graphicsupdate;
    Event<const std::vector<GraphicsPtr>&> callback_graphicschange;  ///< 用于触发命令,需要将图形改变前原始状态记录进命令堆栈方便撤销恢复
    Event<const std::vector<GraphicsPtr>&> callback_graphicsadd;
    Event<const std::vector<GraphicsPtr>&> callback_graphicsdelete;
    Event<const std::vector<GraphicsPtr>&> callback_graphicsselected;

    // Debouncer debouncer;
    // mutable std::mutex mutex;
    // int m_type_controlpoint;
    // ResourceVector<GraphicsPtr> m_graphics;
    // std::vector<ControlPoint> m_controlpoints;
};

#endif //! GRAPHICSMANAGER_H