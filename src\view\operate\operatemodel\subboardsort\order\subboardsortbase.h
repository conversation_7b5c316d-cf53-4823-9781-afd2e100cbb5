/*****************************************************************
 * @file   subboardsortbase.h
 * @brief   子板排序基类
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "projectparam.hpp"
#include "multiboardparam.hpp"
 //Third
namespace subboardsort
{

    class SubboardSortBase
    {
    public:
        virtual ~SubboardSortBase() = default;
        /**
         * @fun Sort
         * @brief  规则子板排序接口
         * @param subboards_
         * @return
         * <AUTHOR>
         * @date 2025.6.6
         */
        virtual int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) = 0;
    protected:
        /**
         * @fun UpdateSubboardInfoOfComponents
         * @brief
         * @param component_
         * @param subboard_name_
         * @param subboard_id_
         * <AUTHOR>
         * @date 2025.6.6
         */
        void UpdateSubboardInfoOfComponents(std::vector<jrsdata::Component>& components_, const std::string& subboard_name_, int subboard_id_);
        void UpdateSubboardInfoOfComponent(jrsdata::Component& components_, const std::string& subboard_name_, int subboard_id_);
        /**
         * @fun UpdateSubboard
         * @brief   更新子板信息
         * @param subboard_
         * @param subboard_id_
         * <AUTHOR>
         * @date 2025.6.6
         */
        void UpdateSubboard(jrsdata::SubBoard& subboard_, int subboard_id_);
    private:
    };
    using SubboardSortBasePtr = std::shared_ptr<subboardsort::SubboardSortBase>;
}

