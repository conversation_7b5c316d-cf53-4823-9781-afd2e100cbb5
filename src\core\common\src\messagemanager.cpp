﻿#include "messagemanager.h"


jrscore::MessageManager::MessageManager()
{

}


jrscore::MessageManager::~MessageManager()
{

}

jrscore::MessageButton jrscore::MessageManager::ShowMessageBox(const jrscore::LogLevel& level_, const std::string& title_, const std::string& msg_, const int& message_btn_)
{
    if (_callback)
    {
        return  _callback(level_, title_, msg_, message_btn_);
    }
    else
    {
        return jrscore::MessageButton::NoButton;
    }
}

void jrscore::MessageManager::SetMessageCallBack(MessageCallBack callback_)
{
    _callback = callback_;
}
