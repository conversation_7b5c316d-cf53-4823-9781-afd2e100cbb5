#include "algofactory.h"
#include "coreapplication.h"
namespace jrsalgo
{
    AlgoFactory::AlgoFactory ()
    {
    }
    AlgoFactory::~AlgoFactory ()
    {
 
    }
    int AlgoFactory::LoadAlgoDrives ( const std::list<std::string>& alog_drive_path_list_ )
    {
        int res = jrscore::AOI_OK;
        for (auto& value : alog_drive_path_list_)
        {
            res = LoadAlgoDrive ( value );
            if (res != jrscore::AOI_OK)
            {
                //return res;
            }
        }
        return res;
    }
    int AlgoFactory::LoadAlgoViews ( const std::list<std::string>& alog_view_path_list_ )
    {
        int res;
        for (auto& value : alog_view_path_list_)
        {
            res = LoadAlgoView ( value );
            if (res != jrscore::AOI_OK)
            {
                //return res;
            }
        }
        return jrscore::AOI_OK;
    }
    int AlgoFactory::LoadAlgoDrive(const std::string& drive_path_)
    {

        auto handle = std::make_shared<jrscore::PluginLoader<jrsoperator::OperatorDriveBase>>( drive_path_ );

        if (!handle)
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_NOT_FOUND_PLUGIN, drive_path_);
            return jrscore::AlgorithmError::E_AOI_ALG_NOT_FOUND_PLUGIN;
        }
        if (!handle->Load())
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL, drive_path_);

            return jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL;
        }

        operator_drive_plugin_handles_v.push_back(handle);

        auto [iter, inserted] = operator_drive_instance_map.emplace(handle->GetPluginInstance()->GetOperatorName(), handle->GetPluginInstance());

        if (!inserted)
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_LOAD_REPEAT,"算子驱动重复加载，请检查算法配置文件："+ drive_path_);

            return jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL;
        }

        return jrscore::AOI_OK;
    }

    int AlgoFactory::LoadAlgoView(const std::string& view_path_)
    {
        auto handle = std::make_shared<jrscore::PluginLoader<jrsoperator::OperatorViewBase>>(view_path_);

        if (!handle)
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_NOT_FOUND_PLUGIN, view_path_);
            return jrscore::AlgorithmError::E_AOI_ALG_NOT_FOUND_PLUGIN;
        }
        if (!handle->Load())
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL, view_path_);

            return jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL;
        }

        operator_view_plugin_handles_v.push_back(handle);

        auto [iter, inserted] = operator_view_instance_map.emplace(handle->GetPluginInstance()->GetOperatorName(), handle->GetPluginInstance());

        if (!inserted)
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_LOAD_REPEAT, "算子界面重复加载，请检查算法配置文件：" + view_path_);

            return jrscore::AlgorithmError::E_AOI_ALG_LOAD_PLUGIN_FAIL;
        }
        
        return jrscore::AOI_OK;
    }
    std::shared_ptr< jrsoperator::OperatorDriveBase> AlgoFactory::GetSpecificAlgoDrive ( const std::string& algo_name_ )
    {

        auto it = operator_drive_instance_map.find ( algo_name_ );
        if (it != operator_drive_instance_map.end ())
        {
            return it->second->Clone();
        }
        Log_ERROR ( "没有找到名为：", algo_name_ ,"的算子驱动实例");
        return nullptr;
    }
    jrsoperator::OperatorViewBase* AlgoFactory::GetSpecificAlgoView ( const std::string& algo_name_ )
    {
        
        auto it = operator_view_instance_map.find ( algo_name_ );
        if (it != operator_view_instance_map.end ())
        {
            return it->second;
        }
        Log_ERROR ( "没有找到名为：" , algo_name_ , "的算子界面实例" );
        return nullptr;
    }


    void AlgoFactory::BindViewAndDrive ()
    {
        for (auto& value_drive : operator_drive_instance_map)
        {
            auto value_view = GetSpecificAlgoView ( value_drive.first );
            if (value_view)
            {
                auto fun_drive = std::bind ( &jrsoperator::OperatorDriveBase::ExecuteOperator , value_drive.second , std::placeholders::_1 );
                value_view->SetExecuteCallBack (fun_drive);
            }
            else
            {
                Log_ERROR ("算法：",value_drive.first,"没有对应的view界面，固无需绑定");
            }
            
        }
    }
}
