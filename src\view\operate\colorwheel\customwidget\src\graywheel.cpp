#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <QtWidgets/qboxlayout.h>
#include <QComboBox>
#include <QCheckBox>
#include "graywheel.h"
#include <QtWidgets/qpushbutton.h>
#include <QWidget>
#include <QLineEdit>
#include <QPainter>
#include <QMouseEvent>
#include <QHBoxLayout>
#include <vector>
#include <algorithm>
#pragma warning(pop)

GrayWheel::~GrayWheel()
{
}
void GrayWheel::UpataGrayTypeIndexSlot(int index)
{
	gray_type_->button(index)->setChecked(true);
	emit UpataGrayTypeIndex(index);
}
void GrayWheel::SetGrayThreSlot(int min_thre, int max_thre)
{
	gray_histogramwidget->SetHistThre(min_thre, max_thre);
	emit SetGrayThre(min_thre, max_thre);
}
GrayWheel::GrayWheel(QWidget* parent) 
    : QWidget(parent)
{
	gray_type_ = new QButtonGroup(this);
	weight_gray_ = new QRadioButton(tr("权重灰度"));
	max_gray_ = new QRadioButton(tr("最大值"));
	min_gray_ = new QRadioButton(tr("最小值"));
	mean_gray_ = new QRadioButton(tr("均值"));
	gray_type_->addButton(weight_gray_, 0);
	gray_type_->addButton(max_gray_, 1);
	gray_type_->addButton(min_gray_, 2);
	gray_type_->addButton(mean_gray_, 3);
	gray_type_->button(gray_type_index_)->setChecked(true);

	QHBoxLayout* radio_layout = new QHBoxLayout();
	radio_layout->addWidget(weight_gray_);
	radio_layout->addWidget(max_gray_);
	radio_layout->addWidget(min_gray_);
	radio_layout->addWidget(mean_gray_);

	QVBoxLayout* hist_layout = new QVBoxLayout(this);
	gray_histogramwidget = new CustomPlotWidget(256, Qt::gray);
	gray_histogramwidget->xAxis->setRange(0, 255);

	gray_histogramwidget->setMinimumHeight(80);
	hist_layout->addLayout(radio_layout); 
	hist_layout->addWidget(gray_histogramwidget); 
	
	connect(gray_type_, &QButtonGroup::idClicked,
		this, &GrayWheel::UpataGrayTypeIndexSlot);
	connect(gray_histogramwidget, &CustomPlotWidget::UpdateThre, this,
		&GrayWheel::SetGrayThreSlot);
}
void GrayWheel::SetGrayHistValue(std::vector<float>& gray_hist)
{
	gray_histogramwidget->SetHistValue(gray_hist);
}