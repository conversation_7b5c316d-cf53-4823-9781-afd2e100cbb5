#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "customlabel.h"

#pragma warning(pop)

int CustomQLabel::GetLabelId()
{
	return id_;
}

void CustomQLabel::UpdateLabelColor(bool is_green)
{
    is_green_ = is_green;
	update();
}

void CustomQLabel::mousePressEvent(QMouseEvent* event)
{
    is_green_ = !is_green_;
    update();
    emit UpdateCurrenImageId(id_);
    QLabel::mousePressEvent(event);
}

void CustomQLabel::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit UpdateCurrenImage(id_);
    }
    QLabel::mouseDoubleClickEvent(event);
}


