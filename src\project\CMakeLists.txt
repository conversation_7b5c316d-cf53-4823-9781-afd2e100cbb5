project(project)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)


# 文件打包
set(manager_src
    projectmanager/src/projectmanager.cpp

)
set (manager_head
    projectmanager/include/projectmanager.h
)

source_group("projectmanager/src" FILES ${manager_src})
source_group("projectmanager/head" FILES ${manager_head})
add_library(${PROJECT_NAME} SHARED
            ${manager_src}
            ${manager_head} 
            ${JRS_VERSIONINFO_RC}

               
)
#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)


target_link_directories(${PROJECT_NAME} 
    PRIVATE
    #OPENCV
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>
)
 
#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}
    
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100>
    #core
    core
)

# 引入头文件
target_include_directories(${PROJECT_NAME} PUBLIC
    ${DIR_PROJECT_CURRENT}src/core/common/include
    ${DIR_PROJECT_CURRENT}src/core/database/include
    ${DIR_PROJECT_CURRENT}src/core/coordinatecenter/include
    ${DIR_PROJECT_CURRENT}src/project/projectmanager/include
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/parambase
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/image
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/projectparam
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/viewparam
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/dataparam
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/deviceparam
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/resultparam
    ${DIR_PROJECT_CURRENT}src/algorithmengine/include
    ${OPENCV_INCLUDE_DIR}
)
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
