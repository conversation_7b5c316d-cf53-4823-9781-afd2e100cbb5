﻿/*****************************************************************//**
 * @file   standardview.h
 * @brief  标准原件view类
 * @details  标准元件即客户保存的此板子的
 * <AUTHOR>
 * @date 2024.4.12
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.4.12         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __SYSPARAVIEW_H__
#define __SYSPARAVIEW_H__
 //QT
#include <QMainWindow>
#include <QLabel>
#include <QString>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTableWidget>
#include <QHeaderView>
//STD
#include <string>
//Custom 
#include "viewmanager.h"
#include "viewbase.h"
#include "coreapplication.h"
//view
#include "systemparamview.h"
#include "commonparamview.h"

namespace jrsaoi
{
    class SettingView : public ViewBase
    {
        Q_OBJECT
    public:
        SettingView(const std::string& name, QWidget* parent = nullptr);
        ~SettingView();
        /**
         * @fun Init 
         * @brief
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Init() override;
        /**
         * @fun UpdateView 
         * @brief
         * @param param_
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save 
         * @brief
         * @param param_
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;
    private:
        void InitMember();
        /**
         * @fun InitView
         * @brief 初始化主界面UI
         * @date 2024.4.16
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun InitConnect
         * @brief 初始化信号槽连接
         * @date 2024.4.16
         * <AUTHOR>
         */
        void InitConnect();
    public slots:
        /**
         * @fun SlotTabChanged
         * @brief
         * @param index
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotTabChanged(int index);
    signals:
        /**
         * @fun SigSave
         * @brief 保存系统参数信号
         * @param data_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigSave(const jrsdata::ViewParamBasePtr& data_);
        /**
         * @fun SigRefresh 
         * @brief
         * @param data_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigRefresh(const jrsdata::ViewParamBasePtr& data_);
    private:
        jrsdata::SettingViewParamPtr _setting_view_param_ptr;
        CommonParamView* _common_param_view;
        SystemParamView* _sys_param_view;
        QTabWidget* _setting_widget; //设置窗口
    };

}
#endif // !__SYSPARAVIEW_H__
