﻿//QT
#include <QSettings>
#include <QJsonObject>
#include <QJsonDocument>
#include <QLineEdit>

//CUSTOM
#include "ui_datasetting.h"
#include "datasetting.h"
#include "checkwidget.h"
#include "paramoperator.h"

DataSetting::DataSetting(QWidget* parent)
    : QWidget(parent)
    , _is_enable_save_param(false)
{
    ui.setupUi(this);
    _detect_result_save_type = new QButtonGroup(this);
    _detect_result_save_type->addButton(ui.radio_btn_save_all_detect_result);
    _detect_result_save_type->addButton(ui.radio_btn_only_save_ng);
    if (map_directory.size() < 9)
    {
        _param_name_map = {
            {QStringLiteral("工程路径："),   jrssettingparam::jrsmachineparam::MACHINE_PARAM_PROJECT_PATH},
            {QStringLiteral("整板图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH},
            {QStringLiteral("元件库路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH},
            {QStringLiteral("维修站路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH},
            {QStringLiteral("测试大图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_TEST_BIG_IMAGE_PATH},
            {QStringLiteral("定位点图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_LOCATION_POINT_IMAGE_PATH},
            {QStringLiteral("算法结果路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGORITHM_RESULT_PATH},
            {QStringLiteral("实时采图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_REAL_TIME_TAKE_IMAGE_PATH},
            {QStringLiteral("原图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ORIGINAL_IMAGE_PATH},
        };
        for (auto& map : _param_name_map)
        {
            SettingData setting;
            setting.label = map.first;
            setting.directory = "";
            setting.enable = false;
            map_directory.insert(setting.label, setting);
        }
        SaveSettingFile();
    }
    UpdateView();
    ConnectSlots();
    InitConnect();
}

DataSetting::~DataSetting()
{
}

void DataSetting::SaveSettingFile()
{
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();
    if (!_is_enable_save_param)
    {
        return; //开机时读取配置文件 拒绝保存
    }
    auto keys = map_directory.keys();
    for (auto& map : map_directory.toStdMap())
    {
        auto map_it = _param_name_map.find(map.first);
        if (map_it != _param_name_map.end())
        {
            std::string value_str = SettingDataToJson(map.second);
            param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, map_it->second, value_str);
        };
    }
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP, ui.edit_ip->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_USER, ui.edit_user->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_DATABASE_NAME, ui.edit_db_name->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PASSWORD, ui.edit_password->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_TYPE, ui.compress_img_type->currentText().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRETY_IMG_TYPE, ui.entirety_img_type->currentText().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_RATE, ui.horizontalSlider_compress_rate->value() * 1.0f);
    bool save_all_detect_result = ui.radio_btn_save_all_detect_result->isChecked();
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL, save_all_detect_result);
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPANY_NO, ui.edit_company_no->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_MACHINE_NO, ui.edit_machine_no->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_SITE_NO, ui.edit_site_no->text().toStdString());
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_THREAD_NO, ui.edit_thread_no->text().toStdString());
    _machine_param.event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;
    emit SigUpdateMachineParam(_machine_param);
}

void DataSetting::UpdateView()
{
    ui.edit_ip->setText(m_ip);
    ui.edit_user->setText(m_user);
    ui.edit_password->setText(m_password);
    ui.edit_db_name->setText(m_db_name);
    ui.compress_img_type->setCurrentText(compress_type);
    ui.entirety_img_type->setCurrentText(entirety_image_type);
    ui.horizontalSlider_compress_rate->setValue(compress_rate);
    ui.edit_company_no->setText(m_company_no);
    ui.edit_machine_no->setText(m_machine_no);
    ui.edit_site_no->setText(m_site_no);
    ui.edit_thread_no->setText(m_thread_no);
}

void DataSetting::UpdateView(const jrsdata::MachineParam& machine_param_)
{
    /** 更新数据库 */
    if (machine_param_.event_name == jrsaoi::DATABASE_CONNECT_EVENT || machine_param_.event_name == jrsaoi::DATABASE_DISCONNECT_EVENT)
    {
        UpdateDBbtn();
        return;
    }
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();
    auto func = [&](const std::string& name_) -> decltype(auto)
        {
            return param_operate->GetSettingParamValueByName<std::string>(jrsdata::ParamLevel::MACHINE, name_);
        };
    for (auto& name_temp : _param_name_map)
    {
        auto value = func(name_temp.second);
        auto setting_data = JsonToSettingData(value);
        map_directory[name_temp.first] = setting_data;
    }
    m_company_no = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPANY_NO).c_str();
    m_machine_no = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MACHINE_NO).c_str();
    m_site_no = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_SITE_NO).c_str();
    m_thread_no = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_THREAD_NO).c_str();
    m_ip = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP).c_str();
    m_db_name = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_DATABASE_NAME).c_str();
    m_user = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_USER).c_str();
    m_password = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PASSWORD).c_str();
    compress_type = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_TYPE).c_str();
    entirety_image_type = func(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRETY_IMG_TYPE).c_str();
    compress_rate = param_operate->GetSettingParamValueByName<float>(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_RATE);
    bool is_save_all_detect_data = param_operate->GetSettingParamValueByName<bool>(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL);
    if (is_save_all_detect_data)
    {
        ui.radio_btn_save_all_detect_result->setChecked(true);
    }
    else
    {
        ui.radio_btn_only_save_ng->setChecked(true);
    }
    UpdateView();
    if (!_is_enable_save_param)
    {
        _is_enable_save_param = true;
    }
}

void DataSetting::ConnectSlots()
{
    connect(ui.btn_clean, &QPushButton::clicked, this, &DataSetting::SlotClean);
    connect(ui.btn_read, &QPushButton::clicked, this, &DataSetting::SlotRead);
    connect(ui.btn_db_operate, &QPushButton::clicked, this, &DataSetting::SlotDBOperate);
    connect(ui.edit_company_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_machine_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_site_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_thread_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_ip, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_db_name, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_user, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    connect(ui.edit_password, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
}

void DataSetting::DisconnectSlots()
{
    disconnect(ui.btn_clean, &QPushButton::clicked, this, &DataSetting::SlotClean);
    disconnect(ui.btn_read, &QPushButton::clicked, this, &DataSetting::SlotRead);
    disconnect(ui.btn_db_operate, &QPushButton::clicked, this, &DataSetting::SlotDBOperate);
    disconnect(ui.edit_company_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_machine_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_site_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_thread_no, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_ip, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_db_name, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_user, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
    disconnect(ui.edit_password, &QLineEdit::editingFinished, this, &DataSetting::SlotLineEditFinished);
}

void DataSetting::SlotClean()
{
    DisconnectSlots();
    auto keys = map_directory.keys();
    for (int i = 0; i < keys.size(); i++)
    {
        map_directory[keys.at(i)].directory = "";
    }
    m_ip = "";
    m_db_name = "";
    m_user = "";
    m_password = "";
    compress_type = "";
    entirety_image_type = "";
    compress_rate = 100;
    m_company_no = "";
    m_site_no = "";
    m_machine_no = "";
    m_thread_no = "";
    UpdateView();
    ConnectSlots();
}

void DataSetting::SlotRead()
{
    _machine_param.event_name = jrsaoi::MACHINE_PARAM_UPDATE_EVENT;
    emit SigUpdateMachineParam(_machine_param);
}

void DataSetting::SlotDBOperate()
{
    auto btn_text = ui.btn_db_operate->text();
    if (btn_text.compare("连接") == 0)
    {
        //发送连接信号
        m_ip = ui.edit_ip->text();
        m_user = ui.edit_user->text();
        m_password = ui.edit_password->text();
        m_db_name = ui.edit_db_name->text();
        SaveSettingFile();
        _machine_param.event_name = jrsaoi::DATABASE_CONNECT_EVENT;
        emit SigUpdateMachineParam(_machine_param);
    }
    else
    {
        //发送断开连接信号
        _machine_param.event_name = jrsaoi::DATABASE_DISCONNECT_EVENT;
        emit SigUpdateMachineParam(_machine_param);
    }
}

void DataSetting::SlotEditIPFinished()
{
    DisconnectSlots();
    m_ip = ui.edit_ip->text();
    SaveSettingFile();
    ConnectSlots();
}

void DataSetting::SlotEditUserFinished()
{
    DisconnectSlots();
    m_user = ui.edit_user->text();
    SaveSettingFile();
    ConnectSlots();
}

void DataSetting::SlotEditPasswordFinished()
{
    DisconnectSlots();
    m_password = ui.edit_password->text();
    SaveSettingFile();
    ConnectSlots();
}

void DataSetting::SlotRadioChecked(QAbstractButton*)
{
    SaveSettingFile();
}

void DataSetting::SlotSaveCompressImg()
{
    jrsdata::RepairData repair_data;
    repair_data.event_name = jrsaoi::OPERATE_SAVE_REAPIR_COMPRESS_IMG_EVENT_NAME;
    emit SigSaveRepairData(repair_data);
}

void DataSetting::SlotSaveBriefComponentInfos()
{
    jrsdata::RepairData repair_data;
    repair_data.event_name = jrsaoi::OPERATE_SAVE_REAPIR_BRIEF_COMPONENT_EVENT_NAME;
    emit SigSaveRepairData(repair_data);
}

void DataSetting::SlotChangeCompressSaveType(QString current_text)
{
    compress_type = current_text;
    SaveSettingFile();
}

void DataSetting::SlotChangeEntiretySaveType(QString current_text)
{
    entirety_image_type = current_text;
    SaveSettingFile();
}

void DataSetting::SlotChangeCompressSaveRate(float value)
{
    compress_rate = value;
    ui.horizontalSlider_compress_rate->setToolTip(QString::number(value));  // 显示当前滑块值
    SaveSettingFile();
}

void DataSetting::SlotLineEditFinished()
{
    DisconnectSlots();
    SaveSettingFile();
    ConnectSlots();
}

void DataSetting::UpdateDBbtn()
{
    auto state = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance()->GetMachineStateParam(jrscheckitem::DATABASE_CHECK_ITEM);
    if (state.check_state == jrsdata::MachineCheckParamInfo::CheckState::OK)
    {
        ui.btn_db_operate->setDisabled(false);
        ui.btn_db_operate->setText("断开连接");
    }
    else if (state.check_state == jrsdata::MachineCheckParamInfo::CheckState::CHECKING)
    {
        ui.btn_db_operate->setDisabled(true);
        ui.btn_db_operate->setText("正在连接");
    }
    else
    {
        if (ui.btn_db_operate->text() != "断开连接")
        {
            JRSMessageBox_INFO("提示", "链接数据库失败，请检查IP、用户名是否正确", jrscore::MessageButton::Ok);
        }
        ui.btn_db_operate->setDisabled(false);
        ui.btn_db_operate->setText("连接");
    }
}

void DataSetting::InitConnect()
{
    connect(ui.btn_save_all_info, &QPushButton::clicked, this, &DataSetting::SlotSaveCompressImg);
    connect(ui.btn_save_all_info, &QPushButton::clicked, this, &DataSetting::SlotSaveBriefComponentInfos);
    connect(ui.compress_img_type, &QComboBox::currentTextChanged, this, &DataSetting::SlotChangeCompressSaveType);
    connect(ui.entirety_img_type, &QComboBox::currentTextChanged, this, &DataSetting::SlotChangeEntiretySaveType);
    connect(ui.horizontalSlider_compress_rate, &QSlider::valueChanged, this, &DataSetting::SlotChangeCompressSaveRate);
    for (auto button : _detect_result_save_type->buttons())
    {
        connect(button, &QAbstractButton::toggled, this, [this, button](bool checked)
            {
                if (checked)
                {
                    this->SlotRadioChecked(button);
                }
            });
    }
}

std::string DataSetting::SettingDataToJson(const SettingData& setting_)
{
    QJsonObject json;
    json["label"] = setting_.label;
    json["directory"] = setting_.directory;
    json["enable"] = setting_.enable;
    QJsonDocument jsonDoc(json);
    QString json_string = jsonDoc.toJson(QJsonDocument::Compact);
    return json_string.toStdString();
}

SettingData DataSetting::JsonToSettingData(const std::string& setting_)
{
    QString json_string = QString::fromStdString(setting_);
    QJsonDocument jsonDoc = QJsonDocument::fromJson(json_string.toUtf8());
    QJsonObject json = jsonDoc.object();
    SettingData settingData;
    settingData.label = json["label"].toString();
    settingData.directory = json["directory"].toString();
    settingData.enable = json["enable"].toBool();
    return settingData;
}

void DataSetting::SlotDataChange(QString label, SettingData setting_data)
{
    DisconnectSlots();
    map_directory[label] = setting_data;
    SaveSettingFile();
    ConnectSlots();
}

