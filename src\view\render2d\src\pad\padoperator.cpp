﻿#include "padoperator.hpp"

#include "render2deventparam.hpp"
#include "paramoperator.h"
//Custom
#include "viewparam.hpp"
#include "coreapplication.h"
#include "cvtools.h"

jrsaoi::PadOperator::PadOperator()
{
    Init();
}

jrsaoi::PadOperator::~PadOperator()
{
}

int jrsaoi::PadOperator::Update(const std::shared_ptr<jrsdata::RenderEventParam>& param_, const jrsaoi::Render2dEventParam& current_event_param)
{

    _render_2d_event_param->update_operator_param = current_event_param.update_operator_param;
    return PadEventHandler(param_);
}

int jrsaoi::PadOperator::TopBottomReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    (void)param_;
    //多次查询 Part Number 待优化！ By:HJC 2025/1/2
    auto component = _project_param_process_instance->ReadComponentRef(_render_2d_event_param->update_operator_param.select_param.component_name, _render_2d_event_param->update_operator_param.select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component.has_value())
    {
        return -1;
    }
    auto new_group_name = _project_param_process_instance->GetComponentUnitNewGroupName(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_units = _project_param_process_instance->ReadComponentUnit(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    for (auto component_unit : component_units)
    {
        if (component_unit.unit_group_name == _render_2d_event_param->update_operator_param.unit_group_name)
        {
            jrsdata::ComponentUnit new_component_unit = component_unit;
            if (new_component_unit.direction == jrsdata::ComponentUnit::Direction::DOWN ||
                new_component_unit.direction == jrsdata::ComponentUnit::Direction::UP)
            { //上下则 镜像
                new_component_unit.x = -new_component_unit.x;
                new_component_unit.y = -new_component_unit.y;
            }
            else  //左右  则复制  偏移10个像素
            {
                new_component_unit.x = new_component_unit.x + 10;
                new_component_unit.y = new_component_unit.y + 10;
            }
            new_component_unit.unit_name = jtools::StringOperation::ReplaceString(new_component_unit.unit_name, new_component_unit.unit_group_name, new_group_name);
            new_component_unit.unit_group_name = new_group_name;
            _project_param_process_instance->CreateComponentUnit(new_component_unit, component->get().component_part_number);
        }
    }
    return 0;
}
int jrsaoi::PadOperator::LeftRightReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    (void)param_;
    auto component = _project_param_process_instance->ReadComponentRef(_render_2d_event_param->update_operator_param.select_param.component_name, _render_2d_event_param->update_operator_param.select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component.has_value())
    {
        return -1;
    }
    auto new_group_name = _project_param_process_instance->GetComponentUnitNewGroupName(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_units = _project_param_process_instance->ReadComponentUnit(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    for (auto component_unit : component_units)
    {
        if (component_unit.unit_group_name == _render_2d_event_param->update_operator_param.unit_group_name)
        {
            jrsdata::ComponentUnit new_component_unit = component_unit;
            if (new_component_unit.direction == jrsdata::ComponentUnit::Direction::DOWN ||
                new_component_unit.direction == jrsdata::ComponentUnit::Direction::UP)
            {    //上下 偏移10个像素
                new_component_unit.x = new_component_unit.x + 10;
                new_component_unit.y = new_component_unit.y + 10;
            }
            else //左右 镜像复制 
            {
                new_component_unit.x = -new_component_unit.x;
                new_component_unit.y = -new_component_unit.y;
            }
            new_component_unit.unit_name = jtools::StringOperation::ReplaceString(new_component_unit.unit_name, new_component_unit.unit_group_name, new_group_name);
            new_component_unit.unit_group_name = new_group_name;
            _project_param_process_instance->CreateComponentUnit(new_component_unit, component->get().component_part_number);
        }
    }
    return 0;
}

int jrsaoi::PadOperator::Rotate90DegreeReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    (void)param_;
    auto component = _project_param_process_instance->ReadComponentRef(_render_2d_event_param->update_operator_param.select_param.component_name, _render_2d_event_param->update_operator_param.select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component.has_value())
    {
        return -1;
    }
    auto new_group_name = _project_param_process_instance->GetComponentUnitNewGroupName(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_units = _project_param_process_instance->ReadComponentUnit(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_body_unit = _project_param_process_instance->ReadComponentBodyRef(component->get().component_part_number);
    for (auto component_unit : component_units)
    {

        if (component_unit.unit_group_name == _render_2d_event_param->update_operator_param.unit_group_name)
        {

            jcvtools::JrsHomMat2D hom_2d;
            jrsdata::ComponentUnit new_component_unit = component_unit;

            hom_2d.AddHomMat2dRotate(90, component->get().x, component->get().y);
            int hight_width_offset = 0;
            hight_width_offset = (component_body_unit->get().height - component_body_unit->get().width) / 2;

            if (new_component_unit.direction == jrsdata::ComponentUnit::Direction::UP
                || new_component_unit.direction == jrsdata::ComponentUnit::Direction::DOWN)
            {    //上下 偏移10个像素
                hight_width_offset = -hight_width_offset;
            }
            hom_2d.AddHomMat2dTranslate(hight_width_offset, hight_width_offset);
            auto new_point = hom_2d.AffineTransPoint({ (float)(component->get().x + new_component_unit.x),  (float)(component->get().y + new_component_unit.y) });

            new_component_unit.direction = GetNextDirection(new_component_unit.direction);
            new_component_unit.x = new_point.x - component->get().x;
            new_component_unit.y = new_point.y - component->get().y;

            int temp_width = new_component_unit.width;
            new_component_unit.width = new_component_unit.height;
            new_component_unit.height = temp_width;

            new_component_unit.unit_name = jtools::StringOperation::ReplaceString(new_component_unit.unit_name, new_component_unit.unit_group_name, new_group_name);
            new_component_unit.unit_group_name = new_group_name;
            _project_param_process_instance->CreateComponentUnit(new_component_unit, component->get().component_part_number);
        }
    }
    return 0;
}
int jrsaoi::PadOperator::Rotate180DegreeReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    (void)param_;
    auto component = _project_param_process_instance->ReadComponentRef(_render_2d_event_param->update_operator_param.select_param.component_name, _render_2d_event_param->update_operator_param.select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component.has_value())
    {
        return -1;
    }
    auto new_group_name = _project_param_process_instance->GetComponentUnitNewGroupName(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_units = _project_param_process_instance->ReadComponentUnit(component->get().component_part_number, jrsdata::ComponentUnit::Type::PAD);
    auto component_body_unit = _project_param_process_instance->ReadComponentBodyRef(component->get().component_part_number);
    for (auto component_unit : component_units)
    {

        if (component_unit.unit_group_name == _render_2d_event_param->update_operator_param.unit_group_name)
        {

            jcvtools::JrsHomMat2D hom_2d;
            jrsdata::ComponentUnit new_component_unit = component_unit;

            hom_2d.AddHomMat2dRotate(180, component->get().x, component->get().y);
            int hight_width_offset = 0;
            hight_width_offset = (component_body_unit->get().height - component_body_unit->get().width) / 2;

            if (new_component_unit.direction == jrsdata::ComponentUnit::Direction::UP
                || new_component_unit.direction == jrsdata::ComponentUnit::Direction::DOWN)
            {    //上下 偏移10个像素
                hight_width_offset = -hight_width_offset;
            }
            hom_2d.AddHomMat2dTranslate(hight_width_offset, hight_width_offset);
            auto new_point = hom_2d.AffineTransPoint({ (float)(component->get().x + new_component_unit.x),  (float)(component->get().y + new_component_unit.y) });


            new_component_unit.direction = GetMirrorDirection(new_component_unit.direction);
            new_component_unit.x = new_point.x - component->get().x;
            new_component_unit.y = new_point.y - component->get().y;

            new_component_unit.unit_name = jtools::StringOperation::ReplaceString(new_component_unit.unit_name, new_component_unit.unit_group_name, new_group_name);
            new_component_unit.unit_group_name = new_group_name;
            _project_param_process_instance->CreateComponentUnit(new_component_unit, component->get().component_part_number);
        }
    }
    return 0;
}
jrsdata::ComponentUnit::Direction jrsaoi::PadOperator::GetNextDirection(jrsdata::ComponentUnit::Direction dir)
{
    switch (dir)
    {
    case jrsdata::ComponentUnit::Direction::UP:
        return jrsdata::ComponentUnit::Direction::RIGHT;
    case jrsdata::ComponentUnit::Direction::RIGHT:
        return jrsdata::ComponentUnit::Direction::DOWN;
    case jrsdata::ComponentUnit::Direction::DOWN:
        return jrsdata::ComponentUnit::Direction::LEFT;
    case jrsdata::ComponentUnit::Direction::LEFT:
        return jrsdata::ComponentUnit::Direction::UP;
    default:
        return jrsdata::ComponentUnit::Direction::UNKNOWN;
    }
}
jrsdata::ComponentUnit::Direction jrsaoi::PadOperator::GetMirrorDirection(jrsdata::ComponentUnit::Direction dir)
{
    switch (dir)
    {
    case jrsdata::ComponentUnit::Direction::UP:
        return jrsdata::ComponentUnit::Direction::DOWN;
    case jrsdata::ComponentUnit::Direction::RIGHT:
        return jrsdata::ComponentUnit::Direction::LEFT;
    case jrsdata::ComponentUnit::Direction::DOWN:
        return jrsdata::ComponentUnit::Direction::UP;
    case jrsdata::ComponentUnit::Direction::LEFT:
        return jrsdata::ComponentUnit::Direction::RIGHT;
    default:
        return jrsdata::ComponentUnit::Direction::UNKNOWN;
    }
}
bool jrsaoi::PadOperator::IsValidParam(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    if (!param_)
    {
        Log_ERROR("project_event_param_为空");
        return false;
    }

    if (param_->event_name.empty())
    {
        Log_ERROR("event_name is empty");
        return false;
    }
    return true;
}
bool jrsaoi::PadOperator::InvokeFun(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{

    auto it = _pad_func_map.find(param_->event_name);
    if (it == _pad_func_map.end())
    {
        Log_ERROR("PadOperator::InvokeFun() event_name:", param_->event_name, " not found");
        return false;
    }
    if (!it->second)
    {
        Log_ERROR("PadOperator::EventHandler() event_name:", param_->event_name, " has invalid function pointer");
        return false;
    }
    it->second(param_);  // 执行函数
    return true;

}
int jrsaoi::PadOperator::PadEventHandler(const std::shared_ptr<jrsdata::RenderEventParam>& param_)
{
    if (!IsValidParam(param_))
    {
        return -1;
    }
    if (!InvokeFun(param_))
    {
        return -1;
    }
    return 0;
}
void jrsaoi::PadOperator::Init()
{
    InitMember();
}

void jrsaoi::PadOperator::InitMember()
{
    _render_2d_event_param = std::make_shared< jrsaoi::Render2dEventParam>();
    _project_param_process_instance = jrsaoi::ParamOperator::GetInstance().GetProjectDataProcessInstance();
    _pad_func_map = { {jrsaoi::SHORTCUT_ACT_COPY_PAD_TOP_BOTTOM_MIRROR,std::bind(&PadOperator::TopBottomReplication,this,std::placeholders::_1)},
                      {jrsaoi::SHORTCUT_ACT_COPY_PAD_LEFT_RIGHT_MIRROR,std::bind(&PadOperator::LeftRightReplication,this,std::placeholders::_1)},
                      {jrsaoi::SHORTCUT_ACT_COPY_PAD_ROTATE_90_DUPLICATE,std::bind(&PadOperator::Rotate90DegreeReplication,this,std::placeholders::_1)},
                      {jrsaoi::SHORTCUT_ACT_COPY_PAD_ROTATE_180_DUPLICATE,std::bind(&PadOperator::Rotate180DegreeReplication,this,std::placeholders::_1)},

    };
}

