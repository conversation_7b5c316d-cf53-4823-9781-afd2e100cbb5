
/*****************************************************************
 * @file   paramprocess.h
 * @brief  对系统参数、机台参数的处理
 * @details
 * <AUTHOR>
 * @date 2025.5.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.5.6          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSPARAMROCESS_H__
#define __JRSPARAMROCESS_H__
 //STD
#include <shared_mutex>  // shared_mutex shared_lock
//custom
#include "pluginexport.hpp"
#include "viewparam.hpp"

namespace jrsparam
{
    class JRS_AOI_PLUGIN_API ParameterProcess
    {
    public:
        /**
        * @fun SetSettingParams
        * @brief 设置所有参数
        * @param param_
        * @return
        * @date 2025.5.6
        * <AUTHOR>
        */
        int SetSettingParams(const jrsdata::SettingViewParamPtr& param_);
        /**
        * @fun SetSystemSettingParams
        * @brief 设置系统参数
        * @param param_
        * @return
        * @date 2025.5.6
        * <AUTHOR>
        */
        int SetSystemSettingParams(const jrsdata::SystemParam& param_);
        /**
        * @fun SetMachineSettingParams
        * @brief 设置机台参数
        * @param param_
        * @return
        * @date 2025.5.6
        * <AUTHOR>
        */
        int SetMachineSettingParams(const jrsdata::MachineParam& param_);
        /**
        * @fun SetMachineStateParams
        * @brief 设置机台状态参数
        * @param param_
        * @return
        * @date 2025.5.6
        * <AUTHOR>
        */
        int SetMachineStateParams(const jrsdata::SystemStateMap& param_);
        /**
         * @brief 获取指定级别下的设定参数值。 HJC - 2025/5/6
         *
         * 根据参数名称和参数等级（系统级或设备级）获取对应的参数值
         *
         *
         * @param level_     参数等级（系统参数或设备参数）。
         * @param item_name_ 参数名称（键值）。
         * @return jrsdata::JrsVariant 获取到的参数值，若未找到或出错返回默认构造的 variant。
         */
        template<typename T>
        T GetSettingParamValueByName(const jrsdata::ParamLevel level_, const std::string& item_name_)
        {
            const jrsdata::SettingParamMap* setting_param_map = nullptr;

            if (level_ == jrsdata::ParamLevel::SYSTEM)
            {
                setting_param_map = &_param_ptr->sys_param.sys_params;
            }
            else if (level_ == jrsdata::ParamLevel::MACHINE)
            {
                setting_param_map = &_param_ptr->machine_param.machine_params_data;
            }

            if (!setting_param_map)
            {
                Log_WARN(/*jrscore::CoreError::E_AOI_CORE_INIT_FAIL,*/ "参数获取失败（无效Level）！");
                return T{};
            }

            auto it = setting_param_map->find(item_name_);
            if (it == setting_param_map->end())
            {
                Log_WARN(/*jrscore::CoreError::E_AOI_CORE_INIT_FAIL,*/ "参数获取失败（未找到项）：", item_name_);
                return T{};
            }

            const auto& param = it->second;
            const std::string& type = param.param_type;
            const std::string& value = param.param_value;

            try
            {
                if constexpr (std::is_same_v<T, int>)
                {
                    if (type == "int")
                        return std::stoi(value);
                }
                else if constexpr (std::is_same_v<T, float>)
                {
                    if (type == "float")
                        return std::stof(value);
                }
                else if constexpr (std::is_same_v<T, double>)
                {
                    if (type == "double")
                        return std::stod(value);
                }
                else if constexpr (std::is_same_v<T, std::string>)
                {
                    if (type == "std::string" || type == "string")
                        return value;
                }
                else if constexpr (std::is_same_v<T, bool>)
                {
                    if (type == "bool")
                        return (value == "true" || value == "1");
                }
                else if constexpr (std::is_same_v<T, std::vector<int>>)
                {
                    if (type == "vector<int>")
                    {
                        std::vector<int> vec;
                        std::istringstream iss(value);
                        std::string token;
                        while (std::getline(iss, token, ','))
                        {
                            if (!token.empty())
                                vec.push_back(std::stoi(token));
                        }
                        return vec;
                    }
                }

                // 类型不匹配
                Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL,
                    "参数类型与期望不匹配，param_type=" + type + ", 模板类型=" + typeid(T).name());
            }
            catch (const std::exception& e)
            {
                Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "参数解析失败：" + std::string(e.what()));
            }

            return T{};  // 统一返回默认值
        }
        /**
        * @fun GetSettingParamExplanByName
        * @brief 根据项目名获取参数解释
        * @param param_
        * @return
        * @date 2025.5.6
        * <AUTHOR>
        */
        std::string GetSettingParamExplanByName(const jrsdata::ParamLevel level_, const std::string& item_name_);
        /**
         * @brief 设置指定级别下的设定参数值。 HJC-2025/5/6
         *
         * 根据参数名称和等级设置指定值，若参数不存在则自动新增。
         *
         * @param level_     参数等级（系统参数或设备参数）。
         * @param item_name_ 参数名称（键值）。
         * @param value_     要设置的参数值，类型为 std::variant。
         * @param explain_   设置说明，可选参数，用于记录设置原因或备注。
         * @return int       设置成功返回 0，失败返回负数。
         */
        int ReplaceSettingParamValueByName(const jrsdata::ParamLevel level_, const std::string& item_name_,
            const jrsdata::JrsVariant& value_, const std::string& explain_ = "");
        /**
        * @brief 根据机台状态项，获取机台状态信息。
        *
        * @param item_name_ 参数名称（键值）。
        * @return jrsdata::MachineCheckParamInfo       机台检测参数信息
        */
        jrsdata::MachineCheckParamInfo GetMachineStateParam(const std::string& item_name_);
        /**
        * @brief 更新或添加系统状态参数  -----TODO：暂时不能用-其他的地方写的太零散了，有时间再规整到一起
        * @param item_name_ 参数名称（键值）。
        * @return jrsdata::MachineCheckParamInfo       机台检测参数信息
        */
        int ReplaceMachineStateParam(const std::string& item_name_, const jrsdata::MachineCheckParamInfo& machine_check_param_info_);



        /**
         * @brief 根据传入的 JSON 路径判断是否启用，并返回路径。
         *
         * 该函数从指定的 JSON 文件路径中提取配置项，判断某项是否启用，
         * 并返回对应的机器路径字符串。
         *
         * @param path_jason_ JSON 配置文件的路径。
         * @return std::pair<bool, std::string>
         *         - 第一个值表示是否启用（true 表示启用，false 表示未启用）。
         *         - 第二个值为路径字符串（如果未启用可为空字符串）。
         */
        std::pair<bool, std::string> GetMachinePathAndEnable(const std::string& path_jason_);

        std::shared_ptr<jrsdata::SettingViewParam>& GetSettingParams();
        ParameterProcess();
        ~ParameterProcess();
        ParameterProcess(const ParameterProcess&) = delete;
        ParameterProcess(ParameterProcess&&) = delete;
        ParameterProcess& operator=(const ParameterProcess&) = delete;
        ParameterProcess& operator=(ParameterProcess&&) = delete;
    private:
        inline bool IsParamPtrEmpty() const
        {
            return !_param_ptr;
        }
        std::shared_ptr<jrsdata::SettingViewParam> _param_ptr;/**< 所有设置参数**/
        jrsdata::SystemStateMap _sys_state_map; /**< 系统状态参数*/

    };
    using ParameterProcessPtr = std::shared_ptr<ParameterProcess>;
}
#endif // !__JRSPARAMROCESS_H__