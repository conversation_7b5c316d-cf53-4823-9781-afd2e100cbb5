/*****************************************************************//**
 * @file   histogramwidget.h
 * @brief  竖向柱状图
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSHISTOGRAMWIDGET_H__
#define __JRSHISTOGRAMWIDGET_H__

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "qcustomplot.h"
#include <opencv2/opencv.hpp>
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QMouseEvent>
#pragma warning(pop)
class CustomPlotWidget : public QCustomPlot
{
    Q_OBJECT 

public:
    CustomPlotWidget(int hist_bin = 256,
        QColor histogram_color = Qt::blue, QWidget* parent = nullptr);
    int SetHistValue(const std::vector<float>& hist);
    int SetHistThre(int min_thre, int max_thre);
    int SetHistBin(int bin);
signals:
    void UpdateThre(int min_thre, int max_thre);
protected:
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
private:
    int min_thre_ = 0;
    int max_thre_= 255;
    int min_thre_copy_= 0;
	int max_thre_copy_= 255;
    int hist_bin_ = 2000;
    QColor histogram_color_;
    std::vector<float> hist_;
    QCPItemStraightLine* lower_limit_line;
    QCPItemStraightLine* upper_limit_line;
    QCPItemRect* fillRect;
    bool dragging_lower_line = false;
    bool dragging_upper_line = false;
    bool min_max_value_is_init = true;
};

#endif