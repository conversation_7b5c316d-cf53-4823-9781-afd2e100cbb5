/*****************************************************************//**
 * @file   spdlogger.h
 * @brief  封装spdlog功能
 * @details    
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __SPDLOGGER_H__
#define __SPDLOGGER_H__


//Custom
#include "abstractlogger.h"
#include "errorhandler.h"

//spdlog
#pragma warning(push, 1)
 
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/dup_filter_sink.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/async_logger.h>
#include <spdlog/details/thread_pool.h>
#include <spdlog/details/thread_pool-inl.h>
#pragma warning(pop)
namespace jrscore
{

class Spdlogger :
    public AbstractLogger
{
public:
    Spdlogger (const std::string& log_name_);
    ~Spdlogger ();


    inline void SetLogFolderPath (const std::string& path)override
    {
        log_folder = path;
    }

    inline const std::string& GetLogFolderPath ()const override
    {
        return log_folder;
    }

    inline const std::string& GetLogName ()override
    {
        return log_name;
    }

    inline void SetLogPosition (const LogPosition pos_)override
    {
        log_sink_pos = pos_;
    }

    inline const LogPosition GetLogPosition () const override
    {
        return log_sink_pos;
    }
    inline void SetLogMode (const LogMode mode_)override
    {
        log_mode = mode_;
    }
    inline const LogMode GetLogMode ()const override
    {
        return log_mode;
    }
    inline void SetLogOutputLevel (const LogLevel level_)override
    {

        log_output_level = level_;
        
        if(p_log_impl)
        {
            p_log_impl->set_level ((spdlog::level::level_enum)log_output_level);
        }
    }

    inline LogLevel GetLogOutputLevel ()const override
    {
        return log_output_level;
    }

     AOIErrorCode Init () override;


     inline void SetLogCallBack (LogCallBack cb_) override
     {
         call_back = cb_;
     }
protected:

 
    void InvokeLogCallBack (const LogLevel level_, const std::string& msg_)override
    {
        if (call_back)
        {
            call_back (log_name,level_, msg_);
        }
    }
  
    void LogInternal (const LogLevel level_, const std::string& msg_) override;


    void Flush ()override
    {
        assert (p_log_impl);
        p_log_impl->flush ();
    }

private:
    std::shared_ptr<spdlog::details::thread_pool> thread_pool_impl;/**< */
    std::once_flag once_flag;/**< 多线程一次初始化标记 */
    std::shared_ptr<spdlog::logger> p_log_impl; /**< spdlog实现类 */

    LogPosition log_sink_pos;    /**< 日志输出位置 */
    LogMode log_mode;            /**< 同步/异步模式 */
    LogLevel log_output_level;   /**< 日志输出级别*/
    std::string log_name;        /**< 日志名 */
    std::string log_folder;      /**< 日志输出文件夹*/
    LogCallBack call_back;       /**< 日志回调处理函数*/



};

}
#endif // !__SPDLOGGER_H__

