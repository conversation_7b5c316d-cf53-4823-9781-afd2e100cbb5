<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OneDetectView</class>
 <widget class="QWidget" name="OneDetectView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>367</width>
    <height>664</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="5" column="0">
       <widget class="QFrame" name="frame_3">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_3">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0" colspan="2">
          <widget class="QWidget" name="widget_4" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>26</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>26</height>
            </size>
           </property>
           <layout class="QGridLayout" name="gridLayout_10">
            <property name="leftMargin">
             <number>3</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>3</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="verticalSpacing">
             <number>0</number>
            </property>
            <item row="0" column="1">
             <widget class="QRadioButton" name="rb_error_type">
              <property name="text">
               <string>错误类型</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QRadioButton" name="rb_device">
              <property name="text">
               <string>元件</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QPushButton" name="btn_clear_device">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>清除</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="0" colspan="2">
          <widget class="QFrame" name="frame_6">
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Plain</enum>
           </property>
           <property name="lineWidth">
            <number>0</number>
           </property>
           <layout class="QGridLayout" name="gridLayout_17" columnstretch="4">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="spacing">
             <number>0</number>
            </property>
            <item row="0" column="0">
             <widget class="HorizontalBarChart" name="horizontalbarchart_result" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>100</height>
               </size>
              </property>
              <layout class="QGridLayout" name="gridLayout_4">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QFrame" name="frame_2">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>100</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Plain</enum>
        </property>
        <property name="lineWidth">
         <number>0</number>
        </property>
        <layout class="QGridLayout" name="gridLayout_6">
         <property name="leftMargin">
          <number>3</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="2" column="0">
          <widget class="QWidget" name="widget_2" native="true">
           <layout class="QGridLayout" name="gridLayout_11" rowstretch="0">
            <property name="leftMargin">
             <number>3</number>
            </property>
            <property name="topMargin">
             <number>3</number>
            </property>
            <property name="rightMargin">
             <number>3</number>
            </property>
            <property name="bottomMargin">
             <number>3</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>3</number>
            </property>
            <item row="0" column="0">
             <widget class="QLabel" name="edit_total_devices">
              <property name="text">
               <string>  总元件：0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLabel" name="edit_run_time">
              <property name="text">
               <string>运行时间：0天 0时 0分 0秒</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QWidget" name="widget_12" native="true">
           <layout class="QGridLayout" name="gridLayout_15" columnstretch="1,1">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>0</number>
            </property>
            <item row="0" column="0">
             <widget class="QWidget" name="widget_10" native="true">
              <layout class="QGridLayout" name="gridLayout_16">
               <property name="leftMargin">
                <number>3</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="text">
                  <string>  工单号：</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="edit_ticket_num"/>
               </item>
              </layout>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="edit_start_time">
              <property name="text">
               <string>开机时间：1900-00-00 00:00:00</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QWidget" name="widget_5" native="true">
           <layout class="QGridLayout" name="gridLayout_12" rowstretch="0">
            <property name="leftMargin">
             <number>3</number>
            </property>
            <property name="topMargin">
             <number>3</number>
            </property>
            <property name="rightMargin">
             <number>3</number>
            </property>
            <property name="bottomMargin">
             <number>3</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>3</number>
            </property>
            <item row="0" column="1">
             <widget class="QLabel" name="edit_loop_time">
              <property name="text">
               <string>检测时间：0.0秒</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="edit_test_count_ng">
              <property name="text">
               <string>NG元件数：0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QWidget" name="widget_8" native="true">
           <layout class="QGridLayout" name="gridLayout_13" rowstretch="0">
            <property name="leftMargin">
             <number>3</number>
            </property>
            <property name="topMargin">
             <number>3</number>
            </property>
            <property name="rightMargin">
             <number>3</number>
            </property>
            <property name="bottomMargin">
             <number>3</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>3</number>
            </property>
            <item row="0" column="1">
             <widget class="QLabel" name="edit_scan_time">
              <property name="text">
               <string>FOV时间：0秒</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="edit_test_count">
              <property name="text">
               <string>  测试数：0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QWidget" name="widget_9" native="true">
           <layout class="QGridLayout" name="gridLayout_14" rowstretch="0">
            <property name="leftMargin">
             <number>3</number>
            </property>
            <property name="topMargin">
             <number>3</number>
            </property>
            <property name="rightMargin">
             <number>3</number>
            </property>
            <property name="bottomMargin">
             <number>3</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>3</number>
            </property>
            <item row="0" column="0">
             <widget class="QLabel" name="edit_project_name">
              <property name="text">
               <string>    工程：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QFrame" name="frame_5">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Plain</enum>
        </property>
        <property name="lineWidth">
         <number>0</number>
        </property>
        <layout class="QGridLayout" name="gridLayout_7">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="CustomHistogram" name="histogram_result" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>140</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>140</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="CustomTableView" name="tv_result">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>132</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>132</height>
         </size>
        </property>
        <property name="editTriggers">
         <set>QAbstractItemView::NoEditTriggers</set>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QWidget" name="widget" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>44</height>
         </size>
        </property>
        <layout class="QGridLayout" name="gridLayout_9">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="horizontalSpacing">
          <number>6</number>
         </property>
         <property name="verticalSpacing">
          <number>0</number>
         </property>
         <item row="0" column="2">
          <widget class="QComboBox" name="comboBox">
           <property name="minimumSize">
            <size>
             <width>108</width>
             <height>0</height>
            </size>
           </property>
           <item>
            <property name="text">
             <string>白班8:00-20:00</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>晚班20:00-8:00</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QRadioButton" name="radioButton">
           <property name="text">
            <string>班次</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QPushButton" name="btn_clear_ratio">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>清除</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QRadioButton" name="radioButton_2">
           <property name="text">
            <string>时间</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QDateTimeEdit" name="data_start"/>
         </item>
         <item row="0" column="0">
          <widget class="QCheckBox" name="checkBox">
           <property name="text">
            <string>统计良率</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QFrame" name="frame_4">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>115</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>115</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="track_layout">
        <property name="spacing">
         <number>0</number>
        </property>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomHistogram</class>
   <extends>QWidget</extends>
   <header>customhistogram.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>HorizontalBarChart</class>
   <extends>QWidget</extends>
   <header>horizontalbarchart.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>CustomTableView</class>
   <extends>QTableView</extends>
   <header>customtableview.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
