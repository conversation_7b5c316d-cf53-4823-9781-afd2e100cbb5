/*****************************************************************//**
 * @file   abstractlogger.h
 * @brief  日志类接口
 * @details    
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>YYZhang      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __ABSTRACTLOGGER_H__

#define __ABSTRACTLOGGER_H__

//Custom
#include <iostream>
#include "jrslogtypedefines.h"
#include "errorhandler.h"
#include <memory>
#include <functional>
namespace jrscore
{

    /*** @brief 日志抽象类接口 YYZhang 2024.1.23*/
    class AbstractLogger
    {

    public:
        virtual ~AbstractLogger () = default;

        /**
         * @fun SetLogFolderPath
         * @brief 设置日志文件夹路径
         * @param path [in] 路径
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void SetLogFolderPath (const std::string& path) = 0;

        /**
         * @fun GetLogFolderPath
         * @brief 返回日志文件夹路径
         * @return 路径
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual const std::string& GetLogFolderPath ()const = 0;

        /**
         * @fun GetLogName
         * @brief 获取日志文件名
         * @return  文件名
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual const std::string& GetLogName () = 0;

        /**
         * @fun SetLogPosition
         * @brief 设置日志输出位置 文件/控制台/all
         * @param pos_ 输出位置
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void SetLogPosition (const LogPosition pos_) = 0;

        /**
         * @fun GetLogPosition
         * @brief 获取日志输出位置
         * @return 日志输出位置
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual const LogPosition GetLogPosition () const = 0;

        /**
         * @fun SetLogMode 
         * @brief 设置日志输出模式 同步/异步
         * @param mode_ [IN] 模式
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void SetLogMode (const LogMode mode_) = 0;
        
        /**
         * @fun GetLogMode 
         * @brief 获取日志模式
         * @return  返回模式
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual const LogMode GetLogMode()const = 0;

        /**
         * @fun SetLogOutputLevel 
         * @brief 设置日志输出等级
         * @param level_ [IN] 日志等级
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void SetLogOutputLevel (const LogLevel level_) = 0;

        /**
         * @fun GetLogOutputLevel 
         * @brief 获取日志输出等级
         * @return 返回日志输出等级
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual LogLevel GetLogOutputLevel ()const = 0;
        /**
         * @fun Init 
         * @brief 初始化
         * @return 成功返回 JRS_OK ,否则返回对应错误码
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual AOIErrorCode Init () = 0;

        /**
         * @fun Flush 
         * @brief 刷新日志
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void Flush () = 0;

        /**
         * @fun Log 
         * @brief 记录日志接口
         * @param level_
         * @param msg
         * @date 2024.1.23
         * <AUTHOR>
         */
        inline void Log (const LogLevel level_, const std::string& msg_)
        {
            LogInternal (level_, msg_);
            InvokeLogCallBack (level_,msg_);
        }

        /***@brief Log信息回调函数 YYZhang 2024.1.23*/
        using  LogCallBack = std::function<void (const std::string& logName, const LogLevel level, const std::string& msg)>;
        
        /**
         * @fun SetLogCallBack 
         * @brief 设置log信息回调函数
         * @param cb_ [IN] 回调函数
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void SetLogCallBack (LogCallBack cb_) = 0;
        
    protected:
        /**
         * @fun InvokeLogCallBack 
         * @brief 触发日志回调函数
         * @param level_[IN] 日志等级
         * @param msg [IN]日志内容
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void InvokeLogCallBack (const LogLevel level_, const std::string& msg) = 0;
        /**
         * @fun LogInternal 
         * @brief 
         * @param level_ 日志等级
         * @param msg_ 日志信息
         * @date 2024.1.23
         * <AUTHOR>
         */
        virtual void LogInternal (const LogLevel level_, const std::string& msg_) = 0;
    };

    using AOILoggerPtr = std::unique_ptr<AbstractLogger>;

}
#endif // !__ABSTRACTLOGGER_H__
