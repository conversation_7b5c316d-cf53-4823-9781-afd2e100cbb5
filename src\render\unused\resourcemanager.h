/**********************************************************************
 * @brief  资源管理类. 已弃用
 *
 * @file   resourcemanager.h
 *
 * @date   2024.07.08
 * <AUTHOR>
**********************************************************************/
#pragma once

#include "renderdefine.hpp"
#include "resourcedefine.hpp"

#include <vector>

#include <mutex>      //lock_guard
#include <functional> //function

class OpenglRenderer2D;

using namespace rr;

class ResourceManager
{
public:
    struct Pel
    {
        // std::vector<Linecvp2f> lines;
        std::vector<Rect> rectangles;
        std::vector<Message> messages;
        std::vector<Polygoncvp2f> polygons;
    };

    static ResourceManager& GetInstance()
    {
        static ResourceManager instance;
        return instance;
    }

    void SetPel(Pel&& pel);
    void ClearPel();
    ResourceOut<Pel, std::shared_mutex> GetPel();

    void GetPolygonsAndDoSomething(std::function<void(const std::vector<Polygoncvp2f>&)> function) const;
    void SetPolygons(std::vector<Polygoncvp2f>& lines);
    void AddPolygons(const std::vector<Polygoncvp2f>& lines);
    void ClearPolygon();

    // void GetLinesAndDoSomething(std::function<void(const std::vector<Linecvp2f>&)> function) const;
    // void SetLines(std::vector<Linecvp2f>& lines);
    // void AddLines(const std::vector<Linecvp2f>& lines);
    // void ClearLine();

    void GetRectsAndDoSomething(std::function<void(const std::vector<Rect>&)> function) const;
    void SetRects(std::vector<Rect>& rects);
    void AddRect(const Rect& rect);
    void ClearRect();

    void GetMessagesAndDoSomething(std::function<void(const std::vector<Message>&)> function) const;
    void SetMessages(std::vector<Message>& messages);
    void AddMessage(const Message& message);
    void ClearMessage();

private:
    ResourceManager() {}
    ~ResourceManager() {}

    // 禁止拷贝和赋值
    ResourceManager(const ResourceManager&) = delete;
    ResourceManager& operator=(const ResourceManager&) = delete;

private:
    ResourceDo<Pel> m_pel;
    ResourceVector<Linecvp2f> m_lines;
    ResourceVector<Rect> m_rectangles;
    ResourceVector<Message> m_messages;
    ResourceVector<Polygoncvp2f> m_polygons;

    // std::vector<Linecvp2f> m_lines;
    // std::vector<Rect> m_rectangles;
    // std::vector<Message> m_messages;
    // mutable std::mutex m_mutex_lines;
    // mutable std::mutex m_mutex_rectangles;
    // mutable std::mutex m_mutex_messages;
};
