/*****************************************************************//**
 * @file   singleslider.h
 * @brief  单滑动条控件
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSSINGLESLIDER_H__
#define __JRSSINGLESLIDER_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <QSlider>
#include <QPainter>
#include <QStyleOptionSlider>
#pragma warning(pop)
class CustomSlider : public QSlider
{
    Q_OBJECT

public:
    CustomSlider(QWidget* parent = nullptr) : QSlider(parent)
    {
        setOrientation(Qt::Horizontal);
        setMinimum(0);
        setMaximum(1000); 
    }

protected:
    void paintEvent(QPaintEvent* event) override;
};
#endif