<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>controlpanelview</class>
 <widget class="jrsaoi::ViewBase" name="controlpanelview">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>428</width>
    <height>200</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>200</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>ControlPanelView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <property name="spacing">
    <number>6</number>
   </property>
   <item row="0" column="0" colspan="2">
    <widget class="QFrame" name="frame_state">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QGridLayout" name="machine_state_layout">
        <property name="spacing">
         <number>0</number>
        </property>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <widget class="QFrame" name="frame_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>50</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="operator_hlayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QPushButton" name="pushButton_information">
          <property name="minimumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(60,172,169);</string>
          </property>
          <property name="text">
           <string>信息记录</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_change_user">
          <property name="minimumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(60,172,169);</string>
          </property>
          <property name="text">
           <string>账号管理</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_online_debug">
          <property name="minimumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(60,172,169);</string>
          </property>
          <property name="text">
           <string>在线调试</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_online_rejudge">
          <property name="minimumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>65</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(60,172,169);</string>
          </property>
          <property name="text">
           <string>在线复判</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2" rowspan="2">
    <widget class="QFrame" name="frame_auto_run">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QGridLayout" name="auto_run_layout">
        <property name="spacing">
         <number>0</number>
        </property>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>jrsaoi::ViewBase</class>
   <extends>QWidget</extends>
   <header>viewbase.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
