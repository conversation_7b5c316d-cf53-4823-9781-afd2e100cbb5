project(datamanager)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
add_compile_options(/bigobj)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)


# 文件打包
set(db_head
    database/db/include/db.h
    database/db/include/dbmanager.h
    database/db/include/dbtablebase.h
    database/db/include/objectpool.hpp
    database/db/include/selectorbase.h
    database/db/include/tablebase.h
    database/db/include/tablefactory.hpp
    database/db/include/tablemanager.h
)

set(db_src
    database/db/src/db.cpp
    database/db/src/dbmanager.cpp
    database/db/src/dbtablebase.cpp
    database/db/src/selectorbase.cpp
    database/db/src/tablebase.cpp
    database/db/src/tablemanager.cpp
)
set(table_head
    database/table/include/boardtable.h
    database/table/include/detecttypetable.h
    database/table/include/detectwindowtable.h
    database/table/include/devicetable.h
    database/table/include/grouptable.h
    database/table/include/subboardtable.h
    database/table/include/usertable.h
    database/table/include/aoimachinetable.h
    database/table/include/aoimachinestatisticstable.h
    database/table/include/projecttable.h
)
set(table_src
    database/table/src/boardtable.cpp
    database/table/src/detecttypetable.cpp
    database/table/src/detectwindowtable.cpp
    database/table/src/devicetable.cpp
    database/table/src/grouptable.cpp
    database/table/src/subboardtable.cpp
    database/table/src/usertable.cpp
    database/table/src/aoimachinetable.cpp
    database/table/src/aoimachinestatisticstable.cpp
    database/table/src/projecttable.cpp
)
set(filesystem_head
    filesystem/include/filehandle.h
    filesystem/include/jrsglobaltable.h
)
set(filesystem_src
    filesystem/src/filehandle.cpp
    filesystem/src/jrsglobaltable.cpp
)

set(data_src
    src/datamanager.cpp
    src/paramconvertor.cpp
    src/statisticsdetectresult.cpp
)
set(data_head
    include/datamanager.h
    include/tdatamanager.h
    include/paramconvertor.h
    include/statisticsdetectresult.h
)

set(file_export_head

    exportfile/include/exportcsv.h
)

set(file_export_src
    
    exportfile/src/exportcsv.cpp
)
# 预编译头文件
set(source_pre_file

    prebuild/datapch.cpp
    prebuild/datapch.h

)

source_group("data/include" FILES ${data_head})
source_group("data/src" FILES ${data_src})
source_group("database/db/include" FILES ${db_head})
source_group("database/db/src" FILES ${db_src})
source_group("database/table/include" FILES ${table_head})
source_group("database/table/src" FILES ${table_src})
source_group("filesystem/include" FILES ${filesystem_head})
source_group("filesystem/src" FILES ${filesystem_src})
source_group("exportfile/include" FILES ${file_export_head})
source_group("exportfile/src" FILES ${file_export_src})
source_group("prebuild/src" FILES ${source_pre_file})
#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

add_library(${PROJECT_NAME} SHARED
    ${data_head}
    ${data_src}
    ${db_head}
    ${db_src}
    ${table_head}
    ${table_src}
    ${filesystem_head}
    ${filesystem_src}
    ${file_export_head}
    ${file_export_src}

    ${source_pre_file}
    ${JRS_VERSIONINFO_RC}

)
#共享依赖头文件
target_include_directories(${PROJECT_NAME} PUBLIC
    ${DIR_PROJECT_CURRENT}/src/datamanager/include

    ${DIR_PROJECT_CURRENT}/src/core/common/include
    ${DIR_PROJECT_CURRENT}/src/core/database/include

    ${DIR_PROJECT_CURRENT}/thirdparty/json/include
    #${DIR_PROJECT_CURRENT}thirdparty/iguana
    ${OPENCV_INCLUDE_DIR}
)

#项目私有
target_include_directories(${PROJECT_NAME} PRIVATE

    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/viewparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/projectparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/paramprocess
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/dataparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/deviceparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/image
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/resultparam
    ${DIR_PROJECT_CURRENT}/src/datamanager/database/table/include
    ${DIR_PROJECT_CURRENT}/src/datamanager/filesystem/include
    ${DIR_PROJECT_CURRENT}/src/datamanager/exportfile/include

    ${DIR_PROJECT_CURRENT}/src/datamanager/database/db/include
    ${DIR_PROJECT_CURRENT}/src/datamanager/prebuild
    ${DIR_PROJECT_CURRENT}/thirdparty/fast_csv/include
    ${DIR_PROJECT_CURRENT}/thirdparty/cereal/include

    )

target_link_directories(${PROJECT_NAME} 
    PUBLIC
    #opencv
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}> 
    #$<$<CONFIG:Release>:${OPENCV_RELEASE_DIR}> 
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>

)
#共享依赖库
target_link_libraries(${PROJECT_NAME} PUBLIC
    core
    parametermanager
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100> 

)

# 启用预编译头
target_precompile_headers(${PROJECT_NAME} PRIVATE "${CMAKE_SOURCE_DIR}/src/datamanager/prebuild/datapch.h")

# 针对 Visual Studio 项目，确保 pch.cpp 正确配置
if(MSVC)
    set_source_files_properties(datapch.cpp PROPERTIES COMPILE_FLAGS "/Ycpch.h")  # 编译 pch.cpp
    set_target_properties(${PROJECT_NAME} PROPERTIES
        COMPILE_PDB_NAME ${PROJECT_NAME}  # 可选：设置 PDB 文件名
    )
endif()
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
