﻿/*****************************************************************//**
 * @file   logshowview.h
 * @brief  日志显示界面类
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __INITSYSVIEW_H__
#define __INITSYSVIEW_H__
 //STD
//Custom
#include "viewbase.h" 

#include "ui_systemstateview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class systemstateviewClass; };
class QColor;
QT_END_NAMESPACE
namespace jrsaoi
{
    struct ImplData;
    class SystemStateView :public ViewBase
    {
        Q_OBJECT
    public:
        SystemStateView(const std::string& name, QWidget* parent = nullptr);
        ~SystemStateView();
        virtual int Init() override;
        Q_INVOKABLE  virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_)override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        bool GetIsChecked();
    signals:

        void SigInitDevice();
        void SigCheckedChanged(bool flag_);

    protected:
        void showEvent(QShowEvent* event) override;
    private slots:
        void SlotClickCell(int row_, int col_);
    private:
        void InitView();
        void InitTableView();
        void InitMember();
        void InitConnect();
        void UpdateState(const jrsdata::SystemStateViewParamPtr& param_);
        void UpdateCheckItemContent();
        void UpdateProgressBar();
        void UpdatePushButton();
        void AdjustTableSize();
        void ChangeProcessBarStyle(bool flag_);
        std::pair<std::string, QColor> GetStateName(jrsdata::MachineCheckParamInfo::CheckState state_);

        ImplData* _impl_data;
    };
}
#endif // !__LOGSHOWVIEW_H__
