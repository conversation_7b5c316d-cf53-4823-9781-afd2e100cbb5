/*****************************************************************//**
 * @file   jrsimage.h
 * @brief  图像数据结构
 * @note 目前丢弃使用，由于输入给算法的图片转换成cv::Mat直接输入 by zhangyuyu 2024.12.3
 * @details
 * <AUTHOR>
 * @date 2024.2.26
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.26         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef JRS_IMAGE_H_
#define JRS_IMAGE_H_

#pragma warning(push, 1)
#pragma warning(disable : 4003 4514 4365 4244 4800 4820 26495)
#include <iguana/iguana.hpp>
#include <opencv2/core/mat.hpp>
#pragma warning(pop)
namespace jrsoperator
{
    /*
   brief: Jrs图像数据类型
   data:  2024.10.25
   */
    using JrsImagedata = std::vector<uint8_t>;
    struct JrsImage
    {
        int row;                    // 图像的行数
        int col;                    // 图像的列数
        int type;                   // 图像的数据类型 (例如：CV_8UC3, CV_32FC1 等)
        int channel;                // 图像的通道数
        std::vector<uint8_t> data;  // 图像数据



        // 默认构造函数
        JrsImage() : row(0), col(0), type(0), channel(0) {}

        // 带参数的构造函数
        JrsImage(int r, int c, int t, int ch)
            : row(r), col(c), type(t), channel(ch), data(r* c* ch)
        {
        }

        // 拷贝构造函数
        JrsImage(const JrsImage& other)
            : row(other.row), col(other.col), type(other.type), channel(other.channel), data(other.data)
        {
        }

        // 赋值运算符
        JrsImage& operator=(const JrsImage& other)
        {
            if (this != &other)
            {
                row = other.row;
                col = other.col;
                type = other.type;
                channel = other.channel;
                data = other.data; // std::vector 会自动处理内存
            }
            return *this;
        }

        // 移动构造函数
        JrsImage(JrsImage&& other) noexcept
            : row(other.row), col(other.col), type(other.type), channel(other.channel), data(std::move(other.data))
        {
            other.row = 0;
            other.col = 0;
            other.type = 0;
            other.channel = 0;
        }

        // 移动赋值运算符
        JrsImage& operator=(JrsImage&& other) noexcept
        {
            if (this != &other)
            {
                row = other.row;
                col = other.col;
                type = other.type;
                channel = other.channel;
                data = std::move(other.data);

                other.row = 0;
                other.col = 0;
                other.type = 0;
                other.channel = 0;
            }
            return *this;
        }

        // 从 OpenCV Mat 创建 JrsImage
        static JrsImage FromMat(const cv::Mat& mat)
        {
            JrsImage img(mat.rows, mat.cols, mat.type(), mat.channels());
            std::memcpy(img.data.data(), mat.data, img.data.size());
            return img;
        }

        // 将 JrsImage 转换为 OpenCV Mat
        cv::Mat ToMat() const
        {
            void* data_void = (void*)(data.data());
            return cv::Mat(row, col, type, data_void);
        }

        // 清理图像数据
        void Clear()
        {
            data.clear();
        }

        // 检查图像是否为空
        bool Empty() const
        {
            return data.empty();
        }
    };
    YLT_REFL(
        JrsImage,
        row,
        col,
        type,
        channel,
        data
    )

}


#endif // JRS_IMAGE_H_