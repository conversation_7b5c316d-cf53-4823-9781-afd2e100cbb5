/*********************************************************************
 * @brief  图形常量.
 *
 * @file   graphicsconstants.hpp
 *
 * @date   2024.02.01
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef GRAPHICS_CONSTANTS_H
#define GRAPHICS_CONSTANTS_H

 /**
  * @brief 图形标记
  */
enum class GraphicsFlag
{
    UNDEFINE = 0,
    rect,
    circle,
    text,
    line,
    polygon,
    Bezier,
    SG,
    multiregion,
    pad,
    pad_group,
    move_tool
};

///**
// * @brief 图形移动标记
// */
//enum GraphicsMoveFlag
//{
//    LEFT = 0,             ///< 左移一个单位
//    RIGHT = 1,            ///< 右移一个单位
//    DOWN = 2,             ///< 下移一个单位
//    UP = 3,               ///< 上移一个单位
//    ROTATION = 4,         ///< 旋转一个单位
//    REVERSE_TOTATION = 5  ///< 逆旋转一个单位
//};

/**
 * @brief 图形编辑标记
 */
enum GraphicsEditFlag
{
    NONE = 0,                                                 // 无
    LEFTRIGHT = 1,                                            // 左右边同时拉伸
    TOPBOTTOM = 2,                                            // 顶底边同时拉伸
    LEFTRIGHT_RESERVE = 4,                                    // 左右边同时收缩
    TOPBOTTOM_RESERVE = 8,                                    // 顶底边同时收缩
    ALLSIDES = LEFTRIGHT | TOPBOTTOM,                         // 所有边同时拉伸
    ALLSIDES_RESERVE = LEFTRIGHT_RESERVE | TOPBOTTOM_RESERVE, // 所有边同时收缩
};

#endif // !GRAPHICS_CONSTANTS_H