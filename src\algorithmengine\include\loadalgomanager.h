/*****************************************************************
 * @file   loadalgomanager.h
 * @brief  导入算子的管理类
 * @details 根据配置文件路径，读取配置文件中算子信息，
 *          调用算子导入工厂，将配置文件中所有的算子导入到系统中使用
 * * <AUTHOR>
 * @date 2024.10.10
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.10          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/


#ifndef __JRSLOADALGOMANAGER_H__
#define __JRSLOADALGOMANAGER_H__

//STD
#include <iostream>
//Custom
#include "algofactory.h"
#include "jsonoperator.hpp"

//Third


namespace jrsalgo 
{

    class LoadAlgoManager
    {
        public:
            explicit LoadAlgoManager (const AlgoFactoryPtr& algo_factory_ptr_,const nlohmann::json& config_json_);
            ~LoadAlgoManager ();
            const std::map < std::string, std::string>& GetAlgoNameMap();
            /**
           * @fun GetAlgoSpecMap
           * @brief 获取算法的规格参数的map，主要是将软件的规格名称，和算法的规格名称对应
           * @param algo_name_ [IN] 算法名称
           * @return  返回规格参数的map
           * <AUTHOR>
           * @date 2025.5.14
           */
           const std::map<std::string/*算法名称*/, std::map<std::string/*算法中规格名称*/, std::vector<std::string>/*软件中规格名称*/>>& GetAlgoSpecMap();
        private:
            //Fun
            
            void InitMember ();

            /**
             * @fun ParseOperatorPluginInfo 
             * @brief 解析json文件中算法的view和drive的导入路径,以及算法的规格映射表
             * <AUTHOR>
             * @date 2024.10.14
             */
            void ParseOperatorPluginInfo ();


            /**
             * @fun ProcessPluginEntry 
             * @brief 处理当前算法的json数据
             * @param current_work_directory [IN] 当前的工作路径
             * @param value_json [IN] 当前算法的json数据 
             * <AUTHOR>
             * @date 2024.10.14
             */
            void ProcessPluginEntry ( const std::string& current_work_directory , const nlohmann::json& value_json );
            void ParseAlgoPaths(const std::string& current_work_directory, const nlohmann::json& value_json);
            /**
             * @fun ParseSpecparamMap 
             * @brief 生成算法的规格参数映射表
             * <AUTHOR>
             * @date 2025.5.14
             */
            void ParseSpecparamMap(const nlohmann::json& value_json);
            /**
             * @fun GeneratePath 
             * @brief 生成插件的路径
             * @param base_path [IN] 基础路径
             * @param sub_path [IN] 子路径
             * @param name [IN] 插件名称
             * @param build_type [IN] 编译类型
             * @return  返回插件的绝对路径
             * <AUTHOR>
             * @date 2024.10.14
             */
            std::string GeneratePath ( const std::string& base_path , const std::string sub_path , const std::string& name , const std::string& build_type );
            
            /**
             * @fun GetBuildType 
             * @brief 获取当前编译器编译类型
             * @return  返回当前编译类型
             * <AUTHOR>
             * @date 2024.10.14
             */
            std::string GetBuildType ()const;

            /**
             * @fun LoadAlgo 
             * @brief 正式加载算法插件
             * <AUTHOR>
             * @date 2024.10.14
             */
            void LoadAlgo ();


            //Member
            AlgoFactoryPtr algo_factory_ptr; /**< 生产算法的工厂指针*/

            std::list<std::string> view_paths;
            std::list<std::string> drive_paths;
            std::map<std::string, std::string> algo_name_lists; /**< 算法名称列表：key 英文名，value:中文名*/
            std::map<std::string/*算法名称*/, std::map<std::string/*算法中规格名称*/, std::vector<std::string>/*软件中规格名称*/>> algo_spec_param_map; /**< 算法规格名称和软件中的规格名称映射表*/
            nlohmann::json algo_config_json;

    };

    using LoadAlgoManagerPtr = std::shared_ptr<LoadAlgoManager>;
}

#endif // !__JRSLOADALGOMANAGER_H__