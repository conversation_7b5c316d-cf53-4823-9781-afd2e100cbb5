/*****************************************************************//**
 * @file   heightwheel.h
 * @brief  高度二值化控件
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSHEIGHTWHEEL_H__
#define __JRSHEIGHTWHEEL_H__
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include"histogramwidget.h"

#pragma warning(pop)
class CustomPlotWidget;
class QButtonGroup;
class QRadioButton;
class QLineEdit;
class QPushButton;
class QCheckBox;
class HeightWheel : public QWidget
{
    Q_OBJECT

public:
    HeightWheel(QWidget* parent = nullptr);
    ~HeightWheel();
private:
public slots:
    void SetHeightHistValue(std::vector<float>& gray_hist);
    void SetHeightHistParams(int bin, float min_hei,
        float max_hei);
    void UpataHeightHistParamsSlot();
	void SetHeightThreSlot(int min_thre, int max_thre);
    void UseOppositeMinHeightSlot();
public:
signals:
    // 2025.01.07 弃用 xailor
    /*void UpataHeightHistParams(int bin, float min_hei, float max_hei);
	void SetHeightThre(int min_thre, int max_thre);*/
    void UpdataHeiHistParmas(int bin, float min_hei, float max_hei, int min_thre, int max_thre,bool is_update_hei_thre = false);

private:
    int FindLeftValley(const std::vector<float>& histogram);
    QLabel* bin_name = nullptr;
    QLineEdit* bin_value = nullptr;
	QLabel* min_height_name = nullptr;
    QLineEdit* min_height_value_label = nullptr;
    QLabel* max_height_name = nullptr;
    QLineEdit* max_height_value_label = nullptr;
	CustomPlotWidget* height_histogramwidget = nullptr;	
    QCheckBox* use_opposite_min_height_ck = nullptr;
    QHBoxLayout* CreateHeightParamLayout(QWidget* parent);
    void SetupLayout(QWidget* parent);
    int bin_ = 2000;
    int m_min_bin = 0;
    int m_max_bin = 2000;
    float m_opp_min_height = 0.0f;
    float m_min_height = 0.0f;
    float m_max_height = 0.0f;
};
#endif