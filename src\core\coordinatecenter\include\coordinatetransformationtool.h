/*****************************************************************//**
 * @file   coordinatetransformationtool.h
 * @brief  AOI通用坐标变换工具
 * @details
 * <AUTHOR>
 * @date  2024.08.20
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.20         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __COORDINATETRANSFORMATIONTOOL_H__
#define __COORDINATETRANSFORMATIONTOOL_H__
#include <opencv2/opencv.hpp>
namespace jrstool
{
    using std::vector;
    using cv::Mat;

    // 多点匹配模式
    enum AffineMatrixType
    {
        DEFAULT, /*多点映射*/
        GOODMARKSCORE, /*优选高分Mark*/
        GOODMARKPOSITION, /*优选误差最小Mark*/
        REGISTRATION /*多点配准*/
    };

    // Mark点数据结构
    template <typename T>
    struct PointLabel
    {
        cv::Point_<T> pt_; /*Mark点*/
        float score_; /*Mark点分数*/

        PointLabel ()
            :score_{}
        {};
        PointLabel(cv::Point_<T> pt, float score) : pt_(pt), score_(score) {}
        PointLabel& operator=(const PointLabel& other)
        {
            if (this != &other)
            {
                this->score_ = other.score_;
                this->pt_ = other.pt_;
            }
            return *this;
        }

        T GetDistance(const PointLabel& other)
        {
            return T(cv::norm(other.pt_ - this->pt_));
        }
    };
    template <typename T>
    struct AlignDatas
    {
        vector<PointLabel<T>> src_mark_pts; /*原始Mark点位置*/
        vector<PointLabel<T>> dst_mark_pts; /*当前Mark点位置*/
        vector<cv::Point_<T>> before_correct_pts; /*更新前CAD位置*/
        int mode; /*多点匹配模式*/
        vector<cv::Point_<T>> after_correct_pts; /*更新后CAD位置*/
        Mat affine_matrix; /*仿射变换矩阵*/
    };
    template <typename T>
    class CoordinatrTransform
    {
    public:
        /**
        * @fun    GetAffineMatrix
        * @brief  获取仿射变换矩阵
        * @param  src_mark_pts 原始Mark点位置
        * @param  dst_mark_pts 当前Mark点位置
        * @param  affine_matrix 仿射变换矩阵
        * @param  mode 多Mark处理方式
        * @date   2024.08.20
        * <AUTHOR>
        */
        static int GetAffineMatrix(
            const vector<PointLabel<T>>& src_mark_pts,
            const vector<PointLabel<T>>& dst_mark_pts,
            Mat& affine_matrix, int mode = jrstool::DEFAULT);
        /**
        * @fun    WarpAffine
        * @brief  应用仿射变换矩阵
        * @param  affine_matrix 仿射变换矩阵
        * @param  before_correct_pts 处理前CAD位置
        * @param  after_correct_pts 处理后CAD位置
        * @param  mode 多Mark处理方式
        * @date   2024.08.20
        * <AUTHOR>
        */
        static int WarpAffine(const cv::Mat& affine_matrix,
            const vector<cv::Point_<T>>& before_correct_pts,
            vector<cv::Point_<T>>& after_correct_pts);
        /**
        * @fun    PositionAlign
        * @brief  CAD坐标更新
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static int PositionAlign(AlignDatas<T>& align_params);

        /**
         * @fun    GetDistance
         * @brief  获取两个点之间的欧几里得距离
         * @param  p1 [IN]点1
         * @param  p2 [IN]点2
         * @date   2025.5.9
         * @return 返回欧几里得距离
         * <AUTHOR>
         */
        inline static T GetBetweenDistance(const jrstool::PointLabel<T>& p1, const jrstool::PointLabel<T>& p2)
        {
            T dx = p1.pt_.x - p2.pt_.x;
            T dy = p1.pt_.y - p2.pt_.y;
            return std::sqrt(dx * dx + dy * dy);
        }
        CoordinatrTransform();
        ~CoordinatrTransform();

    private:
        /*Mat affine_matrix_;
        vector<PointLabel<T>> src_mark_pts_;
        vector<PointLabel<T>> dst_mark_pts_;
        int mode_;*/
        /**
        * @fun    GetMatrixByOneGroupPts
        * @brief  获取单点Mark仿射变换矩阵
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void GetMatrixByOneGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_);
        /**
        * @fun    GetMatrixByTwoGroupPts
        * @brief  获取两点Mark仿射变换矩阵
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void GetMatrixByTwoGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_);
        /**
        * @fun    GetMatrixByTwoGroupPts
        * @brief  获取三点Mark仿射变换矩阵
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void GetMatrixByThreeGroupPts(Mat& affine_matrix_, const  vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_);
        /**
        * @fun    GetMatrixByTwoGroupPts
        * @brief  获取多点Mark仿射变换矩阵
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void GetMatrixByMuchGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_, int mode);
        /**
        * @fun    AffineTransformToPt
        * @brief  对单点应用仿射变换矩阵
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static cv::Point_<T> AffineTransformToPt(const cv::Mat& affine_matrix,
            const cv::Point_<T>& point);
        /**
        * @fun    SortAndRearrangeByScore
        * @brief  对Mark点组按照分数排序
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void SortAndRearrangeByScore(
            std::vector<PointLabel<T>>& src_mark_pts_,
            std::vector<PointLabel<T>>& dst_mark_pts_);
        /**
        * @fun    SortAndRearrangeByDistance
        * @brief  对Mark点组按照误差距离排序
        * @param  AlignDatas 用于更新的参数
        * @date   2024.08.20
        * <AUTHOR>
        */
        static void SortAndRearrangeByDistance(
            std::vector<PointLabel<T>>& src_mark_pts_,
            std::vector<PointLabel<T>>& dst_mark_pts_);
    };

    template<typename T>
    int CoordinatrTransform<T>::GetAffineMatrix(
        const vector<PointLabel<T>>& src_mark_pts,
        const vector<PointLabel<T>>& dst_mark_pts,
        Mat& affine_matrix, int mode)
    {
        if (src_mark_pts.size() != dst_mark_pts.size()) return -1;
        //src_mark_pts_ = src_mark_pts;
        //dst_mark_pts_ = dst_mark_pts;
        //mode_ = mode;
        if (src_mark_pts.size() == 1)
        {
            GetMatrixByOneGroupPts(affine_matrix, src_mark_pts, dst_mark_pts);
        }
        else if (src_mark_pts.size() == 2)
        {
            GetMatrixByTwoGroupPts(affine_matrix, src_mark_pts, dst_mark_pts);
        }
        else if (src_mark_pts.size() == 3)
        {
            GetMatrixByThreeGroupPts(affine_matrix, src_mark_pts, dst_mark_pts);
        }
        else if (src_mark_pts.size() > 3)
        {
            GetMatrixByMuchGroupPts(affine_matrix, src_mark_pts, dst_mark_pts, mode);
        }
        else
        {
            return -1;
        }
        return 0;
    }

    template<typename T>
    inline int CoordinatrTransform<T>::WarpAffine(
        const cv::Mat& affine_matrix,
        const vector<cv::Point_<T>>& before_correct_pts,
        vector<cv::Point_<T>>& after_correct_pts)
    {
        std::vector<int> a;
        if (affine_matrix.empty()) return -1;
        for (auto i = 0; i < before_correct_pts.size(); i++)
        {
            after_correct_pts.push_back(AffineTransformToPt(affine_matrix,
                before_correct_pts[i]));
        }
        return 0;
    }

    template<typename T>
    inline int CoordinatrTransform<T>::PositionAlign(
        AlignDatas<T>& align_params)
    {
        auto step_0 = GetAffineMatrix(
            align_params.src_mark_pts,
            align_params.dst_mark_pts,
            align_params.affine_matrix, align_params.mode);
        if (step_0 != 0)return -1;
        auto step_1 = WarpAffine(align_params.affine_matrix,
            align_params.before_correct_pts,
            align_params.after_correct_pts);
        return 0;
    }

    template<typename T>
    inline CoordinatrTransform<T>::CoordinatrTransform()
    {
    }

    template<typename T>
    inline CoordinatrTransform<T>::~CoordinatrTransform()
    {
    }

    template<typename T>
    inline void CoordinatrTransform<T>::GetMatrixByOneGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_)
    {
        affine_matrix_ = cv::Mat(2, 3, CV_32F);
        auto dx = dst_mark_pts_[0].pt_.x - src_mark_pts_[0].pt_.x;
        auto dy = dst_mark_pts_[0].pt_.y - src_mark_pts_[0].pt_.y;

        affine_matrix_.at<float>(0, 0) = 1.0;
        affine_matrix_.at<float>(0, 1) = 0.0;
        affine_matrix_.at<float>(1, 0) = 0.0;
        affine_matrix_.at<float>(1, 1) = 1.0;
        affine_matrix_.at<float>(0, 2) = float(dx);
        affine_matrix_.at<float>(1, 2) = float(dy);
    }

    template<typename T>
    void CoordinatrTransform<T>::GetMatrixByTwoGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_)
    {
        affine_matrix_ = cv::Mat(2, 3, CV_32F);
        auto dx1 = src_mark_pts_[1].pt_.x - src_mark_pts_[0].pt_.x;
        auto dy1 = src_mark_pts_[1].pt_.y - src_mark_pts_[0].pt_.y;
        auto dx2 = dst_mark_pts_[1].pt_.x - dst_mark_pts_[0].pt_.x;
        auto dy2 = dst_mark_pts_[1].pt_.y - dst_mark_pts_[0].pt_.y;

        auto scale = std::sqrt(dx2 * dx2 + dy2 * dy2) / std::sqrt(dx1 * dx1 + dy1 * dy1);
        auto angle = std::atan2(dy2, dx2) - std::atan2(dy1, dx1);

        affine_matrix_.at<float>(0, 0) = float(scale * std::cos(angle));
        affine_matrix_.at<float>(0, 1) = float(-scale * std::sin(angle));
        affine_matrix_.at<float>(1, 0) = float(scale * std::sin(angle));
        affine_matrix_.at<float>(1, 1) = float(scale * std::cos(angle));
        affine_matrix_.at<float>(0, 2) = float(dst_mark_pts_[0].pt_.x - src_mark_pts_[0].pt_.x * affine_matrix_.at<float>(0, 0) - src_mark_pts_[0].pt_.y * affine_matrix_.at<float>(0, 1));
        affine_matrix_.at<float>(1, 2) = float(dst_mark_pts_[0].pt_.y - src_mark_pts_[0].pt_.x * affine_matrix_.at<float>(1, 0) - src_mark_pts_[0].pt_.y * affine_matrix_.at<float>(1, 1));
    }

    template<typename T>
    void CoordinatrTransform<T>::GetMatrixByThreeGroupPts(Mat& affine_matrix_, const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>& dst_mark_pts_)
    {
        std::vector<cv::Point2f> src_points(3);
        std::vector<cv::Point2f> dst_points(3);
        for (auto i = 0; i < 3; i++)
        {
            src_points[i] = src_mark_pts_[i].pt_;
            dst_points[i] = dst_mark_pts_[i].pt_;
        }
        affine_matrix_ = cv::getAffineTransform(src_points, dst_points);
    }

    template<typename T>
    void CoordinatrTransform<T>::GetMatrixByMuchGroupPts(Mat& affine_matrix_,
        const vector<PointLabel<T>>& src_mark_pts_, const vector<PointLabel<T>>&
        dst_mark_pts_, int mode)
    {
        if (mode == jrstool::DEFAULT)
        {
            std::vector<cv::Point_<T>> src_points(int(src_mark_pts_.size()));
            std::vector<cv::Point_<T>> dst_points(int(src_mark_pts_.size()));
            for (auto i = 0; i < src_mark_pts_.size(); i++)
            {
                src_points[i] = src_mark_pts_[i].pt_;
                dst_points[i] = dst_mark_pts_[i].pt_;
            }
            affine_matrix_ = cv::estimateAffinePartial2D(src_points, dst_points);
        }
        else if (mode == jrstool::GOODMARKSCORE)
        {
            auto src_mark_pts_copy = src_mark_pts_;
            auto dst_mark_pts_copy = dst_mark_pts_;
            SortAndRearrangeByScore(src_mark_pts_copy, dst_mark_pts_copy);
            std::vector<cv::Point2f> src_points(3);
            std::vector<cv::Point2f> dst_points(3);
            for (auto i = 0; i < 3; i++)
            {
                src_points[i] = src_mark_pts_copy[i].pt_;
                dst_points[i] = dst_mark_pts_copy[i].pt_;
            }

            affine_matrix_ = cv::getAffineTransform(src_points, dst_points);
        }
        else if (mode == jrstool::GOODMARKPOSITION)
        {
            auto src_mark_pts_copy = src_mark_pts_;
            auto dst_mark_pts_copy = dst_mark_pts_;
            SortAndRearrangeByDistance(src_mark_pts_copy, dst_mark_pts_copy);
            std::vector<cv::Point2f> src_points(3);
            std::vector<cv::Point2f> dst_points(3);
            for (auto i = 0; i < 3; i++)
            {
                src_points[i] = src_mark_pts_copy[i].pt_;
                dst_points[i] = dst_mark_pts_copy[i].pt_;
            }
            affine_matrix_ = cv::getAffineTransform(
                src_points,
                dst_points
            );
        }
        else if (mode == jrstool::REGISTRATION)
        {
            std::vector<cv::Point2f> src_points(int(src_mark_pts_.size()));
            std::vector<cv::Point2f> dst_points(int(src_mark_pts_.size()));
            for (auto i = 0; i < src_mark_pts_.size(); i++)
            {
                src_points[i] = src_mark_pts_[i].pt_;
                dst_points[i] = dst_mark_pts_[i].pt_;
            }

            double reproj_threshold = 5.0;

            cv::Mat homography = cv::findHomography(src_points,
                dst_points, cv::USAC_ACCURATE, reproj_threshold);
            cv::Mat homography_32f;

            homography.convertTo(homography_32f, CV_32F);
            affine_matrix_ = cv::Mat(2, 3, CV_32F);
            affine_matrix_.at<float>(0, 0) = homography_32f.at<float>(0, 0);
            affine_matrix_.at<float>(0, 1) = homography_32f.at<float>(0, 1);
            affine_matrix_.at<float>(1, 0) = homography_32f.at<float>(1, 0);
            affine_matrix_.at<float>(1, 1) = homography_32f.at<float>(1, 1);
            affine_matrix_.at<float>(0, 2) = homography_32f.at<float>(0, 2);
            affine_matrix_.at<float>(1, 2) = homography_32f.at<float>(1, 2);
        }
        else
        {
            return;
        }
    }
    template<typename T>
    cv::Point_<T> CoordinatrTransform<T>::AffineTransformToPt(
        const cv::Mat& affine_matrix,
        const cv::Point_<T>& point)
    {
        cv::Mat affine_matrix_32f;
        affine_matrix.convertTo(affine_matrix_32f, CV_32F);
        cv::Mat point_mat = (cv::Mat_<float>(3, 1) << point.x, point.y, 1.0);
        cv::Mat transformed_point_mat = affine_matrix_32f * point_mat;
        T x = T(transformed_point_mat.at<float>(0, 0));
        T y = T(transformed_point_mat.at<float>(1, 0));

        return cv::Point_<T>(x, y);
    }
    template<typename T>
    inline void CoordinatrTransform<T>::SortAndRearrangeByScore(
        std::vector<PointLabel<T>>& src_mark_pts_,
        std::vector<PointLabel<T>>& dst_mark_pts_)
    {
        std::vector<int> indices(dst_mark_pts_.size());
        for (int i = 0; i < indices.size(); ++i)
        {
            indices[i] = i;
        }

        std::sort(indices.begin(), indices.end(),
            [&dst_mark_pts_](int a, int b)
            {
                return dst_mark_pts_[a].score_ > dst_mark_pts_[b].score_;
            });

        std::vector<PointLabel<T>> sorted_dst_mark_pts_(dst_mark_pts_.size());
        std::vector<PointLabel<T>> sorted_src_mark_pts_(src_mark_pts_.size());

        for (int i = 0; i < indices.size(); ++i)
        {
            sorted_dst_mark_pts_[i] = dst_mark_pts_[indices[i]];
            sorted_src_mark_pts_[i] = src_mark_pts_[indices[i]];
        }

        dst_mark_pts_ = std::move(sorted_dst_mark_pts_);
        src_mark_pts_ = std::move(sorted_src_mark_pts_);
    }
    template<typename T>
    inline void CoordinatrTransform<T>::SortAndRearrangeByDistance(std::vector<PointLabel<T>>& src_mark_pts_, std::vector<PointLabel<T>>& dst_mark_pts_)
    {
        std::vector<int> indices(src_mark_pts_.size());
        for (int i = 0; i < indices.size(); ++i)
        {
            indices[i] = i;
        }

        std::sort(indices.begin(), indices.end(),
            [&src_mark_pts_, &dst_mark_pts_](int a, int b) {
                float dist_a = float(src_mark_pts_[a].GetDistance(dst_mark_pts_[a]));
                float dist_b = float(src_mark_pts_[b].GetDistance(dst_mark_pts_[b]));
                return dist_a < dist_b;
            });

        std::vector<PointLabel<T>> sorted_src_mark_pts_(src_mark_pts_.size());
        std::vector<PointLabel<T>> sorted_dst_mark_pts_(dst_mark_pts_.size());

        for (int i = 0; i < indices.size(); ++i)
        {
            sorted_src_mark_pts_[i] = src_mark_pts_[indices[i]];
            sorted_dst_mark_pts_[i] = dst_mark_pts_[indices[i]];
        }

        src_mark_pts_ = std::move(sorted_src_mark_pts_);
        dst_mark_pts_ = std::move(sorted_dst_mark_pts_);
    }
}

#endif