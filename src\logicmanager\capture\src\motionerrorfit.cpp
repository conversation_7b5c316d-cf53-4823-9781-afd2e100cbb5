#include <iostream>

#include "motionerrorfit.h"
#include "motionerrorfunc.h"


MotionErrorFit::MotionErrorFit()
{
}

MotionErrorFit::~MotionErrorFit()
{
}

bool MotionErrorFit::Run()
{
  // 创建运动误差模型函数
  BezierCurve_functor functor(outputPts.data(), inputPts.data(), outputPts.size(), param.x_n, param.y_n);
  // 给模型添加自动求导方法
  NumericalDiff<BezierCurve_functor> numerical_diff(functor);
  // 创建LM算法非线性优化器
  LevenbergMarquardt<NumericalDiff<BezierCurve_functor>> lm(numerical_diff);
  lm.parameters.maxfev = 1000;
  // 生成初始化参数
  vector<double> b;
  GenInitParam(b);
  VectorXd vb(b.size());
  std::memcpy(vb.data(), b.data(), b.size() * sizeof(double));

  // 执行优化算法
  auto info = lm.minimize(vb);

  // 将拟合结果存放在模型参数中
  assert(b.size() == (param.x_n + param.y_n) * 2);
  Eigen::VectorXd bx = vb.segment(0, param.x_n * 2);
  Eigen::VectorXd by = vb.segment(param.x_n * 2, param.y_n * 2);
  param.x_ctrl_pts.clear();
  for (size_t i = 0; i < param.x_n; i++)
  {
    param.x_ctrl_pts.push_back(JrsPoint(bx[i * 2], bx[i * 2 + 1]));
  }
  param.y_ctrl_pts.clear();
  for (size_t i = 0; i < param.y_n; i++)
  {
    param.y_ctrl_pts.push_back(JrsPoint(by[i * 2], by[i * 2 + 1]));
  }

  return true;
}


MotionErrorParam MotionErrorFit::GetFitResult()
{
  return param;
}

void MotionErrorFit::GenInitParam(vector<double>& b)
{
  // 在实际的应用中运动误差较小，只是较小的波动，因此这里将直线的等间距点当作模型的初始运动控制点；
  // 这里的参数生成函数可根据实际应用场景做调整
  double xStep = (param.x_end - param.x_start) / (param.x_n - 1);
  double yStep = (param.y_end - param.y_start) / (param.y_n - 1);
  b.resize((param.x_n + param.y_n) * 2);

  for (size_t i = 0; i < param.x_n; i++)
  {
    b[i * 2] = param.x_start + i * xStep;
    std::cout << b[i * 2] << std::endl;
    b[i * 2 + 1] = 0;
    std::cout << b[i * 2 + 1] << std::endl;
  }
  for (size_t i = 0; i < param.y_n; i++)
  {
    b[(i + param.x_n) * 2] = 0.0;
    std::cout << b[(i + param.x_n) * 2] << std::endl;
    b[(i + param.x_n) * 2 + 1] = param.y_start + i * yStep;
    std::cout << b[(i + param.x_n) * 2 + 1] << std::endl;
  }
}

bool MotionErrorFit::SetFitData(const double& _x_start, const double& _x_end, const double& _y_start, const double& _y_end, const vector<JrsPoint>& _theoryPts, const vector<JrsPoint>& _realPts)
{
  // 根据运动区域的范围确定Bezier曲线的阶数
  param.x_n = (int)(_x_end - _x_start + 1) / 50;
  param.y_n = (int)(_y_end - _y_start + 1) / 50;

  param.x_start = _x_start;
  param.x_end = _x_end;
  param.y_start = _y_start;
  param.y_end = _y_end;

  assert(_theoryPts.size() == _realPts.size());

  outputPts = _realPts;

  // 将理论运动的绝对位置转换为相对于运动起止点的相对运动量
  inputPts.clear();
  for (size_t i = 0; i < _theoryPts.size(); i++)
  {
    double tx = (_theoryPts[i].x - param.x_start) / (param.x_end - param.x_start);
    double ty = (_theoryPts[i].y - param.y_start) / (param.y_end - param.y_start);
    inputPts.push_back(JrsPoint(tx, ty));
  }

  return true;
}