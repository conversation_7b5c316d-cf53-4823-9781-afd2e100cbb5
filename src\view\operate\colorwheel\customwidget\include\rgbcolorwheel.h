/*****************************************************************//**
 * @file   rbgcolorwheel.h
 * @brief  rgb竖向柱状图ui
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSRGBCOLORWHEEL_H__
#define __JRSRGBCOLORWHEEL_H__
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include"histogramwidget.h"

#pragma warning(pop)
class CustomPlotWidget;
class RgbColorWheel : public QWidget
{
    Q_OBJECT

public:
    RgbColorWheel(QWidget* parent = nullptr);
    ~RgbColorWheel();
public:
signals:
	void SetRThre(int min_thre, int max_thre);
	void SetGThre(int min_thre, int max_thre);
	void SetBThre(int min_thre, int max_thre);
public slots:
    void SetRgbHistValue(std::vector<float>& r_hist,
        std::vector<float>& g_hist,
        std::vector<float>& b_hist);

    void SetRThreSlot(int min_thre, int max_thre);
	void SetGThreSlot(int min_thre, int max_thre);
	void SetBThreSlot(int min_thre, int max_thre);

private:
    CustomPlotWidget* r_histogramwidget = nullptr;
    CustomPlotWidget* g_histogramwidget = nullptr;
    CustomPlotWidget* b_histogramwidget = nullptr;
};
#endif