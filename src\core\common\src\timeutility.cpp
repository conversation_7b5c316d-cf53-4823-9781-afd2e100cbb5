﻿#include "timeutility.h"

std::time_t jtools::TimeUtility::GetCurrentTimeStamp()
{
    auto now = ClockType::now();
    return TimePointToTimeStamp(now);
}

std::chrono::high_resolution_clock::time_point jtools::TimeUtility::GetCurrentTimePoint()
{
    return std::chrono::high_resolution_clock::now();
}

std::string jtools::TimeUtility::GetCurrentTimeString(const char *format)
{
    auto now = ClockType::now();
    return TimePointToString(now, format);
}

std::string jtools::TimeUtility::GetCurrentTimeStringWithUS(const char *format)
{
    auto now = ClockType::now();
    std::time_t time = TimePointToTimeStamp(now);

    // 提取当前时间的微秒部分
    auto microseconds = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()) % 1000000;

    std::tm *localTime = std::localtime(&time);

    char buffer[30];
    std::strftime(buffer, sizeof(buffer), format, localTime);

    // 使用snprintf将微秒部分追加到时间字符串后
    char result[40];
    std::snprintf(result, sizeof(result), "%s.%06lld", buffer, microseconds.count());

    return std::string(result);
}

std::time_t jtools::TimeUtility::TimePointToTimeStamp(ClockType::time_point timePoint)
{
    // auto duration = timePoint.time_since_epoch();
    // std::chrono::seconds sec = std::chrono::duration_cast<std::chrono::seconds>(duration);
    // return sec.count();
    return ClockType::to_time_t(timePoint); // 仅限system_lock
}

jtools::TimeUtility::ClockType::time_point jtools::TimeUtility::TimeStampToTimePoint(std::time_t timeStamp)
{
    return ClockType::from_time_t(timeStamp);
}

std::string jtools::TimeUtility::TimeStampToString(std::time_t timeStamp, const char *format)
{
    std::tm *localTime = std::localtime(&timeStamp);

    // 使用缓冲区和strftime格式化时间字符串
    char buffer[30];
    std::strftime(buffer, sizeof(buffer), format, localTime);
    return std::string(buffer);
}

std::string jtools::TimeUtility::TimePointToString(ClockType::time_point timePoint, const char *format)
{
    std::time_t time = TimePointToTimeStamp(timePoint);
    return TimeStampToString(time, format);
}

double jtools::TimeUtility::GetTimeDiff(const std::chrono::high_resolution_clock::time_point &start,
                                        const std::chrono::high_resolution_clock::time_point &end,
                                        const std::string &unit)
{
    auto duration = end - start;

    if (unit == "seconds")
    {
        return std::chrono::duration<double>(duration).count();
    }
    else if (unit == "milliseconds")
    {
        return std::chrono::duration<double, std::milli>(duration).count();
    }
    else if (unit == "microseconds")
    {
        return std::chrono::duration<double, std::micro>(duration).count();
    }
    else if (unit == "nanoseconds")
    {
        return std::chrono::duration<double, std::nano>(duration).count();
    }
    else if (unit == "minutes")
    {
        return std::chrono::duration<double, std::ratio<60>>(duration).count();
    }
    else if (unit == "hours")
    {
        return std::chrono::duration<double, std::ratio<3600>>(duration).count();
    }
    return 0;
}
