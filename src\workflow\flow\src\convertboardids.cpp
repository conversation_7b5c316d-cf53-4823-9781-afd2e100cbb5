﻿//CUSTOM
#include "ConvertBoardIDs.h"
//STL
#include <iostream>
#include <algorithm>

ConvertBoardIDs::ConvertBoardIDs()
{
}

ConvertBoardIDs::~ConvertBoardIDs()
{
}

bool ConvertBoardIDs::ConvertBoardIdMapping(int rows, int cols)
{
    if (rows < 1 || cols < 1)
    {
        return false;
    }
    MakeBoardIdMappingHorizontalFlip(m_board_id_mapping_horizontal_flip, rows, cols);
    MakeBoardIdMappingVerticalFlip(m_board_id_mapping_vertical_flip, rows, cols);
    for (auto& mapping_id : m_board_id_mapping_horizontal_flip)
    {
        std::cout << __FUNCTION__ << " m_board_id_mapping_horizontal_flip first " << mapping_id.first << " second " << mapping_id.second << std::endl;
    }
    for (auto& mapping_id : m_board_id_mapping_vertical_flip)
    {
        std::cout << __FUNCTION__ << " m_board_id_mapping_vertical_flip first " << mapping_id.first << " second " << mapping_id.second << std::endl;
    }
    return true;
}


// 比较函数
bool CompareSubboardBarcode(const BoardIDCoors& a, const BoardIDCoors& b)
{
    /**
     * 比较两个 BoardIDCoors 对象的坐标，用于排序。
     * 比较规则：
     * 1. 优先比较 Y 坐标，Y 坐标大的排在前面（从大到小）。
     * 2. 当 Y 坐标相同时，比较 X 坐标，X 坐标小的排在前面（从小到大）。
     * 这样的排序规则可以先按照 Y 坐标进行分层，再在每一层内按照 X 坐标进行排序。
     */
    if (a.y == b.y)
        return a.x < b.x;
    else
        return a.y > b.y;
}

bool ConvertBoardIDs::GetMappingBoardIds(const std::vector<BoardIDCoors>& vec_board_id_coors_a, const std::vector<BoardIDCoors>& vec_board_id_coors_b, 
    const ConvertDirection& convert_direction, std::map<int, int>& map_subboard_id_mapping_flip)
{
    /**
     * 该函数用于实现同一个板的A/B面的两个工程中的子板号的映射关系。
     * 主要逻辑如下：
     * 1. 首先对输入的两个工程中（vec_board_id_coors_a 和 vec_board_id_coors_b）的 子板ID 根据坐标信息按照 Y 坐标从小到大、并X 坐标从小到大的顺序进行排序，以便后续处理。
     * 2. 创建两个映射关系：
     *    - m_mapping_in_real_ids_a：A 板的内部 ID 到真实 ID 的映射关系。
     *    - m_mapping_real_in_ids_b：B 板的真实 ID 到内部 ID 的映射关系。
     * 3. B板根据XY排序后，得到子板号列表（vec_board_ids_b_real），遍历 B 板的真实 ID 列表（vec_board_ids_b_real），对于每个 B 板的真实 ID：
     *    - 通过 m_mapping_real_in_ids_b 将其转换为 B 板的内部 ID。
     *    - 利用预先定义好的映射关系（m_board_id_mapping_horizontal_flip 和 m_board_id_mapping_vertical_flip，分别表示 B 面内部 ID 到 A 面内部 ID 的横向翻转映射和竖向翻转映射），
     *      将 B 板的内部 ID 转换为 A 板的内部 ID（a_in_board_id_x 和 a_in_board_id_y）。
     *    - 再通过 m_mapping_in_real_ids_a 将 A 板的内部 ID 转换为 A 板的真实 ID（a_real_board_id_x 和 a_real_board_id_y）。
     *    - 最终，将 B 板的真实 ID 分别与 A 板的真实 ID 建立映射关系（根据输入的方向convert_direction，选择横向翻转后的映射和竖向翻转后的映射），存储到 map_subboard_id_mapping_flip中。
     * 4. 在整个过程中，如果出现任何映射关系不存在的情况（如 m_mapping_real_in_ids_b、m_board_id_mapping_horizontal_flip、m_board_id_mapping_vertical_flip 或 m_mapping_in_real_ids_a 中缺少对应的映射项），则函数返回 false，表示无法成功建立映射关系；否则，最终返回 true，表示映射关系建立成功。
     *
     * 返回值：
     * - true：成功建立映射关系。
     * - false：无法建立映射关系，可能是因为某些映射关系缺失。
     */
    std::vector<BoardIDCoors> board_id_coors_a = vec_board_id_coors_a;
    std::vector<BoardIDCoors> board_id_coors_b = vec_board_id_coors_b;
    if (board_id_coors_a.size() < 1 || board_id_coors_b.size() < 1)
    {
        return false;
    }
    std::sort(board_id_coors_a.begin(), board_id_coors_a.end(), CompareSubboardBarcode);
    std::sort(board_id_coors_b.begin(), board_id_coors_b.end(), CompareSubboardBarcode);
    std::map<int/*内部ID*/, int/*真实ID*/> m_mapping_in_real_ids_a;                                                         //A板内部和真实ID映射
    std::map<int/*真实ID*/, int/*内部ID*/> m_mapping_real_in_ids_b;                                                         //B板真实和内部ID映射
    for (int i = 0; i < board_id_coors_a.size(); i++)
    {
        m_mapping_in_real_ids_a.insert(std::make_pair(i + 1, board_id_coors_a.at(i).id));                               //内部ID和真实ID的转换关系A
    }
    std::vector<int> vec_board_ids_b_real;
    for (int i = 0; i < board_id_coors_b.size(); i++)
    {
        m_mapping_real_in_ids_b.insert(std::make_pair(board_id_coors_b.at(i).id, i + 1));                               //真实ID和内部ID的转换关系B
        vec_board_ids_b_real.push_back(board_id_coors_b.at(i).id);                                                      //B面真实的ID
    }
    for (auto& b_real_board_id : vec_board_ids_b_real)
    {
        if (m_mapping_real_in_ids_b.count(b_real_board_id) > 0)
        {
            int b_in_board_id = m_mapping_real_in_ids_b[b_real_board_id];                                                   //从B的真实ID转换成B的内部ID
            if (m_board_id_mapping_horizontal_flip.count(b_in_board_id) > 0 && m_board_id_mapping_vertical_flip.count(b_in_board_id) > 0)
            {
                int a_in_board_id_horizontal_flip = m_board_id_mapping_horizontal_flip[b_in_board_id];                                    //从B面的内部ID转成A面的内部ID 横向翻转
                int a_in_board_id_vertical_flip = m_board_id_mapping_vertical_flip[b_in_board_id];                                      //从B面的内部ID转成A面的内部ID 竖向翻转
                if (m_mapping_in_real_ids_a.count(a_in_board_id_horizontal_flip) > 0 && m_mapping_in_real_ids_a.count(a_in_board_id_vertical_flip) > 0)
                {
                    int a_real_board_id_horizontal_flip = m_mapping_in_real_ids_a[a_in_board_id_horizontal_flip];                                       //从A面的内部ID转成A面的真实ID 横向翻转
                    int a_real_board_id_vertical_flip = m_mapping_in_real_ids_a[a_in_board_id_vertical_flip];                                       //从A面的内部ID转成A面的真实ID 竖向翻转
                    if (convert_direction == ConvertDirection::Y_AXIS_FLIP)
                    {
                        map_subboard_id_mapping_flip.insert(std::make_pair(b_real_board_id, a_real_board_id_vertical_flip));              //B面真实ID到A面真实ID的映射 竖向翻转
                    }
                    else
                    {
                        map_subboard_id_mapping_flip.insert(std::make_pair(b_real_board_id, a_real_board_id_horizontal_flip));              //B面真实ID到A面真实ID的映射 横向翻转
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }
    return true;
}

bool ConvertBoardIDs::MakeBoardIdMappingHorizontalFlip(std::map<int, int>& convert_board_ids_map, int board_rows, int board_columns)
{
    /**
     * 该函数用于生成一个从 B 板到 A 板的 ID 转换映射表（convert_board_ids_map），适用于横向翻转的场景。
     * 主要逻辑如下：
     * 1. 首先清空传入的映射表（convert_board_ids_map）。
     * 2. 检查输入的板的行数（board_rows）和列数（board_columns）是否有效（均大于 0），如果无效则直接返回 false。
     * 3. 遍历整个板的行和列，对于每个单元格（由行索引 i 和列索引 j 确定）：
     *    - 计算 B 板的 ID：((i + 1) * board_columns - j)。这里假设 B 板的 ID 是从右上角开始，按列递减的方式编号。
     *    - 计算 A 板的 ID：(i * board_columns + j + 1)。这里假设 A 板的 ID 是从左上角开始，按行递增的方式编号。
     *    - 将 B 板的 ID 映射到 A 板的 ID，并存储到映射表（convert_board_ids_map）中。
     * 4. 在整个过程中，如果输入的行数和列数有效，则最终返回 true，表示成功生成了映射表；否则，返回 false。
     *
     * 参数：
     * - convert_board_ids_map：用于存储 B 板到 A 板的 ID 转换映射关系的映射表。
     * - board_rows：板的行数。
     * - board_columns：板的列数。
     *
     * 返回值：
     * - true：成功生成了映射表。
     * - false：输入的行数或列数无效，无法生成映射表。
     */
    convert_board_ids_map.clear();
    if (board_rows < 1 || board_columns < 1)
    {
        return false;
    }
    for (int i = 0; i < board_rows; i++)
    {
        for (int j = 0; j < board_columns; j++)
        {
            convert_board_ids_map.insert(std::make_pair(((i + 1) * board_columns - j), (i * board_columns + j + 1)));
        }
    }
    return true;
}
bool ConvertBoardIDs::MakeBoardIdMappingVerticalFlip(std::map<int, int>& convert_board_ids_map, int board_rows, int board_columns)
{
    /**
     * 该函数用于生成一个从 B 板到 A 板的 ID 转换映射表（convert_board_ids_map），适用于竖向翻转的场景。
     * 主要逻辑如下：
     * 1. 首先清空传入的映射表（convert_board_ids_map）。
     * 2. 检查输入的板的行数（board_rows）和列数（board_columns）是否有效（均大于 0），如果无效则直接返回 false。
     * 3. 遍历整个板的行和列，对于每个单元格（由行索引 i 和列索引 j 确定）：
     *    - 计算 B 板的 ID：((board_rows - i - 1) * board_columns + j + 1)。这里假设 B 板的 ID 是从底部开始，按行递增的方式编号。
     *    - 计算 A 板的 ID：(i * board_columns + j + 1)。这里假设 A 板的 ID 是从顶部开始，按行递增的方式编号。
     *    - 将 B 板的 ID 映射到 A 板的 ID，并存储到映射表（convert_board_ids_map）中。
     * 4. 在整个过程中，如果输入的行数和列数有效，则最终返回 true，表示成功生成了映射表；否则，返回 false。
     *
     * 参数：
     * - convert_board_ids_map：用于存储 B 板到 A 板的 ID 转换映射关系的映射表。
     * - board_rows：板的行数。
     * - board_columns：板的列数。
     *
     * 返回值：
     * - true：成功生成了映射表。
     * - false：输入的行数或列数无效，无法生成映射表。
     */
    convert_board_ids_map.clear();
    if (board_rows < 1 || board_columns < 1)
    {
        return false;
    }
    for (int i = 0; i < board_rows; i++)
    {
        for (int j = 0; j < board_columns; j++)
        {
            convert_board_ids_map.insert(std::make_pair(((board_rows - i - 1) * board_columns + j + 1), (i * board_columns + j + 1)));
        }
    }
    return true;
}
