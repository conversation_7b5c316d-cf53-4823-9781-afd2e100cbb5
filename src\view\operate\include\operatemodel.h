﻿/*****************************************************************//**
 * @file   operatemodel.h
 * @brief  主操作界面model类
 * @details
 * <AUTHOR>
 * @date 2024.1.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef OPERATEMODEL_H
#define OPERATEMODEL_H
 //CUSTOM
#include "modelbase.h"
#include "algoexecuteparam.hpp"
#include "coordinatetransform.hpp" // CoordinateTransform
namespace subboardsort
{
    class SubboardSortManager;
}

namespace jrsaoi
{
    //! 元件的单个算法检测结果 by zhangyuyu 2025.1.18
    struct ComponentAlgoResult
    {
        jrsoperator::OperatorParamBasePtr algo_result; //! 算法检测结果
        std::string detect_window_name; //! 检测框名称  检测框与算法绑定，本质上两者没关系
        std::string component_name;     //! 元件名称
        std::string part_number_name;   //! 料号名称
        std::string sub_board_name;     //! 子板名称
        bool detect_status_result;             //! 检测结果OK 还是NG
    };
    class  OperateModel :public ModelBase
    {
        Q_OBJECT
    public:
        OperateModel(const std::string& name);
        ~OperateModel();
        /**
         * @fun Update
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        int Update(const jrsdata::ViewParamBasePtr& param_)override;
        /**
         * @fun Save
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        int Save(const jrsdata::ViewParamBasePtr& param_)override;
        /**
         * @fun GetProjectParam
         * @brief
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        const jrsdata::ProjectParamPtr& GetProjectParam();

        /**
         * @fun  GetTemplatesByDetectWinName
         * @brief .
         * @param part_numb_name
         * @param detect_win_name
         * @param templates
         * @data 2024.10.31
         * @return
         * <AUTHOR>
         */
         //int GetTemplatesByDetectWinName(const std::string& part_numb_name, const std::string& detect_win_name, std::vector<jrsdata::Template>& templates);

         /**
          * @fun  SetAlgorithmAndDefectList
          * @brief 设置算法和缺陷列表.
          * @param _algo_name_list
          * @param _defect_list
          * @data 2024.10.31
          * @return
          * <AUTHOR>
          */
        int SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list);

        /**
         * @fun  SetAlgoritmDefaultParam
         * @brief 设置算法默认参数.
         * @param _algo_default_param
         * @data 2024.10.31
         * @return
         * <AUTHOR>
         */
        int  SetAlgoritmDefaultParam(const std::map<std::string, std::string>& _algo_default_param);

        /**
         * @fun  MultiAlgoAdd
         * @brief 多算法检测框的添加
         * @param _algo_default_param
         * @data 2024.10.31
         * @return
         * <AUTHOR>
         */
        int MultiAlgoAdd(jrsdata::ComponentEntity& entity, const jrsdata::MultiAlgoParam& multi_algo_param);

        /**
         * @fun  UpdateSelectedDetectWin
         * @brief 根据检测框名更新当前选中检测框.
         * @param win_name 检测框名
         * @data 2024.12.18
         * <AUTHOR>
         */
        void UpdateSelectedDetectWin(const std::string& win_name);
        void UpdateSelectedDetectWin(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  UpdateSelectedDetectWin
         * @brief 根据检测框名更新当前选中检测框（选中多个检测框）.
         * @param win_name_list 检测框名列表
         * @data 2024.12.18
         * <AUTHOR>
         */
        void UpdateSelectedDetectWin(const std::vector<std::string>& win_name_list);

        /**
         * @fun  UpdateSelectedComponent
         * @brief 根据元件名更新当前选中元件（选中多个元件）.
         * @param component_name_list 元件名列表
         * @param component_type 元件类型
         * @param component_changed 当前选中元件是否改变
         * @param part_number_changed 料号是否改变
         * @data 2024.12.18
         * <AUTHOR>
         */
        void UpdateSelectedComponent(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_,
            bool& component_changed,
            bool& part_number_changed
        );

        /**
         * @fun  UpdateSelectedPad
         * @brief 根据pad名更新当前选中pad.
         * @param pad_list pad名列表
         * @data 2024.12.18
         * <AUTHOR>
         */
        void UpdateSelectedPad(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  UpdateSelectedPNDetectInfo
         * @brief 根据料号名更新当前编辑的所有检测框.
         * @param part_numb_name 料号名
         * @data 2024.12.18
         * <AUTHOR>
         */
        void UpdateSelectedPNDetectInfo(const std::string& part_numb_name);

        /**
        * @fun  AlgoParamChangedCallbackFunc
        * @brief 算法参数更改回调函数.
        * @param param_ 算法参数
        * @data 2024.12.18
        * @return 是否成功
        * <AUTHOR>
        */
        bool AlgoParamChangedCallbackFunc(jrsoperator::OperatorParamBasePtr param_);

        /**
         * @fun  GetDetectWinExecuteParam
         * @brief 获取检测框执行所需参数及数据. 丢弃使用 2025.4.21，统一元件检测和检测框检测时的参数一致
         * @param component 元件
         * @param component_unit 元件组件（cad/焊盘）
         * @param detect_win 检测框
         * @param exect_param 算法执行参数
         * @param run_mode 执行类型（0：整版建模，1：在线检测，2：在线调试）
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int GetDetectWinExecuteParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win, jrsparam::ExecuteAlgoParam& exect_param, cv::Mat& matrix_to_src_image_);

        /**
         * @fun  GetComponentExecuteParam
         * @brief 获取单元件执行所需参数及数据.
         * @param component 元件
         * @param detect_win_exect_param 执行单元件检测所需参数
         * @param run_mode 执行类型（0：整版建模，1：在线检测，2：在线调试）
         * @data 2024.12.24
         * @return 错误码
         * <AUTHOR>
         */
        int GetComponentExecuteParam(const jrsdata::Component& component, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exect_param, cv::Mat& matrix_to_src_image_);

        /**
         * @fun SaveStandardComponentLightImg
         * @brief 保存标准元件灯光图像
         * @return 成功返回 AOI_OK，失败返回错误码
         * @note 目前没有使用，后期可能会用到
         * <AUTHOR>
         * @date 2025.2.4
         */
        int SaveStandardComponentLightImg(/*const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& component_img_map*/);

        /**
         * @fun  SetDetectWinRunResult
         * @brief 将检测结果保存到检测框参数中.
         * @param part_name 检测框所属料号
         * @param detect_model_name 检测框所属模块
         * @param win_name 检测框名称
         * @param result 检测结果
         * @data 2024.12.24
         * <AUTHOR>
         */
        void SetDetectWinRunResult(const std::string part_name, const std::string& detect_model_name, const std::string& win_name, const std::string& result);

        /**
         * @fun  SetCurSelectedToAlgoEventParam
         * @brief 将当前选中参数设置到算法编辑传递参数中.
         * @param param 算法编辑传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int SetCurSelectedToAlgoEventParam(jrsdata::AlgoEventParamPtr& param);

        /**
         * @fun GetCurSelectComponentEntity
         * @brief 获取当前选中的元件信息
         * @param entity [IN] 元件库单个元件信息
         * <AUTHOR>
         * @date 2025.4.23
         */
        void GetCurSelectComponentEntity(jrsdata::ComponentEntity& entity);

        /**
         * @fun  GetBindPartName
         * @brief 根据料号查询料号跟随名称.
         * @param part_name 料号名称
         * @data 2025.4.8
         * @return 料号跟随(元件库元件名称)
         * <AUTHOR>
         */
        std::string GetBindPartName(const std::string part_name);

        /**
         * @fun  GetDeviceImg
         * @brief 根据元件名称获取显示图(以及该图中心点与元件中心点偏移).
         * @param component_name 元件名称
         * @param sub_board_name 子板名称
         * @param img 显示图
         * @param offset_x 显示图中心点与元件中心点的X偏移
         * @param offset_y 显示图中心点与元件中心点的Y偏移
         * @data 2025.4.8
         * @return
         * <AUTHOR>
         */
        int GetDeviceImg(const std::string component_name, const std::string sub_board_name, cv::Mat& img, float& offset_x, float& offset_y);

        /**
         * @fun  GetComponentEntitys
         * @brief 获取当前工程所有的元件信息.
         * @param entitys 获取到的所有元件信息
         * @data 2025.4.8
         * @return
         * <AUTHOR>
         */
        int GetComponentEntitys(jrsdata::ComponentEntitys& entitys);

        /**
         * @fun  GetComponentEntity
         * @brief 根据料号和检测框信息获取元件信息.
         * @param part_name 料号
         * @param info 检测框信息
         * @param entity 获取到的元件信息
         * @data 2025.4.8
         * @return
         * <AUTHOR>
         */
        int GetComponentEntity(const std::string part_name, const jrsdata::PNDetectInfo& info, jrsdata::ComponentEntity& entity);

        /**
         * @fun  GetTemplatesByDetectWinName
         * @brief 根据料号和检测区域名称获取模板图
         * @param templates 模板数据
         * @data 2025.3.20
         * @return 错误码
         * <AUTHOR>
         */
        int GetTemplatesByDetectWinName(std::string part_name, std::string win_name, std::vector<jrsdata::Template>& templates);

        /**
         * @fun  LoadComponentByComponentEntity
         * @brief 加载元件库元件
         * @param entity 元件库元件信息
         * @param use_cur_select 是否应用到当前选中料号
         * @data 2025.4.8
         * @return
         * <AUTHOR>
         */
        int LoadComponentByComponentEntity(const jrsdata::ComponentEntity& entity, bool use_cur_select = false);

        /**
        * @fun  GetProjectCompressImg
        * @brief 获取工程整板压缩图片.
        * @data 2025.1.12
        * @return 工程压缩图片
        * <AUTHOR>
        */
        std::unordered_map<int, cv::Mat> GetProjectCompressImg();

        /**
         * @fun GetAllBriefComponentInfo
         * @brief 获取所有元件的简要信息 简要信息包含坐标，角度
         * @return 返回获取的简要元件信息
         * <AUTHOR>
         * @date 2025.1.14
         */
        std::vector<jrsdata::BriefComponentInfo> GetAllBriefComponentInfo();
        /**
        * @fun  GetProjectEntiretyImg
        * @brief 获取工程整板图片.
        * @data 2025.1.12
        * @return 工程整板图片
        * <AUTHOR>
        */
        std::unordered_map<int, cv::Mat> GetProjectEntiretyImg();
        /**
        * @fun ModifyComponentCoordinate
        * @brief 通过定位后结果修改元件坐标，当使用定位后修改元件坐标功能后
        * @param location_matrix_ [IN] 定位旋转矩阵
        * @param src_matrix_ [IN] 源矩阵
        * @param component_rect_in_crop_img_ [IN] 元件在截图里面的rect
        * @param component_modify_ [OUT] 要修改坐标的元件
        * <AUTHOR>
        * @date 2025.3.31
        */
        void ModifyComponentCoordinate(const cv::Mat& location_matrix_, const cv::Mat& src_matrix_, const JrsRect component_rect_in_crop_img_, jrsdata::Component& component_modify_);

        /**
         * @fun UpdateSingleComponentInfo
         * @brief 更新单个元件信息
         * @param new_component_[IN] 新的元件信息
         * <AUTHOR>
         * @date 2025.3.21
         */
        void UpdateSingleComponentInfo(jrsdata::Component new_component_);

        /**
         * @fun GetNotDetectUnitName
         * @brief 获取不检测的unit名称
         * @return  返回获取的结果值
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::unordered_map<std::string, std::vector<std::string>> GetNotDetectUnitName();
        /**
         * @fun SetDetectWindowDetectResult
         * @brief
         * @param component
         * @param detect_window_name
         * @param algo_result
         * <AUTHOR>
         * @date 2025.3.31
         */
        void SetDetectWindowDetectResult(const jrsdata::Component& component, const std::string& detect_window_name, jrsoperator::OperatorParamBasePtr algo_result);

        /**
         * @fun GetDetectWindowDetectResult
         * @brief
         * @param component_name
         * @param detect_window_name
         * @return
         * <AUTHOR>
         * @date 2025.3.31
         */
        jrsoperator::OperatorParamBasePtr GetDetectWindowDetectResult(const std::string& component_name, const std::string& detect_window_name);

        /**
         * @fun SaveComponentDetectStatus
         * @brief 保存当前元件的检测结果状态
         * @param component 当前元件
         * <AUTHOR>
         * @date 2025.3.31
         */
        void SaveComponentDetectStatus(const jrsdata::Component& component);
        /**
         * @fun EraseSpeficComponentResult
         * @brief 移除指定元件的检测结果状态
         * @param component_name_[IN] 元件名称
         * <AUTHOR>
         * @date 2025.3.31
         */
        void EraseSpeficComponentResult(const std::string& component_name_);
        /**
         * @fun GetAllComponentResultStatus
         * @brief 获取所有元件的检测结果状态
         * @return 所有的元件检测结果map
         * <AUTHOR>
         * @date 2025.3.31
         */
        const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& GetAllComponentResultStatus();

        /**
         * @fun GetSpeficComponentResultStatus
         * @brief 获取指定元件结果状态
         * @param component_name_ 元件名称
         * @return 获取指定的元件结果状态
         * <AUTHOR>
         * @date 2025.3.31
         */
        std::optional<std::unordered_map<std::string, ComponentAlgoResult>>  GetSpeficComponentResultStatus(const std::string& component_name_);;

        /**
        * @fun GetSpeficComponentResultStatus
        * @brief 获取指定元件结果状态
        * @param component_name_ 元件名称
        * @return 获取指定的元件结果状态
        * <AUTHOR>
        * @date 2025.3.31
        */
        int GetUnitIDByUnitShowID(const std::string& subboard_name_, const std::string& component_name_, const std::string& window_name_, int show_id_);

        /**
        * @fun SettingViewParamPtr
        * @brief 获取所有参数
        * @param component_name_ 元件名称
        * @return 返回所有参数
        * <AUTHOR>
        * @date 2025.5.6
        */
        jrsdata::SettingViewParamPtr GetSettingParamsPtr();

        /**
        * @fun GetResolution
        * @brief 获取相机分辨率
        * @param resolution_x [IN] 相机X方向分辨率
        * @param resolution_y [IN] 相机Y方向分辨率
        * <AUTHOR>
        * @date 2025.5.20
        */
        void GetResolution(float& resolution_x_, float& resolution_y_);

        /**
        * @fun CreateSubboardComponent
        * @brief 创建子板AI区域元件
        * @param ai_area [IN] ai元件
        * <AUTHOR>
        * @date 2025.5.22
        */
        int CreateSubboardComponent(jrsdata::Component& ai_area);

        /**
        * @fun CreateAIComponent
        * @brief 根据元件创建子板AI元件
        * @param ai_component [IN] ai元件
        * @param width [IN] 指定元件unit的宽度
        * @param height [IN] 指定元件unit的高度
        * <AUTHOR>
        * @date 2025.5.23
        */
        int CreateAIComponent(jrsdata::Component& ai_component, float width, float height);

        /**
         * @fun ReadPNDetectInfo
         * @brief 根据料号名称读取料号检测信息
         * @param part_number [IN] 料号名称
         * @return 返回读取的料号信息
         * <AUTHOR>
         * @date 2025.5.26
         */
        std::optional<jrsdata::PNDetectInfo> ReadPNDetectInfo(const std::string& part_number_name);

    signals:
        void SigUpdateOperator(const jrsdata::ViewParamBasePtr& param_);

        ///**
        // * @fun SignalAlignMarks 
        // * @brief 添加发送Mark点对位的信号到Controller
        // * @param marks_   找到建模的全部mark点
        // * <AUTHOR>
        // * @date 2024.12.13
        // */
        //void SignalAlignMarks(std::vector<jrsdata::Component>& marks_);



    private:

        /**
         * @fun  UpdateDetectWindowParam
         * @brief 更新界面街检测框参数到当前选中检测框.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return
         * <AUTHOR>
         */
        int UpdateDetectWindowParam(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun  UpdateSelectedDetectWinIPEParam
         * @brief 更新IPE的UI参数到当前选中算法.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int UpdateSelectedDetectWinIPEParam(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun  UpdateTemplateParam
         * @brief 更新当前选中算法的模板.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int UpdateTemplateParam(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun  RenderViewDetectWinUpdate
         * @brief 渲染界面检测框更新事件处理.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int RenderViewDetectWinUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  RenderViewComponentUpdate
         * @brief 渲染界面元件更新事件处理.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int RenderViewComponentUnitUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  RenderViewPadUpdate
         * @brief 渲染界面pad更新事件处理.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int RenderViewPadUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  SetDetectWindowDefaultParam
         * @brief 将默认参数设置给检测框.
         * @param detect_win 需要设置参数的检测框
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int SetDetectWindowDefaultParam(jrsdata::DetectWindow& detect_win);

        /**
         * @fun  AddTemplateToCurSelectedDetectWin
         * @brief 往当前选中检测框中添加模板.
         * @param template_img 模板图像
         * @param color_param 颜色参数
         * @param light_img_id 使用的灯光图像id
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */

        int AddTemplateToCurSelectedDetectWin(const cv::Mat& template_img, const std::string& color_param, const int light_img_id, jrsdata::Template& output_template);

        // int AddTemplateToCurSelectedDetectWin(jrsdata::Template& template_);

        /**
         * @fun  DelTemplateFromCurSelectedDetectWin
         * @brief 从当前选中检测框中删除模板.
         * @param template_id 模板id
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int DelTemplateFromCurSelectedDetectWin(const int& template_id);


        /**
         * @fun  GetPNDetectInfoByPartNum
         * @brief 根据料号获取此料号下所有检测框信息.
         * @param part_num 料号
         * @data 2024.12.18
         * @return 料号下所有检测框信息
         * <AUTHOR>
         */
        jrsdata::PNDetectInfo* GetPNDetectInfoByPartNum(const std::string& part_num);

        /**
         * @fun  ConvertRenderEditGrapihsType
         * @brief 将渲染编辑图形类型转换为operate模块中的类型（operate模块中的类型）.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        jrsdata::OperateWindowType ConvertRenderEditGrapihsType(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun  UpdateCurSelectedParamByRenderEditGrapihicsParam
         * @brief 根据渲染编辑图形参数更新当前选中参数.
         * @param param_ 界面传递参数
         * @data 2024.12.18
         * @return 错误码
         * <AUTHOR>
         */
        int UpdateCurSelectedParamByRenderEditGrapihicsParam(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_);

        /**
         * @fun EraseSpeficDetectWindowResult
         * @brief 移除指定元件指定检测框
         * @param detect_window_name_
         * @param component_name_
         * <AUTHOR>
         * @date 2025.3.31
         */
        void EraseSpeficDetectWindowResult(const std::string& detect_window_name_, const std::string& component_name_);
        /**
         * @fun ErasAllResult
         * @brief 清楚所有的缓存结果(包括元件状态和检测框结果)
         * <AUTHOR>
         * @date 2025.4.29
         */
        void ErasAllResult();
        /**
         * @fun UpdateComponentResultStatus
         * @brief 更新当前元件结果状态
         * @param component_name_ [IN] 元件名称
         * <AUTHOR>
         * @date 2025.3.31
         */
        void UpdateComponentResultStatus(const std::string& component_name_);

        /**
         * @fun ComputeComponentStatus
         * @brief 计算元件结果状态
         * @param component_name_[IN] 元件名称
         * @return 返回计算出来的状态结果，true/false,即(OK/NG )
         * <AUTHOR>
         * @date 2025.3.31
         */
        bool ComputeComponentStatus(const std::string& component_name_);
        jrsparam::ExecuteModeInfo GetExecuteModeInfo();

        /**
         * @fun HandleDetectCopy
         * @brief 处理检测框复制相关的操作
         * @param param_[IN] 输入参数
         * @return
         * <AUTHOR>
         * @date 2025.4.22
         */
        void HandleDetectCopy(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun DetectCopyAction
         * @brief 检测框粘贴
         * @return
         * <AUTHOR>
         * @date 2025.4.23
         */
        void DetectCopyAction();

        /**
         * @fun CreateOneDetectWindow
         * @brief 根据操作类型生成DetectWindow
         * @param detect_operate_type [IN] 检测框操作类型
         * @param detect_window_ [out] 创建的DetectWindow
         * @return
         * <AUTHOR>
         * @date 2025.4.22
         */
        void CreateOneDetectWindow(const jrsdata::DetectOperateType detect_operate_type, jrsdata::DetectWindow& detect_window_);

        /**
         * @fun ProcessMirrorPad
         * @brief 处理pad检测框旋转
         * @param detectwindow [out] 检测框操
         * @param direction [in] 元件方向
         * @return
         * <AUTHOR>
         * @date 2025.5.7
         */
        void ProcessMirrorPad(jrsdata::DetectWindow& detectwindow, jrsdata::ComponentUnit::Direction direction);

        /**
         * @fun RotateCurAllDetectWin
         * @brief 当前选中元件的所有检测框顺时针旋转90度
         * @return
         * <AUTHOR>
         * @date 2025.5.7
         */
        void Rotate90CurAllDetectWin();

        /**
         * @fun CheckRepairDataHasSaved
         * @brief 检查维修站数据是否已经保存  若没保存则保存
         * <AUTHOR>
         * @date 2025.5.18
         */
        void CheckRepairDataHasSaved();
        /**
         * @fun ClearInnerMembersParam
         * @brief 清除内部成员变量参数
         * <AUTHOR>
         * @date 2025.6.12
         */
        void ClearInnerMembersParam();

        /**
         * @fun SubboardSort
         * @brief Used for subboard sorting
         * @param subboard_sort_param_
         * <AUTHOR>
         * @date 2025.6.19
         */
        std::vector<jrsdata::SubBoard> SubboardSort(const jrsdata::SubboardSortParam& subboard_sort_param_);


        std::shared_ptr<jrsdata::RenderEventParam> CreateRenderEventParam(
            const std::string& event_name_,
            const std::shared_ptr<jrsdata::MultiBoardEventParam>& multi_param_ptr_ = nullptr);

        std::shared_ptr<jrsdata::RenderViewParam> CreateRenderViewParam(const std::string& event_name_);

        jrsdata::Component* cur_selected_component = nullptr;
        jrsdata::DetectWindow* cur_selected_detect_win = nullptr;
        jrsdata::PNDetectInfo* cur_selected_spec_region = nullptr;
        jrsdata::ComponentUnit* cur_selected_comp_unit = nullptr;
        std::string             cur_selected_pad_name = "";
        std::string             cur_select_model_name = "";
        jrsdata::LightImageType cur_selected_light_type = jrsdata::LightImageType::RGB;
        std::vector<std::string> defect_list;

        //! 检测框复制对象
        jrsdata::DetectCopyParam detect_copy_param;

        /*********************在线调试相关参数 by zhangyuyu 2025.4.29*************************************************/
        bool is_debugging = false; //!< 是否在线调试
        std::atomic<bool> is_waitting_debug_info; /**< 当前板子调试是否结束，结束后才会继续接受新的板子数据*/
        jrsdata::CurrentDebugInfo current_debug_info; //!< 当前选中的在线调试元件信息
        /*********************在线调试相关参数 by zhangyuyu 2025.4.29*************************************************/

        std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_name_list;
        std::map<std::string, std::string> algo_default_param;

        //! 存放工程下所有检测框的检测结果 by zhangyuyu 2025.1.18
        std::unordered_map<std::string/*元件名*/, std::unordered_map<std::string/**检测框名*/, ComponentAlgoResult>> component_all_detect_window_result;//! 存放工程下所有元件的所有检测框的结果
        std::unordered_map<std::string, jrsdata::ComponentDetectResult> all_component_result; //! 存放工程下所有元件的检测结果 OK /NG  key:元件名称 value:元件检测结果

        std::shared_ptr<subboardsort::SubboardSortManager> _subboard_sort_manager_ptr; //! sub-board sort management pointers
    };
    using OperateModelPtr = std::shared_ptr<OperateModel>;
}
#endif // ! OPERATEMODEL_H
