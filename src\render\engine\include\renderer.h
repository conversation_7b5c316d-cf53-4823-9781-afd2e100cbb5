﻿/*********************************************************************
 * @brief  定义渲染器.
 *
 * @file   renderer.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef RENDERER_H
#define RENDERER_H

#include "renderabstract.hpp" // RenderAbstractPtr
#include "rvec.hpp"
#include <vector>

class WindowInterface;
class GraphicsContext;
class VisualCameraAbstract;
class ShaderProgram;
class RenderAbstract;
struct SpriteVertexFormat;
enum class ProgramType;

class QOpenGLContext;
class QOpenGLShaderProgram;
class QSurface;
class QPaintDevice;

/**
 * @brief 将GL图元包装成枚举类
 */
enum class PrimType
{
    PT_Unknown = -1,
    PT_Points,
    PT_Lines,
    PT_Triangles,
    PT_Line_Strip,
};

/**
 * @brief  渲染设备RAII
 */
struct PaintDeviceRAII
{
    QPaintDevice* device;
    QOpenGLContext* qGLContext;
    PaintDeviceRAII(QPaintDevice* d, QOpenGLContext* c) : device(d), qGLContext(c)
    {
        PushAttribute();
    }
    ~PaintDeviceRAII()
    {
        PopAttribute();
    }
    void PushAttribute();
    void PopAttribute();

    template <class TYPE>
    void EnableAttribute(TYPE* func, const unsigned int feature, unsigned char enabled)
    {
        if (enabled)
            func->glEnable(feature);
        else
            func->glDisable(feature);
    }

    int Blend;
    int BlendDstAlpha;
    int BlendDstRGB;
    int BlendEquationAlpha;
    int BlendEquationRgb;
    int BlendSrcAlpha;
    int BlendSrcRGB;
};

class Renderer
{
public:
    Renderer();
    ~Renderer();

    void Update();
    void Destroy();
    /**
     * @brief  设置Qt渲染环境
     * @note   指针由window管理
     */
    void SetContext(QSurface* surface, QOpenGLContext* mainContext);
    /**
     * @brief  设置Qt渲染设备
     * @note   用于设置qpainter当前opengl上下文,指针由window管理
     */
    void SetQPaintDevice(QPaintDevice* device);
    /**
     * @brief  获取Qt渲染设备
     * @note   用RAII封装,方便恢复opengl上下文,qpainter自带的环境恢复不全
     */
    PaintDeviceRAII GetPaintDeviceRAII() const;

    /**
     * @brief  设置着色器程序
     * @note   着色器程序指针由StandardProgram管理
     */
    void SetProgram(ShaderProgram* p);
    void SetProgram(const ProgramType& type);

    /**
     * @brief  设置相机
     * @note   相机指针由window管理
     */
    void SetCamera(VisualCameraAbstract* c);
    inline VisualCameraAbstract* GetCamera() { return camera; }

    /**
     * @brief  设置视野缩放比率.
     * @fun    GetZoom
     * @return
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    double GetZoom() const;
    // void SetZoomState(int state);

    /**
     * @brief  设置视口大小
     */
    void SetViewport(double width, double height);
    void GetViewport(double& width, double& height) const;
    /**
     * @brief  设置画布大小
     */
    bool SetCanvasSize(int width, int height);
    bool GetCanvasSize(int& width, int& height) const;

    /**
     * @brief  添加渲染对象
     * @note   智能指针的所有权将被转移
     */
    void AddObject(RenderAbstractPtr ro);
    void RemoveObject(RenderAbstractPtr ro);
    /**
     * @brief  设置渲染引擎
     */
    void SetEngine(WindowInterface* engine_);

    // 坐标转换
    bool MouseToWorld(float& x, float& y) const;
    bool WorldToMouse(float& x, float& y) const;
    // void MouseToCAMERA(float &x, float &y) const;
    // void CAMERAToMouse(float &x, float &y) const;

    // 纹理管理
    bool CreateTexture(unsigned int& texture_id, const unsigned char* const data, int width, int height,
        unsigned int format);
    void DeleteTexture(unsigned int texture_id);

    // 插入渲染数据
    void AppendRenderData(SpriteVertexFormat* vertex_data, int vertex_count_, uint32* index_data, int index_count_,
        unsigned int texture, PrimType type);

    template <class Type>
    void AppendRenderData(Type* vertex_data, int vertex_count_, uint32* index_data, int index_count_, PrimType type);

    // 其他渲染相关设置
    void SetLineWidth(int width);
    void SetPointSize(int size);

private:
    /**
     * @brief 刷新,将缓存区数据渲染,并清空缓存区
     */
    void Flush();
    /**
     * @brief 往缓冲区添加纹理
     */
    int SetTexture(unsigned int texture);
    /**
     * @brief 将纹理绑定到纹理单元
     */
    void BindTexture();

    void DrawElements(void* vertex_data, void* index_data, int index_count_, PrimType type);

private:
    QOpenGLContext* main_context;  ///< qt界面上下文
    QSurface* m_surface;           ///< qt界面上下文
    QPaintDevice* m_device;        ///< qt界面上下文
    ShaderProgram* m_program;            ///< 渲染程序,这里不管理内存释放
    WindowInterface* engine;       ///< 渲染引擎,这里不管理内存释放
    VisualCameraAbstract* camera;  ///< 虚拟相机,这里不管理内存释放

    std::vector<RenderAbstractPtr> objects; ///< 渲染对象

    PrimType prim_type;   ///< GL元素渲染模式
    int gl_line_width;    ///< GL线宽
    int gl_point_size;    ///< GL点大小

    static const int max_texture_num = 16;          ///< 单次最大渲染的纹理数量,这个在GLSL内定义,要改需要先改(shadersourceconstants.hpp) 
    static const int vertex_buffer_size = 1048576;  ///< 顶点的缓冲区大小 /*目前是最佳大小,调大调小都会影响帧率*/
    static const int index_buffer_size = 160000;    ///< 索引的缓冲区大小
    int current_texture_count;                      ///< 纹理计数,每超过上限渲染一次
    int current_vertex_count;                       ///< 顶点计数,每超过上限渲染一次
    int current_index_count;                        ///< 顶点索引计数,每超过上限渲染一次
    unsigned int texture_buffer[max_texture_num];   ///< 用于合并纹理的缓冲区 
    char vertex_buffer[vertex_buffer_size];         ///< 用于合并顶点的缓冲区
    uint32 index_buffer[index_buffer_size];         ///< 用于合并索引的缓冲区

    /*debug 使用*/
    // int m1 = 0;
    // int m2 = 0;
    // int m3 = 0;
    // int draw_call_num = 0;
};

template <class Type>
void Renderer::AppendRenderData(Type* vertex_data, int vertex_count_, uint32* index_data, int index_count_, PrimType type)
{
    int total_vertex_count = vertex_buffer_size / sizeof(Type);
    if (total_vertex_count - current_vertex_count < vertex_count_ || index_buffer_size - current_index_count < index_count_)
    {
        this->Flush();
    }
    if (type != prim_type)
    {
        // m3++;
        this->Flush();
        prim_type = type;
    }
    for (int i = 0; i < index_count_; ++i)
    {
        index_buffer[current_index_count + i] = current_vertex_count + index_data[i];
    }

    char* data_header = vertex_buffer + current_vertex_count * sizeof(Type);
    memcpy(data_header, (char*)vertex_data, vertex_count_ * sizeof(Type));

    current_vertex_count += vertex_count_;
    current_index_count += index_count_;
}

#endif // !RENDERER_H
