﻿#pragma once
/*****************************************************************
 * @file   tools.hpp
 * @brief   通用工具
 * @details
 * <AUTHOR>
 * @date 2025.2.24
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.2.24          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
#include <iostream>
#include <vector>
       // 定义宏来简化getter和setter的生成
#define AUTO_PROPERTY(value_type, value_name,FuncName)  \
private:                            \
    value_type _##value_name;                  \
public:                             \
    const value_type& Get##FuncName() const { return _##value_name; }   \
    void Set##FuncName(const value_type& value) { _##value_name = value; }

namespace render
{
    class Tools
    {
    public:
        template<typename T>
        static void RemoveExpiredWeakPtrs(std::vector<std::weak_ptr<T>>& weak_ptr_vector)
        {
            // 使用 erase-remove idiom 删除失效的 weak_ptr
            weak_ptr_vector.erase(
                std::remove_if(weak_ptr_vector.begin(), weak_ptr_vector.end(),
                    [](const std::weak_ptr<T>& weakPtr) {
                        return weakPtr.expired(); // 检查是否失效
                    }),
                weak_ptr_vector.end());
        }

        template<typename T>
        static void RemoveVectorElements(std::vector<T>& source, const std::vector<T>& to_remove)
        {
            source.erase(std::remove_if(source.begin(), source.end(),
                [&to_remove](const T& element)
                {
                    return std::find(to_remove.begin(), to_remove.end(), element) != to_remove.end();
                }), source.end());
        }


        static std::vector<std::string> SplitString(const std::string& s, char delimiter);
        /**
         * @fun JoinString
         * @brief
         * @param str_vec
         * @param connector
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static [[maybe_unused]] std::string JoinString(const std::vector<std::string> str_vec, char connector);

        /**
        * @fun NormalizeAngle
        * @brief  确保角度在[0-360]之间
        * @param angle
        * @return
        * <AUTHOR>
        * @date 2025.2.21
        */
        static double  NormalizeAngle(const double& angle);
        /**
         * @brief 获取当前本地时间的格式化字符串。
         *
         * 此函数提供灵活的时间格式选项，包括是否显示日期、时间、微秒，以及分隔符自定义。
         *
         * @param include_date 是否包含日期部分（例如：2025-04-11）
         * @param include_time 是否包含时间部分（例如：14:30:45）
         * @param include_microseconds 是否包含微秒（例如：14:30:45.123456）
         * @param date_sep 日期字段之间的分隔符（默认 "-"，例如：2025-04-11）
         * @param time_sep 时间字段之间的分隔符（默认 ":"，例如：14:30:45）
         * @param date_time_sep 日期和时间之间的分隔符（默认空格 " "，例如：2025-04-11 14:30:45）
         *
         * @return std::string 格式化后的当前时间字符串
         *
         * @note 该函数基于本地时间而非UTC时间；线程安全。
         * @example
         *     std::string time = GetCurrentTimeString(true, true, true);
         *     // 输出示例: 2025-04-11 14:30:45.123456
         */
        static std::string GetCurrentTimeString(bool include_date = true,
            bool include_time = true,
            bool include_microseconds = false,
            const std::string& date_sep = "-",
            const std::string& time_sep = ":",
            const std::string& date_time_sep = " ");
        /**
         * @fun GetPrefixString
         * @brief  获取前缀字符串
         * @param ori_str_
         * @return
         * <AUTHOR>
         * @date 2025.1.17
         */
        static std::string GetPrefixString(const std::string& ori_str_, const char& mark_char_ = '_');

    };
}