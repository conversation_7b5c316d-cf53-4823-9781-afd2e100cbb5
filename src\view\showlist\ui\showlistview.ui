<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ShowListView</class>
 <widget class="jrsaoi::ViewBase" name="ShowListView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>340</width>
    <height>772</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>ShowListView</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="label_product">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>23</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>23</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background:gray;color:white;</string>
        </property>
        <property name="text">
         <string>产品列表</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="CustomTableView" name="list_product">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="editTriggers">
         <set>QAbstractItemView::NoEditTriggers</set>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="widget_2" native="true">
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QLabel" name="label_part_no">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>23</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>23</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background:gray;color:white;</string>
        </property>
        <property name="text">
         <string>检测工位</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="CustomTableView" name="list_part_no">
        <property name="editTriggers">
         <set>QAbstractItemView::NoEditTriggers</set>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QWidget" name="widget_3" native="true">
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label_device">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>23</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>23</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background:gray;color:white;</string>
        </property>
        <property name="text">
         <string>检测项目</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="CustomTableView" name="list_device">
        <property name="editTriggers">
         <set>QAbstractItemView::DoubleClicked</set>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QWidget" name="widget_9" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <widget class="QFrame" name="frame_window_view">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>476</y>
        <width>292</width>
        <height>271</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>0</number>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>CustomTableView</class>
   <extends>QTableView</extends>
   <header>customtableview.h</header>
  </customwidget>
  <customwidget>
   <class>jrsaoi::ViewBase</class>
   <extends>QWidget</extends>
   <header>viewbase.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
