/*********************************************************************
 * @brief  定义命令基类.
 *
 * @file   commandabstract.hpp
 *
 * @date   2024.06.05
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef COMMAND_ABSTRACT_H
#define COMMAND_ABSTRACT_H
class CommandAbstract
{
public:
    virtual ~CommandAbstract() {};
    /**
     * @brief  执行当前命令.
     * @fun    excute
     *
     * @date   2024.06.05
     * <AUTHOR>
     */
    virtual void excute() = 0;
    /**
     * @brief  撤销当前命令.
     * @fun    revoke
     *
     * @date   2024.06.05
     * <AUTHOR>
     */
    virtual void revoke() = 0;
};
#endif // !COMMAND_ABSTRACT_H