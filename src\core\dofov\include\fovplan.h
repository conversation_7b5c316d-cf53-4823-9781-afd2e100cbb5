/*********************************************************************
 * @brief  fov规划接口.
 *
 * @file   fovplan.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef FOVPLAN_H
#define FOVPLAN_H
#include "calcfov.h"
#include "pluginexport.hpp"
#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "opencv2/opencv.hpp"
#pragma warning(pop)
#include <vector>

class JRS_AOI_PLUGIN_API FovPlan
{
public:
    /**
     * @brief  位置规划模式.
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    enum PositionPlanMode
    {
        GRID = 0,      ///< 网格划分
        ROW_MAJOR_MBR, ///< 行优先最小矩形覆盖
    };
    /**
     * @brief  路径规划模式.
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    enum PathPlanMode
    {
        SNAKE_TOP_TO_BOTTOM = 0, ///< 蛇形，从上到下
        SNAKE_LEFT_TO_RIGHT,     ///< 蛇形，从左到右
        SNAKE_BOTTOM_TO_TOP,     ///< 蛇形，从下到上
        SNAKE_RIGHT_TO_LEFT,     ///< 蛇形，从右到左
    };
    struct FovPlanOutput
    {
        std::vector<cv::Point2f> fov_path; ///< 规划出的fov路径点 TODO:丢弃使用
        std::vector<CalcFov::SeparateFovPath> fov_path_with_id; //!规划出来的fov路径，带fov的id号
        std::vector<CalcFov::Fov> fovs;    ///< 规划出的fov
        std::vector<CalcFov::ObjectOutput> objects; ///< 物体
    };
    struct FovPlanParam
    {
        int region_x;  ///< 规划区域x 单位 pix
        int region_y;  ///< 规划区域y 单位 pix
        int region_w;  ///< 规划区域宽 单位 pix
        int region_h;    ///< 规划区域高 单位 pix
        int fov_w;     ///< fov宽 单位 pix
        int fov_h;     ///< fov高 单位 pix
        int gap_w;     ///< fov横向最小间隔 单位 pix
        int gap_h;     ///< fov纵向最小间隔 单位 pix
        PositionPlanMode mode_position; ///< 位置规划模式
        PathPlanMode mode_path;         ///< 路径规划模式
        std::vector<CalcFov::ObjectInput> rrs; ///< 规划物体外接矩形
        // std::vector<cv::RotatedRect> rrs; ///< 规划物体外接矩形
        cv::Point2f start; ///< 规划起点 起点不纳入路径点

        FovPlanParam() :
            region_x(0),
            region_y(0),
            region_w(0),
            region_h(0),
            fov_w(0),
            fov_h(0),
            gap_w(30),
            gap_h(20),
            mode_position(GRID),
            mode_path(SNAKE_TOP_TO_BOTTOM),
            rrs(),
            start()
        {
        }
    };
    /**
     * @brief  执行fov规划.
     *
     * @fun    DoFovPlan
     * @param  fovs 路径点
     * @param  param 规划参数
     * @return 返回0正常
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static int DoFovPlan(FovPlanOutput& fov, const FovPlanParam& param);
    // static int DoFovPlan(std::vector<cv::Point2f>& fovs, const FovPlanParam& param);
    /**
     * @brief  生成fov规划测试图片.
     *
     * @fun    TestFovPlan
     * @param  param 规划参数
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static int TestFovPlan(const FovPlanParam& param);
    /**
     * @brief  生成fov规划debug文件.
     *
     * @fun    CreateDebugFile
     * @param  param 规划参数
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static int CreateDebugFile(const FovPlanParam& param);

protected:
    /**
     * @brief  打印日志.
     *
     * @fun    PrintInfo
     * @param  info
     * @note   默认打印到终端
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static void PrintInfo(const std::string& info);

private:
    /**
     * @brief  执行位置规划.
     *
     * @fun    PositionPlan
     * @return 正常返回0
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static int PositionPlan(FovPlanOutput& fov_out, const FovPlanParam& param);
    /**
     * @brief  执行路径规划.
     *
     * @fun    PathPlan
     * @return 正常返回0
     *
     * @date   2024.08.01
     * <AUTHOR>
     */
    static int PathPlan(std::vector<CalcFov::Fov>& fovs, const FovPlanParam& param);
};

#endif //! FOVPLAN_H