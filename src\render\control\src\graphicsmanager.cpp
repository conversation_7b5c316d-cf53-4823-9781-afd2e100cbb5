﻿//std
#include <regex>
#include <algorithm>


#include "graphicsmanager.h"
#include "graphicsalgorithm.h"
#include "padgroup.h"
#include "graphicsabstract.hpp"
#include "graphicsserialize.hpp" // 序列化
#include "graphicseventparam.hpp"
#include "eventparam.hpp"
#include "controlpointabstract.h"
#include "customcursortype.hpp"
#include "renderer.h"
#include "painter.h"
#include "log.h"
#include "padmanager.h"
template <typename T, typename Predicate>
std::vector<T> ExtractIf(std::vector<T>& input, Predicate pred)
{
    std::vector<T> extracted; // 用于存储符合条件的元素

    // 使用 std::stable_partition 将符合条件的元素移到前半部分
    auto partition_point = std::stable_partition(input.begin(), input.end(), [&](const T& elem)
        {
            return !pred(elem); // 保留不符合条件的元素
        });

    // 将符合条件的元素移动到 extracted
    extracted.insert(extracted.end(), std::make_move_iterator(partition_point), std::make_move_iterator(input.end()));

    // 删除原数组中符合条件的元素
    input.erase(partition_point, input.end());

    return extracted;
}

const char* const DEFAULT_GRAPHICS_TEMP_LAYER = "temp";       ///< 默认临时图形绘制层
const char* const DEFAULT_GRAPHICS_LAYER = "default";         ///< 默认图形绘制层
const char* const DEFAULT_GRAPHICS_CONTROL_LAYER = "control"; ///< 默认图形控制绘制层

const float response_max_limit = 10; ///< 控制点响应最大距离限制

GraphicsManager::GraphicsManager()
    : RenderAbstract()
    , m_angle(0)
    , m_parent_graphics_id("")
    , m_attr_controlpoint()
    , m_current_layer(DEFAULT_GRAPHICS_LAYER)
    , m_selection_mode(SelectGraphicsMode::FullContainment)
    , m_pad_manager(std::make_shared<PadManager>(this))
    // , m_copy_graphics_id("")
    // , debouncer(1, true)
{
    AddDefaultLayerConfig();
}

void GraphicsManager::Render()
{
    if (!IsHaveRenerer())
        return;

    float zoom = 2.f / (float)renderer->GetZoom(); // 1.0f / (float)renderer->GetZoom() * 2.f

    for (auto& lc : m_layer_configs)
    {
        lc.second->SetLineScale(zoom);
    }

    const auto& ls = ReadLayerConfig();
    Painter p(renderer);
    {
        std::shared_lock<std::shared_mutex> lck(mutex);

        int size = (int)m_graphics.size();

        //printInfo(std::stringstream() << "m_graphics size:" << size);

        for (int i = 0; i < size; ++i)
        {
            const auto& gh = m_graphics[i];
            if (!gh)
                continue;

            const auto& settings = gh->settings;
            auto iter = ls.find(settings.GetLayer());
            if (iter == ls.end())
                continue;

            const auto& layer = iter->second;
            if (!layer->_display_style.is_render)
                continue;

            gh->Draw(renderer, &p, layer.get());
        }
    }
    // // 控制点渲染
    // {
    //     std::function<void(const std::vector<ControlPoint>&, Renderer* r)> func =
    //         std::bind(&GraphicsManager::RenderControlPoint, this, std::placeholders::_1, std::placeholders::_2);
    //     m_controlpoints.UseResourcesDoSomething(func, renderer);
    // }
    if (m_add_graphics)
    {
        auto& gh = m_add_graphics;
        const auto& settings = gh->settings;
        auto iter = ls.find(settings.GetLayer());
        if (iter != ls.end())
        {
            const auto& layer = iter->second;
            gh->Draw(renderer, &p, layer.get());
        }
    }
}

void GraphicsManager::Clear()
{
    DeleteGraphics();

    m_pad_manager->GetGraphicsManager();

    TriggerGraphicsdraw();
}

GraphicsPtr GraphicsManager::CreateGraphics(const GraphicsFlag& flag, const std::string& layer,
    const std::string& group_name, const GraphicsPtr& father_graphics_, bool invoke_callback)
{
    const bool change_layer = !layer.empty() && IsLayerExist(layer);
    GraphicsPtr gh;
    switch (flag)
    {
    case GraphicsFlag::pad_group:
    {
        auto pad_graphics_group_ptr = std::make_shared<PadGraphicsGroup>(m_pad_manager.get(), father_graphics_);

        m_pad_manager->AddPadGroup(pad_graphics_group_ptr, group_name);

        gh = pad_graphics_group_ptr;
    }
    break;
    case GraphicsFlag::rect:
    {
        gh = std::make_shared<RectGraphics>();
    }
    break;
    default:
        break;
    }
    if (!gh) return nullptr;

    if (change_layer)
    {
        gh->settings.SetLayer(layer);
    }
    if (gh->settings.GetLayer().empty())
    {
        gh->settings.SetLayer(m_current_layer);
    }

    auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
        [&gh](const GraphicsPtr& gh_) { return gh_->GetId() == gh->GetId(); });

    if (it != m_graphics.end())
    {
        return nullptr;
    }

    m_graphics.push_back(gh);
    SortGraphics();
    // UpdateGraphics(gh, invoke_callback);
    if (invoke_callback)
    {
        TriggerGraphicsadd(gh);
    }
    return gh;
}

GraphicsPtr GraphicsManager::CreateGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const float& angle, const GraphicsFlag& flag, const std::string& layer)
{
    GraphicsPtr gh = CreateGraphics(flag, layer, false);
    if (!gh)
        return nullptr;

    UpdateGraphicsValue(gh, xstart, ystart, xend, yend, angle);

    // auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
    //     [&gh](const GraphicsPtr& gh_) { return gh_->GetId() == gh->GetId(); });
    // if (it != m_graphics.end())
    // {
    //     return nullptr;
    // }

    // m_graphics.push_back(gh);
    // SortGraphics();
    // // UpdateGraphics(gh, true);,
    TriggerGraphicsadd(gh);
    return gh;
}

std::vector<GraphicsPtr> GraphicsManager::CreateGraphics(const std::string& json, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs;
    std::stringstream oss(json);
    {
        cereal::JSONInputArchive archive(oss);
        archive(ghs);
    }
    for (auto& gh : ghs)
    {
        auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
            [&gh](const GraphicsPtr& gh_) { return gh_->GetId() == gh->GetId(); });
        if (it != m_graphics.end())
        {
            return {};
        }
        m_graphics.push_back(gh);
    }
    SortGraphics();
    // UpdateGraphics(ghs, "", true);
    if (invoke_callback)
    {
        TriggerGraphicsadd(ghs);
    }
    return ghs;
}

int GraphicsManager::UpdateGraphics(const GraphicsPtr& gh_, bool invoke_callback)
{
    if (!gh_ || gh_->GetId().IsEmpty())
    {
        return ErrorCode::INVALID_GRAPHICS;
    }

    if (gh_->settings.GetLayer().empty())
    {
        gh_->settings.SetLayer(m_current_layer);
    }

    auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
        [&gh_](const GraphicsPtr& gh) { return gh_->GetId() == gh->GetId(); });
    if (it != m_graphics.end())
    {
        TriggerGraphicschange({ *it });
        *it = gh_;
        if (invoke_callback)
        {
            TriggerGraphicsupdate(gh_);
        }
    }
    else
    {
        m_graphics.push_back(gh_);
        if (invoke_callback)
        {
            TriggerGraphicsadd(gh_);
        }
    }
    SortGraphics();
    return 0;
}

int GraphicsManager::UpdateGraphics(const GraphicsPtrVec& ghs, bool invoke_callback)
{
    TriggerGraphicsupdate(ghs, invoke_callback);
    return 0;
}

int GraphicsManager::UpdateGraphics(const std::vector<GraphicsPtr>& ghs_, const std::string& layer, bool invoke_callback)
{
    const bool change_layer = !layer.empty() && IsLayerExist(layer);

    TriggerGraphicschange(ghs_);
    std::vector<GraphicsPtr> update_ghs;
    std::vector<GraphicsPtr> create_ghs;
    for (auto& gh_ : ghs_)
    {
        if (!gh_ || gh_->GetId().IsEmpty())
        {
            return ErrorCode::INVALID_GRAPHICS;
        }

        if (change_layer)
        {
            gh_->settings.SetLayer(layer);
        }
        if (gh_->settings.GetLayer().empty())
        {
            gh_->settings.SetLayer(m_current_layer);
        }

        auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
            [&gh_](const GraphicsPtr& gh) { return gh_->GetId() == gh->GetId(); });
        if (it != m_graphics.end())
        {
            *it = gh_;
            update_ghs.emplace_back(gh_);
        }
        else
        {
            m_graphics.emplace_back(gh_);
            create_ghs.emplace_back(gh_);
        }
    }
    SortGraphics();
    if (invoke_callback)
    {
        TriggerGraphicsupdate(update_ghs);
        TriggerGraphicsadd(create_ghs);
    }
    return 0;
}

int GraphicsManager::UpdateGraphics(const GraphicsID& id, const float& xstart, const float& ystart, const float& xend, const float& yend, bool invoke_callback)
{
    if (id.IsEmpty())
    {
        return ErrorCode::INVALID_GRAPHICS_ID;
    }

    GraphicsPtr gh;
    ReadGraphics(gh, id);
    if (!gh)
        return ErrorCode::INVALID_GRAPHICS;

    TriggerGraphicschange({ gh });
    UpdateGraphicsValue(gh, xstart, ystart, xend, yend, m_angle);
    if (invoke_callback)
    {
        TriggerGraphicsupdate(gh);
    }
    return 0;
}

int GraphicsManager::UpdateGraphics(const std::vector<GraphicsPtr>& ghs, const MoveDirection& direction, bool invoke_callback)
{
    for (auto& gh : ghs)
    {
        if (!gh || gh->GetId().IsEmpty())
        {
            return ErrorCode::INVALID_GRAPHICS;
        }
        auto state = KeyMoveGraphics(gh, direction);
        if (state != 0)
        {
            return state;
        }
    }
    if (invoke_callback)
    {
        TriggerGraphicsupdate(ghs);
    }
    return 0;
}

int GraphicsManager::UpdateGraphics(const std::vector<GraphicsPtr>& ghs, const GraphicsPtr& parent_gh, bool invoke_callback)
{
    if (!parent_gh)
        return ErrorCode::INVALID_PARAM;

    for (auto& gh : ghs)
    {
        if (!gh || gh->GetId().IsEmpty())
        {
            return ErrorCode::INVALID_GRAPHICS;
        }
        gh->SetParent(parent_gh);
    }
    if (invoke_callback)
    {
        TriggerGraphicsupdate(ghs);
    }
    return 0;
}

int GraphicsManager::UpdateGraphicsMoveAll(const float& xoffset, const float& yOffset, bool invoke_callback)
{
    std::vector<GraphicsPtr> update_gh;
    for (auto& gh : m_graphics)
    {
        if (!gh || gh->IsHaveParent())
            continue;
        update_gh.emplace_back(gh);
    }

    if (invoke_callback)
    {
        TriggerGraphicschange(update_gh);
    }

    for (auto& gh : update_gh)
    {
        gh->SetXY(gh->x() + xoffset, gh->y() + yOffset);
    }
    if (invoke_callback)
    {
        TriggerGraphicsupdate(update_gh);
    }
    return 0;
}

void GraphicsManager::UpdateGraphicsValue(const GraphicsPtr& gh, const float& xstart, const float& ystart,
    const float& xend, const float& yend, const float& angle)
{
    // 这里不需要判定是否id有效
    if (!gh)
        return;
    auto rotate_rect = GetEffectiveRotateRect(xstart, ystart, xend, yend, angle);

    //    float x, y, w, h;

    //x = (xend + xstart) * 0.5f;
    //y = (yend + ystart) * 0.5f;

    //if (angle == 0)
    //{
    //    w = abs(xend - xstart);
    //    h = abs(yend - ystart);
    //}
    //else
    //{
    //    auto radius = A_DEG_TO_RAD(angle);
    //    float cosA = (float)std::cos(radius);
    //    float sinA = (float)std::sin(radius);
    //    /*方向向量*/
    //    cv::Point2f dirVec(cosA, sinA);
    //    /*垂直向量*/
    //    cv::Point2f verticalVec(-sinA, cosA);
    //    auto start_2_end_vector = cv::Point2f(xend - xstart, yend - ystart);
    //    auto w_vector = ProjectVector(start_2_end_vector, dirVec);
    //    auto h_vector = ProjectVector(start_2_end_vector, verticalVec);
    //    w = VectorLength(w_vector);
    //    h = VectorLength(h_vector);
    //}
    gh->SetValue(rotate_rect.center.x, rotate_rect.center.y, rotate_rect.size.width, rotate_rect.size.height, rotate_rect.angle);
}

int GraphicsManager::UpdateGraphicsValue(const std::vector<GraphicsPtr>& ghs, const std::string& layer)
{
    if (!layer.empty() && !IsLayerExist(layer))
    {
        return ErrorCode::INVALID_LAYER;
    }
    bool use_current_layer = layer.empty();
    for (auto& gh : ghs)
    {
        if (!gh) // 这里不需要判定id是否有效
        {
            return ErrorCode::INVALID_GRAPHICS;
        }

        gh->settings.SetLayer(use_current_layer ? m_current_layer : layer);
    }
    return 0;
}

int GraphicsManager::ReadGraphics(std::vector<GraphicsPtr>& ghs_) const
{
    if (m_graphics.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }

    std::vector<GraphicsPtr> ghs;
    ghs.reserve(m_graphics.size());
    for (auto& gh : m_graphics)
    {
        ghs.emplace_back(gh);
        // ghs.emplace_back(std::move(gh->Clone()));  // 克隆一份
    }
    if (ghs.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }
    ghs_.swap(ghs);
    return 0;
}

int GraphicsManager::ReadGraphics(std::vector<GraphicsPtr>& ghs_, const std::string& layer) const
{
    std::vector<GraphicsPtr> ghs;
    for (auto& gh : m_graphics)
    {
        if (gh->settings.GetLayer() != layer)
            continue;
        ghs.emplace_back(gh);
    }
    if (ghs.empty())
    {
        return ErrorCode::NOT_FOUND;
    }
    ghs_.swap(ghs);
    return 0;
}

int GraphicsManager::ReadGraphics(std::vector<GraphicsPtr>& ghs_, const std::vector<GraphicsID>& ids) const
{
    std::vector<GraphicsPtr> ghs;
    for (auto& id : ids)
    {
        auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
            [&id](const GraphicsPtr& gh)
            {
                //std::string gh_name = gh->GetId().GetString();
                //return gh_name == id.GetString();
                return gh->GetId() == id;
            });
        if (it == m_graphics.end())
        {
            ghs.emplace_back(nullptr);
            continue;
        }
        ghs.emplace_back(*it);
    }
    ghs_.swap(ghs);
    return 0;
}

int GraphicsManager::ReadGraphics(GraphicsPtr& gh_, const GraphicsID& id) const
{
    auto it = std::find_if(m_graphics.begin(), m_graphics.end(),
        [&id](const GraphicsPtr& gh) { return gh->GetId() == id; });
    if (it == m_graphics.end())
    {
        return ErrorCode::NOT_FOUND;
    }
    gh_ = *it;
    return 0;
}

int GraphicsManager::ReadGraphics(std::string& json) const
{
    std::ostringstream oss;
    /*序列化的时候需要销毁档案(archive)后才能安全获取数据*/
    {
        cereal::JSONOutputArchive archive(oss);
        archive(m_graphics);
    }
    json = oss.str();
    return 0;
}
int GraphicsManager::ReadGraphicsSelected(std::vector<GraphicsPtr>& ghs) const
{
    std::vector<GraphicsPtr> tghs;

    for (const auto& gh : m_graphics)
    {
        if (!gh)
            continue;
        //if (gh->settings.GetLayer() != m_current_layer)
        //    continue;

        if (gh->settings.GetIsSelected())
        {
            tghs.emplace_back(gh);
        }
    }
    if (tghs.empty())
    {
        return ErrorCode::NOT_FOUND;
    }
    ghs.swap(tghs);
    return 0;
}

int GraphicsManager::ReadSelectedSingleGraphics(GraphicsPtr& gh_) const
{
    std::vector<GraphicsPtr> tghs;

    for (const auto& gh : m_graphics)
    {
        if (!gh)
            continue;
        //if (gh->settings.GetLayer() != m_current_layer)
        //    continue;

        if (gh->settings.GetIsSelected())
        {
            tghs.emplace_back(gh);
        }
    }
    if (tghs.size() != 1)
    {
        return ErrorCode::NOT_FOUND;
    }
    gh_ = tghs[0];
    return 0;
}

int GraphicsManager::DeleteGraphics(bool invoke_callback)
{
    if (m_graphics.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete.swap(m_graphics);
    if (invoke_callback)
    {
        TriggerGraphicsdelete(ghs_delete);
    }
    for (auto& gh : ghs_delete)
    {
        RemoveGraphics(gh);
    }
    return 0;
}
int GraphicsManager::DeleteGraphicsExceptLayer(const std::string& except_layer_, bool invoke_callback_/* = true*/)
{
    if (m_graphics.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete.swap(m_graphics);


    if (invoke_callback_)
    {
        TriggerGraphicsdelete(ghs_delete);
    }
    for (auto& gh : ghs_delete)
    {
        if (gh->settings.GetLayer() == except_layer_)
        {
            m_graphics.emplace_back(gh);
            continue;
        }
        RemoveGraphics(gh);
    }

    return 0;
}
int GraphicsManager::DeleteGraphics(const GraphicsID& id, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete = ExtractIf(m_graphics, [id](const GraphicsPtr& gp)
        {
            return gp->GetId() == id;
        });
    if (ghs_delete.empty())
    {
        return ErrorCode::NOT_FOUND;
    }

    if (invoke_callback)
    {
        TriggerGraphicsdelete(ghs_delete);
    }
    for (auto& gh : ghs_delete)
    {
        RemoveGraphics(gh);
    }
    return 0;
}

int GraphicsManager::DeleteGraphics(const std::vector<GraphicsID>& ids, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete = ExtractIf(m_graphics, [ids](const GraphicsPtr& gp)
        {
            auto it = std::find(ids.begin(), ids.end(), gp->GetId());
            return it != ids.end();
        });
    if (ghs_delete.empty())
    {
        return ErrorCode::NOT_FOUND;
    }

    if (invoke_callback)
    {
        TriggerGraphicsdelete(ghs_delete);
    }
    for (auto& gh : ghs_delete)
    {
        RemoveGraphics(gh);
    }
    return 0;
}

int GraphicsManager::DeleteGraphics(const std::string& layer, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete = ExtractIf(m_graphics, [layer](const GraphicsPtr& gp)
        {
            return gp->settings.GetLayer() == layer;
        });
    if (ghs_delete.empty())
    {
        return ErrorCode::NOT_FOUND;
    }
    if (invoke_callback)
    {
        TriggerGraphicsdelete(ghs_delete);
    }
    for (auto& gh : ghs_delete)
    {
        RemoveGraphics(gh);
    }
    return 0;
}

int GraphicsManager::DeletePadGroups()
{
    return m_pad_manager->CleareAllPadGroup(); //清除掉所有组
}

int GraphicsManager::DeleteSelectedGraphics()
{
    std::vector<GraphicsPtr> ghs_delete;
    ghs_delete = ExtractIf(m_graphics, [](const GraphicsPtr& gp)
        {
            return gp->settings.GetIsSelected();
        });
    if (ghs_delete.empty())
    {
        return ErrorCode::NOT_FOUND;
    }

    if (AddGroupPadsIfNeeded(ghs_delete, "delete"))  // 删除时特殊条件
    {
        for (auto& gh : ghs_delete)
        {
            m_graphics.emplace_back(gh);
        }
        return -1;
    }


    TriggerGraphicsdelete(ghs_delete);
    for (auto& gh : ghs_delete)
    {
        RemoveGraphics(gh);
    }
    return 0;
}

int GraphicsManager::SelectGraphics(const std::vector<GraphicsPtr>& ghs, const bool& selected, bool invoke_callback)
{
    if (ghs.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }

    for (auto& gh : ghs)
    {
        gh->settings.SetIsSelected(selected);
    }

    if (selected && invoke_callback)
    {
        TriggerGraphicsselected(ghs);
    }
    return 0;
}

int GraphicsManager::SelectGraphics(const std::vector<GraphicsID>& ids, const bool& selected, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs;
    ReadGraphics(ghs, ids);

    return SelectGraphics(ghs, selected, invoke_callback);
}

int GraphicsManager::SelectGraphics(const std::string& layer, const bool& selected, bool invoke_callback)
{
    std::vector<GraphicsPtr> ghs;
    ReadGraphics(ghs, layer);

    return SelectGraphics(ghs, selected, invoke_callback);
}

int GraphicsManager::SelectGraphics(const GraphicsPtr& gh, const bool& selected, bool invoke_callback)
{
    if (!gh)
    {
        return ErrorCode::INVALID_GRAPHICS;
    }

    ClearSelected();  //选择其他元件之前 清除其他选中事件 By: HJC 2024/12/26
    gh->settings.SetIsSelected(selected);

    SetCurrentLayer(gh->settings.GetLayer());


    if (selected && invoke_callback)
    {
        TriggerGraphicsselected({ gh });
    }
    return 0;
}

int GraphicsManager::SelectGraphics(const GraphicsID& id, const bool& selected, bool invoke_callback)
{
    GraphicsPtr gh;
    ReadGraphics(gh, id);

    return SelectGraphics(gh, selected, invoke_callback);
}

bool GraphicsManager::TrySelectGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp)
{
    std::vector<GraphicsPtr> ghs;
    auto select_rect = cv::Rect2f(xstart, ystart, xend - xstart, yend - ystart);
    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        for (auto& gh : m_graphics)
        {
            if (!gh) continue;
            //if (gh->settings.GetLayer() != m_current_layer)
            //{
            //    continue;
            //}
            if (!gh->settings.GetIsEditAble()) continue;
            if (IsSelectGraphics(gh, select_rect))
            {
                ghs.push_back(gh);
            }
        }
    }
    if (ghs.empty())
    {
        return false;
    }
    if (!temp)
    {
        SelectGraphics(ghs, true);
    }
    return true;
}

bool GraphicsManager::TrySelectGraphics(const float& x, const float& y, const bool& temp, const bool& is_invoke)
{
    GraphicsPtr gh;
    int type = 0;
    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        type = SelectGraphicsByBoundaryAndContainment(gh, m_graphics, x, y);
    }
    if (!gh)
    {
        return false;
    }

    auto it = m_layer_configs.find(gh->settings.GetLayer());
    if (it != m_layer_configs.end() && it->second->_is_optional == Selectable::None) {
        return false;
    }

    int cursor_type = -1;
    switch (type)
    {
    case 1:
        cursor_type = static_cast<int>(CustomCursorType::SelectAlternate);
        break;
    case 2:
        cursor_type = static_cast<int>(CustomCursorType::SelectNormal);
        break;
    }
    if (cursor_type > 0)
    {
        TriggerCursorchange(cursor_type);
    }

    if (!temp)
    {
        return SelectGraphics({ gh }, is_invoke) == 0;
    }
    return true;
}

bool GraphicsManager::TrySelectGraphics(const std::vector<Vec2>& paths, const bool& temp)
{
    if (paths.size() < 3)
        return false;

    std::vector<GraphicsPtr> ghs;
    {
        std::vector<cv::Point2f> contours;
        for (const auto& p : paths)
        {
            contours.emplace_back(p.x, p.y);
        }
        std::shared_lock<std::shared_mutex> lck(mutex);
        for (auto& gh : m_graphics)
        {
            if (!gh)
                continue;
            //if (gh->settings.GetLayer() != m_current_layer)
            //    continue;
            if (!gh->settings.GetIsEditAble()) continue;

            if (IsPointInPolygon(gh->GetCenter(), contours))
            {
                ghs.emplace_back(gh);
            }
        }
    }
    if (ghs.empty())
    {
        return false;
    }
    if (!temp)
    {
        return SelectGraphics(ghs, true) == 0;
    }
    return true;
}

bool GraphicsManager::IsSelectGraphics(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const
{
    if (gh->w() <= 0 || gh->h() <= 0)
        return false;
    switch (m_selection_mode)
    {
    case SelectGraphicsMode::FullContainment:
        return IsSelectedByFullContainment(gh, select_rect);
    case SelectGraphicsMode::IntersectingArea:
        return IsSelectedByIntersectingArea(gh, select_rect);
    case SelectGraphicsMode::CenterPoint:
        return IsSelectedByCenterPoint(gh, select_rect);
    }
    return false;
}

void GraphicsManager::ClearSelected()
{
    //printInfo("");
    for (auto& gh : m_graphics)
    {
        if (!gh)
            continue;
        gh->settings.SetIsSelected(false);
    }
}

bool GraphicsManager::IsHaveSelected()
{
    for (const auto& gh : m_graphics)
    {
        if (!gh)
            continue;
        //if (gh->settings.GetLayer() != m_current_layer)
        //    continue;

        if (gh->settings.GetIsSelected())
        {
            return true;
        }
    }
    return false;
}

void GraphicsManager::ResponseEvent(const MouseEventValue& value)
{
    std::vector<GraphicsPtr> ghs;
    if (auto state = ReadGraphicsSelected(ghs); state != 0)
    {
        return;
    }

    for (auto& gh : ghs)
    {
        if (!gh->settings.GetIsEditAble())
            continue;
        gh->ResponseEvent(value);
    }

    TriggerGraphicsupdate(ghs);
    //if (invoke_callback)
    //{

    //    TriggerGraphicsselected(ghs);
    //}
    //GraphicsPtr gh;
    //ReadSelectedSingleGraphics(gh);
    //if (!gh)
    //    return;
    //if (!gh->settings.GetIsEditAble())
    //    return;
    //gh->ResponseEvent(value);
}

int GraphicsManager::TryResponseGraphics(const float& x, const float& y, const bool& temp)
{
    (void)temp;
    std::shared_ptr<ControlPointAbstract> controlpoint = nullptr;

    const auto max_limit = response_max_limit * (1.0 / renderer->GetZoom());
    TryResponseEventParam param(x, y, max_limit);

    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        for (auto& gh : m_graphics)
        {
            if (!gh->settings.GetIsSelected())
                continue;
            //if (gh->settings.GetLayer() != m_current_layer)
            //    continue;
            if (!gh->settings.GetIsEditAble())
                continue;
            if (gh->TryResponseControlPoint(controlpoint, param) == 0)
            {
                break;
            }
        }
    }
    int cursor_shape = 1;
    ControlAttributes attributes;
    if (controlpoint)
    {
        cursor_shape = controlpoint->cpd.cursor_shape;
        attributes = controlpoint->attributes;
    }
    SetResponseAttributes(attributes);
    TriggerCursorchange(cursor_shape);

    if (cursor_shape > 1)
    {
        return 0;
    }
    return 1;
}

int GraphicsManager::ResponseGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp)
{
    const auto& attr = GetResponseAttributes();
    if (attr.type < 0)
    {
        return -1;
    }

    std::vector<GraphicsPtr> ghs;
    if (auto state = ReadGraphicsSelected(ghs); state != 0)
    {
        return state;
    }

    if (!temp)
    {
        TriggerGraphicschange(ghs);
    }

    ResponseEventParam param(xstart, ystart, xend, yend, attr, temp);

    for (auto& gh : ghs)
    {
        if (!gh->settings.GetIsEditAble())
            continue;
        gh->ResponseControlPoint(param);
    }

    if (!temp)
    {
        AddGroupPadsIfNeeded(ghs);

        TriggerGraphicsupdate(ghs);
    }
    return 0;
}

int GraphicsManager::ResponseParentGraphics(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& temp)
{
    const auto& attr = GetResponseAttributes();
    if (attr.type < 0)
    {
        return -1;
    }

    std::vector<GraphicsPtr> ghs;
    if (auto state = ReadGraphicsSelected(ghs); state != 0)
    {
        return state;
    }

    if (!temp)
    {
        TriggerGraphicschange(ghs);
    }
    if (static_cast<ControlPointType>(attr.type) != ControlPointType::MOVE_POINT)  //只能在移动点更改
    {
        return -1;
    }
    ResponseEventParam param(xstart, ystart, xend, yend, attr, temp);
    GraphicsPtrVec update_graphics;
    for (auto& gh : ghs)
    {
        auto parent_attr_ptr = gh->GetParent();
        GraphicsID current_graphics_id;
        GraphicsPtr parent_gh_ptr = nullptr;
        GraphicsPtr object_gh_ptr = gh;
        while (parent_attr_ptr)
        {
            current_graphics_id = parent_attr_ptr->GetId();
            parent_attr_ptr = parent_attr_ptr->GetParent();  // 继续向上找
        }
        ReadGraphics(parent_gh_ptr, current_graphics_id);
        if (parent_gh_ptr)
        {
            object_gh_ptr = parent_gh_ptr;
        }
        if (!object_gh_ptr->settings.GetIsEditAble())
            continue;
        object_gh_ptr->ResponseControlPoint(param);
        update_graphics.emplace_back(object_gh_ptr);
    }

    if (!temp)
    {
        AddGroupPadsIfNeeded(update_graphics);

        TriggerGraphicsupdate(update_graphics);
    }
    return 0;
}

const ControlAttributes& GraphicsManager::GetResponseAttributes() const
{
    return m_attr_controlpoint;
}

void GraphicsManager::SetResponseAttributes(const ControlAttributes& value)
{
    m_attr_controlpoint = value;
}

int GraphicsManager::CopyGraphics(const std::vector<GraphicsPtr>& ghs)
{
    if (ghs.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }
    m_copy_graphics = ghs;
    return 0;
}

int GraphicsManager::PasteGraphics(const float& x, const float& y)
{
    if (m_copy_graphics.empty())
    {
        return ErrorCode::CONTAINER_IS_NULL;
    }
    auto first_gh = m_copy_graphics.front();
    auto x_offset = x - first_gh->x();
    auto y_offset = y - first_gh->y();
    std::vector<GraphicsPtr> new_ghs;
    for (auto& gh : m_copy_graphics)
    {
        if (!gh->settings.GetIsCopy())
        {
            continue;
        }
        auto clone_ghs = GraphicsClone(gh.get());
        //SetAutoGenerateID(clone_ghs);
        for (auto& clone_gh : clone_ghs)
        {
            clone_gh->SetXY(x_offset + clone_gh->x(), y_offset + clone_gh->y());
            new_ghs.emplace_back(clone_gh);
        }
    }
    SetAutoGenerateIDs(new_ghs);
    UpdateGraphics(new_ghs, "", true);
    // TriggerGraphicsadd(new_ghs);
    return 0;
}

int GraphicsManager::CopyGraphicsTo(std::shared_ptr<GraphicsAbstract>& new_gh, const std::shared_ptr<GraphicsAbstract>& gh, float x, float y)
{

    // (void)new_gh;
    // (void)gh;
    // (void)x;
    // (void)y;

    auto x_offset = x - gh->x();
    auto y_offset = y - gh->y();
    std::vector<GraphicsPtr> new_ghs;
    auto clone_ghs = GraphicsClone(gh.get());
    for (auto& clone_gh : clone_ghs)
    {
        clone_gh->SetXY(x_offset + clone_gh->x(), y_offset + clone_gh->y());
        new_ghs.emplace_back(clone_gh);
    }
    if (new_ghs.empty())
    {
        new_gh = nullptr;
        return ErrorCode::CONTAINER_IS_NULL;
    }
    new_gh = new_ghs.front();
    UpdateGraphics(new_ghs, "", true);

    return 0;
}

int GraphicsManager::BeginCreate(GraphicsPtr gh)
{
    m_add_graphics.swap(gh);
    return 0;
}

GraphicsPtr GraphicsManager::GetCreate()
{
    return m_add_graphics;
}

int GraphicsManager::CommitCreate()
{
    UpdateGraphics(m_add_graphics);
    CancelCreate();
    return 0;
}

int GraphicsManager::CancelCreate()
{
    m_add_graphics.reset();
    return 0;
}

int GraphicsManager::UpdateTemporary(const float& xstart, const float& ystart, const float& xend, const float& yend, bool not_rotate)
{
    auto angle = not_rotate ? 0.f : m_angle;
    GraphicsPtr gh = std::make_shared<RectGraphics>();
    UpdateGraphicsValue(gh, xstart, ystart, xend, yend, angle);
    if (!gh)
        return ErrorCode::INVALID_GRAPHICS;
    gh->settings.SetLayer(DEFAULT_GRAPHICS_TEMP_LAYER);
    m_add_graphics.swap(gh);
    return 0;
}

int GraphicsManager::ClearTemporary()
{
    return CancelCreate();
}

int GraphicsManager::RecordParentGraphics(const GraphicsPtr& gh)
{
    if (!gh)
        return ErrorCode::INVALID_GRAPHICS;
    m_parent_graphics_id = gh->GetId();
    return 0;
}

int GraphicsManager::GetParentGraphics(GraphicsPtr& gh)
{
    return ReadGraphics(gh, m_parent_graphics_id);
}

//std::shared_ptr<LayerConfig> GraphicsManager::CreateLayerConfig(const std::string& layer, int r_, int g_, int b_, int a_, int width, bool render)
//{
//}

std::shared_ptr<LayerConfig> GraphicsManager::CreateLayerConfig(const std::string& layer, RenderConfig display_style, RenderConfig focus_style)
{
    if (IsLayerExist(layer))
        return nullptr;
    if (layer.empty())
        return nullptr;

    auto layer_config = std::make_shared<LayerConfig>(display_style, focus_style);
    m_layer_configs.emplace(layer, layer_config);
    return layer_config;

    return std::shared_ptr<LayerConfig>();
}

void GraphicsManager::UpdateLayerConfig(std::shared_ptr<LayerConfig> config, const std::string& layer)
{
    if (!config)
        return;
    if (layer.empty())
        return;

    auto it = m_layer_configs.find(layer);
    if (it != m_layer_configs.end())
    {
        it->second = config;
    }
    else
    {
        m_layer_configs.emplace(layer, config);
    }
}

std::weak_ptr<LayerConfig> GraphicsManager::ReadLayerConfig(const std::string& layer)
{
    auto it = m_layer_configs.find(layer);
    if (it == m_layer_configs.end())
    {
        return {};
    }
    return it->second;
}

const LayerConfigMap& GraphicsManager::ReadLayerConfig()
{
    return m_layer_configs;
}

void GraphicsManager::DeleteLayerConfig(const std::string& layer)
{
    auto it = m_layer_configs.find(layer);
    if (it != m_layer_configs.end())
    {
        m_layer_configs.erase(it);
    }
}

void GraphicsManager::DeleteLayerConfig()
{
    m_layer_configs.clear();
    AddDefaultLayerConfig();
}

int GraphicsManager::SetCurrentLayer(const std::string& layer)
{
    if (!IsLayerExist(layer))
    {
        return 1;
    }
    if (m_current_layer == layer)
    {
        return 0;
    }
    m_current_layer = layer;
    //ClearSelected();
    return 0;
}

bool GraphicsManager::IsLayerExist(const std::string& layer) const
{
    return m_layer_configs.find(layer) != m_layer_configs.end();
}

void GraphicsManager::SetDrawAngle(float val)
{
    if (isnan(val))
        val = 0;
    val = std::fmod(val + 360.0f, 360.0f);
    m_angle = val;
}

void GraphicsManager::SetCallbackGraphicsdraw(std::function<void()> callback)
{
    callback_graphicsdraw += callback;
}

void GraphicsManager::SetCallbackGraphicsupdate(std::function<void(const std::vector<GraphicsPtr>&, bool)> callback)
{
    callback_graphicsupdate += callback;
}

void GraphicsManager::SetCallbackGraphicschange(std::function<void(const std::vector<GraphicsPtr>&)> callback)
{
    callback_graphicschange += callback;
}

void GraphicsManager::SetCallbackGraphicsadd(std::function<void(const std::vector<GraphicsPtr>&)> callback)
{
    callback_graphicsadd += callback;
}

void GraphicsManager::SetCallbackGraphicsdelete(std::function<void(const std::vector<GraphicsPtr>&)> callback)
{
    callback_graphicsdelete += callback;
}

void GraphicsManager::SetCallbackCursorchange(std::function<void(int)> callback)
{
    callback_cursorchange += callback;
}

void GraphicsManager::SetCallbackGraphicsselected(std::function<void(const std::vector<GraphicsPtr>&)> callback)
{
    callback_graphicsselected += callback;
}

void GraphicsManager::TriggerGraphicsdraw()
{
    //printInfo("");
    callback_graphicsdraw();
}

void GraphicsManager::TriggerGraphicsupdate(const std::vector<GraphicsPtr>& ghs, bool is_update_graphics_)
{
    if (ghs.empty())
        return;

    //TODO 更新时同步更新所有子图形 目前还不够智能
    std::vector<GraphicsPtr> tghs;
    for (auto& gh : ghs)
    {
        tghs.push_back(gh);
        auto child = gh->GetChildren();
        if (child.empty())
        {
            continue;
        }
        for (auto& c : child)
        {
            auto cgh = std::dynamic_pointer_cast<GraphicsAbstract>(c.lock());
            if (!cgh) continue;

            if (std::find_if(tghs.begin(), tghs.end(),
                [&cgh](const GraphicsPtr& existingVal)
                {
                    return existingVal == cgh;
                }) == tghs.end())
            {
                tghs.push_back(cgh);
            }
        }
    }
    callback_graphicsupdate(tghs, is_update_graphics_);
}

void GraphicsManager::TriggerGraphicsupdate(const GraphicsPtr& gh)
{
    TriggerGraphicsupdate(std::vector<GraphicsPtr>{ gh });
    //callback_graphicsupdate({ gh });
}

void GraphicsManager::TriggerCursorchange(int cursor_type)
{
    callback_cursorchange(cursor_type);
}

//TODO:待优化 HJC 这个应该放到外面做，有时间优化吧
void GraphicsManager::SetAutoGenerateIDs(const GraphicsPtrVec& gh_ptrs_)
{
    if (gh_ptrs_.empty())
        return;
    std::vector<std::string> graphics_id_names;
    for (const auto& graphic : m_graphics)
    {
        graphics_id_names.push_back(graphic->GetId().GetString());
    }
    for (const auto& gh_ptr : gh_ptrs_)
    {
        std::string current_name = gh_ptr->GetId().GetString();
        if (current_name.empty()) return;  // 防止空字符串

        // 拆分前缀和后缀
        size_t split_pos = current_name.find_last_of('_');
        std::string prefix = current_name;
        std::string suffix;

        if (split_pos != std::string::npos && split_pos + 1 < current_name.size())
        {
            prefix = current_name.substr(0, split_pos);
            suffix = current_name.substr(split_pos); // 包含 '_'
        }

        std::regex pattern(R"(^(.*?)(\d+)$)");
        std::smatch match;

        std::string base_prefix = prefix;
        int base_number = 0;

        if (std::regex_match(prefix, match, pattern))
        {
            base_prefix = match[1];
            try
            {
                base_number = std::stoi(match[2]);
            }
            catch (...)
            {
                base_number = 0;
            }
        }

        int max_number = base_number;

        for (const auto& graphic_name : graphics_id_names)
        {
            if (graphic_name.empty()) continue;

            auto temp_prefix = render::Tools::GetPrefixString(graphic_name);
            if (std::regex_match(temp_prefix, match, pattern) && match[1] == base_prefix)
            {
                try
                {
                    int number = std::stoi(match[2]);
                    max_number = std::max(max_number, number);
                }
                catch (...) { continue; }
            }
            else if (temp_prefix == base_prefix)
            {
                max_number = std::max(max_number, 0);
            }
        }

        std::string new_prefix = base_prefix + std::to_string(max_number + 1);
        std::string new_id = new_prefix + suffix;

        gh_ptr->SetId(new_id);
        graphics_id_names.push_back(new_id);
    }
}


void GraphicsManager::TriggerGraphicsadd(const std::vector<GraphicsPtr>& ghs)
{
    if (ghs.empty())
        return;
    //printInfo("");
    callback_graphicsadd(ghs);
}

void GraphicsManager::TriggerGraphicschange(const std::vector<GraphicsPtr>& ghs)
{
    if (ghs.empty())
        return;
    callback_graphicschange(ghs);
}

void GraphicsManager::TriggerGraphicsadd(const GraphicsPtr& gh)
{
    TriggerGraphicsadd(std::vector<GraphicsPtr>{ gh });
}

void GraphicsManager::TriggerGraphicsdelete(const std::vector<GraphicsPtr>& ghs)
{
    if (ghs.empty())
        return;
    callback_graphicsdelete(ghs);
}

void GraphicsManager::TriggerGraphicsselected(const std::vector<GraphicsPtr>& ghs)
{
    if (ghs.empty())
        return;
    callback_graphicsselected(ghs);
}

std::unordered_map<GraphicsID, GraphicsPtr, GraphicsIDHash, GraphicsIDEqual> GraphicsManager::CreateGraphicsTempHash(
    const std::vector<GraphicsPtr>& vec) const
{
    std::unordered_map<GraphicsID, GraphicsPtr, GraphicsIDHash, GraphicsIDEqual> map_gh;
    map_gh.reserve(vec.size());
    for (const auto& elem : vec)
    {
        map_gh.emplace(elem->GetId(), elem);
    }
    return map_gh;
}

void GraphicsManager::SortGraphics()
{
    std::sort(m_graphics.begin(), m_graphics.end(),
        [](const GraphicsPtr& a, const GraphicsPtr& b)
        {
            return static_cast<int>(a->GetFlag()) > static_cast<int>(b->GetFlag());
        });
}

void GraphicsManager::RemoveGraphics(GraphicsPtr& gh)
{
    for (auto& childptr : gh->GetChildren())
    {
        auto child = childptr.lock();
        if (child)
            child->UnsetParent();
    }
    gh.reset();
}

std::vector<GraphicsPtr> GraphicsManager::GraphicsClone(const GraphicsAbstract* other)
{
    if (other == nullptr)
        return {};

    GraphicsPtr gh = other->Clone();

    /*gh->CreateId()*/;
    gh->ClearChildren();
    std::vector<GraphicsPtr> new_ghs{ gh };
    //! 添加复制图形时是否要复制子项内容 by zhangyuyu 2025.1.7
    if (false)
    {
        for (const auto& childptr : other->GetChildren())
        {
            auto child = childptr.lock();
            if (!child)
                continue;
            auto tchildren = GraphicsClone(dynamic_cast<GraphicsAbstract*>(child.get()));
            for (auto& tchild : tchildren)
            {
                tchild->SetParent(gh);
            }
            new_ghs.insert(new_ghs.end(), tchildren.begin(), tchildren.end());
        }
    }

    return new_ghs;
}

int GraphicsManager::KeyMoveGraphics(const GraphicsPtr& gh, const MoveDirection& direction)
{
    static const int offset = 1; ///< 图形单位移动量
    if (!gh)
        return ErrorCode::INVALID_GRAPHICS;
    if (direction & MoveDirection::LEFT)
    {
        gh->SetX(gh->x() - offset);
    }
    if (direction & MoveDirection::RIGHT)
    {
        gh->SetX(gh->x() + offset);
    }
    if (direction & MoveDirection::UP)
    {
        gh->SetY(gh->y() - offset);
    }
    if (direction & MoveDirection::DOWN)
    {
        gh->SetY(gh->y() + offset);
    }
    return 0;
}

bool GraphicsManager::IsSelectedByFullContainment(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const
{
    return isRotatedRectInside(gh->GetBoundingbox(), select_rect);
}

bool GraphicsManager::IsSelectedByIntersectingArea(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const
{
    return isRectIntersecteWithRotatedRect(select_rect, gh->GetBoundingbox());
}

bool GraphicsManager::IsSelectedByCenterPoint(const GraphicsPtr& gh, const cv::Rect2f& select_rect) const
{
    return isPointInRect(select_rect, gh->GetCenter());
}

int GraphicsManager::SelectGraphicsByBoundaryAndContainment(GraphicsPtr& gh,
    const std::vector<GraphicsPtr>& ghs, float x, float y) const
{
    cv::Point2f pos(x, y);
    /*
    判定逻辑为:
     1. 距离边界最近且小于阈值的选中
     2. 若不存在满足1的, 则选中第一个内部对象
    */
    std::weak_ptr<GraphicsAbstract> gborder, ginside;
    //int current_selected_overlap_area = INT_MIN; // 当前选中的面积
    std::map<int, std::weak_ptr<GraphicsAbstract>> area_to_graphics; // 面积到目标的映射
    const double max_limit = 8.0; // 与边界的最大阈值
    double zoom = renderer->GetZoom();
    double min_dis = max_limit / zoom;

    for (const auto& gp : ghs)
    {
        if (!gp || !gp->settings.GetIsEditAble()) continue;

        auto boundingbox = gp->GetBoundingbox();
        if (boundingbox.size.area() <= 0) continue;

        std::vector<cv::Point2f> contour;
        boundingbox.points(contour);

        // 计算点到轮廓的距离
        double dis = std::abs(getDisPointToContour(pos, contour));
        bool is_inside = isPointInRotatedRect(pos, boundingbox);

        // 边界判定：更新最小距离
        if (dis < min_dis)
        {
            min_dis = dis;
            gborder = gp;
        }

        //内部点判定：记录重叠面积的图形
        auto layer = m_layer_configs.find(gp->settings.GetLayer());
        if (is_inside && layer != m_layer_configs.end() && layer->second->_is_optional == Selectable::Any)
        {
            int area = static_cast<int>(boundingbox.size.width * boundingbox.size.height);
            area_to_graphics[area] = gp; // 按面积升序存储

            //if (gp->settings.GetIsSelected())
            //{
            //    current_selected_overlap_area = area; // 更新当前选中的面积
            //}
        }
    }

    // 优先选择面积大于当前选中目标的图形
    //for (const auto& [area, graphic_weak] : area_to_graphics)
    //{
    //if (!area_to_graphics.empty())
    //{
    //    auto graphic = area_to_graphics.begin()->second.lock();
    //    if (!graphic)
    //    {

    //    };
    //    auto it = m_layer_configs.find(graphic->settings.GetLayer());
    //    if (it != m_layer_configs.end() && it->second->_is_optional != Selectable::Any)
    //    {
    //        continue;
    //    }
    //    if (current_selected_overlap_area != INT_MIN && area > current_selected_overlap_area)
    //    {
    //        gborder = graphic; // 选择比当前目标面积大的一个
    //    }

    //}
    //}

// 如果没有边界目标，选择面积最小的内部目标
    if (gborder.expired() && !area_to_graphics.empty())
    {
        ginside = area_to_graphics.begin()->second;
    }

    // 返回最终目标：优先边界，其次内部
    if (!gborder.expired())
    {
        gh = gborder.lock();
        return 1;
    }
    if (!ginside.expired())
    {
        gh = ginside.lock();
        return 2;
    }

    return 0;
}

void GraphicsManager::AddDefaultLayerConfig()
{
    CreateLayerConfig(DEFAULT_GRAPHICS_TEMP_LAYER, { 0, 120, 215, 55,1,true }, { 0, 120, 215, 55,1,true });
    CreateLayerConfig(DEFAULT_GRAPHICS_CONTROL_LAYER, { 255, 255, 255, 255, 13, true }, { 255, 255, 255, 255, 13, true });
    CreateLayerConfig(DEFAULT_GRAPHICS_LAYER, { 29, 105, 29, 255, 1, true }, { 29, 105, 29, 255, 1, true });
}

int GraphicsManager::AddGroupPadsIfNeeded(std::vector<GraphicsPtr>& ghs, const std::string& command_)
{
    std::vector<GraphicsPtr> group_pads;

    // 遍历所有GraphicsGroup元素
    for (auto& gh : ghs)
    {
        //TODO: 需要将层级放在内部处理 HJC 2025/2/27
        if (gh->settings.GetLayer() == "pad")
        {
            // 尝试转换为PadGraphicsGroup
            auto pad_gh_group_ptr = std::dynamic_pointer_cast<PadGraphicsGroup>(gh);
            if (!pad_gh_group_ptr)
            {
                continue;
            }
            if (pad_gh_group_ptr->GetGroupType() == PadGraphicsGroup::PadGroupType::MIRROR)  //只有对称才删除
            {
                group_pads = pad_gh_group_ptr->GetExceptSelectedPadGroups();
                // 获取该组中需要插入的Pad对象
                //group_pads = pad_gh_group_ptr->GetExceptSelectedPads();
            }
            else if (pad_gh_group_ptr->GetGroupType() == PadGraphicsGroup::PadGroupType::ARRAY)
            {
                if (command_ == "delete")
                {
                    if (pad_gh_group_ptr->_sub_graphics.size() >= 2)
                    {
                        if (pad_gh_group_ptr->IsSelectedLastPad()) /**< 最后一个禁止删除 TODO：待优化 提示是否删除*/
                        {
                            return -1; // 不删除数据
                        }

                        if (pad_gh_group_ptr->_sub_graphics.size() == 2)  // 供删除使用
                        {
                            group_pads = pad_gh_group_ptr->GetExceptSelectedPads();
                        }

                        pad_gh_group_ptr->RemoveSelectSubGraphics();
                        auto update_ghs = pad_gh_group_ptr->GetExceptSelectedPads();
                        TriggerGraphicsupdate(update_ghs, false);
                    }

                }
                else if (command_ == "update")
                {
                    if (pad_gh_group_ptr->_sub_graphics.size() >= 2)  //供更新使用
                    {
                        pad_gh_group_ptr->DoAlign();
                        group_pads = pad_gh_group_ptr->GetExceptSelectedPads();
                    }
                }
            }
        }
    }

    // 如果找到了需要添加的group_pads，则插入到ghs中
    if (!group_pads.empty())
    {
        ghs.insert(ghs.end(), group_pads.begin(), group_pads.end());
    }
    return 0;
}
void GraphicsManager::TestPrintGraphicsInfo()
{
    /*for (auto& graphics : m_graphics)
    {
        auto rect_gh = std::dynamic_pointer_cast<RectGraphics>(graphics);

        printInfo(std::stringstream()
            << " graphics_id:" << graphics->GetId().GetString()
            << " position:[" << graphics->LocalX() << "," << graphics->LocalY() << "]" << "\n"
            << " Size:[" << graphics->w() << "," << graphics->h() << "]\n"
            << " Color:[" << graphics->settings.GetStyle().a << "," << graphics->settings.GetStyle().g << "," << graphics->settings.GetStyle().b << "]"
        );

    }*/
}

