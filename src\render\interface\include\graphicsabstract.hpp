﻿/*********************************************************************
 * @brief  声明图形基类.
 *
 * @file   graphicsabstract.hpp
 *
 * @date   2024.05.30
 * <AUTHOR>
*********************************************************************/
#pragma once
#ifndef GRAPHICS_ABSTRACT_H
#define GRAPHICS_ABSTRACT_H

#include "graphicsapi.hpp"
#include "graphicsattributes.hpp"    // GraphicsAttributes
#include "graphicsconstants.hpp"     // GraphicsFlag
#include "controlpointconstants.hpp" // ControlAttributes ControlPoint
// #include "graphicseventparam.hpp"
//#include "graphicsid.h" // GraphicsID

#include <string>
#include <vector>
#include <unordered_map>

// struct ControlAttributes;
class ControlPointAbstract;
class ResponseEventParam;
class TryResponseEventParam;
class Renderer;
class Painter;
struct MouseEventValue;
/**
 *   渲染配置.
 */
struct GRAPHICS_API RenderConfig
{
    bool is_render;        ///< 是否渲染
    int r;                 ///< 红色 0-255
    int g;                 ///< 绿色 0-255
    int b;                 ///< 蓝色 0-255
    int a;                 ///< 透明度 0-255
    int line_width;        ///< 线宽，-1表示为填充
    float true_line_width; ///< 线宽，实际值 
    float scale;           ///< 当前鼠标刻度
    RenderConfig()
        : is_render(false), r(0), g(0), b(0), a(0), line_width(1), true_line_width(1.0f), scale(1.0)
    {
    }
    RenderConfig(int r_, int g_, int b_, int a_, int line_width_, bool is_render_ = true)
        : is_render(is_render_), r(r_), g(g_), b(b_), a(a_), line_width(line_width_), true_line_width((float)line_width_), scale(1.0)
    {
        limitcolorinregion(r);
        limitcolorinregion(g);
        limitcolorinregion(b);
        limitcolorinregion(a);
    }
    RenderConfig(const RenderConfig& other)
        : is_render(other.is_render), r(other.r), g(other.g), b(other.b), a(other.a),
        line_width(other.line_width), true_line_width(other.true_line_width), scale(other.scale)
    {
    }
    RenderConfig& operator=(const RenderConfig& other)
    {
        if (this != &other) {
            is_render = other.is_render;
            r = other.r;
            g = other.g;
            b = other.b;
            a = other.a;
            line_width = other.line_width;
            true_line_width = other.true_line_width;
            scale = other.scale;
        }
        return *this;
    }
    inline void limitcolorinregion(int& val)
    {
        if (val > 255)
            val = 255;
        if (val < 0)
            val = 0;
    }
    void SetLineScale(float scale_)
    {
        if (scale_ <= 0)
            return;
        this->scale = scale_;
        true_line_width = line_width * scale_;
    }
    float GetLineScale()
    {
        return scale;
    }
};

using RenderConfigPtr = std::shared_ptr<RenderConfig>;

enum class Selectable :int
{
    Any = 0,       /**< 任何地方都可以选取*/
    Center = 1,    /**< 只能中心选取*/
    Boundary = 2,  /**< 只能进行边界进行选取*/
    None = 3,      /**< 无法选取 */
};

/**
 * @brief 图层配置
 */
class GRAPHICS_API LayerConfig/* :public RenderConfig*/
{
public:
    explicit LayerConfig()
        :_display_style(), _focus_style()
    {
    }
    //explicit LayerConfig(int display_r_, int  display_g_, int  display_b_, int  display_a_,
    //    int focus_r_, int focus_g_, int focus_b_, int focus_a_,
    //    int display_line_width_ = 1, int forcus_line_width_ = 1, bool  render_ = true)
    //    :_display_style(display_r_, display_g_, display_b_, display_a_, display_line_width_, render_),
    //    _focus_style(focus_r_, focus_g_, focus_b_, focus_a_, forcus_line_width_, render_)
    //{

    //}
    explicit LayerConfig(RenderConfig display_render_config_,
        RenderConfig focus_render_config_,
        const Selectable& is_optional_ = Selectable::Any)
        :_display_style(display_render_config_),
        _focus_style(focus_render_config_),
        _is_optional(is_optional_)
    {
    }

    void SetLineScale(float scale_)
    {
        _display_style.SetLineScale(scale_);
        _focus_style.SetLineScale(scale_ * 1.8f);  //选中时比较粗
    }
    RenderConfig _display_style; /**<  显示样式 */
    RenderConfig _focus_style;   /**<  选中样式 */
    Selectable _is_optional;      /**< 是否可选*/
};

/**
 * @brief 图形绘制设置
 */
class GRAPHICS_API DrawSettings /*: protected RenderConfig*/
{
public:
    DrawSettings()
    {
    }

    inline const std::string& GetLayer() const { return layer; }
    inline void SetLayer(const std::string& val) { layer = val; }

    inline bool GetIsSelected() const { return is_selected; }
    inline void SetIsSelected(bool val) { is_selected = val; }

    inline bool GetIsEditAble() const { return is_editable; }
    inline void SetIsEditAble(bool val) { is_editable = val; }

    inline bool GetIsCopy() const { return is_copy; }
    inline void SetIsCopy(bool val) { is_copy = val; }

    inline bool GetIsFill() const { return is_fill; }
    inline void SetIsFill(bool val) { is_fill = val; }

    inline bool GetIsFlag() const
    {
        return is_flag;
    }
    inline void SetIsFlag(bool val)
    {
        is_flag = val;
    }

    /**< 图形大小颜色自定义 */
    inline void SetPrivateStyle(RenderConfig display_style_, RenderConfig focus_style_)
    {
        is_private_style = true;/**< 默认开启私有样式 */
        _display_style = display_style_;
        _focus_style = focus_style_;
    }
    inline RenderConfig GetStyle()const
    {
        if (is_selected)
        {
            return _focus_style;
        }
        else
        {
            return _display_style;

        }
    }

    inline void SetRealWidthOfLine(float scale_)
    {
        this->_focus_style.SetLineScale(scale_ * 1.8f);
        this->_display_style.SetLineScale(scale_ /** 1.8f*/);
    }

    /** <是否使用私有样式 */
    inline bool GetIsPrivateStyle()const
    {
        return is_private_style;
    }
    inline void SetIsPrivateStyle(bool is_private_style_)
    {
        is_private_style = is_private_style_;
    }

private:
    bool is_selected = false;                ///< 选中状态
    bool is_editable = true;                 ///< 是否可编辑
    bool is_fill = false;                    ///< 是否需要填充
    bool is_flag = false;                    ///< 是否绘制标记
    bool is_private_style = false;            ///<是否有私有属性，如果有就用私有属性，如果没有则使用layer 属性
    bool is_copy = false;                     ///< 是否可以复制
    std::string layer = "default";           ///< 图形所在层
    RenderConfig _display_style; /**<  显示样式 */
    RenderConfig _focus_style;   /**<  选中样式 */
};

using LayerConfigPtr = std::shared_ptr<LayerConfig>;
using LayerConfigMap = std::unordered_map<std::string, LayerConfigPtr>;

/**
 * @brief 图形基类
 */
class GRAPHICS_API GraphicsAbstract : public GraphicsAttributes<float>
{
public:
    enum ErrorCode
    {
        OK = 0,
        GRAPHICS_NULL,
        UNKNOWN_TYPE
    };

    GraphicsAbstract() : GraphicsAttributes(), settings() {}
    GraphicsAbstract(const std::string& id_) : GraphicsAttributes(), settings() { SetId(id_); }
    GraphicsAbstract(float x, float y, float w, float h, float a,
        std::shared_ptr<GraphicsAttributes> parent_ = nullptr)
        : GraphicsAttributes(x, y, w, h, a), settings()
    {
        SetParent(parent_);
    }
    GraphicsAbstract(float x, float y, float w, float h, float a, const std::string& id_,
        std::shared_ptr<GraphicsAttributes> parent_ = nullptr)
        : GraphicsAttributes(x, y, w, h, a, id_), settings()
    {
        SetParent(parent_);
    }
    GraphicsAbstract(const GraphicsAbstract& other)
        : GraphicsAttributes(other)
        , settings(other.settings)
    {
        // settings.SetIsSelected(false);
    }

    virtual ~GraphicsAbstract() {}

    // 浅拷贝
    GraphicsAbstract& operator=(const GraphicsAbstract& other)
    {
        if (this != &other)
        {
            GraphicsAttributes<float>::operator=(other);
            settings = other.settings;
            // settings.SetIsSelected(false);
        }
        return *this;
    }

    /**
     * @brief 获取类型识别标签
     */
    virtual GraphicsFlag GetFlag() const = 0;

    /**
     * @brief 绘制
     * @param r 渲染器
     * @param config 绘制参数
     */
    virtual void Draw(Renderer* r, const LayerConfig* config) = 0;

    /**
     * @brief 绘制（带画笔）
     * @param r 渲染器
     * @param p 画笔
     * @param config 绘制参数
     */
    virtual void Draw(Renderer* r, Painter* p, const LayerConfig* config) = 0;

    /**
     * @brief 更新
     * @note  父图形更新时,子图形仅进行更新标记,实际更新延迟到渲染时
     */
    virtual void Update() {}

    virtual void SetColorAndThickness(const LayerConfig* config, Color& c, float& thickness)
    {
        if (settings.GetIsPrivateStyle())
        {
            settings.SetRealWidthOfLine(config->_display_style.scale);
            auto private_style = settings.GetStyle();
            c = { private_style.r, private_style.g, private_style.b, private_style.a };
            thickness = private_style.scale;
        }
        else
        {
            if (settings.GetIsSelected())
            {
                c = { config->_focus_style.r, config->_focus_style.g, config->_focus_style.b, config->_focus_style.a };
                thickness = config->_focus_style.true_line_width;
            }
            else
            {
                c = { config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a };
                thickness = config->_display_style.true_line_width;
            }
        }
    }

    /**
     * @brief 更新绘制缓冲区
     */
    virtual void UpdateDrawBuffer() {}

    /**
     * @brief 更新控制点
     */
    virtual void UpdateControlPoint() {}
    /**
     * @brief 绘制控制点
     */
    virtual void DrawControlPoint(Renderer*, Painter*, const LayerConfig*) {}
    /**
     * @brief 获取控制点
     */
    virtual std::vector<ControlPoint> CreateControlPoint() { return {}; }
    /**
     * @brief 尝试响应控制点
     */
    virtual int TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>&, const TryResponseEventParam&) { return ErrorCode::GRAPHICS_NULL; }
    /**
     * @brief 响应控制点
     */
    virtual int ResponseControlPoint(const ResponseEventParam&) { return ErrorCode::GRAPHICS_NULL; }

    virtual int ResponseEvent(const MouseEventValue&) { return ErrorCode::GRAPHICS_NULL; }
    /**
     * @brief 响应控制点（坐标与属性参数）
     */
    virtual int ResponseControlPoint([[maybe_unused]] const float& xstart, [[maybe_unused]] const float& ystart,
        [[maybe_unused]] const float& xend, [[maybe_unused]] const float& yend,
        [[maybe_unused]] const bool& istemp,
        [[maybe_unused]] const ControlAttributes& attr)
    {
        return ErrorCode::GRAPHICS_NULL;
    }

    /**
     * @brief 生成克隆
     * @note  需要实现深拷贝 TODO
     */
    virtual std::shared_ptr<GraphicsAbstract> Clone() const { return nullptr; }

    /**
     * @brief 序列化
     */
    virtual std::string GetSerializedData() { return std::string(); }

public:
    DrawSettings settings; ///< 绘制参数

protected:
    // std::unordered_map<int, std::shared_ptr<ControlPointAbstract>> control_points; ///< 控制点
    std::vector<std::shared_ptr<ControlPointAbstract>> control_points; ///< 控制点
};

using GraphicsPtr = std::shared_ptr<GraphicsAbstract>;
using GraphicsPtrVec = std::vector<GraphicsPtr>;

#endif // !GRAPHICS_ABSTRACT_H
