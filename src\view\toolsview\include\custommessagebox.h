///*****************************************************************//**
// * @file   reminderview.h
// * @brief  提示界面view类
// * @details    
// * <AUTHOR>
// * @date 2024.1.29
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.1.29         <td>V1.0              <td>HJC          <td><EMAIL> <td>
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/
#ifndef __CUSTOM_MESSAGE_BOX_H__
#define __CUSTOM_MESSAGE_BOX_H__
//QT
#include <QMutex>
#include <QMetaObject>
#include <QObject>
#include <QWaitCondition>
//custom
#include "coreapplication.h"
#include "messagebase.h"


Q_DECLARE_METATYPE(std::string);
Q_DECLARE_METATYPE(jrscore::LogLevel);
namespace jrsaoi
{
    struct ImplData;
    class CustomMessageBox : public MessageBase
    {
        Q_OBJECT
    public:
        CustomMessageBox(QWidget* parent = nullptr);
        ~CustomMessageBox();

        /**
         * @fun ShowMessage
         * @brief  提示框显示函数
         * @param msg_
         * @return
         * <AUTHOR>
         * @date 2024.12.3
         */
        jrscore::MessageButton ShowMessage(const jrsaoi::MessageBase::Message& msg_) override;
        /**
        * @fun DisplayMessageBoxMultipleThread
        * @brief 多线程调用 显示消息框
        * @param msg_
        * @return
        * <AUTHOR>
        * @date 2024.12.4
        */
        Q_INVOKABLE void DisplayMessageBoxMultipleThread(const jrsaoi::MessageBase::Message& msg_);

    signals:
        void SigUpdateMessageBoxResualt(const jrscore::MessageButton& msg_btn_, const std::string& time_);
    private:
        void Init();
        void InitMember();
        void InitView();
        void InitConnect();
        jrscore::MessageButton ThreadOperator(const jrsaoi::MessageBase::Message& msg_);
        /**
         * @fun DisplayMessageBoxMainThread
         * @brief 主线程调用
         * @param msg_
         * @return
         * <AUTHOR>
         * @date 2024.12.4
         */
        jrscore::MessageButton DisplayMessageBoxMainThread(const jrsaoi::MessageBase::Message& msg_);

        void WaitForCondition();
        void NotifyCondition();

        ImplData* _impl_data;
        QWidget* _parent;
        std::recursive_mutex _map_mtx;/**<map 线程安全*/
        std::mutex _global_mtx; /**< 全局锁 */
        std::condition_variable _condition_var;/**<条件变量*/
        bool _is_ready; /**<是否可以显示*/
        std::unordered_map<std::string,
            std::tuple<jrscore::MessageButton,
            std::unique_ptr<QMutex>,
            std::unique_ptr<QWaitCondition>
            >
        > _mutexs_and_wait_conditions;/**<存储到来的提示信息 条件变量及其他信息*/

    };

}
#endif //!__CUSTOM_MESSAGE_BOX_H__
