project(parametermanager)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
add_compile_options(/bigobj)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)


# 文件打包
# 界面参数
set(view_param
    define/viewparam/viewparam.hpp
    define/viewparam/viewdefine.h 
    define/viewparam/caddefine.hpp
    define/viewparam/datadefine.hpp
    define/viewparam/multiboardparam.hpp

)
set(param_base
    define/parambase/parambase.hpp
)
# 图片参数
set(image_param

    define/image/image.hpp
)

# 工程参数
set( project_param

    define/projectparam/projectparam.hpp
    define/projectparam/projecteventparam.hpp

)


# 设备参数(运控、相机、光源等)
set( device_param
    define/deviceparam/deviceparam.hpp
)

# 检测结果参数
set(result_param
    define/resultparam/detectresultparam.hpp
)

# 数据参数
set(data_param
    define/dataparam/dbparam.h
    define/dataparam/dataparam.h
    define/dataparam/settingparam.h
)

# 颜色参数
set(color_param

    define/colorparam/colorparams.h
    define/colorparam/colorparams.cpp
    define/colorparam/colorparamsserialization.h
    
)
# 算法运行参数
set(algoexecute_param
    define/algoexecuteparam/algoexecuteparam.hpp
    define/algoexecuteparam/algogeometrydata.hpp
    #define/algoexecuteparam/algoimagedata.h //丢弃使用
    define/algoexecuteparam/judgeparam.h
)
# 算法运行参数基类
set(algobase_param
    #define/algobase/operatordrivebase.cpp
    define/algobase/operatorparambase.cpp
    define/algobase/operatordrivebase.h
    define/algobase/operatorparambase.h
    #define/algobase/generaltool.cpp
    #define/algobase/imageprocessalgo.h
    #define/algobase/generaltool.h
    
)


#  工程数据处理
set(project_process
    process/projectprocess/projectdataprocess.cpp
    process/projectprocess/projectdataprocess.h

)
#  参数处理
set(parameter_process
    process/paramprocess/parameterprocess.cpp
    process/paramprocess/parameterprocess.hpp

)

# 算法执行后参数处理
set(algo_process
    process/algoprocess/algoexecuteparamprocess.cpp
    process/algoprocess/algoexecuteparamprocess.h

)

source_group("define/parambase" FILES ${param_base})
source_group("define/viewparam" FILES ${view_param})
source_group("define/image" FILES ${image_param})
source_group("define/project" FILES ${project_param})
source_group("define/deviceparam" FILES ${device_param})
source_group("define/result" FILES ${result_param})
source_group("define/data" FILES ${data_param})
source_group("define/colorparam" FILES ${color_param})
source_group("define/algoexecuteparam" FILES ${algoexecute_param})
source_group("define/algobase" FILES ${algobase_param})
source_group("process/projectprocess" FILES ${project_process})
source_group("process/algoprocess" FILES ${algo_process})
source_group("process/paramprocess" FILES ${parameter_process})
#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

add_library(${PROJECT_NAME} SHARED
    ${param_base}
    ${view_param} 
    ${data_param}       
    ${image_param} 
    ${project_param}
    ${result_param}
    ${device_param}
    ${color_param}
    ${algoexecute_param}
    ${algobase_param}
    ${project_process}
    ${algo_process}
    ${parameter_process}
    ${JRS_VERSIONINFO_RC}

)
#共享依赖头文件
target_include_directories(${PROJECT_NAME} PUBLIC
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/image
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/projectparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/deviceparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/viewparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/parambase
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/dataparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/resultparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/algoexecuteparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/algobase
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/colorparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/projectprocess
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/paramprocess
    ${DIR_PROJECT_CURRENT}/src/core/common/include
    ${DIR_PROJECT_CURRENT}/src/core/database/include
    ${DIR_PROJECT_CURRENT}/src/core/coordinatecenter/include
    ${DIR_PROJECT_CURRENT}/thirdparty/json/include
    #${DIR_PROJECT_CURRENT}thirdparty/iguana
    ${DIR_PROJECT_CURRENT}/thirdparty/cereal/include

    ${OPENCV_INCLUDE_DIR}
)


target_link_directories(${PROJECT_NAME} 
    PUBLIC
    #opencv
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    #$<$<CONFIG:Release>:${OPENCV_RELEASE_DIR}>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>
    #$<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/colorwheel/lib/release>
    #$<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/colorwheel/lib/debug>
    
)
#共享依赖库
target_link_libraries(${PROJECT_NAME} PUBLIC
    core
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100>
    #$<$<CONFIG:Debug>:colorwheel>
    #$<$<CONFIG:Release>:colorwheel>    
)
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
