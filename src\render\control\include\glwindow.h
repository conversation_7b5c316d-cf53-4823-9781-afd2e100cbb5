/*********************************************************************
 * @brief  GL窗口类.
 *
 * @file   glwindow.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef GLWINDOW_H
#define GLWINDOW_H

#include "windowinterface.h"
#include <qopenglwidget.h>

class QOpenGLContext;
class QWidget;
class GLWindow : public WindowInterface, public QOpenGLWidget
{
public:
    GLWindow(QWidget* parent = nullptr);
    ~GLWindow();

    QOpenGLContext* GetOpenGLContext() const override { return context(); }
    /**
     * @brief  获取opengl上下文
     */
    void DoMakeCurrent() override;
    /**
     * @note   释放opengl上下文
     */
    void DoDoneCurrent() override;

    inline qreal GetDevicePixelRatio() const override { return devicePixelRatio(); }
    inline int qtWidth() const override { return QOpenGLWidget::width(); }
    inline int qtHeight() const override { return QOpenGLWidget::height(); }
    inline QSize qtSize() const override { return QOpenGLWidget::size(); }
    inline QPoint qtPos() const override { return QOpenGLWidget::pos(); }

    inline QWidget* asWidget() override { return this; }

    inline QPaintDevice* GetPaintDevice() override { return this; }
    /**
     * @brief  请求重绘
     */
    void RequestUpdate() override;


protected:
    /**
     * @brief  获取opengl上下文实例
     */
    inline ROpenGLFunctions* functions() const override
    {
        return context() ? context()->versionFunctions<ROpenGLFunctions>()
            : nullptr;
    }

    void onResizeGL(int w, int h);

    void resizeGL(int w, int h) override {

        onResizeGL(w, h);
    }

    int width() const override { return QOpenGLWidget::width(); }
    int height() const override { return QOpenGLWidget::height(); }
    QSize size() const override { return QOpenGLWidget::size(); }

    void initializeGL() override { initialize(); }
    void paintGL() override { doPaintGL(); }

    void mousePressEvent(QMouseEvent* event) override { processMousePressEvent(event); }
    void mouseMoveEvent(QMouseEvent* event) override { processMouseMoveEvent(event); }
    void mouseDoubleClickEvent(QMouseEvent* event) override { processMouseDoubleClickEvent(event); }
    void mouseReleaseEvent(QMouseEvent* event) override { processMouseReleaseEvent(event); }
    void wheelEvent(QWheelEvent* event) override;// { processWheelEvent(event); }
    void keyPressEvent(QKeyEvent* event) override;// { processKeyPressEvent(event); }
    void keyReleaseEvent(QKeyEvent* event) override;// { processKeyReleaseEvent(event); }
    void enterEvent(QEvent*) override { processEnterEvent(); }
    void leaveEvent(QEvent*) override { processLeaveEvent(); }

    //void resizeEvent(QResizeEvent* event) override;

    void moveEvent(QMoveEvent* event) override;

    /**
     * @brief  在自定义离屏渲染情况下禁用原本获取opengl上下文的函数
     */
    void makeCurrent() = delete;
};

#endif // GLWINDOW_H