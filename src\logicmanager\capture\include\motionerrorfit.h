#ifndef MOTIONERRORFIT_H
#define MOTIONERRORFIT_H

#include "MotionErrorParam.h"

/// @brief 运动误差拟合器
class MotionErrorFit
{
public:
	MotionErrorFit();
	~MotionErrorFit();

	/// @brief              设置拟合数据
	/// @param _x_start     拟合区域x起始点
	/// @param _x_end       拟合区域x终点
	/// @param _y_start     拟合区域y起始点
	/// @param _y_end       拟合区域y终点
	/// @param _theoryPts   理论运到到位的位置
	/// @param _realPts     实际运动到位的位置
	/// @return             是否设置成功
	bool SetFitData(const double& _x_start,
		const double& _x_end,
		const double& _y_start,
		const double& _y_end,
		const vector<JrsPoint>& _theoryPts,
		const vector<JrsPoint>& _realPts);


	/// @brief              执行拟合
	/// @return             是否拟合成功
	bool Run();


	/// @brief              获取拟合得到的模型参数
	/// @return             模型参数
	MotionErrorParam GetFitResult();

private:

	MotionErrorParam param;     // 误差函数参数
	vector<JrsPoint> inputPts; // 理论运动位置
	vector<JrsPoint> outputPts;   // 实际运动位置

	/// @brief               根据拟合数据生成模型的初始参数
	/// @param b             初始参数
	void GenInitParam(vector<double>& b);
};

#endif