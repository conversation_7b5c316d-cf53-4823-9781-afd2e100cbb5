#include "graphicsprocess.h"
#include "graphicsabstract.hpp"

#include "graphicsalgorithm.h"
#include "customcursortype.hpp"

#include "painter.h"
// #include "renderer.h"

#include "algodefine.hpp"
#include "log.h"

void LimitRectInRect(float& rect_center_x, float& rect_center_y, const float& rect_width, const float& rect_height,
    const float& limit_rect_center_x, const float& limit_center_y, const float& limit_rect_width, const float& limit_rect_height)
{
    // 计算内矩形的边界
    float half_rect_width = rect_width / 2;
    float half_rect_height = rect_height / 2;

    // 计算外部限制矩形的边界
    float limit_half_width = limit_rect_width / 2;
    float limit_half_height = limit_rect_height / 2;

    // 计算内矩形的最小和最大x，y坐标允许的范围
    float min_x = limit_rect_center_x - limit_half_width + half_rect_width;
    float max_x = limit_rect_center_x + limit_half_width - half_rect_width;

    float min_y = limit_center_y - limit_half_height + half_rect_height;
    float max_y = limit_center_y + limit_half_height - half_rect_height;

    // 限制内矩形的中心坐标在允许的范围内
    rect_center_x = std::min(max_x, std::max(min_x, rect_center_x));
    rect_center_y = std::min(max_y, std::max(min_y, rect_center_y));
}

void RectDoTop(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle > 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    obj->SetX(obj->x() - static_cast<float>(extra * vec.x));
    obj->SetY(obj->y() - static_cast<float>(extra * vec.y));
    obj->SetH(obj->h() + static_cast<float>(length));
}

void RectDoDown(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle < 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    obj->SetX(obj->x() + static_cast<float>(extra * vec.x));
    obj->SetY(obj->y() + static_cast<float>(extra * vec.y));
    obj->SetH(obj->h() + static_cast<float>(length));
}

void RectDoLeft(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle > 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    obj->SetX(obj->x() - static_cast<float>(extra * vec.x));
    obj->SetY(obj->y() - static_cast<float>(extra * vec.y));
    obj->SetW(obj->w() + static_cast<float>(length));
}

void RectDoRight(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle < 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    obj->SetX(obj->x() + static_cast<float>(extra * vec.x));
    obj->SetY(obj->y() + static_cast<float>(extra * vec.y));
    obj->SetW(obj->w() + static_cast<float>(length));
}

int RectResponseControlPoint(GraphicsAbstract* obj, const int& type, const float& xstart, const float& ystart,
    const float& xend, const float& yend)
{
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    const auto& a = obj->a();
    float cosA = (float)std::cos(A_DEG_TO_RAD(a));
    float sinA = (float)std::sin(A_DEG_TO_RAD(a));
    /*方向向量*/
    cv::Point2f dirVec(cosA, sinA);
    /*垂直向量*/
    cv::Point2f verticalVec(-sinA, cosA);
    /*移动向量*/
    cv::Point2f moveVec(xstart - xend, ystart - yend);

    switch (ControlPointType(type))
    {
    case ControlPointType::CORNER_POINT_LEFT_DOWN:
        RectDoLeft(obj, dirVec, moveVec);
        RectDoDown(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_LEFT_TOP:
        RectDoLeft(obj, dirVec, moveVec);
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_TOP:
        RectDoRight(obj, dirVec, moveVec);
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_DOWN:
        RectDoRight(obj, dirVec, moveVec);
        RectDoDown(obj, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_LEFT:
        RectDoLeft(obj, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_TOP:
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_RIGHT:
        RectDoRight(obj, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_DOWN:
        RectDoDown(obj, verticalVec, moveVec);
        break;
    case ControlPointType::MOVE_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        obj->SetXY(obj->x() + xo, obj->y() + yo);
    }
    break;
    case ControlPointType::ROTATE_POINT:
    {
        const int gap = 10; // 旋转最小间隔
        auto ra = CalcVectorRotationAngle(cv::Point2f(obj->x() - xstart, obj->y() - ystart),
            cv::Point2f(obj->x() - xend, obj->y() - yend));
        int rounda = ((int)ra / gap) * gap;
        obj->SetA(a + rounda);
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }

    // if (!temp)
    {
        /*允许越界*/
        obj->SetWH(abs(obj->w()), abs(obj->h()));
        // obj->width = abs(obj->width);
        // obj->height = abs(obj->height);
        /*不允许越界,尚不完善*/
        // obj->width = std::max((float)0, obj->width);
        // obj->height = std::max((float)0, obj->height);
    }
    return GraphicsAbstract::OK;
}

void RectDoTop(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    (void)w;
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle > 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    x -= static_cast<float>(extra * vec.x);
    y -= static_cast<float>(extra * vec.y);
    h += static_cast<float>(length);
}

void RectDoDown(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    (void)w;
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle < 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    x += static_cast<float>(extra * vec.x);
    y += static_cast<float>(extra * vec.y);
    h += static_cast<float>(length);
}

void RectDoLeft(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    (void)h;
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle > 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    x -= static_cast<float>(extra * vec.x);
    y -= static_cast<float>(extra * vec.y);
    w += static_cast<float>(length);
}

void RectDoRight(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec)
{
    (void)h;
    auto pv3 = ProjectVector(moveVec, vec);
    auto length = cv::norm(pv3);
    {
        auto angle = A_RAD_TO_DEG(calculateAngle(moveVec.x, moveVec.y, vec.x, vec.y));
        if (angle < 90)
            length = -length;
    }
    const auto extra = length * 0.5f;
    x += static_cast<float>(extra * vec.x);
    y += static_cast<float>(extra * vec.y);
    w += static_cast<float>(length);
}

int RectResponseControlPoint(float& x, float& y, float& w, float& h, float& a, const int& type,
    const float& xstart, const float& ystart, const float& xend, const float& yend)
{
    float cosA = (float)std::cos(A_DEG_TO_RAD(a));
    float sinA = (float)std::sin(A_DEG_TO_RAD(a));
    /*方向向量*/
    cv::Point2f dirVec(cosA, sinA);
    /*垂直向量*/
    cv::Point2f verticalVec(-sinA, cosA);
    /*移动向量*/
    cv::Point2f moveVec(xstart - xend, ystart - yend);

    switch (ControlPointType(type))
    {
    case ControlPointType::CORNER_POINT_LEFT_DOWN:
        RectDoLeft(x, y, w, h, dirVec, moveVec);
        RectDoDown(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_LEFT_TOP:
        RectDoLeft(x, y, w, h, dirVec, moveVec);
        RectDoTop(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_TOP:
        RectDoRight(x, y, w, h, dirVec, moveVec);
        RectDoTop(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_DOWN:
        RectDoRight(x, y, w, h, dirVec, moveVec);
        RectDoDown(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_LEFT:
        RectDoLeft(x, y, w, h, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_TOP:
        RectDoTop(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_RIGHT:
        RectDoRight(x, y, w, h, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_DOWN:
        RectDoDown(x, y, w, h, verticalVec, moveVec);
        break;
    case ControlPointType::MOVE_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        x += xo;
        y += yo;
    }
    break;
    case ControlPointType::ROTATE_POINT:
    {
        const int gap = 10; // 旋转最小间隔
        auto ra = CalcVectorRotationAngle(cv::Point2f(x - xstart, y - ystart),
            cv::Point2f(x - xend, y - yend));
        int rounda = ((int)ra / gap) * gap;
        a += rounda;
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }

    // if (!temp)
    {
        /*允许越界*/
        w = abs(w);
        h = abs(h);
        // obj->SetWH(abs(obj->w()), abs(obj->h()));
        // obj->width = abs(obj->width);
        // obj->height = abs(obj->height);
        /*不允许越界,尚不完善*/
        // obj->width = std::max((float)0, obj->width);
        // obj->height = std::max((float)0, obj->height);
    }
    return GraphicsAbstract::OK;
}

ControlPoint CreateSingleControlPoint(float cx, float cy, int width, int height, int cursor_shape, bool is_render, const std::vector<Vec2>& contours,
    ControlPointType type, int index)
{
    ControlPointDraw cpd;
    cpd.cx = cx;
    cpd.cy = cy;
    cpd.width = width;
    cpd.height = height;
    cpd.cursor_shape = cursor_shape;
    cpd.is_render = is_render;
    cpd.contours = contours;
    ControlPoint pcpc = ControlPoint::Create(type, index, cpd);
    return pcpc;
}

ControlPoint CreateMoveControlPoint(float x, float y, float w, float h, float a, int cursor_shape, int index, const std::vector<Vec2>& contour)
{
    if (contour.empty())
    {
        cv::Point2f ovp[4];
        cv::RotatedRect(cv::Point2f(x, y), cv::Size2f(w, h), a).points(ovp);
        std::vector<Vec2> paths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };

        return CreateSingleControlPoint(x, y, control_point_w, control_point_h, cursor_shape, false, paths, ControlPointType::MOVE_POINT, index);
    }
    else
    {
        return CreateSingleControlPoint(x, y, control_point_w, control_point_h, cursor_shape, false, contour, ControlPointType::MOVE_POINT, index);
    }
}

ControlPoint CreateMoveControlPoint(GraphicsAbstract* const obj, int cursor_shape, int index, const std::vector<Vec2>& contour)
{
    if (contour.empty())
    {
        cv::Point2f ovp[4];
        obj->GetBoundingbox().points(ovp);
        std::vector<Vec2> paths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };

        return CreateSingleControlPoint(obj->x(), obj->y(), control_point_w, control_point_h, cursor_shape, false, paths, ControlPointType::MOVE_POINT, index);
    }
    else
    {
        return CreateSingleControlPoint(obj->x(), obj->y(), control_point_w, control_point_h, cursor_shape, false, contour, ControlPointType::MOVE_POINT, index);
    }
}

std::vector<ControlPoint> CreateSizeControlPoint(float x, float y, float w, float h, float a, int cursor_shape, int index)
{
    cv::Point2f ovp[4];
    cv::RotatedRect(cv::Point2f(x, y), cv::Size2f(w, h), a).points(ovp);

    std::vector<ControlPoint> cps;
    /*创建角控制点*/
    for (int i = 0; i < 4; ++i)
    {
        auto cp = CreateSingleControlPoint(ovp[i].x, ovp[i].y, control_point_w, control_point_h, cursor_shape, true, {},
            ControlPointType(static_cast<int>(ControlPointType::CORNER_POINT_LEFT_DOWN) + i), index);
        cps.emplace_back(cp);
    }
    /*创建腰控制点*/
    {
        std::vector<cv::Point2f> vcp;
        vcp.reserve(4);
        for (int i = 0; i < 4; ++i)
        {
            vcp.emplace_back((ovp[(i + 4) % 4] + ovp[(i + 5) % 4]) / 2.f);
        }

        for (int i = 0; i < 4; ++i)
        {
            auto cp = CreateSingleControlPoint(vcp[i].x, vcp[i].y, control_point_w, control_point_h, cursor_shape, true, {},
                ControlPointType(static_cast<int>(ControlPointType::WAIST_POINT_LEFT) + i), index);

            cps.emplace_back(cp);
        }
    }
    return cps;
}

std::vector<ControlPoint> CreateSizeControlPoint(GraphicsAbstract* const obj, int cursor_shape, int index)
{
    auto boundingbox = obj->GetBoundingbox();
    /*四顶点*/
    cv::Point2f ovp[4];
    boundingbox.points(ovp);

    std::vector<ControlPoint> cps;
    /*创建角控制点*/
    for (int i = 0; i < 4; ++i)
    {
        auto cp = CreateSingleControlPoint(ovp[i].x, ovp[i].y, control_point_w, control_point_h, cursor_shape, true, {},
            ControlPointType(static_cast<int>(ControlPointType::CORNER_POINT_LEFT_DOWN) + i), index);
        cps.emplace_back(cp);
    }
    /*创建腰控制点*/
    {
        std::vector<cv::Point2f> vcp;
        vcp.reserve(4);
        for (int i = 0; i < 4; ++i)
        {
            vcp.emplace_back((ovp[(i + 4) % 4] + ovp[(i + 5) % 4]) / 2.f);
        }

        for (int i = 0; i < 4; ++i)
        {
            auto cp = CreateSingleControlPoint(vcp[i].x, vcp[i].y, control_point_w, control_point_h, cursor_shape, true, {},
                ControlPointType(static_cast<int>(ControlPointType::WAIST_POINT_LEFT) + i), index);

            cps.emplace_back(cp);
        }
    }
    return cps;
}
std::vector<ControlPoint> CreateRotateControlPoint(GraphicsAbstract* const obj, int cursor_shape)
{
    /*感觉有一个点就够用了*/
    /*只画一个会移动,不好看*/
    cv::RotatedRect nbounding = obj->GetBoundingbox();
    nbounding.size.width += 10;
    nbounding.size.height += 10;
    std::vector<cv::Point2f> vurp;
    nbounding.points(vurp);

    std::vector<ControlPoint> cps;
    for (int i = 0; i < 4; ++i)
    {
        // ControlPointDraw cpd;
        // cpd.cx = vurp[i].x;
        // cpd.cy = vurp[i].y;
        // cpd.width = control_point_w;
        // cpd.height = control_point_h;
        // cpd.cursor_shape = cursor_shape;
        // // cpd.cursor_shape = Qt::DragMoveCursor;
        // cpd.is_render = true;

        // auto pcpc = ControlPoint::Create(ControlPointType::ROTATE_POINT, i, cpd);

        auto cp = CreateSingleControlPoint(vurp[i].x, vurp[i].y, control_point_w, control_point_h, cursor_shape, true, {}, ControlPointType::ROTATE_POINT, i);
        cps.emplace_back(cp);
    }
    return cps;
}

std::vector<ControlPoint> CreateContourControlPoint(GraphicsAbstract* const obj, int cursor_shape, const std::vector<Vec2>& contour)
{
    std::vector<ControlPoint> cps;
    Vec2 center(obj->x(), obj->y());
    int count = 0;
    for (auto& p : contour)
    {
        // ControlPointDraw cpd;
        // cpd.cx = center.x + p.x;
        // cpd.cy = center.y + p.y;
        // cpd.width = control_point_w;
        // cpd.height = control_point_h;
        // cpd.cursor_shape = cursor_shape;
        // cpd.is_render = true;
        auto cp = CreateSingleControlPoint(center.x + p.x, center.y + p.y, control_point_w, control_point_h, cursor_shape, true, {}, ControlPointType::CONTOUR_POINT, count++);
        // auto pcpc = ControlPoint::Create(ControlPointType::CONTOUR_POINT, count++, cpd);
        cps.emplace_back(cp);
    }
    return cps;
}

std::vector<ControlPoint> CreateBorderControlPoint(GraphicsAbstract* const obj, int cursor_shape, const std::vector<Vec2>& points)
{
    std::vector<ControlPoint> cps;
    Vec2 center(obj->x(), obj->y());

    int count = 0;
    for (auto& p : points)
    {
        // ControlPointDraw cpd;
        // cpd.cx = center.x + p.x;
        // cpd.cy = center.y + p.y;
        // cpd.width = control_point_w;
        // cpd.height = control_point_h;
        // cpd.cursor_shape = cursor_shape;
        // cpd.is_render = true;
        // auto pcpc = ControlPoint::Create(ControlPointType::BORDER_POINT, count++, cpd);
        auto cp = CreateSingleControlPoint(center.x + p.x, center.y + p.y, control_point_w, control_point_h, cursor_shape, true, {}, ControlPointType::BORDER_POINT, count++);
        cps.emplace_back(cp);
    }
    return cps;
}

void DrawBoundingbox(Painter* p, const cv::RotatedRect& boundingbox, const Color& c, const float& thickness)
{
    /*四顶点*/
    cv::Point2f ovp[4];
    boundingbox.points(ovp);

    std::vector<Vec2> tvPaths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };

    p->DrawLines(tvPaths, c, true, thickness);
}

std::vector<cv::Point2f> GeneratePositionsWithStartingObject(
    const cv::Point2f& center,
    const cv::Point2f& firstObjectCenter,
    int numObjects,
    float radius)
{
    std::vector<cv::Point2f> positions;

    // 计算第一个物体相对于中心点的初始角度
    float initialAngle = std::atan2(firstObjectCenter.y - center.y, firstObjectCenter.x - center.x);

    // 将第一个物体的中心坐标加入到列表中
    positions.push_back(firstObjectCenter);

    // 每个物体之间的角度差
    double deltaAngle = 2 * CV_PI / numObjects;

    // 生成剩余物体的位置
    for (int i = 1; i < numObjects; ++i) {
        // 当前物体的角度
        double angle = initialAngle + i * deltaAngle;

        // 计算新物体的坐标
        double x = center.x + radius * std::cos(angle);
        double y = center.y + radius * std::sin(angle);

        // 将该物体的坐标存入列表
        positions.emplace_back((float)x, (float)y);
    }

    return positions;
}