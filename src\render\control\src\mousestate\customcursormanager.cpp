
#include "CustomCursorManager.h"
#include "systemmonitor.hpp"

#include <QPixmap>
#include <QApplication>
#include <QFile>
#include <QResource>
// #include <QImageReader>
#include <QFileInfo>
// #include <QDebug>

CustomCursorManager::CustomCursorManager()
{
    AddCustomCursor(CustomCursorType::Default, ":/render/renderimage/SelectNormal.cur");
    AddCustomCursor(CustomCursorType::Normal, ":/render/renderimage/SelectNormal.cur");
    AddCustomCursor(CustomCursorType::Move, ":/render/renderimage/Move.cur");
    AddCustomCursor(CustomCursorType::MoveAll, ":/render/renderimage/MoveAll.cur");
    AddCustomCursor(CustomCursorType::Pickup, ":/render/renderimage/Pickup.cur");
    AddCustomCursor(CustomCursorType::SelectAlternate, ":/render/renderimage/SelectAlternate.cur");
    AddCustomCursor(CustomCursorType::SelectNormal, ":/render/renderimage/SelectNormal.cur");
    AddCustomCursor(CustomCursorType::SelectPosition, ":/render/renderimage/SelectPosition.cur");
    AddCustomCursor(CustomCursorType::SelectPrecision, ":/render/renderimage/SelectPrecision.cur");
    AddCustomCursor(CustomCursorType::SelectText, ":/render/renderimage/SelectText.cur");
    AddCustomCursor(CustomCursorType::SelectLink, ":/render/renderimage/SelectLink.cur");
    AddCustomCursor(CustomCursorType::SelectHelp, ":/render/renderimage/SelectHelp.cur");
    AddCustomCursor(CustomCursorType::SelectMove, ":/render/renderimage/SelectMove.cur");
    AddCustomCursor(CustomCursorType::ResizeVertical, ":/render/renderimage/ResizeVertical.cur");
    AddCustomCursor(CustomCursorType::ResizeHorizontal, ":/render/renderimage/ResizeHorizontal.cur");
    AddCustomCursor(CustomCursorType::ResizeDiagonal1, ":/render/renderimage/ResizeDiagonal1.cur");
    AddCustomCursor(CustomCursorType::ResizeDiagonal2, ":/render/renderimage/ResizeDiagonal2.cur");
    AddCustomCursor(CustomCursorType::HandWriting, ":/render/renderimage/HandWriting.cur");
    AddCustomCursor(CustomCursorType::HandClose, ":/render/renderimage/HandClose.cur");
    AddCustomCursor(CustomCursorType::Unavailable, ":/render/renderimage/Unavailable.cur");
    AddCustomCursor(CustomCursorType::Rotate, ":/render/renderimage/Rotate.cur");
    // TODO 不支持ani格式
    // AddCustomCursor(CustomCursorType::Busy, ":/cursors/Busy.ani");
    // AddCustomCursor(CustomCursorType::WorkingInBackground, ":/cursors/WorkingInBackground.ani");

    ResetCursor();
}

bool CustomCursorManager::AddCustomCursor(const CustomCursorType& type, const char* filename)
{
    if (!QFile::exists(filename))
    {
        // qWarning() << "File does not exist:" << filePath;
        return false;
    }

    QPixmap pixmap(filename);
    if (pixmap.isNull())
    {
        // qWarning() << "Failed to load cursor image from" << filePath;
        return false;
    }

    // 通过qt获取文件的扩展名
    QString ext = QFileInfo(filename).suffix();

    if (ext == "cur")
    {
        // int  hot_x, hot_y;
        // ReadCursorHot(hot_x, hot_y, filename);
        cursorMap[type] = ImportCurCursor(filename);
    }
    return true;
}

void CustomCursorManager::SetCursor(const CustomCursorType& type)
{
    // 如果当前指针样式与目标样式相同，避免重复设置
    if (type == currentCustomCursorType)
    {
        return;
    }
    // 设置之前先重置指针样式,避免重复压栈
    QApplication::restoreOverrideCursor();
    if (type == CustomCursorType::Reset)
    {
        currentCustomCursorType = CustomCursorType::Reset;
        return;
    }
    auto it = cursorMap.find(type);
    if (it == cursorMap.end())
    {
        return;
    }
    QApplication::setOverrideCursor(it->second);
    currentCustomCursorType = type;
}

void CustomCursorManager::ResetCursor()
{
    //QApplication::restoreOverrideCursor();
    //currentCustomCursorType = Default;
    SetCursor(CustomCursorType::Default);
}

QCursor CustomCursorManager::ImportCurCursor(const char* filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly))
    {
        // qWarning("Could not open .cur file");
        return QCursor();
    }

    QDataStream stream(&file);
    stream.setByteOrder(QDataStream::LittleEndian);

    // 解析 .cur 文件头
    quint16 reserved, type, count;
    stream >> reserved >> type >> count;

    if (reserved != 0 || type != 2 || count == 0)
    {
        // qWarning("Invalid .cur file format");
        return QCursor();
    }

    // 读取第一个图标条目
    quint8 width, height, colorCount, reserved2;
    quint16 hotspotX, hotspotY;
    quint32 size, offset;

    stream >> width >> height >> colorCount >> reserved2 >> hotspotX >> hotspotY >> size >> offset;


    return QCursor(QPixmap(filename), hotspotX, hotspotY);
}

QCursor CustomCursorManager::ImportAniCursor(const char* filename, int hot_x, int hot_y)
{
    // 需要额外qt模块,目前实现不了
    (void)filename;
    (void)hot_x;
    (void)hot_y;
    return QCursor();
}

// QString CustomCursorManager::CustomCursorTypeToString(CustomCursorType type)
// {
//     const QMetaEnum metaEnum = QMetaEnum::fromType<CustomCursorType>();
//     return QString(metaEnum.valueToKey(type));
// }
