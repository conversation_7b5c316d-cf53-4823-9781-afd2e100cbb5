﻿/*****************************************************************
 * @file   tdatamanager.h
 * @brief   仅作为临时层，防止模板暴露在外面
 * @details
 * <AUTHOR>
 * @date 2025.2.14
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.2.14          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
#include <future>

//Custom
#include "filehandle.h"
#include "dbmanager.h"
namespace jrsdata
{
    class TDataManager
    {
    public:
        TDataManager()
            ://_file_handle_ptr(std::make_shared<jrsfiledata::FileHandle>()),
            _db_manager_ptr(std::make_shared<jrsdatabase::DBManagers>())
        {
        }
        ~TDataManager()
        {
        }
        int DeleteTable(const std::string& table_name_)
        {
            return _db_manager_ptr->DeleteTable(table_name_);
        }
        template <typename T>
        int OperatorTableData(const std::pair<jrsdatabase::TablesDataOperator::OperatorType, T>& data_)
        {
            int res = jrscore::AOI_OK;

            switch (data_.first)
            {
            case jrsdatabase::TablesDataOperator::OperatorType::Insert:
                res = _db_manager_ptr->Insert(data_.second);
                break;
            case jrsdatabase::TablesDataOperator::OperatorType::Replace:
                res = _db_manager_ptr->Replace(data_.second);
                break;
            case jrsdatabase::TablesDataOperator::OperatorType::Update:
                res = _db_manager_ptr->Update(data_.second);
                break;
            default:
                Log_ERROR("未知的操作类型");
                res = jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
                break;
            }

            if (res != jrscore::AOI_OK)
            {
                if constexpr (is_vector<T>::value)
                {
                    if (!data_.second.empty())
                    {
                        Log_ERROR("操作数据表：", get_table_name_safe(data_.second.front()), "失败，请检查");
                    }
                }
                else
                {
                    Log_ERROR("操作数据表：", get_table_name_safe(data_.second), "失败，请检查");
                }
            }

            return res;
        }

        template <typename T>
        int Save(const T& data_)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
            switch (static_cast<jrsdata::DataSaveMode>(data_.data_save_mode))
            {
            case jrsdata::DataSaveMode::SAVE_FILE:
            {
                auto start = std::chrono::steady_clock::now();
                _file_handle_temp_ptr->Save(data_);
                auto end = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
                Log_DEBUG("保存：", data_.file_param.file_name, "耗时:", duration.count(), "ms");
                //_file_handle_temp_ptr->Save(data_);
            }
            break;
            case  jrsdata::DataSaveMode::SAVE_DATABASE:
                return ParseDataToDB(data_);
                break;
            case  jrsdata::DataSaveMode::SAVE_FILE_AND_DATABASE:
            {
                _file_handle_temp_ptr->Save(data_);
                return ParseDataToDB(data_);
            }
            break;
            default:
            {

                Log_Error_Stack("存储类型不支持,请检查是否正确配置");
                break;
            }

            }
            return jrscore::AOI_OK;
        }

        template <typename T>
        int Read(T& data_)
        {
            if constexpr (std::is_same_v<T, jrsdatabase::jrsselect::SelectorParamBasePtr>) //选择数据
            {
                return _db_manager_ptr->Select(data_);
            }
            else
            {
                int res = jrscore::AOI_OK;
                std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
                auto start = std::chrono::steady_clock::now();
                res = _file_handle_temp_ptr->Read(data_);
                auto end = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
                Log_DEBUG("读取：", data_.file_param.file_name, "耗时:", duration.count(), "ms");
                return res;
                /*return _file_handle_temp_ptr->Read(data_);*/
            }
        }
        inline int SaveImageToBin(const cv::Mat& img_, const std::string& img_path_)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
            /*cv::Mat  image_float_32(1000, 1000, CV_32FC1);
            image_float_32.setTo(100.0);
            (void)img_;*/

            return _file_handle_temp_ptr->WriteMatToBinary(img_, img_path_);
        }
        inline int ReadImageFromBin(cv::Mat& img_, const std::string& img_path_)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
            return _file_handle_temp_ptr->ReadMatFromBinary(img_, img_path_);
        }
        inline int SaveImage(const cv::Mat& img_, const std::string& img_path_)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
            /*cv::Mat  image_float_32(1000, 1000, CV_32FC1);
            image_float_32.setTo(100.0);
            (void)img_;*/

            return _file_handle_temp_ptr->Save(img_, img_path_);
        }
        inline int ReadImage(cv::Mat& img_, const std::string& img_path_)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();
            return _file_handle_temp_ptr->Read(img_, img_path_);
        }
        inline int SaveString(const std::string& path, const std::string& str)
        {
            std::shared_ptr<jrsfiledata::FileHandle> _file_handle_temp_ptr = std::make_shared<jrsfiledata::FileHandle>();

            return _file_handle_temp_ptr->SaveString(path, str);
        }
        std::shared_ptr <jrsdatabase::DBManagers> GetDBMnagersPtr()
        {
            return _db_manager_ptr;
        }
    private:
        template <typename T>
        int ParseDataToDB(const T& data_)
        {
            if constexpr (std::is_same_v<T, jrsdata::EntiretyBoardResult>)
            {
                jrsdata::EntiretyBoardResult entirety_board_param = data_;
                std::atomic<int> board_res = jrscore::AOI_OK;
                std::thread board_insert_thread([&]() {
                    board_res = _db_manager_ptr->Insert(data_.board_info);
                    });
                std::vector<std::thread> threads;
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Replace(data_.user);
                    if (res != jrscore::AOI_OK) {
                        Log_ERROR("user 表更新失败");
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Replace(data_.project);
                    if (res != jrscore::AOI_OK)
                    {
                        Log_ERROR("project 表更新失败");
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    if (data_.aoi_sys.has_value())
                    {
                        int res = _db_manager_ptr->Replace(data_.aoi_sys.value());
                        if (res != jrscore::AOI_OK)
                        {
                            Log_ERROR("AOI_SYS 表更新失败");
                        }
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Replace(data_.aoi_machien_statistics);
                    if (res != jrscore::AOI_OK) {
                        Log_ERROR("user 表更新失败");
                    }
                    }));
                board_insert_thread.join();
                if (board_res != jrscore::AOI_OK)
                {
                    for (auto& thread : threads)
                    {
                        if (thread.joinable()) {
                            thread.join();
                        }
                    }
                    return board_res;  //主板插入失败
                }
                jrsdatabase::jrsselect::SelectTablePtr select_table_ptr = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
                select_table_ptr->table_name = jrsdatabase::jrstable::T_BOARD;
                select_table_ptr->where_condition = "board_barcode='" + data_.board_info.board_barcode + "' ORDER BY board_id DESC LIMIT 1";
                select_table_ptr->select_name = jrsdatabase::jrsselect::T_BOARD_SELECT_BY_WHERE_CONDITION;
                int res = _db_manager_ptr->Select(select_table_ptr);
                if (res != jrscore::AOI_OK || select_table_ptr->boards == std::nullopt || select_table_ptr->boards->empty())
                {
                    for (auto& thread : threads)
                    {
                        if (thread.joinable()) {
                            thread.join();
                        }
                    }
                    return res;
                }
                auto select_board = select_table_ptr->boards->front();
                jrsdatabase::jrstable::TDetectWindowVector t_windows;
                jrsdatabase::jrstable::TDeviceVector t_devices;
                jrsdatabase::jrstable::TGroupVector t_groups;
                jrsdatabase::jrstable::TSubboardVector t_subboards;
                for (auto& subboard : entirety_board_param.subboards)
                {
                    subboard.t_subboard.board_id = select_board.board_id;
                    t_subboards.push_back(subboard.t_subboard);
                    int device_id = 0;
                    for (auto& device : subboard.devices)
                    {
                        device.t_device.subboard_id = subboard.t_subboard.subboard_id;
                        device.t_device.board_id = select_board.board_id;
                        device.t_device.device_id = device_id++;
                        t_devices.push_back(device.t_device);
                        int group_id = 0;
                        for (auto& group : device.groups)
                        {
                            group.t_group.board_id = device.t_device.board_id;
                            group.t_group.device_id = device.t_device.device_id;
                            group.t_group.subboard_id = device.t_device.subboard_id;
                            group.t_group.group_id = group_id++;
                            t_groups.push_back(group.t_group);
                            int detect_window_id = 0;
                            for (auto detect_window : group.detect_windows)
                            {
                                detect_window.t_detect_window.board_id = group.t_group.board_id;
                                detect_window.t_detect_window.device_id = group.t_group.device_id;
                                detect_window.t_detect_window.subboard_id = group.t_group.subboard_id;
                                detect_window.t_detect_window.group_id = group.t_group.group_id;
                                detect_window.t_detect_window.detect_window_id = detect_window_id++;
                                t_windows.push_back(detect_window.t_detect_window);
                            }
                        }
                    }
                }
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Insert(t_windows);
                    if (res != jrscore::AOI_OK)
                    {
                        Log_ERROR("window 保存数据失败");
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Insert(t_groups);
                    if (res != jrscore::AOI_OK)
                    {
                        Log_ERROR("group 保存数据失败");
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Insert(t_devices);
                    if (res != jrscore::AOI_OK)
                    {
                        Log_ERROR("device 保存数据失败");
                    }
                    }));
                threads.push_back(std::thread([&]() {
                    int res = _db_manager_ptr->Insert(t_subboards);
                    if (res != jrscore::AOI_OK)
                    {
                        Log_ERROR("subboard 保存数据失败");
                    }
                    }));

                for (auto& thread : threads)
                {
                    if (thread.joinable()) {
                        thread.join();
                    }
                }
                return jrscore::AOI_OK;
            }
            else
            {
                Log_Error_Stack("该数据结构还不支持存入数据库，请联系管理员");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
        }
        /**< 数据库 */
        std::shared_ptr <jrsdatabase::DBManagers> _db_manager_ptr;
    };
    using TDataManagerPtr = std::shared_ptr<TDataManager>;
}