﻿#include "stringoperation.h"

#include <algorithm>
#include <regex>

std::string jtools::StringOperation::StringToUpper(const std::string& input)
{
    std::string result = input;
    std::transform(result.begin(), result.end(), result.begin(), [](char c)
        { return static_cast<char>(std::toupper(static_cast<unsigned char>(c))); });
    return result;
}

std::string jtools::StringOperation::StringToLower(const std::string& input)
{
    std::string result = input;
    std::transform(result.begin(), result.end(), result.begin(), [](char c)
        { return static_cast<char>(std::tolower(static_cast<unsigned char>(c))); });
    return result;
}

void jtools::StringOperation::StringSplit(std::vector<std::string>& vecSplitResult, const std::string& str, const char split)
{
    // 预分配足够的空间
    size_t estimatedTokenCount = std::count(str.begin(), str.end(), split) + 1;
    vecSplitResult.reserve(estimatedTokenCount);

    std::string token;
    size_t startPos = 0;
    size_t foundPos = str.find(split);

    while (foundPos != std::string::npos)
    {
        // 使用原始字符串操作来提取子串
        token = str.substr(startPos, foundPos - startPos);
        vecSplitResult.push_back(std::move(token));

        startPos = foundPos + 1;
        foundPos = str.find(split, startPos);
    }

    // 处理剩余部分
    token = str.substr(startPos);
    vecSplitResult.push_back(std::move(token));
}

std::vector<std::string> jtools::StringOperation::SplitString(const std::string& str, char delimiter)
{
    std::vector<std::string> result_split;
    // 预分配足够的空间
    size_t estimatedTokenCount = std::count(str.begin(), str.end(), delimiter) + 1;
    result_split.reserve(estimatedTokenCount);

    std::string token;
    size_t startPos = 0;
    size_t foundPos = str.find(delimiter);

    while (foundPos != std::string::npos)
    {
        // 使用原始字符串操作来提取子串
        token = str.substr(startPos, foundPos - startPos);
        result_split.push_back(std::move(token));

        startPos = foundPos + 1;
        foundPos = str.find(delimiter, startPos);
    }

    // 处理剩余部分
    token = str.substr(startPos);
    result_split.push_back(std::move(token));
    return result_split;
}
std::string jtools::StringOperation::ReserveString(const std::string& str)
{
    std::string result = str;
    std::reverse(result.begin(), result.end());
    return result;
}

std::string jtools::StringOperation::ReplaceString(const std::string& str, const std::string& from, const std::string& to)
{
    if (str.empty())
    {
        return str;
    }
    if (from.empty())
    {
        return str; // 防止无限循环
    }
    if (from == to)
    {
        return str;
    }
    std::string result = str;
    size_t startPos = 0;
    while ((startPos = result.find(from, startPos)) != std::string::npos)
    {
        result.replace(startPos, from.length(), to);
        startPos += to.length(); // 移动位置，防止无限循环
    }
    return result;
}

std::string jtools::StringOperation::Trim(const std::string& str)
{ // 找到第一个非空白字符的位置
    size_t start = 0;
    while (start < str.length() && std::isspace(static_cast<unsigned char>(str[start])))
    {
        ++start;
    }

    // 如果字符串全是空白字符，则返回空字符串
    if (start == str.length())
    {
        return "";
    }

    // 找到最后一个非空白字符的位置
    size_t end = str.length() - 1;
    while (end > start && std::isspace(static_cast<unsigned char>(str[end])))
    {
        --end;
    }

    // 返回去除前后空白字符的子字符串
    return str.substr(start, end - start + 1);
}

std::pair<std::string, int> jtools::StringOperation::GetPrefixAndNumber(const std::string& src_str_)
{
    std::regex regex(R"((.*?)(\d+)$)");
    std::smatch match;
    if (std::regex_match(src_str_, match, regex)) {
        std::string prefix = match[1];
        int number = std::stoi(match[2]);
        return { prefix, number };
    }
    return { src_str_, -1 }; // 如果没有数字，返回整个字符串作为前缀，数字为 -1
}
