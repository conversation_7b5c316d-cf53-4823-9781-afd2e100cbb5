/*****************************************************************
 * @file   correctflow.h
 * @brief  矫正流程，主要功能是通过矫正点矫正工程参数
 * @details 在ARP中主要是在拍照检测之前，先扫描MARK点对工程中元件位置进行矫正
 * @note TODO:后期需要优化算法检测部分代码，整理合并 by zhangyuyu 2024.12.30
 * <AUTHOR>
 * @date 2024.11.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.21          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef __JRSCORRECTFLOW_H__
#define __JRSCORRECTFLOW_H__
 //PREBUILD
#include "workflowpch.h"
 //STD
//Custom
//#include "workflowinterfaces.h"
#include "calcfov.h"
#include "fovplan.h"
#include "pcbpathplan.h"
#include "projectdataprocess.h"
#include "coordinatetransformationtool.h"

//Third
namespace jrsalgo
{
    class AlgorithmEngineManager;
}

namespace jrsworkflow
{
  
    class CorrectFlow :public ICorrectFlow
    {
        public:
            CorrectFlow(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_, LogicFunType logic_invoke_);
            ~CorrectFlow ();
            int CorrectProject ( const  std::shared_ptr<jrsdata::ProjectParam>& project_param_ ) override;
            int StopCorrect()override;
            void SetWorkFlowParam(const WorkFlowParam& param_) override;
            
            /**
             * @fun SetMarkInfo 
             * @brief 设置MARK点的信息
             * @param fov_centers [IN] MARK点FOV的中心坐标，用于拍照
             * @param fov_out_ [IN] MARK点FOV规划的结果
             * @param mark_point_info_ [IN] MARK点的信息
             * <AUTHOR>
             * @date 2025.4.9
             */
            void SetMarkInfo(const std::vector<PCBPathPlanning::Fov>& fov_centers, const PCBPathPlanning::OutputParams& fov_out_,const std::unordered_map<int/*FOV的ID*/, std::vector<jrsdata::Component>>& mark_point_info_);
            
            /**
             * @fun AddMarkFovBuffer 
             * @brief 添加MARK点的图片
             * @param fov_img [IN] MARK点的FOV图片
             * <AUTHOR>
             * @date 2025.4.9
             */
            void AddMarkFovBuffer(const jrsdata::JrsImageBuffer& fov_img);

            /**
             * @fun SetFlowInspectionResultPtr 
             * @brief 设置流程检测结果指针，用于将MARK检测结果传递出去
             * @param flow_inspection_result_[OUT]整个板子的检测结果
             * <AUTHOR>
             * @date 2025.4.9
             */
            void SetFlowInspectionResultPtr(const FlowInspectionResultParamPtr& flow_inspection_result_);
        private:
            void Init ();
            void InitMember ();

            /**
             * @fun SaveMarkExecuteParamInfo 
             * @brief 保存MARK点的执行参数信息
             * @param component_value [IN] mark元件信息
             * @param algo_detect_result_ [IN] 算法检测结果
             * @param algo_name_ [IN] 算法名称
             * @param algo_param_ [IN] 算法参数
             * <AUTHOR>
             * @date 2025.5.9
             */
            void SaveMarkExecuteParamInfo(const jrsdata::Component& component_value, const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const std::string& algo_name_, const std::string& algo_param_);
            
            /**
             * @fun ValidateMark 
             * @brief 验证算法识别出来的mark是否正常
             * @param src [IN] 源mark坐标
             * @param dst [IN] 算法检测出来的mark坐标
             * @param threshold [IN] 允许的误差
             * @return 正常返回true,否则返回false
             * <AUTHOR>
             * @date 2025.5.9
             */
            bool ValidateMark(const std::vector<jrstool::PointLabel<float>>& src,
                const std::vector<jrstool::PointLabel<float>>& dst,
                float threshold = 100.f);
            //Member
            std::vector<PCBPathPlanning::Fov> mark_fov_centers;
            PCBPathPlanning::OutputParams fov_out;
            std::unordered_map<int/*FOV的ID*/, std::vector<jrsdata::Component>> assign_mark_point;
            LogicFunType logic_invoke;
            std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_manager_ptr;/**< 算法引擎指针 */
            std::mutex mark_correct_mtx; /**< mark矫正互斥锁 */
            std::mutex mark_result_mtx; /**< mark结果互斥锁 */
            std::condition_variable mark_correct_cv; /**< mark矫正条件变量 */
            std::atomic<bool> mark_correct_finish;/**< mark矫正是否结束 */

            std::shared_ptr<jrsdata::ProjectParam> project_ptr;//! 工程数据

            jrsparam::ProjectDataProcessPtr project_data_process_ptr;/**< 工程数据处理指针*/

            std::vector<jrstool::PointLabel<float>> src_mark_coordinate; /**<源mark坐标 */
            std::vector<jrstool::PointLabel<float>> dst_mark_coordinate; /**<算法检测出来的MARK坐标*/
            std::atomic<size_t> mark_fov_count; /**<已经接受到的mark fov数量*/

            WorkFlowParam work_flow_param;/**< 工作流参数 */

            FlowInspectionResultParamPtr  flow_inspection_result; /**< 整板检测结果*/
    };

}

#endif // !__JRSCORRECTFLOW_H__
