// Custom
#include "trackbase.h"
#include "coreapplication.h"

namespace jrsdevice
{
    TrackBase::TrackBase(std::shared_ptr<Motion> &motion, bool &running)
        : motion_ptr(motion), process_running(running)
    {
        exit_ = false;

        //track_load_thread = std::thread(&TrackBase::LoadThread, this);

        //track_unload_thread = std::thread(&TrackBase::UnLoadThread, this);

        //track_photo_thread = std::thread(&TrackBase::PhotoThread, this);
    }

    TrackBase::~TrackBase()
    {
        exit_ = true;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        if (track_load_thread.joinable())
        {
            track_load_thread.join();
        }
        if (track_unload_thread.joinable())
        {
            track_unload_thread.join();
        }
        if (track_photo_thread.joinable())
        {
            track_photo_thread.join();
        }
    }

    void TrackBase::SetPhotoCallBack(PhotoCallBack callback_)
    {
        photo_callback = callback_;
    }

    void TrackBase::SetLoadFailCallBack(PhotoCallBack callback_)
    {
        load_fail_callback = callback_;
    }

    void TrackBase::SetUnLoadFailCallBack(PhotoCallBack callback_)
    {
        unload_fail_callback = callback_;
    }

    void TrackBase::AddTrackProcess(jrsdevice::TrackIndex trackIndex, jrsdevice::TrackProcess process)
    {
        auto it = track_process.find(trackIndex);
        if (it == track_process.end())
        {
            std::vector<jrsdevice::TrackProcess> processList;
            processList.push_back(process);
            track_process[trackIndex] = processList;
        }
        else
        {
            track_process[trackIndex].push_back(process);
        }
    }

    void TrackBase::RemoveTrackProcess(jrsdevice::TrackIndex trackIndex)
    {
        auto it = track_process.find(trackIndex);
        if (it != track_process.end())
        {
            track_process.erase(it);
        }
    }

    void TrackBase::RemoveAllTrack()
    {
        track_process.clear();
    }

    void TrackBase::LoadThread()
    {
        while (!exit_)
        {
            if (process_running)
            {
                // 可以上料的轨道
                std::vector<jrsdevice::TrackIndex> tracks_to_load;

                {
                    std::lock_guard<std::mutex> lock(track_process_mutex);
                    for (const auto &pair : track_process)
                    {
                        std::vector<jrsdevice::TrackProcess> process = pair.second;
                        for (int i = 0; i < process.size(); i++)
                        {
                            if (i == 0 && process[i].track_load_state == TrackLoadState::UnLoadSuccess)
                            {
                                tracks_to_load.push_back(pair.first);
                                break;
                            }
                            // 当前工位处于下料完成并且上一工位上料完成、拍照完成才允许上料
                            if (i > 0 && process[i].track_load_state == TrackLoadState::UnLoadSuccess && process[i - 1].track_load_state == TrackLoadState::LoadSuccess && process[i - 1].track_photo_state == PhotoState::PhotoSuccess)
                            {
                                tracks_to_load.push_back(pair.first);
                                break;
                            }
                        }
                    }
                }

                std::vector<std::thread> threads;
                for (const auto &track : tracks_to_load)
                {
                    threads.emplace_back([this, track]()
                                         {
                                            // 上料工位
                                            WorkStation cur = WorkStation::Unknown;
                                            {
                                                std::lock_guard<std::mutex> lock(track_process_mutex);
                                                auto &track_pro = this->track_process[track];
                                                for (int i = 0; i < track_pro.size(); i++)
                                                {
                                                    if (track_pro[i].track_load_state == TrackLoadState::UnLoadSuccess)
                                                    {
                                                        track_pro[i].track_load_state = TrackLoadState::Loading;
                                                        cur = track_pro[i].track_station;
                                                        Log_ERROR("轨道" + std::to_string((int)track) + "开始上料 ");
                                                        break;
                                                    }
                                                }
                                            }

                                            // 根据工位执行对应的上料
                                            int res = 0;
                                            if(cur == WorkStation::Station1)
                                            {
                                                res = motion_ptr->Load(track);
                                            }
                                            else if(cur == WorkStation::Station2)
                                            {
                                                res = motion_ptr->Load2(track);
                                            }
                                            if(res == 0)
                                            {
                                                std::lock_guard<std::mutex> lock(track_process_mutex);
                                                auto& track_pro = this->track_process[track];
                                                for (int i = 0; i < track_pro.size(); i++)
                                                {
                                                    if (track_pro[i].track_load_state == TrackLoadState::Loading)
                                                    {
                                                        track_pro[i].track_load_state = TrackLoadState::LoadSuccess;
                                                        Log_ERROR("轨道" + std::to_string((int)track) + "上料完成 ");
                                                        break;
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if(load_fail_callback)
                                                {
                                                    load_fail_callback(track,cur);
                                                }
                                                Log_ERROR("上料失败 ");
                                                auto errList = motion_ptr->GetScriptErrorList();
                                                for (int i = 0; i < errList.size(); i++)
                                                {
                                                    Log_ERROR(errList.at(i));
                                                }
                                            } });
                }

                for (auto &thread : threads)
                {
                    thread.join();
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
    }

    void TrackBase::UnLoadThread()
    {
        while (!exit_)
        {
            if (process_running)
            {
                std::vector<jrsdevice::TrackIndex> tracks_to_unload;

                {
                    std::lock_guard<std::mutex> lock(track_process_mutex);
                    for (const auto &pair : track_process)
                    {
                        std::vector<jrsdevice::TrackProcess> process = pair.second;

                        // 当前轨道是否允许下料(必须所有流程都是拍照完成状态)
                        bool alowUnload = true;
                        for (int i = 0; i < process.size(); i++)
                        {
                            if (process[i].track_photo_state != PhotoState::PhotoSuccess || process[i].track_load_state != TrackLoadState::LoadSuccess)
                            {
                                alowUnload = false;
                                break;
                            }
                        }
                        if (alowUnload)
                        {
                            tracks_to_unload.push_back(pair.first);
                        }
                    }

                    std::vector<std::thread> threads;
                    for (const auto &track : tracks_to_unload)
                    {
                        threads.emplace_back([this, track]()
                                             {
                                             {
                                                std::lock_guard<std::mutex> lock(track_process_mutex);
                                                auto &track_pro = track_process[track];

                                                for (int i = 0; i < track_pro.size(); i++)
                                                {
                                                    track_pro[i].track_load_state = TrackLoadState::UnLoading;
                                                }
                                                Log_ERROR("轨道" + std::to_string((int)track) + "开始下料 ");
                                             }

                                             if (motion_ptr->UnLoad(track) == 0)
                                             {
                                                std::lock_guard<std::mutex> lock(track_process_mutex);
                                                auto &track_pro = track_process[track];

                                                for (int i = 0; i < track_pro.size(); i++)
                                                {
                                                    track_pro[i].track_load_state = TrackLoadState::UnLoadSuccess;
                                                    track_pro[i].track_photo_state = PhotoState::NotStart;
                                                }
                                                Log_ERROR("轨道" + std::to_string((int)track) + "下料完成 ");
                                             }
                                             else
                                             {
                                                if(load_fail_callback)
                                                {
                                                    auto &track_pro = track_process[track];
                                                    load_fail_callback(track,track_pro[track_pro.size() - 1].track_station);
                                                }
                                                Log_ERROR("下料失败 ");
                                                auto errList = motion_ptr->GetScriptErrorList();
                                                for (int i = 0; i < errList.size(); i++)
                                                {
                                                    Log_ERROR(errList.at(i));
                                                }
                                             } });
                    }

                    for (auto &thread : threads)
                    {
                        thread.join();
                    }
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
    }

    void TrackBase::PhotoThread()
    {
        while (!exit_)
        {
            if (process_running)
            {
                std::vector<jrsdevice::TrackIndex> tracks_to_photo;

                {
                    std::lock_guard<std::mutex> lock(track_process_mutex);
                    for (const auto &pair : track_process)
                    {
                        std::vector<jrsdevice::TrackProcess> process = pair.second;
                        for (int i = 0; i < process.size(); i++)
                        {
                            if (process[i].track_load_state == TrackLoadState::LoadSuccess && process[i].track_photo_state == PhotoState::NotStart)
                            {
                                tracks_to_photo.push_back(pair.first);
                                break;
                            }
                        }
                    }
                }

                for (const auto &track : tracks_to_photo)
                {
                    WorkStation cur = WorkStation::Station1;
                    {
                        std::lock_guard<std::mutex> lock(track_process_mutex);
                        auto &track_pro = track_process[track];
                        for (int i = 0; i < track_pro.size(); i++)
                        {
                            if (track_pro[i].track_photo_state == PhotoState::NotStart)
                            {
                                track_pro[i].track_photo_state = PhotoState::PhotoInprocess;
                                cur = track_pro[i].track_station;
                                break;
                            }
                        };
                        Log_ERROR("轨道" + std::to_string((int)track) + "开始拍照 ");
                    }

                    // 拍照
                    if (photo_callback)
                    {
                        auto future = std::async(std::launch::async, photo_callback, track, cur);
                        // 等待异步操作完成并获取返回值
                        int result = future.get();
                        if (result == 0)
                        {
                            std::lock_guard<std::mutex> lock(track_process_mutex);
                            auto &track_pro = track_process[track];
                            for (int i = 0; i < track_pro.size(); i++)
                            {
                                if (track_pro[i].track_station == cur)
                                {
                                    track_pro[i].track_photo_state = PhotoState::PhotoSuccess;
                                    break;
                                }
                            }
                            Log_ERROR("轨道" + std::to_string((int)track) + "拍照结束 ");
                        }
                        else
                        {
                            // 拍照失败处理(暂时停止流程,后续再修改)
                            process_running = false;
                            motion_ptr->AskStop();
                            Log_ERROR("轨道" + std::to_string((int)track) + "拍照失败,流程停止 ");
                        }
                    }
                    else
                    {
                        process_running = false;
                        motion_ptr->AskStop();
                        Log_ERROR("未设置拍照流程,轨道" + std::to_string((int)track) + "拍照失败,流程停止 ");
                    }
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
    }
}
