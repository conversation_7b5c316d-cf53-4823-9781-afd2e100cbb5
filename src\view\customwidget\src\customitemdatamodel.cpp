#include "customitemdatamodel.h"
namespace jrsaoi
{
    CustomDataModel::CustomDataModel(QObject* parent = nullptr)
    : QStandardItemModel(parent)
    {
    }

    void CustomDataModel::SetHorizontalHeaderData(const QStringList& headers) 
    {
        m_horizontal_headers = headers;
    }

    void CustomDataModel::SetVerticalHeaderData(const QStringList& headers) 
    {
        m_vertical_headers = headers;
    }

    std::vector<QVariant> CustomDataModel::GetRowData(int row) const
    {
        if (row >= 0 && row < static_cast<int>(m_data.size()))
        {
            return m_data[row];
        }
        return std::vector<QVariant>(); // 如果越界则返回一个空vector
    }



    void CustomDataModel::UpdateRowData(int row, const std::vector<QVariant>& new_data)
    {
        if (row >= 0 && row < rowCount())
        {
            for (int col = 0; col < columnCount(); ++col)
            {
                m_data[row][col] = new_data[col];
            }
            QModelIndex top_left = index(row, 0);
            QModelIndex bottom_right = index(row, columnCount() - 1);
            emit dataChanged(top_left, bottom_right);
        }
    }

    void CustomDataModel::SetColorRules(const RowColorRule& rule)
    {
        m_color_rule = rule;
    }

    int CustomDataModel::rowCount(const QModelIndex& parent ) const
    {
        Q_UNUSED(parent);
        return static_cast<int>(m_data.size());
    }

    int CustomDataModel::columnCount(const QModelIndex& parent) const  
    {
        Q_UNUSED(parent);
        return m_horizontal_headers.size();
    }

    QVariant CustomDataModel::data(const QModelIndex& index, int role) const  
    {
        if (!index.isValid())
            return QVariant();

        if (role == Qt::DisplayRole) {
            return m_data[index.row()][index.column()];
        }
        if (role == Qt::BackgroundRole)
        {
            int item_index = m_horizontal_headers.indexOf(m_color_rule.item_name);
            if (item_index >= 0 && item_index < columnCount())
            {
                const QVariant& item_name_temp = m_data[index.row()][item_index];
                auto color_opt = m_color_rule.match(item_name_temp);
                if (color_opt.has_value())
                {
                    return color_opt.value();
                }
            }
            

        }
        return QVariant();
    }

    QVariant CustomDataModel::headerData(int section, Qt::Orientation orientation, int role) const 
    {
        if (role == Qt::DisplayRole) {
            if (orientation == Qt::Horizontal) 
            {
                // 返回水平表头数据
                return m_horizontal_headers.value(section);
            }
            else if (orientation == Qt::Vertical) 
            {
                // 返回垂直表头数据
                return m_vertical_headers.value(section);
            }
        }
        return QVariant();
    }

    bool CustomDataModel::insertRows(int row, int count, const QModelIndex& parent)
    {
        Q_UNUSED(parent);
        if (row < 0 || row > rowCount())
            return false;

        beginInsertRows(QModelIndex(), row, row + count - 1);
        for (int i = 0; i < count; ++i) 
        {
            std::vector<QVariant> new_row(columnCount());
            m_data.insert(m_data.begin() + row, new_row);
        }
        endInsertRows();
        return true;
    }

    bool CustomDataModel::removeRows(int row, int count, const QModelIndex& parent)
    {
        Q_UNUSED(parent);
        if (row < 0 || row >= rowCount() || count <= 0 || (row + count) > rowCount())
            return false;

        beginRemoveRows(QModelIndex(), row, row + count - 1);
        m_data.erase(m_data.begin() + row, m_data.begin() + row + count);
        endRemoveRows();
        return true;
    }

    QModelIndex CustomDataModel::index(int row, int column, const QModelIndex& parent) const
    {
        if (!hasIndex(row, column, parent))
            return QModelIndex();

        if (!parent.isValid()) 
        {
            return createIndex(row, column);
        }
        else 
        {
            return QModelIndex();  
        }

    }

    void CustomDataModel::DeleteRowByColumnValue(int column, const QVariant& target_value)
    {
        // 遍历所有行
        for (int row = rowCount() - 1; row >= 0; --row)
        {
            // 获取指定列的数据
            QVariant cellValue = data(index(row, column), Qt::DisplayRole);
            // 检查是否匹配
            bool is_match = false;
            is_match = (cellValue == target_value);
            // 如果匹配，则删除该行
            if (is_match)
            {
                removeRow(row);
                break;
            }
        }
    }

    QModelIndex CustomDataModel::parent(const QModelIndex& child) const
    {
        Q_UNUSED(child);
        return QModelIndex();

    }

    bool CustomDataModel::AddData(const QModelIndex& index, const QVariant& value, int role)
    {
        if (index.isValid() && role == Qt::EditRole)
        {
            m_data[index.row()][index.column()] = value;
            emit dataChanged(index, index);
            return true;
        }
        return false;
    }

    void CustomDataModel::ClearAllData()
    {
        
        beginResetModel(); // 通知视图数据即将发生较大变更
        m_data.clear();    // 清空数据存储
        clear();           // 清空 QStandardItemModel
        endResetModel();   // 通知视图数据已经更新
        
    }

    // 设置数据
    void CustomDataModel::SetData(const std::vector<std::vector<QVariant>>& data)
    {
        beginResetModel();
        if (!m_data.empty())
        {
            m_data.clear();
        }
        m_data = data;
        endResetModel();
    }

    void CustomDataModel::AddRows(const std::vector<std::vector<QVariant>>& rows_data)
    {
        if (rows_data.empty())
        {
            return;
        }
        int row = rowCount();
        int count = static_cast<int>(rows_data.size());
        if (insertRows(row, count, QModelIndex())) 
        {
            for (int i = 0; i < count; ++i) 
            {
                for (int col = 0; col < columnCount(); ++col) 
                {
                    AddData(index(row + i, col), rows_data[i][col], Qt::EditRole);
                }
            }
        }
    }

    void CustomDataModel::DeleteRows(int row, int count)
    {
        removeRows(row,count);
    }
}