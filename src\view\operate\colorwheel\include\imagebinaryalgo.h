/*****************************************************************//**
 * @file   binaryalgo.h
 * @brief  二值化算法实现
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSBINARYALGO_H__
#define __JRSBINARYALGO_H__
#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "colorparams.h"

#include "opencv2/core/mat.hpp"
#pragma warning(pop)
using vecf = std::vector<float>;
using cv::Mat;
using Mat3 = std::vector<Mat>;

class BinaryAlgo
{
public:
	/**
   * @fun  GetBinaryImage
   * @brief  获取二值图
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetBinaryImage(const Mat& image,
		BinProcessParams& params, Mat& binary);
	bool GetImageIsBinary() const;
public:
	/**
   * @fun  GetRgbHist
   * @brief  获取RGB竖向柱状图
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetRgbHist(const cv::Mat& rgb_image,vecf& r_hist,vecf& g_hist,vecf& b_hist);
	/**
   * @fun  GetGrayHist
   * @brief  获取单通道竖向柱状图
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetGrayHist(const cv::Mat& gray_image, vecf& gray_hist, int mode,cv::Mat& gray);
	/**
   * @fun  GetHeightHist
   * @brief  获取高度竖向柱状图
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetHeightHist(const cv::Mat& height_image, vecf& height_hist, 
		int bin_nums,float min_height,float max_height);
private:
	/**
   * @fun  ConvertToGrayWidge
   * @brief  权重灰度
   * @date   2024.08.18
   * <AUTHOR>
   */
	cv::Mat ConvertToGrayWidge(const cv::Mat& image);
	/**
   * @fun  ConvertToGrayMax
   * @brief  最大灰度
   * @date   2024.08.18
   * <AUTHOR>
   */
	cv::Mat ConvertToGrayMax(const cv::Mat& image);
	/**
   * @fun  ConvertToGrayMin
   * @brief  最小灰度
   * @date   2024.08.18
   * <AUTHOR>
   */
	cv::Mat ConvertToGrayMin(const cv::Mat& image);
	/**
   * @fun  ConvertToGrayAvg
   * @brief  平均灰度
   * @date   2024.08.18
   * <AUTHOR>
   */
	cv::Mat ConvertToGrayAvg(const cv::Mat& image);
	/**
   * @fun  CalculateNormalizedHistogram
   * @brief  计算并归一化竖向柱状图
   * @date   2024.08.18
   * <AUTHOR>
   */
	vecf CalculateNormalizedHistogram(const cv::Mat& image, int hist_size);
	/**
   * @fun  GetRgbBinary
   * @brief  获取rgb二值化结果
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetRgbBinary(const Mat& rgb_image, int r_min, 
		int r_max, int g_min, int g_max, 
		int b_min, int b_max);
	/**
   * @fun  GetGrayBinary
   * @brief  获取灰度图二值化结果
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetGrayBinary(const Mat& rgb_image, int gray_min, 
		int gray_max);
	/**
   * @fun  GetHsvBinary
   * @brief  获取HSV二值化结果
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetHsvBinary(const Mat& rgb_image, ColorWheelThreshVal& params);
	/**
   * @fun  GetHeightBinary
   * @brief  获取高度二值化结果
   * @date   2024.08.18
   * <AUTHOR>
   */
	int GetHeightBinary(const Mat& height_image, int bin_nums ,
		int bin_min, int bin_max,float hei_min,float hei_max);
	void GetHsTableAs2DArray(std::vector<unsigned char>& hs_table_one_dim,
		unsigned char hs_table[180][256]);
	cv::Mat binary_;
	bool m_image_is_binary = false;
};
#endif