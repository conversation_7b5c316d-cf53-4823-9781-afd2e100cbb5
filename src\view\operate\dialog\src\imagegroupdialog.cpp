#include "imagegroupdialog.h"
#include "ui_imagegroupdialog.h"
#include "customlistwidget.h"

//QT
#include <QPushButton>
#include <QListWidgetItem>
namespace jrsaoi
{
	struct ImplData
	{
		CustomListWidget* image_group_list;
		Ui::ImageGroupDialog* ui;
	};
}
jrsaoi::ImageGroupDialog::ImageGroupDialog(QWidget* parent)
	: QDialog(parent)
	, impl_data(new ImplData)
{
	InitMember();
	InitView();
	InitConnect();
}

jrsaoi::ImageGroupDialog::~ImageGroupDialog()
{
	delete impl_data->ui;
}


void jrsaoi::ImageGroupDialog::SloSaveImages()
{
	auto current_group_name = impl_data->ui->edit_project_path->text().toStdString();

}

void jrsaoi::ImageGroupDialog::InitMember()
{
	impl_data->ui = new Ui::ImageGroupDialog;
	impl_data->image_group_list = new CustomListWidget();
}

void jrsaoi::ImageGroupDialog::InitView()
{
	impl_data->ui->setupUi(this);
	impl_data->ui->btn_affirm->setIcon(QIcon(":image/affirm.png"));
	impl_data->ui->btn_cancel->setIcon(QIcon(":image/cancel.png"));  // 设置取消按钮图标
	impl_data->ui->edit_project_path->setReadOnly(true);
	impl_data->ui->h_layout_image_group_list->addWidget(impl_data->image_group_list);
}

void jrsaoi::ImageGroupDialog::InitConnect()
{
	connect(impl_data->ui->btn_affirm, &QPushButton::clicked, this, &ImageGroupDialog::SloSaveImages);
}

