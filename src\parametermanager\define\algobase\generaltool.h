/*****************************************************************//**
 * @file   generaltool.h
 * @brief  通用工具类
 * @note 
 * @details
 * <AUTHOR>
 * @date 2024.12.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.18         <td>V1.0              <td>liuchenfan      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef GENERAL_TOOL_H_
#define GENERAL_TOOL_H_
#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>
namespace jrsoperator
{
    /**
    * @fun CropImage
    * @brief 矩形截图
    * @param src 输入图像
    * @param dst 输出图像
    * @param rect 矩形区域
    * @date 2024.12.18
    * <AUTHOR>
    */
    bool CropImage(const cv::Mat& src, cv::Mat& dst, const cv::Rect& rect);
    /**
    * @fun CropImage
    * @brief 旋转矩形截图
    * @param src 输入图像
    * @param dst 输出图像
    * @param rotate_rect 旋转矩形
    * @date 2024.12.18
    * @note 旋转矩形的宽度与其方向所在的向量平行，高度与其方向所在的向量垂直; 裁剪后的旋转矩形会将方向旋转到0°;
    * <AUTHOR>
    */
    bool CropImage(const cv::Mat& src, cv::Mat& dst, const cv::RotatedRect& rotate_rect);
    /**
    * @fun JudgePointOutImage
    * @brief 判断点是否在图像外
    * @param max_x 图像最大x坐标
    * @param max_y 图像最大y坐标
    * @param test_point 测试点
    * @date 2024.12.18
    * <AUTHOR>
    */
    bool JudgePointOutImage(int max_x, int max_y, const cv::Point2f& test_point);
    /**
    * @fun GetRotateRigion
    * @brief 旋转矩形截图
    * @param src 输入图像
    * @param dst 输出图像
    * @param rotate_rect 旋转矩形
    * @date 2024.12.18
    * <AUTHOR>
    */
    bool GetRotateRigion(const cv::Mat& src, cv::Mat& dst, const cv::RotatedRect& rotate_rect);
    /**
    * @fun WarpMatrixToRect
    * @brief 根据旋转矩形和变换矩阵计算旋转矩形
    * @param src_rect 输入矩形
    * @param hom_matrix 变换矩阵
    * @param rotate_rect 输出旋转矩形
    * @param 以自身中点为变换中心
    * @date 2024.12.18
    * <AUTHOR>
    */
    bool WarpMatrixToRect(const cv::Rect2f& src_rect,
        const cv::Mat& hom_matrix, cv::RotatedRect& rotate_rect);
    /**
    * @fun WarpMatrixToRect
    * @brief 根据旋转矩形和变换矩阵计算旋转矩形
    * @param src_rect 输入矩形
    * @param hom_matrix 变换矩阵
    * @param rotate_rect 输出旋转矩形
    * @param center 变换中心
    * @date 2024.12.18
    * <AUTHOR>
    */
    bool WarpMatrixToRect(const cv::Rect2f& src_rect,
        const cv::Mat& hom_matrix, const cv::Point2f center, cv::RotatedRect& rotate_rect);
    /**
    * @fun    AffineTransformToPt
    * @brief  对单点应用仿射变换矩阵
    * @param  affine_matrix 仿射变换矩阵
    * @param  point 待变换点
    * @note   绕原点(0,0）旋转
    * @return 变换后的点
    * @date   2024.12.18
    * <AUTHOR>
    */
    cv::Point2f AffineTransformToPt(const cv::Mat& hom_matrix,
        const cv::Point2f& point);
    /**
   * @fun    AffineTransformToPts
   * @brief  对多点应用仿射变换矩阵
   * @param  affine_matrix 仿射变换矩阵
   * @param  point 待变换点
   * @note   绕原点(0,0）旋转
   * @return 变换后的点
   * @date   2024.12.18
   * <AUTHOR>
   */
    std::vector<cv::Point2f> AffineTransformToPts(const cv::Mat& hom_matrix,
        std::vector<cv::Point2f>& point);
   /**
   * @fun    AffineTransformToPt
   * @brief  对单点应用仿射变换矩阵
   * @param  affine_matrix 仿射变换矩阵
   * @param  point 待变换点
   * @param  rotate_center 旋转中心
   * @note   绕rotate_center旋转
   * @return 变换后的点
   * @date   2024.12.18
   * <AUTHOR>
   */
    cv::Point2f AffineTransformToPt(const cv::Mat& hom_matrix,
        const cv::Point2f& point, const cv::Point2f& rotate_center);
    /**
   * @fun    AffineTransformToPts
   * @brief  对多点应用仿射变换矩阵
   * @param  affine_matrix 仿射变换矩阵
   * @param   rotate_center 旋转中心
   * @note   绕rotate_center旋转
   * @return 变换后的点
   * @date   2024.12.18
   * <AUTHOR>
   */
    std::vector<cv::Point2f> AffineTransformToPts(const cv::Mat& hom_matrix,
        std::vector<cv::Point2f>& point, const cv::Point& rotate_center);
    /**
   * @fun    CreateRotatedRect
   * @brief  创建旋转矩形(点需要顺时针)
   * @param  pt1 矩形顶点1
   * @param  pt2 矩形顶点2
   * @param  pt3 矩形顶点3
   * @param  success 是否成功
   * @date   2024.12.18
   * <AUTHOR>
   */
    cv::RotatedRect CreateRotatedRect(const cv::Point2f& pt1, const cv::Point2f& pt2, const cv::Point2f& pt3);
    /**
   * @fun    CreateMatrix
   * @brief  创建仿射变换矩阵
   * @param  angle 旋转角度
   * @param  x_offset x轴偏移
   * @param  y_offset y轴偏移
   * @date   2024.12.18
   * <AUTHOR>
   */
    cv::Mat CreateMatrix(float angle, float x_offset, float y_offset);
    /**
    * @fun    ReverseMatrix
    * @brief  反转变换矩阵
    * @param  hom_matrix 变换矩阵
    * @return 反转变换矩阵
    * @date   2025.01.15
    * <AUTHOR>
    */
    cv::Mat ReverseMatrix(const cv::Mat& hom_matrix);
    /**
    * @fun    AffineMatrixToMat
    * @brief  将仿射变换矩阵转换为图像
    * @param  image 输入图像
    * @param  hom_matrix 仿射变换矩阵
    * @return 图像
    * @date   2025.01.15
    * <AUTHOR>
    */
    cv::Mat AffineMatrixToMat(const cv::Mat& image, const cv::Mat& hom_matrix);
}
#pragma warning(pop)
#endif // GENERAL_TOOL_H_