/*****************************************************************//**
 * @file   horizontalbarchart.h
 * @brief 横向柱状图显示
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef HORIZONTALBARCHART_H
#define HORIZONTALBARCHART_H
 //QT
#include <QWidget>
#include <QPainter>
//STD
//CUSTOM
#include "datadefine.hpp"

class HorizontalBarChart : public QWidget
{
    Q_OBJECT
public:
    /**
     * @fun HorizontalBarChart
     * @brief 构造函数，初始化 HorizontalBarChart
     * @details 初始化自定义的表格项委托，设置父对象
     * @param parent 父对象
     * @date 2025.02.25
     * <AUTHOR>
     */
    explicit HorizontalBarChart(QWidget* parent = nullptr);
    ~HorizontalBarChart() override;

    /**
     * @fun SetPercent
     * @brief 设置是否显示百分比
     * @param percent 是否显示百分比
     * @return void
     * @date 2024.09.19
     * <AUTHOR>
     */
    void SetPercent(bool percent);

    /**
     * @fun SetDataInfo
     * @brief 设置横向柱状图数据信息
     * @param data_ 环形数据向量
     * @return void
     * @date 2024.09.19
     * <AUTHOR>
     */
    void SetDataInfo(const std::vector<RingData>& data_);

    /**
     * @fun ClearHorizontalBarChart
     * @brief 清空横向柱状图数据
     * @return void
     * @date 2024.09.19
     * <AUTHOR>
     */
    void ClearHorizontalBarChart();
protected:
    /**
     * @fun paintEvent
     * @brief 画图
     * @param
     * @date 2024.9.24
     * <AUTHOR>
     */
    void paintEvent(QPaintEvent*) override;

private:
    /**
     * @fun Init
     * @brief 初始化
     * @date 2024.2.7
     * <AUTHOR>
     */
    void Init();
    /**
     * @fun SetShowCount
     * @brief 设置是否显示数值
     * @param show_count
     * @date 2024.2.6
     * <AUTHOR>
     */
    void SetShowCount(bool show_count);
    /**
     * @fun CalculateMaxLabelWidth
     * @brief 计算所有标签中的最大宽度
     * @param fm QFontMetrics对象，用于测量文本宽度
     * @return 最大标签宽度
     * @date 2024.04.14
     * <AUTHOR>
     */
    int CalculateMaxLabelWidth(const QFontMetrics& fm);
    /**
     * @fun DrawAxes
     * @brief 绘制横向轴和竖向轴，以及竖向轴上的刻度线
     * @param painter 用于绘图的QPainter对象
     * @param x_start 横向轴起始点的x坐标
     * @param x_end 横向轴结束点的x坐标
     * @param y_start 横向轴的y坐标
     * @param y_end 竖向轴的y坐标
     * @param fm QFontMetrics对象，用于测量文本宽度（本函数中未使用）
     * @date 2024.04.14
     * <AUTHOR>
     */
    void DrawAxes(QPainter& painter, int xStart, int xEnd, int yStart, int yEnd, const QFontMetrics& fm);
    /**
     * @fun DrawBars
     * @brief 绘制横向条形图的每个条形及其旁边的数值和百分比
     * @param painter 用于绘图的 QPainter 对象
     * @param x_start 横向轴的起始 x 坐标
     * @param x_end 横向轴的结束 x 坐标
     * @param y_start 条形图的起始 y 坐标
     * @param y_end 条形图的结束 y 坐标
     * @param fm QFontMetrics 对象，用于测量文本宽度
     * @param max_label_width 最大标签宽度（本函数中未使用）
     * @date 2024.04.14
     * <AUTHOR>
     */
    void DrawBars(QPainter& painter, double xStart, double xEnd, double yStart, double yEnd, const QFontMetrics& fm, double maxLabelWidth);
    /**
     * @fun DrawPercentageScale
     * @brief 绘制横向轴上的百分比标尺，包括实线、虚线和百分比文本
     * @param painter 用于绘图的 QPainter 对象
     * @param x_start 横向轴的起始 x 坐标
     * @param x_end 横向轴的结束 x 坐标
     * @param y_start 横向轴的 y 坐标
     * @param fm QFontMetrics 对象，用于测量文本宽度
     * @date 2024.04.14
     * <AUTHOR>
     */
    void DrawPercentageScale(QPainter& painter, int xStart, int xEnd, int yStart, const QFontMetrics& fm);
    /**
     * @fun DrawVerticalLabels
     * @brief 绘制横向条形图左侧的竖向标签
     * @param painter 用于绘图的 QPainter 对象
     * @param x_start 横向轴的起始 x 坐标
     * @param y_start 条形图的起始 y 坐标
     * @param y_end 条形图的结束 y 坐标
     * @param fm QFontMetrics 对象，用于测量文本宽度
     * @date 2024.04.14
     * <AUTHOR>
     */
    void DrawVerticalLabels(QPainter& painter, int xStart, int yStart, int yEnd, const QFontMetrics& fm);

private:
    bool                            m_show_percent;                     /// 是否显示百分比
    bool                            m_show_count;                       /// 是否显示数字
    std::vector<std::vector<QRect>> m_text_rect;                        /// 显示文字的区域
    std::vector<RingData>           m_ring_data;                        /// 数据集合
    std::vector<QColor>             m_colors;                           /// 颜色集合
    std::vector<Qt::AlignmentFlag>  m_alignment;                        /// 对齐方式 每列文字
    int                             m_last_idx;                         /// 显示文字的最后一个索引
    double                          m_axis_width;                       /// X坐标轴刻度宽度
    double                          m_axis_height;                      /// Y坐标轴刻度宽度
    double                          m_max_percent;                      /// 最大百分比
    QPoint                          m_start_pos;                        /// 开始点
    QPoint                          m_axis_hor_start;                   /// 横轴开始点
    QPoint                          m_axis_hor_end;                     /// 横轴结束点
    QPoint                          m_axis_ver_start;                   /// 横轴开始点
    QPoint                          m_axis_ver_end;                     /// 横轴结束点
};
#endif
