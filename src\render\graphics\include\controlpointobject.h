/**********************************************************************
 * @brief  控制点派生类.
 *
 * @file   controlpointobject.h
 *
 * @date   2024.07.08
 * <AUTHOR>
**********************************************************************/

#ifndef CONTROLPOINTOBJECT_H
#define CONTROLPOINTOBJECT_H

#include "controlpointabstract.h"

class ControlPointSize : public ControlPointAbstract
{
public:
    ControlPointSize() {}
    ~ControlPointSize() {}

    void Response(const ResponseEventParam& param, GraphicsAbstract* const obj) override;
};

class ControlPointMove : public ControlPointAbstract
{
public:
    ControlPointMove() {}
    ~ControlPointMove() {}

    void Response(const ResponseEventParam& param, GraphicsAbstract* const obj) override;
    double TryResponse(const float& x, const float& y, const float& min_dis) const override;
};

class ControlPointRotate : public ControlPointAbstract
{
public:
    ControlPointRotate() {}
    ~ControlPointRotate() {}

    void Response(const ResponseEventParam& param, GraphicsAbstract* const obj) override;
};
#endif //! CONTROLPOINTOBJECT_H