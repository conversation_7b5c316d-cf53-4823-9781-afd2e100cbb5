#ifndef DLGTEMPLATELISTEDIT_H
#define DLGTEMPLATELISTEDIT_H

#include <QDialog>

namespace Ui {
class DlgTemplateListEdit;
}

class DlgTemplateListEdit : public QDialog
{
    Q_OBJECT

public:
    explicit DlgTemplateListEdit(QWidget *parent = nullptr);
    ~DlgTemplateListEdit();

    void SetTemplateListWidget(QWidget* widget);

signals:
    void SigDeleteTemplate();

private:
    Ui::DlgTemplateListEdit *ui;
};

#endif // DLGTEMPLATELISTEDIT_H
