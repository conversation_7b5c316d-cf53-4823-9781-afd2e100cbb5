/*****************************************************************
 * @file   subboardsortview.h
 * @brief
 * @details
 * <AUTHOR>
 * @date 2025.6.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.19          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom

 //Third
#include <QWidget>

namespace Ui {
    class SubboardSort;
}
namespace jrsdata
{
    struct SubboardSortParam;
}
class QToolButton;
class SubboardSortView : public QWidget
{
    Q_OBJECT
public:
    SubboardSortView(QWidget* parent = Q_NULLPTR);

    ~SubboardSortView();

signals:
    void SigUpdateSubboardSort(const jrsdata::SubboardSortParam& param_);
private:
    //!Functions
    void Init();
    void InitView();
    void InitMenber();
    void InitConnect();
    /**
     * @brief 设置 QToolButton 的图标等属性
     * @param button_ QToolButton 指针
     * @param icon_path_ 图标路径（例如 ":/icons/icon.png"）
     * @param icon_size_ 图标显示大小（默认 24x24）
     * @param tool_tip_ 可选提示文本
     */
    void SetToolButtonIcon(QToolButton* button_, const QString& icon_path_,
        const QSize& icon_size_ = QSize(36, 36),
        const QString& tool_tip_ = "");
    //!Members
    Ui::SubboardSort* ui;
    std::shared_ptr<jrsdata::SubboardSortParam> _subboard_sort_param;

};


