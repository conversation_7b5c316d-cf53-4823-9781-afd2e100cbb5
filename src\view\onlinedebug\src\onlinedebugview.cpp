#include "onlinedebugview.h"
#include "customitemdatamodel.h"
#include "customtablelistview.h"
//#include "detectresultparam.hpp"
#include "qtools.hpp"
namespace jrsaoi
{
    OnLineDebugView::OnLineDebugView(const std::string &name, QWidget *parent)
        :ViewBase(name,parent)
        , ui(new Ui::OnLineDebugView())
        , is_waitting_debug_info(true)
    {
        ui->setupUi(this);
        Init();
        this->setHidden(true);

    }

    OnLineDebugView::~OnLineDebugView()
    {
        delete ui;
    }
    int jrsaoi::OnLineDebugView::Init()
    {
        InitView();
        InitConnect();
        ui->checkBox_stop_online_debug->setChecked(true);
        return 0;
    }
    void OnLineDebugView::UpdateDebugComponentInfo(const jrsdata::OnlineDebugViewParamPtr& online_debug_info_)
    {
        if (!is_waitting_debug_info)
        {
            return;
        }
        if (!online_debug_info_)
        {
            return;
        }
        if (!online_debug_info_->online_debug_param)
        {
            return;
        }
        online_debug_param_ptr = online_debug_info_->online_debug_param;
        auto debug_component_lists = qtools::QTools::ConvertToQVariantVector(online_debug_param_ptr->ng_component_detect_results);
        if (debug_component_lists.empty())
        {
            return;
        }
        is_waitting_debug_info = false;
        component_show_list_view->UpdateList(debug_component_lists);
        auto detect_num = online_debug_param_ptr->ng_component_detect_results.size();
        ui->lineEdit_detect_num->setText(QString::number(detect_num));

        
    }
    void OnLineDebugView::ClearUiData()
    {
        component_show_list_view->ClearAllData();
        ui->lineEdit_detect_num->clear();
        ui->lineEdit_false_positive_num->clear();
        ui->lineEdit_bad_num->clear();

    }
    void OnLineDebugView::InitView()
    {
        //! 整板子信息显示
        auto head_data_component_ng_list = QStringList() << "编号" << "位号(元件名称)" << "子板号"<<"缺陷"<< "结果";
        auto label_name_show_list = QString("列表");
        RowColorRule color_rule;

        color_rule = RowColorRule({
         "结果",
         [](const QVariant& v) -> std::optional<QColor>
        {
            if (v.type() == QVariant::Bool)
            {
                return v.toBool() == true ? QColor(Qt::green) : QColor(Qt::red);
            }
            return std::nullopt;
        }
            });

        component_show_list_view = new CustomTableListView(head_data_component_ng_list, label_name_show_list, color_rule, 1,this);
        
        ui->verticalLayout_component_show_list->addWidget(component_show_list_view);

    }
    int OnLineDebugView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            this->setHidden(true);

        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            this->setHidden(false);
        }
        else if (param_->event_name == jrsaoi::DETECT_RESULT_ONLINE_DEBUG_INFO_EVENT_NAME)
        {
            auto online_debug_info = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
            UpdateDebugComponentInfo(online_debug_info);
        }
        else if (param_->event_name == jrsaoi::OPERATE_UPDATE_COMPONENT_RESULT_STATUS_EVENT_NAME)
        {
            if (!online_debug_param_ptr)
            {
                return jrscore::AOI_OK;
            }
            auto component_detect_status = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            //! 使用调试的结果将调试信息的内容更新
            for (auto& value : online_debug_param_ptr->ng_component_detect_results)
            {
                auto iter = component_detect_status->component_detect_results.find(value.first);
                if (iter != component_detect_status->component_detect_results.end())
                {
                    value.second.result_status = iter->second.result_status;
                }
            }



            auto current_row_data = component_show_list_view->GetCurrentRowData();
            if (current_row_data.empty())
            {
                return jrscore::AOI_OK;
            }
            auto current_component_name = current_row_data.at(1).toString().toStdString();
            auto current_component_show_id = current_row_data.at(0);

            auto iter = online_debug_param_ptr->ng_component_detect_results.find(current_component_name);
            if (iter != online_debug_param_ptr->ng_component_detect_results.end())
            {
                auto current_row_new_data = qtools::QTools::ConvertToQVariant(iter->second);
                current_row_new_data.insert(current_row_new_data.begin(), current_component_show_id);

                component_show_list_view->UpdateDataRowData(current_row_new_data);
            }
            /*auto debug_component_lists = qtools::QTools::ConvertToQVariantVector(online_debug_param_ptr->ng_component_detect_results);
            component_show_list_view->UpdateList(debug_component_lists);*/
            
        }
        return jrscore::AOI_OK;
    }
    int OnLineDebugView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return 0;
    }
    QWidget* OnLineDebugView::GetCustomWidget()
    {
        return nullptr;
    }
    void OnLineDebugView::SlotIsStopDebug(bool is_stop)
    {
        auto online_debug_param_temp_ptr_ = std::make_shared<jrsdata::OnlineDebugViewParam>();
        online_debug_param_temp_ptr_->module_name = jrsaoi::ONLINEDEBUG_MODULE_NAME;
        online_debug_param_temp_ptr_->topic_name = jrsaoi::ONLINEDEBUG_TOPIC_NAME;
        online_debug_param_temp_ptr_->sub_name = jrsaoi::ONLINEDEBUG_WORKFLOW_SUB_NAME;
        online_debug_param_temp_ptr_->event_name = jrsaoi::ONLINEDEBUG_DEBUG_IS_STOP_WORKFLOW_EVNET_NAME;
        online_debug_param_temp_ptr_->invoke_module_name = jrsaoi::WORKFLOW_MODULE_NAME;
        online_debug_param_temp_ptr_->is_stop_debug = is_stop;

        emit SigUpdateOnline(online_debug_param_temp_ptr_);
    }
    
    void OnLineDebugView::SlotSearchComponent()
    {

        std::cout<<"search"<<std::endl;
    }
    void OnLineDebugView::SlotUpComponent()
    {
        component_show_list_view->PreRow();
    }
    void OnLineDebugView::SlotDownComponent()
    {
        component_show_list_view->NextRow();
    }
    void OnLineDebugView::SlotConfirm()
    {
        is_waitting_debug_info = true;
        ClearUiData();
        online_debug_param_ptr.reset();

        auto online_debug_param_temp_ptr_ = std::make_shared<jrsdata::OnlineDebugViewParam>();
        online_debug_param_temp_ptr_->module_name = jrsaoi::ONLINEDEBUG_MODULE_NAME;
        online_debug_param_temp_ptr_->topic_name = jrsaoi::ONLINEDEBUG_FINISH_TOPIC_NAME;
        online_debug_param_temp_ptr_->sub_name = jrsaoi::ONLINEDEBUG_ALL_SUB_NAME;
        online_debug_param_temp_ptr_->event_name = jrsaoi::ONLINEDEBUG_DEBUG_FINISHED_SEND_EVNET_NAME;
        online_debug_param_temp_ptr_->invoke_module_name = jrsaoi::WORKFLOW_MODULE_NAME;
        online_debug_param_temp_ptr_->is_waitting_debug_info = is_waitting_debug_info;
        emit SigUpdateOnline(online_debug_param_temp_ptr_);
    }
    void OnLineDebugView::SlotExit()
    {
        std::cout<<"exit"<<std::endl;
    }
    void OnLineDebugView::SlotSearchComponentTextChange(const QString& text)
    {
        if (text.isEmpty())
        {
            return;
        }
        else
        {
            component_show_list_view->FileterTextChanged(text);
        }
    }
    void OnLineDebugView::SlotCurrentSelectedRow(const std::vector<QVariant>& current_row_data)
    {
        if (current_row_data.empty())
        {
            Log_ERROR("当前选中行数据为空!");
            return;
        }
        if (!online_debug_param_ptr)
        {
            return;
        }
        auto current_component_name = current_row_data.at(1).toString().toStdString();
        auto current_component_info = online_debug_param_ptr->ng_component_detect_results[current_component_name];
        auto online_debug_param_temp_ptr_ = std::make_shared<jrsdata::OnlineDebugViewParam>();
        online_debug_param_temp_ptr_->current_debug_info.current_component_name = current_component_info.component_name;
        online_debug_param_temp_ptr_->current_debug_info.current_subboard_name = current_component_info.sub_board_name;
        online_debug_param_temp_ptr_->current_debug_info.current_part_number = current_component_info.part_number;

        //! 目前没有跨fov元件，所以只有一个fov
        if (!current_component_info.fov_ids.empty())
        {
            auto fov_id = current_component_info.fov_ids[0];
            online_debug_param_temp_ptr_->current_debug_info.current_component_fov_img = online_debug_param_ptr->ng_fov_imgs[fov_id];
        }
        online_debug_param_temp_ptr_->module_name = jrsaoi::ONLINEDEBUG_MODULE_NAME;
        online_debug_param_temp_ptr_->topic_name = jrsaoi::ONLINEDEBUG_TOPIC_NAME;
        online_debug_param_temp_ptr_->sub_name = jrsaoi::ONLINEDEBUG_ALL_SUB_NAME;
        online_debug_param_temp_ptr_->event_name = jrsaoi::ONLINEDEBUG_CHANGE_DEBUG_COMPONENT_EVENT_NAME;
        emit SigUpdateOnline(online_debug_param_temp_ptr_);
    }
    void OnLineDebugView::InitConnect()
    {
        connect(ui->checkBox_stop_online_debug, &QCheckBox::clicked, this, &OnLineDebugView::SlotIsStopDebug);
        connect(ui->lineEdit_search_component, &QLineEdit::textChanged, this, &OnLineDebugView::SlotSearchComponentTextChange);
        connect(ui->pushButton_search_component,&QPushButton::clicked,this,&OnLineDebugView::SlotSearchComponent);
        connect(ui->pushButton_up,&QPushButton::clicked,this,&OnLineDebugView::SlotUpComponent);
        connect(ui->pushButton_down,&QPushButton::clicked,this,&OnLineDebugView::SlotDownComponent);
        connect(ui->pushButton_confirm,&QPushButton::clicked,this,&OnLineDebugView::SlotConfirm);
        connect(ui->pushButton_exit,&QPushButton::clicked,this,&OnLineDebugView::SlotExit);
        connect(component_show_list_view, &CustomTableListView::SigCurrentRowData, this, &OnLineDebugView::SlotCurrentSelectedRow, Qt::QueuedConnection);


    }
}