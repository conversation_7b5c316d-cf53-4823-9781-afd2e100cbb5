﻿#include <datamanager.h>
//Thirdparty
#pragma warning(push, 1)
#include "gtest/gtest.h"
#pragma warning(pop)

#ifdef _WIN32
#include <Windows.h>
#endif // _WIN32
#include "projectparam.hpp"
#include "dataparam.h"
/** Generate Test Data */
jrsdatabase::jrstable::TProject GenerateMockProject() {
    //return jrsdatabase::jrstable::TProject(
    //    "MockProjectName",          // project_name
    //    "MockBoardProductModel",    // board_product_model
    //    "MockWorkOrderInfo"        // work_order_information
    //    , 0
    //);
    return jrsdatabase::jrstable::TProject();
}

jrsdatabase::jrstable::TUser GenerateMockUser() {
    return jrsdatabase::jrstable::TUser(
        "MockUserName",  // user_name
        "MockPassword",  // password
        "2024-08-19 12:34:56"                   // login_time
        , 0
    );
}

jrsdatabase::jrstable::TAOIMachine GenerateMockAOISystem() {
    return jrsdatabase::jrstable::TAOIMachine(
    );
}

jrsdatabase::jrstable::TBoard GenerateMockTBoard() {
    return jrsdatabase::jrstable::TBoard(
        "MockBoardBarcode",          // board_barcode
        "2024-08-19 08:00:00",                              // board_start_detect_time
        "2024-08-19 08:15:00",                              // board_end_detect_time
        false
        , "MockBoardImgPath",          // board_img_path
        "MockNgImgPath",             // ng_img_path
        "1.0, 0.0, 0.0, 1.0",                               // transform_matrix
        1,
        1080,                                               // img_height
        1920,                                               // img_width
        2,                                                  // subboard_cols
        2,                                                  // subboard_rows
        4,                                                  // num_sub_board
        1,                                                  // layout
        0,                                                  // material
        1,                                                  // board_detect_result
        100,                                                // board_devices
        95,                                                 // board_detected_devices
        5,                                                  // board_no_detected_devices
        2,                                                  // board_ng_devices
        1,                                                  // board_misjudge_devices
        0,                                                  // board_masked_subboards
        0,                                                  // board_masked_subboard_devices
        0,                                                  // board_no_judgment_subboards
        "2024-08-19 08:30:00",                              // board_rejudgment_time
        false,
        true,                                               // board_rejudgment_result
        "MockProjectName",           // project_name
        "MockUserName",              // user_name
        "MockMachineID",             // machine_id
        1,                                                   // board_id
        0,
        ""
    );
}

jrsdatabase::jrstable::TSubboard GenerateMockTSubboardData(int i) {
    //jrsdatabase::jrstable::TSubboard subboard(
    //    i + 1,                                         // subboard_id
    //    1,                                             // board_id (假设都属于同一个总板)
    //    i % 2,                                         // subboard_col (列号，0 或 1)
    //    i / 2,                                         // subboard_row (行号，0 至 4)
    //    i * 100,                                       // subboard_x (x 坐标)
    //    i * 200,                                       // subboard_y (y 坐标)
    //    300,                                           // subboard_width (宽度)
    //    400,                                           // subboard_height (高度)
    //    "MockSubboardBarcode",  // subboard_barcode (模拟条码)
    //    i % 2 == 0,                                    // subboard_result (检测结果)
    //    "2024-08-19 09:00:00",
    //    "2024-08-19 09:00:00",                         // subboard_rejudgment_time (复判时间)
    //    i % 2 == 0,                                    // subboard_rejudgment_result (复判结果)
    //    100,                                           // subboard_devices (总元件数)
    //    95,                                            // subboard_detect_devices (已检测元件数)
    //    5,                                             // subboard_no_detect_devices (未检测元件数)
    //    90,                                            // subboard_pass_devices (检测通过元件数)
    //    5,                                             // subboard_ng_devices (不合格元件数)
    //    i % 3,                                         // subboard_misjudge_devices (误判元件数)
    //    1,                                             // subboard_no_judgment_devices (待复判元件数)
    //    i % 2 == 0                                     // subboard_is_detection (是否屏蔽)
    //);


    //return subboard;
    (void)i;
    jrsdatabase::jrstable::TSubboard subboard;
    return subboard;
}

jrsdatabase::jrstable::TDeviceVector GenerateMockTDeviceData(int count) {
    jrsdatabase::jrstable::TDeviceVector devices;
    devices.reserve(count);

    for (int i = 0; i < count; ++i) {
        devices.emplace_back(
            i + 1,                                         // device_id
            1,                                             // subboard_id (假设都属于同一个子板)
            1,                                             // board_id (假设都属于同一个总板)
            "MockDeviceName",       // device_name
            "MockPartNo",           // device_part_no
            "MockType",             // device_type
            i * 10,                                        // device_x (x 坐标)
            i * 15,                                        // device_y (y 坐标)
            i * 5,                                         // device_angle (角度)
            50,                                            // device_width (宽度)
            50,                                            // device_height (高度)
            i % 2 == 0,                                    // device_result (检测结果)
            i % 3 == 0,                                    // device_is_rejudged (是否复判)
            i % 2 == 0,                                    // device_rejudgment_result (复判结果)
            i % 2 == 0,                                    // device_is_detection (是否检测)
            "MockImagePath",        // device_img_path (元件图片路径)
            "2024-08-19 08:30:00",                         // device_detect_time (检测时间)
            "FlawType1,FlawType2",                         // device_flaw_type_ids (缺陷名称列表)
            "RejudgmentFlawType1",                         // device_rejudgment_flaw_type_ids (复判缺陷)
            "2024-08-19 09:00:00",                         // device_rejudgment_time (复判时间)
            "Group1",                                      // device_group (元件组)
            3,                                             // cnt_detect_windows (检测框数)
            i % 4                                          // ng_cnt_detect_windows (NG检测框数)
        );
    }

    return devices;
}

jrsdatabase::jrstable::TGroupVector GenerateMockTGroupData(int count) {
    jrsdatabase::jrstable::TGroupVector groups;
    groups.reserve(count);

    for (int i = 0; i < count; ++i) {
        groups.emplace_back(
            1,                                      // board_id (假设都属于同一个总板)
            1,                                      // subboard_id (假设都属于同一个子板)
            i + 1,                                  // device_id (每个组对应不同元件)
            i + 1,                                  // group_id (组ID)
            "GroupName" + std::to_string(i + 1),    // group_name (检测组名称)
            static_cast<int>(jrsdatabase::jrstable::TGroup::Usage::DEFAULT),// group_usage (检测组类型)
            i % 2 == 0                              // group_result (检测组总结果)
        );
    }

    return groups;
}

int SaveProjectParam(const jrsdata::ProjectEventParamPtr& param_)
{
    /** < 保存数据 */
    param_->project_param->file_param.file_type = jrsdata::FileType::BIN;
    param_->project_param->file_param.file_path = "F:/Hjc/test/";
    param_->project_param->file_param.file_name = param_->project_param->project_name;
    param_->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
    jrsdata::DataManagerPtr ptr = std::make_shared<jrsdata::DataManager>();
    return ptr->EventHandler(param_);
}

jrsdata::ProjectEventParamPtr GenerateProjectEventParam()
{
    //jrsdata::ProjectEventParamPtr param = std::make_shared<jrsdata::ProjectEventParam>();
    auto project_param_ptr = std::make_shared<jrsdata::ProjectEventParam>();

    project_param_ptr->event_name = jrsaoi::PROJECT_SAVE_EVENT_NAME;  /**< 工程保存事件 */
    project_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    project_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    project_param_ptr->module_name = jrsaoi::DATA_MODULE_NAME;
    //project_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
    project_param_ptr->project_param->thumb_img.compression_rate = 10;

    std::string image_path = "F:/Hjc/test/0.png";
    project_param_ptr->project_param->thumb_img.thumb_img = cv::imread(image_path, cv::IMREAD_COLOR);
    auto param = project_param_ptr->project_param;
    for (int board_id = 0; board_id < 1; ++board_id)
    {
        param->board_info = jrsdata::Board(1920, 1080, 2, 2, 2, 2, 4, 0, 1, 0.0, 0.0, 1920.0, 1080.0, {}, {}, {}, {}, {}, {}, {});
        for (int i = 0; i < 4; ++i) {
            jrsdata::SubBoard sb;
            sb.subboard_name = "SubBoard_" + std::to_string(i);
            sb.id = i;
            sb.col = i % 2;
            sb.row = i / 2;
            sb.x = i * 100.0f;
            sb.y = i * 100.0f;
            sb.width = 100.0f;
            sb.height = 100.0f;
            sb.enable = (i % 2 == 0);
            param->board_info.sub_board.push_back(sb);
        }

    }

    param->project_name = "Project_Name";
    param->project_path = "test";
    std::vector<jrsdata::Template> templates = {
        jrsdata::Template(1, 640, 480, std::vector<uint8_t>(640 * 480, 255)),  // 全白图像
        jrsdata::Template(2, 320, 240, std::vector<uint8_t>(320 * 240, 0)),    // 全黑图像
        jrsdata::Template(3, 1280, 720, std::vector<uint8_t>(1280 * 720, 128)),// 灰色图像
        jrsdata::Template(4, 800, 600, std::vector<uint8_t>(800 * 600, 64)),   // 较暗灰色图像
        jrsdata::Template(5, 1024, 768, std::vector<uint8_t>(1024 * 768, 192)) // 较亮灰色图像
    };
    param->temps = templates;

    param->image_group_names.push_back("test_group1");
    param->image_group_names.push_back("test_group2");
    param->image_group_names.push_back("test_group3");
    param->image_group_names.push_back("test_group4");

    return project_param_ptr;
}

jrsdata::ProjectEventParamPtr GetProjectParam()
{
    /** < 读取数据 */
    auto read_project_param_ptr = std::make_shared<jrsdata::ProjectEventParam>();
    read_project_param_ptr->event_name = jrsaoi::PROJECT_READ_EVENT_NAME;
    read_project_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    read_project_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    read_project_param_ptr->module_name = jrsaoi::DATA_MODULE_NAME;
    read_project_param_ptr->project_param->file_param.file_type = jrsdata::FileType::BIN;
    read_project_param_ptr->project_param->file_param.file_path = "F:/Hjc/test/";
    read_project_param_ptr->project_param->file_param.file_name = "project_name";
    read_project_param_ptr->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
    jrsdata::DataManagerPtr ptr = std::make_shared<jrsdata::DataManager>();
    ptr->EventHandler(read_project_param_ptr);
    return read_project_param_ptr;
}

jrsdata::ProjectEventParamPtr GenerateEntiretyImageParam(const jrsdata::ProjectEventParamPtr& param_)
{
    cv::Mat image = cv::imread("F:/Hjc/test/0.png");

    if (image.empty()) {
        std::cerr << "无法加载图片" << std::endl;
        return nullptr;
    }

    param_->project_param->entirety_board_imgs[0] = image;
    param_->project_param->entirety_board_imgs[1] = image.clone();
    param_->project_param->entirety_board_imgs[2] = image.clone();
    param_->project_param->entirety_board_imgs[3] = image.clone();

    return param_;
}
int SaveEntiretyImageParam(const jrsdata::ProjectEventParamPtr& param_)
{
    /** < 保存数据 */
    param_->event_name = jrsaoi::ENTIRETY_IMAGE_SAVE;  /**< 保存图片存储 */
    param_->project_param->current_group_name = "test_group1";  /**< 置组名 */
    param_->project_param->file_param.file_type = jrsdata::FileType::BIN;
    param_->project_param->file_param.file_path = "F:/Hjc/test/";
    param_->project_param->file_param.file_name = param_->project_param->project_name;
    param_->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
    jrsdata::DataManagerPtr ptr = std::make_shared<jrsdata::DataManager>();
    return ptr->EventHandler(param_);
}
jrsdata::ProjectEventParamPtr GetEntiretyImageParam(const jrsdata::ProjectEventParamPtr& param_)
{
    /** < 读取数据 */
    param_->event_name = jrsaoi::ENTIRETY_IMAGE_READ;  /**< 事件更换 */
    param_->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    param_->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    param_->module_name = jrsaoi::DATA_MODULE_NAME;

    param_->project_param->current_group_name = "test_group1";  /**< 组名 */

    param_->project_param->file_param.file_type = jrsdata::FileType::BIN;
    param_->project_param->file_param.file_path = "F:/Hjc/test/";
    param_->project_param->file_param.file_name = "project_name";
    param_->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
    jrsdata::DataManagerPtr ptr = std::make_shared<jrsdata::DataManager>();
    ptr->EventHandler(param_);
    return param_;
}
void GetSettingParamSystem(const jrsdata::SettingViewParamPtr& param_)
{
    /** < 读取数据 */
    param_->event_name = jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME;  /**< 事件更换 */
    param_->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    param_->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    param_->module_name = jrsaoi::DATA_MODULE_NAME;
}
/** < 结果保存模拟 */
int SaveDetectResultParam()
{
    jrsdata::SettingViewParamPtr setting_view_param_ptr = std::make_shared<jrsdata::SettingViewParam>();
    GetSettingParamSystem(setting_view_param_ptr);
    auto project_param = GenerateProjectEventParam();
    jrsdata::DataManagerPtr ptr = std::make_shared<jrsdata::DataManager>();
    ptr->EventHandler(setting_view_param_ptr);
    ptr->EventHandler(project_param);

    jrsdata::DetectResultParamPtr detect_result_param_ptr = std::make_shared<jrsdata::DetectResultParam>();
    detect_result_param_ptr->event_name = jrsaoi::DETECT_RESULT_PARAM_SAVE_EVENT;
    detect_result_param_ptr->board_code = "jrsdata_test_barcode";
    detect_result_param_ptr->start_detect_time = jrscore::AOITools::GetCurrentDataTime();
    detect_result_param_ptr->finish_detect_time = jrscore::AOITools::GetCurrentDataTime();
    detect_result_param_ptr->detect_result = 0;
    detect_result_param_ptr->subboard_id_and_barcode = {
        { 1,"subboard_barcode_1" },
        { 2,"subboard_barcode_2" },
        { 1,"subboard_barcode_3" },
        { 1,"subboard_barcode_4" } };
    cv::Mat entirety_image = cv::imread("F:/Hjc/test/0.png");

    detect_result_param_ptr->entirety_board_imags = {
        {0,entirety_image.clone()},
        {1,entirety_image.clone()},
        {2,entirety_image.clone()},
        {3,entirety_image.clone()},
    };

    jrsdata::DeviceResult device_result;
    for (int subboard_id = 0; subboard_id < 4; ++subboard_id)
    {

        for (int i = 0; i < 100; ++i)
        {
            int device_id = i + 1;
            device_result.t_device = {
               device_id,                                         // device_id
                1,                                             // subboard_id (假设都属于同一个子板)
                1,                                             // board_id (假设都属于同一个总板)
                "MockDeviceName",       // device_name
                "MockPartNo",           // device_part_no
                "MockType",             // device_type
                i * 10,                                        // device_x (x 坐标)
                i * 15,                                        // device_y (y 坐标)
                i * 5,                                         // device_angle (角度)
                50,                                            // device_width (宽度)
                50,                                            // device_height (高度)
                i % 2 == 0,                                    // device_result (检测结果)
                i % 3 == 0,                                    // device_is_rejudged (是否复判)
                i % 2 == 0,                                    // device_rejudgment_result (复判结果)
                i % 2 == 0,                                    // device_is_detection (是否检测)
                "MockImagePath",        // device_img_path (元件图片路径)
                "2024-08-19 08:30:00",                         // device_detect_time (检测时间)
                "FlawType1,FlawType2",                         // device_flaw_type_ids (缺陷名称列表)
                "RejudgmentFlawType1",                         // device_rejudgment_flaw_type_ids (复判缺陷)
                "2024-08-19 09:00:00",                         // device_rejudgment_time (复判时间)
                "Group1",                                      // device_group (元件组)
                3,                                             // cnt_detect_windows (检测框数)
                i % 4                                          // ng_cnt_detect_windows (NG检测框数)
            };
            device_result.groups;
            jrsdata::DeviceResult devivce;
            for (int group_id = 0; group_id < 1; ++group_id)
            {
                jrsdata::GroupResult group_result;
                group_result.t_group = jrsdatabase::jrstable::TGroup(0, subboard_id, device_id, group_id, "group_id" + std::to_string(group_id), 10, true);
                for (int detect_window_id = 0; detect_window_id < 2; ++detect_window_id)
                {
                    cv::Mat image = cv::imread("F:/Hjc/test/0.png");
                    jrsdata::DetectWindowResult detect_window_result;
                    /*  detect_window_result.t_detect_window = jrsdatabase::jrstable::TDetectWindow(detect_window_id,
                          0, subboard_id, device_id, group_id, detect_window_id, "window_name", 0, 0, 0, 0, 0, "", "", 0, 0, 0, 10, 10, "", "", 0, 0, 0, 0);
                      if (image.empty())
                      {
                          std::cerr << "无法加载图片" << std::endl;
                      }
                      else
                      {
                          detect_window_result.algorithm_image = image.clone();
                      }
                      group_result.detect_windows.push_back(detect_window_result);*/
                }
                devivce.groups.push_back(group_result);
            }
            cv::Mat image = cv::imread("F:/Hjc/test/0.png");
            for (int img_i = 0; img_i < 4; ++img_i)
            {
                devivce.device_images.insert({ img_i,image.clone() });
            }
            devivce.t_device = jrsdatabase::jrstable::TDevice(device_id, subboard_id, 0, "device_name" + std::to_string(device_id),
                "device_part_no", "device_type", 0, 0, 0, 10, 10, device_id % 2, device_id % 2, device_id % 2, device_id % 2, "",
                jrscore::AOITools::GetCurrentDataTime(), "", "", jrscore::AOITools::GetCurrentDataTime(), "", 10, 2);
            detect_result_param_ptr->component_result_vector.push_back(devivce);
        }
    }
    return ptr->EventHandler(detect_result_param_ptr);

}



class DataTest :public::testing::TestWithParam<std::string>
{
protected:
    void SetUp() override
    {
    }
    void TearDown() override
    {
    }
};

jrsdata::DataManagerPtr _db_ptr;
//TEST_F(DataTest, InitDBTest)
//{
//    system("chcp 65001");
//    _db_ptr = std::make_shared<jrsdata::DataManager>();
//    jrsdata::SettingViewParamPtr setting_view_param_ptr = std::make_shared<jrsdata::SettingViewParam>();
//    setting_view_param_ptr->event_name = jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME;
//    _db_ptr->EventHandler(setting_view_param_ptr);
//}
//
//TEST_F(DataTest, SaveEntiretyBoardResult)
//{
//    jrsdata::EntiretyBoardResultPtr entirety_board_result_ptr = std::make_shared<jrsdata::EntiretyBoardResult>();
//    entirety_board_result_ptr->data_save_mode = jrsdata::DataSaveMode::SAVE_DATABASE; /**< 存数据库*/
//    entirety_board_result_ptr->event_name = jrsaoi::DETECT_RESULT_PARAM_SAVE_EVENT;  /**< 保存检测结果事件 */
//    entirety_board_result_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;/**< 主题 可以更换 */
//    entirety_board_result_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;     /**< 必须转发到logic层*/
//    entirety_board_result_ptr->module_name = jrsaoi::DATA_MODULE_NAME;        /**< 转发到Data 模块*/
//
//    entirety_board_result_ptr->aoi_sys = GenerateMockAOISystem();
//    entirety_board_result_ptr->user = GenerateMockUser();
//    entirety_board_result_ptr->project = GenerateMockProject();
//    entirety_board_result_ptr->board_info = GenerateMockTBoard();
//    for (int subboard_id = 0; subboard_id < 10; subboard_id++)
//    {
//        jrsdata::SubboardResult subboard_result;
//        subboard_result.t_subboard = jrsdatabase::jrstable::TSubboard(
//            subboard_id, 0, 1, 1, 0, 0, 500, 500, "subboard_barcode" + std::to_string(subboard_id),
//            subboard_id % 2 == 0,  jrscore::AOITools::GetCurrentDataTime(), true,
//            200, 200, 10, 0, 0, 0, 0, 0);
//
//        for (int device_id = 0; device_id < 100; ++device_id)
//        {
//            jrsdata::DeviceResult devivce;
//            for (int group_id = 0; group_id < 1; ++group_id)
//            {
//                jrsdata::GroupResult group_result;
//                group_result.t_group = jrsdatabase::jrstable::TGroup(0, subboard_id, device_id, group_id, "group_id" + std::to_string(group_id), 10, true);
//                for (int detect_window_id = 0; detect_window_id < 2; ++detect_window_id)
//                {
//                    jrsdata::DetectWindowResult detect_window_result;
//                    detect_window_result.t_detect_window = jrsdatabase::jrstable::TDetectWindow(detect_window_id,
//                        0, subboard_id, device_id, group_id, 0, "window_name", 0, 0, 0, 0, 0, "", "", 0, 0, 0, 10, 10, "", "", 0, 0, 0, 0);
//                    group_result.detect_windows.push_back(detect_window_result);
//                }
//
//                devivce.groups.push_back(group_result);
//            }
//            devivce.t_device = jrsdatabase::jrstable::TDevice(device_id, subboard_id, 0, "device_name" + std::to_string(device_id),
//                "device_part_no", "device_type", 0, 0, 0, 10, 10, device_id % 2, device_id % 2, device_id % 2, device_id % 2, "",
//                jrscore::AOITools::GetCurrentDataTime(), "", "", jrscore::AOITools::GetCurrentDataTime(), "", 10, 2);
//            subboard_result.devices.push_back(devivce);
//        }
//
//        entirety_board_result_ptr->subboards.push_back(subboard_result);
//    }
//
//    if (_db_ptr)
//    {
//        //for (int i = 0; i < 10000; ++i)
//        //{
//        //    //jrsdata::EntiretyBoardResultPtr entirety_board_result_ptr_temp = std::make_shared<jrsdata::EntiretyBoardResult>();
//        //    _db_ptr->EventHandler(entirety_board_result_ptr);
//        //}
//    }
//}
//
//TEST_F(DataTest, AddDataToDB)
//{
//    auto board = GenerateMockTBoard();
//    //_db_ptr->
//}
//
//TEST_F(DataTest, TestSavePorject)
//{
//    auto project_param_ptr = GenerateProjectEventParam();
//    SaveProjectParam(project_param_ptr);
//}
//TEST_F(DataTest, TestReadPorject)
//{
//    auto project_param_ptr = GetProjectParam();
//    if (project_param_ptr)
//    {
//    }
//}
//TEST_F(DataTest, TestPorjectSaveEntiretyImage)
//{
//    auto project_param_ptr = GetProjectParam();
//    project_param_ptr = GenerateEntiretyImageParam(project_param_ptr);
//    if (project_param_ptr)
//    {
//        SaveEntiretyImageParam(project_param_ptr);
//    }
//}
//TEST_F(DataTest, TestPorjectReadEntiretyImage)
//{
//    auto project_param_ptr = GetProjectParam();
//    project_param_ptr = GetEntiretyImageParam(project_param_ptr);
//    if (project_param_ptr)
//    {
//    }
//}
TEST_F(DataTest, TestDetectResult)
{
    system("chcp 65001");
    SaveDetectResultParam();
}