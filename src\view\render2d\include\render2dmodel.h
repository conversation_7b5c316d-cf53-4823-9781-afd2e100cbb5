﻿/*****************************************************************//**
 * @file   render2dmodel.h
 * @brief  Render2d渲染模块model实例
 * @details
 * <AUTHOR>
 * @date 2024.7.16
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.16         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
**********************************************************************/
#ifndef __JRSRENDER2DMODEL_H__
#define __JRSRENDER2DMODEL_H__

#include "modelbase.h"
#include "projectparam.hpp" 
#include "render2deventparam.hpp" // SubBoardCloneParam

class GraphicsAbstract;
// namespace jrsdevice { class DeviceManager; };
namespace jrsaoi
{
    struct UpdateOperatorParam;
    class ParamOperator;
    using CallBackProjectUpdate = std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)>;
    using CallBackCurrentSelectGraphics = std::function <void(const std::vector<std::shared_ptr<GraphicsAbstract>>&) >;
    using CallBackCurrentSelectParam = std::function <void(UpdateOperatorParam&) >;
    using CallBackCreateGraphics = std::function<void(std::shared_ptr<GraphicsAbstract>&, int,
        const std::string&, const std::string&, const std::shared_ptr<GraphicsAbstract>&)>;
    using CallBackClearGraphics = std::function<void(const std::string&, bool)>;
    using CallBackGetGraphics = std::function<void(std::vector<std::shared_ptr<GraphicsAbstract>>&, const std::vector<GraphicsID>&)>;

    class Render2dModel :public ModelBase
    {
    public:
        Render2dModel(const std::string& name);
        ~Render2dModel();
        int Update(const jrsdata::ViewParamBasePtr& param_)override;
        int Save(const jrsdata::ViewParamBasePtr& param_) override;
        // const jrsdata::RenderViewParamPtr& GetModelData();
        // int GetModelData(jrsdata::RenderViewParamPtr& param_);



        jrsaoi::ParamOperator& GetProjectParamInstance();
        /**
         * @brief 获取当前项目参数引用
         */
        const jrsdata::ProjectParamPtr& GetProjectParam();

        /**
         * @brief 获取当前项目图片全部
         */
        std::optional<const std::unordered_map<int, cv::Mat>*> GetProjectImage();
        /**
         * @brief 获取当前项目图片
         * @param image_index 图片索引
         */
        std::optional<cv::Mat> GetProjectImageWithIndex(int image_index);
        /**
         * @brief 获取当前项目所有子板
         */
        std::vector<jrsdata::SubBoard> GetSubBoards();
        /**
         * @brief 获取当前项目指定子板
         * @param subboardname 子板名称
         */
        std::optional<jrsdata::SubBoard> GetSubBoard(const std::string& subboardname);
        // std::optional<jrsdata::Component> GetComponent(const std::string& subBoardName, const std::string& componentName);
        /**< 获取第一个子板名称 */
        std::string GetDefaultSubboardName();
        /**
         * @brief 获取当前项目板子尺寸
         * @param width_ 宽度
         * @param height_ 高度
         * @return int 0:成功
         */
        int GetBoardSize(int& width_, int& height_);
        /**
         * @brief 工程更新时更新图形
         * @param canvas_width 画布宽度
         * @param canvas_height 画布高度
         * @param update_subboard_position 是否更新子板位置
         */
        void ProjectUpdateGraphics(int canvas_width = 0, int canvas_height = 0, bool update_subboard_position = false);
        /**
         * @brief 图形更新时更新工程
         * @param ghs 图形列表
         * @param operator_param 更新操作参数
         */
        GraphicsPtrVec GraphicsUpdateProject(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const UpdateOperatorParam& operator_param);
        // void ProjectUpdateGraphicsPartNumber(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const std::shared_ptr<GraphicsAbstract>& parent, const std::string& part_number_name);
        /**
         * @brief 通过选择图形更新操作参数
         * @return 执行成功返回true
         */
        bool SelectGraphicsUpdateOperator(UpdateOperatorParam& operator_param, const std::shared_ptr<GraphicsAbstract>& gh);
        /**
         * @brief 获取图形实际名称
         * @note  目前的图形名是拼接名
         */
        std::string GetGraphicsActualName(const std::shared_ptr<GraphicsAbstract>& gh);

        /**
         * @brief 获取图形实际名称
         * @param layer[IN] 层
         * @param id[IN] 图形id
         * @return 图形实际名称
         */
        std::string GetGraphicsActualName(const std::string& layer, const std::string& id);
        /**
         * @fun GetComponnetUnitNamesByOperateParam
         * @brief  获取相同组的unit名称
         * @return
         * <AUTHOR>
         * @date 2025.3.26
         */
        std::vector<std::string> GetComponnetUnitNamesByOperateParam(const UpdateOperatorParam& param_);

        /**
         * @brief 获取名称在渲染界面中的图形名字
         * @note  目前的图形名是拼接名
         */
        std::string GetNameInRender(const std::string& subboard_name, const std::string& component_name);
        std::string GetNameInRender(const std::string& subboard_name, const std::string& component_name,
            const std::string& unit_name);
        std::string GetNameInRender(const std::string& subboard_name, const std::string& component_name,
            const std::string& unit_name, const std::string& model_name, const std::string& window_name);
        std::string GetNameInRender(const std::string& subboard_name, const std::string& component_name,
            const std::string& unit_name, const std::string& model_name, const std::string& window_name,
            const std::string& subwindow_name);
        /**
         * @brief 更新工程图片
         * @param type_ 图片类型
         * @param img_ 图片
         */
        void UpdateImageToProject(int type_, const cv::Mat& img_);
        // void UpdateImageToProject(const std::unordered_map<int, std::string>& img_path_hash);
        /**
         * @fun EntiretyBoardTransform
         * @brief mark 仿射变换应用
         * @param affine_transform_matrix_
         * <AUTHOR>
         * @date 2024.12.14
         */
        void EntiretyBoardTransform(const cv::Mat& affine_transform_matrix_);

        /**
         * @brief 整板矫正
         * @param matrix 变换矩阵
         */
        void AlignmentBoard(jrsdata::Board& board, const cv::Mat& matrix);
        /**
         * @brief 子板矫正
         * @param subboard 子板
         * @param matrix 变换矩阵
         */
        void AlignmentSubboard(jrsdata::SubBoard& subboard, const cv::Mat& matrix);
        /**
         * @brief 子板矫正
         * @param subboardname 子板名称
         * @param matrix 变换矩阵
         */
        void AlignmentSubboard(const std::string& subboardname, const cv::Mat& matrix);
        /**
        * @brief 元件矫正
        */
        void AlignmentComponent(jrsdata::Component& component, const cv::Mat& matrix);
        /**
         * @brief 元件矫正
         */
        void AlignmentComponents(std::vector<jrsdata::Component*>& cads, const cv::Mat& matrix);

        cv::RotatedRect GetCurrentPositionFromGraphics(const std::shared_ptr<GraphicsAbstract>& gh);

        /**
         * @brief 设置工程更新时更新图形的回调函数
         */
        void SetCallBackProjectUpdateGraphics(CallBackProjectUpdate callback) { callback_project_update_graphics = callback; }
        /**
         * @brief 设置创建图形的回调函数
         */
        void SetCallBackCreateGraphics(CallBackCreateGraphics callback) { callback_create_graphics = callback; }
        void SetCallBackCurrentSelectParam(CallBackCurrentSelectParam callback) { callback_current_select_param = callback; }
        void SetCallBackClearGraphics(CallBackClearGraphics callback_) { _callback_clear_graphics = callback_; }
        void SetCallBackGetGraphics(CallBackGetGraphics callback_) { _callback_get_graphics = callback_; }

        void GetBoardShow3DImage(cv::Mat& height_image, std::vector<cv::Mat>& texture_images);
        void GetSelectShow3DImages(const cv::Rect& crop_rect, cv::Mat& height_image, std::vector<cv::Mat>& texture_images);
        void GetComponentDetectRegionSmallestRect(const std::string& sub_board_name, const std::string& comp_name, cv::Rect& rect);
        /**
         * @fun GetComponentDegree
         * @brief 获取元件角度
         * @param commponent_name_
         * @param subboard_name_
         * @return
         * <AUTHOR>
         * @date 2025.1.8
         */
        float GetComponentDegree(const std::string& commponent_name_, const std::string& subboard_name_);
        /**
         * @fun CreateSearchWindow
         * @brief 创建一个检测框
         * @param subboard_name_
         * @param component_name_
         * @param unit_name_
         * @param window_name_
         * @param research_size_
         * @param detect_window_graphics_ptr_
         * @param
         * @return
         * <AUTHOR>
         * @date 2025.3.21
         */
        GraphicsPtr CreateSearchWindow(const std::string& subboard_name_,
            const std::string& component_name_, const std::string& unit_name_,
            const std::string& window_name_, GraphicsPtr detect_window_graphics_ptr_, float research_size_);

        std::optional < jrsdata::DetectWindow> GetDetctWindowByCurrentSelect(const UpdateOperatorParam& update_operator_param_);

        std::string GetUnitNameByModelName(const std::string& part_number_name, const std::string& model_name_);
        /**
         * @fun IsDelete
         * @brief  是否删除通知外界
         * @return
         * <AUTHOR>
         * @date 2025.3.27
         */
        bool  IsDelete();


        /**
         * @fun DealCADShortcutOperate
         * @brief  处理CAD快捷键操作
         * @return
         * <AUTHOR>
         * @date 2025.4.25
         */
        void DealCADShortcutOperate(const jrsdata::RenderEventParamPtr& param, const RenderSelectParam& select_param_);

        /**
         * @fun MarkImageToContours
         * @brief mark 图像转contours
         * @param img_
         * @return
         * <AUTHOR>
         * @date 2025.5.26
         */
        std::vector<cv::Point2f> MarkImageToContours(const cv::Mat img_, const cv::Point2f& origin_point_);
        /**
         * @fun UpdateGraphicsBySubboards
         * @brief Temporarily displays subboard graphics
         * @param subboards
         * <AUTHOR>
         * @date 2025.6.23
         */
        void UpdateTemporalGraphicsBySubboards(const std::vector<jrsdata::SubBoard>& subboards_);

    private:
        /**
        * @fun CreateSubboardGraphicsUpdateGraphics
        * @brief 创建临时子板图形
         * @param subboards_
         * <AUTHOR>
         * @date 2025.6.23
         */
        void CreateSubboardGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& graphics_, const std::vector<jrsdata::SubBoard>& subboards_,
            bool is_temp_ = true);

        void CADCreateGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const std::vector<jrsdata::Component>& cs);

        void PNCreateGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const jrsdata::PNDetectInfo& pn, const jrsdata::Component& component, const std::shared_ptr<GraphicsAbstract>& parent);

        void CreateSubboardIdGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const std::vector<jrsdata::SubBoard>& subboard_);

        /**< 返回要更新后的数据  临时这么写 TODO：需要优化　逻辑不合理 */
        GraphicsPtrVec GraphicsUpdateComponent(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        void GraphicsUpdateUnit(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        // void GraphicsUpdatePAD(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        void GraphicsUpdateDetectWindow(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        void GraphicsUpdateSubDetectWindow(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        void GraphicsUpdateSubBoard(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        // void GraphicsUpdateMark(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        // void GraphicsUpdateSubMark(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);
        // void GraphicsUpdateBarcode(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& operator_param);




        void TriggerCallBackProjectUpdateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void TriggerCallBackCreateGraphics(std::shared_ptr<GraphicsAbstract>& gh, int graphics_type,
            const std::string& layer, const std::string& group_name_, const std::shared_ptr<GraphicsAbstract>& father_graphics_ptr_ = nullptr);
        /**
         * @fun GetExecuteModeInfo
         * @brief 生成自动运行时的执行模式信息
         * @return  返回生成的结果
         * <AUTHOR>
         * @date 2025.4.16
         */
        jrsparam::ExecuteModeInfo GetExecuteModeInfo();
        /***************************************<旋转镜像等操作 开始<*********************************/
        /**<对于案件的仿射变换*/
        void ApplyTransformToComponent(jrsdata::Component& component,
            const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type,
            float center_x, float center_y);
        /**<对子板的放射变换*/
        void ApplyTransformToSubBoard(jrsdata::SubBoard& subboard,
            const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type,
            float center_x, float center_y);
        /**<对子板的宽高变换*/
        void SwapWidthHeightIfRotate(jrsdata::SubBoard& subboard,
            const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type);

        /**<对元件的变换*/
        void ApplyRotateOrConvertToComponent(jrsdata::Component& component, const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type);

        /**<旋转所有CAD*/
        void AllCADComponentRotate(const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type_);

        /**
         * @brief 子板变换
         * @param subboardname 子板名称
         * @param type 变换类型
         */
        void SubBoardTransform(const std::string& subboard_name_,
            const jrsdata::MultiBoardEventParam::OperateParam& operate_param_);

        /**< 对整个板子的变换*/
        void EntiretyBoardTransform(const jrsdata::MultiBoardEventParam::OperateParam& operate_param_);
        /***************************************<旋转镜像等操作 结束<*********************************/



    private:
        CallBackCurrentSelectParam callback_current_select_param;  /**< 获取当前选择参数 */
        CallBackProjectUpdate callback_project_update_graphics;  ///<   图形更新回调函数
        CallBackCreateGraphics callback_create_graphics;        ///< 创建图形回调函数
        CallBackClearGraphics _callback_clear_graphics;         ///< 清除图形
        CallBackGetGraphics _callback_get_graphics;             ///<获取图形
        /**
         * @fun  CreateSubDetectWindow
         * @brief 根据检测框位置及子检测框类型创建子检测框.
         * @param detect_window 检测框
         * @param sub_win_type 子检测框类型
         * @param sub_detect_windows 子检测框
         * @data 2024.12.20
         * @return 错误码
         * <AUTHOR>
         */
        int CreateSubDetectWindow(jrsdata::DetectWindow& detect_window, const    int& sub_win_type, std::vector<jrsdata::SubDetectWindow>& sub_detect_windows);

        bool _is_delete;/**<是否删除*/

        //! 在线调试相关
         //! 在线调试相关参数
        bool _is_debugging = false; //!< 是否在线调试
        jrsdata::CurrentDebugInfo _current_debug_info; //!< 当前选中的在线调试元件信息

        //bool _is_clockwise_rotate;/**< 顺时针旋转还是逆时针旋转 */

    };
    using Render2dModelPtr = std::shared_ptr<Render2dModel>;
}

#endif // !__JRSRENDER2DMODEL_H__
