﻿
// Custom
#include "logicmanager.h"
#include "datamanager.h"
#include "coordinatetransform.hpp" // CoordinateTransform
#include "motionerrorcorrect.h"
#include "viewdefine.h"
#include "jrsaoiimgsmanager.h"
#include "motion.h"
#include "datamanager.h"
#include "devicemanager.h"
#include "coreapplication.h"
#include "fileoperation.h"
#include "motionerrorparam.h"
#include "scanprocess.h"
#include "captureimage.h"
#include "projectmanager.h"
#include "deviceparam.hpp"
#include "motion.h"
#include "invokealgoengine.h"
#include "workflowmanager.h"

namespace jrslogic
{
    struct LogicDataImpl
    {
        jrsdevice::DeviceManagerPtr device_manager_ptr;                 /**<设备管理指针*/
        DeviceMsgCallBack device_init_msg;
        std::shared_ptr<ScanProcess> scan_process_ptr;                  /**< 扫描图像流程指针 */
        std::shared_ptr<MotionErrorCorrect> motion_correct_ptr;         /**< 运动位置矫正指针*/
        std::shared_ptr<JrsAoiImgsManager>  imgs_manager_ptr;           /**< 图像管理器指针 */
        std::shared_ptr<CaptureImage>  capture_image_ptr;               /**< 采图流程指针：暂未实现*/

        std::map<std::string, Function> logic_func_map;              /**< 逻辑层中用于分发事件运行函数map*/
        jrsdata::JrsImageBufferCallBack render_imgs_callback;        /**< 渲染界面获取拍照结果回调*/
        jrsdata::JrsImageBufferCallBack work_flow_imgs_callback;    /**< 工程运行获取拍照结果回调*/

        std::shared_ptr<jrscore::CoordinateTransform> coordinate_transform_ptr;  /**< 坐标变换指针 */

        jrsproject::ProjectManagerPtr project_manager_ptr;    /**< 工程管理指针参数*/
        jrsdata::DataManagerPtr data_ptr;                    /**< 数据层指针*/
        jrsdata::StructLightParam struct_light_param;        /**< 结构光参数*/
        InvokeAlgoEnginePtr invoke_algo_engine_ptr;         /**< logic中用于调用算法引擎的封装*/
        jrsworkflow::WorkFlowManagerPtr work_flow_manager_ptr; /**<工程运行管理指针*/

        LogicDataImpl()
            : project_manager_ptr(std::make_shared<jrsproject::ProjectManager>())
            , device_manager_ptr(std::make_shared<jrsdevice::DeviceManager>())
            , data_ptr(std::make_shared<jrsdata::DataManager>())
            , invoke_algo_engine_ptr(std::make_shared<InvokeAlgoEngine>())
        {
        }

    };
    LogicManager::LogicManager()
        :logic_data_impl(new LogicDataImpl())
    {
        Init();
    }

    LogicManager::~LogicManager()
    {
        if (logic_data_impl)
        {
            delete logic_data_impl;
            logic_data_impl = nullptr;
        }
    }

    void LogicManager::Init()
    {
        InitMember();
        //InitDevice();
        InitMotionCorrect();
        InitInvokeFun();
        InitCallBack();
    }

    void LogicManager::InitMember()
    {
        /**坐标转换 */
        logic_data_impl->coordinate_transform_ptr = std::make_shared<jrscore::CoordinateTransform>();


        //! 工程管理
        logic_data_impl->project_manager_ptr->SetTransformCoordinatePtr(logic_data_impl->coordinate_transform_ptr);

        //设备管理
        auto call_back_init_device_param = std::bind(&LogicManager::InitDeviceParamCallback, this, std::placeholders::_1);
        logic_data_impl->device_manager_ptr->SetInitCallBack(call_back_init_device_param);

        //!工程运行管理
        logic_data_impl->work_flow_manager_ptr = std::make_shared<jrsworkflow::WorkFlowManager>(logic_data_impl->device_manager_ptr, logic_data_impl->invoke_algo_engine_ptr->GetAlgoEngineManager(), logic_data_impl->data_ptr);
        logic_data_impl->work_flow_imgs_callback = std::bind(&jrsworkflow::WorkFlowManager::BufferInvoke, logic_data_impl->work_flow_manager_ptr, std::placeholders::_1);
        auto callback_work_flow_invoke = std::bind(&LogicManager::WorkFlowCallback, this, std::placeholders::_1, std::placeholders::_2);
        logic_data_impl->work_flow_manager_ptr->SetLogicInvokeFun(callback_work_flow_invoke);
    }

    int LogicManager::LogicEventHandler(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!IsValidParam(param_))
        {
            return -1;
        }

        if (!InvokeFun(param_))
        {
            return -1;
        }
        return 0;
    }

    bool LogicManager::IsValidParam(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("project_event_param_为空");
            return false;
        }

        if (param_->event_name.empty())
        {
            Log_ERROR("event_name is empty");
            return false;
        }
        return true;
    }

    bool LogicManager::InvokeFun(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name.compare("act_take_picture_and_save") == 0)
        {
            std::cout << __FUNCTION__ << " 当前位置拍照并保存图片" << std::endl;
        }

        else if (param_->event_name == "close_all_device")
        {
            logic_data_impl->device_manager_ptr->ReleaseStructLight();
        }

        auto it = logic_data_impl->logic_func_map.find(param_->event_name);
        if (it == logic_data_impl->logic_func_map.end())
        {
            Log_ERROR("LogicManager::EventHandler() event_name:", param_->event_name, " not found");
            return false;
        }
        if (!it->second)
        {
            Log_ERROR("LogicManager::EventHandler() event_name:", param_->event_name, " has invalid function pointer");
            return false;
        }
        it->second(param_);  // 执行函数
        return true;
    }

    void LogicManager::InitMotionCorrect()
    {
        if (logic_data_impl->motion_correct_ptr.get() == nullptr)
        {
            logic_data_impl->motion_correct_ptr = std::make_shared<MotionErrorCorrect>();
        }

        MotionErrorParam param;
        auto current_table_config_path = jtools::FileOperation::GetCurrentWorkingDirectory() + "/config/tablemappingconfig/tmp_params.json";
        if (ReadMotionErrorParamFile(current_table_config_path, param) &&
            logic_data_impl->motion_correct_ptr.get() != nullptr)
        {
            logic_data_impl->motion_correct_ptr->UpdateParam(param);
        }
    }

    void LogicManager::InitAoiImgsManager()
    {
        if (logic_data_impl->imgs_manager_ptr.get() == nullptr)
        {
            logic_data_impl->imgs_manager_ptr = std::make_shared<JrsAoiImgsManager>(logic_data_impl->struct_light_param.resolution_x, logic_data_impl->struct_light_param.camera_fov_w, logic_data_impl->struct_light_param.camera_fov_h, false, false);
        }
        logic_data_impl->scan_process_ptr->SetJrsAoiImgsManager(logic_data_impl->imgs_manager_ptr);

    }

    void LogicManager::InitInvokeFun()
    {
        logic_data_impl->logic_func_map =
        {
            { jrsaoi::SCANCE_BOARD_EVENT_NAME , std::bind(&LogicManager::ScaneBoardImage , this , std::placeholders::_1) },
            { jrsaoi::START_CONTINUE_GRAB_EVENT_NAME, std::bind(&LogicManager::GrabImageContinue , this , std::placeholders::_1)},
            { jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME,std::bind(&LogicManager::GrabImageContinue , this , std::placeholders::_1)},
            { jrsaoi::UPDATE_COORDINATE_EVENT_NAME,std::bind(&LogicManager::UpdateCoordinateTransform , this , std::placeholders::_1)},
        };

    }

    int LogicManager::ScaneBoardImage(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param_temp = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
        if (!param_temp)
        {
            return 2;
        }
        logic_data_impl->capture_image_ptr->StopContinueTrigger();
        auto fovs = GenerateFovPiexelCoor(param_temp->project_param);
        auto outputParams = std::dynamic_pointer_cast<PCBPathPlanning::OutputParams>(fovs);
        if (outputParams)
        {
            GenerateFovPhysicalCoor(outputParams->fovs);
            return StartScanProcess();
        }
        return -1;
    }

    int LogicManager::StartScanProcess(bool is_auto_run)
    {

        logic_data_impl->scan_process_ptr->StartCapture(is_auto_run);
        return jrscore::AOI_OK;
    }



    std::shared_ptr<PCBPathPlanning::OutputParamsBase> LogicManager::GenerateFovPiexelCoor(const jrsdata::ProjectParamPtr& project_param_)
    {
        InitCoordinateTransform(project_param_);
        cv::Point2f start(0, 0);
        int w, h;
        logic_data_impl->coordinate_transform_ptr->GetSize(w, h);

        // 新版FOV规划
        PCBPathPlanning::InputParams param;
        param.region_w = w;
        param.region_h = h;
        param.fov_w = logic_data_impl->struct_light_param.camera_fov_w;
        param.fov_h = logic_data_impl->struct_light_param.camera_fov_h;
        param.start = start;
        param.mode_path = PCBPathPlanning::PathPlanMode::SNAKE_MODE;
        param.mode_position = PCBPathPlanning::PositionPlanMode::GRID;
        param.max_offset = 0;
        PCBPathPlanning pcb_path_plan;
        return pcb_path_plan.PathPlan(param);
    }

    int LogicManager::GenerateFovPhysicalCoor(const std::vector<PCBPathPlanning::Fov>& fovs_pixel_coor_)
    {
        float z_pos = logic_data_impl->struct_light_param.z_focus_pos;
        int fov_id = 0;
        jrsdata::CaptureFovPosList pos_lists_;//FOV 物理坐标
        pos_lists_.fov_pos_list.clear();
        std::vector<cv::Point2f> fov_pos;
        for (auto& fov : fovs_pixel_coor_)
        {
            fov_id = fov.fov_path.fovid;
            logic_data_impl->coordinate_transform_ptr->PixelToPhysical((int)fov.fov_path.center.x, (int)fov.fov_path.center.y, (float)fov.fov_path.center.x, (float)fov.fov_path.center.y);
            pos_lists_.fov_pos_list.push_back(jrsdata::CaptureFovPos(fov_id, fov.fov_path.center.x, fov.fov_path.center.y, z_pos));
            fov_pos.push_back(cv::Point2f(fov.fov_path.center.x, fov.fov_path.center.y));
        }

        if (fov_pos.empty() || pos_lists_.fov_pos_list.empty())
        {
            PushErrorToStack(jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION, "FOV规划路径结果为空，无法采图");
            return jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION;
        }

        logic_data_impl->imgs_manager_ptr->SetAllFovPos(fov_pos);

        logic_data_impl->scan_process_ptr->SetCapturePosList(pos_lists_);
        return jrscore::AOI_OK;
    }

    int LogicManager::GrabImageContinue(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);
        auto project_param = std::make_shared<jrsdata::ProjectParam>(param_temp->project_param);

        if (param_temp->event_name == jrsaoi::START_CONTINUE_GRAB_EVENT_NAME)
        {
            logic_data_impl->capture_image_ptr->StartContinueTrigger(jrsdata::TriggerModeCapture::LGTS_TRIGGER);
        }
        else if (param_temp->event_name == jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME)
        {
            logic_data_impl->capture_image_ptr->StopContinueTrigger();
        }
        return 0;
    }

    int LogicManager::LogicParamUpdateCallBack(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("指针为空，请检查");
            return -1;
        }
        //根据事件更新该层其他参数
        if (param_->event_name == jrsaoi::MOTION_CONFIG_EVENT_NAME) // 运控配置文件
        {
            jrsdata::OperateViewParamPtr param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            if (param->config_setting_param.event_name == "SaveMotionCfgSetting") // 保存运控配置文件数据
            {
                
            }
        }
        else if (param_->event_name == jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME || param_->event_name == jrsaoi::MACHINE_PARAM_UPDATE_EVENT) // 系统配置文件
        {
            auto param = std::dynamic_pointer_cast<jrsdata::SettingViewParam>(param_);/**< 获取到的机台参数*/

            ////  By:获取系统参数的例子
            //try {

            //    auto temst_str_path = GetSettingParamValueByName(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH, param->machine_param.machine_params_data);
            //    //param->machine_param.machine_params_seting;
            //    //数据获取成功
            //}
            //catch (...)
            //{
            //    //数据获取失败
            //}
        }
        return jrscore::AOI_OK;
    }

    void LogicManager::WorkFlowCallback(const std::vector<PCBPathPlanning::Fov>& fov_path, bool is_auto_run)
    {
        auto fov_path_temp = fov_path;
        auto res = GenerateFovPhysicalCoor(fov_path_temp);
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR("自动流程拍照坐标计算失败！");
            return;
        }
        res = StartScanProcess(is_auto_run);

        Log_INFO("扫图结果：", res);

    }

    int LogicManager::UpdateCoordinateTransform(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("指针转换失败");
            return -1;
        }
        auto project_event_param = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
        InitCoordinateTransform(project_event_param->project_param);
        return jrscore::AOI_OK;
    }

    void LogicManager::InitCoordinateTransform(const jrsdata::ProjectParamPtr& project_param_)
    {
        if (!project_param_)
        {
            Log_ERROR("指针转换失败");
            return; //指针转换为空
        }
        jrscore::CoordinateTransformAttribute attr;

        attr.axis[0] = true;
        attr.axis[1] = true;
        attr.lefttop[0] = static_cast<float>(project_param_->board_info.left_top_x);
        attr.lefttop[1] = static_cast<float>(project_param_->board_info.left_top_y);
        attr.rightbottom[0] = static_cast<float>(project_param_->board_info.right_bottom_x);
        attr.rightbottom[1] = static_cast<float>(project_param_->board_info.right_bottom_y);
        attr.resolution[0] = logic_data_impl->struct_light_param.resolution_x; // 物理转像素
        attr.resolution[1] = logic_data_impl->struct_light_param.resolution_x;
        logic_data_impl->coordinate_transform_ptr->Init(attr);

        //! 获取板子像素尺寸
        int board_width, board_height;
        logic_data_impl->coordinate_transform_ptr->GetSize(board_width, board_height);
        project_param_->board_info.width = board_width;
        project_param_->board_info.height = board_height;
    }

    void LogicManager::ReleaseStructLight()
    {
        logic_data_impl->device_manager_ptr->ReleaseStructLight();
    }

    int LogicManager::InitDeviceParamCallback(const jrsdata::DeviceParamPtr& device_param_)
    {
        logic_data_impl->struct_light_param = device_param_->struct_light_param;
        //!采图 类延后初始化，因为需要结构光参数
        logic_data_impl->scan_process_ptr = std::make_shared<ScanProcess>(logic_data_impl->device_manager_ptr);
        logic_data_impl->capture_image_ptr = std::make_shared<CaptureImage>(logic_data_impl->device_manager_ptr);
        logic_data_impl->scan_process_ptr->SetWorkFlowCallback(logic_data_impl->work_flow_imgs_callback);
        logic_data_impl->capture_image_ptr->SetRenderCallback(logic_data_impl->render_imgs_callback);
        logic_data_impl->scan_process_ptr->SetRenderCallback(logic_data_impl->render_imgs_callback);
        logic_data_impl->work_flow_manager_ptr->SetStructLightParam(logic_data_impl->struct_light_param);

        if (logic_data_impl->device_init_msg)
        {
            device_param_->event_name = jrsaoi::LOGIC_UPDATE_DEVICE_PARAM_EVENT;
            logic_data_impl->device_init_msg(device_param_);
        }
        InitAoiImgsManager();

        return jrscore::AOI_OK;
    }
    int LogicManager::InitCallBack()
    {
        logic_data_impl->data_ptr->SetLogicCallBack(bind(&LogicManager::LogicParamUpdateCallBack, this, std::placeholders::_1));
        return jrscore::AOI_OK;
    }

    int LogicManager::HandleMotion(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }


    int LogicManager::StructLightInvoke(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        //scan_process_ptr->StartCapture ();
        return jrscore::AOI_OK;
    }
    int LogicManager::EventHandler(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_ == nullptr)
        {
            Log_ERROR("指针为空，请检查");
            return jrscore::CommonError::E_AOI_POINTER_EMPTY;
        }
        if (param_->invoke_module_name == jrsaoi::PROJECT_MODULE_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::CADEventParam>(param_);
            // auto ptr = std::make_shared<jrsdata::ProjectParam>(param_temp->project_param);

            logic_data_impl->project_manager_ptr->EventHandler(param_temp);
        }
        else if (param_->invoke_module_name == jrsaoi::DEVICE_MODULE_NAME)
        {
            if (param_->event_name == jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT)
            {
                logic_data_impl->data_ptr->EventHandler(param_);/**<发送到数据层 初始化数据库*/
            }
            logic_data_impl->device_manager_ptr->EventHandler(param_);
        }
        else if (param_->invoke_module_name == jrsaoi::LOGIC_MODULE_NAME)
        {
            this->LogicEventHandler(param_);
        }
        else if (param_->invoke_module_name == jrsaoi::DATA_MODULE_NAME)
        {
            logic_data_impl->data_ptr->EventHandler(param_);
        }
        else if (param_->invoke_module_name == jrsaoi::WORKFLOW_MODULE_NAME)
        {

            if (param_->event_name.compare("act_test_packages") == 0)
            {
                std::cout << __FUNCTION__ << " 料号测试" << std::endl;
            }
            else if (param_->event_name.compare("act_test_devices") == 0)
            {
                std::cout << __FUNCTION__ << " 元件测试" << std::endl;
            }
            else if (param_->event_name.compare("act_test_detects") == 0)
            {
                std::cout << __FUNCTION__ << " 检测框测试" << std::endl;
            }
            else if (param_->event_name.compare("act_test_detects_location") == 0)
            {
                std::cout << __FUNCTION__ << " 料号检测+位置" << std::endl;
            }
            else if (param_->event_name.compare("act_test_devices_location") == 0)
            {
                std::cout << __FUNCTION__ << " 元件测试+位置" << std::endl;
            }
            else if (param_->event_name.compare(jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME) == 0)
            {
                auto param_temp = std::static_pointer_cast<jrsdata::ControlPanelViewParam>(param_);
                if (param_temp->current_project_param == nullptr)
                {
                    Log_ERROR("运行工程时，传入的工程名称数据为空，请检查");
                    return jrscore::CommonError::E_AOI_POINTER_EMPTY;
                }
            }
            //TODO: 临时停止拍照，后期优化
            if (param_->event_name == "auto_run_panel_stop_flow_name")
            {
                logic_data_impl->scan_process_ptr->StopCapture();
            }
            logic_data_impl->work_flow_manager_ptr->WorkFlowEventHandler(param_);
        }

        else if (param_->invoke_module_name == jrsaoi::LOGIC_MODULE_NAME)
        {
            this->LogicEventHandler(param_);
        }
        else if (param_->invoke_module_name == jrsaoi::DATA_MODULE_NAME)
        {
            logic_data_impl->data_ptr->EventHandler(param_);
        }

        return 0;
    }

    void LogicManager::SetInitDeviceCallBack(DeviceMsgCallBack callback_)
    {
        logic_data_impl->device_init_msg = callback_;
    }

    void LogicManager::SetProjectCallback(jrsdata::InvokeProjectFun callback_)
    {
        logic_data_impl->project_manager_ptr->SetCallBack(callback_);
        logic_data_impl->data_ptr->SetProjectCallBack(callback_);
    }

    void LogicManager::OperateViewCallBack(jrsdata::InvokeOperateViewParamFun callback)
    {
        /** 结果回调 */
        logic_data_impl->data_ptr->SetDetectStatisticsCallBack(callback);

    }

    void LogicManager::DetectResultUpdateCallBack(jrsdata::InvokeAlgoEventParamFun callback_)
    {
        logic_data_impl->data_ptr->SetComponentDetectResultCallBack(callback_);
    }

    void LogicManager::SetOnlineDebugInfoCallBack(const jrsdata::InvokeOnlineDebugViewParamFun& callback_)
    {
        if (callback_)
        {
            logic_data_impl->data_ptr->SetOnlieDebugInfoCallBack(callback_);
        }
    }

    void LogicManager::SetSettingParamCallBack(jrsdata::InvokeSettingViewParamFun callback_)
    {
        if (callback_)
        {
            logic_data_impl->data_ptr->SetSettingParamsCallBack(callback_);
        }
    }

    void LogicManager::SetAutoRunPanelParamCallBack(jrsdata::InvokeControlPanelViewParamFun callback_)
    {
        if (callback_)
        {
            logic_data_impl->work_flow_manager_ptr->SetControlPanelCallBack(callback_);
        }
    }

    void LogicManager::SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback)
    {
        logic_data_impl->render_imgs_callback = img_buffer_callback;

    }

    void LogicManager::SetSystemStateParamCallback(jrsdata::InvokeSystemStateParamFun callback_)
    {
        logic_data_impl->data_ptr->SetSystemStateCallBack(callback_);
        logic_data_impl->device_manager_ptr->SetSystemStateCallBack(callback_);
    }

    const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& LogicManager::GetAlgoEngineManager()
    {
        return logic_data_impl->invoke_algo_engine_ptr->GetAlgoEngineManager();
    }

}
