﻿#ifndef __BARCODEDEVICEHUARUI_H__
#define __BARCODEDEVICEHUARUI_H__
#pragma once

//CUSTOM
#include "barcodedevice.h"
#include "easyid.h"
//STL
#include <mutex>
namespace jrsdevice
{


    class BarcodeDeviceHuaRui : public BarcodeDevice
    {

    public:
        BarcodeDeviceHuaRui();
        ~BarcodeDeviceHuaRui();

    public:
        /**
         * @fun EnumDeviceLists
         * @brief 获取设备SerialNumber
         * @param serial_numbers 返回设备的SerialNumber列表
         * @return 无设备返回false
         * @date 2025.5.28
         * <AUTHOR>
         */
        bool EnumDeviceLists(std::vector<std::string>& serial_numbers);
        /**
         * @fun OpenDeviceBySerialNumber
         * @brief 根据SerialNumber打开设备
         * @param serial_numbers
         * @return 返回成功状态
         * @date 2025.5.28
         * <AUTHOR>
         */
        bool OpenDeviceBySerialNumber(std::string& serial_numbers);
        /**
         * @fun OpenDevice 打开设备
         * @brief
         * @return 返回成功状态
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool OpenDevice();
        /**
         * @fun CloseDevice
         * @brief 关闭设备
         * @return 返回成功状态
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool CloseDevice();
        /**
         * @fun StartGrabing
         * @brief 开始采集二维码
         * @return 返回成功状态
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool StartGrabing();
        /**
         * @fun StopGrabing
         * @brief 结束采集二维码
         * @return 返回成功状态
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool StopGrabing();
        /**
         * @fun SetBarcodeCallback
         * @brief 设置获取二维码回调函数
         * @param callback 回调函数
         * @date 2025.5.26
         * <AUTHOR>
         */
        void SetBarcodeCallback(BarcodeCallback callback);
        /**
         * @fun SetEnableSaveImage
         * @brief 设置是否保存二维码图片
         * @param save_image 是否保存图片
         * @date 2025.5.26
         * <AUTHOR>
         */
        void SetEnableSaveImage(bool save_image);
        /**
         * @fun SetBarcodeImagePath
         * @brief 设置二维码保存图片路径
         * @param image_path 二维码图片路径
         * @date 2025.5.26
         * <AUTHOR>
         */
        void SetBarcodeImagePath(std::string image_path);
        /**
         * @fun IsOpenDevice
         * @brief 返回打开设备状态
         * @return true if the device is open;
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool IsOpenDevice();
        /**
         * @fun IsGrabing
         * @brief 返回采集设备状态
         * @return true if it is grabbing;
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool IsGrabing();

    private:
        /**
         * @fun ParsingQRCode
         * @brief 解析读取到的数据结构
         * @param info 读取数据结构
         * @date 2025.5.26
         * <AUTHOR>
         */
        void ParsingQRCode(const EidFrameInfo* info);
        /**
         * @fun AsyncFrameCallback
         * @brief 注册到EasyID的回调函数
         * @param frame 回调获取数据
         * @param userData
         * @date 2025.5.26
         * <AUTHOR>
         */
        static void EASYID_CALL AsyncFrameCallback(const EidFrameInfo* frame, void* userData);
        /**
         * @fun StartGrab
         * @brief 开始采集
         * @param cam 相机HANDLE
         * @return 0成功
         * @date 2025.5.26
         * <AUTHOR>
         */
        static int StartGrab(EidCamera cam);
        /**
         * @fun StopGrab
         * @brief 结束采集
         * @param cam 相机HANDLE
         * @return 0成功
         * @date 2025.5.26
         * <AUTHOR>
         */
        static int StopGrab(EidCamera cam);
        /**
         * @fun ConnectCamera
         * @brief 连接相机
         * @return 成功返回相机HANDLE 否则返回nullptr
         * @date 2025.5.26
         * <AUTHOR>
         */
        bool ConnectCamera(std::string& serial_numbers);

    private:
        std::mutex m_wait_mutex;
        EidCamera m_eid_camera;//相机HANDLE
        bool m_save_image;//是否保存二维码图片
        std::string file_path;//保存图片路径
        std::string m_pre_barcode;//上一个二维码
        static BarcodeDeviceHuaRui* instance; // 静态实例指针
        BarcodeCallback m_barcode_callback; // 回调函数指针

    };
}
#endif