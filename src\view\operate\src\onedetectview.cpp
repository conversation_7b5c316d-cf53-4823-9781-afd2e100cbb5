﻿//CUSTOM
#include "onedetectview.h"
#include "ui_onedetectview.h"
//QT
#include <QDateTime>
#include <QGraphicsPixmapItem>
OneDetectView::OneDetectView(jrsdata::TRACK_NUMBER track_index, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::OneDetectView)
    , m_track_index(track_index)
{
    ui->setupUi(this);
    Init();
    InitStartTime();
    //InitTrackView();
    InitTableView();
    InitShowData();
    InitShowResultData();
    ConnectSlots();
}

OneDetectView::~OneDetectView()
{
    delete ui;
}

void OneDetectView::UpdateView(const jrsdata::OperateViewParamPtr ptr) 
{
    if (ptr->event_name.compare(jrsaoi::SHORTCUT_ACT_SHOW_MARK_RESULT_EVENT_NAME) == 0)
    {
        if (show_mark_result_image_widget != nullptr)
        {
            if (show_mark_result_image_widget->isVisible())
            {
                show_mark_result_image_widget->close();
            }
            else
            {
                show_mark_result_image_widget->show();
            }
        }
    }

    // 根据事件类型调用相应的更新函数
    if (ptr->event_name == jrsaoi::MOTION_UPDATE_DEBUG_VIEW_EVENT) {
        UpdateMotionSettings(ptr); /// 更新运动设置
    }
    else if (ptr->event_name == jrsaoi::OPERATE_DETECT_STATISICS_UPDATE) {
        UpdateDetectionStatistics(ptr);/// 更新检测统计信息
    }
}

void OneDetectView::UpdateMotionSettings(const jrsdata::OperateViewParamPtr ptr) {
    // 更新所有轨道的运动设置
    /*if (track_states.get())
    {
        track_states->UpdateView(ptr);
    }*/
}

void OneDetectView::UpdateDetectionStatistics(const jrsdata::OperateViewParamPtr ptr) {
    // 检查是否是当前轨道的检测统计更新
    if (m_track_index != ptr->detect_statistics_view_param.detect_table_param_board->track_id) {
        return;
    }
    // 更新项目视图、检测结果和可视化数据
    UpdateProjectView(ptr);
    UpdateBoardDetectResultView(ptr);
    UpdateDetectResultRatioView(ptr);
    UpdateDetectDeviceView(ptr);
    UpdateDetectNgTypeView(ptr);
    UpdateBoardResultRatioView();// 更新竖向柱状图数据
    UpdateNgDeviceAndTypeView();// 更新横向柱状图数据
    UpdateMarkResultView(ptr);
}

jrsdata::TRACK_NUMBER OneDetectView::GetTrackIndex()
{
    return m_track_index;
}

void OneDetectView::OnRunTimeTimeout()
{
    UpdateRunTime();
}

void OneDetectView::CustomTableLeftClicked(const QModelIndex& index)
{
    std::vector<DetectResultStruct*> detect_result;
    m_detect_processed->m_detect_result_model->GetDataModel(detect_result);
    if (detect_result.size() > index.row() && index.row() > -1)
    {
        DetectResultStruct* one_detect_result = detect_result.at(index.row());
        ui->edit_total_devices->setText(QString::fromWCharArray(L"  总元件：%1").arg(one_detect_result->m_total_device_count));
        ui->edit_test_count->setText(QString::fromWCharArray(L"  测试数：%1").arg(one_detect_result->m_test_device_count));
        ui->edit_scan_time->setText(QString::fromWCharArray(L"拍照时间：%1s").arg(one_detect_result->m_take_phone_time.c_str()));
        ui->edit_loop_time->setText(QString::fromWCharArray(L"循环时间：%1s").arg(one_detect_result->m_circle_time.c_str()));
        ui->edit_test_count_ng->setText(QString::fromWCharArray(L"NG元件数：%1").arg(one_detect_result->m_ng_device_count));
    }
}

void OneDetectView::Init()
{
    show_mark_result_image_widget = new ShowMarkResultImageWidget();
    m_detect_processed = new DetectProcessed(nullptr);
    m_group_ng_type = new QButtonGroup(nullptr);
    m_group_ng_type->addButton(ui->rb_device, 0);
    m_group_ng_type->addButton(ui->rb_error_type, 1);
}

void OneDetectView::UpdateProjectView(const jrsdata::OperateViewParamPtr param)
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->m_project_name = param->detect_statistics_view_param.detect_result_param->project_name;//工程名
        m_detect_processed->m_ticket_num = param->detect_statistics_view_param.detect_result_param->worker_num;//工单号
        m_detect_processed->m_total_devices = param->detect_statistics_view_param.detect_result_param->total_component_num;//总元件
        m_detect_processed->m_test_count = param->detect_statistics_view_param.detect_result_param->detect_component_num;//测试数
        m_detect_processed->m_scan_time = param->detect_statistics_view_param.detect_result_param->photograph_take_time;//拍照时间
        m_detect_processed->m_loop_time = param->detect_statistics_view_param.detect_result_param->loop_take_time;//循环时间
        m_detect_processed->m_ng_devices_count = param->detect_statistics_view_param.detect_result_param->ng_component_num;//NG元件数
        ui->edit_project_name->setText(QString::fromWCharArray(L"    工程：%1").arg(m_detect_processed->m_project_name.c_str()));
        ui->edit_ticket_num->setText(m_detect_processed->m_ticket_num.c_str());
        ui->edit_total_devices->setText(QString::fromWCharArray(L"  总元件：%1").arg(m_detect_processed->m_total_devices));
        ui->edit_test_count->setText(QString::fromWCharArray(L"  测试数：%1").arg(m_detect_processed->m_test_count));
        ui->edit_scan_time->setText(QString::fromWCharArray(L"拍照时间：%1s").arg(m_detect_processed->m_scan_time.c_str()));
        ui->edit_loop_time->setText(QString::fromWCharArray(L"循环时间：%1s").arg(m_detect_processed->m_loop_time.c_str()));
        ui->edit_test_count_ng->setText(QString::fromWCharArray(L"NG元件数：%1").arg(m_detect_processed->m_ng_devices_count));
    }
}

void OneDetectView::UpdateMarkResultView(const jrsdata::OperateViewParamPtr param)
{
    if (show_mark_result_image_widget != nullptr)
    {
        if (param != nullptr)
        {
            if (param->detect_statistics_view_param.detect_result_param.has_value())
            {
                show_mark_result_image_widget->SetMarkResult(param->detect_statistics_view_param.detect_result_param->mark_imgs,
                    param->detect_statistics_view_param.detect_result_param->mark_scores,
                    param->detect_statistics_view_param.detect_result_param->mark_result_rect,
                    param->detect_statistics_view_param.detect_result_param->mark_status);
            }
        }
    }
}

void OneDetectView::UpdateBoardDetectResultView(const jrsdata::OperateViewParamPtr param)
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->AddOneBoardResult(
            param->detect_statistics_view_param.detect_table_param_board->main_barcode,
            param->detect_statistics_view_param.detect_table_param_board->detect_result,
            param->detect_statistics_view_param.detect_table_param_board->track_id,
            param->detect_statistics_view_param.detect_result_param->total_component_num,
            param->detect_statistics_view_param.detect_result_param->detect_component_num,
            param->detect_statistics_view_param.detect_result_param->ng_component_num,
            param->detect_statistics_view_param.detect_result_param->photograph_take_time,
            param->detect_statistics_view_param.detect_result_param->loop_take_time
        );
        UpdateDetectResultData();
    }
}

void OneDetectView::UpdateDetectDeviceView(const jrsdata::OperateViewParamPtr param)
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->vec_ng_devices_ring.clear();
        for (auto device_name : param->detect_statistics_view_param.detect_result_device_name_param_vec.value())
        {
            m_detect_processed->vec_ng_devices_ring.push_back(RingData(device_name.name, device_name.count));
        }
    }
}

void OneDetectView::UpdateDetectNgTypeView(const jrsdata::OperateViewParamPtr param)
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->vec_ng_types_ring.clear();
        for (auto device_type : param->detect_statistics_view_param.detect_result_device_type_param_vec.value())
        {
            m_detect_processed->vec_ng_types_ring.push_back(RingData(device_type.name, device_type.count));
        }
    }
}

void OneDetectView::UpdateDetectResultRatioView(const jrsdata::OperateViewParamPtr param)
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->m_aoi_ok_count = param->detect_statistics_view_param.detect_result_ratio_param->board_pass_number;
        m_detect_processed->m_aoi_ng_count = param->detect_statistics_view_param.detect_result_ratio_param->board_ng_number;
        m_detect_processed->m_sub_board_aoi_good_count = param->detect_statistics_view_param.detect_result_ratio_param->subboard_pass_number;
        m_detect_processed->m_sub_board_aoi_ng_count = param->detect_statistics_view_param.detect_result_ratio_param->subboard_ng_number;
        m_detect_processed->UpdateResultRatio();
    }
}

void OneDetectView::ClearProjectView()
{
    ui->edit_project_name->clear();
    ui->edit_ticket_num->clear();
    ui->edit_total_devices->setText(QString::fromWCharArray(L"总元件：0"));
    ui->edit_test_count->setText(QString::fromWCharArray(L"测试数：0"));
    ui->edit_scan_time->setText(QString::fromWCharArray(L"拍照时间：0.0s"));
    ui->edit_loop_time->setText(QString::fromWCharArray(L"循环时间：0.0s"));
    ui->edit_test_count_ng->setText(QString::fromWCharArray(L"NG元件数：0"));
}

void OneDetectView::ClearTableResult()
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->vec_board_result.clear();
    }
}

void OneDetectView::ClearNgTypeAndDeviceResult()
{
    m_detect_processed->vec_ng_types_ring.clear();
    m_detect_processed->vec_ng_devices_ring.clear();
    UpdateNgDeviceAndTypeView();
}

void OneDetectView::ClearBoardResultRatio()
{
    m_detect_processed->m_aoi_ok_count = 0;
    m_detect_processed->m_aoi_ng_count = 0;
    m_detect_processed->m_sub_board_aoi_good_count = 0;
    m_detect_processed->m_sub_board_aoi_ng_count = 0;
    UpdateBoardResultRatioView();
    ui->data_start->setDateTime(QDateTime::currentDateTime());
}

void OneDetectView::ConnectSlots()
{
    connect(ui->edit_ticket_num, SIGNAL(editingFinished()), this, SLOT(EditTicketNumFinished()));
    connect(ui->btn_clear_ratio, SIGNAL(clicked()), this, SLOT(ClearActionRatio()));
    connect(ui->btn_clear_device, SIGNAL(clicked()), this, SLOT(ClearActionDevice()));
    connect(ui->rb_device, SIGNAL(toggled(bool)), this, SLOT(OnDeviceToggled(bool)));
    connect(ui->rb_error_type, SIGNAL(toggled(bool)), this, SLOT(OnErrorTypeToggled(bool)));
    connect(ui->tv_result, SIGNAL(CustomLeftClicked(const QModelIndex&)), this, SLOT(CustomTableLeftClicked(const QModelIndex&)));
}

void OneDetectView::InitTableView()
{
    ui->tv_result->setModel(m_detect_processed->m_detect_result_model);
    ui->tv_result->setColumnWidth(0, 20);
    ui->tv_result->setColumnWidth(1, 125);
    ui->tv_result->setColumnWidth(2, 125);
    ui->tv_result->setColumnWidth(3, 45);
    ui->tv_result->setColumnWidth(4, 45);
    ui->tv_result->horizontalHeader()->setDefaultAlignment(Qt::AlignCenter);
    ui->tv_result->verticalHeader()->setVisible(false);
}

void OneDetectView::InitShowData()
{
    ClearBoardResultRatio();
}

void OneDetectView::InitShowResultData()
{
    ClearNgTypeAndDeviceResult();
}


void OneDetectView::InitStartTime()
{
    run_time_second = 0;
    ui->edit_start_time->setText(QString::fromWCharArray(L"开机时间：%1").arg(jrscore::AOITools::GetCurrentDataTime().c_str()));
    ui->edit_run_time->setText(QString::fromWCharArray(L"运行时间：%1").arg("00:00:00:00"));
    run_timer = new QTimer(this);
    connect(run_timer, &QTimer::timeout, this, &OneDetectView::OnRunTimeTimeout);
    run_timer->start(1000);
}

void OneDetectView::UpdateRunTime()
{
    run_time_second++;
    std::chrono::seconds duration(run_time_second);
    std::string run_time_text = jrscore::AOITools::FormatDuration(duration);
    ui->edit_run_time->setText(QString::fromWCharArray(L"运行时间：%1").arg(run_time_text.c_str()));
}

void OneDetectView::UpdateBoardResultRatioView()
{
    m_detect_processed->UpdateResultRatio();// 更新检测结果比率
    ui->histogram_result->SetDataInfo(m_detect_processed->vec_board_result_ring_datas);
}

std::vector<RingData> OneDetectView::GetDetectDatas(SHOW_DETECT_NG_TYPE show_result_type, DetectProcessed* detect_processed) {
    switch (show_result_type) {
    case SHOW_DETECT_NG_TYPE::SHOW_DEVICE_TYPE:
        return detect_processed->vec_ng_devices_ring;
    case SHOW_DETECT_NG_TYPE::SHOW_NG_TYPE:
        return detect_processed->vec_ng_types_ring;
    case SHOW_DETECT_NG_TYPE::SHOW_ANCHOR_TYPE:
        return {};
    default:
        Log_INFO("[", __FUNCTION__, "] 显示结果方式出错", detect_processed->m_show_result_type);
        return {};
    }
}

std::vector<RingData> OneDetectView::GetTopNData(const std::vector<RingData>& detect_datas, int n) {
    std::vector<RingData> sorted_datas = detect_datas;
    std::sort(sorted_datas.begin(), sorted_datas.end(), [](const RingData& a, const RingData& b) {
        return a.m_value > b.m_value;
        });
    std::vector<RingData> top_n_data;
    for (int i = 0; i < n && i < sorted_datas.size(); ++i) {
        top_n_data.push_back(sorted_datas[i]);
    }
    return top_n_data;
}

double OneDetectView::CalculateSumOfRemainingData(const std::vector<RingData>& detect_datas, int start_index) {
    double sum = 0;
    for (int i = start_index; i < detect_datas.size(); ++i) {
        sum += detect_datas[i].m_value;
    }
    return sum;
}

void OneDetectView::UpdateNgDeviceAndTypeView() {
    std::vector<RingData> detect_datas = GetDetectDatas(m_detect_processed->m_show_result_type, m_detect_processed);
    if (detect_datas.empty()) {
        ui->horizontalbarchart_result->SetDataInfo({});
        return;
    }
    std::vector<RingData> top_n_data = GetTopNData(detect_datas, SHOW_NG_TYPE_COUNT);
    //double sum_of_remaining = CalculateSumOfRemainingData(detect_datas, SHOW_NG_TYPE_COUNT);
    //top_n_data.emplace_back(" 其他", sum_of_remaining);
    ui->horizontalbarchart_result->SetDataInfo(top_n_data);
}

void OneDetectView::UpdateDetectResultData()
{
    m_detect_processed->m_detect_result_model->SetDataModel(m_detect_processed->vec_board_result);
}
void OneDetectView::EditTicketNumFinished()
{
    if (m_detect_processed != nullptr)
    {
        m_detect_processed->m_ticket_num = ui->edit_ticket_num->text().toStdString();
        jrsdata::OperateViewParamPtr operate_view_param_ptr = std::make_shared<jrsdata::OperateViewParam>();
        operate_view_param_ptr->event_name = jrsaoi::OPERATE_UPDATE_TICKET_NUMBER;//工单号更新消息
        operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        operate_view_param_ptr->detect_statistics_view_param.detect_result_param = std::make_optional<jrsdata::DetectResultViewParam>();
        operate_view_param_ptr->detect_statistics_view_param.detect_result_param->worker_num = ui->edit_ticket_num->text().toStdString();
        operate_view_param_ptr->detect_statistics_view_param.detect_result_param->project_name = ui->edit_project_name->text().remove("    工程：").toStdString();
        emit SigDetectViewChangeTrigger(operate_view_param_ptr);
    }
}

void OneDetectView::ClearActionRatio()
{
    ClearBoardResultRatio();
    jrsdata::OperateViewParamPtr operate_view_param_ptr = std::make_shared<jrsdata::OperateViewParam>();
    operate_view_param_ptr->event_name = jrsaoi::OPERATE_DETECT_CLEAR_RESULT;//清除检测结果历史记录
    operate_view_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
    operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
    operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    operate_view_param_ptr->detect_statistics_view_param.detect_result_param = std::make_optional<jrsdata::DetectResultViewParam>();
    operate_view_param_ptr->detect_statistics_view_param.detect_result_param->project_name = ui->edit_project_name->text().toStdString();
    emit SigDetectViewChangeTrigger(operate_view_param_ptr);
}

void OneDetectView::ClearActionDevice()
{
    ClearNgTypeAndDeviceResult();
    jrsdata::OperateViewParamPtr operate_view_param_ptr = std::make_shared<jrsdata::OperateViewParam>();
    operate_view_param_ptr->event_name = jrsaoi::OPERATE_DETECT_CLEAR_DEVICE_RESULT;//清除检测元件结果历史记录
    operate_view_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
    operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
    operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    operate_view_param_ptr->detect_statistics_view_param.detect_result_param = std::make_optional<jrsdata::DetectResultViewParam>();
    operate_view_param_ptr->detect_statistics_view_param.detect_result_param->project_name = ui->edit_project_name->text().toStdString();
    emit SigDetectViewChangeTrigger(operate_view_param_ptr);
}

void OneDetectView::OnDeviceToggled(bool checked)
{

    if (m_detect_processed != nullptr)
    {
        if (checked)
        {
            m_detect_processed->m_show_result_type = SHOW_DETECT_NG_TYPE::SHOW_DEVICE_TYPE;
        }
        UpdateNgDeviceAndTypeView();
    }
}

void OneDetectView::OnErrorTypeToggled(bool checked)
{
    if (m_detect_processed != nullptr)
    {
        if (checked)
        {
            m_detect_processed->m_show_result_type = SHOW_DETECT_NG_TYPE::SHOW_NG_TYPE;
        }
        UpdateNgDeviceAndTypeView();
    }
}

