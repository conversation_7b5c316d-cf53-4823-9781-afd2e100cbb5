﻿/*****************************************************************//**
 * @file   connectpool.h
 * @brief  数据库连接池
 * @details 安全连接池
 * @date   April 2024
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>April 2024         <td>         <td>HJC                       <td><EMAIL> <td>
 *   @copyright 版权 CopyRight (C), 2024-2025.
 * *********************************************************************/

#ifndef _CONNECTPOOL_H_
#define _CONNECTPOOL_H_
#include <stdexcept>
#include <deque>
#include <mutex>
#include <memory>
namespace jrsdatabase {
    template <typename DB>
    class ConnectionPool {
    public:

        /**
       * @fun Instance
       * @brief 获取连接池
       * @return  返回一个连接池实例
       * <AUTHOR>
       */
        static ConnectionPool<DB>& Instance() {
            static ConnectionPool<DB> instance;
            return instance;
        }


        /**
         * @fun Init
         * @brief 初始化连接池
         * @param maxsize 最大连接数量
         * @param ...args 数据库连接参数
         * @return  NULL
         * <AUTHOR>
         */
        template <typename... Args>
        int Init(int maxsize, Args&&... args) {
            std::unique_lock<std::mutex> lock(mutex_);
            int res = jrscore::AOI_OK;

            if (initialized_) {
                pool_.clear();
                try {
                    InitImpl(maxsize, res, std::forward<Args>(args)...);
                }
                catch (const std::exception& e) {
                    std::cerr << "Initialization failed: " << e.what() << std::endl;
                    return jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
                }
                return res;
            }
            else {
                try {
                    // 首次初始化
                    std::call_once(flag_, &ConnectionPool<DB>::template InitImpl<Args...>,
                        this, maxsize, res, std::forward<Args>(args)...);
                    initialized_ = true;
                }
                catch (const std::exception& e) {
                    std::cerr << "Initialization failed: " << e.what() << std::endl;
                    return jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
                }
                return res;
            }
        }


        /**
         * @fun Get
         * @brief 从连接池获取连接
         * @return  连接的数据库指针
         * <AUTHOR>
         */
        std::shared_ptr<DB> Get() {
            std::unique_lock<std::mutex> lock(mutex_);

            while (pool_.empty()) {
                if (condition_.wait_for(lock, std::chrono::seconds(60)) ==
                    std::cv_status::timeout) {
                    // timeout
                    return nullptr;
                }
            }
            auto conn = pool_.front();
            pool_.pop_front();

            lock.unlock();
            if (conn == nullptr || !conn->Ping()) {
                return CreateConnection();
            }

            // 检查超时,闲置时间应小于8小时
            auto now = std::chrono::system_clock::now();
            auto last = conn->GetLatestOperateTime();
            auto mins = std::chrono::duration_cast<std::chrono::minutes>(now - last).count();
            if ((mins - 6 * 60) > 0) {
                return CreateConnection();
            }

            conn->UpdateOperateTime();

            return conn;
        }

        /**
         * @fun ReturnBack
         * @brief 将连接放回连接池
         * @param conn 数据库连接
         * @return  NULL
         * <AUTHOR>
         */
        void ReturnBack(std::shared_ptr<DB> conn)
        {

            if (conn == nullptr || conn->HasError()) {
                conn = CreateConnection();
            }

            //std::cout << "[" << __FUNCTION__ << "][" << __LINE__ << "]" << "DB_TEST args_:" << std::get<0>(args_);

            std::unique_lock<std::mutex> lock(mutex_);
            pool_.push_back(conn);
            lock.unlock();
            condition_.notify_one();
        }

    private:
        /**< 禁止拷贝构造函数和赋值运算符*/
        ConnectionPool() = default;
        ~ConnectionPool() = default;
        ConnectionPool(const ConnectionPool&) = delete;
        ConnectionPool& operator=(const ConnectionPool&) = delete;

        /**
        * @fun InitImpl
        * @brief 初始化连接池的实现
        * @param maxsize 最大连接数
        * @param ...args 连接数据库参数
        * @return  NULL
        * <AUTHOR>
        */
        template <typename... Args>
        void InitImpl(int maxsize, int& res, Args &&...args) {
            args_ = std::make_tuple(ConvertToString(std::forward<Args>(args))...); //转换到std::string
            for (int i = 0; i < maxsize; ++i) {
                auto conn = std::make_shared<DB>();
                if (!conn->Connect(std::forward<Args>(args)...)) {
                    pool_.push_back(conn);
                }
                else {
                    res = jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
                    throw std::invalid_argument("init failed");

                }
            }
        }
        /**< c_str() to std::string*/
        template <typename T>
        static decltype(auto) ConvertToString(T&& value) {
            if constexpr (std::is_same_v<std::decay_t<T>, const char*>)
            {
                return std::string(value);
            }
            else {
                return std::forward<T>(value);
            }
        }
        /**<std::string to c_str()*/
        template <typename T>
        static auto ConvertToCStr(T&& value)
        {
            if constexpr (std::is_same_v<std::decay_t<T>, std::string>)
            {
                return value.c_str(); // std::string 转换为 c_str()
            }
            else {
                return std::forward<T>(value);
            }
        }

        /**
        * @fun CreateConnection
        * @brief 创建连接
        * @return  返回连接指针
        * <AUTHOR>
        */
        auto CreateConnection() {
            auto conn = std::make_shared<DB>();
            auto fn = [conn](auto... targs) {
                return !conn->Connect(ConvertToCStr(targs)...);
                };
            return std::apply(fn, args_) ? conn : nullptr;
        }
        std::deque<std::shared_ptr<DB>> pool_;  /**< 连接池*/
        std::mutex mutex_;                      /**< 互斥锁*/
        std::condition_variable condition_;    /**< 条件变量*/
        std::once_flag flag_;                  /**< 标志用于call_once*/
        std::tuple<std::string, std::string, std::string, std::string, int, int> args_;  /**< 连接参数*/
        bool initialized_ = false;             /**< 连接池是否已经初始化 */
    };

    /*
    * @brief 连接保护器, 用于在离开作用域时
    * 自动将连接放回连接池
    */
    template <typename DB>
    struct ConnGuard {
        ConnGuard(std::shared_ptr<DB> con) : conn_(con) {}
        ~ConnGuard() { ConnectionPool<DB>::Instance().ReturnBack(conn_.lock()); }

    private:
        std::weak_ptr<DB> conn_;
    };

};

#endif//!_CONNECTPOOL_H_
