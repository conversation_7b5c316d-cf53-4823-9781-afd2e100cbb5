#ifndef __MACHINESTATEVIEW_H__
#define __MACHINESTATEVIEW_H__

/*****************************************************************
 * @file   machinestateview.h
 * @brief  机器状态界面
 * @details
 * <AUTHOR>
 * @date 2024.11.20
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.20          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 
 // prebuild
#include "pch.h"
 //STD

 //Custom
//#include "viewparam.hpp"

 //Third
#include<QWidget>

QT_BEGIN_NAMESPACE
namespace Ui { class machinestateview; };
QT_END_NAMESPACE

class QHBoxLayout;
class QLabel;
namespace jrsaoi
{
    class MachineStateView : public QWidget
    {
        Q_OBJECT

    public:
        MachineStateView(QWidget* parent = nullptr);
        ~MachineStateView();
        /**
         * @fun UpdateView
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.22
         */
        int UpdateView(const jrsdata::ViewParamBasePtr& param_);
    private:
        //Fun
        void Init();
        void InitView();
        void InitMember();
        void InitConnect();
        /**
         * @fun UpdateDetectView
         * @brief 更新检测状态界面
         * @param detect_state_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void UpdateDetectView(jrsdata::BoardDetectAndDetectFlowState detect_state_);
        /**
         * @fun UpdateStopView
         * @brief 更新停止状态
         * @param stop_state_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void UpdateStopView(std::optional<jrsdata::MachineStateViewParam::StopState> stop_state_);
        /**
         * @fun UpdateDetectFlowStateView
         * @brief 更新检测流程状态
         * @param detect_flow_state_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void UpdateDetectFlowStateView(jrsdata::MachineStateViewParam::DetectFlowState detect_flow_state_);
        /**
         * @fun UpdateBoardDetectFlowStateView
         * @brief 更新板检测流程状态
         * @param board_detect_state_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void UpdateBoardDetectFlowStateView(jrsdata::MachineStateViewParam::BoardDetectState board_detect_state_);
        /**
         * @fun ViewChange
         * @brief 界面切换
         * @param is_auto_run_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void ViewChange(const bool& is_auto_run_);
        /**
         * @fun HideLayout
         * @brief 隐藏布局
         * @param layout_
         * @param is_hide_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void HideLayout(QHBoxLayout* layout_, bool is_hide_);
        /**
         * @fun ShowLabelFont
         * @brief 显示字体
         * @param label_
         * @param content_
         * @param font_size
         * @param color_
         * <AUTHOR>
         * @date 2024.11.22
         */
        void ShowLabelFont(QLabel* const label_, const std::string& content_, int font_size, const Qt::GlobalColor& color_);

        //Member
        Ui::machinestateview* ui;
        bool flow_switch; /**< 流程开关*/
        jrsdata::MachineStateViewParamPtr _param_ptr;
        std::unordered_map<jrsdata::MachineStateViewParam::StopState, std::tuple<std::string, Qt::GlobalColor, std::string >> _stop_state_map;  /**<停止状态参数*/
        std::unordered_map < jrsdata::MachineStateViewParam::BoardDetectState, std::tuple<std::string, Qt::GlobalColor, std::string >> _board_detect_state_map; /**<板子检测状态参数*/
        std::unordered_map<jrsdata::MachineStateViewParam::DetectFlowState, std::tuple<std::string, Qt::GlobalColor, std::string >> _detect_flow_map;  /**<检测流程状态参数*/
        int _font_size;
    };
}

#endif // !__MACHINESTATEVIEW_H__
