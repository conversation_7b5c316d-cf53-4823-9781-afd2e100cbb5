﻿#include "graphicsobject.h"
#include "graphicsprocess.h"
#include "graphicsalgorithm.h"
#include "graphicsserialize.hpp"
#include "graphicseventparam.hpp"

#include "controlpointabstract.h"
#include "controlpointconstants.hpp"
#include "controlpointfactory.h"

#include "eventparam.hpp"
#include "customcursortype.hpp"
#include "painter.h"
#include "renderer.h"
#include "log.h"

#include "qnamespace.h" // CursorShape

// #include <QPainter>

void RectGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void RectGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    obj->Update();

    Color c;
    float think_ness = 0.f;
    this->SetColorAndThickness(config, c, think_ness);
    // cv::RotatedRect boundingbox = obj->GetBoundingbox();
 // /*四顶点*/
 // cv::Point2f ovp[4];
 // boundingbox.points(ovp);

 // if (obj->paths.empty() || obj->IsNeedUpdate())
 // {
 //     std::vector<Vec2> tvPaths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };
 //     tvPaths.swap(obj->paths);
 //     obj->SetUpdated();
 // }

 ////auto& thinkness = config->true_line_width;
 //if (obj->settings.GetIsSelected())
 //{
 //}
    p->DrawLines(obj->paths, c, true, think_ness);

    if (obj->settings.GetIsFlag())
    {
        if (!obj->path_flags.empty())
        {
            // 获取第一个路径标志点作为中心点
            const auto& point = obj->path_flags[0];

            // 计算基础 flag_length
            auto base_flag_length = static_cast<int>(3 / r->GetZoom());

            // 确保 flag_length 不超过窗口尺寸的一半
            int max_possible_length = std::min(this->w() / 2, this->h() / 2);
            auto flag_length = std::min(base_flag_length, max_possible_length);

            // 计算正方形的四个顶点
            std::vector<Vec2> points = {
                point + Vec2(-flag_length, -flag_length), // 左下角
                point + Vec2(flag_length, -flag_length),  // 右下角
                point + Vec2(flag_length, flag_length),   // 右上角
                point + Vec2(-flag_length, flag_length)   // 左上角
            };

            // 绘制正方形
            p->DrawLines(points, c, true, -1);
        }
        // std::vector<Vec2> points{ {(ovp[0].x + ovp[1].x) * 0.5f, (ovp[0].y + ovp[1].y) * 0.5f} };
        // p->DrawPoints(obj->path_flags, c, thinkness);
    }

    if (obj->settings.GetIsSelected())
    {
        DrawControlPoint(r, p, config);

        // 外接框信息文本
        if (0)
        {
            DrawInfo();
        }
    }
}

void RectGraphics::Update()
{
    if (IsNeedUpdate())
    {
        UpdateByTemp(this);
        UpdateControlPoint();
        UpdateDrawBuffer();
        SetUpdated();
    }
}

void RectGraphics::UpdateDrawBuffer()
{
    cv::RotatedRect boundingbox = this->GetBoundingbox();
    /*四顶点*/
    cv::Point2f vertex[4];
    boundingbox.points(vertex);
    std::vector<Vec2> tpaths{
         {vertex[0].x, vertex[0].y}, {vertex[1].x, vertex[1].y},{vertex[2].x, vertex[2].y}, {vertex[3].x, vertex[3].y} };
    tpaths.swap(this->paths);
    std::vector<Vec2> tpath_flags{ {(vertex[2].x + vertex[3].x) * 0.5f, (vertex[2].y + vertex[3].y) * 0.5f} };
    tpath_flags.swap(this->path_flags);
}

void RectGraphics::UpdateControlPoint()
{
    auto const obj = GetTemp(this, false);

    std::vector<std::shared_ptr<ControlPointAbstract>> cps;

    //std::vector<ControlPoint> cps;
    /*创建尺寸控制点*/
    //if (obj->w() > 10 || obj->h() > 10) //点小于10 的无法放大和缩小
    {
        auto tcps = ControlPointFactory::CreateControlPointGroup(ControlPointGroupType::SIZE_POINT, obj);
        //auto tcps = CreateSizeControlPoint(obj, static_cast<int>(CustomCursorType::HandClose));
        cps.insert(cps.end(), tcps.begin(), tcps.end());
    }
    ///*创建旋转控制点*/
    //if (0)
    //{
    //    auto tcps = CreateRotateControlPoint(obj, Qt::DragMoveCursor);
    //    cps.insert(cps.end(), tcps.begin(), tcps.end());
    //}

    /*创建中心拖拽控制点*/
    {
        auto cp = ControlPointFactory::CreateControlPoint(ControlPointType::MOVE_POINT, obj);
        //auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
        cps.emplace_back(cp);
    }
    cps.swap(control_points);
}

void RectGraphics::DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config)
{
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        cp->Draw(r, p, config);
    }
}

std::vector<ControlPoint> RectGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);

    std::vector<ControlPoint> cps;
    {
        auto tcps = CreateSizeControlPoint(obj, static_cast<int>(CustomCursorType::HandClose));
        cps.insert(cps.end(), tcps.begin(), tcps.end());
    }
    ///*创建旋转控制点*/
    //if (0)
    //{
    //    auto tcps = CreateRotateControlPoint(obj, Qt::DragMoveCursor);
    //    cps.insert(cps.end(), tcps.begin(), tcps.end());
    //}

    /*创建中心拖拽控制点*/
    {
        auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
        cps.emplace_back(cp);
    }

    return cps;
}

int RectGraphics::TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam& param)
{
    if (!settings.GetIsSelected())
        return 2;

    double mindis = param.max_limit;
    std::shared_ptr<ControlPointAbstract> response_cp = nullptr;
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        auto dis = cp->TryResponse(param.x, param.y, (float)param.max_limit);
        if (dis > 0 && dis < mindis)
        {
            // printInfo(std::stringstream() << "响应距离:" << dis);
            mindis = dis;
            response_cp = cp;
        }
    }

    if (!response_cp)
        return 1;
    controlpoint = response_cp;
    // attr = response_cp->attributes;
    return 0;
}

int RectGraphics::ResponseControlPoint(const ResponseEventParam& param)
{
    auto obj = GetAndUpdateTemp(this, param.istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;
    obj->SetRequireUpdate();
    for (auto& cp : obj->control_points)
    {
        if (!cp) continue;
        if (cp->attributes == param.attr)
        {
            cp->Response(param, obj);
            return 0;
        }
    }
    return 1;
    // return RectResponseControlPoint(obj, param.attr.type, param.xstart, param.ystart, param.xend, param.yend);
}

int RectGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    return RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
}

int RectGraphics::ResponseEvent(const MouseEventValue& mouse_event_)
{
    if (mouse_event_.state == MouseEventValue::MouseState::wheel)
    {
        auto cur_width = this->w();
        auto cur_height = this->h();
        if (mouse_event_.type == WheelType::ZoomIn)
        {

            //当前目标宽高增加
            cur_width += mouse_event_.step;
            cur_height += mouse_event_.step;
        }
        else if (mouse_event_.type == WheelType::ZoomOut)
        {
            //当前目标宽高减小
            cur_width -= mouse_event_.step;
            cur_height -= mouse_event_.step;
        }
        this->SetWH(cur_width, cur_height);

    }
    return 0;
}

std::shared_ptr<GraphicsAbstract> RectGraphics::Clone() const
{
    return std::make_shared<RectGraphics>(*this);
}

std::string RectGraphics::GetSerializedData()
{
    return createConfig(this);
}

void RectGraphics::DrawInfo()
{
    //     cv::Rect2f tr = boundingbox.boundingRect2f();
    //     auto pd = r->GetPaintDeviceRAII();
    //     auto device = pd.device;
    //     if (device)
    //     {
    //         QPainter painter(device);
    //         painter.beginNativePainting();

    //         QString text = QString("%1 x %2").arg(obj->w(), 0, 'f', 2).arg(obj->h(), 0, 'f', 2);
    //         QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);

    //         float text_rb_x = 0;
    //         float text_rb_y = 0;
    //         r->MouseToWorld(text_rb_x, text_rb_y);
    //         float text_tl_x = textsize.width();
    //         float text_tl_y = textsize.height();
    //         r->MouseToWorld(text_tl_x, text_tl_y);
    //         float text_o_x = abs(text_tl_x - text_rb_x);
    //         float text_o_y = abs(text_tl_y - text_rb_y);

    //         float tlx = obj->x() - abs(text_o_x * 0.5);
    //         float tly = tr.y + tr.height + abs(text_o_y * 0.5);
    //         r->WorldToMouse(tlx, tly);

    //         painter.drawText(QRectF(tlx, tly, textsize.width(), textsize.height()),
    //             Qt::AlignCenter | Qt::TextWordWrap, text);
    //         painter.endNativePainting();
    //      }
}

void CircleGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void CircleGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);

    if (obj->settings.GetIsSelected())
    {
        c = c.GetInv();
    }

    /*外轮廓*/
    if (obj->settings.GetIsSelected())
    {
        cv::RotatedRect boundingbox = obj->GetBoundingbox();
        /*外接矩形*/
        DrawBoundingbox(p, boundingbox, c.GetInv(), 1);
    }

    auto& thinkness = config->_display_style.true_line_width;
    if (obj->paths.empty() || obj->IsNeedUpdate())
    {
        int seg = std::min(720, std::max(8, static_cast<int>(std::round(obj->w() * 1.5))));
        p->DrawRotatedEllipse({ obj->x(), obj->y() },
            obj->w() * 0.5, obj->h() * 0.5, A_DEG_TO_RAD(obj->a()), c, seg,
            thinkness, &obj->paths);
        obj->SetUpdated();
    }
    else
    {
        p->DrawLines(obj->paths, c, true, thinkness);
    }
}

std::string CircleGraphics::GetSerializedData()
{
    return createConfig(this);
}

std::vector<ControlPoint> CircleGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);
    if (!obj)
        return {};

    // cv::RotatedRect boundingbox = obj->GetBoundingbox();
    std::vector<ControlPoint> cps;
    // std::vector<ControlPointCorner> cpcs;

    {
        /*创建尺寸控制点*/
        {
            auto tcps = CreateSizeControlPoint(obj, static_cast<int>(CustomCursorType::HandClose));
            cps.insert(cps.end(), tcps.begin(), tcps.end());
        }
        /*创建旋转控制点*/
        if (0)
        {
            auto tcps = CreateRotateControlPoint(obj, static_cast<int>(CustomCursorType::HandClose));
            cps.insert(cps.end(), tcps.begin(), tcps.end());
        }

        /*创建中心拖拽控制点*/
        {
            auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
            cps.emplace_back(cp);
        }
    }
    return cps;
}

int CircleGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    return RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
}

void TextGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;
    Draw(r, nullptr, config);
}

void TextGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    float x = obj->x();
    float y = obj->y();
    r->WorldToMouse(x, y);

    auto& thinkness = config->_display_style.true_line_width;
    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);
    p->PainterText(text.c_str(), x, y, c, thinkness);
}

std::string TextGraphics::GetSerializedData()
{
    return createConfig(this);
}

std::vector<ControlPoint> TextGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);

    std::vector<ControlPoint> cps;

    {
        /*创建中心拖拽控制点*/
        {
            auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
            cps.emplace_back(cp);
        }
    }
    return cps;
}

int TextGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    if (auto state = RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
        state != GraphicsAbstract::UNKNOWN_TYPE)
    {
        return state;
    }

    // switch (ControlPointType(attr.type))
    // {
    // default:
    //     return GraphicsAbstract::UNKNOWN_TYPE;
    // }
    return OK;
}

std::string LineGraphics::GetSerializedData()
{
    return createConfig(this);
}

void PolygonGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void PolygonGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);

    if (obj->settings.GetIsSelected())
    {
        c = c.GetInv();
    }

    UpdateDrawPath(obj);

    {
        auto& thinkness = config->_display_style.true_line_width;
        p->DrawLines(obj->paths, c, is_close, thinkness);
    }
}

std::string PolygonGraphics::GetSerializedData()
{
    return createConfig(this);
}

std::vector<ControlPoint> PolygonGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);
    if (!obj)
        return {};
    std::vector<ControlPoint> cps;
    {
        /*创建中心拖拽控制点*/
        {
            auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move), 0, obj->paths);
            cps.emplace_back(cp);
        }
    }
    return cps;
}

int PolygonGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    if (auto state = RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
        state != GraphicsAbstract::UNKNOWN_TYPE)
    {
        return state;
    }

    // TODO 
    switch (ControlPointType(attr.type))
    {
    case ControlPointType::CONTOUR_POINT:
    {
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }
    return OK;
}

void PolygonGraphics::ComfirmPoint()
{
    if (this->contours.size() < 1)
        return;

    if (A_FLOAT_EQUAL_DEFAULT(this->x(), 0) && A_FLOAT_EQUAL_DEFAULT(this->y(), 0))
    {
        CreateBoundingBox(this);
    }
    for (size_t i = 0; i < contours.size(); ++i)
    {
        this->contours[i].x -= this->x();
        this->contours[i].y -= this->y();
    }
    this->SetRequireUpdate();
}

void PolygonGraphics::SetPoint(const std::vector<Vec2>& points, bool is_local)
{
    this->contours = points;
    if (!is_local)
    {
        ComfirmPoint();
    }
}

void PolygonGraphics::AddPoint(float x, float y, bool is_local)
{
    if (is_local)
    {
        x -= this->x();
        y -= this->y();
    }
    contours.emplace_back(x, y);
    this->SetRequireUpdate();
}

void PolygonGraphics::DeletePoint()
{
    if (contours.empty())
        return;

    contours.pop_back();

    this->SetRequireUpdate();
}

void PolygonGraphics::CreateBoundingBox(PolygonGraphics* obj)
{
    float maxx, minx, maxy, miny;
    // getContourMinMax(maxx, minx, maxy, miny, contours);
    GetContoursMinMax(maxx, minx, maxy, miny, obj->contours);
    obj->SetXY((maxx + minx) * 0.5f, (maxy + miny) * 0.5f);
    obj->SetWH(std::abs(maxx - minx), std::abs(maxy - miny));
}

void PolygonGraphics::UpdateDrawPath(PolygonGraphics* obj)
{
    if (obj->paths.empty() || obj->IsNeedUpdate())
    {
        obj->paths.clear();
        Vec2 center(obj->x(), obj->y());
        int size = (int)obj->contours.size();

        for (int i = 0; i < size; ++i)
        {
            obj->paths.emplace_back(obj->contours[i] + center);
        }

        obj->SetUpdated();
    }
}

void BezierGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
    {
        return;
    }
    Painter p(r);
    Draw(r, &p, config);
}

void BezierGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);

    UpdateDrawPath(obj);
    auto& thinkness = config->_display_style.true_line_width;

    {
        if (obj->settings.GetIsSelected())
        {
            Vec2 center(obj->x(), obj->y());
            {
                std::vector<Vec2> tpaths;
                tpaths.emplace_back(obj->start + center);
                for (auto& point : obj->controls)
                {
                    tpaths.emplace_back(point + center);
                }
                tpaths.emplace_back(obj->end + center);

                p->DrawLines(tpaths, c, false, thinkness);
            }
            c = c.GetInv();
        }

        p->DrawLines(obj->paths, c, false, thinkness);
    }
    /*外轮廓*/
    if (obj->settings.GetIsSelected())
    {
        //cv::RotatedRect boundingbox = obj->GetBoundingbox();
        ///*外接矩形*/
        //DrawBoundingbox(p, boundingbox, c.GetInv(), thinkness);

        p->DrawLines(obj->select_bounding_contours, c, true, thinkness);
    }
}

std::string BezierGraphics::GetSerializedData()
{
    return createConfig(this);
}

std::vector<ControlPoint> BezierGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);

    std::vector<ControlPoint> cps;

    {
        /*创建中心拖拽控制点*/
        {
            auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move), 0, obj->select_bounding_contours);
            cps.emplace_back(cp);
        }
        /*创建轮廓拖拽控制点*/
        auto tcps = CreateContourControlPoint(obj, static_cast<int>(CustomCursorType::HandClose), obj->controls);
        cps.insert(cps.end(), tcps.begin(), tcps.end());
    }
    return cps;
}

int BezierGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    if (auto state = RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
        state != GraphicsAbstract::UNKNOWN_TYPE)
    {
        return state;
    }

    switch (ControlPointType(attr.type))
    {
    case ControlPointType::CONTOUR_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        if ((int)obj->controls.size() >= attr.id)
        {
            obj->controls[attr.id].x += xo;
            obj->controls[attr.id].y += yo;
            SetRequireUpdate();
        }
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }
    return OK;
}

void BezierGraphics::ComfirmPoint()
{
    if (controls.size() < 1 || start.isnull() || end.isnull())
    {
        return;
    }

    if (A_FLOAT_EQUAL_DEFAULT(this->x(), 0) && A_FLOAT_EQUAL_DEFAULT(this->y(), 0))
    {
        CreateBoundingBox();
    }
    start.x -= this->x();
    start.y -= this->y();
    end.x -= this->x();
    end.y -= this->y();
    for (size_t i = 0; i < controls.size(); i++)
    {
        controls[i].x -= this->x();
        controls[i].y -= this->y();
    }
    this->SetRequireUpdate();
}

void BezierGraphics::SetStart(float x, float y)
{
    start.x = x;
    start.y = y;
    this->SetRequireUpdate();
    // printInfo((std::stringstream() << "path start:" << x << "," << y).str().c_str());
}

void BezierGraphics::SetEnd(float x, float y, bool iscomfirm)
{
    if (iscomfirmend)
        return;

    end.x = x;
    end.y = y;
    if (iscomfirm)
        iscomfirmend = true;
    this->SetRequireUpdate();
}

void BezierGraphics::SetPoint(std::vector<Vec2>& points, bool is_local)
{
    controls = points;
    if (!is_local)
    {
        ComfirmPoint();
    }
}

void BezierGraphics::AddPoint(float x, float y)
{
    if (!start.isnull() && !end.isnull())
        controls.emplace_back(Vec2(x, y));
    this->SetRequireUpdate();
}

void BezierGraphics::DeletePoint()
{
    if (controls.size() < 1)
        return;

    controls.pop_back();

    this->SetRequireUpdate();
}

void BezierGraphics::CreateBoundingBox()
{
    float maxx, maxy;
    float minx, miny;
    maxx = maxy = -FLT_MAX;
    minx = miny = FLT_MAX;

    for (const auto& p : controls)
    {
        UpdateBoundingLimits(p, maxx, maxy, minx, miny);
    }
    UpdateBoundingLimits(start, maxx, maxy, minx, miny);
    UpdateBoundingLimits(end, maxx, maxy, minx, miny);

    this->SetXY((maxx + minx) * 0.5f, (maxy + miny) * 0.5f);
    this->SetWH(std::abs(maxx - minx), std::abs(maxy - miny));
}

void BezierGraphics::UpdateDrawPath(BezierGraphics* obj)
{
    if (obj->paths.empty() || obj->IsNeedUpdate())
    {
        Vec2 center(obj->x(), obj->y());
        obj->paths.clear();
        int size = (int)obj->controls.size();
        if (size > 0)
        {

            std::vector<Vec2> tpaths{ obj->start };
            tpaths.insert(tpaths.end(), obj->controls.begin(), obj->controls.end());
            tpaths.emplace_back(obj->end);

            obj->paths.emplace_back(obj->start + center);
            // 生成贝塞尔曲线点
            {
                const int seg = 50;
                for (int i = 0; i < seg; ++i)
                {
                    Vec2 v;
                    BezierCurve(v, tpaths, i / (float)seg);
                    obj->paths.emplace_back(v + center);
                }
            }
            obj->paths.emplace_back(obj->end + center);
            // 方便选中的选择区域最小外接矩形
            {
                std::vector<cv::Point2f> contours;
                for (auto& p : obj->paths)
                {
                    contours.emplace_back(p.x, p.y);
                }
                auto rr = cv::minAreaRect(contours);
                std::vector<cv::Point2f> points;
                rr.points(points);
                std::vector<Vec2> select_bounding_contours_temp;
                for (auto& p : points)
                {
                    select_bounding_contours_temp.emplace_back(p.x, p.y);
                }
                obj->select_bounding_contours.swap(select_bounding_contours_temp);
            }
        }
        else if (!obj->start.isnull() && !obj->end.isnull())
        {
            obj->paths.emplace_back(obj->start + center);
            obj->paths.emplace_back(obj->end + center);
        }
        else
        {
            return;
        }
        obj->SetUpdated();
    }
}

