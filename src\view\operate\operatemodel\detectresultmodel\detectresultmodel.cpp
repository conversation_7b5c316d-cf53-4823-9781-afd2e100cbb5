#include "detectresultmodel.h"

void DetectResultModel::SetDataModel(std::vector<DetectResultStruct*> vector_data)
{
    beginResetModel();
    m_vec_data = vector_data;
    endResetModel();
}

void DetectResultModel::GetDataModel(std::vector<DetectResultStruct*>& vector_data)
{
    vector_data = m_vec_data;
}

QStringList DetectResultModel::GetHeaderLabels()
{
    return m_header_list;
}

DetectResultModel::DetectResultModel(QObject* parent)
    : QStandardItemModel(parent)
{
    m_header_list.append(QString::fromWCharArray(L"序号"));
    m_header_list.append(QString::fromWCharArray(L"条码"));
    m_header_list.append(QString::fromWCharArray(L"检测时间"));
    m_header_list.append(QString::fromWCharArray(L"轨道号"));
    m_header_list.append(QString::fromWCharArray(L"结果"));
    m_vec_data.clear();
}

DetectResultModel::~DetectResultModel()
{
}

QVariant DetectResultModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        if (role == Qt::DisplayRole)
        {
            if (section < m_header_list.size())
            {
                return m_header_list.at(section);
            }
        }
    }
    return QVariant();
}

QModelIndex DetectResultModel::index(int row, int column, const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return createIndex(row, column);
}

QModelIndex DetectResultModel::parent(const QModelIndex& child) const
{
    Q_UNUSED(child);
    return QModelIndex();
}

int DetectResultModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return int(m_vec_data.size());
}

int DetectResultModel::columnCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return 5;
}

QVariant DetectResultModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.row() >= m_vec_data.size())
    {
        return QVariant();
    }
    if (role == Qt::TextAlignmentRole)
    {
        return Qt::AlignCenter;
    }
    if (role == Qt::DisplayRole || role == Qt::EditRole)
    {
        if (index.row() < m_vec_data.size())
        {
            if (index.column() == 0)
            {
                return int(index.row() + 1);
            }
            if (index.column() < m_vec_data.at(index.row())->GetStringList().size())
            {
                return m_vec_data.at(index.row())->GetStringList().at(index.column()).c_str();
            }
        }
    }
    return QVariant();
}