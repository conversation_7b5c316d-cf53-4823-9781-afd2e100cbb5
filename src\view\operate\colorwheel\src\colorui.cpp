//#include <minwindef.h>
//#include <processenv.h>
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <fstream>
#include <QHBoxLayout>
#include <QInputDialog>
#include "colorui.h"
#include "imagebinarywidget.h"
#include "imagepreprocesswidget.h"
#include "imagepreprocessalgo.h"
#include "imagebinaryalgo.h"
#include <direct.h>
#include <QtWidgets>
#include "imageprocessalgo.h"
#include "fileoperator.h"

#pragma warning(pop)

namespace fs = std::filesystem;

static std::filesystem::path work_file = cv2d::FileOperator::GetCurrentWorkingDirectory();
static std::filesystem::path config_file = work_file.parent_path().parent_path();
static std::string pre_params_path_ = (config_file / "config" /
	"colorconfig").string();

static std::string file_type = ".json";

static std::vector<std::string> pre_param_names;
ColorUi::ColorUi(QWidget* parent)
	: QWidget{ parent }
{
	this->setMaximumHeight(300);
	pre_process_wheel_ = new ImagePreProcessWindow();
	binary_wheel_ = new ImageBinaryControl();
	image_processor_ = new ImageProcessor();
	binary_processor_ = new BinaryAlgo();;
	pre_params_name_ = new QComboBox();
	pre_params_name_->addItem("默认");
	pre_params_name_->setCurrentIndex(0);
	pre_param_names = cv2d::FileOperator::LoadFileWithType
	(pre_params_path_, file_type);
	for (auto& name : pre_param_names)
	{
		name = cv2d::FileOperator::GetBaseName(name);
		pre_params_name_->addItem(QString::fromStdString(name));

	} 
	add_button = new QPushButton(tr("增加"));
	del_button = new QPushButton(tr("删除"));
	update_button = new QPushButton(tr("更新"));
  
	//pre_params_layout->setContentsMargins(0, 0, 0, 0);
	QHBoxLayout *pre_params_layout = new QHBoxLayout();
	pre_params_layout->setContentsMargins(1, 1, 1, 1);
	pre_params_layout->setSpacing(1);
	pre_params_layout->addWidget(pre_params_name_);
	pre_params_layout->addWidget(add_button);
	pre_params_layout->addWidget(del_button);
	pre_params_layout->addWidget(update_button);

	QVBoxLayout *right_layout = new QVBoxLayout();
	right_layout->setContentsMargins(1, 1, 1, 1);
	right_layout->setSpacing(1);
	right_layout->addWidget(binary_wheel_);
	right_layout->addLayout(pre_params_layout);
	right_layout->addStretch(1);

	QHBoxLayout *main_layout = new QHBoxLayout(this);
	main_layout->setSpacing(1);
	main_layout->setContentsMargins(1, 1, 1, 1);

	pre_process_wheel_->setMinimumWidth(280);
	pre_process_wheel_->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
	binary_wheel_->setMinimumWidth(500);
	binary_wheel_->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

	main_layout->addWidget(pre_process_wheel_);
	main_layout->addLayout(right_layout);

	main_layout->setStretch(0, 1);
	main_layout->setStretch(1, 2);

	setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

	connect(pre_params_name_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ColorUi::SetPreParamsToUsed);
	connect(add_button, &QPushButton::clicked, this, &ColorUi::OnAddButtonClicked);
	connect(del_button, &QPushButton::clicked, this, &ColorUi::OnDelButtonClicked);
	connect(update_button, &QPushButton::clicked, this, &ColorUi::OnUpdateButtonClicked);
	auto pre_process_wheel_lamda = [&](PreProcessParams& pre_params, 
        cv::Mat& result)
    {
		params_.pre_process_params = pre_params;
		auto enhance_image = GetPreProcessResult(m_input_img, params_, result);
		if(color_params_changed_func_!=nullptr)
			color_params_changed_func_(params_, enhance_image, result);
		if(!enhance_image.empty())
			binary_wheel_->SetTestMat(enhance_image,
					params_.binary_params.gray_type_index);
    };
	pre_process_wheel_->SetPreProcessParamsChangedCallback(pre_process_wheel_lamda);
	auto binary_wheel_lamda = [&](BinProcessParams& thresh_params, 
       cv::Mat& result)
    {
		params_.binary_params = thresh_params;
		auto enhance_image = GetPreProcessResult(m_input_img, params_, result);
		if (color_params_changed_func_ != nullptr)
			color_params_changed_func_(params_, enhance_image, result);
    };
	binary_wheel_->SetThresholdValChangedCallback(binary_wheel_lamda);
}

int ColorUi::SetTestData(const cv::Mat& image_groups,
	const std::string& color_params_str, bool is_origin_image)
{	
	m_input_img = image_groups;
	if (binary_wheel_)
	{
		// 恢复色盘
		binary_wheel_->RestoreColorWheel();
	}
	pre_process_wheel_->SetProcessImage(m_input_img);
	image_processor_->SetProcessImage(m_input_img);

	if (!color_params_str.empty())
	{
		ColorParams color_params = ColorParams::FromJson(color_params_str);
		pre_process_wheel_->SetPreProcessParmas(color_params.pre_process_params,is_origin_image);
		binary_wheel_->SetTestParams(color_params.binary_params,is_origin_image);
		// 当有预处理参数时，更新图片预处理结果 2025.01.16 by liuchenfan
		cv::Mat process_image;
		ImageProcessor image_processor;
		image_processor.SetProcessImage(m_input_img);
		image_processor.SetProcessParams(color_params.pre_process_params);
		image_processor.GetProcessImage(process_image);
		if (process_image.type() == CV_32FC3)
		{
			JRSMessageBox_ERR("operateview","模板显示控件暂不支持32位浮点型图像，请将图像通道切换成非高度图通道！ ",jrscore::MessageButton::Ok);
			return -1;
		}
		binary_wheel_->SetTestMat(process_image,
			params_.binary_params.gray_type_index);
	}
	else
	{
		ColorParams params;
		pre_process_wheel_->SetPreProcessParmas(params.pre_process_params,is_origin_image);
		binary_wheel_->SetTestParams(params.binary_params,is_origin_image);
		params_ = params;
		binary_wheel_->SetTestMat(m_input_img,
			params_.binary_params.gray_type_index);
	}
	return 0;
}

QWidget* ColorUi::GetWindowHandle()
{
	return this;
}

int ColorUi::ShowWindow()
{
	this->show();
	return 0;
}

int ColorUi::SetColorChangedCallback(ColorParamsChangedFunc func)
{
	color_params_changed_func_ = func;
	return 0;
}

cv::Mat ColorUi::GetPreProcessResult(const cv::Mat& /*image_group*/,
	ColorParams& params, cv::Mat& output_img)
{
	if (m_input_img.empty()) return cv::Mat();
	
	return colorwheel::GetPreProcessResult(m_input_img, params, output_img);
}

int ColorUi::GetColorProcessImage(const cv::Mat& input_image, 
	cv::Mat& out_image, ColorParams& params)
{
	if (pre_process_wheel_ == nullptr || binary_wheel_ == nullptr) return -1;
	if (binary_processor_ == nullptr) return -1;

	pre_process_wheel_->GetPreProcessParam(params.pre_process_params);
	binary_wheel_->GetBinaryControlParams(params.binary_params);
	if (input_image.empty()) return -1;
	ImageLabel image_group;
	image_group.mat = input_image;
	image_processor_->SetProcessImage(m_input_img);
	image_processor_->SetProcessParams(params.pre_process_params);
    image_processor_->GetProcessImage(out_image);
	binary_processor_->GetBinaryImage(out_image, params.binary_params, out_image);
	return 0;
}

int ColorUi::GetColorProcessImageWithParam(const cv::Mat& input_image, 
	ColorParams& params, cv::Mat& out_image)
{
	cv::Mat enhance_image = colorwheel::GetPreProcessResult(input_image, params, out_image);
	// 保证有结果输出，临时处理方式  后续需要将颜色算法整合成一个 2025.01.16 by liuchenfan
	if (out_image.empty())
		out_image = enhance_image.clone();
	return 0;
}

const ColorParams ColorUi::GetColorProcessParam()
{
	ColorParams params;
	pre_process_wheel_->GetPreProcessParam(params.pre_process_params);
	binary_wheel_->GetBinaryControlParams(params.binary_params);
	return params;
}


void ColorUi::OnAddButtonClicked()
{
	bool ok;
    QString params_name = QInputDialog::getText(this, tr("预设颜色参数"),    
                                    tr("输入当前需要保存预设颜色名称:"), QLineEdit::Normal,"", &ok);
	bool add_new = true;
	for (const auto& name : pre_param_names)
	{
		if (name == params_name.toStdString())
		{
			add_new = false;
			break;
		}
	}
	if(add_new)
	{
		pre_params_name_->addItem(params_name);
		std::string json_path = pre_params_path_ + "\\" + params_name.toStdString() + ".json";
		params_.pre_process_params = PreProcessParams();
		auto color_json = params_.ToJson();
		if (!std::filesystem::exists(pre_params_path_)) 
		{
			std::filesystem::create_directories(pre_params_path_);
			
		}
		//cv2d::FileOperator::SaveStringToFile(color_json, json_path);
		std::ofstream file(json_path);

		if (file.is_open())
		{
			file << color_json;
			file.close(); 
		}
	}
	pre_param_names = cv2d::FileOperator::LoadFileWithType(pre_params_path_, file_type);
	pre_params_name_->setCurrentIndex(int(pre_param_names.size()));
}
void ColorUi::OnDelButtonClicked()
{
	auto current_map_name = pre_params_name_->currentText().toStdString();
	pre_params_name_->removeItem(pre_params_name_->currentIndex());
	cv2d::FileOperator::RemoveFile(pre_params_path_ + current_map_name + ".json");
}

void ColorUi::OnUpdateButtonClicked()
{
	auto current_map_name = pre_params_name_->currentText().toStdString();
	std::string json_path = pre_params_path_ + current_map_name + ".json";
	cv2d::FileOperator::RemoveFile(json_path);

	auto color_json = params_.ToJson();
	cv2d::FileOperator::SaveStringToFile(color_json, json_path);
	//map_params_.pre_params_name[current_map_id] = current_map_name;
	//map_params_.pre_params[current_map_id] = params_;
}

void ColorUi::SetPreParamsToUsed()
{
	if (pre_params_name_->currentIndex() == 0) return;
	auto current_map_name = pre_params_name_->currentText().toStdString();
	std::string json_path = pre_params_path_ + "\\" + current_map_name + ".json";
	if (!std::filesystem::exists(pre_params_path_)) return;
	std::ifstream file(json_path);
	if (!file.is_open())
	{
        return;
    }
	std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
	//cv2d::FileOperator::LoadStringFromFile(json_path, pre_param_str);
	params_ = ColorParams::FromJson(content);
	pre_process_wheel_->SetPreProcessParmas(params_.pre_process_params, true);
	binary_wheel_-> SetTestParams(params_.binary_params, true);
	cv::Mat binary_image;
	cv::Mat enhance_image;
	color_params_changed_func_(params_, enhance_image, binary_image);
}







