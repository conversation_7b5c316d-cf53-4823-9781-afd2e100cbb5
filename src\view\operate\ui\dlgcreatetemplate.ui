<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DlgCreateTemplate</class>
 <widget class="QDialog" name="DlgCreateTemplate">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>314</width>
    <height>326</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_2" native="true">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>40</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>矫正角度：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QDoubleSpinBox" name="doubleSpinBox_angle"/>
      </item>
      <item>
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string> °</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_create">
        <property name="text">
         <string>创建</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_apply">
        <property name="text">
         <string>应用</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
