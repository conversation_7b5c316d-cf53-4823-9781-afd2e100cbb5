/*****************************************************************//**
 * @file   topic.h
 * @brief  主题类
 * @details    
 * <AUTHOR>
 * @date 2024.1.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.22         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __TOPIC_H__
#define __TOPIC_H__
//STD
#include <iostream>
#include <mutex>
//Custom
#include "callbackhelper.hpp"
namespace jrscore
{
    class Topic
    {
        public:
            explicit Topic (const std::string& topic_name_param);  
            ~Topic ();
            /**
             * @fun AddCallback 
             * @brief 添加订阅回调
             * @param cb_ 回调函数
             * @return  返回状态码,TODO：改为错误码形式
             * @date 2024.1.22
             * <AUTHOR>
             */
            int AddCallback(SubscribeCallbackHelperPtr& cb_);
            /**
             * @fun RemoveCallback 
             * @brief 删除订阅回调
             * @param cb_ 删除的回调
             * @return 状态码
             * @date 2024.1.22
             * <AUTHOR>
             */
            int RemoveCallback (SubscribeCallbackHelperPtr& cb_);
            /**
             * @fun NotifyOne 
             * @brief 通知指定订阅者
             * @param sub_name 订阅者名称
             * @return 状态码
             * @date 2024.1.22
             * <AUTHOR>
             */
            int NotifyOne(const std::string& sub_name, const std::vector<std::any>& args);
     
            /**
             * @fun NotifyAll 
             * @brief 通知所有订阅者
             * @return 返回错误码
             * @date 2024.1.22
             * <AUTHOR>
             */
            int NotifyAll(const std::vector<std::any>& args);
        
        private:
            std::string topic_name;
            std::recursive_mutex call_back_mutex;
            std::vector<SubscribeCallbackHelperPtr> topic_callback_v;


    };
    using TopicPtr = std::shared_ptr<Topic>;
}
#endif // !__TOPIC_H__
