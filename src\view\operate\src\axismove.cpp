#include "axismove.h"

AxisMove::AxisMove(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::AxisMove)
{
    ui->setupUi(this);
    InitView();
    InitConnect();
    axis_move_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
}
AxisMove::~AxisMove()
{
    delete ui;
}
void AxisMove::UpdateView(const jrsdata::OperateViewParamPtr ptr)
{
    // 更新轴位置
    if (ptr->device_param.motion_param.motion_status.pos.size() > 3)
    {
        ui->xpos->setText(ptr->device_param.motion_param.motion_status.pos.at(0).c_str());
        ui->ypos->setText(ptr->device_param.motion_param.motion_status.pos.at(1).c_str());
        ui->zpos->setText(ptr->device_param.motion_param.motion_status.pos.at(2).c_str());

        jrsdata::CurrentAxisPos temp_pos;
        temp_pos.current_axis_xpos = ui->xpos->text().toFloat();
        temp_pos.current_axis_ypos = ui->ypos->text().toFloat();
        temp_pos.current_axis_zpos = ui->zpos->text().toFloat();
        emit SigCurrentAxisPos(temp_pos);
    }
    // 更新限位
    if (ptr->device_param.motion_param.motion_status.axis_limit.size() > 3)
    {
        auto xlimit = ptr->device_param.motion_param.motion_status.axis_limit.at(0);
        auto ylimit = ptr->device_param.motion_param.motion_status.axis_limit.at(1);
        auto zlimit = ptr->device_param.motion_param.motion_status.axis_limit.at(2);
        std::replace(xlimit.begin(), xlimit.end(), '#', ',');
        std::replace(ylimit.begin(), ylimit.end(), '#', ',');
        std::replace(zlimit.begin(), zlimit.end(), '#', ',');
        ui->xlimit->setText(QString::fromStdString("[" + xlimit + "]"));
        ui->ylimit->setText(QString::fromStdString("[" + ylimit + "]"));
        ui->zlimit->setText(QString::fromStdString("[" + zlimit + "]"));
    }
}
void AxisMove::SlotUpdateAxisPos(const QString& text_)
{

    (void)text_;
    jrsdata::CurrentAxisPos temp_pos;
    temp_pos.current_axis_xpos = ui->xpos->text().toFloat();
    temp_pos.current_axis_ypos = ui->ypos->text().toFloat();
    temp_pos.current_axis_zpos = ui->zpos->text().toFloat();
    emit SigCurrentAxisPos(temp_pos);
}
void AxisMove::SlotPushButtonTrigger()
{
    axis_move_view_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
    emit SigMotionDebugTrigger(axis_move_view_ptr);
}
void AxisMove::InitView()
{
    // X-Y-Axis
    ui->left->setIcon(QIcon(":/image/leftmove.png"));
    ui->right->setIcon(QIcon(":/image/rightmove.png"));
    ui->top->setIcon(QIcon(":/image/upmove.png"));
    ui->bottom->setIcon(QIcon(":/image/downmove.png"));
    ui->center->setIcon(QIcon(":/image/unlock.png"));
    // 急停、清除报警、回零
    ui->stopBtn->setIcon(QIcon(":/image/stop2.png"));
    ui->stopBtn->setToolTip("急停 ");
    ui->clearBtn->setIcon(QIcon(":/image/clear.png"));
    ui->clearBtn->setToolTip("报警清除 ");
    ui->homeBtn->setIcon(QIcon(":/image/initial.png"));
    ui->homeBtn->setToolTip("初始化 ");

    ui->step_2->setVisible(false);
    move_lock = false;
    
}
void AxisMove::InitConnect()
{
    // 模式切换(完整和精简)
    connect(ui->modeSelect, &QPushButton::clicked, this, [=]() {
        if (ui->modeSelect->text() == "完整")
        {
            ui->modeSelect->setText("精简");
            ui->rightFrame->setVisible(false);
            ui->step_2->setVisible(true);
            QDialog* parent = qobject_cast<QDialog*>(this->parent());
            parent->setFixedSize(210, 210);
        }
        else
        {
            ui->modeSelect->setText("完整");
            ui->rightFrame->setVisible(true);
            ui->step_2->setVisible(false);
            QDialog* parent = qobject_cast<QDialog*>(this->parent());
            parent->setFixedSize(460, 210);
        }
        });

    // 轴切换
    connect(ui->axisSelect, &QPushButton::clicked, this, [=]() {
        if (ui->axisSelect->text() == "X-Y")
        {
            ui->axisSelect->setText("Z");
            ui->left->setVisible(false);
            ui->right->setVisible(false);
        }
        else
        {
            ui->axisSelect->setText("X-Y");
            ui->left->setVisible(true);
            ui->right->setVisible(true);
        }
        });

    // 移动锁
    connect(ui->center, &QPushButton::clicked, this, [=]() {
        if (!move_lock)
        {
            ui->center->setIcon(QIcon(":/image/lock.png"));
            ui->left->setEnabled(false);
            ui->right->setEnabled(false);
            ui->top->setEnabled(false);
            ui->bottom->setEnabled(false);
        }
        else
        {
            ui->center->setIcon(QIcon(":/image/unlock.png"));
            ui->left->setEnabled(true);
            ui->right->setEnabled(true);
            ui->top->setEnabled(true);
            ui->bottom->setEnabled(true);
        }
        move_lock = !move_lock;
        });
    
    // 急停
    connect(ui->stopBtn, &QPushButton::clicked, this, [=]() {
        axis_move_view_ptr->device_param.event_name = "Stop";
        SlotPushButtonTrigger();
        });
    // 报警清除
    connect(ui->clearBtn, &QPushButton::clicked, this, [=]() {
        axis_move_view_ptr->device_param.event_name = "ClearAlarm";
        SlotPushButtonTrigger();
        });
    // 回零
    connect(ui->homeBtn, &QPushButton::clicked, this, [=]() {
        axis_move_view_ptr->device_param.event_name = "Initial";
        SlotPushButtonTrigger();
        });
    // 单轴相对移动
    QObject::connect(ui->left, &QPushButton::clicked, this, [=]() {
        axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(1, -1);
        SlotPushButtonTrigger();
        });
    QObject::connect(ui->right, &QPushButton::clicked, this, [=]() {
        axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(1, 1);
        SlotPushButtonTrigger();
        });
    QObject::connect(ui->top, &QPushButton::clicked, this, [=]() {
        if (ui->axisSelect->text() == "X-Y")
        {
            axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(2, -1);
        }
        else
        {
            axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(3, 1);
        }
        SlotPushButtonTrigger();
        });
    QObject::connect(ui->bottom, &QPushButton::clicked, this, [=]() {
        if (ui->axisSelect->text() == "X-Y")
        {
            axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(2, 1);
        }
        else
        {
            axis_move_view_ptr->device_param.motion_param.motion_move = CreateMovParam(3, -1);
        }
        SlotPushButtonTrigger();
        });
    // 步进
    QObject::connect(ui->step20, &QRadioButton::clicked, this, [=]() {
        ui->step->setText("20");
        });
    QObject::connect(ui->step10, &QRadioButton::clicked, this, [=]() {
        ui->step->setText("10");
        });
    QObject::connect(ui->step5, &QRadioButton::clicked, this, [=]() {
        ui->step->setText("5");
        });
    QObject::connect(ui->step2, &QRadioButton::clicked, this, [=]() {
        ui->step->setText("2");
        });
}
jrsdata::MoveParam AxisMove::CreateMovParam(int axis, int direction)
{
    jrsdata::MoveParam param;
    param.axis_index = axis;
    if (ui->modeSelect->text() == "完整")
    {
        param.axis_diatance = ui->step->text().toDouble();
    }
    else
    {
        param.axis_diatance = ui->step_2->text().toDouble();
    }
    if (axis == 1)
    {
        param.x_direction = direction;
    }
    else if (axis == 2)
    {
        param.y_direction = direction;
    }
    else if (axis == 3)
    {
        param.z_direction = direction;
    }
    param.axis_speed = ui->xySpeed->text().toDouble();
    if (axis == 3)
    {
        param.axis_speed = ui->zSpeed->text().toDouble();
    }
    param.axis_movetype = jrsdata::MoveType::Mov;
    axis_move_view_ptr->device_param.event_name = "SingleMove";
    return param;
}
jrsdata::MoveParam AxisMove::CreateLopToParam(int xdirection, int ydirection)
{
    jrsdata::MoveParam param;
    //double distance = ui->movestep->value();
    //param.axis_xpos = ui->xpos->text().toDouble() + distance * xdirection;
    //param.axis_ypos = ui->ypos->text().toDouble() + distance * ydirection;
    //param.axis_speed = ui->speed->value();
    param.axis_movetype = jrsdata::MoveType::LopTo;
    axis_move_view_ptr->device_param.event_name = "LopTo";
    xdirection = 0;
    ydirection = 0;
    return param;
}
