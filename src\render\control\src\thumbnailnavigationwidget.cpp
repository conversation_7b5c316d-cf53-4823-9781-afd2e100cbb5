﻿#include "thumbnailnavigationwidget.h"
#include "thumbnailnavigation.h"
// #include "log.h"
#include <QPainter>
#include <QMouseEvent>

ThumbnailNavigationWidget::ThumbnailNavigationWidget(
    int scene_w /*= 200*/,
    int scene_h /*= 200 */,
    QWidget* parent /*= nullptr*/)

    : QWidget(parent), ThumbnailNavigation(scene_w, scene_h)
    , color_background(0, 0, 0, 0x12)
    , color_valid_region(38, 64, 139, 0x30)
    , color_select_rect(255, 0, 0)
{
    setWindowTitle(QString::fromLocal8Bit("缩略图导航"));
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    setAttribute(Qt::WA_TranslucentBackground); // 半透明背景
    resize(scene_w, scene_h);
}

void ThumbnailNavigationWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    SetWindowSize(event->size().width(), event->size().height());
}

void ThumbnailNavigationWidget::paintEvent(QPaintEvent*)
{
    QPainter painter(this);
    /*绘制本体*/
    QRectF rect = this->rect();
    rect.setWidth(rect.width() - 1); // qt的bug,需要-1否则无法正常绘制
    rect.setHeight(rect.height() - 1);
    painter.fillRect(rect, color_background); /* 绘制背景 */
    painter.drawRect(rect); /* 绘制边框 */

    /*绘制缩略图*/
    float min_x, min_y, max_x, max_y;
    GetRenderRegionLimit(min_x, min_y, max_x, max_y);

    auto draw_rect = QRectF(QPointF(min_x, min_y), QPointF(max_x, max_y));
    if (!m_qimage.isNull())
    {
        painter.drawImage(draw_rect, m_qimage);// 绘制图片
    }
    else
    {
        painter.fillRect(draw_rect, color_valid_region);
    }

    /*绘制视野框*/
    float x, y, w, h;
    GetThumbnailViewport(x, y, w, h);
    //printInfo(std::stringstream() << "view: x: " << x << ",y " << y << ",w " << w << ",h " << h);

    QRectF ROIRect(x, y, w, h);
    painter.setPen(QPen(color_select_rect, 3));
    // painter.setPen(QPen(Qt::white, 3));
    painter.setCompositionMode(QPainter::RasterOp_SourceAndNotDestination);
    painter.drawRect(ROIRect);
}

void ThumbnailNavigationWidget::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton)
    {
        MoveViewportToMouseCenter(event->pos());
    }
    // else if (event->buttons() & Qt::RightButton)
    // {
    //     auto pos = mapToParent(mapFromGlobal(event->globalPos()));
    //     this->move(pos);
    // }
}

void ThumbnailNavigationWidget::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        MoveViewportToMouseCenter(event->pos());
    }
}

void ThumbnailNavigationWidget::enterEvent(QEvent*)
{
    emit SignalMouseEnter();
}

void ThumbnailNavigationWidget::leaveEvent(QEvent*)
{
    emit SignalMouseLeave();
}

void ThumbnailNavigationWidget::MoveViewportToMouseCenter(const QPoint& mousepos)
{
    SetThumbnailViewport((float)mousepos.x(), (float)mousepos.y());

    // if (x != position.x() || y != position.y())
    //     QCursor::setPos(mapToGlobal(QPoint((int)x, (int)y)));

    float x, y, w, h;
    GetViewport(x, y, w, h);
    emit SignalViewportCenterChange(x, y);

    update();
}
