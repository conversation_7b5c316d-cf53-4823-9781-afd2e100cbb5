﻿#include "subboardtable.h"
#include "mysqlimp.hpp"
#include "idatabase.h"
//std
#include <memory>

jrsdatabase::SubboardTable::SubboardTable(const std::string& table_name_)
    :TableBase(table_name_)
{
}

jrsdatabase::SubboardTable::~SubboardTable()
{
}

int jrsdatabase::SubboardTable::Create(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    auto res = conn_ptr_->CreateTable<jrstable::TSubboard>();
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]创建", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }

    res = InitFields(conn_ptr_);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]初始化", _table_name, " 表字段失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }

    res = conn_ptr_->AlterTablePrimaryKey<jrstable::TSubboard>();
    if (res != jrscore::AOI_OK)
    {
        Log_INFO("[", __FUNCTION__, "]创建", _table_name, " 主键失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }

    /*创建 index*/
    InitIndex(conn_ptr_);

    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Drop(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    auto temp_ptr = std::static_pointer_cast<DB_Mysql>(conn_ptr_);

    std::string sql_temp = "TRUNCATE " + _table_name;

    auto res = conn_ptr_->ExecuteSQL(sql_temp);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]删除 ", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Show(jrstable::TableParamBasePtr& db_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    (void)db_;
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    return 0;
}

int jrsdatabase::SubboardTable::Insert(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TSubboard>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->AddData<jrstable::TSubboard>(*temp_ptr);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Insert(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TSubboard>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    auto res = conn_ptr_->AddData<jrstable::TSubboard>(boards);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Update(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TSubboard>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->UpdateData(boards, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更新数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Update(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TSubboard>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->UpdateData<jrstable::TSubboard>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Replace(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto datas = BasePtrVecToObjectVector<jrstable::TSubboard>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->Replace(datas, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更换数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Replace(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TSubboard>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->Replace<jrstable::TSubboard>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::InitIndex([[maybe_unused]] const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    //subboard_detect_time
    auto index_res = conn_ptr_->AddIndex(_table_name, MySQLIndexType::INDEX, "subboard_barcode_index", { "subboard_barcode" });
    if (index_res != jrscore::AOI_OK && index_res != jrscore::DataManagerError::E_AOI_DB_DUPLICATE_KEY_NAME)
    {
        Log_WARN("创建索引失败: ", conn_ptr_->GetLastError());
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::InitFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }
    auto res = InitDataFields(conn_ptr_);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]初始化", _table_name, " 表字段失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }
    return InitVarcharFields(conn_ptr_);
}
int jrsdatabase::SubboardTable::InitDataFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    jrsdatabase::MySQLAlterTableFieldType type;
    type.field_type_name = jrsdatabase::MySQLDataType::DATETIME;
    const std::vector<std::string> fields_to_alter = {
        "subboard_rejudgment_time"
    };
    for (const auto& field : fields_to_alter) {
        auto res = conn_ptr_->AlterTableFieldType(this->_table_name, field, type);
        if (res) return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::SubboardTable::InitVarcharFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    jrsdatabase::MySQLAlterTableFieldType type;
    const std::map<std::string, int> fields_to_alter = {
        {"subboard_barcode", 600}
    };
    for (const auto& field_length : fields_to_alter) {
        type.field_type_name = jrsdatabase::MySQLDataType::VARCHAR;
        type.length = field_length.second;
        auto res = conn_ptr_->AlterTableFieldType(this->_table_name, field_length.first, type);
        if (res) return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::SubboardTable::Select(const jrsselect::SelectorParamBasePtr& selector_ptr_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }

    if (selector_ptr_->select_name == jrsselect::T_SUBBOARD_SELECT_BY_WHERE_CONDITION)
    {
        auto select_ptr = std::dynamic_pointer_cast<jrsselect::SelectTable>(selector_ptr_);
        jrsdatabase::jrstable::TSubboardVector subboards;
        auto res = conn_ptr_->QueryTable<jrsdatabase::jrstable::TSubboard>(subboards, selector_ptr_->where_condition);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("查询", select_ptr->select_name, "数据失败: ", conn_ptr_->GetLastError());
            return res;
        }
        select_ptr->subboards = subboards;
    }
    else if (selector_ptr_->select_name == jrsselect::T_SELECT_SUBBARCODES_OF_ENTIRETY_BOARD_BY_SUBBOARD_BARCODE)
    {
        //先查询主板的
        auto sql_str = BuildSubboardQueryByTBoard(selector_ptr_->where_condition);
        std::vector<std::tuple<int, int, std::string>> query_data;
        auto query_res = conn_ptr_->QueryTable<std::tuple<int, int, std::string>>(query_data, sql_str);
        if (query_res != jrscore::AOI_OK || query_data.size() == 0)
        {
            //再查询子板
            sql_str = BuildSubboardQueryByTSubboard(selector_ptr_->where_condition);
            query_res = conn_ptr_->QueryTable<std::tuple<int, int, std::string>>(query_data, sql_str);
            if (query_res != jrscore::AOI_OK)
            {
                Log_WARN("查询子板数据失败: ", conn_ptr_->GetLastError());
            }
        }

        auto select_ptr = std::dynamic_pointer_cast<jrsselect::SelectCustom>(selector_ptr_);
        if (!select_ptr)
        {
            Log_WARN("SelectCustom 类型转换失败");
            return -1;
        }

        select_ptr->select_subboard_barcodes = std::make_optional<jrsselect::SelectCustom::SelectCustomSubboardBarcode>();
        for (const auto& data : query_data)
        {
            int board_id = std::get<0>(data);
            int subboard_id = std::get<1>(data);
            const std::string& barcode = std::get<2>(data);
            select_ptr->select_subboard_barcodes->board_id = board_id;
            select_ptr->select_subboard_barcodes->subboard_id_and_barcode[subboard_id] = barcode;
        }
    }
    return jrscore::AOI_OK;
}

std::string jrsdatabase::SubboardTable::BuildSubboardQueryByTSubboard(const std::string& subboard_barcode_)
{
    std::stringstream ss;
    ss << "WITH SubboardStats AS (\n"
        << "    SELECT MAX(board_id) AS max_board_id\n"
        << "    FROM\n"
        << "        t_subboard\n"
        << "    WHERE\n"
        << "        subboard_barcode = '" << subboard_barcode_ << "'\n"
        << ")\n\n"
        << "SELECT\n"
        << "    sb.board_id,\n"
        << "    sb.subboard_id,\n"
        << "    sb.subboard_barcode\n"
        << "FROM\n"
        << "    t_subboard sb\n"
        << "INNER JOIN\n"
        << "    SubboardStats bs ON sb.board_id = bs.max_board_id;";

    return ss.str();
}

std::string jrsdatabase::SubboardTable::BuildSubboardQueryByTBoard(const std::string& barcode_)
{
    std::stringstream ss;
    ss << "WITH MaxBoard AS (\n"
        << "    SELECT MAX(board_id) AS max_board_id\n"
        << "    FROM\n"
        << "        t_board\n"
        << "    WHERE\n"
        << "        board_barcode = '" << barcode_ << "'\n"
        << ")\n\n"
        << "SELECT\n"
        << "    sb.board_id,\n"
        << "    sb.subboard_id,\n"
        << "    sb.subboard_barcode\n"
        << "FROM\n"
        << "    t_subboard sb\n"
        << "INNER JOIN\n"
        << "    MaxBoard bs ON sb.board_id = bs.max_board_id;";

    return ss.str();
}
