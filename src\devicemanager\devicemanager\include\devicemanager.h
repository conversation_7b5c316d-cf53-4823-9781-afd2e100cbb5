/*****************************************************************//**
 * @file   devicemanager.h
 * @brief  设备管理模块
 * @details 设备管理模块目前包含如下：结构光成像设备（包含：光源，dlp，闪频板），运动控制
 * <AUTHOR>
 * @date 2024.7.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.31         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSDEVICEMANAGER_H__
#define __JRSDEVICEMANAGER_H__

 //STD
#include <iostream>
#include <sstream>
#include <iomanip>

 //Custom
#include "pluginexport.hpp"
#include "deviceparam.hpp"
#include "coreapplication.h"
#include "viewparam.hpp"
// Third
#include "nlohmann/json.hpp"

using JSON = nlohmann::json;


namespace jrsdevice
{
    class Motion;
    class StructLight;
    class BarcodeDevice;
    using InitCallBack = std::function<int(const jrsdata::DeviceParamPtr& device_param)>; /**< 主要用于初始化时读取的设备参数更新到外部使用*/
    using MotionConfigCallBack = std::function<int(const jrsdata::OperateViewParamPtr& param_)>;
    using InitDeviceCallBack = std::function<int(const jrsdata::SystemStateViewParamPtr& param_)>;
    class JRS_AOI_PLUGIN_API DeviceManager
    {
    public:
        DeviceManager();
        ~DeviceManager();
        std::shared_ptr<StructLight> GetStructLightInstance();

        /**
         * @fun GetBarcodeInstance 
         * @brief 获取扫码枪设备
         * @return  扫码枪设备指针
         * <AUTHOR>
         * @date 2025.6.9
         */
        std::shared_ptr<BarcodeDevice> GetBarcodeInstance();

        /**
         * @fun EventHandler
         * @brief 处理与设备相关的界面操作
         * @param device_param_ 设备参数
         * <AUTHOR>
         * @date 2024.8.20
         */

        void EventHandler(const jrsdata::ViewParamBasePtr& device_param_);

        void InitSystem(const jrsdata::ViewParamBasePtr& param_ptr_);


        void SetInitCallBack(InitCallBack cb_);

        void SetSystemStateCallBack(jrsdata::InvokeSystemStateParamFun callback_);

        void ReleaseStructLight();
    private:
        /**
         * @fun ChangeSystemState
         * @brief
         * @param check_name_
         * @param state_
         * @param code_and_info_
         * @param event_name_
         * <AUTHOR>
         * @date 2024.11.11
         */
        void ChangeSystemState(const std::string& check_name_, const jrsdata::MachineCheckParamInfo::CheckState& state_,
            const std::tuple<int, std::string, std::string>& code_and_info_ = std::make_tuple(jrscore::AOI_OK, "", ""),
            const std::string& event_name_ = jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT);


        // 字符串分割
        std::vector<std::string> SplitString(const std::string& s, char delimiter);

        // 根据name获取对象
        JSON GetObjFromName(JSON Obj, std::string name);

    private:
        //属性 
        std::shared_ptr<StructLight> struct_light_ptr; /**< 结构光实例*/
        std::shared_ptr<BarcodeDevice> barcode_device_ptr; /**< 条码设备实例*/
        bool process_running;                                                  /**< 流程运行中 */
        //bool exit_;                                                            /**< 线程运行标志 */
        jrsdata::DeviceParamPtr device_param;                                 /**< 设备参数*/
        InitCallBack init_callback;                                            /**< 主要用于更新初始化时获取的设备参数，更新到外部使用 zhangyuyu 2024.9.3*/
        jrsdata::InvokeSystemStateParamFun sys_state_callback;                /**< 设备回调*/
        jrsdata::SystemStateViewParamPtr sys_state_param;			          /**< 初始化系统参数 */
        std::mutex _mtxsys_state_param;
    };
    using DeviceManagerPtr = std::shared_ptr<DeviceManager>;
}

#endif // !__JRSDEVICEMANAGER_H__
