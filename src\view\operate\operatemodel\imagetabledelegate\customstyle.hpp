/*****************************************************************//**
 * @file   customstyle.hpp
 * @brief  TableView的Item背景颜色
 * 
 * <AUTHOR>
 * @date   2024.9.25
 *********************************************************************/
#ifndef CUSTOMSTYLE_H
#define CUSTOMSTYLE_H
//QT
#include <QProxyStyle>
#include <QPainter>
#include <QStyle>
#include <QStyleOptionViewItem>

class CustomStyle : public QProxyStyle {
public:
    using QProxyStyle::QProxyStyle;
    /**
     * @fun drawControl 
     * @brief 画单元格颜色
     * @param element
     * @param option
     * @param painter
     * @param widget
     * @date 2024.9.24
     * <AUTHOR>
     */
    void drawControl(ControlElement element, const QStyleOption* option, QPainter* painter, const QWidget* widget) const override {
        if (element == CE_ItemViewItem) {
            if (const QStyleOptionViewItem* viewItemOption = qstyleoption_cast<const QStyleOptionViewItem*>(option)) {
                if (viewItemOption->state & QStyle::State_Selected) {
                    painter->fillRect(viewItemOption->rect, Qt::blue); // 修改为你想要的颜色
                    painter->setPen(Qt::white);
                    painter->drawText(viewItemOption->rect, Qt::AlignCenter, viewItemOption->text);
                    return;
                }
            }
        }
        QProxyStyle::drawControl(element, option, painter, widget);
    }
};
#endif