/*****************************************************************//**
 * @file   viewbase.h
 * @brief  界面基类
 * @details
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __VIEWBASE_H__
#define __VIEWBASE_H__
// prebuild
#include "pch.h"
//STD
#include <iostream>

//QT
#pragma warning(push, 1)
//#include <QWidget>
#pragma warning(pop)

//Custom
//#include "viewparam.hpp"
#include "viewdefine.h"
#include "coreapplication.h"

//单例数据操作
// #include "projectoperator.h" // 放在这里影响编译速度

namespace jrsaoi
{
    class ViewBase :public QWidget
    {
    public:
        virtual ~ViewBase() = default;

        /**
         * @fun Init
         * @brief 初始化view
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual int Init() = 0;

        /**
         * @fun UpdateView
         * @brief 更新view
         * @date 2024.1.18
         * <AUTHOR>
         */
        virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_) = 0;

        /**
         * @fun Save
         * @brief 界面数据保存
         * @param param_
         * @return  true:保存成功 false:保存失败
         * @date 2024.5.30
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) = 0;

        /**
         * @fun GetCustomWidget  
         * @brief 为了将view中自定义的控件提取出来
         * @return 自定义控件
         * <AUTHOR>
         * @date 2025.2.19
         */
        virtual QWidget* GetCustomWidget() { return nullptr; }

    protected:
        explicit ViewBase(const std::string& name, QWidget* parent = nullptr);
    private:
        std::string view_name;
    };
}

#endif // !__VIEWBASE_H__
