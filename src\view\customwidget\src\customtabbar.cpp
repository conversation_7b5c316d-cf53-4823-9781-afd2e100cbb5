#include "customtabbar.h"
#include <QPainter>
#include <QStylePainter>
#include <QStyleOptionTab>

CustomTabBar::CustomTabBar(QWidget *parent) : QTabBar(parent)
{
}

void CustomTabBar::SetTabBackgroundColor(const QString tab_name, const QColor &color)
{
    int index = GetTabIndex(tab_name);
    if(index != -1)
    {
        SetTabBackgroundColor(index, color);
    }
}

void CustomTabBar::SetTabBackgroundColor(int index, const QColor &color)
{
    if(index < 0)
    {
        return;
    }
    if (tab_colors.contains(index)) 
    {
        tab_colors[index] = color;  // 更新现有值
    } 
    else 
    {
        tab_colors.insert(index, color);  // 插入新值
    }
    update();
}

void CustomTabBar::ClearTabBackground(int index)
{
    tab_colors.remove(index);
    update();
}

void CustomTabBar::ClearColors()
{
    tab_colors.clear();
}

int CustomTabBar::GetTabIndex(const QString &tab_name)
{
    for (int i = 0; i < count(); ++i) 
    {
        if (tabText(i) == tab_name) 
        {
            return i;
        }
    }
    return -1;
}

QRect CustomTabBar::TabRect(int index) const
{
    QRect rect = QTabBar::tabRect(index);
    int padding = 0;  // 间隔大小
    // 调整 Tab 的位置和大小
    rect.setLeft(rect.left() + index * padding);  // 增大间隔
    rect.setRight(rect.right() + (index - 1) * padding);        // 增大间隔
    return rect;
}

void CustomTabBar::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event);
    QPainter painter(this);

    for (int i = 0; i < count(); ++i) {
        QRect rect = TabRect(i);
        bool isSelected = (currentIndex() == i);

        // 1. 绘制背景（优先使用自定义颜色）
        QColor bgColor = tab_colors.value(i, QColor("#FFFFFF"));  // 默认白色
        if (bgColor == QColor("#FFFFFF")) {
            painter.fillRect(rect, isSelected ? "#1296DB" : bgColor);
        }
        else {
            painter.fillRect(rect, isSelected ? bgColor.lighter(50) : bgColor);
        }

        // 2. 绘制边框（可选）
        painter.setPen(Qt::gray);
        painter.drawRect(rect.adjusted(0, 0, -1, -1));  // 避免重叠

        // 3. 绘制文本
        painter.setPen(Qt::black);
        painter.drawText(rect, Qt::AlignCenter, tabText(i));
    }
}

