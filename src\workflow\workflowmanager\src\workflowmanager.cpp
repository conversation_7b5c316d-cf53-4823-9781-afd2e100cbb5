
//PREBUILD
#include "workflowpch.h"
//STD
#include <memory>
//Custom
#include "devicemanager.h"
#include "workflowmanager.h"
#include "projectparam.hpp"
#include "image.hpp"
//#include "workflowinterfaces.h"
#include "flowcontroller.h"
namespace jrsworkflow 
{
    struct WorkFlowDataImpl
    {
        //IExecuteFlowPtr execute_flow_ptr; /**< 流程执行指针*/
        IFlowControllerPtr flow_controller_ptr; /**< 流程执行控制指针*/
        jrsdevice::DeviceManagerPtr device_ptr; /**< 设备管理类指针*/
        std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_manager_ptr;/**< 算法引擎类指针 */
        std::shared_ptr<jrsdata::DataManager> data_manager_ptr; /**< 数据管理类指针 */
        WorkFlowParam work_flow_param;
        WorkFlowDataImpl ()
        {

        }
    };

    WorkFlowManager::WorkFlowManager ( const std::shared_ptr<jrsdevice::DeviceManager>& device_ptr_ 
                                       ,const  std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_ptr_
                                       ,const std::shared_ptr<jrsdata::DataManager>data_manager_ptr_)
        :impl_data(new WorkFlowDataImpl())
    {
        impl_data->device_ptr = device_ptr_;
        impl_data->algo_engine_manager_ptr = algo_engine_manager_ptr_;
        impl_data->data_manager_ptr = data_manager_ptr_;
        //InitMember ();
    }
    WorkFlowManager::~WorkFlowManager()
    {
        if (impl_data)
        {
            delete impl_data;
            impl_data = nullptr;
        }
    }
    int WorkFlowManager::WorkFlowEventHandler(const jrsdata::ViewParamBasePtr& param_)
    {

        if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::ControlPanelViewParam>(param_);
            if (param_temp->current_project_param == nullptr)
            {
                LogAutoRun_INFO("运行工程时，传入的工程名称数据为空，请检查");
                return jrscore::CommonError::E_AOI_POINTER_EMPTY;
            }
            impl_data->flow_controller_ptr->Start(param_temp->current_project_param);
            impl_data->work_flow_param.setting_param_ptr = param_temp->setting_param_ptr;
            impl_data->work_flow_param.project_param_map = param_temp->multi_project_param_map;
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);
        }
        else if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME)
        {
            impl_data->flow_controller_ptr->Stop();
        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            impl_data->work_flow_param.is_enable_online_debug = true;
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);

        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            impl_data->work_flow_param.is_enable_online_debug = false;
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);

        }
        else if (param_->event_name == jrsaoi::ONLINEDEBUG_DEBUG_FINISHED_SEND_EVNET_NAME)
        {
            impl_data->work_flow_param.finished_online_debug = true;
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);
        }
        else if (param_->event_name == jrsaoi::ONLINEDEBUG_DEBUG_IS_STOP_WORKFLOW_EVNET_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
            if (!param_temp)
            {
                LogAutoRun_INFO("在线调试是否停机调试切换时，传入的参数转换失败，指针为空！");
                return jrscore::CommonError::E_AOI_POINTER_EMPTY;
            }
            impl_data->work_flow_param.is_stop_debug = param_temp->is_stop_debug.load();
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);
        }
        else if (param_->event_name == jrsaoi::OPERATE_SEND_MOTION_PARAM_TO_WORKFLOW_EVENT_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);

            impl_data->work_flow_param.motion_set_param = param_temp->config_setting_param.motion_setting;
            impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);
        }
        return jrscore::AOI_OK;
    }
    void WorkFlowManager::SetStructLightParam(const jrsdata::StructLightParam& struct_light_param_)
    {
        impl_data->work_flow_param.camera_fov_w = struct_light_param_.camera_fov_w;
        impl_data->work_flow_param.camera_fov_h = struct_light_param_.camera_fov_h;
        impl_data->work_flow_param.resolution_x = struct_light_param_.resolution_x;
        impl_data->work_flow_param.resolution_y = struct_light_param_.resolution_y;
        impl_data->flow_controller_ptr->SetWorkFlowParam(impl_data->work_flow_param);

    }
    void WorkFlowManager::InitMember()
    {
    
    }  

    int WorkFlowManager::StartExecuteFlow(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
    {
        (void)project_param_;
        return 0;
    }

    void WorkFlowManager::BufferInvoke(const jrsdata::JrsImageBuffer& imgs)
    {
        ( void )imgs;
        impl_data->flow_controller_ptr->AddBuffer(imgs);
    }



    void WorkFlowManager::SetLogicInvokeFun(std::function<void(const std::vector<PCBPathPlanning::Fov>& ,bool)> logic_invoke_)
    {
        impl_data->flow_controller_ptr = std::make_shared<FlowController>(impl_data->device_ptr, impl_data->algo_engine_manager_ptr, impl_data->data_manager_ptr,logic_invoke_);
    }

    void WorkFlowManager::SetControlPanelCallBack(jrsdata::InvokeControlPanelViewParamFun callback_)
    {
        if (callback_)
        {
            impl_data->flow_controller_ptr->SetControlPanelCallback(callback_);
        }
    }


}
