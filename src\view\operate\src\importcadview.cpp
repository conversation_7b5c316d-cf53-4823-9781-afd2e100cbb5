﻿// prebuild
#include "pch.h"
#include "ImportCadView.h"
//#include "viewparam.hpp"
//Custom
#include "addcadview.h"
#include "componentvaluedialog.h"
//QT
#pragma warning(push,3)
#include "ui_ImportCadView.h"
#pragma warning(pop)
#include <QComboBox>

using namespace jrsdata;

namespace jrsaoi
{
    ImportCadView::ImportCadView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::ImportCadView)
        , add_cad_view(nullptr)
    {
        ui->setupUi(this);
        Init();
    }

    ImportCadView::~ImportCadView()
    {
        Disconnect();
        if (add_cad_view)
        {
            delete add_cad_view;
            add_cad_view = nullptr;
        }
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
    }

    void ImportCadView::Init()
    {
        qRegisterMetaType<jrsdata::CadEventParam>("jrsdata::CadEventParam");
        InitView();
        InitMemeber();
        InitConnect();
    }

    void ImportCadView::InitMemeber()
    {
        add_cad_view = new AddCadView();
        //for (auto& [key, value] : jrsdata::MultiBoardEventParamMultiSelectTypeMap)
        //{
        //    ui->combo_select_sub_mode->insertItem(static_cast<int>(key), value.c_str(), static_cast<int>(key));
        //}
        ////ui->combo_select_sub_mode->addItems(QStringList() << "矩形选择" << "多边形选择" << "点击选择");
        //ui->combo_select_sub_mode->setCurrentIndex(-1);

        //for (auto& [key, value] : jrsdata::MultiBoardEventParamSubBoardTransformTypeMap)
        //{
        //    ui->combo_transform_sub->insertItem(static_cast<int>(key), value.c_str(), static_cast<int>(key));
        //}
        ////ui->combo_transform_sub->addItems(QStringList() << "阵列" << "旋转");
        //ui->combo_transform_sub->setCurrentIndex(-1);
    }

    void ImportCadView::InitConnect()
    {
        connect(add_cad_view, &AddCadView::GetCADCompleted, this, &ImportCadView::SigImportCadCompleted);
        connect(ui->btn_add_cad, &QPushButton::clicked, this, &ImportCadView::SlotAddCad);
        //connect(ui->btn_ai_add_cad, &QPushButton::clicked, this, &ImportCadView::SlotAIAddCad);
        //connect(ui->btn_manual_add_cad, &QPushButton::clicked, this, &ImportCadView::SlotManualAddCad);
        //connect(ui->btn_delete_cad, &QPushButton::clicked, this, &ImportCadView::SlotDeleteCad);
        connect(ui->btn_select_component_true, &QPushButton::clicked, this, &ImportCadView::SlotSelectComponentTrue);
        connect(ui->btn_clear_select_component, &QPushButton::clicked, this, &ImportCadView::SlotSelectComponentClear);
        connect(ui->btn_confirm_component_true, &QPushButton::clicked, this, &ImportCadView::SlotConfirmComponentTrue);
        // TODO 增加矩形子板和多边形子板设置
        connect(ui->pushButton_rect_sub_board, &QPushButton::clicked, this, &ImportCadView::SlotRectSubBoard);
        connect(ui->pushButton_polyon_sub_board, &QPushButton::clicked, this, &ImportCadView::SlotPolyonSubBoard);

        connect(ui->position_table, &QTableWidget::cellClicked, this, &ImportCadView::SlotSelectComponentPositioned);
    }

    void ImportCadView::InitView()
    {
        QStringList header;
        header << tr("定位元件");
        ui->position_table->setHorizontalHeaderLabels(header);
        ui->position_table->verticalHeader()->setVisible(false);
        ui->position_table->horizontalHeader()->setVisible(false);
        ui->position_table->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

        ui->position_table->setColumnCount(1);
        ui->position_table->setRowCount(0);
    }

    void ImportCadView::SelectSubName(const QString& str)
    {
        Q_UNUSED(str);
        //ui->line_current_sub_name->setText(str);
    }

    void ImportCadView::SlotAddCad()
    {
        add_cad_view->show();
    }

    void ImportCadView::SlotAIAddCad(int current_index)
    {
        Q_UNUSED(current_index);
        emit SignalCadEditStep(static_cast<int>(jrsdata::CadEventParam::Step::AI_ADD_CAD));
    }

    //void ImportCadView::SlotManualAddCad()
    //{
    //    auto dialog = new ComponentDialog(this);
    //    if (dialog->exec() == QDialog::Accepted)
    //    {
    //        jrsdata::CadEventParam param;
    //        param.component_name = dialog->GetComponentName().toLocal8Bit().toStdString();
    //        param.part_name = dialog->GetComponentPart().toLocal8Bit().toStdString();
    //        param.step = jrsdata::CadEventParam::Step::MANUAL_ADD_CAD;
    //        emit SignalCadEditParam(param);
    //    }
    //}

    //void ImportCadView::SlotDeleteCad()
    //{
    //    emit SignalCadEditStep(static_cast<int>(jrsdata::CadEventParam::Step::DELETE_CAD));
    //}
    void ImportCadView::SlotSelectComponentTrue()
    {
        // TODO 临时增加测试 框选位置之前同步发送元件选择的信号 by yaoying_zhang 2024-12-03 
        emit SignalCadEditStep(static_cast<int>(jrsdata::CadEventParam::Step::SELECT_CUR_POSITION_FOR_ALIGNMENT));
    }

    void ImportCadView::SlotConfirmComponentTrue()
    {
        emit SignalCadEditStep(static_cast<int>(jrsdata::CadEventParam::Step::CONFIRM_CUR_POSITION_FOR_ALIGNMENT));
    }

    void ImportCadView::SlotSelectComponentPositioned(int row, int column)
    {
        auto item = ui->position_table->item(row, column);
        if (!item)
        {
            return;
        }
        jrsdata::CadEventParam cad_event_param;
        cad_event_param.step = jrsdata::CadEventParam::Step::SELECT_COMPONENT_CAD_POSITIONED;
        cad_event_param.component_name = item->text().toStdString();

        emit SignalCadEditParam(cad_event_param);
    }

    void ImportCadView::SlotRectSubBoard()
    {
        emit SignalSelectSubMode(static_cast<int>(jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_RECT));
    }

    void ImportCadView::SlotPolyonSubBoard()
    {
        emit SignalSelectSubMode(static_cast<int>(jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_POLYGON));
    }

    void ImportCadView::SlotSelectComponentClear()
    {
        ui->position_table->clear();
        ui->position_table->setRowCount(0);

        emit SignalCadEditStep(static_cast<int>(jrsdata::CadEventParam::Step::CLEAR_POSITION_FOR_ALIGNMENT));
    }

    void ImportCadView::SlotSelectSubBoard()
    {
        //TODO 修改CAD的选择操作 by yaoying_zhang 2024-12-03
        emit SignalSelectSubMode(static_cast<int>(jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_CLICK));

    }
    void jrsaoi::ImportCadView::SlotAddPositionComponent(const QString& component_name_)
    {
        bool found = false;
        for (int row = 0; row < ui->position_table->rowCount(); ++row) {
            if (ui->position_table->item(row, 0)->text() == component_name_)
            {
                found = true;
                ui->position_table->insertRow(0);
                ui->position_table->setItem(0, 0, new QTableWidgetItem(component_name_));
                ui->position_table->removeRow(row + 1);
                break;
            }
        }
        if (!found) {
            ui->position_table->insertRow(0);
            ui->position_table->setItem(0, 0, new QTableWidgetItem(component_name_));
        }

    }

    void ImportCadView::SlotClearPositionComponents()
    {
        SlotSelectComponentClear();
    }

    void ImportCadView::Disconnect()
    {
        disconnect(add_cad_view, &AddCadView::GetCADCompleted, this, &ImportCadView::SigImportCadCompleted);
        disconnect(ui->btn_add_cad, &QPushButton::clicked, this, &ImportCadView::SlotAddCad);
        //TODO 删除界面上CAD ai操作的信号槽 by yaoying_zhang 2024-12-03
        //disconnect(ui->btn_ai_add_cad, SIGNAL(currentIndexChanged(int)), this, SLOT(SlotAIAddCad(int)));
        //disconnect(ui->btn_manual_add_cad, &QPushButton::clicked, this, &ImportCadView::SlotManualAddCad);
        //disconnect(ui->btn_delete_cad, &QPushButton::clicked, this, &ImportCadView::SlotDeleteCad);
        disconnect(ui->btn_select_component_true, &QPushButton::clicked, this, &ImportCadView::SlotSelectComponentTrue);
        disconnect(ui->btn_clear_select_component, &QPushButton::clicked, this, &ImportCadView::SlotSelectComponentClear);
        disconnect(ui->btn_confirm_component_true, &QPushButton::clicked, this, &ImportCadView::SlotConfirmComponentTrue);
        disconnect(ui->position_table, &QTableWidget::cellClicked, this, &ImportCadView::SlotSelectComponentPositioned);
    }
}
