#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <QComboBox>
#include <QCheckBox>
#include <QtWidgets/qpushbutton.h>
#include <QtWidgets/qboxlayout.h>
#include "imagebinarywidget.h"
#include "rgbcolorwheel.h"
#include "heightwheel.h"
#include "graywheel.h"
#include "imagebinaryalgo.h"
#include "colorwheelctrl.h"
#include <fstream>
#include <iomanip>
#pragma warning(pop)

ImageBinaryControl::ImageBinaryControl(QWidget *parent)
    : QWidget(parent)
{
	SetupUi();	
	SetupConnect();
}
void  ImageBinaryControl::OnTabChanged(int index)
{
	//binary_type_index_ = index;
	params_.binary_type_index = index;
	if (index == 1)
	{
		vecf gray_hist;
		Mat gray;
		binary_algo->GetGrayHist(image_, gray_hist, gray_type_index_, gray);
		emit UpdateGrayHist(gray_hist);
		emit UpdateGrayImage(gray);
	}
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}

int ImageBinaryControl::SetTestMat(const cv::Mat& image,int mode)
{
	if (image.empty()/* || image.channels() != 3*/) return -1;

	image_ = image;
	{
		vecf r_hist, g_hist, b_hist;
		binary_algo->GetRgbHist(image_, r_hist, g_hist, b_hist);
		emit UpdateRgbHist(r_hist, g_hist, b_hist);
	}
	{
		vecf gray_hist;
		cv::Mat processed_gray;
		binary_algo->GetGrayHist(image_, gray_hist, mode, processed_gray);
		emit UpdateGrayHist(gray_hist);
	}
	{
		hsv_color_wheel->SetTestImage(image_);
	}
	SetHeightMat(image_);
	return 0;
}
// 保存vector<float>到txt文件
bool SaveVectorToTxt(const std::vector<float>& data, const std::string& filename) {
	std::ofstream outFile(filename);
	if (!outFile.is_open()) {
		std::cerr << "Error: Could not open file " << filename << " for writing\n";
		return false;
	}

	// 设置输出精度
	outFile << std::fixed << std::setprecision(6);

	// 首先写入数据大小
	outFile << data.size() << "\n";

	// 写入数据，每行一个数
	for (const float& value : data) {
		outFile << value << "\n";
	}

	outFile.close();
	return true;
}
int ImageBinaryControl::SetHeightMat(const cv::Mat& height_mat)
{
	if (height_mat.empty() || height_mat.channels() != 1) return -1;
	height_mat_ = height_mat;

	vecf height_hist;
	double min_value, max_value;
	cv::minMaxLoc(height_mat_, &min_value, &max_value, nullptr, nullptr);
	params_.min_hei = float(min_value);
	params_.max_hei = float(max_value);
	binary_algo->GetHeightHist(height_mat_, height_hist, 
		height_bin_nums_, params_.min_hei, params_.max_hei);
	SaveVectorToTxt(height_hist, "height_hist.txt");
	/*auto cur_min_bin_index = FindSpecialIndex(height_hist);

	auto bin_width = (max_value - min_value) / height_bin_nums_;

	auto bin_min_value = min_value + cur_min_bin_index * bin_width;
	params_.min_hei = float(bin_min_value);*/
	height_wheel_->SetHeightHistParams(height_bin_nums_,
		params_.min_hei, params_.max_hei);
	height_wheel_->SetHeightThreSlot(params_.height_thre_min, params_.height_thre_max);
	emit UpdateHeightHist(height_hist);
	return 0;
}
int ImageBinaryControl::SetTestParams(BinProcessParams& params,bool is_origin_image)
{
	m_is_origin_image = is_origin_image;
	params_ = params;
    gray_type_index_ = params.gray_type_index;
    height_bin_nums_ = params.height_bin_nums;
	gray_wheel->UpataGrayTypeIndexSlot(gray_type_index_);
	algo_type_tab_->setCurrentIndex(params_.binary_type_index);
	rgb_color_wheel_->SetRThreSlot(params_.r_thre_min, params_.r_thre_max);
	rgb_color_wheel_->SetGThreSlot(params_.g_thre_min, params_.g_thre_max);
	rgb_color_wheel_->SetBThreSlot(params_.b_thre_min, params_.b_thre_max);
	gray_wheel->SetGrayThreSlot(params_.gray_thre_min, params_.gray_thre_max);
	hsv_color_wheel->SetCurrentValState(params.hsv_paramas);

	m_is_origin_image = true;
	return 0;
}
QWidget* ImageBinaryControl::GetWindowHandle()
{
	return this;
}
void ImageBinaryControl::SetGrayTypeIndex(int index)
{
    gray_type_index_ = index;
	vecf gray_hist;
	Mat gray;
	binary_algo->GetGrayHist(image_, gray_hist, gray_type_index_, gray);
	emit UpdateGrayHist(gray_hist);
	emit UpdateGrayImage(gray);

	params_.gray_type_index = index;
	if (threshold_params_changed_func_ && m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}
//void ImageBinaryControl::SetHeightHistParamsSlot(int bin, 
//	float min_hei, float max_hei)
//{
//	height_bin_nums_ = bin;
//
//	vecf height_hist;
//	binary_algo->GetHeightHist(height_mat_, height_hist, 
//		height_bin_nums_, min_hei, max_hei);
//	emit UpdateHeightHist(height_hist);
//
//	params_.height_bin_nums = bin;
//	params_.min_hei = min_hei;
//	params_.max_hei = max_hei;
//	if (threshold_params_changed_func_&& m_is_origin_image)
//    {
//        threshold_params_changed_func_(params_, result_);
//    }
//}
void ImageBinaryControl::UpdateRThreSlots(int min, int max)
{
	params_.r_thre_min = min;
	params_.r_thre_max = max;
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}
void ImageBinaryControl::UpdateGThreSlots(int min, int max)
{
	params_.g_thre_min = min;
	params_.g_thre_max = max;
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}
void ImageBinaryControl::UpdateBThreSlots(int min, int max)
{
	params_.b_thre_min = min;
	params_.b_thre_max = max;
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}
void ImageBinaryControl::UpdateGrayThreSlots(int min, int max)
{
	params_.gray_thre_min = min;
	params_.gray_thre_max = max;
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}
//void ImageBinaryControl::UpdateHeightThreSlots(int min, int max)
//{
//	params_.height_thre_min = min;
//	params_.height_thre_max = max;
//	if (threshold_params_changed_func_&& m_is_origin_image)
//    {
//        threshold_params_changed_func_(params_, result_);
//    }
//}

void ImageBinaryControl::SetHeightHistParamsSlot(int bin, float min_hei, float max_hei, int min_thre, int max_thre,bool is_thre_hei)
{
	params_.height_thre_min = min_thre;
	params_.height_thre_max = max_thre;
	height_bin_nums_ = bin;

	if (!is_thre_hei)
	{
		vecf height_hist;
		binary_algo->GetHeightHist(height_mat_, height_hist,
			height_bin_nums_, min_hei, max_hei);
		emit UpdateHeightHist(height_hist);

		params_.height_bin_nums = bin;
		params_.min_hei = min_hei;
		params_.max_hei = max_hei;
	}
	
	if (threshold_params_changed_func_ && m_is_origin_image)
	{
		threshold_params_changed_func_(params_, result_);
	}
}

void ImageBinaryControl::UpdateHsvParamsSlots(ColorWheelThreshVal params)
{
	params_.hsv_paramas = params;
	if (threshold_params_changed_func_&& m_is_origin_image)
    {
        threshold_params_changed_func_(params_, result_);
    }
}

int ImageBinaryControl::FindSpecialIndex(const std::vector<float>& values)
{
	if (values.size() < 2) {
		return -1;
	}

	// 初始化最大值索引
	int max_index_first = 0;
	int max_index_second = 1;

	// 确保max_index_first指向较大的值
	if (values[max_index_second] > values[max_index_first]) {
		std::swap(max_index_first, max_index_second);
	}

	// 查找最大的两个值的索引
	for (int i = 2; i < values.size(); ++i) {
		if (values[i] > values[max_index_first]) {
			max_index_second = max_index_first;
			max_index_first = i;
		}
		else if (values[i] > values[max_index_second]) {
			max_index_second = i;
		}
	}

	// 获取较小的索引值
	int current_index = std::min(max_index_first, max_index_second);

	// 每次减10直到找到值小于10.0f的位置
	while (current_index >= 0) {
		if (values[current_index] < 5.0f) {
			return current_index;
		}
		current_index -= 10;
	}
	return -1;
}

int ImageBinaryControl::ShowWindow()
{
	this->show();
	return 0;
}
int ImageBinaryControl::SetThresholdValChangedCallback(
	ThresholdParamsChangedFunc func)
{
	threshold_params_changed_func_ = func;
	return 0;
}
int ImageBinaryControl::GetBinaryControlParams(BinProcessParams& params)
{
	params = params_;
	return 0;
}
void ImageBinaryControl::RestoreColorWheel()
{
	if(hsv_color_wheel)
	{
		hsv_color_wheel->RestoreColorWheel();
	}
}
//int ImageBinaryControl::Threshold(const cv::Mat& input_img, 
//	 BinProcessParams& params, cv::Mat& output_img)
//{
//	binary_algo->GetBinaryImage(image_, params, output_img);
//	return 0;
//}
ImageBinaryControl::~ImageBinaryControl()
{

}

void ImageBinaryControl::SetupUi()
{
    // 创建主布局
    main_layout = new QVBoxLayout(this);
    main_layout->setSpacing(15);
    main_layout->setContentsMargins(1, 1, 1, 1);

    // 创建算法实例
    binary_algo = new BinaryAlgo();

    // 1. 创建二值化方式选择面板
    QGroupBox* method_group = new QGroupBox(tr("图像取色"));
	method_group->setContentsMargins(0, 0, 0, 0);
    algo_type_tab_ = new QTabWidget();
    
    // 创建各个处理模块
    rgb_color_wheel_ = new RgbColorWheel();
    hsv_color_wheel = new ColorWheelCtrl();
    height_wheel_ = new HeightWheel();
    gray_wheel = new GrayWheel();

    // 设置统一的最小尺寸
    QSize min_size(300, 200);
    rgb_color_wheel_->setMinimumSize(min_size);
    hsv_color_wheel->setMinimumSize(min_size);
    height_wheel_->setMinimumSize(min_size);
    gray_wheel->setMinimumSize(min_size);

    // 添加标签页
    algo_type_tab_->addTab(rgb_color_wheel_, tr("RGB阈值"));
    algo_type_tab_->addTab(gray_wheel, tr("灰度阈值"));
    algo_type_tab_->addTab(hsv_color_wheel, tr("HSV空间"));
    algo_type_tab_->addTab(height_wheel_, tr("高度阈值"));

    // 启用所有标签页
    for(int i = 0; i < algo_type_tab_->count(); i++) {
        algo_type_tab_->setTabEnabled(i, true);
    }

    // 设置标签页布局
    QVBoxLayout* method_layout = new QVBoxLayout(method_group);
    method_layout->addWidget(algo_type_tab_);
    method_layout->setContentsMargins(8, 20, 8, 8);

    // 添加到主布局
    main_layout->addWidget(method_group);
    main_layout->addStretch();

    // 设置整体窗口属性
    setMinimumWidth(400);
}


void ImageBinaryControl::SetupConnect()
{
	connect(this, &ImageBinaryControl::UpdateRgbHist,
		rgb_color_wheel_, &RgbColorWheel::SetRgbHistValue);
	connect(this, &ImageBinaryControl::UpdateGrayHist,
		gray_wheel, &GrayWheel::SetGrayHistValue);
	connect(this, &ImageBinaryControl::UpdateHeightHist,
		height_wheel_, &HeightWheel::SetHeightHistValue);
	connect(algo_type_tab_, &QTabWidget::currentChanged,
		this, &ImageBinaryControl::OnTabChanged);
	connect(gray_wheel, &GrayWheel::UpataGrayTypeIndex,
		this, &ImageBinaryControl::SetGrayTypeIndex);
	connect(height_wheel_, &HeightWheel::UpdataHeiHistParmas, this,
		&ImageBinaryControl::SetHeightHistParamsSlot);
	connect(rgb_color_wheel_, &RgbColorWheel::SetRThre, this,
		&ImageBinaryControl::UpdateRThreSlots);
	connect(rgb_color_wheel_, &RgbColorWheel::SetGThre, this,
		&ImageBinaryControl::UpdateGThreSlots);
	connect(rgb_color_wheel_, &RgbColorWheel::SetBThre, this,
		&ImageBinaryControl::UpdateBThreSlots);
	connect(hsv_color_wheel, &ColorWheelCtrl::UpdateHsvParams, this,
		&ImageBinaryControl::UpdateHsvParamsSlots);
	connect(gray_wheel, &GrayWheel::SetGrayThre, this,
		&ImageBinaryControl::UpdateGrayThreSlots);
	/*connect(height_wheel_, &HeightWheel::UpdataHeiHistParmas, this,
		&ImageBinaryControl::SetHeightHistParamsSlot);*/

}
