﻿/**********************************************************************
 * @file   viewdefine.h
 * @brief  模块名称定义，事件名称定义，用于在不同模块中使用
 * @details
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __VIEWDEFINE_H__
#define __VIEWDEFINE_H__

namespace jrsaoi
{
    /***********************UI模块名称******************************/

#pragma region 模块名称
  // init sys界面 MVC InitSystem
    constexpr char SYSTEM_STATE_MODULE_NAME[] = "system_state_module";
    // 主操作界面  MVC InitOperate
    constexpr char OPERATE_MODULE_NAME[] = "operate_module";
    // 在线调试操作界面  MVC InitOnLineDebug
    constexpr char ONLINEDEBUG_MODULE_NAME[] = "onlinedebug_module";
    // 日志显示区域  MVC InitLogShow
    constexpr char LOGSHOW_MODULE_NAME[] = "logshow_module";

    // 右侧显示元件/料号等信息界面  MVC InitShowList
    constexpr char SHOWLIST_MODULE_NAME[] = "showlist_module";
    //MVC  InitControlPanel
    constexpr char CONTROL_PANEL_MODULE_NAME[] = "control_panel_module";
    // 设备模块
    constexpr char DEVICE_MODULE_NAME[] = "device_module";

    // 数据处理模块 MVC InitSetting
    constexpr char DATA_MODULE_NAME[] = "data_module";

    // 逻辑模块
    constexpr char LOGIC_MODULE_NAME[] = "logic_module";

    // 2D渲染界面 MVC InitRender2D
    constexpr char RENDER2D_MODULE_NAME[] = "render2d_module";

    // 配置文件模块
    constexpr char CONFIG_MODULE_NAME[] = "config_module";

    // 工程模块
    constexpr char PROJECT_MODULE_NAME[] = "project_module";

    //工程流程模块
    constexpr char WORKFLOW_MODULE_NAME[] = "workflow_module";

    ////设置模块
    //constexpr char DATA_MODULE_NAME[] = "setting_module";

#pragma endregion

 /***********************处理模块名称******************************/

#pragma region 处理模块名称
    constexpr char VIEW_MODULE_NAME[] = "view_module_name"; /**< 界面处理模块 */
#pragma endregion 处理模块名称

#pragma region 操作界面事件处理名称
    //! 主题
    constexpr char OPERATE_TRIGGER_TOPIC_NAME[] = "operate_topic_name"; /**< 操作界面按钮点击主题名称*/
    constexpr char RENDER_TRIGGER_TOPIC_NAME[] = "RENDER_TRIGGER_TOPIC_NAME"; /**< 渲染界面主题名称*/
    constexpr char RENDER_UPDATE_PROJECT_TOPIC_NAME[] = "RENDER_UPDATE_PROJECT_TOPIC_NAME"; /**< 渲染界面主题名称*/
    constexpr char OPERATE_EXECUTE_COMPONENT_TOPIC_NAME[] = "operate_execute_component_topic_name"; /**< 操作界面执行元件主题名称*/
    //! 订阅者
    constexpr char OPERATE_UPDATE_SUB_NAME[] = "operate_upadate_sub_name";/**< operate 界面更新 订阅 */

    constexpr char SHORTCUT_LOGIC_MOTION_SUB_NAME[] = "shortcut_logic_motion_sub_name"; /**< 快捷界面按钮逻辑层运控订阅者*/
    constexpr char OPERATE_UPDATE_MOTION_SUB_NAME[] = "operate_update_motion_sub_name"; /**< 操作界面运控订阅者*/
    constexpr char OPERATE_AXIS_MOVE_SUB_NAME[] = "operate_axis_move_sub_name"; /**< 操作界面轴移动订阅者*/

    //! TODO:delete  因为logic将会有统一的事件处理机制
    constexpr char OPERATE_LOGIC_MOTION_SUB_NAME[] = "operate_motion_sub_name"; /**< 操作界面按钮运控订阅者*/

    constexpr char OPERATE_LOGIC_SUB_NAME[] = "operate_logic_sub_name"; /**< 操作界面按钮逻辑层订阅者*/

    constexpr char OPERATE_RENDER_SUB_NAME[] = "operate_render_sub_name"; /**< 操作界面按钮render界面订阅者*/

    constexpr char OPERATE_PROJECT_SUB_NAME[] = "OPERATE_PROJECT_SUB_NAME"; /**< 操作界面->工程*/

    constexpr char OPERATE_EXECUTE_COMPONENT_SHOWLIST_SUB_NAME[] = "operate_execute_component_showlist_sub_name"; /**< 列表显示界面订阅操作界面执行元件主题*/
    constexpr char RENDER_SUB_NAME[] = "RENDER_SUB_NAME"; /**<渲染界面订阅者*/
    constexpr char OPERATE_EXECUTE_COMPONENT_ONLINE_DEBUG_SUB_NAME[] = "operate_execute_component_online_debug_sub_name"; /**< 在线调试订阅操作界面执行元件主题*/
    constexpr char OPERATE_EXECUTE_COMPONENT_ALL_SUB_NAME[] = "all"; /**< 全部订阅操作界面执行元件主题*/
    //! 事件
    //! 新建工程部分
    constexpr char PROJECT_CREATE_EVENT_NAME[] = "PROJECT_CREATE_EVENT_NAME";                   /**< 工程创建事件 */
    constexpr char PROJECT_SAVE_EVENT_NAME[] = "PROJECT_SAVE_EVENT_NAME";                       /**< 工程保存事件 */
    constexpr char PROJECT_SAVE_AS_EVENT_NAME[] = "PROJECT_SAVE_AS_EVENT_NAME";                 /**< 工程另存为事件 */
    constexpr char PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME[] = "PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME";           /**< 工程加载图片事件 */
    constexpr char PROJECT_READ_EVENT_NAME[] = "PROJECT_READ_EVENT_NAME";                       /**< 工程读取事件 */
    constexpr char APPEND_PROJECT_EVENT_NAME[] = "append_project_event_name";                       /**< 追加工程事件 */
    constexpr char OPERATE_CONFIRM_LINK_PROJECT_EVENT_NAME[] = "operate_confirm_link_project_event_name";         /**< 确认工程关联事件工程事件 */
    constexpr char OPERATE_CANCLE_LINK_PROJECT_EVENT_NAME[] = "operate_cancle_link_project_event_name";         /**< 取消工程关联事件工程事件 */
    constexpr char OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME[] = "operate_get_loaded_project_info_event_name"; /**< 获取已经加载的工程信息，目前包括已经加载的工程列表和当前选中工程的关联工程名称件 */
    constexpr char PROJECT_UPDATE_EVENT_NAME[] = "PROJECT_UPDATE_EVENT_NAME";                   /**< 工程更新事件 */
    constexpr char RENDER2D_UNPDATE_VIEW_EVENT_NAME[] = "RENDER2D_UNPDATE_VIEW_EVENT_NAME";     /**< 渲染界面更新工程事件 */
    constexpr char CONFIRM_BOARD_POS_EVENT_NAME[] = "comfirm_board_pos_event_name";             /**< 确认板子位置 */
    constexpr char ENTIRETY_IMAGE_READ[] = "entirety_image_read";                               /**< 读取大图 */
    constexpr char ENTIRETY_IMAGE_SAVE[] = "entirety_image_save";                               /**< 保存大图 */
    constexpr char UPDATE_COORDINATE_EVENT_NAME[] = "update_coordinate_event_name";             /**< 更新坐标系参数*/

    //! 导入CAD部分
    constexpr char IMPORT_CAD_EVENT_NAME[] = "import_cad_event_name";                           /**< 导入CAD事件名称 */
    constexpr char COMPONENT_EDIT_EVENT_NAME[] = "COMPONENT_EDIT_EVENT_NAME";                   /**< 元件编辑事件名称 */
    constexpr char SUBBOARD_TRANSFORM_EVENT_NAME[] = "SUBBOARD_TRANSFORM_EVENT_NAME";           /**< 子板变换事件名称 */
    constexpr char ALL_CAD_ROTATE_90_EVENT_NAME[] = "ALL_CAD_ROTATE_90_EVENT_NAME";                                   /**< 所有元件旋转90 */
    constexpr char WINDOW_CHANGE_MODEL_EVENT_NAME[] = "WINDOW_CHANGE_MODEL_EVENT_NAME";         /**< 检测框编辑界面切换模组 */
    constexpr char CREATE_PAD_EVENT_NAME[] = "CREATE_PAD_EVENT_NAME";                           /**< 创建pad */
    constexpr char CREATE_SUBBADMARK_EVENT_NAME[] = "CREATE_SUBBADMARK_EVENT_NAME";             /**< 创建子板坏板标记 */
    constexpr char EDIT_SUBBOARD_REGION_RECT[] = "EDIT_SUBBOARD_REGION_RECT";                   /**< 编辑子板区域尺寸(矩形) */
    constexpr char MULTI_BOARD_OPERATE_EVENT_NAME[] = "MULTIPLE_BOARD_OPERATE_EVENT_NAME";      /**< 多联板操作事件 */
    constexpr char BOARD_SORT_EVENT_NAME[] = "BOARD_SORT_EVENT_NAME";            /**< 板子排序事件 */

    constexpr char SUBBOARD_SELECT_SUB_EVENT_NAME[] = "SUBBOARD_SELECT_SUB_EVENT_NAME";         /**<子板选择 */

    constexpr char MARK_STEP_EVENT_NAME[] = "MARK_STEP_EVENT_NAME";                          /**< MARK步骤 */
    constexpr char REQUEST_RENDER2D_CREATE_MARK_EVENT_NAME[] = "REQUEST_RENDER2D_CREATE_MARK_EVENT_NAME";   /**< 请求渲染界面创建MARK*/
    constexpr char REQUEST_RENDER2D_CREATE_REGION_EVENT_NAME[] = "REQUEST_RENDER2D_CREATE_REGION_EVENT_NAME";   /**< 请求渲染界面创建检测框*/
    constexpr char REQUEST_RENDER2D_CREATE_SUB_REGION_EVENT_NAME[] = "REQUEST_RENDER2D_CREATE_SUB_REGION_EVENT_NAME";   /**< 请求渲染界面创建子检测框*/
    constexpr char REQUEST_RENDER2D_DRAW_DETECT_RESULT_EVENT_NANE[] = "REQUEST_RENDER2D_DRAW_DETECT_RESULT_EVENT_NANE";   /**< 请求渲染界面绘制检测结果*/
    constexpr char REQUEST_REGION_IMAGE_EVENT_NAME[] = "REQUEST_REGION_IMAGE_EVENT_NAME";         /**< 请求区域图片*/
    constexpr char REQUEST_DRAW_TEMP_REGION_EVENT_NAME[] = "REQUEST_DRAW_TEMP_REGION_EVENT_NAME";        /**< 请求绘制临时区域*/
    constexpr char SHOWLIST_CLEAN_EVENT_NAME[] = "SHOWLIST_CLEAN_EVENT_NAME";                                 /**< 文件列表清除事件 */
    constexpr char RENDER2D_GRAPHICS_UPDATE_EVENT_NAME[] = "RENDER2D_GRAPHICS_UPDATE_EVENT_NAME";           /**< 渲染界面图形更新 */
    constexpr char RENDER2D_GRAPHICS_CREATE_EVENT_NAME[] = "RENDER2D_GRAPHICS_CREATE_EVENT_NAME";           /**< 渲染界面图形添加 */
    constexpr char RENDER2D_GRAPHICS_DELETE_EVENT_NAME[] = "RENDER2D_GRAPHICS_DELETE_EVENT_NAME";           /**< 渲染界面图形删除 */
    constexpr char RENDER2D_GRAPHICS_SELECT_EVENT_NAME[] = "RENDER2D_GRAPHICS_SELECT_EVENT_NAME";           /**< 渲染界面图形选择*/
    constexpr char RENDER2D_COMPONENT_DELETE_EVENT_NAME[] = "RENDER2D_COMPONENT_DELETE_EVENT_NAME";           /**< 渲染界面元件删除 */
    constexpr char RENDER2D_COMPONENT_SELECT_EVENT_NAME[] = "RENDER2D_COMPONENT_SELECT_EVENT_NAME";           /**< 渲染界面元件选择 */
    constexpr char RENDER2D_BOARD_SELECT_EVENT_NAME[] = "RENDER2D_BOARD_SELECT_EVENT_NAME";           /**< 渲染界面板选择 */
    constexpr char RENDER2D_SUB_BOARD_SELECT_EVENT_NAME[] = "RENDER2D_SUB_BOARD_SELECT_EVENT_NAME";           /**< 渲染界面子板选择 */
    constexpr char RENDER2D_MARK_SELECT_EVENT_NAME[] = "RENDER2D_MARK_SELECT_EVENT_NAME";           /**< 渲染界面MARK选择 */
    constexpr char RENDER2D_SUB_MARK_SELECT_EVENT_NAME[] = "RENDER2D_SUB_MARK_SELECT_EVENT_NAME";           /**< 渲染界面子板MARK选择 */
    constexpr char RENDER2D_REGION_SELECT_EVENT_NAME[] = "RENDER2D_REGION_SELECT_EVENT_NAME";           /**< 渲染界面检测框选择 */
    constexpr char RENDER2D_SUB_REGION_SELECT_EVENT_NAME[] = "RENDER2D_SUB_REGION_SELECT_EVENT_NAME";           /**< 渲染界面子检测框选择 */
    constexpr char RENDER2D_SHOW_IMAGE_ALGO_EVENT_NAME[] = "RENDER2D_SHOW_IMAGE_ALGO_EVENT_NAME";           /**< 渲染界面渲染算法结果图片 */
    constexpr char RENDER2D_CROP_IMAGE_EVENT_NAME[] = "RENDER2D_CROP_IMAGE_EVENT_NAME";           /**< 渲染界面裁剪图片 */
    constexpr char RENDER2D_DRAW_TEMP_REGION_DONE_EVENT_NAME[] = "RENDER2D_DRAW_TEMP_REGION_DONE_EVENT_NAME"; /**< 渲染界面绘制临时区域完成 */
    constexpr char RENDER2D_AFFINE_TRANSTORM_EVENT_NAME[] = "RENDER2D_AFFINE_TRANSTORM_EVENT_NAME"; /**< 对齐坐标，渲染界面对元件进行放射变换  */
    constexpr char RENDER2D_CAD_UPDATE_POSITION_EVENT_NAME[] = "RENDER2D_CAD_UPDATE_POSITION_EVENT_NAME"; /**< CAD 定位更新 */
    constexpr char OPERATE_MOTION_SETTING_UPDATE[] = "OPERATE_MOTION_SETTING_UPDATE";/**< 运控参数保存 by Haojiangchun 2025.1.13*/
    constexpr char OPERATE_DETECT_STATISICS_UPDATE[] = "OPERATE_DETECT_STATISICS_UPDATE";/**< 检测统计参数更新 by Haojiangchun 2025.1.13*/
    constexpr char RENDER2D_CHANGE_MODE_EVENT_NAME[] = "RENDER2D_CHANGE_MODE_EVENT_NAME";             /**< 渲染界面切换状态 */
    constexpr char SELECT_RENDER2D_LAYER_EVENT_NAME[] = "SELECT_RENDER2D_LAYER_EVENT_NAME";           /**< 选择渲染界面图层 */
    constexpr char DEVICE_CONTROL_EVENT_NAME[] = "device_control_event_name"; /**< 设备控制事件名称 */
    constexpr char MOTION_CONFIG_EVENT_NAME[] = "motion_config_event_name"; /**< 运控配置文件操作事件名称 */
    constexpr char OPERATE_SEND_MOTION_PARAM_TO_WORKFLOW_EVENT_NAME[] = "operate_send_motion_param_to_workflow_event_name";/**< 发送运控配置文件给自动运行模块*/
    constexpr char SCANCE_BOARD_EVENT_NAME[] = "scan_board_event_name"; /**< 扫描整版图事件名称 */



    constexpr char CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME[] = "CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME"; /**< 清除整版大图 */
    constexpr char START_CONTINUE_GRAB_EVENT_NAME[] = "start_continue_grab_event_name"; /**< 点击开始连续采集2d图像 */
    constexpr char STOP_CONTINUE_GRAB_EVENT_NAME[] = "stop_continue_grab_event_name"; /**< 点击停止连续采集2d图像 */
    constexpr char OPERATE_DETECT_CLEAR_RESULT[] = "operate_detect_clear_result"; /**< 检测结果界面上清除按钮消息 */ //chenxixi
    constexpr char OPERATE_DETECT_CLEAR_DEVICE_RESULT[] = "operate_detect_clear_device_result"; /**< 检测结果界面上清除元件按钮消息 */ //chenxixi
    constexpr char OPERATE_UPDATE_TICKET_NUMBER[] = "operate_update_ticket_number"; /**< 检测结果界面上工单号更新消息 */ //chenxixi
    constexpr char SHOWLIST_UPDATE_RENDER_EVENT_NAME[] = "showlist_update_render_event_name"; /**< 显示列表更新到渲染界面事件 */
    constexpr char EDIT_UPDATE_RENDER_EVENT_NAME[] = "edit_update_render_event_name"; /**< 编辑界面更新到渲染界面事件 */
    constexpr char SHOWLIST_SELECT_COMPONENT_UPDATE[] = "showlist_select_component_changed"; /**< 元件变更消息 */
    constexpr char SHOWLIST_SELECT_SUBBOARD_UPDATE[] = "showlist_select_subboard_update"; /**< 子板变更消息 */


    /***********************操作界面事件处理名称******************************/
    constexpr char REQUEST_RENDER2D_UPDATE_PROJECT_EVENT_NAME[] = "REQUEST_RENDER2D_UPDATE_PROJECT_EVENT_NAME";   /**< 请求更新工程事件*/
    constexpr char REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME[] = "REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME";   /**< 请求更新搜索框事件*/
    constexpr char REQUEST_RENDER2D_UPDATE_SELECT_EVENT_NAME[] = "REQUEST_RENDER2D_UPDATE_SELECT_EVENT_NAME";   /**< 请求更新选择事件*/
    constexpr char REQUEST_OPERATE_UPDATE_IPE_PARAM_EVENT_NAME[] = "REQUEST_OPERATE_UPDATE_IPE_PARAM_EVENT_NAME";           /**< 请求更新图像预处理参数*/
    constexpr char REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME[] = "REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME";   /**< 请求更新模板参数*/
    constexpr char REQUEST_RENDER_UPDATE_SELECT_LIGHT_TYPE_EVENT_NAME[] = "REQUEST_RENDER_UPDATE_SELECT_LIGHT_TYPE_EVENT_NAME";   /**< 请求更新渲染界面选择灯光类型*/
    constexpr char OPERATE_UPDATE_IPE_PROCESS_IMAGE_EVENT_NAME[] = "OPERATE_UPDATE_IPE_PROCESS_IMAGE_EVENT_NAME"; /**< 更新图像预处理模块的源图像数据*/
    constexpr char OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME[] = "OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME"; /**< 更新整个算法编辑界面*/
    constexpr char OPERATE_UPDATE_DET_WIN_VIEW_EVENT_NAME[] = "OPERATE_UPDATE_SEL_DET_WIN_VIEW_EVENT_NAME"; /**< 更新选中检测框界面*/
    constexpr char OPERATE_UPDATE_ALGO_DETECT_RESULT_EVENT_NAME[] = "operate_update_algo_detect_result_event_name"; /**< 更新当前算法检测结果到算法编辑UI*/
    constexpr char OPERATE_UPDATE_PAD_WIN_PARAM_EVENT_NAME[] = "OPERATE_UPDATE_PAD_WIN_PARAM_EVENT_NAME"; /**< 更新选中PAD框*/
    constexpr char OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME[] = "OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME";
    constexpr char OPERATE_CREATE_TEMPLATE_BY_DRAW_REGION_EVENT_NAME[] = "OPERATE_CREATE_TEMPLATE_BY_DRAW_REGION_EVENT_NAME"; /**< 根据截取的区域创建模板*/

    constexpr char OPERATE_TEST_DETECT_WIN_EVENT_NAME[] = "OPERATE_TEST_DETECT_WIN_EVENT_NAME";
    constexpr char OPERATE_TEST_COMPONENT_EVENT_NAME[] = "OPERATE_TEST_COMPONENT_EVENT_NAME";
    constexpr char OPERATE_TEST_PART_NUMBER_EVENT_NAME[] = "OPERATE_TEST_PART_NUMBER_EVENT_NAME";

    constexpr char RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME[] = "RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME"; /**< 渲染界面更新算法编辑界面*/
    constexpr char OPERATE_REQUEST_RENDER_FRAME_SELECT_REGION_EVENT_NAME[] = "OPERATE_REQUEST_RENDER_FRAME_SELECT_REGION_EVENT_NAME"; /**< 请求Render框选区域*/

    constexpr char REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME[] = "REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME";               /**< 请求更新检测框参数*/
    constexpr char OPERATE_SAVE_REAPIR_COMPRESS_IMG_EVENT_NAME[] = "operate_save_repair_compress_img_event_name";       /**< 操作界面保存工程压缩大图*/
    constexpr char OPERATE_SAVE_REAPIR_BRIEF_COMPONENT_EVENT_NAME[] = "operate_save_repair_brief_component_event_name";       /**< 操作界面保存工程元件简要信息*/
    constexpr char OPERATE_COMPONENT_GET_ALL_COMPONENT_EVENT_NAME[] = "operate_component_get_all_component_event_name";       /**< 操作界面所有元件的信息*/
    constexpr char OPERATE_COMPONENT_GET_CURSELLECT_COMPONENT_EVENT_NAME[] = "operate_component_get_cursellect_component_event_name";       /**< 操作界面获取当前选中元件信息*/
    constexpr char OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME[] = "operate_component_multialgo_add_event_name";       /**< 添加多个算法检测框*/
    constexpr char OPERATE_COMPONENT_LOAD_CURSELLECT_COMPONENT_EVENT_NAME[] = "operate_component_load_cursellect_component_event_name";       /**< 操作界面获取当前选中元件信息 和上面的区别是获取后的处理不同*/
    constexpr char OPERATE_COMPONENT_SAVE_COMPONENT_EVENT_NAME[] = "operate_component_save_component_event_name";       /**< 操作界面保存当前元件*/
    constexpr char OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME[] = "operate_component_read_component_event_name";       /**< 读取元件地址下所有元件*/
    constexpr char OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME[] = "operate_component_apply_components_event_name";       /**< 加载元件库*/
    constexpr char OPERATE_COMPONENT_REMOVE_COMPONENT_EVENT_NAME[] = "operate_component_remove_component_event_name";       /**< 移除元件库元件*/
    //! 算法运行部分
    constexpr char OPERATE_SAVE_ALGO_EXECUTE_INFO_EVENT_NAME[] = "operate_save_algo_execute_info_event_name";/**< 保存算法执行时的参数信息 by zhangyuyu 2025.1.8*/
    constexpr char OPERATE_UPDATE_COMPONENT_RESULT_STATUS_EVENT_NAME[] = "operate_update_component_result_status_event_name"; /**< 更新所有元件算法执行结果状态，主要用于右侧显示列表上结果状态的更新 by zhangyuyu 2025.1.20*/
#pragma endregion 操作界面事件处理名称


#pragma region 快捷按钮事件名称

    //!主题
    constexpr char SHORTCUT_TRIGGER_TOPIC_NAME[] = "shortcut_topic_name"; /**< 快捷按钮点击主题名称 */

    //! 订阅者
    constexpr char SHORTCUT_RENDER_SUB_NAME[] = "shortcut_render_sub_name"; /**< 快捷按钮事件渲染界面订阅者*/
    constexpr char SHOWLIST_CHANGE_SUB_NAME[] = "showlist_change_sub_name"; /**< 元件列表事件渲染界面订阅者*/
    constexpr char SHOWLIST_OPERATE_SUB_NAME[] = "showlist_operate_sub_name"; /**< 元件列表事件操作模块订阅者*/
    constexpr char SHORTCUT_LOGIC_SUB_NAME[] = "shortcut_logic_sub_name"; /**< 快捷界面按钮逻辑层订阅者*/
    constexpr char SHORTCUT_OPERATER_SUB_NAME[] = "shortcut_operater_sub_name"; /**< 快捷界面按钮逻辑层订阅者*/
    //! 工程相关
    constexpr char SHORTCUT_ACT_SAVE_STANDARD_IMG[] = "shortcut_act_save_standard_img";  /**< 保存标准图片*/
    //快捷键事件
    //! 发出的事件
    //! 快捷键 图形操作
    constexpr char SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME[] = "SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME"; /**<编辑模板快捷键*/
    constexpr char SHORTCUT_ACT_SHOW_MARK_RESULT_EVENT_NAME[] = "SHORTCUT_ACT_SHOW_MARK_RESULT_EVENT_NAME"; /**<显示Mark图片快捷键*/
    constexpr char SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME[] = "SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME"; /**<轴移动快捷键*/
    constexpr char CHANGE_RENDER_SHOW_IMG_TYPE[] = "change_render_show_img_type"; /**< 切换渲染界面显示的大图类型*/
    constexpr char SHORTCUT_ACT_ADD_COMPONENT[] = "SHORTCUT_ACT_ADD_COMPONENT";         /**< 添加元件快捷键*/
    constexpr char SHORTCUT_ACT_COPY_COMPONENT[] = "SHORTCUT_ACT_COPY_COMPONENT";   /**< 复制元件快捷键*/
    constexpr char SHORTCUT_ACT_DELETE_COMPONENT[] = "SHORTCUT_ACT_DELETE_COMPONENT";   /**< 删除元件快捷键*/
    constexpr char SHORTCUT_ACT_CREATE_MARK_EVENT_NAME[] = "SHORTCUT_ACT_CREATE_MARK_EVENT_NAME";   /**< 添加Mark操作*/
    constexpr char SHORTCUT_ACT_CREATE_BARCODE_EVENT_NAME[] = "SHORTCUT_ACT_CREATE_BARCODE_EVENT_NAME";   /**< 添加barcode操作*/
    constexpr char SHORTCUT_ACT_REQUEST_RENDER_CREATE_SUBBADMARK_EVENT_NAME[] = "SHORTCUT_ACT_REQUEST_RENDER_CREATE_SUBBADMARK_EVENT_NAME";   /**< 添加坏板标记*/
    constexpr char SHORTCUT_ACT_REVOKE[] = "SHORTCUT_ACT_REVOKE";   /**< 撤销操作*/
    constexpr char SHORTCUT_ACT_RECOVER[] = "SHORTCUT_ACT_RECOVER";   /**< 恢复操作*/
    //快捷键 图片操作
    constexpr char SHORTCUT_ACT_IMAGE_SIZE_100[] = "SHORTCUT_ACT_IMAGE_SIZE_100";   /**< 图片100%显示操作*/
    constexpr char SHORTCUT_ACT_IMAGE_PANORAMA[] = "SHORTCUT_ACT_IMAGE_PANORAMA";   /**< 全屏显示操作*/
    constexpr char SHORTCUT_ACT_IMAGE_CENTER[] = "SHORTCUT_ACT_IMAGE_CENTER";       /**< 图像居中显示操作*/
    constexpr char SHORTCUT_ACT_IMAGE_ZOOM_IN[] = "SHORTCUT_ACT_IMAGE_ZOOM_IN";     /**<放大显示操作*/
    constexpr char SHORTCUT_ACT_IMAGE_ZOOM_OUT[] = "SHORTCUT_ACT_IMAGE_ZOOM_OUT";   /**< 缩小显示操作*/
    //快捷键 CAD操作
    constexpr char SHORTCUT_ACT_CAD_ROTATEION_90[] = "SHORTCUT_ACT_CAD_ROTATEION_90";       /**< 旋转90° CAD操作*/

    constexpr char SHORTCUT_ACT_CAD_LEFT_RIGHT_MIRROR[] = "SHORTCUT_ACT_CAD_LEFT_RIGHT_MIRROR";             /**< 左右镜像 CAD操作*/
    constexpr char SHORTCUT_ACT_CAD_UP_DOWN_MIRROR[] = "SHORTCUT_ACT_CAD_UP_DOWN_MIRROR";                   /**< 上下镜像 CAD操作*/
    constexpr char SHORTCUT_ACT_CAD_CONVERSION[] = "SHORTCUT_ACT_CAD_CONVERSION";                           /**< 逆时针顺时针转换 CAD操作*/
    constexpr char SHORTCUT_ACT_CAD_ALL_ELEMENTS_ROTATE_90[] = "SHORTCUT_ACT_CAD_ALL_ELEMENTS_ROTATE_90";   /**< 所有元件旋转90度 CAD操作*/
    constexpr char SHORTCUT_ACT_CAD_MOVE[] = "SHORTCUT_ACT_CAD_MOVE";                                       /**< 整体移动 CAD操作*/

    //快捷键 Pad 镜像
    constexpr char SHORTCUT_ACT_COPY_PAD_TOP_BOTTOM_MIRROR[] = "SHORTCUT_ACT_COPY_PAD_TOP_BOTTOM_MIRROR";   /**< Pad上下镜像操作*/
    constexpr char SHORTCUT_ACT_COPY_PAD_LEFT_RIGHT_MIRROR[] = "SHORTCUT_ACT_COPY_PAD_LEFT_RIGHT_MIRROR";   /**< Pad左右镜像操作*/
    constexpr char SHORTCUT_ACT_COPY_PAD_ROTATE_90_DUPLICATE[] = "SHORTCUT_ACT_COPY_PAD_ROTATE_90_DUPLICATE";   /**< Pad90度旋转复制操作*/
    constexpr char SHORTCUT_ACT_COPY_PAD_ROTATE_180_DUPLICATE[] = "SHORTCUT_ACT_COPY_PAD_ROTATE_180_DUPLICATE";   /**< Pad180度旋转复制操作*/

    // 本体检测框快捷键
    constexpr char SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR[] = "SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR";   /**< body检测框上下镜像操作*/
    constexpr char SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR[] = "SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR";   /**< body检测框左右镜像操作*/
    constexpr char SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE[] = "SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE";   /**< body检测框90度旋转复制操作*/
    constexpr char SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE[] = "SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE";   /**< body检测框180度旋转复制操作*/

    // AI识别快捷键
    constexpr char SHORTCUT_ACT_SUBBOARD_AI[] = "SHORTCUT_ACT_SUBBOARD_AI";         /**< 子板AI元件识别*/
    constexpr char SHORTCUT_ACT_BOARD_AI[] = "SHORTCUT_ACT_BOARD_AI";               /**< 整板AI元件识别*/
    constexpr char SHORTCUT_ACT_BODY_AI[] = "SHORTCUT_ACT_BODY_AI";                 /**< 本体AI元件识别*/

    // 检测框复制与粘贴
    constexpr char SHORTCUT_ACT_DETECT_COPY[] = "SHORTCUT_ACT_DETECT_COPY";   /**< 检测框复制*/
    constexpr char SHORTCUT_ACT_COMPONENT_DETECT_COPY[] = "SHORTCUT_ACT_COMPONENT_DETECT_COPY";   /**< 元件检测框复制*/
    constexpr char SHORTCUT_ACT_DETECT_ACTION[] = "SHORTCUT_ACT_DETECT_ACTION";   /**< 检测框粘贴*/
    constexpr char SHORTCUT_ACT_ROTATE_90_ALL[] = "SHORTCUT_ACT_ROTATE_90_ALL";      /**< 所有检测框旋转90度*/


    //! 快捷键 检测事件
    constexpr char ACT_TEST_DETECT_WINDOW[] = "act_test_detect_window"; /**< 检测框测试运行事件    */
    constexpr char ACT_TEST_COMPONET[] = "act_test_componet"; /**< 单元件检测    */
    constexpr char ACT_TEST_PART_NUM[] = "act_test_part_num"; /**< 同料号检测    */
    constexpr char ACT_TEST_POS_ALIGN[] = "act_test_pos_align"; /**< 定位对齐运行事件    */
    constexpr char SHORTCUT_ACT_RUN_COMPONENT_SAVE_ALGO_INFO_EVENT[] = "shortcut_act_run_component_save_algo_info_event"; /**< 执行元件检测并保存元件下所有算法检测信息包括检测结果*/
    constexpr char SHORTCUT_ACT_COMPONENT_LOCATED_RUN_EVENT[] = "shortcut_act_component_located_run_event"; /**< 元件定位后运行事件*/
    constexpr char SHORTCUT_ACT_PARTNUMBER_LOCATED_RUN_EVENT[] = "shortcut_act_partnumber_located_run_event"; /**< 料号定位后运行事件*/
#pragma endregion 快捷按钮事件名称

    /***********************工程模块发布订阅******************************/

#pragma region 工程模块发布订阅

  //! 主题
    constexpr char PROJECT_TOPIC_NAME[] = "project_topic_name";           /**< 工程模块变化主题 */

    //! 事件
    constexpr char PROJECT_RENDER_SUB_NAME[] = "project_render_sub_name"; /**< 渲染界面订阅工程模块变化主题*/
    constexpr char PROJECT_SHOWLIST_SUB_NAME[] = "project_showlist_sub_name"; /**< 元件显示界面订阅工程模块变化主题*/
    constexpr char PROJECT_READ_SUB_NAME[] = "PROJECT_READ_SUB_NAME"; /**< 工程读取主题*/
#pragma endregion 工程模块发布订阅

    /***********************运行面板发布订阅******************************/
#pragma region 运行面板发布订阅
    //! 主题
    constexpr char CONTROL_PANEL_TOPIC_NAME[] = "control_panel_topic_name"; /**< 自动运行界面变化主题*/
    constexpr char CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME[] = "control_panel_onlinedebug_topic_name"; /**< 在线调试变化主题*/
    //! 订阅者
    constexpr char CONTROL_PANEL_LOGIC_SUB_NAME[] = "control_panel_logic_sub_name";  /**< 运行面板逻辑层响应事件*/
    constexpr char CONTROL_PANEL_RENDER_SUB_NAME[] = "CONTROL_PANEL_RENDER_SUB_NAME";  /**< 运行面板渲染界面订阅者 */
    constexpr char CONTROL_PANEL_OPERATE_SUB_NAME[] = "CONTROL_PANEL_OPERATE_SUB_NAME";  /**< 运行面操作模块订阅者 */
    constexpr char CONTROL_PANEL_VIEW_SUB_NAME[] = "control_panel_view_sub_name";	   /**< 自动运行面板订阅者 */
    constexpr char CONTROL_PANEL_ONLINE_DEBUG_ONLINE_DEBUG_VIEW_SUB_NAME[] = "control_panel_onlinedebug_onlinedebug_view_sub_name"; /**< 在线调试界面订阅者 */
    constexpr char CONTROL_PANEL_ONLINE_DEBUG_OPERATE_SUB_NAME[] = "control_panel_online_debug_operate_sub_name"; /**< 在线调试状态切换主题，Operate模块订阅者  by zhangyuyu 2025.4.8*/
    constexpr char CONTROL_PANEL_ONLINE_DEBUG_SHOW_LIST_SUB_NAME[] = "control_panel_onlinedebug_show_list_sub_name"; /**< 在线调试状态切换主题，showlist模块订阅者  by zhangyuyu 2025.4.8*/
    constexpr char CONTROL_PANEL_ONLINE_RENDER_SUB_NAME[] = "control_panel_online_render_sub_name"; /**< 在线调试状态切换主题，render模块订阅者  by zhangyuyu 2025.4.8*/
    constexpr char CONTROL_PANEL_ONLINE_DEBUGE_ALL_SUB_NAME[] = "all"; /**< 在线调试状态变化的所有订阅者订阅者 */
    //! 事件名称
    constexpr char AUTO_RUN_PANEL_START_FLOW_NAME[] = "auto_run_panel_start_flow_name"; /**< 开始流程运行事件    */
    constexpr char AUTO_RUN_PANEL_STOP_FLOW_NAME[] = "auto_run_panel_stop_flow_name"; /**< 结束流程运行事件      */
    constexpr char AUTO_RUN_PANEL_UPDATE_STATE_NAME[] = "auto_run_panel_update_state_name"; /**< 运行流程状态更改事件 */
    //! 发出的事件
    constexpr char CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME[] = "control_panel_enable_online_debug_send_event_name"; /**< 启用在线调试功能    */
    constexpr char CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME[] = "control_panel_disable_online_debug_send_event_name"; /**< 停止在线调试功能    */

#pragma endregion
    /***********************在线调试发布订阅******************************/
#pragma region 在线调试发布订阅


//!主题
    constexpr char ONLINEDEBUG_TOPIC_NAME[] = "onlinedebug_topic_name"; /**< 在线调试变化主题*/
    constexpr char ONLINEDEBUG_FINISH_TOPIC_NAME[] = "onlinedebug_finish_topic_name"; /**< 在线调试完成主题*/

    //! 订阅者
    constexpr char ONLINEDEBUG_RENDER_SUB_NAME[] = "onlinedebug_render_sub_name";  /**< 在线调试渲染界面订阅者 */
    constexpr char ONLINEDEBUG_OPERATE_SUB_NAME[] = "onlinedebug_operate_sub_name";  /**< 在线调试主操作界面订阅者 */
    constexpr char ONLINEDEBUG_WORKFLOW_SUB_NAME[] = "onlinedebug_workflow_sub_name";  /**< 在线调试工作流订阅者 */
    constexpr char ONLINEDEBUG_FINISH_WORKFLOW_SUB_NAME[] = "onlinedebug_finish_workflow_sub_name"; /**< 在线调试完成工作流订阅者 */
    constexpr char ONLINEDEBUG_FINISH_OPERATE_SUB_NAME[] = "onlinedebug_finish_operate_sub_name"; /**< 在线调试完成操作界面订阅者 */
    constexpr char ONLINEDEBUG_ALL_SUB_NAME[] = "all"; /**< 在线调试主题所有订阅者 */

    //! 发出事件
    constexpr char ONLINEDEBUG_CHANGE_DEBUG_COMPONENT_EVENT_NAME[] = "onlinedebug_change_debug_component_event_name"; /**< 在线调试切换调试元件事件*/
    constexpr char ONLINEDEBUG_DEBUG_FINISHED_SEND_EVNET_NAME[] = "onlinedebug_debug_finished_send_event_name"; /**< 在线调试调试完成事件*/
    constexpr char ONLINEDEBUG_DEBUG_IS_STOP_WORKFLOW_EVNET_NAME[] = "onlinedebug_debug_is_stop_workflow_event_name"; /**< 在线调试是否停止流程事件*/
    //! 接受事件
#pragma endregion 在线调试发布订阅




/***********************逻辑模块发布订阅******************************/
#pragma region 逻辑模块发布订阅

    //!主题
    constexpr char LOGIC_TOPIC_NAME[] = "logic_topic_name"; /**< 工程模块变化主题 */
    constexpr char LOGIC_RENDER_SUB_NAME[] = "logic_render_sub_name"; /**< 渲染界面订阅工程模块变化主题*/
    constexpr char LISTVIEW_TRIGGER_TOPIC_NAME[] = "listview_topic_name"; /**< 元件列表按钮点击主题名称 */
    constexpr char LISTVIEW_RENDER_SUB_NAME[] = "listview_render_sub_name"; /**< 元件列表按钮事件渲染界面订阅者*/
    //!事件
    //! 发出事件
    constexpr char LOGIC_UPDATE_DEVICE_PARAM_EVENT[] = "logic_update_deviec_param_event";/**< logic层更新设备参数事件*/
#pragma endregion 逻辑模块发布订阅
    /***********************设置参数事件名称******************************/
#pragma region 设置参数发布订阅

//! 主题
    constexpr char SETTING_TOPIC_NAME[] = "setting_topic_name";           /**< 设置参数变化主题 */
    constexpr char SETTING_SUB_NAME[] = "setting_sub_name";               /**< 设置参数订阅变化主题*/
    constexpr char SHOW_SETTING_VIEW_EVENT_NAME[] = "show_setting_view_event_name";        /**< 设置参数订阅变化主题*/
    constexpr char ALL_PARAM_UPDATE_EVENT_NAME[] = "all_param_update_event_name";   /**< 更新所有参数 事件 */
    constexpr char UPDATE_COMMON_PARAM_EVENT[] = "update_common_param_event";       /**< 更新普通参数  */
    constexpr char SYSTEM_PARAM_SAVE_EVENT[] = "system_param_save_envet";           /**< 保存系统参数事件*/
    constexpr char COMMON_PARAM_SAVE_EVENT[] = "common_param_save_envet";           /**< 保存普通参数事件*/
    constexpr char MACHINE_PARAM_SAVE_EVENT[] = "machine_param_save_envet";         /**< 机台参数保存事件*/
    constexpr char MACHINE_PARAM_UPDATE_EVENT[] = "machine_param_update_envet";     /**< 机台参数更新事件*/
    /**< 数据库事件 */
    constexpr char DATABASE_CONNECT_EVENT[] = "database_connect_envet";			/**< 数据库连接事件*/
    constexpr char DATABASE_DISCONNECT_EVENT[] = "database_disconnect_envet";     /**< 数据库断开连接事件*/
    /**< 结果参数保存事件 **/
    constexpr char DETECT_RESULT_PARAM_SAVE_EVENT[] = "detect_result_param_save_envet";  /**< 检测结果保存参数事件*/
    /**<从数据库中查询结果 */
    constexpr char QUERY_DATABASE_SUBBOARD_BARCODES[] = "query_database_subboard_barcodes"; /**< 查询数据库子板条码事件*/
#pragma endregion 设置参数发布订阅

#pragma region 初始化系统发布订阅
    constexpr char SYSTEM_STATE_TOPIC_NAME[] = "systen_state_topic_name";						/**< 初始化系统主题         */
    constexpr char SYSTEM_STATE_LOGIC_SUB_NAME[] = "SYSTEM_STATE_LOGIC_SUB_NAME";				/**< 初始化系统逻辑层订阅者 */

    constexpr char PARAMS_UPDATE_SUB_NAME[] = "PARAMS_UPDATE_SUB_NAME";                         /**< 参数更新订阅者*/
    constexpr char SYSTEM_STATE_VIEW_SUB_NAME[] = "SYSTEM_STATE_VIEW_SUB_NAME";				/**< 初始化系统逻辑层订阅者 */
    constexpr char SYSTEM_STATE_DEVICE_RESET_EVENT[] = "SYSTEM_STATE_DEVICE_RESET_EVENT";       /**< 初始化系统设备复位事件 */
    constexpr char UPDATE_SYSTEM_STATE_EVENT[] = "update_system_state_event";       /**< 更新系统状态事件 */

#pragma endregion

#pragma region 运控发布订阅
    constexpr char MOTION_UPDATE_DEBUG_VIEW_EVENT[] = "motion_update_debug_view_event";       /**< 运控模块数据更新到调试界面事件 */

#pragma endregion

#pragma region 检测结果发布订阅
    //!主题
    constexpr char DETECT_RESULT_UPDATE_TOPIC_NAME[] = "DETECT_RESULT_TOPIC_NAME"; /**< 检测结果更新 */
    constexpr char DETECT_RESULT_ONLINE_DEBUG_TOPIC_NAME[] = "detect_result_online_debug_topic_name";/**< 在线调试检测结果更新主题 */
    //! 订阅者
    constexpr char DETECT_RESULT_RENDER_SUB_NAME[] = "DETECT_RESULT_RENDER_SUB_NAME"; /**< 检测结果渲染界面订阅*/
    constexpr char DETECT_RESULT_OPERATE_VIEW_SUB_NAME[] = "detect_result_operate_view_sub_name"; /**< 检测结果操作界面订阅*/
    constexpr char DETECT_RESULT_ONLINE_DEBUG_VIEW_SUB_NAME[] = "detect_result_online_debug_view_sub_name"; /**< 在线调试结果在线调试界面订阅者*/
    constexpr char DETECT_RESULT_ALL_SUB_NAME[] = "all"; /**< 检测结果主题所有订阅者 */
    //事件
    constexpr char RENDER2D_COMPONENT_DETECT_RESULT_EVENT_NAME[] = "RENDER2D_COMPONENT_DETECT_RESULT_EVENT_NAME"; /**< 元件检测结果更新到渲染界面*/
    constexpr char DETECT_RESULT_ONLINE_DEBUG_INFO_EVENT_NAME[] = "detect_result_online_debug_info_event_name"; /**< 在线调试检测信息更新到在线调试界面*/

#pragma endregion


}
#endif // !__VIEWDEFINE_H__
