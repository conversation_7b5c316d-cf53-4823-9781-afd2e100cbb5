#include "operatordrivebase.h"
#include "generaltool.h"
#include <imageprocessalgo.h>

bool jrsoperator::OperatorDriveBase::UpdateDetectRect(OperatorParamBasePtr execute_param_)
{ 
    cv::Point2f center(execute_param_->detect_rect.cx,
                 execute_param_->detect_rect.cy);
    std::vector<JrsRect>().swap(execute_param_->output_detect_rects);
    if (execute_param_->detect_rect.Empty())
    {
        cv::Rect rect(1, 1, execute_param_->input_image[0].cols - 1, execute_param_->input_image[0].rows - 1);
        execute_param_->detect_rect = JrsRect::FromCvRect(rect);
    }
    auto cad_rect_temp = execute_param_->component_rect.ToCvRect();
    auto detect_rect_temp = execute_param_->detect_rect.ToCvRect();
    cad_rect_temp.x -= detect_rect_temp.x;
    cad_rect_temp.y -= detect_rect_temp.y;
    m_algo_det_data.detect_component_rect_jrs = JrsRect::FromCvRect(cad_rect_temp);
    m_algo_det_data.detect_component_rect = m_algo_det_data.detect_component_rect_jrs.ToCvRect();


    WarpMatrixToRect(execute_param_->detect_rect.ToCvRect2f(),
        execute_param_->hom_matrix,center, m_algo_det_data.detect_rotate);
    WarpMatrixToRect(execute_param_->ori_detect_rect.ToCvRect2f(),
        execute_param_->hom_matrix, center, m_algo_det_data.ori_detect_rotate);
    execute_param_->output_detect_rects.push_back(JrsRect::FromCvRotatedRect(m_algo_det_data.ori_detect_rotate));
   
    m_algo_det_data.sub_detect_rotates.clear();
    for (auto& [key,rects] : execute_param_->sub_detect_rects)
    {
        for (auto& rect : rects)
        {
            cv::RotatedRect rotate;
            WarpMatrixToRect(rect.ToCvRect2fWithAngle(),
                execute_param_->hom_matrix, center, rotate);
            m_algo_det_data.sub_detect_rotates[key].push_back(rotate);
        }   
    }

    m_algo_det_data.ori_sub_detect_rotates.clear();
    std::vector<JrsRect> current_ori_sub_rects;
    for (auto& [key, rects] : execute_param_->ori_sub_detect_rects)
    {
        for (auto& rect : rects)
        {
            cv::RotatedRect rotate;
            WarpMatrixToRect(rect.ToCvRect2fWithAngle(),
                execute_param_->hom_matrix, center, rotate);
            m_algo_det_data.ori_sub_detect_rotates[key].push_back(rotate);
            current_ori_sub_rects.push_back(JrsRect::FromCvRotatedRect(rotate, rect.id));
        }
    }

    m_algo_det_data.pad_rotates.clear();
    for (auto& [key, rects] : execute_param_->pad_rects)
    {
        for (auto& rect : rects)
        {
            cv::RotatedRect rotate;
            WarpMatrixToRect(rect.ToCvRect2fWithAngle(),
                execute_param_->hom_matrix, center, rotate);
            m_algo_det_data.pad_rotates[key].push_back(rotate);
        }
    }

    if (!current_ori_sub_rects.empty() && !execute_param_->pad_rects.empty())
    {
        current_ori_sub_rects.swap(execute_param_->output_detect_rects);
    }
    return true;
}

//bool jrsoperator::OperatorDriveBase::GetCurDetImage(const ColorParams& color_parm,
//    const cv::Mat& input_image, cv::Mat& det_image)
//{
//    if (m_algo_det_data.detect_rotate.size.area() == 0)
//    {
//        return false;
//    }
//    CropImage(input_image, det_image, m_algo_det_data.detect_rotate);
//    if (det_image.empty())
//    {
//        return false;
//    }
//    colorwheel::ApplyColorParam(color_parm, det_image);
//    return true;
//}

bool jrsoperator::OperatorDriveBase::SaveBaseParamToDirectory(OperatorParamBasePtr execute_param_, std::string directory)
{
    return false;
}


