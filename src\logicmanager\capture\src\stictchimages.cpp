#include "stictchimages.h"

StictchImagesTool::StictchImagesTool(const float& _resolution, const int& _fov_w, const int& _fov_h, const bool& _x_axis_neg, const bool& _y_axis_neg)
{
    resolution = _resolution;
    resolution_x = _resolution;
    resolution_y = _resolution;
    x_axis_neg = _x_axis_neg;
    y_axis_neg = _y_axis_neg;
    fov_w      = _fov_w;
    fov_h      = _fov_h;
}

StictchImagesTool::~StictchImagesTool()
{

}

void StictchImagesTool::SetAllFovPos(const vector<cv::Point2f>& all_img_pos)
{
    auto minmax_x = std::minmax_element(all_img_pos.begin(), all_img_pos.end(), [](const cv::Point2f& a, const cv::Point2f& b) {return a.x < b.x; });
    auto minmax_y = std::minmax_element(all_img_pos.begin(), all_img_pos.end(), [](const cv::Point2f& a, const cv::Point2f& b) {return a.y < b.y; });
    x_min = minmax_x.first->x;
    x_max = minmax_x.second->x;
    y_min = minmax_y.first->y;
    y_max = minmax_y.second->y;
}

void StictchImagesTool::GetBoardImageSize(int& out_img_w, int& out_img_h) const
{
	out_img_h = (int)std::ceil((y_max - y_min) / resolution + fov_h);
	out_img_w = (int)std::ceil((x_max - x_min) / resolution + fov_w);
}

int StictchImagesTool::StictchImages(const vector<std::pair<cv::Point2f, Mat>>& imgs_data, Mat& board_image, Mat& output_mask, const bool& is_merge)
{
	if (imgs_data.empty())
	{
		return -1;
	}
	
	int sigle_image_width = imgs_data[0].second.cols;
	int sigle_image_height = imgs_data[0].second.rows;

	for (int i = 0; i < imgs_data.size(); i++)
	{
		auto cur_img = imgs_data[i];
		// 根据机械轴极性将每张图的中心转成图像坐标
		float center_x = (x_axis_neg) ? (x_max - cur_img.first.x) / resolution_x + sigle_image_width / 2
			: (cur_img.first.x - x_min) / resolution_x + sigle_image_width / 2;
		float center_y = (y_axis_neg) ? (y_max - cur_img.first.y) / resolution_y + sigle_image_height / 2
			: (cur_img.first.y - y_min) / resolution_y + sigle_image_height / 2;

		// 将小图贴在大图上
		cv::Point top_left(int(std::round(center_x - sigle_image_width / 2)), int(std::round(center_y - sigle_image_height / 2)));
		cv::Point bottom_right(top_left.x + sigle_image_width, top_left.y + sigle_image_height);
		cv::Rect  roi(top_left, bottom_right);

		cv::Rect board_image_rect(0, 0, board_image.cols, board_image.rows);
		if (roi.x<board_image_rect.x || roi.y<board_image_rect.y || roi.br().x>board_image_rect.br().x || roi.br().y>board_image_rect.br().y)
		{
			return -1;
		}

		if (is_merge)
		{
			int img_type = cur_img.second.type();
			const int mask_val = 127;
			if (img_type == CV_8UC1 || img_type == CV_8UC3 || img_type == CV_32FC1)
			{
				cv::Mat feather_mask = output_mask(roi);
				cv::Mat input_img1 = cur_img.second;
				cv::Mat input_img2 = board_image(roi);
				cv::Mat output_img;
				CalculateFeatherImage(input_img1, input_img2, feather_mask,output_img);
				output_img.copyTo(board_image(roi));
				cv::Mat img_mask = cv::Mat(cur_img.second.size(), CV_8UC1, cv::Scalar(mask_val));
				img_mask.copyTo(output_mask(roi));
			}
		}
		else
		{
			cur_img.second.copyTo(board_image(roi));
		}
		
	}
	return 0;
}

int StictchImagesTool::CalculateFeatherImage(const cv::Mat& input_img1, const cv::Mat& input_img2, const cv::Mat& feather_mask,cv::Mat& output_img)
{
	cv::Mat weight_mask1;
	const int mask_val = 254;
	if (feather_mask.empty() || feather_mask.type() != CV_8UC1)
	{
		return -1;
	}
	weight_mask1 = cv::Mat::zeros(feather_mask.size(), feather_mask.type());
	weight_mask1.setTo(mask_val);
	cv::blur(weight_mask1, weight_mask1, cv::Size(21, 21), cv::Point(-1, -1), cv::BORDER_CONSTANT);

	const float max_val = mask_val / 2;
	weight_mask1 = weight_mask1 - max_val;

	output_img = cv::Mat(input_img1.size(), input_img1.type());
	if (input_img1.type() == CV_8UC1)
	{
		output_img.forEach<uchar>([&](uchar& pixel, const int* position) {
			if (feather_mask.at<uchar>(position[0], position[1]) == mask_val)
			{
				float weight1_val = weight_mask1.at<uchar>(position[0], position[1]) / max_val;
				pixel = static_cast<uchar>(input_img1.at<uchar>(position[0], position[1]) * weight1_val + input_img2.at<uchar>(position[0], position[1]) * (1 - weight1_val));
			}
			else
			{
				pixel = input_img1.at<uchar>(position[0], position[1]);
			}});
	}
	else if (input_img1.type() == CV_8UC3)
	{
		output_img.forEach<cv::Vec3b>([&](cv::Vec3b& pixel, const int* position) {
			if (feather_mask.at<uchar>(position[0], position[1]) == mask_val)
			{
				float weight1_val = weight_mask1.at<uchar>(position[0], position[1]) / max_val;
				pixel = input_img1.at<cv::Vec3b>(position[0], position[1]) * weight1_val + input_img2.at<cv::Vec3b>(position[0], position[1]) * (1 - weight1_val);
			}
			else
			{
				pixel = input_img1.at<cv::Vec3b>(position[0], position[1]);
			}});
	}
	else if (input_img1.type() == CV_32FC1)
	{
		output_img.forEach<float>([&](float& pixel, const int* position) {
			if (feather_mask.at<uchar>(position[0], position[1]) == mask_val)
			{
				float weight1_val = weight_mask1.at<uchar>(position[0], position[1]) / max_val;
				pixel = input_img1.at<float>(position[0], position[1]) * weight1_val + input_img2.at<float>(position[0], position[1]) * (1 - weight1_val);
			}
			else
			{
				pixel = input_img1.at<float>(position[0], position[1]);
			}});
	}
	return 0;
}