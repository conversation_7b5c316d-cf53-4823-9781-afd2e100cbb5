﻿/*****************************************************************//**
 * @file   algospecparam.h
 * @brief  算法规格参数管理
 * @details
 * <AUTHOR>
 * @date 2025.05.213
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.05.213         <td>V1.0              <td>zhaokunlong      <td><td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef ALGOSPECPARAM_H
#define ALGOSPECPARAM_H

#include "viewdefine.h"
#include "viewparam.hpp"
#include "nlohmann/json.hpp"
using JSON = nlohmann::json;


#include <QDialog>

namespace Ui {
class AlgoSpecParam;
}

class AlgoSpecParam : public QDialog
{
    Q_OBJECT

public:
    explicit AlgoSpecParam(QWidget *parent = nullptr);
    ~AlgoSpecParam();

    // 界面初始化
    void InitView(const std::string& param_);

    // 获取第index行的数据(从0开始)
    jrsdata::AlgoSpecRatioParam GetSpecRatioParam(int index);
private:
    // 绑定信号槽
    void InitConnection();

    // 添加一行数据
    void AddOneLine(QString name, std::vector<float> value);

    // 删除选中行
    void RemoveCurLine();

    // 获取单元格数据
    float GetValue(int row, int col);

    // 打包table数据
    std::string PackgeRatioParam();
signals:
    void SigAlgoRatioChange(const std::string& list);
private:
    Ui::AlgoSpecParam *ui;
};

#endif // ALGOSPECPARAM_H
