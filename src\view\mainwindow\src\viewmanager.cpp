#include "viewmanager.h"
#include "modelbase.h"

#include "viewfactory.hpp"
#include "controlpanelcontroller.h"
#include "controlpanelmodel.h"
#include "controlpanelview.h"
#include "operatecontroller.h"
#include "operatemodel.h"
#include "operateview.h"
#include "toolbarcontroller.h"
#include "toolbarmodel.h"
#include "toolbarview.h"
#include "logshowcontroller.h"
#include "logshowmodel.h"
#include "logshowview.h"
#include "showlistcontroller.h"
#include "showlistmodel.h"
#include "showlistview.h"
#include "render2dcontroller.h"
#include "render2dmodel.h"
#include "render2dview.h"
#include "onlinedebugcontroller.h"
#include "onlinedebugmodel.h"
#include "onlinedebugview.h"
#include "viewdefine.h"

#include "settingcontroller.h"
#include "settingmodel.h"
#include "settingview.h"

#include "systemstatecontroller.h"
#include "systemstatemodel.h"
#include "systemstateview.h"
namespace jrsaoi
{
    struct ManagerImplData
    {
        ManagerImplData()
        {
        }
    };
    ViewManager::ViewManager()
        : impl_data(new ManagerImplData())
    {
        InitMember();// 初始化所有模块
    }

    ViewManager::~ViewManager()
    {
        if (impl_data)
        {
            delete impl_data;
            impl_data = nullptr;
        }
    }

    template<typename Model, typename View, typename Controller>
    int ViewManager::RegisterComponent(std::string name)
    {
        ModelBasePtr model = ViewFactory::CreateModel<Model>(name);
        ViewBase* view = ViewFactory::CreateView<View>(name);
        ControllerBasePtr controller = ViewFactory::CreateController<Controller>(name);
        controller->SetModel(model);
        controller->SetView(view);
        if (mvc_container.find(name) != mvc_container.end())
        {
            return -1;
        }
        else
        {
            mvc_container[name] = std::make_tuple(model, view, controller);
            return 0;
        }
    }
    std::tuple<ModelBasePtr, ViewBase*, ControllerBasePtr> ViewManager::GetMVCComponent(std::string name)
    {
        auto it = mvc_container.find(name);
        if (it != mvc_container.end())
        {
            return it->second;
        }
        // 返回默认值或者抛出异常，视情况而定
        return std::make_tuple(nullptr, nullptr, nullptr);
    }

    ModelBasePtr ViewManager::GetModel(std::string name)
    {
        auto it = mvc_container.find(name);
        if (it != mvc_container.end())
        {
            return std::get<static_cast<int>(MVCTYPE::MODEL)>(it->second);
        }
        return nullptr;
    }
    ViewBase* ViewManager::GetView(std::string name)
    {
        auto it = mvc_container.find(name);
        if (it != mvc_container.end())
        {
            return std::get<static_cast<int>(MVCTYPE::VIEW)>(it->second);
        }
        return nullptr;
    }

    ControllerBasePtr ViewManager::GetController(std::string name)
    {
        auto it = mvc_container.find(name);
        if (it != mvc_container.end())
        {
            return std::get<static_cast<int>(MVCTYPE::CONTROLLER)>(it->second); // 🔑 关键：从tuple中提取Controller
        }
        return nullptr;
    }
    void ViewManager::InitMember()
    {
        InitControlPanel();
        InitRender2D();
        InitOperate();// 初始化所有模块
        InitToolBar();
        InitLogShow();
        InitShowList();
        InitSetting();
        InitSystem();
        InitOnLineDebug();
    }
    void ViewManager::InitControlPanel()
    {
        RegisterComponent<ControlPanelModel, ControlPanelView, ControlPanelController>(jrsaoi::CONTROL_PANEL_MODULE_NAME);
    }
    void ViewManager::InitOperate()
    {
        //注册MVC组件到容器
        RegisterComponent<OperateModel, OperateView, OperateController>(jrsaoi::OPERATE_MODULE_NAME);
    }
    void ViewManager::InitToolBar()
    {
        //RegisterComponent<ToolBarModel, ToolBarView, ToolBarController> (TOOLBAR_MODULE_NAME);
    }
    void ViewManager::InitLogShow()
    {
        RegisterComponent<LogShowModel, LogShowView, LogShowController>(jrsaoi::LOGSHOW_MODULE_NAME);
    }
    void ViewManager::InitRender2D()
    {
        RegisterComponent<Render2dModel, Render2dView, Render2dController>(jrsaoi::RENDER2D_MODULE_NAME);
    }
    void ViewManager::InitShowList()
    {
        RegisterComponent<ShowListModel, ShowListView, ShowListController>(jrsaoi::SHOWLIST_MODULE_NAME);
    }
    void ViewManager::InitSetting()
    {
        RegisterComponent<SettingModel, SettingView, SettingController>(jrsaoi::DATA_MODULE_NAME);
    }

    void ViewManager::InitSystem()
    {
        RegisterComponent<SystemStateModel, SystemStateView, SystemStateController>(jrsaoi::SYSTEM_STATE_MODULE_NAME);
    }
    void ViewManager::InitOnLineDebug()
    {
        RegisterComponent<OnLineDebugModel, OnLineDebugView, OnLineDebugController>(jrsaoi::ONLINEDEBUG_MODULE_NAME);
    }
}