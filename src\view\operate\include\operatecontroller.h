﻿/*****************************************************************//**
 * @file   operatecontroller.h
 * @brief  主操作界面控制类
 * @details  主要为了将操作界面的指令与外界交互
 * <AUTHOR>
 * @date 2024.1.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef OPERATECONTRLLER_H
#define OPERATECONTRLLER_H
 //CUSTOM
#include "controllerbase.h"
#include "operatorparambase.h"
namespace jrsalgo
{
    class AlgorithmEngineManager;
}
namespace jrsparam
{
    struct ExecuteAlgoParam;

}

using OperatorParamBasePtr = std::shared_ptr<jrsoperator::OperatorParamBase>;

namespace jrsaoi
{
    class OperateView;
    class OperateModel;


    class OperateController : public ControllerBase
    {
        Q_OBJECT
    public:
        OperateController(const std::string& name);
        ~OperateController();

        /**
         * @fun Update
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun SetView
         * @brief
         * @param view_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual void SetView(ViewBase* view_param) override;
        /**
         * @fun SetModel
         * @brief
         * @param model_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual void SetModel(ModelBasePtr model_param)override;

        /**
         * @fun SetAlgoEngine
         * @brief 设置算法引擎指针
         * @note TODO:后期优化将算法界面独立加载
         * <AUTHOR>
         * @date 2024.10.15
         */
        void SetAlgoEngine(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_ptr_);



    private slots:

        /**
        * @fun SlotNewProject
        * @brief operate模块中，新建工程部分的controller中的响应槽函数
        * @param param_ 参数
        * <AUTHOR>
        * @date 2024.9.5
        */
        void SlotViewOperator(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun SlotModelOperator
         * @brief
         * @param param_
         * <AUTHOR>
         * @date 2024.12.13
         */
        void SlotModelOperator(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun SlotUpdateCoordinate
         * @brief  更新坐标系参数
         * @param param_
         * <AUTHOR>
         * @date 2024.12.5
         */
        void SlotUpdateCoordinate();
    signals:
        /**
         * @fun SigShowAxisMoveDialog
         * @brief
         * @param param_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigUpdateOperator(const jrsdata::ViewParamBasePtr& param_);

        //// 显示轴移动对话框
        ///**
        // * @fun SigShowAxisMoveDialog
        // * @brief
        // * @param param_
        // * @date 2024.9.24
        // * <AUTHOR>
        // */
        //void SigShowAxisMoveDialog(const jrsdata::OperateViewParamPtr& param_);
        // 保存界面参数信号
        /**
         * @fun SigSaveCfgView
         * @brief
         * @param param_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigSaveCfgView(const jrsdata::OperateViewParamPtr& param_);
    private:
        OperateView* operate_view;
        std::shared_ptr<OperateModel> model;
        std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_ptr;

    

        int  UpdateDetectWinEditView(const jrsdata::ViewParamBasePtr& param_);
        
        /**
         * @fun UpdateAlgoResultToView
         * @brief 将算法检测的结果更新到算法检测框的UI上
         * @param param_ 算法事件参数
         * @param algo_result 当前算法的检测结果
         * <AUTHOR>
         * @date 2024.12.4
         */
        void UpdateAlgoResultToView(const jrsdata::AlgoEventParamPtr& param_, const std::shared_ptr<jrsoperator::OperatorParamBase>algo_result);

        /**
         * @fun  SortDetectWinsByDepedent
         * @brief 根据检测框之间的依赖关系进行有向无环图排序.
         * @param detect_wins 待排序的检测框列表
         * @param sorted_wins 排序后的检测框列表
         * @data 2024.12.24
         * @return 错误码
         * <AUTHOR>
         */
        int  SortDetectWinsByDepedent(const std::vector<jrsdata::DetectWindow>& detect_wins, std::vector<std::vector<jrsdata::DetectWindow>>& sorted_wins);

        /**
         * @fun  ExecuteSingleDetectWin
         * @brief 执行单个检测框的算法检测.
         * @param component 检测框所属元件
         * @param component_unit 检测框所属组件
         * @param detect_win 待执行的检测框
         * @param run_mode 执行类型（0：整版建模，1：在线检测，2：在线调试）
         * @data 2024.12.24
         * @return 检测结果
         * <AUTHOR>
         */
        OperatorParamBasePtr ExecuteSingleDetectWin(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win, cv::Mat& matrix_to_src_image_);

        /**
         * @fun  ExecuteSingleComponent
         * @brief 执行单个元件的所有检测框.
         * @param component 待执行的元件
         * @param pn_detect_info 待执行元件所属料号的所有检测模块
         * @param is_save_algo_info 是否保存算法信息
         * @param is_location 是否用定位结果更新工程元件坐标
         * @data 2024.12.24
         * @return 检测结果
         * <AUTHOR>
         */
        void  ExecuteSingleComponent(const jrsdata::Component& component, const jrsdata::PNDetectInfo& pn_detect_info,bool is_save_algo_info = false,bool is_location = false);

        /**
         * @fun  CurSelectedDetectWinRun
         * @brief 执行当前选中的检测框.
         * @param param_ 算法事件参数
         * @data 2024.12.24
         * <AUTHOR>
         */
        void CurSelectedDetectWinRun(const jrsdata::AlgoEventParamPtr& param_);

        /**
         * @fun  CurSelectedComponentRun
         * @brief 执行当前选中的元件.
         * @param param_ 算法事件参数
         * @param is_save_algo_info 是否保存算法信息
         * @param is_location_first 是否先定位
         * @data 2024.12.24
         * <AUTHOR>
         */
        void CurSelectedComponentRun(const jrsdata::AlgoEventParamPtr& param_,bool is_save_algo_info = false,bool is_location_first = false);

        /**
         * @fun  CurSelectedPartNumbRun
         * @brief 执行当前选中元件同一料号检测.
         * @param param_ 算法事件参数
         * @data 2024.12.24
         * <AUTHOR>
         */
        void CurSelectedPartNumbRun(const jrsdata::AlgoEventParamPtr& param_, bool is_location = false);

        /**
         * @fun SaveAlgoExecuteParam 
         * @brief 保存算法执行时候的参数
         * @param detect_result [IN] 当前算检测结果
         * @param component_ [IN] 当前元件信息
         * @param algo_name_ [IN] 当前算法名称
         * @param start_time_ [IN] 当前算法开始时间
         * <AUTHOR> 
         * @date 2025.3.4
         */
        void SaveAlgoExecuteParam(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_result,const jrsdata::Component&component_,const std::string& algo_name_,const std::string& start_time_,const std::string& algo_param_);
        /**
         * @fun GetDetectWindowDetectResult 
         * @brief 根据元件名+检测框名称获取指定的算法检测结果
         * @param component_name[IN] 元件名称
         * @param detect_window_name[IN] 检测框名称
         * @return  返回对应的算法结果
         * <AUTHOR>
         * @date 2025.1.19
         */
        OperatorParamBasePtr GetDetectWindowDetectResult(const std::string& component_name, const std::string& detect_window_name);

        /**
         * @fun SetDetectWindowDetectResult 
         * @brief 将检测框的结果保存到map中
         * @param component [IN] 当前被检测的元件
         * @param detect_window_name [IN] 检测框名称
         * @param algo_result [IN] 检测框检测结果
         * <AUTHOR>
         * @date 2025.1.19
         */
        void SetDetectWindowDetectResult(const jrsdata::Component& component,const std::string& detect_window_name, OperatorParamBasePtr algo_result);

        /**
         * @fun SaveComponentDetectStatus 
         * @brief 保存元件检测结果
         * @param component
         * <AUTHOR>
         * @date 2025.1.20
         */
        void SaveComponentDetectStatus(const jrsdata::Component& component);

        /**
         * @fun UpdateAlgoResultToRender 
         * @brief 将算法检测的结果状态在render上更新，主要是更新检测框的颜色 ok:绿色，ng红色
         * @param result_algo [IN] 单个算法的检测结果
         * <AUTHOR>
         * @date 2025.1.20
         */
        void UpdateAlgoResultToRender(const OperatorParamBasePtr& result_algo,const std::string& subboard_name,const std::string& component_name,const std::string& detect_window_name);

        /**
         * @fun GetCurSelectComponentEntity 
         * @brief 获取当前选中的元件信息
         * @param param_ [IN] OperateViewParam
         * @param entity [IN] 元件库单个元件信息
         * <AUTHOR>
         * @date 2025.3.27
         */
        void GetCurSelectComponentEntity(const jrsdata::ViewParamBasePtr& param_,jrsdata::ComponentEntity& entity);
        
        /**
         * @fun GetLocationModelMat 
         * @brief 获取定位组的定位结果，即定位旋转矩阵
         * @param component_temp  [IN]当前元件
         * @param detect_models [OUT] 算法模型组,需要将使用过的location的算法组删掉，不再检测
         * @param detect_win_exec_params [OUT] 检测框执行信息
         * @param res_src_matrix [OUT] 将检测区域恢复到大图上的恢复矩阵
         * @param start_time [IN] 开始检测时间
         * @param is_save_algo_info [IN] 是否保存算法检测参数信息
         * <AUTHOR>
         * @date 2025.3.19
         */
        std::pair<cv::Mat, std::vector<cv::Mat>> GetLocationModelMat(const jrsdata::Component& component_temp,
            std::unordered_map<std::string/**< 算法组名 */, jrsdata::DetectModel>& detect_models,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
            cv::Mat& res_src_matrix,
            const std::string& start_time,
            bool is_save_algo_info);

        /**
         * @fun ProcessDetectionWindows 
         * @brief 处理检测框
         * @param component_temp 
         * @param detect_models
         * @param detect_win_exec_params
         * @param res_src_matrix
         * @param start_time
         * @param is_save_algo_info
         * <AUTHOR>
         * @date 2025.3.20
         */
        void ProcessDetectionWindows(jrsdata::Component& component_temp,
            std::unordered_map<std::string/**< 算法组名 */, jrsdata::DetectModel>& detect_models,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
            cv::Mat& res_src_matrix,
            cv::Mat& locate_matrix,
            std::vector<cv::Mat>& base_plane_mask_,
            const std::string& start_time,
            bool is_save_algo_info);
       /**
        * @fun ExecuteDetections 
        * @brief 执行检测框
        * @param sorted_detect_wins 根据关联关系排序后的检测框
        * @param detect_win_exec_params 检测框算法执行信息
        * @param correction_matrix 矫正矩阵
        * @param trans_to_board_matrix 恢复到大图上的转换矩阵
        * @param result_trans_matrixes
        * @param component_temp
        * @param start_time
        * @param is_save_algo_info
        * @return 
        * <AUTHOR>
        * @date 2025.4.1
        */
        bool ExecuteDetections(const std::vector<std::vector<jrsdata::DetectWindow>>& sorted_detect_wins,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
            const cv::Mat& correction_matrix,
            const cv::Mat& trans_to_board_matrix,
            std::vector<cv::Mat>& base_plane_mask,
            std::unordered_map<int, cv::Mat>& result_trans_matrixes,
            const jrsdata::Component& component_temp,
            const std::string& start_time,
            bool is_save_algo_info);

        /**
         * @fun UpdateSelectedComponentAlgoResult 
         * @brief 更新当前选择的元件算法检测结果状态
         * @param param_ 当前选中的元件的信息
         * <AUTHOR>
         * @date 2025.3.20
         */
        void UpdateSelectedComponentAlgoResult(const jrsdata::AlgoEventParamPtr& param_);

        /**
         * @fun GetSpeficAlgoParam 
         * @brief 获取指定算法框的算法参数
         * @param cur_select_detect_win [IN] 当前检测框的信息
         * @return  返回指定检测框的算法参数
         * <AUTHOR>
         * @date 2025.4.8
         */
        OperatorParamBasePtr GetSpeficAlgoParam(const jrsdata::DetectWindow& cur_select_detect_win);

        /**
         * @fun UpdateDetectWinView
         * @brief 刷新检测框(算法列表和render)
         * <AUTHOR>
         * @date 2025.4.8
         */
        void UpdateDetectWinView(std::string event_name);// 获取检测框结果数据

		/**
		 * @fun ConvertDetectResult 
		 * @brief 获取检测框结果数据
		 * @param param_ [IN][OUT] 数据结构体
		 * @date 2025.5.19
		 * <AUTHOR>
		 */
        void ConvertDetectResult(const jrsdata::ViewParamBasePtr& param_);
 };
    using OperateControllerPtr = std::shared_ptr<OperateController>;
}
#endif // ! OPERATECONTRLLER_H
