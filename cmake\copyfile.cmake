#主要用于将运行时所需要的三方库的文件拷贝到运行目录下
#拷贝到debug目录下
file(COPY 
		# wps风格主界面生成三方库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/saribbon/lib/debug/SaribbonBard.dll
		# wps风格主界面生成三方库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/saribbon/lib/debug/SaribbonBard.dll
    # wps风格主界面生成三方库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/saribbon/lib/debug/SaribbonBard.dll
    #vld检测内存泄漏的，只有在debug模式下才生成
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/vld/lib/dbghelp.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/vld/lib/vld_x64.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/vld/lib/Microsoft.DTfW.DHL.manifest
    #uchardet
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/uchartdet/lib/debug/uchardet.dll
    #2d算子库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cv2d/lib/debug/cv2dd.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algo/algoplugin/ocr/weights   
    # onnx
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime_providers_shared.dll
    # 高度基面校准库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/heightbasecorrect/lib/debug/BasePlaneProject.dll
    #配置文件夹拷贝
    ${CMAKE_CURRENT_SOURCE_DIR}/config
    # ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/mark/bin/markd.dll
    DESTINATION ../bin/Debug 
)


#拷贝到release目录下
file(COPY 
    # wps风格主界面生成三方库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/saribbon/lib/release/SaribbonBar.dll
    #uchardet
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/uchartdet/lib/release/uchardet.dll
    #2d算子库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cv2d/lib/release/cv2d.dll
    # onnx
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime_providers_shared.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algo/algoplugin/ocr/weights
    #配置文件夹拷贝
    ${CMAKE_CURRENT_SOURCE_DIR}/config  

    # 高度基面校准库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/heightbasecorrect/lib/release/BasePlaneProject.dll
    DESTINATION ../bin/Release
    )

#拷贝到RelWithDebInfo目录下
file(COPY 
    # wps风格主界面生成三方库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/saribbon/lib/release/SaribbonBar.dll
    #2d算子库
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cv2d/lib/release/cv2d.dll
    # onnx
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime.dll
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/ort_install_1.18/lib/onnxruntime_providers_shared.dll
    #配置文件夹拷贝
    ${CMAKE_CURRENT_SOURCE_DIR}/config  
    # ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/mark/bin/mark.dll
    DESTINATION ../bin/RelWithDebInfo
    )

#算法单独拷贝
file(COPY
${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algo

DESTINATION ../bin/Debug/config

)

file(COPY
${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algo

DESTINATION ../bin/Release/config

)

file(COPY
${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algo

DESTINATION ../bin/RelWithDebInfo/config

)


# 3D 显示控件
file(GLOB DLL_FILES_3D_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/jrsvtk3dshowmodule/lib/debug/*.dll")
file(GLOB DLL_FILES_3D_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/jrsvtk3dshowmodule/lib/release/*.dll")

set (DLL_FILE_DEBUG_PATH   "${CMAKE_CURRENT_SOURCE_DIR}/bin/debug")
set (DLL_FILE_RELEASE_PATH   "${CMAKE_CURRENT_SOURCE_DIR}/bin/release")
set (DLL_FILE_RELEASEWITHDEBUG_PATH   "${CMAKE_CURRENT_SOURCE_DIR}/bin/relwithdebinfo")

# 复制3d显示模块dll到输出目录

foreach(DLL_FILE ${DLL_FILES_3D_DEBUG})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_DEBUG_PATH})
endforeach()

foreach(DLL_FILE ${DLL_FILES_3D_RELEASE})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASE_PATH})
endforeach()

foreach(DLL_FILE ${DLL_FILES_3D_RELEASE})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASEWITHDEBUG_PATH})
endforeach()


#成像模块
file(GLOB DLL_FILES_STRUCTLIGHT_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/jrsstructlightcamera/lib/debug/*.dll")
file(GLOB DLL_FILES_STRUCTLIGHT_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/jrsstructlightcamera/lib/release/*.dll")
file(GLOB DLL_FILES_STRUCTLIGHT_RELEASEWITHDEBINFO "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/jrsstructlightcamera/lib/relwithdebinfo/*.dll")



# 复制成像模块dll到输出目录
foreach(DLL_FILE ${DLL_FILES_STRUCTLIGHT_DEBUG})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_DEBUG_PATH})
endforeach()

foreach(DLL_FILE ${DLL_FILES_STRUCTLIGHT_RELEASE})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASE_PATH})
endforeach()

foreach(DLL_FILE ${DLL_FILES_STRUCTLIGHT_RELEASEWITHDEBINFO})
    file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASEWITHDEBUG_PATH})
endforeach()

# 算法依赖dll

# file(GLOB DLL_FILES_ALGODEPEND_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algodependdll/debug/*.dll")
# file(GLOB DLL_FILES_ALGODEPEND_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/algodependdll/release/*.dll")



# # 复制算法依赖dll到输出目录
# foreach(DLL_FILE ${DLL_FILES_ALGODEPEND_DEBUG})
#     file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_DEBUG_PATH})
# endforeach()

# foreach(DLL_FILE ${DLL_FILES_ALGODEPEND_RELEASE})
#     file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASE_PATH})
# endforeach()

# foreach(DLL_FILE ${DLL_FILES_ALGODEPEND_RELEASE})
#     file(COPY ${DLL_FILE} DESTINATION ${DLL_FILE_RELEASEWITHDEBUG_PATH})
# endforeach()

 