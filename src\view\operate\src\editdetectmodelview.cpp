﻿//STD
#include <memory>
//QT
#pragma warning(push, 1)
#include "ui_editdetectmodelview.h"
#include "../../view/operate/colorwheel/interface/colorwidget.h"
#include <QtWidgets/qmessagebox.h>
#include <qdatetime.h>
#pragma warning(pop)
//Custom
#include "editdetectmodelview.h"
#include "texthorizontalalignedtabbar.h"
#include "regionlisttabwidget.h"
#include "regionlistwidget.h"
#include "regionwidget.h"
#include "templateview.h"
#include "imageprocessalgo.h"
#include "viewdefine.h"
#include "operatorviewbase.h"
#include "dlgcreatedetectwindow.h"
#include "algoexecuteparamprocess.h"
#include "paramoperator.h"
#include "jsonoperator.hpp"

//third


using namespace jrsoperator;
using namespace jrsdata;
using namespace jrsaoi;


//bool is_render_select = false;

EditDetectModelView::EditDetectModelView(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::EditDetectModelView)
{
    ui->setupUi(this);
    Init();
}

EditDetectModelView::~EditDetectModelView()
{
    DisconnectSlots();
    if (det_model_tab)
    {
        delete det_model_tab;
        det_model_tab = nullptr;
    }
    QStackedWidget* stackedWidget = ui->stackedWidget_algo_view_list;
    while (stackedWidget->count() > 0)
    {
        QWidget* widget = stackedWidget->widget(0);
        stackedWidget->removeWidget(widget);
        widget->setParent(nullptr); // 解除父子关系
    }
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void EditDetectModelView::SlotUpdate(ViewParamBasePtr param_)
{
    auto& event_name = param_->event_name;

    if (event_name == OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (param.get() == nullptr || !param->cur_select_component || !param->cur_select_component_unit)
        {
            return;
        }
        //! 只有切换元件了才需要更新

        if (cur_component_name == param->cur_select_component->component_name || param->cur_select_component->component_part_number == cur_component_part_num)
        {
            QString set_tab_name;
            //! 因为当前选中元件的时候会默认选中body算法组
            //! 但是如果我当前已经选中了元件中的其他组的算法
            //! 然后点击选中元件时，不需要切换到body组 by zhangyuyu 2025.5.17
            
            if (!det_model_tab)
            {
                JRSMessageBox_ERR("OperateView", "算法列表tabwidget为空，请切换元件", jrscore::MessageButton::Ok);
                Log_INFO("算法列表tabwidget为空，请切换元件");
                return;
            }
            if (param->cur_select_component_unit->unit_group_name.find("body") != std::string::npos)
            {
                auto current_model_ptr = det_model_tab->GetCurrentModel();
                if (current_model_ptr)
                {
                    set_tab_name = current_model_ptr->GetModelName();
                }
                else
                {
                    return;
                }
            }
            else
            {
                set_tab_name = QString::fromStdString(param->cur_select_component_unit->unit_group_name);
            }
            det_model_tab->SetCurSelectedTable(set_tab_name);
            return;
        }
        else
        {
            cur_component_name = param->cur_select_component->component_name;
            cur_component_part_num = param->cur_select_component->component_part_number;
        }

        RefereshAlgoList(param);

    }
    else if (event_name == jrsaoi::OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME ||
        event_name == jrsaoi::OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME ||
        event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR ||
        event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR ||
        event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE ||
        event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE ||
        event_name == jrsaoi::SHORTCUT_ACT_DETECT_COPY ||
        event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_DETECT_COPY ||
        event_name == jrsaoi::SHORTCUT_ACT_DETECT_ACTION ||
        event_name == jrsaoi::SHORTCUT_ACT_ROTATE_90_ALL)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (param.get() == nullptr || !param->cur_select_component)
        {
            return;
        }
        RefereshAlgoList(param);

        if (event_name == jrsaoi::OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME)
        {
            if (param->cur_select_component_unit == nullptr)
            {
                return;
            }
            // 修改检测框尺寸
            cur_select_detect_win.width = param->cur_select_component_unit->width;
            cur_select_detect_win.height = param->cur_select_component_unit->height;

            // 自动加算法框时设置算法分母
            SlotApplyStdValueParam();
        }
    }
    else if (event_name == OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param)
        {
            return;
        }

        switch (param->data_operate_type)
        {
        case DataUpdateType::ADD_DATA:
        {
            for (auto& template_data : param->templates)
            {
                template_view->AddTemplateModelToList(template_data, param->cur_select_component->angle);
            }
            break;
        }
        }
    }
    else if (event_name == OPERATE_UPDATE_DET_WIN_VIEW_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param)
        {
            return;
        }
        switch (param->data_operate_type)
        {
        case DataUpdateType::ADD_DATA:
        {
            auto const& det_param = *param->cur_select_detect_win;
            DetectWindowItemValue det_value;
            det_value = DetectWindow2DetectWindowItemValue(det_param);
            UpdateAlgoView(param);
            det_model_tab->AddDetectWinItem(det_value);

            // 修改检测框尺寸
            cur_select_detect_win = *param->cur_select_detect_win;

            // 自动加算法框时设置算法分母
            SlotApplyStdValueParam();
            break;
        }
        case DataUpdateType::DELETE_DATA:
        {
            auto w = det_model_tab->currentWidget();
            auto list = dynamic_cast<DetectWindowListWidget*>(w);
            if (!list)
            {
                return;
            }

            for (const auto& name : param->delete_detect_win_name_list)
            {
                list->DeleteDetectWindowItem(QString::fromLocal8Bit(name.c_str()));
                if (list->count() == 0)
                {
                    {
                        ClearAlgoView();
                        template_view->ClearAllData();
                    }
                }
            }
            break;
        }
        case DataUpdateType::SELECT_DATA:
        {

            det_model_tab->SetCurSelectedDetectWinItemByWinName(param->cur_select_detect_win->name, param->cur_select_detect_win->model_name);

            if (param->region_mat.empty() == false)
            {
                cur_detect_window_name = param->cur_select_detect_win->name;
                template_view->Update(param);
            }
            UpdateAlgoView(param);

            if (!param->operator_param || param->operator_param->output_detect_rects.empty())
            {
                break;
            }
            SetDetectWindowListState(param_);

            break;
        }
        case DataUpdateType::UPDATE_DATA:
        {
            if (param->region_mat.empty() == false)
            {
                cur_detect_window_name = param->cur_select_detect_win->name;
                template_view->Update(param);
            }
            UpdateAlgoView(param);

            // 修改检测框尺寸
            if (param->cur_select_detect_win)
            {
                cur_select_detect_win.width = param->cur_select_detect_win->width;
                cur_select_detect_win.height = param->cur_select_detect_win->height;
            }
            break;
        }
        default:
        {
            break;
        }
        }
    }
    else if (event_name == OPERATE_UPDATE_PAD_WIN_PARAM_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param)
        {
            return;
        }

        if (!param->cur_select_component_unit || param->cur_select_component_unit->unit_group_name.empty())
        {
            Log_ERROR("读取元件组名称失败，请检查");
            return;
        }

        switch (param->data_operate_type)
        {
        case DataUpdateType::ADD_DATA:
        {
            det_model_tab->CreateModel(QString::fromStdString(param->cur_select_component_unit->unit_group_name), TypeToQString(param->cur_select_component_unit->unit_type));
            break;
        }
        case DataUpdateType::DELETE_DATA:
        {
            if (param->cur_select_spec_and_detect_region == nullptr)
            {
                return;
            }
            std::vector<std::string> table_names;
            det_model_tab->GetModelTableNames(table_names);
            for (auto& name : table_names)
            {
                auto iter = param->cur_select_spec_and_detect_region->detect_models.find(name);
                if (iter == param->cur_select_spec_and_detect_region->detect_models.end())
                {
                    det_model_tab->DeleteModel(QString::fromLocal8Bit(name.c_str()));
                }
            }
            break;
        }
        case DataUpdateType::SELECT_DATA:
        {
            det_model_tab->SetCurSelectedTable(QString::fromLocal8Bit(param->cur_select_component_unit->unit_group_name.c_str()));
            break;
        }
        case DataUpdateType::UPDATE_DATA:
        {

            break;
        }
        }
    }
    else if (event_name == RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param || param->cur_select_detect_win == nullptr)
        {
            return;
        }
        cur_select_detect_win = *param->cur_select_detect_win;

        det_model_tab->SetCurSelectedDetectWinItemByWinName(param->cur_select_detect_win->name, param->cur_select_detect_win->model_name);
    }
    else if (event_name == OPERATE_UPDATE_ALGO_DETECT_RESULT_EVENT_NAME)
    {

        //！建模时将当前检测的算法数值结果，判定结果显示到算法UI上 by zhangyuyu 2024.11.29
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param || !param->operator_param)
        {
            Log_ERROR("param_ 不是 AlgoEventParam 类型或为空");
            return;
        }
        //! 更新列表状态
        SetDetectWindowListState(param_);

        //! 如果当前检测框不是当前选中的检测框，直接返回，不更新算法界面
        if (param->current_update_result_state.name != cur_detect_window_name)
        {
            return;
        }
        int index = ui->stackedWidget_algo_view_list->currentIndex();
        if (index < 0 || index >= ui->stackedWidget_algo_view_list->count())
        {

            Log_ERROR("更新算法界面失败，当前没有选中的检测框");
            return;
        }
        //UpdateAlgoView(param);
        auto* current_qwidget = ui->stackedWidget_algo_view_list->currentWidget();

        if (!current_qwidget)
        {
            return;
        }
        auto* current_algo_view = dynamic_cast<jrsoperator::OperatorViewBase*>(current_qwidget);

        if (!current_algo_view)
        {

            return;
        }
        current_algo_view->UpdateViewParam(param->operator_param);



    }
    else if (event_name == OPERATE_CREATE_TEMPLATE_BY_DRAW_REGION_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param)
        {
            return;
        }
        std::string ipe_param = "";
        template_view->GetCurIPEParam(ipe_param);

        param->data_operate_type = jrsdata::DataUpdateType::ADD_DATA;
        param->template_color_param = ipe_param;
        param->event_name = REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME;
        SigEditAlgoUpdate(param);
    }
    else if (event_name == OPERATE_UPDATE_IPE_PROCESS_IMAGE_EVENT_NAME)
    {
        auto param = std::dynamic_pointer_cast<AlgoEventParam>(param_);
        if (!param)
        {
            return;
        }

        if (param->region_mat.empty() == false)
        {
            cur_detect_window_name = param->detect_win.name;
            template_view->UpdataInputImage(param->region_mat, param->light_type);
        }
    }
    else if (event_name == MACHINE_PARAM_UPDATE_EVENT)
    {
        view_param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
        std::string path = GetComponentPath();
        if (path == "")
        {
            return;
        }
        // 初始化元件库弹框
        if (component_view == nullptr)
        {
            component_view = new ComponentLibView(path, this);
            QVBoxLayout* layout = new QVBoxLayout(dialog);
            layout->addWidget(component_view);

            connect(component_view, &ComponentLibView::SigComponent, this, [this](const jrsdata::ViewParamBasePtr& param) {
                if (param->event_name == jrsaoi::MACHINE_PARAM_SAVE_EVENT)
                {
                    // 更新选择的元件库分类名称
                    UpdateComponentSelectName(component_view->GetComponentLibName());
                }
                else
                {
                    SigUpdateOperator(param); // 这个信号会通过operate发送到EventCenter，通知Datamanager保存
                }
                });

            // 设置元件库地址
            component_view->SetComponentLibName(GetComponentSelectName());
        }
        else
        {
            // 需要修改元件库地址
            component_view->SetComponentLibFolderPath(path);

            // 设置元件库地址
            component_view->SetComponentLibName(GetComponentSelectName());
        }

        // 算法比例参数初始化
        ui->param_ratio_name->clear();
        std::string algo_param = GetAlgoRatioList();
        algo_spec->InitView(algo_param);
        if (algo_param != "")
        {
            JSON arr = JSON::parse(algo_param);
            if (arr.is_array())
            {
                for (size_t i = 0; i < arr.size(); i++)
                {
                    JSON obj = arr[i];
                    if (obj.contains("name"))
                    {
                        std::string item_name = obj["name"].get<std::string>();
                        ui->param_ratio_name->addItem(QString::fromStdString(item_name));
                    }
                }
                if (arr.size() > 1)
                {
                    ui->param_ratio_name->setCurrentIndex(1);
                }
            }
        }
    }
    else if (event_name == OPERATE_COMPONENT_GET_CURSELLECT_COMPONENT_EVENT_NAME ||
        event_name == OPERATE_COMPONENT_LOAD_CURSELLECT_COMPONENT_EVENT_NAME ||
        event_name == OPERATE_COMPONENT_GET_ALL_COMPONENT_EVENT_NAME ||
        event_name == OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME)
    {
        if (!component_view)
        {
            JRSMessageBox_WARN("警告", "元件库界面为初始化成功，指针为空!", jrscore::MessageButton::Ok);
        }
        component_view->UpdateView(param_);
    }
    else if (event_name == jrsaoi::PROJECT_READ_EVENT_NAME)
    {
        //! 读取工程的时候清空算法列表和检测框
        det_model_tab->Clear(true);
        ui->stackedWidget_algo_view_list->setCurrentWidget(algo_view_list["None"]);
        cur_component_name = "";
        cur_component_part_num = "";
        cur_detect_window_name = "";
        cur_select_detect_win = jrsdata::DetectWindow();


    }


}

void EditDetectModelView::UpdateAlgoView(jrsdata::ViewParamBasePtr _param)
{
    auto param = std::dynamic_pointer_cast<AlgoEventParam>(_param);
    if (!param)
    {
        return;
    }

    if (param->cur_select_detect_win == nullptr || param->cur_select_detect_win->algorithms.empty())
    {
        ui->stackedWidget_algo_view_list->setCurrentWidget(algo_view_list["None"]);
    }
    else
    {
        std::string algo_name = param->cur_select_detect_win->algorithms[0].detect_algorithm_name;
        auto iter = algo_view_list.find(algo_name);
        if (iter != algo_view_list.end())
        {
            ui->stackedWidget_algo_view_list->setCurrentWidget(iter->second);
            auto current_algo_view = dynamic_cast<jrsoperator::OperatorViewBase*>(iter->second);
            if (current_algo_view && param->operator_param.get())
            {
                current_algo_view->UpdateViewParam(param->operator_param);
            }
        }
        else
        {
            ui->stackedWidget_algo_view_list->setCurrentWidget(algo_view_list["None"]);
        }
    }
}

void EditDetectModelView::ShowTemplateView()
{
    //template_view->show();
    if (template_view->isHidden())
    {
        template_view->setHidden(false);
    }
    else
    {
        template_view->setHidden(true);
    }
}

int EditDetectModelView::SetEditAlgorithmViewDefaultParam(const EditAlgorithmViewDefaultParam& _param)
{
    resolution_x = _param.resolution_x;
    resolution_y = _param.resolution_y;
    algo_name_list = _param.algo_name_list;
    defect_list = _param.defect_list;
    algo_view_list = _param.algo_view_list;
    algo_spec_param_map = _param.algo_spec_param_map;
    dlg_create_detect_window->SetAlgoList(algo_name_list);
    dlg_create_detect_window->SetDefectList(defect_list);
    det_model_tab->SetDefectList(defect_list);
    det_model_tab->SetAlgoList(algo_name_list);

    QWidget* widget_empty = new QWidget();
    algo_view_list.insert({ "None",widget_empty });

    while (ui->stackedWidget_algo_view_list->count() > 0)
    {
        QWidget* widget = ui->stackedWidget_algo_view_list->widget(0);
        ui->stackedWidget_algo_view_list->removeWidget(widget);
        delete widget;
    }

    for (auto& view : algo_view_list)
    {
        ui->stackedWidget_algo_view_list->addWidget(view.second);

    }

    ui->stackedWidget_algo_view_list->setCurrentWidget(algo_view_list["None"]);

    // 添加多检测框界面初始化
    multi_detect_window->IniAlgorithmView(algo_name_list.begin()->second);

    // 自动加框初始化
    multi_detect_window->InitView(GetMultiAlgoList());
    return 0;
}

void EditDetectModelView::SlotCreateRegion()
{
    RequestDrawDetectWindow();
}

void EditDetectModelView::SlotDeleteCurSelectedDetectWindow()
{
    auto w = det_model_tab->currentWidget();
    auto list = dynamic_cast<DetectWindowListWidget*>(w);
    if (!list)
    {
        return;
    }

    int row = list->currentRow();

    std::vector<std::string> delete_list;
    list->DeleteSelectedWidget(delete_list);

    for (auto& name : delete_list)
    {
        auto param = std::make_shared<AlgoEventParam>();
        param->detect_win.name = name;
        param->detect_win.model_name = det_model_tab->GetCurrentModel()->GetModelName().toLocal8Bit().constData();
        param->data_operate_type = DataUpdateType::DELETE_DATA;
        param->event_name = REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = "all";

        SigEditAlgoUpdate(param);
    }

    if (list->count() == 0)
    {
        ClearAlgoView();
        return;
    }

    if (row < list->count())
    {
        list->setCurrentRow(row);
    }
    else if (row > 0)
    {
        list->setCurrentRow(row - 1);
    }
}

void EditDetectModelView::SlotBindLogicalOR()
{
    auto w = det_model_tab->currentWidget();
    auto list = dynamic_cast<DetectWindowListWidget*>(w);
    if (!list)
    {
        return;
    }
    QString group_name = tr("组-%1").arg(QDateTime::currentDateTime().toString("ss"));
    list->BindSelectedDetectWindowItems(group_name);
}

void EditDetectModelView::SlotUnbindLogical()
{
    auto w = det_model_tab->currentWidget();
    auto list = dynamic_cast<DetectWindowListWidget*>(w);
    if (!list)
    {
        return;
    }
    list->UnBindSelectedDetectWindowItems();
}

void EditDetectModelView::SlotApplySpecParam()
{
    auto param = algo_spec->GetSpecRatioParam(ui->param_ratio_name->currentIndex());
    std::string current_spec_str;
    iguana::to_json(param, current_spec_str);
    FastUpdateSpecParam(current_spec_str, UpdateSpecParamType::OFFSET_TYPE);
}

void EditDetectModelView::SlotApplyStdValueParam()
{
    jrsdata::AlgoSpecDenominatorParam param;
    param.x_offset = resolution_x * std::min(cur_select_detect_win.width, cur_select_detect_win.height);
    param.y_offset = resolution_y * std::min(cur_select_detect_win.width, cur_select_detect_win.height);

    std::string current_spec_str;
    iguana::to_json(param, current_spec_str);
    FastUpdateSpecParam(current_spec_str, UpdateSpecParamType::STD_TYPE);
}

void EditDetectModelView::Init()
{
    // 历史元件库弹框
    dialog = new QDialog(this);
    dialog->setFixedSize(730, 380);
    component_view = nullptr;

    // 多个检测框弹框
    multi_detect_window = new MultiDetectWindow(this);
    multi_detect_window->setWindowTitle("JRSAOI");
    multi_detect_window->setFixedSize(480, 450);
    multi_detect_window->setWindowFlag(Qt::WindowStaysOnTopHint);

    // 参数管控弹框
    algo_spec = new AlgoSpecParam(this);
    algo_spec->setWindowTitle("JRSAOI");
    algo_spec->setFixedSize(915, 450);
    algo_spec->setWindowFlag(Qt::WindowStaysOnTopHint);

    det_model_tab = new DetectModelWidget();
    dlg_create_detect_window = new DlgCreateDetectWindow(this);
    dlg_create_detect_window->setWindowTitle(tr("选择算法"));
    ui->tab_region_layout->addWidget(det_model_tab);
    AddTemplate();
    ConnectSlots();
}

void EditDetectModelView::ConnectSlots()
{
    // 打开多个检测框
    connect(ui->btn_add_regions, &QPushButton::clicked, this, [=]() {
        multi_detect_window->show();
        });
    // 一键添加算法
    connect(multi_detect_window, &MultiDetectWindow::SigUpdate, this, [=](const jrsdata::ViewParamBasePtr& param) {
        SigUpdateOperator(param);
        });

    //! 应用当前选择的算法规格参数
    connect(ui->select_algo_ratio_btn, &QPushButton::clicked, this, &EditDetectModelView::SlotApplySpecParam);

    //！应用分母
    connect(ui->std_value_btn, &QPushButton::clicked, this, &EditDetectModelView::SlotApplyStdValueParam);

    //! 打开算法规格参数界面
    connect(ui->param_ratio_manager_btn, &QPushButton::clicked, this, [=]() {
        algo_spec->show();
        });
    // TODO 删除按钮 保存元件库的功能 by baron zhang 2025-07-11

    //// 打开元件库
    //connect(ui->open_component, &QPushButton::clicked, this, [=]() {
    //    dialog->show();
    //    });

    // 保存单个元件到元件库
    //connect(ui->save_component, &QPushButton::clicked, this, [=]() {
    //    auto param = std::make_shared<OperateViewParam>();
    //    param->event_name = OPERATE_COMPONENT_GET_CURSELLECT_COMPONENT_EVENT_NAME;
    //    param->module_name = OPERATE_MODULE_NAME;
    //    param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
    //    param->sub_name = OPERATE_LOGIC_SUB_NAME;
    //    SigUpdateOperator(param);
    //    });
    //// 加载元件库
    //connect(ui->load_component, &QPushButton::clicked, this, [=]() {
    //    auto param = std::make_shared<OperateViewParam>();
    //    param->event_name = OPERATE_COMPONENT_LOAD_CURSELLECT_COMPONENT_EVENT_NAME;
    //    param->module_name = OPERATE_MODULE_NAME;
    //    param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
    //    param->sub_name = OPERATE_LOGIC_SUB_NAME;
    //    SigUpdateOperator(param);
    //    });

    //TODO End

    // 自定义算法列表保存
    connect(multi_detect_window, &MultiDetectWindow::SigAlgoListChange, this, [=](const std::string& list) {
        SetMultiAlgoList(list);
        });

    // 算法比例参数保存
    connect(algo_spec, &AlgoSpecParam::SigAlgoRatioChange, this, [=](const std::string& list) {
        SetAlgoRatioList(list);
        });

    connect(ui->btn_add_region, &QPushButton::clicked, this, &EditDetectModelView::SlotCreateRegion);
    connect(ui->btn_delete_region, &QPushButton::clicked, this, &EditDetectModelView::SlotDeleteCurSelectedDetectWindow);
    connect(ui->btn_bind_logical_or, &QPushButton::clicked, this, &EditDetectModelView::SlotBindLogicalOR);
    connect(ui->btn_unbind_logical, &QPushButton::clicked, this, &EditDetectModelView::SlotUnbindLogical);
    connect(det_model_tab, &DetectModelWidget::SignalModelItemValChanged, this, [=](\
        const DetectWindowItemValue& det_win_info)
        {
            auto param = std::make_shared<AlgoEventParam>();

            param->detect_win = DetectWindowItemValue2DetectWindow(det_win_info);
            param->algorithm_param.detect_algorithm_name = det_win_info.algo_name.toLocal8Bit().constData();
            param->cur_algo_view = ui->stackedWidget_algo_view_list->currentWidget();
            param->data_operate_type = DataUpdateType::UPDATE_DATA;
            param->event_name = REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME;
            SigEditAlgoUpdate(param);
        });
    connect(det_model_tab, &DetectModelWidget::SigUpdateAllDetectStatus, this, [=](\
        const std::vector<DetectWindowItemValue>& all_det_win_info)
        {
            for (auto& value : all_det_win_info)
            {
                auto param = std::make_shared<AlgoEventParam>();

                param->detect_win = DetectWindowItemValue2DetectWindow(value);
                param->algorithm_param.detect_algorithm_name = value.algo_name.toLocal8Bit().constData();
                param->cur_algo_view = ui->stackedWidget_algo_view_list->currentWidget();
                param->data_operate_type = DataUpdateType::UPDATE_DATA;
                param->event_name = REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME;
                SigEditAlgoUpdate(param);
            }

        });

    connect(det_model_tab, &DetectModelWidget::SigDetectModelChange, this, [=](const QString& detect_model_name)
        {
            current_detect_model_name = detect_model_name.toStdString();
        }
    );
    connect(det_model_tab, &DetectModelWidget::SignalModelItemSelectedChanged, this, [=](const DetectWindowItemValue& det_win_val)
        {
            auto param = std::make_shared<AlgoEventParam>();
            param->detect_win = DetectWindowItemValue2DetectWindow(det_win_val);

            if (!det_win_val.algo_name.isEmpty())
            {
                auto algo_name_en = FindAlgoEnglishName(current_detect_model_name, det_win_val.algo_name.toStdString());

                if (algo_name_en)
                {
                    param->algorithm_param.detect_algorithm_name = *algo_name_en;

                }
                else
                {
                    JRSMessageBox_ERR("OperateView", "没有找到对应算法的中文名名称，请检查", jrscore::MessageButton::Ok);
                    Log_ERROR("没有找到对应算法的中文名名称，请检查！");
                    return;
                }
            }//为空则清空数据

            param->cur_algo_view = ui->stackedWidget_algo_view_list->currentWidget();
            param->data_operate_type = DataUpdateType::SELECT_DATA;
            param->event_name = REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME;
            param->module_name = OPERATE_MODULE_NAME;
            param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
            param->sub_name = OPERATE_RENDER_SUB_NAME;
            SigEditAlgoUpdate(param);
        });
}

void EditDetectModelView::DisconnectSlots()
{
    disconnect(ui->btn_add_region, &QPushButton::clicked, this, &EditDetectModelView::SlotCreateRegion);
    disconnect(ui->btn_delete_region, &QPushButton::clicked, this, &EditDetectModelView::SlotDeleteCurSelectedDetectWindow);
    disconnect(ui->btn_bind_logical_or, &QPushButton::clicked, this, &EditDetectModelView::SlotBindLogicalOR);
    disconnect(ui->btn_unbind_logical, &QPushButton::clicked, this, &EditDetectModelView::SlotUnbindLogical);
}

void EditDetectModelView::RequestDrawDetectWindow()
{
    auto model = det_model_tab->GetCurrentModel();
    if (!model)
    {
        return;
    }
    auto model_name = model->GetModelName().toLocal8Bit().toStdString();
    std::string algo_type = "body";
    if (model_name.find("pad") != std::string::npos)
    {
        algo_type = "pad";

    }
    dlg_create_detect_window->SetAlgoListShowByAlgoType(algo_type);
    if (dlg_create_detect_window->exec() == QDialog::Accepted)
    {
        auto param = std::make_shared<AlgoEventParam>();

        std::string algo_name;
        std::string defect_name;
        SubWindowType sub_window_type;
        dlg_create_detect_window->GetDetectWindowInfo(algo_name, defect_name, sub_window_type);
        param->detect_win.model_name = model->GetModelName().toLocal8Bit().toStdString();
        param->detect_win.id = model->count() + 1;
        param->detect_win.defect_name = defect_name;
        param->algorithm_param.detect_algorithm_name = algo_name;
        param->sub_wind_type = (int)sub_window_type;
        param->event_name = REQUEST_RENDER2D_CREATE_REGION_EVENT_NAME;
        emit SigEditAlgoUpdate(param);
    }
}

void EditDetectModelView::RequestDrawSubRegion()
{
    auto param = std::make_shared<AlgoEventParam>();
    param->algorithm_param.detect_algorithm_name = "test1";
    param->event_name = REQUEST_RENDER2D_CREATE_SUB_REGION_EVENT_NAME;
    emit SigEditAlgoUpdate(param);
}

void EditDetectModelView::AddTemplate()
{
    template_view = new TemplateView();
    template_view->setHidden(true);
    connect(template_view, &TemplateView::SignalUpdateTemplate, this, &EditDetectModelView::SigEditAlgoUpdate);

    //template_view->SetIPEParamChangedCallback([=](ColorParams& params, cv::Mat& bin_result)
    //    {
    //        (void)params;

    //        if (bin_result.empty())
    //        {
    //            return;
    //        }

    //        cv::Mat src_image;
    //        template_view->GetInputImage(src_image);

    //        //! 不是二值图不需要进行掩膜转换，就将bin_result返回
    //        if (bin_result.channels() == 1)
    //        {
    //            src_image = colorwheel::MaskImageByBinary(src_image, bin_result);

    //        }
    //        else
    //        {
    //            src_image = bin_result;
    //        }
    //        auto param = std::make_shared<AlgoEventParam>();
    //        param->detect_win.name = cur_detect_window_name;
    //        param->detect_win.model_name = det_model_tab->GetCurrentModel()->GetModelName().toStdString();
    //        param->region_mat = src_image;
    //        param->event_name = RENDER2D_SHOW_IMAGE_ALGO_EVENT_NAME;
    //        emit SigEditAlgoUpdate(param);
    //    });
}

void EditDetectModelView::ClearAlgoView()
{
    //QLayoutItem* child;
    //while ((child = ui->verticalLayout_algo_view->takeAt(0)) != 0)
    //{
    //    ui->verticalLayout_algo_view->removeWidget(child->widget());
    //    child->widget()->setParent(0);
    //    delete child;
    //    algo_param_view = nullptr;
    //}
}

std::string EditDetectModelView::GetComponentPath()
{
    if (view_param)
    {
        jrsdata::SettingParamMap setting_map = view_param->machine_param.machine_params_data;

        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        auto param_value = param_operate->GetSettingParamValueByName<std::string>(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH);
        if (param_value.empty())
        {
            return "";
        }

        JSON param_json = JSON::parse(param_value);
        if (param_json.contains("directory"))
        {
            return param_json["directory"].get<std::string>();
        }

    }
    return std::string();
}

std::string EditDetectModelView::GetComponentSelectName()
{
    if (view_param)
    {
        //jrsdata::SettingParamMap setting_map = view_param->machine_param.machine_params_data;
        //auto it = setting_map.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH);
        //if (it != setting_map.end())
        //{
        //    return it->second.param_exp; // 用参数解释保存的
        //}
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        return param_operate->GetSettingParamExplanByName(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH);
    }
    return std::string();
}

void EditDetectModelView::UpdateComponentSelectName(std::string name)
{
    if (view_param)
    {
        // 通知更新
        jrsdata::SettingViewParamPtr _setting_view_param = std::make_shared<jrsdata::SettingViewParam>();
        _setting_view_param->event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;

        _setting_view_param->module_name = OPERATE_MODULE_NAME;
        _setting_view_param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        _setting_view_param->sub_name = OPERATE_LOGIC_SUB_NAME;
        _setting_view_param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
        _setting_view_param->machine_param.machine_params_data = view_param->machine_param.machine_params_data;
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        auto path_str = param_operate->GetSettingParamValueByName<std::string>(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH);
        if (path_str.empty())
        {
            return;
        }
        param_operate->ReplaceSettingParamValueByName(ParamLevel::MACHINE,
            jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH, path_str, name);
        SigUpdateOperator(_setting_view_param);
    }
}

std::string EditDetectModelView::GetMultiAlgoList()
{
    if (view_param)
    {
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        return param_operate->GetSettingParamValueByName<std::string>(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_MULTIALGO_LIST);

        //jrsdata::SettingParamMap setting_map = view_param->machine_param.machine_params_data;
        //auto it = setting_map.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MULTIALGO_LIST);
        //if (it != setting_map.end())
        //{
        //    return it->second.param_value;
        //}
    }
    return std::string();
}

void EditDetectModelView::SetMultiAlgoList(std::string algo)
{
    if (view_param)
    {
        // 通知更新
        jrsdata::SettingViewParamPtr _setting_view_param = std::make_shared<jrsdata::SettingViewParam>();
        _setting_view_param->event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;

        _setting_view_param->module_name = OPERATE_MODULE_NAME;
        _setting_view_param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        _setting_view_param->sub_name = OPERATE_LOGIC_SUB_NAME;
        _setting_view_param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
        //_setting_view_param->machine_param.machine_params_data = view_param->machine_param.machine_params_data;
        //jrsdata::SettingParamMap& setting_map = _setting_view_param->machine_param.machine_params_data;
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        param_operate->ReplaceSettingParamValueByName(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_MULTIALGO_LIST, algo);

        SigUpdateOperator(_setting_view_param);
    }
}

std::string EditDetectModelView::GetAlgoRatioList()
{
    if (view_param)
    {
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        return param_operate->GetSettingParamValueByName<std::string>(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGO_RATIO_LIST);
    }
    return std::string();
}

void EditDetectModelView::SetAlgoRatioList(std::string algo_ration)
{
    if (view_param)
    {
        // 通知更新
        jrsdata::SettingViewParamPtr _setting_view_param = std::make_shared<jrsdata::SettingViewParam>();
        _setting_view_param->event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;

        _setting_view_param->module_name = OPERATE_MODULE_NAME;
        _setting_view_param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        _setting_view_param->sub_name = OPERATE_LOGIC_SUB_NAME;
        _setting_view_param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
        auto param_operate = ParamOperator::GetInstance().GetParameterProcessInstance();
        param_operate->ReplaceSettingParamValueByName(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGO_RATIO_LIST, algo_ration);

        SigUpdateOperator(_setting_view_param);
    }
}

void EditDetectModelView::FastUpdateSpecParam(std::string current_spec_str, UpdateSpecParamType type)
{
    auto* current_qwidget = ui->stackedWidget_algo_view_list->currentWidget();

    if (!current_qwidget)
    {
        return;
    }
    auto* current_algo_view = dynamic_cast<jrsoperator::OperatorViewBase*>(current_qwidget);
    if (!current_algo_view)
    {

        return;
    }
    auto current_algo_name = current_algo_view->GetOperatorName();
    auto iter = algo_spec_param_map.find(current_algo_name);
    if (iter == algo_spec_param_map.end())
    {
        Log_INFO("算法：", current_algo_name.c_str(), "没有对应的规格参数映射表");

        return;
    }
    std::map<std::string, FastSetSpecParamInfo> spec_param_map;

    for (const auto& [key, value] : iter->second)
    {
        FastSetSpecParamInfo info;
        for (size_t i = 0; i < value.size(); i++)
        {
            auto spec_value_str = jrscore::ParseJson(current_spec_str, value[i]);
            if (spec_value_str.empty())
            {
                Log_INFO("算法：", current_algo_name.c_str(), type == UpdateSpecParamType::OFFSET_TYPE ? "规格项：" : "分母: ", key.c_str(), "   ", value[i].c_str(), "没有设置值");

                continue;
            }
            if (type == UpdateSpecParamType::OFFSET_TYPE)
            {
                info.offset_value = std::stof(spec_value_str);
            }
            else if (type == UpdateSpecParamType::STD_TYPE)
            {
                info.std_value = std::stof(spec_value_str);
            }
            spec_param_map.insert({ key,info });
        }
    }
    if (spec_param_map.empty())
    {
        Log_INFO("算法：", current_algo_name.c_str(), "没有设置参数");
    }
    current_algo_view->FastUpdateSpecParam(spec_param_map);
}

//void EditDetectModelView::DrawDetectWinRunResult(const std::vector<std::pair<bool, cv::RotatedRect>>& result)
//{
//    auto param = std::make_shared<AlgoEventParam>();
//    param->detect_win_results = result;
//    param->event_name = REQUEST_RENDER2D_DRAW_DETECT_RESULT_EVENT_NANE;
//    param->module_name = RENDER2D_MODULE_NAME;
//    param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
//    param->sub_name = OPERATE_RENDER_SUB_NAME;
//    emit SigEditAlgoUpdate(param);
//}

jrsdata::ComponentUnit::Type EditDetectModelView::GetComponentUnitType(const std::string& component_group_name, const PNDetectInfo* part_number_info_)
{
    for (auto& value : part_number_info_->units)
    {
        if (value.unit_group_name == component_group_name)
        {
            return value.unit_type;
        }
    }
    return jrsdata::ComponentUnit::Type();
}

void EditDetectModelView::SetDetectWindowListState(jrsdata::ViewParamBasePtr update_param_)
{
    auto param = std::dynamic_pointer_cast<AlgoEventParam>(update_param_);
    if (!param)
    {
        return;
    }

    DetectWindowItemValue item_val = DetectWindow2DetectWindowItemValue(param->current_update_result_state);
    item_val.excute_state = 1;
    bool result_status = std::none_of(param->operator_param->output_detect_rects.begin(), param->operator_param->output_detect_rects.end(),
        [](const auto& value)
        {
            return !value.status;
        }) && !param->operator_param->output_detect_rects.empty();
    if (!result_status)
    {
        item_val.excute_state = -1;

    }
    det_model_tab->SetDetectWinItemVal(item_val, item_val.model_name.toStdString());

    // 根据算法结果更新tab颜色
    det_model_tab->UpdateTabColorByAlgoResult(item_val.model_name.toStdString());
}

void EditDetectModelView::RefereshAlgoList(const jrsdata::AlgoEventParamPtr& param_)
{
    det_model_tab->Clear(true);

    bool is_no_algo = true;
    if (param_->cur_select_spec_and_detect_region)
    {
        for (const auto& detect_window_model : param_->cur_select_spec_and_detect_region->detect_models)
        {
            auto component_unit_type = GetComponentUnitType(detect_window_model.first, param_->cur_select_spec_and_detect_region);
            det_model_tab->CreateModel(QString::fromStdString(detect_window_model.first),
                TypeToQString(component_unit_type));
            for (const auto& detect_window : detect_window_model.second.detect_model)
            {
                if (detect_window.algorithms.empty())
                {
                    continue;
                }
                is_no_algo = false;
                DetectWindowItemValue det_value;
                det_value = DetectWindow2DetectWindowItemValue(detect_window);
                det_model_tab->UpdateView(detect_window_model.first, TypeToQString(component_unit_type), det_value);

            }
        }

    }

    template_view->ClearAllData();
    if (is_no_algo)
    {
        ui->stackedWidget_algo_view_list->setCurrentWidget(algo_view_list["None"]);

    }
    det_model_tab->SortTabsByCustomOrder();
    det_model_tab->SetFirstDetectWinSeleceted(param_->cur_select_component_unit->unit_group_name);
}


DetectWindowItemValue EditDetectModelView::DetectWindow2DetectWindowItemValue(const jrsdata::DetectWindow& detect)
{
    DetectWindowItemValue item_val;

    item_val.parent_window_name = QString::fromLocal8Bit(detect.parent_win_name.c_str());
    item_val.window_name = QString::fromLocal8Bit(detect.name.c_str());
    item_val.model_name = QString::fromLocal8Bit(detect.model_name.c_str());
    if (detect.algorithms.empty())
    {
        Log_ERROR("当前没有选中需要更新的算法，请检查！");
        return item_val;
    }
    auto algo_name_zh = FindAlgoChineseName(current_detect_model_name, detect.algorithms[0].detect_algorithm_name);
    if (algo_name_zh)
    {
        item_val.algo_name = QString::fromStdString(*algo_name_zh);

    }
    else
    {
        item_val.algo_name = "";
        JRSMessageBox_ERR("OperateView", "没有找到对应算法的英文名名称，请检查", jrscore::MessageButton::Ok);
        Log_ERROR("没有找到对应算法的英文名名称，请检查！");


    }
    item_val.group_name = QString::fromLocal8Bit(detect.group_name.c_str());
    item_val.defect_type_name = QString::fromStdString(detect.defect_name);
    item_val.is_enable = detect.enable;
    item_val.serach_size = detect.search_size;
    return item_val;
}

jrsdata::DetectWindow EditDetectModelView::DetectWindowItemValue2DetectWindow(const DetectWindowItemValue& detect_item)
{
    jrsdata::DetectWindow detect;
    detect.parent_win_name = detect_item.parent_window_name.toLocal8Bit().constData();
    detect.name = detect_item.window_name.toLocal8Bit().constData();
    detect.model_name = detect_item.model_name.toLocal8Bit().constData();
    detect.algorithms.resize(1);
    detect.algorithms[0].detect_algorithm_name = detect_item.algo_name.toLocal8Bit().constData();
    detect.group_name = detect_item.group_name.toLocal8Bit().constData();
    detect.defect_name = detect_item.defect_type_name.toUtf8();
    detect.enable = detect_item.is_enable;
    detect.search_size = detect_item.serach_size;
    return detect;
}

std::optional<std::string> EditDetectModelView::FindAlgoChineseName(const std::string& group_name, const std::string& en_name)
{

    auto group_iter = algo_name_list.find(group_name);
    if (group_iter != algo_name_list.end())
    {
        for (const auto& pair : group_iter->second)
        {
            if (pair.first == en_name)
            {
                return pair.second;
            }
        }
    }
    return std::nullopt;
}

std::optional<std::string> EditDetectModelView::FindAlgoEnglishName(const std::string& group_name, const std::string& zh_name)
{
    auto group_it = algo_name_list.find(group_name);
    if (group_it != algo_name_list.end())
    {
        for (const auto& pair : group_it->second)
        {
            if (pair.second == zh_name)
            {
                return pair.first;
            }
        }
    }
    return std::nullopt;

}


int EditDetectModelView::SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list)
{
    algo_name_list = _algo_name_list;
    defect_list = _defect_list;
    dlg_create_detect_window->SetAlgoList(algo_name_list);
    dlg_create_detect_window->SetDefectList(defect_list);
    det_model_tab->SetDefectList(defect_list);
    det_model_tab->SetAlgoList(algo_name_list);
    return 0;
}

