#ifndef _IREGULAR_MULTIPLE_BOARDS_BASE_H__
#define _IREGULAR_MULTIPLE_BOARDS_BASE_H__

#include "multipleboardsbase.h"
namespace jrsaoi
{
    class IrregularMultipleBoards :public MultipleBoardsBase
    {
    public:
        int MultipleBoardsUpdate(jrsdata::MultiBoardEventParamPtr param_) override;

        IrregularMultipleBoards();
        ~IrregularMultipleBoards();

    private:
        /**
         * @fun GenerateMultipleSubboard
         * @brief
         * @return
         * <AUTHOR>
         * @date 2025.4.27
         */
        int GenerateMultipleSubboard(jrsdata::MultiBoardEventParamPtr param_);
        /**
         * @fun ConfirmSelectSubboardComponent
         * @brief
         * @return
         * <AUTHOR>
         * @date 2025.4.27
         */
        int ConfirmSelectSubboardComponent(const jrsdata::MultiBoardEventParamPtr& param_);
        /**
        * @fun SelectSubboardLocation
        * @brief
        * @return
        * <AUTHOR>
        * @date 2025.4.27
        */
        int SelectSubboardLocation(const jrsdata::MultiBoardEventParamPtr& param_);
        /**
       * @fun ConfirmSubboardLocation
       * @brief
       * @return
       * <AUTHOR>
       * @date 2025.4.27
       */
        int ConfirmSubboardLocation(const jrsdata::MultiBoardEventParamPtr& param_);

        jrsdata::RegularParamPtr _regular_multiple_board_param_ptr;
    };
}


#endif//!_IREGULAR_MULTIPLE_BOARDS_BASE_H__
