#include "customgraphicscontrolpoint.h"
#include "customgraphicsobject.h"

#include "graphicseventparam.hpp"

void ControlPointSGTerminal::Response(const ResponseEventParam& param, GraphicsAbstract* const obj)
{
    auto sg = dynamic_cast<SGGraphics*>(obj);
    if (!sg)
        return;
    switch (static_cast<ControlPointType>(param.attr.type))
    {
    case ControlPointType::BORDER_POINT:
    {
        float xo = param.xend - param.xstart;
        float yo = param.yend - param.ystart;
        if (param.attr.id == 0)
        {
            auto new_p = sg->GetStart() + Vec2(xo, yo);
            sg->SetStart(new_p.x, new_p.y, true);
            sg->ComfirmPoint();
        }
        else if (param.attr.id == 1)
        {
            auto new_p = sg->GetEnd() + Vec2(xo, yo);
            sg->SetEnd(new_p.x, new_p.y, true);
            sg->ComfirmPoint();
        }
    }
    break;
    }
}

// double ControlPointSGTerminal::TryResponse(const float& x, const float& y, const float& min_dis) const
// {
//     return 0.0;
// }
