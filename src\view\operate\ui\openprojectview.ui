<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OpenProjectView</class>
 <widget class="QWidget" name="OpenProjectView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>450</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>打开工程</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QLabel" name="label_product">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>23</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>23</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background:rgb(172, 172, 172);
color:rgb(45, 45, 45);
</string>
       </property>
       <property name="text">
        <string>打开工程</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="title">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>路  径：</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="qline_project_path">
         <property name="styleSheet">
          <string notr="true">QLineEdit {
    border: 1px solid rgb(41, 57, 85); 
    border-radius: 3px;  
    background: white;   
    selection-background-color: green; 
    font-size: 16px
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_select_path">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="toolTip">
          <string>选择路径</string>
         </property>
         <property name="text">
          <string>.....</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTableView" name="list_project"/>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLineEdit" name="qline_project_select_path">
         <property name="styleSheet">
          <string notr="true">QLineEdit {
    border: 1px solid rgb(41, 57, 85); 
    border-radius: 3px;  
    background: white;   
    selection-background-color: green; 
    font-size: 16px
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_confirm">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string/>
         </property>
         <property name="text">
          <string>确认</string>
         </property>
         <property name="icon">
          <iconset>
           <normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>28</width>
           <height>28</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_cancel">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string/>
         </property>
         <property name="text">
          <string>取消</string>
         </property>
         <property name="icon">
          <iconset>
           <normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>28</width>
           <height>28</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
