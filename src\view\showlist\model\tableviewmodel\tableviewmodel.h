/*****************************************************************//**
 * @file   tableviewmodel.h
 * @brief 结果TableView的Model
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef TABLEVIEWMODEL_H
#define TABLEVIEWMODEL_H
 //QT
#include <QObject>
#include <QStandardItemModel>
#include <QStringList>
#include <QVariant>
#include <QModelIndex>
#include <QColor>
//CUSTOM
#include "datadefine.hpp"
#include <vector>
#include <map>

class TableViewModel : public QStandardItemModel
{
    Q_OBJECT
public:
    /**
     * @fun index
     * @brief 获取模型索引。
     * @param row 行号。
     * @param column 列号。
     * @param parent 父索引。
     * @return QModelIndex 模型索引。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QModelIndex index(int row, int column, const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun parent
     * @brief 获取父索引。
     * @param child 子索引。
     * @return QModelIndex 父索引。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QModelIndex parent(const QModelIndex& child) const override;
    /**
     * @fun rowCount
     * @brief 获取行数。
     * @param parent 父索引。
     * @return int 行数。
     * @date 2025.02.25
     * <AUTHOR>
     */
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun columnCount
     * @brief 获取列数。
     * @param parent 父索引。
     * @return int 列数。
     * @date 2025.02.25
     * <AUTHOR>
     */
    int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun data
     * @brief 获取单元格数据。
     * @param index 模型索引。
     * @param role 角色。
     * @return QVariant 包含单元格数据的 QVariant。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    /**
     * @fun headerData
     * @brief 获取表头数据。
     * @param section 表头部分。
     * @param orientation 方向。
     * @param role 角色。
     * @return QVariant 包含表头数据的 QVariant。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    /**
     * @fun SetDataModel
     * @brief 设置数据模型，并根据数据类型更新内部数据结构。
     * @details 根据传入的数据向量，将数据分类为子板、料号或元件，并更新对应的列表。
     *          同时，清除旧数据并更新表头。
     * @param vector_data 传入的数据向量，包含不同类型的显示参数。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void SetDataModel(std::vector<ShowTableParamBasePtr> vector_data);
    /**
     * @fun GetDataModel
     * @brief 获取当前数据模型。
     * @param vector_data 用于存储当前数据模型的向量。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void GetDataModel(std::vector<ShowTableParamBasePtr>& vector_data);
    /**
     * @fun GetHeaderLabels
     * @brief 获取表头标签。
     * @return QStringList 包含表头标签的字符串列表。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QStringList GetHeaderLabels();
    /**
     * @fun CheckOverValue
     * @brief 检查给定的行和列是否超出当前数据模型的有效范围。
     * @details 根据不同的数据类型（子板、料号、元件）检查行是否超出范围。
     *          如果列超出表头范围，也返回 true。
     * @param row 行号。
     * @param column 列号。
     * @return bool 是否超出有效范围。
     * @date 2025.02.25
     * <AUTHOR>
     */
    bool CheckOverValue(int row, int column) const;
    /**
     * @fun BackgroundColor
     * @brief 根据行号获取背景颜色。
     * @details 根据数据类型和行号获取对应的检测结果状态，并返回相应的背景颜色。
     * @param row 行号。
     * @return QVariant 包含背景颜色的 QVariant。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant BackgroundColor(int row) const;
    /**
     * @fun ItemData
     * @brief 根据行号和列号获取单元格数据。
     * @details 根据数据类型和列号获取对应的字符串数据。
     * @param row 行号。
     * @param column 列号。
     * @return QVariant 包含单元格数据的 QVariant。
     * @date 2025.02.25
     * <AUTHOR>
     */
    QVariant ItemData(int row, int column) const;
    /**
     * @fun InitData
     * @brief 初始化数据模型。
     * @details 清空所有数据结构，并初始化结果颜色映射。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void InitData();
    /**
     * @fun UpdateHeaders
     * @brief 根据数据类型更新表头。
     * @details 根据当前数据类型（子板、料号、元件）设置表头标签。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateHeaders();
public:
    TableViewModel(QObject* parent);
    ~TableViewModel();
private:
    /// 标题栏数据，存储表头的字符串列表
    QStringList m_header_list;
    /// 结果状态与背景颜色的映射，用于根据检测结果状态设置背景颜色
    std::map<RESULT_STATE, QVariant> result_color_map;
    /// 检测结果数据，存储不同类型的数据模型指针
    std::vector<ShowTableParamBasePtr> m_vec_data;
    /// 子板数据列表，存储子板相关的数据结构指针
    std::vector<SubBoardDataStructPtr> show_subboards;
    /// 料号数据列表，存储料号相关的数据结构指针
    std::vector<PartNumberDataStructPtr> show_part_numbers;
    /// 元件数据列表，存储元件相关的数据结构指针
    std::vector<DeviceDataStructPtr> show_devices;
    /// 数据类型标识，用于区分当前数据模型的类型（子板、料号、元件）
    int type;
};
#endif