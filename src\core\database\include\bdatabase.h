﻿/*****************************************************************//**
 * @file   bdatabase.h
 * @brief  数据库基类
 * @details 某些功能需要子类继承实现后进行使用，根据不同数据库进行派生即可。
 * <AUTHOR>
 * @date   March 2024
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>March 2024         <td>         <td>HJC                       <td><EMAIL> <td>
 *   @copyright 版权 CopyRight (C), 2024-2025.
 * *********************************************************************/

#ifndef _ABSDATABASE_H_
#define _ABSDATABASE_H_
#include <iostream>
#include <chrono>
#include <functional>
#include <string>
#include <string_view>
#include <vector>
#include <set>
#include <any>
#include "errorhandler.h"
#include <map>

#pragma warning(disable: 4996)

namespace jrsdatabase
{

    template<typename DB>
    class AbsDatabase
    {
    public:

        /**
         * @fun CreateDatabase
         * @brief   创建数据库，在连接数据库之前调用一次
         * @param db_host 地址
         * @param user_name 名称
         * @param password  密码
         * @param db_name   数据库名称
         * @return  存在，创建成功:true, 创建失败：false
         * @date 2024.6.28
         * <AUTHOR>
         */
        bool CreateDatabase(const std::string& db_host, const std::string& user_name, const std::string& password, const std::string& db_name)
        {
            return m_connPtr_->CreateDatabase(db_host, user_name, password, db_name);
        }

        /**
         * @fun Connect
         * @brief 连接数据库
         * @detail 可以连接不同的数据库。
         * @param ...args [IN]连接数据库参数。
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        template <typename... Args>
        inline int Connect(Args &&...args) {
            return m_connPtr_->connect(std::forward<Args>(args)...) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun Disconnect
         * @brief 断开连接数据库
         * @return  成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        inline int Disconnect() { return m_connPtr_->disconnect() ? jrscore::AOI_OK : CheckError(); };
#pragma region Database Operation
        /**
         * @fun AlterDatabase
         * @brief 更改数据库字符集
         * @detail 传入子类设定好的结构体进行更改，需要在root用户下进行
         * @param data [IN]不同的数据库传入不同的结构体
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        template<typename T>
        int AlterDatabase(const T& data);



        /**
         * @fun CreateDatabase
         * @brief 创建数据库
         * @detail 传入数据库名称，需要在root用户下进行
         * @param db_name [IN]数据库名称
         * @return 成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        virtual int CreateDatabase(const std::string& db_name) = 0;
        /**
         * @fun DeleteDatabase
         * @brief 删除数据库
         * @detail 传入数据库名称，需要在root用户下进行
         * @param db_name [IN]数据库名称
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        virtual int DeleteDatabase(const std::string& db_name) = 0;

        /**
         * @fun ShowDatabases
         * @brief 显示所有数据库
         * @param res [OUT]所有数据库表
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int ShowDatabases(std::vector<std::tuple<std::string>>& res) = 0;


#pragma endregion

#pragma region Table Operation



        /**
        * @fun QueryTableFields
        * @brief 查询表中字段的属性和详细信息
        * @param table_name [IN]数据表名称
        * @param res [OUT]查询结果
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        template<typename QUERYTFIELDRESULT>
        int QueryTableFields(const std::string& table_name, std::vector<QUERYTFIELDRESULT>& res);

        /**
         * @fun AlterTableName
         * @brief 更改表名称
         * @param t_name 旧表名
         * @param t_new_name 新表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int AlterTableName(const std::string& t_name, const std::string& t_new_name) = 0;
        /**
         * @fun AlterTableFieldName
         * @brief 更改表中字段名称
         * @param t_name 表名
         * @param f_name 旧字段名
         * @param f_new_name 新字段名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int AlterTableFieldName(const std::string& t_name, const std::string& f_name, const std::string& f_new_name) = 0;

        /**
         * @fun AlterTableFieldType
         * @brief 更改表中某字段数据类型
         * @param t_name 表名
         * @param f_name 字段名
         * @param type 数据类型
         * @return  成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        template<typename TYPE>
        int AlterTableFieldType(const std::string& t_name, const std::string& f_name, const TYPE& type) {
            return  AlterTableFieldTypeImp(t_name, f_name, std::make_shared<TYPE>(type));
        }

        /**
         * @fun DeleteTableField
         * @brief 删除表中某个字段
         * @param t_name 表名
         * @param f_name 字段名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int DeleteTableField(const std::string& t_name, const std::string& f_name) = 0;

        /**
         * @fun AddTableField
         * @brief 在表中添加新字段
         * @param t_name 表名
         * @param f_new_name 新字段名
         * @param type 新字段数据类型
         * @param is_first 默认表头添加，否则添加到f_name字段之后
         * @param f_name 在那个字段后添加表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        template<typename TYPE>
        int AddTableField(const std::string& t_name, const std::string& f_new_name, const TYPE& type, const bool is_first = true, const std::string& f_name = "") {
            return  AddTableFieldImp(t_name, f_new_name, std::make_shared<TYPE>(type), is_first, f_name);
        };

        /**
         * @fun DeleteDataTable
         * @brief 删除数据表
         * @detail 根据传入数据表名称，删除数据表。
         * @param table_name [IN]数据表名称
         * @return  成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        virtual int DeleteDataTable(const std::string& table_name) = 0;
        /**
         * @fun ReflashDataTable
         * @brief 更新数据表
         * @detail 根据数据表名称，更新指定的数据表
         * @param table_name [IN]数据表名称
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        virtual int ReflashDataTable(const std::string& table_name) = 0;
        /**
         * @fun ShowTables
         * @brief 显示数据表
         * @detail 显示数据库中的所有数据表
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        virtual int ShowTables(std::vector<std::tuple<std::string>>& res) = 0;


        /**
         * @fun CreateTable
         * @brief 模版函数，创建数据表
         * @detail 模版函数需要传入数据表结构体，结构体中包含表中所有的字段。
         * @param key_field [IN]表中主键字段
         * @param is_auto_key [IN]主键是否自动增长：true自动增长，false普通主键
         * @param uniqu_field [IN]独立字段，该字段内容不能重复
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template<typename T>
        int CreateTable(const std::string& key_field = "", const bool is_auto_key = false, const std::set<std::string>& uniqu_field = std::set<std::string>());

        //添加--索引
        template<typename TYPE>
        int AddIndex(const std::string& table_name, TYPE index_type, const std::string& index_name, const std::vector<std::string>& fields)
        {
            return AddIndexImp(table_name, std::make_shared<TYPE>(index_type), index_name, fields);
        }

#pragma endregion
#pragma region Data Operation
        /**
         * @fun AddData
         * @brief 在表中添加一条新数据
         * @param t [IN]数据表结构体变量
         * @param ...args [IN]不同数据库中使用其余参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int AddData(const T& t, Args &&...args)
        {
            if (m_connPtr_->insert(t, std::forward<Args>(args)...) == 1) {
                return jrscore::AOI_OK;
            }
            return  CheckError();
        }
        /**
         * @fun AddData
         * @brief 在表中添加多条新数据
         * @param v [IN]添加数据集
         * @param ...args [IN]不同数据库中使用其余参数[预留]
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int AddData(const std::vector<T>& v, Args &&...args)
        {
            if (v.size() == 0)
            {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            if (m_connPtr_->insert(v, std::forward<Args>(args)...) == static_cast<int>(v.size())) {
                return jrscore::AOI_OK;
            }
            return  CheckError();
        }
        /**
         * @fun DeleteData
         * @brief 删除数据
         * @param ...where_condition [IN]Null-清空表中数据；根据condition删除表中数据
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int DeleteData(Args &&...where_condition)
        {
            return m_connPtr_->delete_records<T>(std::forward<Args>(where_condition)...) ? jrscore::AOI_OK : CheckError();
        }
        /**
        * @fun QueryTable
        * @brief 搜索表中数据
        * @param res [OUT]查询结果
        * @param ...args [IN]根据条件查询数据，Null-查询表中所有数据
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        template<typename T, typename... Args>
        int QueryTable(std::vector<T>& res, Args &&...args)
        {
            res = m_connPtr_->query<T>(std::forward<Args>(args)...);
            return (res.size() > 0) ? jrscore::AOI_OK : CheckError();
        }


        template<typename T, typename... Args>
        int QueryProcedure(std::vector<T>& res, Args && ...args)
        {
            res = m_connPtr_->QueryProcedure<T>(std::forward<Args>(args)...);
            return (res.size() > 0) ? jrscore::AOI_OK : CheckError();
        }

        /**
         * @fun QueryMultipleTable
         * @brief 多表数据查询
         * @param table_fields_map [IN]表名及个表字段
         * @param where_condition [IN]sql条件
         * @param res [OUT]查询结果
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.9
         * <AUTHOR>
         */
        template<typename T>
        int QueryMultipleTable(const std::map<std::string, std::vector<std::string>>& table_fields_map, const std::string& where_condition, std::vector<T>& res) {
            if (table_fields_map.size() == 0)
            {
                jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
#ifdef JRSCOR_ENABLE_MYSQL /*MYSQL SQL语句*/
            std::string sql = "SELECT ";
            bool firstField = true;
            for (const auto& table_fields : table_fields_map) {
                const std::string& table_name = table_fields.first;
                const std::vector<std::string>& fields = table_fields.second;
                if (fields.empty()) {
                    sql += table_name + ".*, ";
                }
                else {
                    for (const std::string& field : fields) {
                        if (!firstField) {
                            sql += ", ";
                        }
                        sql += table_name + "." + field;
                        firstField = false;
                    }
                }
            }
            if (sql.back() == ' ') {
                sql.pop_back(); // 去除空格
                sql.pop_back(); // 去除逗号
            }
            sql += " FROM ";
            for (const auto& table : table_fields_map) {
                sql += table.first + ", ";
            }
            sql.pop_back();
            sql.pop_back();

            if (!where_condition.empty()) {
                sql += " WHERE " + where_condition;
            }

#endif
            res = m_connPtr_->query<T>(sql);
            return (res.size() > 0) ? jrscore::AOI_OK : CheckError();
        }
        virtual std::string  GetUpdateCustomFieldsDataSQL(const std::string& table_name, const std::map<std::string, std::string>& fields_and_new_value_map, const std::string& condition) = 0;

        //多表更新
        virtual int UpdateCustomFieldsData(const std::string& table_name, const std::map<std::string, std::string>& fields_and_new_value_map, const std::string& condition) = 0;



        /**
         * @fun QueryCountDataInTable
         * @brief 查询表中已有数据总数
         * @param t_name [IN]表名
         * @param total [OUT]查询总数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int QueryCountDataInTable(const std::string& t_name, uint64_t& total) = 0;

        /**
        * @fun GetInsertIdAfterInsert
        * @brief 获取插入单条数据后获取最新的主键值
        * @param t [IN] 插入一条数据
        * @param res [OUT]插入数据后的主键值
        * @param ...args [IN]其他条件，备用参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int GetInsertIdAfterInsert(const T& t, uint64_t& res, Args &&...args) {
            try {
                res = m_connPtr_->get_insert_id_after_insert(t, std::forward<Args>(args)...);
            }
            catch (...) {
                std::cerr << __func__ << ":: this error!!" << std::endl;
            }
            return (res != INT_MIN) ? jrscore::AOI_OK : CheckError();

        }
        /**
        * @fun GetInsertIdAfterInsert
        * @brief 获取插入多条数据后获取最新的主键值
        * @param v [IN] 插入多条数据
        * @param res [OUT] 插入数据后的主键值
        * @param ...args [IN] 其他条件，备用参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int GetInsertIdAfterInsert(const std::vector<T>& v, uint64_t& res, Args &&...args) {
            if (v.size() == 0) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            res = m_connPtr_->get_insert_id_after_insert(v, std::forward<Args>(args)...);
            if (res != INT_MIN) {
                return jrscore::AOI_OK;
            }
            else
            {
                return  CheckError();
            }
        }


        /**
         * @fun Replace
         * @brief 将一条数据添加到数据库中，如果数据库中有相同记录则替换。
         * @param t [IN] 添加单条数据
         * @param res [OUT] 成功更改数据的条数
         * @param ...args  [IN]不同数据库所需参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int Replace(const T& t, int& res, Args &&...args) {
            res = m_connPtr_->replace(t, std::forward<Args>(args)...);
            if (res != INT_MIN) {
                return jrscore::AOI_OK;
            }
            else
            {
                return  CheckError();
            }
        }
        /**
         * @fun Replace
         * @brief 将多条数据添加到数据库中，如果数据库中有相同记录则替换。
         * @param v [IN] 添加多条数据
         * @param res [OUT] 成功更改数据的条数
         * @param ...args  [IN] 不同数据库所需参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int Replace(const std::vector<T>& v, int& res, Args &&...args) {
            if (v.size() == 0)
            {
                jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            res = m_connPtr_->replace(v, std::forward<Args>(args)...);
            if (res != INT_MIN) {
                return jrscore::AOI_OK;
            }
            else
            {
                return  CheckError();
            }
        }
        /**
         * @fun UpdateData
         * @brief 更新表中单条数据-表中有主键，根据主键进行更新；表中没有主键则根据传入条件进行更新。
         * @param t [IN]更新数据
         * @param res [OUT]更新成功的条数
         * @param ...args [IN]条件参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int UpdateData(const T& t, int& res, Args &&...args) {
            res = m_connPtr_->update(t, std::forward<Args>(args)...);
            if (res != INT_MIN) {
                return jrscore::AOI_OK;
            }
            else
            {
                return  CheckError();
            }
        }
        /**
        * @fun UpdateData
        * @brief 更新表中多条数据-仅适用于主键自增长的表
        * @param v [IN]更新数据集
        * @param res [OUT]更新成功的条数
        * @param ...args [IN]条件参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int UpdateData(const std::vector<T>& v, int& res, Args &&...args) {
            if (v.size() == 0) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            res = m_connPtr_->update(v, std::forward<Args>(args)...);
            if (res != INT_MIN) {
                return jrscore::AOI_OK;
            }
            else
            {
                return  CheckError();
            }
        }
        /**
        * @fun ExecuteSQL
        * @brief 执行原始SQL语句。
        * @param sql [IN]SQL语句字符串
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        int ExecuteSQL(const std::string& sql)
        {
            return  m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }

        /**
        * @fun BeginAffair
        * @brief 开始事务
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        int BeginAffair()
        {
            return m_connPtr_->begin() ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun Rollback
         * @brief 事务数据撤回
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        int Rollback()
        {
            return m_connPtr_->rollback() ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun CommitAffair
         * @brief 提交事务
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        int CommitAffair()
        {
            return m_connPtr_->commit() ? jrscore::AOI_OK : CheckError();
        };


        /**
         * @fun UpdateOperateTime
         * @brief 更新操作时间-连接池使用
         * @return Null
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline void UpdateOperateTime()
        {
            m_connPtr_->update_operate_time();
        }
        /**
         * @fun GetLatestOperateTime
         * @brief 获取最后一次操作时间
         * @return 上次操作时间
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline auto GetLatestOperateTime()
        {
            return std::any_cast<std::chrono::system_clock::time_point>(m_connPtr_->get_latest_operate_time());
        }

        /**
         * @fun Ping
         * @brief 用于连接池
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline bool Ping()
        {
            return  m_connPtr_->ping();
        }
        /**
         * @fun HasError
         * @brief 用于连接池
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline bool HasError()
        {
            return m_connPtr_->has_error();
        }

        /**
         * @fun GetLastError
         * @brief 获取最新错误信息
         * @return 有错误:jrscore::AOI_OK，没有错误:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        int GetLastError(std::string& res) const {
            res = m_connPtr_->get_last_error();
            if (res == "") return -1;
            else return jrscore::AOI_OK;
        }

        /**
         * @fun GetLastAffectRows
         * @brief 获取最新影响数据行数
         * @return 返回影响行数
         * @date 2024.4.8
         * <AUTHOR>
         */
        int GetLastAffectRows()
        {
            return m_connPtr_->get_last_affect_rows();
        }


#pragma endregion
    protected:

        std::shared_ptr<DB> m_connPtr_; /**< 数据库连接指针 */
    private:
        /**
         * @fun AlterDatabaseImpl
         * @brief 更改数据库实现函数
         * @param value 消除模版类型，方便重载
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        virtual int AlterDatabaseImpl(const std::shared_ptr<void> value) = 0;
        /**
         * @fun QueryTableFieldsImpl
         * @brief 查询数据表字段实现函数
         * @param table_name [IN]数据表名称
         * @param res [OUT]查询到的表字段
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        virtual int QueryTableFieldsImpl(const std::string& table_name, std::any& res) = 0;

        /**
        * @fun AlterTableFieldTypeImp
        * @brief 实现更改表中某字段数据类型
        * @param t_name 表名
        * @param f_name 字段名称
        * @param ptr 数据类型指针
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int AlterTableFieldTypeImp(const std::string& t_name, const std::string& f_name, const std::shared_ptr<void> ptr) = 0;
        /**
        * @fun AddTableFieldImp
        * @brief 实现在表中添加新字段
        * @param t_name 表名
        * @param f_new_name 新字段名
        * @param type 新字段数据类型
        * @param is_first 默认表头添加，否则添加到f_name字段之后
        * @param f_name 在那个字段后添加表名
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int AddTableFieldImp(const std::string& t_name, const std::string& f_new_name, const std::shared_ptr<void> ptr, const bool is_first, const std::string& f_name) = 0;


        //增加索引实现
        virtual int AddIndexImp(const std::string& table_name, const std::shared_ptr<void> ptr, const std::string& index_name, const std::vector<std::string>& fields) = 0;
        /**
        * @fun CheckError
        * @brief 检测MySQL错误信息
        * @return 成功:jrscore::AOI_OK，失败:-1  根据返回值返回问题 目前成功 jrscore::AOI_OK 错误暂时：1
        * @date 2024.4.8
        * <AUTHOR>
        */
        virtual int CheckError() = 0;

    };

    template<typename DB>
    template<typename T>
    inline int AbsDatabase<DB>::AlterDatabase(const T& data)
    {
        return AlterDatabaseImpl(std::make_shared<T>(data));
    }

    template<typename DB>
    template<typename QUERYTFIELDRESULT>
    inline int AbsDatabase<DB>::QueryTableFields(const std::string& table_name, std::vector<QUERYTFIELDRESULT>& res)
    {
        std::any any_res;
        int result = QueryTableFieldsImpl(table_name, any_res); // 调用 QueryTableFieldsImpl，并传入 std::any 类型的参数
        if (result == 0) {
            // 如果调用成功，则将结果转换为 res
            res = std::any_cast<std::vector<QUERYTFIELDRESULT>>(any_res);
        }
        return result;
    }

    template<typename DB>
    template<typename T>
    int AbsDatabase<DB>::CreateTable(const std::string& key_field, const bool is_auto_key, const std::set<std::string>& uniqu_field)
    {
        int res = -1;
        if (!key_field.empty() && is_auto_key) {
            if (!uniqu_field.empty()) {
                res = m_connPtr_->create_datatable<T>(ormpp_auto_key{ key_field }, ormpp_unique{ uniqu_field });
            }
            else {
                res = m_connPtr_->create_datatable<T>(ormpp_auto_key{ key_field });
            }
        }
        else if (!key_field.empty()) {
            if (!uniqu_field.empty()) {
                res = m_connPtr_->create_datatable<T>(ormpp_key{ key_field }, ormpp_unique{ uniqu_field });
            }
            else {
                res = m_connPtr_->create_datatable<T>(ormpp_key{ key_field });
            }
        }
        else if (!uniqu_field.empty()) {
            res = m_connPtr_->create_datatable<T>(ormpp_unique{ uniqu_field });
        }
        else {
            res = m_connPtr_->create_datatable<T>();
        }
        return res ? jrscore::AOI_OK : CheckError();
    }
};

#endif 
