﻿#include "datamanager.h"
//custom
#include "jrsglobaltable.h"
#include "tdatamanager.h"
#include "dbmanager.h"
#include "projectparam.hpp"
#include "fileoperation.h"
#include "viewparam.hpp"
#include "image.hpp"
#include "dataparam.h"
#include "paramconvertor.h"
#include "jsonoperator.hpp"
#include "coreapplication.h"
#include "exportcsv.h"
#include "stringoperation.h"
#include "timeutility.h"
#include "parameterprocess.hpp"

namespace jrsdata
{
    struct ImplData
    {
        jrsdata::ParamConvertorPtr param_convertor_ptr;
        jrsdata::SystemStateViewParamPtr sys_state_param_ptr;           /**< 初始化系统参数 */
        TDataManagerPtr t_data_manager_ptr;
        jrsdata::ProjectParamPtr project_param_ptr;
        jrsdata::AllSettingParamMap all_setting_param_map;    /**< 即将弃用 */
        jrsparam::ParameterProcessPtr parameter_process_ptr;
    };
}
jrsdata::DataManager::DataManager()
    :_impl_data(new jrsdata::ImplData)
{
    InitMember();
    InitInvokeFun();
}

jrsdata::DataManager::~DataManager()
{
    if (_impl_data)
    {
        delete _impl_data;
        _impl_data = nullptr;
    }
}

int jrsdata::DataManager::EventHandler(const jrsdata::ViewParamBasePtr& param_)
{
    return DataEventHandler(param_);
}

int jrsdata::DataManager::OnlineDebugInfoToOnlineView(const jrsdata::OnlineDebugParmPtr& online_debug_parm_ptr_)
{
    if (onlie_debug_info_callback)
    {
        onlie_debug_info_callback(online_debug_parm_ptr_);
    }
    return 0;
}

/**< 事件注册 */
void jrsdata::DataManager::InitInvokeFun()
{
    auto setting_setting_param_fun = std::bind(&DataManager::SaveSettingParams, this, std::placeholders::_1);
    auto update_setting_param_fun = std::bind(&DataManager::GetSettingParams, this, std::placeholders::_1);
    _data_func_map =
    {
      { jrsaoi::PROJECT_SAVE_EVENT_NAME , std::bind(&DataManager::SavePorjectParams , this , std::placeholders::_1) },
      { jrsaoi::OPERATE_COMPONENT_SAVE_COMPONENT_EVENT_NAME , std::bind(&DataManager::SaveLibComponent , this , std::placeholders::_1) },
      { jrsaoi::OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME , std::bind(&DataManager::ReadLibComponent , this , std::placeholders::_1) },
      { jrsaoi::OPERATE_COMPONENT_REMOVE_COMPONENT_EVENT_NAME , std::bind(&DataManager::RemoveLibComponent , this , std::placeholders::_1) },

      { jrsaoi::PROJECT_READ_EVENT_NAME , std::bind(&DataManager::GetProjectParams , this , std::placeholders::_1) },
      { jrsaoi::APPEND_PROJECT_EVENT_NAME , std::bind(&DataManager::GetProjectParams , this , std::placeholders::_1) },
      { jrsaoi::ENTIRETY_IMAGE_READ , std::bind(&DataManager::GetEntiretyBoardImages , this , std::placeholders::_1) },
      { jrsaoi::ENTIRETY_IMAGE_SAVE , std::bind(&DataManager::SaveEntiretyBoardImages , this , std::placeholders::_1) },
      { jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME,update_setting_param_fun },
      { jrsaoi::UPDATE_COMMON_PARAM_EVENT,  update_setting_param_fun},
      { jrsaoi::MACHINE_PARAM_UPDATE_EVENT, update_setting_param_fun},
      { jrsaoi::SYSTEM_PARAM_SAVE_EVENT, setting_setting_param_fun},
      { jrsaoi::COMMON_PARAM_SAVE_EVENT, setting_setting_param_fun},
      { jrsaoi::MACHINE_PARAM_SAVE_EVENT,setting_setting_param_fun},
      { jrsaoi::MOTION_CONFIG_EVENT_NAME,std::bind(&DataManager::HandleMotionParam , this , std::placeholders::_1)},
      { jrsaoi::DETECT_RESULT_PARAM_SAVE_EVENT,std::bind(&DataManager::SaveDetectResult , this , std::placeholders::_1)},
      { jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT,std::bind(&DataManager::InitSystem , this , std::placeholders::_1)},
      { jrsaoi::DATABASE_CONNECT_EVENT,update_setting_param_fun},
      { jrsaoi::DATABASE_DISCONNECT_EVENT,update_setting_param_fun},
      { jrsaoi::OPERATE_SAVE_ALGO_EXECUTE_INFO_EVENT_NAME,std::bind(&DataManager::SaveComponentExecuteInfo,this,std::placeholders::_1)},
      //{ jrsaoi::SHORTCUT_ACT_SAVE_STANDARD_IMG,std::bind(&DataManager::SaveComponentsImages,this,std::placeholders::_1)},
      { jrsaoi::OPERATE_SAVE_REAPIR_COMPRESS_IMG_EVENT_NAME,std::bind(&DataManager::SaveRepairCompressEntiretyBoardImages,this,std::placeholders::_1)},
      { jrsaoi::OPERATE_SAVE_REAPIR_BRIEF_COMPONENT_EVENT_NAME,std::bind(&DataManager::SaveRepairBriefComponentInfo,this,std::placeholders::_1)},
      { jrsaoi::OPERATE_UPDATE_TICKET_NUMBER,std::bind(&DataManager::UpdateWorkNumber,this,std::placeholders::_1)},
      { jrsaoi::OPERATE_DETECT_CLEAR_RESULT,std::bind(&DataManager::ClearStatisticsData,this,std::placeholders::_1)},
      { jrsaoi::OPERATE_DETECT_CLEAR_DEVICE_RESULT,std::bind(&DataManager::ClearDevicesData,this,std::placeholders::_1)},
        {jrsaoi::QUERY_DATABASE_SUBBOARD_BARCODES,std::bind(&DataManager::QuerySubboardBarcodesBySubboardBarcode,this,std::placeholders::_1)},
    };
}

bool jrsdata::DataManager::IsValidParam(const jrsdata::ViewParamBasePtr& param_)
{
    if (!param_)
    {
        Log_ERROR("project_event_param_为空");
        return false;
    }

    if (param_->event_name.empty())
    {
        Log_ERROR("event_name is empty");
        return false;
    }
    return true;
}

bool jrsdata::DataManager::InvokeFun(const jrsdata::ViewParamBasePtr& param_)
{
    auto it = _data_func_map.find(param_->event_name);
    if (it == _data_func_map.end())
    {
        Log_ERROR("DataManager::InvokeFun() event_name:", param_->event_name, " not found");
        return false;
    }
    if (!it->second)
    {
        Log_ERROR("DataManager::EventHandler() event_name:", param_->event_name, " has invalid function pointer");
        return false;
    }
    it->second(param_);  // 执行函数
    return true;
}

int jrsdata::DataManager::DataEventHandler(const jrsdata::ViewParamBasePtr& param_)
{
    if (!IsValidParam(param_))
    {
        return -1;
    }

    if (!InvokeFun(param_))
    {
        return -1;
    }
    return jrscore::AOI_OK;
}

int jrsdata::DataManager::SaveComponentExecuteInfo(const jrsdata::ViewParamBasePtr param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::ComponentSaveInfo>(param_);
    if (!param_temp)
    {
        Log_ERROR("保存元件执行时信息失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string report_save_path;
    std::string component_info_save_path;
    if (param_temp->algo_name.find("Mark") == std::string::npos)
    {
        GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGORITHM_RESULT_PATH, report_save_path);
        component_info_save_path = GenerateSaveComponentExecuteInfoPath(report_save_path, param_temp);
    }
    else
    {
        GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_LOCATION_POINT_IMAGE_PATH, report_save_path);
        component_info_save_path = GenerateSaveComponentExecuteInfoPath(report_save_path + "/MarkInfo/", param_temp);

    }

    if (component_info_save_path.empty())
    {
        return jrscore::AOI_OK;
    }
    /**<检查磁盘容量 */
    auto res_check_disk_cap = CheckDiskCapacity(component_info_save_path, "保存元件执行信息");
    if (!res_check_disk_cap)
    {
        return -1;
    }


    //! 保存执行算法时输入的图像
    SaveImage(component_info_save_path, param_temp->input_img, "light_img_");
    //!保存执行算法时的转换矩阵
    for (const auto& hom_mat : param_temp->hom_matrix)
    {
        std::string file_name = "/hom_matrix/" + std::to_string(hom_mat.first) + ".tiff";
        _impl_data->t_data_manager_ptr->SaveImage(hom_mat.second, component_info_save_path + file_name);
    }

    //!保存模板信息
    int template_count = 0;
    for (auto& value_temp : param_temp->template_info_vector)
    {
        //!模板信息保存路径：父路径+固定的Template_模板序号
        std::string template_info_save_path = component_info_save_path + "/Template_" + std::to_string(template_count) + "/";

        _impl_data->t_data_manager_ptr->SaveImage(value_temp.template_image, template_info_save_path + "template_img_" + std::to_string(value_temp.light_id) + ".png");
        _impl_data->t_data_manager_ptr->SaveString(template_info_save_path + "template_color_" + std::to_string(value_temp.light_id) + ".json", value_temp.template_color_param);
        template_count++;
    }
    //!保存检测结果图片
    SaveImage(component_info_save_path, param_temp->result_img, "result_img_");
    //!保存输入mask图片
    _impl_data->t_data_manager_ptr->SaveImage(param_temp->output_mask_image, component_info_save_path + "/out_mask.png");

    //! 保存检测区域参数
    std::string algo_input_info_json;
    iguana::to_json(param_temp, algo_input_info_json);
    auto res = _impl_data->t_data_manager_ptr->SaveString(component_info_save_path + "algo_input_info.json", algo_input_info_json);
    if (!res)
    {
        Log_ERROR("保存元件执行时信息失败！");
        return jrscore::DataManagerError::E_AOI_DB_SAVE_FILE_FAILURE;
    }

    return jrscore::AOI_OK;
}

int jrsdata::DataManager::SaveAlgoReport(const std::shared_ptr<DetectResultParam>& result_data_ptr_, const std::string& report_path)
{
    //!生成最终的路径
    auto report_final_path = report_path + "ReportCsv/" + _impl_data->project_param_ptr->project_name + "/" + result_data_ptr_->board_code;
    /**< 检查磁盘容量 */
    auto res_check_disk_cap = CheckDiskCapacity(report_final_path, "保存算法报表");
    if (!res_check_disk_cap)
    {
        return -1;
    }

    ExportCsv export_csv_;
    //!表头
    std::vector<std::string> header_data = { "Component Name", "Subboard ID", "Algorithm Name", "Algorithm Status","Spec Name","Spec Min","Spec Max","Algorithm Result" };
    //!行数据
    std::vector<std::vector<std::string>> row_datas_temp;

    //! 所有元件的算法检测结果数据
    for (const auto& component_result : result_data_ptr_->component_result_vector)
    {
        const auto& component_name_temp = component_result.t_device.device_name;
        auto component_subboard_id_temp = std::to_string(component_result.t_device.subboard_id);
        //！ 每个元件下的算法组
        for (const auto& algo_reuslts : component_result.groups)
        {
            //! 每个组下面包含多个算法检测框结果
            for (const auto& algo_result : algo_reuslts.detect_windows)
            {
                //! 算法名称
                auto algo_name_temp = algo_result.t_detect_window.algorithm_name;
                //! 算法检测状态
                std::string algo_status = algo_result.t_detect_window.detect_window_result ? "OK" : "NG";
                if (algo_result.t_detect_window.window_result_data.compare("null") != 0)
                {
                    nlohmann::json window_result_data_json = jrscore::StringToJson(algo_result.t_detect_window.window_result_data);
                    if (window_result_data_json.is_object())
                    {
                        for (const auto& [key, value] : window_result_data_json.items())
                        {
                            if (value.is_array())
                            {
                                for (auto& one_result_data : value)
                                {
                                    double std_value = jrscore::GetSpeficJsonTypeValue<double>(one_result_data, "std_value").value_or(0.0f);
                                    float max = jrscore::GetSpeficJsonTypeValue<float>(one_result_data, "max").value_or(0.0f);
                                    float min = jrscore::GetSpeficJsonTypeValue<float>(one_result_data, "min").value_or(0.0f);
                                    std::string name = jrscore::GetSpeficJsonTypeValue<std::string>(one_result_data, "name").value_or("");
                                    double result = jrscore::GetSpeficJsonTypeValue<double>(one_result_data, "result").value_or(true);
                                    std::vector<std::string> row =
                                    {
                                        component_name_temp,
                                        component_subboard_id_temp,
                                        algo_name_temp,
                                        algo_status,
                                        name,                       // Spec Name
                                        std::to_string(std_value),  // Spec Std Value
                                        std::to_string(min),        // Spec Min
                                        std::to_string(max),        // Spec Max
                                        std::to_string(result)      // Algorithm Result
                                    };
                                    //!将行数据添加进去
                                    row_datas_temp.push_back(std::move(row));
                                }
                            }
                        }
                    }
                }
                else
                {
                    std::vector<std::string> row =
                    {
                        component_name_temp,
                        component_subboard_id_temp,
                        algo_name_temp,
                        algo_status,
                        "", // Spec Name
                        "", // Spec Std Value
                        "", // Spec Min
                        "", // Spec Max
                        ""  // Algorithm Result
                    };
                    //!将行数据添加进去
                    row_datas_temp.push_back(std::move(row));
                }
            }
        }
    }
    if (export_csv_.ExportToFile(report_final_path, header_data, row_datas_temp))
    {
        Log_ERROR("导出报表失败");
        return jrscore::DataManagerError::E_AOI_DB_EXPORT_ALGO_REPORT_FAILURE;
    }
    return 0;
}

std::string jrsdata::DataManager::GenerateSaveComponentExecuteInfoPath(const std::string& report_path, const std::shared_ptr<ComponentSaveInfo>& component_save_info_)
{
    //!元件算法信息保存的父路径由：软件界面设定的算法report路径+固定的AlgoExecuteInfo+工程名+元件名+元件检测时间+算法名称
    std::ostringstream base_path;
    //! 获取当前日期
    auto current_date = jtools::TimeUtility::GetCurrentTimeString("%Y-%m-%d");

    base_path << report_path;
    std::string algo_res_str = component_save_info_->algo_result_status ? "OK" : "NG";
    base_path << "/AlgoExecuteInfo/"
        << current_date << "/"
        << component_save_info_->project_name << "/"
        << algo_res_str << "/"
        << component_save_info_->algo_name << "/"
        << component_save_info_->current_time + "_" + component_save_info_->component_name;

    int index = 1;
    std::string final_path;
    do {
        std::ostringstream temp_path;
        temp_path << base_path.str() << "_" << index << "/";
        final_path = temp_path.str();
        index++;
    } while (std::filesystem::exists(final_path)); // 检查目录是否存在
    return final_path;

}

int jrsdata::DataManager::SaveImage(const std::string& file_path_, const std::unordered_map<int, cv::Mat>& input_img_,
    const std::string& name_prefix_, const std::string& type_)
{
    std::vector<std::thread> threads;
    bool res = jtools::FileOperation::JRSCreateDirectory(file_path_);
    if (!res)
    {
        Log_ERROR("创建文件夹失败，请检查:", file_path_);
        return jrscore::DataManagerError::E_AOI_DB_CREATE_DIR_FAILURE;
    }
    else
    {
        for (auto& value_img : input_img_)
        {
            std::string image_path;
            if (type_ == ".mbin")
            {
                image_path = file_path_ + name_prefix_ + std::to_string(value_img.first) + ".mbin";
                threads.emplace_back(std::thread(&TDataManager::SaveImageToBin, _impl_data->t_data_manager_ptr, value_img.second, image_path));
            }
            else
            {
                if (static_cast<jrsdata::LightImageType>(value_img.first) == jrsdata::LightImageType::HEIGHT)
                {
                    image_path = file_path_ + name_prefix_ + std::to_string(value_img.first) + ".tiff";
                }
                else
                {
                    image_path = file_path_ + name_prefix_ + std::to_string(value_img.first) + type_;
                }
                threads.emplace_back(std::thread(&TDataManager::SaveImage, _impl_data->t_data_manager_ptr, value_img.second, image_path));
            }

        }
        for (auto& t : threads)
        {
            if (t.joinable())
            {
                t.join();
            }
        }
    }
    return 0;
}

void jrsdata::DataManager::InitMember()
{
    _impl_data->param_convertor_ptr = std::make_shared<ParamConvertor>();
    _impl_data->t_data_manager_ptr = std::make_shared<TDataManager>();
    _impl_data->sys_state_param_ptr = std::make_shared<jrsdata::SystemStateViewParam>();
    _impl_data->parameter_process_ptr = std::make_shared<jrsparam::ParameterProcess>();
}



int jrsdata::DataManager::SaveRepairCompressEntiretyBoardImages(const jrsdata::ViewParamBasePtr param_)
{
    auto param_temp = std::dynamic_pointer_cast<OperateViewParam>(param_);

    if (!param_temp)
    {
        Log_ERROR("保存维修站数据失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string repair_save_path;
    auto is_enable = GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH, repair_save_path);
    if (!is_enable)
    {
        return jrscore::AOI_OK;
    }
    /**<检查磁盘容量 */
    if (!CheckDiskCapacity(repair_save_path, "保存维修站压缩图片"))
    {
        return -1;
    }
    auto compress_img_path = repair_save_path + "/" + param_temp->repair_data.project_name + "/compressentiretyimg/";
    auto save_type = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_TYPE, "获取压缩图片类型");
    SaveImage(compress_img_path, param_temp->repair_data.compress_entirety_board_imgs, "light_img_", save_type);
    return 0;
}

int jrsdata::DataManager::SaveRepairBriefComponentInfo(const jrsdata::ViewParamBasePtr param_)
{
    auto param_temp = std::dynamic_pointer_cast<OperateViewParam>(param_);
    if (!param_temp)
    {
        Log_ERROR("保存维修站简要元件信息失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string repair_save_path;
    auto is_enable = GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH, repair_save_path);
    if (!is_enable)
    {
        return jrscore::AOI_OK;
    }
    if (!CheckDiskCapacity(repair_save_path, "保存维修站元件简要信息"))
    {
        return -1;
    }
    //! 保存工程元件简要信息主要给维修站使用，维修站的路径+ 工程名称+固定的路径(/briefcomponent/briefcomponents.json) by zhangyuyu 2025.1.17
    auto brief_component_path = repair_save_path + "/" + param_temp->repair_data.project_name + "/briefcomponent/briefcomponents.json";

    std::string brief_component_infos;

    iguana::to_json(param_temp->repair_data.brief_component_info_list, brief_component_infos);

    std::vector<BriefComponentInfo> brief_component_info_list;
    iguana::from_json(brief_component_info_list, brief_component_infos);

    _impl_data->t_data_manager_ptr->SaveString(brief_component_path, brief_component_infos);

    return 0;
}

std::string jrsdata::DataManager::GetMachineSpecificParam(const std::string& key, const std::string& param_name)
{
    auto machine_param_map = _impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE];

    if (machine_param_map.find(key) == machine_param_map.end())
    {
        Log_Error_Stack(param_name + " 缺失");
        return "";
    }
    return machine_param_map.at(key).param_value;
}

bool jrsdata::DataManager::GetMachineSpecificSavePathParam(const std::string& specific_path_name, std::string& res_path_str)
{
    //! 获取指定参数内容
    auto res_str = GetMachineSpecificParam(specific_path_name, specific_path_name);
    res_path_str = jrscore::ParseJson(res_str, "directory");
    if (res_path_str.empty())
    {
        Log_ERROR("获取指定参数路径异常！");
        return false;
    }
    auto enable_str = jrscore::ParseJson(res_str, "enable");

    const bool is_enabled = (enable_str == "true");

    return is_enabled;
}

int jrsdata::DataManager::UpdateWorkNumber(const jrsdata::ViewParamBasePtr param_)
{
    // 使用静态指针转换并检查空指针
    auto operate_param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!operate_param)
    {
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }

    // 获取数据库管理指针并检查
    auto db_ptr = _impl_data->t_data_manager_ptr->GetDBMnagersPtr();
    if (!db_ptr)
    {
        Log_ERROR("获取数据库管理对象失败");
        return  jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }

    // 构建更新条件
    jrsdatabase::CustomUpdate custom_update;
    custom_update.table_name = jrsdatabase::jrstable::T_PROJECT;
    custom_update.fields_and_new_value_map =
    {
      { "work_order_information", operate_param->detect_statistics_view_param.detect_result_param->worker_num }
    };
    custom_update.condition = "project_name='" + operate_param->detect_statistics_view_param.detect_result_param->project_name + "'";

    // 执行数据库更新
    auto res = db_ptr->CustomUpdate({ custom_update });
    if (res != jrscore::AOI_OK)
    {
        Log_ERROR("更新工作号失败，数据库更新失败");
        return res;
    }

    return jrscore::AOI_OK;
}

int jrsdata::DataManager::ClearStatisticsData(const jrsdata::ViewParamBasePtr param_)
{
    // 使用静态指针转换并检查参数是否为 OperateViewParam 类型
    auto operate_param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!operate_param)
    {
        // 如果转换失败，记录错误日志并返回空指针错误
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }

    // 检查检测统计参数是否有效
    if (operate_param->detect_statistics_view_param.detect_result_param.has_value())
    {
        // 调用转换器清理统计信息，并返回结果
        return _impl_data->param_convertor_ptr->ClearStatisticsData(
            operate_param->detect_statistics_view_param.detect_result_param->project_name
        );
    }

    // 如果检测统计参数无效，记录错误日志并返回未知数据错误
    Log_ERROR("统计数据无效，无法清除");
    return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
}

int jrsdata::DataManager::ClearDevicesData(const jrsdata::ViewParamBasePtr param_)
{
    // 使用静态指针转换并检查参数是否为 OperateViewParam 类型
    auto operate_param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!operate_param)
    {
        // 如果转换失败，记录错误日志并返回空指针错误
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }

    // 检查检测统计参数是否有效
    if (operate_param->detect_statistics_view_param.detect_result_param.has_value())
    {
        // 调用转换器清理设备数据，并返回结果
        return _impl_data->param_convertor_ptr->ClearDevicesData(operate_param->detect_statistics_view_param.detect_result_param->project_name);
    }

    // 如果检测统计参数无效，记录错误日志并返回未知数据错误
    Log_ERROR("统计数据无效，无法清除");
    return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
}

int jrsdata::DataManager::SaveLibComponent(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!param_temp)
    {
        // 如果转换失败，记录错误日志并返回空指针错误
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }

    /**<检查磁盘容量 */
    if (!param_temp->entitys.components_param.empty())
    {
        auto& entity = param_temp->entitys.components_param.front();
        if (!CheckDiskCapacity(entity.file_param.file_path, "保存元件库信息"))
        {
            return -1;
        }
    }

    int res = jrscore::AOI_OK;
    for (size_t i = 0; i < param_temp->entitys.components_param.size(); i++)
    {
        res = _impl_data->t_data_manager_ptr->Save(param_temp->entitys.components_param.at(i));
    }
    if (res != jrscore::AOI_OK)
    {
        Log_ERROR("元件库保存失败，请检查");
    }
    return res;
}

int jrsdata::DataManager::ReadLibComponent(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!param_temp)
    {
        // 如果转换失败，记录错误日志并返回空指针错误
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }
    std::string path = param_temp->entitys.component_lib_path;
    std::vector<std::string> files = jtools::FileOperation::GetFileOrDirNames(path, false);
    for (size_t i = 0; i < files.size(); i++)
    {
        ComponentEntity entity;
        entity.file_param.file_path = path + "/";
        entity.file_param.file_name = files[i];
        auto res = _impl_data->t_data_manager_ptr->Read(entity);
        if (res != jrscore::AOI_OK)
        {
            entity.file_param.file_type = jrsdata::FileType::JSON;
            auto res_jason = _impl_data->t_data_manager_ptr->Read(entity);
            if (res_jason != jrscore::AOI_OK)
            {
                Log_Error_Stack(files[i] + "读取数据失败");
                return res_jason;
            }
            entity.file_param.file_type = jrsdata::FileType::BIN;

        }
        param_temp->entitys.components_param.push_back(entity);
    }

    if (_operate_callback)
    {
        _operate_callback(param_temp);
    }
    return 0;
}

int jrsdata::DataManager::RemoveLibComponent(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!param_temp)
    {
        // 如果转换失败，记录错误日志并返回空指针错误
        Log_ERROR("参数转换失败，无法获取 OperateViewParam 对象");
        return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    }
    std::string path = param_temp->entitys.component_lib_path;

    // 删除指定的元件
    for (size_t i = 0; i < param_temp->entitys.components_param.size(); i++)
    {
        jtools::FileOperation::RemoveFile(path + "/" + param_temp->entitys.components_param[i].part_name + ".component");
    }
    return 0;
}

int jrsdata::DataManager::QuerySubboardBarcodesBySubboardBarcode(const jrsdata::ViewParamBasePtr& param_)
{
    auto select_ptr = std::dynamic_pointer_cast<jrsdata::QueryDatabaseResult>(param_);
    if (!select_ptr)
    {
        Log_WARN("QuerySubboardBarcodesBySubboardBarcode: cast to QueryDatabaseResult failed.");
        return -1;
    }

    const auto& barcode = select_ptr->query_custom_subboard_barcode.input.subboard_barcode;
    auto select_barcodes_ptr = std::make_shared<jrsdatabase::jrsselect::SelectCustom>();
    select_barcodes_ptr->table_name = jrsdatabase::jrstable::T_SUBBOARD;
    select_barcodes_ptr->where_condition = barcode;
    select_barcodes_ptr->select_name = jrsdatabase::jrsselect::T_SELECT_SUBBARCODES_OF_ENTIRETY_BOARD_BY_SUBBOARD_BARCODE;

    int ret = _impl_data->t_data_manager_ptr->GetDBMnagersPtr()->Select(select_barcodes_ptr);
    if (ret != jrscore::AOI_OK)
    {
        Log_WARN("子板条码查询失败，条件：", barcode);
        return ret;
    }

    if (!select_barcodes_ptr->select_subboard_barcodes.has_value())
    {
        Log_WARN("子板条码查询为空，条件：", barcode);
        return -1;
    }

    const auto& result = *select_barcodes_ptr->select_subboard_barcodes;
    select_ptr->query_custom_subboard_barcode.output.board_id = result.board_id;
    select_ptr->query_custom_subboard_barcode.output.subboard_id_and_barcode = result.subboard_id_and_barcode;

    return jrscore::AOI_OK;
}
int jrsdata::DataManager::UpdateOnlineRejudicationToDatabase(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    //auto start = std::chrono::high_resolution_clock::now(); // 记录开始时间

    //auto online_rejudge_ptr = std::dynamic_pointer_cast<jrsdata::OnlineRejudgeViewParam>(param_);
    //if (!online_rejudge_ptr)
    //{
    //    Log_ERROR("参数转换失败，无法获取 OnlineRejudgeViewParam 对象");
    //    return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    //}
    //auto db_manager_ptr = _impl_data->t_data_manager_ptr->GetDBMnagersPtr();
    //if (!db_manager_ptr)
    //{
    //    Log_ERROR("获取数据库管理对象失败");
    //    return jrscore::CommonError::E_AOI_POINTER_EMPTY;
    //}

    //auto& results_of_rejudgment = online_rejudge_ptr->board_rejudge_result;
    //auto current_time = jtools::TimeUtility::GetCurrentTimeString("%Y-%m-%d %H:%M:%S");
    //for (const auto& [board_rejudge, components] : results_of_rejudgment)
    //{
    //    auto select_table = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
    //    select_table->table_name = jrsdatabase::jrstable::T_BOARD;
    //    select_table->where_condition = "board_barcode='" + board_rejudge.board_barcode + "'";
    //    select_table->where_condition += " order by board_id asc LIMIT 1;";
    //    select_table->select_name = jrsdatabase::jrsselect::T_BOARD_SELECT_BY_WHERE_CONDITION;
    //    auto select_res = db_manager_ptr->Select(select_table);
    //    if (select_res != jrscore::AOI_OK)
    //    {
    //        Log_ERROR("查询板子:", board_rejudge.board_barcode, "失败。");
    //        return -1;
    //    }
    //    auto board_info = select_table->boards;
    //    int board_id = -1;
    //    if (board_info.has_value() && !board_info->empty())
    //    {
    //        auto& board = board_info->front();
    //        board.board_is_rejudgement = board_rejudge.is_rejudged;
    //        board.board_ng_devices = board_rejudge.ng_num;//<update the number of ng components
    //        board.board_rejudgment_time = current_time;
    //        board.board_misjudge_devices = board_rejudge.misjudge_num;//<update the number of misjudge components
    //        board.board_rejudgment_result = static_cast<int>(board_rejudge.detect_result);
    //        db_manager_ptr->Replace(board);
    //        board_id = board.board_id;
    //    }


    //    /**<如果为ignor 则删除子板 元件 检测框等信息 */
    //    if (board_rejudge.detect_result == jrsdata::DetectResult::SKIP)
    //    {
    //        std::string where_condition = "board_id='" + std::to_string(board_id) + "'";
    //        db_manager_ptr->DeleteTables({ { jrsdatabase::jrstable::T_SUBBOARD, where_condition } ,
    //             { jrsdatabase::jrstable::T_DEVICE, where_condition } ,
    //             { jrsdatabase::jrstable::T_DETECT_WINDOW, where_condition } ,
    //             { jrsdatabase::jrstable::T_GROUP, where_condition } ,
    //            });

    //        auto end = std::chrono::high_resolution_clock::now();   // 记录结束时间
    //        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    //        Log_DEBUG(__FUNCTION__, "funtion expend:", std::to_string(duration.count()), "milliseconds");
    //        return jrscore::AOI_OK;
    //    }



    //    // Update device info
    //    std::unordered_map<int, std::vector<jrsdata::ComponentDetectResult>> subboard_and_components;
    //    std::vector<jrsdatabase::CustomUpdate> components_update;
    //    std::vector<jrsdatabase::CustomUpdate> windows_update;

    //    for (const auto& [component_name, component] : components)
    //    {
    //        jrsdatabase::CustomUpdate t_device_update;
    //        t_device_update.table_name = jrsdatabase::jrstable::T_DEVICE;
    //        t_device_update.fields_and_new_value_map = {
    //            {"device_rejudgment_result", std::to_string(component.detect_result)},
    //            {"device_is_rejudged", "1"},
    //            //{"device_rejudgment_flaw_type_names", component.operator_component_rejudgment_flaw_types},  //TODO:类型需要更改
    //            {"device_rejudgment_time",current_time }
    //        };
    //        t_device_update.condition = " board_id= '" + std::to_string(board_id)
    //            + "' and subboard_id= '" + component.subboard_id
    //            + "' and device_name='" + component.component_name + "' and device_result='0'";
    //        components_update.push_back(t_device_update);

    //        for (const auto& [window_name, window] : component.detect_windows_database_info)
    //        {
    //            if (window.detect_window_result)
    //            {
    //                continue;
    //            }
    //            jrsdatabase::CustomUpdate t_detect_window_update;
    //            t_detect_window_update.table_name = jrsdatabase::jrstable::T_DETECT_WINDOW;
    //            t_detect_window_update.fields_and_new_value_map = {
    //                {"window_rejudgement_result", std::to_string(window.detect_window_is_rejudged)},
    //                {"detect_window_is_rejudged", "1"},
    //                {"detect_window_rejudged_type_name", window.detect_window_rejudged_type_name},
    //                {"window_rejudgement_time", current_time},
    //                {"detect_window_ai_rejudged_result", std::to_string(window.detect_window_ai_rejudged_result)},
    //                {"detect_window_ai_rejudged_type_name", window.detect_window_ai_rejudged_type_name},  //TODO:缺陷ID需要做映射！！！
    //            };
    //            t_detect_window_update.condition = " board_id= '" + std::to_string(board_id)
    //                + "' and subboard_id= '" + component.subboard_id
    //                + "' and device_name='" + component.component_name + "' and detect_window_id='" + std::to_string(window.detect_window_id) + "' and detect_window_result='0'";
    //            windows_update.push_back(t_detect_window_update);
    //        }

    //        subboard_and_components[std::stoi(component.subboard_id)].push_back(component);
    //    }
    //    if (!components_update.empty())
    //        db_manager_ptr->CustomUpdate(components_update);
    //    if (!windows_update.empty())
    //        db_manager_ptr->CustomUpdate(windows_update);

    //    // Update subboard info
    //    std::vector<jrsdatabase::CustomUpdate> subboards_update;
    //    for (const auto& [subboard_id, devices] : subboard_and_components)
    //    {
    //        int subboard_ng_devices = 0;
    //        int subboard_no_judgment_devices = (int)devices.size();
    //        bool subboard_is_rejudgement = true;
    //        bool subboard_rejudgment_result = true;
    //        int subboard_misjudge_devices = 0;

    //        for (const auto& device : devices)
    //        {
    //            if (device.detect_result == jrsdata::DetectResult::PASS) // OK
    //            {
    //                ++subboard_misjudge_devices;
    //                --subboard_ng_devices;
    //            }
    //            else // NG
    //            {
    //                subboard_rejudgment_result = false;
    //            }
    //            --subboard_no_judgment_devices;
    //        }

    //        if (subboard_no_judgment_devices == 0)
    //        {
    //            subboard_is_rejudgement = true; //已复判完成
    //        }

    //        jrsdatabase::CustomUpdate subboard_update;
    //        subboard_update.table_name = jrsdatabase::jrstable::T_SUBBOARD;
    //        subboard_update.fields_and_new_value_map = {
    //            {"subboard_ng_devices", std::to_string(subboard_ng_devices)},
    //            {"subboard_no_judgment_devices", std::to_string(subboard_no_judgment_devices)},
    //            {"subboard_rejudgment_time", current_time},
    //            {"subboard_is_rejudgement", std::to_string(subboard_is_rejudgement)},
    //            {"subboard_rejudgment_result", std::to_string(subboard_rejudgment_result)},
    //            {"subboard_misjudge_devices", std::to_string(subboard_misjudge_devices)}
    //        };
    //        subboard_update.condition = " board_id= '" + std::to_string(board_id)
    //            + "' and subboard_id= '" + std::to_string(subboard_id) + "' and subboard_result='0'";
    //        subboards_update.push_back(subboard_update);
    //    }

    //    // Update subboards in batch
    //    if (!subboards_update.empty())
    //    {
    //        db_manager_ptr->CustomUpdate(subboards_update);
    //    }
    //}
    //auto end = std::chrono::high_resolution_clock::now();   // 记录结束时间
    //auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    //Log_DEBUG(__FUNCTION__, "funtion expend:", std::to_string(duration.count()), "milliseconds");
    return jrscore::AOI_OK;
}

void jrsdata::DataManager::SaveThumbnailImage(const cv::Mat& src_img_, const std::string& save_path_, const std::string& save_img_name_,
    const cv::Size& thumbnail_size_)
{
    /**< 保存缩略图 */
    const auto& img = src_img_;
    if (!img.empty())
    {
        auto save_thumb_img_thread = std::thread([this, img, save_path_, save_img_name_, thumbnail_size_]()
            {
                try {
                    // 获取缩略图
                    auto thumbnail_img = jrscore::AOITools::GetThumbnailMat(img, thumbnail_size_.width, thumbnail_size_.height);

                    // 保存缩略图
                    int res = _impl_data->t_data_manager_ptr->SaveImage(thumbnail_img, save_path_ + save_img_name_);

                    // 根据返回结果记录日志
                    if (res != jrscore::AOI_OK) {
                        Log_ERROR("缩略图保存失败! 文件路径: " + save_path_ + save_img_name_);
                    }
                    else {
                        Log_INFO("缩略图保存成功! 文件路径: " + save_path_ + save_img_name_);
                    }
                }
                catch (const std::exception& ex) {
                    // 捕获异常并记录详细错误
                    Log_ERROR("处理缩略图时发生异常: " + std::string(ex.what()));
                }
            });
        save_thumb_img_thread.detach();

    }
}

bool jrsdata::DataManager::CheckDiskCapacity(const std::string& path_, const std::string& action_name_)
{
    auto res = jtools::FileOperation::CheckDiskCapacity(path_);
    if (!res)
    {
        Log_ERROR("磁盘容量不足5%,已无法存储数据," + action_name_ + "失败", "保存路径：", path_);
        JRSMessageBox_WARN("警告", "磁盘容量不足10GB,已无法存储数据," + action_name_ + "失败", jrscore::MessageButton::Ok);
    }
    return res;
}

/** 界面回调 */
void jrsdata::DataManager::SetSettingParamsCallBack(jrsdata::InvokeSettingViewParamFun callback_)
{
    if (callback_)
    {
        _setting_param_callback = callback_;
    }
}

void jrsdata::DataManager::SetProjectCallBack(jrsdata::InvokeProjectFun callback_)
{
    if (callback_)
    {
        _project_param_callback = callback_;
    }
}

void jrsdata::DataManager::SetLogicCallBack(jrsdata::InvokeViewParamBaseFun callback_)
{
    if (callback_)
    {
        _logic_param_callback = callback_;
    }
}

void jrsdata::DataManager::SetSystemStateCallBack(jrsdata::InvokeSystemStateParamFun callback_)
{
    if (callback_)
    {
        sys_state_callback = callback_;
    }
}

void jrsdata::DataManager::SetDetectStatisticsCallBack(jrsdata::InvokeOperateViewParamFun callback_)
{
    if (callback_)
    {
        _operate_callback = callback_;
    }
}

void jrsdata::DataManager::SetComponentDetectResultCallBack(jrsdata::InvokeAlgoEventParamFun callback_)
{
    _detect_result_callback = callback_;
}

void jrsdata::DataManager::SetOnlieDebugInfoCallBack(const jrsdata::InvokeOnlineDebugViewParamFun& callback_)
{
    onlie_debug_info_callback = callback_;
}


/** 数据操作 */
int jrsdata::DataManager::SavePorjectParams(const jrsdata::ViewParamBasePtr& project_param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(project_param_);
    if (!param_temp)
    {
        Log_ERROR("保存工程参数时失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    /**<保存整板参数 */

    //！只有图片没有保存过的时候才保存
    if (!param_temp->project_param->is_save)
    {
        SaveEntiretyBoardImages(project_param_);
    }

    param_temp->project_param->is_save = true;

    /**<更新配置文件中的路径信息  */
    std::string project_path;
    GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PROJECT_PATH, project_path);


    auto res_check_disk_cap = CheckDiskCapacity(project_path, "保存工程");
    if (!res_check_disk_cap)
    {
        return -1;
    }
    //!工程保存和工程读取默认都启用
    param_temp->project_param->file_param.file_path = project_path + "/";

    /**<保存系统参数 */
    auto res = _impl_data->t_data_manager_ptr->Save(*param_temp->project_param);
    if (res != jrscore::AOI_OK)
    {
        Log_ERROR("工程保存失败，请检查");
    }
    _impl_data->project_param_ptr = param_temp->project_param;
    return res;
}

int jrsdata::DataManager::GetProjectParams(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    if (!param_temp)
    {
        Log_ERROR("获取工程参数时失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string project_img_path_father;
    GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH, project_img_path_father);

    auto project_name_temp = param_temp->project_param->project_name;
    std::string project_img_path = project_img_path_father + project_name_temp + "/";

    auto dir_list = jtools::FileOperation::GetFileOrDirNames(project_img_path, true);
    param_temp->project_param->image_group_names.clear();
    for (auto dir : dir_list)
    {
        param_temp->project_param->image_group_names.push_back(dir);
    }
    /** < 读取工程文件*/
    auto res = _impl_data->t_data_manager_ptr->Read(*param_temp->project_param);
    if (res != jrscore::AOI_OK)
    {
        param_temp->project_param->file_param.file_type = jrsdata::FileType::JSON;
        auto res_read_jason = _impl_data->t_data_manager_ptr->Read(*param_temp->project_param);
        if (res_read_jason != jrscore::AOI_OK)
        {
            JRSMessageBox_INFO("读取工程失败", "工程参数更新，旧工程已作废，请重新建立工程!", jrscore::MessageButton::Ok);
            Log_Error_Stack("读取设置参数数据失败");
            param_temp->project_param->file_param.file_type = jrsdata::FileType::BIN;
            return res;
        }
        param_temp->project_param->file_param.file_type = jrsdata::FileType::BIN;
    }

    /** < 工程名称同步 */
    param_temp->project_param->project_name = project_name_temp;
    if (param_temp->event_name != jrsaoi::APPEND_PROJECT_EVENT_NAME)
    {
        _impl_data->project_param_ptr = param_temp->project_param;
    }
    if (_project_param_callback)
    {
        _project_param_callback(param_temp);
    }
    else
    {
        Log_Error_Stack("设置参数数据回调失败，请设置回调函数");
        return -1;
    }
    return jrscore::AOI_OK;
}

int jrsdata::DataManager::GetEntiretyBoardImages(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    if (!param_temp)
    {
        Log_ERROR("获取大图时失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string project_img_path_father;
    GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH, project_img_path_father);
    std::string project_img_path = project_img_path_father + "/" + param_temp->project_param->project_name + "/";
    std::string current_group_name = param_temp->project_param->current_group_name.empty() ? "default" : param_temp->project_param->current_group_name;
    std::string entirety_img_path = project_img_path + "/" + current_group_name + "/";

    std::vector<std::thread> threads;
    for (const auto& img_type : jrsdata::all_img_types)
    {
        int img_type_temp = static_cast<int>(img_type);
        if (img_type_temp + 1 >= all_img_types.size())
        {
            break;
        }
        cv::Mat img;
        std::string img_path;
        /*       if (img_type == jrsdata::LightImageType::HEIGHT)
               {
                   img_path = entirety_img_path + "light_img_" + std::to_string(img_type_temp) + ".tiff";
               }
               else
               {
                   img_path = entirety_img_path + "light_img_" + std::to_string(img_type_temp) + ".png";
               }*/

        img_path = entirety_img_path + "light_img_" + std::to_string(img_type_temp) + ".mbin";
        auto res = _impl_data->t_data_manager_ptr->ReadImageFromBin(img, img_path);
        //auto res = _impl_data->t_data_manager_ptr->ReadImage(img,img_path);
        if (res != jrscore::AOI_OK)
        {
            Log_WARN("读取图片失败，请检查：'", img_path, "' 文件是否存在。");
            //return res; //读取图片失败
        }
        param_temp->project_param->entirety_board_imgs[img_type_temp] = img;
    }
    if (_project_param_callback)
    {
        _project_param_callback(param_temp);
        return jrscore::AOI_OK;
    }
    else
    {
        Log_Error_Stack("设置参数数据回调失败，请设置回调函数");
        return -1;
    }
    return jrscore::AOI_OK;
}
int jrsdata::DataManager::SaveEntiretyBoardImages(const jrsdata::ViewParamBasePtr& param_)
{
    std::vector<std::thread> threads;
    auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    if (!param_temp)
    {
        Log_ERROR("保存大图时失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string project_img_path_father;
    GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH, project_img_path_father);

    std::string project_img_path = project_img_path_father + "/" + param_temp->project_param->project_name + "/";
    std::string current_group_name = param_temp->project_param->current_group_name.empty() ? "default" : param_temp->project_param->current_group_name;
    std::string entirety_img_group_path = project_img_path + current_group_name + "/";
    /**< 检查磁盘容量 */
    auto res_check_disk_cap = CheckDiskCapacity(project_img_path, "保存工程大图");
    if (!res_check_disk_cap)
    {
        return -1;
    }

    auto save_type = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRETY_IMG_TYPE, "获取图片类型");
    if (save_type == ".mbin")
    {
        /**< 保存缩略图 */
        const auto& img = param_temp->project_param->entirety_board_imgs[static_cast<int>(jrsdata::LightImageType::RGB)];
        SaveThumbnailImage(img, entirety_img_group_path);
    }
    return SaveImage(entirety_img_group_path, param_temp->project_param->entirety_board_imgs, "light_img_", save_type);
}

//int jrsdata::DataManager::SaveComponentsImages(const jrsdata::ViewParamBasePtr& param_)
//{
//
//    auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
//    for (auto& component_info : param_temp->standard_info)
//    {
//        auto component_save_info_temp = std::make_shared<jrsdata::ComponentSaveInfo>();
//        component_save_info_temp->project_name = component_info.project_name;
//        component_save_info_temp->subboard_name = component_info.subboard_name;
//        component_save_info_temp->component_name = component_info.component_name;
//        component_save_info_temp->input_img = component_info.input_img;
//        auto res_path = GetComponentInfoSavePath(component_save_info_temp);
//        SaveImage(res_path,component_save_info_temp->input_img);
//    }
//    return 0;
//}

int jrsdata::DataManager::SaveComponentImages(const jrsdata::ViewParamBasePtr& param_)
{
    //TODO:  增加功能
    (void)param_;
    return 0;
}

int jrsdata::DataManager::ReadComponentImages(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    return jrscore::AOI_OK;
}

int jrsdata::DataManager::HandleMotionParam(const jrsdata::ViewParamBasePtr& param_)
{
    jrsdata::OperateViewParamPtr param_temp = std::dynamic_pointer_cast<OperateViewParam>(param_);
    if (!param_temp)
    {
        Log_ERROR("处理运控参数时失败！输入参数为空！");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    if (param_temp->config_setting_param.event_name == "SaveMotionCfgSetting") // 保存运控配置文件数据
    {
        _logic_param_callback(param_);
    }
    return jrscore::AOI_OK;
}
//TODO:HJC TEMP
int jrsdata::DataManager::GetSettingParams(const jrsdata::ViewParamBasePtr& param_)
{
    auto param_temp = std::dynamic_pointer_cast<jrsdata::SettingViewParam>(param_);
    if (param_->event_name == jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME)
    {
        std::string error_info = "";
        std::string description = "";
        error_info = (GetReadSystemParams(param_temp->sys_param) != jrscore::AOI_OK) ? "系统参数读取失败" : error_info;
        if (!error_info.empty())
        {
            description = error_info + ",请检查路径\n" + param_temp->sys_param.file_param.file_path
                + param_temp->sys_param.file_param.file_name + "文件是否存在 \n";
        }
        if (GetReadMachineParams(param_temp->machine_param) != jrscore::AOI_OK)
        {
            error_info += error_info.empty() ? "机台参数读取失败" : "\n机台参数读取失败";
            description += "机台参数读取失败,请检查路径\n" + param_temp->machine_param.file_param.file_path
                + param_temp->machine_param.file_param.file_name + "文件是否存在 \n";
        }
        if (!error_info.empty())
        {
            ChangeSystemState(jrscheckitem::SYSTEM_PARAM_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED,
                { -1,error_info,description });
        }
        else
        {
            ChangeSystemState(jrscheckitem::SYSTEM_PARAM_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::OK);
        }
        auto res = ConnectDatabase(); //连接数据库


        _impl_data->parameter_process_ptr->SetSettingParams(param_temp);/**< 更新所有设置参数 */

        if (_setting_param_callback && _logic_param_callback)
        {
            _setting_param_callback(param_temp);
            _logic_param_callback(param_temp);
        }

        return res;
    }
    if (param_->event_name == jrsaoi::MACHINE_PARAM_UPDATE_EVENT)
    {
        auto res = _impl_data->t_data_manager_ptr->Read(param_temp->machine_param);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("读取设置参数数据失败");
        }
        if (_setting_param_callback)
        {
            _setting_param_callback(param_temp); //更新数据
        }
    }
    if (param_->event_name == jrsaoi::DATABASE_CONNECT_EVENT)
    {
        _impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE] = param_temp->machine_param.machine_params_data;
        //_impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE].insert(param_temp->machine_param.machine_params_seting.begin(),
        //    param_temp->machine_param.machine_params_seting.end()); //将setting 数据也添加到里面。
        ConnectDatabase();
    }
    else if (param_->event_name == jrsaoi::DATABASE_DISCONNECT_EVENT)
    {
        if (jrsdatabase::DBManagers::DisconnectDatabase() == jrscore::AOI_OK)
        {
            Log_INFO("数据库断开连接成功");
            ChangeSystemState(jrscheckitem::DATABASE_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::WAITTING,
                { jrscore::AOI_OK ,"数据库断开连接成功","" }, param_->event_name);
        }
    }

    return jrscore::AOI_OK;
}

int jrsdata::DataManager::GetReadSystemParams(jrsdata::SystemParam& sys_param_)
{
    auto res_sys = _impl_data->t_data_manager_ptr->Read(sys_param_);
    if (res_sys != jrscore::AOI_OK)
    {
        return res_sys;
    }
    else
    {
        _impl_data->all_setting_param_map[jrsdata::ParamLevel::SYSTEM] = sys_param_.sys_params;
        return jrscore::AOI_OK;
    }
}

int jrsdata::DataManager::GetReadMachineParams(jrsdata::MachineParam& machine_param_)
{
    auto res_read = _impl_data->t_data_manager_ptr->Read(machine_param_);
    auto& machine_param_map = machine_param_.machine_params_data;
    auto init_db_res = CheckAndInitDatabase(machine_param_map);
    auto init_paths_res = CheckAndInitMachinePathParams(machine_param_map);
    auto init_machine_param_res = InitMachineParams(machine_param_map);
    _impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE] = machine_param_.machine_params_data;
    if (init_db_res || init_paths_res || init_machine_param_res)
    {
        auto res = _impl_data->t_data_manager_ptr->Save(machine_param_);
        return res;
    }
    return res_read;
}

bool jrsdata::DataManager::CheckAndInitDatabase(jrsdata::SettingParamMap& machine_param_)
{
    auto ip_temp = machine_param_.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP);
    if (ip_temp == machine_param_.end() || ip_temp->second.param_value.empty())
    {
        machine_param_[jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP] = jrsdata::SettingParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP, "std::string", "127.0.0.1", "", static_cast<int>(jrsdata::ParamLevel::MACHINE));
        machine_param_[jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_USER] = jrsdata::SettingParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_USER, "std::string", "jrs", "", static_cast<int>(jrsdata::ParamLevel::MACHINE));
        machine_param_[jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PASSWORD] = jrsdata::SettingParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PASSWORD, "std::string", "Jrs123456", "", static_cast<int>(jrsdata::ParamLevel::MACHINE));
        machine_param_[jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_DATABASE_NAME] = jrsdata::SettingParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_DATABASE_NAME, "std::string", "jrs_aoi_20_db", "", static_cast<int>(jrsdata::ParamLevel::MACHINE));
        return true;
    }
    return true;
}
bool jrsdata::DataManager::IniteMachinePathParam(jrsdata::SettingParamMap& machine_param_, const std::string& key_,
    const std::string& label_, const std::string& default_directory_)
{
    auto result_path = machine_param_.find(key_);
    if (result_path == machine_param_.end() ||
        result_path->second.param_value.empty() ||
        jrscore::ParseJson(result_path->second.param_value, "directory").empty())
    {
        nlohmann::json json;
        json["label"] = label_;
        json["directory"] = default_directory_;
        json["enable"] = true;
        machine_param_[key_] = jrsdata::SettingParam(key_, "std::string", json.dump(), "", static_cast<int>(jrsdata::ParamLevel::MACHINE));
        return true;
    }
    return false;
}

int jrsdata::DataManager::InitMachineParam(jrsdata::SettingParamMap& machine_param_, const std::string& item_name_, const jrsdata::JrsVariant& value_, const std::string& explain_ /*= ""*/)
{
    if (item_name_.empty())
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "设置参数失败：item_name_ 为空！");
        return -2;
    }
    auto& param = machine_param_[item_name_];

    try
    {
        std::visit([&](auto&& arg) {
            using T = std::decay_t<decltype(arg)>;
            if constexpr (std::is_same_v<T, int>)
            {
                param.param_type = "int";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, float>)
            {
                param.param_type = "float";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, double>)
            {
                param.param_type = "double";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, std::string>)
            {
                param.param_type = "string";
                param.param_value = arg;
            }
            else if constexpr (std::is_same_v<T, bool>)
            {
                param.param_type = "bool";
                param.param_value = arg ? "true" : "false";
            }
            else if constexpr (std::is_same_v<T, std::vector<int>>)
            {
                param.param_type = "vector<int>";
                std::ostringstream oss;
                for (size_t i = 0; i < arg.size(); ++i)
                {
                    if (i > 0) oss << ",";
                    oss << arg[i];
                }
                param.param_value = oss.str();
            }
            else
            {
                throw std::runtime_error("不支持的类型");
            }
            }, value_);
        param.param_exp = explain_;
        param.param_level = static_cast<int>(jrsdata::ParamLevel::MACHINE);
    }
    catch (const std::exception& e)
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, std::string("设置参数异常：") + e.what());
        return -3;
    }
    return jrscore::AOI_OK;
}

bool jrsdata::DataManager::CheckAndInitMachinePathParams(jrsdata::SettingParamMap& machine_param_) {
    bool is_save_to_config_file = false;
    is_save_to_config_file |= IniteMachinePathParam(
        machine_param_,
        jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGORITHM_RESULT_PATH,
        "算法结果路径：",
        "D:/AOI/Report/"
    );
    is_save_to_config_file |= IniteMachinePathParam(
        machine_param_,
        jrssettingparam::jrsmachineparam::MACHINE_PARAM_PROJECT_PATH, /**< 工程路径*/
        "工程路径：",
        "D:/__Project/ProjectFile/"
    );
    is_save_to_config_file |= IniteMachinePathParam(
        machine_param_,
        jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH, /**< 原件库路径*/
        "元件库路径：",
        "D:/__Project/Image/Components/"
    );
    is_save_to_config_file |= IniteMachinePathParam(
        machine_param_,
        jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH, /**< 整板大图*/
        "元件库路径：",
        "D:/__Project/Image/EntiretyBoard/"
    );
    return is_save_to_config_file;
}

bool jrsdata::DataManager::InitMachineParams(jrsdata::SettingParamMap& machine_param_)
{
    bool is_updata_file = false;
    if (machine_param_.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL) == machine_param_.end())
    {
        InitMachineParam(machine_param_, jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL, false);
        is_updata_file = true;
    }
    /**<初始化图片读取类型 */
    if (machine_param_.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRETY_IMG_TYPE) == machine_param_.end())
    {
        InitMachineParam(machine_param_, jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRETY_IMG_TYPE, ".mbin");
        is_updata_file = true;
    }
    return is_updata_file;
}

int jrsdata::DataManager::ConnectDatabase()
{
    jrsdatabase::DatabaseConnectParam db_conn_param("127.0.0.1", "jrs", "Jrs123456", "jrs_aoi_20_db", 30);
    //if (machine_param_map.empty())
    /*{
      Log_WARN("获取机台参数失败");
    }
    */
    {
        ChangeSystemState(jrscheckitem::DATABASE_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::CHECKING,
            { jrscore::AOI_OK ,"","" }, jrsaoi::DATABASE_CONNECT_EVENT);

        auto ip = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_IP, "IP地址参数");
        auto user = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_USER, "用户名参数");
        auto pwd = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PASSWORD, "密码参数");
        auto db = GetMachineSpecificParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_DATABASE_NAME, "数据库名称参数");
        if (!ip.empty() || !user.empty() || !pwd.empty() || !db.empty())
        {
            db_conn_param = { ip,user,pwd,db,10 };
        }
        else
        {
            return jrscore::DataManagerError::E_AOI_DB_MACHINE_PARAM_READ_FAILURE;
        }
    }
    auto res = jrsdatabase::DBManagers::InitDatabase(db_conn_param);
    if (res != jrscore::AOI_OK)
    {
        Log_ERROR("数据库初始化失败");
        ChangeSystemState(jrscheckitem::DATABASE_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::FIALED,
            { -1 ,"数据库初始化失败","" }, jrsaoi::DATABASE_CONNECT_EVENT);
    }
    else
    {
        Log_INFO("数据库初始化成功");
        ChangeSystemState(jrscheckitem::DATABASE_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::OK,
            { jrscore::AOI_OK ,"数据库" + db_conn_param.db_ip + "初始化成功" ,"" }, jrsaoi::DATABASE_CONNECT_EVENT);
    }

    return res;
}

int jrsdata::DataManager::SaveSettingParams(const jrsdata::ViewParamBasePtr& param_)
{
    int res = jrscore::AOI_OK;
    auto param_temp = std::static_pointer_cast<jrsdata::SettingViewParam>(param_);
    if (param_->event_name == jrsaoi::SYSTEM_PARAM_SAVE_EVENT)
    {
        res = _impl_data->t_data_manager_ptr->Save(param_temp->sys_param);
        _impl_data->all_setting_param_map[jrsdata::ParamLevel::SYSTEM] = param_temp->sys_param.sys_params;
    }
    else if (param_->event_name == jrsaoi::COMMON_PARAM_SAVE_EVENT)
    {
        res = _impl_data->t_data_manager_ptr->Save(param_temp->comm_param);
    }
    else if (param_->event_name == jrsaoi::MACHINE_PARAM_SAVE_EVENT)
    {
        res = _impl_data->t_data_manager_ptr->Save(param_temp->machine_param);
        _impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE] = param_temp->machine_param.machine_params_data;
        //_impl_data->all_setting_param_map[jrsdata::ParamLevel::MACHINE].insert(param_temp->machine_param.machine_params_seting.begin(),
        //    param_temp->machine_param.machine_params_seting.end()); //将setting 数据也添加到里面。
        //TODO XIXI 
        std::string company_no = "";
        std::string machine_no = "";
        std::string site_no = "";
        std::string thread_no = "";
        for (auto& machine_param_temp : param_temp->machine_param.machine_params_data)
        {
            if (machine_param_temp.first == jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPANY_NO)
            {
                company_no = machine_param_temp.second.param_value;
            }
            if (machine_param_temp.first == jrssettingparam::jrsmachineparam::MACHINE_PARAM_MACHINE_NO)
            {
                machine_no = machine_param_temp.second.param_value;
            }
            if (machine_param_temp.first == jrssettingparam::jrsmachineparam::MACHINE_PARAM_SITE_NO)
            {
                site_no = machine_param_temp.second.param_value;
            }
            if (machine_param_temp.first == jrssettingparam::jrsmachineparam::MACHINE_PARAM_THREAD_NO)
            {
                thread_no = machine_param_temp.second.param_value;
            }
        }
        if (!company_no.empty() && !machine_no.empty() && !site_no.empty() && !thread_no.empty())
        {
            jrsdatabase::jrsselect::SelectorParamBasePtr select_table_ptr = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
            select_table_ptr->table_name = jrsdatabase::jrstable::T_AOI_MACHINE;
            select_table_ptr->select_name = jrsdatabase::jrsselect::T_AOI_MACHINE_SELECT_BY_WHERE_CONDITION;
            select_table_ptr->where_condition = "";
            ReadDataFromDatabase(select_table_ptr);
            auto select_ptr = std::dynamic_pointer_cast<jrsdatabase::jrsselect::SelectTable>(select_table_ptr);
            if (select_ptr->aoi_machines.has_value() && !select_ptr->aoi_machines->empty() && res == jrscore::AOI_OK)
            {
                for (auto& aoi_machine : select_ptr->aoi_machines.value())
                {
                    aoi_machine.company_no = company_no;
                    aoi_machine.machine_no = machine_no;
                    aoi_machine.site_no = site_no;
                    aoi_machine.thread_no = thread_no;
                    std::optional<std::pair<jrsdatabase::TablesDataOperator::OperatorType, jrsdatabase::jrstable::TAOIMachine>> aoi_syss_temp;
                    aoi_syss_temp = std::make_pair(jrsdatabase::TablesDataOperator::OperatorType::Replace, aoi_machine);
                    std::shared_ptr<jrsdatabase::TablesDataOperator> table_data_operator_ = std::make_shared<jrsdatabase::TablesDataOperator>();
                    table_data_operator_->aoi_syss = aoi_syss_temp;
                    DBTableDataOperator(table_data_operator_);
                }
            }
        }
    }

    _impl_data->parameter_process_ptr->SetSettingParams(param_temp);/**< 更新所有设置参数 */

    if (_setting_param_callback) //更新所有数据
    {
        _setting_param_callback(param_temp);
        _logic_param_callback(param_temp);
    }

    return res;
}

int jrsdata::DataManager::SaveDetectResultParam(const jrsdata::ViewParamBasePtr& param_)
{
    jrsdata::EntiretyBoardResultPtr entirety_board_result = std::static_pointer_cast<jrsdata::EntiretyBoardResult>(param_);
    return _impl_data->t_data_manager_ptr->Save(*entirety_board_result);
}

JSON jrsdata::DataManager::GetSystemParam(const std::string& config_name_, const std::string& field_name_, const  std::any& default_value_)
{
    auto it = jrsfiledata::JrsGlobalTable::JrsGetObj(config_name_);
    if (it)
    {
        return it->GetValue(field_name_, default_value_);
    }
    return JSON();
}

int jrsdata::DataManager::ReinitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_)
{
    return jrsdatabase::DBManagers::ReinitDatabase(db_conn_param_);
}

int jrsdata::DataManager::InitSystem(const jrsdata::ViewParamBasePtr& param_)
{
    auto init_sys_param = std::static_pointer_cast<jrsdata::SystemStateViewParam>(param_);
    {
        std::lock_guard<std::mutex> lock(_mtxsys_state_param);
        _impl_data->sys_state_param_ptr = init_sys_param;
    }
    if (init_sys_param->check_item_name == jrscheckitem::CHECK_ALL_ITEMS)
    {
        std::thread t([=]() {
            InitSettingParam();
            });
        //初始化所有内容
        t.detach();
    }
    else if (init_sys_param->check_item_name == jrscheckitem::DATABASE_CHECK_ITEM)
    {
    }
    else if (init_sys_param->check_item_name == jrscheckitem::SYSTEM_PARAM_CHECK_ITEM)
    {
        //系统文件初始化
        InitSettingParam();
    }
    return jrscore::AOI_OK;
}

void jrsdata::DataManager::InitSettingParam()
{
    ChangeSystemState(jrscheckitem::SYSTEM_PARAM_CHECK_ITEM, jrsdata::MachineCheckParamInfo::CheckState::CHECKING);
    jrsdata::SettingViewParamPtr setting_view_param_ptr = std::make_shared<jrsdata::SettingViewParam>();
    setting_view_param_ptr->event_name = jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME;
    GetSettingParams(setting_view_param_ptr);
}

void jrsdata::DataManager::ChangeSystemState(const std::string& check_name_, const jrsdata::MachineCheckParamInfo::CheckState& state_,
    const std::tuple<int, std::string, std::string>& code_and_info_/* = std::tuple(jrscore::AOI_OK, "", "")*/,
    const std::string& event_name_/* = jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT*/)
{
    if (!_impl_data->sys_state_param_ptr)
    {
        Log_ERROR("系统状态指针为空,请检查");
        return;
    }
    _impl_data->sys_state_param_ptr->event_name = event_name_;
    auto it = _impl_data->sys_state_param_ptr->check_items.find(check_name_);
    if (it != _impl_data->sys_state_param_ptr->check_items.end())
    {
        std::lock_guard<std::mutex> lock(_mtxsys_state_param);
        it->second.check_state = state_;
        auto [code, error_info, description] = code_and_info_;
        it->second.code = code;
        it->second.err_info = error_info;
        it->second.description = description;
    }
    else
    {
        _impl_data->sys_state_param_ptr->check_items[check_name_] = jrsdata::MachineCheckParamInfo{ check_name_,state_ };//添加新的
    }
    if (sys_state_callback)
    {
        sys_state_callback(_impl_data->sys_state_param_ptr);
    }
}

int jrsdata::DataManager::SaveDetectResult(const jrsdata::ViewParamBasePtr& param_)
{
    auto result_data = std::static_pointer_cast<jrsdata::DetectResultParam>(param_);
    if (result_data == nullptr || _impl_data->project_param_ptr == nullptr)
    {
        Log_ERROR("结果参数为空，结果保存失败");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    _impl_data->param_convertor_ptr->SetSettingParamPtr(_impl_data->parameter_process_ptr);

    auto entirety_board_result = _impl_data->param_convertor_ptr->DetectResultToEntiretyBoardParam(_impl_data->project_param_ptr, result_data, _impl_data->all_setting_param_map);

    /**<将元件结果显示到界面上 */
    ComponentDetectResultToRender(result_data->component_status_result);
    //! 只有ng的才需要动态调试
    if (!result_data->detect_result && result_data->board_detect_status == jrsdata::BoardDetectStatus::NORMAL)
    {
        OnlineDebugInfoToOnlineView(result_data->online_debug_param_ptr);
    }
    /** <结果保存到数据库 */
    int res = jrscore::AOI_OK;
    res = _impl_data->t_data_manager_ptr->Save(entirety_board_result.entirety_board_result);
    if (res != jrscore::AOI_OK)
    {
        Log_ERROR("检测结果保存到数据库失败，请检查!");
    }


    /** 保存图片 */
    //! 保存元件图片
    //! 维修站路径是否启用
    std::string path_save_temp;
    auto should_save_images = GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH, path_save_temp);

    // 提前判断是否有可用图像路径用于磁盘容量检查
    if (should_save_images && !entirety_board_result.component_images.empty())
    {
        if (!CheckDiskCapacity(path_save_temp, "保存元件图片信息"))
        {
            should_save_images = false;
        }
        if (should_save_images)
        {
            std::thread t([=]() {
                for (auto component_detect_type : entirety_board_result.component_images)
                {
                    if (!component_detect_type.first)
                    {
                        for (auto component_image : component_detect_type.second)
                        {
                            _impl_data->t_data_manager_ptr->SaveImage(component_image.second, component_image.first);
                        }
                    }
                }
                });
            t.detach();
        }
    }

    std::string report_save_path;
    auto is_algo_info_save = GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGORITHM_RESULT_PATH, report_save_path);
    //! 保存除了mark之外的元件执行的信息
    if (is_algo_info_save && !result_data->workflow_component_algo_info_vector.empty())
    {
        if (!CheckDiskCapacity(report_save_path, "保存元件执行信息"))
        {
            is_algo_info_save = false;
        }
        if (is_algo_info_save)
        {
            std::thread save_execute_algo_info_thread([=]() {
                for (auto& value : result_data->workflow_component_algo_info_vector)
                {
                    if (value->algo_name.find("Mark") == std::string::npos)
                    {
                        SaveComponentExecuteInfo(value);
                    }
                }
                });
            save_execute_algo_info_thread.detach();
        }
    }

    //! 保存算法报表信息
    SaveAlgoReport(result_data, report_save_path);

    //!  保存MARK的执行的信息
    std::string mark_save_path;
    auto is_mark_algo_info_save = GetMachineSpecificSavePathParam(jrssettingparam::jrsmachineparam::MACHINE_PARAM_LOCATION_POINT_IMAGE_PATH, mark_save_path);
    if (is_mark_algo_info_save && !result_data->workflow_component_algo_info_vector.empty())
    {
        if (!CheckDiskCapacity(mark_save_path, "保存mark执行信息"))
        {
            is_mark_algo_info_save = false;
        }
        if (is_mark_algo_info_save)
        {
            std::thread save_execute_algo_info_thread([=]() {
                for (auto& value : result_data->workflow_component_algo_info_vector)
                {
                    if (value->algo_name.find("Mark") != std::string::npos)
                    {
                        SaveComponentExecuteInfo(value);
                    }
                }
                });
            save_execute_algo_info_thread.detach();
        }
    }



    //for (auto algo_name_images : entirety_board_result.algorithm_images)
    //{
    //    for (auto algo_image : algo_name_images.second)
    //    {
    //        _impl_data->t_data_manager_ptr->SaveImage(algo_image.second, algo_image.first);
    //    }
    //}
    for (auto& entirety_board_image : entirety_board_result.entirety_board_images)
    {
        if (!CheckDiskCapacity(entirety_board_image.first, "保存结果大图"))
        {
            break;
        }
        _impl_data->t_data_manager_ptr->SaveImage(entirety_board_image.second, entirety_board_image.first);
    }

    /** < 将结果传到界面 */
    GetDetectResultStatistics(entirety_board_result.detect_statistics_param);
    return res;
}
int jrsdata::DataManager::GetDetectResultStatistics(const std::optional<jrsdata::DetectStatisticsViewParam>& param_)
{
    if (!param_.has_value())
    {
        return -1;
    }
    auto operate_param = std::make_shared<jrsdata::OperateViewParam>();
    operate_param->detect_statistics_view_param = *param_;
    operate_param->event_name = jrsaoi::OPERATE_DETECT_STATISICS_UPDATE;
    /** < 将结果传到界面 */
    if (_operate_callback)
    {
        _operate_callback(operate_param);
    }
    return jrscore::AOI_OK;
}

int jrsdata::DataManager::ComponentDetectResultToRender(const std::vector<jrsdata::ComponentDetectResult> component_detect_results_)
{
    jrsdata::AlgoEventParamPtr algo_param_ptr = std::make_shared<jrsdata::AlgoEventParam>();
    algo_param_ptr->component_detect_results = component_detect_results_;
    algo_param_ptr->event_name = jrsaoi::RENDER2D_COMPONENT_DETECT_RESULT_EVENT_NAME;
    if (_detect_result_callback)
    {
        _detect_result_callback(algo_param_ptr);
    }
    return 0;
}

int jrsdata::DataManager::ReadDataFromDatabase(const std::shared_ptr<jrsdatabase::jrsselect::SelectorParamBase>& table_select_ptr_)
{
    auto db_manager_ptr = _impl_data->t_data_manager_ptr->GetDBMnagersPtr();
    if (db_manager_ptr)
    {
        return db_manager_ptr->Select(table_select_ptr_);
    }
    else
    {
        return -1;
    }
}

int jrsdata::DataManager::DBTableDataOperator(const std::shared_ptr < jrsdatabase::TablesDataOperator>& table_data_operator_)
{
    if (!table_data_operator_)
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    _impl_data->t_data_manager_ptr;
    auto res = jrscore::AOI_OK;
    if (table_data_operator_->aoi_syss.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->aoi_syss.value());
    }
    if (table_data_operator_->system_params.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->system_params.value());
    }
    if (table_data_operator_->boards.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->boards.value());
    }
    if (table_data_operator_->subboards.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->subboards.value());
    }
    if (table_data_operator_->devices.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->devices.value());
    }
    if (table_data_operator_->detect_windows.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->detect_windows.value());
    }
    if (table_data_operator_->groups.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->groups.value());
    }
    if (table_data_operator_->projects.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->projects.value());
    }
    if (table_data_operator_->users.has_value())
    {
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->users.value());
    }
    if (table_data_operator_->detect_types.has_value())
    {
        res = _impl_data->t_data_manager_ptr->DeleteTable(jrsdatabase::jrstable::T_DETECT_TYPE);
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR("清除 type 失败，请检查");
            return res;
        }
        res = _impl_data->t_data_manager_ptr->OperatorTableData(table_data_operator_->detect_types.value());
    }
    return res;
}

