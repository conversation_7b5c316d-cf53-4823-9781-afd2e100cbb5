/*****************************************************************//**
 * @file   preview.h
 * @brief  图像浏览窗口
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef PREVIEW_WINDOW_H_
#define PREVIEW_WINDOW_H_

#if _MSC_VER >= 1600
#pragma execution_character_set("utf-8")
#endif
#define IMAGETYPE  1
#define IMAGECHANNEL 7
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>
// qt
#include <QPixmap>
#pragma warning(pop)
// thirparty
#include "customlabel.h"
class CustomQLabel;
class QPushButton;
class QGridLayout;
class QMouseEvent;
class QPaintEvent;
using std::vector;
using cv::Mat;
/**
* @class  PreviewWindow
* @brief  图像浏览窗口
* @date   2024.08.18
* <AUTHOR>
*/
class PreviewWindow : public QWidget
{
    Q_OBJECT

public:
	/**
    * @fun  PreviewWindow
    * @brief  构造函数
    * @date   2024.08.18
    * <AUTHOR>
    */
    explicit PreviewWindow(QWidget* parent = nullptr);
	/**
    * @fun  SetProcessImages
    * @brief  设置需要显示的图像组并初始化ui
    * @date   2024.08.18
    * <AUTHOR>
    */
	void SetProcessImages(const vector<Mat>&process_image_group);
    ~PreviewWindow();
signals:
    void UpdateCurrentId(int id);
protected:
    void closeEvent(QCloseEvent* event) override;
public slots:
	/**
    * @fun  GetCurrentImageIdAndShow
    * @brief  获取当前的ID 并显示原始分辨率图像
    * @date   2024.08.18
    * <AUTHOR>
    */
    void GetCurrentImageIdAndShow(int id);
	/**
    * @fun  GetCurrentId
    * @brief  获取当前的图像ID
    * @date   2024.08.18
    * <AUTHOR>
    */
    void GetCurrentId(int id);
	/**
    * @fun  UpdateCurrentIdSlot
    * @brief  更新图像ID槽
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateCurrentIdSlot();
	/**
    * @fun  UpdateLabelState
    * @brief  更新显示图像UI的显示状态
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateLabelState(int id);
private:
	QPushButton* confire = nullptr;
    QGridLayout* grid_layout = nullptr;
    vector<Mat> process_image_group_;
    std::map<int, CustomQLabel*> button_group;

	int image_width_;
    int image_height_;
    int label_size = 150;
    int id_ = 0;
    void SetupUi();
};

#endif  // PREVIEW_WINDOW_H_
