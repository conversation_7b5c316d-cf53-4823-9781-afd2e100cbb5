/*********************************************************************
 * @brief  管理自定义鼠标指针的类，
 * 支持加载指针图片并通过枚举值切换指针样式.
 *
 * @file   customcursormanager.h
 * @date   2024.11.13
 * <AUTHOR>
**********************************************************************/
#ifndef CUSTOMCURSORMANAGER_H
#define CUSTOMCURSORMANAGER_H

#include "customcursortype.hpp"

#include <QCursor>
// #include <QHash>
#include <unordered_map>

/**
 * @class CustomCursorManager
 * @brief 管理自定义鼠标指针的类，支持加载指针图片并通过枚举值切换指针样式。
 *
 * 该类提供了加载自定义图片作为鼠标指针、切换指针样式以及恢复默认指针的功能。
 * 通过枚举类型或整数值，可以方便地管理多种指针样式。
 */
class CustomCursorManager
{
public:
    /**
     * @brief 构造函数，初始化指针样式。
     *
     * 默认情况下，初始化了四种预定义的指针样式。
     */
    CustomCursorManager();
    ~CustomCursorManager() {}

    /**
     * @brief 加载自定义指针图片。
     *
     * 该方法用于加载给定路径的图片文件，并将其设置为指定的指针样式。
     *
     * @param type 指定的指针样式类型。
     * @param filePath 自定义图片文件的路径。
     * @return 如果加载成功，返回true；如果加载失败，返回false。
     */
    bool AddCustomCursor(const CustomCursorType& type, const char* filename);

    /**
     * @brief 设置当前的鼠标指针样式。
     *
     * 该方法根据提供的指针样式类型，设置当前的鼠标指针。如果当前指针样式与目标样式相同，不会重复设置。
     *
     * @param type 要设置的指针样式。
     */
    void SetCursor(const CustomCursorType& type);

    /**
     * @brief 恢复默认指针样式。
     *
     * 该方法将鼠标指针恢复为默认样式。
     */
    void ResetCursor();

    // /**
    //  * @brief 将指针类型转换为字符串，便于调试。
    //  *
    //  * 该方法将CustomCursorType枚举值转换为字符串表示，便于在调试过程中查看指针类型。
    //  *
    //  * @param type 要转换的指针样式类型。
    //  * @return 返回指针样式的字符串表示。
    //  */
    // static QString CustomCursorTypeToString(CustomCursorType type);

    QCursor ImportCurCursor(const char* filename);
    QCursor ImportAniCursor(const char* filename, int hot_x = -1, int hot_y = -1);

private:
    std::unordered_map<CustomCursorType, QCursor> cursorMap;  /**< 存储不同类型的指针样式映射 */
    CustomCursorType currentCustomCursorType;  /**< 当前使用的指针样式 */
};

#endif // CUSTOMCURSORMANAGER_H
