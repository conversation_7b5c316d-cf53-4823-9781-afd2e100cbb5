//QT
#include <QVBoxLayout>
#include <QHeaderView>
#include <QLabel>
#include <QSortFilterProxyModel >
//Custom
#include "customtablelistview.h"
#include "nocoverbackgrounddelegate.hpp"
namespace jrsaoi
{

    CustomTableListView::CustomTableListView(QStringList horizontal_header_data_, QString show_list_name_, RowColorRule color_rule_, int filter_key_column_,QWidget *parent)
        : QWidget(parent)
        , horizontal_header_data(horizontal_header_data_)
        , show_list_name(show_list_name_)
        , m_color_rule(color_rule_)
        , show_list_model_data(new CustomDataModel(this))
        , filter_key_column(filter_key_column_)
    {
        InitView();
        InitConnect();
        this->setContentsMargins(0,0,0,0);
    }

    CustomTableListView::~CustomTableListView()
    {
    }

    void CustomTableListView::InitView()
    {
        //!新建一个QTableView，然后以垂直布局添加到widget中
        show_table_view = new QTableView(this);
        QLabel* show_list_name_label = new QLabel(this);
        auto layout_v = new QVBoxLayout(this);
        layout_v->setContentsMargins(0,0,0,0);
        layout_v->addWidget(show_list_name_label);
        layout_v->addWidget(show_table_view);

        //! 
        show_table_view->setTextElideMode(Qt::ElideNone);
        //! 不显示垂直表头
        show_table_view->verticalHeader()->setVisible(false);

        //! 设置表格整行选中
        show_table_view->setSelectionBehavior(QAbstractItemView::SelectRows);
        //! 不允许多选
        show_table_view->setSelectionMode(QAbstractItemView::SingleSelection);
        //! 不允许编辑
        show_table_view->setEditTriggers(QAbstractItemView::NoEditTriggers);
        //! 允许排序
        show_table_view->setSortingEnabled(true);
      
        //! 设置tableview选中时，不显示背景，只有边框高亮显示 by zhangyuyu 2025.2.7
        show_table_view->setItemDelegate(new NoCoverBackgroundDelegate());
        show_list_model_data->SetHorizontalHeaderData(horizontal_header_data);
        show_list_model_data->SetColorRules(m_color_rule);
        show_table_view->setModel(show_list_model_data);
        show_list_name_label->setText(show_list_name);


        //! 过滤
        filter_model = new QSortFilterProxyModel(this);
        filter_model->setSourceModel(show_list_model_data);
        filter_model->setFilterKeyColumn(filter_key_column); // 设置过滤的列为第一列
        filter_model->setFilterCaseSensitivity(Qt::CaseInsensitive); // 设置过滤时不区分大小写
        show_table_view->setModel(filter_model);
                    
    }

    void CustomTableListView::InitConnect()
    {
        connect(show_table_view->selectionModel(), &QItemSelectionModel::selectionChanged, this, &CustomTableListView::SlotCurrentRowChanged);

    }

    void CustomTableListView::UpdateList(const std::vector<std::vector<QVariant>>& show_data)
    {

        // 1. 记录当前选中的行
        QModelIndexList selected_rows = show_table_view->selectionModel()->selectedRows();
        int selected_row = -1;
        if (!selected_rows.isEmpty())
        {
            selected_row = selected_rows.first().row(); // 获取第一个选中的行号
        }

        show_list_model_data->SetData(show_data);
        //! 当整体更新数据时，默认选中第一行
        if (show_table_view->model() && show_table_view->model()->rowCount() > 0)//! 判断tableview是否有数据
        {
            if (selected_row != -1)
            {

                show_table_view->selectRow(selected_row);
            }
            else 
            {
                show_table_view->selectRow(0);

            }

        }
        //! 先设置最后一列拉伸
        show_table_view->horizontalHeader()->setStretchLastSection(true);

        //! 自动调整列宽以适应内容
        show_table_view->horizontalHeader()->resizeSections(QHeaderView::ResizeToContents);

        //! 根据第三列的内容排序
        //show_table_view->sortByColumn(2, Qt::AscendingOrder);



    }
    void CustomTableListView::AddRowNewItems(const std::vector<std::vector<QVariant>>& show_data)
    {
        if (show_data.empty())
        {
            return;
        }
        show_list_model_data->AddRows(show_data);

        QModelIndexList selected_index = show_table_view->selectionModel()->selectedRows();
        if (selected_index.isEmpty() && show_table_view->model() && show_table_view->model()->rowCount() > 0)
        {
            show_table_view->selectRow(0);
        }

    }
    void CustomTableListView::DeleteRowItemsFromCurrentRow(int count)
    {
        QModelIndexList selected_index = show_table_view->selectionModel()->selectedRows();
        if (!selected_index.isEmpty())
        {
            show_list_model_data->DeleteRows(selected_index.first().row(), count);

        }

    }
    void CustomTableListView::DeleteRowByColumnValue(int column, const QVariant& value)
    {
        show_list_model_data->DeleteRowByColumnValue(column, value);

    }
    bool CustomTableListView::NextRow()
    {
        QItemSelectionModel* selection_model = show_table_view->selectionModel();
        QModelIndexList selected_indexes = selection_model->selectedIndexes();

        //! 获取当前选中行的第一个索引
        if (!selected_indexes.isEmpty())
        {
            //! 当前选中的一个单元格(可能选中了多个行数据)
            QModelIndex current_index = selected_indexes.first();
            int current_row = current_index.row();

            // !确保有下一行
            if (current_row < show_list_model_data->rowCount() - 1)
            {
                //! 获取下一行的第一个单元格
                QModelIndex next_row_index = show_list_model_data->index(current_row + 1, 0);
                selection_model->setCurrentIndex(next_row_index, QItemSelectionModel::ClearAndSelect | QItemSelectionModel::Rows);  // 选中下一行
                //! 成功下一行
                return true;
            }
            else
            {
                //! 没有下一行,已经到最后一行
                return false;
            }
        }
        return true;
    }
    bool CustomTableListView::PreRow()
    {
        QItemSelectionModel* selection_model = show_table_view->selectionModel();
        QModelIndexList selected_indexes = selection_model->selectedIndexes();

        //! 获取当前选中行的第一个索引
        if (!selected_indexes.isEmpty())
        {
            //! 当前选中的一个单元格(可能选中了多个行数据)
            QModelIndex current_index = selected_indexes.first();
            int current_row = current_index.row();

            //! 确保有上一行
            if (current_row > 0)
            {
                QModelIndex next_row_index = show_list_model_data->index(current_row - 1, 0);  // 获取下一行的第一个单元格
                selection_model->setCurrentIndex(next_row_index, QItemSelectionModel::ClearAndSelect | QItemSelectionModel::Rows);  // 选中下一行

                //! 成功上一行
                return true;
            }
            else
            {

                //! 已经第一行
                return false;
            }
        }
        return true;
    }

    void CustomTableListView::UpdateDataRowData(const std::vector<QVariant>& current_row_new_data)
    {
        QItemSelectionModel* selection_model = show_table_view->selectionModel();
        QModelIndexList selected_indexes = selection_model->selectedIndexes();
        //! 获取当前选中行的第一个索引
        if (!selected_indexes.isEmpty())
        {
            //! 当前选中的一个单元格(可能选中了多个行数据)
            QModelIndex current_index = selected_indexes.first();
            int current_row = current_index.row();
            show_list_model_data->UpdateRowData(current_row, current_row_new_data);
        }
    }

    void CustomTableListView::ClearAllData()
    {
        show_list_model_data->ClearAllData();
    }

    void CustomTableListView::FileterTextChanged(const QString& filter_text_)
    {
        QRegExp reg_exp(filter_text_, Qt::CaseInsensitive, QRegExp::FixedString);
        filter_model->setFilterRegExp(reg_exp);
    }

    std::vector<QVariant> CustomTableListView::GetCurrentRowData() const
    {

        QModelIndexList selected_indexes = show_table_view->selectionModel()->selectedRows();
        if (!selected_indexes.isEmpty())
        {
            int selected_row = selected_indexes.first().row();
            return show_list_model_data->GetRowData(selected_row);
        }
        return {};
    }

    void CustomTableListView::SlotCurrentRowChanged(const QItemSelection& selected, const QItemSelection& deselected)
    {
        Q_UNUSED(selected);
        Q_UNUSED(deselected);
        QModelIndexList selected_indexes = show_table_view->selectionModel()->selectedRows();
        if (!selected_indexes.isEmpty())
        {
            //! 可能会选中多行，这里只取第一行
            int selected_row = selected_indexes.first().row();
            std::vector<QVariant> current_row_data = show_list_model_data->GetRowData(selected_row);
            emit SigCurrentRowData(current_row_data);
        }
    }


}