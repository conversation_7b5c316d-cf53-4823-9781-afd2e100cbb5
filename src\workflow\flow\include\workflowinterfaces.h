#ifndef __JRSWORKFLOWINTERFACES_H__
#define __JRSWORKFLOWINTERFACES_H__

//STD
#include <iostream>
#include <memory>
#include <functional>

//Custom
#include "viewparam.hpp"
#include "calcfov.h"
#include "pcbpathplan.h"
#include "dataparam.h"
namespace jrsdata
{
    struct JrsImageBuffer;
    struct ProjectParam;
    struct DataBase;
}


namespace jrsworkflow
{
    struct WorkFlowParam
    {
        //! 软件初始化时传入
        int camera_fov_w;/**<相机FOV宽度*/
        int camera_fov_h;/**<相机FOV高度*/
        float resolution_x;/**<相机X分辨率*/
        float resolution_y;/**<相机Y分辨率*/
        jrsdata::MotionSetting motion_set_param;/**< 运控设置的参数*/

        //！动态调试相关操作时传入
        std::atomic<bool> is_enable_online_debug;/**<是否启用在线调试*/
        std::atomic<bool> finished_online_debug;/**<当前板子调试完成*/
        std::atomic<bool> is_stop_debug;/**<是否停止调试*/
        //！自动流程运行时传入
        jrsdata::SettingViewParamPtr setting_param_ptr; /**< 机台设置参数*/
        std::map<std::string,jrsdata::ProjectParamPtr> project_param_map; /**< 工程参数列表*/
        WorkFlowParam()
            : camera_fov_w(0)
            , camera_fov_h(0)
            , resolution_x(0)
            , resolution_y(0)
            , motion_set_param{}
            , is_enable_online_debug(false)
            , finished_online_debug(true)
            , is_stop_debug(true)
            , setting_param_ptr{}
            , project_param_map{}
        {
        }

        // 自定义复制构造函数
        WorkFlowParam(const WorkFlowParam& other)
            : camera_fov_w(other.camera_fov_w)
            , camera_fov_h(other.camera_fov_h)
            , resolution_x(other.resolution_x)
            , resolution_y(other.resolution_y)
            , motion_set_param(other.motion_set_param)
            , is_enable_online_debug(other.is_enable_online_debug.load()) 
            , finished_online_debug(other.finished_online_debug.load())
            , is_stop_debug(other.is_stop_debug.load())
            , setting_param_ptr(other.setting_param_ptr)
            , project_param_map(other.project_param_map)
        {
        }

        // 自定义赋值运算符
        WorkFlowParam& operator=(const WorkFlowParam& other)
        {
            if (this != &other)
            {
                camera_fov_w = other.camera_fov_w;
                camera_fov_h = other.camera_fov_h;
                resolution_x = other.resolution_x;
                resolution_y = other.resolution_y;
                motion_set_param = other.motion_set_param;
                is_enable_online_debug.store(other.is_enable_online_debug.load());
                finished_online_debug.store(other.finished_online_debug.load());
                is_stop_debug.store(other.is_stop_debug.load());
                setting_param_ptr = other.setting_param_ptr;
                project_param_map = other.project_param_map;
            }
            return *this;
        }
    };
    using LogicFunType = std::function<void(const std::vector<PCBPathPlanning::Fov>&,bool)>;

    /**
     * .  by zhangyuyu 2024.11.25
     *
     * @brief 检测结果基类,主要用于方便不同机型之间的接口统一
     */
    struct InspectionResultBase
    {
        InspectionResultBase() = default;
        virtual ~InspectionResultBase() = default;
        virtual bool GetInspectionResultStatus() = 0;
        virtual void SetSpeficParam(const std::string& key, const std::any& value)
        {
            (void)key;
            (void)value;
            // 默认不做处理（有些子类不关心）
        }

    };

    /**
     * .  by zhangyuyu 2024.11.25
     *
     * @brief ARP流程检测结果参数
     */
    struct FlowInspectionResultParam :public InspectionResultBase
    {
        std::shared_ptr<jrsdata::DetectResultParam> detect_result_param;
        FlowInspectionResultParam()
            : detect_result_param(std::make_shared<jrsdata::DetectResultParam>())
        {
        }
        ~FlowInspectionResultParam() = default;
        bool GetInspectionResultStatus() override
        {
            return detect_result_param->detect_result;
        }

        void SetSpeficParam(const std::string& key, const std::any& value)override
        {
            //! 默认做子板ID和条码处理
            (void)key;
            if (value.type() == typeid(std::unordered_map<int, std::string>))
            {
                detect_result_param->subboard_id_and_barcode = std::any_cast<std::unordered_map<int, std::string>>(value);
            }
        }

    };
    using InspectionResultBasePtr = std::shared_ptr<InspectionResultBase>;
    using FlowInspectionResultParamPtr = std::shared_ptr<FlowInspectionResultParam>;

    /**
     * .  by zhangyuyu 2024.11.25
     * @brief 矫正流程接口类
     */
    class ICorrectFlow
    {
    public:
        virtual ~ICorrectFlow() = default;
        virtual int CorrectProject(const std::shared_ptr<jrsdata::ProjectParam>& project_param_) = 0;
        virtual int StopCorrect() = 0;
        virtual void SetWorkFlowParam(const WorkFlowParam& param_) = 0;
    };

    // 视觉检测流程接口类
    class IVisionInspectionFlow
    {
    public:
        virtual ~IVisionInspectionFlow() = default;
        virtual int StartInspection(const std::shared_ptr<jrsdata::ProjectParam>& project_param_) = 0;
        virtual void StopInspection() = 0;
        virtual void AddBuffer(const jrsdata::JrsImageBuffer& imgs) = 0;
        virtual InspectionResultBasePtr GetInspectionResult() = 0;
        virtual void StopAllTasks() = 0;
        virtual void WaitAllTasks() = 0;
        virtual void SetWorkFlowParam(const WorkFlowParam& param_) = 0;
    };

    // 轨道流程接口类
    class ITrackFlow
    {
    public:
        virtual ~ITrackFlow() = default;
        
        /**
         * @fun LoadMaterial 
         * @brief 上料
         * @param rail_index 轨道号
         * @return 成功/失败
         * <AUTHOR>
         * @date 2025.5.14
         */
        virtual int LoadMaterial(int rail_index) = 0;
        /**
         * @fun UnloadMaterial 
         * @brief 下料
         * @param rail_index 轨道号
         * @return  成功/失败
         * <AUTHOR>
         * @date 2025.5.14
         */
        virtual int UnloadMaterial(int rail_index) = 0;

        /**
         * @fun GetTrackErrStr 
         * @brief 获取轨道错误信息
         * @return 错误信息
         * <AUTHOR>
         * @date 2025.5.14
         */
        virtual std::string GetTrackErrStr() = 0;
        /**
         * @fun StopTrack 
         * @brief 停止轨道
         * @return  成功/失败
         * <AUTHOR>
         * @date 2025.5.14
         */
        virtual int StopTrack() = 0;

    };

    // 结果存储流程
    class IResultStorageFlow
    {
    public:
        virtual ~IResultStorageFlow() = default;
        virtual int SaveResult(const InspectionResultBasePtr& reult_param_) = 0;

        virtual int GetDataFromDataBase(jrsdata::QueryDatabaseResult& query_data) = 0;
        virtual int StopSaveResult() = 0;
    };

    // 流程控制接口类
    class IFlowController
    {
    public:
        virtual ~IFlowController() = default;
        virtual void Start(const std::shared_ptr<jrsdata::DataBase>& project_param_) = 0;
        virtual void Stop() = 0;
        virtual void Pause() = 0;
        virtual void Resume() = 0;
        virtual void SetWorkFlowParam(const WorkFlowParam& param_) = 0;
        virtual std::string GetCurrentStateName() = 0;

        virtual void AddBuffer(const jrsdata::JrsImageBuffer& imgs) = 0;

        virtual void SetControlPanelCallback(const jrsdata::InvokeControlPanelViewParamFun& cb_) = 0;


    };

    using ICorrectFlowPtr = std::shared_ptr<ICorrectFlow>;
    using IVisionInspectionFlowPtr = std::shared_ptr<IVisionInspectionFlow>;
    using ITrackPtr = std::shared_ptr<ITrackFlow>;
    using IResultStoragePtr = std::shared_ptr<IResultStorageFlow>;
    using IFlowControllerPtr = std::shared_ptr<IFlowController>;




}

#endif // __JRSWORKFLOWINTERFACES_H__