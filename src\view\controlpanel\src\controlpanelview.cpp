﻿#include "controlpanelview.h"
#include "machinestateview.h"
#include "autorunpanelview.h"
//thirdparty
#include "ui_controlpanelview.h"
#include <QHBoxLayout>
#include <QIcon>

namespace jrsaoi
{
   
    struct ImplData
    {
        MachineStateView* machine_state_view;
        AutoRunPanelView* auto_run_panel_view;
        Ui::controlpanelview* ui;
        jrsdata::ControlPanelViewParamPtr param_ptr;
        bool is_online = false;
    };

    ControlPanelView::ControlPanelView(const std::string& name, QWidget* parent)
        : ViewBase(name, parent)
        , _impl_data(new ImplData())

    {
        Init();
    }

    ControlPanelView::~ControlPanelView()
    {
        delete _impl_data->ui;
    }
    int ControlPanelView::Init()
    {
        InitMember();
        InitView();
        InitConnect();
        return jrscore::AOI_OK;

    }
    Q_INVOKABLE int ControlPanelView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("param_ is nullptr");
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        _impl_data->param_ptr = std::static_pointer_cast<jrsdata::ControlPanelViewParam>(param_);
        _impl_data->param_ptr->machine_state_param->event_name = param_->event_name;
        if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_UPDATE_STATE_NAME || param_->event_name == jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME || param_->event_name == jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME)
        {
            _impl_data->machine_state_view->UpdateView(_impl_data->param_ptr->machine_state_param);
        }
        return jrscore::AOI_OK;
    }

    int ControlPanelView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }

    void ControlPanelView::SlotAutoRunPanelUpdate(const jrsdata::ViewParamBasePtr& param_)
    {
        _impl_data->param_ptr->topic_name = param_->topic_name;
        _impl_data->param_ptr->sub_name = param_->sub_name;
        _impl_data->param_ptr->invoke_module_name = param_->invoke_module_name;

        _impl_data->param_ptr->event_name = param_->event_name;
        _impl_data->param_ptr->autorun_panel_param = std::static_pointer_cast<jrsdata::AutoRunPanelViewParam>(param_);
        _impl_data->param_ptr->machine_state_param->event_name = param_->event_name;
        _impl_data->param_ptr->machine_state_param->is_auto_run = _impl_data->param_ptr->autorun_panel_param->flow_switch;
        if (!_impl_data->param_ptr->machine_state_param->is_auto_run)
        {
            _impl_data->param_ptr->machine_state_param->current_stop_state = jrsdata::MachineStateViewParam::StopState::IDLE;
        }
        else
        {
            _impl_data->param_ptr->machine_state_param->current_detect_state = { jrsdata::MachineStateViewParam::BoardDetectState::WAITING,jrsdata::MachineStateViewParam::DetectFlowState::REQUIRE_BOARD };
        }
        _impl_data->machine_state_view->UpdateView(_impl_data->param_ptr->machine_state_param);
        emit SigControlPanelUpdate(_impl_data->param_ptr);
    }

    void ControlPanelView::SlotOnlineDebug()
    {
        auto param = std::make_shared<jrsdata::ControlPanelViewParam>();
        //! 需要通知workflow和onlionedebug两个订阅者
        _impl_data->param_ptr->topic_name = jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME;
        _impl_data->param_ptr->sub_name = jrsaoi::CONTROL_PANEL_ONLINE_DEBUGE_ALL_SUB_NAME;
        _impl_data->param_ptr->invoke_module_name = jrsaoi::WORKFLOW_MODULE_NAME;
        if (_impl_data->is_online)
        {
            _impl_data->is_online = false;
            _impl_data->param_ptr->event_name = jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME;
        }
        else
        {
            _impl_data->is_online = true;
            _impl_data->param_ptr->event_name = jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME;
        }
        emit SigControlPanelUpdate(_impl_data->param_ptr);

       
    }

    void ControlPanelView::InitView()
    {
        _impl_data->ui->setupUi(this);
        _impl_data->ui->machine_state_layout->addWidget(_impl_data->machine_state_view, 1, 1);
        _impl_data->ui->auto_run_layout->addWidget(_impl_data->auto_run_panel_view, 1, 1);
       
    }

    void ControlPanelView::InitMember()
    {
        _impl_data->ui = new Ui::controlpanelview();
        _impl_data->param_ptr = std::make_shared<jrsdata::ControlPanelViewParam>();
        _impl_data->machine_state_view = new MachineStateView(this);
        _impl_data->auto_run_panel_view = new AutoRunPanelView(this);
        _impl_data->param_ptr->machine_state_param = std::make_shared<jrsdata::MachineStateViewParam>();
        _impl_data->param_ptr->autorun_panel_param = std::make_shared<jrsdata::AutoRunPanelViewParam>();
    }

    void ControlPanelView::InitConnect()
    {
        connect(_impl_data->auto_run_panel_view, &jrsaoi::AutoRunPanelView::SigUpdateAutoRunPanelView, this, &ControlPanelView::SlotAutoRunPanelUpdate);
    
        connect(_impl_data->ui->pushButton_online_debug,&QPushButton::clicked,this,&ControlPanelView::SlotOnlineDebug);
    }
};
