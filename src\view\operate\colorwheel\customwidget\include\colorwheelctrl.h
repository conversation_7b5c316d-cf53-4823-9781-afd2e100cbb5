/*****************************************************************//**
 * @file   colorwheelctrl.h
 * @brief  hsv颜色控件
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef COLORCONTROLSUI_H
#define COLORCONTROLSUI_H

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#include <QWidget>
#pragma warning(pop)
#include "wigtcolorwheel.h"
//#include "coloralgo.h"
#include "vwheel.h"
#include "colorparams.h"
using ThresholdValChangedFunc = std::function<void(const
	ColorWheelThreshVal& thresh_val, const cv::Mat& bin_result)>;

class ColorWheelCtrl : public QWidget
{
	Q_OBJECT
public:
	ColorWheelCtrl(QWidget *parent = nullptr);

	/**
	* @fun  RestoreColorWheel
	* @brief  恢复色盘
	* @date   2025.04.24
	* <AUTHOR>
	*/
	void RestoreColorWheel();

public:
	std::map<std::string, std::vector<double>> color_value_;
	std::string cur_wheel_name_;

private:
	//ColorAlgo*              color_algo = nullptr;
	WigtColorWheel*         color_wheel = nullptr;
	VChanelWheel*  double_thr_histogram = nullptr;

	ThresholdValChangedFunc threshold_val_changed_func;
	ColorWheelThreshVal     threshold_val_;

	cv::Mat m_h_img;
	cv::Mat m_s_img;
	cv::Mat m_v_img;
	cv::Mat m_output_img;

	bool m_has_test_img = false; // 是否为测试流程图像
	unsigned char hs_table[180][256]; // hs二维查找表
private:
	void ApplyThresholdValsToTestImg();
signals:
	void UpdateHsvParams(ColorWheelThreshVal params);
private slots:
	void UpdateVThresholdVal(const int& left_Val, const int& right_val);
	void UpdateHSThresholdVal(const cv::Mat& colorMap, const std::array<QPointF, 6>& axisPoints);

public:
	int SetTestImage(const cv::Mat& img);
    int SetThresholdValChangedCallback(ThresholdValChangedFunc func);
	int Threshold(const cv::Mat& input_img, const ColorWheelThreshVal& threshold_val, cv::Mat& output_img);
	int SetCurrentValState(ColorWheelThreshVal& threshold_val) ;

};

#endif // COLORCONTROLSUI_H
