@startuml 文件系统
'导入数据  导出数据 导出格式等功能

'保存的文件格式

enum FileType
{
	JSON,                 /**< Json 格式*/
	XML,                  /**< XML  格式*/
	YAML,                 /**< YAML  格式*/
	CSV,                  /**< CSV  格式*/
	BIN,                  /**< BIN 二进制格式*/
}
'存储模式 to DB？ or FILE?
enum DataSaveMode
{
	SAVE_FILE,              /**<保存到文件 */
	SAVE_DATABASE,          /**<保存到数据库 */
	SAVE_FILE_AND_DATABASE  /**<保存到文件及数据库 */
}
' 文件信息 
struct FileParam
{
	std::string file_path; /**<文件存储路径*/
	std::string file_name; /**<文件存储名称*/
	FileType file_type;    /**<文件存储类型*/
}

' 数据基类 
struct DataBase
{
	DataBase() = default;
	virtual ~DataBase() = default;
	DataSaveMode data_save_mode{};  /**< 数据存储方式 */
	FileParam file_param{};         /**< 文件参数 */
}
'using DataBasePtr = std::shared_ptr<DataBase>;
class FileManager
{
    +int EventHandler(const jrsdata::DataBasePtr& param_ptr_);
	-int OutputFile(const jrsdata::DataBasePtr& param_ptr_);
	-int ImputFile(const jrsdata::DataBasePtr& param_ptr_);
}

class ImportFile
{
    +int ImportImages(const jrsdata::DataBasePtr& param_ptr_);
	+int ImportProject(const jrsdata::DataBasePtr& param_ptr_);
	。。。
}
class OutputFile
{
	+int OutputImage(const jrsdata::DataBasePtr& param_ptr_);
	+int OutputProject(const jrsdata::DatabasePtr& param_ptr_);
	+
}
' 表间关系
TUser "1" --* "n" TBoard
TAOIMachine "1"--* "n" TBoard
TSubboard "1" --* "n" TBoard
TSubboard "1" *-- "n" TDevice
TGroup "n" --* "1" TDevice
TGroup "1" *-- "n" TDetectWindow
TDevice"1" *-- "n"TDetectType