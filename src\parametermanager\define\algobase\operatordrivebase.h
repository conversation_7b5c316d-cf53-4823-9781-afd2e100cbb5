/*****************************************************************//**
 * @file   operatordrivebase.h
 * @brief  算子驱动基类
 * @details
 * <AUTHOR>
 * @date 2024.2.26
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.26         <td>V1.0              <td>YYZhang      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSOPERATORDRIVEBASE_H__
#define __JRSOPERATORDRIVEBASE_H__

 //STD
#include <string>
//Custom
#include "operatorparambase.h"
namespace jrsoperator
{
    // 用于算法检测时，算法检测框位置信息
    struct AlgoDetData
    {
        cv::Rect detect_component_rect; /**< 检测时CAD位置(相对于搜索区域) 用于cv算法*/ 
        JrsRect detect_component_rect_jrs; /**< 检测时CAD位置(相对于搜索区域) 用于JrsRect相关计算*/
        cv::RotatedRect detect_rotate; /**< 放大后检测框位置(相对于图片) */
        cv::RotatedRect ori_detect_rotate; /**< 原始检测框位置(相对于图片) */
        std::unordered_map<int, std::vector<cv::RotatedRect>> pad_rotates; /**< 放大后pad框位置 {方向,框组} */
        std::unordered_map<int, std::vector<cv::RotatedRect>> sub_detect_rotates;   /**< 子检测框位置 {方向,框组} */
        std::unordered_map<int, std::vector<cv::RotatedRect>> ori_sub_detect_rotates; /**< 原始子检测框位置 {方向,框组} */

    };

    class OperatorDriveBase
    {
    public:
        using OperatorDriveBasePtr = std::shared_ptr<OperatorDriveBase>;

        explicit OperatorDriveBase(const std::string& operator_name_) : operator_name(operator_name_) {}
        virtual ~OperatorDriveBase() = default;
        OperatorDriveBase& OperatorDriveBase::operator=(const OperatorDriveBase& other)
        {
            if (this != &other)
            {
                operator_name = other.operator_name;
            }
            return *this;
        }
        OperatorDriveBase(const OperatorDriveBase& other) : operator_name(other.operator_name) {}

        /**
         * @fun GetOperatorName
         * @brief 获取算子名称
         * @return 算子名称
         * @date 2024.2.29
         * <AUTHOR>
         */
        const std::string& GetOperatorName()const { return operator_name; }

        /**
         * @fun Clone
         * @brief 获取算子实例
         * @return 实例算子
         * @date 2024.2.29
         * <AUTHOR>
         */
        virtual OperatorDriveBasePtr Clone() const = 0;
        /**
         * @fun ExecuteOperator
         * @brief 执行算子
         * @param execute_param_ 算子参数
         * @return
         * @date 2024.2.29
         * <AUTHOR>
         */
        virtual bool ExecuteOperator(OperatorParamBasePtr execute_param_) = 0;
        /**
         * @fun UpdateDetectRect
         * @brief 更新算法检测框位置
         * @return
         * @date 2024.12.24
         * <AUTHOR>
         */
        virtual bool UpdateDetectRect(OperatorParamBasePtr execute_param_);
        ///**
        // * @fun GetCurDetImage
        // * @brief 获取当前算法检测框图像
        // * @return
        // * @date 2024.12.24
        // * <AUTHOR>
        // */
        //virtual bool GetCurDetImage(const ColorParams& color_parm,
        //    const cv::Mat& input_image, cv::Mat &det_image);
        /**
         * @fun GetCurDetImage
         * @brief 获取当前算法检测框图像
         * @return
         * @date 2024.12.24
         * <AUTHOR>
         */
        virtual bool SaveBaseParamToDirectory(OperatorParamBasePtr execute_param_, std::string directory);
    protected:
        /**
         * @note 将来删除
         */
        template<typename K, typename V>
        bool IsKeyInMap(const std::map<K, V>& map, const K& key)
        {
            return map.find(key) != map.end();
        }
        template<typename K, typename V>
        bool IsKeyInMap(const std::unordered_map<K, V>& map, const K& key)
        {
            return map.find(key) != map.end();
        }
        std::string operator_name;
        AlgoDetData m_algo_det_data;
    };
}


#endif // !__JRSOPERATORDRIVEBASE_H__

