/*****************************************************************//**
 * @file   jrslogtypedefines.h
 * @brief  日志相关定义
 * @details    
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSLOGTYPEDEFINES_H__
#define __JRSLOGTYPEDEFINES_H__

namespace jrscore
{

    /** @brief 日志输出位置 */
    enum class LogPosition  :int
    {
        CONSOLE = 0x01,							/**< 控制台输出 */
        FILELOG = 0x02,							/**< 文件输出 */
        CONSOLE_AND_FILE = CONSOLE | FILELOG,   /**< 控制台和文件都输出 */
    };

    /** @brief 输出模式 */
    enum class LogMode
    {
        SYNC = 0,     /**< 同步模式 */
        ASYNC = 1,    /**< 异步模式 */
    };

    /** @brief  */
    enum class LogLevel
    {

        LEVEL_TRACE = 0, /**< 最详细的日志级别，用于跟踪程序内部的细节信息 */

        LEVEL_DEBUG = 1, /**< 调试级别的日志，用于输出一些调试相关的信息 */

        LEVEL_INFO = 2, /**< 普通信息级别的日志，用于输出程序运行的一般性消息 */

        LEVEL_WARN = 3, /**< 警告级别的日志，用于输出可能表示潜在问题或异常情况的消息 */

        LEVEL_ERROR = 4,/**< 错误级别的日志，用于输出错误信息 */

        LEVEL_CRITI = 5, /**< 严重错误级别的日志，表示程序运行时遇到的致命错误。 */

        LEVEL_OFF = 6, /**< 关闭日志输出，相应的日志消息将不会被记录 */
    };

    /**
     *  @brief 日志配置属性
     */
    struct LogSetting
    {
        LogMode     outMode = LogMode::ASYNC;
        LogPosition outPos = LogPosition::FILELOG;
        LogLevel    outLevel = LogLevel::LEVEL_TRACE;
    };



}

#endif // !__JRSLOGTYPEDEFINES_H__