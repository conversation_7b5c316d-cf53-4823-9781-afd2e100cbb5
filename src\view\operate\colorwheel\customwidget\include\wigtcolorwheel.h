/*****************************************************************//**
 * @file   wigtcolorwheel.h
 * @brief  hsv控件交互
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef COLORWHEEL_H
#define COLORWHEEL_H

#define PTPMINDISTANCE 0.5

#include <vector>
#include <array>

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#include <QRegion>
#include <QWidget>
#pragma warning(pop)
class WigtColorWheel : public QWidget
{
    Q_OBJECT
public:
	/**
	* @fun  WigtColorWheel
	* @brief  构造函数
	* @date   2024.08.18
	* <AUTHOR>
	*/
    explicit WigtColorWheel(QWidget *parent = nullptr);
public:
	/**
	* @fun  SetAxisPos
	* @brief  设置HS色盘的状态
	* @date   2024.08.18
	* <AUTHOR>
	*/
	void SetAxisPos(const std::array<QPointF, 6>& map_points,bool update_ui = true);

	/**
	* @fun  RestoreColorWheel
	* @brief  恢复色盘
	* @date   2025.04.24
	* <AUTHOR>
	*/
	void RestoreColorWheel();

private:
    size_t  axis_point_size = 10;
	size_t  radius = 100;
    size_t  wheel_width = 0;
    size_t  wheel_height = 0;
    QPointF wheel_center {100,100};
    std::array<QPointF, 6> axis_points;
	std::array<QPointF, 6> axis_points_copy;

    QPoint  start_pos;
    bool    is_press_on_axis_point = false;  
    int     current_select_index = -1;   

    QRegion wheel_region;
    QRegion color_region;
    cv::Mat color_region_mask;
    cv::Mat color_map_table;
    	
    QPointF RelaPos2AbsPos(const QPointF& relaPos);
    QPointF AbsPos2RelaPos(const QPointF& absPos);
    
    void DrawBackground(QPainter& painter);
    void UpdateColorRegion();
    void UpdateColorMapTable();

protected:
	/**
	* @fun  paintEvent
	* @brief  色盘绘制
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void paintEvent(QPaintEvent *) override;
	/**
	* @fun  mouseMoveEvent
	* @brief  更新色盘状态
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
	/**
	* @fun  mousePressEvent
	* @brief  更新HS色盘按钮的选中状态
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void mousePressEvent(QMouseEvent *event) override;
	/**
	* @fun  resizeEvent
	* @brief  更新HS色盘的大小
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void resizeEvent(QResizeEvent *event) override;
	/**
	* @fun  mouseDoubleClickEvent
	* @brief  还原HS色盘位置
	* @date   2024.08.18
	* <AUTHOR>
	*/
	void mouseDoubleClickEvent(QMouseEvent *event) override;

signals:
	/**
	* @fun  ColorMapChanged
	* @brief 返回HS色盘当前的状态
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void ColorMapChanged(const cv::Mat& colorMap,
		const std::array<QPointF, 6>& axisPoints);
	/**
	* @fun  UpdateWheelSize
	* @brief 更新HS色盘的尺寸
	* @date   2024.08.18
	* <AUTHOR>
	*/
    void UpdateWheelSize(const int width,const int height);

};
#endif // COLORWHEEL_H
