//#include "toolbarcontroller.h"
//namespace jrsaoi
//{
//    ToolBarController::ToolBarController (const std::string& name) :ControllerBase (name)
//    {
//    }
//    ToolBarController::~ToolBarController ()
//    {
//    }
//    bool ToolBarController::Save (const jrsdata::ViewParamBasePtr& param_)
//    {
//        return false;
//    }
//    void ToolBarController::Update (const jrsdata::ViewParamBasePtr& param_)
//    {
//    }
//    //void ToolBarController::SetView (ViewBase* view_param)
//    //{
//    //    //view = static_cast<ToolBarView*>(view_param);
//    //}
//    void ToolBarController::SetModel (ModelBasePtr model_param)
//    {
//        model = std::dynamic_pointer_cast<ToolBarModel>(model_param);
//    }
//}
