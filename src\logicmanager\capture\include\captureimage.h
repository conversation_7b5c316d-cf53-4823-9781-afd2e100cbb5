/*****************************************************************//**
 * @file   captureimage.h
 * @brief  采集图像采集单张图像
 * @details
 * <AUTHOR>
 * @date 2024.8.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.5          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
//STD
#include <iostream>

//Custom
#include "image.hpp"
//Third
namespace jrsdevice
{
    class DeviceManager;
}

namespace jrslogic
{
    
    class JrsAoiImgsManager;
    class CaptureImage
    {
        public:
            CaptureImage (const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_);
            CaptureImage () = delete;
            ~CaptureImage ();

            /**
            * @fun GetSingleBuffer 
            * @brief 获取单张buffer
            * @return 
            * <AUTHOR>
            * @date 2024.8.6
            */
            jrsdata::JrsImageBuffer GetSingleBuffer ();

            /**
             * @fun TriggerSingleCaptureBuffer 
             * @brief 触发单张采集
             * @param trigger_mode 采集触发模式，分为单张采集，多张采集等十几种
             * @param fov_id 当前采集的FOV编号，如果没有fov编号，默认为0
             * @return 
             * <AUTHOR>
             * @date 2024.8.6
             */
            int TriggerSingleCaptureBuffer ( const jrsdata::TriggerModeCapture& trigger_mode, const int& fov_id = 0);

            int StartContinueTrigger(const jrsdata::TriggerModeCapture& trigger_mode);

            int StopContinueTrigger(); 

            int ContinueTriggerFunc(const jrsdata::TriggerModeCapture& trigger_mode);


            /**
             * @fun SetCaptureTriggerDoneCallBack 
             * @brief 设置触发完成回调
             * @param callback_ 回调参数
             * <AUTHOR>
             * @date 2024.8.11
             */
            void SetCaptureTriggerDoneCallBack (jrsdata::Grab2DImgCallBack callback_);

            /**
             * @fun SetMergeImageDoneCallBack 
             * @brief 设置图片合并完成回调
             * @param callback_ 回调参数
             * <AUTHOR>
             * @date 2024.8.11
             */
            void SetMergeImageDoneCallBack (jrsdata::CaptureCallBack callback_);


            void SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback);
            
        private:

            /**
             * @fun Init 
             * @brief 初始化
             * <AUTHOR>
             * @date 2024.8.6
             */
            void Init ();

                        /**
             * @fun InitCallback
             * @brief 初始化回调函数
             * <AUTHOR>
             * @date 2024.8.6
             */
            void InitCallback();


            /**
             * @fun InitMemeber 
             * @brief 初始化成员变量
             * <AUTHOR>
             * @date 2024.8.6
             */
            void InitMemeber ();

            /**
             * @fun CaptureTriggerDone 
             * @brief 采图触发成功
             * @param result 用于判断是否触发成功
             * <AUTHOR>
             * @date 2024.8.6
             */
            void CaptureTriggerDone ( const int& result );

            /**
             * @fun MergeImageDone 
             * @brief 采图成功
             * @param imgs 采集的图像数据
             * @param result 用于判断是否采图成功
             * <AUTHOR>
             * @date 2024.8.6
             */
            void MergeImageDone (const jrsdata::OneFovImgs& imgs,  const int& result );

            
            std::shared_ptr<jrsdevice::DeviceManager>    device_manager_ptr;   /**< 设备管理实例*/
            std::shared_ptr<jrslogic::JrsAoiImgsManager> aoi_imgs_manager_ptr; /**< 图像管理实例*/
            jrsdata::Grab2DImgCallBack                   trigger_done_callback;/**< 触发完成回调*/
            jrsdata::CaptureCallBack                     merge_done_callback;  /**< merge图片完成回调*/   
            jrsdata::JrsImageBufferCallBack              render_imgs_callback;                    /**< 图像渲染回调 */     

            bool                                         continue_trigger = false;
            std::mutex                                   mtx_continue_grab;    /**< 扫图线程互斥锁 */
            std::condition_variable                      cv_continue_grab;/**< 扫图线程条件变量 */
            std::atomic<bool>                            waittting;   /**< 等待当前fov拍照完成 */ 
    };
}
