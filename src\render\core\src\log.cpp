#include "log.h"
#include "tools.hpp"
// #define NONE_DEBUG_INFO // 禁止输出log

#define WIN32_LEAN_AND_MEAN
#define WIN32_EXTRA_LEAN
#define NOGDICAPMASKS
#define NOSERVICE
#define NOMCX
#define NOMINMAX
#define NOIME
#define NOCOMM
#include <windows.h>
#include <iostream>
#include <chrono>


void addNewlineIfNeeded(std::string& str)
{
    // 判断字符串是否为空
    if (!str.empty())
    {
        // 获取字符串最后一个字符
        char lastChar = str.back();

        // 如果最后一个字符不是换行符 '\n'，则添加换行符
        if (lastChar != '\n')
        {
            str += '\n';
        }
    }
    else
    {
        str += '\n';
    }
}

void printDebugInfo(const char* info, [[maybe_unused]] const char* file, int line, const char* function)
{
#ifdef NONE_DEBUG_INFO
    return;
#endif

    std::ostringstream oinfo;
    oinfo << render::Tools::GetCurrentTimeString << " [";
    // if (sizeof(file) != 0)
    //{
    //     oinfo << file << ":";
    // }
    oinfo << function << " - Line " << line << "] " << info;

    std::string res = oinfo.str();
    addNewlineIfNeeded(res);

    if (GetStdHandle(STD_OUTPUT_HANDLE) != NULL)
    {
        std::cout << res << std::flush;
        // std::cout << res << std::endl;
    }
    else
    {
        OutputDebugStringA(res.c_str());
    }
}

void printDebugInfo(const std::stringstream& ss, const char* file, int line, const char* function)
{
    printDebugInfo(ss.str().c_str(), file, line, function);
}
