/*****************************************************************
 * @file   right_bottom_to_left_top_z.hpp
 * @brief  z字形排序 从右下到左上
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class ZRightBottomToLeftTop :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int subboard_id = 1;
            int row_size = static_cast<int>(subboards_.size());

            for (int row_num = row_size - 1;row_num >= 0;--row_num)
            {
                for (auto& subboard : subboards_[row_num])
                {
                    UpdateSubboard(subboard, subboard_id++);
                }
            }
            return jrscore::AOI_OK;
        }
    };
}
