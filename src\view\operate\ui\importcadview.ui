<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImportCadView</class>
 <widget class="QWidget" name="ImportCadView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>454</width>
    <height>100</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>100</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>100</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>ImportCadView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="1">
       <widget class="QWidget" name="widget" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>4</number>
         </property>
         <property name="rightMargin">
          <number>4</number>
         </property>
         <property name="bottomMargin">
          <number>4</number>
         </property>
         <property name="spacing">
          <number>4</number>
         </property>
         <item row="0" column="0">
          <widget class="QPushButton" name="btn_add_cad">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>+CAD</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="pushButton_rect_sub_board">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>矩形子板</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QPushButton" name="btn_clear_select_component">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>清除定位</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QPushButton" name="pushButton_polyon_sub_board">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>多边子板</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QPushButton" name="btn_select_component_true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>框选位置</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QPushButton" name="btn_confirm_component_true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>75</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>75</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>确认定位</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QFrame" name="frame_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_5">
         <property name="leftMargin">
          <number>4</number>
         </property>
         <property name="topMargin">
          <number>4</number>
         </property>
         <property name="rightMargin">
          <number>4</number>
         </property>
         <property name="bottomMargin">
          <number>4</number>
         </property>
         <property name="spacing">
          <number>4</number>
         </property>
         <item row="0" column="0">
          <widget class="QTableWidget" name="position_table">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>导
入
C
A
D</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
