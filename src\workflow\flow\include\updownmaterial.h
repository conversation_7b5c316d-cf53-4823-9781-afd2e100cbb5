/*****************************************************************
 * @file   updownmaterial.h
 * @brief  自动流程上下料逻辑封装调用(丢弃使用 by zhangyuyu 2024.10.23)
 * @details 调用设备类进行上下料功能逻辑的实现
 * <AUTHOR>
 * @date 2024.10.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.23          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSUPDOWNMATERIAL_H__
#define __JRSUPDOWNMATERIAL_H__

//STD
#include <iostream>
//Custom
#include "devicemanager.h"
#include "motion.h"
//Third

namespace jrsworkflow 
{
    class UpDownMaterial
    {
        public:
            UpDownMaterial ( const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_ );
            ~UpDownMaterial ();
            /**
             * @fun UpMaterial 
             * @brief 上料
             * @param rail_index [IN] 轨道号
             * <AUTHOR>
             * @date 2024.11.1
             */
            void UpMaterial (int rail_index);

            /**
             * @fun DownMaterial 
             * @brief 下料 [IN] 轨道号
             * @param rail_index
             * <AUTHOR>
             * @date 2024.11.1
             */
            void DownMaterial (int rail_index);
        private:
            
            //Fun
            void InitMember ();

            //Member
            std::shared_ptr<jrsdevice::DeviceManager> device_manager_ptr;
    };
    using UpDownMaterialPtr = std::shared_ptr<UpDownMaterial>;
}


#endif // !__JRSUPDOWNMATERIAL_H__