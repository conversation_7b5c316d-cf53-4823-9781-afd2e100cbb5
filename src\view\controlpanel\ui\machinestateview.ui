<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>machinestateview</class>
 <widget class="QWidget" name="machinestateview">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>322</width>
    <height>200</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>200</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MachineStateView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QFrame" name="state_frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="1" column="0">
       <layout class="QHBoxLayout" name="detect_state_hlayout">
        <item>
         <widget class="QLabel" name="label_detect_state">
          <property name="text">
           <string>检测状态：</string>
          </property>
          <property name="scaledContents">
           <bool>false</bool>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="margin">
           <number>0</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="detect_state_label">
          <property name="text">
           <string>检测</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="3" column="0">
       <layout class="QHBoxLayout" name="detect_flow_hlayout">
        <item>
         <widget class="QLabel" name="label_detect_flow">
          <property name="text">
           <string>检测流程：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="detect_flow_state_label">
          <property name="text">
           <string>未启动</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0">
       <layout class="QHBoxLayout" name="stop_stop_hlayout">
        <property name="spacing">
         <number>6</number>
        </property>
        <item>
         <widget class="QLabel" name="label_stop_state">
          <property name="text">
           <string>停止状态：</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="stop_state_label">
          <property name="text">
           <string>正常</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="board_detect_state_hlayout">
        <item>
         <widget class="QLabel" name="label_board_detect_state">
          <property name="text">
           <string>检测结果：</string>
          </property>
          <property name="scaledContents">
           <bool>false</bool>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="margin">
           <number>0</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="board_detect_state_label">
          <property name="text">
           <string>-</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
