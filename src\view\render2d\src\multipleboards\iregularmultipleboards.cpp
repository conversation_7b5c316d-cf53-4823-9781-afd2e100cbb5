#include "iregularmultipleboards.h"

#include "coordinatetransformationtool.h"
#include "coreapplication.h"
int jrsaoi::IrregularMultipleBoards::MultipleBoardsUpdate(jrsdata::MultiBoardEventParamPtr param_)
{
    if (param_->irregular_multiple_board_param->step == jrsdata::MultiBoardEventParam::IrregularParam::Step::GENERATE_MULTI_BOARD)
    {
        return GenerateMultipleSubboard(param_);
    }
    else if (param_->irregular_multiple_board_param->step == jrsdata::MultiBoardEventParam::IrregularParam::Step::CONFIRM_SUBBOARD_COMPONENT)
    {
        ConfirmSelectSubboardComponent(param_);
    }
    else if (param_->irregular_multiple_board_param->step == jrsdata::MultiBoardEventParam::IrregularParam::Step::SELECT_SUBBOARD_LOCATION)
    {
        SelectSubboardLocation(param_);
    }
    else if (param_->irregular_multiple_board_param->step == jrsdata::MultiBoardEventParam::IrregularParam::Step::CONFIRM_SUBBOARD_LOCATION)
    {
        ConfirmSubboardLocation(param_);
    }
    param_->irregular_multiple_board_param->step = jrsdata::MultiBoardEventParam::IrregularParam::Step::NONE;
    return jrscore::AOI_OK;
}

jrsaoi::IrregularMultipleBoards::IrregularMultipleBoards()
{

}

jrsaoi::IrregularMultipleBoards::~IrregularMultipleBoards()
{

}

int jrsaoi::IrregularMultipleBoards::GenerateMultipleSubboard(jrsdata::MultiBoardEventParamPtr param_)
{
    auto project_param = _project_param_instance.GetProjectDataProcessInstance();
    auto board = project_param->ReadBoard();
    if (!board.has_value())
    {
        Log_ERROR("没有工程数据");
        return -1;
    }
    auto& subboards = board->get().sub_board;
    if (subboards.empty())
    {
        Log_ERROR("获取子板数据为空，请检查");
        return -1;
    }
    auto subboard = subboards.front();/*< 获取第一个子板*/

    float gap_x = subboard.width;
    float gap_y = subboard.height;
    for (int row = 0; row < param_->irregular_multiple_board_param->rows; ++row)
    {
        for (int col = 0; col < param_->irregular_multiple_board_param->cols; ++col)
        {
            if (row == 0 && col == 0)
            {
                continue;
            }
            auto new_subboard = project_param->CopySubboard(subboard, { gap_x * (col),gap_y * (row) });
            project_param->CreateSubBoard(new_subboard);
        }
    }
    param_->irregular_multiple_board_param->cols = 0;
    param_->irregular_multiple_board_param->rows = 0;
    _project_update_graphics_callback();
    return jrscore::AOI_OK;
}

int jrsaoi::IrregularMultipleBoards::ConfirmSelectSubboardComponent(const jrsdata::MultiBoardEventParamPtr& param_)
{
    (void)param_;
    return jrscore::AOI_OK;
}

int jrsaoi::IrregularMultipleBoards::SelectSubboardLocation(const jrsdata::MultiBoardEventParamPtr& param_)
{
    (void)param_;
    auto current_render_event_param = _get_current_select_param();
    if (!current_render_event_param.has_value())
    {
        Log_ERROR("获取定位框检测框失败");
        return -1;
    }
    auto current_select_gh_object = current_render_event_param->get().update_operator_param.select_object.component_graphics;
    if (current_select_gh_object.expired())
    {
        JRSMessageBox_INFO("未选择元件", "请选择一个元件，再进行定位", jrscore::MessageButton::Ok);
        return -1;
    }

    if (_set_current_layer_callback && _set_vision_mode_callback)
    {
        _set_current_layer_callback(Layer::temp_mark);
        _set_vision_mode_callback(VisionMode::MANUAL_CREATE_GRAPHICS);
        return jrscore::AOI_OK;
    }
    return -1;
}

int jrsaoi::IrregularMultipleBoards::ConfirmSubboardLocation(const jrsdata::MultiBoardEventParamPtr& param_)
{
    (void)param_;
    if (!_get_current_select_param)
    {

        return -1;
    }
    auto current_render_event_param = _get_current_select_param();
    if (!current_render_event_param.has_value())
    {
        Log_ERROR("获取定位框检测框失败");
        return -1;
    }

    auto& temp_gh_name = current_render_event_param->get().sub_edit_param.multi_event_param.irregular_multiple_board_param->graphics_name;
    std::shared_ptr<GraphicsAbstract> gh;
    _get_graphics_callback(gh, temp_gh_name);
    if (!gh)
    {
        JRSMessageBox_INFO("获取数据失败", "获取临时定位框失败，无法进行定位", jrscore::MessageButton::Ok);
        return -1; //获取临时框位置出错，请检查
    }

    auto project_param = _project_param_instance.GetProjectDataProcessInstance();
    auto& current_select_param = current_render_event_param->get().update_operator_param.select_param;
    cv::Point2f location_center_point = cv::Point2f{ gh->x() /*- gh->w() / 2*/,gh->y() /*- gh->h() / 2*/ };

    /**<获取元件*/
    auto component = project_param->ReadComponentRef(current_select_param.component_name, current_select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component.has_value())
    {
        Log_ERROR("查询元件失败");
        return -1;
    }
    auto component_ref = component->get();

    //生成矩阵
    cv::Mat matrix;
    std::vector<jrstool::PointLabel<float>> ori, cur;
    ori.emplace_back(jrstool::PointLabel<float>({ component_ref.x ,component_ref.y }, 100.0f));
    cur.emplace_back(jrstool::PointLabel<float>(location_center_point, 100.0f));
    jrstool::CoordinatrTransform<float>::GetAffineMatrix(ori, cur, matrix);
    auto subboard = project_param->ReadSubBoardRef(current_select_param.subboard_name);
    if (!subboard.has_value())
    {
        Log_ERROR("获取子板数据失败");
        return -1;
    }
    if (_clear_layer_callback)
    {
        _clear_layer_callback(LayerConverter::ToString(Layer::temp_mark), false);
    }
    project_param->AlignmentSubBoard(subboard->get(), matrix);
    _project_update_graphics_callback();
    return jrscore::AOI_OK;
}

