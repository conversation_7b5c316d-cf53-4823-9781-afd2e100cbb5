/*****************************************************************
 * @file   algoparamparse.h
 * @brief  解析算法参数功能类，主要负责将算法的参数设置和获取
 * @details 算法设置：因为算法参数是继承自iguana::base的，所以可以使用iguana::base的成员函数进行设置和获取，
 *          当前类主要实现了将：1.根据算法名称创建算法参数对象的功能
 *                              2.给创建好的算法指针，通过算法参数字符串赋初值
 *                              3.获取算法指针的字符串值
 *                              4.设置指定变量名称的参数值
 *                              5.获取指定变量名称的参数值
 * <AUTHOR>
 * @date 2024.10.17
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.17          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSALGOPARAMPARSE_H__
#define __JRSALGOPARAMPARSE_H__
//STD
#include <iostream>
//Custom
#include "algoexecuteparam.hpp"
//Third

namespace jrsoperator 
{

    struct OperatorParamBase;
}
namespace iguana {
    namespace detail {
        struct base;
    }
    using base = detail::base;
}


namespace jrsalgo 
{
    
    class AlgoParamParse
    {
        public :
            AlgoParamParse ();
            ~AlgoParamParse ();
            /**
             * @fun CreateAlgoParamPtr 
             * @brief 根据算法名称创建算法参数对象，类型为iguana::base基类
             * @param algo_name_ 算法名称
             * @return  返回算法参数对象
             * <AUTHOR>
             * @date 2024.12.3
             */
            static std::shared_ptr<iguana::base> CreateAlgoParamPtr (const std::string& algo_name_);
            /**
             * @fun SetSpeficParamValue 
             * @brief 设置指定的算法参数，主要用于动态参数的设置
             * @param param_ptr_  算法参数指针
             * @param param_name_ 算法参数名称
             * @param param_value_ 算法参数值
             * <AUTHOR>
             * @date 2024.12.3
             */
            static void SetSpeficParamValue (std::shared_ptr<iguana::base>& param_ptr_,const std::string& param_name_,const jrsparam::AlgoParamType& param_value_ );
            
            /**
             * @fun GetSpeficParamValue 
             * @brief 获取算法指定参数的值
             * @param param_ptr_ [IN]算法参数指针
             * @param param_name_[IN] 算法参数名称
             * @return 返回获取的算法参数值
             * <AUTHOR>
             * @date 2025.5.6
             */
            template <typename T>
            inline static jrsparam::AlgoParamType GetSpeficParamValue(const std::shared_ptr<iguana::base>& param_ptr_, const std::string& param_name_)
            {
                try
                {
                    return  param_ptr_->get_field_value<T>(param_name_);
                }
                catch (const std::exception& e)
                {
                    std::cerr << "Get algo param value failure:" << e.what() << std::endl;
                }
                return jrsparam::AlgoParamType();
            }
            /**
             * @fun GetAlgoParamString 
             * @brief 获取算法参数字符串，将算法参数转换成字符串
             * @param param_ptr_  算法参数指针
             * @return  成功返回字符串，失败返回空
             * <AUTHOR>
             * @date 2024.12.3
             */
            static std::string GetAlgoParamString ( const std::shared_ptr<iguana::base>& param_ptr_ );
            /**
             * @fun SetAlgoParamFromString 
             * @brief 将算法参数从字符串转换成算法参数指针 
             * @param param_ptr 输出参数，成功返回算法参数指针，失败返回空
             * @param param_str_ 算法参数字符串
             * <AUTHOR>
             * @date 2024.12.3
             */
            static void SetAlgoParamFromString (std::shared_ptr<iguana::base>& param_ptr,const std::string_view& param_str_);
        private:
            //Fun
            void InitMember ();
            //Member

    };
    using AlgoParamParsePtr = std::shared_ptr<AlgoParamParse>;
}


#endif // !__JRSALGOPARAMPARSE_H__

