#ifndef __TABLEBASE_HPP__
#define __TABLEBASE_HPP__

//PREBUILD
#include "datapch.h"
//#include <dbtablebase.h>
#include <selectorbase.h>

namespace jrsdatabase {
    class TableBase : public DBTableBase, public SelectorBase
    {
    public:

        virtual ~TableBase() = default;

        virtual int Insert(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Insert(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Update(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Update(const std::vector <jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Replace(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Replace(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;

    protected:

        explicit TableBase(const std::string& table_name_/*表的名称*/);

        template <typename T>
        inline std::vector<T> BasePtrVecToObjectVector(const std::vector<jrstable::TableParamBasePtr>& baseVec) {
            static_assert(std::is_base_of<jrstable::TableParamBase, T>::value, "T must be derived from TableParamBase");
            std::vector<T> obj_vec;
            obj_vec.reserve(baseVec.size());
            for (const auto& basePtr : baseVec) {
                if (auto derivedPtr = std::dynamic_pointer_cast<T>(basePtr)) {
                    obj_vec.push_back(*derivedPtr); // 拷贝对象
                }
            }
            return obj_vec;
        }
    };
    using TableBasePtr = std::shared_ptr<TableBase>;
};
#endif //!__DBCONTENTOPERATORBASE_HPP__
