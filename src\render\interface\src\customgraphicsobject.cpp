﻿#include "customgraphicsobject.h"
#include "customgraphicscontrolpoint.h"
#include "graphicsprocess.h"
#include "graphicsalgorithm.h"
#include "graphicsserialize.hpp"
#include "graphicseventparam.hpp"

#include "controlpointabstract.h"
#include "controlpointconstants.hpp"
#include "controlpointfactory.h"

#include "eventparam.hpp"
#include "customcursortype.hpp"
#include "painter.h"
#include "renderer.h"
#include "log.h"

#include <QPainter>

std::vector<std::shared_ptr<ControlPointAbstract>> CreateSGTerminalControlPointGroup(SGGraphics* obj)
{
    std::vector<std::shared_ptr<ControlPointAbstract>> cps;
    std::vector<cv::Point2f> vertex;
    auto x = obj->x();
    auto y = obj->y();
    vertex.emplace_back(obj->GetStart().x + x, obj->GetStart().y + y);
    vertex.emplace_back(obj->GetEnd().x + x, obj->GetEnd().y + y);
    int vertex_cursor = static_cast<int>(CustomCursorType::ResizeDiagonal1);
    bool is_render = true;
    for (int i = 0; i < vertex.size(); ++i)
    {
        auto cpd = ControlPointDraw(vertex[i].x, vertex[i].y, 20, 20,
            vertex_cursor, is_render, {});
        auto attr = ControlAttributes(static_cast<int>(ControlPointType::BORDER_POINT), i, -1);
        auto cp = std::make_shared<ControlPointSGTerminal>();
        cp->SetAttributes(attr);
        cp->SetDraw(cpd);
        cps.emplace_back(cp);
    }
    return cps;
}


void SGGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void SGGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    obj->Update();
    Color c;
    float think_ness;
    this->SetColorAndThickness(config, c, think_ness);

    Vec2 center(obj->x(), obj->y());

    /*绘制起点和终点*/
    if (!obj->start_terminal.isnull() && !obj->end_terminal.isnull())
    {
        auto s = obj->start_terminal + center;
        auto e = obj->end_terminal + center;
        auto size = obj->terminal_size * 0.5f;
        switch (obj->terminal_type)
        {
        case 0:
        {
            p->DrawRect(Vec2(s.x - size, s.y - size),
                Vec2(s.x + size, s.y + size), c, 0, 1);
            p->DrawRect(Vec2(e.x - size, e.y - size),
                Vec2(e.x + size, e.y + size), c.GetInv(), 0, 1);
        }
        break;
        case 1:
        {
            p->DrawCircle(s, size, c);
            p->DrawCircle(e, size, c);
        }
        break;
        default:
            break;
        }
        DrawInfo(obj, r, p, config);
    }

    /* if (obj->settings.GetIsSelected())
     {
         c = c.GetInv();
     }*/

     /**绘制路径点 */
    p->DrawLines(obj->paths, c, false, think_ness);

    /*外轮廓*/
    // if (obj->settings.GetIsSelected())
    // {
    //     p->DrawLines(obj->paths_boundingbox, c.GetInv(), true, thinkness);
    // }

}

void SGGraphics::Update()
{
    if (IsNeedUpdate())
    {
        UpdateByTemp(this);
        UpdateControlPoint();
        UpdateDrawBuffer();
        SetUpdated();
    }
}

void SGGraphics::UpdateDrawBuffer()
{
    auto* obj = this;
    {
        cv::RotatedRect boundingbox = obj->GetBoundingbox();
        /*四顶点*/
        cv::Point2f vertex[4];
        boundingbox.points(vertex);
        std::vector<Vec2> tpaths{
             {vertex[0].x, vertex[0].y}, {vertex[1].x, vertex[1].y},{vertex[2].x, vertex[2].y}, {vertex[3].x, vertex[3].y} };
        tpaths.swap(obj->paths_boundingbox);
    }
    {
        Vec2 center(obj->x(), obj->y());

        obj->paths.clear();

        if (!obj->start_terminal.isnull() && !obj->end_terminal.isnull())
        {
            auto s = obj->start_terminal + center;
            auto e = obj->end_terminal + center;
            obj->paths.emplace_back(s);
            if (!obj->connect_nodes.empty())
            {
                for (const auto& v : obj->connect_nodes)
                {
                    obj->paths.emplace_back(v + center);
                }
            }
            obj->paths.emplace_back(e);
        }
    }
}

void SGGraphics::UpdateControlPoint()
{
    auto const obj = GetTemp(this, false);
    std::vector<std::shared_ptr<ControlPointAbstract>> cps;
    /*创建端点控制点*/
    {
        auto tcps = CreateSGTerminalControlPointGroup(obj);
        cps.insert(cps.end(), tcps.begin(), tcps.end());
    }
    /*创建中心拖拽控制点*/
    {
        auto cp = ControlPointFactory::CreateControlPoint(ControlPointType::MOVE_POINT, obj);
        //auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
        cps.emplace_back(cp);
    }
    cps.swap(control_points);
}

void SGGraphics::DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config)
{
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        cp->Draw(r, p, config);
    }
}

std::vector<ControlPoint> SGGraphics::CreateControlPoint()
{
    // auto const obj = GetTemp(this, true);

    std::vector<ControlPoint> cps;

    // Vec2 center(obj->x(), obj->y());
    // {
    //     /*创建边界控制点*/
    //     {
    //         auto tcps = CreateBorderControlPoint(obj, static_cast<int>(CustomCursorType::SelectLink), { obj->start_terminal, obj->end_terminal });
    //         cps.insert(cps.end(), tcps.begin(), tcps.end());
    //     }
    //     /*创建轮廓拖拽控制点*/
    //     {
    //         auto tcps = CreateContourControlPoint(obj, static_cast<int>(CustomCursorType::HandClose), obj->contours);
    //         cps.insert(cps.end(), tcps.begin(), tcps.end());
    //     }
    //     /*创建中心拖拽控制点*/
    //     {
    //         auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
    //         cps.emplace_back(cp);
    //     }
    // }
    return cps;
}

int SGGraphics::TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam& param)
{
    if (!settings.GetIsSelected())
        return 2;

    double mindis = param.max_limit;
    std::shared_ptr<ControlPointAbstract> response_cp = nullptr;
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        auto dis = cp->TryResponse(param.x, param.y, (float)param.max_limit);
        if (dis > 0 && dis < mindis)
        {
            // printInfo(std::stringstream() << "响应距离:" << dis);
            mindis = dis;
            response_cp = cp;
        }
    }

    if (!response_cp)
        return 1;
    controlpoint = response_cp;
    // attr = response_cp->attributes;
    return 0;
}

int SGGraphics::ResponseControlPoint(const ResponseEventParam& param)
{
    auto obj = GetAndUpdateTemp(this, param.istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;
    SetRequireUpdate();
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        if (cp->attributes == param.attr)
        {
            cp->Response(param, obj);
            return 0;
        }
    }
    return 1;
}

int SGGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    if (auto state = RectResponseControlPoint(obj, attr.type, xstart, ystart, xend, yend);
        state != GraphicsAbstract::UNKNOWN_TYPE)
    {
        return state;
    }

    switch (ControlPointType(attr.type))
    {
    case ControlPointType::CONTOUR_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        if ((int)obj->connect_nodes.size() > attr.id)
        {
            obj->connect_nodes[attr.id].x += xo;
            obj->connect_nodes[attr.id].y += yo;
        }
    }
    break;
    case ControlPointType::BORDER_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        if (attr.id == 0)
        {
            obj->start_terminal.x += xo;
            obj->start_terminal.y += yo;
        }
        else if (attr.id == 1)
        {
            obj->end_terminal.x += xo;
            obj->end_terminal.y += yo;
        }
        // 修正boundingbox位置
        {
            // obj->start_terminal.x += obj->x(); obj->start_terminal.y += obj->y();
            // obj->end_terminal.x += obj->x(); obj->end_terminal.y += obj->y();
            auto x = obj->x();
            auto y = obj->y();
            CreateBoundingBox(obj);
            obj->SetXY(obj->x() + x, obj->x() + y);
            // obj->start_terminal.x -= obj->x(); obj->start_terminal.y -= obj->y();
            // obj->end_terminal.x -= obj->x(); obj->end_terminal.y -= obj->y();
        }
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }
    return OK;
}

std::shared_ptr<GraphicsAbstract> SGGraphics::Clone() const
{
    return std::make_shared<SGGraphics>(*this);
}

std::string SGGraphics::GetSerializedData()
{
    return createConfig(this);
}

void SGGraphics::SetStart(float x, float y, bool isrelative)
{
    if (!isrelative)
    {
        x -= this->x();
        y -= this->y();
    }
    this->start_terminal.x = x;
    this->start_terminal.y = y;
    this->SetRequireUpdate();
}

void SGGraphics::SetEnd(float x, float y, bool isrelative)
{
    if (!isrelative)
    {
        x -= this->x();
        y -= this->y();
    }
    this->end_terminal.x = x;
    this->end_terminal.y = y;
    this->SetRequireUpdate();
}

void SGGraphics::EndComfirm()
{
    iscomfirmend = true;
}

void SGGraphics::SetPoint(const std::vector<Vec2>& points, bool isrelative)
{
    this->connect_nodes = points;
    if (!isrelative)
    {
        auto c_x = this->x();
        auto c_y = this->y();
        for (auto& p : this->connect_nodes)
        {
            p.x -= c_x;
            p.y -= c_y;
        }
    }
    this->SetRequireUpdate();
}

void SGGraphics::AddConnect(float x, float y, bool isrelative)
{
    // 必须存在起点和终点
    if (this->start_terminal.isnull() || this->end_terminal.isnull())
        return;

    if (!isrelative)
    {
        x -= this->x();
        y -= this->y();
    }
    this->connect_nodes.emplace_back(Vec2(x, y));
    this->SetRequireUpdate();
}

void SGGraphics::DeleteConnect()
{
    if (this->connect_nodes.empty())
        return;

    this->connect_nodes.pop_back();
    this->SetRequireUpdate();
}

void SGGraphics::ComfirmPoint()
{
    auto c_x = this->x();
    auto c_y = this->y();
    {
        this->start_terminal.x += c_x;
        this->start_terminal.y += c_y;
        this->end_terminal.x += c_x;
        this->end_terminal.y += c_y;
        for (auto& p : this->connect_nodes)
        {
            p.x += c_x;
            p.y += c_y;
        }
    }

    CreateBoundingBox(this);
    c_x = this->x();
    c_y = this->y();

    {
        this->start_terminal.x -= c_x;
        this->start_terminal.y -= c_y;
        this->end_terminal.x -= c_x;
        this->end_terminal.y -= c_y;
        for (auto& p : this->connect_nodes)
        {
            p.x -= c_x;
            p.y -= c_y;
        }
    }
    this->SetRequireUpdate();
}

void SGGraphics::CreateBoundingBox(SGGraphics* obj)
{
    auto line_vec = obj->start_terminal - obj->end_terminal;
    auto calc_dis = [](const Vec2& start, const Vec2& end, const float& p_x, const float& p_y)
        {
            return std::abs((end.y - start.y) * p_x - (end.x - start.x) * p_y + end.x * start.y - end.y * start.x) /
                std::sqrt((end.y - start.y) * (end.y - start.y) + (end.x - start.x) * (end.x - start.x));
        };
    float maxdis = -FLT_MAX;
    for (auto& p : obj->connect_nodes)
    {
        auto dis = calc_dis(obj->start_terminal, obj->end_terminal, p.x, p.y);
        if (dis > maxdis) maxdis = dis;
    }

    maxdis = std::max(maxdis, 10.f);

    float center_x = (obj->start_terminal.x + obj->end_terminal.x) * 0.5f;
    float center_y = (obj->start_terminal.y + obj->end_terminal.y) * 0.5f;

    float w = (float)cv::norm(cv::Point2f(obj->start_terminal.x, obj->start_terminal.y) - cv::Point2f(obj->end_terminal.x, obj->end_terminal.y));
    float h = maxdis * 2;
    float a = A_RAD_TO_DEG(std::atan2(-line_vec.y, -line_vec.x));
    obj->SetXY(center_x, center_y);
    obj->SetWH(w, h);
    obj->SetA(a);
}

void SGGraphics::DrawInfo(SGGraphics* obj, Renderer* r, Painter* p, const LayerConfig* config)
{
    (void)config;(void)p;
    // cv::Rect2f tr = obj->GetBoundingbox().boundingRect2f();
    auto pd = r->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        Color c;
        float think_ness;
        this->SetColorAndThickness(config, c, think_ness);
        QPainter painter(device);
        painter.beginNativePainting();
        if (obj->settings.GetIsSelected())
        {
            painter.setPen(QPen(QColor(c.r, c.g, c.b, c.a), 1,
                Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin));
            painter.setFont(QFont("Arial", 10));
        }
        else
        {
            painter.setPen(QPen(QColor(c.r, c.g, c.b, c.a), 1,
                Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin));
        }

        QString text = QString("ID:%1").arg(obj->GetId().GetString().c_str());
        QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);

        float text_rb_x = 0;
        float text_rb_y = 0;
        r->MouseToWorld(text_rb_x, text_rb_y);
        float text_tl_x = textsize.width();
        float text_tl_y = textsize.height();
        r->MouseToWorld(text_tl_x, text_tl_y);
        float text_o_x = abs(text_tl_x - text_rb_x);
        float text_o_y = abs(text_tl_y - text_rb_y);

        auto x = obj->x();
        auto y = obj->y();
        auto start = obj->GetStart();
        float tlx = start.x + x - abs(text_o_x * 0.5);
        float tly = start.y + y - obj->terminal_size - abs(text_o_y * 0.5);
        r->WorldToMouse(tlx, tly);

        painter.drawText(QRectF(tlx, tly, textsize.width(), textsize.height()),
            Qt::AlignCenter | Qt::TextWordWrap, text);
        painter.endNativePainting();
    }
}

std::string MultiRegionGraphics::GetSerializedData()
{
    return std::string();
}

void MultiRegionGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void MultiRegionGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;
    Color c;
    float think_ness;
    this->SetColorAndThickness(config, c, think_ness);

    UpdateDrawPath(obj);

    auto size = cv::Size2f(obj->region_w, obj->region_h);
    for (auto& point : obj->controls)
    {
        cv::RotatedRect subrect(cv::Point2f(point.x, point.y), cv::Size2f(obj->region_w, obj->region_h),
            obj->region_angle);
        DrawBoundingbox(p, subrect, c, think_ness);
    }
    DrawBoundingbox(p, obj->GetBoundingbox(), c, think_ness);
}

std::vector<ControlPoint> MultiRegionGraphics::CreateControlPoint()
{
    auto const obj = GetTemp(this, true);
    UpdateDrawPath(obj);

    std::vector<ControlPoint> cps;

    /*创建中心拖拽控制点*/
    {
        auto tcps = CreateSizeControlPoint(obj->x(), obj->y(), obj->w(), obj->h(), obj->a(), static_cast<int>(CustomCursorType::HandClose), -1);
        cps.insert(cps.end(), tcps.begin(), tcps.end());
        auto cp = CreateMoveControlPoint(obj->x(), obj->y(), obj->w(), obj->h(), obj->a(), static_cast<int>(CustomCursorType::Move), -1);
        cps.emplace_back(cp);
    }
    /*创建轮廓拖拽控制点*/
    {
        int count = 0;
        for (auto& p : obj->controls)
        {
            auto tcps = CreateSizeControlPoint(p.x, p.y, region_w, region_h, region_angle, static_cast<int>(CustomCursorType::HandClose), count);
            cps.insert(cps.end(), tcps.begin(), tcps.end());

            auto cp = CreateSingleControlPoint(p.x, p.y, control_point_w, control_point_h, static_cast<int>(CustomCursorType::SelectAlternate), true, {}, ControlPointType::CONTOUR_POINT, count);
            cps.emplace_back(cp);
            count++;
        }
    }
    return cps;
}

int MultiRegionGraphics::ResponseControlPoint(const float& xstart, const float& ystart, const float& xend, const float& yend, const bool& istemp, const ControlAttributes& attr)
{
    auto obj = GetAndUpdateTemp(this, istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;

    if (attr.id == -1)
    {
        float x = obj->x(), y = obj->y(), w = obj->w(), h = obj->h(), a = obj->a();
        if (auto state = RectResponseControlPoint(x, y, w, h, a, attr.type, xstart, ystart, xend, yend);
            state != GraphicsAbstract::UNKNOWN_TYPE)
        {
            obj->SetXY(x, y);
            obj->SetWH(w, h);
            obj->SetA(a);
            SetRequireUpdate();
            return state;
        }
    }
    else if ((int)obj->controls.size() > attr.id)
    {
        float& x = obj->controls[attr.id].x, & y = obj->controls[attr.id].y, & w = obj->region_w, & h = obj->region_h, & a = obj->region_angle;
        if (auto state = RectResponseControlPoint(x, y, w, h, a, attr.type, xstart, ystart, xend, yend);
            state != GraphicsAbstract::UNKNOWN_TYPE)
        {
            auto boundingrect = obj->GetBoundingbox().boundingRect2f();
            LimitRectInRect(x, y, w, h,
                boundingrect.x + boundingrect.width * 0.5f, boundingrect.y + boundingrect.height * 0.5f, boundingrect.width, boundingrect.height);

            auto center = obj->GetCenter();
            obj->region_cx = x - center.x;
            obj->region_cy = y - center.y;

            SetRequireUpdate();
            UpdateDrawPath(obj);
            return state;
        }
    }

    switch (ControlPointType(attr.type))
    {
    case ControlPointType::CONTOUR_POINT:
    {
        float xo = xend - xstart;
        float yo = yend - ystart;
        if ((int)obj->controls.size() > attr.id)
        {
            auto control = obj->controls[attr.id];
            control.x += xo;
            control.y += yo;
            auto boundingrect = obj->GetBoundingbox().boundingRect2f();
            LimitRectInRect(control.x, control.y, obj->region_w, obj->region_h,
                boundingrect.x + boundingrect.width * 0.5f, boundingrect.y + boundingrect.height * 0.5f, boundingrect.width, boundingrect.height);

            auto center = obj->GetCenter();
            obj->region_cx = control.x - center.x;
            obj->region_cy = control.y - center.y;

            SetRequireUpdate();
            UpdateDrawPath(obj);
        }
    }
    break;
    default:
        return GraphicsAbstract::UNKNOWN_TYPE;
    }
    return OK;
}

void MultiRegionGraphics::UpdateDrawPath(MultiRegionGraphics* obj)
{
    if (obj->IsNeedUpdate() || obj->controls.empty())
    {
        if (obj->region_num < 2)
            return;
        auto center = obj->GetCenter();
        std::vector<Vec2> controls_;
        switch (region_type)
        {
        case 0:
        {
            auto region_first_center = cv::Point2f(obj->region_cx, obj->region_cy) + center;
            float radius = sqrt(obj->region_cx * obj->region_cx + obj->region_cy * obj->region_cy);
            auto points = GeneratePositionsWithStartingObject(center, region_first_center, obj->region_num, radius);
            for (auto& point : points)
            {
                controls_.emplace_back(point.x, point.y);
            }
        }
        break;
        case 1:
        {
            controls_.emplace_back(center.x + obj->region_cx, center.y + obj->region_cy);
            controls_.emplace_back(center.x - obj->region_cx, center.y + obj->region_cy);
        }
        break;
        case 2:
        {
            controls_.emplace_back(center.x + obj->region_cx, center.y + obj->region_cy);
            controls_.emplace_back(center.x + obj->region_cx, center.y - obj->region_cy);
        }
        break;
        };
        obj->controls.swap(controls_);
        obj->SetUpdated();
    }
}

