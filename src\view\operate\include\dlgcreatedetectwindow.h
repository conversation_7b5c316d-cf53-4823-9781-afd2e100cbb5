﻿#ifndef DLGCREATEDETECTWINDOW_H
#define DLGCREATEDETECTWINDOW_H

#include <QDialog>
#include <map>
#include <vector>

namespace Ui {
    class DlgCreateDetectWindow;
}

enum class SubWindowType
{
    NO_WINDOW, // 没有检测框
    ONE_WINDOW, // 单检测框
    CROSS_WINDOWS, // 十字检测框
    FOUR_CORNERS_WINDOWS, // 四角检测框
    HORIZONTAL_SYMMETRY_WINDOWS, //  水平对称检测框
    VERTICAL_SYMMETRY_WINDOWS   //  垂直对称检测框
};


class DlgCreateDetectWindow : public QDialog
{
    Q_OBJECT

public:
    explicit DlgCreateDetectWindow(QWidget* parent = nullptr);
    ~DlgCreateDetectWindow();

    void SetAlgoListShowByAlgoType(std::string _algo_type);
    void SetAlgoList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list);
    void SetDefectList(const std::vector<std::string>& _defect_list);
    void GetDetectWindowInfo(std::string& algo_name, std::string& defect_name, SubWindowType& sub_window_type);

private:
    Ui::DlgCreateDetectWindow* ui;
    std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_name_list;

    /**
     * @fun InitAlogSpeficDefectName
     * @brief 初始化算法默认缺陷名称
     * <AUTHOR>
     * @date 2025.2.19
     */
    void InitAlogSpeficDefectName();
    void SetDeflautDefectByAlgo(std::string algo_name);
    void SetDrawSubWindowEnableByAlgo(std::string algo_name);

    std::map<std::string, bool> draw_sub_window_enable_map;
    std::map<std::string, std::string> default_defect_map;
    std::vector<std::string> defect_name_lists;
};

#endif // DLGCREATEDETECTWINDOW_H
