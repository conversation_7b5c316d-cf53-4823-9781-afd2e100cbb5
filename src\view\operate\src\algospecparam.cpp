﻿#include "algospecparam.h"
#include "ui_algospecparam.h"

AlgoSpecParam::AlgoSpecParam(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::AlgoSpecParam)
{
    ui->setupUi(this);
    InitConnection();
}

AlgoSpecParam::~AlgoSpecParam()
{
    delete ui;
}

void AlgoSpecParam::InitView(const std::string& param_)
{
    while (ui->param_table_widget->rowCount() > 0)
    {
        ui->param_table_widget->removeRow(0);
    }
    if (param_ == "")
    {
        return;
    }
    try
    {
        // key:name value:json字符串
        JSON arr = JSON::parse(param_);
        if (arr.is_array())
        {
            for (size_t i = 0; i < arr.size(); i++)
            {
                JSON obj = arr[i];
                if (obj.contains("name") && obj.contains("value"))
                {
                    std::string item_name = obj["name"].get<std::string>();
                    std::vector<float> item_value = obj["value"].get<std::vector<float>>();
                    if (item_value.size() > 0 && item_name != "")
                    {
                        AddOneLine(QString::fromStdString(item_name), item_value);
                    }
                }
            }
        }
    }
    catch (const std::exception&)
    {

    }
    ui->param_table_widget->resizeColumnsToContents();
    if (ui->param_table_widget->columnCount() > 0)
    {
        ui->param_table_widget->setColumnWidth(0, 100);
    }
}

jrsdata::AlgoSpecRatioParam AlgoSpecParam::GetSpecRatioParam(int index)
{
    jrsdata::AlgoSpecRatioParam param;
    int column_count = ui->param_table_widget->columnCount();
    if (index >= 0 && column_count > 0)
    {
        param.x_offset_ratio = GetValue(index, 1);
        param.y_offset_ratio = GetValue(index, 2);
        param.rotate_ratio = GetValue(index, 3);
        param.score_ratio = GetValue(index, 4);
        param.area_ratio = GetValue(index, 5);
        param.height_measurement = GetValue(index, 6);
        param.relative_height = GetValue(index, 7);
        param.weld_length_ratio = GetValue(index, 8);
        param.weld_width_ratio = GetValue(index, 9);
        param.weld_fullness_ratio = GetValue(index, 10);
        param.bridge_length_ratio = GetValue(index, 11);
        param.block_ratio = GetValue(index, 12);
        param.enable = true;
    }
    return param;
}

void AlgoSpecParam::InitConnection()
{
    // 保存
    connect(ui->save_btn, &QPushButton::clicked, this, [=]() {
        // 先保存
        emit SigAlgoRatioChange(PackgeRatioParam());
        this->close();
        });

    // 新增
    connect(ui->add_btn, &QPushButton::clicked, this, [=]() {
        std::vector<float> default = { 33.0f, 33.0f, 60.0f,70.0f,20.0f,100.0f,100.0f,67.0f,67.0f,67.0f,100.0f,66.0f };
        AddOneLine("默认", default);
        });

    // 删除
    connect(ui->delete_btn, &QPushButton::clicked, this, [=]() {
        RemoveCurLine();
        });

    // 关闭
    connect(ui->close_btn, &QPushButton::clicked, this, [=]() {
        this->close();
        });

}

void AlgoSpecParam::AddOneLine(QString name, std::vector<float> value)
{
    // 获取当前行数
    int rowCount = ui->param_table_widget->rowCount();

    // 在末尾插入新行
    ui->param_table_widget->insertRow(rowCount);

    // 添加数据
    QTableWidgetItem* item_name = new QTableWidgetItem(name);
    ui->param_table_widget->setItem(rowCount, 0, item_name);
    for (int col = 1; col < value.size() + 1; ++col)
    {
        QTableWidgetItem* item = new QTableWidgetItem(QString::number(value[col - 1]));
        ui->param_table_widget->setItem(rowCount, col, item);
    }
}

void AlgoSpecParam::RemoveCurLine()
{
    int selected_row = ui->param_table_widget->currentRow();
    if (selected_row != -1)
    {
        ui->param_table_widget->removeRow(selected_row);
    }
}

float AlgoSpecParam::GetValue(int row, int col)
{
    QTableWidgetItem* item1 = ui->param_table_widget->item(row, col);
    int column_count = ui->param_table_widget->columnCount();
    if (item1 && item1->text() != "" && col < column_count)
    {
        return std::abs(item1->text().toFloat());
    }
    return 0.0f;
}

std::string AlgoSpecParam::PackgeRatioParam()
{
    int row_count = ui->param_table_widget->rowCount();
    int column_count = ui->param_table_widget->columnCount();
    JSON arr;
    for (int i = 0; i < row_count; i++)
    {
        std::vector<float> row_value;
        for (int j = 1; j < column_count; j++)
        {
            row_value.push_back(GetValue(i, j));
        }
        QTableWidgetItem* item0 = ui->param_table_widget->item(i, 0);
        JSON obj;
        obj["name"] = item0->text().toStdString();
        obj["value"] = row_value;

        arr.push_back(obj);
    }
    return arr.dump();
}
