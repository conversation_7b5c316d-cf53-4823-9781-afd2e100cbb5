
#ifndef GRAPHICSPROCESS_H
#define GRAPHICSPROCESS_H
#include "controlpointconstants.hpp"

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "opencv2/core/types_c.h"
#pragma warning(pop)
#include <vector>
#include <string>

class GraphicsAbstract;
class Painter;

const int control_point_w = 2; /**< 控制点宽度 */
const int control_point_h = 2; /**< 控制点高度 */

template <typename T>
std::string createConfig(T* m)
{
    std::ostringstream oss;
    /*序列化的时候需要销毁档案(archive)后才能安全获取数据*/
    {
        cereal::JSONOutputArchive archive(oss);
        /*cereal只支持智能指针,这里将this转成智能指针,
            为了避免释放指针,自定义删除器*/
        std::shared_ptr<T> gp(m, [](T*)
            {
                // do nothing
            });
        archive(gp);
        // archive(this);
    }
    return oss.str();
}

/**
 * @brief  更新点边界
 * @fn     UpdateBoundingLimits
 * @note   这里不使用min(),max()函数的原因是，min(),max()函数会对传入数据类型进行限制
 *         需要在外部先给maxx,maxy,minx,miny赋初值(极大,极小),否则函数可能不生效
 * @tparam Point 点类型,要求包含x,y两个成员变量
 */
template <typename Point>
void UpdateBoundingLimits(const Point& p, float& maxx, float& maxy, float& minx, float& miny)
{
    if (maxx < p.x)
        maxx = p.x;
    if (maxy < p.y)
        maxy = p.y;
    if (minx > p.x)
        minx = p.x;
    if (miny > p.y)
        miny = p.y;
}

template <typename T>
void GetContoursMinMax(float& maxx, float& minx, float& maxy, float& miny, const std::vector<T>& contours)
{
    maxx = maxy = -FLT_MAX;
    minx = miny = FLT_MAX;
    for (const auto& p : contours)
    {
        UpdateBoundingLimits(p, maxx, maxy, minx, miny);
    }
}

void LimitRectInRect(float& rect_center_x, float& rect_center_y, const float& rect_width, const float& rect_height,
    const float& limit_rect_center_x, const float& limit_center_y, const float& limit_rect_width, const float& limit_rect_height);


void RectDoTop(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoDown(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoLeft(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoRight(GraphicsAbstract* const obj, const cv::Point2f& vec, const cv::Point2f& moveVec);

int RectResponseControlPoint(GraphicsAbstract* obj, const int& type, const float& xstart, const float& ystart,
    const float& xend, const float& yend);

void RectDoTop(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoDown(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoLeft(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec);

void RectDoRight(float& x, float& y, float& w, float& h, const cv::Point2f& vec, const cv::Point2f& moveVec);

int RectResponseControlPoint(float& x, float& y, float& w, float& h, float& a, const int& type, const float& xstart, const float& ystart,
    const float& xend, const float& yend);

ControlPoint CreateSingleControlPoint(float cx, float cy, int width, int height, int cursor_shape, bool is_render, const std::vector<Vec2>& contours,
    ControlPointType type, int index);

ControlPoint CreateMoveControlPoint(float x, float y, float w, float h, float a, int cursor_shape, int index = 0, const std::vector<Vec2>& contour = {});

ControlPoint CreateMoveControlPoint(GraphicsAbstract* const obj, int cursor_shape, int index = 0, const std::vector<Vec2>& contour = {});

std::vector<ControlPoint> CreateSizeControlPoint(float x, float y, float w, float h, float a, int cursor_shape, int index = 0);

std::vector<ControlPoint> CreateSizeControlPoint(GraphicsAbstract* const obj, int cursor_shape, int index = 0);

std::vector<ControlPoint> CreateRotateControlPoint(GraphicsAbstract* const obj, int cursor_shape);

std::vector<ControlPoint> CreateContourControlPoint(GraphicsAbstract* const obj, int cursor_shape, const std::vector<Vec2>& contour);

std::vector<ControlPoint> CreateBorderControlPoint(GraphicsAbstract* const obj, int cursor_shape, const std::vector<Vec2>& points);

void DrawBoundingbox(Painter* p, const cv::RotatedRect& boundingbox, const Color& c, const float& thickness);

std::vector<cv::Point2f> GeneratePositionsWithStartingObject(
    const cv::Point2f& center,
    const cv::Point2f& firstObjectCenter,
    int numObjects,
    float radius);
#endif // GRAPHICSPROCESS_H