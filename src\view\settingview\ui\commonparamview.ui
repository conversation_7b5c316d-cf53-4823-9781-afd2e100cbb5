<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>commonparamviewClass</class>
 <widget class="QWidget" name="commonparamviewClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>910</width>
    <height>646</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="btn_refresh_param">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>846</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_save_param">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QGroupBox" name="groupBox">
       <property name="title">
        <string>Demo</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>Demo</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="line_edit_demo"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_2">
       <property name="title">
        <string>工程设置</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>默认路径：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="line_edit_project_path"/>
        </item>
        <item row="0" column="2">
         <widget class="QToolButton" name="btn_choose_project_path">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>数据库设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="1" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>用 户 名：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>密    码：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>数据库IP：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>数据库名：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>连 接 数：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QSpinBox" name="db_connect_num_spin_box">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>50</number>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="QPushButton" name="btn_db_connect">
        <property name="text">
         <string>连接</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1" colspan="2">
       <widget class="QLineEdit" name="db_name_line_edit">
        <property name="text">
         <string>jrs_aoi20_db</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1" colspan="2">
       <widget class="QLineEdit" name="db_pwd_line_edit">
        <property name="text">
         <string>Jrs123456</string>
        </property>
        <property name="echoMode">
         <enum>QLineEdit::PasswordEchoOnEdit</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1" colspan="2">
       <widget class="QLineEdit" name="db_ip_line_edit"/>
      </item>
      <item row="1" column="1" colspan="2">
       <widget class="QLineEdit" name="db_user_line_edit">
        <property name="text">
         <string>jrs</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="1">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
