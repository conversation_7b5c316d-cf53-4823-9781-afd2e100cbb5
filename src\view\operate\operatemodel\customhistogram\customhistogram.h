/*****************************************************************//**
 * @file   CustomHistogram.h
 * @brief 竖向柱状图显示界面
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef CUSTOM_HISTOGRAM_H
#define CUSTOM_HISTOGRAM_H
 //QT
#include <QWidget>
#include <QMouseEvent>
 //STD
#include <vector>
//CUSTOM
#include "datadefine.hpp"

class CustomHistogram : public QWidget
{
    Q_OBJECT
public:
    explicit CustomHistogram(QWidget* parent = 0);
    ~CustomHistogram();
    /**
     * @fun ClearHistogram
     * @brief 清除竖向柱状图数据和界面
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ClearHistogram();
    /**
     * @fun SetDataInfo
     * @brief 设置竖向柱状图数据
     * @param data
     * @date 2024.2.6
     * <AUTHOR>
     */
    void SetDataInfo(std::vector<RingData> data_);
    /**
     * @fun SetPercent
     * @brief 设置是否显示百分比
     * @param perc
     * @date 2024.2.6
     * <AUTHOR>
     */
    void SetPercent(bool percent);
    /**
     * @fun SetShowCount
     * @brief 设置是否显示数值
     * @param show_count
     * @date 2024.2.6
     * <AUTHOR>
     */
    void SetShowCount(bool show_count);
    /**
     * @fun SetGroupEnable
     * @brief 设置按照分组计算方式
     * @param value true 按照分数个数计算比例
     * @date 2025.2.24
     * <AUTHOR>
     */
    void SetGroupEnable(bool value);
    /**
     * @fun SetGroupNum
     * @brief 设置每个分组个数
     * @param value 每个分组内个数
     * @date 2025.2.24
     * <AUTHOR>
     */
    void SetGroupNum(int value);
protected:
    /**
     * @fun paintEvent
     * @brief 画竖向柱状图
     * @param
     * @date 2024.9.24
     * <AUTHOR>
     */
    void paintEvent(QPaintEvent*);
private:
    std::vector<QColor>     m_colors;                           //颜色集合
    std::vector<RingData>   m_ring_data;                        //数据集合
    QPoint                  m_start_pos;                        //开始点
    bool                    m_show_percent;                     //是否显示百分比
    bool                    m_show_count;                       //是否显示数字
    double                  m_axis_width;                       //X坐标轴刻度宽度
    double                  m_axis_height;                      //Y坐标轴刻度宽度
    double                  m_max_percent;                      //最大百分比
    bool                    m_group_enable;                     //分组计算百分比
    int                     m_group_num;                        //每个分组内元素个数

private:
    /**
     * @fun Init
     * @brief 初始化
     * @date 2024.2.7
     * <AUTHOR>
     */
    void Init();
    /**
     * @fun GetSumValue
     * @brief 获取数据总和，数据存放在m_ring_data
     * @param start 开始index
     * @param end 结束index
     * @return
     * @date 2024.2.6
     * <AUTHOR>
     */
    double GetSumValue(int start, int end);
    /**
     * @fun PaintCoordinateValue
     * @brief 绘制坐标轴上的数值标签，包括 Y 轴的百分比标签和 X 轴的分类标签。
     * @param painter QPainter 对象，用于绘图操作。
     * @date 2025.02.21
     * <AUTHOR>
     */
    void PaintCoordinateValue(QPainter* painter);
    /**
     * @fun PaintCoordinateCalibration
     * @brief 绘制坐标刻度
     * @param painter
     * @date 2024.2.6
     * <AUTHOR>
     */
    void PaintCoordinateCalibration(QPainter* painter);
    /**
     * @fun PaintCoordinateAxis
     * @brief 绘制坐标轴两条直线
     * @param painter
     * @date 2024.2.6
     * <AUTHOR>
     */
    void PaintCoordinateAxis(QPainter* painter);
    /**
     * @fun DrawHistogram
     * @brief 绘制柱状图，包括计算百分比、更新矩形位置、绘制柱状图以及坐标轴相关元素。
     * @param painter QPainter 对象，用于绘图操作。
     * @date 2025.02.21
     * <AUTHOR>
     */
    void DrawHistogram(QPainter* painter);
    /**
    * @fun CalculateHistogramPercentages
    * @brief 计算每个柱状图的百分比和标签。
    * @date 2025.02.21
    * <AUTHOR>
    */
    void CalculateHistogramPercentages();
    /**
     * @fun CalculateGroupSums
     * @brief 计算每个分组的总和。
     * @return 返回每个分组的总和。
     * @date 2025.02.21
     * <AUTHOR>
     */
    std::vector<double> CalculateGroupSums();
    /**
     * @fun GetGroupIndex
     * @brief 根据索引获取分组编号。
     * @param index 当前索引。
     * @return 分组编号。
     * @date 2025.02.21
     * <AUTHOR>
     */
    int GetGroupIndex(int index);
    /**
     * @fun UpdateHistogramRectangles
     * @brief 更新柱状图矩形的位置和大小。
     * @date 2025.02.21
     * <AUTHOR>
     */
    void UpdateHistogramRectangles();
    /**
     * @fun DrawHistogramBars
     * @brief 绘制柱状图。
     * @param painter QPainter 对象，用于绘图操作。
     * @date 2025.02.21
     * <AUTHOR>
     */
    void DrawHistogramBars(QPainter* painter);
};
#endif
