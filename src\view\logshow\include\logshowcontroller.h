/*****************************************************************//**
 * @file   logshowcontroller.h
 * @brief  日志显示控制器
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __LOGSHOWCONTROLLER_H__
#define __LOGSHOWCONTROLLER_H__
 //STD
 // 
//Custom
#include "controllerbase.h"
//Third
namespace jrsaoi
{
    class LogShowView;
    class LogShowModel;
    class LogShowController :public ControllerBase
    {
    public:
        LogShowController(const std::string& name);
        ~LogShowController();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        virtual void SetView(ViewBase* view_param)override;
        virtual void SetModel(ModelBasePtr model_param)override;
    private:
        LogShowView* log_show_view;
        std::shared_ptr<LogShowModel> model;

    };
    using LogShowControllerPtr = std::shared_ptr<LogShowController>;

}
#endif // !__LOGSHOWCONTROLLER_H__
