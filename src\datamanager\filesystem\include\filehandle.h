#pragma once
//std
#include <iostream>
//core
#include"coreapplication.h"
#include "projectparam.hpp"

//thirdparty
#pragma warning(push,1)

#include <iguana/iguana.hpp>
#include <ylt/struct_pack.hpp>

#include "opencv2/opencv.hpp"

#pragma warning(pop)
namespace jrsfiledata
{
    const std::string project_default_ext = ".jrspro";

    class FileHandle
    {
    public:
        FileHandle();
        ~FileHandle();
        /** < 文件读取  */
        template <typename T>
        int Read(T& data_)
        {
            if (!std::is_base_of<jrsdata::DataBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            std::string content_str = "";
            if (!ReadFile(data_.file_param.file_path + data_.file_param.file_name, content_str))
            {
                Log_Error_Stack("打开文件失败，请检查文件路径");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            try
            {
                switch (static_cast<jrsdata::FileType>(data_.file_param.file_type))
                {
                case jrsdata::FileType::JSON:
                    iguana::from_json(data_, content_str);
                    break;
                case jrsdata::FileType::XML:
                    //iguana::from_xml(data_, content_str);
                    break;
                case jrsdata::FileType::YAML:
                    //iguana::from_yaml(data_, content_str);
                    break;
                case jrsdata::FileType::BIN:
                {
                    auto ec = struct_pack::deserialize_to(data_, content_str);
                    if (ec.ec != struct_pack::errc::ok)
                    {
                        // Log_Error_Stack("序列化失败", ec.message());
                        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
                    }
                    break;
                }
                default:
                    break;
                }
            }
            catch (const std::exception& e)
            {
                Log_Error_Stack("文件映射失败，请检查:", e.what());
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            return 0;
        }
        /**< 文件保存  */
        template <typename T>
        int Save(T data_)
        {
            if (!iguana::ylt_refletable_v<T>)
            {
                Log_Error_Stack("该结构体不满足映射条件，请检查是否映射");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

            std::string data_str = "";
            try
            {
                switch (static_cast<jrsdata::FileType>(data_.file_param.file_type))
                {
                case jrsdata::FileType::JSON:
                    iguana::to_json(data_, data_str);
                    break;
                case jrsdata::FileType::XML:
                    //iguana::to_xml(data_, data_str);
                    break;
                case jrsdata::FileType::YAML:
                    iguana::to_yaml(data_, data_str);
                    break;
                case jrsdata::FileType::BIN:
                    /* iguana::*/
                    data_str = struct_pack::serialize<std::string>(data_);
                    //iguana::to_pb(data_, data_str);
                default:
                    break;
                }
            }
            catch (const std::exception& e)
            {
                Log_Error_Stack("文件映射失败，请检查:", e.what());
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            auto path = data_.file_param.file_path + data_.file_param.file_name;
            if (path.empty())
            {
                Log_Error_Stack("文件路径为空，保存数据失败！");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

            WriteFile(path, data_str);
            return 0;
            //写入文件
        }

        int Save(const cv::Mat& img_, const std::string& file_path_);
        int Read(cv::Mat& img_, const std::string& file_path_);

        /**< 将cv::Mat保存成二进制  */

        // 写入矩阵到二进制文件
        int WriteMatToBinary(const cv::Mat& mat, const std::string& filename);

        // 从二进制文件读取矩阵
        int ReadMatFromBinary(cv::Mat& mat, const std::string& filename);


        /**
         * @fun SaveString
         * @brief 保存字符串信息到本地文件
         * @param path_[IN] 路径地址
         * @param content_ [IN] 内容
         * @return 成功返回AOI_OK,失败返回错误码
         * <AUTHOR>
         * @date 2025.1.11
         */
        int SaveString(const std::string& path_, const std::string& content_);

    private:

        /**
         * @fun WriteFile
         * @brief
         * @param path_
         * @param content_
         * @return
         * <AUTHOR>
         * @date 2025.1.11
         */
        bool WriteFile(const std::string& path_, const std::string& content_);

        bool ReadFile(const std::string& path_, std::string& content_);

    };
    using FileHandlePtr = std::shared_ptr<FileHandle>;
}