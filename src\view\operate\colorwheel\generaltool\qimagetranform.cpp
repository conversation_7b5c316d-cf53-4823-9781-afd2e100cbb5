#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "qimagetranform.h"
#include <QPixmap>
#include <QSize>
#include <QLabel>
#pragma warning(pop)

QImage imagetrans::MatToQImage(const cv::Mat& mat)
{
	auto mat_copy = mat.clone();

    if (mat_copy.channels() == 1)
    {
        return QImage(mat_copy.data, int(mat_copy.cols), int(mat_copy.rows), int(mat_copy.step), QImage::Format_Grayscale8);
    } 
    else if (mat_copy.channels() == 3) 
    {
        return QImage(mat_copy.data, int(mat_copy.cols), int(mat_copy.rows), int(mat_copy.step), QImage::Format_RGB888).rgbSwapped();
    } 
    else
    {
        throw std::runtime_error("Unsupported image format");
    }
}

void imagetrans::SetImageToLabel(QLabel* label, const cv::Mat& image)
{
    QImage qImage = MatToQImage(image);
    QPixmap pixmap = QPixmap::fromImage(qImage);
    QSize labelSize = label->size();
    QPixmap scaledPixmap = pixmap.scaled(labelSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    label->setPixmap(scaledPixmap);
}
