#include <QHeaderView>
#include "templatelistwidget.h"

Q_DECLARE_METATYPE(cv::Mat)

TemplateListWidget::TemplateListWidget(QWidget *parent)
    : QListWidget{parent}
{
    setViewMode(QListView::IconMode);
    setFlow(QListView::LeftToRight);
    setSpacing(10);
    setIconSize(QSize(100, 100));
    connect(this, &QListWidget::itemSelectionChanged, this, &TemplateListWidget::SoltItemSelectionChanged);
    connect(this, &QListWidget::itemDoubleClicked, this, &TemplateListWidget::SoltItemDoubleClicked);
}

int TemplateListWidget::UpdateAllItems(const QList<TemplateItemValue>& item_vals)
{
    clear();
    for (int i = 0; i < item_vals.size(); i++)
    {
        AddItem(item_vals[i]);
    }
    return 0;
}

int TemplateListWidget::AddItem(const TemplateItemValue& item_val)
{
    QListWidgetItem* item = new QListWidgetItem(item_val.img_show, QString::number(item_val.id));
    item->setData(Qt::UserRole, item_val.id);
    item->setData(Qt::UserRole + 1, item_val.light_type);
    item->setData(Qt::UserRole + 2, item_val.color_param);
    const QVariant qvar_img_src = QVariant::fromValue(item_val.img_src);
    item->setData(Qt::UserRole + 3, qvar_img_src);
    item->setTextAlignment(Qt::AlignHCenter); // 设置文本居中
    addItem(item);
    item->setText(QString::number(item_val.id)+"("+ QString::number(item_val.light_type) +")");
    return 0;
}

int TemplateListWidget::GetItemTemplateData(int index, TemplateItemValue& item_val)
{
    QListWidgetItem* it = this->item(index);
    item_val.id = it->data(Qt::UserRole).toInt();
    item_val.light_type = it->data(Qt::UserRole + 1).toInt();
    item_val.color_param = it->data(Qt::UserRole + 2).toString();
    item_val.img_src = it->data(Qt::UserRole + 3).value<cv::Mat>();
    return 0;
}

int TemplateListWidget::UpdateItemTemplateData(int index, const TemplateItemValue& item_val)
{
    QListWidgetItem* it = this->item(index);
    it->setData(Qt::UserRole, item_val.id);
    it->setData(Qt::UserRole + 1, item_val.light_type);
    it->setData(Qt::UserRole + 2, item_val.color_param);
    const QVariant qvar_img_src = QVariant::fromValue(item_val.img_src);
    it->setData(Qt::UserRole + 3, qvar_img_src);
    it->setIcon(item_val.img_show);
    it->setText(QString::number(item_val.id));
    return 0;
}

std::vector<int> TemplateListWidget::DeleteSelectItem()
{
    QList<QListWidgetItem*> items = selectedItems();
    std::vector<int> ids;
    for (int i = 0; i < items.size(); i++)
    {
        QListWidgetItem* item = items[i];
        ids.push_back(item->data(Qt::UserRole).toInt());
        delete item;  
    }
    return ids;
}

void TemplateListWidget::SoltItemDoubleClicked(QListWidgetItem* item)
{
    TemplateItemValue item_val;
    item_val.id = item->data(Qt::UserRole).toInt();
    item_val.light_type = item->data(Qt::UserRole + 1).toInt();
    item_val.color_param = item->data(Qt::UserRole + 2).toString();
    item_val.img_src = item->data(Qt::UserRole + 3).value<cv::Mat>();
    emit SigTemplateDoubleClicked(item_val);
}

void TemplateListWidget::SoltItemSelectionChanged()
{
    QList<QListWidgetItem*> items = selectedItems();
    if (items.size() == 1)
    {
        TemplateItemValue item_val;

        item_val.id = items[0]->data(Qt::UserRole).toInt();
        item_val.light_type = items[0]->data(Qt::UserRole + 1).toInt();
        item_val.color_param = items[0]->data(Qt::UserRole + 2).toString();
        item_val.img_src = items[0]->data(Qt::UserRole + 3).value<cv::Mat>();     
        emit SigTemplateSelectionChanged(item_val);
    }
}
