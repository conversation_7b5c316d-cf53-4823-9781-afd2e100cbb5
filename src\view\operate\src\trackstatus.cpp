#include "trackstatus.h"
#include "coreapplication.h"
#pragma warning(push, 3)
#include "ui_trackstatus.h"
#pragma warning(pop)

TrackStatus::TrackStatus(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::TrackStatus)
{
    ui->setupUi(this);
    trackstatus_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
    track_index = jrsdata::TRACK_1;
}

TrackStatus::~TrackStatus()
{
    delete ui;
}

void TrackStatus::SetTrackName(QString name)
{
    ui->track_name->setText(name);
}

void TrackStatus::SetTrackIndex(jrsdata::TRACK_NUMBER index)
{
    track_index = index;
}

void TrackStatus::UpdateView(const jrsdata::OperateViewParamPtr ptr)
{
    if (!ptr)
    {
        return;
    }

    if (!CheckTrackStatus(ptr))
    {
        return;
    }

    const jrsdata::TrackInput& input = ptr->device_param.motion_param.motion_status.track_status[track_index].track_input;
    const jrsdata::TrackOutput& output = ptr->device_param.motion_param.motion_status.track_status[track_index].track_output;
    const jrsdata::Track& status = ptr->device_param.motion_param.motion_status.track_status[track_index];

    UpdateTrackMode(status);
    UpdateTrackDirection(status);
    UpdateBoardStatus(input, output);
    UpdateCylinderStatus(input, output);
    UpdateShieldStatus(input, output, status);
    UpdateBoardWidth(status);
}

bool TrackStatus::CheckTrackStatus(const jrsdata::OperateViewParamPtr ptr) const
{
    if (ptr->device_param.motion_param.motion_status.track_status.count(track_index) == 0)
    {
        Log_ERROR("当前轨道号没有轨道状态信息！");
        return false;
    }
    return true;
}

void TrackStatus::UpdateTrackMode(const jrsdata::Track& status)
{
    QString track_mode;
    switch (status.track_mode)
    {
    case 1:
        track_mode = "单机模式";
        break;
    case 2:
        track_mode = "老化模式";
        break;
    default:
        track_mode = "在线模式";
        break;
    }
    ui->track_mode->setText(track_mode);
}

void TrackStatus::UpdateTrackDirection(const jrsdata::Track& status)
{
    QString enter = status.enterDirection == 0 ? "左进" : "右进";
    QString leave = status.leaveDirection == 0 ? "左出" : "右出";
    ui->track_direction->setText(enter + leave);
}

void TrackStatus::UpdateBoardStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output)
{
    UpdateLabelBackGroundColor(ui->track_askboard_front, input.previous_ask_status, color_lightgray_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_askboard_back, input.next_ask_status, color_lightgray_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_import, input.import_status, color_transparent_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_slow, input.slow_status, color_transparent_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_stop, input.stop_status, color_transparent_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_export, input.export_status, color_transparent_style, color_lightgreen_style);

    UpdateLabelBackGroundColor(ui->track_ask_board, output.previous_output_status, color_lightgray_style, color_lightgreen_style);
    UpdateLabelBackGroundColor(ui->track_has_board, output.next_output_status, color_lightgray_style, color_lightgreen_style);
}

void TrackStatus::UpdateCylinderStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output)
{
    if (input.cylinder1_sensor__index == -1)
    {
        UpdateLabelBackGroundColor(ui->track_cylinder_top, output.cylinder1_status, color_lightblue_border_style, color_lightblue_style);
        UpdateLabelBackGroundColor(ui->track_cylinder_bottom, output.cylinder1_status, color_lightblue_border_style, color_lightblue_style);
    }
    else
    {
        UpdateLabelBackGroundColor(ui->track_cylinder_top, input.cylinder1_sensor_status, color_lightblue_border_style, color_lightblue_style);
        UpdateLabelBackGroundColor(ui->track_cylinder_bottom, input.cylinder1_sensor_status, color_lightblue_border_style, color_lightblue_style);
    }
}

void TrackStatus::UpdateShieldStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output, const jrsdata::Track& status)
{
    if (input.shield1_sensor__index == -1)
    {
        UpdateShieldLabels(output.shield1_status, status.enterDirection);
    }
    else
    {
        UpdateShieldLabels(input.shield1_sensor_status, status.enterDirection);
    }
}

void TrackStatus::UpdateShieldLabels(const std::string& status, int enterDirection)
{
    if (enterDirection == 0)  // 左进
    {
        UpdateLabelBackGroundColor(ui->track_shield_left, "0", color_lightblue_border_style, color_lightblue_style);
        UpdateLabelBackGroundColor(ui->track_shield_right, status, color_lightblue_border_style, color_lightblue_style);
    }
    else
    {
        UpdateLabelBackGroundColor(ui->track_shield_right, "0", color_lightblue_border_style, color_lightblue_style);
        UpdateLabelBackGroundColor(ui->track_shield_left, status, color_lightblue_border_style, color_lightblue_style);
    }
}

void TrackStatus::UpdateBoardWidth(const jrsdata::Track& status)
{
    ui->track_board_width->setText(QString::number(status.board_width) + "mm");
}

void TrackStatus::UpdateLabelBackGroundColor(QLabel* label, std::string state, QString style1, QString style2)
{
    label->setStyleSheet(state == "0" ? style1 : style2);
}