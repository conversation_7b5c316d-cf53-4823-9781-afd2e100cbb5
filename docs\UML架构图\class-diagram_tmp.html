<!DOCTYPE html>
<html>
<head>
<title>class-diagram.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="jrsaoi-20-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84uml%E7%B1%BB%E5%9B%BE">JRSAOI 2.0 整体架构UML类图</h1>
<pre><code class="language-mermaid"><div class="mermaid">classDiagram
    %% 视图层 (View Layer)
    class MainWindow {
        -ViewManager* view_manager
        -CustomTitleView* title_view
        -StatusBarView* status_bar
        +MainWindow(parent)
        +GetInitView() ViewBase*
        +HideLeftDockWidget()
        +HideRightDockWidget()
    }

    class ViewManager {
        -map~string, tuple~ mvc_container
        +RegisterComponent()
        +GetController(name) ControllerBasePtr
        +InitMember()
    }

    class ViewBase {
        <<abstract>>
        #string name
        +Init() int
        +UpdateView(param) int
        +Save(param) int
    }

    class OperateView {
        -Ui::OperateView* ui
        -MotiondebugView* motionview
        -DetectView* detect_view
        -ProjectView* project_view
        -EditDetectModelView* edit_detect_model_view
        +UpdateView(param) int
        +GetCustomWidget() QWidget*
    }

    class Render2DView {
        -Renderer2DWidget* render_widget
        -GraphicsManager* graphics_manager
        +UpdateGraphics(param)
        +HandleMouseEvent(event)
    }

    %% 控制器层 (Controller Layer)
    class ControllerBase {
        <<abstract>>
        #string name
        #ModelBasePtr model
        +Update(param) int
        +Save(param) int
        +SetView(view) void
        +SetModel(model) void
    }

    class OperateController {
        -OperateView* operate_view
        -AlgorithmEngineManager* algo_engine_ptr
        +SlotViewOperator(param)
        +SlotModelOperator(param)
        +ExecuteSingleComponent(component, pn_detect_info, is_save_algo_info, is_location)
        +ExecuteDetections(sorted_detect_wins, detect_win_exec_params, ...)
        +CurSelectedComponentRun(param, is_save_algo_info, is_location)
    }

    class Render2DController {
        -Render2DView* render_view
        -Render2DModel* render_model
        +HandleGraphicsUpdate(param)
        +ProcessCoordinateTransform(param)
    }

    %% 模型层 (Model Layer)
    class ModelBase {
        <<abstract>>
        #string name
        +Update(param) int
        +Save(param) int
    }

    class OperateModel {
        -Component* cur_selected_component
        -DetectWindow* cur_selected_detect_win
        -PNDetectInfo* cur_selected_spec_region
        -map~string, vector~ComponentAlgoResult~~ component_algo_results
        -SubboardSortManager* subboard_sort_manager_ptr
        +GetProjectParam() ProjectParamPtr
        +UpdateSelectedDetectWin(win_name)
        +UpdateSelectedComponent(param, component_changed, part_number_changed)
        +CreateOneDetectWindow(detect_operate_type, detect_window)
        +EraseSpeficComponentResult(component_name)
    }

    class Render2DModel {
        -vector~GraphicsObject~ graphics_objects
        -CoordinateTransform* coord_transform
        +AddGraphicsObject(obj)
        +RemoveGraphicsObject(id)
        +UpdateGraphicsData(param)
    }

    %% 逻辑层 (Logic Layer)
    class LogicManager {
        -DeviceManager* device_manager_ptr
        -ScanProcess* scan_process_ptr
        -JrsAoiImgsManager* imgs_manager_ptr
        -WorkFlowManager* work_flow_manager_ptr
        -DataManager* data_ptr
        -ProjectManager* project_manager_ptr
        +EventHandler(param) int
        +LogicEventHandler(param)
        +InitMember()
    }

    class ProjectManager {
        -ProjectParam* current_project
        +CreateProject(param) int
        +SaveProject(param) int
        +LoadProject(param) int
        +EventHandler(param) int
    }

    class WorkFlowManager {
        -vector~WorkFlowStep~ workflow_steps
        +WorkFlowEventHandler(param)
        +StartWorkFlow()
        +StopWorkFlow()
        +ExecuteStep(step)
    }

    %% 插件层 (Plugin Layer)
    class AlgorithmEngineManager {
        -AlgoFactory* algo_factory_ptr
        -AlgoExecute* algo_execute_ptr
        -LoadAlgoManager* load_algo_manager_ptr
        -nlohmann::json algo_param_config
        +ExecuteSpecificAlgoDrive(execute_algo_param) OperatorParamBasePtr
        +GetAlgoNameMap() map~string, string~
        +LoadAlgorithms()
    }

    class DeviceManager {
        -StructLight* struct_light_ptr
        -Motion* motion_ptr
        -BarcodeDevice* barcode_device_ptr
        -MotionDebug* motion_debug
        -MotionStatus* motion_status
        +EventHandler(param)
        +GetStructLightInstance() StructLightPtr
        +GetMotionInstance() MotionPtr
        +InitDevices()
    }

    %% 核心层 (Core Layer)
    class CoreApplication {
        <<singleton>>
        -ErrorHandler* error_handler
        -LogManager* log_manager
        +GetInstance() CoreApplication*
        +GetErrorHandler() ErrorHandlerPtr
        +GetLogManager() LogManagerPtr
    }

    class CoordinateTransform {
        -cv::Mat transform_matrix
        +PixelToPhysical(pixel_coord) PhysicalCoord
        +PhysicalToPixel(physical_coord) PixelCoord
        +SetTransformMatrix(matrix)
        +ApplyTransform(points) vector~Point~
    }

    %% 数据层 (Data Layer)
    class DataManager {
        -TableManager* table_manager_ptr
        -ParamConvertor* param_convertor_ptr
        -ProjectParam* project_param_ptr
        -AllSettingParamMap all_setting_param_map
        +EventHandler(param) int
        +SaveDetectResult(result_data)
        +LoadProjectData(project_name) ProjectParamPtr
        +ComponentDetectResultToRender(result)
    }

    class TableManager {
        -BoardTable* board_table
        -ProjectTable* project_table
        -DetectWindowTable* detect_window_table
        -UserTable* user_table
        +Save~T~(data) int
        +Query~T~(condition) vector~T~
        +Delete~T~(condition) int
    }

    %% 参数定义类
    class ProjectParam {
        +string project_name
        +string current_group_name
        +string project_path
        +Board board_info
        +vector~Template~ temps
        +unordered_map~int, cv::Mat~ entirety_board_imgs
        +bool is_save
    }

    class Board {
        +int width, height
        +int cols, rows
        +double real_width, real_height
        +vector~Mark~ marks
        +vector~Barcode~ barcodes
        +vector~SubBoard~ sub_board
        +unordered_map~string, PNDetectInfo~ part_nums_and_detect_regions
    }

    class Component {
        +string component_name
        +string component_part_number
        +double cx, cy
        +double width, height
        +double angle
        +bool enable
        +vector~ComponentModule~ modules
    }

    class DetectWindow {
        +string model_name
        +string name
        +string defect_name
        +int id
        +int cx, cy
        +int width, height
        +int level
        +bool enable
        +vector~DetectAlgorithm~ algorithms
    }

    %% 设备相关类
    class StructLight {
        -bool is_connected
        +Connect() bool
        +Disconnect()
        +CaptureImage() cv::Mat
        +SetExposure(exposure)
    }

    class Motion {
        -string ip_address
        -int port
        -bool is_connected
        +Connect() bool
        +MoveTo(x, y, z) bool
        +GetCurrentPosition() Position
        +SetSpeed(speed)
    }

    %% 渲染相关类
    class Renderer2D {
        -vector~GraphicsObject~ objects
        -Camera2D camera
        +Render()
        +AddObject(obj)
        +RemoveObject(id)
        +SetViewport(viewport)
    }

    class GraphicsObject {
        +int id
        +ObjectType type
        +vector~Point2D~ points
        +Color color
        +float line_width
        +Draw(renderer)
        +IsPointInside(point) bool
    }

    %% 继承关系
    ViewBase <|-- OperateView
    ViewBase <|-- Render2DView
    ControllerBase <|-- OperateController
    ControllerBase <|-- Render2DController
    ModelBase <|-- OperateModel
    ModelBase <|-- Render2DModel

    %% 组合关系
    MainWindow *-- ViewManager
    ViewManager *-- OperateController
    ViewManager *-- Render2DController
    OperateController *-- OperateView
    OperateController *-- OperateModel
    OperateController *-- AlgorithmEngineManager
    Render2DController *-- Render2DView
    Render2DController *-- Render2DModel

    LogicManager *-- DeviceManager
    LogicManager *-- ProjectManager
    LogicManager *-- WorkFlowManager
    LogicManager *-- DataManager

    DeviceManager *-- StructLight
    DeviceManager *-- Motion
    DataManager *-- TableManager
    DataManager *-- ProjectParam

    ProjectParam *-- Board
    Board *-- Component
    Component *-- DetectWindow

    Render2DView *-- Renderer2D
    Renderer2D *-- GraphicsObject

    %% 依赖关系
    OperateController ..> CoreApplication
    DataManager ..> CoreApplication
    AlgorithmEngineManager ..> CoordinateTransform
    OperateModel ..> ProjectParam
</div></code></pre>

</body>
</html>
