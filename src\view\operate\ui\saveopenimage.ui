<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SaveOpenImageView</class>
 <widget class="QWidget" name="SaveOpenImageView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>438</width>
    <height>362</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>保存和打开图片</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="label_product">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>23</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>23</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background:rgb(172, 172, 172);
color:rgb(45, 45, 45);
</string>
       </property>
       <property name="text">
        <string>保存和打开图片</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QLabel" name="title">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>路    径：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="title_2">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>工程名称：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QLineEdit" name="qline_save_img_path">
           <property name="styleSheet">
            <string notr="true">QLineEdit {
    border: 1px solid rgb(41, 57, 85); 
    border-radius: 3px;  
    background: white;   
    selection-background-color: green; 
    font-size: 16px
}</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="combobox_project_name"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <widget class="QPushButton" name="pushbutton_select_save_img_path">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>.....</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>17</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item row="2" column="0">
      <widget class="QTableView" name="list_image"/>
     </item>
     <item row="3" column="0">
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QLabel" name="title_3">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>备    注：</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="qline_image_notes">
         <property name="styleSheet">
          <string notr="true">QLineEdit {
    border: 1px solid rgb(41, 57, 85); 
    border-radius: 3px;  
    background: white;   
    selection-background-color: green; 
    font-size: 16px
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="checkbox_auto_serial_num">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QCheckBox::indicator {
    width: 14px;
    height: 14px;
    border: 1px solid blue;
    border-radius: 4px;
}
QCheckBox::indicator:checked {
    background-color: blue;
    border: 1px solid blue;
}
QCheckBox::indicator:unchecked {
    background-color: white;
}</string>
         </property>
         <property name="text">
          <string>自动流水号</string>
         </property>
         <property name="checked">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="4" column="0">
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_confirm">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string>调试当前图片</string>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string>保存</string>
         </property>
         <property name="icon">
          <iconset>
           <normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>28</width>
           <height>28</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_open">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string>调试当前图片</string>
         </property>
         <property name="text">
          <string>打开</string>
         </property>
         <property name="icon">
          <iconset>
           <normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>28</width>
           <height>28</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushbutton_cancel">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string>调试当前图片</string>
         </property>
         <property name="text">
          <string>取消</string>
         </property>
         <property name="icon">
          <iconset>
           <normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</normaloff>../../../../../../DBWB/code/GIT/code/VisionEngine/ui/Resources/icon/downtest2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>28</width>
           <height>28</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
