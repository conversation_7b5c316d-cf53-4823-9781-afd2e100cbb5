
#ifndef CUSTOMGRAPHICS_CONTROL_POINT_H
#define CUSTOMGRAPHICS_CONTROL_POINT_H

#include "controlpointabstract.h"

class ControlPointSGTerminal : public ControlPointAbstract
{
public:
    ControlPointSGTerminal() {}
    ~ControlPointSGTerminal() {}

    void Response(const ResponseEventParam& param, GraphicsAbstract* const obj) override;
    // double TryResponse(const float& x, const float& y, const float& min_dis) const override;
};

#endif //! CUSTOMGRAPHICS_CONTROL_POINT_H