#include "regionlisttabwidget.h"
#include "texthorizontalalignedtabbar.h"
#include "regionlistwidget.h"
#include "algoselectwidget.h"


DetectModelWidget::DetectModelWidget(QWidget* parent)
    :QTabWidget(parent),
    m_custom_tabbar(new CustomTabBar(this))
{
    // 设置自定义TabBar
    setTabBar(m_custom_tabbar);

    this->setTabPosition(QTabWidget::North);

    InitConnect();
}

void DetectModelWidget::SlotOnCurrentChanged(int index)
{
    auto current_tab_widget = this->widget(index);
    auto list = dynamic_cast<DetectWindowListWidget*>(current_tab_widget);

    if (list == nullptr)
    {
        return;
    }
    if (list->count() > 0)
    {
        list->setCurrentRow(0);
    }
    auto detect_model_name = list->property("TYPE").toString().toStdString();
    emit SigDetectModelChange(QString::fromStdString(detect_model_name));
}


std::string DetectModelWidget::ModelNameBindCustomStr(const QString& detect_model_name)
{

    const auto& detect_model_name_str = detect_model_name.toStdString();
    std::string custom_name = "";
    if (detect_model_name_str.find("location") != std::string::npos)
    {
        custom_name = "元件";
    }
    else if (detect_model_name_str.find("body") != std::string::npos)
    {
        custom_name = "本体";
    }
    else if (detect_model_name_str.find("pad_group_id") != std::string::npos)
    {
        auto id = jrscore::AOITools::GetSuffixNumber(detect_model_name_str);
        custom_name = "焊盘" + std::to_string(id);

    }
    else
    {
        Log_ERROR("映射失败，请检查model_name：", detect_model_name_str, "是否正确");
        return custom_name;
    }
    _model_name_mapping_custom_name[detect_model_name_str] = custom_name;
    return custom_name;
}

void DetectModelWidget::InitConnect()
{
    connect(this, &QTabWidget::currentChanged, this, &DetectModelWidget::SlotOnCurrentChanged, Qt::DirectConnection);

    connect(this, &QTabWidget::tabBarClicked, this, [=](int index) {
        auto current_tab_widget = this->widget(index);
        auto list = dynamic_cast<DetectWindowListWidget*>(current_tab_widget);
        if (list == nullptr)
        {
            return;

            ///<如果为空则发送model变化到render
        }


        DetectWindowItemValue det_win_info;
        det_win_info.model_name = list->GetModelName();
        list->clearSelection();
        emit SignalModelItemSelectedChanged(det_win_info);

        //如果有选择 则取消选择，发送model_name 到render;


        }, Qt::DirectConnection);
}


DetectWindowListWidget* DetectModelWidget::CreateModel(const QString& model_name, const QString& type_name)
{
    for (int i = 0; i < this->count(); ++i)
    {
        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
        if (list == nullptr)
        {
            continue;
        }

        if (list->GetModelName() == model_name)
        {
            return list;
        }
    }


    auto list = new DetectWindowListWidget(model_name, type_name);
    auto std_model_name = model_name.toStdString();
    if (algo_list.find(std_model_name) != algo_list.end())
    {
        list->SetAlgoList(algo_list.find(std_model_name)->second);
    }
    list->SetDefectTypeList(defect_list);

    auto custom_name = ModelNameBindCustomStr(model_name);
    this->addTab(list, QString::fromStdString(custom_name));

    connect(list, &DetectWindowListWidget::SignalDetectWinValueChanged, this, [this, list](DetectWindowItemValue& det_win_info)
        {
            det_win_info.model_name = list->GetModelName();
            emit SignalModelItemValChanged(det_win_info);
        });

    connect(list, &DetectWindowListWidget::SignalSelectedDetectWinChanged, this, [this](DetectWindowItemValue& det_val)
        {
            for (int i = 0; i < count(); ++i)
            {
                DetectWindowListWidget* list = static_cast<DetectWindowListWidget*>(widget(i));
                if (list->GetModelName() != det_val.model_name)
                {
                    list->clearSelection();
                }
            }
            emit SignalModelItemSelectedChanged(det_val);
        });

    return list;
}

void DetectModelWidget::DeleteModel(const QString& model_name)
{
    auto custom_name = _model_name_mapping_custom_name[model_name.toStdString()];
    for (int i = 0; i < this->count(); ++i)
    {
        if (this->tabText(i) == QString::fromStdString(custom_name))
        {
            if (this->widget(i))
            {
                this->removeTab(i);
            }
            break;
        }
    }
}

void DetectModelWidget::UpdateView(const std::string& model_name, const QString& type_name, const DetectWindowItemValue& det_win_val)
{
    auto list = CreateModel(QString::fromStdString(model_name), type_name);
    list->CreateDetectWindowItem(det_win_val);
}

void DetectModelWidget::MoveTabToFirst(const std::string& tab_name)
{
    auto custom_name = _model_name_mapping_custom_name[tab_name];
    for (int i = 0; i < this->count(); ++i)
    {
        if (this->tabText(i) == QString::fromStdString(custom_name))
        {
            QWidget* widget = this->widget(i);
            QIcon icon = this->tabIcon(i);
            QString text = this->tabText(i);
            this->blockSignals(true);
            this->removeTab(i); // 移除原来的 tab
            this->insertTab(0, widget, icon, text); // 插入到第一个位置
            this->blockSignals(false);
            //this->blockSignals(true);
            //this->setCurrentIndex(0); // 选中新 tab
            //this->blockSignals(false);
            return;
        }
    }
}

void DetectModelWidget::SortTabsByCustomOrder()
{
    struct TabInfo {
        QWidget* widget;
        QIcon icon;
        QString text;
    };

    std::map<QString, TabInfo> fixed_tabs;  // 保存“元件”“本体”
    std::map<int, TabInfo> pad_tabs;        // 保存“焊盘N”
    std::vector<TabInfo> others;            // 其他未识别项

    // 收集所有 tab 信息
    for (int i = 0; i < this->count(); ++i)
    {
        QString text = this->tabText(i);
        QWidget* widget = this->widget(i);
        QIcon icon = this->tabIcon(i);

        if (text == "元件")
            fixed_tabs["元件"] = { widget, icon, text };
        else if (text == "本体")
            fixed_tabs["本体"] = { widget, icon, text };
        else if (text.startsWith("焊盘"))
        {
            bool ok = false;
            int number = text.mid(2).toInt(&ok);
            if (ok)
                pad_tabs[number] = { widget, icon, text };
            else
                others.push_back({ widget, icon, text });
        }
        else
        {
            others.push_back({ widget, icon, text });
        }
    }

    // 禁止信号，避免触发槽函数
    this->blockSignals(true);
    this->clear(); // 清空所有 tab

    // 按顺序重新插入 tab
    if (fixed_tabs.count("元件")) {
        const auto& tab = fixed_tabs["元件"];
        this->addTab(tab.widget, tab.icon, tab.text);
    }

    if (fixed_tabs.count("本体")) {
        const auto& tab = fixed_tabs["本体"];
        this->addTab(tab.widget, tab.icon, tab.text);
    }

    for (const auto& [num, tab] : pad_tabs) {
        this->addTab(tab.widget, tab.icon, tab.text);
    }

    for (const auto& tab : others) {
        this->addTab(tab.widget, tab.icon, tab.text);
    }

    this->blockSignals(false);
}

void DetectModelWidget::SetCurSelectedDetectWinItemByWinName(const std::string& det_win_name, const std::string& model_name)
{
    std::vector<DetectWindowListWidget*> lists;
    if (model_name != "")
    {
        DetectWindowListWidget* list;
        for (int i = 0; i < this->count(); ++i)
        {
            list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
            if (list == nullptr || list->GetModelName().toLocal8Bit().toStdString() != model_name)
            {
                list = nullptr;
                continue;
            }
            lists.push_back(list);
        }
    }

    if (lists.size() != 1)
    {
        return;
    }

    auto& cur_list = lists[0];
    this->setCurrentWidget(cur_list);

    if (cur_list->GetCurSelectName() != det_win_name)
    {
        cur_list->clearSelection();
        for (int i = 0; i < cur_list->count(); ++i)
        {
            auto item = cur_list->item(i);
            auto item_data = item->data(Qt::UserRole).toString().toLocal8Bit().toStdString();
            if (item_data == det_win_name)
            {
                cur_list->setItemSelected(item, true);
                return;
            }
        }
    }
}

void DetectModelWidget::SetDetectWinItemVal(const DetectWindowItemValue& det_win_val, const std::string& model_name)
{
    QListWidgetItem* item = nullptr;
    DetectWindowListWidget* list = nullptr;
    FindItemByWinName(det_win_val.window_name.toLocal8Bit().toStdString(), item, list, model_name);
    if (item && list)
    {
        list->SetDetectWindowItemValue(det_win_val, item);
    }
}

void DetectModelWidget::SetCurSelectedTable(const QString& table_name)
{
    for (int i = 0; i < this->count(); ++i)
    {
        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
        if (list == nullptr)
        {
            continue;
        }
        if (list->GetModelName() == table_name)
        {
            this->setCurrentWidget(list);
            //! tab没有切换的时候，也需要设置当前选中的算法，这样才能给外界通知当前的算法
            //! 主要用于同料号元件之间的切换不通知的情况
            auto cur_selected_algo_name = QString::fromStdString(list->GetCurSelectName());
            list->SelectDetectWin(cur_selected_algo_name);
            break;
        }
    }
}

void DetectModelWidget::AddDetectWinItem(const DetectWindowItemValue& det_win_val)
{
    auto list = ReadModel(det_win_val.model_name);
    if (list == nullptr)
    {
        return;
    }
    list->CreateDetectWindowItem(det_win_val, true);
}

void DetectModelWidget::SetAlgoList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_list)
{
    algo_list = _algo_list;

    std::vector<DetectWindowListWidget*> list_widgets;
    for (int i = 0; i < this->count(); ++i)
    {
        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
        if (list == nullptr)
        {
            continue;
        }
        list->clear();
        list_widgets.push_back(list);
    }
}

void DetectModelWidget::SetDefectList(const std::vector<std::string>& _defect_list)
{
    defect_list = _defect_list;
}

void DetectModelWidget::GetModelTableNames(std::vector<std::string>& table_names)
{
    for (int i = 0; i < this->count(); ++i)
    {
        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
        if (list == nullptr)
        {
            continue;
        }
        table_names.push_back(list->GetModelName().toLocal8Bit().toStdString());
    }
}

void DetectModelWidget::SetFirstDetectWinSeleceted(const std::string& model_name_)
{

    for (int i = 0; i < this->count(); ++i)
    {

        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
        if (list == nullptr)
        {
            continue;
        }

        if (list->GetModelName().toStdString() == model_name_)
        {
            this->setCurrentIndex(i);
            list->setCurrentItem(list->item(0));
            break;
        }
    }
}

void DetectModelWidget::FindItemByWinName(const std::string& win_name, QListWidgetItem*& item, DetectWindowListWidget*& list, const std::string& model_name)
{
    std::vector<DetectWindowListWidget*> lists;
    GetDetectWindowListWidgets(model_name, lists);

    for (auto _list : lists)
    {
        int count = _list->count();
        for (int i = 0; i < count; ++i)
        {
            auto _item = _list->item(i);
            auto item_data = _item->data(Qt::UserRole).toString().toLocal8Bit().toStdString();
            if (item_data == win_name)
            {
                list = _list;
                item = _item;
                return;
            }
        }
    }
}

DetectWindowListWidget* DetectModelWidget::ReadModel(const QString& model_name)
{
    auto custom_name = _model_name_mapping_custom_name[model_name.toStdString()];

    for (int i = 0; i < this->count(); ++i)
    {
        if (this->tabText(i) == QString::fromStdString(custom_name))
        {
            auto widget = this->widget(i);
            if (widget)
            {
                auto list = dynamic_cast<DetectWindowListWidget*>(widget);
                return list;
            }
        }
    }
    return nullptr;
}

DetectWindowListWidget* DetectModelWidget::GetCurrentModel()
{
    auto widget = this->currentWidget();
    if (widget)
    {
        auto list = dynamic_cast<DetectWindowListWidget*>(widget);
        return list;
    }
    return nullptr;
}

void DetectModelWidget::Clear(bool is_block_signal)
{
    if (is_block_signal)
    {
        disconnect(this, &QTabWidget::currentChanged, this, &DetectModelWidget::SlotOnCurrentChanged);
        this->clear();
        connect(this, &QTabWidget::currentChanged, this, &DetectModelWidget::SlotOnCurrentChanged);
    }
    else
    {
        this->clear();
    }
    m_custom_tabbar->ClearColors();
    _model_name_mapping_custom_name.clear();
}

void DetectModelWidget::UpdateTabColorByAlgoResult(const std::string& model_name)
{
    std::vector<DetectWindowListWidget*> lists;
    GetDetectWindowListWidgets(model_name, lists);

    int ok_count = 0;
    int ng_count = 0;
    int untest_count = 0;

    for (auto _list : lists)
    {
        std::vector<DetectWindowItemValue> value = _list->GetAllDetectWindowItemValue();
        for (int i = 0; i < value.size(); ++i)
        {
            if (value[i].excute_state == -1)
            {
                ng_count++;
            }
            else if (value[i].excute_state == 0)
            {
                untest_count++;
            }
            else if (value[i].excute_state == 1)
            {
                ok_count++;
            }
        }
    }

    // 根据结果修改tab颜色
    if (ng_count > 0)
    {
        SetTabBackgroundByName(QString::fromStdString(_model_name_mapping_custom_name[model_name]), "#FF0000");
    }
    else if (ok_count > 0)
    {
        SetTabBackgroundByName(QString::fromStdString(_model_name_mapping_custom_name[model_name]), "#00FF00");
    }
    else
    {
        SetTabBackgroundByName(QString::fromStdString(_model_name_mapping_custom_name[model_name]), "#FFFFFF");
    }
}


void DetectModelWidget::SetTabBackgroundByName(const QString& tab_name, const QVariant& color)
{
    // 查找对应名称的索引
    int index = -1;
    for (int i = 0; i < this->count(); ++i)
    {
        if (this->tabText(i) == tab_name)
        {
            index = i;
            break;
        }
    }

    if (index == -1)
    {
        return;
    }

    SetTabBackgroundByIndex(index, color);
}

void DetectModelWidget::SetTabBackgroundByIndex(int index, const QVariant& color)
{
    if (index < 0 || index >= m_custom_tabbar->count()) 
    {
        return;
    }

    if (color.isValid() && !color.isNull()) 
    {
        QColor bgColor;
        if (color.canConvert<QColor>()) {
            bgColor = color.value<QColor>();
        }
        else {
            bgColor = QColor(color.toString());
        }

        if (bgColor.isValid()) 
        {
            m_custom_tabbar->SetTabBackgroundColor(index, bgColor);
        }
    }
    else 
    {
        m_custom_tabbar->ClearTabBackground(index);
    }
}

void DetectModelWidget::GetDetectWindowListWidgets(const std::string& model_name, std::vector<DetectWindowListWidget*>& lists)
{
    if (model_name != "")
    {
        DetectWindowListWidget* _list;
        for (int i = 0; i < this->count(); ++i)
        {
            _list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
            if (_list == nullptr || _list->GetModelName().toLocal8Bit().toStdString() != model_name)
            {
                _list = nullptr;
                continue;
            }
            lists.push_back(_list);
        }
    }
    else
    {
        for (int i = 0; i < this->count(); ++i)
        {
            auto _list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
            if (_list == nullptr)
            {
                continue;
            }
            lists.push_back(_list);
        }
    }
}

void DetectModelWidget::resizeEvent(QResizeEvent* event)
{
    QTabWidget::resizeEvent(event);
    // 确保TabBar正确填充
    m_custom_tabbar->setGeometry(tabBar()->geometry());
}


//void DetectModelWidget::ClearAllTabSelected()
//{
//    for (int i = 0; i < this->count(); ++i)
//    {
//        auto list = dynamic_cast<DetectWindowListWidget*>(this->widget(i));
//        if (list == nullptr)
//        {
//            continue;
//        }
//        if (list->selectedItems().size() > 0)
//        {
//            list->blockSignals(true);
//            list->clearSelection();
//            list->blockSignals(false);
//        }
//    }
//}
