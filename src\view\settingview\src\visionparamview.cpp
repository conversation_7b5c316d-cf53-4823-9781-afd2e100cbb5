﻿#include "visionparamview.h"

jrsaoi::VisionParamView::VisionParamView(QWidget* parent)
	:QWidget(parent)
{
}

jrsaoi::VisionParamView::~VisionParamView()
{
}

void jrsaoi::VisionParamView::UpdateView(const jrsdata::VisionParamSettingVec& vision_params_)
{
	if (vision_params_.empty())
	{

	}
}

jrsdata::VisionParamSettingVec jrsaoi::VisionParamView::GetVisionParam()
{
	return jrsdata::VisionParamSettingVec();
}

void jrsaoi::VisionParamView::InitMember()
{
}

void jrsaoi::VisionParamView::InitConnect()
{
}

void jrsaoi::VisionParamView::SaveVisionParam()
{
}

void jrsaoi::VisionParamView::UpdateStaticParam()
{
}

void jrsaoi::VisionParamView::SetHideController(const jrsdata::VisionParamSettingVec& vision_params_)
{
	if (vision_params_.empty())
	{

	}
}
void jrsaoi::VisionParamView::SlotSaveVisionParam()
{

}