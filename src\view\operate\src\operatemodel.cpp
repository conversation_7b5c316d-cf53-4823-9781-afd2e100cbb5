﻿//Custom
#include "operatemodel.h"
#include "projectparam.hpp"
#include "viewparam.hpp"
#include "layerconverter.hpp"
#include "imageprocessalgo.h"
#include "algoexecuteparam.hpp"
#include "generalimagetool.h"
//#include "queryparam.hpp"
#include "settingparam.h"
#include "algoexecuteparamprocess.h"
#include "cvtools.h"
#include "jsonoperator.hpp"
#include "subboardsortmanager.h"
//Third
#include "iguana/iguana.hpp"
#include "operatorparambase.h"
namespace jrsaoi
{
    OperateModel::OperateModel(const std::string& name) :ModelBase(name)
        , is_waitting_debug_info(true)
        , _subboard_sort_manager_ptr(std::make_shared<subboardsort::SubboardSortManager>())
    {
    }
    OperateModel::~OperateModel()
    {
    }
    int OperateModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    const jrsdata::ProjectParamPtr& OperateModel::GetProjectParam()
    {
        return project_param_instance.GetProjectDataProcessInstance()->GetProjectParam();
    }


    int OperateModel::SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list)
    {
        algo_name_list = _algo_name_list;
        defect_list = _defect_list;
        return 0;
    }

    int OperateModel::SetAlgoritmDefaultParam(const std::map<std::string, std::string>& _algo_default_param)
    {
        algo_default_param = _algo_default_param;
        return 0;
    }

    int OperateModel::MultiAlgoAdd(jrsdata::ComponentEntity& entity, const jrsdata::MultiAlgoParam& multi_algo_param)
    {
        // 先删除之前的再添加
        project_param_instance.GetProjectDataProcessInstance()->DeleteAllDetectWindows(entity);
        project_param_instance.GetProjectDataProcessInstance()->CreateMultiDetectWindows(entity, multi_algo_param, algo_default_param);
        return 0;
    }

    void OperateModel::UpdateSelectedDetectWin(const std::string& win_name)
    {
        if (cur_selected_component == nullptr || cur_selected_component->component_part_number.empty())
        {
            return;
        }
        try
        {
            auto det_win = project_param_instance.GetProjectDataProcessInstance()->ReadDetectWindowRef(cur_selected_component->component_part_number, win_name);
            if (det_win.has_value())
            {
                cur_selected_detect_win = &det_win->get();
                cur_selected_detect_win->model_name;
                for (auto& unit : cur_selected_spec_region->units)
                {
                    if (cur_selected_detect_win->model_name == unit.unit_group_name)
                    {
                        if (cur_selected_comp_unit == nullptr || cur_selected_comp_unit->unit_group_name != unit.unit_group_name)
                        {
                            cur_selected_comp_unit = &unit;
                            cur_select_model_name = cur_selected_comp_unit->unit_group_name;
                            break;
                        }
                    }
                }
            }
            else
            {
                cur_selected_detect_win = nullptr;
            }
        }
        catch (const std::exception&)
        {

        }
    }

    void OperateModel::UpdateSelectedDetectWin(const std::vector<std::string>& win_name_list)
    {
        if (win_name_list.empty())
        {
            cur_selected_detect_win = nullptr;
            return;
        }
        if (cur_selected_component == nullptr)
        {
            return;
        }

        for (const auto& win_name : win_name_list)
        {
            auto det_win = project_param_instance.GetProjectDataProcessInstance()->ReadDetectWindowRef(cur_selected_component->component_part_number, win_name);
            if (det_win.has_value())
            {
                cur_selected_detect_win = &det_win->get();
            }
            else
            {
                cur_selected_detect_win = nullptr;
            }
        }
    }

    void OperateModel::UpdateSelectedDetectWin(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        if (!cur_selected_comp_unit)
        {
            Log_WARN("当前选择的unit 为空");
            return;
        }
        auto unit_name = param_->graphics_and_select_units.second.unit_name;
        if (cur_selected_comp_unit->unit_name != param_->graphics_and_select_units.second.unit_name)
        {
            auto component_unit = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnitRef(cur_selected_component->component_part_number, unit_name);
            if (component_unit.has_value())
            {
                cur_selected_comp_unit = &component_unit.value().get();
                cur_select_model_name = cur_selected_comp_unit->unit_group_name;
            }
        }
        UpdateSelectedDetectWin({ param_->graphics_and_select_units.second.current_name });
    }

    void OperateModel::UpdateSelectedComponent(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_,
        bool& component_changed, bool& part_number_changed)
    {
        if (!param_)
        {
            return;
        }
        jrsdata::Component* component_ptr = nullptr;
        auto componet_type = param_->graphics_and_select_units.second.type_and_component_name.first;
        auto component_name = param_->graphics_and_select_units.second.type_and_component_name.second;
        std::optional<std::reference_wrapper<jrsdata::Component>> component_info;
        if (componet_type == jrsdata::Component::Type::CAD)
        {
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadCADRef(component_name);
        }
        else if (componet_type == jrsdata::Component::Type::MARK)
        {
            const std::string mark_name = component_name;
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadMarkRef(mark_name);
        }
        else if (componet_type == jrsdata::Component::Type::SUB_MARK)
        {
            const std::string sub_mark_name = component_name;
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadSubMarkRef(sub_mark_name);
        }
        else if (componet_type == jrsdata::Component::Type::SUB_BARCODE)
        {
            const std::string subboard_barcode_name = component_name;
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadSubBoardBarcodeRef(subboard_barcode_name);
        }
        else if (componet_type == jrsdata::Component::Type::SUB_BADMARK)
        {
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadSubBadMarkRef(component_name);
        }
        else if (componet_type == jrsdata::Component::Type::BARCODE)
        {
            component_info = project_param_instance.GetProjectDataProcessInstance()->ReadBarcodeRef(component_name);
        }
        if (component_info.has_value())
        {
            component_ptr = &component_info->get();
        }
        else
        {
            component_ptr = nullptr;
        }

        if (component_ptr != cur_selected_component)
        {
            component_changed = true;
            if (component_ptr == nullptr)
            {
                part_number_changed = true;
                cur_selected_spec_region = nullptr;
                cur_selected_detect_win = nullptr;
            }
            else if (cur_selected_component == nullptr)
            {
                part_number_changed = true;
                UpdateSelectedPNDetectInfo(component_ptr->component_part_number);
            }
            else if (component_ptr->component_part_number != cur_selected_component->component_part_number)
            {
                part_number_changed = true;
                UpdateSelectedPNDetectInfo(component_ptr->component_part_number);
            }
            cur_selected_component = component_ptr;
        }

        if (cur_selected_component != nullptr)
        {


            auto unit_info = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnitRef(cur_selected_component->component_part_number, param_->graphics_and_select_units.second.unit_name);
            if (unit_info.has_value())
            {
                cur_selected_comp_unit = &unit_info->get();
            }
            else
            {
                cur_selected_comp_unit = nullptr;
            }


        }
        else
        {
            cur_selected_comp_unit = nullptr;
        }

    }

    void OperateModel::UpdateSelectedPad(const jrsdata::GraphicsUpdateProjectEventParamPtr& param)
    {
        //!< 2025.02.24 wangzhengkai 未选中元件时，将当前选中元组置为空
        if (!param || cur_selected_component == nullptr)
        {
            cur_selected_comp_unit = nullptr;
            return;
        }

        auto pad_info = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnitRef(cur_selected_component->component_part_number, param->graphics_and_select_units.second.current_name);
        if (pad_info.has_value())
        {
            cur_selected_comp_unit = &pad_info->get();
            cur_select_model_name = cur_selected_comp_unit->unit_group_name;
        }
        else
        {
            cur_selected_comp_unit = nullptr;
        }
    }

    void OperateModel::UpdateSelectedPNDetectInfo(const std::string& part_numb_name)
    {
        auto spec_region = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(part_numb_name);
        if (spec_region.has_value())
        {
            //!< 料号切换时将当前选中检测框赋值为空 2025.02.20 wangzhengkai
            if (cur_selected_spec_region != &spec_region->get())
            {
                cur_selected_spec_region = &spec_region->get();
                cur_selected_detect_win = nullptr;
            }
        }
        else
        {
            cur_selected_spec_region = nullptr;
            cur_selected_detect_win = nullptr;
        }
    }

    bool OperateModel::AlgoParamChangedCallbackFunc(jrsoperator::OperatorParamBasePtr param_)
    {
        if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
        {
            return false;
        }

        auto algo = &cur_selected_detect_win->algorithms[0];

        auto operator_ptr = std::dynamic_pointer_cast<iguana::base>(param_);
        std::string algo_param_str;
        operator_ptr->to_json(algo_param_str);
        algo->param = algo_param_str;
        return true;
    }

    int OperateModel::UpdateDetectWindowParam(const jrsdata::ViewParamBasePtr& param_)
    {
        bool is_update_render_2d = false;
        auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
        if (!param)
        {
            return -1;
        }

        switch (param->data_operate_type)
        {
        case jrsdata::DataUpdateType::UPDATE_DATA:
        {
            if (!cur_selected_component || !cur_selected_detect_win || !cur_selected_comp_unit)
            {
                return -1;
            }
            std::vector<std::pair<int, cv::RotatedRect>> detect_rects;
            const auto& run_mode_info = GetExecuteModeInfo();
            project_param_instance.GetProjectDataProcessInstance()->CropDetectWinImageFromProject(cur_selected_light_type, *cur_selected_component, param->detect_win.name, *cur_selected_comp_unit, param->region_mat, detect_rects, run_mode_info);
            project_param_instance.GetProjectDataProcessInstance()->GetTemplatesByDetectWinName(cur_selected_component->component_part_number, cur_selected_detect_win->name, param->templates);
            cur_selected_detect_win->model_name = param->detect_win.model_name;
            cur_selected_detect_win->name = param->detect_win.name;
            cur_selected_detect_win->parent_win_name = param->detect_win.parent_win_name;
            cur_selected_detect_win->defect_name = param->detect_win.defect_name;
            cur_selected_detect_win->enable = param->detect_win.enable;
            cur_selected_detect_win->group_name = param->detect_win.group_name;
            if (cur_selected_detect_win->search_size != param->detect_win.search_size)
            {
                is_update_render_2d = true;
                cur_selected_detect_win->search_size = param->detect_win.search_size;
            }
            param->algorithm_param = cur_selected_detect_win->algorithms[0];
            param->cur_select_detect_win = cur_selected_detect_win;
            param->cur_select_component = cur_selected_component;
            param->cur_select_component_unit = cur_selected_comp_unit;
            param->light_type = cur_selected_light_type;
            param->detect_rects = detect_rects;
            break;
        }
        case jrsdata::DataUpdateType::DELETE_DATA:
        {
            if (cur_selected_detect_win != nullptr)
            {
                cur_selected_detect_win->name = param->detect_win.name;
                cur_selected_detect_win = nullptr;
            }
            break;
        }
        case jrsdata::DataUpdateType::SELECT_DATA:
        {
            cur_select_model_name = param->detect_win.model_name;
            if (!cur_selected_component || !cur_selected_comp_unit)
            {
                return -1;
            }
            UpdateSelectedDetectWin(param->detect_win.name);
            param->cur_select_detect_win = cur_selected_detect_win;
            param->cur_select_component = cur_selected_component;
            std::vector<std::pair<int, cv::RotatedRect>> detect_rects;
            const auto& run_mode_info = GetExecuteModeInfo();

            project_param_instance.GetProjectDataProcessInstance()->CropDetectWinImageFromProject(cur_selected_light_type, *cur_selected_component, param->detect_win.name, *cur_selected_comp_unit, param->region_mat, detect_rects, run_mode_info);

            if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
            {
                return -1;
            }
            auto algo = &cur_selected_detect_win->algorithms[0];
            if (algo == nullptr)
            {
                return -1;
            }
            param->algorithm_param = *algo;
            param->detect_win.width = cur_selected_detect_win->width;
            param->detect_win.height = cur_selected_detect_win->height;
            param->detect_win.search_size = cur_selected_detect_win->search_size;
            param->detect_rects = detect_rects;
            param->light_type = cur_selected_light_type;

            project_param_instance.GetProjectDataProcessInstance()->GetTemplatesByDetectWinName(cur_selected_component->component_part_number, cur_selected_detect_win->name, param->templates);
            break;
        }

        default:
        {
            break;
        }
        }
        if (is_update_render_2d)
        {
            auto param_temp = std::make_shared<jrsdata::ViewParamBase>();
            param_temp->event_name = jrsaoi::REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME;
            SigUpdateOperator(param_temp);
        }
        return 0;
    }
    int OperateModel::UpdateSelectedDetectWinIPEParam(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
        if (!param)
        {
            return -1;
        }

        if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
        {
            return -1;
        }
        cur_selected_detect_win->algorithms[0].color_param = param->algorithm_param.color_param;
        return 0;
    }
    int OperateModel::UpdateTemplateParam(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
        if (!param)
        {
            return -1;
        }

        switch (param->data_operate_type)
        {
        case jrsdata::DataUpdateType::ADD_DATA:
        {
            jrsdata::Template algo_template;
            if (AddTemplateToCurSelectedDetectWin(param->region_mat, param->template_color_param, (int)param->light_type, algo_template) == 0)
            {
                param->templates.push_back(algo_template);
            }
            break;
        }
        case jrsdata::DataUpdateType::DELETE_DATA:
        {
            DelTemplateFromCurSelectedDetectWin(param->template_id);
            break;
        }
        case jrsdata::DataUpdateType::UPDATE_DATA:
        {
            if (param->templates.size() == 0)
            {
                return -1;
            }
            project_param_instance.GetProjectDataProcessInstance()->UpdateTemplate(param->templates[0]);
            break;
        }
        default:
        {
            break;
        }
        }
        param_->event_name = jrsaoi::OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME;
        return 0;
    }

    int OperateModel::RenderViewDetectWinUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        const auto& det_win_name = param_->graphics_and_select_units.second.current_name;
        auto param_algoview = std::make_shared<jrsdata::AlgoEventParam>();
        param_algoview->module_name = jrsaoi::OPERATE_MODULE_NAME;
        param_algoview->event_name = jrsaoi::OPERATE_UPDATE_DET_WIN_VIEW_EVENT_NAME;
        UpdateSelectedDetectWin(param_);
        SetCurSelectedToAlgoEventParam(param_algoview);

        if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME)
        {
            if (cur_selected_detect_win == nullptr || cur_selected_component == nullptr)
            {
                return -1;
            }

            SetDetectWindowDefaultParam(*cur_selected_detect_win);
            std::vector<std::pair<int, cv::RotatedRect>> detect_rects;
            const auto& run_mode_info = GetExecuteModeInfo();

            project_param_instance.GetProjectDataProcessInstance()->CropDetectWinImageFromProject(cur_selected_light_type, *cur_selected_component, cur_selected_detect_win->name, *cur_selected_comp_unit, param_algoview->region_mat, detect_rects, run_mode_info);
            param_algoview->data_operate_type = jrsdata::DataUpdateType::ADD_DATA;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME)
        {
            std::vector<std::string> detect_win_list = { det_win_name };
            param_algoview->delete_detect_win_name_list = detect_win_list;
            param_algoview->data_operate_type = jrsdata::DataUpdateType::DELETE_DATA;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME)
        {
            if (cur_selected_detect_win == nullptr || cur_selected_component == nullptr)
            {
                return -1;
            }
            param_algoview->data_operate_type = jrsdata::DataUpdateType::SELECT_DATA;
            param_algoview->event_name = RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME)
        {
            if (cur_selected_detect_win == nullptr || cur_selected_component == nullptr)
            {
                return -1;
            }
            const auto& run_mode_info = GetExecuteModeInfo();

            project_param_instance.GetProjectDataProcessInstance()->CropDetectWinImageFromProject(cur_selected_light_type, *cur_selected_component, cur_selected_detect_win->name, *cur_selected_comp_unit, param_algoview->region_mat, param_algoview->detect_rects, run_mode_info);
            project_param_instance.GetProjectDataProcessInstance()->GetTemplatesByDetectWinName(cur_selected_component->component_part_number, cur_selected_detect_win->name, param_algoview->templates);
            param_algoview->data_operate_type = jrsdata::DataUpdateType::UPDATE_DATA;
        }
        SigUpdateOperator(param_algoview);
        return 0;
    }
    int OperateModel::RenderViewComponentUnitUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        bool is_sel_compoent_changed = false;
        bool is_sel_part_numb_changed = false;

        UpdateSelectedComponent(param_, is_sel_compoent_changed, is_sel_part_numb_changed);
        if (is_sel_part_numb_changed || is_sel_compoent_changed || param_->event_name == jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME)
        {
            auto param_algo_event = std::make_shared<jrsdata::AlgoEventParam>();
            param_algo_event->module_name = jrsaoi::OPERATE_MODULE_NAME;
            param_algo_event->event_name = jrsaoi::OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME;
            SetCurSelectedToAlgoEventParam(param_algo_event);
            SigUpdateOperator(param_algo_event);
        }

        if (param_->graphics_and_select_units.first == "pad" && param_->event_name != jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME)
        {
            RenderViewPadUpdate(param_);
        }

        return 0;
    }
    int OperateModel::RenderViewPadUpdate(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        std::vector<std::string> pad_list = { param_->graphics_and_select_units.second.current_name };
        auto param_algo_event = std::make_shared<jrsdata::AlgoEventParam>();
        param_algo_event->module_name = jrsaoi::OPERATE_MODULE_NAME;
        param_algo_event->event_name = jrsaoi::OPERATE_UPDATE_PAD_WIN_PARAM_EVENT_NAME;


        UpdateSelectedPad(param_);
        SetCurSelectedToAlgoEventParam(param_algo_event);

        //!< 未选中元件时不更新界面 2025.02.24 wangzhengkai
        if (cur_selected_component == nullptr)
        {
            JRSMessageBox_WARN("警告", "当前未选中元件", jrscore::MessageButton::Ok);
            return -1;
        }

        if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME)
        {
            param_algo_event->data_operate_type = jrsdata::DataUpdateType::ADD_DATA;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME)
        {
            param_algo_event->data_operate_type = jrsdata::DataUpdateType::DELETE_DATA;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME)
        {
            param_algo_event->data_operate_type = jrsdata::DataUpdateType::SELECT_DATA;
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME)
        {
            param_algo_event->data_operate_type = jrsdata::DataUpdateType::UPDATE_DATA;
        }
        SigUpdateOperator(param_algo_event);
        return 0;
    }

    int OperateModel::SetDetectWindowDefaultParam(jrsdata::DetectWindow& detect_win)
    {
        if (cur_selected_comp_unit == nullptr)
        {
            return -1;
        }
        detect_win.enable = true;
        detect_win.group_name = "None";
        detect_win.algorithms[0].light_image_id = (int)cur_selected_light_type;
        ColorParams color_param;
        detect_win.algorithms[0].color_param = color_param.ToJson();
        detect_win.algorithms[0].param = algo_default_param[detect_win.algorithms[0].detect_algorithm_name];
        return 0;
    }
    //int OperateModel::AddTemplateToCurSelectedDetectWin(jrsdata::Template& template_)
    //{
    //    project_param_instance.GetProjectDataProcessInstance()->CreateTemplate(template_);

    //    if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
    //    {
    //        return -1;
    //    }
    //    auto algo = &cur_selected_detect_win->algorithms[0];

    //    if (algo == nullptr)
    //    {
    //        return -1;
    //    }

    //    if (algo->detect_algorithm_name == "PositionOperator")
    //    {
    //        cur_selected_detect_win->level = 1;
    //    }
    //    else
    //    {
    //        cur_selected_detect_win->level = 2;
    //    }
    //    algo->template_image_ids.push_back(template_.id);

    //    return 0;
    //}

    int OperateModel::AddTemplateToCurSelectedDetectWin(const cv::Mat& template_img, const std::string& color_param, const int light_img_id, jrsdata::Template& output_template)
    {
        if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
        {
            return -1;
        }

        auto algo = &cur_selected_detect_win->algorithms[0];

        if (algo == nullptr)
        {
            return -1;
        }

        project_param_instance.GetProjectDataProcessInstance()->CreateTemplate(template_img, color_param, light_img_id, output_template);

        if (algo->detect_algorithm_name == "PositionOperator")
        {
            cur_selected_detect_win->level = 1;
        }
        else
        {
            cur_selected_detect_win->level = 2;
        }
        algo->template_image_ids.push_back(output_template.id);

        return 0;
    }

    int OperateModel::DelTemplateFromCurSelectedDetectWin(const int& template_id)
    {
        if (cur_selected_detect_win == nullptr || cur_selected_detect_win->algorithms.empty())
        {
            return -1;
        }
        auto algo = &cur_selected_detect_win->algorithms[0];

        if (algo != nullptr)
        {
            algo->template_image_ids.erase(std::remove(algo->template_image_ids.begin(), algo->template_image_ids.end(), template_id), algo->template_image_ids.end());
        }
        project_param_instance.GetProjectDataProcessInstance()->DeleteTemplate(template_id);

        return 0;
    }



    jrsdata::PNDetectInfo* OperateModel::GetPNDetectInfoByPartNum(const std::string& part_num)
    {
        auto result = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(part_num);
        if (result.has_value())
        {
            return &result->get();
        }
        else
        {
            return nullptr;
        }
    }

    jrsdata::OperateWindowType OperateModel::ConvertRenderEditGrapihsType(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        jrsdata::OperateWindowType operate_type = jrsdata::OperateWindowType::NONE;
        auto type_str = param_->graphics_and_select_units.first;
        if (type_str == "component" ||
            type_str == "mark" ||
            type_str == "barcode" || type_str == "subbarcode" ||
            //type_str == "subregion" ||  //导致current_component 为空
            type_str == "submark" ||
            type_str == "badmark")
        {
            operate_type = jrsdata::OperateWindowType::COMPONENT_CAD;
        }
        else if (type_str == "pad")
        {
            operate_type = jrsdata::OperateWindowType::PAD;
        }
        else if (type_str == "region")
        {
            operate_type = jrsdata::OperateWindowType::COMP_DETECT_WINDOW;
        }
        else if (type_str == "detect_region")
        {
            operate_type = jrsdata::OperateWindowType::COMP_DETECT_SHOW_WINDOW;
        }
        else if (type_str == "subregion")
        {
            operate_type = jrsdata::OperateWindowType::COMP_SUB_DETECT_WINDOW;
        }
        return operate_type;
    }

    int OperateModel::UpdateCurSelectedParamByRenderEditGrapihicsParam(const jrsdata::GraphicsUpdateProjectEventParamPtr& param_)
    {
        auto type = ConvertRenderEditGrapihsType(param_);
        switch (type)
        {
        case jrsdata::OperateWindowType::NONE:
        {
            //!< 添加界面取消选中时清空当前选中指针信息，并更新界面 2025.02.20 wangzhengkai
            cur_selected_component = nullptr;
            cur_selected_comp_unit = nullptr;
            cur_selected_spec_region = nullptr;
            cur_selected_detect_win = nullptr;

            auto param_algo_event = std::make_shared<jrsdata::AlgoEventParam>();
            param_algo_event->module_name = jrsaoi::OPERATE_MODULE_NAME;
            param_algo_event->event_name = jrsaoi::OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME;
            SetCurSelectedToAlgoEventParam(param_algo_event);
            SigUpdateOperator(param_algo_event);

            break;
        }
        case jrsdata::OperateWindowType::PAD:
        case jrsdata::OperateWindowType::COMPONENT_CAD:
        {
            if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME)
            {
                //! 如果是删除事件，同步需要删除元件检测状态的数据

                //! 临时方案，后期需要更改 by zhangyuyu 2025.3.31
                const auto& graphics_name = param_->graphics_and_select_units.second.type_and_component_name.second;
                std::vector<std::string> component_name_list = { graphics_name };
                for (auto& component_name : component_name_list)
                {
                    EraseSpeficComponentResult(component_name);

                }
            }
            RenderViewComponentUnitUpdate(param_);

            break;
        }
        case jrsdata::OperateWindowType::COMP_DETECT_WINDOW:
        {
            if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME)
            {
                //! 如果是删除事件，同步需要删除检测框检测状态的数据
                //! 临时方案，后期需要更改 by zhangyuyu 2025.3.31

                const auto& graphics_name = param_->graphics_and_select_units.second.type_and_component_name.second;
                std::vector<std::string> component_name_list = { graphics_name };
                const auto& detect_window_name = param_->graphics_and_select_units.second.window_name;
                for (auto& component_name : component_name_list)
                {
                    auto comp_iter = component_all_detect_window_result.find(component_name);

                    if (comp_iter != component_all_detect_window_result.end())
                    {

                        auto& detect_window_map = comp_iter->second;
                        auto detect_window_result_iter = detect_window_map.find(detect_window_name);

                        if (detect_window_result_iter != detect_window_map.end())
                        {
                            auto current_part_num = detect_window_result_iter->second.part_number_name;
                            auto current_part_num_components = project_param_instance.GetProjectDataProcessInstance()->ReadComponents(current_part_num);
                            if (current_part_num_components.has_value())
                            {
                                for (auto& component_value : current_part_num_components.value())
                                {
                                    EraseSpeficDetectWindowResult(detect_window_name, component_value.component_name);
                                }
                            }

                        }
                    }


                }
            }
            RenderViewDetectWinUpdate(param_);
            break;
        }
        case jrsdata::OperateWindowType::COMP_SUB_DETECT_WINDOW:
        {
            break;
        }
        case jrsdata::OperateWindowType::PAD_DETECT_WINDOW:
        {
            break;
        }
        case jrsdata::OperateWindowType::PAD_SUB_DETECT_WINDOW:
        {
            break;
        }
        case jrsdata::OperateWindowType::COMP_DETECT_SHOW_WINDOW:
        {
            break;
        }
        default:
        {
            break;
        }
        }
        return 0;
    }

    void OperateModel::EraseSpeficComponentResult(const std::string& component_name_)
    {
        // 删除 component_all_detect_window_result 中的该元件
        component_all_detect_window_result.erase(component_name_);

        // 删除 all_component_result 中的该元件
        all_component_result.erase(component_name_);
    }

    void OperateModel::EraseSpeficDetectWindowResult(const std::string& detect_window_name_, const std::string& component_name_)
    {
        auto comp_iter = component_all_detect_window_result.find(component_name_);

        if (comp_iter != component_all_detect_window_result.end())
        {
            //! 获取该元件的所有检测框
            auto& detect_window_map = comp_iter->second;

            //! 删除目标检测框
            detect_window_map.erase(detect_window_name_);

            //! 如果该元件下已无检测框，删除该元件
            if (detect_window_map.empty())
            {
                component_all_detect_window_result.erase(comp_iter);
                all_component_result.erase(component_name_); // 该元件没有检测框了，也删除整体检测结果
            }
            else
            {
                //! 如果不为空的话，要更新一下元件结果状态，因为删除了一个算检测框结果，会影响最终元件结果
                UpdateComponentResultStatus(component_name_);
            }
        }
    }

    void OperateModel::ErasAllResult()
    {
        component_all_detect_window_result.clear();
        all_component_result.clear();
    }

    void OperateModel::UpdateComponentResultStatus(const std::string& component_name_)
    {

        auto component_status = ComputeComponentStatus(component_name_);

        all_component_result[component_name_].result_status = component_status;
    }

    bool OperateModel::ComputeComponentStatus(const std::string& component_name_)
    {
        auto comp_iter = component_all_detect_window_result.find(component_name_);
        if (comp_iter == component_all_detect_window_result.end())
        {
            Log_WARN("元件 {} 不存在，无法计算状态", component_name_);
            return true;  // 该元件不存在，返回 true
        }

        const auto& detect_window_map = comp_iter->second;
        if (detect_window_map.empty())
        {
            Log_WARN("元件 {} 没有检测框，默认设置为 NG", component_name_);
            return true;  // 没有检测框，返回 true
        }

        // 判断所有检测框是否都是 OK
        return std::all_of(detect_window_map.begin(), detect_window_map.end(),
            [](const auto& pair)
            {
                return pair.second.detect_status_result;
            });
    }

    jrsparam::ExecuteModeInfo OperateModel::GetExecuteModeInfo()
    {
        jrsparam::ExecuteModeInfo run_mode_info;
        if (is_debugging)
        {
            run_mode_info.execute_mode = jrsparam::ExecuteMode::AutoMode;
            run_mode_info.fov_left_top_x = current_debug_info.current_component_fov_img.pos.x;
            run_mode_info.fov_left_top_y = current_debug_info.current_component_fov_img.pos.y;
            run_mode_info.src_img = current_debug_info.current_component_fov_img.imgs;

        }
        else
        {
            run_mode_info.execute_mode = jrsparam::ExecuteMode::ManualMode;
        }
        return run_mode_info;
    }

    void OperateModel::HandleDetectCopy(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);
        if (!param_temp)
        {
            return;
        }
        jrsdata::DetectWindow detect_window_;
        std::string part_number_name = cur_selected_component == nullptr ? "" : cur_selected_component->component_part_number;

        if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR)
        {
            // 垂直镜像(元件内操作)
            CreateOneDetectWindow(jrsdata::DetectOperateType::VERTICAL_MIRROR_COPY, detect_window_);
            project_param_instance.GetProjectDataProcessInstance()->CreateDetectWindow(cur_select_model_name, part_number_name, detect_window_);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR)
        {
            // 水平镜像(元件内操作)
            CreateOneDetectWindow(jrsdata::DetectOperateType::HORIZONTAL_MIRROR_COPY, detect_window_);
            project_param_instance.GetProjectDataProcessInstance()->CreateDetectWindow(cur_select_model_name, part_number_name, detect_window_);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE)
        {
            // 90度旋转(元件内操作)
            CreateOneDetectWindow(jrsdata::DetectOperateType::ROTATE_90_COPY, detect_window_);
            project_param_instance.GetProjectDataProcessInstance()->CreateDetectWindow(cur_select_model_name, part_number_name, detect_window_);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE)
        {
            // 180度旋转(元件内操作)
            CreateOneDetectWindow(jrsdata::DetectOperateType::ROTATE_180_COPY, detect_window_);
            project_param_instance.GetProjectDataProcessInstance()->CreateDetectWindow(cur_select_model_name, part_number_name, detect_window_);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_DETECT_COPY)
        {
            if (cur_selected_detect_win == nullptr)
            {
                return;
            }
            // 检测框复制
            detect_copy_param.cur_select_detect_win.clear();
            CreateOneDetectWindow(jrsdata::DetectOperateType::RECTS_COPY, detect_window_);
            detect_copy_param.detect_operate_type = jrsdata::DetectOperateType::RECTS_COPY;
            detect_copy_param.cur_select_detect_win.push_back(detect_window_);

            // 模版图复制
            detect_copy_param.cur_select_detect_templete.clear();
            std::vector<jrsdata::Template> list;
            GetTemplatesByDetectWinName(part_number_name, cur_selected_detect_win->name, list);
            detect_copy_param.cur_select_detect_templete.push_back(list);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_DETECT_COPY)
        {
            // 元件检测框复制
            detect_copy_param.detect_operate_type = jrsdata::DetectOperateType::COMPONENT_RECTS_COPY;
            GetCurSelectComponentEntity(detect_copy_param.select_entity);
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_DETECT_ACTION)
        {
            // 检测框粘贴和创建模版图
            DetectCopyAction();
        }
        else if (param_temp->event_name == jrsaoi::SHORTCUT_ACT_ROTATE_90_ALL)
        {
            // 所有检测框旋转90度
            Rotate90CurAllDetectWin();
        }

        // 通知信号
        auto algo_param_algo_update = std::make_shared<jrsdata::AlgoEventParam>();
        algo_param_algo_update->event_name = param_->event_name;
        SigUpdateOperator(algo_param_algo_update);
    }

    void OperateModel::DetectCopyAction()
    {
        if (detect_copy_param.detect_operate_type == jrsdata::DetectOperateType::RECTS_COPY)
        {
            auto cur_select = detect_copy_param.cur_select_detect_win;// 拷贝一份
            for (size_t i = 0; i < cur_select.size(); i++)
            {
                // 先创建模版图，拿到模版图id，模板图的id需要更新到检测框中
                std::vector<int> templete_ids;
                auto& templete = detect_copy_param.cur_select_detect_templete[i];
                for (size_t j = 0; j < templete.size(); j++)
                {
                    jrsdata::Template out_templete;
                    jrsdata::Template& temp = templete[j];
                    project_param_instance.GetProjectDataProcessInstance()->CreateTemplate(temp.GetMatImage(), temp.color_params, temp.light_image_id, out_templete);
                    templete_ids.push_back(out_templete.id);
                }
                auto& algorithms = cur_select[i].algorithms;
                for (size_t k = 0; k < algorithms.size(); k++)
                {
                    algorithms[i].template_image_ids = templete_ids;
                }
                std::string part_number_name = cur_selected_component == nullptr ? "" : cur_selected_component->component_part_number;
                cur_select[i].model_name = cur_select_model_name; // 需要更新该检测框模型组名称
                project_param_instance.GetProjectDataProcessInstance()->CreateDetectWindow(cur_select_model_name, part_number_name, cur_select[i]);
            }
        }
        else if (detect_copy_param.detect_operate_type == jrsdata::DetectOperateType::COMPONENT_RECTS_COPY)
        {
            LoadComponentByComponentEntity(detect_copy_param.select_entity, true);
        }
    }

    void OperateModel::CreateOneDetectWindow(const jrsdata::DetectOperateType detect_operate_type, jrsdata::DetectWindow& detect_window_)
    {
        if (cur_selected_detect_win == nullptr)
        {
            return;
        }

        ProcessMirrorPad(*cur_selected_detect_win, cur_selected_comp_unit->direction);

        detect_window_.width = cur_selected_detect_win->width;
        detect_window_.height = cur_selected_detect_win->height;
        detect_window_.model_name = cur_selected_detect_win->model_name;
        detect_window_.defect_name = cur_selected_detect_win->defect_name;
        detect_window_.group_name = cur_selected_detect_win->group_name;
        detect_window_.enable = true;

        // 修改检测框位置
        switch (detect_operate_type)
        {
        case jrsdata::DetectOperateType::HORIZONTAL_MIRROR_COPY:
        {
            detect_window_.cx = -cur_selected_detect_win->cx;
            detect_window_.cy = cur_selected_detect_win->cy;

            // 元件角度影响镜像
            float angle = std::abs(cur_selected_component->angle);
            if (cur_selected_comp_unit->unit_type == jrsdata::ComponentUnit::Type::BODY && std::abs(angle - 90) < 10 || std::abs(angle - 270) < 10)
            {
                detect_window_.cx = cur_selected_detect_win->cx;
                detect_window_.cy = -cur_selected_detect_win->cy;
            }
        }
        break;
        case jrsdata::DetectOperateType::VERTICAL_MIRROR_COPY:
        {
            detect_window_.cx = cur_selected_detect_win->cx;
            detect_window_.cy = -cur_selected_detect_win->cy;

            // 元件角度影响镜像
            float angle = std::abs(cur_selected_component->angle);
            if (cur_selected_comp_unit->unit_type == jrsdata::ComponentUnit::Type::BODY && std::abs(angle - 90) < 10 || std::abs(angle - 270) < 10)
            {
                detect_window_.cx = -cur_selected_detect_win->cx;
                detect_window_.cy = cur_selected_detect_win->cy;
            }
        }
        break;
        case jrsdata::DetectOperateType::ROTATE_90_COPY:
        {
            // 交换宽高
            detect_window_.width = cur_selected_detect_win->height;
            detect_window_.height = cur_selected_detect_win->width;

            // 计算旋转后的cx,cy
            float new_x, new_y;
            jrscore::CoordinateTransform::Rotate(cur_selected_detect_win->cx, cur_selected_detect_win->cy, new_x, new_y, 0.0f, 0.0f, 90);
            detect_window_.cx = new_x;
            detect_window_.cy = new_y;
        }
        break;
        case jrsdata::DetectOperateType::ROTATE_180_COPY:
        {
            detect_window_.cx = -cur_selected_detect_win->cx;
            detect_window_.cy = -cur_selected_detect_win->cy;
        }
        break;
        case jrsdata::DetectOperateType::RECTS_COPY:
        {
            detect_window_.cx = 0;
            detect_window_.cy = 0;
        }
        break;
        default:
            break;
        }

        // 算法默认参数添加
        std::string algo_name = cur_selected_detect_win->algorithms[0].detect_algorithm_name;
        jrsdata::DetectAlgorithm detect_algo;
        detect_algo.detect_algorithm_name = algo_name;
        detect_window_.algorithms.push_back(detect_algo);
        detect_window_.algorithms[0].light_image_id = 0;
        ColorParams color_param;
        detect_window_.algorithms[0].color_param = color_param.ToJson();

        auto it = algo_default_param.find(algo_name);
        if (it != algo_default_param.end())
        {
            detect_window_.algorithms[0].param = algo_default_param.at(algo_name);

            // 创建子检测框
            project_param_instance.GetProjectDataProcessInstance()->CreateSubDetectWindow(detect_window_, 0, detect_algo.algorithm_detect_windows);
        }
    }

    void OperateModel::ProcessMirrorPad(jrsdata::DetectWindow& detectwindow, jrsdata::ComponentUnit::Direction direction)
    {
        if (!detectwindow.model_name._Starts_with("pad"))
        {
            return;
        }

        cv::Point2f component_center(0, 0);
        cv::Point2f unit_center(detectwindow.cx, detectwindow.cy);
        cv::RotatedRect detect_window_rotated(
            { detectwindow.cx, detectwindow.cy },
            { detectwindow.width, detectwindow.height },
            0
        );

        auto new_rect = project_param_instance.GetProjectDataProcessInstance()->GetRotatedRectByPadDirection(component_center, unit_center, detect_window_rotated, direction);

        detectwindow.cx = new_rect.center.x;
        detectwindow.cy = new_rect.center.y;
        detectwindow.width = new_rect.size.width;
        detectwindow.height = new_rect.size.height;
    }

    void OperateModel::Rotate90CurAllDetectWin()
    {
        if (cur_selected_spec_region == nullptr)
        {
            return;
        }
        auto& detect_models = cur_selected_spec_region->detect_models;
        auto& units = cur_selected_spec_region->units;
        std::vector<std::string> has_unit_group_name;
        for (size_t i = 0; i < units.size(); i++)
        {
            // 组件组名称
            std::string unit_group_name = units[i].unit_group_name;
            auto has_exchange = std::find(has_unit_group_name.begin(), has_unit_group_name.end(), unit_group_name);
            if (has_exchange == has_unit_group_name.end())
            {
                auto it = detect_models.find(unit_group_name);
                if (it != detect_models.end())
                {
                    std::vector<jrsdata::DetectWindow>& detect_model = it->second.detect_model;
                    for (size_t j = 0; j < detect_model.size(); j++)
                    {
                        // 交换宽高即可
                        float width = detect_model[j].width;
                        float height = detect_model[j].height;
                        detect_model[j].width = height;
                        detect_model[j].height = width;
                    }
                }
                has_unit_group_name.push_back(unit_group_name);
            }
        }
    }


    void OperateModel::CheckRepairDataHasSaved()
    {
        //检查图片和jason 文件是否存在 存在则不保存，不存在则保存
        auto& instance = project_param_instance.GetInstance();
        auto process_instance = instance.GetParameterProcessInstance();
        auto project_data_instance = instance.GetProjectDataProcessInstance();

        auto machine_param_repair_path = process_instance->GetSettingParamValueByName<std::string>(
            jrsdata::ParamLevel::MACHINE,
            jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH);

        auto project_name = project_data_instance->GetProjectName();

        if (machine_param_repair_path.empty()) {
            Log_WARN("获取保存维修站数据路径为空，无法自动保存");
            return;
        }

        auto enable_path_pair = process_instance->GetMachinePathAndEnable(machine_param_repair_path);
        if (!enable_path_pair.first)
            return;  //未启用 维修站数据保存

        const std::string& base_path = enable_path_pair.second;

        // 工具函数：创建并填充 OperateViewParam
        auto MakeRepairParam = [&](const std::string& event_name) {
            auto param_temp = std::make_shared< jrsdata::OperateViewParam >();
            param_temp->event_name = event_name;
            param_temp->module_name = jrsaoi::VIEW_MODULE_NAME;
            param_temp->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
            param_temp->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
            param_temp->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
            param_temp->repair_data.project_name = project_name;
            return param_temp;
            };

        // 检查图片是否存在
        std::string compress_image_path = base_path + "/" + project_name + "/compressentiretyimg/";
        auto image_type = process_instance->GetSettingParamValueByName<std::string>(
            jrsdata::ParamLevel::MACHINE,
            jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_TYPE);

        if (!jtools::FileOperation::HasFileByNameOrExt(compress_image_path, image_type)) {
            auto param_temp = MakeRepairParam(OPERATE_SAVE_REAPIR_COMPRESS_IMG_EVENT_NAME);
            param_temp->repair_data.compress_entirety_board_imgs = this->GetProjectCompressImg();
            emit SigUpdateOperator(param_temp);
        }

        // 检查 briefcomponents.json 是否存在
        std::string brief_component_path = base_path + "/" + project_name + "/briefcomponent/";
        if (!jtools::FileOperation::HasFileByNameOrExt(brief_component_path, "briefcomponents.json")) {
            auto param_temp = MakeRepairParam(OPERATE_SAVE_REAPIR_BRIEF_COMPONENT_EVENT_NAME);
            param_temp->repair_data.brief_component_info_list = this->GetAllBriefComponentInfo();
            emit SigUpdateOperator(param_temp);
        }
    }

    void OperateModel::ClearInnerMembersParam()
    {
        cur_selected_component = nullptr;
        cur_selected_detect_win = nullptr;
        cur_selected_spec_region = nullptr;
        cur_selected_comp_unit = nullptr;
        cur_selected_pad_name = "";
        cur_select_model_name = "";
    }

    std::vector<jrsdata::SubBoard> OperateModel::SubboardSort(const jrsdata::SubboardSortParam& subboard_sort_param_)
    {
        auto project_process_ptr = project_param_instance.GetProjectDataProcessInstance();
        auto res = _subboard_sort_manager_ptr->SubboardSort(project_process_ptr, subboard_sort_param_);
        if (res != jrscore::AOI_OK)
        {
            JRSMessageBox_INFO("提示", "无法进行子板排序，请检查子板名称是否正确或子板是否存在。", jrscore::MessageButton::Ok);
        }
        return _subboard_sort_manager_ptr->GetSortedSubboards();
    }

    std::shared_ptr<jrsdata::RenderEventParam> OperateModel::CreateRenderEventParam(const std::string& event_name_, const std::shared_ptr<jrsdata::MultiBoardEventParam>& multi_param_ptr_ /*= nullptr*/)
    {
        auto param = std::make_shared<jrsdata::RenderEventParam>();
        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        param->event_name = event_name_;
        param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
        param->multi_param_ptr = multi_param_ptr_;
        return param;
    }

    std::shared_ptr<jrsdata::RenderViewParam> OperateModel::CreateRenderViewParam(const std::string& event_name_)
    {
        auto param = std::make_shared<jrsdata::RenderViewParam>();
        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        param->event_name = event_name_;
        param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
        return param;
    }

    int OperateModel::SetCurSelectedToAlgoEventParam(jrsdata::AlgoEventParamPtr& param)
    {
        param->cur_select_component = cur_selected_component;
        param->cur_select_detect_win = cur_selected_detect_win;
        param->cur_select_spec_and_detect_region = cur_selected_spec_region;
        param->cur_select_component_unit = cur_selected_comp_unit;
        return 0;
    }

    void OperateModel::GetCurSelectComponentEntity(jrsdata::ComponentEntity& entity)
    {
        // 获取当前选择的料号信息
        auto param = std::make_shared<jrsdata::AlgoEventParam>();
        SetCurSelectedToAlgoEventParam(param);
        if (param->cur_select_component_unit == nullptr || param->cur_select_component == nullptr)
        {
            return;
        }
        entity.detect_info = *param->cur_select_spec_and_detect_region;
        // 获取本体宽高
        auto& units = entity.detect_info.units;
        for (size_t i = 0; i < units.size(); i++)
        {
            if (units[i].unit_type == jrsdata::ComponentUnit::Type::BODY)
            {
                entity.width = units[i].width;
                entity.height = units[i].height;
                break;
            }
        }
        entity.part_name = param->cur_select_component->component_part_number;
        entity.bind_part_name = GetBindPartName(entity.part_name);

        // 显示图
        cv::RotatedRect crop_image_rect; // 最小外接矩形
        cv::Mat display;
        float offset_x = 0.0f;
        float offset_y = 0.0f;
        GetDeviceImg(param->cur_select_component->component_name, param->cur_select_component->subboard_name, display, offset_x, offset_y);
        entity.offset_x = offset_x;
        entity.offset_y = offset_y;
        jrsdata::Template display_img;
        display_img.SetMatImage(display);
        entity.display_img = display_img;

        // 获取当前料号所有的模板图
        std::vector<std::string> win_names = entity.detect_info.GetAllWinName();
        std::vector<jrsdata::Template> templates;
        for (size_t i = 0; i < win_names.size(); i++)
        {
            std::vector<jrsdata::Template> list;
            GetTemplatesByDetectWinName(param->cur_select_component->component_part_number, win_names[i], list);
            templates.insert(templates.end(), list.begin(), list.end());
        }
        entity.mask_img = templates;
    }

    std::string OperateModel::GetBindPartName(const std::string part_name)
    {
        if (part_name != "")
        {
            auto info = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(part_name);
            if (info.has_value())
            {
                return info.value().get().bind_part_name;
            }
        }
        return std::string();
    }

    int OperateModel::GetDeviceImg(const std::string component_name, const std::string sub_board_name, cv::Mat& img, float& offset_x, float& offset_y)
    {
        if (component_name != "")
        {
            auto component_ref = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(component_name, sub_board_name, jrsdata::Component::Type::CAD);
            if (component_ref.has_value())
            {
                std::unordered_map<int, cv::Mat> img_map;
                cv::RotatedRect crop_image_rect;
                project_param_instance.GetProjectDataProcessInstance()->GetComponentImage(component_ref.value().get(), img_map, crop_image_rect);
                if (!img_map.empty())
                {
                    auto it = img_map.begin();
                    img = it->second;
                    offset_x = crop_image_rect.center.x - component_ref.value().get().x;
                    offset_y = crop_image_rect.center.y - component_ref.value().get().y;
                }
            }
        }
        return 0;
    }

    int OperateModel::GetComponentEntitys(jrsdata::ComponentEntitys& entitys)
    {
        auto project_param = project_param_instance.GetProjectDataProcessInstance()->GetProjectParam();
        if (!project_param)
        {
            return -1;
        }
        auto part_nums_and_detect_regions = project_param->board_info.part_nums_and_detect_regions;
        entitys.components_param.clear();
        for (const auto& item : part_nums_and_detect_regions)
        {
            std::string part_name = item.first;
            // 定位点、条码、坏板不保存到元件库
            if (part_name.find("mark") == std::string::npos && part_name.find("barcode") == std::string::npos)
            {
                jrsdata::ComponentEntity entity;
                GetComponentEntity(part_name, item.second, entity);
                entitys.components_param.push_back(entity);
            }
        }
        return 0;
    }

    int OperateModel::GetComponentEntity(const std::string part_name, const jrsdata::PNDetectInfo& info, jrsdata::ComponentEntity& entity)
    {
        entity.part_name = part_name;                        // 料号
        entity.bind_part_name = GetBindPartName(entity.part_name);
        entity.detect_info = info;                          // 检测框信息
        entity.width = entity.detect_info.GetBodyWidth();   // 宽度
        entity.height = entity.detect_info.GetBodyWidth();  // 高度

        // 获取模板图
        std::vector<std::string> win_names = entity.detect_info.GetAllWinName();
        std::vector<jrsdata::Template> templates;
        for (size_t i = 0; i < win_names.size(); i++)
        {
            std::vector<jrsdata::Template> list;
            project_param_instance.GetProjectDataProcessInstance()->GetTemplatesByDetectWinName(part_name, win_names[i], list);
            templates.insert(templates.end(), list.begin(), list.end());
        }
        entity.mask_img = templates;

        // 获取显示图
        std::string component_name = "";
        std::string subboard_name = "";
        project_param_instance.GetProjectDataProcessInstance()->GetComponentNameFromPartNumber(entity.part_name, component_name, subboard_name);
        cv::Mat display;

        float offset_x = 0.0f;
        float offset_y = 0.0f;
        GetDeviceImg(component_name, subboard_name, display, offset_x, offset_y);
        entity.offset_x = offset_x;
        entity.offset_y = offset_y;
        if (!display.empty())
        {
            jrsdata::Template display_img;
            display_img.SetMatImage(display);
            entity.display_img = display_img;
        }

        return 0;
    }

    int OperateModel::GetTemplatesByDetectWinName(std::string part_name, std::string win_name, std::vector<jrsdata::Template>& templates)
    {
        if (cur_selected_component == nullptr || cur_selected_detect_win == nullptr)
        {
            return -1;
        }
        project_param_instance.GetProjectDataProcessInstance()->GetTemplatesByDetectWinName(part_name, win_name, templates);
        return 0;
    }

    int OperateModel::LoadComponentByComponentEntity(const jrsdata::ComponentEntity& entity, bool use_cur_select)
    {
        if (!cur_selected_component)
        {
            return -1;
        }
        std::string part_name = entity.part_name;
        jrsdata::PNDetectInfo info = entity.detect_info;
        std::vector<jrsdata::Template> mask_img = entity.mask_img;
        // 使用当前选中的料号
        if (use_cur_select)
        {
            part_name = cur_selected_component->component_part_number;
        }

        return project_param_instance.GetProjectDataProcessInstance()->UpdateCompoentInfo(part_name, info, mask_img);
    }


    std::unordered_map<int, cv::Mat> OperateModel::GetProjectCompressImg()
    {
        auto res_imgs = project_param_instance.GetProjectDataProcessInstance()->ReadImage();

        std::unordered_map<int, cv::Mat> res_img_map;

        if (!res_imgs.has_value())
        {
            JRSMessageBox_ERR("Error", "读取工程图片失败，请查看是否加载图片！", jrscore::MessageButton::Ok);
            return res_img_map;
        }

        auto compress_rate_struct_value = project_param_instance.GetInstance().GetParameterProcessInstance()->
            GetSettingParamValueByName<float>(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPRESS_IMG_RATE);
        auto compress_rate_value = compress_rate_struct_value / 100.f;
        for (auto& [key, val] : **res_imgs)
        {
            if (static_cast<jrsdata::LightImageType>(key) == jrsdata::LightImageType::RGB) //仅保存一张压缩图片 HJC BY:2025/5/8
            {
                auto low_res_img = jrstool::CropAndPasteTool::GenerateLowResolutionImage(val, compress_rate_value);

                if (low_res_img.empty())
                {

                    break;
                }
                res_img_map[key] = low_res_img;
                break;
            }
        }
        return res_img_map;
    }

    std::vector<jrsdata::BriefComponentInfo> OperateModel::GetAllBriefComponentInfo()
    {
        return project_param_instance.GetProjectDataProcessInstance()->ReadAllBriefComponentInfo();
    }

    std::unordered_map<int, cv::Mat> OperateModel::GetProjectEntiretyImg()
    {

        auto res_imgs = project_param_instance.GetProjectDataProcessInstance()->ReadImage();

        return  **res_imgs;
    }

    void OperateModel::ModifyComponentCoordinate(const cv::Mat& location_matrix_, const cv::Mat& src_matrix_, const JrsRect component_rect_in_crop_img_, jrsdata::Component& component_modify_)
    {
        // 更新元件位置
        jcvtools::JrsHomMat2D hom_mat(location_matrix_);
        jcvtools::JrsHomMat2D hom_src(src_matrix_);
        hom_mat.HomMat2dCompose(hom_src);

        auto transformed_component = hom_mat.AffineTransPoint(cv::Point2f(component_rect_in_crop_img_.cx, component_rect_in_crop_img_.cy));

        component_modify_.x = transformed_component.x;
        component_modify_.y = transformed_component.y;

    }

    void OperateModel::UpdateSingleComponentInfo(jrsdata::Component new_component_)
    {

        project_param_instance.GetProjectDataProcessInstance()->UpdateSingleComponentInfo(new_component_);

    }

    std::unordered_map<std::string, std::vector<std::string>> OperateModel::GetNotDetectUnitName()
    {

        return project_param_instance.GetProjectDataProcessInstance()->GetNotDetectUnitName();
    }

    void OperateModel::SetDetectWindowDetectResult(const jrsdata::Component& component, const std::string& detect_window_name, jrsoperator::OperatorParamBasePtr algo_result)
    {
        ComponentAlgoResult temp;
        temp.algo_result = algo_result;
        temp.component_name = component.component_name;
        temp.sub_board_name = component.subboard_name;
        temp.part_number_name = component.component_part_number;
        temp.detect_window_name = detect_window_name;
        bool result_status = std::none_of(algo_result->output_detect_rects.begin(), algo_result->output_detect_rects.end(),
            [](const auto& value)
            {
                return !value.status;
            }) && !algo_result->output_detect_rects.empty();
        temp.detect_status_result = result_status;
        component_all_detect_window_result[component.component_name][detect_window_name] = temp;
    }

    jrsoperator::OperatorParamBasePtr OperateModel::GetDetectWindowDetectResult(const std::string& component_name, const std::string& detect_window_name)
    {
        if (component_all_detect_window_result.find(component_name) != component_all_detect_window_result.end())
        {
            const auto& detect_window_results_map = component_all_detect_window_result[component_name];
            if (detect_window_results_map.find(detect_window_name) != detect_window_results_map.end())
            {
                return detect_window_results_map.at(detect_window_name).algo_result;
            }
        }
        // 如果找不到，返回一个空指针
        return nullptr;
    }

    void OperateModel::SaveComponentDetectStatus(const jrsdata::Component& component)
    {

        bool component_status = ComputeComponentStatus(component.component_name);
        all_component_result[component.component_name] = { component.component_name,component.subboard_name, component.subboard_name,component.component_part_number, "", component_status };
    }

    const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& OperateModel::GetAllComponentResultStatus()
    {

        return all_component_result;
    }

    std::optional<std::unordered_map<std::string, ComponentAlgoResult>> OperateModel::GetSpeficComponentResultStatus(const std::string& component_name_)
    {

        auto iter = component_all_detect_window_result.find(component_name_);
        if (iter != component_all_detect_window_result.end())
        {
            return iter->second;
        }
        return std::nullopt; // 没找到返回空

    }


    int OperateModel::GetUnitIDByUnitShowID(const std::string& subboard_name_, const std::string& component_name_, const std::string& window_name_, int show_id_)
    {
        auto project_process = project_param_instance.GetProjectDataProcessInstance();
        const auto& component = project_process->ReadComponentRef(component_name_, subboard_name_, jrsdata::Component::Type::CAD);
        if (!component.has_value())
        {
            return -1;// 查询失败

        }
        auto model_name = project_process->GetModelNameByPartNumberAndDetectWindowName(component->get().component_part_number, window_name_);
        if (model_name.empty())
        {
            return -1;
        }
        auto unit = project_process->ReadComponentUnitByShowID(component->get().component_part_number, model_name, show_id_);
        if (unit.has_value())
        {
            return unit->id;
        }
        return -1;
    }

    jrsdata::SettingViewParamPtr OperateModel::GetSettingParamsPtr()
    {
        auto& param_process_instance = project_param_instance.GetInstance().GetParameterProcessInstance();
        return param_process_instance->GetSettingParams();
    }

    void OperateModel::GetResolution(float& resolution_x_, float& resolution_y_)
    {
        auto& param_process_instance = project_param_instance.GetProjectDataProcessInstance();
        param_process_instance->GetResolution(resolution_x_, resolution_y_);
    }

    int OperateModel::CreateSubboardComponent(jrsdata::Component& ai_area)
    {
        return project_param_instance.GetProjectDataProcessInstance()->CreateSubboardComponent(ai_area);
    }

    int OperateModel::CreateAIComponent(jrsdata::Component& ai_component, float width, float height)
    {
        // 需要修改元件的位置
        if (cur_selected_component != nullptr)
        {
            ai_component.x = cur_selected_component->x;
            ai_component.y = cur_selected_component->y;
            ai_component.subboard_name = cur_selected_component->subboard_name;
            return project_param_instance.GetProjectDataProcessInstance()->CreateAIComponent(ai_component, width, height);
        }
        return -1;
    }

    std::optional<jrsdata::PNDetectInfo> OperateModel::ReadPNDetectInfo(const std::string& part_number_name)
    {
        return project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfo(part_number_name);
    }

    int OperateModel::GetDetectWinExecuteParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit,
        const jrsdata::DetectWindow& detect_win, jrsparam::ExecuteAlgoParam& exect_param, cv::Mat& matrix_to_src_image_)
    {
        if (detect_win.algorithms.empty())
        {
            return -1;
        }
        const auto& run_mode_info = GetExecuteModeInfo();

        project_param_instance.GetProjectDataProcessInstance()->GetDetectWindowExecuteParam(component, component_unit, detect_win, exect_param, matrix_to_src_image_, run_mode_info);

        return 0;
    }

    int OperateModel::GetComponentExecuteParam(const jrsdata::Component& component,
        std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exect_param, cv::Mat& matrix_to_src_image_)
    {
        const auto& run_mode_info = GetExecuteModeInfo();

        project_param_instance.GetProjectDataProcessInstance()->GetComponentExecuteParam(component, detect_win_exect_param, matrix_to_src_image_, run_mode_info);
        return 0;
    }

    int OperateModel::SaveStandardComponentLightImg(/*const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& component_img_map*/)
    {
        /* auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
         auto components = project_param_instance.GetProjectDataProcessInstance()->ReadAllComponent();
         for (auto& value_component : components)
         {
             jrsdata::ComponentSaveInfo component_temp;
             component_temp.project_name = project_param_instance.GetProjectDataProcessInstance()->GetProjectParam()->project_name;
             component_temp.subboard_name = value_component.get().subboard_name;
             component_temp.component_name = value_component.get().component_name;

             project_param_instance.GetProjectDataProcessInstance()->GetComponentLightImg(value_component, component_temp.input_img);
             param_temp->standard_info.emplace_back(component_temp);
         }


         param_temp->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
         param_temp->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
         param_temp->event_name = jrsaoi::SHORTCUT_ACT_SAVE_STANDARD_IMG;
         param_temp->module_name = jrsaoi::OPERATE_MODULE_NAME;
         param_temp->invoke_module_name = jrsaoi::DATA_MODULE_NAME;

         emit SigUpdateOperator(param_temp);*/

        return jrscore::AOI_OK;
    }

    void OperateModel::SetDetectWinRunResult(const std::string part_name, const std::string& detect_model_name, const std::string& win_name, const std::string& result)
    {
        auto pn_detect_info = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(part_name);

        if (pn_detect_info.has_value())
        {
            auto win_ptr = pn_detect_info->get().ReadDetectWindowPtr(win_name, detect_model_name);
            if (win_ptr == nullptr || win_ptr->algorithms.empty())
            {
                return;
            }
            win_ptr->algorithms[0].param = result;
        }
    }

    int OperateModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::BOARD_SORT_EVENT_NAME)
        {
            auto operate_model = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            if (!operate_model ||
                !operate_model->multi_board_event_param_ptr.has_value() ||
                !operate_model->multi_board_event_param_ptr.value() ||
                !operate_model->multi_board_event_param_ptr.value()->subboard_sort_param.has_value())
            {
                Log_ERROR("子板排序参数检查失败！");
                return -1;
            }

            auto& multi_board_param = operate_model->multi_board_event_param_ptr.value();
            auto& sort_param = multi_board_param->subboard_sort_param;

            switch (sort_param->event)
            {
            case jrsdata::SubboardSortParam::Event::REGULAR_SORT:
            {
                sort_param->sorted_subboards = SubboardSort(sort_param.value());
                auto render_param = CreateRenderEventParam(
                    jrsaoi::MULTI_BOARD_OPERATE_EVENT_NAME,
                    multi_board_param
                );
                emit SigUpdateOperator(render_param);
                break;
            }
            case jrsdata::SubboardSortParam::Event::CONFIRM_SORT:
            {
                _subboard_sort_manager_ptr->UpdateSortedSubboardToProject(project_param_instance.GetProjectDataProcessInstance());
                auto render_param = CreateRenderViewParam(jrsaoi::REQUEST_RENDER2D_UPDATE_PROJECT_EVENT_NAME);
                emit SigUpdateOperator(render_param);
                _subboard_sort_manager_ptr->ClearSortedSubboards();
                break;
            }
            case jrsdata::SubboardSortParam::Event::CANCEL_SORT:
            {
                _subboard_sort_manager_ptr->ClearSortedSubboards();
                auto render_param = CreateRenderViewParam(jrsaoi::REQUEST_RENDER2D_UPDATE_PROJECT_EVENT_NAME);
                emit SigUpdateOperator(render_param);
                break;
            }
            default:
                Log_ERROR("未知排序事件类型！");
                return -1;
            }
        }

        if (param_->sub_name == OPERATE_UPDATE_MOTION_SUB_NAME)
        {
            return 0;
        }

        if (param_->event_name == CHANGE_RENDER_SHOW_IMG_TYPE)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::RenderEventParam>(param_);
            cur_selected_light_type = param_temp->select_param.light_type;

            auto param_algo_event = std::make_shared<jrsdata::AlgoEventParam>();
            param_algo_event->module_name = jrsaoi::OPERATE_MODULE_NAME;
            param_algo_event->event_name = jrsaoi::OPERATE_UPDATE_IPE_PROCESS_IMAGE_EVENT_NAME;
            auto& project_data_instance = jrsaoi::ParamOperator::GetInstance();

            if (cur_selected_component && cur_selected_detect_win && cur_selected_comp_unit)
            {
                param_algo_event->light_type = cur_selected_light_type;
                std::vector<std::pair<int, cv::RotatedRect>> detect_rects;
                const auto& run_mode_info = GetExecuteModeInfo();

                project_data_instance.GetProjectDataProcessInstance()->CropDetectWinImageFromProject(cur_selected_light_type, *cur_selected_component, cur_selected_detect_win->name, *cur_selected_comp_unit, param_algo_event->region_mat, detect_rects, run_mode_info);
                SetCurSelectedToAlgoEventParam(param_algo_event);
                SigUpdateOperator(param_algo_event);
            }
        }
        else if (param_->event_name == jrsaoi::PROJECT_CREATE_EVENT_NAME &&
            param_->sub_name != jrsaoi::SHORTCUT_OPERATER_SUB_NAME)
        {

            auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            if (!param_temp)
            {
                Log_ERROR(jrscore::ProjectError::E_AOI_PROJECT_POINTER_EMPTY, "新建工程指针转换失败！");
                return jrscore::ProjectError::E_AOI_PROJECT_POINTER_EMPTY;
            }
            project_param_instance.GetProjectDataProcessInstance()->ClearProjectMap();
            project_param_instance.GetProjectDataProcessInstance()->SetProjectParam(param_temp->project_param);
            project_param_instance.GetProjectDataProcessInstance()->AppendProject(param_temp->project_param);

        }
        else if (param_->event_name == jrsaoi::CONFIRM_BOARD_POS_EVENT_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            project_param_instance.GetProjectDataProcessInstance()->SetBoardPosition(param_temp->project_param->board_info.left_top_x,
                param_temp->project_param->board_info.left_top_y,
                param_temp->project_param->board_info.right_bottom_x,
                param_temp->project_param->board_info.right_bottom_y
            );
            project_param_instance.GetProjectDataProcessInstance()->SetBoardRealSize(
                param_temp->project_param->board_info.real_width,
                param_temp->project_param->board_info.real_height
            );
        }
        else if (param_->event_name == jrsaoi::REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME)
        {
            return UpdateDetectWindowParam(param_);
        }
        else if (param_->event_name == jrsaoi::REQUEST_OPERATE_UPDATE_IPE_PARAM_EVENT_NAME)
        {
            return UpdateSelectedDetectWinIPEParam(param_);
        }
        else if (param_->event_name == jrsaoi::REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME)
        {
            return UpdateTemplateParam(param_);
        }
        else if (param_->event_name == jrsaoi::RENDER2D_DRAW_TEMP_REGION_DONE_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param_);
            if (!param)
            {
                return -1;
            }

            cv::Mat template_mat;
            project_param_instance.GetProjectDataProcessInstance()->RotateCropImageFromProject(cur_selected_light_type, param->temp_region, template_mat);
            if (template_mat.empty())
            {
                return -2;
            }

            auto param_algo_event = std::make_shared<jrsdata::AlgoEventParam>();
            param_algo_event->module_name = jrsaoi::OPERATE_MODULE_NAME;
            param_algo_event->event_name = jrsaoi::OPERATE_CREATE_TEMPLATE_BY_DRAW_REGION_EVENT_NAME;
            param_algo_event->region_mat = template_mat;
            SigUpdateOperator(param_algo_event);
        }
        else if (param_->event_name == jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME ||
            param_->event_name == jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME ||// By: 5.改变位置和大小后，不要刷新居中； 2024/12/13 HJC
            param_->event_name == jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME ||
            param_->event_name == jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param_);
            if (!param)
            {
                return -1;
            }

            if (UpdateCurSelectedParamByRenderEditGrapihicsParam(param) != 0)
            {
                return -1;
            }
            return 0;
        }
        else if (param_->event_name == jrsaoi::ENTIRETY_IMAGE_READ)
        {

            auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            if (!param_temp)
            {
                Log_ERROR(jrscore::ProjectError::E_AOI_PROJECT_POINTER_EMPTY, "新建工程指针转换失败！");
                return jrscore::ProjectError::E_AOI_PROJECT_POINTER_EMPTY;
            }
            project_param_instance.GetProjectDataProcessInstance()->SetCurrentEntiretyGroupName(param_temp->project_param->current_group_name);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_DETECT_WINDOW ||
            param_->event_name == jrsaoi::ACT_TEST_COMPONET ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_RUN_COMPONENT_SAVE_ALGO_INFO_EVENT ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_LOCATED_RUN_EVENT
            )  //TODO 增加界面快捷键的检测框测试按钮 by baron_zhang 2024-12-10
        {
            auto param_temp = std::make_shared<jrsdata::AlgoEventParam>();
            param_temp->event_name = param_->event_name;
            SetCurSelectedToAlgoEventParam(param_temp);
            SigUpdateOperator(param_temp);
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_DETECT_COPY ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_DETECT_COPY ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_DETECT_ACTION ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_ROTATE_90_ALL
            )
        {
            // 与检测框复制有关的操作
            HandleDetectCopy(param_);
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_SUBBOARD_AI ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_BOARD_AI ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_BODY_AI)
        {
            // 转到operatorcontroller
            SigUpdateOperator(param_);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_PART_NUM || param_->event_name == jrsaoi::SHORTCUT_ACT_PARTNUMBER_LOCATED_RUN_EVENT)
        {
            if (is_debugging)
            {
                JRSMessageBox_ERR("Error", "在线调试模式，不支持料号执行！", jrscore::MessageButton::Ok);
                return jrscore::ViewError::E_AOI_VIEW_ALGO_EXECUTE_FAILURE;
            }
            if (!cur_selected_spec_region)
            {
                JRSMessageBox_WARN("警告", "当前未选中料号！", jrscore::MessageButton::Ok);
                return -1;
            }

            auto param_temp = std::make_shared<jrsdata::AlgoEventParam>();
            param_temp->event_name = param_->event_name;
            SetCurSelectedToAlgoEventParam(param_temp);
            const auto& components = project_param_instance.GetProjectDataProcessInstance()->ReadAllComponent();
            for (auto& value : components)
            {
                if (value.get().component_part_number == param_temp->cur_select_spec_and_detect_region->part_name)
                {
                    param_temp->same_part_numb_components.push_back(&value.get());
                }
            }
            SigUpdateOperator(param_temp);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_POS_ALIGN)  //TODO 新增mark点对位的操作 by baron_zhang 2024-12-13
        {
            Log_INFO("Mark点定位测试");
            auto param_temp = std::make_shared<jrsdata::AlgoEventParam>();
            param_temp->event_name = jrsaoi::ACT_TEST_POS_ALIGN;
            SigUpdateOperator(param_temp);

        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_SAVE_STANDARD_IMG)
        {
            SaveStandardComponentLightImg();
        }
        else if (param_->event_name == jrsaoi::LOGIC_UPDATE_DEVICE_PARAM_EVENT)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::OperateViewParam>(param_);

            project_param_instance.GetProjectDataProcessInstance()->SetResolution(param_temp->device_param.struct_light_param.resolution_x,
                param_temp->device_param.struct_light_param.resolution_y);

        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            is_debugging = true;
        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            is_debugging = false;
            ErasAllResult();
        }
        else if (param_->event_name == jrsaoi::ONLINEDEBUG_CHANGE_DEBUG_COMPONENT_EVENT_NAME)
        {
            auto online_debug_param = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
            current_debug_info = online_debug_param->current_debug_info;
        }
        else if (param_->event_name == jrsaoi::ONLINEDEBUG_DEBUG_FINISHED_SEND_EVNET_NAME)
        {
            is_waitting_debug_info.store(true);
        }
        else if (param_->event_name == jrsaoi::DETECT_RESULT_ONLINE_DEBUG_INFO_EVENT_NAME)
        {
            if (is_debugging && is_waitting_debug_info.load())
            {
                auto online_debug_info = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
                if (!online_debug_info)
                {
                    return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
                }
                is_waitting_debug_info.store(false);
                ErasAllResult();
                for (auto& component_result : online_debug_info->online_debug_param->ng_component_detect_results)
                {
                    auto component_result_iter = component_result.second;
                    for (auto& detect_window_result : component_result_iter.detect_window_results)
                    {
                        auto component_ref = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(component_result_iter.component_name, component_result_iter.sub_board_name, jrsdata::Component::Type::CAD);

                        if (component_ref.has_value())
                        {
                            SetDetectWindowDetectResult(component_ref.value(), detect_window_result.first, detect_window_result.second);
                        }

                    }
                }
            }
        }
        else if (is_debugging && param_->event_name == jrsaoi::PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME)
        {
            JRSMessageBox_ERR("Error", "在线调试模式，无法加载整板图！", jrscore::MessageButton::Ok);

            return jrscore::ViewError::E_AOI_VIEW_READ_ENTIRY_IMAGE_FAILURE;

        }
        else if (param_->event_name == jrsaoi::MACHINE_PARAM_SAVE_EVENT)
        {
            auto param_process = project_param_instance.GetInstance().GetParameterProcessInstance();
            auto& setting_params = param_process->GetSettingParams();
            auto setting_param = std::dynamic_pointer_cast<jrsdata::SettingViewParam>(param_);
            if (!setting_param)
            {
                return -1;
            }
            setting_param->sys_param = setting_params->sys_param;
            setting_param->machine_param = setting_params->machine_param;
            setting_param->comm_param = setting_params->comm_param;
        }
        else if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME)
        {
            CheckRepairDataHasSaved();
        }
        else if (param_->event_name == jrsaoi::OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            param_temp->project_event_info.project_name_lists = project_param_instance.GetProjectDataProcessInstance()->GetProjectNameLists();
            param_temp->project_event_info.link_project_name = project_param_instance.GetProjectDataProcessInstance()->GetLinkedProjectName();
        }
        else if (param_->event_name == jrsaoi::OPERATE_CONFIRM_LINK_PROJECT_EVENT_NAME)
        {
            auto param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            project_param_instance.GetProjectDataProcessInstance()->SetCurrentProjectLink(param_temp->project_event_info.link_project_name);
        }
        else if (param_->event_name == jrsaoi::OPERATE_CANCLE_LINK_PROJECT_EVENT_NAME)
        {
            project_param_instance.GetProjectDataProcessInstance()->CancleCurrentProjectLink();
        }

        else if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME && param_->sub_name == jrsaoi::OPERATE_LOGIC_SUB_NAME
            || param_->event_name == jrsaoi::PROJECT_UPDATE_EVENT_NAME)
        {
            //! 清理选中的状态，包括选中的元件，检测框等
            ClearInnerMembersParam();
            //! 将所有的检测结果状态清除
            ErasAllResult();
        }
        return jrscore::AOI_OK;

    }
}
