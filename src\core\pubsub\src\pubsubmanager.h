/*****************************************************************//**
 * @file   pubsubmanager.h
 * @brief  发布订阅管理类
 * @details 单例模式   
 * <AUTHOR>
 * @date 2024.1.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.22         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __PUBSUBMANAGER_H__
#define __PUBSUBMANAGER_H__
#include <iostream>
#include "SingleTonT.hpp"
#include "module.h"
#include "pluginexport.hpp"
namespace jrscore
{
    class JRS_AOI_PLUGIN_API PubSubManager :public SingleTonT<PubSubManager>
    {
    public:
        /**
         * @fun AddModule 
         * @brief 添加模块
         * @param module_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        inline int AddModule(const std::string& module_name_);
        /**
         * @fun RemoveModule 
         * @brief 删除模块
         * @param module_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int RemoveModule (const std::string& module_name_);

        /**
         * @fun AddAdvertise 
         * @brief 添加主题
         * @param module_name_
         * @param topic_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int AddAdvertise(const std::string& module_name_, const std::string& topic_name_);
        /**
         * @fun RemoveAdvertise 
         * @brief 删除主题
         * @param module_name_
         * @param topic_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int RemoveAdvertise (const std::string& module_name_, const std::string& topic_name_);
        
        /**
         * @fun AddSubscriber 
         * @brief 添加订阅者
         * @param module_name_
         * @param topic_name_
         * @param call_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int AddSubscriber(const std::string& module_name_, const std::string& topic_name_, SubscribeCallbackHelperPtr& call_);
       
        
        /**
         * @fun RemoveSubscriber 
         * @brief 删除订阅者
         * @param module_name
         * @param topic_name_
         * @param call_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int RemoveSubscriber (const std::string& module_name, const std::string& topic_name_,SubscribeCallbackHelperPtr& call_);

        /**
         * @fun NotifyOne 
         * @brief 通知订阅者
         * @param module_name_
         * @param topic_name_
         * @param sub_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int NotifyOne(const std::string& module_name_, const std::string& topic_name_, const std::string& sub_name_, const std::vector<std::any>& args);
        
        /**
         * @fun NotifyAll 
         * @brief 通知所有订阅者
         * @param module_name_
         * @param topic_name_
         * @return 
         * @date 2024.1.22
         * <AUTHOR>
         */
        int NotifyAll(const std::string& module_name_, const std::string& topic_name_, const std::vector<std::any>& args);
        
    protected:
        friend class SingleTonT<PubSubManager>;
        PubSubManager ()=default;
        ~PubSubManager () =default;
    private:
        std::map<std::string, ModulePtr> module_map;
        
        std::recursive_mutex pub_sub_mutex;


    };

}

#endif // !__PUBSUBMANAGER_H__

