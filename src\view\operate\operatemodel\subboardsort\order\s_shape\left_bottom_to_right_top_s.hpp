/*****************************************************************
 * @file   left_bottom_to_right_top_s.hpp
 * @brief  s形状排序 从左下到右上
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class SLeftBottomToRightTop :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int subboard_id = 1;
            int current_row = 1; /**< control row direction */
            for (int row_num = static_cast<int>(subboards_.size()) - 1;row_num >= 0;--row_num)
            {
                auto& row_data = subboards_[row_num];
                if (current_row % 2 == 0)
                {
                    // 左到右
                    for (size_t col = 0; col < row_data.size(); ++col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
                else
                {
                    // 右到左
                    for (int col = static_cast<int>(row_data.size()) - 1; col >= 0; --col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
                ++current_row;
            }
            return jrscore::AOI_OK;
        }
    };
}
