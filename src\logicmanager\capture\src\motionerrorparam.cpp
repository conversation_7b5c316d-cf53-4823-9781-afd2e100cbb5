#include <fstream>
#include <cereal/archives/json.hpp>
#include <cereal/types/vector.hpp>
#include "motionerrorparam.h"

template <class Archive>
void serialize(Archive& archive, JrsPoint& pt)
{
  archive(
    cereal::make_nvp("x", pt.x),
    cereal::make_nvp("y", pt.y),
    cereal::make_nvp("z", pt.z));
}

template <class Archive>
void serialize(Archive& archive, MotionErrorParam& param)
{
  archive(
    cereal::make_nvp("x_start", param.x_start),
    cereal::make_nvp("x_end", param.x_end),
    cereal::make_nvp("y_start", param.y_start),
    cereal::make_nvp("y_end", param.y_end),
    cereal::make_nvp("x_n", param.x_n),
    cereal::make_nvp("y_n", param.y_n),
    cereal::make_nvp("x_ctrl_pts", param.x_ctrl_pts),
    cereal::make_nvp("y_ctrl_pts", param.y_ctrl_pts));
}

bool ReadMotionErrorParamFile(const string& file_path, MotionErrorParam& param)
{
  try
  {
    std::ifstream in(file_path, std::ios::in);
    if (in.is_open())
    {
      {
        cereal::JSONInputArchive archive_in(in);
        archive_in(param);
      }
      in.close();
    }
    else
    {
      std::cerr << "Failed to open file: " << file_path << '\n';
      return false;
    }
    return true;
  }
  catch(const std::exception& e)
  {
    std::cout<< e.what() <<std::endl;
    return false;
  }
}

bool WriteMotionErrorParamFile(const string& file_path, const MotionErrorParam& param)
{
  try
  {
    std::ofstream out(file_path, std::ios::out);
    if (out.is_open())
    {
      {
        cereal::JSONOutputArchive archive_in(out);
        archive_in(param);
      }
      out.close();
    }
    else
    {
      return false;
    }
    return true;
  }
  catch(const std::exception& e)
  {
    std::cout<< e.what() <<std::endl;
    return false;
  }
}