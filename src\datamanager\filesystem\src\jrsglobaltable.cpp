#include "jrsglobaltable.h"
#include<sstream>
std::map<std::string, jrsfiledata::JrsGlobalTable*> jrsfiledata::JrsGlobalTable::s_objMap;
std::mutex jrsfiledata::JrsGlobalTable::s_objMapMutex;

jrsfiledata::JrsGlobalTable* jrsfiledata::JrsGlobalTable::JrsGetObj(std::string objName)
{
    std::lock_guard<std::mutex> map_guard(s_objMapMutex);

    // 若映射表中已存在该名称的对象，则直接返回，否则重新构造对象，并添加到映射表中
    if (s_objMap.find(objName) != s_objMap.end())
    {
        return s_objMap[objName.c_str()];
    }

    jrsfiledata::JrsGlobalTable* pObj = new jrsfiledata::JrsGlobalTable(objName);
    s_objMap[objName] = pObj;

    return pObj;
}

void jrsfiledata::JrsGlobalTable::JrsDelObj(std::string objName)
{
    // 先检查映射表中是否存在该对象
    if (s_objMap.find(objName) == s_objMap.end())
    {
        return;
    }

    delete s_objMap[objName]; // 对象析构后会自动从映射表中移除
}

jrsfiledata::JrsGlobalTable::JrsGlobalTable(std::string objName)
    : m_objName(objName)
{
}

jrsfiledata::JrsGlobalTable::~JrsGlobalTable()
{
    std::lock_guard<std::mutex> map_guard(s_objMapMutex);
    s_objMap.erase(m_objName);
}

void jrsfiledata::JrsGlobalTable::SetGlobalTable(std::string jsonString)
{
    std::lock_guard<std::mutex> global_guard(m_mutex);
    m_globalTable = JSON::parse(jsonString);
}

std::string jrsfiledata::JrsGlobalTable::GetJsonString()
{
    std::lock_guard<std::mutex> global_guard(m_mutex);
    return m_globalTable.dump(4);
}

JSON jrsfiledata::JrsGlobalTable::GetValue(std::string keyPath, std::any defaultValue)
{
    JSON defult;
    if (defaultValue.type() == typeid(int))
    {
        defult = std::any_cast<int>(defaultValue);
    }
    else if (defaultValue.type() == typeid(std::string))
    {
        defult = std::any_cast<std::string>(defaultValue);
    }
    else if (defaultValue.type() == typeid(bool))
    {
        defult = std::any_cast<bool>(defaultValue);
    }
    else if (defaultValue.type() == typeid(double))
    {
        defult = std::any_cast<double>(defaultValue);
    }

    std::lock_guard<std::mutex> global_guard(m_mutex);
    if (m_globalTable.is_null())
    {
        return defult;
    }

    JSON temp = m_globalTable;
    auto keys = SplitString(keyPath, '.');
    for (int i = 0; i < keys.size(); ++i)
    {
        if (temp.is_null())
        {
            return defult;
        }
        if (IsInteger(keys[i]))
        {
            int index = std::stoi(keys[i]);
            if (temp.is_array() && temp.size() > index)
            {
                temp = temp[index];
            }
            else
            {
                return defult;
            }
        }
        else
        {
            std::string key = keys[i];
            if (temp.contains(key))
            {
                temp = temp[keys[i]];
            }
            else
            {
                return defult;
            }
        }
    }
    return temp;
}

void jrsfiledata::JrsGlobalTable::UpdateValue(const std::string& jsonStr, const std::string& basePath)
{
    // 解析传入的JSON字符串
    JSON doc;
    try
    {
        doc = JSON::parse(jsonStr);
    }
    catch (const JSON::parse_error& e)
    {
        std::cout << "Parse error: " << e.what() << std::endl;
        return;
    }

    // 更新基础值
    JSON& baseValue = GetElementByPath(m_globalTable, basePath);
    for (auto& [key, value] : doc.items())
    {
        baseValue[key] = value;
    }
}

void jrsfiledata::JrsGlobalTable::SaveSetting(const std::string filePath)
{
    std::string json_string = GetJsonString();
    // 尝试以写入模式打开文件（如果文件已存在，它将被覆盖）
    std::ofstream outfile(filePath, std::ios::out | std::ios::trunc); // 使用std::ios::trunc来截断文件内容
    if (!outfile.is_open())
    {
        std::cerr << "Unable to open file for writing!" << std::endl;
    }
    // 写入新的JSON字符串
    outfile << json_string;

    // 关闭文件
    outfile.close();
}

std::string jrsfiledata::JrsGlobalTable::ReadSetting(const std::string filePath)
{
    // 创建一个ifstream对象来读取文件
    std::ifstream file(filePath);

    // 检查文件是否成功打开
    if (!file.is_open())
    {
        return "";
    }

    // 创建一个字符串来存储文件内容
    std::string fileContent((std::istreambuf_iterator<char>(file)),
        std::istreambuf_iterator<char>());

    // 关闭文件
    file.close();

    return fileContent;
}

std::vector<std::string> jrsfiledata::JrsGlobalTable::SplitString(const std::string& s, char delimiter)
{
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(s);
    while (std::getline(tokenStream, token, delimiter))
    {
        tokens.push_back(token);
    }
    return tokens;
}

bool jrsfiledata::JrsGlobalTable::IsInteger(const std::string& s)
{
    int n;
    std::istringstream iss(s);
    iss >> n;

    // 确保读取成功且之后没有多余字符
    return iss.eof() && !iss.fail();
}

JSON& jrsfiledata::JrsGlobalTable::GetElementByPath(JSON& root, const std::string& path)
{
    std::stringstream ss(path);
    std::string segment;
    JSON* current = &root;

    while (getline(ss, segment, '.'))
    {
        // 尝试将segment转换为整数，以处理数组索引
        try {
            size_t index = std::stoul(segment); // 将字符串转换为无符号长整数
            // 如果当前JSON对象是一个数组，并且index在数组范围内
            if (current->is_array() && index < current->size())
            {
                current = &(*current)[index];
            }
            else
            {
                // 如果不是数组或者索引超出范围，则创建一个新的JSON对象
                if (!current->contains(segment)) {
                    (*current)[segment] = JSON::object();
                }
                current = &(*current)[segment];
            }
        }
        catch ([[maybe_unused]] const std::invalid_argument& e) {
            // 如果segment不能转换为整数，则将其视为键名
            if (!current->contains(segment))
            {
                (*current)[segment] = JSON::object();
            }
            current = &(*current)[segment];
        }
        catch ([[maybe_unused]] const std::out_of_range& e)
        {
            // 处理整数溢出的情况
            std::cout << "Index out of range: " << segment << std::endl;
            // 可以选择抛出一个异常或者创建一个新的JSON对象
            if (!current->contains(segment))
            {
                (*current)[segment] = JSON::object();
            }
            current = &(*current)[segment];
        }
    }

    return *current;
}