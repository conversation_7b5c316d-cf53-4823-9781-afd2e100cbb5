#include "operatorviewbase.h"

namespace jrsoperator
{
    OperatorViewBase::OperatorViewBase(const std::string& operator_name_, QWidget* parent)
        :QWidget(parent)
        ,operator_name(operator_name_)
    {
    }
    const std::string& OperatorViewBase::GetOperatorName() const
    {
        return operator_name;
    }
    void OperatorViewBase::SetExecuteCallBack(ExecuteCallBack cb_)
    {
        execute_cb = cb_;
    }
    void OperatorViewBase::SetSaveCallBack(SaveCallBack cb_)
    {
        save_cb = cb_;
    }
}