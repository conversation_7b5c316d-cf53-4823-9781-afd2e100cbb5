//#include "algoexecuteparamprocess.h"
//#include "operatorparambase.h"
//#include "jsonoperator.hpp"
//bool jrsparam::AlgoExecuteParamProcess::GetAlgoExecuteResultStatus(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_res_)
//{
//    bool res_status = true;
//    res_status = std::none_of(detect_res_->output_detect_rects.begin(), detect_res_->output_detect_rects.end(),
//        [](const auto& value) { return !value.status; }) && !detect_res_->output_detect_rects.empty();
//    return res_status;
//}
//
//bool jrsparam::AlgoExecuteParamProcess::GetAlgoExecuteResultStatusTemp(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_res_, float& mark_score_)
//{
//    bool res_status = true;
//    auto res_detect = std::dynamic_pointer_cast<iguana::base>(detect_res_);
//    std::string json_res;
//    res_detect->to_json(json_res);
//    mark_score_ = std::stof(jrscore::ParseJson(json_res, "mark_score_"));
//    auto res_mark_min_score = std::stof(jrscore::ParseJson(json_res, "mark_min_score_"));
//
//    if (mark_score_ < res_mark_min_score)
//    {
//        res_status = false;
//    }
//
//    return res_status;
//}

