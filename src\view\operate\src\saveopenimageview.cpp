﻿

#include "ui_saveopenimageview.h"
#include "saveopenimageview.h"

namespace jrsaoi
{
    struct SelectImageGroupInfo
    {
        QString image_group_path;
        QString image_group_name;
        QString image_group_time;

        SelectImageGroupInfo(QString path, QString name, QString time)
        {
            image_group_path = path;
            image_group_name = name;
            image_group_time = time;
        }
    };

    SaveOpenImageView::SaveOpenImageView(QWidget* parent)
        : QWidget(parent), ui(new Ui::SaveOpenImageView)
    {
        setWindowFlags(Qt::WindowStaysOnTopHint);  // 设置窗体总在最上层
        setAttribute(Qt::WA_TransparentForMouseEvents);  // 允许点击后面的窗体
        ui->setupUi(this);
        Init();
    }

    SaveOpenImageView::~SaveOpenImageView()
    {
        delete ui;
        ui = nullptr;
    }

    void SaveOpenImageView::SetCurrentProjectPathAndName(const std::string& project_path_, const std::string& project_name_)
    {
        ui->qline_save_img_path->setText(QString::fromStdString(project_path_));
        //获取工程路径下的所有组名
        UpdateProjectFolderNames(project_path_);
        auto current_project_name_index = ui->comboBox_project_folder_names->findText(QString::fromStdString(project_name_));
        ui->comboBox_project_folder_names->setCurrentIndex(current_project_name_index);
        ui->pushbutton_filter_save_img_project->clicked();
    }

    void SaveOpenImageView::Init()
    {
        InitShowListHeader();
        InitListView();
        InitMember();
        InitConnect();


    }

    void SaveOpenImageView::InitListView()
    {
        ui->list_image_group->verticalHeader()->setVisible(false);
        ui->list_image_group->setStyle(new CustomStyle());
        std::vector<int> column_width_product = { 80,50 };
        list_image_group_model.setRowCount(1);
        list_image_group_model.setColumnCount(2);
        list_image_group_model.setHorizontalHeaderLabels(header_list);
        ui->list_image_group->setModel(&list_image_group_model);
        ui->list_image_group->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
        ui->list_image_group->setSelectionBehavior(QAbstractItemView::SelectRows);
        list_image_group_model.removeRow(0);
        InitTableViewColumnWidth(ui->list_image_group, column_width_product);
        ui->list_image_group->setEditTriggers(QAbstractItemView::NoEditTriggers);

        ui->pushbutton_select_save_img_path->setHidden(true);
        ui->qline_save_img_path->setReadOnly(true);

        this->setWindowFlags(this->windowFlags() | Qt::WindowStaysOnTopHint);
    }

    void SaveOpenImageView::InitMember()
    {
        // 创建 QCompleter
        project_folder_name_model = new QStringListModel(project_folder_names, this);
        project_folder_name_completer = new QCompleter(project_folder_name_model, this);
        project_folder_name_completer->setCaseSensitivity(Qt::CaseInsensitive); // 设置大小写不敏感
        project_folder_name_completer->setCompletionMode(QCompleter::PopupCompletion); // 设置补全模式为弹出窗口
        project_folder_name_completer->setFilterMode(Qt::MatchContains);
        ui->comboBox_project_folder_names->setCompleter(project_folder_name_completer);

        project_group_image_name_model = new QStringListModel(project_group_names, this);
        project_group_image_name_completer = new QCompleter(project_group_image_name_model, this);
        project_group_image_name_completer->setCaseSensitivity(Qt::CaseInsensitive); // 设置大小写不敏感
        project_group_image_name_completer->setCompletionMode(QCompleter::PopupCompletion); // 设置补全模式为弹出窗口
        project_group_image_name_completer->setFilterMode(Qt::MatchContains);
        ui->qline_image_notes->setCompleter(project_group_image_name_completer);
    }

    void SaveOpenImageView::InitShowListHeader()
    {
        header_list.append(QString::fromWCharArray(L"名称"));
        header_list.append(QString::fromWCharArray(L"时间"));
    }

    void SaveOpenImageView::InitTableViewColumnWidth(QTableView* table_view, std::vector<int> column_widths)
    {
        if (table_view == nullptr)
        {
            return;
        }
        for (int i = 0; i < table_view->model()->columnCount() && i < column_widths.size(); i++)
        {
            table_view->setColumnWidth(i, column_widths.at(i));
        }
    }

    void SaveOpenImageView::InitConnect()
    {
        connect(ui->pushbutton_select_save_img_path, &QPushButton::clicked, this, &SaveOpenImageView::SlotOpenImageDirectory);
        //connect(ui->list_image_group, &QTableView::clicked, this, &SaveOpenImageView::SlotListViewImageFile);
        connect(ui->pushbutton_confirm, &QPushButton::clicked, this, &SaveOpenImageView::SlotSelectImageFile);
        connect(ui->pushbutton_cancel, &QPushButton::clicked, this, &SaveOpenImageView::SlotCancelSelectImageFile);
        connect(ui->pushbutton_open, &QPushButton::clicked, this, &SaveOpenImageView::SlotOpenImageGroup);
        connect(ui->comboBox_project_folder_names, SIGNAL(currentIndexChanged(int)), this, SLOT(SlotSaveImageProjectNameChanged(int)));
        connect(ui->pushbutton_filter_save_img_project, &QPushButton::clicked, this, &SaveOpenImageView::SlotFilterSaveImageProject);
        connect(ui->list_image_group, &QTableView::clicked, this, &SaveOpenImageView::SlotListImageGroupTableRowClicked);
    }

    void SaveOpenImageView::SlotOpenImageDirectory()
    {
        Log_INFO(__FUNCTION__, " 打开工程文件夹");
        QString dirPath = QFileDialog::getExistingDirectory(nullptr, "选择工程文件夹", QDir::homePath());
        ui->qline_save_img_path->setText(dirPath);
    }

    void SaveOpenImageView::SlotSelectImageFile()
    {
        QString image_group_name = ui->qline_image_notes->text();/*list_image_group_model.item(row)->text();*/
        if (image_group_name.isEmpty())
        {
            Log_ERROR(__FUNCTION__, "当前组名为空");
            return;
        }
        if (ui->checkbox_auto_serial_num->isChecked())
        {
            image_group_name += QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
        }
        emit SignalSaveImageGroup(image_group_name.toStdString());
    }

    void SaveOpenImageView::SlotCancelSelectImageFile()
    {
        this->close();
    }

    void SaveOpenImageView::SlotOpenImageGroup()
    {
        Log_INFO(__FUNCTION__, " 打开工程");
        QString save_image_path = ui->qline_save_img_path->text();
        //QString project_name = ui->comboBox_project_folder_names->text();
        //获取list_image_group_model中的数据，并获取当前选中的行，获取选中行的数据，并获取选中行的路径，打开该路径
        int row = ui->list_image_group->currentIndex().row();
        if (row < 0)
        {
            Log_ERROR(__FUNCTION__, " 未选中行");
            return;
        }
        //获取选中行中第一列的数据
        QString image_group_name = list_image_group_model.item(row)->text();
        ////判断save_image_path和project_name 加上image_group_name的路径是否存在
        emit SignalOpenImageGroup(image_group_name.toStdString());
    }

    void jrsaoi::SaveOpenImageView::SlotSaveImageProjectNameChanged(int index_)
    {
        (void)index_;
        ////这里当工程名称改变时，重新获取分组名称，并更新下拉列表
        //QString project_name = ui->comboBox_project_folder_names->currentText();
        //QString save_image_path = ui->qline_save_img_path->text();
        SlotFilterSaveImageProject();
        /*SlotGetImageGroupName(save_image_path, project_name);*/
    }


    void jrsaoi::SaveOpenImageView::SlotFilterSaveImageProject()
    {
        //过滤当前路径下的文件夹名称，如果名称和工程名一致，在添加到image_group_info_list中
        QString project_name = ui->comboBox_project_folder_names->currentText();
        QString save_image_path = ui->qline_save_img_path->text();
        QDir dir(save_image_path + "\\" + project_name);
        if (!dir.exists())
        {
            Log_ERROR(__FUNCTION__, " 路径不存在");
            return;
        }
        project_group_names.clear();
        image_group_info_list.clear();
        QFileInfoList dirList = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot, QDir::Time);
        foreach(const QFileInfo & info, dirList)
        {
            image_group_info_list.append(SelectImageGroupInfo(info.path(), info.fileName(), info.lastModified().toString("yyyy-MM-dd HH:mm")));
            project_group_names << info.fileName();
        }
        SlotUpdateImageGroupList();
    }

    void jrsaoi::SaveOpenImageView::SlotUpdateImageGroupList()
    {
        Log_INFO(__FUNCTION__, " 更新工程列表");
        list_image_group_model.clear();
        list_image_group_model.setColumnCount(2);
        list_image_group_model.setHorizontalHeaderLabels(header_list);

        for (int row = 0; row < image_group_info_list.size(); ++row)
        {
            QList<QStandardItem*> rowItems;
            rowItems << new QStandardItem(image_group_info_list.at(row).image_group_name) << new QStandardItem(image_group_info_list.at(row).image_group_time);
            list_image_group_model.appendRow(rowItems);
        }
        if (list_image_group_model.rowCount() > 0)
        {
            ui->list_image_group->setFocus();
            ui->list_image_group->setCurrentIndex(list_image_group_model.index(0, 0));
            ui->list_image_group->selectRow(0);
            ui->list_image_group->update();
            SlotListImageGroupTableRowClicked(list_image_group_model.index(0, 0));
        }
    }

    void SaveOpenImageView::SlotListImageGroupTableRowClicked(const QModelIndex& index_)
    {
        auto item = list_image_group_model.item(index_.row(), 0);
        ui->qline_image_notes->setText(item->text());


    }

    QFileInfoList SaveOpenImageView::GetAllFolderNamesByCurrentPath(const std::string& project_path_)
    {
        //过滤并获取当前文件夹下面名称为project_name的文件夹
        QDir dir_temp(QString::fromStdString(project_path_));
        //判断文件夹是否存在
        if (!dir_temp.exists())
        {
            return QFileInfoList();
        }
        return dir_temp.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    }

    void SaveOpenImageView::UpdateProjectFolderNames(const std::string& project_path_)
    {
        project_folder_names.clear();
        ui->comboBox_project_folder_names->clear();
        auto folders_info = GetAllFolderNamesByCurrentPath(project_path_);
        for (auto folder_info : folders_info)
        {
            project_folder_names << folder_info.fileName();
        }
        project_folder_name_model->setStringList(project_folder_names);
        ui->comboBox_project_folder_names->insertItems(0, project_folder_names);
    }



}
