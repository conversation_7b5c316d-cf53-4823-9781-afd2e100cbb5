<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddMarkView</class>
 <widget class="QWidget" name="AddMarkView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>207</width>
    <height>151</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>新增定位点</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QRadioButton" name="radio_btn_entirety_mark">
       <property name="text">
        <string>整板mark</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radio_btn_subboard_mark">
       <property name="text">
        <string>子板mark</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QRadioButton" name="radio_btn_shape">
         <property name="text">
          <string>形状</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="radio_btn_image">
         <property name="text">
          <string>图像</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="radio_btn_convert_mark">
         <property name="text">
          <string>转换为定位点</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QFrame" name="shape_frame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_shape">
          <item>
           <widget class="QComboBox" name="comboBox_shapes">
            <item>
             <property name="text">
              <string>圆形</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>矩形</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="label">
              <property name="text">
               <string>直径：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="spinBox_length"/>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QRadioButton" name="radio_btn_light">
              <property name="text">
               <string>亮</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radio_btn_dark">
              <property name="text">
               <string>暗</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <widget class="QPushButton" name="btn_identify">
       <property name="text">
        <string>识别</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_frame_select">
       <property name="text">
        <string>框选</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
