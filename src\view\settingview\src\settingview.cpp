﻿// Custom
#include "settingview.h"

namespace jrsaoi
{

    SettingView::SettingView(const std::string& name, QWidget* parent)
        : ViewBase(name, parent)
    {
      Init();
    }

    SettingView::~SettingView()
   {
   }

   int SettingView::Init()
   {
      try
      {
         InitMember();
         InitView();
         InitConnect();
      }
      catch (std::exception& e)
      {
         std::cout << __FUNCTION__ << " " << __LINE__ << " "<< e.what() << std::endl;
      }

      return jrscore::AOI_OK;
   }
   int SettingView::UpdateView(const jrsdata::ViewParamBasePtr&param_)
   {
        if (!param_)
        {
            Log_ERROR("参数为空指针！");
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        auto temp_param = std::static_pointer_cast<jrsdata::SettingViewParam>(param_);
        _setting_view_param_ptr = temp_param;


        if (param_->event_name == jrsaoi::SHOW_SETTING_VIEW_EVENT_NAME)
        {
            this->show();
            return jrscore::AOI_OK;
        }
		else if (param_->event_name == jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME)
		{
            _sys_param_view->UpdateView(temp_param->sys_param.sys_params);
            _common_param_view->UpdateView(temp_param->comm_param.comm_params);
        }
        else if (param_->event_name ==jrsaoi::UPDATE_COMMON_PARAM_EVENT)
        {
            _common_param_view->UpdateView(temp_param->comm_param.comm_params);
        }
   
      return jrscore::AOI_OK;
   }
   int SettingView::Save(const jrsdata::ViewParamBasePtr&param_)
   {
       if (!param_)
       {
           return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
       }

       return jrscore::AOI_OK;
   }
   void SettingView::InitMember()
   {
       _setting_widget = new QTabWidget(this);
       _sys_param_view = new SystemParamView(this);
       //_algorithm_param_view = new AlgorithmParamView(this);
       //_motion_param_view = new MotionParamView(this);
       _common_param_view = new CommonParamView(this);
       //_vision_param_view = new VisionParamView(this);
       //_motion_param_view = new MotionParamView(this);
   }
   void SettingView::InitView()
   {
      this->setMinimumSize(800, 600);
      this->setWindowIcon(QIcon(":/image/JRS.ico"));

      Log_INFO("初始化系统参数widget");

      QVBoxLayout* v_layout_center_widget = new QVBoxLayout(this);

      _setting_widget->insertTab(0, _sys_param_view,"系统设置");
      //_setting_widget->insertTab(1, _motion_param_view,"运控设置");
      _setting_widget->insertTab(1, _common_param_view, "工程设置");
      //_setting_widget->insertTab(3, _algorithm_param_view, "算法设置");
      //_setting_widget->insertTab(4, _vision_param_view, "视觉设置");

      v_layout_center_widget->addWidget(_setting_widget);
      
   }




   void SettingView::InitConnect()
   {
       connect(_sys_param_view, &SystemParamView::SigSaveSystem, this, [this](const jrsdata::SettingParamMap& system_params_) {
           _setting_view_param_ptr->sys_param.sys_params = system_params_;
           _setting_view_param_ptr->event_name = jrsaoi::SYSTEM_PARAM_SAVE_EVENT;
           emit SigSave(_setting_view_param_ptr);
           JRSMessageBox_INFO( "参数保存", "参数已修改，请重启软件",jrscore::MessageButton::Ok);
           });

	   connect(_common_param_view, &CommonParamView::SigSaveParam, this, [this](const jrsdata::SettingParamMap& common_params_) {
		   _setting_view_param_ptr->comm_param.comm_params = common_params_;
		   _setting_view_param_ptr->event_name = jrsaoi::COMMON_PARAM_SAVE_EVENT;
		   emit SigSave(_setting_view_param_ptr);
		   });

	   connect(_common_param_view, &CommonParamView::SigUpdateParam, this, [this]() {
		   _setting_view_param_ptr->event_name = jrsaoi::UPDATE_COMMON_PARAM_EVENT;
		   emit SigRefresh(_setting_view_param_ptr);
		   });

   }

   void SettingView::SlotTabChanged(int index)
   {
	   std::cout << __FUNCTION__ << " " << __LINE__ << " "<< index << std::endl;
   }
}
