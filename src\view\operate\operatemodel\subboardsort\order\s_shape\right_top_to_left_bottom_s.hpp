/*****************************************************************
 * @file   right_top_to_left_bottom_s.hpp
 * @brief  从右上到左下 s形状排序
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class SRightTopToLeftBottom :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int subboard_id = 1;
            for (size_t row = 0; row < static_cast<int>(subboards_.size()); ++row)
            {
                auto& row_data = subboards_[row];
                if (row % 2 == 0)
                {
                    // 左到右
                    for (size_t col = 0; col < row_data.size(); ++col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
                else
                {
                    // 右到左
                    for (int col = static_cast<int>(row_data.size()) - 1; col >= 0; --col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
            }
            return jrscore::AOI_OK;
        }

    };
}