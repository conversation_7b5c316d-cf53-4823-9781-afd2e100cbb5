/*****************************************************************
 * @file   invokealgoengine.h
 * @brief  用于在逻辑层中调用算法引擎
 * @details
 * <AUTHOR>
 * @date 2024.10.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSINVOKEALGOENGINE_H__
#define __JRSINVOKEALGOENGINE_H__
//STD
#include <iostream>
//Custom
//Third
namespace jrsalgo
{
    class AlgorithmEngineManager;
}
namespace jrslogic 
{
   
    struct InvokeDataImpl;
    class InvokeAlgoEngine
    {
        public:
        InvokeAlgoEngine ();
        ~InvokeAlgoEngine ();

        const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& GetAlgoEngineManager ();

        private:
        //Fun
        void InitMember ();


        //Member
        InvokeDataImpl* invoke_data_impl;
    };
    using InvokeAlgoEnginePtr = std::shared_ptr<InvokeAlgoEngine>;
}


#endif // !__JRSINVOKEALGOENGINE_H__

