﻿#include "motiondebugview.h"
#include <iostream>

namespace jrsaoi
{
    MotiondebugView::MotiondebugView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::MotiondebugView())
    {
        ui->setupUi(this);
        InitView();
        InitConnect();
        motiondebug_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
    }
    MotiondebugView::~MotiondebugView()
    {
        delete ui;
    }
    void MotiondebugView::UpdateView(const jrsdata::OperateViewParamPtr ptr)
    {
        if (ptr->event_name == jrsaoi::SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME)
        {
            // 从QSettings中读取位置
            QSettings settings("SuZhouJRS", "jrsaoi");
            QScreen* screen = QGuiApplication::primaryScreen();
            QRect screenGeometry = screen->geometry();
            QPoint center = screenGeometry.center();

            QPoint position = settings.value("DialogGeometry", QPoint(center.x() - dialog->width() / 2, center.y() - dialog->height() / 2)).toPoint();
            dialog->move(position);
            dialog->show();
        }
        else
        {
            axis_move_widget->UpdateView(ptr);
            track_debug_widget->UpdateView(ptr);
        }
    }
    void MotiondebugView::InitView()
    {
        axis_move_widget = new AxisMove();
        track_debug_widget = new TrackDebug();
        ui->verticalLayout->addWidget(track_debug_widget);
        QSpacerItem* verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);
        ui->verticalLayout->addSpacerItem(verticalSpacer);
        dialog = new QDialog(this);
        dialog->setFixedSize(460, 210);
        QVBoxLayout* layout = new QVBoxLayout(dialog);
        layout->addWidget(axis_move_widget);

        // QDialog绑定关闭信号的处理,记录关闭时的位置
        connect(dialog, &QDialog::rejected, this, [=]() {
            QSettings settings("SuZhouJRS", "jrsaoi");
            settings.setValue("DialogGeometry", dialog->pos());
            });
    }
    void MotiondebugView::InitConnect()
    {
        connect(axis_move_widget, &AxisMove::SigMotionDebugTrigger, this, &MotiondebugView::SlotPushButtonTrigger);
        connect(axis_move_widget, &AxisMove::SigCurrentAxisPos, this, &MotiondebugView::SigCurrentAxisPos);
        connect(track_debug_widget, &TrackDebug::SigMotionDebugTrigger, this, &MotiondebugView::SlotPushButtonTrigger);
    }
    void MotiondebugView::SlotPushButtonTrigger(jrsdata::OperateViewParamPtr operateparam)
    {
        motiondebug_view_ptr->device_param = operateparam->device_param;
        emit SigMotionDebugTrigger(motiondebug_view_ptr);
    }
}