/*****************************************************************//**
 * @file   onedetectview.h
 * @brief  一个检测框界面，方便根据轨道数目添加界面
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef ONEDETECTVIEW_H
#define ONEDETECTVIEW_H

 //PREBUILD
#include "pch.h"
#pragma warning(push, 3)
 //QT
#include <QButtonGroup>
#include <QTimer>
#include "ui_onedetectview.h"
#pragma warning(pop)
// CUSTOM
//#include "trackstatus.h"
#include "detectprocessed.h"
#include "showmarkresultimagewidget.h"
namespace Ui {
    class OneDetectView;
}

class OneDetectView : public QWidget
{
    Q_OBJECT
public:
    /**
     * @fun OneDetectView
     * @brief 构造函数
     * @param track_index
     * @param parent
     * @date 2025.2.26
     * <AUTHOR>
     */
    explicit OneDetectView(jrsdata::TRACK_NUMBER track_index = jrsdata::TRACK_1, QWidget* parent = nullptr);
    /**
     * @fun ~OneDetectView
     * @brief 析构函数
     * @date 2025.2.26
     * <AUTHOR>
     */
    ~OneDetectView();
    /**
     * @fun Init
     * @brief 初始化
     * @date 2024.2.7
     * <AUTHOR>
     */
    void Init();
signals:
    /**
     * @fun SigDetectViewChangeTrigger
     * @brief 清除历史检测结果消息
     * @param param
     * @date 2024.9.19
     * <AUTHOR>
     */
    void SigDetectViewChangeTrigger(const jrsdata::ViewParamBasePtr& param_);
private:
    static const int SHOW_NG_TYPE_COUNT = 5;
    /**
     * @fun UpdateProjectView
     * @brief 更新工程界面数据
     * @date 2024.2.6
     * <AUTHOR>
     */
    void UpdateProjectView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun UpdateMarkResultView 
     * @brief 刷新mark图片
     * @param param
     * @date 2025.5.12
     * <AUTHOR>
     */
    void UpdateMarkResultView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun UpdateBoardDetectResultView
     * @brief 更新主板检测结果表格
     * @param param
     * @date 2025.2.19
     * <AUTHOR>
     */
    void UpdateBoardDetectResultView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun UpdateDetectDeviceView
     * @brief 更新检测NG元件比例表格
     * @param param
     * @date 2025.2.19
     * <AUTHOR>
     */
    void UpdateDetectDeviceView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun UpdateDetectNgTypeView
     * @brief 更新检测NG缺陷名称比例表格
     * @param param
     * @date 2025.2.19
     * <AUTHOR>
     */
    void UpdateDetectNgTypeView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun UpdateDetectResultRatioView
     * @brief 更新检测结果直通率图
     * @param param
     * @date 2025.2.19
     * <AUTHOR>
     */
    void UpdateDetectResultRatioView(const jrsdata::OperateViewParamPtr param);
    /**
     * @fun ClearProjectView
     * @brief 清除界面数据
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ClearProjectView();
    /**
     * @fun ClearTableResult
     * @brief 清除表格结果
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ClearTableResult();
    /**
      * @fun ClearNgTypeAndDeviceResult
      * @brief 清除缺陷横向柱状图
      * @date 2024.2.6
      * <AUTHOR>
      */
    void ClearNgTypeAndDeviceResult();
    /**
     * @fun ClearBoardResultRatio
     * @brief 清除统计结果竖向柱状图
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ClearBoardResultRatio();
    /**
     * @fun ConnectSlots
     * @brief 关联槽函数
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ConnectSlots();

    /**
     * @fun InitTableView
     * @brief 初始化TableView
     * @date 2024.2.6
     * <AUTHOR>
     */
    void InitTableView();
    /**
     * @fun InitShowData
     * @brief 初始化显示数据
     * @date 2024.2.6
     * <AUTHOR>
     */
    void InitShowData();
    /**
     * @fun InitShowResultData
     * @brief 初始化显示结果数据
     * @date 2024.2.6
     * <AUTHOR>
     */
    void InitShowResultData();

    /**
     * @fun InitStartTime
     * @brief 初始化开机时间
     * @date 2025.2.19
     * <AUTHOR>
     */
    void InitStartTime();
    /**
     * @fun UpdateRunTime
     * @brief 更新运行时间（一秒更新一次）
     * @date 2025.2.19
     * <AUTHOR>
     */
    void UpdateRunTime();
    /**
     * @fun UpdateBoardResultRatioView
     * @brief 刷新主板和子板直通率 NG率
     * @date 2025.2.24
     * <AUTHOR>
     */
    void UpdateBoardResultRatioView();
    /**
     * @fun UpdateNgDeviceAndTypeView
     * @brief 刷新NG元件和NG错误类型
     * @date 2025.2.24
     * <AUTHOR>
     */
    void UpdateNgDeviceAndTypeView();
    /**
    * @fun GetDetectDatas
    * @brief 根据显示结果类型获取相应的检测数据
    * @param show_result_type 显示结果类型
    * @param detect_processed 检测处理对象指针
    * @return 检测数据向量
    * @date 2024.07.26
    * <AUTHOR>
    */
    std::vector<RingData> GetDetectDatas(SHOW_DETECT_NG_TYPE show_result_type, DetectProcessed* detect_processed);
    /**
     * @fun GetTopNData
     * @brief 获取前N个检测数据
     * @param detect_datas 检测数据向量
     * @param n 要获取的前N个数据
     * @return 前N个检测数据向量
     * @date 2024.07.26
     * <AUTHOR>
     */
    std::vector<RingData> GetTopNData(const std::vector<RingData>& detect_datas, int n);
    /**
     * @fun CalculateSumOfRemainingData
     * @brief 计算剩余检测数据的总和
     * @param detect_datas 检测数据向量
     * @param start_index 开始计算总和的索引
     * @return 剩余检测数据的总和
     * @date 2024.07.26
     * <AUTHOR>
     */
    double CalculateSumOfRemainingData(const std::vector<RingData>& detect_datas, int start_index);
    /**
     * @fun UpdateDetectResultData
     * @brief 刷新检测框结果，分主板和子板
     * @date 2025.2.24
     * <AUTHOR>
     */
    void UpdateDetectResultData();
public slots:
    /**
     * @fun EditTicketNumFinished
     * @brief 工单号编辑结束
     * @date 2024.2.6
     * <AUTHOR>
     */
    void EditTicketNumFinished();
    /**
     * @fun ClearActionRatio
     * @brief 清除直通图
     * @date 2024.2.6
     * <AUTHOR>
     */
    void ClearActionRatio();
    /**
     * @fun ClearActionDevice
     * @brief 清除元件表和横向柱状图
     * @date 2025.2.20
     * <AUTHOR>
     */
    void ClearActionDevice();
    /**
    * @fun OnDeviceToggled
    * @brief 元件
    * @param state
    * @date 2024.2.6
    * <AUTHOR>
    */
    void OnDeviceToggled(bool checked);
    /**
     * @fun OnErrorTypeToggled
     * @brief 错误类型
     * @param state
     * @date 2024.2.6
     * <AUTHOR>
     */
    void OnErrorTypeToggled(bool checked);
    /**
    * @fun UpdateView
    * @brief 更新界面参数
    * @param ptr
    * @date 2024.8.22
    * <AUTHOR>
    */
    void UpdateView(const jrsdata::OperateViewParamPtr ptr);
    /**
     * @fun UpdateMotionSettings
     * @brief 更新运动设置
     * @param ptr
     * @date 2025.2.20
     * <AUTHOR>
     */
    void UpdateMotionSettings(const jrsdata::OperateViewParamPtr ptr);
    /**
     * @fun UpdateDetectionStatistics
     * @brief 更新检测统计信息
     * @param ptr
     * @date 2025.2.20
     * <AUTHOR>
     */
    void UpdateDetectionStatistics(const jrsdata::OperateViewParamPtr ptr);
    /**
     * @fun GetTrackIndex
     * @brief 获取轨道号
     * @return
     * @date 2025.1.14
     * <AUTHOR>
     */
    jrsdata::TRACK_NUMBER GetTrackIndex();
    /**
     * @fun OnRunTimeTimeout
     * @brief 运行时间
     * @date 2025.2.19
     * <AUTHOR>
     */
    void OnRunTimeTimeout();
    void CustomTableLeftClicked(const QModelIndex& index);
private:
    Ui::OneDetectView* ui;                      /// 界面
    DetectProcessed* m_detect_processed;        /// 检测界面数据
    QButtonGroup* m_group_ng_type;              /// 显示缺陷原因类型的按钮组
    //std::shared_ptr<TrackStatus> track_states;  /// 轨道状态
    jrsdata::TRACK_NUMBER m_track_index;        /// 轨道号
    long run_time_second;                       /// 运行时间的累积时间 单位秒
    QTimer* run_timer;                          /// 运行时间的定时器
    ShowMarkResultImageWidget* show_mark_result_image_widget;
};
#endif // ONEONEDETECTVIEW_H
