﻿/*****************************************************************
 * @file   jsonoperator.hpp
 * @brief  json操作工具类
 * @details 当前封装了nlohmann类 TODO：要抽象出接口，适用于各类的json库
 * <AUTHOR>
 * @date 2024.10.11
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.11          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSJSONOPERATOR_HPP__
#define __JRSJSONOPERATOR_HPP__
 //STD
#include <iostream>
#include <fstream>
//Custom
//Third
#include "nlohmann/json.hpp"

namespace jrscore
{
    /**
     * @fun ReadJson
     * @brief 读取指定路径下的json文件
     * @param json_path_ [IN] json存放路径
     * @return 返回读取后的json格式的数据
     * <AUTHOR>
     * @date 2024.10.11
     */
    inline  nlohmann::json ReadJson(const std::string& json_path_)
    {
        try
        {
            std::ifstream input_file(json_path_);
            nlohmann::json json_data;
            if (!input_file.is_open())
            {
                std::cerr << "无法打开文件:" << json_path_ << std::endl;
                return json_data;
            }

            // 读取并解析 JSON 文件

            input_file >> json_data;
            input_file.close();
            return json_data;
        }
        catch (const std::exception&)
        {
            std::cerr << "打开解析json失败" << std::endl;
            return nlohmann::json(nullptr);
        }

    }

    /**
     * @fun SaveStringToJson
     * @brief 就爱那个字符串以json格式保存
     * @param json_string [IN] 要保存的字符串内容
     * @param file_path   [IN] 要保存下来的路径
     * <AUTHOR>
     * @date 2024.10.11
     */
    inline void SaveStringToJson(const std::string& src_string, const std::string& file_path)
    {
        try
        {
            nlohmann::json json_data = nlohmann::json::parse(src_string);

            std::ofstream file(file_path);
            if (file.is_open())
            {
                file << std::setw(4) << json_data << std::endl;
                file.close();
            }
            else
            {
                std::cerr << "无法打开文件: " << file_path << std::endl;
            }
        }
        catch (nlohmann::json::parse_error& e)
        {
            std::cerr << "JSON 解析错误: " << e.what() << std::endl;
        }
    }

    /**
     * @fun JsonToString
     * @brief 将json格式的数据转换成std::string返回
     * @param json_data [IN] 输入的json数据
     * @return  返回转换后的std::string格式数据
     * <AUTHOR>
     * @date 2024.10.11
     */
    inline std::string JsonToString(const nlohmann::json& json_data)
    {
        //以4格空格为字符串缩进，可以为其他数量如：1，2，3....n
        try
        {
            std::string res_str = json_data.dump(4);
            return res_str;
        }
        catch (const std::exception& e)
        {
            return "Error converting JSON to string: " + std::string(e.what());
        }
        return "";
    }

    /**
     * @fun StringToJson
     * @brief 将std::string 字符串转换成nlohmann格式的json数据
     * @param str_src 源std::string字符串数据
     * @return 返回转换后的json数据
     * <AUTHOR>
     * @date 2024.10.11
     */
    inline nlohmann::json StringToJson(const std::string& str_src)
    {
        try
        {
            return nlohmann::json::parse(str_src);
        }
        catch (const nlohmann::json::parse_error& e)
        {
            (void)e;
            return nlohmann::json();
        }
    }

    /**
    * @fun GetSpeficJsonValue
    * @brief 获取指定路径的json值
    * @param json_data [IN] json数据
    * @param path [IN] 所要获取指定字段内容的路径，例如:std::string path = "/input/input_param/2/input_img"
    * @return  成功返回获取的内容，失败则返回空
    * <AUTHOR>
    * @date 2024.10.11
    */
    inline nlohmann::json GetSpeficJsonValue(const nlohmann::json& json_data, const std::string& path)
    {
        try
        {
            auto parts = nlohmann::json::json_pointer(path);

            nlohmann::json current_json_data = json_data;

            nlohmann::json::json_pointer pointer(path);
            return current_json_data.at(pointer);
        }
        catch (const std::exception&)
        {
            return nlohmann::json(nullptr);
        }
    }
    /**
     * @fun ParseJson
     * @brief
     * @param json_str_
     * @param json_key_str_
     * @return
     * <AUTHOR>
     * @date 2024.11.18
     */
    inline std::string ParseJson(const std::string& json_str_, const std::string& json_key_str_)
    {
        try
        {
            nlohmann::json json = nlohmann::json::parse(json_str_);
            if (json.contains(json_key_str_))
            {
                /** 兼容string和bool，都返回为std::string ,后期增加其他格式 by zhangyuyu 2025/1/16 */
                const auto& value = json[json_key_str_];
                if (value.is_string())
                {
                    return value.get<std::string>();
                }
                else if (value.is_boolean())
                {
                    return value.get<bool>() ? "true" : "false";
                }
                else if (value.is_number_float())
                {
                    return std::to_string(value.get<float>());
                }
                return "";
            }
            else
            {
                return "";
            }

        }
        catch (...)
        {
            return "";
        }
    }
    
    

    /**
     * @fun AddKeyValueToObj
     * @brief 向 JSON 对象中添加键值对
     * @param obj JSON 对象
     * @param key 键
     * @param value 值
     * @return 正常返回 true 异常返回 false
     * <AUTHOR>
     * @date 2025.05.16
     */
    inline bool AddKeyValueToObj(nlohmann::json& obj, const std::string& key, const nlohmann::json& value) {
        try
        {
            obj[key] = value;
            return true;
        }
        catch (const nlohmann::json::parse_error& e) {
            std::cerr << "Parse error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::type_error& e) {
            std::cerr << "Type error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::out_of_range& e) {
            std::cerr << "Out of range error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::invalid_iterator& e) {
            std::cerr << "Invalid iterator error: " << e.what() << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Other exception: " << e.what() << std::endl;
        }
        return false;
    }

    // 将 JSON 对象添加到数组中
    /**
     * @fun AddObjectToArray
     * @brief JSON 对象添加到JSON数组中
     * @param array JSON 组
     * @param obj JSON 对象
     * @return 正常返回 true 异常返回 false
     * <AUTHOR>
     * @date 2025.05.16
     */
    inline bool AddObjectToArray(nlohmann::json& array, const nlohmann::json& obj) {
        try
        {
            array.push_back(obj);
            return true;
        }
        catch (const nlohmann::json::parse_error& e) {
            std::cerr << "Parse error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::type_error& e) {
            std::cerr << "Type error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::out_of_range& e) {
            std::cerr << "Out of range error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::invalid_iterator& e) {
            std::cerr << "Invalid iterator error: " << e.what() << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Other exception: " << e.what() << std::endl;
        }
        return false;
    }

    /**
    * @fun SpeficKeyJsonExists
    * @brief json中指定key是否存在
    * @param json_data json内容
    * @param name 指定的key字段的名称
    * @return 正常返回 true 异常返回 false
    * <AUTHOR>
    * @date 2025.05.16
    */
    inline bool SpeficKeyJsonExists(const nlohmann::json& json_data, const std::string& name)
    {
        if (json_data.is_object())
        {
            if (json_data.contains(name))
            {
                return true;
            }
        }
        return false;
    }

    /**
    * @fun GetSpeficJsonTypeValue
    * @brief 获取json中指定key名的内容,返回的是具体类型的内容，不是json格式
    * @param json_data [IN] json值
    * @param name [IN] 指定的key值名称
    * @return 返回搜索到的内容，通过模板返回指定类型
    * <AUTHOR>
    * @date 2025.05.16
    */
    template<typename T>
    std::optional<T> GetSpeficJsonTypeValue(const nlohmann::json& json_data, const std::string& name) {
        try {
            if (json_data.contains(name)) { // 检查键是否存在
                const auto& value = json_data.at(name); // 获取键对应的值

                // 根据 T 的类型进行判断
                if constexpr (std::is_same<T, bool>::value) {
                    if (value.is_boolean()) {
                        return value.get<T>();
                    }
                    else {
                        return std::nullopt;
                    }
                }
                else if constexpr (std::is_same<T, float>::value) {
                    if (value.is_number_float()) {
                        return value.get<T>();
                    }
                    else {
                        return std::nullopt;
                    }
                }
                else if constexpr (std::is_same<T, double>::value) {
                    if (value.is_number_float()) {
                        return value.get<T>();
                    }
                    else {
                        return std::nullopt;
                    }
                }
                else if constexpr (std::is_same<T, std::string>::value) {
                    if (value.is_string()) {
                        return value.get<T>();
                    }
                    else {
                        return std::nullopt;
                    }
                }
                else
                {
                    // 对于其他类型，直接尝试提取（假设用户知道他们在做什么）
                    return value.get<T>();
                }
            }
        }
        catch (const nlohmann::json::exception& e) {
            std::cerr << "[JsonValue] error for key '" << name << "': " << e.what() << std::endl;
        }
        return std::nullopt;
    }
    /**
    * @fun IsValidJsonData
    * @brief 检查 JSON 数据是否有效。
    * @param json_data JSON 数据。
    * @return 如果 JSON 数据有效返回 true，否则返回 false。
    * @date 2025.05.05
    * <AUTHOR>
    */
    inline bool IsValidJsonData(const nlohmann::json& json_data)
    {
        return !json_data.is_null() && !json_data.empty();
    }
}


#endif // !__JRSJSONOPERATOR_HPP__


