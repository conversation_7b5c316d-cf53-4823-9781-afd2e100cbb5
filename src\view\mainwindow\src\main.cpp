﻿
#pragma warning(push, 1)

#pragma warning(pop)
//STD
#include <iostream>
#include <memory>
#ifdef _DEBUG
#include "vld.h"
#include "vld_def.h"
#endif // DEBUG

// QT
#include <QFile>
#include <QtWidgets/QApplication>
#include <QTextCodec>
#include <QEventLoop>
#include <QTranslator>
#include <QMetaObject>
#include <QScreen>
#include <QProcess>
#include <QDir>
#include <QSharedMemory>
#include <QMessageBox>
//Custom
#include "mainwindow.h"
#include "systemstateview.h"
#include "customqvarientregister.hpp"
#include "crashhandler.h"
using namespace jrsaoi;

#ifdef __cplusplus
extern "C"
{
#endif
#if defined(WIN32) || defined(_WIN32) || defined(WIN64) || defined(_WIN64)
    __declspec(dllexport) uint32_t NvOptimusEnablement = 1;
    __declspec(dllexport) int AmdPowerXpressRequestHighPerformance = 1;
#endif
#ifdef __cplusplus
}
#endif



void StartSystemInitView(MainWindow* main_window_)
{
    auto init_view = static_cast<jrsaoi::SystemStateView*>(main_window_->GetInitView());
    init_view->show();

    QEventLoop loop;
    QObject::connect(init_view, &jrsaoi::SystemStateView::SigCheckedChanged, [&loop](bool isChecked)
        {
            if (isChecked)
            {
                loop.quit();
            }
            else
            {
                loop.quit();
                exit(-1);
            }
        });
    loop.exec();
}


// 判断进程是否在运行
bool IsProcessRunning(std::string process_name)
{
    QProcess process;
    QString command = QString("tasklist | findstr /I \"%1\"").arg(QString::fromStdString(process_name));
    process.start(command);
    process.waitForFinished();
    QString output = process.readAllStandardOutput();
    return !output.isEmpty();
}

// 启动指定进程
bool StartProcess(std::string path)
{
    static QProcess process; // 注意这里需要定义为静态变量,否则要启动的程序还没启动,QProcess就被释放了
    QString program = QString::fromStdString(path);
    QDir workingDir = QDir(QFileInfo(program).absolutePath());
 
    // 设置工作目录为可执行文件所在的目录
    process.setWorkingDirectory(workingDir.absolutePath());
   
    // 等待进程启动
    process.start(program);
    if (!process.waitForStarted(3000)) 
    {
        std::cerr << "Process did not start within timeout period. Error: " << process.errorString().toStdString() << "\n";
        return false;
    }
    return true;
}

void BindMainThreadToCpu(int cpu_index)
{
    #ifdef _WIN32
    DWORD_PTR mask = 1ULL << cpu_index;
    HANDLE thread = GetCurrentThread();
    if (SetThreadAffinityMask(thread, mask) == 0) {
        //qDebug() << "Failed to bind main thread to CPU core";
    }
    else {
        //qDebug() << "Main thread bound to CPU core " << cpu_index;
    }
    #endif
}



int main(int argc, char* argv[])
{
  
    BindMainThreadToCpu(0);
    /** 解决控制台显示中文乱码问题 */
    system("chcp 65001");

    QApplication a(argc, argv);
 
    static QSharedMemory shared_memory("JRSAOI");
    if (!shared_memory.create(1)) 
    {
        QMessageBox::warning(nullptr, "警告", "程序已在运行,请先退出！");

        return 1; // 说明已有进程在运行
    }
    // 2025.01.08 wangzhengkai 增加自定义QVariant类型注册 统一放到CustomQVariantRegister 中
    CustomQVariantRegister::RegisterType();

    // 检测运控软件是否已经运行,如果没运行则启动
    std::string motionpath = "./JRSMotion/JRSMotion.exe";
    if(!IsProcessRunning("JRSMotion.exe"))
    {
        if(StartProcess(motionpath))
        {
            std::cout << "Start JRSMotion.exe success!!!" <<std::endl;
        }
        else
        {
            std::cout << "Start JRSMotion.exe failed!!!" <<std::endl;
        }
    }


    MainWindow* main_window = new MainWindow(nullptr);
    //! 离线调试时可注释
    StartSystemInitView(main_window);

    /** 强制显示到主屏幕 */
    QScreen* primaryScreen = QGuiApplication::primaryScreen();
    if (primaryScreen)
    {
        // 获取主屏幕的几何信息
        QRect screenGeometry = primaryScreen->geometry();

        // 将窗口移动到主屏幕中央
        int x = screenGeometry.x() + (screenGeometry.width() - main_window->width()) / 2;
        int y = screenGeometry.y() + (screenGeometry.height() - main_window->height()) / 2;
        main_window->move(x, y);
    }
    //! dump生成
    #if defined(_WIN32) || defined(_WIN64)
    CrashHandler::Init();
    #endif
    main_window->show();

    return a.exec();
}
