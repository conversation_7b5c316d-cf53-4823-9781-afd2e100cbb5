﻿#include "controlpanelcontroller.h"
#include "controlpanelmodel.h"
#include "controlpanelview.h"

namespace  jrsaoi
{
    ControlPanelController::ControlPanelController(const std::string& name) :ControllerBase(name)
    {

    }
    ControlPanelController::~ControlPanelController()
    {
    }
    int ControlPanelController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        if (model == nullptr || auto_run_panel_view == NULL)
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        model->Update(param_);
        auto param = model->GetModelData();
        QMetaObject::invokeMethod(auto_run_panel_view,
            "UpdateView",
            Qt::QueuedConnection,
            Q_ARG(const jrsdata::ViewParamBasePtr&, param)
        );
        return jrscore::AOI_OK;

    }
    int ControlPanelController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    void ControlPanelController::SetView(ViewBase* view_param)
    {
        auto_run_panel_view = static_cast<ControlPanelView*>(view_param);
        InitConnect();
    }
    void ControlPanelController::SetModel(ModelBasePtr model_param)
    {
        model = std::dynamic_pointer_cast<ControlPanelModel>(model_param);
    }

    void ControlPanelController::InitMember()
    {
    }
    void ControlPanelController::InitConnect()
    {
        connect(auto_run_panel_view, &ControlPanelView::SigControlPanelUpdate, this, &ControlPanelController::SlotAutoPanelUpdate);
    }

    void ControlPanelController::AutoPanelUpdateToRender2D(const jrsdata::ViewParamBasePtr& param_)
    {
        auto update_render = std::make_shared<jrsdata::ViewParamBase>();
        update_render->sub_name = jrsaoi::CONTROL_PANEL_RENDER_SUB_NAME;
        update_render->topic_name = param_->topic_name;
        update_render->event_name = param_->event_name;
        emit SigControlPanelUpdate(update_render);
    }

    void ControlPanelController::AutoPanelUpdateToOperate(const jrsdata::ViewParamBasePtr& param_)
    {
        auto update_operate = std::make_shared<jrsdata::ViewParamBase>();
        update_operate->sub_name = jrsaoi::CONTROL_PANEL_OPERATE_SUB_NAME;
        update_operate->topic_name = param_->topic_name;
        update_operate->event_name = param_->event_name;
        emit SigControlPanelUpdate(update_operate);
    }

    void ControlPanelController::SlotAutoPanelUpdate(const jrsdata::ViewParamBasePtr& param_)
    {
        model->Update(param_);
        AutoPanelUpdateToOperate(param_);
        AutoPanelUpdateToRender2D(param_);
        auto temp_param = std::static_pointer_cast<jrsdata::ControlPanelViewParam>(param_);
        auto model_param_temp = std::static_pointer_cast<jrsdata::ControlPanelViewParam>(model->GetAutoRunParam());
        if (!temp_param->current_project_param)
        {
            JRSMessageBox_ERR("ControlPanel", "未打开工程，请先打开工程！", jrscore::MessageButton::Yes);
            return;
        }
        temp_param->current_project_param = model_param_temp->current_project_param;
        temp_param->multi_project_param_map = model_param_temp->multi_project_param_map;
       
        if (const auto& link_name = temp_param->current_project_param->link_project_name;
            link_name && !link_name->empty())
        {
            if (!temp_param->is_load_link_project)
            {
                const std::string& name = *link_name;
                const std::string err_msg =
                    "当前工程关联了工程: " + name + ", 但该工程未添加，请先添加！";

                JRSMessageBox_ERR("ControlPanel", err_msg.c_str(), jrscore::MessageButton::Yes);
                return;
            }
        }
        emit SigControlPanelUpdate(temp_param);
    }


}