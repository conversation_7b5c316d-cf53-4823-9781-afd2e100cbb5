#include "conditionwaiter.h"
namespace jrsworkflow 
{
    void ConditionWaiter::AddCondition(std::function<bool()> condition)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        conditions_.emplace_back(std::move(condition));
    }
    void ConditionWaiter::Wait()
    {
        std::unique_lock<std::mutex> lock(mutex_);
        cond_var_.wait(lock, [this]() 
        {
            for (const auto& condition : conditions_) 
            {
                if (!condition()) 
                {
                    return false;
                }
            }
            return true;
        });
    }
    void ConditionWaiter::Notify()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        cond_var_.notify_all();
    }
}