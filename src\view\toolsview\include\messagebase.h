/*****************************************************************
 * @file   messagebase.h
 * @brief  提示框基类，统一接口
 * @details
 * <AUTHOR>
 * @date 2024.12.3
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.3          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
//prebuild
#include "pch.h"
//STD
#include <iostream>
//Custom
#include "coreapplication.h"
//#include "viewparam.hpp"
//qt
#pragma warning(push, 1)
//#include <QWidget>
#pragma warning(pop)
 //Third
#ifndef __CUSTOM_MESSAGE_BASE_H__
#define __CUSTOM_MESSAGE_BASE_H__
namespace jrsaoi
{
    class MessageBase :public QWidget
    {
    public:
        struct Message
        {
            std::string time;/**<唯一标识*/
            std::string title;
            jrscore::LogLevel level;
            std::string msg;
            int message_button;
            Message()
                :time(""), title(""), level(jrscore::LogLevel::LEVEL_DEBUG), msg(""), message_button(jrscore::MessageButton::Ok)
            {
            }
            Message(const std::string& time_, const std::string& title_, jrscore::LogLevel level_, const std::string& msg_, const int& message_button_)
                :time(time_), title(title_), level(level_), msg(msg_), message_button(message_button_)
            {
            }
        };

        ~MessageBase() = default;
        virtual jrscore::MessageButton ShowMessage(const jrsaoi::MessageBase::Message& msg_) = 0;
    protected:
        MessageBase(QWidget* parent = nullptr);

    };
}
#endif
