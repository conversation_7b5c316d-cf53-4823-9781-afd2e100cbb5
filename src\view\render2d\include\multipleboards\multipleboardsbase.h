﻿/*****************************************************************
 * @file   multipleboardsbase.h
 * @brief  多联板添加删除基类
 * @details
 * <AUTHOR>
 * @date 2024.12.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.31          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef _MULTIPLE_BOARDS_BASE_H__
#define _MULTIPLE_BOARDS_BASE_H__
 //prebuild
#include "pch.h"
 //STD
#include <iostream>

//Custom
//#include "viewparam.hpp"
#include "coreapplication.h"
#include "paramoperator.h"
#include "graphicsabstract.hpp"
#include "layerconverter.hpp"
#include "controlconstants.hpp"
#include "render2deventparam.hpp"
namespace jrsaoi
{
    typedef std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback)> AddGraphicsCallBack;
    typedef std::function<void()> ProjectUpdateGraphicsCallBack;
    typedef std::function<std::optional<std::reference_wrapper<jrsaoi::Render2dEventParam>>()> GetCurrentSelectParamCallBack;
    typedef std::function<void(const std::string& layer, bool invoke_callback_)> ClearLayerCallBack;
    typedef std::function<int(const Layer& layer)> SetCurrentLayerCallBack;
    typedef std::function<void(const VisionMode& state)> SetVisionModeCallBack;
    typedef std::function<void(GraphicsPtr& gh, const std::string& name)> GetGraphicsCallBack;
    class MultipleBoardsBase
    {
    public:
        virtual ~MultipleBoardsBase() = default;
        /**
         * @fun MultipleBoardsUpdate
         * @brief 多联板操作接口
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        virtual int MultipleBoardsUpdate(jrsdata::MultiBoardEventParamPtr param_) = 0;

        /*<  render 2d 内功能回调 */
        void SetAddGraphicsCallBack(AddGraphicsCallBack callback_);
        void SetProjectUpdateGraphicsCallBack(ProjectUpdateGraphicsCallBack callback_);
        void SetClearLayerGraphicsCallBack(ClearLayerCallBack callback_);
        void SetRenderCurrentLayerCallBack(SetCurrentLayerCallBack callback_);
        void SetRenderVisionModeCallBack(SetVisionModeCallBack callback_);
        void SetGetGraphicsCallBack(GetGraphicsCallBack callback_);
        void SetGetCurrentSelectParam(GetCurrentSelectParamCallBack callback_);
    protected:
        MultipleBoardsBase();
        AddGraphicsCallBack _add_graphics_callback;                      /**<在render界面 添加图形回调*/
        ProjectUpdateGraphicsCallBack _project_update_graphics_callback; /**<工程更新到渲染界面回调*/
        ClearLayerCallBack _clear_layer_callback;                        /**<清除 渲染界面某一层的所有图形*/
        SetCurrentLayerCallBack _set_current_layer_callback;             /**<设置当前渲染界面的层级*/
        SetVisionModeCallBack _set_vision_mode_callback;                 /**<设置显示模式回调 */
        GetGraphicsCallBack _get_graphics_callback;                      /**<获取某个图形参数回调*/
        GetCurrentSelectParamCallBack _get_current_select_param;          /**< 获取当前选择的参数*/
        jrsaoi::ParamOperator& _project_param_instance;           /**<工程文件获取*/
    };
}

#endif //!_MULTIPLE_BOARDS_BASE_H__
