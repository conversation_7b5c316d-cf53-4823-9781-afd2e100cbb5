//!丢弃使用 by zhangyuyu 2024.12.1
//Custom
#include "updownmaterial.h"
#include "motion.h"
#include "devicemanager.h"

namespace jrsworkflow 
{
    UpDownMaterial::UpDownMaterial ( const std::shared_ptr<jrsdevice::<PERSON><PERSON>Manager>& device_manager_ptr_ )
        :device_manager_ptr(device_manager_ptr_)
    {

    }

    UpDownMaterial::~UpDownMaterial ()
    {

    }

    void UpDownMaterial::UpMaterial ( int rail_index )
    {
        device_manager_ptr->GetMotionInstance ()->Load (static_cast< jrsdevice::TrackIndex >(rail_index));
    }

    void UpDownMaterial::DownMaterial ( int rail_index )
    {
        device_manager_ptr->GetMotionInstance ()->UnLoad ( static_cast< jrsdevice::TrackIndex >( rail_index ) );

    }

    void UpDownMaterial::InitMember ()
    {

    }
}
