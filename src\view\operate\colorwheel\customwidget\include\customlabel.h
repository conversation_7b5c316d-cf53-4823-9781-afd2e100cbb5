/*****************************************************************//**
 * @file   customlabel.h
 * @brief  带标签的label
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSCUSTOMLABEL_H__
#define __JRSCUSTOMLABEL_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <QLabel>
#include <QPainter>
#include <QMouseEvent>
#pragma warning(pop)

class CustomQLabel : public QLabel 
{
    Q_OBJECT

public:
    CustomQLabel(int id, bool is_green = false, 
        QWidget* parent = nullptr) : QLabel(parent),
        id_(id), is_green_(is_green) {}
	int GetLabelId();
    void UpdateLabelColor(bool is_green);
protected:
    void paintEvent(QPaintEvent* event) override
    {
        QLabel::paintEvent(event);
        QPainter painter(this);
        painter.setPen(QPen(is_green_ ? Qt::green : Qt::black, is_green_ ? 9 : 3));  
        painter.setBrush(Qt::NoBrush);  
        painter.drawRect(this->rect().adjusted(1, 1, -1, -1));  
    }
    void mousePressEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
signals:
    void UpdateCurrenImageId(int id);
	void UpdateCurrenImage(int id);
private:
    bool is_green_;  
	int  id_ = 0;
};
#endif
