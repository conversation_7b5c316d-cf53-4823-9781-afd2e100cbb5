﻿//custom
#include "machinestateview.h"
//thirdparty
#include "ui_machinestateview.h"
#include <QHBoxLayout>
#include <QLabel>

jrsaoi::MachineStateView::MachineStateView(QWidget* parent /*= nullptr*/)
    :QWidget(parent),
    ui(new Ui::machinestateview())
{
    Init();
}

jrsaoi::MachineStateView::~MachineStateView()
{
    delete ui;
}

int jrsaoi::MachineStateView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
{
    _param_ptr = std::dynamic_pointer_cast<jrsdata::MachineStateViewParam>(param_);
    if (!_param_ptr)
    {
        Log_ERROR("MachineStateView::UpdateView param is nullptr");
        return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
    }

    ViewChange(_param_ptr->is_auto_run);

    if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME)
    {
        UpdateStopView(_param_ptr->current_stop_state);
    }
    else if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_UPDATE_STATE_NAME || param_->event_name == jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME)/**<运行检测期间状态更换*/
    {
        UpdateDetectView(_param_ptr->current_detect_state);
    }
    return jrscore::AOI_OK;
}


void jrsaoi::MachineStateView::Init()
{
    InitMember();
    InitView();
    InitConnect();

}

void jrsaoi::MachineStateView::InitView()
{
    ui->setupUi(this);
    ViewChange(false);
    ShowLabelFont(ui->label_detect_state, "检测状态：", _font_size, Qt::black);
    ShowLabelFont(ui->label_stop_state, "停止状态：", _font_size, Qt::black);
    ShowLabelFont(ui->label_detect_flow, "检测流程：", _font_size, Qt::black);
    ShowLabelFont(ui->stop_state_label, "闲置", _font_size, Qt::black);
    ShowLabelFont(ui->label_board_detect_state, "检测结果：", _font_size, Qt::black);
    ShowLabelFont(ui->detect_flow_state_label, "-", _font_size, Qt::black);
}

void jrsaoi::MachineStateView::InitMember()
{
    _font_size = 15;
    _board_detect_state_map = {
        {jrsdata::MachineStateViewParam::BoardDetectState::CHECKING, std::make_tuple("检测中",Qt::black,"#ADD8E6") },
        { jrsdata::MachineStateViewParam::BoardDetectState::PASS,std::make_tuple("PASS",Qt::green,"#ADD8E6") },
        { jrsdata::MachineStateViewParam::BoardDetectState::NG,std::make_tuple("NG", Qt::red,"#ADD8E6") },
        { jrsdata::MachineStateViewParam::BoardDetectState::WAITING,std::make_tuple("等待检测", Qt::black,"#ADD8E6") },
        { jrsdata::MachineStateViewParam::BoardDetectState::GOOD,std::make_tuple("GOOD", Qt::green,"#ADD8E6") },
        { jrsdata::MachineStateViewParam::BoardDetectState::SKIP,std::make_tuple("SKIP", Qt::blue,"#ADD8E6") },
    };
    _stop_state_map = {
        {jrsdata::MachineStateViewParam::StopState::IDLE, std::make_tuple("闲置",Qt::black,"#ADD8E6")},
        {jrsdata::MachineStateViewParam::StopState::ALARM, std::make_tuple("报警",Qt::red,"#ADD8E6")},
        {jrsdata::MachineStateViewParam::StopState::E_STOP, std::make_tuple("急停",Qt::red,"#ADD8E6")},
    };
    _detect_flow_map = {
        {jrsdata::MachineStateViewParam::DetectFlowState::REQUIRE_BOARD,std::tuple("需要上板",Qt::black,"#ADD8E6")},
        {jrsdata::MachineStateViewParam::DetectFlowState::ENTER_BOARD, std::tuple("正在进板",Qt::black,"#ADD8E6")},
        {jrsdata::MachineStateViewParam::DetectFlowState::DETECT_FOV, std::tuple("正在检测",Qt::black,"#ADD8E6")},
        {jrsdata::MachineStateViewParam::DetectFlowState::OUT_BOARD, std::tuple("正在出板",Qt::black,"#ADD8E6")},
    };

}

void jrsaoi::MachineStateView::InitConnect()
{

}

void jrsaoi::MachineStateView::UpdateDetectView(jrsdata::BoardDetectAndDetectFlowState detect_state_)
{
    if (!detect_state_.has_value())
    {
        return;
    }
    UpdateBoardDetectFlowStateView(detect_state_->first);
    UpdateDetectFlowStateView(detect_state_->second);

}

void jrsaoi::MachineStateView::UpdateStopView(std::optional<jrsdata::MachineStateViewParam::StopState> stop_state_)
{
    if (!stop_state_.has_value())
    {
        return;
    }
    auto stop_state_it = _stop_state_map.find(stop_state_.value());
    if (stop_state_it != _stop_state_map.end())
    {
        auto [content_, color_, backgroud_color_] = stop_state_it->second;
        ShowLabelFont(ui->stop_state_label, content_, _font_size, color_);
    }
}

void jrsaoi::MachineStateView::UpdateDetectFlowStateView(jrsdata::MachineStateViewParam::DetectFlowState detect_flow_state_)
{
    auto detect_flow_it = _detect_flow_map.find(detect_flow_state_);
    if (detect_flow_it != _detect_flow_map.end())
    {
        auto [content_, color_, backgroud_color_] = detect_flow_it->second;
        if (detect_flow_state_ == jrsdata::MachineStateViewParam::DetectFlowState::DETECT_FOV)
        {
            //TODO 注释不显示当前FOV检测信息数量 by baron_zhang 2024-12-18
            //content_ += "    " + std::to_string(_param_ptr->detected_fov) + "/" + std::to_string(_param_ptr->total_fov);
        }
        ShowLabelFont(ui->detect_flow_state_label, content_, _font_size, color_);
    }

}

void jrsaoi::MachineStateView::UpdateBoardDetectFlowStateView(jrsdata::MachineStateViewParam::BoardDetectState board_detect_state_)
{
    auto detect_state_it = _board_detect_state_map.find(board_detect_state_);
    if (detect_state_it == _board_detect_state_map.end())
    {
        return;
    }
    auto [content_, color_, backgroud_color_] = detect_state_it->second;
    //!更新板子状态
    if (board_detect_state_ == jrsdata::MachineStateViewParam::BoardDetectState::NG ||
        board_detect_state_ == jrsdata::MachineStateViewParam::BoardDetectState::PASS ||
        board_detect_state_ == jrsdata::MachineStateViewParam::BoardDetectState::GOOD ||
        board_detect_state_ == jrsdata::MachineStateViewParam::BoardDetectState::SKIP

        )
    {
        ShowLabelFont(ui->board_detect_state_label, content_, _font_size, color_);
    }
    else
    {
        ShowLabelFont(ui->detect_state_label, content_, _font_size, color_);

    }

}

void jrsaoi::MachineStateView::ViewChange(const bool& is_auto_run_)
{
    if (is_auto_run_)
    {
        HideLayout(ui->detect_state_hlayout, false);
        HideLayout(ui->detect_flow_hlayout, false);
        HideLayout(ui->board_detect_state_hlayout, false);
        HideLayout(ui->stop_stop_hlayout, true);
        //ui->state_frame->setStyleSheet("background-color: #90EE90;");

    }
    else
    {
        HideLayout(ui->detect_state_hlayout, true);
        HideLayout(ui->detect_flow_hlayout, true);
        HideLayout(ui->board_detect_state_hlayout, true);
        HideLayout(ui->stop_stop_hlayout, false);
        //ui->state_frame->setStyleSheet("background-color: #ADD8E6;");

    }
}

void jrsaoi::MachineStateView::HideLayout(QHBoxLayout* layout_, bool is_hide_)
{
    if (!layout_) return;
    for (int i = 0; i < layout_->count(); ++i)
    {
        QWidget* widget = layout_->itemAt(i)->widget();
        if (widget)
        {
            widget->setHidden(is_hide_);
        }
    }
}

void jrsaoi::MachineStateView::ShowLabelFont(QLabel* const label_, const std::string& content_, int font_size, const Qt::GlobalColor& color_)
{
    if (!label_)
    {
        Log_ERROR("label_ is null");
        return;
    }
    Log_INFO(content_);
    label_->setText(QString::fromStdString(content_));
    QFont font("Microsoft Sans Serif", font_size, QFont::Bold);
    label_->setFont(font);

    QPalette palette;
    palette.setColor(QPalette::WindowText, color_);
    label_->setPalette(palette);
}
