#include "visualcameraabstract.h"
#include "algodefine.hpp"
#include "log.h"

VisualCameraAbstract::VisualCameraAbstract(QVector3D default_position_) :
    view_matrix(new QMatrix4x4), projection_matrix(new QMatrix4x4),
    perspective(false),
    movement_speed(11.125f), near_plane(0.1f), far_plane(1000.f), yaw(-90.0f), pitch(0.0f),
    parallel_scale(0.5), view_angle(45.0),
    parallel_scale_min(0.05), parallel_scale_max(50),
    viewport_width(0.), viewport_height(0.),
    default_position(default_position_), position(default_position_),
    front_axis(QVector3D(0.0f, 0.0f, -1.0f)), worldup_axis(QVector3D(0.0f, 1.0f, 0.0f))
{
    UpdateVectors();
}

VisualCameraAbstract::~VisualCameraAbstract()
{
    if (view_matrix)
    {
        delete view_matrix;
        view_matrix = nullptr;
    }
    if (projection_matrix)
    {
        delete projection_matrix;
        projection_matrix = nullptr;
    }
}

QMatrix4x4* VisualCameraAbstract::GetViewMatrix()
{
    view_matrix->setToIdentity();
    view_matrix->lookAt(position, position + front_axis, up_axis);
    return view_matrix;
}

QMatrix4x4* VisualCameraAbstract::GetProjectionMatrix()
{
    projection_matrix->setToIdentity();

    if (perspective)
    {
        projection_matrix->perspective(view_angle, GetAspetRatio(), near_plane, far_plane);
    }
    else
    {
        /*t=0.5时,可以按等比例显示图像数据*/
        // float t = view_angle / 90.0;//->等价 0.5 + (fov - 45.0) / 90.0;
        float t = parallel_scale;
        projection_matrix->ortho(-viewport_width * t, viewport_width * t, -viewport_height * t, viewport_height * t, near_plane, far_plane);
    }
    return projection_matrix;
}

void VisualCameraAbstract::Reset()
{
    movement_speed = 11.125f;
    near_plane = 0.1f;
    far_plane = 1000.f;
    yaw = -90.0f;
    pitch = 0.0f;
    view_angle = 45.0;
    parallel_scale = 0.5;
    position = default_position;
    front_axis = QVector3D(0.0f, 0.0f, -1.0f);
    worldup_axis = QVector3D(0.0f, 1.0f, 0.0f);

    UpdateVectors();
}

void VisualCameraAbstract::Move(const CameraDirection& d)
{
    static const float zoom_ratio = 0.95f;
    float move_ratio = movement_speed * (view_angle / 90.f);// = 1.0f; 

    switch (d)
    {
    case CameraDirection::Up:
        position += up_axis * move_ratio;
        break;
    case CameraDirection::Down:
        position -= up_axis * move_ratio;
        break;
    case CameraDirection::Left:
        position -= right_axis * move_ratio;
        break;
    case CameraDirection::Right:
        position += right_axis * move_ratio;
        break;
    case CameraDirection::Front:
        Zoom(zoom_ratio);
        break;
    case CameraDirection::Rear:
        Zoom(1.f / zoom_ratio);
        break;
    default:
        break;
    }
}

void VisualCameraAbstract::SetScaleMode([[maybe_unused]] const CameraScaleMode& state)
{
    Reset();
}

void VisualCameraAbstract::SetResetMode([[maybe_unused]] const CameraResetMode& state)
{
    Reset();
}

void VisualCameraAbstract::Zoom(double amount)
{
    if (amount <= 0.0)
        return;

    if (this->perspective)
    {
        this->SetViewAngle(this->view_angle / amount);
    }
    else
    {
        this->SetParallelScale(this->parallel_scale / amount);
    }
    // printInfo((std::stringstream() << "in_zoom: " << 0.5 / GetZoom() << " zoom : " << GetZoom()));
}

void VisualCameraAbstract::SetZoom(float outer_zoom)
{
    if (outer_zoom <= 0.0)
        return;

    double zoom = 0.5 / outer_zoom; // 外部比例转换到内部zoom
    if (this->perspective)
    {
        this->SetViewAngle(zoom);
    }
    else
    {
        this->SetParallelScale(zoom);
    }
}

double VisualCameraAbstract::GetZoom() const
{
    return 0.5f / GetInnerZoom(); // 内部zoom转换到外部比例
}

double VisualCameraAbstract::GetInnerZoom() const
{
    double zoom = 0;
    if (this->perspective)
    {
        zoom = this->GetViewAngle();
    }
    else
    {
        zoom = this->GetParallelScale();
    }
    return zoom;
}

void VisualCameraAbstract::SetCameraPosition(QVector3D p)
{
    position = p;
}

void VisualCameraAbstract::SetCameraPosition(double x, double y, double z)
{
    position.setX(x);
    position.setY(y);
    position.setZ(z);
}

void VisualCameraAbstract::SetCameraPosition(double x, double y)
{
    position.setX(x);
    position.setY(y);
    //PrintInfoDebug();
}

void VisualCameraAbstract::GetCameraPosition(double& x, double& y, double& z) const
{
    x = position.x();
    y = position.y();
    z = position.z();
}

void VisualCameraAbstract::GetCameraPosition(double& x, double& y) const
{
    x = position.x();
    y = position.y();
}

QVector4D VisualCameraAbstract::GetCameraPosition() const
{
    return QVector4D(position, 1.0);
}

void VisualCameraAbstract::SetPerspective(bool state)
{
    perspective = state;
}

void VisualCameraAbstract::SetViewAngle(double angle)
{
    static const double min = 0.00000001;
    static const double max = 179.0;

    if (this->view_angle != angle)
    {
        this->view_angle = A_CLAMP(min, angle, max);
        // this->view_angle = (angle < min ? min : (angle > max ? max : angle));
    }
}

void VisualCameraAbstract::SetParallelScale(double scale)
{
    //PrintInfoDebug();
    const auto& min = parallel_scale_min;
    const auto& max = parallel_scale_max;
    // static const double min = 0.05;
    // static const double max = 999;
    //printInfo((std::stringstream() << "inner scale: " << scale << " outer scale:" << 0.5 / scale));
    if (this->parallel_scale != scale)
    {
        this->parallel_scale = A_CLAMP(min, scale, max);
        // this->parallel_scale = (scale < min ? min : (scale > max ? max : scale));
    }
    //PrintInfoDebug();
}

void VisualCameraAbstract::SetParallelScaleMax(double max)
{
    parallel_scale_max = max;
}

void VisualCameraAbstract::SetParallelScaleMinMax(double min, double max)
{
    parallel_scale_min = min;
    parallel_scale_max = max;
}

void VisualCameraAbstract::SetViewport(double width, double height)
{
    viewport_width = width;
    viewport_height = height;
    //PrintInfoDebug();
}

void VisualCameraAbstract::GetViewport(double& width, double& height) const
{
    width = viewport_width;
    height = viewport_height;
    //PrintInfoDebug();
}

void VisualCameraAbstract::UpdateVectors()
{
    front_axis.setX(cos(A_DEG_TO_RAD(yaw)) * cos(A_DEG_TO_RAD(pitch)));
    front_axis.setY(sin(A_DEG_TO_RAD(pitch)));
    front_axis.setZ(sin(A_DEG_TO_RAD(yaw)) * cos(A_DEG_TO_RAD(pitch)));
    front_axis.normalize();

    right_axis = QVector3D::crossProduct(front_axis, worldup_axis).normalized();
    up_axis = QVector3D::crossProduct(right_axis, front_axis).normalized();
}

void VisualCameraAbstract::PrintInfoDebug() const
{
    qDebug() << "perspective:" << perspective;
    qDebug() << "movement_speed:" << movement_speed;
    qDebug() << "near_plane:" << near_plane;
    qDebug() << "far_plane:" << far_plane;
    qDebug() << "yaw:" << yaw;
    qDebug() << "pitch:" << pitch;
    qDebug() << "view_angle:" << view_angle;
    qDebug() << "parallel_scale:" << parallel_scale;
    qDebug() << "parallel_scale_min:" << parallel_scale_min;
    qDebug() << "parallel_scale_max:" << parallel_scale_max;

    qDebug() << "viewport_width:" << viewport_width;
    qDebug() << "viewport_height:" << viewport_height;

    qDebug() << "default_position:" << default_position;
    qDebug() << "position:" << position;
    qDebug() << "front_axis:" << front_axis;
    qDebug() << "up_axis:" << up_axis;
    qDebug() << "right_axis:" << right_axis;
    qDebug() << "worldup_axis:" << worldup_axis;

    if (view_matrix)
        qDebug() << "view_matrix:" << *view_matrix;
    else
        qDebug() << "view_matrix: nullptr";

    if (projection_matrix)
        qDebug() << "projection_matrix:" << *projection_matrix;
    else
        qDebug() << "projection_matrix: nullptr";
}
