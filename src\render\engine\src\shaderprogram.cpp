﻿#include "shaderprogram.h"
#include "shadersourceconstants.hpp"
#include "log.h"
// #include "ropenglfunctions.hpp" // ROpenGLFunctions
// #include "windowinterface.h"
//#include <QOpenGLShaderProgram>
#include <qopenglshaderprogram.h>
#include <string>
#include <vector>
// #define struct_offset(s, m) (&(((s *)0)->m))

//void printInfo([[maybe_unused]] const std::string& info)
//{
//    printInfo(info.c_str());
//}

ShaderProgram::ShaderProgram(ProgramType type) :
    qt_shader(new QOpenGLShaderProgram),
    m_program_type(type),
    vertexAttributes(),
    nVertexAttributeCount(0)
{
}

ShaderProgram::~ShaderProgram()
{
    if (qt_shader)
    {
        delete qt_shader;
        qt_shader = nullptr;
    }
}

void ShaderProgram::bind()
{
    if (!qt_shader->isLinked())
        qt_shader->link();
    qt_shader->bind();
}

void ShaderProgram::release()
{
    qt_shader->release();
}

bool ShaderProgram::CreateProgram(const char* vname, const char* fname, const char* gname)
{
    auto& program = qt_shader;
    if (!vname)
    {
        printInfo("invalid vertex");
        return false;
    }
    if (!fname)
    {
        printInfo("invalid frament");
        return false;
    }
    if (!program->addShaderFromSourceCode(QOpenGLShader::Vertex, vname))
    { // 添加并编译顶点着色器
        printInfo(program->log().toStdString().c_str());
        return false;
    }
    //program->setUniformValue()
    if (!program->addShaderFromSourceCode(QOpenGLShader::Fragment, fname))
    { // 添加并编译片段着色器
        printInfo(program->log().toStdString().c_str());
        return false;
    }
    if (gname)
    {
        if (!program->addShaderFromSourceCode(QOpenGLShader::Geometry, gname))
        {
            printInfo(program->log().toStdString().c_str());
            return false;
        }
    }
    // if (!program->link())
    // { // 链接着色器
    //     printInfo(program->log().toStdString());
    //     return false;
    // }
    return true;
}

void ShaderProgram::BindVertexDataToGPU(ROpenGLFunctions* function, void* data)
{
    void* data_offset = nullptr;
    for (int i = 0; i < nVertexAttributeCount; i++)
    {
        data_offset = static_cast<char*>(data) + vertexAttributes[i].offset;

        /* 上传顶点数据 */
        function->glVertexAttribPointer(vertexAttributes[i].layout, vertexAttributes[i].size, vertexAttributes[i].type,
            GL_FALSE, vertexAttributes[i].stride, data_offset);

        function->glEnableVertexAttribArray(vertexAttributes[i].layout);
    }
}

void ShaderProgram::UnbindVertexDataFromGPU(ROpenGLFunctions* function)
{
    for (int i = 0; i < nVertexAttributeCount; i++)
    {
        function->glDisableVertexAttribArray(vertexAttributes[i].layout);
    }
}
void ShaderProgram::SetVertexAttribute(int location, int size, int type, int offset, int stride)
{
    vertexAttributes[location].layout = location;
    vertexAttributes[location].size = size;
    vertexAttributes[location].type = type;
    vertexAttributes[location].stride = stride;
    vertexAttributes[location].offset = offset;

    if (nVertexAttributeCount <= location)
    {
        nVertexAttributeCount = location + 1;
    }
}

ShaderProgram* ShaderProgramManager::GetProgram(const ProgramType& type)
{
    auto iter = map_program.find(static_cast<int>(type));
    if (iter == map_program.end())
    {
        return nullptr;
    }
    return iter->second;
}

ShaderProgramManager::ShaderProgramManager()
{
    {
        ProgramType type = ProgramType::TEXTURE;
        ShaderProgram* p = new ShaderProgram(type);
        p->CreateProgram(VERTEXSHADER_TEXTURE_MULTI, FRAGMENTSOURCE_TEXTURE_MULTI, nullptr);

        // p->CreateProgram(VERTEXSHADER_TEXTURE, FRAGMENTSOURCE_TEXTURE, nullptr);
        // p->CreateProgram(LineSegmentVertexShader, LineSegmentFragmentShader, nullptr);
        int num = 0;
        std::vector<int> vecattribute{
            3, 2, 2, 2, 1, 1 };
        for (auto& idx : vecattribute)
        {
            num += idx;
        }
        int count = 0;
        for (size_t i = 0; i < vecattribute.size(); ++i)
        {
            p->SetVertexAttribute((int)i, vecattribute[i], GL_FLOAT, (int)(count * sizeof(GLfloat)), (int)(num * sizeof(GLfloat)));
            count += vecattribute[i];
        }

        // p->SetVertexAttribute(0, 3, GL_FLOAT, 0, (int)(9 * sizeof(GLfloat)));
        // p->SetVertexAttribute(1, 2, GL_FLOAT, (int)(3 * sizeof(GLfloat)), (int)(9 * sizeof(GLfloat)));
        // p->SetVertexAttribute(2, 2, GL_FLOAT, (int)(5 * sizeof(GLfloat)), (int)(9 * sizeof(GLfloat)));
        // // p->SetVertexAttribute(3, 2, GL_FLOAT, (int)(7 * sizeof(GLfloat)), (int)(9 * sizeof(GLfloat)));
        // p->SetVertexAttribute(3, 1, GL_FLOAT, (int)(7 * sizeof(GLfloat)), (int)(9 * sizeof(GLfloat)));
        // p->SetVertexAttribute(4, 1, GL_FLOAT, (int)(8 * sizeof(GLfloat)), (int)(9 * sizeof(GLfloat)));
        map_program.emplace(static_cast<int>(type), p);
    }
    {
        ProgramType type = ProgramType::GRAPHICS;
        ShaderProgram* p = new ShaderProgram(type);
        p->CreateProgram(VERTEXSHADER_GRAPHICS, FRAGMENTSOURCE_COLOR, nullptr);
        p->SetVertexAttribute(0, 3, GL_FLOAT, 0, (int)(7 * sizeof(GLfloat)));
        p->SetVertexAttribute(1, 4, GL_FLOAT, (int)(3 * sizeof(GLfloat)), (int)(7 * sizeof(GLfloat)));
        // p->SetVertexAttribute(2, 4, GL_FLOAT, (int)(7 * sizeof(GLfloat)), (int)(11 * sizeof(GLfloat)));
        map_program.emplace(static_cast<int>(type), p);
    }
    // {
    //     ProgramType type = ProgramType::FRONTGROUND;
    //     ShaderProgram *p = new ShaderProgram(type);
    //     p->CreateProgram(VERTEXSHADER_GRAPHICS, FRAGMENTSOURCE_COLOR, nullptr);
    //     p->SetVertexAttribute(0, 3, GL_FLOAT, 0, (int)(7 * sizeof(GLfloat)));
    //     p->SetVertexAttribute(1, 4, GL_FLOAT, (int)(3 * sizeof(GLfloat)), (int)(7 * sizeof(GLfloat)));
    //     map_program.emplace(static_cast<int>(type), p);
    // }
}

ShaderProgramManager::~ShaderProgramManager()
{
    for (auto& pair : map_program)
    {
        delete pair.second;
        pair.second = nullptr;
    }
    std::unordered_map<int, ShaderProgram*>().swap(map_program);
}

StandardProgram& StandardProgram::GetInstance()
{
    static StandardProgram instance;
    return instance;
}

StandardProgram::StandardProgram()
    :ShaderProgramManager()
{
}

StandardProgram::~StandardProgram()
{
}