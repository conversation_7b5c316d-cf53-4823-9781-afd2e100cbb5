/**********************************************************************
 * @brief  标尺UI.
 *
 * @file   rulerwidget.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef RULERWIDGET_H
#define RULERWIDGET_H

#include <QWidget>

enum class RulerTextAlignment
{
    ABOVE,    // 刻度尺在文字上方
    BELOW,    // 刻度尺在文字下方
};

class RulerWidget : public QWidget
{
    Q_OBJECT
public:
    RulerWidget(Qt::Orientations orientation, QWidget* parent = nullptr);
    ~RulerWidget() {}

    // 设置标记位
    void SetCursorPosition(int pos);
    // 设置范围值
    void SetRange(double minValue_, double maxValue_);
    void SetRange(int minValue_, int maxValue_);
    // 设置刻度比率
    void SetScale(double scale);
    void SetUseScale(bool state);
    void ChangeUseScale();

    // 设置最大最小值
    void SetMinValue(double minValue_);
    void SetMaxValue(double maxValue_);

    // 设置精确度
    void SetPrecision(int precision_);

    // 设置刻度尺位置
    void SetRulerTextAlignment(RulerTextAlignment alignment);

    void SetBackgroundColor(QColor color);
    void SetTextAndLineColor(QColor color);
    void SetSlidingLineColor(QColor color);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;

private:
    void DrawCursorLine(QPainter* painter, int slide_width, int width, int height);
    void DrawRuler(QPainter* painter, int width, int height, bool isTop);

    void DrawRulerTop(QPainter* painter, int width, int height);
    void DrawRulerBottom(QPainter* painter, int width, int height);

private:
    bool use_scale;  ///< 是否使用刻度比率
    RulerTextAlignment text_alignment;///< 刻度线与文字位置关系

    int tick_step_long;       ///< 长线条等分步长
    int tick_step_short;      ///< 短线条等分步长
    int cursor_position; ///< 标记指向位置
    int precision;      ///< 精确度,小数点后几位
    int ruler_width;    ///< 尺宽度

    double tick_scale; ///< 刻度比率
    double tick_min_value; ///< 最小值
    double tick_max_value; ///< 最大值
    static const std::vector<double> scale_factors; ///< 刻度间隔

    Qt::Orientations orientation; ///< 标尺布局
    QFont font;                   ///< 文本字体
    QColor color_background;      ///< 背景色
    QColor color_text_and_tick;   ///< 文本和刻度颜色
    QColor color_cursor_line;     ///< 游标颜色
};

#endif // RULERWIDGET_H