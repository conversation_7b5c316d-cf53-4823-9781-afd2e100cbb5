#include <QLabel>
#include <Qpainter>
#include <QRectF>
#include <QSpinBox>
#include <QResizeEvent>
#include <QMessageBox>

#include "dlgcreatetemplate.h"
#include "ui_dlgcreatetemplate.h"


DlgCreateTemplate::DlgCreateTemplate(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::DlgCreateTemplate)
{
    ui->setupUi(this);

    m_scene = new QGraphicsScene(this);
    m_graphicsView = new GraphicsView2D();
    m_graphicsView->setScene(m_scene);

    ui->widget->layout()->addWidget(m_graphicsView);

    setWindowTitle(tr("模板角度修正"));

    ui->doubleSpinBox_angle->setMinimum(-360);
    ui->doubleSpinBox_angle->setMaximum(360);

    connect(ui->doubleSpinBox_angle, static_cast<void(QDoubleSpinBox::*)(double)>(&QDoubleSpinBox::valueChanged), this, &DlgCreateTemplate::AngleValueChanged);
    connect(ui->pushButton_apply, &QPushButton::clicked, this, &DlgCreateTemplate::OnApply);
    connect(ui->pushButton_create, &QPushButton::clicked, this, &DlgCreateTemplate::OnCreate);
    connect(m_scene, &QGraphicsScene::selectionChanged, this, &DlgCreateTemplate::SelecRectItem);
}

DlgCreateTemplate::~DlgCreateTemplate()
{
    delete ui;
}


void DlgCreateTemplate::SetCreateTemplateData(const  std::vector<std::pair<int, cv::RotatedRect>>& rects, const QPixmap& image, const std::string& ipe_params)
{
    m_scene->clear();
    m_src_image = nullptr;
    UpdataShowImage(image);
    UpdateRectItem(rects);
    UpdateEnable();

    m_ipe_params = ipe_params;
}

void DlgCreateTemplate::SetExistedTemplate(const QPixmap& image, const std::string& ipe_params)
{
    UpdataShowImage(image);
    m_graphicsView->FitWin();
    if (m_rect_items.size() > 0)
    {
        for (auto item : m_rect_items)
        {
            m_scene->removeItem(item);
        }
        m_rect_items.clear();
    }
    UpdateEnable();

    m_ipe_params = ipe_params;
}

void DlgCreateTemplate::resizeEvent(QResizeEvent* event)
{
    QDialog::resizeEvent(event);
    auto temp = m_scene->sceneRect();
    m_graphicsView->fitInView(m_scene->sceneRect(), Qt::KeepAspectRatio);
}

void DlgCreateTemplate::OnCreate()
{
    if (m_selected_item == nullptr)
    {
        QMessageBox::warning(this, "警告", "未选中截图框", QMessageBox::Ok);
        return;
    }
    QRectF rect;
    float angle;
    m_selected_item->GetRect(rect, angle);
    /** < 解决一个像素误差问题 */
    cv::RotatedRect rect_cv;
    rect_cv.center.x = std::round(rect.center().x() + m_selected_item->pos().x());
    rect_cv.center.y = std::round(rect.center().y() + m_selected_item->pos().y());
    rect_cv.size.width = rect.width();
    rect_cv.size.height = rect.height();
    rect_cv.angle = angle;

    int dir = m_selected_item->data(Qt::UserRole).toInt();

    emit SigCreateTemplate(rect_cv, dir, m_ipe_params);
    //float angle =m_rect_item->rotation();
    //auto pos  = m_rect_item->pos();
    //auto rect = m_rect_item->rect();

    //int left_top_x = m_src_rect.x() + pos.x();
    //int left_top_y = m_src_rect.y() + pos.y();

    //QRect dst_rect(left_top_x, left_top_y, rect.width(), rect.height());
}

void DlgCreateTemplate::OnApply()
{
    emit SigUpdateTemplate(m_ipe_params);
}

void DlgCreateTemplate::AngleValueChanged(double value)
{
    if (m_rect_items.size() > 0)
    {
        for (auto item : m_rect_items)
        {
            item->setRotation(value);
        }
        //m_rect_item->setRotation(value);
    }
}

void DlgCreateTemplate::UpdateEnable()
{
    if (m_rect_items.size() > 0)
    {
        ui->pushButton_apply->setEnabled(false);
        ui->pushButton_create->setEnabled(true);
        ui->doubleSpinBox_angle->setEnabled(true);
    }
    else
    {
        ui->pushButton_apply->setEnabled(true);
        ui->pushButton_create->setEnabled(false);
        ui->doubleSpinBox_angle->setEnabled(false);
    }
}

void DlgCreateTemplate::UpdataShowImage(const QPixmap& image)
{
    if (m_src_image == nullptr)
    {
        m_src_image = m_scene->addPixmap(image);
        m_src_image->setZValue(-1);
    }
    else
    {
        m_src_image->setPixmap(image);
        m_src_image->setZValue(-1);
    }

    m_graphicsView->SetImageSize(image.size());
}

void DlgCreateTemplate::UpdateRectItem(const  std::vector<std::pair<int, cv::RotatedRect>>& rects)
{
    m_rect_items.clear();
    for (int i = 0; i < rects.size(); i++)
    {
        auto cv_rect = rects[i].second.boundingRect2f();
        QRectF rect_f(cv_rect.x, cv_rect.y, cv_rect.width, cv_rect.height);
        ComGraphicsRectItem* item = new ComGraphicsRectItem(rect_f);
        //item->setRotation(rects[i].second.angle);
        item->setData(Qt::UserRole, rects[i].first);
        m_scene->addItem(item);
        m_rect_items.push_back(item);
    }
    m_scene->clearSelection();
    if (m_rect_items.size() > 0)
    {
        auto qrect = m_rect_items[0]->boundingRect();
        QRect new_rect(qrect.x() - qrect.width() / 2, qrect.y() - qrect.height() / 2, qrect.width() * 2, qrect.height() * 2);

        m_graphicsView->fitInView(new_rect, Qt::KeepAspectRatio);
        m_graphicsView->centerOn(m_rect_items[0]);
        m_rect_items[0]->setSelected(true);
    }


}

void DlgCreateTemplate::SelecRectItem()
{
    auto items = m_scene->selectedItems();
    if (items.size() > 0)
    {
        m_selected_item = dynamic_cast<ComGraphicsRectItem*>(items[0]);
        if (m_selected_item != nullptr)
        {
            ui->doubleSpinBox_angle->setValue(m_selected_item->rotation());
        }
    }
    else
    {
        m_selected_item = nullptr;
    }

}

void DlgCreateTemplate::ImageChanged(const QPixmap& image, const std::string& params)
{
    m_ipe_params = params;
    UpdataShowImage(image);
    UpdateEnable();
}

void DlgCreateTemplate::AngleValueChangedSlot(const float angle)
{
    ui->doubleSpinBox_angle->setValue(angle);
}

void ComGraphicsRectItem::GetRect(QRectF& rect, float& angle) const
{
    rect = this->rect();
    angle = this->rotation();
}
