#pragma once

#include "ui_showmarkresultimagewidget.h"
#include "viewergraphicsviewimpl.h"

#include <QDialog>
#include <QPixmap>
#include <QGraphicsScene>
#include <QShowEvent>
#include <QResizeEvent>


class ShowMarkResultImageWidget : public QDialog
{
    Q_OBJECT

public:
    ShowMarkResultImageWidget(QWidget *parent = Q_NULLPTR);
    ~ShowMarkResultImageWidget();

    void InitView();

    void SetMarkResult(std::map<std::string/*mark名称*/, std::unordered_map<int/*light id*/, cv::Mat>>& mark_results, std::map<std::string/*mark名称*/, float/*mark 分数*/>& mark_scores, 
        std::map<std::string/*mark名称*/, std::tuple<float/*x*/, float/*y*/, float/*width*/, float/*height*/>/*矩形框*/> mark_rects, int& mark_status);
    void CreatePixmapItem(QPixmap& pixmap, int current_x, int current_y);
    void CreateTextItem(std::string text, int current_x, int current_y);
    bool CreateEllipseItem(std::string mark_name, std::map<std::string/*mark名称*/,
        std::tuple<float/*x*/, float/*y*/, float/*width*/, float/*height*/>/*矩形框*/> mark_rects,
        float& out_left, float& out_top, int& current_x, int& current_y);
    std::string CreateShowString(std::string mark_name, std::map<std::string/*mark名称*/, float/*mark 分数*/>& mark_scores);
    cv::Mat CreateRectImage(cv::Mat src, std::string mark_name, std::map<std::string/*mark名称*/,
        std::tuple<float/*x*/, float/*y*/, float/*width*/, float/*height*/>/*矩形框*/> mark_rects, float& out_left, float& out_top);
    void ShowDialog(int mark_status);
    void ImageFitViewSlot();
private:
    QPixmap CvMatToQPixmap(const cv::Mat& src);
protected:
    void showEvent(QShowEvent* show);
private:
    Ui::ShowMarkResultImageWidget ui;
    QGraphicsScene* m_graphics_scene_mark = nullptr;
    ViewerGraphicsViewImpl* m_graphics_view_mark = nullptr;
    QFont font;
    QColor text_color;
    int max_width = 0;
    int max_height = 0;
};
