/*****************************************************************
 * @file   customtablelistview.h
 * @brief  自定义tableview，用于显示数据，可对数据进行排序和过滤，可接受任意数据类型进行显示和颜色渲染
 * @details
 * <AUTHOR>
 * @date 2025.4.12
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.4.12          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef C55862EA_79F9_4003_A241_13C95FDD906B
#define C55862EA_79F9_4003_A241_13C95FDD906B

//STD
//QT
#include <QWidget>
#include <QTableView>
#include <QSortFilterProxyModel>
//Custom
#include "customitemdatamodel.h"
//Third
namespace jrsaoi
{

    class CustomTableListView : public QWidget
    {
        Q_OBJECT

        public:
            CustomTableListView(QStringList horizontal_header_data_, QString show_list_name_,RowColorRule color_rule_, int filter_key_column_,QWidget *parent = nullptr);
            ~CustomTableListView();
            /**
            * @fun UpdateList
            * @brief 更新整个列表
            * @param data [IN] 整个列表数据
            * <AUTHOR>
            * @date 2025.2.10
            */
            void UpdateList(const std::vector<std::vector<QVariant>>& show_data);

            /**
             * @fun AddRowNewItems
             * @brief 在已有数据基础上添加新数据
             * @param data [IN] 新数据
             * <AUTHOR>
             * @date 2025.2.10
             */
            void AddRowNewItems(const std::vector<std::vector<QVariant>>& show_data);

            /**
             * @fun DeleteRowItemsFromCurrentRow
             * @brief 自当前行开始删除count行
             * @param row [IN] 当前行数
             * @param count [IN] 删除行数
             * <AUTHOR>
             * @date 2025.2.10
             */
            void DeleteRowItemsFromCurrentRow(int count);

            /**
             * @fun DeleteRowByColumnValue 
             * @brief 根据列值删除行
             * @param column [IN] 列
             * @param value [IN] 值
             * <AUTHOR>
             * @date 2025.4.12
             */
            void DeleteRowByColumnValue(int column, const QVariant& value);

            /**
             * @fun NextRow
             * @brief 下一行
             * @return 成功true,失败false
             * <AUTHOR>
             * @date 2025.2.10
             */
            bool NextRow();

            /**
             * @fun PreRow
             * @brief 上一行
             * @return 成功true,失败false
             * <AUTHOR>
             * @date 2025.2.10
             */
            bool PreRow();

            /**
             * @fun UpdateDataRowData
             * @brief 更新当前行数据
             * @param current_row_new_data
             * <AUTHOR>
             * @date 2025.2.14
             */
            void UpdateDataRowData(const std::vector<QVariant>& current_row_new_data);

            /**
             * @fun ClearAllData
             * @brief 清空所有数据
             * <AUTHOR>
             * @date 2025.3.14
             */
            void ClearAllData();

            /**
             * @fun FileterTextChanged 
             * @brief 根据设定的列的内容进行显示过滤
             * @param filter_text_ [IN] 输入的过滤文本
             * <AUTHOR>
             * @date 2025.4.29
             */
            void FileterTextChanged(const QString& filter_text_);

            std::vector<QVariant> GetCurrentRowData() const;
        signals:

            /**
             * @fun SigCurrentRowData
             * @brief 当前行发生变化
             * @param current_row_data [IN] 当前行数据
             * <AUTHOR>
             * @date 2025.2.10
             */
            void SigCurrentRowData(const std::vector<QVariant>& current_row_data);
        private slots:

        /**
         * @fun SlotCurrentRowChanged
         * @brief 当前行发生变化
         * @param selected [IN] 选中的行
         * @param deselected [IN] 取消选中的行
         * <AUTHOR>
         * @date 2025.2.10
         */
        void SlotCurrentRowChanged(const QItemSelection& selected, const QItemSelection& deselected);
        private:
            void InitView();
            void InitConnect();
            //!Member
            CustomDataModel* show_list_model_data;
            QStringList horizontal_header_data;
            QString show_list_name;
            QTableView* show_table_view;
            RowColorRule m_color_rule; /**< 渲染颜色规则*/
            QSortFilterProxyModel* filter_model; //!< 过滤模型
            int filter_key_column = 0; //!< 过滤的列,用于查询
};
}


#endif /* C55862EA_79F9_4003_A241_13C95FDD906B */
