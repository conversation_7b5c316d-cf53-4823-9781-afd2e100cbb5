stages:
  - test
  - deploy
 
build-job:       # This job runs in the build stage, which runs first.
  stage: build
  script:
    - echo "Start configuring and building with CMake"
    - mkdir build
    - cd build
    - cmake .. -G "Visual Studio 17 2022" -A x64
    - cmake --build . --config Release --parallel
    - echo "Build finished successfully."

unit-test-job:   # This job runs in the test stage.
  stage: test
  script:
      - echo "Running unit tests..."
      - cd build
      - ctest -C Release --output-on-failure
deploy-job:      # This job runs in the deploy stage.
  stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
  environment: production 
  script:
    - echo "Hello, G it Lab  Runner is work ing!" 