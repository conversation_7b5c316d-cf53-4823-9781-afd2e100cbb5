﻿/*****************************************************************//**
 * @file   EditView.h
 * @brief  pad编辑ui
 *
 * <AUTHOR>
 * @date   2024.11.26
 *********************************************************************/

#ifndef EditView_H
#define EditView_H
 //prebuild
#include "pch.h"
//#include "viewparam.hpp"
#include <QWidget>

namespace Ui
{
    class EditView;
}

namespace jrsaoi
{
    class EditView : public QWidget
    {
        Q_OBJECT
    public:
        explicit EditView(QWidget* parent = nullptr);
        ~EditView();

        enum class EditWidget
        {
            Direction,
            X,
            Y,
            Width,
            Height,
            <PERSON>le,
            <PERSON><PERSON>,
            <PERSON>s,
            Is_Detect
        };

        /**
         * @brief 更新界面
         */
        int UpdateView(const ShowTableParamBasePtr& param_);



    signals:
        /**
         * @brief 焊盘参数更新信号
         * @param param
         * @date 2024.11.26
         * <AUTHOR>
         */
        void SigUpdateView(const jrsdata::ViewParamBasePtr& param);

    private slots:

        /**
         * < .pad 创建响应函数
         */
        void SlotHandlePad();
        /** <更新pad类型更换 */
        void SlotAddPadTypeChange(int current_index);
        /** <将编辑信息更新到界面上 */
        void SlotUpdateEditData(double value);
        /** <将更改的信息更新到render*/
        void SlotUpdateToRender();
        /**
         * @fun SlotUpdateEqualValue
         * @brief  检测框坐标等价于本体坐标
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SlotUpdateDetectWindowValue();
        /**
         * @fun SlotUpdateComponentDegree
         * @brief 更新元件坐标
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SlotUpdateComponentDegree();
    private:
        void Init();
        /**
         * @fun InitView
         * @brief 界面更新
         * <AUTHOR>
         * @date 2025.2.19
         */
        void InitView();
        /**
         * @fun InitHideView
         * @brief  隐藏界面
         * <AUTHOR>
         * @date 2025.3.16
         */
        void InitHideView();
        /**
         * @fun InitConnect
         * @brief  初始化 connect
         * <AUTHOR>
         * @date 2025.3.16
         */
        void InitConnect();

        /**
         * 更新相应界面
         */
        void UpdateWidgets(const jrsdata::CommonEditData& data_, jrsdata::EditViewData::UpdateWidgets widget_type_);

        /** <构建信号 */
        void EmitRenderEvent(const std::string& event_name, const jrsdata::RenderEventParamPtr& param);
        /**
         * @fun LockTabPage
         * @brief 从start_index_ 所起page
         * @param start_index_
         * <AUTHOR>
         * @date 2025.3.17
         */
        void EditView::LockTabPage(int start_index_);
        /**
         * @fun SetTabState
         * @brief 设置tab
         * @param currentIndex
         * @param tab0
         * @param tab1
         * @param tab2
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SetTabState(int currentIndex, bool tab0, bool tab1, bool tab2);
        /**
         * @fun SetEnabledPadWidget
         * @brief  pad widget 的部分控件是否可用
         * @param enable_
         * <AUTHOR>
         * @date 2025.3.18
         */
        void SetEnabledPadWidget(bool enable_);
        /**
         * @fun SetRadioButtonChecked
         * @brief  设置方向
         * @param widget
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SetRadioButtonChecked(QWidget* widget);
        /**
         * @fun SetComboBoxCurrentIndex
         * @brief 设置pad 类型
         * @param index
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SetComboBoxCurrentIndex(int index);
        /**
         * @fun UpdateComponentWidgets
         * @brief 更新元件界面
         * @param edit_view_ptr
         * <AUTHOR>
         * @date 2025.3.17
         */
        void UpdateComponentWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr);
        /**
         * @fun UpdatePadWidgets
         * @brief 更新pad 界面
         * @param edit_view_ptr
         * <AUTHOR>
         * @date 2025.3.17
         */
        void UpdatePadWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr);
        /**
         * @fun UpdateDetectWindowWidgets
         * @brief 更新检测框界面
         * @param edit_view_ptr
         * <AUTHOR>
         * @date 2025.3.17
         */
        void UpdateDetectWindowWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr);
        /**
         * @fun RemoveLayout
         * @brief  移除Layuer，隐藏布局，可能之后会用到
         * @param layout
         * <AUTHOR>
         * @date 2025.3.25
         */
        void RemoveLayout(QLayout* layout);
    private:
        const std::map<jrsdata::ComponentUnit::Direction, std::string> _direction_map =
        {
            {jrsdata::ComponentUnit::Direction::UNKNOWN, "自动识别"},
            {jrsdata::ComponentUnit::Direction::UP, "UP"},
            {jrsdata::ComponentUnit::Direction::DOWN, "DOWN"},
            {jrsdata::ComponentUnit::Direction::RIGHT, "RIGHT"},
            {jrsdata::ComponentUnit::Direction::LEFT, "LEFT"},
            {jrsdata::ComponentUnit::Direction::INSIDE, "INSIDE"}
        };
        Ui::EditView* ui;

        jrsdata::PadEventParam pad_event_param;
        std::unordered_map<jrsdata::EditViewData::UpdateWidgets, std::unordered_map<EditWidget, QWidget*>> _widget_and_widgets;
        std::unordered_map<jrsdata::ComponentUnit::Direction, QWidget*> _detect_window_direction_radios;
        jrsdata::EditViewDataPtr _edit_veiw_data_ptr;
    };
}
#endif //! EditView_H