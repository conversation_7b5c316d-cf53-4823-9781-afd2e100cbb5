/**********************************************************************
 * @brief  定义一些基本的算法参数.
 *
 * @file   algodefine.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef ALGO_DEFINE_H
#define ALGO_DEFINE_H

constexpr double A_E = 2.71828182845904523536; ///< e

constexpr double A_PI = 3.14159265358979323846;        ///< pi
constexpr double A_PI_OVER_2 = 1.57079632679489661923; ///< pi / 2
constexpr double A_PI_2 = 6.2831853071795;             ///< pi * 2

constexpr double A_1_OVER_PI = 0.318309886183790671538; ///< 1 / pi
constexpr double A_2_OVER_PI = 0.636619772367581343076; ///< 2 / pi

constexpr double A_DEG_TO_RAD_FACTOR = 0.01745329251994329577;  ///< pi / 180.0
constexpr double A_RAD_TO_DEG_FACTOR = 57.29577951308232286465; ///< 180.0 / pi
//#define A_DEG_TO_RAD(v) (v * A_DEG_TO_RAD_FACTOR)               ///< 角度转弧度
//#define A_RAD_TO_DEG(v) (v * A_RAD_TO_DEG_FACTOR)               ///< 弧度转角度

constexpr double A_EPSILON = 1e-5;                                                     ///< 默认精度
//#define A_FLOAT_EQUAL(x, y, epsilon) (((x) < (y) ? (y) - (x) : (x) - (y)) < (epsilon)) ///< 对比两个浮点数是否相同
//#define A_FLOAT_EQUAL_DEFAULT(x, y) A_FLOAT_EQUAL(x, y, A_EPSILON)                     ///< 以默认精度对比两个浮点数是否相同

/**
 * @brief  角度转弧度.
 * @fun    A_DEG_TO_RAD
 * @param  degrees 角度 单浮点型
 * @return 弧度
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline float A_DEG_TO_RAD(float degrees)
{
    return degrees * float(A_DEG_TO_RAD_FACTOR);
}

/**
 * @brief  角度转弧度.
 * @fun    A_DEG_TO_RAD
 * @param  degrees 角度 双浮点型
 * @return 弧度
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline double A_DEG_TO_RAD(double degrees)
{
    return degrees * (A_DEG_TO_RAD_FACTOR);
}

/**
 * @brief  弧度转角度.
 * @fun    A_RAD_TO_DEG
 * @param  radians 弧度 单浮点型
 * @return 角度
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline float A_RAD_TO_DEG(float radians)
{
    return radians * float(A_RAD_TO_DEG_FACTOR);
}

/**
 * @brief  弧度转角度.
 * @fun    A_RAD_TO_DEG
 * @param  radians 弧度 双浮点型
 * @return 角度
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline double A_RAD_TO_DEG(double degrees)
{
    return degrees * (A_RAD_TO_DEG_FACTOR);
}
/**
 * @brief  判断两个浮点数是否相等.
 * @fun    A_FLOAT_EQUAL
 * @param  a 一个浮点数
 * @param  b 另一个浮点数
 * @param  epsilon 精度
 * @return
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline bool A_FLOAT_EQUAL(double a, double b, double epsilon)
{
    return (((a) < (b) ? (b)-(a) : (a)-(b)) < (epsilon));
}

constexpr inline bool A_FLOAT_EQUAL(float a, float b, float epsilon)
{
    return (((a) < (b) ? (b)-(a) : (a)-(b)) < float(epsilon));
}

/**
 * @brief  判断两个浮点数是否相等,采用默认精度.
 * @fun    A_FLOAT_EQUAL_DEFAULT
 * @param  a 一个浮点数
 * @param  b 另一个浮点数
 * @return
 *
 * @date   2024.09.29
 * <AUTHOR>
 */
constexpr inline bool A_FLOAT_EQUAL_DEFAULT(double a, double b)
{
    return (((a) < (b) ? (b)-(a) : (a)-(b)) < (A_EPSILON));
}

constexpr inline bool A_FLOAT_EQUAL_DEFAULT(float a, float b)
{
    return (((a) < (b) ? (b)-(a) : (a)-(b)) < (float(A_EPSILON)));
}

/**
 * @brief  限制值在区间内.
 * @param v 要限制的值
 * @param min 最小值
 * @param max 最大值
 * @return 限制后的值
 */
template <class T>
constexpr T A_CLAMP(const T& min, const T& v, const T& max)
{
    if (max < min) return v;
    return v < min ? min : (v > max ? max : v);
}

#endif //! ALGO_DEFINE_H