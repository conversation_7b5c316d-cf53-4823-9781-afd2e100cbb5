#pragma warning(disable: 4172)
#include "algoexecute.h"
#include "coreapplication.h"
#include "jsonoperator.hpp"
#include "algoparamparse.h"
#include "fileoperation.h"
//Third
#include "iguana/iguana.hpp"

namespace jrsalgo
{
    AlgoFactoryPtr AlgoExecute::algo_factory_ptr = nullptr;

    AlgoExecute::AlgoExecute()
    {
        InitMember();
    }
    AlgoExecute::~AlgoExecute()
    {
    }
    void AlgoExecute::SetAlgoFactoryPtr(const AlgoFactoryPtr& ptr)
    {
        algo_factory_ptr = ptr;

    }
    int AlgoExecute::SetAlgoDynamicOutParamValue(const jrsparam::AlgoExecuteResultParam& execute_algo_result_param_, const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_, const nlohmann::json& json_data_, const std::string& algo_name_)
    {
        (void)execute_algo_result_param_;
        auto param_ptr_temp = std::dynamic_pointer_cast<iguana::base>(param_ptr_);
        if (!param_ptr_temp)
        {
            return jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE;

        }
        std::string spefic_path_config = "/algo/" + algo_name_ + "/params";
        auto spefic_algo_param_json = GetSpeficAlgoConfigJson(json_data_, spefic_path_config);
        if (spefic_algo_param_json.empty())
        {
            return jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE;
        }
        /** . 因为算法配置参数的配置文件中参数字段是数组存储的 */
        auto algo_param_json = spefic_algo_param_json[0];
        if (algo_param_json.contains("output_params"))
        {
            for (auto& [specific_param_name, speficif_param_value] : algo_param_json["output_params"][0].items())
            {
                std::string spefic_param_value_str;

                if (speficif_param_value.is_string())
                {
                    spefic_param_value_str = speficif_param_value.get<std::string>();
                }
                else
                {
                    std::cerr << "invaild param: spefic_param_value_str is not string ,please check json file." << std::endl;

                    break;
                }
           /*     if (specific_param_name == "result_contours_point")
                {
                    AlgoParamParse::SetSpeficParamValue(param_ptr_temp, spefic_param_value_str, execute_algo_result_param_.result_contours_point);

                }*/
            }
        }
        return jrscore::AOI_OK;
    }
    void AlgoExecute::InitMember()
    {

    }
    int AlgoExecute::ExecuteSpecificAlgoDrive(const std::string& algo_name_, jrsoperator::OperatorParamBasePtr& execute_param)
    {
        auto algo_specific_temp = algo_factory_ptr->GetSpecificAlgoDrive(algo_name_);
        if (algo_specific_temp)
        {
            return algo_specific_temp->ExecuteOperator(execute_param);
        }
        else
        {
            return jrscore::AlgorithmError::E_AOI_ALG_ALGO_NOT_FOUND;
        }
    }

    jrsoperator::OperatorParamBasePtr AlgoExecute::ExecuteSpeficAlgo(const jrsparam::ExecuteAlgoParam& execute_algo_param_, const nlohmann::json& json_data_)
    {
        if (execute_algo_param_.algo_name.empty())
        {
            return nullptr;
        }
        /**. 获取算法参数的指针  */
        auto algo_param_test = AlgoParamParse::CreateAlgoParamPtr(execute_algo_param_.algo_name);
        if (!algo_param_test)
        {

            return nullptr;
        }
        /** . 获取算法配置参数的json */
        std::string spefic_path_config = "/algo/" + execute_algo_param_.algo_name + "/params";
        auto spefic_algo_param_json = GetSpeficAlgoConfigJson(json_data_, spefic_path_config);

        SetAlgoStaticValue(execute_algo_param_, algo_param_test);
        /** .解析算法参数文件，为算法动态参数赋值  */
        //TODO 修改直接传递新版本算法版本 by baron_zhang 2024-12-20 
        //SetAlgoDynamicValue ( execute_algo_param_,algo_param_test,spefic_algo_param_json );
        SetAlgoValue(execute_algo_param_, algo_param_test);
        /** .执行算法 */
        auto param_temp = std::dynamic_pointer_cast<jrsoperator::OperatorParamBase>(algo_param_test);

        auto execute_res = ExecuteSpecificAlgoDrive(execute_algo_param_.algo_name, param_temp);
        if (!execute_res)
        {
            Log_ERROR("算法检测失败！");
        }

        return param_temp;
    }
    jrsparam::AlgoExecuteResultParam AlgoExecute::GetSpeficAlgoExecuteResult(const jrsoperator::OperatorParamBasePtr& param_ptr_, const nlohmann::json& json_data_, const std::string& algo_name_)
    {
        jrsparam::AlgoExecuteResultParam temp_result_param{};
        auto param_ig_base = std::dynamic_pointer_cast<iguana::base>(param_ptr_);

        /** . 获取算法输出的配置json */
        std::string spefic_path_config = "/algo/" + algo_name_ + "/params";
        auto spefic_algo_param_json = GetSpeficAlgoConfigJson(json_data_, spefic_path_config);
        if (spefic_algo_param_json.empty() || !param_ig_base)
        {
            return temp_result_param;
        }
        //! 因为所有的算法都有OK和NG两个结果，所以这里直接根据父类里面的结果直接进行判断，不需要通过配置文件获取判断
        temp_result_param.result_algo_status = std::none_of(param_ptr_->output_detect_rects.begin(), param_ptr_->output_detect_rects.end(),
            [](const auto& value)
            {
                return !value.status;
            }) && !param_ptr_->output_detect_rects.empty();

        auto algo_out_param_json = spefic_algo_param_json[0];
        if (algo_out_param_json.contains("output_params"))
        {
            for (auto& [spefic_out_param_name, spefic_out_param_value] : algo_out_param_json["output_params"][0].items())
            {
                std::string spefic_out_param_value_str;
                if (spefic_out_param_value.is_string())
                {
                    spefic_out_param_value_str = spefic_out_param_value.get<std::string>();
                }
                else
                {
                    std::cerr << "invaild param: spefic_param_value_str is not string ,please check json file." << std::endl;

                    break;
                }

                if (spefic_out_param_name == "result_str")
                {
                    auto result_str_temp = AlgoParamParse::GetSpeficParamValue<std::string>(param_ig_base, spefic_out_param_value_str);
                    temp_result_param.result_str = jrsparam::ConvertVariantTo<std::string>(result_str_temp);
                }
                else if (spefic_out_param_name == "result_x_coordinate")
                {
                    auto result_str_temp = AlgoParamParse::GetSpeficParamValue<float>(param_ig_base, spefic_out_param_value_str);
                    temp_result_param.result_x_coordinate = jrsparam::ConvertVariantTo<float>(result_str_temp);
                }
                else if (spefic_out_param_name == "result_y_coordinate")
                {
                    auto result_str_temp = AlgoParamParse::GetSpeficParamValue<float>(param_ig_base, spefic_out_param_value_str);
                    temp_result_param.result_y_coordinate = jrsparam::ConvertVariantTo<float>(result_str_temp);
                }
                else if (spefic_out_param_name == "result_score")
                {
                    auto result_str_temp = AlgoParamParse::GetSpeficParamValue<float>(param_ig_base, spefic_out_param_value_str);
                    temp_result_param.result_score = jrsparam::ConvertVariantTo<float>(result_str_temp);
                }

            }

        }


        return temp_result_param;


    }



    bool AlgoExecute::GetAlgoExecuteResultStatus(const jrsoperator::OperatorParamBasePtr& param_ptr_)
    {
        if (!param_ptr_)
        {
            return false;
        }
        return std::none_of(param_ptr_->output_detect_rects.begin(), param_ptr_->output_detect_rects.end(),
            [](const auto& value)
            {
                return !value.status;
            }) && !param_ptr_->output_detect_rects.empty();
    }

    std::string AlgoExecute::GetAlgoExecuteRectInfo(const jrsoperator::OperatorParamBasePtr& param_ptr_)
    {
        if (!param_ptr_)
        {
            return "nullptr";
        }
        jrsparam::AlgoExcuteRectsParam temp_rects_param;
        temp_rects_param.component_rect = param_ptr_->component_rect;
        temp_rects_param.original_detect_win_rect = param_ptr_->ori_detect_rect;
        temp_rects_param.expand_detect_win_rect = param_ptr_->detect_rect;
        temp_rects_param.pad_rects = param_ptr_->pad_rects;
        temp_rects_param.original_sub_win_rects = param_ptr_->ori_sub_detect_rects;
        temp_rects_param.expand_sub_win_rects = param_ptr_->sub_detect_rects;
        std::string algo_execute_rects_param_json;
        iguana::to_json(temp_rects_param, algo_execute_rects_param_json);

        return algo_execute_rects_param_json;
    }

    nlohmann::json AlgoExecute::GetSpeficAlgoConfigJson(const nlohmann::json& json_data_, const std::string& config_path)
    {
        return jrscore::GetSpeficJsonValue(json_data_, config_path);
    }

    int AlgoExecute::SetAlgoStaticValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_)
    {
        AlgoParamParse::SetAlgoParamFromString(param_ptr_, execute_algo_param_.algo_param);
        return jrscore::AOI_OK;
    }

    int AlgoExecute::SetAlgoDynamicValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_, const nlohmann::json& json_data_)
    {
        (void)param_ptr_.get();
        (void)execute_algo_param_;

        if (json_data_.empty())
        {
            return jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE;
        }
        /** . 因为算法配置参数的配置文件中参数字段是数组存储的 */
        auto algo_param_json = json_data_[0];
        if (algo_param_json.contains("dynamic_input_params"))
        {
            for (auto& [specific_param_name, speficif_param_value] : algo_param_json["dynamic_input_params"][0].items())
            {
                std::string spefic_param_value_str;

                if (speficif_param_value.is_string())
                {
                    spefic_param_value_str = speficif_param_value.get<std::string>();
                }
                else
                {
                    std::cerr << "invaild param: spefic_param_value_str is not string ,please check json file." << std::endl;

                    break;
                }
                if (specific_param_name == "input_img")
                {
                    AlgoParamParse::SetSpeficParamValue(param_ptr_, spefic_param_value_str, execute_algo_param_.input_img);

                }
                else if (specific_param_name == "template_data")
                {
                    AlgoParamParse::SetSpeficParamValue(param_ptr_, spefic_param_value_str, execute_algo_param_.template_data);
                }
                else if (specific_param_name == "component_rect")
                {
                    AlgoParamParse::SetSpeficParamValue(param_ptr_, spefic_param_value_str, execute_algo_param_.component_rect);
                }

            }
        }
        return jrscore::AOI_OK;
    }



    int AlgoExecute::SetAlgoValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_)
    {
        auto param_temp = std::dynamic_pointer_cast<jrsoperator::OperatorParamBase>(param_ptr_);
        param_temp->component_rect = execute_algo_param_.component_rect;
        param_temp->detect_rect = execute_algo_param_.expand_detect_win_rect;
        param_temp->ori_detect_rect = execute_algo_param_.original_detect_rect;
        param_temp->input_image = execute_algo_param_.input_img;
        param_temp->template_data = execute_algo_param_.template_data;
        param_temp->x_resolution = execute_algo_param_.x_resolution;
        param_temp->y_resolution = execute_algo_param_.y_resolution;
        param_temp->pad_rects = execute_algo_param_.pad_rects;
        param_temp->ori_sub_detect_rects = execute_algo_param_.original_sub_win_rects;
        param_temp->sub_detect_rects = execute_algo_param_.expand_sub_win_rects;
        param_temp->input_mask_image = execute_algo_param_.input_mask_image;
        param_temp->hom_matrix = execute_algo_param_.correction_matrixes;

        return jrscore::AOI_OK;
    }
}
