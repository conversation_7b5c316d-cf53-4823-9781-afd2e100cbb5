#include "detectview.h"

DetectView::DetectView(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::DetectView)
{
    ui->setupUi(this);
    ui->detect_tab->setContentsMargins(0, 0, 0, 0);
    vec_track_numbers = { jrsdata::TRACK_1,jrsdata::TRACK_2,jrsdata::TRACK_3,jrsdata::TRACK_4,jrsdata::TRACK_5,
                    jrsdata::TRACK_6,jrsdata::TRACK_7,jrsdata::TRACK_8,jrsdata::TRACK_9,jrsdata::TRACK_10 };
    InitConnect();
}

DetectView::~DetectView()
{
    delete ui;
}

void DetectView::UpdateView(const jrsdata::OperateViewParamPtr ptr)
{
    if (ptr != nullptr)
    {
        if (ptr->detect_statistics_view_param.detect_table_param_board.has_value())
        {
            jrsdata::TRACK_NUMBER track_index = jrsdata::TRACK_NUMBER(ptr->detect_statistics_view_param.detect_table_param_board->track_id);
            if (map_one_detect_view[track_index] != nullptr)
            {
                map_one_detect_view[track_index]->UpdateView(ptr);
            }
        }
        else
        {
            for (auto one_detect_view : map_one_detect_view)
            {
                one_detect_view.second->UpdateView(ptr);
            }
        }
    }
}

void DetectView::InitConnect()
{
    connect(this, &DetectView::SigUpdateView, this, [&](const jrsdata::OperateViewParamPtr ptr)
        {
            if (ptr->config_setting_param.event_name == jrsaoi::OPERATE_MOTION_SETTING_UPDATE)
            {
                int track_count = ptr->config_setting_param.motion_setting.trackCout;//后面替换成读取到的轨道数目
                //track_count = 1;
                for (int i = 0; i < track_count && i < vec_track_numbers.size(); i++)
                {
                    OneDetectView* widget = new OneDetectView(vec_track_numbers.at(i), this);
                    map_one_detect_view[vec_track_numbers.at(i)] = widget;
                    ui->detect_tab->addTab(widget, QString::fromWCharArray(L"轨道%1").arg(vec_track_numbers.at(i)));
                }
                for (auto it = map_one_detect_view.begin(); it != map_one_detect_view.end(); it++)
                {
                    connect(this, &DetectView::SigUpdateView, it->second, &OneDetectView::UpdateView);
                    connect(it->second, &OneDetectView::SigDetectViewChangeTrigger, this, &DetectView::SigDetectViewChangeTrigger);
                }
            }
        });
}