﻿/*********************************************************************
 * @brief  常用文件处理函数.
 *
 * @file   fileoperation.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef JTOOLS_FILEOPERATION_H
#define JTOOLS_FILEOPERATION_H
#include "jtoolsapi.hpp"

#include <string>
#include <vector>
#ifdef _WIN32
#include <windows.h>
#else
#include <sys/statvfs.h>
#endif
namespace jtools
{
    struct DiskSpaceInfo
    {
        uint64_t total_bytes;     /**< 总容量（字节） */
        uint64_t available_bytes; /**< 可用容量（字节） */
        bool success;             /**< 是否成功获取 */
    };

    class JT_EXPORTS FileOperation
    {
    public:
        /**
         * @brief  判断输入路径是否是一个合理的路径字符串.
         *
         * @fun    IsValidPath
         * @param  path
         * @param  exactPath 是否需要是绝对路径
         * @return 合理返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsValidPath(const std::string& path, bool exactPath = false);

        /**
         * @brief  判断路径是否存在.
         *
         * @fun    IsPathExist
         * @param  path
         * @return 存在返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsPathExist(const char* path);

        /**
         * @brief  判断路径是否是目录并且存在.
         *
         * @fun    IsDir
         * @param  path
         * @return 满足条件返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsDir(const std::string& path);

        /**
         * @brief  判断路径是否是文件并且存在.
         *
         * @fun    IsFile
         * @param  path
         * @return 满足条件返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsFile(const std::string& path);

        /**
         * @brief  判断文件名是否合法.
         *
         * @fun    IsVaildFileName
         * @param  path
         * @return 满足条件返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsVaildFileName(const std::string& path);

        /**
         * @brief  判断文件的扩展名是否支持.
         *
         * @fun    IsFileExtensionSupport
         * @param  path
         * @param  strExtension 扩展名字符串
         * @return 支持返回true
         *
         * @note   支持扩展名的写法-> ".jpg .xml .txt" 或 "*.png *.xlsx *.mp3"
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool IsFileExtensionSupport(const std::string& path, const std::string& strExtension);

        /**
         * @brief  创建文件路径,会逐层创建.
         *
         * @fun    JRSCreateDirectory
         * @param  path
         * @return 正常执行返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool JRSCreateDirectory(const std::string& path);

        /**
         * @brief  删除文件路径.
         *
         * @fun    DeleteDirectory
         * @param  path
         * @return 正常执行返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool DeleteDirectory(const std::string& path);

        /**
         * @brief  删除路径下的所有文件及文件夹.
         *
         * @fun    DeleteAllInDirectory
         * @param  path
         * @return
         *
         * @note   大概是节约了一个创建目录的时间
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool DeleteAllInDirectory(const std::string& path);

        // 移除文件
        static bool RemoveFile(const std::string& filepath);

        /**
         * @brief  拷贝源路径到指定路径下（无论源路径指代的是文件还是文件夹）.
         *
         * @fun    CopyFileOrDirectory
         * @param  source_path  源路径
         * @param  destination_path  目标路径
         * @return 正常执行返回true
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool CopyFileOrDirectory(const std::string& source_path, const std::string& destination_path);

        /**
         * @brief  获取当前工作目录.
         *
         * @fun    GetCurrentWorkingDirectory
         * @return 当前工作目录
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string GetCurrentWorkingDirectory();

        /**
         * @brief  在指定目录下查找target.
         *
         * @fun    GetTargetFilesInDir
         * @param  vecTargetFiles
         * @param  path 目标
         * @param  target 查找目标
         * @param  recursive 设置是否遍历子目录
         * @return 正常执行返回true
         *
         * @note   文件名中只要包含target就会找到,在扩展名中也会被找到
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool GetTargetFilesInDir(std::vector<std::string>& vecTargetFiles, const std::string& path, const std::string& target, bool recursive);

        /**
         * @brief  从路径上拆分出文件名.
         *
         * @fun    GetFileNameFromPath
         * @param  path
         * @param  isWithExtension 是否带扩展名
         * @param  isExist 是否检查文件存在，可以区分目录和无扩展名的文件
         * @return 文件名 异常时返回空字符串
         *
         * @note   经过考虑，默认不检查输入的路径是否是有效存在的文件，
         *         那意味着只能检查真实路径，但也导致会将目录识别为文件名
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string GetFileNameFromPath(const std::string& path, bool isWithExtension, bool isExist = false);

        /**
         * @brief  从路径上拆分出文件名和扩展名.
         *
         * @fun    GetFileNameAndExtension
         * @param  path
         * @return first 文件名/second 扩展名
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::pair<std::string, std::string> GetFileNameAndExtension(const std::string& path);

        /**
         * @brief  重命名文件或目录.
         * @fun    RenameFileOrDirectory
         * @param  old_path 旧路径
         * @param  new_path 新路径，需包含完整路径
         * @return 新/旧路径不存在，没有文件权限，路径名过长等情况下会失败
         *
         * @note   如果指定的路径不一致，文件将会被移动到新指定的路径下
         * @date   2024.04.03
         * <AUTHOR>
         */
        static bool RenameFileOrDirectory(const std::string& old_path, const std::string& new_path);

        /**
         * @fun ReadFileDatas
         * @brief 读取文件到std::string中
         * @param file_name 文件名
         * @param datas 输出读取到的数据
         * @return
         * @date 2025.4.22
         * <AUTHOR>
         */
        static bool ReadFileDatas(const std::string file_name, std::string& datas);
        /**
         * @fun GetFileOrDirNames
         * @brief 获取文件名和文件夹名称
         * @param directory_path
         * @param is_read_dir_name
         * @return
         * @date 2024.9.4
         * <AUTHOR>
         */
        static std::vector<std::string> GetFileOrDirNames(const std::string& directory_path, bool is_read_dir_name);


        /**
         * @fun GetDiskSpace
         * @brief 获取磁盘空间信息
         * @param path 磁盘路径
         * @return
         * <AUTHOR>
         * @date 2025.5.14
         */
        static DiskSpaceInfo GetDiskSpace(const std::string& path);


        /**
         * @fun ExtractDiskRoot
         * @brief window 或linux 摘取盘符数据
         * @param path 路径信息
         * @return
         * <AUTHOR>
         * @date 2025.5.14
         */
        static std::string ExtractDiskRoot(const std::string& path);

        /**
         * @fun CheckDiskCapacity
         * @brief  检测磁盘容量是否足够
         * @param path_ 磁盘路径
         * @param capacity_size_ 默认大于10G返回true
         * @return
         * <AUTHOR>
         * @date 2025.5.14
         */
        static bool CheckDiskCapacity(const std::string& path_, int capacity_size_ = 10);

        /**
        * @brief 判断这个文件夹内是否已经有相同的文件了
        *
        * @param folder_path_ 文件夹路径
        * @param name_or_ext_ 文件名或扩展名（如 ".txt" 或 "abc.txt"）
        * @return true 已有文件
        * @return false 没有文件
        */
        static bool HasFileByNameOrExt(const std::string& folder_path_, const std::string& name_or_ext_);
    private:
        /**
        * @fun GetDriveLetter
        * @brief windiws 下获取盘符信息
        * @param path  路径地址
        * @return
        * <AUTHOR>
        * @date 2025.5.14
        */
        static std::string GetDriveLetter(const std::string& path);

        /**
         * @fun GetMountRoot
         * @brief  linux 下获取根目录
         * @param path
         * @return
         * <AUTHOR>
         * @date 2025.5.14
         */
        static std::string GetMountRoot(const std::string& path);

    };
}

#endif // JTOOLS_FILEOPERATION_H
