
#ifndef EVENT_PARAM_HPP
#define EVENT_PARAM_HPP
enum  WheelType
{
    ZoomIn = 1,
    ZoomOut = -1,
    None = 0
};
/**
 * @enum  MouseEventValue
 * @brief 封装Qt鼠标事件
 */
struct MouseEventValue
{
    enum MouseState
    {
        press = 0x00,
        move = 0x01,
        release = 0x02,
        clicked = 0x03,
        wheel = 0x04,
    };
    WheelType type;
    int cx;
    int cy;
    int px;
    int py;
    int step;   /**< 间隔数 */
    MouseState state;

    MouseEventValue(WheelType type_, int cx_, int cy_, int px_, int py_, int step_, MouseState state_)
        : type(type_), cx(cx_), cy(cy_), px(px_), py(py_), step(step_), state(state_)
    {
    }
    MouseEventValue() : type(WheelType::None), cx(0), cy(0), px(0), py(0), step(0), state(MouseState::wheel) {}
};

#endif //! EVENT_PARAM_HPP