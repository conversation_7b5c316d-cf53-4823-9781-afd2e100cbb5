﻿/*****************************************************************//**
 * @file   idatabase.h
 * @brief  数据库操作接口
 * @details 根据接口进行操作相应数据库
 * <AUTHOR>
 * @date   April 2024
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>April 2024         <td>   <td>HJC                       <td><EMAIL> <td>
 *   @copyright 版权 CopyRight (C), 2024-2025.
 * *********************************************************************/

#ifndef _IDATABASE_H_
#define _IDATABASE_H_

#include <chrono>
#include <functional>
#include <string>
#include <string_view>
#include <vector>
#include <set>
#include <map>
#include <memory>
//#include "plog/Log.h"
namespace jrsdatabase {

    template <typename DB>
    class IDatabase {
    public:
        IDatabase() = default;
        IDatabase(const IDatabase&) = delete;
        virtual ~IDatabase() 
        {
            Disconnect(); 
        }
        /**
         * @fun CreateDatabase 
         * @brief   链接数据库时进行检测，如果数据库存在则链接，如果不存在则创建
         * @param db_host  数据库地址
         * @param user_hame  用户名
         * @param password   密码
         * @param db_name  数据库名称
         * @return 
         * @date 2024.7.10
         * <AUTHOR>
         */
        bool CreateDatabase(const std::string& db_host, const std::string& user_hame, const std::string& password, const std::string& db_name)
        {
            return db_.CreateDatabase(db_host, user_hame, password, db_name);
        }
        /**
         * @fun Connect
         * @brief 连接数据库
         * @detail 可以连接不同的数据库。
         * @param ...args [IN]连接数据库参数。
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        template <typename... Args>
        int Connect(Args &&...args) 
        { 
            //LOGD << "this test db address::" << &db_ << std::endl;
            return db_.Connect(std::forward<Args>(args)...); 
        }
        /**
         * @fun Disconnect
         * @brief 断开连接数据库
         * @return  成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        int Disconnect() 
        { 
            return db_.Disconnect(); 
        }
#pragma region Database
        /**
         * @fun AlterDatabase
         * @brief 更改数据库字符集
         * @detail 传入子类设定好的结构体进行更改，需要在root用户下进行
         * @param data [IN]不同的数据库传入不同的结构体
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        template<typename T>
        int AlterDatabase(const T& data) 
        {
            return db_.AlterDatabase(data);
        }
        /**
         * @fun CreateDatabase
         * @brief 创建数据库
         * @detail 传入数据库名称，需要在root用户下进行
         * @param db_name [IN]数据库名称
         * @return 成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        int CreateDatabase(const std::string& db_name) 
        {
            return db_.CreateDatabase(db_name);
        }
        /**
         * @fun DeleteDatabase
         * @brief 删除数据库
         * @detail 传入数据库名称，需要在root用户下进行
         * @param db_name [IN]数据库名称
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        int DeleteDatabase(const std::string& db_name) 
        {
            return db_.DeleteDatabase(db_name);
        }
        /**
         * @fun ShowDatabases
         * @brief 显示所有数据库
         * @param res [OUT]所有数据库表
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        int ShowDatabases(std::vector<std::tuple<std::string>>& res) 
        {
            return db_.ShowDatabases(res);
        }
#pragma endregion
#pragma region Table


        template<typename T>
        int AlterTablePrimaryKey()
        {
            return db_.AlterTablePrimaryKey<T>();
        } 

        /**
        * @fun QueryTableFields
        * @brief 查询表中字段的属性和详细信息
        * @param table_name [IN]数据表名称
        * @param res [OUT]查询结果
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */ 
        template<typename QUERYTFIELDRESULT>
        int QueryTableFields(const std::string& table_name, std::vector<QUERYTFIELDRESULT>&res) 
        {
            return db_.QueryTableFields(table_name, res);
        }
      
        /**
         * @fun AlterTableName
         * @brief 更改表名称
         * @param t_name [IN]旧表名
         * @param t_new_name [IN]新表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        int AlterTableName(const std::string& t_name, const std::string& t_new_name) 
        {
            return db_.AlterTableName(t_name, t_new_name);
        };
        /**
         * @fun AlterTableFieldName
         * @brief 更改表中字段名称
         * @param t_name [IN]表名
         * @param f_name [IN]旧字段名
         * @param f_new_name [IN]新字段名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        int AlterTableFieldName(const std::string& t_name, const std::string& f_name, const std::string& f_new_name) 
        {
            return db_.AlterTableFieldName(t_name, f_name, f_new_name);
        };

        /**
         * @fun AlterTableFieldType
         * @brief 更改表中某字段数据类型
         * @param t_name [IN]表名
         * @param f_name [IN]字段名
         * @param type [IN]数据类型
         * @return  成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        template<typename TYPE>
        int AlterTableFieldType(const std::string& t_name, const std::string& f_name, const TYPE& type) 
        {
            return  db_.AlterTableFieldType(t_name, f_name, type);
        }

        /**
         * @fun DeleteTableField
         * @brief 删除表中某个字段
         * @param t_name [IN]表名
         * @param f_name [IN]字段名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        int DeleteTableField(const std::string& t_name, const std::string& f_name) 
        {
            return  db_.DeleteTableField(t_name, f_name);
         }

        /**
         * @fun AddTableField
         * @brief 在表中添加新字段
         * @param t_name [IN]表名
         * @param f_new_name [IN]新字段名
         * @param type [IN]新字段数据类型
         * @param is_first [IN]默认表头添加，否则添加到f_name字段之后
         * @param f_name [IN]在那个字段后添加表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        template<typename TYPE>
        int AddTableField(const std::string& t_name, const std::string& f_new_name, const TYPE& type, const bool is_first = true, const std::string& f_name = "") 
        {
            return  db_.AddTableField(t_name, f_new_name, type, is_first, f_name);
        };
        /**
        * @fun AddIndex
        * @brief 添加索引
        * @param table_name 表名
        * @param ptr  索引类型
        * @param index_name 索引名称
        * @param fields 字段集
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        template<typename TYPE>
        int AddIndex(const std::string& table_name, TYPE index_type, const std::string& index_name, const std::vector<std::string>& fields)
        {
            return  db_.AddIndex(table_name, index_type, index_name, fields);
        }


        /**
         * @fun DeleteDataTable
         * @brief 删除数据表
         * @detail 根据传入数据表名称，删除数据表。
         * @param table_name [IN]数据表名称
         * @return  成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        int DeleteDataTable(const std::string& table_name) 
        {
            return db_.DeleteDataTable(table_name);
        }
        /**
         * @fun ReflashDataTable
         * @brief 更新数据表
         * @detail 根据数据表名称，更新指定的数据表
         * @param table_name [IN]数据表名称
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        int ReflashDataTable(const std::string& tableName) 
        {
            return db_.ReflashDataTable(tableName);
        }
        /**
         * @fun ShowTables
         * @brief 显示数据表
         * @detail 显示数据库中的所有数据表
         * @return  成功:jrscore::AOI_OK，失败:-1
         * <AUTHOR>
         */
        int ShowTables(std::vector<std::tuple<std::string>>& res) 
        {
            return db_.ShowTables(res);
        }
        /**
         * @fun CreateTable
         * @brief 模版函数，创建数据表
         * @detail 模版函数需要传入数据表结构体，结构体中包含表中所有的字段。
         * @param key_field [IN]表中主键字段
         * @param is_auto_key [IN]主键是否自动增长：true自动增长，false普通主键
         * @param uniqu_field [IN]独立字段，该字段内容不能重复
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template <typename T>
        int CreateTable(const std::string& key_field = "", const bool is_auto_key = false, const std::set<std::string>& uniqu_field = std::set<std::string>())
        {
            return db_.CreateTable<T>(key_field, is_auto_key, uniqu_field);
        }

        /**
         * @fun QueryCountDataInTable
         * @brief 查询表中已有数据总数
         * @param t_name [IN]表名
         * @param total [OUT]查询总数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.3
         * <AUTHOR>
         */
        int QueryCountDataInTable(const std::string& t_name, uint64_t& total) 
        {
            return db_.QueryCountDataInTable(t_name, total);
        };

        

#pragma endregion
        
     
#pragma region Data
        /**
         * @fun AddData
         * @brief 在表中添加一条新数据
         * @param t [IN]数据表结构体变量
         * @param ...args [IN]不同数据库中使用其余参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int AddData(const T& t, Args &&...args) 
        {
            return db_.AddData(t, std::forward<Args>(args)...);
        }

        /**
         * @fun AddData
         * @brief 在表中添加多条新数据
         * @param v [IN]添加数据集
         * @param ...args [IN]不同数据库中使用其余参数[预留]
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int AddData(const std::vector<T>& v, Args &&...args) 
        {
            return db_.AddData(v, std::forward<Args>(args)...);
        }
        /**
         * @fun DeleteData
         * @brief 删除数据
         * @param ...where_condition [IN]Null-清空表中数据；根据condition删除表中数据
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int DeleteData(Args &&...where_condition) 
        {
            return db_.DeleteData<T>(std::forward<Args>(where_condition)...);
        }
        /**
        * @fun QueryTable
        * @brief 搜索表中数据
        * @param res 查询结果
        * @param ...args [IN]根据条件查询数据，Null-查询表中所有数据
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        template<typename T, typename... Args>
        int QueryTable(std::vector<T>& res,Args &&...args) 
        {
            return db_.QueryTable<T>(res,std::forward<Args>(args)...);
        }

        template<typename T, typename... Args>
        int QueryProcedure(std::vector<T>& res, Args && ...args)
        {
            return db_.QueryProcedure<T>(res,std::forward<Args>(args)...);
           
        }


        /**
         * @fun QueryMultipleTable
         * @brief 多表数据查询
         * @param table_fields_map [IN]表名及个表字段
         * @param where_condition [IN]sql条件
         * @param res [OUT]查询结果
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.3
         * <AUTHOR>
         */
        template<typename T>
        int QueryMultipleTable(const std::map<std::string, std::vector<std::string>>& table_fields_map, const std::string& where_condition, std::vector<T>& res)
        {
            return db_.QueryMultipleTable<T>(table_fields_map, where_condition, res);
        }
        virtual std::string  GetUpdateCustomFieldsDataSQL(const std::string& table_name, const std::map<std::string, std::string>& fields_and_new_value_map, const std::string& condition)
        {
            return db_.GetUpdateCustomFieldsDataSQL(table_name, fields_and_new_value_map, condition);
        };
        /**
        * @fun UpdateMultipleTablesData
        * @brief 更新多表数据
        * @param table_fields_and_new_value_map [IN]表名及各自段的更新值
        * @param where_condition [IN]sql条件
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int UpdateCustomFieldsData(const std::string& table_name, const std::map<std::string, std::string>& fields_and_new_value_map, const std::string& condition)
        {
            return db_.UpdateCustomFieldsData(table_name, fields_and_new_value_map, condition);
        }
        /**
         * @fun GetInsertIdAfterInsert
         * @brief 获取插入单条数据后获取最新的主键值
         * @param t [IN] 插入一条数据
         * @param res [OUT]插入数据后的主键值
         * @param ...args [IN]其他条件，备用参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.3
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int GetInsertIdAfterInsert(const T& t, uint64_t& res, Args &&...args) 
        {
            return db_.GetInsertIdAfterInsert(t, res, std::forward<Args>(args)...);
        }
        /**
        * @fun GetInsertIdAfterInsert
        * @brief 获取插入多条数据后获取最新的主键值
        * @param v [IN] 插入多条数据
        * @param res [OUT] 插入数据后的主键值
        * @param ...args [IN] 其他条件，备用参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int GetInsertIdAfterInsert(const std::vector<T>& v, uint64_t& res, Args &&...args) 
        {
            return db_.GetInsertIdAfterInsert(v, res,std::forward<Args>(args)...);
        }




        /**
         * @fun Replace
         * @brief 将一条数据添加到数据库中，如果数据库中有相同记录则替换。
         * @param t [IN] 添加单条数据
         * @param res [OUT] 成功更改数据的条数
         * @param ...args  [IN]不同数据库所需参数
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        template <typename T, typename... Args>
        int Replace(const T& t, int& res, Args &&...args) 
        {
            return db_.Replace(t, res, std::forward<Args>(args)...);
        }
        /**
        * @fun Replace
        * @brief 将多条数据添加到数据库中，如果数据库中有相同记录则替换。
        * @param v [IN] 添加多条数据
        * @param res [OUT] 成功更改数据的条数
        * @param ...args  [IN] 不同数据库所需参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int Replace(const std::vector<T>& v, int& res, Args &&...args)
        {
            return db_.Replace(v, res,std::forward<Args>(args)...);
        }

        /**
        * @fun UpdateData
        * @brief 更新表中单条数据-表中有主键，根据主键进行更新；表中没有主键则根据传入条件进行更新。-仅适用于表为自增关键字
        * @param v [IN]更新数据集
        * @param res [OUT]更新成功的条数
        * @param ...args [IN]条件参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int UpdateData(const std::vector<T>& v, int& res, Args &&...args) 
        {
            return db_.UpdateData(v, res, std::forward<Args>(args)...);
        }

        //批量根据表中某个字段定位批量更新
        /**
        * @fun UpdateData
        * @brief 更新表中多条数据-仅适用于主键自增的表
        * @param t [IN]更新数据
        * @param res [OUT]更新成功的条数
        * @param ...args [IN]条件参数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        template <typename T, typename... Args>
        int UpdateData(const T& t, int& res, Args &&...args) 
        {
            return db_.UpdateData(t, res, std::forward<Args>(args)...);
        }

#pragma endregion


#pragma region other
        /**
        * @fun ExecuteSQL
        * @brief 执行原始SQL语句。
        * @param sql [IN]SQL语句字符串
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        int ExecuteSQL(const std::string& sql) 
        { 
            return db_.ExecuteSQL(sql);
        }
        /**
        * @fun BeginAffair
        * @brief 开始事务
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.2
        * <AUTHOR>
        */
        int BeginAffair() 
        { 
            return db_.BeginAffair(); 
        }
        /**
         * @fun Rollback
         * @brief 事务数据撤回
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        int Rollback() 
        { 
            return db_.Rollback(); 
        }
        /**
         * @fun CommitAffair
         * @brief 提交事务
         * @return 成功:jrscore::AOI_OK，失败:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        int CommitAffair() 
        { 
            return db_.CommitAffair(); 
        }


        
        /**
        * @fun UpdateOperateTime
        * @brief 更新操作时间-连接池使用
        * @return Null
        * @date 2024.4.2
        * <AUTHOR>
        */
        inline void UpdateOperateTime() 
        { 
            db_.UpdateOperateTime(); 
        }
        /**
         * @fun GetLatestOperateTime
         * @brief 获取最后一次操作时间-连接池使用
         * @return 上次操作时间
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline auto GetLatestOperateTime() 
        {
            return std::any_cast<std::chrono::system_clock::time_point>(db_.GetLatestOperateTime()); 
        }
        /**
         * @fun Ping
         * @brief 用于连接池
         * @return 成功：true； 失败：false。
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline bool Ping() 
        {
            return  db_.Ping(); 
        }
        /**
         * @fun HasError
         * @brief 用于连接池
         * @return 成功：true； 失败：false。
         * @date 2024.4.2
         * <AUTHOR>
         */
        inline bool HasError() 
        { 
            return db_.HasError(); 
        }


        /**
         * @fun GetLastError
         * @brief 获取最新错误信息
         * @return 有错误:jrscore::AOI_OK，没有错误:-1;
         * @date 2024.4.2
         * <AUTHOR>
         */
        std::string GetLastError() const 
        {
            std::string res_err = "";
           if (db_.GetLastError(res_err) != jrscore::AOI_OK)
           {
               return"错误类型未知，获取错误信息未空";
           }
            return res_err;
        }

        /**
         * @fun GetLastAffectRows
         * @brief 获取最新影响数据行数
         * @return 返回影响行数
         * @date 2024.4.8
         * <AUTHOR>
         */
        int GetLastAffectRows() 
        {
            return db_.GetLastAffectRows(); 
        }


#pragma endregion

    private:
        DB db_;  //数据库
};
template <typename DB>
using IDatabasePtr = std::shared_ptr<IDatabase<DB>>;
} 

#endif  
