/*****************************************************************//**
 * @file   pluginfactory.hpp
 * @brief  插件导出工厂
 * @details    
 * <AUTHOR>
 * @date 2024.2.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.21         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSPLUGINFACTORY_HPP__
#define __JRSPLUGINFACTORY_HPP__

#include "pluginexport.hpp"
namespace jrscore
{
    constexpr char PLUGIN_FACTORY_CREATE[] = "CreatePlugin";
    constexpr char PLUGIN_FACTORY_DESTROY[] = "DestroyPlugin";
}

#define OPERATOR_PLUGIN_DECLARATION(T)                \
extern "C"                                          \
{                                                   \
    JRS_ControlCenter_PLUGIN_API T* CreatePlugin();               \
    JRS_ControlCenter_PLUGIN_API void DestroyPlugin();            \
}



#define OPERATOR_PLUGIN_IMPLEMENTATION(T,name)        \
T* globalInstance = NULL;                   \
T*  CreatePlugin()                    \
{                                           \
    if (!globalInstance)                    \
        globalInstance = new T(name);           \
    return globalInstance;              \
}                                       \
void  DestroyPlugin()      \
{                                       \
    if (globalInstance)                 \
    {                                   \
        delete globalInstance;          \
        globalInstance = NULL;          \
    }                                   \
}

#endif // !__JRSPLUGINFACTORY_HPP__
