#ifndef __TABLESELECTOR_HPP__
#define __TABLESELECTOR_HPP__


//PREBUILD
#include "datapch.h"
#include <iostream>

/** 数据库 */
//#include "dbparam.h"

namespace jrsdatabase {
    class SelectorBase
    {
    public:
        virtual ~SelectorBase() = default;

        virtual int Select(const jrsselect::SelectorParamBasePtr& selector_ptr_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
    protected:
        explicit SelectorBase(const std::string& type_name);

    private:
        std::string type_name;
    };
};
#endif //!__TABLESELECTOR_HPP__