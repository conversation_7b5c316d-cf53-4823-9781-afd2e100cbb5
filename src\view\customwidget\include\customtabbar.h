/*****************************************************************
 * @file   customtabbar.h
 * @brief  
 * @details
 * <AUTHOR>
 * @date 2025.5.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.5.5          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
//STD
//Custom
//Third
#ifndef CUSTOMTABBAR_H
#define CUSTOMTABBAR_H

#include <QTabBar>
#include <QStyleOptionTab>

class CustomTabBar : public QTabBar
{
    Q_OBJECT
public:
    explicit CustomTabBar(QWidget *parent = nullptr);

    // 设置tab背景色
    void SetTabBackgroundColor(const QString tab_name, const QColor &color);
    void SetTabBackgroundColor(int index, const QColor &color);

    // 清除tab背景色
    void ClearTabBackground(int index);

    // 清空tab_colors
    void ClearColors();


protected:
    void paintEvent(QPaintEvent *event) override;
private:
    QRect TabRect(int index) const;
    int GetTabIndex(const QString &tab_name);
private:
    QMap<int, QColor> tab_colors;
};

#endif // CUSTOMTABBAR_H