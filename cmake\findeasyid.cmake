IF(EASYID_DIR)
    SET(EASYID_FIND_QUIETLY TRUE)
ENDIF(EASYID_DIR)

if(WIN32)
    find_path(EASYID_DIR easyidfind.txt
            $ENV{PROGRAMFILES} 
            $ENV{SYSTEMDRIVE}
    )
endif(WIN32)

if(EASYID_DIR)
    set(EASYID_FOUND TRUE)
endif(EASYID_DIR)

if(EASYID_FOUND)
    message(STATUS "Found EasyID: ${EASYID_DIR}")

    # 设置lib,这样之后只需要导入EASYID_LIBS,不需要按照模式配置三遍
    set(EASYID_INCLUDE_DIR ${EASYID_DIR}/include)
    include_directories(${EASYID_INCLUDE_DIR})
    set(EASYID_LIB_DIR ${EASYID_DIR}/lib/)
    set(EASYID_DEBUG_DIR ${EASYID_DIR}/lib/debug/)
    set(EASYID_RELEASE_DIR ${EASYID_DIR}/lib/release/)
    
    add_library(EASYID_world SHARED IMPORTED)
    set_property(TARGET EASYID_world APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
    set_target_properties(EASYID_world PROPERTIES
        IMPORTED_IMPLIB_DEBUG "${EASYID_LIB_DIR}/EasyID.lib"
        IMPORTED_LOCATION_DEBUG "${EASYID_LIB_DIR}/EasyID.dll"
    )
    set_target_properties(EASYID_world PROPERTIES
        IMPORTED_IMPLIB_RELEASE "${EASYID_LIB_DIR}/EasyID.lib"
        IMPORTED_LOCATION_RELEASE "${EASYID_LIB_DIR}/EasyID.dll"
    )
    set(EASYID_LIBS EASYID_world)

else(EASYID_FOUND)
    message(FATAL_ERROR "Could not find EasyID")
endif(EASYID_FOUND)