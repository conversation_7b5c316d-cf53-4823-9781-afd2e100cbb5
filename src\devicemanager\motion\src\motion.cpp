// Custom
#include "motionprotocal.h"
#include "motionworker.h"
#include "motion.h"
#include "coreapplication.h"
namespace jrsdevice
{

    Motion::Motion(const std::string& ip, const int port)
    {
        InitialAxisMap();

        device_type = DeviceType::AOI3D;

        protocal_ptr = std::make_shared<MotionProtocal>();
        worker_ptr = std::make_shared<MotionWorker>();

        // 连接TCP服务器
        auto res = worker_ptr->InitClient(ip, port);

        if (res != jrscore::AOI_OK)
        {
            PushErrorToStack (res,"运控链接服务器失败");
        }
        // 开启运行线程
        exit_ = false;
        motion = std::thread(&Motion::RunningMsgDealer, this);
    }

    Motion::~Motion()
    {
        exit_ = true;
        if (motion.joinable())
        {
            motion.join();
        }
    }

    void Motion::SetPublishMsgCallBack(const PublishMsgCallBack &callback)
    {
        publish_msg_callback = callback;
    }

    void Motion::SetDeviceType(DeviceType type)
    {
        device_type = type;
    }

    int Motion::AskInitial(int timeout)
    {
        std::string sendMsg = "ASKINIT";
        std::string check = "ASKINIT";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::AskStop(const int timeout, const int proIndex)
    {
        std::string sendMsg = "ASKSTOP:" + std::to_string(proIndex);
        std::string check = "ASKSTOP:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::AskPause(const int timeout, const int proIndex)
    {
        std::string sendMsg = "ASKPAUSE" + std::to_string(proIndex);
        std::string check = "ASKPAUSE:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::PhotoTook(OutputIndex index, const int timeout, const int proIndex)
    {
        std::string sendMsg = "PHOTOOK:" + std::to_string(proIndex) + "," + std::to_string((int)index);
        std::string check = "PHOTOOK:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::RePhtoTo(const int timeout, const int proIndex)
    {
        std::string sendMsg = "REPHOTO:" + std::to_string(proIndex);
        std::string check = "REPHOTO:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::SendResult(const TestResult result, const TrackIndex index, const int timeout)
    {
        std::string ok = (result == TestResult::OK) ? "OK" : "NG";
        std::string sendMsg = "TESTRESULT:" + ok + "," + std::to_string((int)index);
        std::string check = "TESTRESULT";
        return SendAndResult(sendMsg, check, timeout);
    }

    std::string Motion::CreateNC(const std::string poslist, GType type, Axis axis1, Axis axis2, Axis axis3, OutputIndex index)
    {
        JSON pos = JSON::array();
        if (poslist != "")
        {
            pos = JSON::parse(poslist);
        }
        std::string nc_content = "";
        for (int i = 0; i < pos.size(); i++)
        {
            std::string line = "";
            if(axis1 != Axis::None)
            {
                line += axis_map[axis1] + std::to_string(pos[i]["xAxis"].get<float>());
            }
            if(axis2 != Axis::None)
            {
                line += axis_map[axis2] + std::to_string(pos[i]["yAxis"].get<float>());
            }
            if(axis3 != Axis::None)
            {
                line += axis_map[axis3] + std::to_string(pos[i]["zAxis"].get<float>());
            }
            nc_content += (type == GType::G00?"G00": "G01") + line + "\n";
            // 触发拍照
            nc_content += "M100P" + std::to_string((int)index) + "T10\n";
            // 等待PHOTOOK
            nc_content += "M500\n";
        }
        return nc_content;
    }

    std::string Motion::CreateNC(jrsdata::CaptureFovPosList poslist, GType type, Axis axis1, Axis axis2, Axis axis3, OutputIndex index)
    {
        JSON pos = JSON::array();
        auto list = poslist.fov_pos_list;
        for (int i = 0; i < list.size(); i++)
        {
            jrsdata::CaptureFovPos cur_pos = list.at(i);
            JSON cur_obj = JSON::object();
            cur_obj["xAxis"] = cur_pos.x_fov_coordinate;
            cur_obj["yAxis"] = cur_pos.y_fov_coordinate;
            cur_obj["zAxis"] = cur_pos.z_fov_coordinate;
            pos.push_back(cur_obj);
        }
        return CreateNC(pos.dump(4), type, axis1, axis2 , axis3, index);
    }

    std::string Motion::ReadNc(const std::string filePath)
    {
        return ReadFile(filePath);
    }

    int Motion::InterruptProcess(const TrackIndex index, const int timeout)
    {
        return ScriptFunction((int)index - 1, timeout);
    }

    int Motion::InitialCheck(const TrackIndex index, const int timeout)
    {
        return ScriptFunction((int)index + 1, timeout);
    }

    int Motion::TransportMode(const TrackIndex index, const int timeout)
    {
        return ScriptFunction((int)index + 3, timeout);
    }

    int Motion::TransToImport(const TrackIndex index, const int timeout)
    {
        return ScriptFunction((int)index + 9, timeout);
    }

    int Motion::TransToExport(const TrackIndex index, const int timeout)
    {
        return ScriptFunction((int)index + 11, timeout);
    }

    int Motion::Load(const TrackIndex index, const int timeout)
    {
        if(device_type == DeviceType::AOI3D)
        {
            return ScriptFunction((int)index + 5, timeout);
        }
        else if(device_type == DeviceType::V2000)
        {
            return ScriptFunction((int)index + 51, timeout);
        }
        return false;
    }

    int Motion::Load2(const TrackIndex index, const int timeout)
    {
        if(device_type == DeviceType::AOI3D)
        {
            
        }
        else if(device_type == DeviceType::V2000)
        {
            return ScriptFunction((int)index + 55, timeout);
        }
        return false;
    }

    int Motion::DirectLoad2(const TrackIndex index, const int timeout)
    {
        if(device_type == DeviceType::AOI3D)
        {
            
        }
        else if(device_type == DeviceType::V2000)
        {
            return ScriptFunction((int)index + 57, timeout);
        }
        return false;
    }

    int Motion::UnLoad(const TrackIndex index, const int timeout)
    {
        if(device_type == DeviceType::AOI3D)
        {
            return ScriptFunction((int)index + 7, timeout);
        }
        else if(device_type == DeviceType::V2000)
        {
            return ScriptFunction((int)index + 53, timeout);
        }
        return false;
    }

    int Motion::OffSet(const double x, const double y, const int timeout, const int proIndex)
    {
        std::string sendMsg = "OFFSET:" + std::to_string(proIndex) + "," + std::to_string(x) + "," + std::to_string(y);
        std::string check = "OFFSET:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::SendNc(const std::string content, const int timeout, const int proIndex)
    {
        std::string sendMsg = "SENDNC:" + std::to_string(proIndex) + "," + content;
        std::string check = "SENDNC:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::ChangeRecipe(const std::string recipename, const int timeout)
    {
        std::string sendMsg = "ChangeRecipe:" + recipename;
        std::string check = "ChangeRecipe";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::ChangeLightMode(LightMode mode, const int timeout)
    {
        std::string sendMsg = "LIGHTGROUP:" + std::to_string((int)mode);
        std::string check = "LIGHTGROUP";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::AskStart(const int index, const int timeout, const int proIndex)
    {
        std::string sendMsg = "ASKSTART:" + std::to_string(proIndex) + "," + std::to_string(index);
        std::string check = "ASKSTART:" + std::to_string(proIndex);
        return SendAndResult(sendMsg, check, timeout);
    }
    
    int Motion::Mov(const Axis axis, const double distance, const double speed, const int timeout)
    {
        std::string sendMsg = "MOV:" + axis_map[axis] + "," + std::to_string(distance) + "," + std::to_string(speed);
        std::string check = "MOV";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::Mova(const Axis axis, const double position, const double speed, const int timeout)
    {
        std::string sendMsg = "MOVA:" + axis_map[axis] + "," + std::to_string(position) + "," + std::to_string(speed);
        std::string check = "MOVA";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::LopTo(const double xpos, const double ypos, const double speed, const int timeout)
    {
        std::string sendMsg = "LOPTo:X#" + std::to_string(xpos) + "|Y#" + std::to_string(ypos) + "," + std::to_string(speed);
        std::string check = "LOPTO";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::Interpolation(const double xpos, const double ypos, const double speed, const int timeout)
    {
        std::string sendMsg = "Interpolation:X#" + std::to_string(xpos) + "|Y#" + std::to_string(ypos) + "," + std::to_string(speed);
        std::string check = "INTERPOLATION";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::LopToXYZ(const double xpos, const double ypos, const double zpos, const double speed, const int timeout)
    {
        std::string sendMsg = "LOPTo:X#" + std::to_string(xpos) + "|Y#" + std::to_string(ypos) + "|Z#" + std::to_string(zpos) + "," + std::to_string(speed);
        std::string check = "LOPTO";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::InterpolationXYZ(const double xpos, const double ypos, const double zpos, const double speed, const int timeout)
    {
        std::string sendMsg = "Interpolation:X#" + std::to_string(xpos) + "|Y#" + std::to_string(ypos) + "|Z#" + std::to_string(zpos) + "," + std::to_string(speed);
        std::string check = "INTERPOLATION";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::OutPut(const CardType cardType, const int cardID, const OutputIndex index, const bool flag, const int timeout)
    {
        std::string sendMsg = "OUTPUT:" + std::to_string((int)cardType) + "," + std::to_string(cardID) + "," + std::to_string((int)index) + "," + (flag ? "true" : "false");
        std::string check = "OUTPUT";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::ClearAlarm(const int timeout)
    {
        std::string sendMsg = "ALARMCLEAR";
        std::string check = "ALARMCLEAR";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::Jog(const Axis axis, const bool flag, const int direction, const double speed, const int timeout)
    {
        std::string sendMsg = "JOG:" + axis_map[axis] + "," + (flag ? "true" : "false") + "," + std::to_string(direction) + "," + std::to_string(speed);
        std::string check = "JOG";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::Home(const Axis axis, const int timeout)
    {
        std::string sendMsg = "HOME:" + axis_map[axis];
        std::string check = "HOME";
        return SendAndResult(sendMsg, check, timeout);
    }

    int Motion::GroupHome(const int groupIndex, const int timeout)
    {
        std::string sendMsg = "GROUPHOME:" + std::to_string(groupIndex);
        std::string check = "GROUPHOME:"+ std::to_string(groupIndex);
        return SendAndResult(sendMsg, check, timeout);
    }

    std::string Motion::Pos(const int timeout)
    {
        std::string sendMsg = "POS";
        std::string check = "POS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON pos_obj = GetSpeficJsonValue(res,"/DATA/pos");
            if (result_obj != nullptr && pos_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto pos = pos_obj.get<std::vector<std::string>>();
                JSON position = pos;
                return position.dump(4);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::Pos(const Axis axis, const int timeout)
    {
        std::string sendMsg = "POS";
        std::string check = "POS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON pos_obj = GetSpeficJsonValue(res,"/DATA/pos");
            if (result_obj != nullptr && pos_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto pos = pos_obj.get<std::vector<std::string>>();
                return pos.at((LONGLONG)axis - 1);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::InputStatus(const CardType cardType, const int cardID, const int timeout)
    {
        std::string sendMsg = "INPUTSTATUS:" + std::to_string((int)cardType) + "," + std::to_string(cardID);
        std::string check = "INPUTSTATUS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON input_obj = GetSpeficJsonValue(res,"/DATA/input");
            if (result_obj != nullptr && input_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto input = input_obj.get<std::vector<std::string>>();
                JSON inputStauts = input;
                return inputStauts.dump(4);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::InputStatus(const CardType cardType, const int cardID, const InputIndex index, const int timeout)
    {
        std::string sendMsg = "INPUTSTATUS:" + std::to_string((int)cardType) + "," + std::to_string(cardID);
        std::string check = "INPUTSTATUS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON input_obj = GetSpeficJsonValue(res,"/DATA/input");
            if (result_obj != nullptr && input_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto input = input_obj.get<std::vector<std::string>>();
                return input.at((LONGLONG)index - 1);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }

        return std::string();
    }

    std::string Motion::OutputStatus(const CardType cardType, const int cardID, const int timeout)
    {
        std::string sendMsg = "OUTPUTSTATUS:" + std::to_string((int)cardType) + "," + std::to_string(cardID);
        std::string check = "OUTPUTSTATUS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON output_obj = GetSpeficJsonValue(res,"/DATA/output");
            if (result_obj != nullptr && output_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto output = res["DATA"]["output"].get<std::vector<std::string>>();
                JSON outputStauts = output;
                return outputStauts.dump(4);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::OutputStatus(const CardType cardType, const int cardID, const OutputIndex index, const int timeout)
    {
        std::string sendMsg = "OUTPUTSTATUS:" + std::to_string((int)cardType) + "," + std::to_string(cardID);
        std::string check = "OUTPUTSTATUS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON output_obj = GetSpeficJsonValue(res,"/DATA/output");
            if (result_obj != nullptr && output_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto output = output_obj.get<std::vector<std::string>>();
                return output.at((LONGLONG)index - 1);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::AxisLimit(const int timeout)
    {
        std::string sendMsg = "AXISLIMIT";
        std::string check = "AXISLIMIT";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON limit_obj = GetSpeficJsonValue(res,"/DATA/limit");
            if (result_obj != nullptr && limit_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto pos = limit_obj.get<std::vector<std::string>>();
                JSON position = pos;
                return position.dump(4);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::AxisLimit(const Axis axis, const int timeout)
    {
        std::string sendMsg = "AXISLIMIT";
        std::string check = "AXISLIMIT";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON limit_obj = GetSpeficJsonValue(res,"/DATA/limit");
            if (result_obj != nullptr && limit_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto pos = limit_obj.get<std::vector<std::string>>();
                return pos.at((LONGLONG)axis - 1);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::ProcessStatus(const int timeout)
    {
        std::string sendMsg = "PROCESSSTATUS";
        std::string check = "PROCESSSTATUS";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON status_obj = GetSpeficJsonValue(res,"/DATA/status");
            if (result_obj != nullptr && status_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto status = status_obj.get<std::vector<std::string>>();
                JSON position = status;
                return position.dump(4);
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        return std::string();
    }

    std::string Motion::GetDeviceTrack(const int timeout)
    {
        std::string sendMsg = "CURSETTING";
        std::string check = "CURSETTING";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON setting_obj = GetSpeficJsonValue(res,"/DATA/setting");
            if (result_obj != nullptr && setting_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto setting = setting_obj.get<std::string>();
                if(setting != "")
                {
                    try
                    {
                        auto json = JSON::parse(setting);
                        if(json == nullptr || json.empty())
                        {
                            return std::string();
                        }
                        JSON Track = GetSpeficJsonValue(json,"/Device/Track");
                        if(Track == nullptr || Track.empty())
                        {
                            return std::string();
                        }
                        return Track.dump(4);
                    }
                    catch (JSON::parse_error& e)
                    {
                        std::cout << e.what() << std::endl;
                    }
                }
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    std::string Motion::GetUserSetTrack(const int timeout)
    {
        std::string sendMsg = "CURSETTING";
        std::string check = "CURSETTING";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::string();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::string();
            }
            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON setting_obj = GetSpeficJsonValue(res,"/DATA/setting");
            if (result_obj != nullptr && setting_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                auto setting = setting_obj.get<std::string>();
                if (setting != "")
                {
                    try
                    {
                        auto json = JSON::parse(setting);
                        if(json == nullptr || json.empty())
                        {
                            return std::string();
                        }
                        JSON Track = GetSpeficJsonValue(json,"/UserSet/Track");
                        if(Track == nullptr || Track.empty())
                        {
                            return std::string();
                        }
                        return Track.dump(4);
                    }
                    catch (JSON::parse_error& e)
                    {
                        std::cout << e.what() << std::endl;
                    }
                }
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::string();
    }

    int Motion::UpdateUserSetTrack(std::string userset_tack,const int timeout)
    {
        std::string sendMsg = "UpdateUserSetTrack:" + userset_tack;
        std::string check = "UpdateUserSetTrack";
        return SendAndResult(sendMsg, check, timeout);
    }

    std::string Motion::GetLastError()
    {
        return last_error;
    }

    std::vector<std::string> Motion::GetScriptErrorList(const int timeout)
    {
        std::string sendMsg = "ERRORLIST";
        std::string check = "ERRORLIST";
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return std::vector<std::string>();
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if(res == nullptr || res.empty())
            {
                return std::vector<std::string>();
            }

            JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
            JSON list_obj = GetSpeficJsonValue(res,"/DATA/list");
            if (result_obj != nullptr && list_obj != nullptr && result_obj.is_boolean() && result_obj.get<bool>())
            {
                return list_obj.get<std::vector<std::string>>();
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }
        
        return std::vector<std::string>();
    }

    int Motion::ScriptFunction(const int mode, const int timeout)
    {
        std::string sendMsg = "ScriptFunction:" + std::to_string(mode);
        std::string check = "SCRIPTFUNCTION:" + std::to_string(mode);
        return SendAndResult(sendMsg, check, timeout);
    }

    void Motion::InitialAxisMap()
    {
        axis_map[Axis::Axis01] = "X";
        axis_map[Axis::Axis02] = "Y";
        axis_map[Axis::Axis03] = "Z";
        axis_map[Axis::Axis04] = "A";
        axis_map[Axis::Axis05] = "B";
        axis_map[Axis::Axis06] = "C";
        axis_map[Axis::Axis07] = "D";
        axis_map[Axis::Axis08] = "E";

        axis_map[Axis::Axis09] = "XX";
        axis_map[Axis::Axis10] = "YX";
        axis_map[Axis::Axis11] = "ZX";
        axis_map[Axis::Axis12] = "AX";
        axis_map[Axis::Axis13] = "BX";
        axis_map[Axis::Axis14] = "CX";
        axis_map[Axis::Axis15] = "DX";
        axis_map[Axis::Axis16] = "EX";

        axis_map[Axis::Axis17] = "XY";
        axis_map[Axis::Axis18] = "YY";
        axis_map[Axis::Axis19] = "ZY";
        axis_map[Axis::Axis20] = "AY";
        axis_map[Axis::Axis21] = "BY";
        axis_map[Axis::Axis22] = "CY";
        axis_map[Axis::Axis23] = "DY";
        axis_map[Axis::Axis24] = "EY";

        axis_map[Axis::Axis25] = "XZ";
        axis_map[Axis::Axis26] = "YZ";
        axis_map[Axis::Axis27] = "ZZ";
        axis_map[Axis::Axis28] = "AZ";
        axis_map[Axis::Axis29] = "BZ";
        axis_map[Axis::Axis30] = "CZ";
        axis_map[Axis::Axis31] = "DZ";
        axis_map[Axis::Axis32] = "EZ";
    }

    void Motion::RunningMsgDealer()
    {
        while (!exit_)
        {
            // 接收运控消息
            MotionMsgRecvDealer();

            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    void Motion::MotionMsgRecvDealer()
    {
        // 接收运控消息
        auto msg = worker_ptr->Receive(10);
        if (msg != "")
        {
            // 消息解析{"CHECK":,"DATA":}
            auto motionMsgPackage = protocal_ptr->MotionMsgDecode(msg);

            // 消息校验
            auto cmdPack = CompareCmdAndMotionCmd(motionMsgPackage);

            // 消息保存
            if (!cmdPack.empty())
            {
                 std::lock_guard<std::mutex> guard(receive_mutex);
                 g_receive_packlist.insert(std::pair<std::string, std::string>(motionMsgPackage["CHECK"], motionMsgPackage.dump(4)));
                // 通知等待的线程
                receive_cv.notify_all();
            }
            else
            {
                // 推送过来的消息
                JSON content = GetSpeficJsonValue(motionMsgPackage,"/DATA/content");
                JSON CHECK = GetSpeficJsonValue(motionMsgPackage,"/CHECK");
                if (content != nullptr && CHECK != nullptr)
                {
                    NewPublishMsg(CHECK.get<std::string>(), content.get<std::string>());
                }
            }
        }
    }

    JSON Motion::CompareCmdAndMotionCmd(const JSON msg)
    {
        std::lock_guard<std::mutex> guard(send_mutex);
        auto it = std::remove_if( g_cmd_packlist.begin(), g_cmd_packlist.end(), [&msg](JSON cmd)
                                 { return msg["CHECK"] == cmd["check"]; });
        if (it != g_cmd_packlist.end())
        {
            g_cmd_packlist.erase(it);
            return msg;
        }
        return JSON();
    }

    std::string Motion::ConductSend(std::string sendMsg, const std::string check, const int timeout)
    {
        std::string result;
        sendMsg = sendMsg + R"(\r\n)"; // 需要用原始字面量

        // 检查一下有没有发过
        if (CheckSend(sendMsg, check, timeout))
        {
            return std::string();
        }
        try
        {
            // 发送
            if (worker_ptr->Send(sendMsg))
            {
                // 添加检查包
                AddCheckerPack(sendMsg, check, timeout);

                std::unique_lock<std::mutex> lock(receive_mutex);
                receive_cv.wait_for(lock, std::chrono::milliseconds(timeout), [this, &check]() 
                {
                    return !g_receive_packlist.empty() && g_receive_packlist.find(check) != g_receive_packlist.end();
                });
                if (g_receive_packlist.find(check) != g_receive_packlist.end()) 
                {
                    std::string recive = g_receive_packlist[check];
                    g_receive_packlist.erase(check);
                    return recive;
                } 
                else 
                {
                    // 构建超时包
                    JSON cmdPack;
                    cmdPack["CHECK"] = check;
                    JSON data;
                    data["result"] = false;
                    data["errMsg"] = "超时";
                    cmdPack["DATA"] = data;

                    // 从g_cmd_packlist中删除
                    CompareCmdAndMotionCmd(cmdPack);

                    return cmdPack.dump(4);
                }
            }
        }
        catch (const std::exception&)
        {

        }

        return result;
    }

    bool Motion::CheckSend(std::string sendMsg, const std::string check, const int timeout)
    {
        std::lock_guard<std::mutex> guard(send_mutex);
        timeout;
        try
        {
            for (int i = 0; i < g_cmd_packlist.size(); i++)
            {
                JSON temp_pack = g_cmd_packlist.at(i);
                if(temp_pack != nullptr && !temp_pack.empty())
                {
                    if(temp_pack.contains("check") && temp_pack.contains("sendMsg"))
                    {
                        if (temp_pack["check"].get<std::string>() == check && temp_pack["sendMsg"].get<std::string>() == sendMsg)
                        {
                            return true;
                        }
                    }
                }
            }
        }
        catch(const std::exception& e)
        {
            std::cerr << e.what() << '\n';
        }
        
        return false;
    }

    void Motion::AddCheckerPack(const std::string sendMsg, const std::string check, const int timeout)
    {
        std::lock_guard<std::mutex> guard(send_mutex);
        // 构建命令检查包
        JSON json = protocal_ptr->ConstructCheckerPack(sendMsg, check, timeout);
        auto it = std::find( g_cmd_packlist.begin(), g_cmd_packlist.end(), json);
        if (it == g_cmd_packlist.end())
        {
            g_cmd_packlist.push_back(json);
        }
    }

    void Motion::NewPublishMsg(const std::string cmd, const std::string msg)
    {
        if (publish_msg_callback)
        {
            publish_msg_callback (cmd, msg);
        }
    }

    std::string Motion::ReadFile(const std::string filePath)
    {
        // 创建一个ifstream对象来读取文件
        std::ifstream file(filePath);

        // 检查文件是否成功打开
        if (!file.is_open())
        {
            return "";
        }

        // 创建一个字符串来存储文件内容
        std::string fileContent((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());

        // 关闭文件
        file.close();

        return fileContent;
    }

    int Motion::SendAndResult(std::string sendMsg, std::string check, int timeout)
    {
        std::string resStr = ConductSend(sendMsg, check, timeout);
        if(resStr == "")
        {
            return jrscore::MotionError::E_CONTROL_Motion_UNKNOWN;
        }
        try
        {
            JSON res = JSON::parse(resStr);
            if (!res.empty())
            {
                JSON result_obj = GetSpeficJsonValue(res,"/DATA/result");
                if (result_obj != nullptr && result_obj.is_boolean() && !result_obj.get<bool>())
                {
                    JSON err_obj = GetSpeficJsonValue(res,"/DATA/errMsg");
                    if(err_obj == nullptr)
                    {
                        last_error = "JSON不存在errMsg";
                        return jrscore::MotionError::E_CONTROL_Motion_UNKNOWN;
                    }
                    last_error = jrscore::MotionError::GetErrorDescriptionFromErrorString(err_obj.get<std::string>());
                    return jrscore::MotionError::GetErrorCodeFromErrorString(err_obj.get<std::string>());
                }
                return jrscore::AOI_OK;
            }
        }
        catch(const std::exception& e)
        {
            std::cout << "JSON parse err: "<< e.what() << std::endl;
        }

        return jrscore::MotionError::E_CONTROL_Motion_UNKNOWN;
    }

    JSON Motion::GetSpeficJsonValue(const nlohmann::json& json_data, const std::string& path)
    {
        try
        {
            nlohmann::json current_json_data = json_data;
            nlohmann::json::json_pointer pointer(path);
            return current_json_data.at(pointer);
        }
        catch (const std::exception&)
        {
            return nlohmann::json(nullptr);
        }
    }
}