#include"GetGround.h"
#include"PointClass.h"
#include<opencv2/opencv.hpp>
namespace jrsoperator{
    class BasePlaneMask{
    public:
        /// <summary>
        /// 用于在缺失mask时生成mask
        /// </summary>
        /// <param name="depth_image"></param>
        /// <param name="bin_size">生成直方图时每个分类的深度区间大小</param>
        /// <param name="x_resolution"></param>
        /// <param name="y_resoltion"></param>
        /// <param name="segmentThreshold">最终拟合平面距离阈值,阈值内将作为基面点</param>
        /// <param name="mask"></param>
        /// <returns></returns>
        bool GetGroundMask(const cv::Mat& depth_image,const float& bin_size, const float& x_resolution, const float& y_resoltion, const float& segmentThreshold, cv::Mat& mask){
            cv::Mat mask_temp;
            mask=cv::Mat::zeros(depth_image.size(),CV_8UC1);
            j3drevision::BasePlane bp;
            if(0!=bp.MaskFilter(depth_image,bin_size,mask_temp)){
                return false;
            }
            std::vector<j3dcore::PointXYZ> pts_output;
            if(0!=bp.SortAndConvertMatToPointclass(depth_image,mask_temp,x_resolution,y_resoltion,pts_output)){
                return false;
            }
            std::vector<int> indices;
            if(0!=bp.GetPtsIndice(static_cast<int>(pts_output.size()),static_cast<int>(pts_fit_size_),indices)){
                return false;
            }
            // j3dsegmentation::Histogram histo;
            // histo.Apply()
            std::vector<float> plane_param;
            if(0!=bp.GetPlaneParam(pts_output,indices,plane_param)){
                return false;
            }
            cv::Mat ground_mask,other_mask;
            if(!bp.run(ground_mask,other_mask,depth_image,x_resolution,y_resoltion,segmentThreshold,plane_param)){
                return false;
            }
            try{
                for(int j=0;j<depth_image.rows;j++){
                    const uchar* g_mask_p=ground_mask.ptr<const uchar>(j);
                    const uchar* o_mask_p=other_mask.ptr<const uchar>(j);
                    uchar* mask_p=mask.ptr<uchar>(j);
                    for(int i=0;i<depth_image.cols;i++){
                        if(*g_mask_p==255 &&*o_mask_p==255){
                            return false;
                        }
                        if(*g_mask_p==255){
                            *mask_p=1;
                        }else if(*o_mask_p==255){
                            *mask_p=2;
                        }
                        g_mask_p++;
                        o_mask_p++;
                        mask_p++;

                    }
                }
            }catch(cv::Exception){
                return false;
            }
            
        return true;
        }
        void SetPtsFitSize(int size){
            pts_fit_size_=size;
        }
    private:
        int pts_fit_size_ = 30;//用于将所有点云下采样减少计算成本,代表有多少个点参与到平面或者曲面拟合中
    };
}
