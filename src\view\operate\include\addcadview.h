/*****************************************************************//**
 * @file   addcadview.h
 * @brief  主要功能是导入CAD进行解析
 * @details　导入CAD之前的一些解析CAD的操作，以及导入内容的验证
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef ADDCADVIEW__H
#define ADDCADVIEW__H
 //QT
#include <QDialog>
#include <QWidget>

// 没用的文件都移动到cpp里了 jerx
//Custom
#include "caddefine.hpp" // jrsdata::CadStruct

//using namespace jrsdata; // 这个不应该放在.h文件里 jerx
class AddCadViewStandardItemModel;

namespace Ui {
    class AddCadView;
}
class AddCadViewModel;
namespace jrsaoi
{
    class AddCadView : public QDialog
    {
        Q_OBJECT
    public:
        /**
         * @fun AddCadView
         * @brief 构造函数，初始化 AddCadView 对话框。
         * @param parent 父窗口部件。
         * @date 2025.02.25
         * <AUTHOR>
         */
        AddCadView(QWidget* parent = Q_NULLPTR);
        /**
         * @fun ~AddCadView
         * @brief 析构函数，释放资源。
         * @date 2025.02.25
         * <AUTHOR>
         */
        ~AddCadView();

    signals:
        /**
         * @fun GetCADCompleted
         * @brief 发送CAD导入完成状态
         * @param param_ 导入的数据
         * @date 2024.9.24
         * <AUTHOR>
         */
        void GetCADCompleted(std::vector<jrsdata::CadStruct> param_);
    private:
        /**
         * @fun InitView
         * @brief 初始化视图，设置窗口标志和模型。
         * @details 设置窗口标志，初始化 CAD 数据模型，并将其绑定到表格视图。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun ConnectSlots
         * @brief 连接信号与槽。
         * @details 连接按钮点击信号到对应的槽函数。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void ConnectSlots();
        /**
         * @fun SetComboList
         * @brief 设置下拉菜单的选项列表。
         * @param items 选项列表。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SetComboList(QStringList items);

        /**
         * @fun ResolveCADFile
         * @brief 解析 CAD 文件内容。
         * @details 读取文件内容，解析每一行数据，并更新表格视图。
         *          如果文件内容为空或读取失败，返回 false。
         * @param file_name 文件路径。
         * @return bool 是否成功解析文件。
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool ResolveCADFile(std::string file_name);
        /**
         * @fun UpdateTableDatas 
         * @brief 更新表格数据
         * @param real_data_list
         * @return 
         * @date 2025.4.22
         * <AUTHOR>
         */
        bool UpdateTableDatas(std::vector<std::string> real_data_list);
        /**
         * @fun GetStringData
         * @brief 从数据向量中获取指定列的字符串数据。
         * @param data_vector 数据向量，包含多列字符串数据。
         * @param column_index 指定的列索引，从0开始。
         * @param output_string 输出参数，获取到的字符串数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool GetStringData(std::vector<std::string>& data_vector, int column_index, std::string& output_string);
        /**
         * @fun GetComponentData
         * @brief 从数据向量中获取指定列的元件库ID。
         * @param data_vector 数据向量，包含多列字符串数据。
         * @param column_index 指定的列索引，从0开始。
         * @param output_string 输出参数，获取到的字符串数据。
         * @param part_no 料号
         * @return 成功返回true，失败返回false。
         * @date 2025.3.24
         * <AUTHOR>
         */
        bool GetComponentData(std::vector<std::string>& data_vector, int column_index, std::string& output_string, std::string part_no);
        /**
         * @fun GetIntData
         * @brief 将字符串转换为整数。
         *        使用std::stol进行转换，并检查是否转换成功。
         * @param input_data 输入的字符串数据。
         * @param output_data 输出参数，转换后的整数数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool GetIntData(std::string& input_data, int& output_data);
        /**
         * @fun GetIntData
         * @brief 从数据向量中获取指定列的整数数据。
         *        先通过GetStringData获取字符串数据，再将其转换为整数。
         * @param data_vector 数据向量，包含多列字符串数据。
         * @param column_index 指定的列索引，从0开始。
         * @param output_data 输出参数，解析得到的整数数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool GetIntData(std::vector<std::string>& data_vector, int column_index, int& output_data);
        /**
         * @fun GetFloatData
         * @brief 将字符串转换为浮点数。
         *        使用std::stod进行转换，并检查是否转换成功。
         * @param input_data 输入的字符串数据。
         * @param output_data 输出参数，转换后的浮点数数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool GetFloatData(std::string& input_data, float& output_data);
        /**
         * @fun GetFloatData
         * @brief 从数据向量中获取指定列的浮点数数据。
         *        先通过GetStringData获取字符串数据，再将其转换为浮点数。
         * @param data_vector 数据向量，包含多列字符串数据。
         * @param column_index 指定的列索引，从0开始。
         * @param output_data 输出参数，解析得到的浮点数数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool GetFloatData(std::vector<std::string>& data_vector, int column_index, float& output_data);
        /**
         * @fun ConvertCADData
         * @brief 转换CAD文件数据到数据结构
         * @param vec_cad_datas CAD数据向量。
         * @return 成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool ConvertCADData(std::vector<std::vector<std::string>>& vec_cad_datas);
        /**
        * @fun ValidateInputRange
        * @brief 验证输入范围是否有效。
        * @param start_index 起始索引。
        * @param end_index 结束索引。
        * @param data_size 数据大小。
        * @return 成功返回true，失败返回false。
        * @date 2025.03.24
        * <AUTHOR>
        */
        bool ValidateInputRange(int start_index, int end_index, size_t data_size);
        /**
         * @fun ProcessSingleCadData
         * @brief 处理单条CAD数据。
         * @param one_cad_data 单条CAD数据。
         * @param unit_conversion_factor 单位转换因子。
         * @param subboard_id_from_name 是否从名称中获取子板ID。
         * @return 处理成功返回true，失败返回false。
         * @date 2025.03.24
         * <AUTHOR>
         */
        bool ProcessSingleCadData(std::vector<std::string>& one_cad_data, double unit_conversion_factor, jrsdata::CadStruct& one_cad);
        /**
         * @fun GetSubboardId
         * @brief 从数据向量中获取指定列的子板ID。第一列作为没有子板的设置，有的CAD没有子板
         * @param data_vector 数据向量，包含多列字符串数据。
         * @param column_index 指定的列索引，从0开始。
         * @param output_data 输出参数，解析得到的整数数据。
         * @return 成功返回true，失败返回false。
         * @date 2025.3.24
         * <AUTHOR>
         */
        bool GetSubboardId(std::vector<std::string>& cad_data_row, int column_index, int& output_value);
        /**
         * @fun NormalizeAngle 
         * @brief 将角度转换为0到360度之间的等效角度
         * @param angle
         * @return 
         * @date 2025.3.28
         * <AUTHOR>
         */
        int NormalizeAngle(int angle);
        /**
         * @fun ReplaceString 
         * @brief 替换字符串中的文件名中不允许的字符
         * @param name
         * @date 2025.3.31
         * <AUTHOR>
         */
        std::string ReplaceString(std::string name);
    protected slots:
        /**
         * @fun ReadCADFile
         * @brief 读取 CAD 文件。
         * @details 调用 `ReadCADFileLines` 方法读取 CAD 文件内容。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void ReadCADFile();
        /**
         * @fun ImportCADData
         * @brief 导入 CAD 数据。
         * @details 从表格模型中读取数据，解析并存储为 CAD 结构数据。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void ImportCADData();

        /**
         * @fun SelectStartLine
         * @brief 选择起始行。
         * @details 根据当前选中的行设置起始行，并更新下拉菜单选项。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SelectStartLine();
        /**
         * @fun SelectEndLine
         * @brief 选择结束行。
         * @details 根据当前选中的行设置结束行。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SelectEndLine();
    private:
        Ui::AddCadView* ui;
    private:
        /// 存储 CAD 结构数据的向量，用于管理 CAD 相关信息。
        std::vector<jrsdata::CadStruct> m_cad_structs;

        /// 指向 AddCadViewModel 的指针，用于处理 CAD 添加相关的视图模型。
        AddCadViewModel* m_add_cad_model;
    };
}
#endif // !__ADDCADVIEW__H
