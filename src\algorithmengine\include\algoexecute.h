/*****************************************************************
 * @file   algoexecute.h
 * @brief  主要负责执行算法
 * @details 执行算法的过程中需要将算法参数封装成ExecuteAlgoParam结构体，然后在算法引擎中进行转换成各自的数据类型
 * <AUTHOR>
 * @date 2024.10.11
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.11          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSALGOEXECUTE_H__
#define __JRSALGOEXECUTE_H__
 //STD
#include <iostream>
//Custom
#include "algofactory.h"
#include "algoexecuteparam.hpp"
//Third


namespace iguana
{
    namespace detail
    {
        struct base;
    }
    using base = detail::base;
}

namespace jrsalgo
{
    using HandlerTypeConvertFunc = std::function<void(const jrsparam::AlgoParamType&)>;

    class AlgoExecute
    {
    public:
        AlgoExecute();
        ~AlgoExecute();

        static void SetAlgoFactoryPtr(const AlgoFactoryPtr& ptr);

        /**
         * @fun ExecuteSpeficAlgo 
         * @brief 执行指定的算法
         * @param execute_algo_param_[IN] 指定算法的执行参数，此参数为AOI端生成的算法的执行参数，到引擎里面需要转换成算法的执行参数
         * @param json_data_ [IN] 所有算法参数配置文件内容，即algoinfo.json里面的内容
         * @return 返回算法的执行结果指针
         * <AUTHOR>
         * @date 2025.5.6
         */
        static jrsoperator::OperatorParamBasePtr ExecuteSpeficAlgo(const jrsparam::ExecuteAlgoParam& execute_algo_param_, const nlohmann::json& json_data_);

        /**
         * @fun GetSpeficAlgoExecuteResult 
         * @brief 解析算法结果指针，根据算法配置文件，获取子项的结果值，如x坐标，y坐标，分数等
         * @param param_ptr_[IN] 指定算法的执行结果
         * @param json_data_ [IN] 算法配置参数
         * @param algo_name_ [IN] 算法名称
         * @return 返回解析后的算法结果
         * <AUTHOR>
         * @date 2025.5.7
         */
        static jrsparam::AlgoExecuteResultParam GetSpeficAlgoExecuteResult(const jrsoperator::OperatorParamBasePtr& param_ptr_, const nlohmann::json& json_data_, const std::string& algo_name_);
        
        /**
         * @fun GetAlgoExecuteResultStatus 
         * @brief 获取算法执行结果的状态，只有算法的ok和ng，其他没有
         * @param param_ptr_ [IN] 指定算法的执行结果
         * @return ok返回true，ng返回false
         * <AUTHOR>
         * @date 2025.5.7
         */
        static bool GetAlgoExecuteResultStatus(const jrsoperator::OperatorParamBasePtr& param_ptr_);
        /**
         * @fun GetAlgoExecuteRectInfo 
         * @brief 获取算法的执行时的参数信息字符串
         * @param param_ptr_ [IN] 指定算法的执行参数信息
         * @return 返回算法的执行参数信息字符串
         * <AUTHOR>
         * @date 2025.5.6
         */
        static std::string GetAlgoExecuteRectInfo(const jrsoperator::OperatorParamBasePtr& param_ptr_);

        /**
         * @fun SetAlgoDynamicOutParamValue
         * @brief 动态设置算法的输出参数，因为需要将算法执行的结果赋值给我
         * @param execute_algo_result_param_[IN]  算法执行的结果参数结构体
         * @param param_ptr_ [OUT] 赋值后的算法参数指针
         * @param json_data_ [IN] 算法配置文件的json信息
         * @param algo_name_ [IN] 指定算法的名称
         * @return 成功返回jrscore::AOI_OK，否则返回错误码
         * <AUTHOR>
         * @date 2025.6.11
         */
        static int SetAlgoDynamicOutParamValue(const jrsparam::AlgoExecuteResultParam& execute_algo_result_param_, const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_, const nlohmann::json& json_data_, const std::string& algo_name_);

      
    private:
        //Fun
        void InitMember();

        /**
        * @fun GetSpeficAlgoConfigJson
        * @brief 获取指定算法的参数配置文件
        * @param json_data_ [IN] 所有算法的配置文件
        * @param config_path [IN] 指定算法的参数配置路径
        * @return 返回获取的json内容
        * <AUTHOR>
        * @date 2024.10.31
        */
        static nlohmann::json GetSpeficAlgoConfigJson(const nlohmann::json& json_data_, const std::string& config_path);

        /**
         * @fun SetAlgoStaticValue
         * @brief  设置算法的静态参数，具体的每个算法参数有哪些参考算法参数配置文件
         * @param execute_algo_param_ [IN]  算法执行时的AOI端获取的算法参数
         * @param param_ptr_ [OUT] 算法引擎中的算法参数，是底层算法的输入参数
         * @return  成功返回0，失败返回错误码
         * <AUTHOR>
         * @date 2024.12.3
         */
        static int SetAlgoStaticValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_);

        /**
         * @fun SetAlgoDynamicValue
         * @brief 设置算法动态参数的值，动态参数为每次检测的时候都会变化的参数值，如输入图片，模板图片等
         *  具体每个算法的动态参数有哪些参考算法参数配置文件
         * @param execute_algo_param_ [IN] 算法执行时的AOI端获取的算法参数
         * @param param_ptr_[OUT] 算法引擎中的算法参数，是底层算法的输入参数
         * @param json_data_ [IN ]算法参数配置文件内容
         * @return  成功返回0，失败返回错误码
         * <AUTHOR>
         * @date 2024.12.3
         */
        static int SetAlgoDynamicValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_, const nlohmann::json& json_data_);

        /**
        * @fun SetAlgoValue
        * @brief 设置算法动态参数的值，动态参数为每次检测的时候都会变化的参数值，如输入图片，模板图片等
        *  具体每个算法的动态参数有哪些参考算法参数配置文件
        * @param execute_algo_param_ [IN] 算法执行时的AOI端获取的算法参数
        * @param param_ptr_[OUT] 算法引擎中的算法参数，是底层算法的输入参数
        * @param json_data_ [IN ]算法参数配置文件内容
        * @return  成功返回0，失败返回错误码
        * <AUTHOR>
        * @date 2024.12.20
        */
        static int AlgoExecute::SetAlgoValue(const jrsparam::ExecuteAlgoParam& execute_algo_param_, std::shared_ptr<iguana::base>& param_ptr_);
        /**
         * @fun ExecuteSpecificAlgoDrive
         * @brief 执行指定算法
         * @param algo_name_ [IN] 指定算法的名称
         * @param execute_param [IN]执行算法参数
         * @return AOI_OK 执行成功，否则失败
         * <AUTHOR>
         * @date 2024.10.14
         */
        static int ExecuteSpecificAlgoDrive(const std::string& algo_name_, jrsoperator::OperatorParamBasePtr& execute_param);

        //Member
        static AlgoFactoryPtr algo_factory_ptr;
        std::unordered_map<std::string, HandlerTypeConvertFunc> param_handlers;

    };
    using AlgoExecutePtr = std::shared_ptr<AlgoExecute>;
}

#endif // !__JRSALGOEXECUTE_H__

