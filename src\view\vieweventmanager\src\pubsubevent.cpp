
#include "pubsubevent.h"
namespace jrsaoi
{
    PubSubEvent::PubSubEvent ()
    {
    }

    PubSubEvent::~PubSubEvent ()
    {
    }

    int PubSubEvent::AdveristerEvent ( jrscore::ModuleHandlePtr& moudle_handle_ptr, const std::string topic_name )
    {
        moudle_handle_ptr->AddAdvertise (topic_name);
        return jrscore::AOI_OK;
    }

    int PubSubEvent::SubscriberEvent ( jrscore::ModuleHandlePtr& moudle_handle_ptr, const std::string& topic_name, const std::string& sub_name, jrscore::CallBackFunction<jrsdata::ViewParamBasePtr> cb )
    {
        moudle_handle_ptr->AddSubscriber<jrsdata::ViewParamBasePtr> ( topic_name, sub_name, cb );
        return jrscore::AOI_OK;
    }

    int PubSubEvent::PublishEvent ( jrscore::ModuleHandlePtr& module_handle_ptr, const std::string& topic_name , const std::string& sub_name,const jrsdata::ViewParamBasePtr& args )
    {
        module_handle_ptr->NotifyOne (topic_name,sub_name,args);
        return jrscore::AOI_OK;
    }

}
