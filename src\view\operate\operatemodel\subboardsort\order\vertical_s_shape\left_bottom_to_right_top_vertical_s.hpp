/*****************************************************************
 * @file   left_bottom_to_right_top_invert_s.hpp
 * @brief  竖形s：左下到右上排序
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class SVerticalLeftBottomToRightTop :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int row_count = static_cast<int>(subboards_.size());
            int max_col = 0;
            for (const auto& row : subboards_)
            {
                if (row.size() > max_col)
                {
                    max_col = static_cast<int>(row.size());
                }
            }
            int subboard_id = 1;
            int current_col = 0; /**< control vertical direction */
            for (int col = max_col - 1; col >= 0; --col) // right to left
            {
                if (current_col % 2 != 0)
                {
                    // top to bottom
                    for (int row = 0; row < row_count; ++row)
                    {
                        UpdateSubboard(subboards_[row][col], subboard_id++);
                    }
                }
                else
                {
                    // bottom to top
                    for (int row = row_count - 1; row >= 0; --row)
                    {
                        UpdateSubboard(subboards_[row][col], subboard_id++);
                    }
                }
                ++current_col;
            }
            return jrscore::AOI_OK;
        }
    };
}
