project(logicmanager)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#导出宏
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)
 
# 逻辑管理器
set(logicmanager_src
    logicmanager/src/logicmanager.cpp
)

set(logicmanager_head
    logicmanager/include/logicmanager.h
)

# 采图模块
set(capture_src
    capture/src/scanprocess.cpp
    capture/src/captureimage.cpp
)
set(capture_head
    capture/include/scanprocess.h
    capture/include/captureimage.h
)


set(motioncorrect_src
    capture/src/motionerrorcorrect.cpp
    capture/src/motionerrorparam.cpp
    capture/src/motionerrorfunc.cpp
)

set(stictchimages_head
    capture/include/stictchimages.h
)
set(stictchimages_src
    capture/src/stictchimages.cpp
)

set(aoiimagemanger_head
    capture/include/jrsaoiimgsmanager.h
)
set(aoiimagemanger_src
    capture/src/jrsaoiimgsmanager.cpp
)

#算法引擎
set(algorithmengine_head
    algoengine/include/invokealgoengine.h
)
set(algorithmengine_src
    algoengine/src/invokealgoengine.cpp
)

source_group("logicmanager/src" FILES ${logicmanager_src}) 
source_group("logicmanager/head" FILES ${logicmanager_head})
source_group("capture/src" FILES ${capture_src})
source_group("capture/head" FILES ${capture_head})

source_group("motioncorrect/head" FILES ${motioncorrect_head})
source_group("motioncorrect/src" FILES ${motioncorrect_src})

source_group("stictchimages/head" FILES ${stictchimages_head})
source_group("stictchimages/src" FILES ${stictchimages_src})

source_group("aoiimagemanger/head" FILES ${aoiimagemanger_head})
source_group("aoiimagemanger/src" FILES ${aoiimagemanger_src})

source_group("algorithmengine/src" FILES ${algorithmengine_src})
source_group("algorithmengine/head" FILES ${algorithmengine_head})

add_library(${PROJECT_NAME} SHARED
    ${logicmanager_src}
    ${logicmanager_head}
    ${capture_src}
    ${capture_head}
    ${motioncorrect_head}
    ${motioncorrect_src}
    ${stictchimages_head}
    ${stictchimages_src}
    ${aoiimagemanger_head}
    ${aoiimagemanger_src}
    ${algorithmengine_src}
    ${algorithmengine_head}
    ${JRS_VERSIONINFO_RC}

)

#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

target_link_directories(${PROJECT_NAME} 
    PRIVATE
    #OPENCV
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>

    # 成像模块
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/debug>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>
    $<$<CONFIG:RelWithDebInfo>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/relwithdebinfo>
    




)
 
#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100>
    #project
    project
    # datamanager 
    datamanager
    #devicemanager
    devicemanager
    #algorithmengine
    algorithmengine
    # 成像
    $<$<CONFIG:Debug>:jrsstructlightcontrlmodule>
    $<$<CONFIG:Release>:jrsstructlightcontrlmodule> 
    $<$<CONFIG:RelWithDebInfo>:jrsstructlightcontrlmodule> 


    #workflow
    workflow
)

# 引入头文件
target_include_directories(${PROJECT_NAME} PUBLIC
    #module
    ${DIR_PROJECT_CURRENT}/src/logicmanager/capture/include
    ${DIR_PROJECT_CURRENT}/src/logicmanager/logicmanager/include
    ${DIR_PROJECT_CURRENT}/src/logicmanager/dofov/include
    ${DIR_PROJECT_CURRENT}/src/logicmanager/algoengine/include
    ${DIR_PROJECT_CURRENT}/src/core/coordinatecenter/include
    ${DIR_PROJECT_CURRENT}/src/project/projectmanager/include
    ${DIR_PROJECT_CURRENT}/src/workflow/workflowmanager/include
    #other
    ${DIR_PROJECT_CURRENT}/src/devicemanager/devicemanager/include
    ${DIR_PROJECT_CURRENT}/src/devicemanager/structlight/include
    ${DIR_PROJECT_CURRENT}/src/devicemanager/motion/include
    ${DIR_PROJECT_CURRENT}/src/algorithmengine/include
    
    ${DIR_PROJECT_CURRENT}/src/core/common/include
    ${DIR_PROJECT_CURRENT}/thirdparty/cereal/include

    ${OPENCV_INCLUDE_DIR}
    ${EIGEN3_INCLUDE_DIR}
)
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")

