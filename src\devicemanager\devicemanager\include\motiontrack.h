/*****************************************************************/ /**
* @file   motiontrack.h
* @brief  轨道模块
* @details 自动流程的轨道调度
* <AUTHOR>
* @date 2024.9.2
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
* <tr><td>2024.9.2         <td>V2.0              <td>zhaokunlong      <td>                      <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/

#ifndef __JRSMOTIONTRACK_H__
#define __JRSMOTIONTRACK_H__

// Custom
#include "deviceparam.hpp"
#include "motion.h"
#include "viewparam.hpp"
#include "trackbase.h"

namespace jrsdevice
{

    using Function = std::function<void(const jrsdata::DeviceParamPtr &param_)>;
    class JRS_AOI_PLUGIN_API MotionTrack : public TrackBase
    {

    public:
        MotionTrack(std::shared_ptr<Motion> &motion, bool &running);
        ~MotionTrack();

        /**
         * @fun ExcuteCommond
         * @brief 执行绑定的函数
         * @param device_param_
         * <AUTHOR>
         * @date 2024.9.2
         */
        void ExcuteCommond(const jrsdata::DeviceParamPtr &device_param_);

        /**
         * @fun SetMotionSetting
         * @brief 运控轨道设置信息(进料方向、出料方向、模式等)
         * @param track 轨道配置
         * <AUTHOR>
         * @date 2024.9.2
         */
        void SetMotionSetting(jrsdata::MotionSetting motion);

    private:
        // 运控处理函数绑定
        void MotionFunctionBind();

        // 初始化
        void AskInitial(const jrsdata::DeviceParamPtr &param_);

        // 启动流程
        void AskStart(const jrsdata::DeviceParamPtr &param_);

        // 停止流程
        void AskStop(const jrsdata::DeviceParamPtr &param_);

    private:
        std::shared_ptr<Motion> motion_ptr;              /**< 运控实例*/
        std::map<std::string, Function> motion_func_map; /**< 运控函数绑定map */

        bool process_running;                  /**< 流程运行中 */
        JSON track_setting;                    /**< 轨道配置 */
        jrsdata::MotionSetting motion_setting; /**< 运控轨道设置信息 */
    };
}

#endif // !__JRSMOTIONTRACK_H__
