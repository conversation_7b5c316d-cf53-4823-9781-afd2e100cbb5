/*****************************************************************//**
 * @file   deviceparam.hpp
 * @brief  设备参数定义
 * @details
 * <AUTHOR>
 * @date 2024.8.20
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.20         <td>V1.0              <td>zhaokunlong      <td> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __DEVICEPARAM_HPP__
#define __DEVICEPARAM_HPP__

 //STD
#include <iostream>

//Third
#pragma warning(push, 3)
#include <opencv2/opencv.hpp>
#pragma warning(pop)
//Custom
#include "parambase.hpp"

namespace jrsdata
{
    using InitDeivceResults = std::unordered_map<std::string, std::tuple<bool, std::string>>;

    //! 结构光参数
    struct StructLightParam
    {
        int camera_fov_w; /**< 相机视野宽度(像素)*/
        int camera_fov_h; /**< 相机视野高度(像素)*/
        float resolution_x; /**< 相机x轴分辨率*/
        float resolution_y; /**< 相机y轴分辨率*/
        float z_focus_pos;  /**< Z轴相机清晰位置*/

        // 构造函数
        StructLightParam()
            : camera_fov_w{}
            , camera_fov_h{}
            , resolution_x{}
            , resolution_y{}
            , z_focus_pos{}
        {
        }
    };
    // 轨道输入参数结构体
    struct TrackInput
    {
        int import_index;                               /**< 来料传感器索引 */
        int slow_index;                                 /**< 减速传感器索引 */
        int stop_index;                                 /**< 停止传感器索引 */
        int export_index;                               /**< 出料传感器索引 */

        std::string import_status;                      /**< 来料传感器状态 */
        std::string slow_status;                        /**< 减速传感器状态 */
        std::string stop_status;                        /**< 停止传感器状态 */
        std::string export_status;                      /**< 出料传感器状态 */

        int cylinder1_sensor__index;                    /**< 夹板气缸传感器索引1 */
        int cylinder2_sensor__index;                    /**< 夹板气缸传感器索引2 */
        int shield1_sensor__index;                      /**< 挡板气缸传感器索引1 */
        int shield2_sensor__index;                      /**< 挡板气缸传感器索引2 */

        std::string cylinder1_sensor_status;            /**< 夹板气缸传感器1状态 */
        std::string cylinder2_sensor_status;            /**< 夹板气缸传感器2状态 */
        std::string shield1_sensor_status;              /**< 挡板气缸传感器1状态(左挡板) */
        std::string shield2_sensor_status;              /**< 挡板气缸传感器2状态(右挡板) */

        int previous_ask_index;                         /**< 前机出料信号索引 */
        int next_ask_index;                             /**< 后机要料信号索引 */

        std::string previous_ask_status;                /**< 前机出料信号索引状态 */
        std::string next_ask_status;                    /**< 后机要料信号索引状态 */

        TrackInput()
        {
            import_index = -1;
            slow_index = -1;
            stop_index = -1;
            export_index = -1;

            import_status = "0";
            slow_status = "0";
            stop_status = "0";
            export_status = "0";

            cylinder1_sensor__index = -1;
            cylinder2_sensor__index = -1;
            shield1_sensor__index = -1;
            shield2_sensor__index = -1;

            cylinder1_sensor_status = "0";
            cylinder2_sensor_status = "0";
            shield1_sensor_status = "0";
            shield2_sensor_status = "0";

            previous_ask_index = -1;
            next_ask_index = -1;

            previous_ask_status = "0";
            next_ask_status = "0";
        }
    };

    // 轨道输出参数结构体
    struct TrackOutput
    {
        int cylinder1_index;                            /**< 夹板气缸索引1 */
        int cylinder2_index;                            /**< 夹板气缸索引2 */
        int shield1_index;                              /**< 挡板气缸索引1 */
        int shield2_index;                              /**< 挡板气缸索引2 */

        std::string cylinder1_status;                   /**< 夹板气缸索引1输出状态 */
        std::string cylinder2_status;                   /**< 夹板气缸索引2输出状态 */
        std::string shield1_status;                     /**< 挡板气缸索引1输出状态 */
        std::string shield2_status;                     /**< 挡板气缸索引2输出状态 */

        int previous_output_index;                      /**< 向前机要板信号索引 */
        int next_output_index;                          /**< 向后机输出有板信号索引 */

        std::string previous_output_status;             /**< 向前机要板信号状态 */
        std::string next_output_status;                 /**< 向后机输出有板信号状态 */

        int detect_ok_index;                            /**< 检测OK输出索引 */
        int detect_ng_index;                            /**< 检测NG输出索引 */

        std::string detect_ok_status;                   /**< 检测OK输出状态 */
        std::string detect_ng_status;                   /**< 检测NG输出状态 */

        TrackOutput()
        {
            cylinder1_index = -1;
            cylinder2_index = -1;
            shield1_index = -1;
            shield2_index = -1;

            cylinder1_status = "0";
            cylinder2_status = "0";
            shield1_status = "0";
            shield2_status = "0";

            previous_output_index = -1;
            next_output_index = -1;

            previous_output_status = "0";
            next_output_status = "0";

            detect_ok_index = -1;
            detect_ng_index = -1;

            detect_ok_status = "0";
            detect_ng_status = "0";
        }
    };

    // 轨道状态
    struct Track
    {
        TrackInput track_input;                         /**< 轨道输入状态 */
        TrackOutput track_output;                       /**< 轨道输出状态 */
        int enterDirection;                             /**< 进料方向[0:左进,1:右进] */
        int leaveDirection;                             /**< 出料方向[0:左出,1:右出] */
        int track_mode;                                 /**< 轨道模式[0:在线模式，1:单机模式，2:老化模式] */
        int board_width;                                /**< 轨道对应的板宽 */
        Track()
            :enterDirection(0)
            , leaveDirection(0)
            , track_mode(1)
            , board_width(0)
        {
        }
    };
    enum TRACK_NUMBER
    {
        TRACK_NONE = -1,
        TRACK_1 = 1,
        TRACK_2,
        TRACK_3,
        TRACK_4,
        TRACK_5,
        TRACK_6,
        TRACK_7,
        TRACK_8,
        TRACK_9,
        TRACK_10
    };
    // 轴位置、输入、输出、轨道输入输出参数等
    struct MotionSatus
    {
        std::vector<std::string> pos;                   /**< 轴位置 */
        std::vector<std::string> motioncard_input;      /**< 主卡输入状态 */
        std::vector<std::string> iocard_input;          /**< IO卡输入状态 */
        std::vector<std::string> motioncard_output;     /**< 主卡输出状态 */
        std::vector<std::string> iocard_output;         /**< IO卡输出状态 */
        std::vector<std::string> axis_limit;            /**< 轴限位 */
        std::map<TRACK_NUMBER, Track> track_status;     /**< 轨道状态 */

        MotionSatus()
        {
        }
        // 相等
        bool operator==(const MotionSatus& other) const
        {
            if (pos != other.pos)
            {
                return false;
            }
            if (motioncard_input != other.motioncard_input)
            {
                return false;
            }
            if (iocard_input != other.iocard_input)
            {
                return false;
            }
            if (motioncard_output != other.motioncard_output)
            {
                return false;
            }
            if (iocard_output != other.iocard_output)
            {
                return false;
            }
            if (axis_limit != other.axis_limit)
            {
                return false;
            }
            return true;
        }

        // 不相等
        bool operator!=(const MotionSatus& other) const
        {
            if (pos != other.pos)
            {
                return true;
            }
            if (motioncard_input != other.motioncard_input)
            {
                return true;
            }
            if (iocard_input != other.iocard_input)
            {
                return true;
            }
            if (motioncard_output != other.motioncard_output)
            {
                return true;
            }
            if (iocard_output != other.iocard_output)
            {
                return true;
            }
            if (axis_limit != other.axis_limit)
            {
                return true;
            }
            return false;
        }
    };

    // 轴移动类型
    enum class MoveType
    {
        Mov = 0,
        Mova,
        LopTo,
        Jog
    };

    // 轴运动参数
    struct MoveParam
    {
        int axis_index;                                 /**< 轴索引 */
        MoveType axis_movetype;                         /**< 轴移动类型 */
        double axis_speed;                              /**< 移动速度 */

        double axis_diatance;                           /**< Mov时表示相对位置，Mova时表示绝对位置 */
        int x_direction;                                /**< Mov模式起作用，X移动方向 */
        int y_direction;                                /**< Mov模式起作用，Y移动方向 */
        int z_direction;                                /**< Mov模式起作用，Z移动方向 */

        double axis_xpos;                               /**< LopTo模式起作用，表示X移动绝对位置 */
        double axis_ypos;                               /**< LopTo模式起作用，表示Y移动绝对位置 */
        double axis_zpos;                               /**< LopTo模式起作用，表示Z移动绝对位置 */

        bool axis_jog;                                  /**< JOG模式起作用，表示启停 */
        int axis_jog_direction;                         /**< JOG模式起作用，轴JOG方向 */
    };

    // 轨道控制类型
    enum class TrackControlType
    {
        Cylinder = 0,       // 气缸控制
        Shield,             // 挡板控制
        Load,               // 上料控制
        UnLoad,             // 下料控制
        LoadAndUnLoad,      // 上下料控制
        InitTrack,          // 初始化检测-复位轨道
        RebackInputSignal,  //回到进料信号位置
        RebackOutputSignal  //回到出料信号位置
    };

    // 轨道气缸控制参数
    struct TrackControlParam
    {
        int track_index;
        TrackControlType track_type;
        bool track_state;
        TrackControlParam()
        {
            track_index = 0;
            track_type = TrackControlType::Cylinder;
            track_state = false;
        }
    };

    // 运控参数
    struct MotionParam
    {
        MotionSatus motion_status;                      /**< 状态参数 */
        MoveParam motion_move;                          /**< 轴运动参数 */
        TrackControlParam track_param;                  /**< 轨道控制参数 */
        std::vector<std::string> flow_path;             /**< 流程文件地址 */
        std::string flow_poslist;                       /**< 点位JSON数组字符串，结构[{"xAxis":-100.2345, "yAxis" : 100.3411}, ...] */
        MotionParam()
            : motion_status({})
            , motion_move({})
            , track_param({})
            , flow_path({})
            , flow_poslist({})
        {
        }
    };

    // 相机参数

    // 光源参数

    //板子材质参数
    enum BOARD_MATERIAL
    {
        GREEN_BOARD,
        WHITE_BOARD,
        BLACK_BOARD
    };

    enum class DeviceType
    {
        Undefined,  //未定义
        Motion,     // 运控
        Camera,     // 相机
        Light       // 光源
    };

    // 额外参数
    struct ExtraParam
    {
        double product_width;   //! 产品宽度(用于运动到远端位置)
        double product_height;  //! 产品高度
        ExtraParam()
            :product_width(0)
            , product_height(0)
        {
        }
    };

    // 设备参数
    struct DeviceParam
    {
        std::string event_name;

        StructLightParam struct_light_param;
        // 运控参数
        //MotionParam motion_param;

        // 相机参数

        // 光源参数

        // 额外参数
        ExtraParam extra_param;

        DeviceType device_type;
        DeviceParam()
            :device_type(DeviceType::Undefined)
            //  , sys_param_ptr(nullptr)
        {
        }
    };

    //!当前轴坐标位置 by zhangyuyu 2024.08.28
    struct CurrentAxisPos
    {
        float current_axis_xpos; /**<当前x轴坐标位置 */
        float current_axis_ypos; /**<当前y轴坐标位置 */
        float current_axis_zpos; /**<当前z轴坐标位置*/
    };

    using DeviceParamPtr = std::shared_ptr<DeviceParam>;
}
namespace jrsdevice {
    constexpr char TRACK_DEVICE[] = "track_device";
    constexpr char OPTICAL_AXIS_DEVICE[] = "optical_axis_device";
    constexpr char LIGHT_SOURCE_DEVICE[] = "light_source_device";
    constexpr char CAMERA_DEVICE[] = "camera_device";           /**< 包含2D 3D 图片*/
}
#endif // !__DEVICEPARAM_HPP__