/*****************************************************************//**
 * @file   specalgoresultupdate.h
 * @brief  zh-cn: 该类主要用于创建规格界面并提供接口获取和设置参数值，包括创建规格界面、更新已有界面的值、获取界面当前值、设置指定参数的算法结果、批量设置多个参数的算法结果、设置字符规格的当前值、设置字符规格的标准值、设置缺陷状态等功能。
 * <AUTHOR>
 * @date 2024.12.12
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.12        <td>V1.0              <td>xailor       <td><EMAIL> <td>
 * </table>
 * @copyright  (C), 2023-2024.
 *********************************************************************/
#ifndef SPECALGORESULTUPDATE_H
#define SPECALGORESULTUPDATE_H
// std
#include <unordered_map>
#include <string>

// custom
#include "specificationwidgetparam.h"
#include "judgeparam.h"

class SpecAlgoResultUpdate
{
public:
    /**
     * @fun SetAlgoResult
     * @brief 设置单个算法结果
     * @param spec_map 规格参数map
     * @param param_name 判定项名称
     * @param algo_result 算法结果
     * @date 2024.12.12
     * <AUTHOR>
     */
    static void SetAlgoResult(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::string& param_name, double algo_result,bool use_spec_judge = true);
    /**
     * @fun SetAlgoResults
     * @brief 设置多个算法结果
     * @param spec_map 规格参数map
     * @param algo_results 算法结果映射
     * @date 2024.12.12
     * <AUTHOR>
     */
    static void SetAlgoResults(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::unordered_map<std::string, double>& algo_results, bool use_spec_judge = true);
    /**
      * @fun SetCharValues
      * @brief 设置字符规格当前值
      * @param param_name 规格名称
      * @param char_values 字符规格当前值
      * @date 2024.12.12
      * <AUTHOR>
      */
    static void SetCharValues(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::string& param_name, const std::vector<std::string>& char_values, bool use_spec_judge = true);
    /**
      * @fun SetCharStdValues
      * @brief 设置字符规格标准值
      * @param param_name 规格名称
      * @param char_values 字符规格当前值
      * @date 2024.12.12
      * <AUTHOR>
      */
    static void SetCharStdValues(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::string& param_name, const std::vector<std::string>& char_std_values);
    /**
   * @fun SetDefectStatus
   * @brief 设置缺陷当前状态 也可用于ocr 条码等和字符相关的显示
   * @param spec_map 规格参数map
   * @param param_name 判定项名称
   * @param defect_status 缺陷状态
   * @date 2024.12.12
   * <AUTHOR>
   */
    static void SetDefectStatus(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::string& param_name, const std::unordered_map<std::string, bool>& defect_status);
    /**
   * @fun SetDefectStatus
   * @brief 设置缺陷当前状态 也可用于ocr 条码等和字符相关的显示
   * @param spec_map 规格参数map
   * @param param_name 判定项名称
   * @param defect_status 缺陷状态
   * @date 2024.12.12
   * <AUTHOR>
   */
    static void SetAlgoResParam(std::unordered_map<std::string, ControlSpec>& spec_map,
        AlgoJudgeResult& algo_res_param);
    /**
   * @fun SetDefectStatus
   * @brief 设置缺陷当前状态 也可用于ocr 条码等和字符相关的显示
   * @param spec_map 规格参数map
   * @param param_name 判定项名称
   * @param defect_status 缺陷状态
   * @date 2024.12.12
   * <AUTHOR>
   */
    static void SetAlgoResParams(std::unordered_map<std::string, ControlSpec>& spec_map,
        std::unordered_map<int, AlgoJudgeResult>& algo_res_param);
    /**
   * @fun RestorateSpecStatus
   * @brief 初始化规格状态
   * @param specification_map 用于创建规格界面以及界面参数的映射表
   * @date 2025.01.07
   * <AUTHOR>
   */
    static void RestorateSpecStatus(std::unordered_map<std::string, ControlSpec>& specification_map);
};

#endif // SPECALGORESULTUPDATE_H