#include "logmanager.h"
#include "spdlogger.h"
namespace jrscore
{
    struct LogManagerImplData
    {
        /*** @brief 日志指针存储器 YYZhang 2024.1.24*/
        using Logger_M = std::unordered_map<std::string, AOILoggerPtr>;
        AOILoggerPtr default_log; /**< 默认日志器 */
        Logger_M custom_loggers_m; /**< 自定义日志指针表*/
        std::mutex custom_logger_mutex;

        AbstractLogger::LogCallBack call_back;     /**< 日志回调函数 */
        std::string log_floder_path;         /**< 日志输出文件夹 */
        LogLevel    log_output_level;        /**< 日志输出等级 */
        LogMode     log_mode;                /**< 日志同步/异步模式 */
        LogPosition log_pos;                 /**< 日志输出方式 控制台/文件 */

        LogManagerImplData ()
            :default_log (nullptr)
            , call_back (NULL)
            , log_floder_path ("")
            , log_output_level (LogLevel::LEVEL_INFO)
            , log_mode (LogMode::ASYNC)
            , log_pos (LogPosition::CONSOLE_AND_FILE)
        {

        }

    };

    LogManager::LogManager ():
        p_data(new LogManagerImplData)
        
    {
    }
    LogManager::~LogManager ()
    {
        delete p_data;
        p_data = nullptr;
    }
    void LogManager::SetLogFolderPath (const std::string& path_)
    {
        p_data->log_floder_path = path_;
    }
    const std::string& LogManager::GetFolderPath () const
    {
        return p_data->log_floder_path;
    }
    void LogManager::SetLogPosition (const LogPosition pos_)
    {
        p_data->log_pos = pos_;
        assert (!p_data->default_log);
        if (!p_data->default_log)
        {
            return;
        }
        p_data->default_log->SetLogPosition (pos_);
        std::lock_guard<std::mutex> lock (p_data->custom_logger_mutex);
        for (auto& value : p_data->custom_loggers_m)
        {
            value.second->SetLogPosition (pos_);
        }
    }
    void LogManager::SetLogMode (const LogMode mode_)
    {
        p_data->log_mode = mode_;
    }
    void LogManager::SetLogOutputLevel (const LogLevel level_)
    {
        p_data->log_output_level = level_;
        if (!p_data->default_log)
        {
            return;
        }
        p_data->default_log->SetLogOutputLevel (level_);
        std::lock_guard<std::mutex> lock (p_data->custom_logger_mutex);
        for (auto& value : p_data->custom_loggers_m)
        {
            value.second->SetLogOutputLevel (level_);
        }
    }
    void LogManager::SetLogCallBack (AbstractLogger::LogCallBack cb_)
    {
        p_data->call_back = cb_;

        std::lock_guard<std::mutex> lck (p_data->custom_logger_mutex);
        for (auto& value : p_data->custom_loggers_m)
        {
            value.second->SetLogCallBack (p_data->call_back);
        }
    }
    const LogPosition LogManager::GetLogPosition () const
    {
        return p_data->log_pos;
    }
    const LogMode LogManager::GetLogMode () const
    {
       
        return p_data->log_mode;
    }
    LogLevel LogManager::GetLogOutputLevel () const
    {
        return p_data->log_output_level;
    }
    AOIErrorCode LogManager::CreateLogger (const std::string& logger_name_)
    {
        std::lock_guard<std::mutex> lck (p_data->custom_logger_mutex);
        if (p_data->custom_loggers_m.find (logger_name_) == p_data->custom_loggers_m.end ())
        {
            p_data->custom_loggers_m[logger_name_] = std::make_unique<Spdlogger> (logger_name_);
            p_data->custom_loggers_m[logger_name_]->SetLogCallBack (p_data->call_back);
            p_data->custom_loggers_m[logger_name_]->SetLogPosition (GetLogPosition ());
            p_data->custom_loggers_m[logger_name_]->SetLogMode (GetLogMode ());
            p_data->custom_loggers_m[logger_name_]->SetLogOutputLevel (GetLogOutputLevel ());
            p_data->custom_loggers_m[logger_name_]->SetLogFolderPath (GetFolderPath ());
            return p_data->custom_loggers_m[logger_name_]->Init ();
        }
        return CoreError::E_AOI_CORE_REPEAT_INIT;
    }
    AOIErrorCode LogManager::CreateDefaultLogger ()
    {
        if (p_data->default_log)
            return CoreError::E_AOI_CORE_REPEAT_INIT;

        if (p_data->log_floder_path.empty ())
            return CoreError::E_AOI_CORE_INIT_FAIL;


        p_data->default_log = std::make_unique<Spdlogger> ("JrsAOI");
        p_data->default_log->SetLogCallBack (p_data->call_back);
        p_data->default_log->SetLogPosition (GetLogPosition ());
        p_data->default_log->SetLogMode (GetLogMode ());
        p_data->default_log->SetLogOutputLevel (GetLogOutputLevel ());
        p_data->default_log->SetLogFolderPath (GetFolderPath ());

        return p_data->default_log->Init ();
    }
    void LogManager::LogDefault (const LogLevel level_, const std::string& msg_)
    {
        assert (p_data->default_log);
        return p_data->default_log->Log (level_, msg_);
    }
    void LogManager::LogCustom (const std::string& log_name_, const LogLevel level_, const std::string& msg_)
    {

        if (p_data->custom_loggers_m.find (log_name_) == p_data->custom_loggers_m.end ())
        {
            CreateLogger (log_name_);
        }
        p_data->custom_loggers_m[log_name_]->Log (level_, msg_);
    }
}