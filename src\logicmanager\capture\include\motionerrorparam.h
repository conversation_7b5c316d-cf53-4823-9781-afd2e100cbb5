#ifndef MOTIONERRORPARAM_H
#define MOTIONERRORPARAM_H

#include <vector>
#include <string>

//using namespace std;

using std::vector;
using std::string;

/// @brief 点结构
struct JrsPoint
{
    double x;
    double y;
    double z;
    JrsPoint() : x(0.0), y(0.0), z(0.0) {};
    JrsPoint(const double& _x, const double& _y) : x(_x), y(_y), z(0.0) {};
    JrsPoint(const double& _x, const double& _y, const double& _z) : x(_x), y(_y), z(_z) {};
    JrsPoint operator+(const JrsPoint& other) const
    {
        JrsPoint result;
        result.x = x + other.x;
        result.y = y + other.y;
        result.z = z + other.z;
        return result;
    }
};

/// @brief 运动误差模型参数
struct MotionErrorParam
{
    double x_start, x_end;       // x方向的标定范围
    double y_start, y_end;       // y方向的标定范围
    int x_n;                     // x方向Bezier曲线阶数
    int y_n;                     // y方向Bezier曲线阶数
    vector<JrsPoint> x_ctrl_pts; // x方向Bezier曲线控制点
    vector<JrsPoint> y_ctrl_pts; // y方向Bezier曲线控制点
};

bool ReadMotionErrorParamFile(const string& file_path, MotionErrorParam& param);
bool WriteMotionErrorParamFile(const string& file_path, const MotionErrorParam& param);

#endif