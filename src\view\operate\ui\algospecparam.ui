<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AlgoSpecParam</class>
 <widget class="QDialog" name="AlgoSpecParam">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>707</width>
    <height>450</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="text">
        <string>参数管控列表</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="param_table_widget">
     <column>
      <property name="text">
       <string>名称</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>偏移X(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>偏移Y(%)</string>
      </property>
      <property name="textAlignment">
       <set>AlignCenter</set>
      </property>
     </column>
     <column>
      <property name="text">
       <string>旋转(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>分数(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>面积(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>高度量测</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>相对高度</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>焊锡漫延(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>焊接宽度(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>焊接饱满度(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>锡桥长度(%)</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>区块(%)</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="save_btn">
       <property name="text">
        <string>保存</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="add_btn">
       <property name="text">
        <string>新增</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="delete_btn">
       <property name="text">
        <string>删除</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="close_btn">
       <property name="text">
        <string>关闭</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
