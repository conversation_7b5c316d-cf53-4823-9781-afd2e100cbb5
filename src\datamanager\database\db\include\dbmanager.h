﻿#pragma once
#include <iostream>

//db
#include "db.h"
#include "tablemanager.h"
#include "objectpool.hpp"
//core
#include "coreapplication.h"
#include "tools.h"
#include "idatabase.h"
namespace jrsdatabase
{
    /**
     *  @brief 单个数据库的连接管理
     */
    class  DBManager
    {
    public:
        DBManager();
        DBManager(std::shared_ptr<IDatabase<MySqlImp>> db_conn_ptr_);
        ~DBManager();
        //int InitDatabase();
        inline int SetDBConnPtr(std::shared_ptr<IDatabase<MySqlImp>> db_conn_ptr_)
        {
            if (!db_conn_ptr_)
            {
                Log_Error_Stack("传入指针为空，请检查!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            _db_conn_ptr = db_conn_ptr_;
            return jrscore::AOI_OK;
        }
        /**<根据模板 调用指针 */
        template <typename T>
        int Insert(const T& table_)
        {
            //std::cout << "this struct name:" << typeid(T).name() << std::endl;
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

            auto table_temp_ptr = _table_ptr->GetTable(table_.table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            std::shared_ptr<jrsdatabase::jrstable::TableParamBase> temp_ptr = std::make_shared<T>(table_);
            return table_temp_ptr->Insert(temp_ptr, _db_conn_ptr);
        }
        template <typename T>
        int Insert(const std::vector<T>& tables_)
        {
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            if (tables_.empty() && !_db_conn_ptr)
            {
                Log_Error_Stack("结构体或数据库连接指针为空,请检查!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            auto table_temp_ptr = _table_ptr->GetTable(tables_.front().table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            table_temp_ptr->Insert(TVectorToBaseVector(tables_), _db_conn_ptr);

            return 0;
        }
        template <typename T>
        int Update(const T& table_)
        {
            //std::cout << "this struct name:" << typeid(T).name() << std::endl;
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

            auto table_temp_ptr = _table_ptr->GetTable(table_.table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            std::shared_ptr<jrsdatabase::jrstable::TableParamBase> temp_ptr = std::make_shared<T>(table_);
            table_temp_ptr->Update(temp_ptr, _db_conn_ptr);
            return 0;
        }
        template <typename T>
        int Update(const std::vector<T>& tables_)
        {
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            if (tables_.empty() && !_db_conn_ptr)
            {
                Log_Error_Stack("结构体或数据库连接指针为空,请检查!");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            auto table_temp_ptr = _table_ptr->GetTable(tables_.front().table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            table_temp_ptr->Update(TVectorToBaseVector(tables_), _db_conn_ptr);
            return 0;
        }
        template <typename T>
        int Replace(const T& table_)
        {
            //std::cout << "this struct name:" << typeid(T).name() << std::endl;
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

            auto table_temp_ptr = _table_ptr->GetTable(table_.table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            std::shared_ptr<jrsdatabase::jrstable::TableParamBase> temp_ptr = std::make_shared<T>(table_);
            table_temp_ptr->Replace(temp_ptr, _db_conn_ptr);
            return 0;
        }
        template <typename T>
        int Replace(const std::vector<T>& tables_)
        {
            if (!std::is_base_of<jrstable::TableParamBase, T>::value)
            {
                Log_Error_Stack("请检查结构体是否已经继承");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            if (tables_.empty() && !_db_conn_ptr)
            {
                Log_Error_Stack("结构体或数据库连接指针为空,请检查!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            auto table_temp_ptr = _table_ptr->GetTable(tables_.front().table_name);
            if (!table_temp_ptr)
            {
                Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            table_temp_ptr->Replace(TVectorToBaseVector(tables_), _db_conn_ptr);
            return 0;
        }

        /**
         * @fun ExecuteSQL
         * @brief 执行自定义sql语句
         * @param sql_
         * @return 成功 AOI_OK  失败错误代码
         * <AUTHOR>
         * @date 2025.2.20
         */
        int ExecuteSQL(const std::string& sql_);

        /**
         * @fun CustomUpdate
         * @brief  自定义更新接口
         * @param custom_data_  输入更新数据的条件
         * @return
         * <AUTHOR>
         * @date 2025.2.20
         */
        int CustomUpdate(const std::vector<jrsdatabase::CustomUpdate>& custom_data_);
        /**
         * @fun DeleteTables
         * @brief
         * @param tables_and_where_conditions_
         * @return
         * <AUTHOR>
         * @date 2025.6.18
         */
        int DeleteTables(const std::unordered_map<std::string, std::string>& tables_and_where_conditions_);

        int Select(const jrsselect::SelectorParamBasePtr& selector_ptr_);

        inline auto GetLatestOperateTime()
        {
            return std::any_cast<std::chrono::system_clock::time_point>(_db_conn_ptr->GetLatestOperateTime());
        }
        inline auto GetDBConnPtr()
        {
            return _db_conn_ptr;
        }
        int InitTable();
    private:
        int InitMember();
        template <typename T>
        std::vector<jrstable::TableParamBasePtr> TVectorToBaseVector(const std::vector<T>& vec) {
            static_assert(std::is_base_of<jrstable::TableParamBase, T>::value, "T must be derived from TableParamBase");
            std::vector<jrstable::TableParamBasePtr> base_vec;
            base_vec.reserve(vec.size());
            for (const auto& item : vec)
            {
                base_vec.push_back(std::make_shared<T>(item));
            }
            return base_vec;
        }

        std::mutex _mtx;
        std::shared_ptr<jrsdatabase::TableManager> _table_ptr;
        std::shared_ptr<IDatabase<MySqlImp>> _db_conn_ptr;
    };
    using DBManagerPtr = std::shared_ptr<DBManager>;

    /**
     * @brief 多个数据库的连接管理.
     */
    class DBManagers
    {
    public:
        DBManagers();
        ~DBManagers();

        static int InitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_);
        static int ReinitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_);

        static int DisconnectDatabase();
        static bool GetDBState();
        /**< 数据库接口 */
        template<typename Table>
        int Insert(const Table& data_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr)
            {
                auto res = db_ptr->Insert(data_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }
        template<typename Table>
        int Insert(const std::vector<Table>& datas_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            if (datas_.empty())
            {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr)
            {
                auto res = db_ptr->Insert(datas_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }
        /**< 新增接口 */
        template <typename T>
        int Update(const T& data_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr) {
                auto res = db_ptr->Update(data_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }
        template <typename T>
        int Update(const std::vector<T>& datas_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            if (datas_.empty())
            {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr) {
                auto res = db_ptr->Update(datas_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }
        /**< 新增接口 */
        template <typename T>
        int Replace(T& data_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr) {
                auto res = db_ptr->Replace(data_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }
        template <typename T>
        int Replace(const std::vector<T>& datas_)
        {
            if (!GetDBState())
                return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
            if (datas_.empty())
            {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            auto db_ptr = _db_obj_pool.AcquireObject();
            if (db_ptr)
            {
                auto res = db_ptr->Replace(datas_);
                _db_obj_pool.ReturnBack(db_ptr);
                return res;
            }
            else
            {
                Log_ERROR("获取数据库连接失败，请检查数据!");
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
        }

        int Select(const jrsdatabase::jrsselect::SelectorParamBasePtr& selector_ptr_);

        int CustomUpdate(const std::vector<jrsdatabase::CustomUpdate>& custom_data_);



        /**
        * @fun DeleteTables
        * @brief
        * @param tables_and_where_conditions_
        * @return
        * <AUTHOR>
        * @date 2025.6.18
        */
        int DeleteTables(const std::unordered_map<std::string, std::string>& tables_and_where_conditions_);

        int ExecuteSQL(const std::string& sql_);
        /**
         * @fun DeleteTable
         * @brief
         * @param table_name_
         * @return
         * <AUTHOR>
         * @date 2025.6.20
         */
        int DeleteTable(const std::string& table_name_);
    private:
        /**< 数据库参数  */
        static jrsdatabase::DBPtr _db_ptr;
        static std::atomic<bool> _is_init_pool;
        static ObjectPool<jrsdatabase::DBManager> _db_obj_pool;
    };
    using DBManagersPtr = std::shared_ptr<DBManagers>;
};
