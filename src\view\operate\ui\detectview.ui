<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DetectView</class>
 <widget class="QWidget" name="DetectView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>169</width>
    <height>867</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="detect_tab"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
