#include "generaltool.h"

namespace jrsoperator
{
    bool CropImage(const cv::<PERSON>& src, cv::Mat& dst, const cv::Rect& rect)
    {
        if (src.empty() || rect.empty()) return false;
        if (JudgePointOutImage(src.cols, src.rows, rect.br())) return false;
        if (JudgePointOutImage(src.cols, src.rows, rect.tl())) return false;

        dst = src(rect).clone();
        return true;
    }
    bool CropImage(const cv::Mat& src, cv::Mat& dst, const cv::RotatedRect& rect)
    {
        if (rect.size.area() == 0) return false;
        cv::Point2f vertices[4];
        rect.points(vertices);
        auto row_max = src.rows;
        auto col_max = src.cols;
        for (auto& point : vertices)
        {
            if (JudgePointOutImage(col_max, row_max, point)) return false;
        }
        cv::Rect rotate_bounding_rect = rect.boundingRect();
        rotate_bounding_rect.x = std::max(0, rotate_bounding_rect.x);
        rotate_bounding_rect.y = std::max(0, rotate_bounding_rect.y);
        rotate_bounding_rect.width = std::min(src.cols, rotate_bounding_rect.width - 1);
        rotate_bounding_rect.height = std::min(src.rows, rotate_bounding_rect.height - 1);
        if (rotate_bounding_rect.width <= 0 || rotate_bounding_rect.height <= 0 ||
            rotate_bounding_rect.x >= src.cols || rotate_bounding_rect.y >= src.rows ||
            rotate_bounding_rect.x + rotate_bounding_rect.width > src.cols ||
            rotate_bounding_rect.y + rotate_bounding_rect.height > src.rows)
            return false;
        cv::Mat big_to_small_mat = src(rotate_bounding_rect).clone();
        cv::RotatedRect big_to_small_rotate = rect;
        big_to_small_rotate.center.x -= rotate_bounding_rect.x;
        big_to_small_rotate.center.y -= rotate_bounding_rect.y;
        GetRotateRigion(big_to_small_mat, dst, big_to_small_rotate);
        return true;
    }
    bool JudgePointOutImage(int max_x, int max_y, const cv::Point2f& test_point)
    {
        bool pt_is_out_image = false;
        auto pt_x = test_point.x;
        auto pt_y = test_point.y;
        if (pt_x<0 || pt_y<0 || pt_x>max_x || pt_y>max_y)
        {
            pt_is_out_image = true;
        }
        return pt_is_out_image;
    }
    bool GetRotateRigion(const cv::Mat& src, cv::Mat& dst, const cv::RotatedRect& rotate)
    {
        if (src.empty() || rotate.size.area() == 0)
        {
            return false;
        }
        double angle = static_cast<double>(rotate.angle);
        cv::Size2f rect_size = rotate.size;
        /*if (rect_size.width < rect_size.height)
        {
            angle += 90.0;
            rect_size = cv::Size2f(rotate.size.height, rotate.size.width);
        }*/
        if (rect_size.width <= 0 || rect_size.height <= 0)
        {
            return false;
        }
        cv::Rect bounding_box = rotate.boundingRect();
        bounding_box.x = std::max(0, bounding_box.x);
        bounding_box.y = std::max(0, bounding_box.y);
        bounding_box.width = std::min(bounding_box.width, src.cols - bounding_box.x);
        bounding_box.height = std::min(bounding_box.height, src.rows - bounding_box.y);

        if (bounding_box.width <= 0 || bounding_box.height <= 0)
        {
            return false;
        }

        cv::Mat cropped;
        cv::getRectSubPix(src, bounding_box.size(), cv::Point2f(static_cast<float>(rotate.center.x),
            static_cast<float>(rotate.center.y)), cropped, src.type());

        cv::Point2f center_point(static_cast<float>(bounding_box.width) * 0.5f,
            static_cast<float>(bounding_box.height) * 0.5f);

        cv::Mat rotation_matrix = cv::getRotationMatrix2D(center_point, angle, 1.0);
        cv::Mat rotated;
        cv::warpAffine(cropped, rotated, rotation_matrix, bounding_box.size(), cv::INTER_CUBIC, cv::BORDER_CONSTANT);

        cv::Size final_size(static_cast<int>(std::round(rect_size.width)),
            static_cast<int>(std::round(rect_size.height)));

        cv::getRectSubPix(rotated, final_size, center_point, dst, src.type());

        return true;
    }
    bool WarpMatrixToRect(const cv::Rect2f& src_rect, const cv::Mat& hom_matrix, cv::RotatedRect& rotate_rect)
    {
        cv::Point2f center(src_rect.x + src_rect.width * 0.5f, src_rect.y + src_rect.height * 0.5f);
        auto rect_pt1 = src_rect.tl() - center;
        auto rect_pt2 = cv::Point2f(src_rect.x + src_rect.width, src_rect.y) - center;
        auto rect_pt3 = src_rect.br() - center;
        std::vector<cv::Point2f> points = { rect_pt1 , rect_pt2, rect_pt3 };
        auto transform_points = AffineTransformToPts(hom_matrix, points);

        rotate_rect = CreateRotatedRect(transform_points[0], transform_points[1], transform_points[2]);
        rotate_rect.center += center;
        if (rotate_rect.size.area() == 0)
        {
            return false;
        }
        return true;
    }
    bool WarpMatrixToRect(const cv::Rect2f& src_rect, 
        const cv::Mat& hom_matrix, const cv::Point2f center, cv::RotatedRect& rotate_rect)
    {
        auto rect_pt1 = src_rect.tl() - center;
        auto rect_pt2 = cv::Point2f(src_rect.x + src_rect.width, src_rect.y) - center;
        auto rect_pt3 = src_rect.br() - center;
        std::vector<cv::Point2f> points = { rect_pt1 , rect_pt2, rect_pt3 };
        auto transform_points = AffineTransformToPts(hom_matrix, points);

        rotate_rect = CreateRotatedRect(transform_points[0], transform_points[1], transform_points[2]);
        rotate_rect.center += center;
        if (rotate_rect.size.area() == 0)
        {
            return false;
        }
        return true;
    }
    cv::Point2f AffineTransformToPt(const cv::Mat& hom_matrix, const cv::Point2f& point)
    {
        cv::Mat affine_matrix_32f;
        hom_matrix.convertTo(affine_matrix_32f, CV_32F);
        cv::Mat point_mat = (cv::Mat_<float>(3, 1) << point.x, point.y, 1.0);
        cv::Mat transformed_point_mat = affine_matrix_32f * point_mat;
        auto x = float(transformed_point_mat.at<float>(0, 0));
        auto y = float(transformed_point_mat.at<float>(1, 0));
        return cv::Point2f(x, y);
    }
    std::vector<cv::Point2f> AffineTransformToPts(const cv::Mat& hom_matrix,
        std::vector<cv::Point2f>& points)
    {
        if (points.empty())
        {
            return std::vector<cv::Point2f>();
        }

        std::vector<cv::Point2f> transformed_points;
        transformed_points.resize(points.size());

        cv::Mat affine_matrix_23;
        if (hom_matrix.type() != CV_32F)
        {
            cv::Mat temp;
            hom_matrix.convertTo(temp, CV_32F);
            affine_matrix_23 = temp(cv::Rect(0, 0, 3, 2));
        }
        else
        {
            affine_matrix_23 = hom_matrix(cv::Rect(0, 0, 3, 2));
        }

        cv::transform(points, transformed_points, affine_matrix_23);

        return transformed_points;
    }
    cv::Point2f AffineTransformToPt(const cv::Mat& hom_matrix, const cv::Point2f& point, const cv::Point2f& rotate_center)
    {
        cv::Point2f current_rotate = point - rotate_center;
        cv::Mat affine_matrix_32f;
        hom_matrix.convertTo(affine_matrix_32f, CV_32F);
        cv::Mat point_mat = (cv::Mat_<float>(3, 1) << current_rotate.x, current_rotate.y, 1.0);
        cv::Mat transformed_point_mat = affine_matrix_32f * point_mat;
        auto current_x = float(transformed_point_mat.at<float>(0, 0));
        auto current_y = float(transformed_point_mat.at<float>(1, 0));

        return cv::Point2f(current_x, current_y) + rotate_center;
    }
    std::vector<cv::Point2f> AffineTransformToPts(const cv::Mat& hom_matrix, std::vector<cv::Point2f>& points,
        const cv::Point2f& rotate_center)
    {
        std::vector<cv::Point2f> current_points;
        for (auto& point : points)
        {
            current_points.push_back(point - rotate_center);
        }

        if (points.empty())
        {
            return std::vector<cv::Point2f>();
        }

        std::vector<cv::Point2f> transformed_points;
        transformed_points.resize(points.size());

        cv::Mat affine_matrix_23;
        if (hom_matrix.type() != CV_32F)
        {
            cv::Mat temp;
            hom_matrix.convertTo(temp, CV_32F);
            affine_matrix_23 = temp(cv::Rect(0, 0, 3, 2));
        }
        else
        {
            affine_matrix_23 = hom_matrix(cv::Rect(0, 0, 3, 2));
        }

        cv::transform(current_points, transformed_points, affine_matrix_23);

        for (auto& point : transformed_points)
        {
            point += rotate_center;
        }

        return transformed_points;
    }
    cv::RotatedRect CreateRotatedRect(const cv::Point2f& pt1, const cv::Point2f& pt2, const cv::Point2f& pt3)
    {
        const float epsilon = 1e-6f;
        if (cv::norm(pt2 - pt1) < epsilon ||
            cv::norm(pt3 - pt2) < epsilon ||
            cv::norm(pt3 - pt1) < epsilon)
        {
            return cv::RotatedRect();
        }
        cv::Point2f center = (pt1 + pt3) / 2.0f;
        cv::Point2f vec1 = pt2 - pt1;
        cv::Point2f vec2 = pt3 - pt2;
        float width = static_cast<float>(cv::norm(vec1));
        float height = static_cast<float>(cv::norm(vec2));
        if (width < epsilon || height < epsilon)
        {
            return cv::RotatedRect();
        }
        float angle = float(atan2(vec1.y, vec1.x) * 180.0f / CV_PI);
        return cv::RotatedRect(center, cv::Size2f(width, height), angle);
    }
    cv::Mat CreateMatrix(float angle, float x_offset, float y_offset)
    {
        cv::Mat matrix = cv::Mat::ones(2, 3, CV_32F);
        float radian = float(angle * CV_PI / 180.0f);
        matrix.at<float>(0, 0) = std::cos(radian);
        matrix.at<float>(0, 1) = -std::sin(radian);
        matrix.at<float>(1, 0) = std::sin(radian);
        matrix.at<float>(1, 1) = std::cos(radian);
        matrix.at<float>(0, 2) = x_offset;
        matrix.at<float>(1, 2) = y_offset;
        return matrix;
    }
    cv::Mat ReverseMatrix(const cv::Mat& hom_matrix)
    {
        cv::Mat reverse_matrix = hom_matrix.inv();
        return reverse_matrix;    
    }
    cv::Mat AffineMatrixToMat(const cv::Mat& image, const cv::Mat& hom_matrix)
    {
        cv::Mat affine_matrix_32f;
        hom_matrix.convertTo(affine_matrix_32f, CV_32F);
        cv::Mat image_32f;
        image.convertTo(image_32f, CV_32F);
        cv::Mat result;
        cv::warpAffine(image_32f, result, affine_matrix_32f, image.size());
        return result;
    }
}