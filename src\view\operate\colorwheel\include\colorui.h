/*****************************************************************//**
 * @file   colorui.h
 * @brief  颜色控件实现
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSCOLORUI_H__
#define __JRSCOLORUI_H__
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
// std
#include <string>
#include <map>
#include <vector>
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>
// custom
#include "colorwidget.h"
#include "colorparams.h"
#pragma warning(pop)

class ImageBinaryControl;
class ImagePreProcessWindow;
class ImageProcessor;
class BinaryAlgo;
class QPushButton;
class QComboBox;
class ColorUi : public QWidget, public ColorModule
{
public:
	explicit ColorUi(QWidget* parent = nullptr);
    virtual int SetTestData(const cv::Mat& image_groups,
		const std::string& color_params_str = "", bool is_origin_image = false) override;
	virtual QWidget* GetWindowHandle() override;
    virtual int ShowWindow() override;
    virtual int SetColorChangedCallback(ColorParamsChangedFunc func) override;
    virtual cv::Mat GetPreProcessResult(const cv::Mat& image_group, ColorParams& params,
			cv::Mat& output_img)  override;
	virtual int GetColorProcessImage(const cv::Mat& input_image, cv::Mat& out_image, ColorParams& params) override;	
    virtual int GetColorProcessImageWithParam(const cv::Mat& input_image,ColorParams& params, cv::Mat& out_image) override;
    virtual const ColorParams GetColorProcessParam()override;
public slots:
	/**
    * @fun  OnAddButtonClicked
    * @brief  增加预配参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	void OnAddButtonClicked();
	/**
    * @fun  OnDelButtonClicked
    * @brief  减少预配参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	void OnDelButtonClicked();
	/**
    * @fun  OnUpdateButtonClicked
    * @brief  更新预配参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	void OnUpdateButtonClicked();
	/**
    * @fun  GetPreProcessResult
    * @brief  获取预处理结果
    * @date   2024.08.18
    * <AUTHOR>
    */
	void SetPreParamsToUsed();
private:
	ImageBinaryControl* binary_wheel_;
	ImagePreProcessWindow* pre_process_wheel_;
	//cv::Mat pre_process_image_;
	ColorParamsChangedFunc color_params_changed_func_;
	//cv::Mat bianry_image_;
    //cv::Mat result_;
	ImageProcessor* image_processor_;
	BinaryAlgo* binary_processor_;
	QPushButton* add_button = nullptr;
	QPushButton* del_button = nullptr;
    QPushButton* update_button = nullptr;
	QComboBox* pre_params_name_= nullptr;
	ColorParams params_;
    cv::Mat m_input_img;
    //cv::Mat m_enhance_image;
    //std::string pre_params_path_;
	//ColorMap map_params_;

};
#endif