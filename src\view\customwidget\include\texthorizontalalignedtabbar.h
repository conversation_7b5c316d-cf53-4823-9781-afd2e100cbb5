/*********************************************************************
 * @brief  qtabwidget文字水平显示.
 *
 * @file   texthorizontalalignedtabbar.h
 *
 * @date   2024.09.18
 * <AUTHOR>
 *********************************************************************/
#ifndef TEXTHORIZONTALALIGNEDTABBAR_H
#define TEXTHORIZONTALALIGNEDTABBAR_H
#include <QStyleOptionTab>
#include <QStylePainter>
#include <QTabBar>

class TextHorizontalAlignedTabBar :public QTabBar
{
public:
    TextHorizontalAlignedTabBar();
    QSize tabSizeHint(int index) const override;

protected:
    void paintEvent(QPaintEvent*) override;
};

#endif // TEXTHORIZONTALALIGNEDTABBAR_H