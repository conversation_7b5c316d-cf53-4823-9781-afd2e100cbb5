/*****************************************************************//**
 * @file   imagepreprocess.h
 * @brief  图像预处理UI
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSIMAGEPREPROCESS_H__
#define __JRSIMAGEPREPROCESS_H__
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>
// qt
#include <QWidget>
#include <QComboBox>
#include <QPushButton>
#include <QCheckBox>
#include <QLineEdit>
#include <QSlider>
#include <QLabel>
#pragma warning(pop)
// thirparty
#include "colorparams.h"
class CustomSlider;
class ImageProcessor;
class PreviewWindow;
using std::vector;
using cv::Mat;
using PreProcessParamsChangedFunc = 
std::function<void(PreProcessParams& params, 
	cv::Mat& bin_result)>;
class ImagePreProcessWindow : public QWidget
{
  Q_OBJECT

public:
	/**
    * @fun  ImagePreProcessWindow
    * @brief  构造函数
    * @date   2024.08.18
    * <AUTHOR>
    */
	explicit ImagePreProcessWindow(QWidget* parent = nullptr);
	/**
    * @fun  SetPreProcessParamsChangedCallback
    * @brief  设置图像预处理回调函数
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetPreProcessParamsChangedCallback(PreProcessParamsChangedFunc func);
	/**
    * @fun  SetProcessImage
    * @brief  设置预处理图像组
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetProcessImage(const cv::Mat& image_group_);
	/**
    * @fun  SetPreProcessParmas
    * @brief  设置预处理参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetPreProcessParmas(PreProcessParams& params,bool is_origin_image);
	/**
    * @fun  GetPreProcessImage
    * @brief  获取预处理结果
    * @date   2024.08.18
    * <AUTHOR>
    */
		/**
    * @fun  RestoreParams
    * @brief  预处理参数恢复默认值
    * @date   2024.08.18
    * <AUTHOR>
    */
	void RestoreParams();

    int GetPreProcessParam(PreProcessParams& pre_process_params);
	cv::Mat GetPreProcessImage();
	~ImagePreProcessWindow();
signals:
	/**
    * @fun  UpdateSelectId
    * @brief  将图像ID设置到UI
    * @date   2024.08.18
    * <AUTHOR>
    */
	void UpdateSelectId(int id);
private slots:
	/**
    * @fun    OnPreviewButtonClicked
    * @brief  打开图像浏览窗口
    * @date   2024.08.18
    * <AUTHOR>
    */
	void OnPreviewButtonClicked();
	/**
    * @fun    MapRangeToDouble
    * @brief  将整形数字映射到doubel
    * @date   2024.08.18
    * <AUTHOR>
    */
	void MapRangeToDouble(int value, QLineEdit* slider,double min, double max);
	/**
    * @fun  GetImageId
    * @brief  从图像浏览窗口获取当前图像的ID
    * @date   2024.08.18
    * <AUTHOR>
    */
	void GetImageId(int id);
	/**
    * @fun  UpdataProcessParams
    * @brief  更新图像预处理参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	void UpdataProcessParams();
private:
	QPushButton* preview_button_ = nullptr; // 打开浏览窗口按钮
	QPushButton* restore_button_ = nullptr; // 恢复图像按钮
	QCheckBox* invert_checkbox_ = nullptr; // 是否反转图像颜色
	QLineEdit* brightness_lineedit_ = nullptr; // 亮度显示值
	CustomSlider* brightness_slider_ = nullptr; // 亮度调整条
	QLineEdit* contrast_lineedit_ = nullptr; // 对比度值显示
	CustomSlider* contrast_slider_ = nullptr; // 对比度调整条
	QLineEdit* hue_lineedit_ = nullptr; // 色调显示值
	CustomSlider* hue_slider_ = nullptr; // 色调滑动条
	QLineEdit* saturation_lineedit_ = nullptr; // 饱和度值
	CustomSlider* saturation_slider_ = nullptr;// 饱和度调整条
	QLineEdit* gamma_lineedit_ = nullptr; // 伽马显示值
	CustomSlider* gamma_slider_ = nullptr;// 伽马调整滑动条
	//ImageProcessor* image_processor_ = nullptr;
	PreviewWindow* preview_window_ = nullptr; // 预览窗口
    
	PreProcessParams pre_process_params_; // 图像与处理参数
	Mat height_mat_; // 高度图
	Mat result_; // 结果图
	vector<Mat> process_image_group_; // 图像处理组
    //std::string image_name_; // 图像名称
    cv::Mat m_input_image;// 输入图像
	//vector<std::string> image_names_;
	int id_ = 0; // 图像id
	//float contrast_value_ = 1.0; // 对比度值
	//float brightness_value_= 1.0; // 亮度值
	//float hue_value_ = 0; // 色调值
	//float saturation_value_= 1.0; // 饱和度值
	//float gamma_value_ = 1.0; // 伽马值
	//bool is_reverse_ = false; // 反转
	bool is_initialize = true; // 初始化
 //   std::map<int, int> id_mapping; // id映射表
	void SetupUi();
    void InitConnect();
	int DoubleToMapRange(double value, int min, int max);
    bool m_is_origin_mat;
	PreProcessParamsChangedFunc pre_process_params_changed_func_;
};

#endif  // MAIN_WINDOW_H_
