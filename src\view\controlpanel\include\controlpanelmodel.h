/*****************************************************************//**
 * @file   controlconsoleModel.h
 * @brief  主界面上控制面板的model类
 * @details 主界面上软件运行停止/运行状态显示/打开硬件/信息/切换用户面板的model类
 * <AUTHOR>
 * @date 2024.1.17
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.17         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __CONTROLCONDOLEMODEL_H__
#define __CONTROLCONDOLEMODEL_H__


#include "modelbase.h"

namespace jrsaoi
{
    class  ControlPanelModel :public ModelBase
    {
    public:
        ControlPanelModel(const std::string& name);
        ~ControlPanelModel();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        const jrsdata::ViewParamBasePtr GetAutoRunParam();
        const jrsdata::ControlPanelViewParamPtr& GetModelData();
    private:
        void InitMember();
        void UpdateSystemState(const jrsdata::SystemStateParamPtr& system_state_ptr_);


        //Member
        jrsdata::ControlPanelViewParamPtr auto_run_panel_param_ptr;
    };
    using ControlPanelModelPtr = std::shared_ptr<ControlPanelModel>;



}

#endif // !__CONTROLCONDOLEMODEL_H__
