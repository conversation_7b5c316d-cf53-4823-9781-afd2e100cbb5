﻿/*****************************************************************//**
 * @file   showlistview.h
 * @brief  产品列表界面
 *
 * <AUTHOR>
 * @date   2024.8.22
 *********************************************************************/
#ifndef __SHOWLISTVIEW_H__
#define __SHOWLISTVIEW_H__
 //CUSTOM
#include "datadefine.hpp"
#include "tableviewmodel.h"
#include "viewbase.h"
#include "viewparam.hpp"
//QT
#include <QBrush>
#include <QModelIndex>
#include <QPainter>
#include <QStandardItemModel>
#include <QStyledItemDelegate>
#include <QStyleOptionViewItem>
#include <QVariant>
#include <QTableView>

using namespace jrsdata;

#pragma warning(push, 3)
#include "ui_showlistview.h"
#pragma warning(pop)
QT_BEGIN_NAMESPACE
namespace Ui { class ShowListView; };
QT_END_NAMESPACE

namespace jrsaoi
{
    enum class LISTVIEW_DATA_DEFINE
    {
        /// 主体形状为圆形
        LISTVIEW_BODY_CIRCULAR = 0,
        /// 主体形状为矩形
        LISTVIEW_BODY_RECTANGLE,
        /// 主体未测试标志
        LISTVIEW_BODY_NOT_TESTED,
        /// 主体宽度
        LISTVIEW_BODY_WIDTH,
        /// 主体高度
        LISTVIEW_BODY_HEIGHT,
        /// 主体角度值
        LISTVIEW_BODY_ANGLE_VALUE,
        /// 主体角度增加值
        LISTVIEW_BODY_ANGLE_VALUE_ADD
    };
    class ShowListView : public ViewBase
    {
        Q_OBJECT
    public:
        ShowListView(const std::string& name, QWidget* parent = nullptr);
        ~ShowListView();
        /**
         * @fun Init
         * @brief 初始化界面和数据模型。
         * @return [int] 初始化结果，返回 jrscore::AOI_OK 表示成功。
         * @date 2024.08.22
         * <AUTHOR>
         */
        virtual int Init() override;
        /**
         * @fun InitializeModels
         * @brief 初始化数据模型。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void InitializeModels();
        /**
         * @fun SetCustomDelegates
         * @brief 设置自定义委托，避免选中行背景覆盖。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void SetCustomDelegates();
        /**
         * @fun HideVerticalHeaders
         * @brief 隐藏垂直表头。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void HideVerticalHeaders();
        /**
         * @fun InitializeViewParameters
         * @brief 初始化显示参数。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void InitializeViewParameters();

        /**
         * @fun UpdateView
         * @brief 根据事件参数更新界面。
         * @param param_ [const jrsdata::ViewParamBasePtr&] 事件参数。
         * @return [int] 更新结果，返回 jrscore::AOI_OK 表示成功。
         * @date 2024.08.22
         * <AUTHOR>
         */
        virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_) override;

        /**
         * @fun Save
         * @brief 保存当前视图参数。
         * @param param_ [const jrsdata::ViewParamBasePtr&] 需要保存的参数。
         * @return [int] 保存结果，返回 jrscore::AOI_OK 表示成功。
         * @date 2024.08.22
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;

        /**
         * @fun SetWindowView
         * @brief 设置焊盘视图的布局。
         * @param widget [QWidget*] 焊盘视图的控件。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void SetWindowView(QWidget* widget);

        /**
         * @fun BoardUpdateView
         * @brief 更新主板列表视图。
         * @param vector_data [std::vector<SubBoardDataStructPtr>] 主板数据列表。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void BoardUpdateView(std::vector<SubBoardDataStructPtr> vector_data);

        /**
         * @fun PartNumberUpdateView
         * @brief 更新部件编号列表视图。
         * @param vector_data [std::vector<PartNumberDataStructPtr>] 部件编号数据列表。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void PartNumberUpdateView(std::vector<PartNumberDataStructPtr> vector_data);

        /**
         * @fun DeviceUpdateView
         * @brief 更新元件列表视图。
         * @param vector_data [std::vector<DeviceDataStructPtr>] 元件数据列表。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void DeviceUpdateView(std::vector<DeviceDataStructPtr> vector_data);

        /**
         * @fun BoardSelectIndex
         * @brief 选择指定的主板索引。
         * @param row [int] 要选择的行号。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void BoardSelectIndex(int row);

        /**
         * @fun PartNumberSelectIndex
         * @brief 选择指定的部件编号索引。
         * @param row [int] 要选择的行号。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void PartNumberSelectIndex(int row);

        /**
         * @fun DeviceSelectIndex
         * @brief 选择指定的元件索引。
         * @param row [int] 要选择的行号。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void DeviceSelectIndex(int row);
        /**
         * @fun SelectTableRow 
         * @brief 选择table_view表的行
         * @param selectionModel
         * @param index
         * @param table_view
         * @date 2025.4.23
         * <AUTHOR>
         */
        void SelectTableRow(QItemSelectionModel* selectionModel, QModelIndex index, CustomTableView* table_view);
        /**
         * @fun DeviceInfoUpdateRow
         * @brief 更新选中的元件信息行。
         * @param row [int] 要更新的行号。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void DeviceInfoUpdateRow(int row);

        /**
         * @fun AddPartNumberID
         * @brief 料号加id。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void AddPartNumberID(int id);

        /**
         * @fun AddDeviceID
         * @brief 元件号加id。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void AddDeviceID(int id);



        /**
         * @fun ShowUntested
         * @brief 显示未测试的元件。
         * @param tested [bool] 是否显示未测试的元件。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void ShowUntested(bool tested);

        void ShowUnedited(bool edited);

        void ShowNg(bool ng);

        void ShowAll(bool all);
        /**
         * @fun ShowEnableDatas
         * @brief 根据显示类型和状态更新设备的显示状态。
         * @param state 设备的显示状态（true 或 false）。
         * @param type 显示类型（SHOW_TYPE 枚举值）。
         * @return 无
         * @date 2025.05.05
         * <AUTHOR>
         */
        void ShowEnableDatas(bool state, int type);
        /**
         * @fun IsDeviceVisible
         * @brief 根据显示类型和状态判断设备是否可见。
         * @param device_data 设备数据指针。
         * @param state 设备的显示状态。
         * @param type 显示类型。
         * @return 如果设备可见返回 true，否则返回 false。
         * @date 2025.05.05
         * <AUTHOR>
         */
        bool IsDeviceVisible(DeviceDataStructPtr& device_data, bool state, int type);
        /**
         * @fun ConnectSlots
         * @brief 连接信号和槽。
         * @date 2024.08.22
         * <AUTHOR>
         */
        void ConnectSlots();
        /**
         * @fun UpdateVariant
         * @brief 更新元件字段的值。
         * @param value [const QVariant&] 要更新的值。
         * @param type [int] 字段类型。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void UpdateVariant(const QVariant& value, int type);

        /**
         * @fun GetDeviceData
         * @brief 获取当前选中的元件数据。
         * @return [std::shared_ptr<DeviceDataStruct>] 当前选中的元件数据。
         * @date 2025.02.21
         * <AUTHOR>
         */
        std::shared_ptr<DeviceDataStruct> GetDeviceData() const;
        /**
         * @fun UpdateDeviceField
         * @brief 根据字段类型更新元件数据。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param type [int] 字段类型。
         * @param value [const QVariant&] 要更新的值。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateDeviceField(DeviceDataStructPtr device_data, int type, const QVariant& value) const;

        /**
         * @fun UpdateBodyShapeType
         * @brief 更新元件的主体形状类型。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要更新的值（整数类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyShapeType(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun UpdateBodyNotTested
         * @brief 更新元件的主体未测试标志。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要更新的值（布尔类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyNotTested(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun UpdateBodyWidth
         * @brief 更新元件的主体宽度。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要更新的值（浮点类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyWidth(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun UpdateBodyHeight
         * @brief 更新元件的主体高度。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要更新的值（浮点类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyHeight(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun UpdateBodyAngle
         * @brief 更新元件的主体角度。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要更新的值（浮点类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyAngle(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun UpdateBodyAngleAdd
         * @brief 增加元件的主体角度。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @param value [const QVariant&] 要增加的角度值（浮点类型）。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void UpdateBodyAngleAdd(DeviceDataStructPtr device_data, const QVariant& value) const;

        /**
         * @fun SaveDeviceData
         * @brief 将更新后的元件数据保存回模型。
         * @param device_data [DeviceDataStructPtr] 元件数据的智能指针。
         * @date 2025.02.23
         * <AUTHOR>
         */
        void SaveDeviceData(DeviceDataStructPtr device_data);

        /**
         * @fun QueryConditionFocus
         * @brief 搜索框设置焦点
         * @param value
         * @date 2024.9.19
         * <AUTHOR>
         */
        void QueryConditionFocus();
        /**
         * @fun UpdateAllTableView
         * @brief 重新刷新所有三个列表  更新所有表格视图的主函数，负责协调各个子模块的更新和选择状态的恢复
         * @date 2024.9.19
         * <AUTHOR>
         */
        void UpdateAllTableView();
        /**
         * @fun BlockSignals
         * @brief 阻塞或解除阻塞 UI 元素的信号，防止更新过程中触发不必要的事件。
         * @param block [bool] 是否阻塞信号。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void BlockSignals(bool block);
        /**
         * @fun UpdateBoardView
         * @brief 更新主板视图，根据参数决定是否需要更新数据
         * @date 2025.2.20
         * <AUTHOR>
         */
        void UpdateBoardView();
        /**
         * @fun UpdatePartNumberView
         * @brief 更新部件编号视图，根据参数决定是否需要更新数据
         * @date 2025.2.20
         * <AUTHOR>
         */
        void UpdatePartNumberView();
        /**
         * @fun UpdateDeviceView
         * @brief 更新元件视图，根据参数决定是否需要更新数据
         * @date 2025.2.20
         * <AUTHOR>
         */
        void UpdateDeviceView();
        /**
         * @fun RestoreSelections
         * @brief 恢复用户之前的选择状态，包括主板、部件编号和元件的选中项
         * @date 2025.2.20
         * <AUTHOR>
         */
        void RestoreSelections();

        /**
         * @fun UpdateSubboardDatas
         * @brief 刷新子板数据列表
         * @param params [jrsdata::GraphicsUpdateProjectEventParamPtr] 包含更新数据的参数。
         * @date 2024.9.19
         * <AUTHOR>
         */
        void UpdateSubboardDatas(jrsdata::GraphicsUpdateProjectEventParamPtr params);
        /**
         * @fun CleanDatas
         * @brief 清除元件列表数据
         * @date 2024.9.23
         * <AUTHOR>
         */
        void CleanDatas();

    private slots:
        /**
         * @fun BoardLeftClicked
         * @brief 切换板子数据记录
         * @param index
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BoardLeftClicked(const QModelIndex& index);
        /**
         * @fun PartNumberLeftClicked
         * @brief 切换板子数据记录
         * @param index
         * @date 2024.8.22
         * <AUTHOR>
         */
        void PartNumberLeftClicked(const QModelIndex& index);
        /**
         * @fun DeviceLeftClicked
         * @brief 切换板子数据记录
         * @param index
         * @date 2024.8.22
         * <AUTHOR>
         */
        void DeviceLeftClicked(const QModelIndex& index);
        /**
         * @fun QueryConditionTextChanged
         * @brief 查询输入框变化处理
         * @param text
         * @date 2024.8.22
         * <AUTHOR>
         */
        void QueryConditionTextChanged(QString text);

        /**
         * @fun QueryPreClicked
         * @brief 查询上一个按钮处理
         * @date 2024.8.22
         * <AUTHOR>
         */
        void QueryPreClicked();
        /**
         * @fun QueryPostClicked
         * @brief 查询下一个按钮处理
         * @date 2024.8.22
         * <AUTHOR>
         */
        void QueryPostClicked();
        /**
         * @fun QueryRefreshClicked
         * @brief 刷新按钮处理
         * @date 2024.8.22
         * <AUTHOR>
         */
        void QueryRefreshClicked();
        /**
         * @fun EnableAllStateChanged
         * @brief 所有Check框选中处理
         * @param state
         * @date 2024.8.22
         * <AUTHOR>
         */
        void EnableAllStateChanged(int state);
        /**
         * @fun EnableUneditedStateChanged
         * @brief 未编辑Check框选中处理
         * @param state
         * @date 2024.8.22
         * <AUTHOR>
         */
        void EnableUneditedStateChanged(int state);
        /**
         * @fun EnableNoTestedStateChanged
         * @brief 不测试Check框选中处理
         * @param state
         * @date 2024.8.22
         * <AUTHOR>
         */
        void EnableNoTestedStateChanged(int state);
        /**
         * @fun EnableNgStateChanged
         * @brief NG Check框选中处理
         * @param state
         * @date 2024.8.22
         * <AUTHOR>
         */
        void EnableNgStateChanged(int state);
        /**
         * @fun BodyCircularToggled
         * @brief 圆形Radio选中处理
         * @param checked
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyCircularToggled(bool checked);
        /**
         * @fun BodyRectangleToggled
         * @brief 矩形Radio选中处理
         * @param checked
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyRectangleToggled(bool checked);
        /**
         * @fun BodyNoTestedStateChanged
         * @brief 不测Radio选中处理
         * @param state
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyNoTestedStateChanged(int state);
        /**
         * @fun BodyWidthValueChanged
         * @brief 本体尺寸宽DoubleSpin数据变化处理
         * @param value
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyWidthValueChanged(double value);
        /**
         * @fun BodyHeightValueChanged
         * @brief 本体尺寸高DoubleSpin数据变化处理
         * @param value
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyHeightValueChanged(double value);
        /**
         * @fun BodyAngleValueChanged
         * @brief 本体角度Spin数据变化处理
         * @param value
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyAngleValueChanged(int value);
        /**
         * @fun BodyAdd90DegreesClicked
         * @brief 本体角度加90按钮点击处理
         * @date 2024.8.22
         * <AUTHOR>
         */
        void BodyAdd90DegreesClicked();
        /**
        * @fun RenderInvokeFun
        * @brief 按钮响应部分
        * <AUTHOR>
        * @date 2024.7.23
        */
        void RenderInvokeFun(const std::shared_ptr<DeviceDataStruct>& ptra_, int type_);
        /**
         * @fun ProjectUpdate
         * @brief 解析CAD数据后刷新里元件列表
         * @param param
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        int ProjectUpdate(const jrsdata::ViewParamBasePtr& param);
        /**
         * @fun GraphicsSelect
         * @brief 实现图片选中元件后,刷新元件列表
         * @param param
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        int GraphicsSelect(const jrsdata::ViewParamBasePtr& param);
        /**
         * @fun GraphicsUpdate
         * @brief 实现图片选中元件的数据更新
         * @param param
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        int GraphicsUpdate(const jrsdata::ViewParamBasePtr& param);
        /**
         * @fun GraphicsCreate
         * @brief 实现图片中创建的元件的数据更新。
         * @param param [const jrsdata::ViewParamBasePtr&] 包含创建元件所需数据的参数。
         * @return [int] 返回值为 0 表示成功，其他值表示失败。
         * @date 2024.09.19
         * <AUTHOR>
         */
        int GraphicsCreate(const jrsdata::ViewParamBasePtr& param);

        /**
         * @fun GraphicsDelete
         * @brief 实现图片中删除的元件的数据更新。
         * @param param [const jrsdata::ViewParamBasePtr&] 包含删除元件所需数据的参数。
         * @return [int] 返回值为 0 表示成功，其他值表示失败。
         * @date 2024.09.19
         * <AUTHOR>
         */
        int GraphicsDelete(const jrsdata::ViewParamBasePtr& param);

        /**
         * @fun CadSelect
         * @brief 元件选择处理。
         * @param param [const jrsdata::ViewParamBasePtr&] 包含选择元件所需数据的参数。
         * @return [int] 返回值为 0 表示成功，其他值表示失败。
         * @date 2024.09.19
         * <AUTHOR>
         */
        int CadSelect(const jrsdata::ViewParamBasePtr& param);
        /**
         * @fun BoardTableColumnResize 
         * @brief Board表格列宽变化Slot
         * @param index
         * @param old_size
         * @param new_size
         * @date 2025.4.23
         * <AUTHOR>
         */
        void BoardTableColumnResize(int index, int old_size, int new_size);
        /**
         * @fun PartNumberTableColumnResize 
         * @brief 料号表格列宽变化Slot
         * @param index
         * @param old_size
         * @param new_size
         * @date 2025.4.23
         * <AUTHOR>
         */
        void PartNumberTableColumnResize(int index, int old_size, int new_size);
        /**
         * @fun DeviceTableColumnResize 
         * @brief Device表格列宽变化Slot
         * @param index
         * @param old_size
         * @param new_size
         * @date 2025.4.23
         * <AUTHOR>
         */
        void DeviceTableColumnResize(int index, int old_size, int new_size);

    private:
        /**
         * @fun CleanDeviceInfo
         * @brief 清除数据结构。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void CleanDeviceInfo();
        /**
         * @fun SaveLayoutJson 
         * @brief 保存Json格式的TableView的列宽
         * @param table_view
         * @param table_model
         * @param file_name
         * @date 2025.4.23
         * <AUTHOR>
         */
        void SaveLayoutJson(CustomTableView* table_view, TableViewModel* table_model, std::string file_name);
        /**
         * @fun LoadLayoutJson 
         * @brief 加载Json格式的TableView的列宽
         * @param table_view
         * @param table_model
         * @param file_name
         * @date 2025.4.23
         * <AUTHOR>
         */
        void LoadLayoutJson(CustomTableView* table_view, TableViewModel* table_model, std::string file_name);
        /**
         * @fun IsValidInput
         * @brief 检查输入参数是否有效。
         * @param table_view 表格视图指针。
         * @param table_model 表格模型指针。
         * @param file_name JSON 文件名。
         * @return 如果输入有效返回 true，否则返回 false。
         * @date 2025.05.05
         * <AUTHOR>
         */
        bool IsValidInput(CustomTableView* table_view, TableViewModel* table_model, const std::string& file_name) const;
        /**
         * @fun IsValidJsonData
         * @brief 检查 JSON 数据是否有效。
         * @param json_data JSON 数据。
         * @return 如果 JSON 数据有效返回 true，否则返回 false。
         * @date 2025.05.05
         * <AUTHOR>
         */
        bool IsValidJsonData(const nlohmann::json& json_data) const;
        /**
         * @fun ParseColumnWidthsFromJson
         * @brief 从 JSON 数据中解析列宽。
         * @param json_data JSON 数据。
         * @return 解析后的列宽列表。
         * @date 2025.05.05
         * <AUTHOR>
         */
        std::vector<int> ParseColumnWidthsFromJson(const nlohmann::json& json_data) const;
        /**
         * @fun SetColumnWidth 
         * @brief 设置TableView的列宽
         * @param table_view
         * @param table_model
         * @param column_widths
         * @date 2025.4.23
         * <AUTHOR>
         */
        void SetColumnWidth(CustomTableView* table_view, TableViewModel* table_model, std::vector<int> column_widths);
        /**
         * @fun GetColumnWidth 
         * @brief 获取TableView的列宽
         * @param table_view
         * @param table_model
         * @param column_widths
         * @date 2025.4.23
         * <AUTHOR>
         */
        void GetColumnWidth(CustomTableView* table_view, TableViewModel* table_model, std::vector<int>& column_widths);
        /**
         * @fun EnsureDirectoryExists 
         * @brief 判断文件是否存在，不存在创建
         * @param path
         * @return 
         * @date 2025.4.23
         * <AUTHOR>
         */
        bool EnsureDirectoryExists(std::string& path);
        /**
         * @fun ResizeBoardColumn
         * @brief 修改Board列宽度变化的属性
         * @date 2025.4.23
         * <AUTHOR>
         */
        void ResizeBoardColumn();
        /**
         * @fun ResizePartNumberColumn
         * @brief 修改料号列宽度变化的属性
         * @date 2025.4.23
         * <AUTHOR>
         */
        void ResizePartNumberColumn();
        /**
         * @fun ResizeDeviceColumn
         * @brief 修改Device列宽度变化的属性
         * @date 2025.4.23
         * <AUTHOR>
         */
        void ResizeDeviceColumn();

    signals:
        /**
         * @fun ShowListRenderTrigger
         * @brief 发送 CAD 数据变化到显示界面。
         * @param param [const jrsdata::ViewParamBasePtr&] 包含 CAD 数据变化的参数。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void ShowListRenderTrigger(const jrsdata::ViewParamBasePtr& param);

        /**
         * @fun FindTrigger
         * @brief 发送筛选元件信号。
         * @param param [jrsdata::QueryListViewParam] 包含筛选条件的参数。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void FindTrigger(const jrsdata::QueryListViewParam param);

        /**
         * @fun ChangeShowListViewParam
         * @brief 发送显示列表视图参数变化信号。
         * @param param [ShowListViewParam] 包含显示列表视图参数变化的参数。
         * @date 2024.09.19
         * <AUTHOR>
         */
        void ChangeShowListViewParam(ShowListViewParam param);
    private:
        /// UI 界面对象，用于管理界面元素。
        Ui::ShowListView* ui;

        /// 是否需要发送渲染信号的标志。
        /// 当界面数据更新时，设置为 true，触发渲染更新。
        bool m_send_signal_to_render;

        /// 显示列表的参数结构，用于存储当前界面的状态。
        ShowListViewParam m_show_list_params;

        /// 整板列表界面的数据模型，用于展示整板信息。
        TableViewModel* show_board_model;

        /// 料号列表界面的数据模型，用于展示料号信息。
        TableViewModel* show_part_number_model;

        /// 元件列表界面的数据模型，用于展示元件信息。
        TableViewModel* show_device_model;

        /// 存储整板数据的向量，包含所有整板的智能指针。
        std::vector<SubBoardDataStructPtr> m_sub_board_datas;

        QWidget* _edit_widget;/**< 检测框、元件、pad 编辑框*/
        /// Board保存的Json文件名
        std::string board_file_name = "board_layout_setting.json";
        /// 料号保存的Json文件名
        std::string part_no_file_name = "part_no_layout_setting.json";
        /// Device保存的Json文件名
        std::string device_file_name = "device_layout_setting.json";
        /// 保存的文件类型
        jrsdata::FileType setting_file_type = jrsdata::FileType::JSON;
        /// 保存Json文件的路径
        std::string file_path = jtools::FileOperation::GetCurrentWorkingDirectory()
            + "/config/paramsetting/showlistview/";
    };
}
#endif // !__SHOWLISTVIEW_H__
