﻿/*****************************************************************
* @file   logicmanager.h
* @brief  主要用于实现逻辑层与外部交互功能
* @details
* <AUTHOR>
* @date 2024.8.5
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
* <tr><td>2024.8.5         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/
#ifndef __JRSLOGICMANAGER_H__
#define __JRSLOGICMANAGER_H__

// STD
#include <iostream>
#include <functional>
#include <sstream>
#include <future>
#include <map>

// Custom
#include "pluginexport.hpp"
#include "fovplan.h"
#include "pcbpathplan.h"



#include "viewparam.hpp"

// Third
#include "nlohmann/json.hpp"

using JSON = nlohmann::json;
namespace jrsdevice
{
    class DeviceManager;
}

namespace jrscore
{
    class CoordinateTransform;

}
namespace jrsalgo
{
    class AlgorithmEngineManager;
}
namespace jrslogic
{
    class CaptureImage;
    class ScanProcess;
    struct LogicDataImpl;
    class InvokeAlgoEngine;
    using Function = std::function<void(const jrsdata::ViewParamBasePtr& param_)>;

    using DeviceMsgCallBack = std::function<int(const jrsdata::DeviceParamPtr& msg)>;

    class JRS_AOI_PLUGIN_API LogicManager
    {
    public:
        LogicManager();
        ~LogicManager();
        LogicManager(const LogicManager&) = delete;
        LogicManager& operator=(const LogicManager&) = delete;
        LogicManager(LogicManager&&) = delete;
        LogicManager& operator=(LogicManager&&) = delete;

        /**
         * @fun HandleMotion
         * @brief 界面端运控到逻辑层的响应
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.8.13
         */
        int HandleMotion(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun StructLightInvoke
         * @brief 界面结构光操作逻辑层响应函数
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.8.13
         */
        int StructLightInvoke(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun EventHandler
         * @brief 界面事件处理
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.8.15
         */
        int EventHandler(const jrsdata::ViewParamBasePtr& param_);


        /**
         * @fun SetInitDeviceCallBack 
         * @brief 初始化设备消息回调,主要用于设备参数传出
         * @param callback_  回调函数
         * <AUTHOR>
         * @date 2025.3.6
         */
        void SetInitDeviceCallBack(DeviceMsgCallBack callback_);
        /**
         * @fun SetProjectCallback
         * @brief 工程消息回调
         * @param callbacke_ 回调函数
         * <AUTHOR>
         * @date 2024.8.15
         */
        void SetProjectCallback(jrsdata::InvokeProjectFun callback_);

        /**
         * @fun SetMotionConfigCallBack
         * @brief 运控配置文件信息回调
         * @param callback 回调函数
         * <AUTHOR>
         * @date 2024.8.26
         */
        void OperateViewCallBack(jrsdata::InvokeOperateViewParamFun callback);

        /**
         * @fun DetectResultUpdateCallBack
         * @brief  检测结果更新回调
         * @param callback_
         * <AUTHOR>
         * @date 2025.1.22
         */
        void DetectResultUpdateCallBack(jrsdata::InvokeAlgoEventParamFun callback_);

        /**
         * @fun SetOnlineDebugInfoCallBack 
         * @brief 在线调试信息回调
         * @param callback_ [IN]回调函数
         * <AUTHOR>
         * @date 2025.4.14
         */
        void SetOnlineDebugInfoCallBack(const jrsdata::InvokeOnlineDebugViewParamFun& callback_);
        /**
         * @fun SetSettingParamCallBack
         * @brief 设置参数回调信息
         * @param callbacke_ 回调函数
         * <AUTHOR>
         * @date 2024.8.25
         */
        void SetSettingParamCallBack(jrsdata::InvokeSettingViewParamFun callback_);
        /**
         * @fun SetAutoRunPanelParamCallBack
         * @brief 设置自动自动运行参数回调信息
         * @param callback_ 会掉函数
         * <AUTHOR>
         * @date 2024.11.21
         */
        void SetAutoRunPanelParamCallBack(jrsdata::InvokeControlPanelViewParamFun callback_);

        /**
         * @fun SetRenderCallback
         * @brief
         * @param img_buffer_callback
         * <AUTHOR>
         * @date 2024.10.25
         */
        void SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback);

        void SetSystemStateParamCallback(jrsdata::InvokeSystemStateParamFun callback_);

        /**
         * @fun GetAlgoEngineManager
         * @brief 获取算法引擎管理指针
         * @return  算法引擎指针
         * <AUTHOR>
         * @date 2024.10.25
         */
        const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& GetAlgoEngineManager();
    private:
        /**
         * @fun Init
         * @brief 初始化
         * <AUTHOR>
         * @date 2024.8.17
         */
        void Init();

        /**
         * @fun InitMember
         * @brief 初始化成员变量
         * <AUTHOR>
         * @date 2024.8.17
         */
        void InitMember();

        /**
         * @fun IsValidParam
         * @brief 验证参数是否有效
         * @param param_ [IN] UI层参数
         * @return  返回检测结果，True：有效 false:无效
         * <AUTHOR>
         * @date 2024.10.25
         */
        bool IsValidParam(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun InitMotionCorrect
         * @brief 初始化运动误差,即tablemapping
         * <AUTHOR>
         * @date 2024.10.25
         */
        void InitMotionCorrect();

        /**
         * @fun InitAoiImgsManager
         * @brief 初始化图片管理类
         * <AUTHOR>
         * @date 2024.10.25
         */
        void InitAoiImgsManager();

        /**
         * @fun InitInvokeFun
         * @brief 初始化调用函数
         * <AUTHOR>
         * @date 2024.10.25
         */
        void InitInvokeFun();
        /**
        * @fun InitCallBack
        * @brief 回调初始化
        * @return
        * <AUTHOR>
        * @date 2024.9.5
        */
        int InitCallBack();
        /**
         * @fun InvokeFun
         * @brief 根据event_name执行回调
         * @param param_ 事件参数
         * @return  有效：true，无效：false
         *
         * @note 改了名字,不然总觉得是一个条件判断函数
         * <AUTHOR>
         * @date 2024.8.17
         */
        bool InvokeFun(const jrsdata::ViewParamBasePtr& param_);

        /**
        * @fun InitDeviceParam
        * @brief 初始设备参数
        * @param device_param_ 设备参数
        * @return  AOI_OK 成功，其他失败
        * <AUTHOR>
        * @date 2024.9.3
        */
        int InitDeviceParamCallback(const jrsdata::DeviceParamPtr& device_param_);

        /**
         * @fun LogicEventHandler
         * @brief 逻辑层事件处理
         * @param param_ [IN] UI层参数
         * @return  成功返回AOI_OK，否则失败
         * <AUTHOR>
         * @date 2024.10.25
         */
        int LogicEventHandler(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun ScaneBoardImage
         * @brief 扫正板图片
         * @param param_ [IN] 界面层参数
         * @return  AOI_OK成功，否则失败
         * <AUTHOR>
         * @date 2024.10.25
         */
        int ScaneBoardImage(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun StartScanProcess
         * @brief 开始扫图
         * @param [IN] 是否是自动流程
         * @return 成功返回AOI_OK，其他失败
         * <AUTHOR>
         * @date 2024.10.25
         */
        int StartScanProcess(bool is_auto_run = false);

        /**
         * @fun GenerateFovPiexelCoor
         * @brief 生成 FOV的像素坐标
         * @param project_param_ [IN]  工程参数
         * @return  生成的 FOV像素坐标值
         * <AUTHOR>
         * @date 2024.10.25
         */
        std::shared_ptr<PCBPathPlanning::OutputParamsBase> GenerateFovPiexelCoor(const jrsdata::ProjectParamPtr& project_param_);

        /**
         * @fun GenerateFovPhysicalCoor
         * @brief
         * @param fovs_pixel_coor_
         * @return
         * <AUTHOR>
         * @date 2024.10.25
         */
        int GenerateFovPhysicalCoor(const std::vector<PCBPathPlanning::Fov>& fovs_pixel_coor_);

        /**
         * @fun GrabImageContinue
         * @brief 用于控制单帧采图
         * @param param_ [IN]工程参数
         * @return  AOI_OK成功，否则失败
         * <AUTHOR>
         * @date 2024.10.25
         */
        int GrabImageContinue(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun LogicParamUpdateCallBack
         * @brief 逻辑层参数更新，从data层进行数据更新
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.9.5
         */
        int LogicParamUpdateCallBack(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun WorkFlowCallback
         * @brief 工程运行的callback
         * @param fov_path [IN]规划的 FOV路径
         * @param is_auto_run[IN] 是否自动流程
         * <AUTHOR>
         * @date 2024.11.4
         */
        void WorkFlowCallback(const std::vector<PCBPathPlanning::Fov>& fov_path, bool is_auto_run);
        /**
         * @fun UpdateCoordinateTransform
         * @brief 更新坐标系
         * @param param_
         * <AUTHOR>
         * @date 2024.12.5
         */
        int UpdateCoordinateTransform(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun InitCoordinateTransform
         * @brief  初始化全局坐标转换功能类类
         * @note 通过工程中左上右下的点和相机分辨率构造物理转像素，像素转物理矩阵
         * @param project_param_ 工程参数
         * <AUTHOR>
         * @date 2024.12.1
         */
        void InitCoordinateTransform(const jrsdata::ProjectParamPtr& project_param_);


        void ReleaseStructLight();



    private:
        LogicDataImpl* logic_data_impl; ///< 所有参数放在这个结构体里,隐藏实现细节,减少工程编译耗时 jerx

    };
    using LogicManagerPtr = std::shared_ptr<LogicManager>;
}

#endif // !__JRSLOGICMANAGER_H__
