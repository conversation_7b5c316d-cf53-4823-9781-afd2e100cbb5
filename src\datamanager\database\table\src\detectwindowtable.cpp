﻿#include "detectwindowtable.h"
#include "mysqlimp.hpp"
#include "idatabase.h"
//std
#include <memory>

jrsdatabase::DetectWindowTable::DetectWindowTable(const std::string& table_name_)
    :TableBase(table_name_)
{
}

jrsdatabase::DetectWindowTable::~DetectWindowTable()
{
}

int jrsdatabase::DetectWindowTable::Create(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    //HJC TEST:  创建外键  并且修改database time 外键
    auto res = conn_ptr_->CreateTable<jrstable::TDetectWindow>();
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]创建", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }

    res = InitFields(conn_ptr_);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]初始化", _table_name, " 表字段失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }

    res = conn_ptr_->AlterTablePrimaryKey<jrstable::TDetectWindow>();
    if (res != jrscore::AOI_OK)
    {
        Log_INFO("[", __FUNCTION__, "]创建", _table_name, " 主键失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }

    /*创建 index*/
    InitIndex(conn_ptr_);

    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::Drop(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    auto temp_ptr = std::static_pointer_cast<DB_Mysql>(conn_ptr_);

    std::string sql_temp = "TRUNCATE " + _table_name;

    auto res = conn_ptr_->ExecuteSQL(sql_temp);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]删除 ", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::Show(jrstable::TableParamBasePtr& db_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    (void)db_;
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    return 0;
}

int jrsdatabase::DetectWindowTable::Insert(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectWindow>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->AddData<jrstable::TDetectWindow>(*temp_ptr);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::Insert(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TDetectWindow>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    auto res = conn_ptr_->AddData<jrstable::TDetectWindow>(boards);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::Update(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectWindow>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->UpdateData<jrstable::TDetectWindow>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectWindowTable::Update(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TDetectWindow>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->UpdateData(boards, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更新数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::Replace(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectWindow>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->Replace<jrstable::TDetectWindow>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectWindowTable::Replace(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto datas = BasePtrVecToObjectVector<jrstable::TDetectWindow>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->Replace(datas, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更换数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::InitIndex([[maybe_unused]] const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    _index_container.push_back("index_board_id");
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectWindowTable::InitFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }
    InitVarcharFields(conn_ptr_);
    return InitDataFields(conn_ptr_);
}
int jrsdatabase::DetectWindowTable::InitDataFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    jrsdatabase::MySQLAlterTableFieldType type;
    type.field_type_name = jrsdatabase::MySQLDataType::DATETIME;
    const std::vector<std::string> fields_to_alter = {
        "window_rejudgement_time",
    };
    for (const auto& field : fields_to_alter) {
        auto res = conn_ptr_->AlterTableFieldType(this->_table_name, field, type);
        if (res) return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectWindowTable::InitVarcharFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    jrsdatabase::MySQLAlterTableFieldType type;
    const std::map<std::string, int> fields_to_alter = {
        {"detect_window_name", 600},
//{"window_result_data",2500},
        {"color_param",1000},
        {"light_param",1000}
    };
    for (const auto& field_length : fields_to_alter) {
        type.field_type_name = jrsdatabase::MySQLDataType::VARCHAR;
        type.length = field_length.second;
        auto res = conn_ptr_->AlterTableFieldType(this->_table_name, field_length.first, type);
        if (res) return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectWindowTable::Select(const jrsselect::SelectorParamBasePtr& selector_ptr_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }

    if (selector_ptr_->select_name == jrsselect::T_DETECT_WINDOW_SELECT_BY_WHERE_CONDITION)
    {
        auto select_ptr = std::dynamic_pointer_cast<jrsselect::SelectTable>(selector_ptr_);
        jrsdatabase::jrstable::TDetectWindowVector detect_windows;
        auto res = conn_ptr_->QueryTable<jrsdatabase::jrstable::TDetectWindow>(detect_windows, selector_ptr_->where_condition);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("查询", select_ptr->select_name, "数据失败: ", conn_ptr_->GetLastError());
            return res;
        }
        select_ptr->detect_windows = detect_windows;
    }
    return jrscore::AOI_OK;
}