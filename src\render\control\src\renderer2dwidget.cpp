﻿#include "renderer2dwidget.h"
#include "ui_renderer2dwidget.h"

#include "log.h"
#include "viewergraphicsviewimpl.h"
#include <QMouseEvent>
Renderer2DWidget::Renderer2DWidget(QWidget* parent)
    : QWidget(parent), ui(new Ui::Renderer2DWidgetClass())
    , renderer(nullptr), ruler_horizontal(nullptr), ruler_vertical(nullptr)
    , _image_render(nullptr)
{
    ui->setupUi(this);
    setMouseTracking(true);
    connect(ui->pb_change_resolution, &QPushButton::clicked, this, &Renderer2DWidget::SignalChangeUseState);
    //installEventFilter(this);
    //setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
   // setAttribute(Qt::WA_TranslucentBackground); // 半透明背景
    //this->raise();
}

Renderer2DWidget::~Renderer2DWidget()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void Renderer2DWidget::AddImageRender(QWidget* widget_)
{
    widget_->setObjectName("image_render");
    widget_->installEventFilter(this);
    ui->layout_renderer->addWidget(widget_);
    _image_render = widget_;

}

void Renderer2DWidget::AddRenderer(QWidget* widget)
{
    widget->setObjectName("renderer");
    widget->installEventFilter(this);
    //ui->layout_renderer->addWidget(widget);
    renderer = widget;
}

void Renderer2DWidget::AddRulerHorizontal(QWidget* widget)
{
    widget->setObjectName("ruler_horizontal");
    widget->installEventFilter(this);
    ui->layout_horizontalRuler->addWidget(widget);
    ruler_horizontal = widget;
}

void Renderer2DWidget::AddRulerVertical(QWidget* widget)
{
    widget->setObjectName("ruler_vertical");
    widget->installEventFilter(this);
    ui->layout_verticalRuler->addWidget(widget);
    ruler_vertical = widget;
}
#include <QDebug>
bool Renderer2DWidget::eventFilter(QObject* target, QEvent* event)
{
    (void)target;
    //if (renderer == target && event->type() != QEvent::UpdateRequest && event->type() != QEvent::Paint)
    //{

    //    qDebug() << "Event target:" << target << " event type:" << event->type() <<
    //        " Event accepted before filter:" << event->isAccepted();
    //}

    if (event->type() == QEvent::MouseMove && target == renderer)
    {
        // printInfo("");
        for (auto& obj : { ruler_horizontal, ruler_vertical })
        {
            auto new_event = std::make_unique<QMouseEvent>(*dynamic_cast<QMouseEvent*>(event));
            QCoreApplication::postEvent(obj, new_event.release()); // 避免内存泄漏
        }
    }
    if (event->type() == QEvent::Resize && _image_render == target)
    {
        auto new_event = std::make_unique<QResizeEvent>(*dynamic_cast<QResizeEvent*>(event));
        renderer->resize(new_event->size());
        new_event.release();
        //SigUpdateZoom();  //测试使用
    }
    return false;
}

void Renderer2DWidget::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    renderer->show();
}


void Renderer2DWidget::moveEvent(QMoveEvent* event)
{
    auto new_pos = _image_render->mapToGlobal({ 0,0 });
    auto old_pos = _image_render->mapToGlobal(event->oldPos());
    auto new_event = std::make_unique<QMoveEvent>(new_pos, old_pos);

    QCoreApplication::postEvent(renderer, new_event.release()); // 避免内存泄漏
}
