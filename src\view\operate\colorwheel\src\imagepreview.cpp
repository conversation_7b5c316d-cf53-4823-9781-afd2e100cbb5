#include "imagepreview.h"
#include <QLabel>
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "qimagetranform.h"
#include <QVBoxLayout>
#include <QLineEdit>
#include <QPushButton>
#include <QPainter>
#include <QEvent>
#include <QMouseEvent>
#include "imagepreprocessalgo.h"
#include <QtWidgets/qmessagebox.h>
#include <opencv2/opencv.hpp>
#pragma warning(pop)
PreviewWindow::PreviewWindow(QWidget* /*parent*/)
{
    setWindowTitle("预览图");
	this->setMaximumSize(1200, 700);
    grid_layout = new QGridLayout(this);
    //confire = new QPushButton();
    //confire->setText("确认");
    /*connect(confire, &QPushButton::clicked,
                this, &PreviewWindow::UpdateCurrentIdSlot);*/
}

void PreviewWindow::GetCurrentImageIdAndShow(int id)
{
    id_ = id;
	QLabel* new_label = new QLabel();
	QImage qImage = imagetrans::MatToQImage(process_image_group_[id_]);
    QPixmap pixmap = QPixmap::fromImage(qImage);
    new_label->setPixmap(pixmap);
    new_label->setWindowFlags(Qt::Window); 
    new_label->setAttribute(Qt::WA_DeleteOnClose); 
    new_label->show();
    UpdateLabelState(id_);
}
void PreviewWindow::SetProcessImages(const vector<Mat>&
    process_image_group)
{
    process_image_group_ = process_image_group;
	SetupUi();
}


PreviewWindow::~PreviewWindow() {}

void PreviewWindow::closeEvent(QCloseEvent* event)
{   
    emit UpdateCurrentId(id_);  
    QWidget::closeEvent(event);      
}

void PreviewWindow::GetCurrentId(int id)
{
    id_ = id;
}

void PreviewWindow::UpdateCurrentIdSlot()
{
    emit UpdateCurrentId(id_);
	this->close();
}


void PreviewWindow::UpdateLabelState(int id)
{
    for (auto& button_id : button_group)
    {
        if (id == button_id.first)
        {
            button_id.second->UpdateLabelColor(true);
        }
        else
        {
			button_id.second->UpdateLabelColor(false);
        }
    }
}

QLineEdit* CreateReadOnlyLineEdit(const QString& text, 
    int max_width = 50, QWidget* parent = nullptr) 
{
    QLineEdit* line_edit = new QLineEdit(parent);
    line_edit->setReadOnly(true);
    line_edit->setText(text);
    line_edit->setMaximumHeight(20);
    line_edit->setMaximumWidth(max_width);
    line_edit->setStyleSheet("QLineEdit { background: transparent; border: none; }");
    line_edit->setAlignment(Qt::AlignHCenter);

    return line_edit;
}


void PreviewWindow::SetupUi()
{
    if (process_image_group_.size() != IMAGETYPE * IMAGECHANNEL)
    {
        QMessageBox::critical(this, "错误", "图像数据量异常！");
        return ;
    }
    image_height_ = process_image_group_.front().rows;
    image_width_ = process_image_group_.front().cols;
    int id = 0;
    for (int row = 1; row < IMAGETYPE + 1; ++row) 
    {
        for (int col = 1; col < IMAGECHANNEL + 1; ++col) 
        {
            bool is_green = id_ == id ? true : false;
            CustomQLabel* image_label = new CustomQLabel(id, is_green);
            button_group[id] = image_label;
            image_label->setFixedSize(label_size, label_size); 
            imagetrans::SetImageToLabel(image_label, process_image_group_[id]);
            grid_layout->addWidget(image_label, row, col);
            connect(image_label, &CustomQLabel::UpdateCurrenImage,
                this, &PreviewWindow::GetCurrentImageIdAndShow);
			connect(image_label, &CustomQLabel::UpdateCurrenImageId,
                this, &PreviewWindow::GetCurrentId);
	        connect(image_label, &CustomQLabel::UpdateCurrenImageId,
                this, &PreviewWindow::UpdateLabelState);
            id++;
        }
    }
	
    // 创建 QLineEdit 控件用于显示原图类型和当前类型
   /* QLineEdit* true_image_title = CreateReadOnlyLineEdit(tr("真彩图"), image_height_, this);
    QLineEdit* rgb_image_title = CreateReadOnlyLineEdit(tr("彩色图"), image_height_, this);
    QLineEdit* low_image_title = CreateReadOnlyLineEdit(tr("低角度光"), image_height_, this);
    QLineEdit* height_image_title = CreateReadOnlyLineEdit(tr("高角度光"), image_height_, this);*/
	QLineEdit* ori_image_title = CreateReadOnlyLineEdit(tr("原图"), image_width_, this);
    QLineEdit* r_image_title = CreateReadOnlyLineEdit(tr("R"), image_width_, this);
    QLineEdit* g_image_title = CreateReadOnlyLineEdit(tr("G"), image_width_, this);
    QLineEdit* b_image_title = CreateReadOnlyLineEdit(tr("B"), image_width_, this);
    QLineEdit* h_image_title = CreateReadOnlyLineEdit(tr("H"), image_width_, this);
    QLineEdit* s_image_title = CreateReadOnlyLineEdit(tr("S"), image_width_, this);
    QLineEdit* v_image_title = CreateReadOnlyLineEdit(tr("V"), image_width_, this);
    
    // 将 QLineEdit 添加到布局的第一列
	grid_layout->addWidget(confire, 1, 0);
    //grid_layout->addWidget(true_image_title, 1, 0);
    //grid_layout->addWidget(rgb_image_title, 2, 0);
    //grid_layout->addWidget(low_image_title, 3, 0);
    //grid_layout->addWidget(height_image_title, 4, 0);
    grid_layout->addWidget(ori_image_title, 0, 1);
    grid_layout->addWidget(r_image_title, 0, 2);
    grid_layout->addWidget(g_image_title, 0, 3);
    grid_layout->addWidget(b_image_title, 0, 4);
    grid_layout->addWidget(h_image_title, 0, 5);
    grid_layout->addWidget(s_image_title, 0, 6);
	grid_layout->addWidget(v_image_title, 0, 7);
    setLayout(grid_layout);
}