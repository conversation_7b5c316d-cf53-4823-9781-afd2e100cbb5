﻿/*****************************************************************//**
 * @file   viewergraphicsviewimpl.h
 * @brief  基于QGraphicsView实现的图片显示器
 * @details
 * <AUTHOR>
 * @date 2024.4.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.4.13         <td>V1.0              <td>YYZhang      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSVIEWERGRAPHICSVIEWIMPL_H__
#define __JRSVIEWERGRAPHICSVIEWIMPL_H__

 //QT 
#include <QImage> 
#include <QGraphicsView> 
//std
#include <iostream>

#include "viewparam.hpp"

class QGraphicsScene;
class QGraphicsPixmapItem;
Q_DECLARE_METATYPE(cv::Mat)

class ViewerGraphicsViewImpl : public QGraphicsView
{
    Q_OBJECT
public:
    explicit ViewerGraphicsViewImpl(QWidget* parent = nullptr);
    ~ViewerGraphicsViewImpl();
    /**
   * @brief  将图像贴到界面上. CPU渲染
   *
   * @fun    CreateImage
   * @param  image
   * @param  x 水平方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
   * @param  y 垂直方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
   * @param  z 堆叠高度 z越大处于越上层 只允许非负数
   * @param  angle 旋转角度 单位：deg(°)
   * @param  is_draw_center 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
   *
   * @date   2025-2-9
   * <AUTHOR>
   */
    int CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center);
    /**
     * @fun AddGraphicsShape
     * @brief 添加图形
     * @param graphics_shape_
     * @return  0：添加成功，-1：添加失败
     * <AUTHOR>
     * @date 2025.3.20
     */
    int AddGraphicsShapes(const jrsdata::GraphicsViewShape& graphics_shape_);
    /**
     * @fun ClearGraphicsShapes
     * @brief
     * @return
     * <AUTHOR>
     * @date 2025.3.20
     */
    int ClearGraphicsShapes();
    /**
     * @fun ClearImage
     * @brief  清除所有显示图片
     * @return
     * <AUTHOR>
     * @date 2025.2.9
     */
    Q_INVOKABLE int ClearImage(int key_);
    /**
     * @fun ShowImageByKey
     * @brief
     * @param key_ 显示图像
     * @return
     * <AUTHOR>
     * @date 2025.2.7
     */
    Q_INVOKABLE int ShowImageByKey(int key_);

protected:
    void resizeEvent(QResizeEvent* event);
public slots:

    /**
      * @fun SlotMoveCamera
      * @brief 移动相机位置，相机位置以GL传入为准
      * @date 2025.2.17
      * <AUTHOR>
      */
    void SlotMoveCamera(const double& x_, const double& y_);
    /**
     * @fun SlotSetZoom
     * @brief 设置缩放比例，缩放比例以 GLWidget 为主
     * @param scale_
     * <AUTHOR>
     * @date 2025.2.7
     */
    void SlotSetZoom(const float& scale_);

    // 恢复到原始比例
    int ResetToOriginalScale();
private:
    enum class ItemType :int
    {
        Temperary = 0,
        Entirety = 1
    };



    /**
     * @fun CvMatToQimage
     * @brief 将cv::mat格式图片转换成
     * @param src
     * @return
     * @date 2024.4.13
     * <AUTHOR>
     */
    QImage CvMatToQimage(const cv::Mat& src);

    /**
     * @fun QImageCharToQimage
     * @brief 将char格式数据转换成QImage图片
     * @param src_
     * @return 返回转换后的结果
     * @date 2024.4.29
     * <AUTHOR>
     */
    QImage CharToQimage(std::vector<char>& src_);

    /**
     * @fun CreateCheckerboardBackground
     * @brief 创建棋盘格
     * @param size 棋盘格的大小
     * @return 返回的结果
     * @date 2024.5.14
     * <AUTHOR>
     */
    QPixmap CreateCheckerboardBackground(int size = 10);

    void ConvertDepthToGray(cv::Mat& gray_image_, const cv::Mat& depth_image_);
    /**
     * @fun PrintCenterPoint
     * @brief 打印场景坐标
     * <AUTHOR>
     * @date 2025.2.7
     */
    void PrintCenterPoint();
protected:
    void wheelEvent(QWheelEvent* event) override;  // 重写滚轮事件
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

private:
    bool _is_show_entirety_img;
    std::shared_ptr < QGraphicsPixmapItem> _temperary_item;
    QGraphicsScene* scene;
    std::unordered_map<int, std::shared_ptr<QGraphicsPixmapItem>> _key_and_images;

    std::vector< std::shared_ptr<QGraphicsItem>> _graphics_shapes; /**< 图形管理*/

    std::mutex _image_mutex;

    QPoint _last_viewport_pos;  // 使用视图坐标而不是场景坐标
    bool _is_dragging;       // 标记是否正在拖动
};

#endif // !__JRSVIEWERGRAPHICSVIEWIMPL_H__
