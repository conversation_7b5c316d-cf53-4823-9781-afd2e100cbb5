﻿/*********************************************************************
 * @brief  控制层常量.
 *
 * @file   controlconstants.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef CONTROL_CONSTANTS_H
#define CONTROL_CONSTANTS_H
#include <string>
 /**
  * @brief 鼠标模式
  */
enum class VisionMode : int
{
    NONE = -1,    ///< 无
    HOVER = 0,    ///< 悬停
    MOVE_CAMERA,  ///< 移动相机
    VIEW_ALL,     ///< 渲染全景

    MOVE_ALL_GRAPHICS,             ///< 移动全部图形
    SELECT_GRAPHICS,               ///< 选取图形
    SELECT_GRAPHICS_BATCH,         ///< 批量选取图形
    SELECT_GRAPHICS_BATCH_POLYGON, ///< 多边形批量选取图形
    RESPONSE_GRAPHICS,             ///< 响应图形 - 是否可编辑 
    EDIT_GRAPHICS,                 ///< 编辑图形
    CREATE_GRAPHICS,               ///< 添加图形 - render 内部添加图形
    MANUAL_CREATE_GRAPHICS,        ///< 手动添加图形
};

inline std::string VisionModeToString(VisionMode mode)
{
    switch (mode)
    {
    case VisionMode::NONE: return "NONE";
    case VisionMode::HOVER: return "HOVER";
    case VisionMode::MOVE_CAMERA: return "MOVE_CAMERA";
    case VisionMode::VIEW_ALL: return "VIEW_ALL";
    case VisionMode::MOVE_ALL_GRAPHICS: return "MOVE_ALL_GRAPHICS";
    case VisionMode::SELECT_GRAPHICS: return "SELECT_GRAPHICS";
    case VisionMode::SELECT_GRAPHICS_BATCH: return "SELECT_GRAPHICS_BATCH";
    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON: return "SELECT_GRAPHICS_BATCH_POLYGON";
    case VisionMode::RESPONSE_GRAPHICS: return "RESPONSE_GRAPHICS";
    case VisionMode::EDIT_GRAPHICS: return "EDIT_GRAPHICS";
    case VisionMode::CREATE_GRAPHICS: return "CREATE_GRAPHICS";
    case VisionMode::MANUAL_CREATE_GRAPHICS: return "MANUAL_CREATE_GRAPHICS";
    default: return "UNKNOWN";
    }
}
/**
 * @brief 添加图形模式
 */
enum class CreateGraphicsMode : int
{
    NONE = -1, ///< 无
    RECT,      ///< 矩形
    CIRCLE,    ///< 圆形
    POLYGON,   ///< 多边形
    BEZIER,    ///< 贝塞尔
    SG,        ///< 金线
    MULTI_REGION, ///< 多框组
    PAD,       ///< 锡
    // SELECT_GRAPHICS_BATCH_POLYGON ///< 批量选择图形用多边形
};

/**
 * @brief 滚轮缩放模式
 */
enum class WheelMode : int
{
    VIEW_CENTER,  ///< 视野中心
    CURSOR_CENTER, ///< 鼠标中心
};
/**
 * @brief 缩放模式
 */
enum class CameraScaleMode : int
{
    DEFAULT_SCALE, ///< 重置到默认缩放比例
    AUTO_SCALE,    ///< 自动缩放到等比例填充视野
    TRUE_SCALE,    ///< 缩放到图像实际大小
    MAX_SCALE,     ///< 最大缩放比例
    MIN_SCALE,     ///< 最小缩放比例
};

/**
 * @brief 相机移动方向.
 */
enum class CameraDirection : int
{
    None = 0,
    Up = 1,    ///< 相机向上
    Down = 2,  ///< 相机向下
    Left = 3,  ///< 相机向左
    Right = 4, ///< 相机向右
    Front = 5, ///< 相机向前-> 缩小视野 放大图片
    Rear = 6   ///< 相机向后-> 放大视野 缩小图片
};

/**
 * @brief 相机重置位置.
 */
enum class CameraResetMode : int
{
    AlignCenter,                    ///< 重置相机位置，使相机中心与图像中心对齐
    AlignTopLeft,                   ///< 重置相机位置，使相机中心移动到图像左上角
    TopLeftToViewTopLeft,           ///< 移动相机位置，使图像左上角对准相机视野的左上角
    TopRightToViewTopRight,         ///< 移动相机位置，使图像右上角对准相机视野的右上角
    BottomRightToViewBottomRight,   ///< 移动相机位置，使图像右下角对准相机视野的右下角
    BottomLeftToViewBottomLeft,     ///< 移动相机位置，使图像左下角对准相机视野的左下角
};

#endif // !CONTROL_CONSTANTS_H