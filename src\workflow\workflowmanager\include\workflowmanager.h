/*****************************************************************//**
 * @file   workflowmanager.h
 * @brief  工程运行模块管理类，主要负责工程运行模块与外部交互等功能实现
 * @details
 * <AUTHOR>
 * @date 2024.8.14
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.14          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSWORKFLOWMANAGER_H__
#define __JRSWORKFLOWMANAGER_H__

 //STD
#include <iostream>
#include <functional>
//Custom
#include "pluginexport.hpp"
#include "calcfov.h"
#include "pcbpathplan.h"
//Third
namespace jrslogic
{
    class JrsAoiImgsManager;
    class ScanProcess;
}
namespace jrsdata
{
    struct JrsImageBuffer;
    struct ProjectParam;
    class DataManager;
}

namespace jrsdevice
{
    class DeviceManager;

}
namespace jrsalgo 
{
    class AlgorithmEngineManager;
}

namespace jrsworkflow
{
    struct WorkFlowDataImpl;
 
    class JRS_AOI_PLUGIN_API WorkFlowManager
    {
        public:
            WorkFlowManager (const std::shared_ptr<jrsdevice::DeviceManager>& device_ptr_
                             ,const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_ptr_
                             ,const std::shared_ptr<jrsdata::DataManager>data_manager_ptr_);
            ~WorkFlowManager ();
           
            /**
             * @fun WorkFlowEventHandler 
             * @brief workflow事件处理
             * @param event_name_ [IN] 事件名称
             * @param project_param [IN] 工程参数
             * @return 成功返回AOI_OK，否则返回错误码
             * <AUTHOR>
             * @date 2025.3.6
             */
            int WorkFlowEventHandler (const jrsdata::ViewParamBasePtr& param_);
            
            /**
             * @fun StartExecuteFlow 
             * @brief 开始执行流程
             * @param project_param_ [IN] 工程参数
             * @return  成功返回AOI_OK，否则返回错误码
             * <AUTHOR>
             * @date 2025.3.6
             */
            int StartExecuteFlow ( const std::shared_ptr<jrsdata::ProjectParam>& project_param_);
            /**
             * @fun BufferInvoke 
             * @brief 图像缓存回调
             * @param imgs [IN] 图像
             * <AUTHOR>
             * @date 2025.3.6
             */
            void BufferInvoke ( const jrsdata::JrsImageBuffer& imgs );
            /**
             * @fun SetLogicInvokeFun 
             * @brief 设置逻辑回调
             * @param logic_invoke_ [IN] 逻辑回调函数
             * <AUTHOR>
             * @date 2025.3.6
             */
            void SetLogicInvokeFun (std::function<void(const std::vector<PCBPathPlanning::Fov>&,bool )> logic_invoke_);
           
            
            /**
             * @fun SetControlPanelCallBack 
             * @brief 设置控制面板回调
             * @param callback_ [IN] 回调函数
             * <AUTHOR>
             * @date 2025.3.6
             */
            void SetControlPanelCallBack(jrsdata::InvokeControlPanelViewParamFun callback_);
            
            /**
             * @fun SetStructLightParam 
             * @brief 设置结构光参数
             * @param struct_light_param_ [IN] 结构光参数
             * <AUTHOR>
             * @date 2025.3.6
             */
            void SetStructLightParam(const jrsdata::StructLightParam& struct_light_param_);
        private:
            //Fun
            void InitMember ();
            //Member

        WorkFlowDataImpl* impl_data;

    };
    using WorkFlowManagerPtr = std::shared_ptr<WorkFlowManager>;
}

#endif // !__JRSWORKFLOWMANAGER_H__
