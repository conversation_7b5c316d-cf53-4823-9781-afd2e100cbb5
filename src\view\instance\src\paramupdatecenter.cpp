#include "paramupdatecenter.h"

#include "paramoperator.h"
//#include "queryparam.hpp"
namespace jrsaoi
{
    struct ImplData {
        jrsparam::ProjectDataProcessPtr project_data_process_ptr;
        std::shared_ptr<jrsparam::ParameterProcess> parameter_process_instance;
        jrsaoi::Function call_back;
        jrsdata::OperateViewParamPtr operate_view_param;
        jrsdata::GraphicsUpdateProjectEventParamPtr graphics_update_param;
    };
}

jrsaoi::InstanceParamUpdateCenter::InstanceParamUpdateCenter()
    :_impl_data(new ImplData())
{
    Init();

}

jrsaoi::InstanceParamUpdateCenter::~InstanceParamUpdateCenter()
{

}

int jrsaoi::InstanceParamUpdateCenter::EventHandler(const jrsdata::ViewParamBasePtr& param_)
{
    Log_INFO("进入参数更新事件处理！");
    if (!IsValidParam(param_))
    {
        return -1;
    }

    if (!InvokeFun(param_))
    {
        return -1;
    }
    return jrscore::AOI_OK;
}


void jrsaoi::InstanceParamUpdateCenter::SetNotifyCallBack(Function notify_call_back_)
{
    _impl_data->call_back = notify_call_back_;
}

int jrsaoi::InstanceParamUpdateCenter::UpdateProjectParam(const jrsdata::ViewParamBasePtr& param_)
{
    auto project_param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    _impl_data->project_data_process_ptr->SetProjectParam(project_param_temp->project_param);

    if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME)
    {

        //! 如果是读取新工程的时候，先清空工程vector
        _impl_data->project_data_process_ptr->ClearProjectMap();
        _impl_data->project_data_process_ptr->AppendProject(project_param_temp->project_param);

    }
    if (_impl_data->call_back)
    {
        _impl_data->call_back(_impl_data->graphics_update_param);
    }
    return jrscore::AOI_OK;
}

int jrsaoi::InstanceParamUpdateCenter::AppendProject(const jrsdata::ViewParamBasePtr& param_)
{
    auto project_param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    _impl_data->project_data_process_ptr->AppendProject(project_param_temp->project_param);

    return jrscore::AOI_OK;
}


int jrsaoi::InstanceParamUpdateCenter::UpdateEntiretyImageParam(const jrsdata::ViewParamBasePtr& param_)
{
    auto project_param_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
    _impl_data->project_data_process_ptr->SetCurrentEntiretyGroupName(project_param_temp->project_param->current_group_name);
    for (auto& entirety_image_map : project_param_temp->project_param->entirety_board_imgs)
    {
        _impl_data->project_data_process_ptr->UpdateImage(entirety_image_map.first, entirety_image_map.second);
    }
    //_impl_data->graphics_update_param->project_param = project_param_temp->project_param;
    if (_impl_data->call_back)
    {
        _impl_data->call_back(_impl_data->graphics_update_param);
    }

    return jrscore::AOI_OK;
}


int jrsaoi::InstanceParamUpdateCenter::UpdateSystemParam(const jrsdata::ViewParamBasePtr& param_)
{
    auto setting_view_param = std::static_pointer_cast<jrsdata::SettingViewParam>(param_);
    _impl_data->parameter_process_instance->SetSystemSettingParams(setting_view_param->sys_param);
    //_impl_data->param_instance->SetParams(jrsdata::SYSTEM_PARAM, setting_view_param->sys_param.sys_params);
    UpdateMachineParam(param_);
    return jrscore::AOI_OK;
}


int jrsaoi::InstanceParamUpdateCenter::UpdateMachineParam(const jrsdata::ViewParamBasePtr& param_)
{
    auto setting_view_param = std::static_pointer_cast<jrsdata::SettingViewParam>(param_);
    _impl_data->operate_view_param->event_name = jrsaoi::MACHINE_PARAM_UPDATE_EVENT;
    _impl_data->operate_view_param->machine_param = setting_view_param->machine_param;
    _impl_data->parameter_process_instance->SetMachineSettingParams(setting_view_param->machine_param);
    //_impl_data->param_instance->SetParams(jrsdata::MACHINE_PARAM, setting_view_param->machine_param.machine_params_data);
    //_impl_data->param_instance->SetParams(jrsdata::MACHINE_PARAM, setting_view_param->machine_param.machine_params_seting);
    if (_impl_data->call_back)
    {
        _impl_data->call_back(_impl_data->operate_view_param);
    }
    return jrscore::AOI_OK;
}


int jrsaoi::InstanceParamUpdateCenter::UpdateSystemStateParam(const jrsdata::ViewParamBasePtr& param_)
{
    _impl_data->operate_view_param->event_name = param_->event_name;
    auto sys_state_param = std::static_pointer_cast<jrsdata::SystemStateViewParam>(param_);
    _impl_data->parameter_process_instance->SetMachineStateParams(sys_state_param->check_items);
    //_impl_data->param_instance->SetParams(jrsdata::SYSTEM_PARAM, sys_state_param->check_items);
    if (_impl_data->call_back)
    {
        _impl_data->call_back(_impl_data->operate_view_param);
    }
    return jrscore::AOI_OK;
}

void jrsaoi::InstanceParamUpdateCenter::Init()
{
    InitMember();
    InitInvokeFun();
}

void jrsaoi::InstanceParamUpdateCenter::InitMember()
{
    _impl_data->project_data_process_ptr = jrsaoi::ParamOperator::GetInstance().GetProjectDataProcessInstance();
    _impl_data->parameter_process_instance = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();
    _impl_data->call_back = nullptr;
    {
        _impl_data->operate_view_param = std::make_shared<jrsdata::OperateViewParam>();
        _impl_data->operate_view_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        _impl_data->operate_view_param->sub_name = jrsaoi::SHORTCUT_OPERATER_SUB_NAME;
        _impl_data->operate_view_param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
    }
    {
        _impl_data->graphics_update_param = std::make_shared<jrsdata::GraphicsUpdateProjectEventParam>();
        _impl_data->graphics_update_param->event_name = PROJECT_UPDATE_EVENT_NAME;
        _impl_data->graphics_update_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        _impl_data->graphics_update_param->topic_name = jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME;
        _impl_data->graphics_update_param->sub_name = PROJECT_SHOWLIST_SUB_NAME;
    }
}


/**< 事件注册 */
void jrsaoi::InstanceParamUpdateCenter::InitInvokeFun()
{
    auto sys_state_update_func = std::bind(&InstanceParamUpdateCenter::UpdateSystemStateParam, this, std::placeholders::_1);
    auto sys_param_update_func = std::bind(&InstanceParamUpdateCenter::UpdateSystemParam, this, std::placeholders::_1);
    auto machine_param_update_func = std::bind(&InstanceParamUpdateCenter::UpdateMachineParam, this, std::placeholders::_1);
    auto project_param_update_func = std::bind(&InstanceParamUpdateCenter::UpdateProjectParam, this, std::placeholders::_1);
    auto append_project_update_func = std::bind(&InstanceParamUpdateCenter::AppendProject, this, std::placeholders::_1);
    _center_func_map =
    {
        { jrsaoi::PROJECT_READ_EVENT_NAME , project_param_update_func},
        { jrsaoi::APPEND_PROJECT_EVENT_NAME , append_project_update_func},
        { jrsaoi::IMPORT_CAD_EVENT_NAME , project_param_update_func},
        { jrsaoi::ENTIRETY_IMAGE_READ , std::bind(&InstanceParamUpdateCenter::UpdateEntiretyImageParam , this , std::placeholders::_1) },
        { jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME,sys_param_update_func },
        { jrsaoi::MACHINE_PARAM_UPDATE_EVENT, machine_param_update_func },
        { jrsaoi::MACHINE_PARAM_SAVE_EVENT, machine_param_update_func },
        { jrsaoi::UPDATE_SYSTEM_STATE_EVENT, sys_param_update_func},
        { jrsaoi::SYSTEM_PARAM_SAVE_EVENT, sys_param_update_func },
        { jrsaoi::DATABASE_CONNECT_EVENT,sys_state_update_func },
        { jrsaoi::DATABASE_DISCONNECT_EVENT,sys_state_update_func },
    };

}

bool jrsaoi::InstanceParamUpdateCenter::IsValidParam(const jrsdata::ViewParamBasePtr& param_)
{
    if (!param_)
    {
        Log_ERROR("project_event_param_为空");
        return false;
    }

    if (param_->event_name.empty())
    {
        Log_ERROR("event_name is empty");
        return false;
    }
    return true;
}

bool jrsaoi::InstanceParamUpdateCenter::InvokeFun(const jrsdata::ViewParamBasePtr& param_)
{
    auto it = _center_func_map.find(param_->event_name);
    if (it == _center_func_map.end())
    {
        return false;
    }
    if (!it->second)
    {
        return false;
    }
    it->second(param_);  // 执行函数
    return true;
}

