﻿
//Custom
#include "algorithmenginemanager.h"
#include "loadalgomanager.h"
#include "algoexecute.h"
#include "algoexecuteparam.hpp"
#include "fileoperation.h"
#include "timeutility.h"
#pragma warning(push, 0)
#include "barcodeoperatorparam.hpp"
#include "baseplaneparam.hpp"
#include "bridgeoperatorparam.hpp"
#include "blockoperatorparam.hpp"
#include "heightmeasureoperatorparam.hpp"
#include "markoperatorparam.hpp"
#include "halcontestoperatorparam.hpp"
#include "ocvoperatorparam.hpp"
#include "positionoperatorparam.hpp"
#include "solderchipoperatorparam.hpp"
#include "ocroperatorparam.hpp"
#include "markshapeoperatorparam.hpp"
#include "positionshapeoperatorparam.hpp"
#include "location3doperatorparam.hpp"
#include "leadoperatorparam.hpp"
#include "edgeoperatorparam.hpp"
#include "polarantioperatorparam.hpp"
//#include "fpcpadpositionoperatorparam.hpp"
#include "padlocationoperatorparam.hpp"
#include "positioncontouroperatorparam.hpp"
#include "nickelalignmentoperatorparam.hpp"
#include "algodatacollectoperatorparam.hpp"
//#include "deeplearndeductionoperatorparam.hpp"
#pragma warning(pop)

//Third
#include "nlohmann/json.hpp"
namespace jrsalgo
{
    struct AlgoEngineDataImpl
    {
        AlgoFactoryPtr algo_factory_ptr; /**< 算法导入工厂*/
        AlgoExecutePtr algo_execute_ptr; /**< 算法执行实例*/
        LoadAlgoManagerPtr load_algo_manager_ptr; /**< 算法导入实例*/
        nlohmann::json algo_param_config;      /**< 算法参数配置列表*/
    };
    AlgorithmEngineManager::AlgorithmEngineManager()
        : algo_engine_data_impl(new AlgoEngineDataImpl())

    {
        InitMember();
    }

    AlgorithmEngineManager::~AlgorithmEngineManager()
    {
        if (algo_engine_data_impl)
        {
            delete algo_engine_data_impl;
            algo_engine_data_impl = nullptr;
        }
    }

    std::string AlgorithmEngineManager::GetAlgoExecuteRectInfo(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_)
    {
        return AlgoExecute::GetAlgoExecuteRectInfo(param_ptr_);

    }

    int AlgorithmEngineManager::SetAlgoDynamicOutParamValue(const jrsparam::AlgoExecuteResultParam& execute_algo_result_param_, const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_, const std::string& algo_name_)
    {
        return AlgoExecute::SetAlgoDynamicOutParamValue(execute_algo_result_param_, param_ptr_, algo_engine_data_impl->algo_param_config, algo_name_);

    }

    const std::map<std::string, std::map<std::string, std::vector<std::string>>>& AlgorithmEngineManager::GetAlgoSpecMap()
    {
        return  algo_engine_data_impl->load_algo_manager_ptr->GetAlgoSpecMap();
    }

    jrsparam::AlgoExecuteResultParam AlgorithmEngineManager::GetAlgoExecuteResultParam(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_, const std::string& algo_name_)
    {
        return AlgoExecute::GetSpeficAlgoExecuteResult(param_ptr_, algo_engine_data_impl->algo_param_config, algo_name_);
    }

    bool AlgorithmEngineManager::GetAlgoExecuteResultStatus(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_)
    {
        return AlgoExecute::GetAlgoExecuteResultStatus(param_ptr_);
    }

    void AlgorithmEngineManager::InitMember()
    {
        auto current_work_directory = jtools::FileOperation::GetCurrentWorkingDirectory();
        std::filesystem::path config_path = current_work_directory + "/config/algo/algoplugin/loadinfo/algoinfo.json";
        algo_engine_data_impl->algo_param_config = jrscore::ReadJson(config_path.string());

        algo_engine_data_impl->algo_factory_ptr = std::make_shared<AlgoFactory>();
        algo_engine_data_impl->load_algo_manager_ptr = std::make_shared<LoadAlgoManager>(algo_engine_data_impl->algo_factory_ptr, algo_engine_data_impl->algo_param_config);
        algo_engine_data_impl->algo_execute_ptr = std::make_shared<AlgoExecute>();
        algo_engine_data_impl->algo_execute_ptr->SetAlgoFactoryPtr(algo_engine_data_impl->algo_factory_ptr);


    }

    jrsoperator::OperatorViewBase* AlgorithmEngineManager::GetSpecificAlgoView(const std::string& algo_name_)
    {
        return  algo_engine_data_impl->algo_factory_ptr->GetSpecificAlgoView(algo_name_);
    }

    std::shared_ptr<jrsoperator::OperatorParamBase> AlgorithmEngineManager::GetSpecificAlgoParamPtr(const std::string& algo_name)
    {
        static const std::string prefix = "jrsoperator::";
        static const std::string suffix = "Param";
        std::string full_class_name;
        full_class_name.reserve(prefix.size() + algo_name.size() + suffix.size());
        full_class_name = prefix + algo_name + suffix;
        try
        {
            auto algo_param_ptr = std::dynamic_pointer_cast<jrsoperator::OperatorParamBase>(iguana::create_instance(full_class_name));
            return algo_param_ptr;
        }
        catch (const std::exception& e)
        {
            std::cout << e.what() << std::endl;
            return nullptr;
        }

    }

    const std::map<std::string, std::string>& AlgorithmEngineManager::GetAlgoNameMap()
    {
        return algo_engine_data_impl->load_algo_manager_ptr->GetAlgoNameMap();
    }

    std::shared_ptr<jrsoperator::OperatorParamBase> AlgorithmEngineManager::ExecuteSpecificAlgoDrive(const jrsparam::ExecuteAlgoParam& execute_algo_param_)
    {
        jtools::ScopedTimer timer_scoped(execute_algo_param_.algo_name + "单个算法检测时间", true, false);

        try
        {
            return AlgoExecute::ExecuteSpeficAlgo(execute_algo_param_, algo_engine_data_impl->algo_param_config);

        }
        catch (...)
        {
            return nullptr;
        }
    }
}

