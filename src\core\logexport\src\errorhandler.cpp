#include "errorhandler.h"

namespace jrscore
{

    struct ErrorImplData
    {
        ///*** @brief 错误描述信息 YYZhang 2024.1.23*/
        //struct ErrorContext
        //{
        //    std::string error_description; /**< 错误信息描述 */
        //    std::string error_modlue; /**< 错误模块 */
        //    AOIErrorCode error_code; /**< 错误码*/
        //};

        /*** @brief 错误信息描述表 YYZhang 2024.1.23*/
        using ErrorContext_M = std::map<int, ErrorInfo>;


        ErrorContext_M error_contexts;/**< 错误码字典*/
        ErrorHandler::ErrorHandleFlushCallBack error_flush_cb; /**<处理错误刷新回调*/
        ErrorHandler::ErrorHandleGenerateCallBack error_generate_cb; /**< 错误处理回调*/

    };

    ErrorHandler::ErrorHandler ():p_data(new ErrorImplData())
    {
        p_data->error_contexts.insert(J3DError::Get3DErrorMap().begin(), J3DError::Get3DErrorMap().end());
   

    }

    ErrorHandler::~ErrorHandler ()
    {
        delete p_data;
        p_data = nullptr;
    }

    void ErrorHandler::PushStackError (const AOIErrorCode err, const std::string& what)
    {
        ErrorInfo err_info_temp;
        //err_info_temp.error_code = err;
        err_info_temp.err_string = p_data->error_contexts[err].err_string;

        err_info_temp.err_description = p_data->error_contexts[err].err_description;
        err_info_temp.module_name = p_data->error_contexts[err].module_name;
        err_info_temp.what = what;
        if (p_data->error_generate_cb)
        {
            p_data->error_generate_cb (err_info_temp);
        }
    }

    void ErrorHandler::FlushStackError (FlushStrategy straegy)
    {
        if (p_data->error_flush_cb)
        {
            p_data->error_flush_cb (straegy);
        }
    }

    void ErrorHandler::SetErrorGenerateCallBack (ErrorHandleGenerateCallBack cb_)
    {
        p_data->error_generate_cb = cb_;
    }



    void ErrorHandler::SetErrorFlushCallBack (ErrorHandleFlushCallBack cb_)
    {
        p_data->error_flush_cb = cb_;
    }

}
