﻿#pragma warning(push, 3)
//QT
#include <QCoreApplication>
#include <QTableWidgetItem>
#include <QFile>
#include <QStringList>
#include <QTextStream>
#include <QTableWidgetItem>
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QGraphicsPixmapItem>
#include <QVBoxLayout>
#include <QPainter>
#include <QTabWidget>
#include <QDesktopWidget>
#include <QRect>

//CUSTOM
#include "templateview.h"
#include "coreapplication.h"
#include "fileoperation.h"
#include "viewtool.hpp"
#include "colorwidget.h"
//#include "projectoperator.h"
#include "imageprocessalgo.h"

#pragma warning(disable: 4244)

using namespace jtools;
using namespace jrsdata;

namespace jrsaoi
{
    TemplateView::TemplateView(QWidget* parent)
        : QDialog(parent)
        , ui(new Ui::TemplateView())
    {
        ui->setupUi(this);
        InitView();
        ConnectSlots();
    }
    TemplateView::~TemplateView()
    {
        delete ui;
    }
    void TemplateView::SoltTemplateSelectChanged(const TemplateItemValue& item_val)
    {
        // TODO 此为临时方案，后续需要将信号发送值快捷操作控件，通过控件切换显示和截图
        auto param = std::make_shared<AlgoEventParam>();
        param->light_type = (jrsdata::LightImageType)item_val.light_type;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_RENDER_SUB_NAME;
        param->event_name = REQUEST_RENDER_UPDATE_SELECT_LIGHT_TYPE_EVENT_NAME;
        SignalUpdateTemplate(param);

        ipe_module->SetTestData(m_current_src_image, item_val.color_param.toLocal8Bit().toStdString());
        auto color_params = ColorParams::FromJson(item_val.color_param.toLocal8Bit().toStdString());
        cv::Mat mat_data_color_process;
        if (color_params.deal_type != ColorWheelDealType::NONE)
        {
            ipe_module->GetColorProcessImageWithParam(m_current_src_image, color_params, mat_data_color_process);
        }
        else
        {
            mat_data_color_process = m_current_src_image.clone();
        }
        //std::vector<std::pair<int, cv::RotatedRect>>rects;
        //m_create_template_dlg->SetCreateTemplateData(rects, MatToQPixmap(mat_data_color_process), item_val.color_param.toLocal8Bit().toStdString());
        //m_create_template_dlg->SetCreateTemplateData(qrect, MatToQPixmap(mat_data_color_process), item_val.color_param.toLocal8Bit().toStdString(), item_val.light_type);
    }

    void TemplateView::SoltTemplateItemDoubleClicked(const TemplateItemValue& item_val)
    {
        ColorParams color_params = ColorParams::FromJson(item_val.color_param.toLocal8Bit().toStdString());
        cv::Mat mat_data_color_process;
        ipe_module->SetTestData(item_val.img_src, color_params.ToJson());
        colorwheel::GetProprecessResult(color_params, item_val.img_src, mat_data_color_process);
        m_create_template_dlg->SetExistedTemplate(MatToQPixmap(mat_data_color_process), item_val.color_param.toLocal8Bit().toStdString());
        m_currect_light_type = (LightImageType)item_val.light_type;
        m_template_tab->setCurrentIndex(1);
    }

    void TemplateView::SlotCreateTemplate(const cv::RotatedRect& rect, const int& direction, const std::string& ipe_params)
    {
        if (m_current_src_image.empty())
        {
            JRSMessageBox_WARN("警告", "当前图片不存在", jrscore::MessageButton::Ok);

            return;
        }

        auto rotated_matrix = cv::getRotationMatrix2D(rect.center, rect.angle, 1.0);
        cv::Mat rotated_img;
        cv::warpAffine(m_current_src_image, rotated_img, rotated_matrix, m_current_src_image.size());

        cv::RotatedRect rect_roi = cv::RotatedRect(rect.center, rect.size, 0);

        cv::Mat cropped_img = rotated_img(rect_roi.boundingRect() & cv::Rect(0, 0, rotated_img.cols, rotated_img.rows));

        if (cropped_img.empty())
        {
            JRSMessageBox_WARN("警告", "截取区域错误", jrscore::MessageButton::Ok);
            return;
        }

        if (direction > 0)
        {
            int angle = (static_cast<int>(jrsdata::ComponentUnit::Direction::UP) - static_cast<int>(m_detect_win_direction)) * 90;
            if (angle < 0)
            {
                angle += 360;
            }
            cv::RotateFlags flags = (cv::RotateFlags)((angle / 90) - 1);
            cv::rotate(cropped_img, cropped_img, flags);
        }

        m_template_tab->setCurrentIndex(0);
        auto param = std::make_shared<AlgoEventParam>();
        param->data_operate_type = jrsdata::DataUpdateType::ADD_DATA;
        param->region_mat = cropped_img;
        param->light_type = (jrsdata::LightImageType)m_currect_light_type;
        param->template_color_param = ipe_params;
        param->event_name = REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME;



        SignalUpdateTemplate(param);
    }

    void TemplateView::SlotUpdateSelectedTemplate(const std::string& ipe_params)
    {
        QListWidgetItem* currentItem = m_template_list_table->currentItem();
        if (currentItem == nullptr)
        {
            return;
        }
        int currentIndex = m_template_list_table->row(currentItem);
        TemplateItemValue item_val;
        m_template_list_table->GetItemTemplateData(currentIndex, item_val);

        ColorParams color_params = ColorParams::FromJson(ipe_params);
        cv::Mat mat_data_color_process;
        colorwheel::GetProprecessResult(color_params, item_val.img_src, mat_data_color_process);
        item_val.img_show = MatToQPixmap(mat_data_color_process);
        item_val.light_type = (int)m_currect_light_type;
        item_val.color_param = QString::fromLocal8Bit(ipe_params.c_str());
        m_template_list_table->UpdateItemTemplateData(currentIndex, item_val);

        m_template_tab->setCurrentIndex(0);

        auto param = std::make_shared<AlgoEventParam>();


        jrsdata::Template template_data(item_val.id, item_val.img_src, item_val.color_param.toLocal8Bit().toStdString(), item_val.light_type);

        param->data_operate_type = jrsdata::DataUpdateType::UPDATE_DATA;
        param->templates.push_back(template_data);
        param->event_name = REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME;
        SignalUpdateTemplate(param);
    }

    QPixmap TemplateView::MatToQPixmap(const cv::Mat& mat)
    {
        cv::Mat rgbMat;
        cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB); // 转换为 RGB
        QImage image(rgbMat.data, int(rgbMat.cols), int(rgbMat.rows), int(rgbMat.step[0]), QImage::Format_RGB888);
        return QPixmap::fromImage(image);
    }

    int TemplateView::GetCurIPEParam(std::string& param)
    {
        ColorParams color_params = ipe_module->GetColorProcessParam();
        param = color_params.ToJson();
        if (param != "")
        {
            return 0;
        }
        else
        {
            return -1;
        }
    }

    int TemplateView::UpdateInputImageInfo(const AlgoEventParamPtr& param)
    {
        AddTestImageToIPE(param->region_mat, param->algorithm_param.color_param);
        m_current_src_image = param->region_mat.clone();
        m_currect_light_type = param->light_type;

        if (param->detect_rects.size() > 0)
        {
            m_detect_win_direction = param->detect_rects[0].first;
        }
        else
        {
            m_detect_win_direction = -1;
        }

        cv::Mat mat_data_color_process;
        ColorParams ipe_params;
        ipe_module->GetColorProcessImage(param->region_mat, mat_data_color_process, ipe_params);

        if (mat_data_color_process.type() == CV_32FC1)
        {
            cv::Mat temp_mat;
            cv::normalize(mat_data_color_process, m_current_show_image, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        }

        auto show_img = MatToQPixmap(mat_data_color_process);
        m_create_template_dlg->SetCreateTemplateData(param->detect_rects, show_img, ipe_params.ToJson());
        //m_template_tab->setCurrentIndex(1);

        return 0;
    }

    int TemplateView::UpdataInputImage(const cv::Mat& input_image, const LightImageType light_type)
    {
        AddTestImageToIPE(input_image, "");
        m_current_src_image = input_image.clone();

        auto show_img = MatToQPixmap(m_current_src_image);
        m_create_template_dlg->UpdataShowImage(show_img);
        m_template_tab->setCurrentIndex(1);
        m_currect_light_type = light_type;
        return 0;
    }

    void TemplateView::InitView()
    {
        setWindowFlags(Qt::WindowCloseButtonHint | Qt::Window | Qt::WindowStaysOnTopHint);
        ipe_module = ColorModule::CreateColorWheel();

        m_create_template_dlg = new DlgCreateTemplate(this);
        m_edit_template_dlg = new DlgTemplateListEdit(this);
        m_template_list_table = new TemplateListWidget(this);
        m_edit_template_dlg->SetTemplateListWidget(m_template_list_table);

        m_template_tab = new QTabWidget(this);
        m_template_tab->addTab(m_edit_template_dlg, "模板列表");
        m_template_tab->addTab(m_create_template_dlg, "模板编辑");
        ui->widget_10->layout()->addWidget(m_template_tab);
        ui->widget_2->layout()->addWidget(ipe_module->GetWindowHandle());
    }


    void TemplateView::AddTestImageToIPE(cv::Mat color_data, std::string params)
    {
        ipe_module->SetTestData(color_data, params);
    }

    void TemplateView::SetIPEParamChangedCallback(ColorParamsChangedFunc func)
    {
        if (ipe_module)
        {
            ipe_module->SetColorChangedCallback(func);
        }
    }

    void TemplateView::UpdateTempalteModelList(std::vector<jrsdata::Template>& templates, const double component_angle)
    {
        QList<TemplateItemValue> items;

        double rotate_angle = component_angle;
        if (m_detect_win_direction > 1)
        {
            rotate_angle = component_angle + 90 * (m_detect_win_direction - 1);
        }

        for (auto& template_data : templates)
        {
            TemplateItemValue item_value;
            Template2TemplateItemValue(template_data, item_value, rotate_angle);
            items.push_back(item_value);
        }
        m_template_list_table->UpdateAllItems(items);
    }

    void TemplateView::AddTemplateModelToList(jrsdata::Template& template_data, const double component_angle)
    {
        double rotate_angle = component_angle;
        if (m_detect_win_direction > 1)
        {
            rotate_angle = component_angle + 90 * (m_detect_win_direction - 1);
        }

        TemplateItemValue item_value;
        Template2TemplateItemValue(template_data, item_value, rotate_angle);
        m_template_list_table->AddItem(item_value);
    }

    void TemplateView::Update(const AlgoEventParamPtr& param)
    {
        UpdateInputImageInfo(param);
        UpdateTempalteModelList(param->templates, param->cur_select_component->angle);
        if (param->templates.size() > 0)
        {
            m_template_tab->setCurrentIndex(0);
        }
        else
        {
            m_template_tab->setCurrentIndex(1);
        }
    }

    void TemplateView::ClearAllData()
    {
        m_current_src_image = cv::Mat();
        m_template_list_table->clear();
    }

    void TemplateView::GetInputImage(cv::Mat& input_image)
    {
        input_image = m_current_src_image.clone();
    }

    void TemplateView::ConnectSlots()
    {
        connect(m_edit_template_dlg, &DlgTemplateListEdit::SigDeleteTemplate, this, &TemplateView::DeleteModelSlot);
        connect(m_template_list_table, &TemplateListWidget::SigTemplateSelectionChanged, this, &TemplateView::SoltTemplateSelectChanged);
        connect(m_template_list_table, &TemplateListWidget::SigTemplateDoubleClicked, this, &TemplateView::SoltTemplateItemDoubleClicked);
        connect(m_create_template_dlg, &DlgCreateTemplate::SigCreateTemplate, this, &TemplateView::SlotCreateTemplate);
        connect(m_create_template_dlg, &DlgCreateTemplate::SigUpdateTemplate, this, &TemplateView::SlotUpdateSelectedTemplate);
        connect(this, &TemplateView::SignalIPEParamChanged, m_create_template_dlg, &DlgCreateTemplate::ImageChanged);

        SetIPEParamChangedCallback([=](ColorParams& params, cv::Mat& enhanced_img, cv::Mat& binary_img)
            {
                QPixmap image;
                if (IpeMoudleImage2QPixmap(enhanced_img, binary_img, image) == 0)
                {
                    emit SignalIPEParamChanged(image, params.ToJson());
                }
            });
    }
    void TemplateView::DisconnectSlots()
    {

    }
    QPixmap TemplateView::RotatedPixmap(const QPixmap& pixmap, qreal rotationAngle)
    {
        QPixmap rotated(pixmap.size());
        rotated.fill(Qt::transparent);
        QPainter painter(&rotated);
        painter.setRenderHint(QPainter::SmoothPixmapTransform, true);
        painter.translate(pixmap.size().width() / 2, pixmap.size().height() / 2);
        painter.rotate(rotationAngle);
        painter.translate(-pixmap.size().width() / 2, -pixmap.size().height() / 2);
        painter.drawPixmap(0, 0, pixmap);
        return rotated;
    }
    void TemplateView::CropTemplateSlot()
    {
        auto param = std::make_shared<AlgoEventParam>();
        param->event_name = REQUEST_DRAW_TEMP_REGION_EVENT_NAME;
        SignalUpdateTemplate(param);
    }
    void TemplateView::DeleteModelSlot()
    {
        auto del_template_ids = m_template_list_table->DeleteSelectItem();
        if (del_template_ids.empty())
        {
            return;
        }

        for (const auto& id : del_template_ids)
        {
            auto param = std::make_shared<AlgoEventParam>();
            param->data_operate_type = jrsdata::DataUpdateType::DELETE_DATA;
            param->event_name = REQUEST_OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME;
            param->template_id = id;
            SignalUpdateTemplate(param);
        }
    }
    void TemplateView::showEvent(QShowEvent*)
    {
        QDesktopWidget* desktop = QApplication::desktop();
        QRect screenRect = desktop->availableGeometry();
        int x = (screenRect.width() - width()) / 2;
        int y = screenRect.height() - height()/* - 30*/;
        move(x, y);
    }

    int TemplateView::IpeMoudleImage2QPixmap(const cv::Mat& enhance_image, const cv::Mat& mask_image, QPixmap& show_image)
    {
        if (enhance_image.empty())
        {
            return -1;
        }

        cv::Mat show_mat;
        if (enhance_image.type() == CV_32FC1)
        {
            cv::normalize(enhance_image, show_mat, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        }
        else
        {
            show_mat = enhance_image.clone();
        }

        if (!mask_image.empty())
        {
            if (show_mat.channels() == 1)
            {
                cv::cvtColor(show_mat, show_mat, cv::COLOR_GRAY2BGR);
            }
            show_mat = colorwheel::MaskImageByBinary(show_mat, mask_image);
        }

        show_image = MatToQPixmap(show_mat);

        return 0;
    }
    int TemplateView::Template2TemplateItemValue(jrsdata::Template& template_data, TemplateItemValue& item_val, const double component_angle)
    {
        cv::Mat mat_data = template_data.GetMatImage().clone();
        if (!mat_data.empty())
        {
            if (component_angle != 0)
            {
                cv::Point2f center(mat_data.cols / 2.0, mat_data.rows / 2.0);

                // 计算旋转矩阵
                cv::Mat rotMat = cv::getRotationMatrix2D(center, 360 - component_angle, 1.0);

                // 计算旋转后的图像尺寸
                double absCos = std::abs(rotMat.at<double>(0, 0));
                double absSin = std::abs(rotMat.at<double>(0, 1));
                int new_width = int(mat_data.rows * absSin + mat_data.cols * absCos);
                int new_height = int(mat_data.rows * absCos + mat_data.cols * absSin);

                // 调整旋转矩阵中的平移量
                rotMat.at<double>(0, 2) += (new_width / 2.0) - center.x;
                rotMat.at<double>(1, 2) += (new_height / 2.0) - center.y;

                // 旋转图像并调整大小
                cv::Mat rotated;
                cv::warpAffine(mat_data, rotated, rotMat, cv::Size(new_width, new_height));
                mat_data = rotated.clone();
            }

            ColorParams color_params = ColorParams::FromJson(template_data.color_params);
            cv::Mat mat_data_color_process;
            colorwheel::GetProprecessResult(color_params, mat_data, mat_data_color_process);

            item_val.id = template_data.id;
            item_val.img_show = MatToQPixmap(mat_data_color_process);
            item_val.img_src = mat_data;
            item_val.light_type = template_data.light_image_id;
            item_val.color_param = QString::fromLocal8Bit(template_data.color_params.c_str());
        }
        return 0;
    }
    int TemplateView::TemplateItemValue2Template(const TemplateItemValue& item_val, jrsdata::Template& template_data)
    {
        template_data.id = item_val.id;
        template_data.light_image_id = item_val.light_type;
        template_data.color_params = item_val.color_param.toStdString();
        template_data.SetMatImage(item_val.img_src);
        return 0;
    }
}
