//custom
#include "tablemanager.h"
#include "tablefactory.hpp"

//tables
#include "BoardTable.h"
#include "aoimachinetable.h"
#include "detecttypetable.h"
#include "detectwindowtable.h"
#include "devicetable.h"
#include "grouptable.h"
#include "projecttable.h"
#include "subboardtable.h"
#include "usertable.h"
#include "aoimachinestatisticstable.h"
namespace jrsdatabase
{
    struct ManagerImplData
    {
        ////主题
        //jrscore::ModuleHandlePtr view_update; /**< 界面端从逻辑层获取数据信息*/
        //jrscore::ModuleHandlePtr device_list_change;       /**< 复判列表复判元件变化 */
        //jrsdata::ViewParamBasePtr system_temp;
        //ManagerImplData()
        //{
        //	system_temp = std::make_shared<jrsdata::SystemParamAll>();
        //	system_temp->view_name = jrscore::SYSPARA_MODULE_NAME;
        //}
    };
}
std::atomic<bool> jrsdatabase::TableManager::_is_create_table = true;
std::map<std::string, jrsdatabase::TableBasePtr> jrsdatabase::TableManager::_table_container;
template<typename Table>
inline int jrsdatabase::TableManager::RegisterComponent(const std::string& name_)
{
    jrsdatabase::TableBasePtr table = jrsdatabase::TableFactory::CreateTable<Table>(name_);
    if (_table_container.find(name_) != _table_container.end())
    {
        return  -1;//ERROR CODE - HJC
    }
    else
    {
        _table_container[name_] = table;
        return jrscore::AOI_OK;
    }
}

jrsdatabase::TableManager::TableManager()
{
}

jrsdatabase::TableManager::~TableManager()
{
}

jrsdatabase::TableBasePtr jrsdatabase::TableManager::GetTable(const std::string& name_)
{
    auto it = _table_container.find(name_);
    if (it != _table_container.end())
    {
        return it->second;
    }
    Log_Error_Stack("容器中不存在名为:", name_, "的Table实例!");
    return nullptr;
}

int jrsdatabase::TableManager::InitTables(const std::shared_ptr<DB_Mysql>& conn_ptr)
{
    InitAOIMachine();
    InitAOIMachineStatstics();
    InitUser();
    InitProject();
    InitDetectType();
    InitBoard();
    InitSubboard();
    InitDevice();
    InitGroup();
    InitDetectWindow();

    //if (_is_create_table)//
    //{
    //	_is_create_table = false;
    for (auto& table : _table_container)///这里多线程使用肯呢个会有问题
    {
        table.second->Create(conn_ptr);
    }
    //	}
    return 0;
}

void jrsdatabase::TableManager::InitAOIMachine()
{
    auto res = RegisterComponent<jrsdatabase::AOIMachineTable>(jrstable::T_AOI_MACHINE);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_AOI_MACHINE,"表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitAOIMachineStatstics()
{
    auto res = RegisterComponent<jrsdatabase::AOIMachineStatisticsTable>(jrstable::T_AOI_MACHINE_STATISTICS);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_AOI_MACHINE,"表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitUser()
{
    auto res = RegisterComponent<jrsdatabase::UserTable>(jrstable::T_USER);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_USER, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitProject()
{
    auto res = RegisterComponent<jrsdatabase::ProjectTable>(jrstable::T_PROJECT);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_PROJECT,"表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitDetectType()
{
    auto res = RegisterComponent<jrsdatabase::DetectTypeTable>(jrstable::T_DETECT_TYPE);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_DETECT_TYPE, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitBoard()
{
    auto res = RegisterComponent<jrsdatabase::BoardTable>(jrstable::T_BOARD);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_BOARD, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitSubboard()
{
    auto res = RegisterComponent<jrsdatabase::SubboardTable>(jrstable::T_SUBBOARD);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_SUBBOARD, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitDevice()
{
    auto res = RegisterComponent<jrsdatabase::DeviceTable>(jrstable::T_DEVICE);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_DEVICE, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitGroup()
{
    auto res = RegisterComponent<jrsdatabase::GroupTable>(jrstable::T_GROUP);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_GROUP, "表注册失败，请检查");
    }
}

void jrsdatabase::TableManager::InitDetectWindow()
{
    auto res = RegisterComponent<jrsdatabase::DetectWindowTable>(jrstable::T_DETECT_WINDOW);
    if (res != jrscore::AOI_OK)
    {
        //Log_Error_Stack(jrstable::T_DETECT_WINDOW, "表注册失败，请检查");
    }
}