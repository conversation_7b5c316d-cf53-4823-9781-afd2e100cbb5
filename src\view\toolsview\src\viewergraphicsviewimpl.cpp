﻿//QT
#include <QScrollBar>
#include <QImageReader>
#include <QObject>
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QMouseEvent>
#include <QWheelEvent>
//Custom
#include "viewergraphicsviewimpl.h"
//#include "errorhandler.h"
//#include "coreapplication.h"
ViewerGraphicsViewImpl::ViewerGraphicsViewImpl(QWidget* parent)
    :QGraphicsView(parent)
    , scene(new QGraphicsScene(this))
    , _is_show_entirety_img(false)
    , _temperary_item(nullptr)
    , _is_dragging(false)
{
    // 注册 cv::Mat 类型
    qRegisterMetaType<cv::Mat>("cv::Mat");


    this->setScene(scene);
    setDragMode(QGraphicsView::DragMode::NoDrag);

    // 设置棋盘格背景
    QPixmap checkerboard = CreateCheckerboardBackground(20);
    setBackgroundBrush(QBrush(checkerboard));
    setSceneRect(QRectF(-99999, -99999, 199998, 199998));
    this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
}

ViewerGraphicsViewImpl::~ViewerGraphicsViewImpl()
{
}


int ViewerGraphicsViewImpl::CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center)
{
    std::lock_guard<std::mutex> lock(_image_mutex);
    std::shared_ptr<QGraphicsPixmapItem> item = nullptr;
    auto q_img = CvMatToQimage(image);

    auto it = _key_and_images.find(key_);
    if (it != _key_and_images.end())
    {
        item = it->second;
        item->setPixmap(QPixmap::fromImage(q_img));
    }
    else
    {
        item = std::make_shared<QGraphicsPixmapItem>(QPixmap::fromImage(q_img));
        _key_and_images[key_] = item;
        scene->addItem(item.get());
    }
    item->setZValue(z);
    item->setRotation(angle);
    if (is_draw_center)
    {
        qreal center_x = x - item->pixmap().width() / 2.0;
        qreal center_y = y - item->pixmap().height() / 2.0;
        item->setPos(center_x, center_y);
    }
    else
    {
        item->setPos(x, y);
    }

    centerOn(item->boundingRect().center());
    ShowImageByKey(key_);
    return 0;
}



int ViewerGraphicsViewImpl::AddGraphicsShapes(const jrsdata::GraphicsViewShape& graphics_shape_)
{
    if (!scene) {
        return -1;
    }

    QGraphicsItem* item = nullptr;

    switch (graphics_shape_.type) {
    case jrsdata::GraphicsViewShape::Rect:
        item = scene->addRect(QRectF(graphics_shape_.center_position.x - graphics_shape_.size.width / 2.0,
            graphics_shape_.center_position.y - graphics_shape_.size.height / 2.0,
            graphics_shape_.size.width, graphics_shape_.size.height),
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]), graphics_shape_.thickness),
            QBrush(Qt::NoBrush));
        break;
    case jrsdata::GraphicsViewShape::Circle:
        item = scene->addEllipse(QRectF(graphics_shape_.center_position.x - graphics_shape_.radius,
            graphics_shape_.center_position.y - graphics_shape_.radius,
            graphics_shape_.radius * 2, graphics_shape_.radius * 2),
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0])),
            QBrush(Qt::NoBrush));
        break;
    case jrsdata::GraphicsViewShape::Line:
        item = scene->addLine(graphics_shape_.center_position.x,
            graphics_shape_.center_position.y,
            graphics_shape_.center_position.x + graphics_shape_.size.width,
            graphics_shape_.center_position.y + graphics_shape_.size.height,
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]), graphics_shape_.thickness));
        break;
    case jrsdata::GraphicsViewShape::Text:
    {
        QGraphicsTextItem* textItem = scene->addText(QString::fromStdString(graphics_shape_.text));
        textItem->setDefaultTextColor(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]));
        textItem->setPos(graphics_shape_.center_position.x, graphics_shape_.center_position.y);
        item = textItem;
    }
    break;
    case jrsdata::GraphicsViewShape::Polygon:
    {
        QPolygonF polygon;
        for (const auto& pt : graphics_shape_.polygon_points) {
            polygon << QPointF(pt.x, pt.y);
        }
        item = scene->addPolygon(polygon, QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]), graphics_shape_.thickness));
    }
    break;
    default:
        return -1;
    }

    if (item) {
        item->setZValue(graphics_shape_.thickness);
        std::lock_guard<std::mutex> lock(_image_mutex);
        _graphics_shapes.push_back(std::shared_ptr<QGraphicsItem>(item));
    }

    return 0;
}
int ViewerGraphicsViewImpl::ClearGraphicsShapes()
{
    std::lock_guard<std::mutex> lock(_image_mutex);
    for (auto& shape : _graphics_shapes) {
        scene->removeItem(shape.get());
    }
    _graphics_shapes.clear();
    return 0;
}
int ViewerGraphicsViewImpl::ClearImage(int key_)
{
    std::lock_guard<std::mutex> lock(_image_mutex);
    if (_key_and_images.empty())
    {
        return -1;
    }
    if (key_ == -1)
    {
        // 清除所有图像项
        for (auto& [key, item] : _key_and_images)
        {
            scene->removeItem(item.get());
        }
        _key_and_images.clear();
        scene->clear();
        //setEnabled(false);
        return 0;
    }
    else
    {
        auto it = _key_and_images.find(key_);
        if (it != _key_and_images.end())
        {
            auto item = it->second;
            scene->removeItem(item.get());
            _key_and_images.erase(it);
            return 0;
        }
        else
        {
            return -1;
        }
    }
}
int ViewerGraphicsViewImpl::ShowImageByKey(int key_)
{
    int res = -1;
    bool is_any_image_shown = false;
    // 先隐藏所有图像
    for (auto& [temp_key_, temp_item_] : _key_and_images)
    {

        if (temp_key_ != key_ && temp_item_->isVisible())  // 只有已显示的图像才隐藏
        {
            temp_item_->setVisible(false);
        }
        else if (temp_key_ == key_ && !temp_item_->isVisible())
        {
            temp_item_->setVisible(true);
            is_any_image_shown = true;
            res = 0;  // 成功显示图像
        }
    }

    // 如果有图像被显示，触发场景更新
    ResetToOriginalScale();
    if (is_any_image_shown)
    {
        scene->update();
    }
    return res;
}

void ViewerGraphicsViewImpl::resizeEvent(QResizeEvent* event)
{
    if (!scene || scene->items().isEmpty())
    {
        QGraphicsView::resizeEvent(event);
        return;
    }
    auto view_size = this->size();
    auto delta_size = event->size() - view_size;
    translate(delta_size.width(), delta_size.height());
}

void ViewerGraphicsViewImpl::SlotMoveCamera(const double& x_, const double& y_)
{
    centerOn(-x_, y_);
    //PrintCenterPoint();
}

void ViewerGraphicsViewImpl::SlotSetZoom(const float& scale_)
{
    auto real_scale = scale_ / this->transform().m11();
    scale(real_scale, real_scale);
    //PrintCenterPoint();
}

int ViewerGraphicsViewImpl::ResetToOriginalScale()
{
    if (!scene || _key_and_images.empty()) {
        return -1;
    }

    // 1. 重置所有变换
    resetTransform();

    // 2. 计算所有可见图像的总边界
    QRectF totalRect;
    for (auto& [key, item] : _key_and_images) {
        if (item->isVisible()) {
            totalRect = totalRect.united(item->sceneBoundingRect());
        }
    }

    if (totalRect.isEmpty()) {
        return -1;
    }

    // 3. 计算最佳缩放比例（带5%边距）
    QSize viewSize = viewport()->size();
    qreal scale = qMin(
        viewSize.width() / totalRect.width(),
        viewSize.height() / totalRect.height()
    ) * 0.95;

    // 4. 应用缩放（此时是安全的，因为已经resetTransform）
    QGraphicsView::scale(scale, scale);

    // 5. 居中显示
    centerOn(totalRect.center());

    return 0;
}


QImage ViewerGraphicsViewImpl::CvMatToQimage(const cv::Mat& src)
{
    switch (src.type())
    {
        // 8-bit, 4 channel
    case CV_8UC4:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_ARGB32);

        return image;
    }

    // 8-bit, 3 channel
    case CV_8UC3:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_RGB888);

        return image.rgbSwapped();
    }

    // 8-bit, 1 channel
    case CV_8UC1:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return image;
    }

    // 32-bit, 1 channel
    case CV_32FC1:
    {
        cv::Mat* imgNormalize = new cv::Mat(src.rows, src.cols, CV_8UC1);
        cv::normalize(src, *imgNormalize, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        QImage image(imgNormalize->data,
            imgNormalize->cols, imgNormalize->rows,
            static_cast<int>(imgNormalize->step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return image;
    }

    default:
        break;
    }

    return QImage();
}
QImage ViewerGraphicsViewImpl::CharToQimage(std::vector<char>& src_)
{
    QImage image;
    unsigned char* _data = reinterpret_cast<unsigned char*>(src_.data());
    image.loadFromData(_data, static_cast<uint>(src_.size()));
    if (image.isNull())
    {
        //
        std::cout << "图片为空,请检查";
    }
    return image;
}
QPixmap ViewerGraphicsViewImpl::CreateCheckerboardBackground(int size)
{
    QPixmap pixmap(size * 2, size * 2);
    pixmap.fill(Qt::white);
    QPainter painter(&pixmap);

    QColor color1("#38a8a8");
    QColor color2("#38a8a8");
    //QColor color1(200, 200, 200);  // 浅灰色
    //QColor color2(255, 255, 255);  // 白色

    painter.fillRect(0, 0, size, size, color1);
    painter.fillRect(size, size, size, size, color1);
    painter.fillRect(size, 0, size, size, color2);
    painter.fillRect(0, size, size, size, color2);

    painter.end();
    return pixmap;
}

void ViewerGraphicsViewImpl::ConvertDepthToGray(cv::Mat& gray_image_, const cv::Mat& depth_image_)
{
    // 检查输入深度图是否为 CV_32FC1 类型
    if (depth_image_.type() != CV_32FC1) {
        return;
    }

    // Step 1: 归一化，将深度图的值归一化到 [0, 255] 的范围
    cv::Mat normalizedDepthImage;
    cv::normalize(depth_image_, normalizedDepthImage, 0, 255, cv::NORM_MINMAX);

    // Step 2: 将归一化后的浮点型图像转换为 8 位图像
    normalizedDepthImage.convertTo(gray_image_, CV_8UC1);
}

void ViewerGraphicsViewImpl::PrintCenterPoint()
{
    if (scene->items().size() > 0)
    {
        auto item = scene->items().first();
        QPointF item_center = item->boundingRect().center();  // 获取场景中心点
        QPointF item_to_scene_center = mapFromScene(item_center);  // 将场景中心点转换为视图中的位置
    }
    QPointF scene_center = scene->sceneRect().center();  // 获取场景中心点
    QPointF view_center = mapFromScene(scene_center);  // 将场景中心点转换为视图中的位置
    QPointF viewCenterInScene = mapToScene(viewport()->rect().center());
}

void ViewerGraphicsViewImpl::wheelEvent(QWheelEvent* event)
{
    // 保存当前鼠标位置在场景中的坐标
    QPointF old_scene_pos = mapToScene(event->position().toPoint());

    // 计算缩放因子
    qreal scaleFactor = (event->angleDelta().y() > 0) ? 1.1 : 0.9;
    scale(scaleFactor, scaleFactor);

    // 调整视图使鼠标位置保持不变
    QPointF new_scene_pos = mapToScene(event->position().toPoint());
    QPointF delta = new_scene_pos - old_scene_pos;
    translate(delta.x(), delta.y());

    event->accept();
}
void ViewerGraphicsViewImpl::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        _last_viewport_pos = event->pos();  // 保存视图坐标
        _is_dragging = true;
        setCursor(Qt::ClosedHandCursor);
        event->accept();
    }
    else {
        QGraphicsView::mousePressEvent(event);
    }
}

void ViewerGraphicsViewImpl::mouseMoveEvent(QMouseEvent* event)
{
    if (_is_dragging) {
        QPoint delta = event->pos() - _last_viewport_pos;

        // 直接调整滚动条，不受缩放影响
        horizontalScrollBar()->setValue(horizontalScrollBar()->value() - delta.x());
        verticalScrollBar()->setValue(verticalScrollBar()->value() - delta.y());

        _last_viewport_pos = event->pos();
        event->accept();
    }
    else {
        QGraphicsView::mouseMoveEvent(event);
    }
}

void ViewerGraphicsViewImpl::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton && _is_dragging) {
        _is_dragging = false;
        setCursor(Qt::ArrowCursor);
        event->accept();
    }
    else {
        QGraphicsView::mouseReleaseEvent(event);
    }
}

//void ViewerGraphicsViewImpl::AddImage(int key_, const QPixmap& pixmap_)
//{
//    //std::lock_guard<std::mutex> lock(_image_mutex);
//    auto pixmap_item = std::make_shared<QGraphicsPixmapItem>(pixmap_);
//    _key_and_images[key_] = pixmap_item;
//    pixmap_item->setZValue(key_);
//    scene->addItem(pixmap_item.get());
//    CenterOn(ItemType::Entirety);
//}
//
//void ViewerGraphicsViewImpl::CenterOn(const ItemType& item_type_)
//{
//    std::shared_ptr < QGraphicsPixmapItem> item = nullptr;
//    if (item_type_ == ItemType::Temperary)
//    {
//        if (_is_show_entirety_img)
//        {
//            //std::lock_guard<std::mutex> lock(_image_mutex);
//            //隐藏整板图片
//            for (auto& [key_temp, item_temp] : _key_and_images)
//            {
//                (void)key_temp;
//                item_temp->hide();
//            }
//            _is_show_entirety_img = false;
//        }
//        if (_temperary_item)
//        {
//            item = _temperary_item;
//        }
//    }
//    else if (item_type_ == ItemType::Entirety)
//    {
//        //<清除临时 item
//        if (_temperary_item)
//        {
//            scene->removeItem(_temperary_item.get());
//            _temperary_item = nullptr;
//        }
//        //std::lock_guard<std::mutex> lock(_image_mutex);
//        if (_is_show_entirety_img || _key_and_images.empty())
//        {
//            return;
//        }
//        item = _key_and_images.at(0);
//        _is_show_entirety_img = true;
//    }
//    if (item)
//    {
//        centerOn(item->boundingRect().center());
//    }
//}

