/*****************************************************************//**
 * @file   viewfactory.h
 * @brief  MVC工厂
 * @details 用于MVC的生产   
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __VIEWFACTORY_H__
#define __VIEWFACTORY_H__

//STD
#include <map>
#include <string>
#include <iostream>
//
 

namespace jrsaoi
{

    enum class VIEWTYPE :int
    { 
        KCONTROLPANEL,
    };
    class ViewFactory
    {
    public:
        ViewFactory () = delete;
       template<class TempModel>
       static std::shared_ptr<TempModel> CreateModel(const std::string& name)
       {
           return std::make_shared<TempModel>(name);
       }
       template<class TempView >
       static TempView* CreateView (const std::string& name)
       {
           return new TempView (name);
       }
       template<class TempController>
       static std::shared_ptr<TempController> CreateController(const std::string& name)
       {
           return std::make_shared<TempController> (name);
       }


    };

}
#endif // !__VIEWFACTORY_H__