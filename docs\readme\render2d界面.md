# Renderer2D

## 简介
- 本项目是一个基于Qt的2D图形渲染器，支持基本的图形绘制、纹理渲染及交互功能
- 本项目使用OpenGL进行图形渲染，使用Qt进行界面开发，使用OpenCV进行图像处理
- 本项目支持多种图形绘制方式，包括矩形、圆形、多边形等
- 本项目支持多种交互方式，包括鼠标拖拽、键盘快捷键等
- 本项目支持多种图形属性设置，包括颜色、线宽等
- 本项目支持多种图形操作，包括复制、粘贴、删除等

## 目录
1. [环境依赖](#环境依赖)
1. [使用说明](#使用说明)
1. [类图](#类图)
1. [目录结构](#目录结构)

## 环境依赖
### 软件环境
- qt 5.15.2
- opencv 4.8.1 (world)
    - cuda 11.3
    - cudnn 8.2.4
- opengl 
- cereal 
- freetype
- uuid_v4
### 硬件环境
- 显卡最低需支持opengl 3.3
    > 显卡支持的opengl版本可通过软件`everest ultimate`查看

## 使用说明
### 键盘快捷键
>  #### 视野
    - `R` : 重置视野
    - `Q` : 放大视野
    - `E` : 缩小视野
    - `W` : 视野向上平移
    - `S` : 视野向下平移
    - `A` : 视野向左平移
    - `D` : 视野向右平移
    - `空格` : 选中图形居中显示
    - `Ctrl + B` : 开/关视野移动限制
    - `Ctrl + M` : 开/关缩略图导航窗口
> #### 图形
    - `Delete` : 删除选中的图形
    - `Ctrl + C` : 复制选中的图形
    - `Ctrl + V` : 粘贴到鼠标位置
    - `Ctrl + F` : 标记为父图形
    - `Ctrl + G` : 选中图形设置为标记父图形的子图形
    - `Ctrl + Z` : 撤销操作
    - `Ctrl + Shift + Z` : 重做操作
    - `Ctrl + Left` : 选中图形左移
    - `Ctrl + Right` : 选中图形右移
    - `Ctrl + Up` : 选中图形上移
    - `Ctrl + Down` : 选中图形下移
    - `Ctrl + 1` : 绘制矩形
    - `Ctrl + 2` : 绘制圆形
    - `Ctrl + 3` : 绘制多边形
### 鼠标操作
    - 鼠标左键拖拽 : 选中图形
    - 鼠标右键拖拽 : 平移视野
    - 鼠标左键点击 : 选中图形
    - 鼠标滚轮 : 缩放视野
    - 鼠标左键点击 + shift : 多边形选择图形
## 类图
```plantuml
@startuml

qt.QOpenGLWidget <|-- glwindow
qt.QWidget <|-- ThumbnailNavigationWidget 
qt.QWidget <|-- RulerWidget 
qt.QWidget <|-- Renderer2DWidget 
qt.QObject <|-- windowsignalemitter 

ThumbnailNavigationWidget o--> qt.QImage

ThumbnailNavigation o--> cv.Mat

GraphicsID *--> std.string

TemporaryControl <|--GraphicsAbstract
GraphicsAttributes <|-- GraphicsAbstract
GraphicsAbstract <|-- RectGraphics
GraphicsAbstract <|-- PolygonGraphics
GraphicsAbstract <|-- CircleGraphics
GraphicsAbstract <|-- TextGraphics

GraphicsAbstract *--> GraphicsID 
GraphicsAbstract *--> DrawSettings

GraphicsManager *--> GraphicsAbstract
GraphicsManager *--> ControlPoint
GraphicsManager *--> LayerConfig
GraphicsManager *--> ResourceVector

RenderAbstract o--> Renderer
RenderAbstract o--> RenderAbstract

qt.QOpenGLShaderProgram <|-- Program
StandardProgram *--> Program

VisualCameraAbstract *-->qt.QVector3D
VisualCameraAbstract *-->qt.QVector4D
VisualCameraAbstract o-->qt.QMatrix4x4

VisualCameraAbstract <|-- VisualCameraTopDown

Renderer o--> qt.QOpenGLContext
Renderer o--> qt.QSurface
Renderer o--> qt.QPaintDevice
Renderer o--> RenderAbstract
Renderer o--> Program
Renderer *--> VisualCameraAbstract

windowinterface o--> windowsignalemitter
windowinterface *--> Renderer
windowinterface <|-- glwindow

Renderer2DManager o--> RenderAbstract
Renderer2DManager *--> Renderer2DWidget
Renderer2DManager *--> ThumbnailNavigationWidget
Renderer2DManager *--> glwindow
Renderer2DManager *--> RulerWidget

CommandAbstract <|-- CommandCreateGraphics
CommandAbstract <|-- CommandDeleteGraphics
CommandAbstract <|-- CommandEditGraphics

CommandManager o--> CommandAbstract

StateManager *-->GraphicsManager
StateManager *-->Renderer2DManager
StateManager *-->CommandManager

Renderer2D *--> StateManager

@enduml
```

## 目录结构

```
│  .gitignore
│  README.md
│  
├─thirdparty
│  ├─cereal
│  │              
│  ├─freetype
│  │              
│  └─uuid_v4
├─core
│  │  CMakeLists.txt
│  │  
├─base
│  ├─include
│  │      debouncer.hpp
│  │      delegate.hpp
│  │      log.h
│  │      qtfileoperation.hpp
│  │      randomhelper.hpp
│  │      resourcedefine.hpp
│  │      systemmonitor.hpp
│  │      
│  └─src
│          log.cpp
│          
├─control
│  ├─include
│  │      commandabstract.hpp
│  │      commandmanager.h
│  │      commandobject.h
│  │      glwindow.h
│  │      graphicsmanager.h
│  │      renderer2dmanager.h
│  │      renderer2dwidget.h
│  │      rulerwidget.h
│  │      statemanager.h
│  │      thumbnailnavigation.h
│  │      thumbnailnavigationwidget.h
│  │      windowsignalemitter.h
│  │      
│  ├─src
│  │      commandmanager.cpp
│  │      commandobject.cpp
│  │      glwindow.cpp
│  │      graphicsmanager.cpp
│  │      renderer2dmanager.cpp
│  │      renderer2dwidget.cpp
│  │      rulerwidget.cpp
│  │      statemanager.cpp
│  │      thumbnailnavigation.cpp
│  │      thumbnailnavigationwidget.cpp
│  │      windowsignalemitter.cpp
│  │      
│  └─ui
│          renderer2dwidget.ui
│          
├─engine
│  ├─include
│  │      painter.h
│  │      program.h
│  │      renderabstract.hpp
│  │      renderbackground.h
│  │      renderconstants.hpp
│  │      renderer.h
│  │      renderfore.h
│  │      rendertest.h
│  │      rendertexture.h
│  │      shadersourceconstants.hpp
│  │      visualcameraabstract.h
│  │      visualcameratopdown.h
│  │      windowinterface.h
│  │      
│  └─src
│          painter.cpp
│          program.cpp
│          renderbackground.cpp
│          renderer.cpp
│          renderfore.cpp
│          rendertest.cpp
│          rendertexture.cpp
│          visualcameraabstract.cpp
│          visualcameratopdown.cpp
│          windowinterface.cpp
│          
├─graphics
│  ├─include
│  │      graphicsalgorithm.h
│  │      graphicsprocess.h
│  │      graphicsserialize.hpp
│  │      
│  └─src
│          graphicsalgorithm.cpp
│          graphicsprocess.cpp
│          
├─out
│  ├─include
│  │      algodefine.hpp
│  │      controlconstants.hpp
│  │      controlpointconstants.hpp
│  │      graphicsabstract.hpp
│  │      graphicsapi.hpp
│  │      graphicsconstants.hpp
│  │      graphicsid.h
│  │      graphicsobject.h
│  │      renderer2d.h
│  │      rvec.hpp
│  │      tempcontrol.hpp
│  │      
│  └─src
│          graphicsid.cpp
│          graphicsobject.cpp
│          renderer2d.cpp
│          
└─unused
        geometry2d.hpp
        hierarchicalobject.hpp
        renderdefine.hpp
        resourcemanager.cpp
        resourcemanager.h
└─test
    │  CMakeLists.txt
    │  
    └─test
        ├─include
        │      DemoTest.h
        │      
        ├─src
        │      DemoTest.cpp
        │      main_test_render.cpp
        │      
        └─ui
                DemoUI.ui
```
