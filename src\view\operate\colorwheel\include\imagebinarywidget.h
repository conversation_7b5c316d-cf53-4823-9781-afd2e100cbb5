/*****************************************************************//**
 * @file   imagebinarycontrol.h
 * @brief  二值化空间
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSIMAGEBINARYCONTROL_H__
#define __JRSIMAGEBINARYCONTROL_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#include <QtWidgets/QWidget>
#pragma warning(pop)
#include "colorparams.h"
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
class HistogramWidget;
class QComboBox;
class QCheckBox;
class RgbColorWheel;
class QVBoxLayout;
class QPushButton;
class QHBoxLayout;
class HeightWheel;
class QTabWidget;
class GrayWheel;
class BinaryAlgo;
class ColorWheelCtrl;

using cv::Mat;
using Mat3 = std::vector<Mat>;
using ThresholdParamsChangedFunc = 
std::function<void(BinProcessParams& params, 
	cv::Mat& bin_result)>;
class ImageBinaryControl : public QWidget
{
    Q_OBJECT

public:
	/**
    * @fun  ImageBinaryControl
    * @brief  构造函数
    * @date   2024.08.18
    * <AUTHOR>
    */
    explicit ImageBinaryControl(QWidget *parent = nullptr);
	/**
    * @fun  SetTestMat
    * @brief  设置传入图像
    * @param  image 图像
    * @param  mode 灰度类型
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetTestMat(const cv::Mat& image,int mode);
	/**
    * @fun  SetHeightMat
    * @brief  设置高度数据
    * @param  mat 高度数据
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetHeightMat(const cv::Mat& mat);
	/**
    * @fun  SetTestParams
    * @brief  传入参数
    * @date   2024.08.18
    * <AUTHOR>
    */
    int SetTestParams(BinProcessParams& params,bool is_origin_image);
	/**
    * @fun  GetWindowHandle
    * @brief  获取窗口句柄
    * @date   2024.08.18
    * <AUTHOR>
    */
    QWidget* GetWindowHandle();
	/**
    * @fun  ShowWindow
    * @brief  显示窗口
    * @date   2024.08.18
    * <AUTHOR>
    */
    int ShowWindow();
	/**
    * @fun  SetThresholdValChangedCallback
    * @brief  设置回调函数
    * @date   2024.08.18
    * <AUTHOR>
    */
    int SetThresholdValChangedCallback(ThresholdParamsChangedFunc func);

    int GetBinaryControlParams(BinProcessParams& params);

    /**
    * @fun  RestoreColorWheel
    * @brief  恢复色盘
    * @date   2025.04.24
    * <AUTHOR>
    */
    void RestoreColorWheel();


    ~ImageBinaryControl();

signals:
	/**
    * @fun  UpdateGrayImage
    * @brief  更新灰度图像
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateGrayImage(const cv::Mat& gray);
	/**
    * @fun  UpdateRgbHist
    * @brief  更新rgb竖向柱状图
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateRgbHist(std::vector<float>& r_hist,
        std::vector<float>& g_hist,
        std::vector<float>& b_hist);
	/**
    * @fun  UpdateGrayHist
    * @brief  更新灰度竖向柱状图
    * @date   2024.08.18
    * <AUTHOR>
    */
	void UpdateGrayHist(std::vector<float>& gray_hist);
	/**
    * @fun  UpdateHeightHist
    * @brief  更新高度竖向柱状图
    * @date   2024.08.18
    * <AUTHOR>
    */
	void UpdateHeightHist(std::vector<float>& height_hist);
private slots:
	/**
    * @fun  OnTabChanged
    * @brief  二值化方式
    * @date   2024.08.18
    * <AUTHOR>
    */
    void OnTabChanged(int index);
	/**
    * @fun  SetGrayTypeIndex
    * @brief  灰度化方式
    * @date   2024.08.18
    * <AUTHOR>
    */
	void SetGrayTypeIndex(int index);
	///**
 //   * @fun  SetHeightHistParamsSlot
 //   * @brief  高度数据
 //   * @date   2024.08.18
 //   * <AUTHOR>
 //   * @note 2025.01.07 弃用
 //   */
 //   void SetHeightHistParamsSlot(int bin, float min_hei, float max_hei);
	/**
    * @fun  UpdateRThreSlots
    * @brief  更新R
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateRThreSlots(int min, int max);
	/**
    * @fun  UpdateRThreSlots
    * @brief  更行G
    * @date   2024.08.18
    * <AUTHOR>
    */
	void UpdateGThreSlots(int min, int max);
	/**
    * @fun  UpdateRThreSlots
    * @brief 更新B
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateBThreSlots(int min, int max);
	/**
    * @fun  UpdateRThreSlots
    * @brief  更行Gray
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateGrayThreSlots(int min, int max);
	///**
 //   * @fun  UpdateRThreSlots
 //   * @brief  更新Height
 //   * @date   2024.08.18
 //   * <AUTHOR>
 //   * @note 2025.01.07 弃用
 //   */
 //   void UpdateHeightThreSlots(int min, int max);
    /**
    * @fun  SetHeightHistParamsSlot
    * @brief  高度数据
    * @date   2024.08.18
    * <AUTHOR>
    * @note 2025.01.07 弃用
    */
    void SetHeightHistParamsSlot(int bin, float min_hei, float max_hei, int min_thre, int max_thre,bool is_height_thre);
	/**
    * @fun  UpdateRThreSlots
    * @brief  更行hsv参数
    * @date   2024.08.18
    * <AUTHOR>
    */
    void UpdateHsvParamsSlots(ColorWheelThreshVal params);
    /**
     * @brief 查找满足特定条件的索引
     *
     * 该函数首先找出vector中最大的两个数的索引，取其中较小的索引值，
     * 然后每次减10直到找到对应值小于10.0f的位置
     *
     * @param values 输入的浮点数向量
     * @return int 满足条件的索引值，如果未找到返回-1
     */
    int FindSpecialIndex(const std::vector<float>& values);
private:
	int height_bin_nums_ = 2000;
	int gray_type_index_ = 0;
    BinProcessParams params_;

private:
    QVBoxLayout* main_layout = nullptr;
	QTabWidget* algo_type_tab_ = nullptr;
    RgbColorWheel* rgb_color_wheel_ = nullptr;   
    GrayWheel* gray_wheel = nullptr;
    HeightWheel* height_wheel_ = nullptr;   
    BinaryAlgo* binary_algo = nullptr;
    ColorWheelCtrl* hsv_color_wheel = nullptr;
	ThresholdParamsChangedFunc threshold_params_changed_func_;

    Mat image_; // 待调配颜色图
    Mat height_mat_; // 高度数据图
    Mat result_; // 结果图
    bool m_is_origin_image; // 是否为初始图像
	void SetupUi();
    void SetupConnect();
};
#endif
