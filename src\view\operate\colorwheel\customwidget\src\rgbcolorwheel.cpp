#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
//QT
#include <QtWidgets/qboxlayout.h>
#include <QComboBox>
#include <QCheckBox>
#include <QtWidgets/qpushbutton.h>
#include <QWidget>
#include <QLineEdit>
#include <QPainter>
#include <QMouseEvent>
#include <QHBoxLayout>

//STD
#include <vector>
#include <algorithm>
#include <iostream>
//Custom
#include"rgbcolorwheel.h"
#pragma warning(pop)


RgbColorWheel::~RgbColorWheel()
{
}
void RgbColorWheel::SetRThreSlot(int min_thre, int max_thre)
{
	r_histogramwidget->SetHistThre(min_thre, max_thre);
	emit SetRThre(min_thre, max_thre);
}
void RgbColorWheel::SetGThreSlot(int min_thre, int max_thre)
{
	g_histogramwidget->SetHistThre(min_thre, max_thre);
	emit SetGThre(min_thre, max_thre);
}
void RgbColorWheel::SetBThreSlot(int min_thre, int max_thre)
{
	b_histogramwidget->SetHistThre(min_thre, max_thre);
	emit SetBThre(min_thre, max_thre);
}
RgbColorWheel::RgbColorWheel(QWidget* parent) 
    : QWidget(parent)
{
	r_histogramwidget = new CustomPlotWidget(256, Qt::red);
	r_histogramwidget->xAxis->setRange(0, 255);
	g_histogramwidget = new CustomPlotWidget(256, Qt::green);
	g_histogramwidget->xAxis->setRange(0, 255);
	b_histogramwidget = new CustomPlotWidget(256, Qt::blue);
	b_histogramwidget->xAxis->setRange(0, 255);

	/*r_histogramwidget->setMaximumHeight(50);
	g_histogramwidget->setMaximumHeight(50);
	b_histogramwidget->setMaximumHeight(50);*/
	r_histogramwidget->setContentsMargins(0, 0, 0, 0);
	g_histogramwidget->setContentsMargins(0, 0, 0, 0);
	b_histogramwidget->setContentsMargins(0, 0, 0, 0);

	QVBoxLayout* layout = new QVBoxLayout();
	layout->setContentsMargins(0, 0, 0, 0);
	layout->setSpacing(0); 

	layout->addWidget(r_histogramwidget);
    layout->addWidget(g_histogramwidget);
    layout->addWidget(b_histogramwidget);
	layout->addStretch(1);
	this->setLayout(layout);

	connect(r_histogramwidget, &CustomPlotWidget::UpdateThre, this,
		&RgbColorWheel::SetRThreSlot);
	connect(g_histogramwidget, &CustomPlotWidget::UpdateThre, this,
		&RgbColorWheel::SetGThreSlot);
	connect(b_histogramwidget, &CustomPlotWidget::UpdateThre, this,
		&RgbColorWheel::SetBThreSlot);
}
void RgbColorWheel::SetRgbHistValue(std::vector<float>& r_hist,
	std::vector<float>& g_hist,
	std::vector<float>& b_hist)
{
	r_histogramwidget->SetHistValue(r_hist);
	g_histogramwidget->SetHistValue(g_hist);
	b_histogramwidget->SetHistValue(b_hist);
}