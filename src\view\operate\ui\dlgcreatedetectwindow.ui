<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DlgCreateDetectWindow</class>
 <widget class="QDialog" name="DlgCreateDetectWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>438</width>
    <height>189</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QWidget" name="widget" native="true">
       <layout class="QGridLayout" name="gridLayout_2" rowstretch="0,0,0,0,0" columnstretch="1,2,1,2">
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>算法类型：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QComboBox" name="comboBox_algo_name_list"/>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="comboBox_algo_type_list"/>
        </item>
        <item row="0" column="2">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>算法名称：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QComboBox" name="comboBox_defect_type_list"/>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>缺陷类型：</string>
          </property>
         </widget>
        </item>
        <item row="4" column="3">
         <widget class="QComboBox" name="comboBox_sub_win_type"/>
        </item>
        <item row="4" column="2">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>子检测框类型：</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QCheckBox" name="checkBox_add_sub_win">
          <property name="text">
           <string>子检测框</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QDialogButtonBox" name="buttonBox">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="standardButtons">
        <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DlgCreateDetectWindow</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DlgCreateDetectWindow</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
