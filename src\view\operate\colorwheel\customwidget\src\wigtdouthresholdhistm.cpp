
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "wigtdouthresholdhistm.h"
#include "wigtcolorwheel.h"
#pragma warning(pop)

WigtDouThresholdHistm::WigtDouThresholdHistm(QWidget *parent)
    : QWidget{parent}
{
    histogram = new QCustomPlot(this);
    histogram->setBackground(QBrush(QColor(0,0,0,0)));
    histogram->xAxis->setRange(0, 255);
    histogram->yAxis->setRange(0,1.0);
    selected_bars = new QCPGraph(histogram->xAxis, histogram->yAxis);
    no_selected_bars_left = new QCPGraph(histogram->xAxis, histogram->yAxis);
	no_selected_bars_right = new QCPGraph(histogram->xAxis, histogram->yAxis);

    selected_bars->setPen(QPen(QColor(0, 0, 0)));
    selected_bars->setBrush(QColor(0, 0, 0));

    no_selected_bars_left->setPen(QPen(QColor(125, 128, 128)));
    no_selected_bars_left->setBrush(QColor(128, 128, 128));
	no_selected_bars_right->setPen(QPen(QColor(125, 128, 128)));
    no_selected_bars_right->setBrush(QColor(128, 128, 128));

}

void WigtDouThresholdHistm::UpdateHistogram()
{
    QVector<double> no_selected_keys_left, no_selected_keys_right, selected_keys;
    QVector<double> no_selected_vals_left, no_selected_vals_right, selected_vals;

    auto max_val = std::max_element(m_hist_values.begin(), m_hist_values.end());
    double yaxis_max = (*max_val + 0.1 > 1.0) ? 1.0 : *max_val + 0.1;

    histogram->yAxis->setRange(0.0, yaxis_max);

    if (m_hist_values.empty()) return;
    for (qreal i = 0; i < 255; i++)
    {
        if (m_left_val <= i && i <= m_right_val)
        {
            selected_keys.push_back(i);
            selected_vals.push_back(m_hist_values[unsigned __int64(i)]);
        }
        else if(i < m_left_val)
        {
			if(i==0) continue;
            no_selected_keys_left.push_back(i);
            no_selected_vals_left.push_back(m_hist_values[unsigned __int64(i)]);
        }
        else if (i > m_right_val)
        {
			no_selected_keys_right.push_back(i);
            no_selected_vals_right.push_back(m_hist_values[unsigned __int64(i)]);
        }
    }
    selected_bars->setData(selected_keys, selected_vals);
    no_selected_bars_left->setData(no_selected_keys_left, no_selected_vals_left);
	no_selected_bars_right->setData(no_selected_keys_right, no_selected_vals_right);

    histogram->replot();
}



void WigtDouThresholdHistm::SetHistValues(const std::vector<float>& hist_values)
{
    m_hist_values = hist_values;
    UpdateHistogram();
}

void WigtDouThresholdHistm::SetClickState()
{
    on_click = true;
    emit UpdateClickState(on_click);
}

void WigtDouThresholdHistm::SetThresholdVals(const float& left_val, const float& right_val)
{
    m_left_val = int(std::max(left_val * 255 - (1E-3), 0.0));
    m_right_val = int(std::min(right_val * 255 + (1E-3), 255.0));

    UpdateHistogram();
    emit ThresholdChanged(m_left_val, m_right_val);
}

void WigtDouThresholdHistm::resizeEvent(QResizeEvent */*event*/)
{
    histogram->setGeometry(0, 0, rect().width() - 10, int(rect().height() * 0.8));
    /*double_slider->setGeometry(0, rect().height() * 0.8,
        rect().width(), rect().height() * 0.2);*/
}

void WigtDouThresholdHistm::mouseDoubleClickEvent(QMouseEvent* event)
{
	Q_UNUSED(event);
    emit ThrDoubleClickedSignal();
}

void WigtDouThresholdHistm::HandleSingleClicked()
{
    on_click = true;
}
