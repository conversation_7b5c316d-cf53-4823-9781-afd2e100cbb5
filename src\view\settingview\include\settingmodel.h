/*****************************************************************//**
 * @file   standardmodel.h
 * @brief  标准图片model类
 * @details    
 * <AUTHOR>
 * @date 2024.4.12
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.4.12         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/


#ifndef __JRS_SYSPARAMODEL_H__
#define __JRS_SYSPARAMODEL_H__

//Custom
//STD
#include <mutex>

#include "modelbase.h"
namespace jrsaoi
{
    class SettingModel : public ModelBase
    {
    public:
        SettingModel(const std::string& name_);
        ~SettingModel();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;
        const jrsdata::SettingViewParamPtr& GetModelData();
    private:
        jrsdata::SettingViewParamPtr _param_data;
        std::mutex mutex_data;
    };
    using SettingModelPtr = std::shared_ptr<SettingModel>;
}

#endif // !__JRS_STANDARDMODEL_H__
