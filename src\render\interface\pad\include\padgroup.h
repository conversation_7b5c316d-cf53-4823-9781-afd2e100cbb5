﻿#ifndef PAD_GROUP_H
#define PAD_GROUP_H
/*****************************************************************
 * @file   padgroup.h
 * @brief   pad一组的概念，组内进行对齐等操作
 * @details
 * <AUTHOR>
 * @date 2025.2.24
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.2.24          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
#include "graphicsapi.hpp"
#include "graphicsabstract.hpp"
#include "transientobjectcontrol.hpp"
#include "rvec.hpp"
#include "tools.hpp"

class PadManager;
class GRAPHICS_API PadGraphicsGroup : public GraphicsAbstract, public TransientObjectControl<PadGraphicsGroup>
{
public:
    enum class PadDirection /**< 外加表示pad类型*/
    {
        UNKNOWN,
        UP,             /**< 上 */
        RIGHT,          /**< 右 */
        DOWN,           /**< 下 */
        LEFT,           /**< 左 */
        INSIDE,         /**< 内部 矩阵 */
    };
    /** TODO: move to PadManager HJC */
    enum class PadGroupType
    {
        UNKNOWN,
        SINGLE,         /**< 单个*/
        MIRROR,         /**< 镜像*/
        ARRAY,          /**< 阵列*/
        MATRIX,         /**< 矩阵*/
    };
    /**
     * @brief 构造,因为缺少构造参数,因此只能通过管理器的函数构造
     */
    PadGraphicsGroup(PadManager* pad_manager_ptr_, std::weak_ptr<GraphicsAbstract> parent_ = {});

    PadGraphicsGroup& operator=(const PadGraphicsGroup& other);

    GraphicsFlag GetFlag() const override { return GraphicsFlag::pad_group; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    void Update() override;
    void UpdateDrawBuffer() override;
    void UpdateControlPoint()  override;

    /** < 阵列创建pad实际个数 */
    int SetArrayPadsNumber(int number_);


    std::shared_ptr<GraphicsAbstract> Clone();
    void DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config) override;
    int TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>&, const TryResponseEventParam&) override;
    int ResponseControlPoint(const ResponseEventParam&) override;
    int ResponseEvent(const MouseEventValue&) override;


    //void SetParentGraphics(std::shared_ptr<GraphicsAbstract> parent_);
    void AddSubGraphics(std::shared_ptr<GraphicsAbstract> sub_);
    void AddSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_);
    void RemoveSubGraphics(std::shared_ptr<GraphicsAbstract> sub_);
    void RemoveSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_);

    void CreateSubGraphics();
    void DeleteSubGraphics();

    void DoAlign(bool is_append_gh_name_ = true);

    std::string GetPadGroupUniqueName();

    /** < 获取本组内的其余 pad 信息    */
    std::vector<std::shared_ptr<GraphicsAbstract>> GetExceptSelectedPads();
    /** < 获取 相同组名其他组的pad信息 */
    std::vector<std::shared_ptr<GraphicsAbstract>> GetExceptSelectedPadGroups();

    bool RemoveSelectSubGraphics();

    /** <当前选择的pad 是否时最后一个pad */
    bool IsSelectedLastPad();

    /** < 更新大组名 */
    void UpdatePadGroupName(const std::string& group_name_);

private:
    void UpdateBoundingBox();
    void UpdateState();
    void PrintCurrentPadGroupInfo(const std::string& function_name_, int line_);
    PadDirection RotateClockwise(PadDirection direction_, float angle_);
    /**
     * @fun FindMatchingPadGroup
     * @brief 查找匹配的组
     * @return
     * <AUTHOR>
     * @date 2025.3.24
     */
    std::shared_ptr<PadGraphicsGroup> FindMatchingPadGroup(bool is_append_gh_name_ = true);
private:
    /** 将pad更新到工程  */
    void UpdateProjectExceptGraphicsVec(const GraphicsPtrVec& except_ghs_ = {});
    /** < 阵列排列 */
    void DoAlignArray();
    /** < 镜像对称 */
    void DoAlignMirrorSymmetry(bool is_append_gh_name_);
    /** < 矩阵排列 */
    void DoAlignMatrix();
    /** < 自动识别方向  */
    void AutoIdentifyDirection();

    /**< 将元件角度变换成0度 */
    void ParentAngleToZeroAngle();
    /**< 将元件角度变换成0度 */
    void ZeroAngleToParentAngle();

    /**
     * @fun GetPadDirection
     * @brief 自动获取方向
     * @param angle
     * @return
     * <AUTHOR>
     * @date 2025.3.30
     */
    PadGraphicsGroup::PadDirection  GetPadDirection(double angle_, double width_, double height_);
    AUTO_PROPERTY(PadDirection, direction, Direction);          /**< 方向*/
    AUTO_PROPERTY(PadGroupType, pad_group_type, GroupType);  /**< 组类型*/
    AUTO_PROPERTY(std::string, pad_group_name, PadGroupName);  /**< group_name 并不唯一，可以有多个同名的*/
    AUTO_PROPERTY(int, pad_group_id, PadGroupID);  /**< group_id 再同一group_name 唯一，可以有多个同名的，不用group_name不唯一*/
    AUTO_PROPERTY(std::weak_ptr<GraphicsAbstract>, parent_graphics, ParentGraphicsPtr);  /**< group_id 再同一group_name 唯一，可以有多个同名的，不用group_name不唯一*/
    std::vector<Vec2> _paths;  ///< 顶点缓存
    PadManager* const _pad_manager_ptr; ///< 图形管理器
    std::vector<std::weak_ptr<GraphicsAbstract>>  _sub_graphics; ///< 子图形
    bool _is_align; ///< 是否对齐
};

#endif //! GRAPHICSTOOLS_H