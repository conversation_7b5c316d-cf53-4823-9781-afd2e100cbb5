﻿#include "renderer.h"
#include "shaderprogram.h"
#include "renderabstract.hpp" // RenderAbstract
#include "renderconstants.hpp"
#include "visualcameraabstract.h"
#include "visualcameratopdown.h"

#include "log.h"
// #include "ropenglfunctions.hpp" // ROpenGLFunctions
#include "windowinterface.h" // WindowInterface

#include <QOpenGLShaderProgram>
#include <QOpenGLFunctions>
#include <QOpenGLContext>
// #include <sstream>

static GLenum PrimTypeToGL(PrimType type)
{
    switch (type)
    {
    case PrimType::PT_Points:
        return GL_POINTS;
    case PrimType::PT_Lines:
        return GL_LINES;
    case PrimType::PT_Triangles:
        return GL_TRIANGLES;
    case PrimType::PT_Line_Strip:
        return GL_LINE_STIPPLE;
    default:
        break;
    }
    return GL_TRIANGLES;
}

void PaintDeviceRAII::PushAttribute()
{
    auto func = qGLContext->versionFunctions<ROpenGLFunctions>();
    func->glGetIntegerv(GL_BLEND, &Blend);
    func->glGetIntegerv(GL_BLEND_DST_ALPHA, &BlendDstAlpha);
    func->glGetIntegerv(GL_BLEND_DST_RGB, &BlendDstRGB);
    func->glGetIntegerv(GL_BLEND_EQUATION_ALPHA, &BlendEquationAlpha);
    func->glGetIntegerv(GL_BLEND_EQUATION_RGB, &BlendEquationRgb);
    func->glGetIntegerv(GL_BLEND_SRC_ALPHA, &BlendSrcAlpha);
    func->glGetIntegerv(GL_BLEND_SRC_RGB, &BlendSrcRGB);
}

void PaintDeviceRAII::PopAttribute()
{
    auto func = qGLContext->versionFunctions<ROpenGLFunctions>();
    func->glBlendEquationSeparate(BlendEquationRgb, BlendEquationAlpha);
    func->glBlendFuncSeparate(BlendSrcRGB, BlendDstRGB, BlendSrcAlpha,
        BlendDstAlpha);
    EnableAttribute(func, GL_BLEND, Blend);
}

Renderer::Renderer() :
    m_program(nullptr), camera(nullptr), main_context(nullptr),
    m_surface(nullptr), m_device(nullptr),
    engine(nullptr),
    prim_type(PrimType::PT_Points),
    gl_line_width(1), gl_point_size(1),
    current_vertex_count(0), current_index_count(0), current_texture_count(0),
    texture_buffer(), vertex_buffer(), index_buffer()
{
}

Renderer::~Renderer()
{
    // if (camera)
    // {
    //     delete camera;
    //     camera = nullptr;
    // }
}

void Renderer::Update()
{
    // draw_call_num = 0;
    // m1 = 0;
    // m2 = 0;
    // m3 = 0;

    //auto func = main_context->versionFunctions<ROpenGLFunctions>();
    // func->glLineWidth(1);

    // m_program->bind();
    // if (camera)
    // {
    //     m_program->SetUniformValue("view", *(camera->GetViewMatrix()));
    //     m_program->SetUniformValue("projection", *(camera->GetProjectionMatrix()));
    // }
    // else
    // {
    //     m_program->SetUniformValue("view", QMatrix4x4());
    //     m_program->SetUniformValue("projection", QMatrix4x4());
    // }

    for (size_t i = 0; i < objects.size(); ++i)
    {
        objects[i]->Render();
    }
    this->Flush();

    // m_program->release();

    // printInfo((std::stringstream() << "draw_call :" << draw_call_num).str().c_str());
    // printInfo((std::stringstream() << "m1 :" << m1 << " m2 :" << m2 << " m3 :" << m3).str().c_str());
}

void Renderer::Destroy()
{
    for (size_t i = 0; i < objects.size(); ++i)
    {
        objects[i]->Destroy();
    }
}

void Renderer::SetContext(QSurface* surface, QOpenGLContext* mainContext)
{
    main_context = mainContext;
    m_surface = surface;

    // /*查看纹理加载限制*/
    GLint maxtexture;
    main_context->makeCurrent(m_surface);
    auto func = main_context->versionFunctions<ROpenGLFunctions>();
    func->glGetIntegerv(GL_MAX_TEXTURE_SIZE, &maxtexture);
    //printInfo((std::stringstream() << "max texture size: " <<
     //   maxtexture).str().c_str());
}

void Renderer::SetQPaintDevice(QPaintDevice* device) { m_device = device; }

PaintDeviceRAII Renderer::GetPaintDeviceRAII() const
{
    return PaintDeviceRAII(m_device, main_context);
}

void Renderer::SetProgram(ShaderProgram* p)
{
    if (m_program == p)
        return;

    if (m_program)
        this->Flush();
    m_program = p;
}

void Renderer::SetProgram(const ProgramType& type)
{
    if (m_program && m_program->GetProgramType() == type)
        return;
    if (m_program)
        this->Flush();
    if (!engine)
        return;
    engine->RenderSetProgram(this, type);
    // m_program = StandardProgram::GetInstance().GetProgram(type);
}

void Renderer::SetCamera(VisualCameraAbstract* c)
{
    // if (!window) return;
    // auto context = window->GetOpenGLContext();
    // if (context && context->isValid())
    //{
    camera = c;
    //    this->Flush();
    //    //window->DoMakeCurrent();
    //    //auto func = context->versionFunctions<ROpenGLFunctions>();
    //}
}

double Renderer::GetZoom() const
{
    if (!camera)
        return 0;
    return camera->GetZoom();
}

void Renderer::SetViewport(double width, double height)
{
    if (!camera)
        return;
    camera->SetViewport(width, height);
}

void Renderer::GetViewport(double& width, double& height) const
{
    if (!camera)
        return;
    camera->GetViewport(width, height);
}

bool Renderer::SetCanvasSize(int width, int height)
{
    if (!camera)
        return false;
    auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
    if (!topdown)
        return false;
    return topdown->SetCanvas(width, height);
}

bool Renderer::GetCanvasSize(int& width, int& height) const
{
    if (!camera)
        return false;
    auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
    if (!topdown)
        return false;
    topdown->GetCanvas(width, height);
    return true;
}

void Renderer::AddObject(RenderAbstractPtr ro)
{
    ro->SetRenderer(this);
    objects.emplace_back(std::move(ro));
}

void Renderer::RemoveObject(RenderAbstractPtr ro)
{
    if (!ro)
        return;
    ro->SetRenderer(nullptr);
    objects.erase(std::remove_if(objects.begin(), objects.end(),
        [ro](const RenderAbstractPtr& p)
        {
            return p.get() == ro.get();
        }),
        objects.end());
}

/**
 * Qt 屏幕坐标系
 *
 *       +X →
 * +Y ↓ ┌───────┐
 *      │(0,0)  │
 *      │       │
 *      └───────┘
 *
 * OpenGL 标准设备坐标系 (NDC)
 *
 *        +Y
 *         ↑ +1
 *  -1     │     +1
 *   ────(0,0)────→ +X
 *         │
 *         ↓ -1
 */
void Renderer::SetEngine(WindowInterface* engine_)
{
    engine = engine_;
}

bool Renderer::MouseToWorld(float& x, float& y) const
{
    if (!camera)
        return false;
    double w, h;
    camera->GetViewport(w, h);
    if (w <= 1 || h <= 1)
        return false;
    const float sw = static_cast<float>(w) * 0.5f;
    const float sh = static_cast<float>(h) * 0.5f;

    // NDC坐标
    x = (x - sw) / sw;
    y = (sh - y) / sh;

    QVector4D vec(x, y, 1.0, 1.0); // 未启用透视投影所以不使用深度
    // 反向变换
    // vec = ((*projectionmatrix) * (*viewmatrix)).inverted() * vec;
    vec = (*camera->GetProjectionMatrix() * *camera->GetViewMatrix()).inverted() * vec;
    // vec /= vec.w(); // 透视除法,仅在透视投影下有效

    x = vec.x();
    y = -vec.y(); // 像素y轴反向
    return true;
}

bool Renderer::WorldToMouse(float& x, float& y) const
{
    if (!camera)
        return false;
    double w, h;
    camera->GetViewport(w, h);
    if (w <= 1 || h <= 1)
        return false;
    const float sw = static_cast<float>(w) * 0.5f;
    const float sh = static_cast<float>(h) * 0.5f;

    QVector4D vec(x, -y, 1.0, 1.0); // 未启用透视投影所以不使用深度
    vec *= vec.w();                 // 透视除法的反操作,仅在透视投影下有效,加上不会影响效率就不删除了
    // vec = ((*projectionmatrix) * (*viewmatrix)) * vec;
    vec = (*camera->GetProjectionMatrix() * *camera->GetViewMatrix()) * vec;

    // NDC坐标
    x = vec.x();
    y = vec.y();

    x = x * sw + sw;
    y = -y * sh + sh; // 注意y轴方向相反
    return true;
}

bool Renderer::CreateTexture(unsigned int& texture_id,
    const unsigned char* const data, int width,
    int height, unsigned int format)
{
    if (!main_context || !m_surface)
        return false;
    if (!data)
        return false;
    if (width < 0 || height < 0)
        return false;
    if (format < GL_COLOR_INDEX || format > GL_LUMINANCE_ALPHA)
        return false;

    int internalFormat = GL_RGB;
    switch (format)
    {
    case GL_RGB:
    {
        internalFormat = GL_RGB;
    }
    break;
    case GL_RGBA:
    {
        internalFormat = GL_RGBA;
    }
    break;
    case GL_RG:
    {
        internalFormat = GL_RG;
    }
    break;
    case GL_RED:
    {
        internalFormat = GL_LUMINANCE;
    }
    break;
    default:
        return false;
    }

    {
        main_context->makeCurrent(m_surface);
        auto func = main_context->versionFunctions<ROpenGLFunctions>();

        func->glGenTextures(1, &texture_id);
        func->glBindTexture(GL_TEXTURE_2D, texture_id); // 绑定到对应纹理单元
        func->glPixelStorei(GL_UNPACK_ALIGNMENT, 1);    // 1字节像素对齐

        /*判断是否能够加载当前纹理到内存空间*/
        func->glTexImage2D(GL_PROXY_TEXTURE_2D, 0, internalFormat, width, height, 0, format, GL_UNSIGNED_BYTE, NULL);
        func->glGetTexLevelParameteriv(GL_PROXY_TEXTURE_2D, 0, GL_TEXTURE_WIDTH, &width);
        func->glGetTexLevelParameteriv(GL_PROXY_TEXTURE_2D, 0, GL_TEXTURE_HEIGHT, &height);
        if (width <= 0 || height <= 0)
        {
            printInfo(std::string("缺少足够资源加载2D纹理").c_str());
            return false;
        }

        /*为当前绑定的纹理对象设置环绕、过滤方式*/
        if (internalFormat == GL_RGBA)
        {
            func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
            func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        }
        else
        {
            func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
            func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
        }
        func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_NEAREST);
        func->glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
        func->glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width, height, 0, format, GL_UNSIGNED_BYTE, data); // 定义二维纹理
        func->glGenerateMipmap(GL_TEXTURE_2D);                                                                  // 生成多级渐远纹理
        func->glPixelStorei(GL_UNPACK_ALIGNMENT, 4);                                                            // 恢复默认像素对齐
        // printInfo(std::to_string(func->glGetError()).c_str());
    }
    return true;
}

void Renderer::DeleteTexture(unsigned int texture_id)
{
    // TODO
    // context被提前释放,没办法在这里获取
    // return;

    main_context->makeCurrent(m_surface);
    auto func = main_context->versionFunctions<ROpenGLFunctions>();
    func->glDeleteTextures(1, &texture_id);

    // auto context = window->GetOpenGLContext();
    // if (context && context->isValid())
    //{
    //     window->DoMakeCurrent();
    //     auto func = context->versionFunctions<ROpenGLFunctions>();
    //     func->glDeleteTextures(1, &texture_id);
    // }
}

void Renderer::AppendRenderData(SpriteVertexFormat* vertex_data,
    int vertex_count, uint32* index_data,
    int index_count, unsigned int texture,
    PrimType type)
{
    int texture_index = this->SetTexture(texture);
    for (int i = 0; i < vertex_count; i++)
    {
        vertex_data[i].texindex = texture_index;
    }
    this->AppendRenderData(vertex_data, vertex_count, index_data, index_count,
        type);
}

void Renderer::SetLineWidth(int width)
{
    if (gl_line_width == width)
        return;
    if (!main_context || !m_surface)
        return;
    this->Flush();
    gl_line_width = width;
    main_context->makeCurrent(m_surface);
    auto func = main_context->versionFunctions<ROpenGLFunctions>();
    func->glLineWidth(width);
}

void Renderer::SetPointSize(int size)
{
    if (gl_point_size == size)
        return;
    if (!main_context || !m_surface)
        return;
    this->Flush();
    gl_point_size = size;
    main_context->makeCurrent(m_surface);
    auto func = main_context->versionFunctions<ROpenGLFunctions>();
    func->glPointSize(size);
}

void Renderer::Flush()
{
    if (!m_program)
        return;

    if (current_vertex_count == 0 || current_index_count == 0)
        return;

    //  draw_call_num++;

    {
        m_program->bind();
        if (camera)
        {
            m_program->SetUniformValue("view", *(camera->GetViewMatrix()));
            m_program->SetUniformValue("projection", *(camera->GetProjectionMatrix()));
        }
        else
        {
            m_program->SetUniformValue("view", QMatrix4x4());
            m_program->SetUniformValue("projection", QMatrix4x4());
        }

        this->BindTexture();
        this->DrawElements(vertex_buffer, index_buffer, current_index_count, prim_type);

        m_program->release();
    }
    current_vertex_count = current_index_count = 0;
    current_texture_count = 0;
}

int Renderer::SetTexture(unsigned int texture)
{
    int texture_index = -1;
    /* 查找是否已经保存纹理 */
    for (int i = 0; i < current_texture_count; i++)
    {
        if (texture_buffer[i] == texture)
        {
            texture_index = i;
            break;
        }
    }
    /* 新的纹理？添加到纹理数组 */
    if (texture_index == -1 && current_texture_count < max_texture_num)
    {
        texture_buffer[current_texture_count] = texture;
        texture_index = current_texture_count++;
    }
    /* 纹理数组已满？绘制上一批次数据，插入纹理到纹理数组 */
    if (texture_index == -1)
    {
        this->Flush();
        texture_index = 0;
        texture_buffer[texture_index] = texture;
        current_texture_count = 1;
    }
    return texture_index;
}

void Renderer::BindTexture()
{
    if (current_texture_count <= 0)
        return;
    if (!main_context || !m_surface)
        return;
    if (!m_program)
        return;

    auto func = main_context->versionFunctions<ROpenGLFunctions>();

    for (int i = 0; i < current_texture_count; i++)
    {
        func->glActiveTexture(GL_TEXTURE0 + i);
        func->glBindTexture(GL_TEXTURE_2D, texture_buffer[i]);
        m_program->SetUniformValue(("Texture" + std::to_string(i)).c_str(), i);
    }
}

void Renderer::DrawElements(void* vertex_data, void* index_data, int index_count_, PrimType type)
{
    auto func = main_context->versionFunctions<ROpenGLFunctions>();
    m_program->BindVertexDataToGPU(func, vertex_data);
    func->glDrawElements(PrimTypeToGL(type), index_count_, GL_UNSIGNED_INT, index_data);
    m_program->UnbindVertexDataFromGPU(func);
}

// void Renderer::MouseToCAMERA(float &x, float &y) const
// {
//     MouseToWorld(x, y);
//     y *= -1;
// }

// void Renderer::CAMERAToMouse(float &x, float &y) const
// {
//     y *= -1;
//     WorldToMouse(x, y);
// }
