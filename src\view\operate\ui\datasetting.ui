<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DataSetting</class>
 <widget class="QWidget" name="DataSetting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>508</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>DataSetting</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="1" column="0">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    margin-top: 10px;
    border: 1px solid black;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 5px;
}

</string>
     </property>
     <property name="title">
      <string>设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>85</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>维修站数据库：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_7">
              <property name="text">
               <string>数据库名称：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_2">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>85</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>用户名：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_3">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>85</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>密码：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <widget class="QLineEdit" name="edit_ip"/>
            </item>
            <item>
             <widget class="QLineEdit" name="edit_db_name"/>
            </item>
            <item>
             <widget class="QLineEdit" name="edit_user"/>
            </item>
            <item>
             <widget class="QLineEdit" name="edit_password">
              <property name="echoMode">
               <enum>QLineEdit::PasswordEchoOnEdit</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="btn_db_operate">
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>连接</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="minimumSize">
             <size>
              <width>85</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>85</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>维修站机种：</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_save_all_info">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>保存全部信息</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_6">
            <property name="minimumSize">
             <size>
              <width>85</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>85</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>缩略大图参数:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="compress_img_type">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>.mbin</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>.jpg</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>.png</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QSlider" name="horizontalSlider_compress_rate">
            <property name="minimum">
             <number>10</number>
            </property>
            <property name="maximum">
             <number>100</number>
            </property>
            <property name="sliderPosition">
             <number>10</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="tickPosition">
             <enum>QSlider::NoTicks</enum>
            </property>
            <property name="tickInterval">
             <number>1</number>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1,1">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>检测结果保存：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radio_btn_save_all_detect_result">
            <property name="text">
             <string>保存全部数据</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radio_btn_only_save_ng">
            <property name="text">
             <string>只保存NG数据</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0,0">
          <item>
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>大图保存类型：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="entirety_img_type">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>.png</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>.jpg</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>.mbin</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QPushButton" name="btn_clean">
     <property name="text">
      <string>清空</string>
     </property>
    </widget>
   </item>
   <item row="5" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="4" column="0">
    <widget class="QPushButton" name="btn_read">
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    margin-top: 10px;
    border: 1px solid black;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 5px;
}

</string>
     </property>
     <property name="title">
      <string>机器设置</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="1" column="1">
       <widget class="QLineEdit" name="edit_site_no"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_12">
        <property name="text">
         <string>线体ID：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="edit_company_no"/>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_9">
        <property name="text">
         <string>工单信息：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QLineEdit" name="edit_thread_no"/>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="edit_machine_no"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>站点编号：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>设备ID：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
