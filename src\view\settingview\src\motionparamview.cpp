﻿#include "motionparamview.h"

jrsaoi::MotionParamView::MotionParamView(QWidget* parent)
	:QWidget(parent)
{
}

jrsaoi::MotionParamView::~MotionParamView()
{
}

void jrsaoi::MotionParamView::UpdateView(const jrsdata::MotionParamSettingVec& motion_params_)
{
	if (motion_params_.empty())
	{

	}
}

jrsdata::MotionParamSettingVec jrsaoi::MotionParamView::GetMotionParam()
{
	return jrsdata::MotionParamSettingVec();
}

void jrsaoi::MotionParamView::InitConnect()
{
}

void jrsaoi::MotionParamView::InitMember()
{
}

void jrsaoi::MotionParamView::SaveMotionParam()
{
}

void jrsaoi::MotionParamView::UpdateStaticParam()
{
}

void jrsaoi::MotionParamView::SetHideController(const jrsdata::MotionParamSettingVec& motion_params_)
{
	if (motion_params_.empty())
	{

	}
}
void jrsaoi::MotionParamView::SlotSaveMotionParam()
{

}