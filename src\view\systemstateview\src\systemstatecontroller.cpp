#include "systemstatecontroller.h"
#include "systemstatemodel.h"
#include "systemstateview.h"


jrsaoi::SystemStateController::SystemStateController(const std::string& name)
    :ControllerBase(name)
{

}

jrsaoi::SystemStateController::~SystemStateController()
{

}

int jrsaoi::SystemStateController::Update(const jrsdata::ViewParamBasePtr& param_)
{
    if (_model == nullptr)
    {
        return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
    }
    _model->Update(param_);
    return UpdateView();
}

int jrsaoi::SystemStateController::Save(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    return jrscore::AOI_OK;
}

void jrsaoi::SystemStateController::SetView(ViewBase* view_param)
{
    _view = static_cast<SystemStateView*>(view_param);
    InitConnect();
    UpdateView();
}

void jrsaoi::SystemStateController::SetModel(ModelBasePtr model_param)
{
    _model = std::dynamic_pointer_cast<SystemStateModel>(model_param);
}

void jrsaoi::SystemStateController::SlotInitDevice()
{
    auto data_temp = _model->GetModelData();
    std::thread t([&]() {
        Log_INFO("进入初始化线程！");
        emit SigUpdateSystemState(data_temp);
        });
    t.join();
}

void jrsaoi::SystemStateController::SlotCheckedChanged(bool flag_)
{
    if (flag_)
    {
        auto param = _model->GetSystemState();
        emit SigUpdateSystemState(param);
    }
}

int jrsaoi::SystemStateController::UpdateView()
{
    auto model_data = _model->GetModelData();
    QMetaObject::invokeMethod(_view,
        "UpdateView",
        Qt::QueuedConnection,
        Q_ARG(const jrsdata::ViewParamBasePtr, model_data)
    );
    return jrscore::AOI_OK;
}

void jrsaoi::SystemStateController::InitConnect()
{
    connect(_view, &SystemStateView::SigInitDevice, this, &SystemStateController::SlotInitDevice, Qt::QueuedConnection);
    connect(_view, &SystemStateView::SigCheckedChanged, this, &SystemStateController::SlotCheckedChanged, Qt::QueuedConnection);
    /* connect(_view,&SystemStateView::close,this,SystemStateView)*/
}

