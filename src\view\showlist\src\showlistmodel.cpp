﻿#include "showlistmodel.h"
#include "coreapplication.h"
//#include "projectoperator.h"
namespace jrsaoi
{
    ShowListModel::ShowListModel(const std::string& name)
        : ModelBase(name),
        m_send_signal_to_render(false),
        m_graphics_params(nullptr),
        _project_operater(jrsaoi::ParamOperator::GetInstance()),
        _edit_view_data_ptr(std::make_shared<jrsdata::EditViewData>()),
        m_select_component_name(""),
        b_select_component_update(false)
    {
    }

    ShowListModel::~ShowListModel()
    {
    }

    int ShowListModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        // 将传入的参数赋值给成员变量
        show_list_param = param_;
        // 检查参数指针是否为空
        if (!show_list_param)
        {
            // 如果为空，可以记录错误日志（此处注释掉的代码为示例）
            //PushErrorToStack(jrscore::ViewError::E_AOI__EMPTY_POINTER, "ShowList更新数据时，传入的数据指针为空");
        }
        /**< 填充Edit信息 */
        UpdateEditViewData(param_);
        // 根据事件名称调用相应的处理函数
        if (show_list_param->event_name.compare(jrsaoi::PROJECT_UPDATE_EVENT_NAME) == 0)
        {
            // 处理项目更新事件
            if (!ProjectUpdate())
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        if (show_list_param->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME) == 0)
        {
            // 处理2D图形选择事件
            if (!GraphicsSelect())
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        if (show_list_param->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME) == 0)
        {
            // 处理2D图形更新事件
            if (!GraphicsUpdate())
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        if (show_list_param->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME) == 0)
        {
            // 处理2D图形创建事件
            if (!GraphicsCreate())
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        if (show_list_param->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME) == 0)
        {
            // 处理2D图形删除事件
            //if (!GraphicsDelete())//2025 03 27 因为删除第一个元件会删除其他子板同样元件未发消息  暂时改成全部刷新  后期要和郝江春合作改回来的 chenxixi
            if (!GraphicsCreate())
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        if (show_list_param->event_name.compare(jrsaoi::OPERATE_UPDATE_COMPONENT_RESULT_STATUS_EVENT_NAME) == 0)
        {
            // 处理组件检测结果状态更新事件
            if (!UpdateComponentDetectResultStatus(show_list_param))
            {
                return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
            }
        }
        /**< 填充Edit信息 */
        UpdateEditViewData(param_);
        // 返回成功状态
        return jrscore::AOI_OK;
    }

    int ShowListModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }

    const jrsdata::ViewParamBasePtr& ShowListModel::GetModelData()
    {
        return show_list_param;
    }

    bool ShowListModel::UpdateBoardResult(std::vector<std::string> board_names, std::vector<RESULT_STATE> status)
    {
        // 遍历主板名称列表和状态列表
        for (int i = 0; i < board_names.size() && i < status.size(); i++)
        {
            // 遍历图形参数中的子板数据列表
            for (int subboard_id = 0; subboard_id < m_graphics_params->m_sub_board_datas.size(); subboard_id++)
            {
                // 检查当前子板的名称是否与目标主板名称匹配
                if (m_graphics_params->m_sub_board_datas.at(subboard_id)->m_subboard_name.compare(board_names.at(i)) == 0)
                {
                    // 如果匹配，则更新当前子板的状态
                    m_graphics_params->m_sub_board_datas.at(subboard_id)->m_result_state = status.at(i);
                }
            }
        }
        return true;
    }

    bool ShowListModel::UpdatePartNumberResult(std::vector<std::string> part_number_names, std::vector<RESULT_STATE> status)
    {
        // 遍历部件编号名称列表和状态列表
        for (int i = 0; i < part_number_names.size() && i < status.size(); i++)
        {
            // 遍历图形参数中的子板数据列表
            for (int subboard_id = 0; subboard_id < m_graphics_params->m_sub_board_datas.size(); subboard_id++)
            {
                // 遍历当前子板中的部件编号数据列表
                for (int part_number_id = 0; part_number_id < m_graphics_params->m_sub_board_datas.at(subboard_id)->m_part_number_datas.size(); part_number_id++)
                {
                    // 检查当前部件编号的名称是否与目标部件编号名称匹配
                    if (m_graphics_params->m_sub_board_datas.at(subboard_id)->m_part_number_datas.at(part_number_id)->m_part_number.compare(part_number_names.at(i)) == 0)
                    {
                        // 如果匹配，则更新该部件编号的状态
                        m_graphics_params->m_sub_board_datas.at(subboard_id)->m_part_number_datas.at(part_number_id)->m_result_state = status.at(i);
                    }
                }
            }
        }
        return true;
    }

    bool ShowListModel::UpdateDeviceResult(const std::vector<std::string>& device_names, const std::vector<RESULT_STATE>& status)
    {
        // 检查元件名称列表和状态列表的大小是否匹配
        if (device_names.size() != status.size())
        {
            // 如果不匹配，记录错误日志并返回
            Log_ERROR("元件名称列表和状态列表大小不匹配！");
            return false;
        }
        // 遍历元件名称列表和状态列表
        for (size_t i = 0; i < device_names.size(); ++i)
        {
            // 调用 UpdateDeviceStatus 函数，更新当前元件的状态
            UpdateDeviceStatus(device_names[i], status[i]);
        }
        return true;
    }

    bool ShowListModel::UpdateDeviceStatus(const std::string& device_name, RESULT_STATE status)
    {
        // 遍历图形参数中的子板数据列表
        for (size_t subboard_id = 0; subboard_id < m_graphics_params->m_sub_board_datas.size(); ++subboard_id)
        {
            // 获取当前子板数据的引用
            auto& subboard = m_graphics_params->m_sub_board_datas[subboard_id];
            // 更新当前子板中目标元件的状态
            UpdateDeviceStatus(subboard, device_name, status);
        }
        return true;
    }

    bool ShowListModel::UpdateDeviceStatus(const SubBoardDataStructPtr& subboard, const std::string& device_name, RESULT_STATE status)
    {
        // 遍历子板数据中的部件编号数据列表
        for (size_t part_number_id = 0; part_number_id < subboard->m_part_number_datas.size(); ++part_number_id)
        {
            // 获取当前部件编号数据的引用
            auto& part_number = subboard->m_part_number_datas[part_number_id];
            // 更新当前部件编号中目标元件的状态
            UpdateDeviceStatus(part_number, device_name, status);
        }
        return true;
    }

    bool ShowListModel::UpdateDeviceStatus(const PartNumberDataStructPtr& part_number, const std::string& device_name, RESULT_STATE status)
    {
        // 遍历部件编号数据中的元件数据列表
        for (size_t device_id = 0; device_id < part_number->m_device_datas.size(); ++device_id)
        {
            // 获取当前元件的引用
            auto& device = part_number->m_device_datas[device_id];
            // 检查当前元件的名称是否与目标元件名称匹配
            if (device->m_device_name == device_name)
            {
                // 如果匹配，则更新该元件的结果状态
                device->m_result_state = status;
                // 找到目标元件后，退出循环
                break;
            }
        }
        return true;
    }

    bool ShowListModel::ResolveBoardData(const jrsdata::ProjectParamPtr project_event_param)
    {
        // 检查传入的项目事件参数是否为空
        if (!project_event_param)
        {
            // 如果为空，直接返回，不进行任何操作
            return false;
        }
        // 清空当前图形参数中的子板数据列表
        ClearSubboardData();
        // 根据项目事件参数中的子板信息，创建并填充子板数据
        CreateSubboardData(project_event_param->board_info.sub_board);
        // 插入一个组合子板数据到子板数据列表的最前面
        InsertCombinedSubboard(project_event_param->board_info);
        return true;
    }

    bool ShowListModel::ClearSubboardData()
    {
        // 清空图形参数中子板数据列表的所有内容
        m_graphics_params->m_sub_board_datas.clear();
        return true;
    }

    bool ShowListModel::CreateSubboardData(const std::vector<SubBoard>& sub_boards)
    {
        // 遍历传入的子板数据列表
        for (const auto& board_data : sub_boards)
        {
            // 将当前子板数据转换为 SubBoardDataStruct 对象
            auto subboard_data = MakeSubboardDataStruct(board_data.id, board_data);
            // 将生成的子板数据对象添加到图形参数的子板数据列表中
            m_graphics_params->m_sub_board_datas.push_back(subboard_data);
        }
        return true;
    }

    bool ShowListModel::InsertCombinedSubboard(const jrsdata::Board& board_info)
    {
        // 调用 ConvertAllSubboardToOne 函数，将所有子板数据转换为一个组合子板对象
        auto combined_subboard = ConvertAllSubboardToOne(
            m_graphics_params->m_sub_board_datas, // 传入当前图形参数中的子板数据
            board_info                            // 传入完整的主板信息
        );
        // 将生成的组合子板对象插入到图形参数的子板数据列表的最前面
        m_graphics_params->m_sub_board_datas.insert(
            m_graphics_params->m_sub_board_datas.begin(),  // 插入位置：列表的开头
            combined_subboard                            // 插入对象：组合子板
        );
        return true;
    }

    SubBoardDataStructPtr ShowListModel::MakeSubboardDataStruct(int board_id, jrsdata::SubBoard sub_board)
    {
        // 创建一个 SubBoardDataStruct 类型的智能指针对象
        SubBoardDataStructPtr sub_board_data = std::make_shared<SubBoardDataStruct>();
        // 设置主板的ID
        sub_board_data->m_id = board_id;
        // 设置子板的ID
        sub_board_data->m_subboard_id = sub_board.id;
        // 初始化子板的角度为0
        sub_board_data->m_angle = sub_board.angle;
        // 初始化子板的结果状态为 RESULT_NONE（无结果）
        sub_board_data->m_result_state = RESULT_STATE::RESULT_NONE;
        // 设置子板的名称
        sub_board_data->m_subboard_name = sub_board.subboard_name;
        std::vector<jrsdata::Component> vec_component;
        if (!sub_board.bad_mark.component_name.empty())
        {
            vec_component.push_back(sub_board.bad_mark);
        }
        if (!sub_board.barcode.component_name.empty())
        {
            vec_component.push_back(sub_board.barcode);
        }
        for (auto& mark : sub_board.sub_mark)
        {
            if (!mark.component_name.empty())
            {
                vec_component.push_back(mark);
            }
        }
        if (vec_component.size() > 0)
        {
            MakePartNumberDataStructVector(sub_board_data->m_part_number_datas_top, sub_board_data->m_subboard_id, sub_board_data->m_subboard_name, vec_component);
        }
        if (sub_board.component_info.size() > 0)
        {
            MakePartNumberDataStructVector(sub_board_data->m_part_number_datas_bottom, sub_board_data->m_subboard_id, sub_board_data->m_subboard_name, sub_board.component_info);
        }
        // 返回构建好的 SubBoardDataStruct 对象
        return sub_board_data;
    }

    PartNumberDataStructPtr ShowListModel::MakePartNumberDataStruct(int board_id, std::string board_name, int part_no_id, std::string part_number, std::vector<jrsdata::Component> vec_component)
    {
        // 创建一个 PartNumberDataStruct 类型的智能指针对象
        PartNumberDataStructPtr part_number_data_struct = std::make_shared<PartNumberDataStruct>();
        // 设置子板名称
        part_number_data_struct->m_subboard_name = board_name;
        // 设置子板ID
        part_number_data_struct->m_subboard_id = board_id;
        // 设置部件编号ID
        part_number_data_struct->m_id = part_no_id;
        // 设置部件编号
        part_number_data_struct->m_part_number = part_number;
        // 设置部件编号的跟随值（通常与部件编号相同）
        part_number_data_struct->m_part_number_follow = part_number_data_struct->m_part_number;
        // 初始化结果状态为 RESULT_NONE（无结果）
        part_number_data_struct->m_result_state = RESULT_STATE::RESULT_NONE;
        // 初始化元件ID为1
        int device_id = 1;
        // 遍历传入的组件数据向量
        for (auto& component_data : vec_component)
        {
            // 为每个组件创建 DeviceDataStruct 对象，并将其添加到元件数据列表中
            part_number_data_struct->m_device_datas.push_back(MakeDeviceDataStruct(board_id, device_id++, component_data));
        }
        // 返回构建好的 PartNumberDataStruct 对象
        return part_number_data_struct;
    }

    DeviceDataStructPtr ShowListModel::MakeDeviceDataStruct(int board_id, int device_id, jrsdata::Component component)
    {
        // 创建一个元件数据结构的智能指针
        DeviceDataStructPtr device_data = std::make_shared<DeviceDataStruct>();
        // 设置元件数据结构中的子板名称
        device_data->m_subboard_name = component.subboard_name;
        device_data->m_subboard_id = board_id;
        //// 通过子板名称获取子板ID，并设置到元件数据结构中
        //device_data->m_subboard_id = _project_operater.GetProjectDataProcessInstance()->GetSubBoardIdBySubBoardName(component.subboard_name);
        // 设置元件数据结构中的部件编号
        device_data->m_part_number = component.component_part_number;
        // 设置元件数据结构中的元件ID
        device_data->m_id = device_id;
        // 设置元件数据结构中的元件名称
        device_data->m_device_name = component.component_name;
        // 设置元件数据结构中的X坐标
        device_data->m_x = component.x;
        // 设置元件数据结构中的Y坐标
        device_data->m_y = component.y;
        // 设置元件数据结构中的角度
        device_data->m_angle = component.angle;
        device_data->m_body_tested = component.enable;
        // 初始化元件数据结构中的结果状态为RESULT_NONE
        device_data->m_result_state = RESULT_STATE::RESULT_NONE;
        if (!device_data->m_body_tested)
        {
            device_data->m_result_state = RESULT_STATE::RESULT_UNTESTED;
        }
        // 读取组件的主体信息，并设置元件的宽度和高度
        auto body = project_param_instance.GetProjectDataProcessInstance()->ReadComponentBodyRef(device_data->m_part_number);
        if (body.has_value())
        {
            device_data->m_width = body.value().get().width;
            device_data->m_height = body.value().get().height;
        }
        // 返回生成的元件数据结构指针
        return device_data;
    }

    std::map<std::string, std::vector<Component>> ShowListModel::MakePartNumberFromComponent(std::vector<jrsdata::Component> vec_component)
    {
        std::map<std::string, std::vector<Component>> map_part_number;
        // 遍历所有组件数据
        for (auto& device_data : vec_component)
        {
            if (device_data.component_part_number.empty())
            {
                continue;
            }
            if (map_part_number.count(device_data.component_part_number) == 0)
            {
                map_part_number[device_data.component_part_number] = std::vector<Component>();
            }
            map_part_number[device_data.component_part_number].push_back(device_data);
        }
        // 返回按料号分组的组件数据映射
        return map_part_number;
    }

    bool ShowListModel::MakePartNumberDataStructVector(std::vector<PartNumberDataStructPtr>& part_number_datas, int board_id, std::string board_name, std::vector<jrsdata::Component> vec_component)
    {
        // 将组件列表转换为料号分组的映射
        std::map<std::string, std::vector<Component>> map_part_number = MakePartNumberFromComponent(vec_component);
        // 初始化料号 ID，从 1 开始编号
        int part_number_id = 1;
        // 遍历料号映射，为每个料号创建数据结构并添加到料号列表中
        for (auto iter = map_part_number.begin(); iter != map_part_number.end(); iter++)
        {
            // 创建料号数据结构，并将其添加到料号列表中
            part_number_datas.push_back(MakePartNumberDataStruct(board_id, board_name, part_number_id++, iter->first, iter->second));
        }
        // 返回成功标志
        return true;
    }

    bool ShowListModel::UpdateComponentDetectResultStatus(const jrsdata::ViewParamBasePtr& param)
    {
        // 第1步：将参数转换为预期的类型
        auto component_detect_status = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param);
        if (!component_detect_status)
        {
            HandleInvalidComponentStatus();  // 处理无效的元件状态
            return false;
        }
        // 第2步：根据元件检测结果更新元件结果
        UpdateDeviceResults(component_detect_status->component_detect_results);
        // 第3步：根据更新后的元件结果更新子板状态
        UpdateSubboardStates();
        // 第4步：标记显示列表参数为已更新
        MarkShowListParamsAsUpdated();
        return true;
    }

    void ShowListModel::HandleInvalidComponentStatus()
    {
        JRSMessageBox_WARN("ShowListModel", "元件检测结果异常，无法更新显示列表！", jrscore::MessageButton::Ok);
        Log_ERROR("检测结果异常，无法更新显示列表界面！");
    }

    bool ShowListModel::UpdateDeviceResults(const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& component_result_map)
    {
        // 遍历所有子板数据
        for (const auto& subboard : m_graphics_params->m_sub_board_datas)
        {
            // 遍历子板中的所有料号数据
            for (const auto& part : subboard->m_part_number_datas)
            {
                // 遍历料号中的所有元件数据
                for (const auto& device : part->m_device_datas)
                {
                    // 根据检测结果更新每个元件的状态
                    UpdateDeviceResult(device, component_result_map);
                }
            }
        }
        return true;
    }

    bool ShowListModel::UpdateDeviceResult(const DeviceDataStructPtr& device, const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& component_result_map)
    {
        // 检查元件名称是否存在于检测结果映射中
        if (component_result_map.count(device->m_device_name) == 0)
        {
            // 如果元件名称不在检测结果中，直接返回
            return false;
        }
        // 根据检测结果更新元件的状态
        // 如果检测结果为 true，则元件状态为 RESULT_OK；否则为 RESULT_NG
        device->m_result_state = component_result_map.at(device->m_device_name).result_status ? RESULT_STATE::RESULT_OK : RESULT_STATE::RESULT_NG;
        return true;
    }

    bool ShowListModel::UpdateSubboardStates()
    {
        // 遍历所有子板数据
        for (const auto& subboard : m_graphics_params->m_sub_board_datas)
        {
            // 更新每个子板的状态
            // 调用 UpdateSubboardState 方法计算子板的最终状态，并将结果赋值给子板的 m_result_state
            subboard->m_result_state = UpdateSubboardState(subboard);
        }
        return true;
    }

    bool ShowListModel::MarkShowListParamsAsUpdated()
    {
        // 标记显示列表参数中的所有数据需要更新
        m_graphics_params->show_list_params.m_update_subboard_data = true;    // 标记子板数据需要更新
        m_graphics_params->show_list_params.m_update_part_number_data = true; // 标记料号数据需要更新
        m_graphics_params->show_list_params.m_update_device_data = true;      // 标记元件数据需要更新
        return true;
    }

    SubBoardDataStructPtr ShowListModel::ConvertAllSubboardToOne(const std::vector<SubBoardDataStructPtr>& subboard_datas, const jrsdata::Board& board)
    {
        auto combined_subboard = CreateCombinedSubboard();
        std::vector<std::vector<jrsdata::Component>> component_sources =
        {
            board.marks,                    // 板上的标记
            board.barcodes,                 // 板上的条码
            board.carrier_barcodes,         // 承载条码
            board.cover_plate_barcodes      // 盖板条码
        };
        if (!MergeBoardTopFromMultipleSources(combined_subboard, component_sources))
        {
            return combined_subboard;
        }
        // 第5步：将各个子板中的料号合并到合并后的子板中
        MergeSubboardPartNumbers(combined_subboard, subboard_datas);
        for (auto& subboard_data : subboard_datas)
        {
            subboard_data->m_part_number_datas.insert(subboard_data->m_part_number_datas.end(), subboard_data->m_part_number_datas_top.begin(), subboard_data->m_part_number_datas_top.end());
            subboard_data->m_part_number_datas.insert(subboard_data->m_part_number_datas.end(), subboard_data->m_part_number_datas_bottom.begin(), subboard_data->m_part_number_datas_bottom.end());
        }
        return combined_subboard;
    }

    SubBoardDataStructPtr ShowListModel::CreateCombinedSubboard()
    {
        // 创建一个合并后的子板数据结构
        auto combined_subboard = std::make_shared<SubBoardDataStruct>();
        // 初始化合并后的子板属性
        combined_subboard->m_id = 0;  // 设置子板 ID 为 0
        combined_subboard->m_angle = 0.0;  // 设置子板角度为 0.0
        combined_subboard->m_subboard_name = QString::fromWCharArray(L"全部子板").toStdString();  // 设置子板名称为“全部子板”
        combined_subboard->m_subboard_id = 0;  // 设置子板内部 ID 为 0
        combined_subboard->m_result_state = RESULT_STATE::RESULT_NONE;  // 设置子板的初始状态为 RESULT_NONE
        // 返回创建的合并子板对象
        return combined_subboard;
    }

    std::vector<jrsdata::Component> ShowListModel::CollectSubboardTopValues(const std::vector<SubBoard>& sub_boards)
    {
        // 初始化用于存储不良标记和条码的向量
        std::vector<jrsdata::Component> top_omponents;  // 存储不良标记
        // 遍历所有子板
        for (const auto& sub_board : sub_boards)
        {
            // 检查当前子板Mark
            for (auto& sub_mark : sub_board.sub_mark)
            {
                if (!sub_mark.component_name.empty())
                {
                    top_omponents.push_back(sub_mark);
                }
            }
            // 检查当前子板是否有不良标记
            if (!sub_board.bad_mark.component_name.empty())
            {
                // 如果有不良标记，将其添加到不良标记列表中
                top_omponents.push_back(sub_board.bad_mark);
            }
            // 检查当前子板是否有条码
            if (!sub_board.barcode.component_name.empty())
            {
                // 如果有条码，将其添加到条码列表中
                top_omponents.push_back(sub_board.barcode);
            }
        }
        // 返回包含不良标记和条码的元组
        return top_omponents;
    }

    bool ShowListModel::AddComponentsToCombinedSubboard(SubBoardDataStructPtr combined_subboard, const std::vector<jrsdata::Component>& components)
    {
        // 将组件列表转换为料号数据结构，并添加到合并后的子板中
        return MakePartNumberDataStructVector(combined_subboard->m_part_number_datas,
            combined_subboard->m_subboard_id, combined_subboard->m_subboard_name, components);
    }

    bool ShowListModel::MergeBoardTopFromMultipleSources(SubBoardDataStructPtr combined_subboard, const std::vector<std::vector<jrsdata::Component>>& component_sources)
    {
        for (const auto& components : component_sources)
        {
            if (!AddComponentsToCombinedSubboard(combined_subboard, components))
            {
                return false;
            }
        }
        return true;
    }

    bool ShowListModel::MergeSubboardPartNumbers(SubBoardDataStructPtr combined_subboard, const std::vector<SubBoardDataStructPtr>& subboard_datas)
    {
        // 遍历所有子板数据
        for (const auto& subboard : subboard_datas)
        {
            // 遍历当前子板中的所有料号数据
            for (const auto& part : subboard->m_part_number_datas_top)
            {
                // 将当前料号合并到合并后的子板中
                MergePartNumber(combined_subboard, part);
            }
        }
        for (const auto& subboard : subboard_datas)
        {
            // 遍历当前子板中的所有料号数据
            for (const auto& part : subboard->m_part_number_datas_bottom)
            {
                // 将当前料号合并到合并后的子板中
                MergePartNumber(combined_subboard, part);
            }
        }
        return true;
    }

    bool ShowListModel::MergePartNumber(SubBoardDataStructPtr combined_subboard, const PartNumberDataStructPtr& part)
    {
        // 获取合并后的子板中的料号列表
        auto& combined_parts = combined_subboard->m_part_number_datas;
        // 在合并后的料号列表中查找是否存在相同料号
        auto it = std::find_if(combined_parts.begin(), combined_parts.end(),
            [&part](const PartNumberDataStructPtr& combined_part)
            {
                // 比较料号名称是否相同
                return combined_part->m_part_number == part->m_part_number;
            });
        if (it != combined_parts.end())
        {
            // 如果找到相同料号，将当前料号的元件数据追加到合并后的料号中
            (*it)->m_device_datas.insert((*it)->m_device_datas.end(), part->m_device_datas.begin(), part->m_device_datas.end());
        }
        else
        {
            // 如果未找到相同料号，将当前料号复制一份并添加到合并后的料号列表中
            combined_parts.push_back(std::make_shared<PartNumberDataStruct>(*part));
        }
        return true;
    }

    RESULT_STATE ShowListModel::UpdateSubboardState(SubBoardDataStructPtr subboard)
    {
        // 检查传入的子板数据是否有效
        if (subboard == nullptr)
        {
            // 如果子板数据为空，返回默认状态 RESULT_NONE
            return RESULT_STATE::RESULT_NONE;
        }
        // 创建一个映射，用于统计子板中料号的状态分布
        std::map<RESULT_STATE, int> map_result_state;
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_NONE, 0));  // 初始化状态计数器
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_NG, 0));
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_OK, 0));
        // 遍历子板中的所有料号数据
        for (auto& part : subboard->m_part_number_datas)
        {
            // 更新料号的状态
            RESULT_STATE state = UpdatePartNumberState(part);
            // 将更新后的状态赋值给料号
            part->m_result_state = state;
            // 更新状态统计映射
            map_result_state[state]++;
        }
        // 根据统计结果确定子板的最终状态
        if (map_result_state[RESULT_STATE::RESULT_NG] > 0)
        {
            // 如果存在状态为 NG 的料号，则子板状态为 NG
            return RESULT_STATE::RESULT_NG;
        }
        if (map_result_state[RESULT_STATE::RESULT_OK] > 0)
        {
            // 如果存在状态为 OK 的料号，但没有 NG 料号，则子板状态为 OK
            return RESULT_STATE::RESULT_OK;
        }
        // 如果没有 NG 或 OK 料号，则子板状态为 NONE
        return RESULT_STATE::RESULT_NONE;
    }

    RESULT_STATE ShowListModel::UpdatePartNumberState(PartNumberDataStructPtr part_number)
    {
        // 检查传入的料号数据是否有效
        if (part_number == nullptr)
        {
            // 如果料号数据为空，返回默认状态 RESULT_NONE
            return RESULT_STATE::RESULT_NONE;
        }
        // 创建一个映射，用于统计料号中元件的状态分布
        std::map<RESULT_STATE, int> map_result_state;
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_NONE, 0));  // 初始化状态计数器
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_NG, 0));
        map_result_state.insert(std::make_pair(RESULT_STATE::RESULT_OK, 0));
        // 遍历料号中的所有元件数据
        for (auto device : part_number->m_device_datas)
        {
            // 根据元件的状态更新对应的计数器
            map_result_state[device->m_result_state]++;
        }
        // 根据统计结果确定料号的最终状态
        if (map_result_state[RESULT_STATE::RESULT_NG] > 0)
        {
            // 如果存在状态为 NG 的元件，则料号状态为 NG
            return RESULT_STATE::RESULT_NG;
        }
        if (map_result_state[RESULT_STATE::RESULT_OK] > 0)
        {
            // 如果存在状态为 OK 的元件，但没有 NG 元件，则料号状态为 OK
            return RESULT_STATE::RESULT_OK;
        }
        // 如果没有 NG 或 OK 元件，则料号状态为 NONE
        return RESULT_STATE::RESULT_NONE;
    }

    int ShowListModel::UpdateEditViewData(const jrsdata::ViewParamBasePtr& param_)
    {
        auto graphics_info_ptr = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param_);
        if (!graphics_info_ptr)
        {
            return -1;
        }
        _edit_view_data_ptr->Clear();
        auto project_process = _project_operater.GetProjectDataProcessInstance();
        auto& [layer_, data_] = graphics_info_ptr->graphics_and_select_units;
        if (layer_ == "component"
            || layer_ == "mark"
            || layer_ == "subbarcode"
            || layer_ == "badmark"
            || layer_ == "submark"
            || layer_ == "barcode")
        {
            UpdateComponentData(project_process, data_);
        }
        else if (layer_ == "pad")
        {
            UpdatePadEditData(project_process, data_);
        }
        else if (layer_ == "region")
        {
            UpdateRegionEditData(project_process, data_);
        }
        _select_units = graphics_info_ptr->graphics_and_select_units.second;
        return 0;
    }

    bool ShowListModel::UpdateComponentData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process_, const jrsdata::SelectUnitsName& data_)
    {
        auto component = project_process_->ReadComponentRef(data_.type_and_component_name.second, data_.subboard_name, data_.type_and_component_name.first);
        if (!component.has_value())
        {
            return false;
        }
        auto component_unit = project_process_->ReadComponentUnitRef(component->get().component_part_number, "body");
        if (!component_unit.has_value())
        {
            return false;
        }
        _edit_view_data_ptr->component_edit_data.angle = component->get().angle;
        _edit_view_data_ptr->component_edit_data.cx = component->get().x;
        _edit_view_data_ptr->component_edit_data.cy = component->get().y;
        _edit_view_data_ptr->component_edit_data.width = component_unit->get().width;
        _edit_view_data_ptr->component_edit_data.height = component_unit->get().height;
        _edit_view_data_ptr->component_edit_data.is_detect = component->get().enable;
        _edit_view_data_ptr->update_widget = EditViewData::UpdateWidgets::Component;
        return true;
    }

    bool ShowListModel::UpdatePadEditData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process, const jrsdata::SelectUnitsName& data_)
    {
        UpdateComponentData(project_process, data_);
        auto component_unit = project_process->ReadComponentUnit(data_.type_and_component_name.second, data_.subboard_name, data_.unit_name, data_.type_and_component_name.first);
        if (component_unit.has_value())
        {
            _edit_view_data_ptr->update_widget = EditViewData::UpdateWidgets::PAD;
            _edit_view_data_ptr->pad_edit_data.common.cx = component_unit->x;
            _edit_view_data_ptr->pad_edit_data.common.cy = component_unit->y;
            _edit_view_data_ptr->pad_edit_data.common.height = component_unit->height;
            _edit_view_data_ptr->pad_edit_data.common.width = component_unit->width;
            _edit_view_data_ptr->pad_edit_data.common.is_detect = component_unit->enable;
            _edit_view_data_ptr->pad_edit_data.direction = component_unit->direction;
            _edit_view_data_ptr->pad_edit_data.pad_type = component_unit->pad_type;
            /**<根据方向gap 计算gap */
            _edit_view_data_ptr->pad_edit_data.col_gap;/**< 因结构问题 行间距和列间距目前无法修改和获取 暂时不实现改功能*/
            _edit_view_data_ptr->pad_edit_data.row_gap;
        }
        return true;
    }

    bool ShowListModel::UpdateRegionEditData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process, const jrsdata::SelectUnitsName& data_)
    {
        /**< 先获取检测框 */
        auto component = project_process->ReadComponentRef(data_.type_and_component_name.second,
            data_.subboard_name,
            data_.type_and_component_name.first);
        if (!component.has_value())
        {
            return false;
        }
        auto detect_window = project_process->ReadDetectWindow(component->get().component_part_number, data_.window_name);
        if (!detect_window.has_value())
        {
            return false;
        }
        CommonEditData edit_data;
        edit_data.cx = detect_window->cx;
        edit_data.cy = detect_window->cy;
        edit_data.width = detect_window->width;
        edit_data.height = detect_window->height;
        if (data_.unit_name.find("body") != std::string::npos)
        {
            _edit_view_data_ptr->detect_window_edit_data.first = jrsdata::ComponentUnit::Type::BODY;
            UpdateComponentData(project_process, data_);
        }
        else if (data_.unit_name.find("pad") != std::string::npos)
        {
            _edit_view_data_ptr->detect_window_edit_data.first = jrsdata::ComponentUnit::Type::PAD;
            UpdatePadEditData(project_process, data_);
            /**<如果是pad 需要旋转检测框*/
            cv::RotatedRect detect_window_rotated(
                { edit_data.cx,
               edit_data.cy },
                { edit_data.width,
                edit_data.height },
                0
            );
            detect_window_rotated = project_process->GetRotatedRectByPadDirection({ component->get().x,component->get().y },
                { _edit_view_data_ptr->pad_edit_data.common.cx,_edit_view_data_ptr->pad_edit_data.common.cy },
                detect_window_rotated, jrsdata::ComponentUnit::Direction::UP, _edit_view_data_ptr->pad_edit_data.direction);
            edit_data.cx = detect_window_rotated.center.x;
            edit_data.cy = detect_window_rotated.center.y;
            edit_data.width = detect_window_rotated.size.width;
            edit_data.height = detect_window_rotated.size.height;
        }
        //edit_data.is_detect = detect_window->enable;
        _edit_view_data_ptr->detect_window_edit_data.second = edit_data;
        _edit_view_data_ptr->update_widget = EditViewData::UpdateWidgets::DETECT_WINDOW;
        return true;
    }
    void ShowListModel::UpdateSubboardDeviceData(std::string subboard_name, std::string device_name, jrsdata::Component::Type type)
    {
        auto component = GetComponentRef(device_name, subboard_name, type);
        if (!component.has_value()) {
            return;
        }

        auto unit = GetComponentUnitRef(component.value().component_part_number, "body");
        if (!unit.has_value()) {
            return;
        }

        UpdateDeviceData(subboard_name, device_name, component.value(), unit.value());
    }
    std::optional<jrsdata::Component> ShowListModel::GetComponentRef(std::string device_name, std::string subboard_name, jrsdata::Component::Type type)
    {
        return _project_operater.GetProjectDataProcessInstance()->ReadComponentRef(device_name, subboard_name, type);
    }
    std::optional<jrsdata::ComponentUnit> ShowListModel::GetComponentUnitRef(std::string part_number, std::string unit_name)
    {
        return _project_operater.GetProjectDataProcessInstance()->ReadComponentUnitRef(part_number, unit_name);
    }

    void ShowListModel::UpdateDeviceData(std::string subboard_name, std::string device_name, const jrsdata::Component& component, const jrsdata::ComponentUnit& unit)
    {
        for (auto& subboard : m_graphics_params->m_sub_board_datas) {
            if (subboard->m_subboard_name.compare(subboard_name) == 0) {
                for (auto& part_number : subboard->m_part_number_datas) {
                    for (auto& device : part_number->m_device_datas) {
                        if (device->m_device_name.compare(device_name) == 0) {
                            UpdateDeviceProperties(device, component, unit);
                            m_graphics_params->show_list_params.m_update_device_data = true;
                        }
                    }
                }
            }
        }
    }

    void ShowListModel::UpdateDeviceProperties(DeviceDataStructPtr& device, const jrsdata::Component& component, const jrsdata::ComponentUnit& unit)
    {
        device->m_x = component.x;
        device->m_y = component.y;
        device->m_angle = component.angle;
        device->m_width = unit.width;
        device->m_height = unit.height;
        device->m_body_tested = component.enable;

        if (!component.enable) {
            device->m_result_state = RESULT_STATE::RESULT_UNTESTED;
        }
        else {
            device->m_result_state = RESULT_STATE::RESULT_NONE;
        }
    }
    bool ShowListModel::UpdateEditViewDataToProject(const jrsdata::ViewParamBasePtr& param_)
    {
        auto project_ptr = _project_operater.GetProjectDataProcessInstance();
        auto render_param_ptr = std::dynamic_pointer_cast<RenderEventParam>(param_);
        if (render_param_ptr->edit_data_update_ptr->update_widget == EditViewData::UpdateWidgets::Component)
        {
            auto component = project_ptr->ReadComponentRef(_select_units.type_and_component_name.second,
                _select_units.subboard_name, _select_units.type_and_component_name.first);
            if (!component)
            {
                Log_ERROR("元件查询失败");
                return false;
            }
            component->get().enable = render_param_ptr->edit_data_update_ptr->component_edit_data.is_detect;
            component->get().x = render_param_ptr->edit_data_update_ptr->component_edit_data.cx;
            component->get().y = render_param_ptr->edit_data_update_ptr->component_edit_data.cy;
            float angle_val = render_param_ptr->edit_data_update_ptr->component_edit_data.angle - component->get().angle;
            component->get().angle = render_param_ptr->edit_data_update_ptr->component_edit_data.angle;
            /**< 同步到其他相同子板*/
            auto update_component_info = project_ptr->UpdateComponentAnglesAndEnablesOfDifferentSubboards(component->get().component_name,
                component->get().subboard_name, component->get().component_type,
                angle_val, component->get().enable);
            update_component_info[component->get().subboard_name] = component->get().component_name;
            auto unit = project_ptr->ReadComponentUnitRef(component->get().component_part_number, "body");
            if (!unit)
            {
                Log_ERROR("PAD查询失败");
                return false;
            }
            unit->get().height = render_param_ptr->edit_data_update_ptr->component_edit_data.height;
            unit->get().width = render_param_ptr->edit_data_update_ptr->component_edit_data.width;
            for (auto& [subboard_name, component_name] : update_component_info)
            {
                UpdateSubboardDeviceData(subboard_name, component_name, component->get().component_type);
            }
        }  //Render2D更改Pad位置  该model 不能更改pad信息
        else if (render_param_ptr->edit_data_update_ptr->update_widget == EditViewData::UpdateWidgets::PAD)
        {
            auto component = project_ptr->ReadComponentRef(_select_units.type_and_component_name.second,
                _select_units.subboard_name, _select_units.type_and_component_name.first);
            if (!component)
            {
                Log_ERROR("元件查询失败");
                return false;
            }
            auto unit = project_ptr->ReadComponentUnitRef(component->get().component_part_number, _select_units.unit_name);
            if (!unit)
            {
                Log_ERROR("PAD查询失败");
                return false;
            }
            unit->get().enable = render_param_ptr->edit_data_update_ptr->pad_edit_data.common.is_detect;
        }
        else if (render_param_ptr->edit_data_update_ptr->update_widget == EditViewData::UpdateWidgets::DETECT_WINDOW)
        {
            auto component = project_ptr->ReadComponentRef(_select_units.type_and_component_name.second,
                _select_units.subboard_name, _select_units.type_and_component_name.first);
            if (!component.has_value())
            {
                return false;
            }
            auto component_unit = project_ptr->ReadComponentUnit(component->get().component_part_number, _select_units.unit_name);
            if (!component_unit.has_value())
            {
                return false;
            }
            auto detect_window = project_ptr->ReadDetectWindowRef(component->get().component_part_number, _select_units.window_name);
            if (!detect_window.has_value())
            {
                return false;
            }
            //detect_window->get().enable = !render_param_ptr->edit_data_update_ptr->detect_window_edit_data.second.is_detect;
            cv::RotatedRect detect_window_rotated(
                { render_param_ptr->edit_data_update_ptr->detect_window_edit_data.second.cx,  render_param_ptr->edit_data_update_ptr->detect_window_edit_data.second.cy },
                { render_param_ptr->edit_data_update_ptr->detect_window_edit_data.second.width,render_param_ptr->edit_data_update_ptr->detect_window_edit_data.second.height },
                0
            );
            if (component_unit->unit_type == jrsdata::ComponentUnit::Type::PAD)
            {
                detect_window_rotated = project_ptr->GetRotatedRectByPadDirection({ component->get().x,component->get().y }, { component_unit->x,component_unit->y },
                    detect_window_rotated, component_unit->direction);
            }
            detect_window->get().cx = detect_window_rotated.center.x;
            detect_window->get().cy = detect_window_rotated.center.y;
            detect_window->get().width = detect_window_rotated.size.width;
            detect_window->get().height = detect_window_rotated.size.height;
        }
        return true;
    }

    void ShowListModel::SetSelectComponentUpdateData(std::string device_name)
    {
        m_select_component_name = device_name;
        b_select_component_update = true;
    }

    ShowTableParamBasePtr ShowListModel::GetEditViewData()
    {
        return _edit_view_data_ptr;
    }

    void ShowListModel::UpdateShowListView(ShowListViewParam show_list_view_param)
    {
        // 更新图形参数中的显示列表参数
        // 将传入的显示列表参数赋值给模型中的显示列表参数
        m_graphics_params->show_list_params = show_list_view_param;
    }

    bool ShowListModel::FindGraphicsIDFromSubboard(std::string graphics_id)
    {
        // 检查图形参数是否有效
        if (!m_graphics_params)
            return false;  // 如果图形参数为空，直接返回 false
        // 当前选中的是全部子板，就从全部子板中查找元件
        if (m_graphics_params->show_list_params.m_subboard_id == 0)
        {
            if (m_graphics_params->m_sub_board_datas.size() > 0)
            {
                if (FindGraphicsIDFromSubboardStruct(m_graphics_params->m_sub_board_datas.at(0), graphics_id, 0))
                {
                    // 如果找到匹配的图形 ID，更新显示列表参数中的子板 ID
                    m_graphics_params->show_list_params.m_subboard_id = 0;
                    m_graphics_params->show_list_params.m_update_subboard_data = true;
                    // 返回 true 表示找到匹配的图形 ID
                    return true;
                }
            }
        }
        // 遍历所有剩下子板数据查找元件
        for (int board_id = 1; board_id < m_graphics_params->m_sub_board_datas.size(); board_id++)
        {
            // 获取当前子板中的所有料号数据
            auto& part_numbers = m_graphics_params->m_sub_board_datas.at(board_id)->m_part_number_datas;
            // 在当前子板的料号数据中查找匹配的图形 ID
            if (FindGraphicsIDFromPartNumber(part_numbers, graphics_id))
            {
                // 如果找到匹配的图形 ID，更新显示列表参数中的子板 ID
                m_graphics_params->show_list_params.m_subboard_id = board_id;
                m_graphics_params->show_list_params.m_update_subboard_data = true;
                // 返回 true 表示找到匹配的图形 ID
                return true;
            }
        }
        // 如果选中的不是全部子板，先遍历所有子板后，再去全部子板里面查找
        if (m_graphics_params->m_sub_board_datas.size() > 0)
        {
            if (FindGraphicsIDFromSubboardStruct(m_graphics_params->m_sub_board_datas.at(0), graphics_id, 0))
            {
                // 如果找到匹配的图形 ID，更新显示列表参数中的子板 ID
                m_graphics_params->show_list_params.m_subboard_id = 0;
                m_graphics_params->show_list_params.m_update_subboard_data = true;
                // 返回 true 表示找到匹配的图形 ID
                return true;
            }
        }
        // 如果遍历完所有子板仍未找到匹配的图形 ID，返回 false
        return false;
    }

    bool ShowListModel::FindGraphicsIDFromPartNumber(std::vector<PartNumberDataStructPtr> part_numbers, std::string graphics_id)
    {
        // 遍历所有料号数据
        for (int part_id = 0; part_id < part_numbers.size(); part_id++)
        {
            // 获取当前料号中的元件数据
            auto& devices = part_numbers.at(part_id)->m_device_datas;
            // 在当前料号的元件数据中查找匹配的图形 ID
            if (FindGraphicsIDFromDevice(devices, graphics_id))
            {
                // 如果找到匹配的元件，更新显示列表参数：
                m_graphics_params->show_list_params.m_part_number_id = part_id;
                m_graphics_params->show_list_params.m_update_part_number_data = true;
                // 返回 true 表示找到匹配的图形 ID
                return true;
            }
        }
        // 如果遍历完所有料号仍未找到匹配的图形 ID，返回 false
        return false;
    }

    bool ShowListModel::FindGraphicsIDFromSubboardStruct(SubBoardDataStructPtr subboad_data, std::string graphics_id, int board_id)
    {
        if (subboad_data != nullptr)
        {
            // 获取当前子板中的所有料号数据
            auto& part_numbers = subboad_data->m_part_number_datas;
            // 在当前子板的料号数据中查找匹配的图形 ID
            if (FindGraphicsIDFromPartNumber(part_numbers, graphics_id))
            {
                // 如果找到匹配的图形 ID，更新显示列表参数中的子板 ID
                m_graphics_params->show_list_params.m_subboard_id = board_id;
                m_graphics_params->show_list_params.m_update_subboard_data = true;
                // 返回 true 表示找到匹配的图形 ID
                return true;
            }
        }
        return false;
    }

    bool ShowListModel::ProjectUpdate()
    {
        // 将显示列表参数转换为图形更新项目事件参数
        auto graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(show_list_param);
        // 检查参数是否有效
        if (!graphics_params)
        {
            // 如果参数无效，直接返回
            return false;
        }
        // 更新模型中的图形参数对象
        m_graphics_params = graphics_params;
        // 调用 UpdateProjectData 方法更新项目数据
        // 包括解析子板数据和重置显示列表参数
        UpdateProjectData();
        return true;
    }

    bool ShowListModel::UpdateProjectData()
    {
        auto project_param = _project_operater.GetProjectDataProcessInstance()->GetProjectParam();
        // 解析项目参数中的子板数据
        ResolveBoardData(project_param);
        // 重置显示列表参数，准备加载新的项目数据
        ResetShowListParams();
        return true;
    }

    bool ShowListModel::ResetShowListParams()
    {
        // 获取显示列表参数的引用
        auto& show_list_params = m_graphics_params->show_list_params;
        // 标记所有数据需要更新
        show_list_params.m_update_subboard_data = true;      // 标记子板数据需要更新
        show_list_params.m_update_part_number_data = true;   // 标记料号数据需要更新
        show_list_params.m_update_device_data = true;        // 标记元件数据需要更新
        // 重置选中的子板、料号和元件的 ID 为初始值（0）
        show_list_params.m_subboard_id = 0;                  // 重置子板 ID
        show_list_params.m_part_number_id = 0;               // 重置料号 ID
        show_list_params.m_device_id = 0;                    // 重置元件 ID
        return true;
    }

    bool ShowListModel::GraphicsSelect()
    {
        if (!m_graphics_params) {
            return false;
        }

        auto graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(show_list_param);
        if (!graphics_params) {
            return false;
        }

        if (!ProcessGraphicsMap(graphics_params)) {
            return false;
        }
        if (!ProcessGraphicsAndSelectUnits(graphics_params))
        {
            return false;
        }
        return true;
    }

    bool ShowListModel::ProcessGraphicsMap(const std::shared_ptr<jrsdata::GraphicsUpdateProjectEventParam>& graphics_params)
    {
        for (const auto& [layer, graphics_ids] : graphics_params->graphics_map) {
            if (layer.compare("pad") == 0) {
                break;
            }

            if (graphics_ids.empty()) {
                continue;
            }

            if (b_select_component_update && m_select_component_name.compare(graphics_ids.front().c_str()) == 0) {
                b_select_component_update = false;
                return false;
            }

            if (FindGraphicsIDFromSubboard(graphics_ids.front().c_str())) {
                graphics_params->show_list_params = m_graphics_params->show_list_params;
                return true;
            }
        }

        return false;
    }

    bool ShowListModel::ProcessGraphicsAndSelectUnits(const std::shared_ptr<jrsdata::GraphicsUpdateProjectEventParam>& graphics_params)
    {
        auto& [layer_, data_] = graphics_params->graphics_and_select_units;
        if (layer_.compare("pad") == 0)
        {
            if (FindGraphicsIDFromSubboard(data_.type_and_component_name.second))
            {
                graphics_params->show_list_params = m_graphics_params->show_list_params;
            }
            else
            {
                return false;
            }
        }
        return true;
    }
    bool ShowListModel::GraphicsUpdate()
    {
        // 将显示列表参数转换为图形更新事件参数
        auto graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(show_list_param);
        // 检查参数是否有效
        if (!graphics_params || !m_graphics_params)
        {
            // 如果参数无效或模型参数为空，直接返回
            return false;
        }
        // 遍历图形参数中的所有图形映射
        for (const auto& entry : graphics_params->graphics_map)
        {
            // 检查图形 ID 列表是否为空
            for (auto& component_name : entry.second)
            {
                UpdateDevicePosition(graphics_params, component_name);
            }
        }
        m_graphics_params->show_list_params.m_update_device_data = true;
        return true;
    }

    bool ShowListModel::UpdateDevicePosition(const jrsdata::GraphicsUpdateProjectEventParamPtr& graphics_params, const std::string& graphics_id)
    {
        Q_UNUSED(graphics_params);
        // 获取项目参数中的子板信息
        const auto& sub_boards = _project_operater.GetProjectDataProcessInstance()->GetProjectParam()->board_info.sub_board;
        // 遍历所有子板
        for (const auto& sub_board : sub_boards)
        {
            // 遍历子板中的所有元件
            for (const auto& component : sub_board.component_info)
            {
                // 检查元件名称是否与目标图形 ID 匹配
                if (component.component_name == graphics_id)
                {
                    // 如果匹配，更新元件的位置和尺寸
                    for (auto& m_subboard : m_graphics_params->m_sub_board_datas)
                    {
                        for (auto& m_part_number : m_subboard->m_part_number_datas)
                        {
                            for (auto& m_device : m_part_number->m_device_datas)
                            {
                                if (m_device->m_device_name == graphics_id)
                                {
                                    m_device->m_x = component.x;
                                    m_device->m_y = component.y;
                                    m_device->m_angle = component.angle;
                                    return true;
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
        return false;
    }

    bool ShowListModel::UpdateDeviceData(const ShowListViewParam& list_param, const jrsdata::Component& component)
    {
        // 根据显示列表参数定位到具体的元件数据
        if (list_param.m_subboard_id > -1 && list_param.m_subboard_id < m_graphics_params->m_sub_board_datas.size())
        {
            auto subboard = m_graphics_params->m_sub_board_datas.at(list_param.m_subboard_id);
            if (list_param.m_part_number_id > -1 && list_param.m_part_number_id < subboard->m_part_number_datas.size())
            {
                auto part_nunber = subboard->m_part_number_datas.at(list_param.m_part_number_id);
                if (list_param.m_device_id > -1 && list_param.m_device_id < part_nunber->m_device_datas.size())
                {
                    auto device_data = part_nunber->m_device_datas.at(list_param.m_device_id);
                    // 更新元件的位置和角度信息
                    device_data->m_x = component.x;  // 更新元件的 X 坐标
                    device_data->m_y = component.y;  // 更新元件的 Y 坐标
                    device_data->m_angle = component.angle;  // 更新元件的角度
                    // 根据元件的料号更新元件的尺寸
                    UpdateDeviceSize(device_data, component.component_part_number);
                    return true;
                }
            }
        }
        return false;
    }

    bool ShowListModel::UpdateDeviceSize(DeviceDataStructPtr device_data, const std::string& component_part_number)
    {
        // 通过项目参数实例获取元件的主体信息
        auto body = project_param_instance.GetProjectDataProcessInstance()->ReadComponentBodyRef(component_part_number);
        // 检查是否成功获取到元件主体信息
        if (body.has_value())
        {
            // 如果获取成功，更新元件的宽度和高度
            device_data->m_width = body->get().width;  // 更新元件宽度
            device_data->m_height = body->get().height;  // 更新元件高度
        }
        return true;
    }

    bool ShowListModel::GraphicsCreate()
    {
        // 将显示列表参数转换为图形更新事件参数
        auto new_graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(show_list_param);
        auto project_param = _project_operater.GetProjectDataProcessInstance()->GetProjectParam();
        // 检查参数是否有效
        if (!new_graphics_params || !project_param)
        {
            // 如果参数无效，直接返回
            return false;
        }
        // 解析项目参数，初始化子板数据
        if (!ResolveBoardData(project_param))
        {
            return false;
        }
        m_graphics_params->show_list_params.m_update_device_data = true;
        m_graphics_params->show_list_params.m_update_part_number_data = true;
        m_graphics_params->show_list_params.m_update_subboard_data = true;
        // 遍历图形参数中的所有图形映射 chenxixi 先删除测试一下，记得恢复
        for (const auto& entry : new_graphics_params->graphics_map)
        {
            for (const auto& graphics_id : entry.second)
            {
                FindGraphicsIDFromSubboard(graphics_id);
                break;
            }
        }
        return true;
    }

    bool ShowListModel::GraphicsDelete()
    {
        // 将显示列表参数转换为图形更新事件参数
        auto graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(show_list_param);
        if (!graphics_params)
        {
            // 如果参数类型转换失败，直接返回
            return false;
        }
        // 遍历图形参数中的所有删除请求
        for (const auto& entry : graphics_params->graphics_map)
        {
            const std::string& key = entry.first;  // 获取删除请求的类型（如 "subboard" 或 "device"）
            const std::vector<std::string>& values = entry.second;  // 获取需要删除的名称列表
            if (key == "subboard")
            {
                DeleteSubboard(values);// 删除子板
            }
            else if (key == "component")
            {
                DeleteDevice(values);// 删除元件
            }
            else
            {
                return false;
            }
        }
        // 重置显示列表参数，以反映删除操作后的状态
        ResetShowListParams();
        return true;
    }

    bool ShowListModel::DeleteDevice(const std::vector<std::string>& device_names)
    {
        // 遍历所有子板数据，删除料号下的元件
        for (auto& subboard : m_graphics_params->m_sub_board_datas)
        {
            for (auto& part_number : subboard->m_part_number_datas)
            {
                for (auto& delete_device_name : device_names)
                {
                    DeleteDeviceFromPartNumber(delete_device_name, part_number);
                }
            }
        }
        // 删除没有元件的料号
        for (auto& subboard_data : m_graphics_params->m_sub_board_datas)
        {
            // 使用 std::remove_if 算法移除没有元件的料号
            auto delete_part_data_end = std::remove_if(
                subboard_data->m_part_number_datas.begin(),
                subboard_data->m_part_number_datas.end(),
                [](std::shared_ptr<PartNumberDataStruct> part_data) {
                    return part_data->m_device_datas.size() == 0;
                });
            subboard_data->m_part_number_datas.erase(delete_part_data_end, subboard_data->m_part_number_datas.end());
        }
        for (auto& subboard : m_graphics_params->m_sub_board_datas)
        {
            for (auto& part_number : subboard->m_part_number_datas)
            {
                UpdateDeviceIds(part_number);
            }
        }
        return true;
    }

    bool ShowListModel::DeleteDeviceFromPartNumber(const std::string& delete_device_name, PartNumberDataStructPtr part_number)
    {
        auto delete_device_if = std::remove_if(
            part_number->m_device_datas.begin(),
            part_number->m_device_datas.end(),
            [delete_device_name](std::shared_ptr<DeviceDataStruct> device_data) {
                return device_data->m_device_name.compare(delete_device_name) == 0;
            });
        part_number->m_device_datas.erase(delete_device_if, part_number->m_device_datas.end());
        return true;
    }

    bool ShowListModel::UpdateDeviceIds(PartNumberDataStructPtr part_number)
    {
        // 遍历料号中的所有元件数据
        for (size_t device_id = 0; device_id < part_number->m_device_datas.size(); ++device_id)
        {
            // 更新每个元件的 ID，使其从 1 开始连续编号
            part_number->m_device_datas[device_id]->m_id = int(device_id) + 1;
        }
        return true;
    }

    bool ShowListModel::DeleteSubboard(const std::vector<std::string>& subboard_names)
    {
        // 遍历所有需要删除的子板名称
        for (auto delete_subboard_name : subboard_names)
        {
            // 使用 std::remove_if 算法从子板列表中移除匹配的子板
            auto delete_subboard_name_end = std::remove_if(
                m_graphics_params->m_sub_board_datas.begin(),
                m_graphics_params->m_sub_board_datas.end(),
                [delete_subboard_name](std::shared_ptr<SubBoardDataStruct> subboard_data) {
                    // 匹配条件：子板名称是否等于需要删除的子板名称
                    return subboard_data->m_subboard_name == delete_subboard_name;
                });
            // 删除所有匹配的子板
            m_graphics_params->m_sub_board_datas.erase(delete_subboard_name_end, m_graphics_params->m_sub_board_datas.end());
            // 遍历剩余的子板数据
            for (auto& subboard_data : m_graphics_params->m_sub_board_datas)
            {
                // 使用 std::remove_if 算法从料号列表中移除属于被删除子板的料号
                auto delete_part_data_end = std::remove_if(
                    subboard_data->m_part_number_datas.begin(),
                    subboard_data->m_part_number_datas.end(),
                    [delete_subboard_name](std::shared_ptr<PartNumberDataStruct> part_data) {
                        // 匹配条件：料号所属的子板名称是否等于需要删除的子板名称
                        return part_data->m_subboard_name == delete_subboard_name;
                    });
                // 删除所有匹配的料号
                subboard_data->m_part_number_datas.erase(delete_part_data_end, subboard_data->m_part_number_datas.end());
            }
        }
        return true;
    }

    bool ShowListModel::FindGraphicsIDFromDevice(std::vector<DeviceDataStructPtr> devices, std::string graphics_id)
    {
        // 遍历元件列表，查找与给定 graphics_id 匹配的元件
        for (int device_id = 0; device_id < devices.size(); device_id++)
        {
            auto device = devices.at(device_id);  // 获取当前元件对象
            // 比较元件名称与目标 graphics_id 是否匹配
            if (device->m_device_name.compare(graphics_id) == 0)
            {
                // 如果找到匹配的元件，更新显示列表参数：
                m_graphics_params->show_list_params.m_device_id = device_id;
                m_graphics_params->show_list_params.m_update_device_data = true;
                return true;  // 返回 true 表示找到匹配的元件
            }
        }
        // 如果遍历完所有元件仍未找到匹配项，返回 false
        return false;
    }

    void ShowListModel::Query(const jrsdata::QueryListViewParam& param)
    {
        // 根据查询类型执行不同的查询操作
        switch (param.m_type)
        {
        case SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER:  // 查询料号名称
            QueryPartNumber(param.m_query_condition.c_str(), param.m_show_type);  // 根据料号名称进行查询
            break;
        case SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER_FOLLOW:  // 查询料号跟随名称
            QueryPartNumberFollow(param.m_query_condition.c_str(), param.m_show_type);  // 根据料号跟随名称进行查询
            break;
        case SEARCH_SHOW_LIST_TYPE::SEARCH_DEVICE_NAME:  // 查询元件名称
            QueryDeviceName(param.m_query_condition.c_str(), param.m_show_type);  // 根据元件名称进行查询
            break;
        }
        m_graphics_params->show_list_params.m_subboard_id = 0;
        m_graphics_params->show_list_params.m_part_number_id = 0;
        m_graphics_params->show_list_params.m_device_id = 0;
        m_graphics_params->show_list_params.m_update_subboard_data = true;
        m_graphics_params->show_list_params.m_update_part_number_data = true;
        m_graphics_params->show_list_params.m_update_device_data = true;
    }
    void ShowListModel::QueryPartNumber(QString part_number, SHOW_TYPE show_type)
    {
        QueryCommon(part_number, show_type, [](PartNumberDataStructPtr& part_number_data, DeviceDataStructPtr& device_data, QString& query)
            {
                (void)device_data;
                QString name = part_number_data->m_part_number.c_str();
                return name.toUpper().contains(query.toUpper());
            });
    }
    void ShowListModel::QueryPartNumberFollow(QString part_number, SHOW_TYPE show_type)
    {
        QueryCommon(part_number, show_type, [](PartNumberDataStructPtr& part_number_data, DeviceDataStructPtr& device_data, QString& query)
            {
                (void)device_data;
                QString name = part_number_data->m_part_number_follow.c_str();
                return name.toUpper().contains(query.toUpper());
            });
    }
    void ShowListModel::QueryDeviceName(QString device_name, SHOW_TYPE show_type)
    {
        QueryCommon(device_name, show_type, [](PartNumberDataStructPtr& part_number_data, DeviceDataStructPtr& device_data, QString& query)
            {
                (void)part_number_data;
                QString name = device_data->m_device_name.c_str();
                if (name.toUpper().contains(query.toUpper()))
                {
                    return true;
                }
                return false;
            });
    }
    void ShowListModel::UpdateDeviceShowState(DeviceDataStructPtr& device_data, SHOW_TYPE show_type)
    {
        switch (show_type) {
        case SHOW_TYPE::SHOW_ALL_DEVICE:
            device_data->m_show_enable = true;
            break;
        case SHOW_TYPE::SHOW_UN_EDITED:
            device_data->m_show_enable = true;
            break;
        case SHOW_TYPE::SHOW_UN_TEST:
            device_data->m_show_enable = !device_data->m_body_tested;
            break;
        case SHOW_TYPE::SHOW_NG:
            device_data->m_show_enable = (device_data->m_result_state == RESULT_STATE::RESULT_NG);
            break;
        }
    }
    void ShowListModel::QueryCommon(QString& query, SHOW_TYPE show_type,
        std::function<bool(PartNumberDataStructPtr&, DeviceDataStructPtr& device_data, QString&)> query_part_number_or_device)
    {
        if (!m_graphics_params)
        {
            return;
        }

        for (auto& subboard_data : m_graphics_params->m_sub_board_datas) 
        {
            bool show_subboard = false;
            for (auto& part_number_data : subboard_data->m_part_number_datas) 
            {
                bool part_enable = false;

                for (auto& device_data : part_number_data->m_device_datas) 
                {
                    if (query_part_number_or_device(part_number_data,device_data, query)) 
                    {
                        UpdateDeviceShowState(device_data, show_type);
                        part_enable |= device_data->m_show_enable;
                    }
                    else 
                    {
                        device_data->m_show_enable = false;
                    }
                }

                part_number_data->m_show_enable = part_enable;
                show_subboard |= part_enable;
            }
            subboard_data->m_show_enable = show_subboard;
        }
    }
    // 获取显示列表视图参数
    ShowListViewParam ShowListModel::GetShowListViewParam()
    {
        return m_graphics_params->show_list_params;
    }

    // 获取图形参数（项目更新事件参数）
    jrsdata::GraphicsUpdateProjectEventParamPtr ShowListModel::GetGraphicsParam()
    {
        return m_graphics_params;
    }

    // 清理数据
    bool ShowListModel::CleanDatas()
    {
        // 重置显示列表视图参数为默认值
        m_graphics_params->show_list_params = ShowListViewParam();
        // 清空子板数据列表
        m_graphics_params->m_sub_board_datas.clear();
        return true;
    }
}