//QT
#include <QLabel>
#include <QString>
//Custom
#include "StatusBarView.h"

namespace jrsaoi
{


    StatusBarView::StatusBarView(QWidget* parent)
        :QWidget(parent)
    {
        Init();
    }

    StatusBarView::~StatusBarView()
    {

    }

    void StatusBarView::Init()
    {
        InitMember();
        InitView();
    }

    void StatusBarView::InitMember()
    {
        layout_view = new QHBoxLayout(this);
    }

    void StatusBarView::InitView()
    {
        setObjectName("StatusBarView");
        setFixedHeight(20);
        layout_view->setContentsMargins(0, 0, 0, 0);
        layout_view->setSpacing(0);
        status_camera = GetLabel("相机");
        status_x = GetLabel("X轴");
        status_y = GetLabel("Y轴");
        status_z = GetLabel("Z轴");
        status_dlp_1 = GetLabel("DLP1");
        status_dlp_2 = GetLabel("DLP2");
        status_dlp_3 = GetLabel("DLP3");
        status_dlp_4 = GetLabel("DLP4");
        status_card_1 = GetLabel("板卡1");
        status_card_2 = GetLabel("板卡2");
        layout_view->addWidget(status_camera);
        layout_view->addWidget(status_x);
        layout_view->addWidget(status_y);
        layout_view->addWidget(status_z);
        layout_view->addWidget(status_card_1);
        layout_view->addWidget(status_card_2);
        layout_view->addWidget(status_dlp_1);
        layout_view->addWidget(status_dlp_2);
        layout_view->addWidget(status_dlp_3);
        layout_view->addWidget(status_dlp_4);

        SetLabelColor(status_camera, true);
        SetLabelColor(status_x, false);
        SetLabelColor(status_y, true);
        SetLabelColor(status_z, false);
        SetLabelColor(status_dlp_1, true);
        SetLabelColor(status_dlp_2, false);
        SetLabelColor(status_dlp_3, true);
        SetLabelColor(status_dlp_4, false);
        SetLabelColor(status_card_1, true);
        SetLabelColor(status_card_2, false);


    }

    QFrame* StatusBarView::GetLine()
    {
        QFrame* line = new QFrame();
        line->setFrameShape(QFrame::VLine);
        line->setFrameShadow(QFrame::Plain);
        line->setFixedHeight(20);
        line->setFixedWidth(3);
        return line;
    }

    QLabel* StatusBarView::GetLabel(std::string name)
    {
        QLabel* label = new QLabel(name.c_str());
        //label->setStyleSheet("background:red;");
        label->setFont(QFont("黑体", 12));
        label->setAlignment(Qt::AlignCenter);
        label->setFixedHeight(20);
        return label;
    }

    void StatusBarView::SetLabelColor(QLabel* label, bool status)
    {
        if (label == nullptr)return;
        if (status)
        {
            label->setStyleSheet("background:green;color:white;");
        }
        else
        {
            label->setStyleSheet("background:red;color:white;");
        }
    }


}

