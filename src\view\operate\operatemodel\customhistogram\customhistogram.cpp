//CUSTOm
#include "CustomHistogram.h"
//QT
#include <QPainter>
#include <QBrush>
//STL
#include <qmath.h>

const double COORDINATE_CALIBRATION_LENGTH = 4;    // 坐标刻度的长度
const double COOR_X_ZERO_LEFT = 25;
const double COOR_X_ZERO_RIGHT = 0;
const double COOR_Y_ZERO_TOP = 20;
const double COOR_Y_ZERO_BOTTOM = 20;

CustomHistogram::CustomHistogram(QWidget* parent) : QWidget(parent)
{
    Init();
    SetPercent(true);
    SetShowCount(true);
}
CustomHistogram::~CustomHistogram()
{
}
void CustomHistogram::Init()
{
    m_group_enable = true;
    m_group_num = 2;
    m_colors =
    {
            QColor(0, 255, 0),
            QColor(255, 0, 0),
            QColor(55, 173, 107),
            QColor(255, 113, 247),
            QColor(164, 145, 224),
            QColor(255, 134, 116),
            QColor(254, 136, 0),
            QColor(194, 102, 87),
            QColor(186, 156, 148),
            QColor(0, 0, 0)
    };
}
void CustomHistogram::paintEvent(QPaintEvent*)
{
    QPainter painter(this);
    painter.translate(COOR_X_ZERO_LEFT, height() - COOR_Y_ZERO_BOTTOM);
    painter.scale(1, -1);
    DrawHistogram(&painter);
}
void CustomHistogram::SetShowCount(bool show_count)
{
    m_show_count = show_count;
}
void CustomHistogram::DrawHistogram(QPainter* painter)
{
    if (!painter)
    {
        return; // 确保 painter 不为空，且有数据可绘制
    }
    painter->save(); // 保存绘图状态
    // 初始化绘图区域参数
    m_start_pos.setX(0);
    m_start_pos.setY(0);
    m_axis_width = width() - COOR_X_ZERO_LEFT - COOR_X_ZERO_RIGHT - 2.0;
    m_axis_height = height() - COOR_Y_ZERO_BOTTOM - COOR_Y_ZERO_TOP - 2.0;
    // 计算每个柱状图的百分比和标签
    CalculateHistogramPercentages();
    // 更新柱状图矩形的位置和大小
    UpdateHistogramRectangles();
    PaintCoordinateCalibration(painter);
    // 绘制柱状图
    DrawHistogramBars(painter);
    // 绘制坐标轴及刻度和标签
    PaintCoordinateAxis(painter);
    PaintCoordinateValue(painter);
    painter->restore(); // 恢复绘图状态
}

void CustomHistogram::CalculateHistogramPercentages()
{
    std::vector<double> group_sums = CalculateGroupSums();
    for (int i = 0; i < int(m_ring_data.size()); ++i)
    {
        int group_index = GetGroupIndex(i);
        double group_sum = 100;
        if (m_group_enable)
        {
            group_sum = group_sums[group_index];
        }
        else
        {
            group_sum = group_sums[0];
        }
        if (group_sum < 1)
        {
            m_ring_data[i].m_percent = 0;
        }
        else
        {
            m_ring_data[i].m_percent = m_ring_data[i].m_value / group_sum * 100.0;
        }
        m_ring_data[i].m_color_idx = int(i % m_colors.size());
        m_ring_data[i].m_label_precent.clear();
        if (m_show_percent)
        {
            m_ring_data[i].m_label_precent.push_back(
                QString("%1%").arg(QString::number(m_ring_data[i].m_percent, 'f', 2)).toStdString());
        }
        if (m_show_count)
        {
            m_ring_data[i].m_label_precent.push_back(
                QString("%1").arg(m_ring_data[i].m_value).toStdString());
        }
    }
}
std::vector<double> CustomHistogram::CalculateGroupSums()
{
    std::vector<double> sums;
    if (m_group_enable)
    {
        for (int i = 0; i < int(m_ring_data.size()); i += m_group_num)
        {
            double sum = GetSumValue(i, i + m_group_num - 1);
            sums.push_back(sum);
        }
    }
    else
    {
        sums.push_back(GetSumValue(0, int(m_ring_data.size()) - 1));
    }
    return sums;
}
int CustomHistogram::GetGroupIndex(int index)
{
    return index / 2;
}
void CustomHistogram::UpdateHistogramRectangles()
{
    double one_width = m_axis_width / m_ring_data.size();
    m_max_percent = 100.0;
    for (size_t i = 0; i < m_ring_data.size(); ++i)
    {
        double bar_height = m_max_percent < 1 ? 0 : (m_ring_data[i].m_percent / m_max_percent * m_axis_height);
        m_ring_data[i].m_rect = new CustomRect(i * one_width + 5, 0, one_width - 10, bar_height);
    }
}
void CustomHistogram::DrawHistogramBars(QPainter* painter)
{
    for (const auto& item : m_ring_data)
    {
        painter->setPen(Qt::white);
        painter->setBrush(QBrush(m_colors.at(item.m_color_idx)));
        painter->drawRect(QRect(item.m_rect->x, item.m_rect->y, item.m_rect->width, item.m_rect->height));
    }
}
void CustomHistogram::SetGroupEnable(bool value)
{
    m_group_enable = value;
}
void CustomHistogram::SetGroupNum(int value)
{
    m_group_num = value;
}
double CustomHistogram::GetSumValue(int start, int end)
{
    double sum = 0;
    for (int i = start; i <= end && i < m_ring_data.size(); i++)
    {
        sum += m_ring_data.at(i).m_value;
    }
    return sum;
}
void CustomHistogram::SetDataInfo(std::vector<RingData> data_)
{
    m_ring_data = data_;
    repaint();
}
void CustomHistogram::SetPercent(bool percent)
{
    m_show_percent = percent;
    this->update();
}
void CustomHistogram::ClearHistogram()
{
    m_ring_data.clear();
    repaint();
}

void CustomHistogram::PaintCoordinateAxis(QPainter* painter)
{
    painter->setFont(QFont("宋体", 10));
    painter->setPen(QPen(Qt::black));
    QPoint axis_start_point;
    QPoint axis_x_end_point; // x 轴终点
    QPoint axis_y_end_point; // y 轴终点
    axis_start_point.setX(m_start_pos.x());
    axis_start_point.setY(m_start_pos.y());
    axis_x_end_point.setX(m_start_pos.x() + m_axis_width);
    axis_x_end_point.setY(m_start_pos.y());
    axis_y_end_point.setX(m_start_pos.x());
    axis_y_end_point.setY(m_start_pos.y() + m_axis_height);
    painter->drawLine(axis_start_point, axis_x_end_point);
    painter->drawLine(axis_start_point, axis_y_end_point);
}

void CustomHistogram::PaintCoordinateCalibration(QPainter* painter)
{
    if (m_max_percent < 1)return;
    double delta_x = m_axis_width / m_ring_data.size();  // X 轴坐标刻度宽度
    double delta_y = m_axis_height / m_max_percent; // Y 轴坐标刻度宽度
    painter->setFont(QFont("宋体", 10));
    painter->setPen(QPen(Qt::black));
    QPoint calibration_start_point;
    QPoint calibration_end_point;
    for (double i = m_start_pos.x(); i < m_axis_width + m_start_pos.x(); )
    {
        // 横坐标位置每次递增
        i = i + delta_x;
        // 坐标刻度起始点
        calibration_start_point.setX(i);
        calibration_start_point.setY(m_start_pos.y());
        // 坐标刻度结束点
        calibration_end_point.setX(i);
        calibration_end_point.setY(m_start_pos.y() - COORDINATE_CALIBRATION_LENGTH);
        painter->drawLine(calibration_start_point, calibration_end_point);
    }
    bool draw_dash = false;
    for (double i = m_start_pos.y() + delta_y; i <= m_axis_height + m_start_pos.y() + delta_y * 20; i += delta_y * 20)
    {
        // 坐标刻度起始点
        calibration_start_point.setX(m_start_pos.x()); // x 轴不变，y 轴变
        calibration_start_point.setY(i);
        // 坐标刻度结束点
        calibration_end_point.setX(m_start_pos.x() - COORDINATE_CALIBRATION_LENGTH);
        calibration_end_point.setY(i);
        painter->drawLine(calibration_start_point, calibration_end_point);
        calibration_end_point.setX(m_start_pos.x() + m_axis_width);
        if (draw_dash)
        {
            QPen pen;
            pen.setStyle(Qt::DashLine);
            pen.setColor(Qt::gray);
            painter->setPen(pen);
            painter->drawLine(calibration_start_point, calibration_end_point);
            pen.setStyle(Qt::SolidLine);
            pen.setColor(Qt::black);
            painter->setPen(pen);
        }
        draw_dash = true;
    }
}

void CustomHistogram::PaintCoordinateValue(QPainter* painter)
{
    painter->setFont(QFont("宋体", 9));
    painter->setPen(QPen(Qt::black));
    int widget_height = height();
    QFontMetrics font_metrics = painter->fontMetrics();
    double text_one_height = font_metrics.height();
    double y_one_value = 20.0;
    int axis_y_value = 0;
    QPoint temp;
    double delta_x = m_axis_width / m_ring_data.size();  // X 轴坐标刻度宽度
    double delta_y = m_axis_height / m_max_percent * y_one_value; // Y 轴坐标刻度宽度
    painter->translate(COOR_X_ZERO_LEFT, widget_height - COOR_Y_ZERO_BOTTOM);
    painter->scale(1, -1);
    for (double i = 0; i <= m_max_percent / y_one_value; i++)
    {
        QString str_axis_y_value = QString("%1").arg(axis_y_value);
        temp.setX(m_start_pos.x() - COOR_X_ZERO_LEFT - font_metrics.horizontalAdvance(str_axis_y_value) - 4);
        temp.setY(widget_height - i * delta_y - COOR_Y_ZERO_BOTTOM + 1);
        painter->drawText(temp, str_axis_y_value);
        axis_y_value += y_one_value;
    }
    QString str_left_top = "(%)";
    painter->drawText(QPoint(0 - COOR_X_ZERO_LEFT - font_metrics.horizontalAdvance(str_left_top), widget_height - text_one_height + 7), str_left_top);
    for (double i = 0; i < m_ring_data.size(); i++)
    {
        temp.setX(m_start_pos.x() + i * delta_x + (delta_x - font_metrics.horizontalAdvance(m_ring_data.at(i).m_label.c_str())) / 2.0 - COOR_X_ZERO_LEFT);
        temp.setY(widget_height - 2);
        painter->drawText(temp, m_ring_data.at(i).m_label.c_str());
        for (int j = 0; j < m_ring_data.at(i).m_label_precent.size(); j++)
        {
            temp.setX(double(m_start_pos.x() + i * delta_x + (delta_x - font_metrics.horizontalAdvance(m_ring_data.at(i).m_label_precent.at(j).c_str())) / 2.0 - COOR_X_ZERO_LEFT));
            temp.setY(widget_height - COOR_Y_ZERO_BOTTOM - m_ring_data.at(i).m_rect->height - text_one_height * j - 2);
            painter->drawText(temp, m_ring_data.at(i).m_label_precent.at(j).c_str());
        }
    }
}