﻿#include "parametersettings.h"
#pragma warning(push, 3)
#include "ui_parametersettings.h"
#pragma warning(pop)

#include "paramoperator.h"

ParameterSettings::ParameterSettings(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ParameterSettings)
{
    ui->setupUi(this);
    config_has_update = false;
    stop_board_index = -1;
    operateparam = std::make_shared<jrsdata::OperateViewParam>();
    //InitTrackView();
    InitConnect();
}
ParameterSettings::~ParameterSettings()
{
    delete ui;
}
//void ParameterSettings::InitTrackView()
//{
//    ShowTrack2(false);
//}
void ParameterSettings::UpdateViewMachineParam(const jrsdata::MachineParam& machine_param_)
{
    ui->data_setting->UpdateView(machine_param_);
    ui->data_view->UpdateView(machine_param_);
}
void ParameterSettings::PathPlanningToggled(bool check)
{
    (void)check;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PATH_PLANNING_INT, ui->rb_standard_path_planning_mode->isChecked() ? 0 : 1);
}

void ParameterSettings::PathPatternToggled(int index)
{
    (void)index;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PATH_PATTERN_INT, ui->path_pattern->currentIndex());
}

void ParameterSettings::BarcodeToggled(bool check)
{
    (void)check;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BARCODE_INT, ui->rb_barcode_optimization_recognition->isChecked() ? 0 : 1);
}
void ParameterSettings::BadBoardMarkToggled(bool check)
{
    (void)check;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BAD_BOARD_MARK_INT, ui->rb_bad_board_mark_priority_recognition->isChecked() ? 0 : 1);
}
void ParameterSettings::SubBoardPositioningPointToggled(bool check)
{
    (void)check;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_SUB_BOARD_POSITIONING_POINT_INT, ui->rb_sub_board_positioning_point_priority_recognition->isChecked() ? 0 : 1);
}
void ParameterSettings::InspectionAreaToggled(bool check)
{
    (void)check;
    int value = 0;
    if (ui->rb_inspection_area_sub_board_area->isChecked())
    {
        value = 1;
    }
    if (ui->rb_inspection_area_manually_set_area->isChecked())
    {
        value = 2;
    }
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_INSPECTION_AREA_INT, value);
}
void ParameterSettings::BadBoardSettingToggled(bool check)
{
    (void)check;
    int value = 0;
    if (ui->rb_bad_board_setting_manual_specification->isChecked())
    {
        value = 1;
    }
    if (ui->rb_bad_board_setting_bad_board_mark->isChecked())
    {
        value = 2;
    }
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BAD_BOARD_SETTING_INT, value);
}
void ParameterSettings::EnableRejectionTestToggled(bool check)
{
    (void)check;
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENABLE_REJECTION_TEST_INT, ui->cb_enable_rejection_test->isChecked() ? 1 : 0);
}
void ParameterSettings::MinimumHeightFinished()
{
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MINIMUM_HEIGHT_FLOAT, static_cast<float>(ui->edt_minimum_height->value()));
}
void ParameterSettings::MinimumAreaFinished()
{
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MINIMUM_AREA_FLOAT, static_cast<float>(ui->edt_minimum_area->value()));
}
void ParameterSettings::MaskExpansionFinished()
{
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MASK_EXPANSION_FLOAT, static_cast<float>(ui->edt_mask_expansion->value()));
}
void ParameterSettings::ProductSwitchCurrentIndexChange(int index)
{
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PRODUCT_SWITCH_INT, index);
}
void ParameterSettings::PercentageDynamicShieldingFinished()
{
    MachineParamChangeSave(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PERCENTAGE_DYNAMIC_SHIELDING_FLOAT, static_cast<float>(ui->percentage_dynamic_shielding->value()));
}
void ParameterSettings::showEvent(QShowEvent* event)
{
    (void)event;
    std::cout << __FUNCTION__ << std::endl;
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();

    auto func_string = [&](const std::string& name_) -> decltype(auto) {
        return param_operate->GetSettingParamValueByName<std::string>(jrsdata::ParamLevel::MACHINE, name_);
        };
    auto func_float = [&](const std::string& name_) -> decltype(auto) {
        return param_operate->GetSettingParamValueByName<float>(jrsdata::ParamLevel::MACHINE, name_);
        };
    auto func_int = [&](const std::string& name_) -> decltype(auto) {
        return param_operate->GetSettingParamValueByName<int>(jrsdata::ParamLevel::MACHINE, name_);
        };

    auto machine_param_path_planning_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PATH_PLANNING_INT);
    if (machine_param_path_planning_int == 0)
    {
        ui->rb_standard_path_planning_mode->setChecked(true);
    }
    else
    {
        ui->rb_optimized_path_planning->setChecked(true);
    }
    // 轨迹模式
    ui->path_pattern->setCurrentIndex(func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PATH_PATTERN_INT));

    auto machine_param_barcode_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BARCODE_INT);
    if (machine_param_barcode_int == 0)
    {
        ui->rb_barcode_optimization_recognition->setChecked(true);
    }
    else
    {
        ui->rb_barcode_same_component_recognition->setChecked(true);
    }
    auto machine_param_bad_board_mark_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BAD_BOARD_MARK_INT);
    if (machine_param_bad_board_mark_int == 0)
    {
        ui->rb_bad_board_mark_priority_recognition->setChecked(true);
    }
    else
    {
        ui->rb_bad_board_mark_same_component_recognition->setChecked(true);
    }
    auto machine_param_sub_board_positioning_point_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_SUB_BOARD_POSITIONING_POINT_INT);
    if (machine_param_sub_board_positioning_point_int == 0)
    {
        ui->rb_sub_board_positioning_point_priority_recognition->setChecked(true);
    }
    else
    {
        ui->rb_sub_board_positioning_point_same_component_recognition->setChecked(true);
    }
    auto machine_param_inspection_area_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_INSPECTION_AREA_INT);
    switch (machine_param_inspection_area_int)
    {
    case 0:
        ui->rb_inspection_area_scanning_area->setChecked(true);
        break;
    case 1:
        ui->rb_inspection_area_sub_board_area->setChecked(true);
        break;
    default:
        ui->rb_inspection_area_manually_set_area->setChecked(true);
        break;
    }
    auto machine_param_bad_board_setting_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_BAD_BOARD_SETTING_INT);
    switch (machine_param_bad_board_setting_int)
    {
    case 0:
        ui->rb_bad_board_setting_percentage_dynamic_shielding->setChecked(true);
        break;
    case 1:
        ui->rb_bad_board_setting_manual_specification->setChecked(true);
        break;
    default:
        ui->rb_bad_board_setting_bad_board_mark->setChecked(true);
        break;
    }
    auto machine_param_enable_rejection_test_int = func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENABLE_REJECTION_TEST_INT);
    ui->cb_enable_rejection_test->setChecked(machine_param_enable_rejection_test_int == 1);
    ui->edt_minimum_height->setValue(func_float(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MINIMUM_HEIGHT_FLOAT));
    ui->edt_minimum_area->setValue(func_float(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MINIMUM_AREA_FLOAT));
    ui->edt_mask_expansion->setValue(func_float(jrssettingparam::jrsmachineparam::MACHINE_PARAM_MASK_EXPANSION_FLOAT));
    ui->percentage_dynamic_shielding->setValue(func_float(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PERCENTAGE_DYNAMIC_SHIELDING_FLOAT));
    ui->cb_product_switch->setCurrentIndex(func_int(jrssettingparam::jrsmachineparam::MACHINE_PARAM_PRODUCT_SWITCH_INT));
}
void ParameterSettings::InitConnect()
{
    QObject::connect(ui->track_width1_get, &QPushButton::clicked, this, [=]() {
        stop_board_index = -1;
        emit SigGetBoardWith();
        });
    QObject::connect(ui->track_width2_get, &QPushButton::clicked, this, [=]() {
        stop_board_index = -1;
        emit SigGetBoardWith();
        });
    // 轨道1左下
    QObject::connect(ui->track1_leftbottom_btn, &QPushButton::clicked, this, [=]() {
        stop_board_index = 0;
        emit SigGetBoardWith();
        });
    // 轨道1右下
    QObject::connect(ui->track1_rightbottom_btn, &QPushButton::clicked, this, [=]() {
        stop_board_index = 1;
        emit SigGetBoardWith();
        });
    // 轨道2左上
    QObject::connect(ui->track2_lefttop_btn, &QPushButton::clicked, this, [=]() {
        stop_board_index = 2;
        emit SigGetBoardWith();
        });
    // 轨道2右上
    QObject::connect(ui->track2_righttop_btn, &QPushButton::clicked, this, [=]() {
        stop_board_index = 3;
        emit SigGetBoardWith();
        });
    // 轨道类型
    QObject::connect(ui->track_type, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
        ShowTrack2(index != 0);
        operateparam->config_setting_param.motion_setting.trackCout = index + 1;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 双轨设置
    QObject::connect(ui->track_module_13, &QRadioButton::toggled, this, [this](bool checkd) {
        operateparam->config_setting_param.motion_setting.trackSelection = checkd ? 0 : 1;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    QObject::connect(ui->track_module_14, &QRadioButton::toggled, this, [this](bool checkd) {
        operateparam->config_setting_param.motion_setting.trackSelection = checkd ? 1 : 0;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 自动板宽
    QObject::connect(ui->track_auto_width, &QCheckBox::stateChanged, this, [this](int state) {
        operateparam->config_setting_param.motion_setting.autotrack = (state == Qt::Checked);
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    //// 板宽1零点基准
    //QObject::connect(ui->track_width_base1, &QLineEdit::textChanged, this, [this](const QString str) {
    //    if (str != "")
    //    {
    //        bool ok;
    //        double value = str.toDouble(&ok);
    //        if (ok && value > 0)
    //        {
    //            operateparam->config_setting_param.motion_setting.board_width_base[0] = value;
    //            if (!config_has_update)
    //            {
    //                return;
    //            }
    //            emit SigSaveSetting(operateparam);
    //        }
    //    }
    //    });
    //// 板宽2零点基准
    //QObject::connect(ui->track_width_base2, &QLineEdit::textChanged, this, [this](const QString str) {
    //    if (str != "")
    //    {
    //        bool ok;
    //        double value = str.toDouble(&ok);
    //        if (ok && value > 0)
    //        {
    //            operateparam->config_setting_param.motion_setting.board_width_base[1] = value;
    //            if (!config_has_update)
    //            {
    //                return;
    //            }
    //            emit SigSaveSetting(operateparam);
    //        }
    //    }
    //    });
    // 定板测试
    QObject::connect(ui->track_fixed_test, &QCheckBox::stateChanged, this, [this](int state) {
        operateparam->config_setting_param.motion_setting.fixedTest = (state == Qt::Checked);
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 模式设置
    QObject::connect(ui->track_mode, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
        operateparam->config_setting_param.motion_setting.trackMode = index;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 老化次数
    QObject::connect(ui->track_repeat, &QLineEdit::textChanged, this, [this](const QString str) {
        if (str != "")
        {
            bool ok;
            int value = str.toInt(&ok);
            if (ok && value > 0)
            {
                operateparam->config_setting_param.motion_setting.cycleCount = value;
                if (!config_has_update)
                {
                    return;
                }
                emit SigSaveSetting(operateparam);
            }
        }
        });
    // 进出板方向
    QObject::connect(ui->track_direction, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
        if (index == 0)
        {
            operateparam->config_setting_param.motion_setting.enterDirection = 0;
            operateparam->config_setting_param.motion_setting.leaveDirection = 0;
        }
        else if (index == 1)
        {
            operateparam->config_setting_param.motion_setting.enterDirection = 0;
            operateparam->config_setting_param.motion_setting.leaveDirection = 1;
        }
        else if (index == 2)
        {
            operateparam->config_setting_param.motion_setting.enterDirection = 1;
            operateparam->config_setting_param.motion_setting.leaveDirection = 0;
        }
        else if (index == 3)
        {
            operateparam->config_setting_param.motion_setting.enterDirection = 1;
            operateparam->config_setting_param.motion_setting.leaveDirection = 1;
        }
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    //QObject::connect(ui->track_export_direction, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
    //    operateparam->config_setting_param.motion_setting.leaveDirection = index;
    //    if (!config_has_update)
    //    {
    //        return;
    //    }
    //    emit SigSaveSetting(operateparam);
    //    });
    // 直通模式

    QObject::connect(ui->track_trans1, &QCheckBox::stateChanged, this, [this](int state) {
        if (operateparam->config_setting_param.motion_setting.transportMode.size() < 1)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.transportMode[0] = (state == Qt::Checked);
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    QObject::connect(ui->track_trans2, &QCheckBox::stateChanged, this, [this](int state) {
        if (operateparam->config_setting_param.motion_setting.transportMode.size() < 2)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.transportMode[1] = (state == Qt::Checked);
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 出板方式
    QObject::connect(ui->track_normal, &QRadioButton::toggled, this, [this](bool checkd) {
        if (operateparam->config_setting_param.motion_setting.unLoadMode.size() < 2)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.unLoadMode[1] = !checkd;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    QObject::connect(ui->track_force_without_station, &QRadioButton::toggled, this, [this](bool checkd) {
        if (operateparam->config_setting_param.motion_setting.unLoadMode.size() < 2)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.unLoadMode[1] = checkd;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 检测结果
    QObject::connect(ui->basedon_test_result, &QRadioButton::toggled, this, [this](int checkd) {
        if (operateparam->config_setting_param.motion_setting.unLoadMode.size() < 1)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.unLoadMode[0] = !checkd;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    QObject::connect(ui->basedon_review_result, &QRadioButton::toggled, this, [this](int checkd) {
        if (operateparam->config_setting_param.motion_setting.unLoadMode.size() < 1)
        {
            return;
        }
        operateparam->config_setting_param.motion_setting.unLoadMode[0] = checkd;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    // 硬件条码枪
    QObject::connect(ui->track_barcode, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
        operateparam->config_setting_param.motion_setting.barcode_mode = index;
        if (!config_has_update)
        {
            return;
        }
        emit SigSaveSetting(operateparam);
        });
    QObject::connect(ui->track_width1_set, &QPushButton::clicked, this, [=]() {
        jrsdata::MoveParam param;
        param.axis_index = 4;
        double base1 = operateparam->config_setting_param.motion_setting.board_width_base.at(0);
        param.axis_diatance = base1 - ui->track_width1->text().toDouble();
        param.axis_speed = 50;
        param.axis_movetype = jrsdata::MoveType::Mova;
        operateparam->device_param.event_name = "SingleMovea";
        //operateparam->device_param.motion_param.motion_move = param;
        operateparam->device_param.device_type = jrsdata::DeviceType::Motion;
        emit SigSetBoardWidth(operateparam);
        });
    QObject::connect(ui->track_width2_set, &QPushButton::clicked, this, [=]() {
        jrsdata::MoveParam param;
        param.axis_index = -1;
        if (ui->track_module_13->isChecked())
        {
            param.axis_index = 6;
        }
        if (ui->track_module_14->isChecked())
        {
            param.axis_index = 5;
        }
        if (param.axis_index == -1)
        {
            return;
        }
        param.axis_diatance = ui->track_width2->text().toDouble();
        param.axis_speed = 100;
        param.axis_movetype = jrsdata::MoveType::Mova;
        operateparam->device_param.event_name = "SingleMovea";
        //operateparam->device_param.motion_param.motion_move = param;
        operateparam->device_param.device_type = jrsdata::DeviceType::Motion;
        emit SigSetBoardWidth(operateparam);
        });
    // 回零
    QObject::connect(ui->track_home, &QPushButton::clicked, this, [=]() {
        jrsdata::MoveParam param;
        param.axis_index = 4; // 暂时就当做单轨来
        operateparam->device_param.event_name = "Home";
       // operateparam->device_param.motion_param.motion_move = param;
        operateparam->device_param.device_type = jrsdata::DeviceType::Motion;
        emit SigSetBoardWidth(operateparam);
        });
    QObject::connect(ui->rb_standard_path_planning_mode, &QRadioButton::toggled, this, &ParameterSettings::PathPlanningToggled);
    QObject::connect(ui->path_pattern, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ParameterSettings::PathPatternToggled);

    QObject::connect(ui->rb_optimized_path_planning, &QRadioButton::toggled, this, &ParameterSettings::PathPlanningToggled);
    QObject::connect(ui->rb_barcode_optimization_recognition, &QRadioButton::toggled, this, &ParameterSettings::BarcodeToggled);
    QObject::connect(ui->rb_barcode_same_component_recognition, &QRadioButton::toggled, this, &ParameterSettings::BarcodeToggled);
    QObject::connect(ui->rb_bad_board_mark_priority_recognition, &QRadioButton::toggled, this, &ParameterSettings::BadBoardMarkToggled);
    QObject::connect(ui->rb_bad_board_mark_same_component_recognition, &QRadioButton::toggled, this, &ParameterSettings::BadBoardMarkToggled);
    QObject::connect(ui->rb_sub_board_positioning_point_priority_recognition, &QRadioButton::toggled, this, &ParameterSettings::SubBoardPositioningPointToggled);
    QObject::connect(ui->rb_sub_board_positioning_point_same_component_recognition, &QRadioButton::toggled, this, &ParameterSettings::SubBoardPositioningPointToggled);
    QObject::connect(ui->rb_inspection_area_scanning_area, &QRadioButton::toggled, this, &ParameterSettings::InspectionAreaToggled);
    QObject::connect(ui->rb_inspection_area_sub_board_area, &QRadioButton::toggled, this, &ParameterSettings::InspectionAreaToggled);
    QObject::connect(ui->rb_inspection_area_manually_set_area, &QRadioButton::toggled, this, &ParameterSettings::InspectionAreaToggled);
    QObject::connect(ui->rb_bad_board_setting_percentage_dynamic_shielding, &QRadioButton::toggled, this, &ParameterSettings::BadBoardSettingToggled);
    QObject::connect(ui->rb_bad_board_setting_manual_specification, &QRadioButton::toggled, this, &ParameterSettings::BadBoardSettingToggled);
    QObject::connect(ui->rb_bad_board_setting_bad_board_mark, &QRadioButton::toggled, this, &ParameterSettings::BadBoardSettingToggled);
    QObject::connect(ui->cb_enable_rejection_test, &QRadioButton::toggled, this, &ParameterSettings::EnableRejectionTestToggled);
    QObject::connect(ui->edt_minimum_height, &QDoubleSpinBox::editingFinished, this, &ParameterSettings::MinimumHeightFinished);
    QObject::connect(ui->edt_minimum_area, &QDoubleSpinBox::editingFinished, this, &ParameterSettings::MinimumAreaFinished);
    QObject::connect(ui->edt_mask_expansion, &QDoubleSpinBox::editingFinished, this, &ParameterSettings::MaskExpansionFinished);
    QObject::connect(ui->percentage_dynamic_shielding, &QDoubleSpinBox::editingFinished, this, &ParameterSettings::PercentageDynamicShieldingFinished);
    QObject::connect(ui->cb_product_switch, SIGNAL(currentIndexChanged(int)), this, SLOT(ProductSwitchCurrentIndexChange(int)));

    connect(ui->data_setting, &DataSetting::SigSaveRepairData, this, &ParameterSettings::SigSaveRepairData);
    connect(ui->data_setting, &DataSetting::SigUpdateMachineParam, this, &ParameterSettings::SigUpdateMachineParam);
    connect(ui->data_view, &DataView::SigUpdateMachineParam, this, &ParameterSettings::SigUpdateMachineParam);
}
void ParameterSettings::ShowTrack2(bool flag)
{
    ui->frame_70->setVisible(flag);
    ui->frame_35->setVisible(flag);
    ui->track_trans2->setVisible(flag);
    ui->frame_68->setVisible(flag);
    //ui->frame_33->setVisible(flag);
}
void ParameterSettings::MachineParamChangeSave(const std::string param_name, const jrsdata::JrsVariant& value_)
{
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();
    param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, param_name, value_);
    jrsdata::MachineParam _machine_param; /**<机台参数*/
    _machine_param.event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;
    emit SigUpdateMachineParam(_machine_param);
}
void ParameterSettings::UpdateView(const jrsdata::OperateViewParamPtr ptr)
{
    if (ptr->config_setting_param.event_name == jrsaoi::OPERATE_MOTION_SETTING_UPDATE && ptr->config_setting_param.setting_type == jrsdata::SettingType::Motion)
    {
        operateparam->config_setting_param = ptr->config_setting_param;
        int track_cout = ptr->config_setting_param.motion_setting.trackCout;
        int track_mode = ptr->config_setting_param.motion_setting.trackMode;
        int track_repeat = ptr->config_setting_param.motion_setting.cycleCount;
        int enterDirection = ptr->config_setting_param.motion_setting.enterDirection;
        int leaveDirection = ptr->config_setting_param.motion_setting.leaveDirection;
        bool track1_TransportMode = false;
        bool track2_TransportMode = false;
        if (ptr->config_setting_param.motion_setting.transportMode.size() >= 2)
        {
            track1_TransportMode = ptr->config_setting_param.motion_setting.transportMode.at(0);
            track2_TransportMode = ptr->config_setting_param.motion_setting.transportMode.at(1);
        }
        bool track_UnLoadMode1 = false; // 是否等待测试结果出板
        bool track_UnLoadMode2 = false; // 是否无后站强制出板
        if (ptr->config_setting_param.motion_setting.unLoadMode.size() >= 2)
        {
            track_UnLoadMode1 = ptr->config_setting_param.motion_setting.unLoadMode.at(0);
            track_UnLoadMode2 = ptr->config_setting_param.motion_setting.unLoadMode.at(1);
        }
        if (ptr->config_setting_param.motion_setting.board_width_base.size() >= 2)
        {
            //ui->track_width_base1->setText(QString::number(ptr->config_setting_param.motion_setting.board_width_base.at(0)));
            //ui->track_width_base2->setText(QString::number(ptr->config_setting_param.motion_setting.board_width_base.at(1)));
        }
        int barcode_mode = ptr->config_setting_param.motion_setting.barcode_mode;
        int trackSelection = ptr->config_setting_param.motion_setting.trackSelection;
        bool autotrack = ptr->config_setting_param.motion_setting.autotrack;
        bool fixedTest = ptr->config_setting_param.motion_setting.fixedTest;
        auto BoardPosition = ptr->config_setting_param.motion_setting.board_stop;
        jrsdata::Point track1_left;
        jrsdata::Point track1_right;
        jrsdata::Point track2_left;
        jrsdata::Point track2_right;
        if ((int)BoardPosition.size() >= 2)
        {
            track1_left = BoardPosition[0].left;
            track1_right = BoardPosition[0].right;
            track2_left = BoardPosition[1].left;
            track2_right = BoardPosition[1].right;
        }
        if (track_cout > 0)
        {
            ui->track_type->setCurrentIndex(track_cout - 1);
            ui->track_mode->setCurrentIndex(track_mode);
            ui->track_repeat->setText(QString::number(track_repeat));
            ui->track_direction->setCurrentIndex(enterDirection * 2 + leaveDirection);
            //ui->track_export_direction->setCurrentIndex(leaveDirection);
            ui->track_trans1->setChecked(track1_TransportMode);
            ui->track_trans2->setChecked(track2_TransportMode);
            ui->track_normal->setChecked(!track_UnLoadMode2);
            ui->track_force_without_station->setChecked(track_UnLoadMode2);
            ui->basedon_test_result->setChecked(!track_UnLoadMode1);
            ui->basedon_review_result->setChecked(track_UnLoadMode1);
            ui->track_barcode->setCurrentIndex(barcode_mode);
            ui->track_module_13->setChecked(trackSelection == 0);
            ui->track_module_14->setChecked(trackSelection == 1);
            ui->track_auto_width->setChecked(autotrack);
            ui->track_fixed_test->setChecked(fixedTest);
            ui->track1_leftbottom->setText(QString::number(track1_left.x) + "," + QString::number(track1_left.y));
            ui->track1_rightbottom->setText(QString::number(track1_right.x) + "," + QString::number(track1_right.y));
            ui->track2_lefttop->setText(QString::number(track2_left.x) + "," + QString::number(track2_left.y));
            ui->track2_righttop->setText(QString::number(track2_right.x) + "," + QString::number(track2_right.y));
        }
        config_has_update = true;
        // 加载完配置文件后将运控的配置文件信息发送给workflow
        emit SigSendMotionParamToWorkFlow(operateparam);
    }
}
void ParameterSettings::UpdateBoardWidth(const jrsdata::OperateViewParamPtr param_)
{
    //if (param_->device_param.motion_param.motion_status.pos.size() < 8)
    //{
    //    return;
    //}
    //double track1_width = 0 - std::stod(param_->device_param.motion_param.motion_status.pos.at(3)) + operateparam->config_setting_param.motion_setting.board_width_base.at(0);
    //ui->track_width1->setText(QString::number(track1_width));
    //if (ui->track_module_13->isChecked())
    //{
    //    ui->track_width2->setText(param_->device_param.motion_param.motion_status.pos.at(5).c_str());
    //}
    //if (ui->track_module_14->isChecked())
    //{
    //    ui->track_width2->setText(param_->device_param.motion_param.motion_status.pos.at(4).c_str());
    //}
    //// 停板位置
    //std::string pos = param_->device_param.motion_param.motion_status.pos.at(0) + "," + param_->device_param.motion_param.motion_status.pos.at(1);
    //if (stop_board_index == 0)
    //{
    //    ui->track1_leftbottom->setText(pos.c_str());
    //    QStringList list = ui->track1_leftbottom->text().split(',');
    //    if (list.size() == 2)
    //    {
    //        operateparam->config_setting_param.motion_setting.board_stop.at(0).left = jrsdata::Point(list.at(0).toDouble(), list.at(1).toDouble());
    //    }
    //    if (!config_has_update)
    //    {
    //        return;
    //    }
    //    emit SigSaveSetting(operateparam);
    //}
    //else if (stop_board_index == 1)
    //{
    //    ui->track1_rightbottom->setText(pos.c_str());
    //    QStringList list = ui->track1_rightbottom->text().split(',');
    //    if (list.size() == 2)
    //    {
    //        operateparam->config_setting_param.motion_setting.board_stop.at(0).right = jrsdata::Point(list.at(0).toDouble(), list.at(1).toDouble());
    //    }
    //    if (!config_has_update)
    //    {
    //        return;
    //    }
    //    emit SigSaveSetting(operateparam);
    //}
    //else if (stop_board_index == 2)
    //{
    //    ui->track2_lefttop->setText(pos.c_str());
    //    QStringList list = ui->track2_lefttop->text().split(',');
    //    if (list.size() == 2)
    //    {
    //        operateparam->config_setting_param.motion_setting.board_stop.at(1).left = jrsdata::Point(list.at(0).toDouble(), list.at(1).toDouble());
    //    }
    //    if (!config_has_update)
    //    {
    //        return;
    //    }
    //    emit SigSaveSetting(operateparam);
    //}
    //else if (stop_board_index == 3)
    //{
    //    ui->track2_righttop->setText(pos.c_str());
    //    QStringList list = ui->track2_righttop->text().split(',');
    //    if (list.size() == 2)
    //    {
    //        operateparam->config_setting_param.motion_setting.board_stop.at(1).right = jrsdata::Point(list.at(0).toDouble(), list.at(1).toDouble());
    //    }
    //    if (!config_has_update)
    //    {
    //        return;
    //    }
    //    emit SigSaveSetting(operateparam);
    //}
    //stop_board_index = -1;
}
