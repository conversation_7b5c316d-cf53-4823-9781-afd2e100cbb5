#pragma once

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "wigtcolorwheel.h"
#include <vector>
#include <QPen>
#include <QPainter>
#include <QPainterPath>
#include <QMoveEvent>
#include <math.h>
#include <QBrush>
#include <QImage>
#include <QStandardPaths>
#include <QRadialGradient>
#include <algorithm>
#include <opencv2/opencv.hpp>
#pragma warning(pop)
#define M_PI 3.1415926
std::map<int, QPointF> point_map =
{
    {0, QPointF(-0.5,0)}, {1, QPointF(0.5,0)},
    {2, QPointF(-0.25,0.45)}, {3, QPointF(0.25,-0.45)},
    {4, QPointF(0.25,0.45)}, {5, QPointF(-0.25,-0.45)}
};
int axis_map[] = {1, 0, 3, 2, 5, 4};

using namespace std;

using namespace std;
using namespace cv;

WigtColorWheel::WigtColorWheel(QWidget *parent): QWidget{parent}
{
    resize((int)radius*2, (int)radius*2);
    wheel_center = QPointF(qreal(radius),qreal(radius));
    axis_point_size =radius/15;
    
    QRect circleRect(int(wheel_center.x() - radius), int(wheel_center.y() - radius), 2*(int)radius,2*(int)radius);
    wheel_region = QRegion (circleRect, QRegion::Ellipse);

    
    for(int i = 0; i < axis_points.size() / 2; i++)
    {
        auto line_angle = i * M_PI / 3;
        //auto scale = 0.5;
        QPointF line_start(cos(line_angle) * radius, -sin(line_angle) * radius);
        QPointF line_end(cos(line_angle + M_PI) * radius, -sin(line_angle + M_PI) * radius);
                
        // 记录点的相对位置，同时用比例记录是为了缩放的时候调整位置
        axis_points[i * 2] = AbsPos2RelaPos(line_start + wheel_center);
        axis_points[i * 2 + 1] = AbsPos2RelaPos(line_end + wheel_center);
    }
    axis_points_copy = axis_points;
    UpdateColorRegion();
}


void WigtColorWheel::paintEvent(QPaintEvent *) {
    QPainter painter(this);

    DrawBackground(painter);

    for (int i = 0; i<axis_points.size(); i++)
    {
        painter.setBrush(QBrush(QColor(200,200,200,128)));
        painter.drawEllipse( RelaPos2AbsPos(axis_points[i]),
            qreal(axis_point_size), qreal(axis_point_size));
    }
    
    auto region_draw = wheel_region.subtracted(color_region);
    
    painter.setClipRegion(region_draw);
    painter.setBrush(QBrush(QColor(80,80,80,128)));
    painter.drawEllipse(wheel_center, qreal(radius), qreal(radius));
}
void WigtColorWheel:: mouseMoveEvent(QMouseEvent *event)
{
    if ((event->buttons() & Qt::LeftButton)&& is_press_on_axis_point) 
    {
        QPoint current_position = event->pos();
        
        int axis_index = current_select_index / 2;
        float axis_angle = float(axis_index * M_PI / 3);
        
        QVector2D axis_vec(cos(axis_angle), -sin(axis_angle));
        QVector2D move_vec(float(current_position.x() - start_pos.x()),
            float(current_position.y() - start_pos.y()));
        QVector2D offset_vec = QVector2D::dotProduct(axis_vec, move_vec)
            / axis_vec.length() * axis_vec;
        
        auto new_position = RelaPos2AbsPos(axis_points[current_select_index])
            + QPointF(offset_vec.x(), offset_vec.y());
        
        if (QLineF(new_position,wheel_center).length() > radius)
        {
            return;
        }
        axis_points[current_select_index] = AbsPos2RelaPos(new_position);
        start_pos = current_position;

        UpdateColorRegion();
        emit ColorMapChanged(color_map_table, axis_points);
        update();
    }
}

void WigtColorWheel::mousePressEvent(QMouseEvent *event)
{
    int aixs_index = -1;
    if (event->button() == Qt::LeftButton) 
    {
        QPoint current_position = event->pos();
        auto current_position_rela = AbsPos2RelaPos(current_position);
        for (int i = 0; i < axis_points.size(); i++)
        {
            QLineF line(RelaPos2AbsPos(axis_points[i]),current_position);
            if(line.length( )< axis_point_size)
            {
                current_select_index = i;
                break;
            }
        }
        if (current_select_index != -1)
        {
            start_pos = current_position;
            is_press_on_axis_point = true;
        }
        else
        {
            axis_points = axis_points_copy;
            is_press_on_axis_point = false;			
            for (auto i =0;i<axis_points.size();i++)
            {
				QLineF line(axis_points[i],AbsPos2RelaPos(current_position));
                if (line.length() < PTPMINDISTANCE)
                {
                    aixs_index = i;
                    break;
                }
            }

            if (aixs_index >= 0 && aixs_index < 6)
            {
				axis_points[axis_map[aixs_index]] = point_map[axis_map[aixs_index]];
            }

			if (aixs_index != -1)
            {
                UpdateColorRegion();
                emit ColorMapChanged(color_map_table, axis_points);
                update();
            }
        }
    }  	
}

void WigtColorWheel::mouseReleaseEvent(QMouseEvent */*event*/)
{
    is_press_on_axis_point = false;
    current_select_index = -1;
}

void WigtColorWheel::resizeEvent(QResizeEvent */*event*/)
{
    wheel_width = rect().width();
    wheel_height = rect().height();
    emit UpdateWheelSize((int)wheel_width, (int)wheel_height);
    radius = size_t(std::min(rect().width()/2.0, rect().height()/2.0));
    wheel_center = rect().center();
    
    QRect circle_rect(int(wheel_center.x() - radius),
        int(wheel_center.y() - radius), 2 * (int)radius, 2 * (int)radius);
    wheel_region = QRegion(circle_rect, QRegion::Ellipse);
    
    UpdateColorRegion();
}

void WigtColorWheel::mouseDoubleClickEvent(QMouseEvent* /*event*/)
{
    RestoreColorWheel();
}


void WigtColorWheel::UpdateColorMapTable() 
{
    cv::Size img_size(rect().width(), rect().height());
    //std::cout << __FUNCTION__ << " " << __LINE__ << " "<< imgSize<<std::endl;
    Mat image(img_size,CV_8UC1);
    image.setTo(0);
    auto img_ptr = image.ptr<uchar>();
       
    for (const auto& rect : color_region)
    {
        for (int r = rect.top(); r <= rect.bottom(); r++)
        {
            if (r < 0)
            {
                continue;
            }
            for (int c = rect.left(); c <= rect.right(); c++)
            {
                if (c < 0)
                {
                    continue;
                }
                img_ptr[r * image.cols + c] = 255;
            }
        }
    }
    
    try
    {
        cv::warpPolar(image, color_map_table, cv::Size(256, 180),
            cv::Point(image.cols / 2, image.rows / 2), double(radius), cv::WARP_POLAR_LINEAR);
    }
    catch (std::exception& e) 
    {
        Log_ERROR("调色界面异常：",e.what());
    }

}

void WigtColorWheel::UpdateColorRegion() 
{
    vector<QPolygon>polygons;
    for (int i = 0; i < 3; i++)
    {
        double line_angle = i * M_PI / 3;
        QPointF p1(cos(line_angle) * radius, -sin(line_angle) * radius);
        QPointF p2(cos(line_angle + M_PI) * radius, -sin(line_angle + M_PI) * radius);
        QLineF center_line(p1 + wheel_center, p2 + wheel_center);
        
        auto offset_x = cos(line_angle + M_PI / 2.0) * radius;
        auto offset_y = -sin(line_angle + M_PI / 2.0) * radius;
        
    
        p1 = QPointF(RelaPos2AbsPos(axis_points[i * 2]).x()
            + offset_x, RelaPos2AbsPos(axis_points[i * 2]).y() + offset_y);
        p2 = QPointF(RelaPos2AbsPos(axis_points[i * 2]).x()
            - offset_x, RelaPos2AbsPos(axis_points[i * 2]).y() - offset_y);
        QLineF up_line(p1,p2);
        
        p1 = QPointF(RelaPos2AbsPos(axis_points[i * 2 + 1]).x()
            + offset_x, RelaPos2AbsPos(axis_points[i * 2 + 1]).y() + offset_y);
        p2 = QPointF(RelaPos2AbsPos(axis_points[i * 2 + 1]).x()
            - offset_x, RelaPos2AbsPos(axis_points[i * 2 + 1]).y() - offset_y);
        QLineF bottom_line(p1,p2);
        
        offset_x = cos(line_angle + M_PI / 2.0) * radius;
        offset_y = -sin(line_angle + M_PI / 2.0) * radius;
                
        QLineF left_line = center_line;
        QLineF right_line = center_line;
        left_line.translate(offset_x, offset_y);
        right_line.translate(-offset_x, -offset_y);
        
        QPointF v1, v2, v3, v4;
        left_line.intersects(up_line, &v1);
        up_line.intersects(right_line, &v2);
        right_line.intersects(bottom_line, &v3);
        bottom_line.intersects(left_line, &v4);
        
        QVector<QPoint> points;
        points.push_back(QPoint(int(v1.x()), int(v1.y())));
        points.push_back(QPoint(int(v2.x()), int(v2.y())));
        points.push_back(QPoint(int(v3.x()), int(v3.y())));
        points.push_back(QPoint(int(v4.x()), int(v4.y())));
        
        QPolygon polygon(points);
        polygons.emplace_back(polygon);
    }

    color_region = wheel_region;
    for (const auto& polygon: polygons)
    {
        color_region = color_region.intersected(QRegion(polygon));
    }
    UpdateColorMapTable();
}

void WigtColorWheel::DrawBackground(QPainter& painter)
{
    
    painter.setRenderHint(QPainter::Antialiasing,true);
    radius = qMin(width(), height()) / 2;
    QConicalGradient gradient(wheel_center.x(), wheel_center.y(), 0.0);
    
    gradient.setColorAt(0.0, Qt::red);
    gradient.setColorAt(1.0/6.0, Qt::magenta);
    gradient.setColorAt(2.0/6.0, Qt::blue);
    gradient.setColorAt(3.0/6.0, Qt::cyan);
    gradient.setColorAt(4.0/6.0, Qt::green);
    gradient.setColorAt(5.0/6.0, Qt::yellow);
    gradient.setColorAt(1.0, Qt::red);
    
    painter.setBrush(QBrush(gradient));
    painter.drawEllipse(wheel_center, qreal(radius), qreal(radius));
    
    QRadialGradient radial(wheel_center.x(), wheel_center.y(), qreal(radius));
    radial.setColorAt(0.0, QColor(255,255,255,255));
    radial.setColorAt(1.0, QColor(255,0,0,0));
    painter.setBrush(QBrush(radial));
    painter.drawEllipse(wheel_center, qreal(radius), qreal(radius));
    
    for (int i = 0; i<3; i++)
    {
        double line_angle = i * M_PI / 3;
        QPointF p1(cos(line_angle) * radius, -sin(line_angle) * radius);
        QPointF p2(cos(line_angle + M_PI) * radius, -sin(line_angle + M_PI) * radius);
        QLineF center_line(p1 + wheel_center, p2 + wheel_center);
        painter.drawLine(center_line);
    }
}

QPointF WigtColorWheel::AbsPos2RelaPos(const QPointF &absPos) 
{
    return (absPos - wheel_center) / qreal(radius);
}

QPointF WigtColorWheel::RelaPos2AbsPos(const QPointF &rela_pos)
{
    return rela_pos * qreal(radius) + wheel_center;
}


void WigtColorWheel::SetAxisPos(const std::array<QPointF, 6>& map_points ,bool update_ui)
{
    if (map_points.empty())return;
    axis_points = map_points;
	UpdateColorRegion();
    if (update_ui)
    {
		emit ColorMapChanged(color_map_table, axis_points);
        update();
    }
}

void WigtColorWheel::RestoreColorWheel()
{
    axis_points = axis_points_copy;

    UpdateColorRegion();
    emit ColorMapChanged(color_map_table, axis_points);
    update();
}


