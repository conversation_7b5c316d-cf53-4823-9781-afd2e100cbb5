@startuml 整体流程
title 整体流程

start
:启动一次流程;
:获取整板内所有子板;
:根据指定的待检测子板,获取所有待检测元件;
:获取整板所有MARK;
:进入循环检测;
:坐标转换->MARK坐标(绝对像素坐标)转换到(绝对物理坐标);
:执行MARK为中心的图片拍摄;
:执行tablemapping,将拍摄坐标转换到(实际物理位置);
:坐标转换->实际MARK物理位置(绝对物理坐标)转换到(绝对像素坐标);
:每张图片中可获取一个MARK的偏移量;
:偏移量相对于实际MARK像素位置,计算可得新的MARK坐标(绝对像素坐标);
:根据MARK坐标生成全局仿射变换矩阵;
:根据仿射变换矩阵对所有待检测元件进行中心坐标矫正[不进行角度修正];
:对所有待检测元件进行FOV规划,将元件分配到FOV(绝对像素坐标);
:坐标转换->FOV坐标(绝对像素坐标)转换到(绝对物理坐标);
:执行FOV坐标为中心的图片拍摄(以理论位置拍摄);
:执行tablemapping,将FOV坐标转换到(实际物理位置);
fork
:拍摄的FOV图片传入检测队列;
fork again
while (检测队列为空) is (否)
    :FOV拼接到大图;
    :获取FOV内所有待检测元件;
    while (遍历所有元件) is (否)
        :获取元件;
        :获取元件坐标(绝对像素坐标);
        :坐标转换->元件坐标转换到(fov内的绝对像素坐标);
        :获取元件所在料号;
        :获取料号对应检测框;
        while (遍历所有检测框) is (否)
            :获取检测框;
            :获取检测框坐标(相对元件的相对像素坐标);
            :坐标转换->检测框坐标转换到(fov内的绝对像素坐标);
            :获取检测框下所有算法;
            while (遍历所有算法) is (否)
                :获取算法;
                :获取算法所需灯图;
                :获取算法颜色参数;
                :坐标转换->检测框中心坐标转左上角坐标;
                :执行图片裁剪;
                :执行调色;
                :获取算法参数;
                :调用算法;
                :获取算法结果;
                :处理算法结果;
            endwhile (是)
        endwhile (是)
    endwhile (是)
    if (是最后一个FOV and 存在大元件) then (是)
        :大图传入检测队列(将大图当做一张大小不同的FOV);
    endif
endwhile (是)
end merge
:整理结果数据,生成报表;
:执行下一次循环;
stop

@enduml

@startuml 建框流程
title 建框流程

start
:选择元件;
:获取元件所在料号;
:获取料号的检测框;
:渲染检测框;
:选择元件模型;
:选择需要新增的检测框形状;
:在渲染进行检测框绘制;
:将检测框添加到模型;
:为检测框选择算法;
:确认;
:将更改保存到料号;
stop

@enduml


@startuml 工程操作流程
title 工程操作流程

actor "UI" as U
participant "渲染模块\n(ModuleView)" as MV
participant "事件中心\n(EventCenter)" as E
participant "数据模块\n(ModuleData)" as MD
participant "其他模块\n(ModuleOther)" as MO

== 工程保存 ==
U -> MV : 点击工程保存按钮
MV -> E : 发布事件(工程保存)
E -> MD : 转发事件(工程保存)
MD -> MD : 进行工程保存

== 工程读取 ==
U -> MV : 点击工程读取按钮
MV -> E : 发布事件(工程读取)
E -> MD : 转发事件(工程读取)
MD -> MD : 进行工程读取
MD -> E : 发布事件(工程更新)
E -> MO : 转发事件(工程更新)
@enduml

