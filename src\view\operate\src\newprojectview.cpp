﻿//Custom
#include "NewProjectView.h"
#include "viewdefine.h"
//tool
#include "viewtool.hpp"
#include "fileoperation.h"
#include "viewdefine.h"
#include "stringoperation.h"
#include "coreapplication.h"
//QT
#pragma warning(push, 1)
#include <QPushButton>
#include <QDoubleSpinBox>
#include "ui_NewProjectView.h"
#pragma warning(pop)

using namespace jrscore;

namespace jrsaoi
{
    NewProjectView::NewProjectView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::NewProjectView)
        , param_ptr(std::make_shared<jrsdata::OperateViewParam>())
        , position_index(-1) {
        ui->setupUi(this);
        InitMember();
        InitView();
        InitConnect();
    }
    NewProjectView::~NewProjectView()
    {
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
        if (new_project_file_view)
        {
            delete new_project_file_view;
            new_project_file_view = nullptr;
        }
        if (project_link_view)
        {
            delete project_link_view;
            project_link_view = nullptr;
        }
    }

    void NewProjectView::UpdateProjectPath(const std::string& project_path_)
    {
        default_path = project_path_;
    }

    void NewProjectView::SlotUpdateInfo(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == PROJECT_READ_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            if (!param)
                return;
            _is_update_project = false;
            ui->project_name->setText(QString::fromStdString(param->project_param->project_name));
           /* ui->board_width->setValue(param->project_param->board_info.real_width);
            ui->board_length->setValue(param->project_param->board_info.real_height);
            ui->line_posa_x->setText(QString::number(param->project_param->board_info.left_top_x));
            ui->line_posa_y->setText(QString::number(param->project_param->board_info.left_top_y));
            ui->line_posb_x->setText(QString::number(param->project_param->board_info.right_bottom_x));
            ui->line_posb_y->setText(QString::number(param->project_param->board_info.right_bottom_y));*/
            _is_update_project = true;

        }
        else if (param_->event_name == jrsaoi::OPERATE_MOTION_SETTING_UPDATE)
        {
            /**< 更新运控参数 */
            auto  param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            param_ptr->config_setting_param = param->config_setting_param;
        }
        else if (param_->event_name == jrsaoi::OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            if (!param)
            {
                return;
            }
            ShowLinkProjectView(param->project_event_info.project_name_lists, param->project_event_info.link_project_name);

        }
    }

    void NewProjectView::SlotNewProject()
    {
        new_project_file_view->show();
    }

    void NewProjectView::SlotConfirmFileInfo(const jrsdata::ProjectEventInfo& info)
    {
        CreateProject(info);
        ui->project_name->setText(QString::fromStdString(info.project_name));
      /*  ui->board_width->setValue(info.info.width_board);
        ui->board_length->setValue(info.info.height_board);*/
        _project_event_info = info;
        _is_update_project = true;
        SlotUpdateABPoints();

    }


    void NewProjectView::UpdatePosition(jrsdata::OperateViewParamPtr param_)
    {
        //_is_update_project = false;/**< 屏蔽掉 输入宽高自动计算AB点位置*/
        //if ((int)param_->device_param.motion_param.motion_status.pos.size() > 0 && position_index >= 0)
        //{
        //    // std::string pos = param_->device_param.motion_param.motion_status.pos.at(0) + "," + param_->device_param.motion_param.motion_status.pos.at(1);
        //    auto xstr = param_->device_param.motion_param.motion_status.pos.at(0).c_str();
        //    auto ystr = param_->device_param.motion_param.motion_status.pos.at(1).c_str();
        //    if (position_index == 0)
        //    {
        //        ui->line_posa_x->setText(xstr);
        //        ui->line_posa_y->setText(ystr);
        //    }
        //    else if (position_index == 1)
        //    {
        //        ui->line_posb_x->setText(xstr);
        //        ui->line_posb_y->setText(ystr);
        //    }
        //}

        ////TODO  更新长度和宽度的值 by baron_zhang 202-11-25
        //ui->board_width->setValue(std::abs(ui->line_posa_x->text().toDouble() - ui->line_posb_x->text().toDouble()));
        //ui->board_length->setValue(std::abs(ui->line_posa_y->text().toDouble() - ui->line_posb_y->text().toDouble()));
        //auto project_param = std::make_shared<jrsdata::ProjectParam>();
        ///**< 更新位置和板宽 By HJC 2024/11/30  */
        //UpdatePositionAndBoardRealSize();
        ////emit SigScanceBoardImags();
        //position_index = -1;
        //_is_update_project = true;
    }

    //void NewProjectView::SlotSaveEntiretyImages()
    //{
    //    //选择组
    //    emit SigSaveEntiretyImages("defualt");
    //}


    void NewProjectView::InitView()
    {
    }
    void NewProjectView::InitMember()
    {
        new_project_file_view = new NewFileView();
        project_link_view = new ProjectLinkView();
        _is_update_project = true;
    }
    void NewProjectView::InitConnect()
    {
        connect(new_project_file_view, &NewFileView::SigConfirmFileInfo, this, &NewProjectView::SlotConfirmFileInfo);
        connect(ui->new_project, &QPushButton::clicked, this, &NewProjectView::SlotNewProject);
       // connect(ui->scan_image, &QPushButton::clicked, this, &NewProjectView::SigScanceBoardImags);
        //connect(ui->board_stop_pos, &QPushButton::clicked, this, [=]() {
        //    param_ptr->device_param.event_name = "BoardStop";
        //    param_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
        //    emit SigMotionDebugTrigger(param_ptr);
        //    });
        //connect(ui->board_remote_pos, &QPushButton::clicked, this, [=]() {
        //    param_ptr->device_param.event_name = "BoardRemote";
        //    // TODO 修改远端位置设置bug by baron_zhang 2024-12-02
        //    if (ui->board_width->text() != "")
        //    {
        //        param_ptr->device_param.extra_param.product_width = ui->board_width->value();
        //    }
        //    if (ui->board_length->text() != "")
        //    {
        //        param_ptr->device_param.extra_param.product_height = ui->board_length->value();
        //    }
        //    param_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
        //    emit SigMotionDebugTrigger(param_ptr);
        //    });
        //// 确认A
        //connect(ui->confirm_a, &QPushButton::clicked, this, [=]() {
        //    position_index = 0;
        //    emit SigGetPosition();
        //    });
        //// 确认B
        //connect(ui->confirm_b, &QPushButton::clicked, this, [=]() {
        //    position_index = 1;

        //    emit SigGetPosition();
        //    });

        connect(new_project_file_view, &NewFileView::SigGetProductWidth, this, [=]() {
            emit SigNewFileGetProductWidth();
            });

        //connect(ui->board_width, &QDoubleSpinBox::editingFinished, this, &NewProjectView::SlotUpdateABPoints);
        //connect(ui->board_length, &QDoubleSpinBox::editingFinished, this, &NewProjectView::SlotUpdateABPoints);


        connect(ui->pushButton_link, &QPushButton::clicked, this, &NewProjectView::SlotGetProjectLists);

        connect(project_link_view, &ProjectLinkView::SigComfirmLinkProject, this, &NewProjectView::SigComfirmLinkProject);
        connect(project_link_view, &ProjectLinkView::SigCancleLinkProject, this, &NewProjectView::SigCancleLinkProject);
    }
    void NewProjectView::CreateProject(const jrsdata::ProjectEventInfo& info)
    {
        auto project_param = std::make_shared<jrsdata::ProjectParam>();
        // 默认参数
        {
            jrsdata::SubBoard sub;
            sub.id = 1;
            sub.subboard_name = info.project_name + "_" + std::to_string(1);/* "subboard_row-0_col-0"*/
            sub.x = 0;
            sub.y = 0;
            sub.width = 500;
            sub.height = 500;
            project_param->board_info.sub_board.emplace_back(std::move(sub));
        }
        project_param->board_info.real_width = info.info.width_board;
        project_param->board_info.real_height = info.info.height_board;
        project_param->project_name = info.project_name;
        project_param->file_param.file_path = default_path + "/";
        project_param->file_param.file_name = info.project_name + default_ext; // 工程保存时不会进行扩展名拼接
        //project_param->file_param.file_type = jrsdata::FileType::BIN;
        //project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        emit SignalCreateProject(project_param);
        //emit SigScanceBoardImags();
    }

    void NewProjectView::UpdatePositionAndBoardRealSize()
    {
        /** < 左上右上  位置 和 宽度 */
        auto project_param = std::make_shared<jrsdata::ProjectParam>();
        // 分开来显示就不用解析了
       /* project_param->board_info.left_top_x = ui->line_posa_x->text().toDouble();
        project_param->board_info.left_top_y = ui->line_posa_y->text().toDouble();
        project_param->board_info.right_bottom_x = ui->line_posb_x->text().toDouble();
        project_param->board_info.right_bottom_y = ui->line_posb_y->text().toDouble();
        project_param->board_info.real_height = ui->board_length->value();
        project_param->board_info.real_width = ui->board_width->value();*/
        emit SigConfirmBoardPos(project_param);
    }

    void NewProjectView::ShowLinkProjectView(const std::list<std::string>& project_lists, const std::string& linked_project_name)
    {
        if (project_lists.empty())
        {
            JRSMessageBox_WARN("警告", "没有可关联的工程", jrscore::MessageButton::Ok);
            return;
        }
        project_link_view->SetLinkedProjectName(linked_project_name);
        project_link_view->SetProjectLists(project_lists);
        project_link_view->show();
    }

    void NewProjectView::SlotUpdateABPoints()
    {
        //if (!_is_update_project)
        //{
        //    return;
        //}
        //_project_event_info.info.height_board = ui->board_length->value();
        //_project_event_info.info.width_board = ui->board_width->value();

        ///** < 出板方向 0左出，1右出 */
        //try
        //{
        //    auto enter_direction = param_ptr->config_setting_param.motion_setting.enterDirection;
        //    jrsdata::Point a_point;
        //    jrsdata::Point b_point;
        //    if (param_ptr->config_setting_param.motion_setting.board_stop.empty())
        //    {
        //        /**<数据为空*/
        //        JRSMessageBox_WARN("获取配置文件失败", "获取运控数据为空，请检查运控配置文件是否正常加载", jrscore::MessageButton::Ok);
        //        return;
        //    }
        //    if (enter_direction == 0)
        //    {/**<右进*/
        //        a_point = param_ptr->config_setting_param.motion_setting.board_stop.at(0).right;    /**< 右下 停板位置*/
        //        b_point.x = a_point.x - _project_event_info.info.width_board;
        //        b_point.y = a_point.y - _project_event_info.info.height_board;
        //    }
        //    else
        //    {
        //        a_point = param_ptr->config_setting_param.motion_setting.board_stop.at(0).left;     /**< 左下 停板位置*/
        //        b_point.x = a_point.x + _project_event_info.info.width_board;
        //        b_point.y = a_point.y - _project_event_info.info.height_board;
        //    }
        //    ui->line_posa_x->setText(QString::number(a_point.x));
        //    ui->line_posa_y->setText(QString::number(a_point.y));
        //    ui->line_posb_x->setText(QString::number(b_point.x));
        //    ui->line_posb_y->setText(QString::number(b_point.y));
        //}
        //catch (std::exception e)
        //{
        //    JRSMessageBox_WARN("警告", "获取运控参数异常，自动计算AB点失败，请检查", jrscore::MessageButton::Ok);
        //}
        //UpdatePositionAndBoardRealSize();
        ///**< 判断是否扫图 */
        //if (_project_event_info.info.is_scan)
        //{
        //    _project_event_info.info.is_scan = false;
        //    emit SigScanceBoardImags();
        //}
    }

    void NewProjectView::SlotGetProjectLists()
    {
        if (ui->project_name->text().isEmpty())
        {
            JRSMessageBox_WARN("警告", "请先创建工程或打开工程", jrscore::MessageButton::Ok);
            return;
        }
        emit SigGetMultiProjectLists();

    }

    void NewProjectView::UpdateNewFileProductWidth(double track_width1, double track_width2)
    {
        new_project_file_view->UpdateNewFileProductWidth(track_width1, track_width2);
    }
}