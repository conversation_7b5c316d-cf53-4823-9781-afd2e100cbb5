﻿/*********************************************************************
 * @file   projectparam.hpp
 * @brief  工程参数，包括各类板子数据，元件数据，设置数据等...
 * @details
 * <AUTHOR>
 * @date 2024.8.8
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.8          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
**********************************************************************/
#ifndef __JRSPROJECTPARAM_HPP__
#define __JRSPROJECTPARAM_HPP__

///< STD
//#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
// Custom
#include "caddefine.hpp"
#include "parambase.hpp"  // DataBase

//Third
#pragma warning(push, 1)
#include "opencv2/opencv.hpp"
#include <ylt/struct_pack/compatible.hpp>
#pragma warning(pop)

/****************************************************************************************************
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * TODO:  结构体内字段变更时 请在相应结构体下的 JRSREFLECTION() 宏变量内修改相应字段！！！！！！！
 *        无法注册三方库变量，如cv::Mat 等。
 *                                                                                   - 24/8/20 HJC 注
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 * ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
 ****************************************************************************************************/

 /*************************************************
  * 工程变量命名规范
  * 1、子板ID 从1开始
  * 2、子板名称=工程名_子板ID
  * 3、元件名称=元件名_子板ID
  * 4、整板Mark 名称= mark名称_markID;   整板Barode名称 =barcode名称_barcodeID   结构体内的subboard_name="entirety_board";
  *    子板Mark 名称= mark名称_子板ID;   子板Barcode名称 =barcode名称_子板ID;
  *     BadMark 名称= bad_mark名称_子板ID;
  *                             By:HJC 2025/1/17   2025/3/27更新
  **************************************************/

namespace jrsdata
{
    //! 采图位置结构体
    struct CaptureFovPos
    {
        int id;
        float x_fov_coordinate; //! x 坐标
        float y_fov_coordinate; //! y 坐标
        float z_fov_coordinate; //! z 坐标

        CaptureFovPos()
            : id(0), x_fov_coordinate(0.0f), y_fov_coordinate(0.0f), z_fov_coordinate(0.0f)
        {
        }
        CaptureFovPos(int id_, float x_fov_coordinate_, float y_fov_coordinate_, float z_fov_coordinate_)
            : id(id_), x_fov_coordinate(x_fov_coordinate_), y_fov_coordinate(y_fov_coordinate_), z_fov_coordinate(z_fov_coordinate_)
        {
        }
        CaptureFovPos(float x_fov_coordinate_, float y_fov_coordinate_, float z_fov_coordinate_)
            : id(0), x_fov_coordinate(x_fov_coordinate_), y_fov_coordinate(y_fov_coordinate_), z_fov_coordinate(z_fov_coordinate_)
        {
        }
        CaptureFovPos(const CaptureFovPos& other)
            : id(other.id), x_fov_coordinate(other.x_fov_coordinate), y_fov_coordinate(other.y_fov_coordinate), z_fov_coordinate(other.z_fov_coordinate)
        {
        }
    };
    JRSREFLECTION(CaptureFovPos, id, x_fov_coordinate, y_fov_coordinate, z_fov_coordinate);

    //! 采图位置列表结构体
    struct CaptureFovPosList
    {
        std::vector<CaptureFovPos> fov_pos_list; //! 采图位置列表
        CaptureFovPosList() : fov_pos_list() {}
        CaptureFovPosList(const std::vector<CaptureFovPos>& fov_pos_list_) : fov_pos_list(fov_pos_list_) {}
        CaptureFovPosList(const CaptureFovPosList& other) : fov_pos_list(other.fov_pos_list) {}
    };
    JRSREFLECTION(CaptureFovPosList, fov_pos_list);

    //! 规格
    struct DetectSpec
    {
        std::string spec_name; ///< 规格名称(显示用)
        std::string spec_type; ///< 规格类型(与算法输出值绑定)
        float spec_upper;      ///< 规格上限
        float spec_lower;      ///< 规格下限
        bool is_upper;         ///< 是否有上限
        bool is_lower;         ///< 是否有下限

        DetectSpec()
            : spec_name(""), spec_type(""), spec_upper(0.0f), spec_lower(0.0f), is_upper(false), is_lower(false)
        {
        }
        DetectSpec(const std::string& spec_name_, const std::string& spec_type_, float spec_upper_, float spec_lower_, bool is_upper_, bool is_lower_)
            : spec_name(spec_name_), spec_type(spec_type_), spec_upper(spec_upper_), spec_lower(spec_lower_), is_upper(is_upper_), is_lower(is_lower_)
        {
        }
        DetectSpec(const DetectSpec& other)
            : spec_name(other.spec_name), spec_type(other.spec_type), spec_upper(other.spec_upper), spec_lower(other.spec_lower), is_upper(other.is_upper), is_lower(other.is_lower)
        {
        }
    };
    JRSREFLECTION(DetectSpec, spec_name, spec_type, spec_upper, spec_lower, is_upper, is_lower);

    /**
     * @brief 子检测框
     * @note  子检测框实际位置 = 检测框位置 + 子检测框偏移量
     */
    struct  SubDetectWindow
    {
        std::string name;                        ///< 检测区域名称
        int id;                                  ///< 检测框 id
        float cx;                                  ///< X偏移量(相对于检测框):像素
        float cy;                                  ///< Y偏移量(相对于检测框):像素
        float width;                               ///< 检测区域的宽度:像素
        float height;                              ///< 检测区域的高度:像素
        int detect_state;                        ///< 检测状态(ok，ng，未检测，不检测，检测异常)

        SubDetectWindow()
            : name(""), id(0), cx(0), cy(0), width(0), height(0), detect_state(0)
        {
        }
        SubDetectWindow(const std::string& name_, int id_, float cx_, float cy_, float width_, float height_, int detect_state_)
            : name(name_), id(id_), cx(cx_), cy(cy_), width(width_), height(height_), detect_state(detect_state_)
        {
        }
        SubDetectWindow(const SubDetectWindow& other)
            : name(other.name), id(other.id), cx(other.cx), cy(other.cy), width(other.width), height(other.height), detect_state(other.detect_state)
        {
        }
        SubDetectWindow& operator=(const SubDetectWindow& other)
        {
            if (this != &other)
            {
                name = other.name;
                id = other.id;
                cx = other.cx;
                cy = other.cy;
                width = other.width;
                height = other.height;
                detect_state = other.detect_state;
            }
            return *this;
        }
    };
    JRSREFLECTION(SubDetectWindow, name, id, cx, cy, width, height, detect_state);

    enum DetectAlgoType
    {
        Localization = 0,
        Detection
    };

    //! 算法
    struct DetectAlgorithm
    {
        std::string detect_algorithm_name;       ///< 算法名称
        // std::string detect_spec_name;            ///< 检测规格名称
        std::string param;                       ///< 算法参数
        std::string color_param;                 ///< 颜色参数
        std::string light_param;                 ///< 灯光配置 (通过选择通道图组合调配出标准图片以外的图)
        int light_image_id;                      ///< 灯光图类型
        DetectAlgoType type;                     ///< 算法类型
        std::vector<int>template_image_ids;         //模板图片id (使用id是因为如果涉及到深拷贝时代价太大)
        std::vector<SubDetectWindow> algorithm_detect_windows;/**< 算法子检测框*/

        DetectAlgorithm()
            : detect_algorithm_name(""), param(""), light_image_id(0), template_image_ids{}, color_param(""), light_param(""), algorithm_detect_windows({})
        {
        }
        DetectAlgorithm(const std::string& detect_algorithm_name_, const std::string& param_, int light_image_id_, int template_image_id_, std::string color_param_, std::string light_param_, const std::vector<SubDetectWindow>& algorithm_detect_windows_)
            : detect_algorithm_name(detect_algorithm_name_), param(param_), light_image_id(light_image_id_), template_image_ids({ template_image_id_ }), color_param(color_param_), light_param(light_param_), algorithm_detect_windows(algorithm_detect_windows_)
        {
        }
        DetectAlgorithm(const DetectAlgorithm& other)
            : detect_algorithm_name(other.detect_algorithm_name), param(other.param), light_image_id(other.light_image_id), template_image_ids(other.template_image_ids)
            , color_param(other.color_param), light_param(other.light_param), algorithm_detect_windows(other.algorithm_detect_windows)
        {
        }
    };
    JRSREFLECTION(DetectAlgorithm, detect_algorithm_name, param, light_image_id, template_image_ids, color_param, light_param, algorithm_detect_windows);

    //! 模板
    struct Template
    {
        int id;                      ///< 模板id
        int cols;                    ///< 模板宽度
        int rows;                    ///< 模板高度
        int light_image_id;          ///< 模板对应的灯图id
        std::vector<uint8_t> image;  ///< 模板图像
        std::string color_params;    ///< 颜色参数

        void SetMatImage(const cv::Mat& mat_image)
        {
            if (mat_image.empty())
            {
                cols = 0;
                rows = 0;
                image.clear();
                return;
            }
            cols = mat_image.cols;
            rows = mat_image.rows;

            cv::Mat image_continuous = mat_image;
            if (!mat_image.isContinuous())
            {
                image_continuous = mat_image.clone();
            }

            image.resize(image_continuous.total() * image_continuous.elemSize());
            std::memcpy(image.data(), image_continuous.data, image_continuous.total() * image_continuous.elemSize());
        }

        cv::Mat GetMatImage()
        {
            if (cols == 0 || rows == 0)
            {
                return cv::Mat{};
            }
            size_t total_size = image.size();
            int channels = static_cast<int>(total_size / (cols * rows));
            cv::Mat mat_image;
            if (channels == 1)
            {
                mat_image = cv::Mat(rows, cols, CV_8UC1, image.data());
            }
            else if (channels == 3)
            {
                mat_image = cv::Mat(rows, cols, CV_8UC3, image.data());
            }
            else
            {
                return cv::Mat{};
            }
            return mat_image;
        };

        Template() : id(0), cols(0), rows(0), light_image_id(0), image({}) {}
        Template(int id_, const cv::Mat& image_, const std::string& params = "", int light_image_id_ = 0)
            : id(id_), light_image_id(light_image_id_), cols(image_.cols), rows(image_.rows), color_params(params)
        {
            cv::Mat image_continuous = image_;
            if (!image_.isContinuous())
            {
                image_continuous = image_.clone();
            }

            image.resize(image_continuous.total() * image_continuous.elemSize());
            std::memcpy(image.data(), image_continuous.data, image_continuous.total() * image_continuous.elemSize());
        }
        Template(int id_, int cols_, int rows_, const std::vector<uint8_t>& image_)
            :id(id_), cols(cols_), rows(rows_), image(image_), light_image_id(0)
        {
        }
    };
    JRSREFLECTION(Template, id, cols, rows, light_image_id, image, color_params);

    /**
     * @brief 检测框
     * @note  检测框实际位置 [cx,cy] = M(元件) * M(组件) * [cx,cy]
     */
    struct DetectWindow
    {
        bool enable;                             ///< 是否检测
        int id;                                  ///< 检测框 id
        float cx;                                ///< X偏移量(相对于ComponentUnit):像素
        float cy;                                ///< Y偏移量(相对于ComponentUnit):像素
        float width;                             ///< 检测区域的宽度:像素
        float height;                            ///< 检测区域的高度:像素
        int level;                               ///< 检测等级(等级越高的检测框检测越优先)
        int detect_state;                        ///< 检测状态(ok，ng，未检测，不检测，检测异常)
        float search_size;                       ///< 检测框搜索范围,相对于检测框大小的比例,需要大于0,0表示未使用
        std::string model_name;                  ///< 模型组名称(显示名称，如body，pad1,pad2...，后期改为可修改)
        std::string name;                        ///< 检测区域名称
        std::string defect_name;                 ///< 缺陷名称
        std::string group_name;                  ///< 缺陷组名称
        std::string parent_win_name;             ///< 上级检测框命     2024/12/19 wangzhengkai 用于多级检测
        std::vector<DetectAlgorithm> algorithms; ///< 算法

        DetectWindow()
            :name(""),
            defect_name(""),
            group_name(""),
            model_name(""),
            id(0),
            parent_win_name(""),
            cx(0),
            cy(0),
            width(0),
            height(0),
            detect_state(0),
            level(0),
            algorithms(),
            enable(false),
            search_size(0.5f)
        {
        }

        DetectWindow(
            std::string model_name_,
            std::string window_name_,
            std::string defect_name_,
            std::string group_name_,
            std::string parent_win_name_,
            int id_,
            int level_,
            float cx_,
            float cy_,
            float width_,
            float height_,
            const std::vector<DetectAlgorithm>& algorithms_,
            int detect_state_,
            bool enable_,
            float search_size_)
            :model_name(),
            defect_name(defect_name_),
            group_name(group_name_),
            name(window_name_),
            id(id_),
            parent_win_name(parent_win_name_),
            cx(cx_),
            cy(cy_),
            width(width_),
            height(height_),
            detect_state(detect_state_),
            level(level_),
            algorithms(algorithms_),
            enable(enable_),
            search_size(search_size_)
        {
        }
        DetectWindow(const DetectWindow& other)
            :model_name(other.model_name),
            defect_name(other.defect_name),
            group_name(other.group_name),
            parent_win_name(other.parent_win_name),
            name(other.name),
            id(other.id),
            cx(other.cx),
            cy(other.cy),
            width(other.width),
            height(other.height),
            detect_state(other.detect_state),
            level(other.level),
            algorithms(other.algorithms),
            enable(other.enable),
            search_size(other.search_size)
        {
        }


    };
    JRSREFLECTION(DetectWindow, name, model_name, group_name, parent_win_name, defect_name, id, level, cx, cy, width, height, algorithms, detect_state, enable, search_size);

    //! 元件组件
    struct ComponentUnit
    {
        //! 组件类型
        enum class Type : uint8_t
        {
            BODY, ///< 元件本体
            PAD,  ///< 焊盘
            HOLE, ///< 孔洞
        };
        //! 组件形状
        enum class Shape : uint8_t
        {
            RECT, ///< 矩形
            Polygon,///<多边形
            Circle,  ///<圆形
        };
        //! 组件环绕方向
        enum class Direction : uint8_t
        {
            UNKNOWN,
            UP,        ///< 在元件的上方
            RIGHT,     ///< 在元件的右方
            DOWN,      ///< 在元件的下方
            LEFT,      ///< 在元件的左方
            INSIDE,    ///< 在元件内部
        };
        //! 组件环绕方向
        enum class PadType : uint8_t
        {
            UNKNOWN,
            SINGLE,         /**< 单个*/
            MIRROR,         /**< 镜像*/
            ARRAY,          /**< 阵列*/
            MATRIX,         /**< 矩阵*/
        };
        bool enable;                        ///< 组件是否检测
        float x;                             ///< 中心x坐标(相对于Component):像素
        float y;                             ///< 中心y坐标(相对于Component):像素
        float width;                         ///< 宽度:像素
        float height;                        ///< 高度:像素
        int id;                            ///< 编号
        std::string unit_name;             ///< 组件名称，即body，pad等，默认是body(本体)
        std::string unit_group_name;       ///< 算法模型组名称：组名，如本体法组，定位算法组，PAD1算法组等，名称随意
        Type unit_type;                    ///< 组件类型 PAD或者body
        Shape unit_shape;                  ///< 组件形状,预留参数
        Direction direction;               ///< 组件环绕方向
        PadType pad_type;
        int show_id;                       /**< 界面显示ID-pad专用,Render 自动分配，无需根据保存*/

        ComponentUnit()
            : x(0), y{ 0 }, width(0), height(0), id(1), unit_name(""), unit_group_name("")
            , unit_type(Type::BODY), unit_shape(Shape::RECT),
            direction(Direction::UNKNOWN),
            pad_type(PadType::UNKNOWN), enable(true), show_id(-1)
        {
        }
        // 带参数构造函数
        ComponentUnit(
            float x_,
            float y_,
            float width_,
            float height_,
            int id_,
            std::string unit_name_,
            std::string unit_group_name_,
            Type unit_type_,
            Shape unit_shape_ = Shape::RECT,
            Direction direction_ = Direction::INSIDE,
            PadType pad_type_ = PadType::UNKNOWN,
            bool enable_ = true,
            int show_id_ = -1
        )
            : x(x_), y(y_), width(width_), height(height_), id(id_), unit_name(unit_name_), unit_group_name(unit_group_name_),
            unit_type(unit_type_), unit_shape(unit_shape_), direction(direction_), pad_type(pad_type_), enable(enable_), show_id(show_id_)
        {
        }

        // 拷贝构造函数
        ComponentUnit(const ComponentUnit& other)
            : x(other.x), y(other.y), width(other.width), height(other.height), id(other.id), unit_name(other.unit_name), unit_group_name(other.unit_group_name)
            , unit_type(other.unit_type), unit_shape(other.unit_shape), direction(other.direction), pad_type(other.pad_type), enable(other.enable), show_id(other.show_id)
        {
        }

        // 赋值操作符
        ComponentUnit& operator=(const ComponentUnit& other)
        {
            if (this != &other)
            {
                x = other.x;
                y = other.y;
                width = other.width;
                height = other.height;
                id = other.id;
                unit_name = other.unit_name;
                unit_group_name = other.unit_group_name;
                unit_type = other.unit_type;
                unit_shape = other.unit_shape;
                direction = other.direction;
                pad_type = other.pad_type;
                enable = other.enable;
                show_id = other.show_id;
            }
            return *this;
        }
    };
    JRSREFLECTION(ComponentUnit, x, y, width, height, id, unit_name, unit_group_name, unit_type, unit_shape, direction, pad_type, enable, show_id);

    //! 元件
    struct Component
    {
        //! 元件类型
        enum class Type : uint8_t
        {
            CAD,                       ///< 元件,非cad导入也应用该类型
            MARK,                      ///< 定位点
            BARCODE,                   ///< 条码
            BADMARK,                   ///< 坏板标记
            SUB_MARK,                  ///< 子板定位点
            SUB_BARCODE,               ///< 子板条码
            SUB_BADMARK,               ///< 子板坏板标记
            CARRIER_BARCODE,           ///< 载具条码
            COVERPLATE_BARCODE,        ///< 盖板条码
            AI_REGION                  ///< AI区域
        };
        Type component_type;                  ///< 元件类型
        bool enable;                          ///< 是否检测
        // int sub_board_id;                     ///< 子板id,从1开始
        int component_id;                     ///< 元件id,用于多联板同步,多联板同一个位置的元件名称可能不同但是id相同
        float x;                                ///< x 坐标(中心坐标 绝对):像素
        float y;                                ///< y 坐标(中心坐标 绝对):像素
        float angle;                          ///< 角度(绝对):度
        float z;                              ///< z 坐标(绝对):毫米,预留参数
        std::string component_name;           ///< 元件名称
        std::string component_part_number;    ///< 料号
        std::string subboard_name;            ///< 子板名
        std::vector<int> fov_ids;             ///< 元件所在的FOVid
        cv::Mat device_img;                   ///< 元件图片
        // 构造函数
        Component()
            : component_type{ Type::CAD }, enable{ true }, component_id{ 0 }
            , x{ 0 }, y{ 0 }, angle{ 0.f }, z{ 0.f },
            component_name{ "" }, component_part_number{ "" }, subboard_name{ "" }, fov_ids{ }, /*modules{ },*/ device_img{ }
        {
        }
        // 带参数构造函数
        Component(Type component_type_, bool enable_, int component_id_
            , float x_, float y_, float angle_, float z_, std::string component_name_
            , std::string component_part_number_, std::string subboard_name_, std::vector<int> fov_ids_, /*std::vector<ComponentUnit> modules_,*/ cv::Mat device_img_)
            : component_type(component_type_), enable(enable_), component_id(component_id_)
            , x(x_), y(y_), angle(angle_), z(z_), component_name(component_name_)
            , component_part_number(component_part_number_), subboard_name(subboard_name_), fov_ids(fov_ids_), /*modules(modules_),*/ device_img(device_img_)
        {
        }

        // 拷贝构造函数
        Component(const Component& other)
            : component_type(other.component_type), enable(other.enable), component_id(other.component_id)
            , x(other.x), y(other.y), angle(other.angle), z(other.z), component_name(other.component_name)
            , component_part_number(other.component_part_number), subboard_name(other.subboard_name), fov_ids(other.fov_ids),/* modules(other.modules),*/ device_img(other.device_img)
        {
        }

        // 赋值操作符
        Component& operator=(const Component& other)
        {
            if (this != &other)
            {
                component_type = other.component_type;
                enable = other.enable;
                // sub_board_id = other.sub_board_id;
                component_id = other.component_id;
                x = other.x;
                y = other.y;
                // width = other.width;
                // height = other.height;
                angle = other.angle;
                z = other.z;
                component_name = other.component_name;
                component_part_number = other.component_part_number;
                subboard_name = other.subboard_name;
                fov_ids = other.fov_ids;
                //modules = other.modules;
                device_img = other.device_img;
            }
            return *this;
        }
    };
    JRSREFLECTION(Component, component_type, enable, component_id, x, y, angle, z, component_name, component_part_number, subboard_name, fov_ids/*, modules*/);


    //! 元件简要信息， 主要是为了给维修站和算法检测时保存元件原始的宽高坐标等信息的
    struct BriefComponentInfo
    {
        std::string component_name;            ///< 元件名称
        float x;                               ///< 在大图上 x 坐标(中心坐标 绝对):像素
        float y;                               ///< 在大图上 y 坐标(中心坐标 绝对):像素
        float angle;                          ///< 角度(绝对):度
        float width;                          //! 元件宽度
        float height;                         //!  元件高度
        BriefComponentInfo()
            : component_name{}, x(0.f), y(0.f), angle(0.f), width(0.f), height(0.f)
        {
        }
        BriefComponentInfo(std::string component_name_, float x_, float y_, float angle_, float width_, float height_)
            : component_name(component_name_), x(x_), y(y_), angle(angle_), width(width_), height(height_)
        {
        }
        BriefComponentInfo(const BriefComponentInfo& other)
            : component_name(other.component_name), x(other.x), y(other.y), angle(other.angle), width(other.width), height(other.height)
        {
        }
        BriefComponentInfo& operator=(const BriefComponentInfo& other)
        {
            if (this != &other)
            {
                component_name = other.component_name;
                x = other.x;
                y = other.y;
                angle = other.angle;
                width = other.width;
                height = other.height;
            }
            return *this;
        }
    };

    JRSREFLECTION(BriefComponentInfo, component_name, x, y, angle, width, height)
        enum class MarkGraphicsCategory
    {
        CIRCLE,     ///< 圆形
        CROSS,      ///< 十字形
        SQUARE,     ///< 方形
        DIAMOND,    ///< 菱形
        TRIANGLE,   ///< 三角形
    };
    struct MarkParam
    {
        int match_method;
        MarkGraphicsCategory graphics_category;
        float graphics_size;
        float graphics_score;

        MarkParam() :
            match_method(0),
            graphics_category(MarkGraphicsCategory::CIRCLE),
            graphics_size(1.0),
            graphics_score(50)
        {
        }
    };
    JRSREFLECTION(MarkParam, match_method, graphics_category, graphics_size, graphics_score);

    //! 模型组
    struct DetectModel
    {
        std::vector<DetectWindow> detect_model;
    };
    JRSREFLECTION(DetectModel, detect_model);

    //! 料号
    struct PNDetectInfo
    {
        std::string part_name;              ///< 料号名称
        std::string bind_part_name;         ///< 跟随料号名称,如果该值有效,则使用参数从跟随料号对应的结构体里获取
        std::vector<DetectSpec> specs;      ///< 规格
        std::unordered_map<std::string/**< 算法组名 */, DetectModel> detect_models; ///< 算法模型组，存放算法 这里的key跟units中的unit_group_name相对应为pad1.pad2,body1,body2等
        std::vector<ComponentUnit> units; ///< 元件组件:包括本体，PAD，孔洞
        // 默认构造函数
        PNDetectInfo()
            :part_name(""), bind_part_name(""), specs(), detect_models(), units()
        {
        }

        // 带参构造函数
        PNDetectInfo(const std::string& part_name_, const std::string& bind_part_name_, const std::vector<DetectSpec>& specs_, const std::unordered_map<std::string, DetectModel>& detect_models_, const std::vector<ComponentUnit>& units_)
            : part_name(part_name_), bind_part_name(bind_part_name_), specs(specs_), detect_models(detect_models_), units(units_)
        {
        }

        // 获取本体宽度
        int GetBodyWidth()
        {
            for (const auto& item : units)
            {
                if (item.unit_type == jrsdata::ComponentUnit::Type::BODY)
                {
                    return (int)item.width;
                }
            }
            return 0;
        }

        // 获取本体高度
        int GetBodyHeight()
        {
            for (const auto& item : units)
            {
                if (item.unit_type == jrsdata::ComponentUnit::Type::BODY)
                {
                    return (int)item.height;
                }
            }
            return 0;
        }

        // 根据unit_group_name获取宽高(包括本体和PAD)
        int GetUnitWidth(std::string unit_group_name)
        {
            for (const auto& item : units)
            {
                if (item.unit_group_name == unit_group_name)
                {
                    return (int)item.width;
                }
            }
            return 0;
        }
        int GetUnitHeight(std::string unit_group_name)
        {
            for (const auto& item : units)
            {
                if (item.unit_group_name == unit_group_name)
                {
                    return (int)item.height;
                }
            }
            return 0;
        }

        // 根据unit_group_name获取元件方向
        int GetUnitDirection(std::string unit_group_name)
        {
            for (const auto& item : units)
            {
                if (item.unit_group_name == unit_group_name)
                {
                    return (int)item.direction;
                }
            }
            return 0;
        }

        // 获取所有的检测区域名称
        std::vector<std::string> GetAllWinName(const std::string model_name = "")
        {
            std::vector<std::string> names;
            if (model_name == "")
            {
                for (auto& model : detect_models)
                {
                    for (auto& win : model.second.detect_model)
                    {
                        names.push_back(win.name);
                    }
                }
            }
            else
            {
                auto detect_model = detect_models.find(model_name);
                if (detect_model != detect_models.end())
                {
                    for (auto& win : detect_model->second.detect_model)
                    {
                        names.push_back(win.name);
                    }
                }
            }
            return names;
        }

        DetectWindow* ReadDetectWindowPtr(const std::string& win_name, const std::string model_name = "")
        {
            if (model_name == "")
            {
                for (auto& model : detect_models)
                {
                    for (auto& win : model.second.detect_model)
                    {
                        if (win.name == win_name)
                        {
                            return &win;
                        }
                    }
                }
            }
            else
            {
                auto detect_model = detect_models.find(model_name);
                if (detect_model != detect_models.end())
                {
                    for (auto& win : detect_model->second.detect_model)
                    {
                        if (win.name == win_name)
                        {
                            return &win;
                        }
                    }
                }
            }
            return nullptr;
        }
    };
    //JRSREFLECTION(PNDetectInfo, specs);
    JRSREFLECTION(PNDetectInfo, part_name, bind_part_name, specs, detect_models, units);

    //! 子板
    struct SubBoard
    {
        std::string subboard_name;              ///< 子板名称
        int id;                                 ///< 子板id
        int col;                                ///< 列号
        int row;                                ///< 行号
        float x;                                ///< 子板中心x坐标 整图中的像素坐标
        float y;                                ///< 子板中心y坐标 整图中的像素坐标
        float width;                            ///< 子板宽度
        float height;                           ///< 子板高度
        float angle;                            ///< 子板角度
        bool enable;                            ///< 是否检测
        std::vector<Component> component_info;  ///< 元件信息
        std::vector<Component> sub_mark;        ///< 子板mark
        Component bad_mark;                     ///< 子板坏板mark
        Component barcode;                      ///< 子板条码
        std::string backup;                     ///< 拓展字段，用于多联板拓展时，标记相同的子板；后续其他需求也可以在该字段内拓展


        SubBoard()
            : subboard_name{}
            , id{}
            , col{}
            , row{}
            , x{  }
            , y{  }
            , width{}
            , height{}
            , angle{}
            , enable{}
            , component_info{}
            , sub_mark{}
            , bad_mark{}
            , barcode{}
            , backup{}
        {
        }

        SubBoard(const std::string& subboard_name_,
            int id_,
            int col_,
            int row_,
            float x_,
            float y_,
            float width_,
            float height_,
            float angle_,
            bool is_detection_,
            std::vector<Component> device_info_,
            std::vector<Component> sub_mark_,
            Component bad_mark_,
            Component barcode_,
            std::string backup_
        ) :
            subboard_name(subboard_name_),
            id(id_),
            col(col_),
            row(row_),
            x(x_),
            y(y_),
            width(width_),
            height(height_),
            angle(angle_),
            enable(is_detection_),
            component_info(component_info),
            sub_mark(sub_mark_),
            bad_mark(bad_mark_),
            barcode(barcode_),
            backup(backup_)
        {
        }
        SubBoard(const SubBoard& other) :
            subboard_name(other.subboard_name),
            id(other.id),
            col(other.col),
            row(other.row),
            x(other.x),
            y(other.y),
            width(other.width),
            height(other.height),
            angle(other.angle),
            enable(other.enable),
            component_info(other.component_info), // std::vector 自动深拷贝
            sub_mark(other.sub_mark),
            bad_mark(other.bad_mark),
            barcode(other.barcode),
            backup(other.backup)
        {
        }
        SubBoard& operator=(const SubBoard& other)
        {
            if (this != &other)
            { // 防止自赋值
                subboard_name = other.subboard_name;
                id = other.id;
                col = other.col;
                row = other.row;
                x = other.x;
                y = other.y;
                width = other.width;
                height = other.height;
                angle = other.angle;
                enable = other.enable;
                component_info = other.component_info; // std::vector 自动深拷贝
                sub_mark = other.sub_mark;
                bad_mark = other.bad_mark;
                barcode = other.barcode;
                backup = other.backup;
            }
            return *this;
        }
    };
    JRSREFLECTION(SubBoard, subboard_name, id, col, row, x, y, width, height, angle, enable, component_info, sub_mark, bad_mark, barcode, backup);

    //! 整板
    struct Board
    {
        int width;                                           ///< 宽度(像素)
        int height;                                          ///< 高度(像素)
        int cols;                                            ///< 子板列数
        int rows;                                            ///< 子板行数
        int num_sub_board;                                   ///< 子板个数
        int layout;                                          ///< 子板布局方式，如鸳鸯板，太极板子，复制板子 (枚举)
        int material;                                        ///< 整板材质 (黑板，白板，胶板)
        double real_width;                                   ///< 宽度 (mm)
        double real_height;                                  ///< 高度 (mm)
        double left_top_x;                                   ///< 左上角坐标 物理坐标(mm) (相对于停板位置的相对坐标)
        double left_top_y;                                   ///< 左上角坐标 物理坐标(mm) (相对于停板位置的相对坐标)
        double right_bottom_x;                               ///< 右下坐标 物理坐标(mm)(相对于停板位置的相对坐标)
        double right_bottom_y;                               ///< 右下坐标 物理坐标(mm)(相对于停板位置的相对坐标)
        std::vector<Component> marks;                        ///< 整板MARK
        std::vector<Component> barcodes;                     ///< 整板条码
        std::vector<Component> carrier_barcodes;             ///< 载具条码,可能由硬件检测,区分出来
        std::vector<Component> cover_plate_barcodes;         ///< 盖板条码,可能由硬件检测,区分出来
        std::vector<SubBoard> sub_board;                     ///< 子板信息
        std::unordered_map<std::string, std::string> params; ///< 其他参数 <字符串，json>
        std::unordered_map<std::string/**< part num str */, PNDetectInfo> part_nums_and_detect_regions; /**<料号与检测区域*/;

        Board() :
            width{},
            height{},
            cols{},
            rows{},
            num_sub_board{},
            layout{},
            material{},
            real_width{},
            real_height{},
            left_top_x{},
            left_top_y{},
            right_bottom_x{},
            right_bottom_y{},
            marks{},
            barcodes{},
            carrier_barcodes{},
            cover_plate_barcodes{},
            sub_board{},
            part_nums_and_detect_regions({}),
            params{}
        {
        }

        Board(int width_,
            int height_,
            int cols_,
            int rows_,
            int num_sub_board_,
            int layout_,
            int material_,
            double real_width_,
            double real_height_,
            double left_top_x_,
            double left_top_y_,
            double right_bottom_x_,
            double right_bottom_y_,
            std::vector<Component> marks_,
            std::vector<Component> barcodes_,
            std::vector<Component> carrier_barcodes_,
            std::vector<Component> cover_plate_barcodes_,
            std::vector<SubBoard> sub_board_,
            std::unordered_map<std::string, PNDetectInfo> part_nums_and_detect_regions_,
            std::unordered_map<std::string, std::string> params_) :
            width(width_),
            height(height_),
            cols(cols_),
            rows(rows_),
            num_sub_board(num_sub_board_),
            layout(layout_),
            material(material_),
            real_width(real_width_),
            real_height(real_height_),
            left_top_x(left_top_x_),
            left_top_y(left_top_y_),
            right_bottom_x(right_bottom_x_),
            right_bottom_y(right_bottom_y_),
            marks(marks_),
            barcodes(barcodes_),
            carrier_barcodes(carrier_barcodes_),
            cover_plate_barcodes(cover_plate_barcodes_),
            sub_board(sub_board_),
            params(params_),
            part_nums_and_detect_regions(part_nums_and_detect_regions_)
        {
        }

        Board(const Board& other) :
            width(other.width),
            height(other.height),
            cols(other.cols),
            rows(other.rows),
            num_sub_board(other.num_sub_board),
            layout(other.layout),
            material(other.material),
            real_width(other.real_width),
            real_height(other.real_height),
            left_top_x(other.left_top_x),
            left_top_y(other.left_top_y),
            right_bottom_x(other.right_bottom_x),
            right_bottom_y(other.right_bottom_y),
            marks(other.marks),
            barcodes(other.barcodes),
            carrier_barcodes(other.carrier_barcodes),
            cover_plate_barcodes(other.cover_plate_barcodes),
            sub_board(other.sub_board),
            params(other.params),
            part_nums_and_detect_regions(other.part_nums_and_detect_regions)
        {
        }

        Board& operator=(const Board& other)
        {
            if (this != &other)
            { // 防止自赋值
                width = other.width;
                height = other.height;
                cols = other.cols;
                rows = other.rows;
                num_sub_board = other.num_sub_board;
                layout = other.layout;
                material = other.material;
                real_width = other.real_width;
                real_height = other.real_height;
                left_top_x = other.left_top_x;
                left_top_y = other.left_top_y;
                right_bottom_x = other.right_bottom_x;
                right_bottom_y = other.right_bottom_y;
                marks = other.marks;
                barcodes = other.barcodes;
                carrier_barcodes = other.carrier_barcodes;
                cover_plate_barcodes = other.cover_plate_barcodes;
                sub_board = other.sub_board;
                params = other.params;
                part_nums_and_detect_regions = other.part_nums_and_detect_regions;
            }
            return *this;
        }
    };
    JRSREFLECTION(Board, width, height, cols, rows, num_sub_board, layout, material, real_width, real_height, left_top_x, left_top_y, right_bottom_x, right_bottom_y, marks, barcodes, carrier_barcodes, cover_plate_barcodes, sub_board, part_nums_and_detect_regions, params);
    /** < 缩略图信息 */
    struct ThumbImage
    {
        int compression_rate;/**< 压缩比 */
        cv::Mat thumb_img;  /**< 图片 */
        /** 可以添加缩略图的其他信息 */
        ThumbImage()
            :compression_rate(100), thumb_img({})
        {
        };
    };
    JRSREFLECTION(ThumbImage, compression_rate);
    //! 工程参数
    struct ProjectParam :jrsdata::DataBase
    {
        std::string project_name;                                  /**< 工程名称 */
        struct_pack::compatible <std::string, 20250608> link_project_name;
        std::string current_group_name;      /**< 当前组名 */
        std::string project_path;            /**< 工程路径 */
        std::string version;                ///< 工程版本号
        std::unordered_map<int/**< 图片类型 */, cv::Mat> entirety_board_imgs; /**< 整板图片 */
        std::vector<Template> temps;                               /**< 模版图片 */
        ThumbImage thumb_img;                /**< 缩略图   */
        std::vector<std::string> image_group_names; /**< 所有组名 */
        bool is_save;                       ///< 是否已经保存
        Board board_info;                                          /**< 整版信息 */
        ProjectParam()
            : project_name{}
            , link_project_name{}
            , current_group_name{}
            , project_path{}
            , version{}
            , entirety_board_imgs()
            , temps{}
            , thumb_img({})
            , is_save{ false }
            , board_info{}
        {
            this->file_param.file_type = jrsdata::FileType::BIN;
            this->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        }

        ProjectParam(const ProjectParam& other)
       
            : project_name(other.project_name)
            , link_project_name(other.link_project_name)
            , current_group_name(other.current_group_name)
            , project_path(other.project_path)
            , version(other.version)
            , entirety_board_imgs(other.entirety_board_imgs)
            , temps(other.temps)
            , thumb_img(other.thumb_img)
            , is_save(other.is_save)
            , board_info(other.board_info)

        {
            this->file_param = other.file_param;
            this->data_save_mode = other.data_save_mode;
        }
        ProjectParam& operator=(const ProjectParam& other)
        {
            if (this != &other)
            {
                
                project_name = other.project_name;
                link_project_name = other.link_project_name;
                current_group_name = other.current_group_name;
                project_path = other.project_path;
                version = other.version;
                entirety_board_imgs = other.entirety_board_imgs;
                temps = other.temps;
                thumb_img = other.thumb_img;
                is_save = other.is_save;
                board_info = other.board_info;
                this->file_param = other.file_param;
                this->data_save_mode = other.data_save_mode;
            }
            return *this;
        }
    };
    JRSREFLECTION(ProjectParam, project_name, project_path, board_info, temps, image_group_names, current_group_name, is_save, version,link_project_name);

    using ProjectParamPtr = std::shared_ptr<ProjectParam>;




}

#endif //__JRSPROJECTPARAM_HPP__