#include "savecomponentdialog.h"
#include "ui_savecomponentdialog.h"

SaveComponentDialog::SaveComponentDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::SaveComponentDialog)
{
    ui->setupUi(this);
    Init();
}

SaveComponentDialog::~SaveComponentDialog()
{
    delete ui;
}

void SaveComponentDialog::Init()
{
    connect(ui->over_write_btn, &QPushButton::clicked, this, [this]() { emit this->SigCoverSave(); });
    connect(ui->save_with_partnum_btn, &QPushButton::clicked, this, [this]() { emit this->SigPartNumFirstSave(); });
}
