﻿/*****************************************************************//**
 * @file   objectpool.hpp
 * @brief  对象池
 * @details 与mysql 连接池搭配使用，进行绑定供外部多线程调用
 * <AUTHOR>
 * @date   April 2024
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>April 2024         <td>         <td>HJC                       <td><EMAIL> <td>
 *   @copyright 版权 CopyRight (C), 2024-2025.
 * *********************************************************************/

#ifndef _OBJECTPOOL_H_
#define _OBJECTPOOL_H_
#include <stdexcept>
#include <deque>
#include <mutex>
#include <memory>
#include <iostream>
#include "SingleTonT.hpp"
#include "connectpool.h"
#include "mysqlimp.hpp"
#include "dbmanager.h"

template<typename T>
class ObjectPool {
public:
    /**
     * @fun ObjectPool
     * @brief 对象池构造函数
     * <AUTHOR>
     */
    ObjectPool() {}

    ~ObjectPool()
    {
        DestroyAllObjects();
    }
    /**
     * @fun InitObjectPool
     * @brief 初始化对象池，并且从连接池中获取对象进行连接使用
     * @param pool_size 对象池初始化大小
     * @return  成功：0；失败：错误码
     * <AUTHOR>
     */
    int InitObjectPool(const size_t pool_size, const jrsdatabase::DBPtr& db_ptr_)
    {
        pool_size_ = pool_size;
        for (size_t i = 0; i < pool_size; ++i) {
            auto db_conn_ptr = db_ptr_->GetConnPtr();
            if (db_conn_ptr != nullptr) {
                auto obj_ptr = std::make_shared<T>(db_conn_ptr);
                object_pool_.push(obj_ptr);
            }
            else
            {
                return  jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
            }
        }
        return 0;
    }
    /**
     * @fun AcquireObject
     * @brief 从对象池中获取对象
     * @return  成功返回连接指针，失败则等待
     * <AUTHOR>
     */
    std::shared_ptr<T> AcquireObject() {
        std::unique_lock<std::mutex> lock(mutex_);
        bool success = condition_variable_.wait_for(lock, std::chrono::seconds(30), [&]() {
            return !object_pool_.empty();
            });

        if (!success) {
            return nullptr;
        }
        /*condition_variable_.wait(lock, [&]() {
            return !object_pool_.empty();
        });*/
        //while (object_pool_.empty()) {
        //    // 如果对象池为空，则等待
        //    condition_variable_.wait(lock);
        //}
        auto obj = object_pool_.front();
        object_pool_.pop();

        //// 检查超时,闲置时间应小于8小时
        //auto now = std::chrono::system_clock::now();
        //auto last = obj->GetLatestOperateTime();
        //auto mins = std::chrono::duration_cast<std::chrono::minutes>(now - last).count();
        //if ((mins - 6 * 60) > 0) {
        obj->SetDBConnPtr(_db_ptr->ReGetConnPtr(obj->GetDBConnPtr()));
        /*   }*/
        return obj;
    }
    /**
     * @fun ReturnBack
     * @brief 将对象放回对象池
     * @param obj 对象指针
     * @return  失败:错误码，成功：0
     * <AUTHOR>
     */
    int ReturnBack(std::shared_ptr<T> obj) {
        if (obj == nullptr) {
            return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY; //参数为空
        }
        std::unique_lock<std::mutex> lock(mutex_);
        object_pool_.push(obj);
        lock.unlock();
        condition_variable_.notify_one();
        return 0;
    }
    /**
     * @fun DestroyAllObjects
     * @brief 销毁所有对象
     * <AUTHOR>
     */
    void DestroyAllObjects() {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!object_pool_.empty()) {
            object_pool_.pop(); // 从对象池中移除对象
        }
    }

private:

    /** 对象池 */
    std::queue<std::shared_ptr<T>> object_pool_;
    std::mutex mutex_;/**互斥锁*/
    std::condition_variable condition_variable_;/**条件变量*/
    size_t pool_size_; /**对象池大小*/
    const jrsdatabase::DBPtr _db_ptr;
};

#endif