/*********************************************************************
 * @brief  窗口信号转发类.
 *
 * @file   windowsignalemitter.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef WINDOWSIGNALEMITTER_H
#define WINDOWSIGNALEMITTER_H

#include <QObject>

class WindowSignalEmitter : public QObject
{
    Q_OBJECT

public:
    explicit WindowSignalEmitter(QObject* parent) : QObject(parent) {}
    ~WindowSignalEmitter() {};

Q_SIGNALS:
    void signal_mouse_clicked(int type, int x, int y);
    void signal_mouse_press(int type, int x, int y);
    void signal_mouse_release(int type, int cx, int cy, int px, int py);
    void signal_mouse_move(int type, int lx, int ly, int cx, int cy, int px, int py);

    void signal_move_pos(int x, int y);
    void signal_view_size_change(int x, int y, int w, int h);
    void signal_wheel_delta(int delta, int x, int y);
    void signal_key_press(int, bool);
    void signal_key_release(int, bool);
    void signal_window_resize(int width, int height);
    void signal_window_enter();
    void signal_window_leave();

private:
};

#endif // !WINDOWSIGNALEMITTER_H