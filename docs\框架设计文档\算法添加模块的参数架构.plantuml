@startuml 123

title 算法添加模块的参数架构

class 料号 {
    名称
    算法结构配置
    模型结构配置
}

class 图形 {
    图形形状
    图形位置
    图形参数
}

class 模型 {
    图形
}

class 算法结构配置 {
    算法名称
    算法参数
    算法检测区域
    算法所需模型
}

class PAD {
    编号
    方向
    尺寸
    坐标
}

class 元件 {
    料号
    坐标
    尺寸
    角度
}

class 模型结构配置 {
    名称
    {模型}
}

class 本体 {
    坐标
    尺寸
    角度
}

料号 *-- 算法结构配置
料号 *-- 模型结构配置
模型结构配置 *-- 模型
模型 *-- 图形
元件 *-- 料号

CHIP --|> 算法结构配置
SOT --|> 算法结构配置

本体 --|> 模型
PAD --|> 模型
本体检测 --|> 模型结构配置
焊盘组1 --|> 模型结构配置
焊盘组2 --|> 模型结构配置
焊盘组3 --|> 模型结构配置


@enduml

