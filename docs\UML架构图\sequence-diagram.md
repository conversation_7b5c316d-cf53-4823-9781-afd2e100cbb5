# JRSAOI 检测流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant MainWindow as 主窗口
    participant OperateView as 操作视图
    participant OperateController as 操作控制器
    participant OperateModel as 操作模型
    participant Logic<PERSON>anager as 逻辑管理器
    participant <PERSON>goEng<PERSON> as 算法引擎
    participant Devi<PERSON><PERSON>anager as 设备管理器
    participant DataManager as 数据管理器
    participant StructLight as 结构光相机
    participant Motion as 运动控制

    %% 项目加载流程
    rect rgb(240, 248, 255)
        Note over User, Motion: 项目加载阶段
        User->>MainWindow: 点击打开项目
        MainWindow->>OperateView: 触发项目加载事件
        OperateView->>OperateController: SlotViewOperator(PROJECT_READ_EVENT)
        OperateController->>OperateModel: Update(project_param)
        OperateModel->>DataManager: LoadProjectData(project_name)
        DataManager-->>OperateModel: ProjectParam
        OperateModel-->>OperateController: 项目数据加载完成
        OperateController->>OperateView: 更新界面显示
        OperateView-->>User: 显示项目信息
    end

    %% 设备初始化流程
    rect rgb(255, 248, 240)
        Note over User, Motion: 设备初始化阶段
        User->>OperateView: 点击设备初始化
        OperateView->>OperateController: 设备初始化事件
        OperateController->>LogicManager: EventHandler(DEVICE_INIT_EVENT)
        LogicManager->>DeviceManager: InitDevices()
        
        par 并行初始化设备
            DeviceManager->>StructLight: Connect()
            StructLight-->>DeviceManager: 连接状态
        and
            DeviceManager->>Motion: Connect()
            Motion-->>DeviceManager: 连接状态
        end
        
        DeviceManager-->>LogicManager: 设备初始化完成
        LogicManager-->>OperateController: 设备状态更新
        OperateController->>OperateView: 更新设备状态显示
    end

    %% 检测执行流程
    rect rgb(248, 255, 248)
        Note over User, Motion: 检测执行阶段
        User->>OperateView: 点击开始检测
        OperateView->>OperateController: SlotViewOperator(DETECT_START_EVENT)
        OperateController->>OperateModel: GetProjectParam()
        OperateModel-->>OperateController: 当前项目参数
        
        %% 坐标校正
        OperateController->>LogicManager: 请求Mark点定位
        LogicManager->>DeviceManager: 移动到Mark点位置
        DeviceManager->>Motion: MoveTo(mark_position)
        Motion-->>DeviceManager: 移动完成
        DeviceManager->>StructLight: CaptureImage()
        StructLight-->>DeviceManager: Mark点图像
        DeviceManager-->>LogicManager: Mark点图像数据
        LogicManager->>AlgoEngine: ExecuteMarkDetection(image)
        AlgoEngine-->>LogicManager: Mark点坐标
        LogicManager-->>OperateController: 坐标校正矩阵
        
        %% FOV检测循环
        loop 每个FOV位置
            OperateController->>LogicManager: 请求FOV图像采集
            LogicManager->>DeviceManager: 移动到FOV位置
            DeviceManager->>Motion: MoveTo(fov_position)
            Motion-->>DeviceManager: 移动完成
            DeviceManager->>StructLight: CaptureImage()
            StructLight-->>DeviceManager: FOV图像
            DeviceManager-->>LogicManager: FOV图像数据
            
            %% 算法检测
            LogicManager->>OperateController: FOV图像数据
            OperateController->>AlgoEngine: ExecuteSingleComponent(component, image)
            
            loop 每个检测框
                AlgoEngine->>AlgoEngine: 执行具体算法检测
                Note right of AlgoEngine: OCR/OCV/位置检测等
            end
            
            AlgoEngine-->>OperateController: 检测结果
            OperateController->>OperateModel: 保存检测结果
            OperateModel->>DataManager: SaveDetectResult(result)
            DataManager-->>OperateModel: 保存完成
        end
        
        %% 结果汇总
        OperateController->>DataManager: 生成检测报告
        DataManager-->>OperateController: 检测报告
        OperateController->>OperateView: 更新检测结果显示
        OperateView-->>User: 显示检测完成和结果
    end

    %% 异常处理
    rect rgb(255, 240, 240)
        Note over User, Motion: 异常处理
        alt 设备异常
            DeviceManager->>LogicManager: 设备异常通知
            LogicManager->>OperateController: 异常事件
            OperateController->>OperateView: 显示异常信息
            OperateView-->>User: 异常提示
        else 算法异常
            AlgoEngine->>OperateController: 算法执行异常
            OperateController->>OperateView: 显示算法异常
            OperateView-->>User: 算法异常提示
        end
    end
```

## 流程说明

### 项目加载阶段
1. 用户通过主窗口触发项目加载操作
2. 操作视图接收事件并传递给控制器
3. 控制器调用模型更新项目参数
4. 模型通过数据管理器从存储中加载项目数据
5. 数据加载完成后更新界面显示

### 设备初始化阶段
1. 用户触发设备初始化操作
2. 逻辑管理器协调设备管理器进行初始化
3. 并行初始化结构光相机和运动控制设备
4. 设备状态更新反馈到界面显示

### 检测执行阶段
1. **坐标校正**: 通过Mark点检测建立坐标变换矩阵
2. **FOV循环**: 遍历所有视野位置进行图像采集和检测
3. **算法执行**: 对每个检测框执行相应的检测算法
4. **结果保存**: 将检测结果保存到数据库
5. **报告生成**: 汇总检测结果并更新界面显示

### 异常处理
- 设备异常: 设备连接失败、运动异常等
- 算法异常: 算法执行失败、参数错误等
- 统一通过事件机制通知用户界面
