/*****************************************************************
 * @file   projectdataprocess.h
 * @brief  对数据单例数据的操作如Project结构体进行操作
 * @details
 * <AUTHOR>
 * @date 2024.8.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.29          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSPROJECTDATAPROCESS_H__
#define __JRSPROJECTDATAPROCESS_H__

 //STD
#include <shared_mutex>  // shared_mutex shared_lock
//custom
#include "projectparam.hpp"
#include "pluginexport.hpp"
#include "algoexecuteparam.hpp"
#include "viewparam.hpp"

///!  子板编号均为1开头！！！
namespace jrsdata
{
    enum class LightImageType;
}

namespace jrsparam
{
    struct ExecuteAlgoParam;
    struct AlgoExcuteRectsParam;
    class  JRS_AOI_PLUGIN_API ProjectDataProcess
    {
    public:
        inline bool IsParamPtrEmpty() const
        {
            return !_param;
        }
        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间2024/08/27
        * @brief: 设置工程参数，仅在新建工程、打开工程后进行配置
        * @param {const jrsdata::ProjectParamPtr&} project_param: 工程参数地址
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        void SetProjectParam(const jrsdata::ProjectParamPtr& project_param);
        /**
        *
        */
        void UpdateProjectBoardInfo(const jrsdata::Board& board_info_);
        /**************************************************************************
        * <AUTHOR>
        * @date   完成时间2024/08/27
        * @brief: 获取工程参数，仅在保存工程时调用
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        const jrsdata::ProjectParamPtr& GetProjectParam();
        /**
         * @fun GetProjectName
         * @brief 获取工程名称
         * @return
         * <AUTHOR>
         * @date 2025.1.15
         */
        std::string GetProjectName();

        /**
         * @fun CalculateRowsAndCols
         * @brief  计算子板的行数和列数
         * @return
         * <AUTHOR>
         * @date 2025.6.9
         */
        std::vector<std::vector<jrsdata::SubBoard>> CalculateRowsAndCols(const jrsdata::ProjectParamPtr& project_param_ptr_);

        /**< 元件操作部分功能代码 */
        /**
         * @brief 创建元件
         * @note  注意注意注意注意注意注意注意注意:元件比较特殊,应在外部生成元件的name [jerx]
         */
        int CreateComponent(jrsdata::Component& component);
        int UpdateComponent(jrsdata::Component& component);
        std::optional<std::reference_wrapper<jrsdata::Component>>
            ReadComponentRef(const std::string& component_name, const std::string& subboard_name, jrsdata::Component::Type type);
        std::optional<std::reference_wrapper<jrsdata::Component>>
            ReadComponentRef(const std::string& component_name, jrsdata::Component::Type type);
        /**
         * @brief .
         *  根据料号查询其他元件信息
         * @param component_number_name_
         * @return
         */
        std::optional<std::vector<jrsdata::Component>> ReadComponents(const std::string& component_number_name_);
        /**
         * @brief . 在这个料号内是否还存在元件信息
         *
         * @param component_part_number_
         * @return
         */
        bool IsExistComponentInComponentPartNumber(const std::string& component_part_number_, const jrsdata::Component::Type& type_ = jrsdata::Component::Type::CAD);
        /**
         * @fun ReadAllBriefComponentInfo
         * @brief 读取工程中所有的元件简要信息，主要包含位置信息，主要用于维修站绘制元件区域
         * @return  所有元件简要信息
         * <AUTHOR>
         * @date 2025.1.14
         */
        std::vector<jrsdata::BriefComponentInfo> ReadAllBriefComponentInfo();
        /**
        * @fun ReadAllComponent
        * @brief 获取工程中所有元件信息，包括cad,mark,barcode.....
        * @return 返回工程中所有元件信息
        * <AUTHOR>
        * @date 2025.1.9
        */
        std::vector< std::reference_wrapper<jrsdata::Component >> ReadAllComponent();
        int DeleteComponent(const std::string& component_name, const std::string& subboard_name, jrsdata::Component::Type type);
        int DeleteComponent(const std::string& component_name, jrsdata::Component::Type type);

        /**< 元件组件 */
        int CreateComponentUnit(jrsdata::ComponentUnit& unit, const std::string& part_number_name);
        int UpdateComponentUnit(jrsdata::ComponentUnit& unit, const std::string& part_number_name);
        std::optional<std::reference_wrapper<jrsdata::ComponentUnit>>
            ReadComponentUnitRef(const std::string& part_number_name, const std::string& unit_name);
        std::optional<jrsdata::ComponentUnit>
            ReadComponentUnitByShowID(const std::string& part_number_name_, const std::string& model_name_, const int& show_id_);
        /**<更新unit 宽高*/
        int UpdateComponentUnitPositionAndSize(const std::string& part_number_name, const std::string& unit_name, const cv::Point2f& center_point_, const cv::Size2f& unint_size_);
        /**
         * @brief 读取元件本体组件
         * @param part_number_name 元件料号
         * @return
         */
        std::optional<std::reference_wrapper<jrsdata::ComponentUnit>>
            ReadComponentBodyRef(const std::string& part_number_name);
        /**
         * @fun ReadComponentUnit
         * @brief 查询一个Unit
         * @param component_name
         * @param subboard_name
         * @param unit_name
         * @param type
         * @return
         * <AUTHOR>
         * @date 2025.3.26
         */
        std::optional<jrsdata::ComponentUnit> ReadComponentUnit(const std::string& component_name, const std::string& subboard_name,
            const std::string& unit_name, jrsdata::Component::Type type);
        /**
         * @fun ReadComponentUnitsBySameGroupName
         * @brief  查询相同组的unit
         * @param component_name
         * @param subboard_name
         * @param unit_name
         * @param type
         * @return
         * <AUTHOR>
         * @date 2025.3.26
         */
        std::optional<std::vector<jrsdata::ComponentUnit>>  ReadComponentUnitsBySameGroupName(const std::string& component_name, const std::string& subboard_name,
            const std::string& unit_name, jrsdata::Component::Type type);


        std::optional<jrsdata::ComponentUnit> ReadComponentUnit(const std::string& part_number_name, const std::string& unit_name);
        std::vector<jrsdata::ComponentUnit> ReadComponentUnit(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type);
        int DeleteComponentUnit(const std::string& part_number_name, const std::string& unit_name);
        int DeleteComponentUnit(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type);

        std::vector<jrsdata::ComponentUnit> ReadComponentUnitGroup(const std::string& part_number_name, const std::string& unit_group_name);
        int DeleteComponentUnitGroup(const std::string& part_number_name, const std::string& unit_group_name);

        int GetComponentUnitMaxId(const std::string& part_number_name);
        std::string GetComponentUnitNewGroupName(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type);


        // 获取料号中的某个元件的名称以及它的子板名称(找到一个就返回)
        int GetComponentNameFromPartNumber(const std::string& part_number_name, std::string& component_name, std::string& subboard_name);


        /**< 子板*/
        /**
         * @fun GetSubboardMaxId
         * @brief 获取工程内最大的一个子板ID
         * @return
         * <AUTHOR>
         * @date 2025.1.6
         */
        int GetSubboardMaxId();
        int GetSubboardUnusedID();
        std::optional<std::reference_wrapper<jrsdata::Board>> ReadBoard();
        int CreateSubBoard(const float& cx_, const float& cy_, const float& width_, const float& height_);
        int CreateSubBoard(jrsdata::SubBoard& subBoard);
        std::optional<jrsdata::SubBoard> ReadSubBoard(int subBoardId);
        std::optional<std::reference_wrapper<jrsdata::SubBoard>> ReadSubBoardRef(const int& subBoardId);
        std::optional<jrsdata::SubBoard> ReadSubBoard(const std::string& name);
        std::optional<std::reference_wrapper<jrsdata::SubBoard>> ReadSubBoardRef(const std::string& name);
        int UpdateSubBoard(const jrsdata::SubBoard& newSubBoard);
        /**
         * @fun CopySubboard
         * @brief CopySubboard
         * @return  克隆出来一个新的子板
         * <AUTHOR>
         * @date 2025.4.27
         */
        jrsdata::SubBoard CopySubboard(const jrsdata::SubBoard& subboard_, const cv::Point2d& subboard_center_point_offset_);
        /**
         * @fun UpdateComponentAnglesOfDifferentSubboards
         * @brief 跟新不同子板的所有元件角度
         * @param src_component_name_
         * @param src_subboard_name_
         * @param angle
         * @return  更新过的元件信息和子板信息
         * <AUTHOR>
         * @date 2025.3.12
         */
        std::unordered_map<std::string, std::string > UpdateComponentAnglesAndEnablesOfDifferentSubboards(const std::string& src_component_name_,
            const std::string& src_subboard_name_, const jrsdata::Component::Type& type_,
            float angle,
            bool enable
        );
        /**
         *  更新所有子板的宽高大小和偏移位置.
         */
        int UpdateSubboard(const std::string& subboard_name_, float cx_offset_
            , float cy_offset_, float width_, float height_);
        int ReplaceSubboard(const jrsdata::SubBoard& new_subboard_);
        int UpdateSubBoardSize(jrsdata::SubBoard& newSubBoard);
        int DeleteSubBoard(int subBoardId);
        int DeleteSubBoard(const std::string& subBoardName);

        // 清空子板里面所有元件
        int ClearSubBoard(int subBoardId);
        int ClearSubBoard(const std::string& subBoardName);

        /**
        * @fun GetSubBoardIdBySubBoardName
        * @brief 通过子板名称获取子板ID
        * @param sub_board_name [IN] 子板名称
        * @return 返回子板ID
        * <AUTHOR>
        * @date 2024.12.27
        */
        int GetSubBoardIdBySubBoardName(const std::string& sub_board_name);
        //int GetSubboardNameByComponent(const std::string& component_name);
        /**
         * @fun GetPartNumberInfo
         * @brief  Get All part number map
         * @return
         * <AUTHOR>
         * @date 2024.12.31 - (´▽`ʃ♡ƪ) 17：30  Bye！ 2024
         */
        std::optional<std::unordered_map<std::string, jrsdata::PNDetectInfo>> ProjectDataProcess::GetPartNumberInfo();
        /**
         * @fun CreatePNDetectInfo
         * @brief 根据输入的料号名称，创建料号，料号中包含料号名称信息，规格，算法检测框组信息等
         * @param part_number [IN] 料号名称
         * @param info [OUT] 创建的料号信息
         * @return 成功返回AOI_OK，否则返回错误码
         * <AUTHOR>
         * @date 2024.12.27
         */
        int CreatePNDetectInfo(const std::string& part_number, jrsdata::PNDetectInfo& info);
        /**
         * @fun ReadPNDetectInfo
         * @brief 根据料号名称读取料号检测信息
         * @param part_number [IN] 料号名称
         * @return 返回读取的料号信息
         * <AUTHOR>
         * @date 2024.12.27
         */
        std::optional<jrsdata::PNDetectInfo> ReadPNDetectInfo(const std::string& part_number);
        std::optional<std::reference_wrapper<jrsdata::PNDetectInfo>> ReadPNDetectInfoRef(const std::string& part_number);
        int UpdatePNDetectInfo(const std::string& part_number, jrsdata::PNDetectInfo& info);

        /**
         * @fun UpdatePNDetectModel
         * @brief 元件库更新检测框信息专用接口
         * @param part_number [IN] 料号名称
         * @param info [IN] 从元件库里面查询到的检测框信息
         * @return
         * <AUTHOR>
         * @date 2025.4.8
         */
        int UpdatePNDetectModel(const std::string& part_number, jrsdata::PNDetectInfo& info);
        /**
         * @brief . 检查并删除多余的PartNumber
         *
         * @param part_number_
         * @return
         */
        int DeleteSurplusPNDetectInfo(const std::string& part_number_, const jrsdata::Component::Type& type_ = jrsdata::Component::Type::CAD);
        int DeletePNDetectInfo(const std::string& part_number);


        // 修改料号对应的检测信息和模版图
        int UpdateCompoentInfo(const std::string& part_number, jrsdata::PNDetectInfo& info, std::vector<jrsdata::Template>& temp);
        /**
        * @fun GetModelNameByPartNumberAndDetectWindowName
        * @brief 根据料号和检测框名称，获取model name
        * @param part_number_ [IN] 料号名称
        * @param detect_window_name_ [IN] 检测框名称
        * @return
        * <AUTHOR>
        * @date 2025.5.7
        */
        std::string GetModelNameByPartNumberAndDetectWindowName(const std::string& part_number_, const std::string& detect_window_name_);

        /**< 规格 */
        int CreateSpec(const std::string& part_number_name, jrsdata::DetectSpec& spec);
        std::optional<jrsdata::DetectSpec> ReadSpec(const std::string& part_number_name, const std::string& spec_name);
        int UpdateSpec(const std::string& part_number_name, jrsdata::DetectSpec& spec);
        int DeleteSpec(const std::string& part_number_name, const std::string& spec_name);

        /**< 检测框 */
        int CreateDetectWindow(const std::string& model_name, const std::string& part_number_name, jrsdata::DetectWindow& det_window);
        std::optional<jrsdata::DetectWindow> ReadDetectWindow(const std::string& model_name, const std::string& part_number_name, const std::string& window_name);
        std::optional<std::reference_wrapper<jrsdata::DetectWindow>> ReadDetectWindowRef(const std::string& model_name, const std::string& part_number_name, const std::string& window_name);
        int UpdateDetectWindow(const std::string& model_name, const std::string& part_number_name, jrsdata::DetectWindow& det_window);
        int DeleteDetectWindow(const std::string& model_name, const std::string& part_number_name, const std::string& window_name);
        int DeleteDetectWindowAndTemplate(const std::string& part_number_name, const std::string& window_name);

        int DeleteDisableDetectWindow(std::vector<jrsdata::DetectWindow>& detect_windows_);

        /**< 检测框 不使用model_name 查找速度会下降 */
        std::optional<jrsdata::DetectWindow> ReadDetectWindow(const std::string& part_number_name, const std::string& window_name);
        std::optional<std::reference_wrapper<jrsdata::DetectWindow>> ReadDetectWindowRef(const std::string& part_number_name, const std::string& window_name);
        int UpdateDetectWindow(const std::string& part_number_name, jrsdata::DetectWindow& det_window);
        int DeleteDetectWindow(const std::string& part_number_name, const std::string& window_name);

        // 批量添加算法检测框
        int CreateMultiDetectWindows(jrsdata::ComponentEntity& entity, const jrsdata::MultiAlgoParam& multi_algo_param, const std::map<std::string, std::string>& algo_default_param);

        // 根据元件信息删除所有的检测框
        int DeleteAllDetectWindows(jrsdata::ComponentEntity& entity);


        /**< 算法 */
        int CreateAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, jrsdata::DetectAlgorithm& algorithm);
        std::optional<jrsdata::DetectAlgorithm> ReadAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name);
        std::optional<std::reference_wrapper<jrsdata::DetectAlgorithm>>
            ReadAlgorithmRef(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name);
        int UpdateAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, jrsdata::DetectAlgorithm& algorithm);
        int DeleteAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name);

        /**< 子检测框 */
        int CreateSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, jrsdata::SubDetectWindow& subwindow);
        int CreateSubDetectWindow(jrsdata::DetectWindow& detect_window, const int& sub_win_type, std::vector<jrsdata::SubDetectWindow>& sub_detect_windows);

        std::optional<jrsdata::SubDetectWindow> ReadSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name);
        std::optional<std::reference_wrapper<jrsdata::SubDetectWindow>>
            ReadSubDetectWindowRef(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name);
        int UpdateSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, jrsdata::SubDetectWindow& subwindow);
        int DeleteSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name);

        /** CAD */
        int CreateCAD(jrsdata::Component& cad, const std::string& subboard_name);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadCADRef(const std::string& subboard_name, const std::string& cad_name);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadCADRef(const std::string& cad_name);

        int DeleteCAD(const std::string& subboard_name, const std::string& cad_name);
        int CreateCAD(jrsdata::Component& cad);
        int UpdateCAD(jrsdata::Component& cad);
        int DeleteCAD(const int& cad_id);
        /**
         * @fun GetComponentName
         * @brief
         * @param component_
         * @return
         * <AUTHOR>
         * @date 2025.4.3
         */
        int GenerateComponentName(jrsdata::Component& component_);
        int GenerateComponentNameBySubboardID(jrsdata::Component& component_, int subboard_id = -1);
        /**MARK*********************************/

        //! 添加主板mark
        int CreateMark(jrsdata::Component& mark);
        //! 根据名字查询mark
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadMarkRef(const std::string& markName);

        /**
         * @fun CreateAIComponent
         * @brief 添加AI区域(AI算法专用接口)
         * @param ai_area [IN] 元件信息
         * @param width [IN] 区域宽度
         * @param height [IN] 区域高度
         * @return
         * <AUTHOR>
         * @date 2025.5.26
         */
        int CreateAIComponent(jrsdata::Component& ai_area, float width, float height);

        /**
         * @fun CreateSubboardComponent
         * @brief 添加子板区域元件(AI算法专用接口)
         * @param ai_area [IN] 子板元件信息
         * @return
         * <AUTHOR>
         * @date 2025.5.26
         */
        int CreateSubboardComponent(jrsdata::Component& ai_area);

        /**
         * @fun GetAllBoardMarkRef
         * @brief 获取所有整板MARK数据
         * @return 返回所有整板MARK的值
         * <AUTHOR>
         * @date 2024.12.4
         */
        std::optional<std::reference_wrapper<std::vector<jrsdata::Component>>> ReadAllBoardMarkRef();
        //! 更新mark
        int UpdateMark(jrsdata::Component& mark);

        //! 根据名字删除mark
        int DeleteMark(const std::string& markName);

        //! 查找最大的mark ID
        int GetSubMarkMaxId(const jrsdata::SubBoard& subboard_name);

        //! 根据子板名称创建子板mark
        int CreateSubMark(jrsdata::Component& submark);

        //! 根据子板的名称和子板mark的名称获取mark
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadSubMarkRef(const std::string& subBoardName, const std::string& submarkmarkName);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadSubMarkRef(const std::string& component_name_);
        //! 根据子板的名称更新子板mark
        int UpdateSubMark(jrsdata::Component& submark);

        //! 根据子板名称和子板mark的名称删除子板mark
        int DeleteSubMark(const std::string& subBoardName, const std::string& submarkmarkName);

        int ReplaceSubBadMark(jrsdata::Component& mark);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadSubBadMarkRef(const std::string& component_name_);
        int DeleteSubBadMark(const std::string& subboard_name);

        /** 条码 */
        int CreateBarcode(jrsdata::Component& barcode);
        std::optional<jrsdata::Component> ReadBarcode(const std::string& barcode_name);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadBarcodeRef(const std::string& barcode_name);
        int UpdateBarcode(jrsdata::Component& barcode);
        int DeleteBarcode(const std::string& barcode_name);

        /** 子板条码 */
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadSubBoardBarcodeRef(const std::string& subboard_name, const std::string& barcode_name);
        std::optional<std::reference_wrapper<jrsdata::Component>> ReadSubBoardBarcodeRef(const std::string& barcode_name);
        std::optional<jrsdata::Component> ReadSubBoardBarcode(const std::string& barcode_name);
        /**
         * @fun ReadSubBoardBarcode
         * @brief  根据子板名称与barcode名称查找子板二维码
         * @param subboard_name
         * @param barcode_name
         * @return
         * <AUTHOR>
         * @date 2025.3.27
         */
        std::optional<jrsdata::Component> ReadSubBoardBarcode(const std::string& subboard_name, const std::string& barcode_name);
        int DeleteSubBoardBarcode(const std::string& subboard_name);

        int ReplaceSubBoardBarcode(jrsdata::Component& barcode);

        /** 创建模板 */
        int CreateTemplate(const cv::Mat& src_img, const std::string& color_params, int light_image_id_, jrsdata::Template& output_template);;

        //int CreateTemplate(jrsdata::Template& temp);
        std::optional<jrsdata::Template> ReadTemplate(int temp_id);
        int UpdateTemplate(const jrsdata::Template& temp);
        int DeleteTemplate(int id);

        /**
         * @fun SetProjectName
         * @brief
         * @param project_name
         * @param width  物理单位 mm
         * @param height 物理单位 mm
         * @return
         * <AUTHOR>
         * @date 2024.8.29
         */
        int SetProject(const std::string& project_name_, double width_, double height_);
        int GetProject(std::string& project_name_, double& widht_, double& height_);
        /**
         * @fun SetBoardSize
         * @brief
         * @param width_ 单位 像素
         * @param height_单位 像素
         * @return
         * <AUTHOR>
         * @date 2024.8.29
         */
        int SetBoardSize(int width_, int height_);
        int ReadBoardSizePixel(int& width_, int& height_);
        /**
         * @fun SetBoardRealSize
         * @brief 设置物理大小 单位 mm
         * @param real_width_
         * @param real_height_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int SetBoardRealSize(double real_width_, double real_height_);
        int GetBoardRealSize(double& real_width_, double& real_height_);
        //! 设置主板的子板行列
        int SetBoardRowCol(int row, int col);

        //! 设置主板物理坐标(相对于停板位置的相对坐标)
        int SetBoardPosition(double left_top_x, double left_top_y, double right_bottom_x, double right_bottom_y);

        /** <操作CAD 接口*/

        // int ReplaceCAD(const jrsdata::CadStruct& cad_);
        // int SlotDeleteCad(const std::string& cad_name_);
        // std::optional<jrsdata::CadStruct> ReadCAD(const std::string& cad_name_);
        /**< 整版大图 */
        int UpdateImage(int type_, const cv::Mat& img_);
        std::optional<cv::Mat> ReadImage(int type);
        std::optional<const std::unordered_map<int, cv::Mat>*> ReadImage();
        /**< 清除所有图像，HJC 2025/4/10*/
        int ClearEntiretyBoardImages();
        /**<当前大图组名 */
        std::optional <std::string> GetCurrentEntiretyGroupName();
        int SetCurrentEntiretyGroupName(const std::string& current_group_name_);
        std::optional <std::vector<std::string>> GetEntiretyGroupNames();

        int GetTemplatesByDetectWinName(const std::string& part_numb_name, const std::string& detect_win_name, std::vector<jrsdata::Template>& templates);

        /**
         * @fun  GetSignelComponentExcuteRectsParam
         * @brief 获取单元件执行所需的检测位置信息.
         * @param compoent 元件
         * @param algo_excute_rects_params 算法执行所需的检测位置信息
         * @param crop_image_rect 截取图像的矩形区域
         * @param run_mode 执行类型（0：整版建模，1：在线检测，2：在线调试）
         * @data 2024.12.22
         * @return 错误码
         * <AUTHOR>
         */
        int GetSignelComponentExcuteRectsParam(const jrsdata::Component& compoent, std::map<std::string, jrsparam::AlgoExcuteRectsParam>& algo_excute_rects_params, cv::RotatedRect& crop_image_rect, const ExecuteModeInfo& execute_info = ExecuteModeInfo());

        /**
         * @fun SetPadRectToLocationParam
         * @brief 将pad的矩形信息赋值给location下的算法，因为如焊盘定位这样的算法是需要pad的矩形信息的
         * @param algo_excute_rects_params [OUT] 算法执行所需要的参数信息
         * @return 错误码
         * <AUTHOR>
         * @date 2025.5.13
         */
        int SetPadRectToLocationParam(const std::string& component_part_num_, std::map<std::string, ExecuteAlgoParam>& detect_window_execute_param_);
        /**
         * @fun  GetSignelDetectWinExcuteRectsParam
         * @brief 获取单检测框执行所需的检测位置信息.
         * @param component 检测框所属元件
         * @param component_unit 当前检测框类型（本体检测框、pad检测框、....）
         * @param detect_win 检测框
         * @param algo_excute_rects_param 算法执行所需的检测位置信息
         * @param run_mode 执行类型（0：整版建模，1：在线检测，2：在线调试）
         *  @data 2024.12.22
         * @return 错误码
         * <AUTHOR>
         */
        int GetSignelDetectWinExcuteRectsParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win,
            jrsparam::AlgoExcuteRectsParam& algo_excute_rects_param, const ExecuteModeInfo& execute_info = ExecuteModeInfo());

        /**
         * @fun  CropDetectWinImageFromProject
         * @brief 从整版中截取检测框区域图像.
         * @param type 图像类型
         * @param component 元件
         * @param window_name 检测框名称
         * @param img 截取后图像
         * @param detect_rects 检测框在截图中的位置
         * @param run_mode_info 执行模式信息
         * @data 2025.3.10
         * @return 错误码
         * <AUTHOR>
         */
        int CropDetectWinImageFromProject(const jrsdata::LightImageType type, const jrsdata::Component& component, const std::string& window_name, const jrsdata::ComponentUnit& component_unit, \
            cv::Mat& img, std::vector<std::pair<int, cv::RotatedRect>>& detect_rects, const ExecuteModeInfo& run_mode_info = ExecuteModeInfo());

        /**
         * @fun  RotateCropImageFromProject
         * @brief 从整版图像中截取旋转矩形区域并转正.
         * @param type 图像类型
         * @param rect 矩形区域
         * @param img 截取后图像
         * @param rotate_center 旋转中心
         * @param angle 旋转角度
         * @param matrix_to_src_image 将检测框逆变换到大图的矩阵
         * @data 2024.12.22
         * @return 错误码
         * <AUTHOR>
         */
        int RotateCropImageFromProject(const jrsdata::LightImageType type, const cv::Rect& rect, cv::Mat& croped_image, const cv::Point2f rotate_center = cv::Point2f(-1.0, -1.0), const double angle = 0, cv::Mat* matrix_to_src_image = nullptr, const ExecuteModeInfo& run_mode_info = ExecuteModeInfo());



        /********************工程数据矫正部分**********************/
        /**
         * @fun AlignmentBoard
         * @brief 通过旋转矩阵矫正整版数据坐标
         * @note 正板数据矫正时分为这几个部分：子板数据、整板barcode数据，整板mark数据
         * @param board [IN/OUT] 正版数据
         * @param transform_matrix[IN] 旋转矩阵
         * <AUTHOR>
         * @date 2024.12.26
         */
        void AlignmentBoard(jrsdata::Board& board, const cv::Mat& transform_matrix);
        /**
         * @fun AlignmentSubBoard
         * @brief 通过子板信息，矫正子板坐标数据
         * @note 正板数据矫正时分为这几个部分：元件信息、子板barcode数据，子板mark数据
         * @param sub_board [IN/OUT] 子板信息
         * @param transform_matrix [IN] 旋转矩阵
         * <AUTHOR>
         * @date 2024.12.26
         */
        void AlignmentSubBoard(jrsdata::SubBoard& sub_board, const cv::Mat& transform_matrix);

        /**
         * @fun AlignmentSubBoard
         * @brief 通过子板名称矫正子板坐标数据
         * @param sub_board_name [IN] 子板名称
         * @param transform_matrix [OUT] 旋转矩阵
         * <AUTHOR>
         * @date 2024.12.26
         */
        void AlignmentSubBoard(const std::string& sub_board_name, const cv::Mat& transform_matrix);

        /**
         * @fun AlignmentComponent
         * @brief 通过旋转矩阵，矫正元件坐标信息
         * @param component [IN/OUT] 元件信息
         * @param transform_matrix[IN] 旋转矩阵
         * <AUTHOR>
         * @date 2024.12.26
         */
        void AlignmentComponent(jrsdata::Component& component, const cv::Mat& transform_matrix);

        /**
         * @fun AlignmentComponents
         * @brief 通过旋转矩阵，矫正多个元件坐标信息
         * @param components [IN/OUT] 多个元件信息
         * @param transform_matrix [IN] 旋转矩阵
         * <AUTHOR>
         * @date 2024.12.26
         */
        void AlignmentComponents(std::vector<jrsdata::Component*>& components, const cv::Mat& transform_matrix);


        /************算法执行将工程里面参数转换到算法执行参数中*********************************************/


         /**
         * @fun GetComponentExecuteParam
         * @brief 获取指定元件的执行参数
         * @param component [IN] 指定的元件
         * @param detect_window_execute_param_ [OUT] 当前元件的所有算法检测框参数
         * @param matrix_to_src_image_ [OUT] 将检测框坐标逆变换到大图的矩阵
         * @param execute_mode_ [IN] 执行模式,目前两种模式，自动模式和手动模式
         * @return 成功返回AOI_OK，否则返回错误码
         * <AUTHOR>
         * @date 2024.12.26
         */
        int GetComponentExecuteParam(const jrsdata::Component& component, std::map<std::string, ExecuteAlgoParam>& detect_window_execute_param_, cv::Mat& matrix_to_src_image_, const ExecuteModeInfo& execute_mode_ = ExecuteModeInfo());

        // 获取元件截图和最小外接矩形
        int GetComponentImage(const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& img, cv::RotatedRect& crop_image_rect);
        /**
         * @fun GetComponentLightImg
         * @brief 获取指定元件的4种灯光图片
         * @param component [IN] 指定的元件
         * @param component_img_map [OUT] 元件的灯光图
         * @return 成功返回AOI_OK，否则返回错误码
         * <AUTHOR>
         * @date 2024.12.26
         */
        int GetComponentLightImg(const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& component_img_map);

        int GetDetectWindowExecuteParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win, jrsparam::ExecuteAlgoParam& exect_param, cv::Mat& matrix_to_src_image_, const ExecuteModeInfo& execute_mode_ = ExecuteModeInfo());

        /**
        * @fun GetExecuteImg
        * @brief 获取检测时的多种灯光图片
        * @note 检测过程种需要将工程中的的算法检测框，pad框，子检测框等信息赋值给算法的检测参数种
        * @param matrix_to_src_image_[OUT] 将裁剪的图片坐标还原到原图矩阵
        * @param component_ [IN] 指定的元件
        * @param dst_img_map_ [OUT]  多种灯光图片
        * @param crop_img_rect [IN] 当前元件在整板图/FOV图片上准备截取的区域
        * @param execute_mode_info_[IN] 当前的运行模式信息
        * @return  成功返回AOI_OK，失败返回错误码
        * <AUTHOR>
        * @date 2024.12.27
        */
        int GetExecuteImg(cv::Mat& matrix_to_src_image_, const jrsdata::Component& component_, std::unordered_map<int, cv::Mat>& dst_img_map_, const cv::Rect& crop_img_rect, const ExecuteModeInfo& execute_mode_info_ = ExecuteModeInfo());

        /**
         * @fun SetAlgoExecuteParam
         * @brief 设置算法检测时需要的参数
         * @param component_part_num_name_ [IN] 当前元件的料号名称
         * @param input_img_map_ [IN] 算法检测时的输入图片
         * @param algo_execute_rects_param_ [IN]
         * @param detect_win_execute_param_ [OUT] 当前元件所有算法检测框的信息
         * @return
         * <AUTHOR>
         * @date 2024.12.27
         */
        void SetAlgoExecuteParam(const std::string& component_part_num_name_, const std::unordered_map<int, cv::Mat>& input_img_map_,
            std::map<std::string, jrsparam::AlgoExcuteRectsParam>& algo_execute_rects_param_,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param_);

        /**
        * @fun SetSingleDetectWindowExecuteParam
        * @brief  设置单个算法检测的运行rect参数
        * @param component_part_num_name_ [IN] 当前元件的料号名称
        * @param input_img_map_ [IN] 算法检测时的输入图片
        * @param algo_win_name [IN]
        * @param single_algo_execute_rects_
        * @param single_algo_execute_param
        * <AUTHOR>
        * @date 2024.12.27
        */
        void SetSingleDetectWindowExecuteParam(const std::string& component_part_num_name_, const std::unordered_map<int, cv::Mat>& input_img_map_, const std::string& algo_win_name, jrsparam::AlgoExcuteRectsParam& single_algo_execute_rects_, jrsparam::ExecuteAlgoParam& single_algo_execute_param);

        /**
         * @fun SetAlgoExcuteRectsParam2ExecuteAlgoParam
         * @brief 将算法检测框参数设置到算法运行参数中
         * @param excute_rects_param [IN]
         * @param execute_algo_param [IN]
         * <AUTHOR>
         * @date 2024.12.27
         */
        void  SetAlgoExcuteRectsParam2ExecuteAlgoParam(const jrsparam::AlgoExcuteRectsParam& excute_rects_param, jrsparam::ExecuteAlgoParam& execute_algo_param);


        /*********************算法执行部分功能代码************************/

        /**
         * @fun SortDetectWinsByDepedent
         * @brief 将元件的检测框根据依赖关系排序
         * @param detect_wins [IN] 检测框信息
         * @param sorted_wins [OUT] 排序后的信息
         * @return  成功返回AOI_OK，否则返回错误码
         * <AUTHOR>
         * @date 2024.12.31
         */
        int SortDetectWinsByDepedent(const std::vector<jrsdata::DetectWindow>& detect_wins, std::vector<std::vector<jrsdata::DetectWindow>>& sorted_wins);

        /**
         * @fun GetSearchRegionSize
         * @brief  获取搜索区域大小
         * @param detect_window_
         * @return
         * <AUTHOR>
         * @date 2025.1.15
         */
        std::pair<int/**< width */, int /**< hight */> GetSearchRegionSize(const jrsdata::DetectWindow& detect_window_);
        /**
         * @fun GetSearchRegionSize
         * @brief  获取搜索范围 根据size和search_size 获取
         * @param src_size_
         * @param search_size_
         * @return
         * <AUTHOR>
         * @date 2025.3.5
         */
        cv::Size2f GetSearchRegionSize(const cv::Size2f& src_size_, const float& search_size_);

        /**
         * @fun GetRotatedRectByPadDirection
         * @brief   根据pad方向获取旋转矩形   输入和输出 均为相对坐标
         * @param component_center_point_
         * @param pad_center_point_
         * @param detect_window_rect_
         * @param src_direction_
         * @param obj_direction_
         * @return
         * <AUTHOR>
         * @date 2025.3.5
         */
        cv::RotatedRect GetRotatedRectByPadDirection(
            const cv::Point2f& component_center_point_, /**< 元件中心坐标*/
            const cv::Point2f& pad_center_point_,       /**<pad 中心坐标*/
            const cv::RotatedRect& detect_window_rect_,/**< 检测框中心坐标*/
            const jrsdata::ComponentUnit::Direction& src_direction_,  /**< 原始方向*/
            const jrsdata::ComponentUnit::Direction& obj_direction_ = jrsdata::ComponentUnit::Direction::UP
        );


        /**
         * @fun SetResolution
         * @brief 设置相机分辨率
         * @param resolution_x [IN] 相机X方向分辨率
         * @param resolution_y [IN] 相机Y方向分辨率
         * <AUTHOR>
         * @date 2025.3.6
         */
        void SetResolution(float resolution_x_, float resolution_y_);

        /**
        * @fun GetResolution
        * @brief 获取相机分辨率
        * @param resolution_x [IN] 相机X方向分辨率
        * @param resolution_y [IN] 相机Y方向分辨率
         * <AUTHOR>
         * @date 2025.5.20
        */
        void GetResolution(float& resolution_x_, float& resolution_y_);

        /****************元件操作************************/

        /**
         * @fun UpdateSingleComponentInfo
         * @brief 更新单个元件信息，不同步到多联板,如元件坐标，角度，名称等，当前只有更改x,y坐标,角度
         * @param new_component_ [IN] 新的元件信息
         * <AUTHOR>
         * @date 2025.3.21
         */
        void UpdateSingleComponentInfo(const jrsdata::Component& new_component_);

        /**
         * @fun GetNotDetectUnitName
         * @brief 每个每个料号下不检测的组名 丢弃使用，目前没有用到，后期有组功能后会用到 by zhangyuyu 2025.4.30
         * @return 返回料号下不检测的组名
         * <AUTHOR>
         * @date 2025.3.24
         */
        std::unordered_map<std::string/*料号名称*/, std::vector<std::string/*料号下的组名*/>> GetNotDetectUnitName();

        /****************多工程操作************************/
        /**
         * @fun AppendProject
         * @brief 添加一个新的工程到多工程列表中
         * @param new_project [IN] 新的工程参数
         * <AUTHOR>
         * @date 2025.3.24
         */
        void AppendProject(const jrsdata::ProjectParamPtr& new_project);

        /**
         * @fun GetMultiProjectMap
         * @brief 获取多工程
         * <AUTHOR>
         * @date 2025.3.24
         */
        const std::map<std::string, jrsdata::ProjectParamPtr>& GetMultiProjectMap() const;

        /**
         * @fun SetMultiProjectMap
         * @brief 设置多工程
         * @param multi_project_vector_ [IN] 多工程vector
         * <AUTHOR>
         * @date 2025.6.9
         */
        void SetMultiProjectMap(const std::map<std::string, jrsdata::ProjectParamPtr>& multi_project_map_);
        /**
         * @fun ClearProjectMap
         * @brief 清除掉导入的多工程，当重新打开一个工程后
         * <AUTHOR>
         * @date 2025.6.8
         */
        void ClearProjectMap();

        /**
         * @fun SetCurrentProjectLink
         * @brief 设置当前工程的关联工程
         * @param link_project_name [IN] 关联工程名称
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SetCurrentProjectLink(const std::string& link_project_name);

        /**
         * @fun CancleCurrentProjectLink
         * @brief 取消当前工程的关联工程
         * <AUTHOR>
         * @date 2025.6.8
         */
        void CancleCurrentProjectLink();

        /**
         * @fun GetProjectNameLists
         * @brief 获取多工程列表名称
         * @return  返回多工程列表名称列表
         * <AUTHOR>
         * @date 2025.6.9
         */
        std::list<std::string> GetProjectNameLists();

        /**
         * @fun GetLinkedProjectName
         * @brief 获取当前工程已经关联的工程名称
         * @return
         * <AUTHOR>
         * @date 2025.6.9
         */
        std::string GetLinkedProjectName();
        /**
         * @fun IsLoadLinkProject
         * @brief 关联的工程是否已经导入
         * @param link_project_name_ [IN] 关联的工程名称
         * @return  导入：true,未导入：false
         * <AUTHOR>
         * @date 2025.6.9
         */
        bool IsLoadLinkProject(const std::string& link_project_name_);
    public:

        ProjectDataProcess();
        ~ProjectDataProcess();
        ProjectDataProcess(const ProjectDataProcess&) = delete;
        ProjectDataProcess(ProjectDataProcess&&) = delete;
        ProjectDataProcess& operator=(const ProjectDataProcess&) = delete;
        ProjectDataProcess& operator=(ProjectDataProcess&&) = delete;

        void SetProjectEdited() { project_edited = true; }
        void SetProjectSaved() { project_edited = false; }
        bool IsProjectNeedSave() { return project_edited; }

        bool project_edited = false;     ///< 工程是否被编辑,控制是否进行保存
        jrsdata::ProjectParamPtr _param; ///< 当前选中执行工程参数，因为导入了多个工程参数，所以需要一个当前选中执行的工程参数 by zhangyuyu 2025.6.8
        std::map<std::string/*工程名称*/, jrsdata::ProjectParamPtr> multi_project_map;/**< 导入多个工程的存储容器*/

        mutable std::shared_mutex mutex; ///< 允许共享的锁
        float resolution_x; /**< 相机X方向分辨率*/
        float resolution_y; /**< 相机Y方向分辨率*/

    private:
        /**
         * @fun GetNewComponentID
         * @brief  获取一个新元件ID
         * @param component_
         * @return
         * <AUTHOR>
         * @date 2025.4.2
         */
        int GetNewComponentID(const std::vector<jrsdata::Component>& component_);
        /**
         * @fun SynchronousOtherSameSubboard
         * @brief  创建时将 component 同步到其他子板
         * @param component_
         * @return
         * <AUTHOR>
         * @date 2025.4.3
         */
        int SynchronousOtherSameSubboard(const jrsdata::Component& component_);

        /**
         * @fun DeleteSynchronousOtherSameSubboard
         * @brief  删除其他相同子板的component
         * @param component_
         * @return
         * <AUTHOR>
         * @date 2025.4.3
         */
        int SynchronousDeleteOtherSameSubboard(const jrsdata::Component& component_);
        /**
         * @fun SynchronousUpdateOtherSameSubboard
         * @brief 更新到其他相同子板的component
         * @param component_
         * @return
         * <AUTHOR>
         * @date 2025.4.4
         */
        int SynchronousUpdateOtherSameSubboard(const jrsdata::Component& component_);
    };
    using ProjectDataProcessPtr = std::shared_ptr<ProjectDataProcess>;
};

#endif // !__JRSPROJECTDATAPROCESS_H__
