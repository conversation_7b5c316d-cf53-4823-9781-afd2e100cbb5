/*****************************************************************
 * @file   left_top_to_right_bottom_invert_z.hpp
 * @brief  竖形z 从左上到右下
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class ZVerticalLeftTopToRightBottom :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int row_count = static_cast<int>(subboards_.size());
            int max_col = 0;
            for (const auto& row : subboards_)
            {
                if (row.size() > max_col)
                {
                    max_col = static_cast<int>(row.size());
                }
            }

            int subboard_id = 1;
            for (int col = max_col - 1; col >= 0; --col) //right to left
            {
                for (int row = 0; row < row_count; ++row)  // top to bottom
                {
                    if (col < static_cast<int>(subboards_[row].size()))
                    {
                        UpdateSubboard(subboards_[row][col], subboard_id++);
                    }
                }
            }
            return jrscore::AOI_OK;
        }
    };
}
