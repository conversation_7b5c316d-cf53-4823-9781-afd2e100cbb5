#pragma once 
/*****************************************************************
 * @file   projectoperator.h
 * @brief  对数据单例数据的操作如Project结构体进行操作
 * @details
 * <AUTHOR>
 * @date 2024.8.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.29          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //custom
#include "projectparam.hpp"
#include "projectdataprocess.h"
#include "parameterprocess.hpp"

namespace jrsaoi
{

    class ParamOperator
    {
    public:
        static ParamOperator& GetInstance()
        {
            static ParamOperator instance; // 在首次调用时创建实例，并在程序生命周期内保持
            return instance;
        }

        /**
         * @fun GetProjectDataProcessInstance
         * @brief 获取工程数据处理实例,数据处理中主要是对工程数据(整板数据，子板数据，元件....)进行指定获取，复制，删除等操作
         * @return  返回工程数据处理实例
         * <AUTHOR>
         * @date 2024.12.25
         */
        const std::shared_ptr<jrsparam::ProjectDataProcess>& GetProjectDataProcessInstance();
        /**
         * @fun GetParameterProcessInstance
         * @brief 获取参数处理实力，主要对参数的设置和获取
         * @return  返回参数处理实例
         * <AUTHOR>
         * @date 2025.5.6
         */
        const std::shared_ptr<jrsparam::ParameterProcess>& GetParameterProcessInstance();
    private:
        ParamOperator();
        ~ParamOperator();
        ParamOperator(const ParamOperator&) = delete;
        ParamOperator(ParamOperator&&) = delete;
        ParamOperator& operator=(const ParamOperator&) = delete;
        ParamOperator& operator=(ParamOperator&&) = delete;

        std::shared_ptr<jrsparam::ProjectDataProcess> project_data_process_ptr;

        std::shared_ptr<jrsparam::ParameterProcess> param_process_ptr;
    };

};
