#pragma once

#include <QProxyStyle>
#include <QPainter>
#include <QStyleOptionViewItem>
#include <QStyle>

class CustomStyle : public QProxyStyle {
public:
    using QProxyStyle::QProxyStyle;

    void drawControl(ControlElement element, const QStyleOption* option, QPainter* painter, const QWidget* widget) const override {
        if (element == CE_ItemViewItem) {
            if (const QStyleOptionViewItem* viewItemOption = qstyleoption_cast<const QStyleOptionViewItem*>(option)) {
                if (viewItemOption->state & QStyle::State_Selected) {
                    painter->fillRect(viewItemOption->rect, Qt::blue); // 修改为你想要的颜色
                    painter->setPen(Qt::white);
                    painter->drawText(viewItemOption->rect, Qt::AlignCenter, viewItemOption->text);
                    return;
                }
            }
        }
        QProxyStyle::drawControl(element, option, painter, widget);
    }
};
