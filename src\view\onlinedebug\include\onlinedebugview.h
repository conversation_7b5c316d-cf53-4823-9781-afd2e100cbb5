/*****************************************************************
 * @file   onlinedebugview.h
 * @brief   在线调试视图，用于显示在线调试的数据
 * @details
 * <AUTHOR>
 * @date 2025.3.10
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.3.10          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef DFB4BB4A_2E52_4467_B719_2CDAE43F7B7F
#define DFB4BB4A_2E52_4467_B719_2CDAE43F7B7F
//!Custom
#include "pch.h"
//!QT
#include "ui_onlinedebugview.h"
QT_BEGIN_NAMESPACE
namespace Ui { class OnLineDebugView; };
QT_END_NAMESPACE
namespace jrsdata
{
    struct OnlineDebugParm;
}
namespace jrsaoi
{
    class CustomTableListView;
    class OnLineDebugView : public ViewBase
    {
        Q_OBJECT

        public:
            OnLineDebugView(const std::string& name, QWidget* parent = nullptr);
            ~OnLineDebugView();
            int Init() override;
            Q_INVOKABLE int UpdateView(const jrsdata::ViewParamBasePtr& param_) override;
            int Save(const jrsdata::ViewParamBasePtr& param_) override;
            QWidget* GetCustomWidget() override;
        private slots:

            /**
             * @fun SlotIsStopDebug
             * @brief  是否停机调试
             * @param  is_stop [IN]是否停机调试
             * <AUTHOR>
             * @date 2025.4.8
             */
            void SlotIsStopDebug(bool is_stop); 
            /**
             * @fun SlotSearchComponent 
             * @brief 查询元件按钮响应槽函数
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotSearchComponent();
            /**
             * @fun SlotUpComponent 
             * @brief 上移元件按钮响应槽函数
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotUpComponent();
            /**
             * @fun SlotDownComponent 
             * @brief 下移元件按钮响应槽函数
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotDownComponent();
            /**
             * @fun SlotConfirm 
             * @brief 确认按钮响应槽函数
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotConfirm();
            /**
             * @fun SlotExit 
             * @brief 退出按钮响应槽函数
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotExit();
            /**
             * @fun SlotSearchComponentTextChange 
             * @brief 查询元件输入框响应槽函数
             * @param text [IN]输入框内容
             * <AUTHOR>
             * @date 2025.4.11
             */
            void SlotSearchComponentTextChange(const QString& text);
            /**
            * @fun SlotCurrentSelectedRow
            * @brief 当前行被选中时的槽函数
            * @param current_row_data [IN] 当前行的数据
            * <AUTHOR>
            * @date 2025.2.10
            */
            void SlotCurrentSelectedRow(const std::vector<QVariant>& current_row_data);
        signals:
        
            void SigUpdateOnline(const jrsdata::ViewParamBasePtr& param_);
        private:
            //!Member
            
            /**
             * @fun UpdateDebugComponentInfo 
             * @brief  更新调试元件信息
             * @param online_debug_info_[IN]调试元件信息
             * <AUTHOR>
             * @date 2025.4.14
             */
            void UpdateDebugComponentInfo(const jrsdata::OnlineDebugViewParamPtr& online_debug_info_);
            /**
             * @fun ClearUiData 
             * @brief  确认按钮点击后，清空UI数据
             * <AUTHOR>
             * @date 2025.4.16
             */
            void ClearUiData();
            //!Function
            void InitView();
            void InitConnect();
            Ui::OnLineDebugView* ui;
            CustomTableListView* component_show_list_view;
            std::shared_ptr<jrsdata::OnlineDebugParm> online_debug_param_ptr;
            bool is_waitting_debug_info;
    };

}
#endif /* DFB4BB4A_2E52_4467_B719_2CDAE43F7B7F */
