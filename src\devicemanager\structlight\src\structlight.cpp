#include "jrsstructlightcontrlmodule.h"
#include "structlight.h"
#include "coreapplication.h"
#include "tools.h"
#include "baseground.h"
namespace jrsdevice
{
    StructLight::StructLight()
        :struct_light_control(std::make_unique<JrsStructlightContrlModule>())
    {
        grab_imgs_thread = new std::thread(&StructLight::GrabImgsThreadFunc, this);
    }

    StructLight::~StructLight()
    {
        if (grab_imgs_thread)
        {
            kill_grab_imgs_thread = true;
            con_grab_imgs.notify_all();
            grab_imgs_thread->join();
            delete grab_imgs_thread;
            grab_imgs_thread = nullptr;
        }
    }

    int StructLight::Init(std::string& init_error_result_)
    {

        int res = -1;
        jrsdata::InitDeivceResults device_result_map;
        /*占初始化64.7%耗时 注释方便离线调试 jerx*/
        res = InitDevices(jrscore::AOITools::GetCurrentPath() + "//config//structlightconfig/slcamera_config.json", device_result_map);
        for (auto& check_item_map : device_result_map)
        {
            auto [resualt, description] = check_item_map.second;
            if (!resualt)
            {
                init_error_result_.append(description + "\n");
            }
        }
        //获取标定参数 //////////////////////////////
        SLCameraParam param;
        struct_light_control->GetCameraParam(param,true);
        struct_light_param.camera_fov_h = param.camera_screen_h;
        struct_light_param.camera_fov_w = param.camera_screen_w;
        struct_light_param.resolution_x = param.resolution_x;
        struct_light_param.resolution_y = param.resolution_y;
        struct_light_param.z_focus_pos = param.z_focus_pos;
        ////////////////////////////////////////////

        auto new_buffer_call_back = std::bind(&StructLight::NewBuffer, this, std::placeholders::_1, std::placeholders::_2);
        auto capture_trigger_done = std::bind(&StructLight::CaptureTriggerDone, this, std::placeholders::_1);
        struct_light_control->SetGrabCallBack(new_buffer_call_back);
        struct_light_control->SetGrab2DImgeDoneCallBack(capture_trigger_done);
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR("初始化成像设备失败....");
            return res;
        }
        else
        {
            Log_INFO("初始化成像设备成功");
        }

 
        return jrscore::AOI_OK;
    }

    int StructLight::InitDevices(const std::string& config_path, jrsdata::InitDeivceResults& device_result_)
    {
        return struct_light_control->InitDevices(config_path, device_result_);
    }

    void StructLight::NewBuffer(const ImgsBuffer& imgs, const int& error_code_)
    {

        jrsdata::OneFovImgs one_fov_imgs;
        if (error_code_ != jrscore::AOI_OK)
        {
            PushErrorToStack(jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION, "回调回来的图片异常");
            if (merge_done_callback)
            {
                merge_done_callback(one_fov_imgs, jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION);

            }
            return;
        }

        std::vector<cv::Mat> img_vec;
        struct_light_control->SLImageBufferCopyToVecImgs(imgs, img_vec);

        jrsdata::TriggerModeCapture trigger_mode_capture = static_cast<jrsdata::TriggerModeCapture>(imgs.output_imgs.type);

        int res = VectorImage2OneFovImgsByTriggerType(img_vec, trigger_mode_capture, one_fov_imgs);

        one_fov_imgs.fov_id = imgs.output_imgs.fov_id;

        /**************基面校正调用****************/
        //auto& image = one_fov_imgs.imgs[jrsdata::LightImageType::HEIGHT];
        //if (!image.empty())
        //{
        //    int error_code = PlaneProjectByHist1(image, image, this->struct_light_param.resolution_x, this->struct_light_param.resolution_y);
        //    if (error_code != 0) {
        //        if (error_code != jrscore::AOI_OK)
        //        {
        //            PushErrorToStack(jrscore::DeviceError::E_AOI_DEVICE_BASEPLANE_WARP_FAILURE, "高度校正失败。");
        //            return;
        //        }
        //    }
        //    /************ 基面校正调用*****************/
        //}
        
        if (res != jrscore::AOI_OK)
        {
            PushErrorToStack(jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION, "合成图片异常");
            return;
        }

        std::lock_guard < std::mutex > locker(deque_mutex);
        grab_imgs_queue.push(one_fov_imgs);
        con_grab_imgs.notify_all();
    }

    void StructLight::CaptureTriggerDone(const int& error_code)
    {
        if (error_code != jrscore::AOI_OK)
        {

            PushErrorToStack(jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_TRIGGER_FAILURE, "单次触发采集图像失败");
        }

        //! 用于将单次触发完成通知给外部
        if (callback_trigger_done)
        {
            callback_trigger_done(error_code);

        }

    }

    /*int StructLight::SLImagesBuffer2OneFovImgs(const ImgsBuffer& img_buffer, jrsdata::OneFovImgs& one_fov_imgs)
    {
        one_fov_imgs.imgs.clear();
        one_fov_imgs.fov_id = img_buffer.output_imgs.fov_id;
        switch (img_buffer.output_imgs.type)
        {
        case JRS_LIGHT_TRIGGER_TYPE::RGB_TRIGGER:
        {
            Mat rgb;
            vector<Mat> vm;
            vm.assign(img_buffer.output_imgs.gray_images.begin(), img_buffer.output_imgs.gray_images.begin() + 3);
            cv::merge(vm, rgb);
            cv::cvtColor(rgb, rgb, cv::COLOR_BGR2RGB);
            one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = rgb;
            break;
        }
        case JRS_LIGHT_TRIGGER_TYPE::LGTS_TRIGGER:
        {
            Mat rgb, white, gray;
            vector<Mat> vm;
            vm.assign(img_buffer.output_imgs.gray_images.begin(), img_buffer.output_imgs.gray_images.begin() + 3);
            cv::merge(vm, rgb);
            cv::cvtColor(rgb, rgb, cv::COLOR_BGR2RGB);
            one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = rgb;

            vm.assign(img_buffer.output_imgs.gray_images.begin() + 3, img_buffer.output_imgs.gray_images.begin() + 6);
            cv::merge(vm, white);
            cv::cvtColor(white, white, cv::COLOR_BGR2RGB);
            one_fov_imgs.imgs[jrsdata::LightImageType::WHITE] = white;

            Mat low_img = (img_buffer.output_imgs.gray_images.begin() + 6)->clone();
            one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = low_img;
            break;
        }
        case JRS_LIGHT_TRIGGER_TYPE::LOW_TRIGGER:
        {
            Mat low_img = (img_buffer.output_imgs.gray_images.begin() + 6)->clone();
            one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = low_img;
            break;
        }
        case JRS_LIGHT_TRIGGER_TYPE::SLE_TRIGGER:
        case JRS_LIGHT_TRIGGER_TYPE::NO_TRIGGER:
        {
            Mat low_img = img_buffer.output_imgs.gray_images[0].clone();
            one_fov_imgs.imgs[jrsdata::LightImageType::OTHERS] = low_img;
            break;
        }
        case JRS_LIGHT_TRIGGER_TYPE::LGTS_HEIG_TRIGGER:
        {
            Mat rgb, white, gray;
            vector<Mat> vm;
            vm.assign(img_buffer.output_imgs.gray_images.begin(), img_buffer.output_imgs.gray_images.begin() + 3);
            cv::merge(vm, rgb);
            cv::cvtColor(rgb, rgb, cv::COLOR_BGR2RGB);
            one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = rgb;

            vm.assign(img_buffer.output_imgs.gray_images.begin() + 3, img_buffer.output_imgs.gray_images.begin() + 6);
            cv::merge(vm, white);
            cv::cvtColor(white, white, cv::COLOR_BGR2RGB);
            one_fov_imgs.imgs[jrsdata::LightImageType::WHITE] = white;

            Mat low_img = (img_buffer.output_imgs.gray_images.begin() + 6)->clone();
            one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = low_img;

            Mat height_img = img_buffer.output_imgs.height.clone();
            one_fov_imgs.imgs[jrsdata::LightImageType::HEIGHT] = height_img;
            break;
        }
        default:
        {
            return -1;
            break;
        }
        }
        return 0;
    }*/

    int StructLight::VectorImage2OneFovImgsByTriggerType(const std::vector<cv::Mat>& imgs, const jrsdata::TriggerModeCapture triger_type, jrsdata::OneFovImgs& one_fov_imgs)
    {
        switch (triger_type)
        {
            case jrsdata::TriggerModeCapture::RGB_TRIGGER:
            {
                one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = imgs[0].clone();
                break;
            }
            case jrsdata::TriggerModeCapture::LGTS_TRIGGER:
            {
                one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = imgs[0].clone();
                one_fov_imgs.imgs[jrsdata::LightImageType::WHITE] = imgs[1].clone();
                one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = imgs[2].clone();
                break;
            }
            case jrsdata::TriggerModeCapture::LOW_TRIGGER:
            {
                one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = imgs[0].clone();
                break;
            }
            case jrsdata::TriggerModeCapture::LGTS_HEIG_TRIGGER:
            {
                one_fov_imgs.imgs[jrsdata::LightImageType::RGB] = imgs[0].clone();
                one_fov_imgs.imgs[jrsdata::LightImageType::WHITE] = imgs[1].clone();
                one_fov_imgs.imgs[jrsdata::LightImageType::LOWWHITE] = imgs[2].clone();
                one_fov_imgs.imgs[jrsdata::LightImageType::HEIGHT] = imgs[3].clone();
                break;
            }
        default:
            break;
        }

        return 0;
    }

    int StructLight::GrabImgsThreadFunc()
    {
        mutex data_mutex;
        unique_lock<mutex> locker(data_mutex);
        while (true)
        {
            con_grab_imgs.wait(locker, [this]() {
                return (!grab_imgs_queue.empty()) || 
                       kill_grab_imgs_thread;});

            if (kill_grab_imgs_thread)
            {
                {
                    std::lock_guard < std::mutex > queue_locker(deque_mutex);
                    while (!grab_imgs_queue.empty())
                    {
                        grab_imgs_queue.pop();
                    }
                }
                break;
            }
            jrsdata::OneFovImgs one_fov_imgs;
            {
                std::lock_guard < std::mutex > queue_locker(deque_mutex);
                one_fov_imgs = grab_imgs_queue.front();
                grab_imgs_queue.pop();
            }

            if (merge_done_callback)
            {
                merge_done_callback(one_fov_imgs, jrscore::AOI_OK);
            }
        }
        return 0;
    }

    int StructLight::PushTriggerToQueue(const jrsdata::TriggerModeCapture& trigger_type, const int fov_id)
    {
        auto res = struct_light_control->PushTriggerToQueue(static_cast<JRS_LIGHT_TRIGGER_TYPE>(trigger_type), fov_id);
        return res;
    }

    jrsdata::JrsImageBuffer StructLight::GetSingleBuffer()
    {

        // if(buffer_queue.empty())
        // {
        //     Log_ERROR ("buffer_queue is empty");
        //     return jrsdata::JrsBuffer();
        // }
        // std::lock_guard<std::mutex> lock ( deque_mutex );
        // auto buffer_temp = std::move(buffer_queue.front ());
        // buffer_queue.pop_front ();
        jrsdata::JrsImageBuffer buffer_temp;
        return buffer_temp;
    }

    void StructLight::SetTriggerDoneCallback(jrsdata::Grab2DImgCallBack callback_trigger_done_)
    {
        callback_trigger_done = callback_trigger_done_;
    }

    void StructLight::SetMergeImageDoneCallbacek(jrsdata::CaptureCallBack merge_done_callback_)
    {
        merge_done_callback = merge_done_callback_;
    }

    jrsdata::StructLightParam StructLight::GetStructLightParam()
    {
        return struct_light_param;
    }

    void StructLight::SetIOTriggerFuc(std::function<void(const int& io_id, int& erro_code)> io_trigger_fuc)
    {
        struct_light_control->SetIOTriggerFunc(io_trigger_fuc);
    }

    int StructLight::SetLightValByBoardMaterial(const jrsdata::BOARD_MATERIAL& board_material)
    {
        JRS_LIGHT_VAL_LEVEL light_val_level = JRS_LIGHT_VAL_LEVEL::LIGHT_VAL_LOW_LEVEL;
        DLP_LIGHT_LEVEL     dlp_light_level = DLP_LIGHT_LEVEL::LOW;
        switch (board_material)
        {
        case jrsdata::BOARD_MATERIAL::GREEN_BOARD:
        {
            light_val_level = JRS_LIGHT_VAL_LEVEL::LIGHT_VAL_MID_LEVEL;
            dlp_light_level = DLP_LIGHT_LEVEL::MEDIUM;
            break;
        }
        case jrsdata::BOARD_MATERIAL::WHITE_BOARD:
        {
            light_val_level = JRS_LIGHT_VAL_LEVEL::LIGHT_VAL_LOW_LEVEL;
            dlp_light_level = DLP_LIGHT_LEVEL::LOW;
            break;
        }
        case jrsdata::BOARD_MATERIAL::BLACK_BOARD:
        {
            light_val_level = JRS_LIGHT_VAL_LEVEL::LIGHT_VAL_HIGH_LEVEL;
            dlp_light_level = DLP_LIGHT_LEVEL::HIGH;
            break;
        }
        default:
        {
            Log_ERROR("未知的板子类型，无法设置灯光值....");
            break;
        }  
        } 
        int res = -1;
        res=struct_light_control->Set2DLightValByLevel(light_val_level);
        if (res != 0)
        {
            Log_ERROR("设置2D灯光失败....");
            return -1;
        }
        res=struct_light_control->SetDlpLightValByLevel(dlp_light_level);
        if (res != 0)
        {
            Log_ERROR("设置DLP亮度值失败....");
            return -2;
        }
        return 0;
    }
}
