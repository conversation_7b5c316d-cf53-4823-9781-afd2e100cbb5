/*****************************************************************//**
 * @file   colorwidget.h
 * @brief  AOI颜色控件
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSCOLORWIDGET_H__
#define __JRSCOLORWIDGET_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>

// qt
#include <qwidget.h>

// custom
#include "colorparams.h"



#pragma warning(pop)
class QWidget;
using ColorParamsChangedFunc = 
std::function<void(ColorParams& params, 
	cv::Mat& enhanced_img, cv::Mat& binary_img)>;

class ColorModule
{
public:

	ColorModule();
	~ColorModule();
	/**
	* @fun  CreateColorWheel
	* @brief  构造函数
	* @date   2024.08.18
	* <AUTHOR>
	*/
	static ColorModule* CreateColorWheel(QWidget* parent = nullptr);
public:
	/**
	* @fun  GetWindowHandle
	* @brief 获取当前窗口句柄
	* @date   2024.08.18
	* <AUTHOR>
	*/
    virtual QWidget* GetWindowHandle() = 0;
	/**
	* @fun  ShowWindow
	* @brief  显示当前窗口
	* @date   2024.08.18
	* <AUTHOR>
	*/
    virtual int ShowWindow() = 0;
	/**
	* @fun  SetTestData
	* @brief  设置传入数据
	* @param  image_groups 图像
	* @param  color_params_str 参数字符串
	* @date   2024.08.18
	* <AUTHOR>
	*/
    virtual int SetTestData(const cv::Mat& image_groups, 
		const std::string& color_params_str = "", bool is_origin_image = false) = 0;
	/**
	* @fun  SetColorChangedCallback
	* @brief  设置回调函数
	* @date   2024.08.18
	* <AUTHOR>
	*/
	virtual int SetColorChangedCallback(ColorParamsChangedFunc func) = 0;
	/**
	* @fun  GetPreProcessResult
	* @brief  获取处理结果
	* @date   2024.08.18
	* @note  临时将函数的返回值变为cv::Mat 类型，后续需要修改。 该返回值是图像增强的处理结果 2025.01.08 Xailor
	* <AUTHOR>
	*/
	virtual cv::Mat GetPreProcessResult(const cv::Mat& image_group, ColorParams& params,
			cv::Mat& output_img) = 0;
	/**
	* @fun    GetColorProcessImage
	* @brief  传入图像获取处理结果
	* @param  input_image 输入图像
	* @param  out_image   输出图像
	* @param  params      调色参数（输出）
	* @date   2024.10.23
	* <AUTHOR>
	*/
	virtual int GetColorProcessImage(const cv::Mat& input_image, cv::Mat& out_image, ColorParams& params) = 0;
	/**
	* @fun    GetColorProcessImage
	* @brief  传入图像和颜色参数获取处理结果
	* @param  input_image 输入图像
	* @param  params      调色参数（输入）
	* @param  out_image   输出图像

	* @date   2024.10.23
	* <AUTHOR>
	*/
	virtual int GetColorProcessImageWithParam(const cv::Mat& input_image,ColorParams& params, cv::Mat& out_image) = 0;
	/**
	* @fun    GetColorProcessParams
	* @brief  获取颜色处理的参数
	* @date   2024.11.1
	* <AUTHOR>
	*/
	virtual const ColorParams GetColorProcessParam() = 0;


};
#endif 
