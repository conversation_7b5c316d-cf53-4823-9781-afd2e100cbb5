#pragma once
/*****************************************************************
 * @file   paramupdatecenter.h
 * @brief  参数数据更新
 * @details
 * <AUTHOR>
 * @date 2024.11.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.6          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
//STD
//Custom
#include "controllerbase.h"
//Third
namespace jrsaoi
{
	struct ImplData;
}
namespace jrsaoi {
	using Function = std::function<int(const jrsdata::ViewParamBasePtr& param_)>;

	class InstanceParamUpdateCenter
	{
	public:
		InstanceParamUpdateCenter();
		~InstanceParamUpdateCenter();
		/**
		 * @fun EventHandler 
		 * @brief  事件分发器
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int EventHandler(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun SetNotifyCallBack 
		 * @brief 事件中心回调
		 * @param notify_call_back_
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		void SetNotifyCallBack(jrsaoi::Function notify_call_back_);
	private:
		/**
		 * @fun UpdateProjectParam 
		 * @brief 工程参数更新
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int UpdateProjectParam(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun AppendProject 
		 * @brief 追加工程，主要用于多工程
		 * @param param_ [IN] 工程参数
		 * @return  成功返回 AOI_OK，失败返回 错误码
		 * <AUTHOR>
		 * @date 2025.6.7
		 */
		int AppendProject(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun UpdateEntiretyImageParam 
		 * @brief 整板图像更新
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int UpdateEntiretyImageParam(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun UpdateSystemParam 
		 * @brief 系统参数更新
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int UpdateSystemParam(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun UpdateMachineParam 
		 * @brief 机台参数更新
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int UpdateMachineParam(const jrsdata::ViewParamBasePtr& param_);
		/**
		 * @fun UpdateSystemStateParam 
		 * @brief 系统状态参数更新
		 * @param param_
		 * @return 
		 * <AUTHOR>
		 * @date 2024.11.7
		 */
		int UpdateSystemStateParam(const jrsdata::ViewParamBasePtr& param_);
		
		/** 数据初始化 */
		void Init();
		void InitMember();
		void InitInvokeFun();
		bool IsValidParam(const jrsdata::ViewParamBasePtr& param_);
		bool InvokeFun(const jrsdata::ViewParamBasePtr& param_);
			
		ImplData* _impl_data;
		std::unordered_map<std::string, Function> _center_func_map;

	};
	using InstanceParamUpdateCenterPtr = std::shared_ptr<InstanceParamUpdateCenter>;
}
