﻿/*****************************************************************
 * @file   regularmultipleboards.h
 * @brief  规则多联板添加、删除
 * @details
 * <AUTHOR>
 * @date 2024.12.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.31          <td>V2.0              <td>YY<PERSON>hang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef _REGULAR_MULTIPLE_BOARDS_BASE_H__
#define _REGULAR_MULTIPLE_BOARDS_BASE_H__


#include "multipleboardsbase.h"
namespace jrsaoi
{
    class RegularMultipleBoards :public MultipleBoardsBase
    {
    public:
        int MultipleBoardsUpdate(jrsdata::MultiBoardEventParamPtr param_) override;

        RegularMultipleBoards();
        ~RegularMultipleBoards();

    private:
        /**
         * @fun MarkComponentAsTemplate
         * @brief 标记某个元件为模板
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        int MarkComponentAsTemplate(jrsdata::MultiBoardEventParam::RegularParam& param_);
        /**
         * @fun CalculateDistance
         * @brief 计算 原点与x方向 y方向 之间的距离
         * @param origin_image_
         * @param temp_mark_rect_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        cv::Point2f CalculateDistance(std::pair<cv::Rect2f, std::unordered_map<jrsdata::LightImageType, cv::Mat> > origin_image_, cv::Rect2f temp_mark_rect_);
        /**
         * @fun IdentifyRemoteSubboard
         * @brief   根据计算的偏移距离，生成临时多联板 绘制到temp_mark层
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        int IdentifyRemoteSubboard(jrsdata::MultiBoardEventParam::RegularParam& param_);
        /**
         * @fun ImageCapture
         * @brief  根据 rect 截取图片
         * @param rect_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        std::unordered_map<jrsdata::LightImageType, cv::Mat> ImageCapture(cv::Rect2f rect_);
        /**
         * @fun SubboardClone
         * @brief  克隆其他子板数据
         * @param subboard_
         * @param clone_subboard_centers_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        std::vector<jrsdata::SubBoard> SubboardClone(const std::optional<jrsdata::SubBoard>& subboard_,
            const std::unordered_map<std::string, cv::Vec3f>& clone_subboard_centers_);
        /**
         * @fun GenerateCloneSubboards
         * @brief  生成克隆子板信息
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.12.31
         */
        int GenerateCloneSubboards(jrsdata::MultiBoardEventParam::RegularParam& param_);
        /**
         * @fun CancelGenerateCloneSubboards
         * @brief  取消生成多联板
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.1.6
         */
        int CancelGenerateCloneSubboards(jrsdata::MultiBoardEventParam::RegularParam& param_);

        int ShowImage(std::string window_name_, const cv::RotatedRect& rect_);

        jrsdata::RegularParamPtr _regular_multiple_board_param_ptr;   /**< 规则多联板参数 */
    };
}
#endif //!_REGULAR_MULTIPLE_BOARDS_BASE_H__
