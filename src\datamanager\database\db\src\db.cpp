﻿//core
#include "coreapplication.h"
//custom
#include "db.h"
#include <mysqlimp.hpp>
#include <idatabase.h>
#include <connectpool.h>

jrsdatabase::DBHandel::DBHandel()
{
}

int jrsdatabase::DBHandel::Create(const jrsdatabase::DatabaseConnectParam& conn_param_)
{
    _latest_tm = std::chrono::system_clock::now();
    jrsdatabase::IDatabasePtr<jrsdatabase::MySqlImp> db_ = std::make_shared<jrsdatabase::IDatabase<jrsdatabase::MySqlImp>>();
    auto res = db_->CreateDatabase(conn_param_.db_ip, conn_param_.db_user_name, conn_param_.db_pwd, conn_param_.db_name);
    if (!res)
    {
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }
    res = InitDBConnectPool(conn_param_.max_connect, conn_param_.db_ip, conn_param_.db_user_name, conn_param_.db_pwd, conn_param_.db_name);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("初始化数据库连接池失败!");
        return res;
    }
    return jrscore::AOI_OK;
}

auto jrsdatabase::DBHandel::GetConnPtr()->std::shared_ptr<IDatabase<MySqlImp>>
{
    auto conn_ptr = jrsdatabase::ConnectionPool<jrsdatabase::IDatabase<jrsdatabase::MySqlImp>>::Instance().Get();
    if (conn_ptr)
    {
        return conn_ptr;
    }
    else
    {
        return nullptr;
    }
}

int jrsdatabase::DBHandel::BackDump([[maybe_unused]] const std::string& db_name_)
{
    return 0;
}

int jrsdatabase::DBHandel::InitDBConnectPool(const int& conn_max_, const std::string& db_host_, const std::string& db_user_name_, const std::string& db_pwd_, const std::string& db_name_)
{
    auto& pool = jrsdatabase::ConnectionPool<jrsdatabase::IDatabase<jrsdatabase::MySqlImp>>::Instance();
    auto res = pool.Init(conn_max_, db_host_.c_str(), db_user_name_.c_str(), db_pwd_.c_str(), db_name_.c_str(), 5, 3306);
    if (res != jrscore::AOI_OK)
    {
        return res;
    }
    return jrscore::AOI_OK;
}

auto jrsdatabase::DBHandel::GetLastCreateTime()
{
    return _latest_tm;
}