﻿#ifndef __PARAM_CONVERTOR_H__
#define __PARAM_CONVERTOR_H__

/*****************************************************************
 * @file   paramconvertor.h
 * @brief  1、将检测结果转换成数据库的整板结果进行数据保存。 2、进行数据统计 供界面用
 * @details
 * <AUTHOR>
 * @date 2024.11.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.19          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
#include "dataparam.h"
#include "viewparam.hpp"
#include "projectparam.hpp"
#include "fileoperation.h"
#include "settingparam.h"

 //Third
namespace jrsdatabase
{
    class DBManagers;
}
namespace jrsparam
{
    class ParameterProcess;
}

namespace jrsdata {
    using NameImageMap = std::unordered_map<std::string, cv::Mat>;         /**< 名称和类型 */
    using AlgorithmImageMap = std::unordered_map<std::string, NameImageMap>; /**< 算法归类 */
    using ComponentImageMap = std::unordered_map<bool, NameImageMap>;   /**< 元件图片 */
    using SubboardIDAndDevices = std::unordered_map<int, std::vector<jrsdata::DeviceResult>>;
    using EntiretyBoardImages = std::unordered_map<std::string, cv::Mat>;
    struct BoardDetectionAssets
    {
        jrsdata::EntiretyBoardResult entirety_board_result;/**< 整板结果*/
        std::optional<jrsdata::DetectStatisticsViewParam> detect_statistics_param; /*<检测统计参数 传给界面*/
        ComponentImageMap component_images;      /**< 元件图片 */
        AlgorithmImageMap algorithm_images;      /**< 算法归类的图片 */
        EntiretyBoardImages entirety_board_images;  /**< 整板图片 */
        jrsdata::NgStatistics ng_statistics;
        // 默认构造函数
        BoardDetectionAssets()
            : entirety_board_result(),
            detect_statistics_param(std::nullopt),
            component_images(),
            algorithm_images(),
            entirety_board_images(),
            ng_statistics()
        {
        }

        // 拷贝构造函数
        BoardDetectionAssets(const BoardDetectionAssets& other)
            : entirety_board_result(other.entirety_board_result),
            detect_statistics_param(other.detect_statistics_param),
            component_images(other.component_images),
            algorithm_images(other.algorithm_images),
            entirety_board_images(other.entirety_board_images)
        {
        }
    };
    class ParamConvertor
    {
    public:
        ParamConvertor();
        ~ParamConvertor();
        void SetTDataManagerPtr(const std::shared_ptr<jrsdatabase::DBManagers>& db_manager_ptr_);
        /**
         * @fun SetSettingParamPtr
         * @brief  设置设置指针
         * @param param_process_ptr_
         * <AUTHOR>
         * @date 2025.5.26
         */
        void SetSettingParamPtr(const std::shared_ptr<jrsparam::ParameterProcess>& param_process_ptr_);
        /**
         * @fun DetectResultToEntiretyBoardParam
         * @brief 检测结果 转换到整板结果
         * @param project_param_ptr_
         * @param detect_result_param_
         * @param setting_params_
         * @return
         * <AUTHOR>
         * @date 2024.11.17
         */
        jrsdata::BoardDetectionAssets DetectResultToEntiretyBoardParam(
            const jrsdata::ProjectParamPtr& project_param_ptr_,
            const jrsdata::DetectResultParamPtr& detect_result_param_,
            const jrsdata::AllSettingParamMap& setting_params_);
        /**
         * @fun ClearStatisticsData
         * @brief  清除板子统计数据
         * @return
         * <AUTHOR>
         * @date 2025.2.20
         */
        int ClearStatisticsData(const std::string& project_name_);
        /**
         * @fun ClearDevicesData
         * @brief  清除元件统计数据
         * @param project_name_
         * @return
         * @date 2025.3.10
         * <AUTHOR>
         */
        int ClearDevicesData(const std::string& project_name_);

    private:
        /**
         * @fun CreateBoardInfo
         * @brief 根据检测结果和工程信息填写主板表
         * @param detect_result_param_
         * @param project_param_ptr_
         * @return
         * <AUTHOR>
         * @date 2024.11.18
         */
        jrsdatabase::jrstable::TBoard CreateBoardInfo(const jrsdata::DetectResultParamPtr& detect_result_param_,
            const jrsdata::ProjectParamPtr& project_param_ptr_);
        /**
         * @fun CreateProjectInfo
         * @brief 根据工程参数填写工程表
         * @param project_param_ptr_
         * @return
         * <AUTHOR>
         * @date 2024.11.18
         */
        jrsdatabase::jrstable::TProject CreateProjectInfo(const jrsdata::ProjectParamPtr& project_param_ptr_);
        /**
         * @fun GroupDevicesBySubboard
         * @brief 根据检测结果工程参数获取整个元件的参数 及图片
         * @param detect_result_param_
         * @param project_param_ptr_
         * @param image_path_
         * @return
         * <AUTHOR>
         * @date 2024.11.18
         */
        std::tuple<SubboardIDAndDevices, AlgorithmImageMap, ComponentImageMap, jrsdata::EntiretyBoardImages>
            GroupDevicesBySubboard(
                const jrsdata::DetectResultParamPtr& detect_result_param_,
                const jrsdata::ProjectParamPtr& project_param_ptr_, const std::string& image_path_);
        /**
         * @fun CreateSubboardInfo
         * @brief 根据检测信息，工程参数 和元件参数 填写子板信息
         * @param project_param_ptr_
         * @param detect_result_param_
         * @param board_masked_subboards
         * @param subboard_devices
         * @return
         * <AUTHOR>
         * @date 2024.11.18
         */
        std::vector<jrsdata::SubboardResult> CreateSubboardInfo(const jrsdata::ProjectParamPtr& project_param_ptr_,
            const jrsdata::DetectResultParamPtr& detect_result_param_, int& board_masked_subboards,
            const std::unordered_map<int, std::vector<jrsdata::DeviceResult>>& subboard_devices);
        /**
         * @fun GenerateDeviceID
         * @brief  对每一块子板生成元件ID
         * @param device_result
         * <AUTHOR>
         * @date 2025.1.3
         */
        void GenerateDeviceID(std::vector<jrsdata::DeviceResult>& device_result);
        /**
         * @fun GenerateFilePath
         * @brief  生成保存图片和结果的文件路径
         * @param base_path_
         * @param sub_folder_
         * @param file_name_
         * @return
         * <AUTHOR>
         * @date 2024.11.18
         */
        std::string GenerateFilePath(const std::string& base_path_, const std::string& sub_folder_, const std::string& file_name_);
        /**
         * @fun AddDeviceImage
         * @brief 根据元件检测信息、元件图片保存路径、获取元件图片
         * @param device_
         * @param device_path
         * @param component_image_map_
         * @param pass_map_
         * @param ng_map_
         * <AUTHOR>
         * @date 2024.11.18
         */
        std::string AddDeviceImage(const jrsdata::DeviceResult& device_, const std::string& device_path,
            jrsdata::ComponentImageMap& component_image_map_, jrsdata::NameImageMap& pass_map_, jrsdata::NameImageMap& ng_map_);
        /**
         * @fun ProcessDetectWindows
         * @brief 获取检测算法图片
         * @param device_
         * @param base_path_
         * @param project_param_ptr_
         * @param algo_type_image_map_
         * <AUTHOR>
         * @date 2024.11.18
         */
        void ProcessDetectWindows(const jrsdata::DeviceResult& device_, const std::string& base_path_,
            const jrsdata::ProjectParamPtr& project_param_ptr_, jrsdata::AlgorithmImageMap& algo_type_image_map_);
        /**
        * @fun GetEntiretyBoardImages
        * @brief
        * @param detect_result_param_
        * @param path_
        * @return
        * <AUTHOR>
        * @date 2024.11.19
        */
        jrsdata::EntiretyBoardImages GetEntiretyBoardImages(const jrsdata::DetectResultParamPtr& detect_result_param_, const std::string& path_);
        /**
         * @fun GetSavePath
         * @brief
         * @param all_setting_param_map_
         * @return
         * <AUTHOR>
         * @date 2024.11.19
         */
        std::string GetSavePath(jrsdata::AllSettingParamMap all_setting_param_map_);
        /**
         * @fun GetDetectStatisticsParam
         * @brief   获取检测统计信息
         * @param entirety_board_result_
         * @return
         * <AUTHOR>
         * @date 2025.1.13
         */
        jrsdata::DetectStatisticsViewParam GetDetectStatisticsParam(const jrsdata::EntiretyBoardResult& entirety_board_result_, const jrsdata::DetectResultParamPtr& detect_result_param_);

        /**
         * @fun GetSubboardStatisticsParam
         * @brief  子板数据分析
         * @param subboard_
         * <AUTHOR>
         * @date 2025.1.20
         */
        void GetSubboardStatisticsParam(jrsdata::SubboardResult& subboard_);
        /**
         * @fun GetBoardStatisticsParam
         * @brief
         * @param board_
         * <AUTHOR>
         * @date 2025.1.20
         */
        void GetBoardStatisticsParam(jrsdata::EntiretyBoardResult& board_);

        /** <临时放在这里 TODO：需要移动到其他位置*/

        /**< 更新本地统计信息 */
        void UpdateLocalMachineStatisticsByDB(const std::string& machine_id_, const std::string& project_name_);
        /**< 将本地信息更新到数据库 */
        void UpdateDBMachineStatisticsByLocal(jrsdata::EntiretyBoardResult& entirety_result_);
        void UpdateDBMachineBoardDataByLocal(jrsdata::EntiretyBoardResult& entirety_result_);

        /**< 读取或者创建机台统计数据*/
        jrsdatabase::jrstable::TAOIMachineStatistics GetOrCreateMachineStatisicsData(const std::string& machine_id_, const std::string& project_name_);
        /**<读取机台参数 */
        std::optional<jrsdatabase::jrstable::TAOIMachine> GetMachineDataFromDatabase(const std::string& machine_id_);
        /**< 根据更称名称读取工单号*/
        int GetWorkOrderInfomationByProjectName(const std::string& project_name_, std::string& work_order_information);

        std::shared_ptr<jrsdatabase::DBManagers> _db_manager_ptr;
        std::shared_ptr<jrsparam::ParameterProcess> _param_process_ptr;
        std::optional<jrsdatabase::jrstable::TAOIMachineStatistics> _current_machine_statistics;
        jrsdata::NgStatistics _ng_statistics;/**< ng 数据统计，供数据库使用 */
    };

    using ParamConvertorPtr = std::shared_ptr<ParamConvertor>;
}
#endif
