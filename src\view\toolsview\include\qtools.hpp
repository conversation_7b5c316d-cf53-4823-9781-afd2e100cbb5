
/*****************************************************************
 * @file   qtools.hpp
 * @brief  ARP2.0 QT相关工具类
 * @details 主要用于QT相关的处理工具
 * <AUTHOR>
 * @date 2025.4.14
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.4.14          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef A6CC454B_6E3D_4EB2_8439_C328B2030910
#define A6CC454B_6E3D_4EB2_8439_C328B2030910
 //STD
#include <variant>
#include <string>
//QT
#include <QVariant>
//Custom
#include "pch.h"
#include "detectresultparam.hpp"
//Third
namespace qtools
{
    class QTools
    {
    public:

        //! Fun
         /**
         * @fun ConvertToQVariant
         * @brief 将JrsVariant转换成QVariant
         * @param input_data [IN] 输入的JrsVariant
         * @return 返回单个转换好的QVariant数据
         * <AUTHOR>
         * @date 2025.2.15
         */
        template <typename T>
        static std::vector<QVariant> ConvertToQVariant(T&& input_data)
        {
            auto jrs_data = input_data.ToVariantVector();
            return JrsVariantToQVariant(jrs_data);
        }
        // 判断类型是否是 std::map
        template <typename T>
        struct is_std_map : std::false_type {
        };

        template <typename K, typename V, typename... Args>
        struct is_std_map<std::map<K, V, Args...>> : std::true_type
        {
        };
        /**
        * @fun ConvertToQVariantVector
        * @brief 将JrsVariant容器转换成QVariantVector
        * @param input_data [IN] 输入的JrsVariant
        * @return 返回转换后的QVariant数据列表，用于在tabview中显示
        * <AUTHOR>
        * @date 2025.2.14
        */
        template <typename T>
        static std::vector<std::vector<QVariant>> ConvertToQVariantVector(T&& input_data)
        {
            std::vector<std::vector<QVariant>> output_lists;
            output_lists.reserve(input_data.size());
            int id = 1;
            for (auto&& item : std::forward<T>(input_data))
            {

                std::vector<QVariant> row;
                row.push_back(QVariant(id++));
                //! map类型
                if constexpr (is_std_map<std::decay_t<T>>::value)
                {
                    auto inner = ConvertToQVariant(item.second); // value 转换
                    row.insert(row.end(), inner.begin(), inner.end());
                }
                else
                {
                    //vector类型
                    auto inner = ConvertToQVariant(item); // 普通 vector
                    row.insert(row.end(), inner.begin(), inner.end());
                }
                output_lists.emplace_back(std::move(row));

            }


            return output_lists;

        }


        static inline std::vector<QVariant> JrsVariantToQVariant(const std::vector<jrsdata::JrsVariant>& jrs_variant)
        {


            std::vector<QVariant> m_temp;
            if (jrs_variant.empty())
            {
                return std::vector<QVariant>();
            }
            else
            {
                for (auto& value : jrs_variant)
                {
                    auto temp_data = std::visit([](const auto& value) -> QVariant
                        {
                            if constexpr (std::is_same_v<std::decay_t<decltype(value)>, std::string>)
                            {
                                return QVariant::fromValue(QString::fromStdString(value)); // 转换 std::string 为 QVariant
                            }
                            else if constexpr (std::is_same_v<std::decay_t<decltype(value)>, std::vector<int>>)
                            {
                                QStringList list;
                                for (int v : value)
                                {
                                    list << QString::number(v);
                                }
                                return list.join(", "); // 显示为字符串
                            }
                            else
                            {
                                return QVariant::fromValue(value); // 直接转换其他类型
                            }
                        }, value);
                    m_temp.push_back(temp_data);
                }
            }

            return m_temp;

        }

        /**
        * @brief 将窗口显示在指定控件的左下角或右下角
        * @param anchor_widget 指定锚点控件（将弹窗显示在它下方）
        * @param popup_widget 弹出窗口
        * @param right_align 如果为 true，则显示在右下角；否则左下角
        */
        static inline void ShowPopupAtWidget(QWidget* anchor_widget, QWidget* popup_widget, bool right_align = false)
        {
            if (!anchor_widget || !popup_widget) return;

            // 设置为浮动弹窗，只需设置一次
            if (!(popup_widget->windowFlags() & Qt::Popup)) {
                popup_widget->setWindowFlags(Qt::Popup);
            }

            popup_widget->adjustSize();

            QSize popup_size = popup_widget->size();
            QSize anchor_size = anchor_widget->size();

            // 基准点（锚点的全局位置）
            QPoint base_pos = anchor_widget->mapToGlobal(QPoint(0, anchor_size.height()));

            if (right_align) {
                base_pos.setX(base_pos.x() + anchor_size.width() - popup_size.width());
            }

            popup_widget->move(base_pos);
            popup_widget->show();
            popup_widget->raise();
            popup_widget->activateWindow();
        }
    private:


    private:
        QTools() = delete;
        QTools(const QTools&) = delete;
        QTools& operator=(const QTools&) = delete;
        QTools(QTools&&) = delete;
        QTools& operator=(QTools&&) = delete;
    };

}

#endif /* A6CC454B_6E3D_4EB2_8439_C328B2030910 */
