/*****************************************************************//**
 * @file   wigtdouthresholdhistm.h
 * @brief  双阈值控件交互
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef DOUTHRESHOLDHISTOGRAM_H
#define DOUTHRESHOLDHISTOGRAM_H

//PREBUILD
#include "pch.h"
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
//#include <QWidget>
#include "qcustomplot.h"
#include <opencv2/opencv.hpp>
#pragma warning(pop)

class WigtDouThresholdHistm : public QWidget
{
    Q_OBJECT
public:
    explicit WigtDouThresholdHistm(QWidget *parent = nullptr);
    
    void SetHistValues(const std::vector<float>& hist_values);	

signals:
    void ThresholdChanged(const int& left_Val, const int& right_val);
    void ThrDoubleClickedSignal();
    void UpdateClickState(const bool& state);

private:
    void SetClickState();
    void UpdateHistogram();

public slots:
	void HandleSingleClicked();
    void SetThresholdVals(const float& left_val, const float& right_val);

private:
	bool on_click = false;
    int  m_left_val  = 0;
    int  m_right_val = 255;

    QCustomPlot*   histogram = nullptr;
    QCPGraph*      selected_bars;
    QCPGraph*      no_selected_bars_left;
	QCPGraph*      no_selected_bars_right;

    int gray_min_ = 0;
    int gray_max_ = 255;

    std::vector<float> m_hist_values;

protected:
    virtual void resizeEvent(QResizeEvent *event) override;
	void mouseDoubleClickEvent(QMouseEvent *event) override;

};

#endif // DOUTHRESHOLDHISTOGRAM_H
