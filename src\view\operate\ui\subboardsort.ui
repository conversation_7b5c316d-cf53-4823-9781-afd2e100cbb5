<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SubboardSort</class>
 <widget class="QWidget" name="SubboardSort">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>172</width>
    <height>155</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0">
     <item>
      <widget class="QFrame" name="frame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <family>黑体</family>
            <pointsize>13</pointsize>
           </font>
          </property>
          <property name="text">
           <string>子板规则排序</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_4">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="2">
           <widget class="QToolButton" name="btn_left_bottom_to_right_top_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QToolButton" name="btn_right_bottom_to_left_top_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QToolButton" name="btn_left_top_to_right_bottom_vertical_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QToolButton" name="btn_right_top_to_left_bottom_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QToolButton" name="btn_left_top_to_right_bottom_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QToolButton" name="btn_left_top_to_right_bottom_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QToolButton" name="btn_left_top_to_right_bottom_vertical_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QToolButton" name="btn_right_top_to_left_bottom_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QToolButton" name="btn_left_bottom_to_right_top_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QToolButton" name="btn_right_bottom_to_left_top_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QToolButton" name="btn_right_top_to_left_bottom_vertical_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QToolButton" name="btn_left_bottom_to_right_top_vertical_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QToolButton" name="btn_right_bottom_to_left_top_vertical_s">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QToolButton" name="btn_right_top_to_left_bottom_vertical_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QToolButton" name="btn_left_bottom_to_right_top_vertical_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QToolButton" name="btn_right_bottom_to_left_top_vertical_z">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_3">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="1">
         <widget class="QPushButton" name="btn_confirm">
          <property name="text">
           <string>确认</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="0">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="3">
         <widget class="QPushButton" name="btn_cancel">
          <property name="text">
           <string>取消</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
