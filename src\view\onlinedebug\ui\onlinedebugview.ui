<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OnLineDebugView</class>
 <widget class="QWidget" name="OnLineDebugView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>512</width>
    <height>538</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>onlinedebugview</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,1,10,1,1,1">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_8">
       <property name="spacing">
        <number>6</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>不良列表</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_stop_online_debug">
           <property name="text">
            <string>停机调试</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>检出数：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_detect_num">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>误判数：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_false_positive_num">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>不良数：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_bad_num">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QWidget" name="widget_component_list" native="true">
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <layout class="QVBoxLayout" name="verticalLayout_component_show_list"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_search_component">
       <property name="cursor">
        <cursorShape>IBeamCursor</cursorShape>
       </property>
       <property name="inputMask">
        <string/>
       </property>
       <property name="echoMode">
        <enum>QLineEdit::Normal</enum>
       </property>
       <property name="placeholderText">
        <string>请输入要查找的元件名称...</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QPushButton" name="pushButton_search_component">
         <property name="text">
          <string>查询</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_up">
         <property name="text">
          <string>上一个</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_down">
         <property name="text">
          <string>下一个</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_7">
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QPushButton" name="pushButton_confirm">
           <property name="text">
            <string>确认</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_exit">
           <property name="text">
            <string>退出</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
