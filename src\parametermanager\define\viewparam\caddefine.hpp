/*****************************************************************//**
 * @file   caddefine.hpp
 * @brief  CAD 数据格式
 *
 * <AUTHOR>
 * @date   2024.8.12
 *********************************************************************/
#ifndef CADDEFINE_HPP
#define CADDEFINE_HPP
 //STL
#include <string>
//#include <vector>
//#include <bitset>
//CUSTOM
#include "mysqlimp.hpp"

namespace jrsdata
{
    /**
     * @struct CadStruct
     * @brief Cad导入数据定义
     * @date 2024.2.6
     * <AUTHOR>
     */
    struct CadStruct
    {
        std::string m_cad_name;
        float m_cad_x;                                          //! CAD的坐标X
        float m_cad_y;                                          //! CAD的坐标Y
        float m_cad_angle;                                      //! CAD的角度angle
        std::string m_cad_part_no;                              //! CAD的料号
        std::string m_cad_type;                                 //! CAD的Type
        std::string m_cad_basic_part_no;                        //! CAD的基础库料号
        int m_cad_sub_id;                                       //! CAD的子板ID
        CadStruct()
        {
            m_cad_name = "";
            m_cad_x = 0.0;
            m_cad_y = 0.0;
            m_cad_angle = 0.0;
            m_cad_part_no = "";
            m_cad_type = "";
            m_cad_basic_part_no = "";
            m_cad_sub_id = 1;
        }
        CadStruct(const std::string& cad_name_,
            float cad_x_,
            float cad_y_,
            float cad_angle_,
            const std::string& cad_part_no_,
            const std::string& cad_type_,
            const std::string& cad_basic_part_no_,
            int cad_sub_id_)
            : m_cad_name(cad_name_),
            m_cad_x(cad_x_),
            m_cad_y(cad_y_),
            m_cad_angle(cad_angle_),
            m_cad_part_no(cad_part_no_),
            m_cad_type(cad_type_),
            m_cad_basic_part_no(cad_basic_part_no_),
            m_cad_sub_id(cad_sub_id_) {
        }

        // 自定义赋值运算符重载
        CadStruct& operator=(const CadStruct& other)
        {
            if (this != &other)  // 防止自我赋值
            {
                m_cad_name = other.m_cad_name;
                m_cad_x = other.m_cad_x;
                m_cad_y = other.m_cad_y;
                m_cad_angle = other.m_cad_angle;
                m_cad_part_no = other.m_cad_part_no;
                m_cad_type = other.m_cad_type;
                m_cad_basic_part_no = other.m_cad_basic_part_no;
                m_cad_sub_id = other.m_cad_sub_id;
            }
            return *this;
        }
        JRSREFLECTION(CadStruct, m_cad_name, m_cad_x, m_cad_y, m_cad_angle, m_cad_part_no, m_cad_type, m_cad_basic_part_no, m_cad_sub_id)
    };
}

#endif