﻿// QT
#include <QGridLayout>
#include <QPushButton>
// Custom
#include "ui_render2dview.h"
#include "render2dview.h"
#include "render2deventparam.hpp"
//#include "projectoperator.h"
#include "layerconverter.hpp"
#include "renderer2d.h"
#include "graphicsobject.h"
#include "graphicsparam.hpp"
// 2024/12/16 wangzhengkai 更换3D显示控件
#include "jrs3dvisionwindow.h"


#include "stringoperation.h"
// Tool
#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "coordinatetransformationtool.h" // 坐标矫正工具
#pragma warning(pop)

#include <qshortcut.h>

namespace jrsaoi
{
    using namespace jrsdata;
    /**
     * @note
     * 大图默认设置到底层,将当前需要渲染的大图设置到当前渲染图片层
     * 临时性图片，放到临时层
     */
    constexpr const int image_layer_bottom = 0; ///< 图像层级-底层
    constexpr const int image_layer_top = 99;   ///< 图像层级-顶层
    constexpr const int image_layer_temp = 3;   ///< 图像层级-临时层
    constexpr const int image_layer_current_show = 1; ///< 图像层级-当前渲染图片层

    Render2dView::Render2dView(const std::string& name, QWidget* parent)
        : ViewBase(name, parent)
        , ui(new Ui::Render2dView())
        , jrs_3d_vision_window(nullptr)
        , render_manager(std::make_shared<Renderer2D>())
        , current_image_index(jrsdata::LightImageType::RGB)
    {
        ui->setupUi(this);
        Init();
    }

    Render2dView::~Render2dView()
    {
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
        /*if (vtk_3d_show)
          {
              vtk_3d_show = nullptr;
          }*/
    }
    int Render2dView::Init()
    {
        qRegisterMetaType<cv::Mat>("cv::Mat");
        qRegisterMetaType<std::shared_ptr<GraphicsAbstract>>("std::shared_ptr<GraphicsAbstract>");
        qRegisterMetaType<std::vector<std::shared_ptr<GraphicsAbstract>>>("std::vector<std::shared_ptr<GraphicsAbstract>>");
        qRegisterMetaType<std::vector<ImageShowParam>>("std::vector<ImageShowParam>");
        qRegisterMetaType<jrsdata::LightImageType>("jrsdata::LightImageType");
        qRegisterMetaType<LayerConfig>("LayerConfig");
        //qRegisterMetaType<RenderEditParam>("RenderEditParam");
        qRegisterMetaType<GraphicsID>("GraphicsID");

        InitMemeber();
        InitView();
        InitCallback();
        return jrscore::AOI_OK;
    }

    int Render2dView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            PushErrorToStack(jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER, "Render view 更新界面时输入参数为空！");
            return jrscore::RenderError::E_AOI_REMNDER_POINTER_TRANFER_FAILURE;
        }
        return jrscore::AOI_OK;
    }

    int Render2dView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }

    int Render2dView::GetCurrentVisionMode()
    {
        return render_manager->GetState();
    }

    int Render2dView::GetLayerGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, std::string layer)
    {
        return render_manager->GetLayerGraphics(ghs, layer);
    }

    std::shared_ptr<GraphicsManager> Render2dView::GetGraphicsManager() const
    {
        return render_manager->GetGraphicsManager();
    }

    void Render2dView::SlotRenderStateChange(int state)
    {
        render_manager->SetState(state);
    }

    void Render2dView::SlotRenderCreateGraphicsModeChange(int mode)
    {
        render_manager->SetCreateGraphicsMode(mode);
    }

    void Render2dView::SlotAddImage(const cv::Mat& image, int img_index, int x, int y, int z, float angle, bool is_draw_center)
    {
        (void)img_index;
        unsigned int id;
        render_manager->CreateTexture(id, image, x, y, z, angle, is_draw_center);

        if (image_layer_bottom == z)
        {
            // image_index_map.emplace(img_index, id);
        }
    }

    void Render2dView::SlotShowImageChange(const uint8_t& set_key_, int key_)
    {
        render_manager->ShowImage(set_key_, key_);
        //auto img_type_index = static_cast<jrsdata::LightImageType>(img_index);
        ///*       if (current_image_index == img_type_index) // By :实时切换图片无法进行下一步
        //           return;*/

        //auto texture_id_it = image_index_map.find(img_type_index);
        //if (texture_id_it == image_index_map.end())
        //{
        //    //Log_ERROR("没有找到对应的纹理ID！");
        //    return;
        //}

        //auto id = texture_id_it->second;
        ////render_manager->SetTextureZ(id, image_layer_current_show);
        //render_manager->ShowImage(id);
        //current_image_index = img_type_index;
    }

    void Render2dView::SlotShowImageChangeWithStr(const uint8_t& set_key_, const std::string& img_type_str)
    {
        auto it = image_type_map.find(img_type_str);
        if (it == image_type_map.end())
        {
            Log_ERROR("传入的切换图片类型有误！");
            return;
        }

        SlotShowImageChange(set_key_, static_cast<int>(it->second));
    }

    void Render2dView::SlotClearImage(const int& set_key_, int key_)
    {
        render_manager->ClearImage(set_key_, key_);
        //   render_manager->ClearTexture(z);
    }

    void Render2dView::SlotShowImages(const std::vector<ImageShowParam>& params)
    {
        unsigned int id;
        for (auto& p : params)
        {
            render_manager->CreateTexture(id, p.image, p.x, p.y, p.z, p.angle, true);
        }
    }

    void Render2dView::SlotDrawAngleChange(int angle)
    {
        render_manager->SetManualDefaultDrawAngle(angle);
    }

    void Render2dView::SlotRenderCanvasSizeChange(int width, int height)
    {
        render_manager->SetCanvasSize(width, height);
    }

    void Render2dView::SlotCurrentLayerChange(const std::string& layer)
    {
        render_manager->SetCurrentLayer(layer);
    }

    void Render2dView::SlotCameraScaleModeChange(int mode)
    {
        render_manager->SetZoomState(mode);
    }

    void Render2dView::SlotCameraResetModeChange(int mode)
    {
        render_manager->ResetCamera(mode);
    }

    void Render2dView::SlotResetCamera()
    {
        render_manager->ResetCamera(0);

    }
    void Render2dView::SlotMoveCamera(int direction)
    {
        render_manager->MoveCamera(direction);
    }

    void Render2dView::SlotMoveCameraToGraphics(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        render_manager->MoveCameraTo(gh);
    }

    void Render2dView::SlotMoveCameraToGraphicsWithID(const GraphicsID& id)
    {
        std::shared_ptr<GraphicsAbstract> gh;
        render_manager->GetGraphics(gh, GraphicsID(id));
        if (!gh)
        {
            return;
        }
        render_manager->MoveCameraTo(gh);
    }

    void Render2dView::SlotMoveCameraToSelectedGraphics()
    {
        std::shared_ptr<GraphicsAbstract> gh;
        auto res = render_manager->GetCurrentSelectedGraphicsSingle(gh);
        if (!gh)
        {
            Log_ERROR("没有选中任何图形！无法移动相机！");
            return;
        }
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR("移动相机失败！");
            return;
        }
        render_manager->MoveCameraTo(gh);
    }

    void Render2dView::SlotThumbnailShow()
    {
        render_manager->SetThumbnailShow();
    }
    void Render2dView::SlotAddGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback)
    {
        render_manager->AddGraphics(ghs, layer, invoke_callback);
    }

    void Render2dView::SlotAddGraphicsSingle(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer, bool invoke_callback)
    {
        render_manager->AddGraphics(gh, layer, invoke_callback);
    }

    void Render2dView::SlotGraphicsSelect(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state)
    {
        render_manager->SetGraphicsSelected(ghs, state);
    }

    void Render2dView::SlotGraphicsSelectSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback)
    {
        render_manager->SetCurrentLayer(gh->settings.GetLayer());
        render_manager->SetGraphicsSelectedSingle(gh, invoke_callback);
    }

    void Render2dView::SlotGraphicsSelectSingleWithID(const GraphicsID& id, bool invoke_callback)
    {
        std::shared_ptr<GraphicsAbstract> gh;
        render_manager->GetGraphics(gh, id);
        render_manager->SetGraphicsSelectedSingle(gh, invoke_callback);
    }

    void Render2dView::SlotGraphicsAttributeEditSingleSelected(double val, int type)
    {
        std::shared_ptr<GraphicsAbstract> gh;
        auto res = render_manager->GetCurrentSelectedGraphicsSingle(gh);
        if (!gh)
        {
            return;
            // return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        if (res != jrscore::AOI_OK)
        {
            return;
            // return res;
        }
        switch (GraphicAttributeEditType(type))
        {
        case GraphicAttributeEditType::edit_x:
            gh->SetX(val);
            break;
        case GraphicAttributeEditType::edit_y:
            gh->SetY(val);
            break;
        case GraphicAttributeEditType::edit_w:
            gh->SetW(val);
            break;
        case GraphicAttributeEditType::edit_h:
            gh->SetH(val);
            break;
        case GraphicAttributeEditType::edit_a:
            gh->SetA(val);
            break;
        default:
            return;
            // return jrscore::ViewError::E_AOI_VIEW_UNDEFINE_ENUM;
        }
        render_manager->Update();
        emit SignalGraphicsUpdated({ gh });
        return;
    }

    void Render2dView::SlotGraphicsAttributeEditByGraphicsPtr(const std::shared_ptr<GraphicsAbstract>& gh_ptr, double val, int type)
    {
        if (!gh_ptr)
        {
            return;
        }
        switch (GraphicAttributeEditType(type))
        {
        case GraphicAttributeEditType::edit_x:
            gh_ptr->SetX(val);
            break;
        case GraphicAttributeEditType::edit_y:
            gh_ptr->SetY(val);
            break;
        case GraphicAttributeEditType::edit_w:
            gh_ptr->SetW(val);
            break;
        case GraphicAttributeEditType::edit_h:
            gh_ptr->SetH(val);
            break;
        case GraphicAttributeEditType::edit_a:
            gh_ptr->SetA(val);
            break;
        default:
            return;
        }
        render_manager->Update();
        emit SignalGraphicsUpdated({ gh_ptr });
        return;

    }

    void Render2dView::SlotAddGraphicsLayerConfig(const std::string& layer, std::shared_ptr<LayerConfig> config)
    {
        render_manager->AddGraphicsLayerConfig(layer, config);
    }

    void Render2dView::SlotShowGraphicsLayer(const std::string& layer)
    {
        Log_INFO(__FUNCTION__, " " + layer);
        std::weak_ptr<LayerConfig> result;
        render_manager->GetLayerConfig(result, layer);
        if (result.expired())
        {
            return;
        }
        result.lock()->_display_style.is_render = !result.lock()->_display_style.is_render;
        render_manager->AddGraphicsLayerConfig(layer, result.lock());
    }

    void Render2dView::SlotClearLayerGraphics(const std::string& layer, bool invoke_callback_)
    {
        render_manager->DeleteGraphicsWithLayer(layer, invoke_callback_);
    }

    void Render2dView::SlotClearGraphics(bool invoke_callback)
    {
        render_manager->ClearGraphics(invoke_callback);
    }

    void Render2dView::SlotClearGraphicsExceptLayer(const std::string& except_layer, bool invoke_callback)
    {
        render_manager->ClearGraphics(except_layer, invoke_callback);
    }

    void Render2dView::SlotClearPadGroup()
    {
        render_manager->ClearPadGroups();
    }

    void Render2dView::SlotGetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids)
    {
        render_manager->GetGraphics(ghs, ids);
    }

    void Render2dView::SlotGetGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id)
    {
        render_manager->GetGraphics(gh, id);
    }

    void Render2dView::SlotGetCurrentSelectedGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer)
    {
        render_manager->GetCurrentSelectedGraphics(ghs, layer);
    }

    void Render2dView::SlotGetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer)
    {
        render_manager->GetCurrentSelectedGraphicsSingle(gh, layer);
    }

    void Render2dView::SlotAddGraphicsShapes(const GraphicsShape& graphics_shape_)
    {
        render_manager->AddGraphicsShapes(graphics_shape_);
    }

    void Render2dView::SlotClearGraphicsShapes()
    {
        render_manager->ClearGraphicsShapes();
    }

    void Render2dView::SlotCreateGraphics(std::shared_ptr<GraphicsAbstract>& gh, int graphics_type,
        const std::string& layer, const std::string& group_name_, const  std::shared_ptr<GraphicsAbstract>& father_graphics_)
    {
        gh = render_manager->CreateGraphicsTool(graphics_type, layer, group_name_, father_graphics_);

    }


    void Render2dView::SlotRevoke()
    {
        render_manager->Undo();
    }

    void Render2dView::SlotRecover()
    {
        render_manager->Redo();
    }

    void Render2dView::SlotShowCenterCrossLine(bool state)
    {
        render_manager->SetShowCenterCrossLine(state);
    }

    void Render2dView::SlotShow3DView(const cv::Mat& model_data, const double& rx, const double& ry, const std::vector<cv::Mat>& texture_datas)
    {
        if (model_data.empty() || texture_datas.empty())
        {
            Log_ERROR("输入的3D图像或2D纹理图为空！");
            JRSMessageBox_WARN("3D显示", "输入的3D图像或2D纹理图为空！", jrscore::MessageButton::Ok);
            return;

        }
        if (!jrs_3d_vision_window)
        {
            Log_ERROR("3D显示控件异常！");
            JRSMessageBox_WARN("3D显示", "3D显示控件异常！", jrscore::MessageButton::Ok);

            return;
        }
        jrs_3d_vision_window->AddImageModel(model_data, rx, ry, texture_datas);
        jrs_3d_vision_window->ShowWindow();
    }

    void Render2dView::SlotCreateTexture(int key_, const cv::Mat& img_, int create_layer, int img_index, bool resize_canvas, int current_show_image_key_)
    {
        if (img_.empty())
        {
            Log_ERROR("创建纹理图时，传入的图片为空！");
            return;
        }

        if (resize_canvas)
        {
            render_manager->SetCanvasSize(img_.cols, img_.rows);
        }

        auto state = render_manager->CreateImage(key_, img_, 0, 0, create_layer, 0, false, current_show_image_key_);
        //auto state = render_manager->CreateTexture(id, img_, 0, 0, create_layer, 0, false);
        if (state != 0)
            return;

        /*
        * @brief 如果在底层创建的纹理，需要将id信息保存下来，为了快速切换大图不同颜色
        by zhangyuyu 2024.09.29 */
        if (image_layer_bottom == create_layer)
        {
            image_index_map.emplace(static_cast<jrsdata::LightImageType>(img_index), key_);
        }

        //创建完成图像纹理后，切换到默认图像上
        //SlotShowImageChange(static_cast<int>(jrsdata::LightImageType::RGB));
    }

    void Render2dView::SlotCreateImages(const GraphicsImage& graphics_img_)
    {
        if (graphics_img_.key_and_imgs.empty())
        {
            Log_ERROR("创建图像时，传入的图像为空！");
            return;
        }

        if (graphics_img_.is_resize_canvas)
        {
            auto key_img_pair = graphics_img_.key_and_imgs.begin();
            render_manager->SetCanvasSize(key_img_pair->second.cols, key_img_pair->second.rows);

        }

        if (graphics_img_.is_move_camera)
        {
            //相机移动到图像中心
            if (graphics_img_.is_draw_center)
            {
                render_manager->MoveCameraTo(graphics_img_.center_point.x, graphics_img_.center_point.y);
            }
            else
            {
                auto img_size = graphics_img_.key_and_imgs.begin()->second.size();
                render_manager->MoveCameraTo(graphics_img_.center_point.x + img_size.width / 2,
                    graphics_img_.center_point.y + img_size.height / 2);  //移动到图片中心
            }
        }



        render_manager->CreateImages(graphics_img_);

    }



    void Render2dView::InitMemeber()
    {
        image_type_map =
        {
            {"原图",jrsdata::LightImageType::RGB },
            {"真彩图",jrsdata::LightImageType::WHITE },
            {"低角度光图",jrsdata::LightImageType::LOWWHITE },
            {"高度图",jrsdata::LightImageType::HEIGHT }
        };
    }

    void Render2dView::InitView()
    {
        // 2D渲染界面布局
        auto render_layout = this->layout();
        auto render_view = render_manager->GetWidget();
        render_view->setContentsMargins(0, 0, 0, 0);
        render_layout->setContentsMargins(0, 0, 0, 0);
        render_layout->addWidget(render_view);

        // 3D 初始化 
        /*占用初始化17.6%耗时 注释方便离线调试 jerx*/
        jrs_3d_vision_window = jrs3dvision::Jrs3dVisionWindow::CreateJrs3dVisionWindow();
        if (jrs_3d_vision_window)
        {
            jrs_3d_vision_window->Resize(480, 480);
        }
        int display_thread_width = 1;
        int focus_thread_width = 1;
        //颜色调整
        RenderConfig display_color(139, 139, 10, 200, display_thread_width, true);
        RenderConfig focus_color(255, 255, 0, 255, focus_thread_width);



        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::temp_mark),
            std::make_shared<LayerConfig>(
                RenderConfig{ 0, 139, 139, 255,display_thread_width, true },
                RenderConfig{ 0, 238, 238,255, focus_thread_width, true }));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::board),
            std::make_shared<LayerConfig>(RenderConfig{ 0, 139, 139, 255,display_thread_width, true },
                RenderConfig{ 0, 238, 238,255, focus_thread_width, true }
            ));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::subboard),
            std::make_shared<LayerConfig>(display_color, focus_color, Selectable::Boundary  /**<选择边界*/));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::barcode),
            std::make_shared<LayerConfig>(display_color, focus_color));
        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::subbarcode),
            std::make_shared<LayerConfig>(display_color, focus_color));
        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::mark),
            std::make_shared<LayerConfig>(display_color, focus_color));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::submark),
            std::make_shared<LayerConfig>(display_color, focus_color));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::badmark),
            std::make_shared<LayerConfig>(display_color, focus_color));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::pad),
            std::make_shared<LayerConfig>(display_color, focus_color));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::component),
            std::make_shared<LayerConfig>(display_color, focus_color));

        RenderConfig detect_window_display_color(0, 150, 0, 255, display_thread_width, true);
        RenderConfig detect_window_focus_color(0, 255, 0, 255, focus_thread_width);
        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::region),
            std::make_shared<LayerConfig>(detect_window_display_color, detect_window_focus_color));
        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::subregion),
            std::make_shared<LayerConfig>(detect_window_display_color, detect_window_focus_color));

        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::detect_region),
            std::make_shared<LayerConfig>(RenderConfig{ 0, 0, 255, 150,display_thread_width, true },
                RenderConfig{ 0, 0, 255, 150, focus_thread_width, true }, Selectable::None
            ));

        /** <虚线待实线 */
        render_manager->AddGraphicsLayerConfig(LayerConverter::ToString(Layer::search_region),
            std::make_shared<LayerConfig>(RenderConfig{ 255, 255, 255,200,display_thread_width, true },
                RenderConfig{ 255, 255, 255,150, focus_thread_width, true }, Selectable::None
            ));

        render_manager->SetCurrentLayer(LayerConverter::ToString(Layer::component));
    }

    void Render2dView::InitCallback()
    {
        render_manager->SetCallbackGraphicsselected(std::bind(&Render2dView::GraphicsSelectedCallback, this, std::placeholders::_1));
        render_manager->SetCallbackGraphicsupdate(std::bind(&Render2dView::GraphicsUpdateCallback, this, std::placeholders::_1, std::placeholders::_2));
        render_manager->SetCallbackGraphicscreate(std::bind(&Render2dView::GraphicsCreateCallback, this, std::placeholders::_1));
        render_manager->SetCallbackGraphicsdelete(std::bind(&Render2dView::GraphicsDeleteCallback, this, std::placeholders::_1));
        render_manager->SetCallbackRegionselected(std::bind(&Render2dView::RegionSelectedCallback, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
        // 临时放在这里
        auto sc = new QShortcut(QKeySequence(Qt::ALT + Qt::Key_Z), this);
        QObject::connect(sc, &QShortcut::activated, this, [=]
            {
                auto layer = render_manager->GetCurrentLayer();
                switch (LayerConverter::FromString(layer))
                {
                case Layer::component:
                {
                    SlotCurrentLayerChange(LayerConverter::ToString(Layer::region));
                }
                break;
                case Layer::region:
                {
                    SlotCurrentLayerChange(LayerConverter::ToString(Layer::component));
                }
                break;
                default:
                {
                    SlotCurrentLayerChange(LayerConverter::ToString(Layer::component));
                }
                break;
                }
            });
    }

    void Render2dView::GraphicsCreateCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        emit SignalGraphicsCreated(ghs);
    }

    void Render2dView::GraphicsUpdateCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool is_update_graphics_)
    {
        emit SignalGraphicsUpdated(ghs, is_update_graphics_);
    }

    void Render2dView::GraphicsDeleteCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        emit SignalGraphicsDelete(ghs);
    }

    void Render2dView::GraphicsSelectedCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        emit SignalGraphicsSelected(ghs);
    }

    void Render2dView::RegionSelectedCallback(float x, float y, float w, float h)
    {
        float center_x = x + w / 2;
        float center_y = y + h / 2;
        emit SignalRegionSelected(center_x, center_y, w, h);
    }
}
