#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "imagepreprocessalgo.h"

#pragma warning(pop)

int ImageProcessor::SetProcessImage(const cv::Mat& image_group_)
{
	if (image_group_.empty()) return -1;
    image_ = image_group_;   
    /*image_map_[0] = image_group_.true_image;
	image_map_[1] = image_group_.rgb_image;
    image_map_[2] = image_group_.low_light_image;
    image_map_[3] = image_group_.height_light_image;
    height_image_ = image_group_.height_mat;*/
	return 0;
}

int ImageProcessor::SetProcessParams(const PreProcessParams& params)
{
    contrast_value_ = params.contrast_value;
    brightness_value_ = params.brightness_value;
    hue_value_ = params.hue_value;
    saturation_value_ = params.saturation_value;
    gamma_value_ = params.gamma_value;
    is_reverse_ = params.is_reverse;
    id_ = params.image_id;
    return 0;
}

int ImageProcessor::GetProcessImage(cv::Mat& precess_image)
{
    cv::Mat cur_process_image;
	Mats3 split_images;

    precess_image = image_.clone();

	//if (std::find(id_group_all.begin(), id_group_all.end(), id_) != id_group_all.end()) 
    {
        if (id_ >= 1 && id_ <= 3)
        {
            split_images = GetRgbChannel(precess_image);
            precess_image = split_images[id_ - 1];

        }
        else if (id_ >= 4 && id_ <= 6)
        {
            split_images = GetHsvChannel(precess_image);
            precess_image = split_images[id_ - 4];                       
        }
    } 
	if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (contrast_value_ != 1.0) AdjustContrast(contrast_value_, precess_image);
	if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (brightness_value_ != 1.0) AdjustBrightness(brightness_value_, precess_image);
    if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (hue_value_ != 0) AdjustHue(hue_value_, precess_image);
    if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (saturation_value_ != 1.0) AdjustSaturation(saturation_value_, precess_image);
    if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (gamma_value_ != 1.0) AdjustGamma(gamma_value_, precess_image);
    if (precess_image.channels() != 3)
    {
        cv::cvtColor(precess_image,precess_image,cv::COLOR_GRAY2BGR);
    }
    if (is_reverse_) precess_image = ~precess_image;
    //cv::imwrite(R"(E:\data\NTC\res.png)", precess_image);
    return 0;
}

bool ImageProcessor::GetEnhanceImageStatus() const
{
    return enhance_image;
}

Mats3 ImageProcessor::GetRgbChannel(const Mat& rgb_image)
{
    Mats3 rbg_split_images;
    if (rgb_image.channels() != 3)
    {
        rbg_split_images = {rgb_image ,rgb_image ,rgb_image };
    }
    else
    {
        cv::split(rgb_image, rbg_split_images);
    }
    return rbg_split_images;
}

Mats3 ImageProcessor::GetHsvChannel(const Mat& rgb_image)
{
	Mats3 hsv_split_images;
    if (rgb_image.channels() != 3)
    {
        hsv_split_images = {rgb_image ,rgb_image ,rgb_image };
    }
    else
    {
        Mat hsv_image;
        cv::cvtColor(rgb_image, hsv_image, cv::COLOR_BGR2HSV);
        cv::split(hsv_image, hsv_split_images);
    }
    return hsv_split_images;
}

ImageProcessor::ImageProcessor()
{
}

ImageProcessor::~ImageProcessor()
{
}

void ImageProcessor::AdjustContrast(double value, Mat& image)
{
  enhance_image = true;
  image.convertTo(image, -1, value, 0);
}

void ImageProcessor::AdjustBrightness(double value, Mat& image)
{
  enhance_image = true;
  auto value_255 = MapDoubleToByteRange(value);
  image.convertTo(image, -1, 1, value_255);
}

void ImageProcessor::AdjustHue(double value, Mat& image)
{
  enhance_image = true;
  cv::Mat hsv;
  cv::cvtColor(image, hsv, cv::COLOR_BGR2HSV);

  for (int i = 0; i < hsv.rows; ++i)
  {
    for (int j = 0; j < hsv.cols; ++j)
    {
      hsv.at<cv::Vec3b>(i, j)[0] = 
          cv::saturate_cast<uchar>(hsv.at<cv::Vec3b>(i, j)[0] + value);
    }
  }

  cv::cvtColor(hsv, image, cv::COLOR_HSV2BGR);
}

void ImageProcessor::AdjustSaturation(double value, Mat& image)
{
  enhance_image = true;
  cv::Mat hsv;
  cv::cvtColor(image, hsv, cv::COLOR_BGR2HSV);
  for (int i = 0; i < hsv.rows; ++i)
  {
    for (int j = 0; j < hsv.cols; ++j)
    {
      hsv.at<cv::Vec3b>(i, j)[1] = 
          cv::saturate_cast<uchar>(hsv.at<cv::Vec3b>(i, j)[1] * value);
    }
  }
  cv::cvtColor(hsv, image, cv::COLOR_HSV2BGR);
}

void ImageProcessor::AdjustGamma(double value, Mat& image)
{
  enhance_image = true;
  cv::Mat lut(1, 256, CV_8UC1);
  uchar* p = lut.ptr();
  for (int i = 0; i < 256; ++i) 
  {
    p[i] = cv::saturate_cast<uchar>(std::pow(i / 255.0, value) * 255.0);
  }
  cv::LUT(image, lut, image);
}

int ImageProcessor::MapDoubleToByteRange(double value)
{
    if (value <= 1.0) 
    {
        return int(-255 + (value - 0.0) * (0.0 - (-255.0)) / (1.0 - 0.0));
    } 
    else 
    {
        return int(0 + (value - 1.0) * (255.0 - 0.0) / (2.0 - 1.0));
    }
}
