﻿#include "controlpointabstract.h"
#include "graphicsalgorithm.h"
#include "painter.h"


void ControlPointAbstract::SetAttributes(const ControlAttributes& attributes_)
{
    this->attributes = attributes_;
}

void ControlPointAbstract::SetDraw(const ControlPointDraw& cpd_)
{
    this->cpd = cpd_;
}

void ControlPointAbstract::SetPos(const float& x, const float& y)
{
    cpd.cx = x;
    cpd.cy = y;
}

void ControlPointAbstract::Draw(Renderer* r, Painter* p, const LayerConfig* config) const
{
    if (!r || !p || !config)
        return;
    if (!cpd.is_render)
        return;
    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);
    if (!cpd.contours.empty())
    {
        p->DrawLines(cpd.contours, c, true, -1);
    }
    else
    {
        p->DrawPoints({ Vec2(cpd.cx, cpd.cy) }, c, -1);
    }
}


double ControlPointAbstract::TryResponse(const float& x, const float& y, const float& min_dis) const
{
    if (cpd.contours.empty())
    {
        auto dis = abs(cpd.cx - x) + abs(cpd.cy - y);
        if (dis > 0 && dis <= min_dis)
        {
            return dis;
        }
    }
    else
    {
        float dis = static_cast<float>(PointDisToPolygon(Vec2(x, y), cpd.contours));
        if (dis > 0)
        {
            return 1;
            // return dis;
        }
    }
    // 表示未响应
    return -1.0;
}