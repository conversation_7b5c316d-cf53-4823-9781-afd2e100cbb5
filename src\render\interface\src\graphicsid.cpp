﻿#include "graphicsid.h"

#pragma warning(push, 1)
#pragma warning(disable : 4244)
#include "uuid_v4.h"
#pragma warning(pop)

UUIDv4::UUIDGenerator<std::mt19937_64> uuidGenerator;

struct GraphicsID::GraphicsIDTempData
{
    std::string id;
};

GraphicsID::GraphicsID(const std::string& id_)
    : data(new GraphicsIDTempData())
{
    SetString(id_);
}

GraphicsID::GraphicsID(const int& id_)
    : data(new GraphicsIDTempData())
{
    SetInt(id_);
}

GraphicsID::GraphicsID()
    : data(new GraphicsIDTempData())
{
    Create();
}

GraphicsID::GraphicsID(const GraphicsID& other)
    : data(new GraphicsIDTempData())
{
    data->id = other.data->id;
}

GraphicsID::GraphicsID(GraphicsID&& other) noexcept
    :data(other.data)
{
    other.data = nullptr;
}

GraphicsID& GraphicsID::operator=(const GraphicsID& other)
{
    if (this != &other) // 防止自赋值
    {
        if (data != nullptr)
        {
            delete data;
        }
        data = new GraphicsIDTempData();
        data->id = other.data->id;
    }
    return *this;
}

GraphicsID& GraphicsID::operator=(GraphicsID&& other) noexcept
{
    if (this != &other) // 防止自赋值
    {
        delete data;
        data = other.data;
        other.data = nullptr;
    }
    return *this;
}

GraphicsID::~GraphicsID()
{
    if (data != nullptr)
    {
        delete data;
        data = nullptr;
    }
}

void GraphicsID::Create()
{
    data->id = uuidGenerator.getUUID().str();
}

void GraphicsID::SetString(const std::string& name_)
{
    data->id = name_;
}

void GraphicsID::SetInt(const int& id_)
{
    data->id = std::to_string(id_);
}

std::string GraphicsID::GetString() const
{
    return data->id;
}

size_t GraphicsID::Hash() const
{
    return std::hash<std::string>()(data->id);
}

bool GraphicsID::IsEmpty() const
{
    return data->id.empty();
}

bool GraphicsID::operator==(const GraphicsID& other) const
{
    return other.data->id == this->data->id;
}

GraphicsIDSet::GraphicsIDSet()
    : std::vector<GraphicsID>()
{
}

void GraphicsIDSet::Insert(const GraphicsID& value)
{
    if (!Contains(value))
    {
        this->push_back(value);
    }
}

void GraphicsIDSet::Insert(const GraphicsIDSet& values)
{
    for (const auto& value : values)
    {
        Insert(value);
    }
}

void GraphicsIDSet::Insert(const std::vector<GraphicsID>& values)
{
    for (const auto& value : values)
    {
        Insert(value);
    }
}

void GraphicsIDSet::Erase(const GraphicsID& value)
{
    auto it = std::find(this->begin(), this->end(), value);
    if (it != this->end())
    {
        this->erase(it);
    }
}

bool GraphicsIDSet::Contains(const GraphicsID& value)
{
    return std::find(this->begin(), this->end(), value) != this->end();
}

void GraphicsIDSet::Clear()
{
    GraphicsIDSet().swap(*this);
}

size_t GraphicsIDSet::Size() const
{
    return this->size();
}
