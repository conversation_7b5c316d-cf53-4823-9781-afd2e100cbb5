/*****************************************************************//**
 * @file   algofactory.cpp
 * @brief  算子创建工厂
 * @details 主要提供算子插件的加载管理功能，提供算子对象实例导入
 * <AUTHOR>
 * @date 2024.5.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.5.31         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSALGOFACTORY_H__
#define __JRSALGOFACTORY_H__

//STD
#include <iostream>
#include <vector>
//Custom
#include "pluginloader.hpp"
#include "coreapplication.h"
#pragma warning(push, 0)
#include "judgeparam.h"
#include "operatorviewbase.h"
#include "operatordrivebase.h"
#pragma warning(pop)
namespace jrsalgo
{
    class AlgoFactory
    {
        public:
            AlgoFactory();
            ~AlgoFactory();
            AlgoFactory(const AlgoFactory&) = delete;
            AlgoFactory& operator=(const AlgoFactory&) = delete;
            AlgoFactory(AlgoFactory&&) = delete;
            AlgoFactory& operator=(AlgoFactory&&) = delete;

            /**
             * @fun LoadAlgoDrives 
             * @brief 加载路径下的算子驱动实例
             * @param alog_drive_path_list_ [IN]  算法驱动的路径列表
             * @return  成功返回AOI_OK,否则返回错误码
             * <AUTHOR>
             * @date 2024.10.10
             */
            int LoadAlgoDrives(const std::list<std::string>& alog_drive_path_list_ );

            /**
             * @fun LoadAlgoViews 
             * @brief 加载路径下的算子界面实例
             * @param alog_view_path_list_ [IN] 算法界面的路径列表
             * @return 成功返回AOI_OK,否则返回错误码
             * <AUTHOR>
             * @date 2024.10.10
             */
            int LoadAlgoViews(const std::list<std::string>& alog_view_path_list_ );
         
            /**
             * @fun GetSpecificAlgoDrive 
             * @brief 获取指定算法驱动实例
             * @param operator_name_ [IN] 指定的算法名称
             * @return  返回指定算法驱动指针
             * <AUTHOR>
             * @date 2024.10.8
             */
            std::shared_ptr< jrsoperator::OperatorDriveBase> GetSpecificAlgoDrive ( const std::string& algo_name_ );
            
            /**
             * @fun GetSpecificAlgoView 
             * @brief 获取指定算法的界面实例
             * @param operator_name_ [IN] 指定的算法名称
             * @return  返回单个算法的界面指针
             * <AUTHOR>
             * @date 2024.10.8
             */
            jrsoperator::OperatorViewBase* GetSpecificAlgoView (const std::string& algo_name_ );

            /**
             * @fun BindViewAndDrive 
             * @brief 将view中执行的算法回调跟对应的算法绑定
             * @note 如果算法没有对应的view则不绑定
             * <AUTHOR>
             * @date 2024.10.13
             */
            void BindViewAndDrive ();
        private:

            //Fun
            /**
             * @fun LoadAlogs 
             * @brief 加载算子驱动
             * @param drive_path_ 算子驱动dll路径
             * @return 成功返回AOI_OK,否则返回错误码
             * @date 2024.5.31
             * <AUTHOR>
             */
            int LoadAlgoDrive(const std::string& drive_path_);
            /**
             * @fun LoadAlogView 
             * @brief 加载算子界面
             * @param view_path_ 算子界面dll路径
             * @return 成功返回AOI_OK,否则返回错误码
             * @date 2024.6.12
             * <AUTHOR>
             */
            int LoadAlgoView(const std::string& view_path_);

            //Member

            using PluginHandlesDriveVector = std::vector<jrscore::PluginLoaderPtr<jrsoperator::OperatorDriveBase>>;
            using PluginHandlesViewVector = std::vector<jrscore::PluginLoaderPtr<jrsoperator::OperatorViewBase>>;

            using OperatorDriveInstanceMap = std::unordered_map<std::string,jrsoperator::OperatorDriveBase*>;
            using OperatorViewInstanceMap = std::unordered_map<std::string,jrsoperator::OperatorViewBase*>;

            PluginHandlesDriveVector operator_drive_plugin_handles_v;/**< 算子驱动插件句柄 */
            PluginHandlesViewVector  operator_view_plugin_handles_v; /**< 算子界面插件句柄*/

            OperatorDriveInstanceMap operator_drive_instance_map; /**< 算子驱动实例*/
            OperatorViewInstanceMap operator_view_instance_map;   /**< 算子界面实例*/


    };
    
    using AlgoFactoryPtr = std::shared_ptr<AlgoFactory>; /**< 算子工厂指针 */
}
#endif // !__JRSALGOFACTORY_H__
