// Custom
#include "operateview.h"
#include "viewdefine.h"

namespace jrsaoi
{
    OperateView::OperateView(const std::string& name, QWidget* parent)
        : ViewBase(name, parent), ui(new Ui::OperateView()),
        _setting_view_param(std::make_shared<jrsdata::SettingViewParam>())
        , _project_param(std::make_shared<jrsdata::ProjectEventParam>())
    {
        ui->setupUi(this);
        Init();
    }
    OperateView::~OperateView()
    {
        delete ui;
    }
    int OperateView::Init()
    {
        InitMember();
        InitView();
        InitConnect();
        return jrscore::AOI_OK;
    }
    int OperateView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    QWidget* OperateView::GetCustomWidget()
    {
        return edit_detect_model_view->GetTemplateView();
    }
    void OperateView::InitMember()
    {
        auto data_view_func = std::bind(&OperateView::UpdateDataView, this, std::placeholders::_1);
        auto flow_view_func = std::bind(&OperateView::UpdateFlowView, this, std::placeholders::_1);
        //auto motion_view_func = std::bind(&OperateView::UpdateMotionView, this, std::placeholders::_1);
        auto machine_setting_param_func = std::bind(&OperateView::UpdateMotionSettingParam, this, std::placeholders::_1);
        auto detect_statistics_result_param_func = std::bind(&OperateView::UpdateDetectStatisticsResultParam, this, std::placeholders::_1);
        auto update_add_component_func = std::bind(&OperateView::UpdateAddComponentParam, this, std::placeholders::_1);
        operate_view_param_ptr = std::make_shared<jrsdata::OperateViewParam>();
        _operate_update_map =
        {
            {MACHINE_PARAM_UPDATE_EVENT, data_view_func},
            {DATABASE_CONNECT_EVENT, data_view_func},
            {DATABASE_DISCONNECT_EVENT, data_view_func},
            {PROJECT_SAVE_EVENT_NAME,flow_view_func},
            {PROJECT_READ_EVENT_NAME,flow_view_func},
            {APPEND_PROJECT_EVENT_NAME,flow_view_func},
            {PROJECT_CREATE_EVENT_NAME,flow_view_func},
            {ENTIRETY_IMAGE_READ,flow_view_func},
            {ENTIRETY_IMAGE_SAVE,flow_view_func},
            {PROJECT_SAVE_AS_EVENT_NAME,flow_view_func},
            {jrsaoi::OPERATE_MOTION_SETTING_UPDATE,machine_setting_param_func},
            //{jrsaoi::SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME,motion_view_func},
            {jrsaoi::OPERATE_DETECT_STATISICS_UPDATE,detect_statistics_result_param_func},
            {jrsaoi::SHORTCUT_ACT_SHOW_MARK_RESULT_EVENT_NAME,detect_statistics_result_param_func},
            {PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME,flow_view_func},
            { jrsaoi::SHORTCUT_ACT_CREATE_BARCODE_EVENT_NAME,update_add_component_func },
            { jrsaoi::SHORTCUT_ACT_CREATE_MARK_EVENT_NAME,update_add_component_func },
            { jrsaoi::SHORTCUT_ACT_ADD_COMPONENT,update_add_component_func },
        };
        _project_param->project_param = std::make_shared<jrsdata::ProjectParam>();
        _project_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        _project_param->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        _project_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        _project_param->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
    }
    void OperateView::InitView()
    {
        // 设置
        //param_setting_view = new ParameterSettings(this);
        project_view = new ProjectView();
        edit_detect_model_view = new EditDetectModelView();
        detect_view = new DetectView(this);     // 检测
       // motionview = new MotiondebugView(this); // 调试
        ui->tabWidget_operate->insertTab(0, project_view, QString::fromWCharArray(L"流程"));
        ui->tabWidget_operate->insertTab(1, edit_detect_model_view, QString::fromWCharArray(L"编辑"));
        ui->tabWidget_operate->insertTab(2, detect_view, QString::fromWCharArray(L"检测"));
        ui->tabWidget_operate->insertTab(3, param_setting_view, QString::fromWCharArray(L"设置"));
        //ui->tabWidget_operate->insertTab(4, motionview, QString::fromWCharArray(L"调试"));
        ui->tabWidget_operate->setCurrentIndex(0);
        add_component_view = new AddComponentView(this);
        add_component_view->setHidden(true);
    }
    void OperateView::InitConnect()
    {
        // 排序了一下,太乱了 jerx

        /**< 三个信号待优化-删除-  by:HJC 2025/1/13 */
        connect(this, &OperateView::SigUpdateView, detect_view, &DetectView::SigUpdateView);
        connect(detect_view, &DetectView::SigDetectViewChangeTrigger, this, &OperateView::SigUpdateOperator);
        //connect(this, &OperateView::SigUpdateView, motionview, &MotiondebugView::UpdateView);
        connect(this, &OperateView::SigUpdateView, param_setting_view, &ParameterSettings::UpdateView);

        connect(this, &OperateView::SignalUpdateProjectView, project_view, &ProjectView::SlotUpdate); // 统一槽函数 jerx
        connect(this, &OperateView::SignalUpdateProjectView, edit_detect_model_view, &EditDetectModelView::SlotUpdate);
        connect(this, &OperateView::SigUpdateBoardWidth, param_setting_view, &ParameterSettings::UpdateBoardWidth);
        connect(this, &OperateView::SignalUpdateEditDetectModelView, edit_detect_model_view, &EditDetectModelView::SlotUpdate);
        connect(this, &OperateView::SigUpdateNewFileGetProductWidth, project_view, &ProjectView::SlotUpdateNewFileProductWidth);

       connect(project_view, &ProjectView::SigGetProductWidth, this, &OperateView::SlotUpdateProductWidth);

        //! 工程界面部分connect
        connect(project_view, &ProjectView::SignalViewEvent, this, &OperateView::SigUpdateOperator);
        connect(project_view, &ProjectView::SigMotionDebugTrigger, this, &OperateView::SlotDebugView);
        connect(project_view, &ProjectView::SigGetPosition, this, [&]()
            {
                emit SignalUpdateProjectView(operate_view_param_ptr);
            });
        ////! 运控调试部分
        //connect(motionview, &MotiondebugView::SigMotionDebugTrigger, this, &OperateView::SlotDebugView);

        //! 编辑检测框部分
        connect(edit_detect_model_view, &EditDetectModelView::SigEditAlgoUpdate, this, &OperateView::SlotAlgoView);
        connect(edit_detect_model_view, &EditDetectModelView::SigUpdateOperator, this, &OperateView::SigUpdateOperator);

        //! 元件库保存
        connect(this, &OperateView::SigUpdateView, edit_detect_model_view, &EditDetectModelView::SlotUpdate);

        //! 参数设置部分
        connect(param_setting_view, &ParameterSettings::SigUpdateMachineParam, this, &OperateView::SlotMachineSetting);
        connect(param_setting_view, &ParameterSettings::SigGetBoardWith, this, [&]()
            { emit SigUpdateBoardWidth(operate_view_param_ptr); });
        connect(param_setting_view, &ParameterSettings::SigSetBoardWidth, this, [&](const jrsdata::OperateViewParamPtr param_)
            { SlotDebugView(param_); });
        // 保存界面配置参数
        connect(param_setting_view, &ParameterSettings::SigSaveSetting, this, [=](const jrsdata::OperateViewParamPtr param_)
            { SlotSaveSetting(param_); }); 
        connect(param_setting_view, &ParameterSettings::SigSendMotionParamToWorkFlow, this,&OperateView::SlotSendMotionParamToWorkFlow);
        connect(param_setting_view, &ParameterSettings::SigSaveRepairData, this, &OperateView::SlotSaveRepairData);
        connect(project_view, &ProjectView::SigSaveProject, this, [=]() {
            _project_param->event_name = jrsaoi::PROJECT_SAVE_EVENT_NAME;

            emit SigUpdateOperator(_project_param);
            });
        connect(project_view, &ProjectView::SigSaveEntiretyImages, this, [&](const std::string& group_name_)
            {
                _project_param->project_param->current_group_name = group_name_;
                _project_param->event_name = jrsaoi::ENTIRETY_IMAGE_SAVE;
                emit SigUpdateOperator(_project_param);
            });
        connect(project_view, &ProjectView::SigOpenEntiretyImages, this, [&](const std::string& group_name_)
            {
                if (!_project_param->project_param)
                {
                    JRSMessageBox_INFO("Operate", "请先打开工程或者创建工程", jrscore::MessageButton::Ok);
                    return;
                }

                /**<清除Render2D界面图片释放内存*/
                auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
                param_temp->event_name = jrsaoi::CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME;
                param_temp->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param_temp->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                emit SigUpdateOperator(param_temp);

                _project_param->project_param->current_group_name = group_name_;
                _project_param->event_name = jrsaoi::ENTIRETY_IMAGE_READ;
                emit SigUpdateOperator(_project_param);
            });
        connect(project_view, &ProjectView::SigReadEntiretyImages, this, [&](const std::string& group_name_)
            {
                _project_param->project_param->current_group_name = group_name_;
             
                _project_param->event_name = jrsaoi::ENTIRETY_IMAGE_READ;
                emit SigUpdateOperator(_project_param);
            });

        connect(add_component_view, &AddComponentView::SigUpdateView, this, &OperateView::SigUpdateOperator);
    }

    int OperateView::UpdateDataView(const jrsdata::OperateViewParamPtr& param_)
    {
        //统一!的机台参数更新，更新到界面
        param_->machine_param.event_name = param_->event_name;
       // param_setting_view->UpdateViewMachineParam(param_->machine_param);
        /** <更新参数 */
        project_view->UpdateView(param_);

        /** <推送到编辑页面 */
        if (param_->event_name == MACHINE_PARAM_UPDATE_EVENT)
        {
            jrsdata::ViewParamBasePtr view_param = std::static_pointer_cast<jrsdata::ViewParamBase>(param_);
            edit_detect_model_view->SlotUpdate(view_param);
        }
        return jrscore::AOI_OK;
    }

    int OperateView::UpdateFlowView(const jrsdata::OperateViewParamPtr& param_)
    {
        project_view->UpdateView(param_);
        return jrscore::AOI_OK;
    }

    /*int OperateView::UpdateMotionView(const jrsdata::OperateViewParamPtr& param_)
    {
        motionview->UpdateView(param_);
        return jrscore::AOI_OK;
    }*/

    int OperateView::UpdateMotionSettingParam(const jrsdata::OperateViewParamPtr& param_)
    {
        if (param_->event_name == jrsaoi::OPERATE_MOTION_SETTING_UPDATE)
        {
            operate_view_param_ptr->config_setting_param = param_->config_setting_param;
        }

        project_view->UpdateView(param_);
        //motionview->UpdateView(param_);
        /**< 检测界面发送数据  By:HJC 2025/2/13 */
        detect_view->SigUpdateView(param_);
        param_setting_view->UpdateView(param_);
        return 0;
    }
    /** 更新数据界面就可以了 */
    int OperateView::UpdateDetectStatisticsResultParam(const jrsdata::OperateViewParamPtr& param_)
    {
        /** <更新到  检测统计页面 即可 */
        detect_view->UpdateView(param_);
        return 0;
    }

    int OperateView::UpdateAddComponentParam(const jrsdata::OperateViewParamPtr& param_)
    {
        add_component_view->UpdateView(param_);
        return 0;
    }

    int OperateView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::ENTIRETY_IMAGE_READ && param_->sub_name == jrsaoi::PARAMS_UPDATE_SUB_NAME)
        {
            return jrscore::AOI_OK;
        }
        // 界面更新
        if (param_->event_name == jrsaoi::SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME)
        {
            edit_detect_model_view->ShowTemplateView();

            return jrscore::AOI_OK;
        }
        if (auto param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_); param != nullptr)
        {
            auto map_it = _operate_update_map.find(param->event_name);
            if (map_it != _operate_update_map.end())
            {
                return map_it->second(param);
            }
            if (param->event_name == "set_algo_name_lists")
            {
                //TODO:后面更改 by YYZhang
                auto lists = param->algo_name_lists;
                param->algo_name_lists = lists;
                emit SignalUpdateEditDetectModelView(param_);
                return jrscore::AOI_OK;
            }
            if (param->event_name == jrsaoi::SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME || param->event_name == jrsaoi::MOTION_UPDATE_DEBUG_VIEW_EVENT)
            {
                //operate_view_param_ptr->device_param.motion_param.motion_status = param->device_param.motion_param.motion_status;
            }
            if (param->event_name == OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME)
            {
                edit_detect_model_view->SlotUpdate(param);
            }
            emit SigUpdateView(param);
        }
       /* else if (auto param = std::dynamic_pointer_cast<jrsdata::RenderEventParam>(param_); param != nullptr)
        {
            emit SignalUpdateProjectView(param);
        }
        else if (auto param = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param_); param != nullptr)
        {
            emit SignalUpdateEditDetectModelView(param_);
        }
        else if (auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_); param != nullptr)
        {
            emit SignalUpdateEditDetectModelView(param_);
        }
        else if (auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_); param != nullptr)
        {
            if (param->event_name == jrsaoi::PROJECT_READ_EVENT_NAME)
            {
                _project_param->project_param = param->project_param;
            }
            emit SignalUpdateProjectView(param_);
        }
        */
        return jrscore::AOI_OK;
    }
    void OperateView::SlotDebugView(const jrsdata::OperateViewParamPtr& operateparam)
    {
        operate_view_param_ptr->device_param = operateparam->device_param;
        operate_view_param_ptr->event_name = jrsaoi::DEVICE_CONTROL_EVENT_NAME;
        operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::DEVICE_MODULE_NAME;
        emit SigUpdateOperator(operate_view_param_ptr);
    }
    void OperateView::SlotSaveSetting(const jrsdata::OperateViewParamPtr& operateparam)
    {
        operate_view_param_ptr->config_setting_param = operateparam->config_setting_param;
        operate_view_param_ptr->config_setting_param.event_name = "SaveMotionCfgSetting";
        operate_view_param_ptr->event_name = jrsaoi::MOTION_CONFIG_EVENT_NAME;
        operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        emit SigUpdateOperator(operate_view_param_ptr);
    }

    void OperateView::SlotSendMotionParamToWorkFlow(const jrsdata::OperateViewParamPtr& operateparam)
    {
        operate_view_param_ptr->config_setting_param = operateparam->config_setting_param;
        operate_view_param_ptr->event_name = jrsaoi::OPERATE_SEND_MOTION_PARAM_TO_WORKFLOW_EVENT_NAME;
        operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::WORKFLOW_MODULE_NAME;
        emit SigUpdateOperator(operate_view_param_ptr);
    }

    void OperateView::SlotMachineSetting(const jrsdata::MachineParam& machine_param_)
    {
        //QObject* sender_obj = sender();
        //if (sender_obj == nullptr)
        //{
        //    /*    qDebug() << "Sender is nullptr";*/
        //    return;
        //}
        //else if (qobject_cast<DataView*>(sender_obj))
        //{
        //    _setting_view_param->machine_param.machine_params_data = machine_param_.machine_params_data;
        //    //_setting_view_param->machine_param.machine_params_seting=param_setting_view->//! TODO  需要在设置参数界面添加一个获取当前参数的接口！！！
        //}
        //else if (qobject_cast<ParameterSettings*>(sender_obj))
        //{
        //    ////设置界面发送函数
        //    //_setting_view_param->machine_param.machine_params_seting = machine_param_.machine_params_seting;
        //    _setting_view_param->machine_param.machine_params_data = data_view->GetCurrentMachineParam();
        //}

        //_setting_view_param->machine_param = machine_param_;
        _setting_view_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        _setting_view_param->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        _setting_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        _setting_view_param->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        _setting_view_param->event_name = machine_param_.event_name;
        emit SigUpdateOperator(_setting_view_param);
    }

    void OperateView::SlotAlgoView(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME)
        {
            emit SigUpdateOperator(param_);
        }
        else
        {
            auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
            if (!param)
                return;
            param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
            param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
            param->module_name = jrsaoi::VIEW_MODULE_NAME;
            param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
            emit SigUpdateOperator(param);
        }
    }
    int OperateView::SetEditAlgorithmViewDefaultParam(const EditAlgorithmViewDefaultParam& _param)
    {
        return edit_detect_model_view->SetEditAlgorithmViewDefaultParam(_param);
    }
    int OperateView::SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list)
    {
        return edit_detect_model_view->SetAlgorithmAndDefectList(_algo_name_list, _defect_list);
    }

    void OperateView::SlotUpdateProductWidth()
    {
        //if (operate_view_param_ptr->device_param.motion_param.motion_status.pos.size() < 8)
        //{
        //    return;
        //}
        ////获取轨道的宽度返回
        //if (operate_view_param_ptr->device_param.motion_param.motion_status.pos.size() < 8)
        //{
        //    return;
        //}

        ////By HJC 解决板子为空时崩溃的问题。2/13
        //double board_width = 0;
        //if (!operate_view_param_ptr->config_setting_param.motion_setting.board_width_base.empty())
        //{
        //    board_width = operate_view_param_ptr->config_setting_param.motion_setting.board_width_base.front();
        //}
        //else
        //{
        //    JRSMessageBox_WARN("警告", "读取板子宽度异常", jrscore::MessageButton::Ok);
        //}

       /* double track1_width = 0 - std::stod(operate_view_param_ptr->device_param.motion_param.motion_status.pos.at(3)) + board_width;
        double track2_width = std::stod(operate_view_param_ptr->device_param.motion_param.motion_status.pos.at(4));
        emit SigUpdateNewFileGetProductWidth(track1_width, track2_width);*/
        //emit SigUpdateNewFileGetProductWidth(150, 151);
    }
    void OperateView::SlotSaveRepairData(const jrsdata::RepairData& repair_data)
    {
        operate_view_param_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        operate_view_param_ptr->event_name = repair_data.event_name;
        emit SigUpdateOperator(operate_view_param_ptr);
    }
}