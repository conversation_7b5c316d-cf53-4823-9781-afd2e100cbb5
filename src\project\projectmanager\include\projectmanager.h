/*****************************************************************
 * @file   projectmanager.h
 * @brief  工程模块管理类，主要负责工程模块中各类的初始化，交互等
 * @details
 * <AUTHOR>
 * @date 2024.8.14
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.14          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSPROJECTMANAGER_H__
#define __JRSPROJECTMANAGER_H__

 //STD
#include <iostream>
#include <functional>
//Custom
#include "viewparam.hpp"
#include "projectparam.hpp"

#include "pluginexport.hpp"
//Third

namespace jrscore
{
    class CoordinateTransform;
}
namespace jrsproject
{
    using EventInvokeMap = std::unordered_map<std::string, jrsdata::InvokeProjectFun>;
    using ViewEventInvokeMap = std::unordered_map<std::string, jrsdata::InvokeViewParamBaseFun>;
    struct ProjectDataImpl;
    class JRS_AOI_PLUGIN_API ProjectManager
    {
    public:
        ProjectManager();
        ~ProjectManager();

        /**
         * @fun EventHandler
         * @brief 处理工程事件，根据具体的事件名称，调用对应的响应函数
         * @param project_event_param_ 工程事件参数
         * <AUTHOR>
         * @date 2024.8.15
         */
        //void EventHandler(const jrsdata::ProjectParamPtr& project_event_param_);
        void EventHandler(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun SetCallBack
         * @brief 设置回调函数，这个回调函数主要是为了将工程层的信息更新到界面端
         * @param callback_ 回调函数
         * <AUTHOR>
         * @date 2024.8.15
         */
        void SetCallBack(jrsdata::InvokeProjectFun callback_);

        void SetTransformCoordinatePtr(const std::shared_ptr<jrscore::CoordinateTransform>& trans_ptr_);

    private:
        /**
         * @fun Init
         * @brief 初始化
         * <AUTHOR>
         * @date 2024.8.15
         */
        void Init();

        /**
         * @fun InitMember
         * @brief 初始化成员变量
         * <AUTHOR>
         * @date 2024.8.15
         */
        void InitMember();

        /**
         * @fun InitInvokeFun
         * @brief 初始化响应函数map
         * <AUTHOR>
         * @date 2024.8.18
         */
        void InitInvokeFun();

        /**
         * @fun IsValidProjectParam
         * @brief 判断工程参数是否有效
         * @param param_ 工程参数
         * @return  有效：true，无效：false
         * <AUTHOR>
         * @date 2024.8.18
         */
        bool IsValidParam(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun InvokeFun
         * @brief 根据event_name执行回调
         * @param project_event_param_ 参数
         * @return  有效：true，无效：false
         *
         * @note 改了名字,不然总觉得是一个条件判断函数
         * <AUTHOR>
         * @date 2024.8.18
         */
        bool InvokeFun(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun ParseCadInfo
         * @brief 将CAD信息映射到工程的元件信息中
         * @param project_param_ 工程参数
         * @return  成功返回AOI_OK，其他失败
         * <AUTHOR>
         * @date 2024.8.18
         */
        int ParseCadInfo(const jrsdata::ViewParamBasePtr& param_);

        ProjectDataImpl* project_data_impl;
    };
    using ProjectManagerPtr = std::shared_ptr<ProjectManager>;
}

#endif // !__JRSPROJECTMANAGER_H__



