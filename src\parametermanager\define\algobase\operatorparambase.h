/*****************************************************************//**
 * @file   operatorparambase.h
 * @brief  算子参数基类
 * @note 算法基类参数修改，主要改动为将算法的图片输入更改为cv::Mat格式输入，为了减少数据转换的耗时 by liuchenfan 2024.12.3
 * @details
 * <AUTHOR>
 * @date 2024.2.26
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.26         <td>V1.0              <td>YY<PERSON>hang      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSOPERATORPARAMBASE_H__
#define __JRSOPERATORPARAMBASE_H__

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
 // std
#include <memory>
#include <map>
// opencv
#include <opencv2/core.hpp>
// custom
// custom
#include "colorparams.h"
#include "algogeometrydata.hpp"
#include "specificationwidgetparam.h"
#include "judgeparam.h"
#pragma warning(pop)
namespace jrsoperator
{
    /**
   * @brief 掩膜类型
   */
    enum class AlgoInputMaskType :int
    {
        MASK_AI = 0,
        MASK_3D = 1,
        MASK_2D = 2,
    };

    /**
     * @brief pad方向
     */
    enum class DirectionType : int
    {
        UP = 1,
        RIGHT = 2,
        DOWN = 3,
        LEFT = 4,
    };

    /**
     * @brief 灯图类型
     */
    enum class LightType : int
    {
        NONE = -1,
        RGB = 0,
        WHITE = 1,
        LOWWHITE = 2,
        HEIGHT = 3,
        OTHERS = 4,
        COUNT,
    };

    /**
         * @brief 模板颜色参数组
         * by zhangyuyu 2024.12.3
         */
    struct TemplColorParamsGroup
    {
        TemplColorParamsGroup() = default;
        TemplColorParamsGroup(const ColorParams& color_params, const cv::Mat& light_img, LightType id)
            : color_params(color_params), template_img(light_img), id(id)
        {
        }
        TemplColorParamsGroup(LightType id, const ColorParams& color_params)
            : id(id), color_params(color_params)
        {
        }
        LightType id = LightType::NONE; // 灯图id
        ColorParams color_params; // 模板颜色参数  11/29/2024 后面需要增加是否修改的标志位
        cv::Mat template_img = cv::Mat(); // 模板光源图片
    };

    struct FastSetSpecParamInfo
    {
        float std_value = -1.0f;    /**< 规格的标准值 */
        float offset_value = -1.0f;  /**< 规格的偏移量 */
    };

    struct OperatorParamBase
    {
        ////////// 输入 //////////
        double x_resolution;                                      /**< x方向分辨率 */
        double y_resolution;                                      /**< y方向分辨率 */
        int algo_result_index;                                    /**< 算法结果索引 主要用于pad检测算法结果的显示*/
        JrsRect component_rect;                                   /**< cad位置(相对于图片) */
        JrsRect detect_rect;                                      /**< 放大后检测框位置(相对于图片) */
        JrsRect ori_detect_rect;                                  /**< 原始检测框位置(相对于图片) */
        std::unordered_map<int, std::vector<JrsRect>> pad_rects; /**< 原始pad框位置 {方向 本体检测时为-1,框组} */
        std::unordered_map<int, std::vector<JrsRect>> sub_detect_rects;   /**< 放大后子检测框位置 {方向 本体检测时为-1,框组} 检测框内部的检测框,相对于detect_rect */
        std::unordered_map<int, std::vector<JrsRect>> ori_sub_detect_rects; /**< 放大前的子检测框位置 宽高*/

        std::unordered_map<int, cv::Mat> input_image;             /**< 算法检测输入图像 */
        std::unordered_map<int, std::vector<cv::Mat>> input_mask_image;  /**< 算法检测输入mask */
        std::vector<TemplColorParamsGroup> template_data = std::vector<TemplColorParamsGroup>();  /**< 算法检测输入颜色参数 */
        std::unordered_map<std::string, ControlSpec> spec_params = std::unordered_map<std::string, ControlSpec>();         /**< 规格配置 {规格项名称,规格参数} */
        std::unordered_map<int, cv::Mat> hom_matrix; /**< 平移旋转矩阵 用于接受上一个算法的定位输出 int:检测框或者子检测框对应的id  目前算法关联只有定位结果的关联,后期拓展可以任意数据格式的关联 by zhangyuyu 2025.1.21*/
        cv::Mat transform_hom_matrix = cv::Mat::eye(2, 3, CV_32F);/**< 平移旋转矩阵 能够将检测框转换到真实位置 (true_rect = matrix * detect_rect) */

        ////////// 输出 //////////
        cv::Mat output_mask_image;                                        /**< 输出mask图像 */
        cv::Mat height_mat;                                               /**< 输出高度图 */
        std::vector<JrsVec3f> point_cloud = std::vector<JrsVec3f>();                                /**< 输出点云图 */
        std::vector<JrsRect> output_detect_rects = std::vector<JrsRect>();                         /**< 输出检测位置 用于绘制 */
        std::unordered_map<int, cv::Mat> result_image_group = std::unordered_map<int, cv::Mat>();              /**< 输出图像组 {id,算法输出图像} */
        std::unordered_map<std::string, cv::Mat> intermediate_result = std::unordered_map<std::string, cv::Mat>();     /**< 算法中间结果 {名称,图片} 保存到本地时也是该名称 */
        std::string error_message = "";                                        /**< 算法异常信息 */
        std::unordered_map<int, AlgoJudgeResult> spec_value_params = std::unordered_map<int, AlgoJudgeResult>();       /**< 规格结果 {检测框编号(JrsRect.id),{AlgoJudgeResult.judge_param 规格项名称,结果值} {AlgoJudgeResult.detect_rect_status 检测框状态 ture/false}} */

        OperatorParamBase() :x_resolution(0), y_resolution(0), component_rect(), detect_rect() {
        }
        virtual ~OperatorParamBase() = default;
    };
    using OperatorParamBasePtr = std::shared_ptr<OperatorParamBase>;
}

#endif // !__JRSOPERATORPARAMBASE_H__
