<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ExpansionMultiPanel</class>
 <widget class="QWidget" name="ExpansionMultiPanel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>340</width>
    <height>595</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>ExpansionMultiPanel</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_13">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <widget class="QFrame" name="frame_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_11">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QWidget" name="widget_2" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QGridLayout" name="gridLayout_3">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="spacing">
             <number>0</number>
            </property>
            <item row="0" column="0">
             <widget class="QFrame" name="frame_3">
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Plain</enum>
              </property>
              <property name="lineWidth">
               <number>0</number>
              </property>
              <layout class="QGridLayout" name="gridLayout_9">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
               <item row="0" column="1">
                <widget class="QWidget" name="widget_5" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>110</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>110</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string/>
                 </property>
                 <layout class="QGridLayout" name="gridLayout">
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <property name="spacing">
                   <number>2</number>
                  </property>
                  <item row="1" column="1">
                   <widget class="QSpinBox" name="spin_x">
                    <property name="enabled">
                     <bool>true</bool>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="prefix">
                     <string>X：</string>
                    </property>
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>999999</number>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QSpinBox" name="spin_y">
                    <property name="enabled">
                     <bool>true</bool>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="prefix">
                     <string>Y：</string>
                    </property>
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>999999</number>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QPushButton" name="delete_sub_board">
                    <property name="enabled">
                     <bool>true</bool>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true"/>
                    </property>
                    <property name="text">
                     <string>删除子板</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="1">
                   <widget class="QPushButton" name="pushbutton_confirm_sort">
                    <property name="text">
                     <string>确认排序</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QPushButton" name="pushbutton_substandard_multile_boards">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>100</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>不规则多连板</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="0">
                   <widget class="QPushButton" name="pushbutton_irregular_sort_boards">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>100</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>不规则排序</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QPushButton" name="pushbutton_rule_multile_boards">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>100</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>规则多连板</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QPushButton" name="pushbutton_rule_sort_boards">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>100</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>规则排序</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="2">
                   <spacer name="horizontalSpacer_10">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label">
                 <property name="minimumSize">
                  <size>
                   <width>30</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>30</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>扩
展
多
联
板</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QWidget" name="widget" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QGridLayout" name="gridLayout_10">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="spacing">
             <number>0</number>
            </property>
            <item row="0" column="0">
             <widget class="QStackedWidget" name="stackedWidget">
              <property name="currentIndex">
               <number>1</number>
              </property>
              <widget class="QWidget" name="page">
               <layout class="QGridLayout" name="gridLayout_14">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QWidget" name="widget_3" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_4">
                   <property name="leftMargin">
                    <number>3</number>
                   </property>
                   <property name="topMargin">
                    <number>3</number>
                   </property>
                   <property name="rightMargin">
                    <number>3</number>
                   </property>
                   <property name="bottomMargin">
                    <number>3</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_confirm_component">
                     <property name="text">
                      <string>确认元件</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_confirm">
                     <property name="text">
                      <string>预览</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_gen">
                     <property name="text">
                      <string>确认生成</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="5">
                    <widget class="Line" name="line">
                     <property name="orientation">
                      <enum>Qt::Vertical</enum>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0" colspan="3">
                    <widget class="QLabel" name="label_2">
                     <property name="text">
                      <string>步骤一：请在选中的子板中选择一个元件</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_template">
                     <property name="text">
                      <string>截图模板</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="0">
                    <widget class="QLabel" name="label_10">
                     <property name="minimumSize">
                      <size>
                       <width>160</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>步骤六：生成多连板CAD</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_Y_detect">
                     <property name="text">
                      <string>Y框选识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="6">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_X_detect">
                     <property name="text">
                      <string>X框选识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="0" colspan="3">
                    <widget class="QLabel" name="label_3">
                     <property name="text">
                      <string>步骤二：元件截图作为模板</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0" colspan="3">
                    <widget class="QLabel" name="label_9">
                     <property name="text">
                      <string>步骤五：确认多联板临时预览</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="1">
                    <widget class="QPushButton" name="pushbutton_rule_multile_board_cancel">
                     <property name="text">
                      <string>取消生成</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="0" colspan="3">
                    <widget class="QLabel" name="label_7">
                     <property name="text">
                      <string>步骤三：框选横向最后一个子板元件</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="0" colspan="3">
                    <widget class="QLabel" name="label_8">
                     <property name="text">
                      <string>步骤四：框选竖向最后一个子板元件</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QWidget" name="widget_4" native="true">
                  <layout class="QGridLayout" name="gridLayout_5">
                   <property name="leftMargin">
                    <number>3</number>
                   </property>
                   <property name="topMargin">
                    <number>3</number>
                   </property>
                   <property name="rightMargin">
                    <number>3</number>
                   </property>
                   <property name="bottomMargin">
                    <number>3</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_rule_component_name">
                     <property name="text">
                      <string>元件名称：</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QWidget" name="widget_rule_component_template" native="true">
                     <layout class="QGridLayout" name="gridLayout_6">
                      <property name="leftMargin">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <item row="0" column="0">
                       <widget class="QGraphicsView" name="component_graphics_view">
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>90</height>
                         </size>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_2">
               <layout class="QGridLayout" name="gridLayout_12">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QWidget" name="widget_6" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_8">
                   <property name="leftMargin">
                    <number>3</number>
                   </property>
                   <property name="topMargin">
                    <number>3</number>
                   </property>
                   <property name="rightMargin">
                    <number>3</number>
                   </property>
                   <property name="bottomMargin">
                    <number>3</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="1" column="4">
                    <widget class="QPushButton" name="pushbutton_substandard_multile_board_select_location">
                     <property name="text">
                      <string>框选位置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="4">
                    <widget class="QPushButton" name="pushbutton_substandard_multile_board_confirm_position">
                     <property name="text">
                      <string>确认位置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="0" colspan="2">
                    <widget class="QLabel" name="label_16">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>步骤三：确认子板位置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0" colspan="3">
                    <widget class="QLabel" name="label_11">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>步骤一：生成多连扳</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QPushButton" name="pushbutton_generate_irregular_multile_board">
                     <property name="text">
                      <string>生成多连板</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="0" colspan="3">
                    <widget class="QLabel" name="label_13">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>步骤二：框选目标移动位置</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QWidget" name="widget_7" native="true">
                  <layout class="QGridLayout" name="gridLayout_7">
                   <property name="leftMargin">
                    <number>3</number>
                   </property>
                   <property name="topMargin">
                    <number>3</number>
                   </property>
                   <property name="rightMargin">
                    <number>3</number>
                   </property>
                   <property name="bottomMargin">
                    <number>3</number>
                   </property>
                   <property name="horizontalSpacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_substandard_component_name">
                     <property name="text">
                      <string>元件名称</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QWidget" name="widget_substandard_component_template" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
