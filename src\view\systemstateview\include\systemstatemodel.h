﻿/*****************************************************************//**
 * @file   logshowmodel.h
 * @brief  日志显示数据类
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
 //Third
#ifndef __INITSYSMODEL_H__
#define __INITSYSMODEL_H__


#include "modelbase.h"
namespace jrsaoi
{
    class SystemStateModel :
        public ModelBase
    {
    public:
        SystemStateModel(const std::string& name_);
        ~SystemStateModel();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        const jrsdata::SystemStateViewParamPtr& GetModelData();
        const jrsdata::SystemStateParamPtr& GetSystemState();
    private:
        /**
         * @fun InitMember
         * @brief
         * <AUTHOR>
         * @date 2024.11.21
         */
        void InitMember();
        /**
         * @fun InitSystemState
         * @brief 初始化系统状态
         * @param system_state
         * <AUTHOR>
         * @date 2024.11.21
         */
        void InitSystemState(std::unordered_map<jrsdata::SystemStateParam::SystemItem, jrsdata::SystemStateParam::StateLevelInfo>& system_state);

        /**
         * @fun UpdateCheckState
         * @brief 更新每项的检测状态
         * @param check_items_
         * @param is_ok_
         * @param await_check_item_num_
         * <AUTHOR>
         * @date 2024.11.21
         */
        void UpdateCheckState(jrsdata::SystemStateMap& check_items_, bool& is_ok_, int& await_check_item_num_);
        /**
         * @fun CalculateProgressRate
         * @brief 计算进度比
         * @param total_items_
         * @param await_check_item_num_
         * @return
         * <AUTHOR>
         * @date 2024.11.21
         */
        int CalculateProgressRate(int total_items_, int await_check_item_num_);
        /**
         * @fun UpdateSystemState
         * @brief 更新系统状态
         * <AUTHOR>
         * @date 2024.11.21
         */
        void UpdateSystemState();
        /**
         * @fun GetErrorInfo
         * @brief 获取错误信息
         * @param code
         * @param err_info
         * @return
         * <AUTHOR>
         * @date 2024.11.21
         */
        std::string GetErrorInfo(int code, const std::string& err_info);
        /**
         * @fun UpdateSystemState
         * @brief 更新系统状态
         * @param check_items_data
         * @param check_items_map
         * @param system_state
         * <AUTHOR>
         * @date 2024.11.21
         */
        void UpdateSystemState(const std::unordered_map<std::string, jrsdata::MachineCheckParamInfo>& check_items_data,
            const std::vector<std::pair<std::string, jrsdata::SystemStateParam::SystemItem>>& check_items_map,
            std::unordered_map<jrsdata::SystemStateParam::SystemItem, jrsdata::SystemStateParam::StateLevelInfo>& system_state);
        std::mutex _mtx;
        jrsdata::SystemStateViewParamPtr _param_data;
        jrsdata::SystemStateParamPtr _sys_state_param;
    };
    using SystemStateModelPtr = std::shared_ptr<SystemStateModel>;
}
#endif // !__LOGSHOWMODEL_H__
