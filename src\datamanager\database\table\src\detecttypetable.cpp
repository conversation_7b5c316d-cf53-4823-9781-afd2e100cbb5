﻿#include "detecttypetable.h"
#include "mysqlimp.hpp"
#include "idatabase.h"
//std
#include <memory>

jrsdatabase::DetectTypeTable::DetectTypeTable(const std::string& table_name_)
    :TableBase(table_name_)
{
}

jrsdatabase::DetectTypeTable::~DetectTypeTable()
{
}

int jrsdatabase::DetectTypeTable::Create(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    auto res = conn_ptr_->CreateTable<jrstable::TDetectType>("detect_type_id", true);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]创建", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }

    res = InitFields(conn_ptr_);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]初始化", _table_name, " 表字段失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
    }

    if (res == jrscore::AOI_OK)
    {
        ExcuteInitData(conn_ptr_);
    }

    /*创建 index*/
    InitIndex(conn_ptr_);

    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Drop(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    auto temp_ptr = std::static_pointer_cast<DB_Mysql>(conn_ptr_);

    std::string sql_temp = "TRUNCATE " + _table_name;

    auto res = conn_ptr_->ExecuteSQL(sql_temp);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("[", __FUNCTION__, "]删除 ", _table_name, " 表失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Show(jrstable::TableParamBasePtr& db_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    (void)db_;
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    return 0;
}

int jrsdatabase::DetectTypeTable::Insert(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectType>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->AddData<jrstable::TDetectType>(*temp_ptr);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Insert(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TDetectType>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    auto res = conn_ptr_->AddData<jrstable::TDetectType>(boards);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("插入数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Update(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectType>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->UpdateData<jrstable::TDetectType>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Update(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto datas = BasePtrVecToObjectVector<jrstable::TDetectType>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->UpdateData(datas, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更新数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Replace(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto temp_ptr = std::static_pointer_cast<jrsdatabase::jrstable::TDetectType>(table_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    int affect_res = 0;
    //std::cout << "thead id:" << std::this_thread::get_id() << "this ptr:" << conn_ptr_ << "\n";
    auto res = conn_ptr_->Replace<jrstable::TDetectType>(*temp_ptr, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("更新数据失败 ", _table_name);
        return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectTypeTable::Replace(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    auto boards = BasePtrVecToObjectVector<jrstable::TDetectType>(tables_);
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    int affect_res = 0;
    auto res = conn_ptr_->Replace(boards, affect_res);
    if (res != jrscore::AOI_OK)
    {
        Log_Error_Stack("数据库更换数据失败 ", conn_ptr_->GetLastError());
        return res;
    }
    return jrscore::AOI_OK;
}
int jrsdatabase::DetectTypeTable::InitIndex([[maybe_unused]] const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    _index_container.push_back("index_board_id");
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::InitFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }
    return InitVarcharFields(conn_ptr_);
}

int jrsdatabase::DetectTypeTable::InitVarcharFields(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    jrsdatabase::MySQLAlterTableFieldType type;
    const std::map<std::string, int> fields_to_alter = {
        {"detect_type_name", 600},
        {"detect_type_description",600},
        {"map_default_code",600},
        {"map_default_name",600},
    };
    for (const auto& field_length : fields_to_alter) {
        type.field_type_name = jrsdatabase::MySQLDataType::VARCHAR;
        type.length = field_length.second;
        auto res = conn_ptr_->AlterTableFieldType(this->_table_name, field_length.first, type);
        if (res) return res;
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::ExcuteInitData(const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    // 先清空表
    {
        std::string truncate_sql = "TRUNCATE t_detect_type;";
        auto res = conn_ptr_->ExecuteSQL(truncate_sql);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("[", __FUNCTION__, "] 清空数据表 ", _table_name, " 失败! [", __LINE__, "] : ", conn_ptr_->GetLastError());
            return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
        }
    }

    // 插入数据
    std::vector<std::string> insert_sql_list = {
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(1, 'OK', '', 'Jrs001', 'OK');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(2, '默认', '', 'Jrs002', '默认');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(3, '缺料', '', 'Jrs003', '缺料');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(4, '偏移', '', 'Jrs004', '偏移');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(5, '错件', '', 'Jrs005', '错件');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(6, '极性', '', 'Jrs006', '极性');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(7, '翘起', '', 'Jrs007', '翘起');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(8, '旋转', '', 'Jrs008', '旋转');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(9, '少锡', '', 'Jrs009', '少锡');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(10, '虚焊', '', 'Jrs010', '虚焊');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(11, '锡桥', '', 'Jrs011', '锡桥');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(12, '损件', '', 'Jrs012', '损件');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(13, '锡珠', '', 'Jrs013', '锡珠');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(14, '异物', '', 'Jrs014', '异物');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(15, '双镍片', '', 'Jrs015', '双镍片');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(16, '锡包', '', 'Jrs016', '锡包');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(17, '镍片变形', '', 'Jrs017', '镍片变形');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(18, '镍片浮高', '', 'Jrs018', '镍片浮高');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(19, '焊盘翘起', '', 'Jrs019', '焊盘翘起');",
        "INSERT INTO `jrs_aoi_20_db`.`t_detect_type` (`detect_type_id`, `detect_type_name`, `detect_type_description`, `map_default_code`, `map_default_name`) VALUES(20, '引脚外漏', '', 'Jrs020', '引脚外漏');"
    };

    for (const auto& sql : insert_sql_list)
    {
        auto res = conn_ptr_->ExecuteSQL(sql);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("[", __FUNCTION__, "] 插入数据失败! SQL: ", sql, " [", __LINE__, "] : ", conn_ptr_->GetLastError());
            return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
        }
    }

    return jrscore::AOI_OK;
}

int jrsdatabase::DetectTypeTable::Select(const jrsselect::SelectorParamBasePtr& selector_ptr_, const std::shared_ptr<DB_Mysql>& conn_ptr_)
{
    if (IsConnPtrEmpty(conn_ptr_))
    {
        return jrscore::DataManagerError::E_AOI_DB_SERVICE_PTR_UNINIT;
    }

    if (selector_ptr_->select_name == jrsselect::T_DETECT_TYPE_SELECT_BY_WHERE_CONDITION)
    {
        auto select_ptr = std::dynamic_pointer_cast<jrsselect::SelectTable>(selector_ptr_);
        jrsdatabase::jrstable::TDetectTypeVector detect_types;
        auto res = conn_ptr_->QueryTable<jrsdatabase::jrstable::TDetectType>(detect_types, selector_ptr_->where_condition);

        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("查询", select_ptr->select_name, "数据失败: ", conn_ptr_->GetLastError());
            return res;
        }
        select_ptr->detect_types = detect_types;
    }
    return jrscore::AOI_OK;
}