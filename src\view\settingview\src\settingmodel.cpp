#include "settingmodel.h"

#include "jtools.hpp"

namespace jrsaoi
{
    SettingModel::SettingModel(const std::string& name_)
        :ModelBase(name_)
        , _param_data(std::make_shared<jrsdata::SettingViewParam>())

    {
        _param_data->module_name = name_;
        _param_data->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        _param_data->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        _param_data->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        _param_data->event_name = jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME;

    }
    SettingModel::~SettingModel()
    {
    }
    int SettingModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("参数为空指针！");
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }

        std::lock_guard<std::mutex> lock(mutex_data);
        _param_data->event_name = param_->event_name;
        auto setting_view_param_ptr = std::dynamic_pointer_cast<jrsdata::SettingViewParam>(param_);
        if (_param_data->event_name == jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME)
        {
            _param_data->sys_param = setting_view_param_ptr->sys_param;
            _param_data->comm_param = setting_view_param_ptr->comm_param;
        }
        else if(_param_data->event_name==jrsaoi::UPDATE_COMMON_PARAM_EVENT)
        {
            _param_data->comm_param = setting_view_param_ptr->comm_param;
        }
        return jrscore::AOI_OK;
    }
    int SettingModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("参数为空指针！");
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        auto setting_view_param_ptr=std::dynamic_pointer_cast<jrsdata::SettingViewParam>(param_);
        if (param_->event_name == jrsaoi::SYSTEM_PARAM_SAVE_EVENT)
        {
            _param_data->sys_param = setting_view_param_ptr->sys_param;
        }
        else if(param_->event_name ==jrsaoi::COMMON_PARAM_SAVE_EVENT)
        {
            _param_data->comm_param = setting_view_param_ptr->comm_param;
        }
        else
        {
			Log_ERROR(jrscore::CommonError::E_AOI_COMMON_UNKNOWN, "SysParaModel 数据转换失败!");
			return jrscore::CommonError::E_AOI_COMMON_UNKNOWN;
        }
        return jrscore::AOI_OK;
    }

    const jrsdata::SettingViewParamPtr& SettingModel::GetModelData()
    {
        return _param_data;
    }
}
