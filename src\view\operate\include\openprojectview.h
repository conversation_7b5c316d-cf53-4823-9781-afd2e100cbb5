﻿/*****************************************************************
 * @file   openprojectview.h
 * @brief  用于打开工程界面
 * @details
 * <AUTHOR>
 * @date 2024.11.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.15          <td>V2000              <td>baron      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef OPENPROJECTVIEW_H
#define OPENPROJECTVIEW_H
// prebuild
#include "pch.h"
 //STD
#include <string>

//QT
//#include <QWidget>
#include <QTableView>
#include <QStandardItemModel>
#include <QStyledItemDelegate>
#include <QPushButton>
#include <QFileDialog>
#include <QDir>
#include <QFileInfo>
#include <QDateTime>

//CUSTOM
#include "customstyle.hpp"
//#include "viewparam.hpp"

using namespace jrscore;
using namespace std;

namespace Ui
{
    class OpenProjectView;
}
namespace jrsaoi
{

    struct SelectProjectInfo;

    class OpenProjectView : public QWidget
    {
        Q_OBJECT

    public:
        explicit OpenProjectView(QWidget* parent = nullptr);
        ~OpenProjectView();

    public slots:
        void SlotUpdateProjectPath(const std::string& project_path_);
        /**
         * @fun SlotUpdateProjectList
         * @brief 更新工程列表
         * <AUTHOR>
         * @date 2024.11.15
         */
        void SlotUpdateProjectList();

        /**
         * @fun SlotOpenProject
         * @brief 打开工程路径
         * <AUTHOR>
         * @date 2024.11.15
         */
        void SlotOpenProjectDirectory();

        /**
         * @fun SlotSelectProjectFile
         * @brief 选择工程文件
         * <AUTHOR>
         * @date 2024.11.16
         */
        void SlotListViewSelectProjectFile(const QModelIndex& index);

        /**
         * @fun SlotSelectProjectFile
         * @brief 选择工程文件
         * <AUTHOR>
         * @date 2024.11.16
         */
        void SlotSelectProjectFile();

        /**
         * @fun SlotCancelSelectProjectFile
         * @brief 取消选择工程文件
         * <AUTHOR>
         * @date 2024.11.16
         */
        void SlotCancelSelectProjectFile();

    private:

        /**
         * @fun Init
         * @brief 进行界面的初始化工作
         * <AUTHOR>
         * @date 2024.11.15
         */
        void Init();

        /**
         * @fun InitListView
         * @brief  初始化list界面
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitListView();

        /**
         * @fun InitMember
         * @brief 初始化属性
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitMember();

        /**
         * @fun InitShowListHeader
         * @brief 初始化添加list的头
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitShowListHeader();

        /**
         * @fun InitTableViewColumnWidth
         * @brief
         * @param table_view
         * @param column_widths
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitTableViewColumnWidth(QTableView* table_view, std::vector<int> column_widths);

        /**
         * @fun InitConnect
         * @brief 初始化槽函数连接
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitConnect();


        /**
        */
        void GetProjectListByDirPath(const std::string& dir_);
    signals:
        /**
         * @fun SignalSelectProjectFile
         * @brief 选择工程文件
         * <AUTHOR>
         * @date 2024.11.16
         */
        void SignalSelectProjectFile(const jrsdata::ProjectEventInfo& info);
    private:
        Ui::OpenProjectView* ui;
        QStandardItemModel list_prject_model;
        QStringList header_list;//标题栏数据
        QList<SelectProjectInfo> project_info_list;
        int select_project_index = -1;
    };
}

#endif // OpenProjectView_H