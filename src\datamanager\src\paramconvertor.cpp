﻿#include "paramconvertor.h"
#include "jsonoperator.hpp"
#include "tdatamanager.h"
#include "stringoperation.h"
#include "parameterprocess.hpp"
//third
#include <iguana/json_reader.hpp>
#include <iguana/json_writer.hpp>


jrsdata::ParamConvertor::ParamConvertor()
    :_db_manager_ptr(nullptr)
{
}

jrsdata::ParamConvertor::~ParamConvertor()
{
}

void jrsdata::ParamConvertor::SetTDataManagerPtr(const std::shared_ptr<jrsdatabase::DBManagers>& db_manager_ptr_)
{
    _db_manager_ptr = db_manager_ptr_;
}

void jrsdata::ParamConvertor::SetSettingParamPtr(const std::shared_ptr<jrsparam::ParameterProcess>& param_process_ptr_)
{
    _param_process_ptr = param_process_ptr_;
}

jrsdata::BoardDetectionAssets jrsdata::ParamConvertor::DetectResultToEntiretyBoardParam(
    const jrsdata::ProjectParamPtr& project_param_ptr_,
    const jrsdata::DetectResultParamPtr& detect_result_param_,
    const jrsdata::AllSettingParamMap& setting_params_)
{
    BoardDetectionAssets board_detection_assets;
    jrsdata::EntiretyBoardResult entirety_board_result;
    UpdateLocalMachineStatisticsByDB("jrs_aoi_20", project_param_ptr_->project_name);
    entirety_board_result.data_save_mode = jrsdata::DataSaveMode::SAVE_DATABASE;

    auto aoi_machine = GetMachineDataFromDatabase("jrs_aoi_20");
    if (aoi_machine.has_value())
    {
        aoi_machine->one_fov_take_time = detect_result_param_->one_fov_take_time;
        aoi_machine->project_name = project_param_ptr_->project_name;
        entirety_board_result.aoi_sys = aoi_machine.value();
    }
    else
    {
        Log_ERROR("获取机台数据失败，整板检测结果无法保存到数据库");
    }
    entirety_board_result.user = { "jrs_root", "Jrs123456", jrscore::AOITools::GetCurrentDataTime(), 0 };

    entirety_board_result.project = CreateProjectInfo(project_param_ptr_);
    entirety_board_result.board_info = CreateBoardInfo(detect_result_param_, project_param_ptr_);
    entirety_board_result.board_info.machine_id = "jrs_aoi_20";

    if (detect_result_param_->board_detect_status == BoardDetectStatus::NORMAL)
    {
        auto base_path = GetSavePath(setting_params_);
        if (base_path.empty())
        {
            base_path = "D:/AOI/Report/";
        }
        auto [subboard_and_devices, algo_images, component_images, entirety_board_images] = GroupDevicesBySubboard(detect_result_param_, project_param_ptr_, base_path);

        entirety_board_result.subboards = CreateSubboardInfo(project_param_ptr_, detect_result_param_,
            entirety_board_result.board_info.board_masked_subboards, subboard_and_devices);
        board_detection_assets.component_images = component_images;
        board_detection_assets.algorithm_images = algo_images;
        board_detection_assets.entirety_board_images = entirety_board_images;

        GetBoardStatisticsParam(entirety_board_result);

        UpdateDBMachineStatisticsByLocal(entirety_board_result);
    }
    else
    {
        entirety_board_result.board_info.board_detect_result = jrsdata::DetectResult::SKIP;
    }

    board_detection_assets.entirety_board_result = entirety_board_result;
    board_detection_assets.ng_statistics = _ng_statistics;
    /** <获取 检测分析的数据 */
    board_detection_assets.detect_statistics_param = GetDetectStatisticsParam(entirety_board_result, detect_result_param_);

    return board_detection_assets;
}

int jrsdata::ParamConvertor::ClearStatisticsData(const std::string& project_name_)
{
    // 忽略传入的项目名称（未使用）
    // TODO: 从QT转std::string为乱码，需进一步研究：HJC 2025/2/24
    (void)project_name_;

    // 创建一个临时的整板结果对象
    jrsdata::EntiretyBoardResult temp_entirety_board_result;
    // 根据本地数据更新机器统计信息
    UpdateDBMachineBoardDataByLocal(temp_entirety_board_result);
    // 将更新后的机器统计信息替换到数据库中
    _db_manager_ptr->Replace(temp_entirety_board_result.aoi_machien_statistics);

    // 返回操作成功状态
    return jrscore::AOI_OK;
}

int jrsdata::ParamConvertor::ClearDevicesData(const std::string& project_name_)
{
    // 忽略传入的项目名称（未使用）
    (void)project_name_;

    // 清空NG组件统计信息
    _ng_statistics.component_ng_statistics.clear();
    // 清空缺陷类型统计信息
    _ng_statistics.flaw_type_statistics.clear();
    // 重置NG组件数量为0
    _ng_statistics.ng_compoent_number = 0;

    // 创建一个临时的整板结果对象
    jrsdata::EntiretyBoardResult temp_entirety_board_result;
    // 根据本地数据更新机器统计信息
    UpdateDBMachineStatisticsByLocal(temp_entirety_board_result);
    // 将更新后的机器统计信息替换到数据库中
    _db_manager_ptr->Replace(temp_entirety_board_result.aoi_machien_statistics);

    // 返回操作成功状态
    return jrscore::AOI_OK;
}
jrsdatabase::jrstable::TBoard jrsdata::ParamConvertor::CreateBoardInfo(const jrsdata::DetectResultParamPtr& detect_result_param_, const jrsdata::ProjectParamPtr& project_param_ptr)
{
    jrsdatabase::jrstable::TBoard board_info;
    board_info.board_barcode = detect_result_param_->board_code;
    board_info.board_start_detect_time = detect_result_param_->start_detect_time;
    board_info.board_end_detect_time = detect_result_param_->finish_detect_time.empty() ? jrscore::AOITools::GetCurrentDataTime() : detect_result_param_->finish_detect_time;
    const auto& board_project = project_param_ptr->board_info;
    board_info.layout = board_project.layout;
    board_info.material = board_project.material;
    board_info.num_sub_board = board_project.num_sub_board;
    board_info.subboard_cols = board_project.cols;
    board_info.subboard_rows = board_project.rows;
    board_info.board_rejudgment_time = jrscore::AOITools::GetCurrentDataTime();
    board_info.project_name = project_param_ptr->project_name;
    board_info.board_masked_subboards = 0;
    board_info.track_id = detect_result_param_->track_id;
    return board_info;
}

jrsdatabase::jrstable::TProject jrsdata::ParamConvertor::CreateProjectInfo(const jrsdata::ProjectParamPtr& project_param_ptr)
{
    jrsdatabase::jrstable::TProject project_info;
    project_info.project_name = project_param_ptr->project_name;
    GetWorkOrderInfomationByProjectName(project_info.project_name, project_info.work_order_information);
    return project_info;
}

std::string jrsdata::ParamConvertor::GenerateFilePath(const std::string& base_path_, const std::string& sub_folder_, const std::string& file_name_)
{
    std::string full_path = base_path_ + sub_folder_;
    //jtools::FileOperation::JRSCreateDirectory(full_path);
    return full_path + file_name_;
}
std::string jrsdata::ParamConvertor::AddDeviceImage(const jrsdata::DeviceResult& device_, const std::string& device_path,
    jrsdata::ComponentImageMap& component_image_map_, jrsdata::NameImageMap& pass_map_, jrsdata::NameImageMap& ng_map_)
{
    //! 元件图片名称= 元件名_元件id_灯光id_
    std::string all_img_path_to_db = "";
    std::string device_image_name = device_.t_device.device_name + "_";
    std::string full_path = "";

    for (auto& map_detect_device_image : device_.device_images)
    {
        //device_image_name.append(std::to_string(map_detect_device_image.first)+"_");
        std::string device_image_full_name = device_image_name + std::to_string(map_detect_device_image.first);
        if (static_cast<jrsdata::LightImageType>(map_detect_device_image.first) == jrsdata::LightImageType::HEIGHT)
        {
            device_image_full_name += ".tiff";
        }
        else
        {
            device_image_full_name += ".jpg";
        }
        full_path = device_path + device_image_full_name;

        if (device_.t_device.device_result)
        {
            pass_map_[full_path] = map_detect_device_image.second;
            component_image_map_[true] = pass_map_;
        }
        else
        {
            ng_map_[full_path] = map_detect_device_image.second;
            component_image_map_[false] = ng_map_;
        }
        all_img_path_to_db.append(full_path + ";");
    }
    return all_img_path_to_db;
}

void jrsdata::ParamConvertor::ProcessDetectWindows(const jrsdata::DeviceResult& device_, const std::string& base_path_,
    const jrsdata::ProjectParamPtr& project_param_ptr_, jrsdata::AlgorithmImageMap& algo_type_image_map_) {
    static std::string previous_window_name = "";
    static std::shared_ptr<jrsdata::NameImageMap> algo_image_map = std::make_shared<jrsdata::NameImageMap>();

    for (const auto& group : device_.groups)
    {
        for (const auto& detect_window : group.detect_windows)
        {
            const auto& window_name = detect_window.t_detect_window.detect_window_name;
            if (previous_window_name != window_name)
            {
                algo_image_map = std::make_shared<jrsdata::NameImageMap>();
                previous_window_name = window_name;
            }
            std::string algo_image_path = GenerateFilePath(base_path_, "algo_image/" + window_name + "/", "");
            std::string algo_image_name = window_name + "_" +
                std::to_string(device_.t_device.subboard_id) + "_" +
                std::to_string(detect_window.t_detect_window.device_id) + "_" +
                detect_window.t_detect_window.algorithm_name + ".png";
            algo_image_map->emplace(algo_image_path + algo_image_name, detect_window.algorithm_image);
            auto& temps = project_param_ptr_->temps;
            auto it = std::find_if(temps.begin(), temps.end(),
                [&](const jrsdata::Template& t) { return t.id == detect_window.template_image_id; });
            if (it != temps.end())
            {
                std::string template_image_name = window_name + "_" +
                    std::to_string(device_.t_device.subboard_id) + "_" +
                    std::to_string(detect_window.t_detect_window.device_id) + "_" +
                    detect_window.t_detect_window.algorithm_name +
                    "_template.png";
                algo_image_map->emplace(algo_image_path + template_image_name, it->GetMatImage());
            }
            algo_type_image_map_[window_name] = *algo_image_map;
        }
    }
}
std::tuple<jrsdata::SubboardIDAndDevices, jrsdata::AlgorithmImageMap,
    jrsdata::ComponentImageMap, jrsdata::EntiretyBoardImages>
    jrsdata::ParamConvertor::GroupDevicesBySubboard(const jrsdata::DetectResultParamPtr& detect_result_param_,
        const jrsdata::ProjectParamPtr& project_param_ptr_, const std::string& image_path_)
{
    ComponentImageMap component_image_map;
    AlgorithmImageMap algo_type_image_map;
    NameImageMap device_image_pass_map;
    NameImageMap device_image_ng_map;
    SubboardIDAndDevices subboard_devices;
    std::string board_detect_result_path =
        image_path_ + "/" + project_param_ptr_->project_name + "/" +
        (detect_result_param_->detect_result ? "GOOD_" : "NG_") + detect_result_param_->board_code +
        "_" + jrscore::AOITools::GetCurrentDataTime("%Y%m%d%H%M%S") + "/";
    auto entirety_board_images = GetEntiretyBoardImages(detect_result_param_, board_detect_result_path);

    for (auto& device : detect_result_param_->component_result_vector) {
        std::string ng_or_pass_str = (device.t_device.device_result == jrsdata::DetectResult::GOOD ? "GOOD/" : "NG/");
        std::string device_path = GenerateFilePath(board_detect_result_path, "component_image/" + ng_or_pass_str, "");
        device.t_device.device_img_path = AddDeviceImage(device, device_path, component_image_map, device_image_pass_map, device_image_ng_map);

        /** <保存所有数据 还是只保存ng数据*/
        auto is_save_pass_value = true;
        if (_param_process_ptr)
        {
            is_save_pass_value = _param_process_ptr->GetSettingParamValueByName<bool>(ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL);
        }
        if (is_save_pass_value || !device.t_device.device_result)  // 保存所有或仅保存NG
        {
            subboard_devices[device.t_device.subboard_id].push_back(device);
        }

        if (device.t_device.device_result == jrsdata::DetectResult::GOOD)
        {
            ++_current_machine_statistics->component_pass_number;
        }
        else
        {
            ++_current_machine_statistics->component_ng_number;
            auto& count = _ng_statistics.component_ng_statistics[device.t_device.device_name];
            count++;
            std::vector<std::string> split_flaw_type_names = jrscore::AOITools::SplitString(device.t_device.device_detect_flaw_type_names, ';');
            if (split_flaw_type_names.size() > 0)
            {
                _ng_statistics.flaw_type_statistics[split_flaw_type_names.at(0)]++;//统计错误类型，目前逻辑只统计第一个NG原因
            }
            ++_ng_statistics.ng_compoent_number;
            /** <错误类型也在这里统计，不过需要映射，流程跑通后再添加 TODO：HJC 2025/2/14*/
        }

        //ProcessDetectWindows(device, board_detect_result_path, project_param_ptr_, algo_type_image_map);
    }

    return std::make_tuple(subboard_devices, algo_type_image_map, component_image_map, entirety_board_images);
}

std::vector<jrsdata::SubboardResult> jrsdata::ParamConvertor::CreateSubboardInfo(const jrsdata::ProjectParamPtr& project_param_ptr,
    const jrsdata::DetectResultParamPtr& detect_result_param_,
    int& board_masked_subboards,
    const std::unordered_map<int, std::vector<jrsdata::DeviceResult>>& subboard_devices)
{
    std::vector<jrsdata::SubboardResult> subboard_infos;
    const auto& board_project = project_param_ptr->board_info;

    for (auto& subboard : board_project.sub_board)
    {
        jrsdata::SubboardResult subboard_info;
        auto subboard_barcode_it = detect_result_param_->subboard_id_and_barcode.find(subboard.id);
        if (subboard_barcode_it != detect_result_param_->subboard_id_and_barcode.end())
        {
            subboard_info.t_subboard.subboard_barcode = subboard_barcode_it->second;
        }
        subboard_info.t_subboard.subboard_id = subboard.id;
        subboard_info.t_subboard.subboard_col = subboard.col;
        subboard_info.t_subboard.subboard_row = subboard.row;
        subboard_info.t_subboard.subboard_width = (int)subboard.width;
        subboard_info.t_subboard.subboard_height = (int)subboard.height;
        subboard_info.t_subboard.subboard_x = (int)subboard.x;
        subboard_info.t_subboard.subboard_y = (int)subboard.y;
        subboard_info.t_subboard.subboard_devices = static_cast<int>(subboard.component_info.size());
        subboard_info.t_subboard.subboard_no_judgment_devices = static_cast<int>(subboard.component_info.size());//未复判元件数 xixi
        subboard_info.t_subboard.subboard_is_detection = subboard.enable;
        subboard_info.t_subboard.subboard_detect_time = jrscore::AOITools::GetCurrentDataTime();
        subboard_info.t_subboard.subboard_rejudgment_time = jrscore::AOITools::GetCurrentDataTime();
        /**< 子板是否为坏板 */
        auto is_bad_it = detect_result_param_->subboard_bad_info.find(subboard_info.t_subboard.subboard_id);
        if (is_bad_it != detect_result_param_->subboard_bad_info.end() && is_bad_it->second)
        {
            subboard_info.t_subboard.subboard_result = jrsdata::DetectResult::SKIP;
        }

        if (!subboard.enable)
        {
            board_masked_subboards++;
        }

        auto device_it = subboard_devices.find(subboard_info.t_subboard.subboard_id);
        if (device_it != subboard_devices.end())
        {
            subboard_info.devices = device_it->second;
        }
        GetSubboardStatisticsParam(subboard_info);

        subboard_infos.push_back(subboard_info);
    }
    return subboard_infos;
}

void jrsdata::ParamConvertor::GenerateDeviceID(std::vector<jrsdata::DeviceResult>& device_result)
{
    int device_id = 0;
    for (auto& device : device_result)
    {
        device.t_device.device_id = device_id++;
    }
}

jrsdata::EntiretyBoardImages jrsdata::ParamConvertor::GetEntiretyBoardImages(const jrsdata::DetectResultParamPtr& detect_result_param_, const std::string& path_)
{
    jrsdata::EntiretyBoardImages entirety_board_imgs;
    for (const auto& device : detect_result_param_->entirety_board_imags)
    {
        std::string image_save_path = path_;
        image_save_path.append("/" + std::to_string(device.first) + ".png");
        entirety_board_imgs[image_save_path] = device.second;
    }
    return entirety_board_imgs;
}

std::string jrsdata::ParamConvertor::GetSavePath(jrsdata::AllSettingParamMap all_setting_param_map_)
{
    //获取保存路径
    const auto it = all_setting_param_map_.find(jrsdata::ParamLevel::MACHINE);
    if (it == all_setting_param_map_.end())
    {
        Log_ERROR("MACHINE 参数未找到");
        return "";
    }
    const auto& machine_param = it->second;
    auto detect_result_it = machine_param.find(jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH);
    if (detect_result_it == machine_param.end())
    {
        Log_ERROR("检测结果路径未找到");
        return "";
    }
    auto detect_result_path = detect_result_it->second;
    auto detect_result_base_path = jrscore::ParseJson(detect_result_path.param_value, "directory");
    return detect_result_base_path;
}
//TODO: xixi
jrsdata::DetectStatisticsViewParam jrsdata::ParamConvertor::GetDetectStatisticsParam(const jrsdata::EntiretyBoardResult& entirety_board_result_, const jrsdata::DetectResultParamPtr& detect_result_param_)
{
    jrsdata::DetectStatisticsViewParam detect_statistics_param;
    DetectResultViewParam detect_result_param;
    DetectTableParam detect_table_param_board;
    DetectTableParamVec detect_table_param_subboard = DetectTableParamVec();
    long total_subboard_count = 0;//当前检测的子板累计元件总数
    long detect_component_count = 0;//当前检测的子板累计检测元件总数
    long ng_component_count = 0;//当前检测的子板累计NG元件总数
    long ok_component_count = 0;//当前检测的子板累计PASS元件总数

    /** <整板子判断*/
    if (detect_result_param_->board_detect_status == BoardDetectStatus::NORMAL)
    {
        if (entirety_board_result_.board_info.board_detect_result)
        {
            detect_table_param_board.detect_result = jrsdata::DetectResult::GOOD;
        }
        else
        {
            detect_table_param_board.detect_result = jrsdata::DetectResult::NG;
        }
    }
    else
    {
        detect_table_param_board.detect_result = jrsdata::DetectResult::SKIP;
    }

    for (auto sub : entirety_board_result_.subboards)
    {
        total_subboard_count += sub.t_subboard.subboard_devices;
        detect_component_count += sub.t_subboard.subboard_detect_devices;
        ng_component_count += sub.t_subboard.subboard_ng_devices;
        ok_component_count += sub.t_subboard.subboard_pass_devices;
        DetectTableParam one_sub_result;
        one_sub_result.id = sub.t_subboard.subboard_id;
        one_sub_result.track_id = entirety_board_result_.board_info.track_id;

        if (detect_table_param_board.detect_result == jrsdata::DetectResult::SKIP)
        {
            one_sub_result.detect_result = jrsdata::DetectResult::SKIP;
        }
        else
        {
            if (sub.t_subboard.subboard_result == jrsdata::DetectResult::GOOD)
            {
                one_sub_result.detect_result = jrsdata::DetectResult::GOOD;
            }
            else
            {
                one_sub_result.detect_result = jrsdata::DetectResult::NG;
            }

        }

        one_sub_result.main_barcode = sub.t_subboard.subboard_barcode;
        one_sub_result.detect_time = sub.t_subboard.subboard_detect_time;
        detect_table_param_subboard.push_back(one_sub_result);
    }
    detect_statistics_param.detect_table_param_subboard = detect_table_param_subboard;

    detect_result_param.project_name = entirety_board_result_.project.project_name;
    detect_result_param.worker_num = entirety_board_result_.project.work_order_information;
    detect_result_param.total_component_num = total_subboard_count;// entirety_board_result_.aoi_machien_statistics.component_ng_number + entirety_board_result_.aoi_machien_statistics.component_pass_number;
    detect_result_param.detect_component_num = detect_component_count;// entirety_board_result_.aoi_machien_statistics.component_ng_number + entirety_board_result_.aoi_machien_statistics.component_pass_number;
    detect_result_param.ng_component_num = ng_component_count;// entirety_board_result_.aoi_machien_statistics.component_ng_number;
    detect_result_param.ok_component_num = ok_component_count;// entirety_board_result_.aoi_machien_statistics.component_pass_number;
    /**< 其他字段赋值 */
    detect_table_param_board.id = entirety_board_result_.board_info.board_id;
    detect_table_param_board.main_barcode = entirety_board_result_.board_info.board_barcode;


    detect_table_param_board.track_id = entirety_board_result_.board_info.track_id;
    detect_table_param_board.detect_time = entirety_board_result_.board_info.board_end_detect_time;

    detect_statistics_param.detect_table_param_board = detect_table_param_board;

    /**< 直通率赋值 */
    DetectResultRatioParam detect_result_ratio;
    detect_result_ratio.board_ng_number = entirety_board_result_.aoi_machien_statistics.board_ng_number;
    detect_result_ratio.board_pass_number = entirety_board_result_.aoi_machien_statistics.board_pass_number;
    detect_result_ratio.board_misjudge_number = entirety_board_result_.aoi_machien_statistics.board_misjudge_number;
    detect_result_ratio.subboard_pass_number = entirety_board_result_.aoi_machien_statistics.subboard_pass_number;
    detect_result_ratio.subboard_ng_number = entirety_board_result_.aoi_machien_statistics.subboard_ng_number;
    detect_result_ratio.subboard_misjudge_number = entirety_board_result_.aoi_machien_statistics.subboard_misjudge_number;
    detect_result_ratio.component_ng_number = entirety_board_result_.aoi_machien_statistics.component_ng_number;
    detect_result_ratio.component_pass_number = entirety_board_result_.aoi_machien_statistics.component_pass_number;
    detect_result_ratio.component_misjudge_number = entirety_board_result_.aoi_machien_statistics.component_misjudge_number;
    detect_statistics_param.detect_result_ratio_param = detect_result_ratio;//chenxixi 添加赋值

    /**< ng 元件 赋值  */
    DetectResultTypeParamVec detect_ng_component_params;
    _ng_statistics.ng_compoent_number = _ng_statistics.ng_compoent_number == 0 ? 1 : _ng_statistics.ng_compoent_number;
    for (auto ng_data : _ng_statistics.component_ng_statistics)
    {
        DetectResultTypeParam detect_result;
        detect_result.name = ng_data.first;
        detect_result.count = ng_data.second;
        detect_result.percent = detect_result.count / _ng_statistics.ng_compoent_number;
        detect_ng_component_params.push_back(detect_result);
    }
    /**< ng 类型赋值 */
    DetectResultTypeParamVec detect_result_type_params;
    for (auto ng_data : _ng_statistics.flaw_type_statistics)
    {
        DetectResultTypeParam detect_result;
        detect_result.name = ng_data.first;
        detect_result.count = ng_data.second;
        detect_result.percent = detect_result.count / _ng_statistics.ng_compoent_number;
        detect_result_type_params.push_back(detect_result);
    }
    detect_statistics_param.detect_result_device_name_param_vec = detect_ng_component_params;
    detect_statistics_param.detect_result_device_type_param_vec = detect_result_type_params;

    //TODO: 需要耗时赋值：
    std::string detect_entirety_board_take_times = jrscore::AOITools::CalculateTimeDifference(entirety_board_result_.board_info.board_start_detect_time,
        entirety_board_result_.board_info.board_end_detect_time);
    detect_result_param.loop_take_time = detect_entirety_board_take_times;//! 整板子检测时间

    detect_result_param.photograph_take_time = detect_result_param_->one_fov_take_time;//entirety_board_result_.aoi_sys.one_fov_take_time;//单个FOV时间
    detect_statistics_param.detect_result_param = detect_result_param;

    /** <获取当前mark 数据*/
    for (auto& value : detect_result_param_->workflow_component_algo_info_vector)
    {
        if (value->algo_name.find("Mark") != std::string::npos)
        {
            auto& mark_imgs = detect_statistics_param.detect_result_param->mark_imgs;
            mark_imgs[value->component_name] = value->input_img;//value->result_img;//
        }
    }
    detect_statistics_param.detect_result_param->mark_status = int(detect_result_param_->board_detect_status);
    detect_statistics_param.detect_result_param->mark_results.clear();
    for (auto& value : detect_result_param_->component_status_result)
    {
        std::string component_name_upper = jtools::StringOperation::StringToUpper(value.component_name);
        if (component_name_upper.find("MARK") != std::string::npos)
        {
            detect_statistics_param.detect_result_param->mark_results.push_back(value);
        }
    }
    return detect_statistics_param;
}

void jrsdata::ParamConvertor::GetSubboardStatisticsParam(jrsdata::SubboardResult& subboard_)
{
    subboard_.t_subboard.subboard_ng_devices = 0;
    subboard_.t_subboard.subboard_no_detect_devices = 0;
    for (const auto& device : subboard_.devices)
    {
        if (device.t_device.device_result == jrsdata::DetectResult::NG)
        {
            ++subboard_.t_subboard.subboard_ng_devices;
        }
        if (!device.t_device.device_is_detection)
        {
            ++subboard_.t_subboard.subboard_no_detect_devices;
        }
    }
    if (subboard_.t_subboard.subboard_result != jrsdata::DetectResult::SKIP)
        subboard_.t_subboard.subboard_result = (subboard_.t_subboard.subboard_ng_devices == 0) ? jrsdata::DetectResult::GOOD : jrsdata::DetectResult::NG;
    subboard_.t_subboard.subboard_pass_devices = subboard_.t_subboard.subboard_devices - subboard_.t_subboard.subboard_ng_devices - subboard_.t_subboard.subboard_no_detect_devices;
    subboard_.t_subboard.subboard_detect_devices = subboard_.t_subboard.subboard_devices - subboard_.t_subboard.subboard_no_detect_devices;
    subboard_.t_subboard.subboard_is_detection = (subboard_.t_subboard.subboard_detect_devices != 0);

    if (subboard_.t_subboard.subboard_result == jrsdata::DetectResult::GOOD)
    {
        ++_current_machine_statistics->subboard_pass_number;
    }
    else if (subboard_.t_subboard.subboard_result == jrsdata::DetectResult::NG)
    {
        ++_current_machine_statistics->subboard_ng_number;
    }
}

void jrsdata::ParamConvertor::GetBoardStatisticsParam(jrsdata::EntiretyBoardResult& board_)
{
    auto& board = board_.board_info;
    board.board_devices = 0;
    board.num_sub_board = (int)board_.subboards.size();

    board.board_detect_result = true;
    board.board_no_judgment_subboards = (int)(board_.subboards.size());//未复判子板数 xixi
    for (const auto& subboard : board_.subboards)
    {
        board.board_devices += subboard.t_subboard.subboard_devices;
        board.board_ng_devices += subboard.t_subboard.subboard_ng_devices;
        board.board_detected_devices += subboard.t_subboard.subboard_detect_devices;
        //board.board_masked_subboard_devices += subboard.t_subboard.subboard_no_detect_devices;
        board.board_no_detected_devices += subboard.t_subboard.subboard_no_detect_devices;
        if (!subboard.t_subboard.subboard_is_detection)
        {
            ++board.board_masked_subboards;
        }
    }
    board.board_detect_result = static_cast<jrsdata::DetectResult>(board.board_ng_devices == 0);

    if (board.board_detect_result == jrsdata::DetectResult::GOOD)
    {
        ++_current_machine_statistics->board_pass_number;
    }
    else if (board.board_detect_result == jrsdata::DetectResult::NG)
    {
        ++_current_machine_statistics->board_ng_number;
    }
}

void jrsdata::ParamConvertor::UpdateLocalMachineStatisticsByDB(const std::string& machine_id_, const std::string& project_name_)
{
    /** 可开线程获取，节省时间  更新统计数据*/
    _current_machine_statistics = GetOrCreateMachineStatisicsData(machine_id_, project_name_);
    try
    {
        iguana::from_json(_ng_statistics, _current_machine_statistics->ng_json_data);
    }
    catch (const std::exception&)
    {
        Log_ERROR("更新统计数据失败，请检查字符串是否被篡改。");
    }
}

void jrsdata::ParamConvertor::UpdateDBMachineStatisticsByLocal(jrsdata::EntiretyBoardResult& entirety_result_)
{
    /**< 统计赋值 */
    std::string ng_statistics_str = "";
    iguana::to_json(_ng_statistics, ng_statistics_str);
    _current_machine_statistics->ng_json_data = ng_statistics_str;
    entirety_result_.aoi_machien_statistics = _current_machine_statistics.value();
}

void jrsdata::ParamConvertor::UpdateDBMachineBoardDataByLocal(jrsdata::EntiretyBoardResult& entirety_result_)
{
    _current_machine_statistics->board_misjudge_number = 0;
    _current_machine_statistics->board_ng_number = 0;
    _current_machine_statistics->board_pass_number = 0;
    _current_machine_statistics->subboard_misjudge_number = 0;
    _current_machine_statistics->subboard_ng_number = 0;
    _current_machine_statistics->subboard_pass_number = 0;
    _current_machine_statistics->component_misjudge_number = 0;
    _current_machine_statistics->component_ng_number = 0;
    _current_machine_statistics->component_pass_number = 0;
    entirety_result_.aoi_machien_statistics = _current_machine_statistics.value();
}

jrsdatabase::jrstable::TAOIMachineStatistics jrsdata::ParamConvertor::GetOrCreateMachineStatisicsData(const std::string& machine_id_, const std::string& project_name_)
{
    jrsdatabase::jrsselect::SelectorParamBasePtr select_table_ptr = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
    select_table_ptr->table_name = jrsdatabase::jrstable::T_AOI_MACHINE_STATISTICS;
    select_table_ptr->select_name = jrsdatabase::jrsselect::T_AOI_MACHINE_STATISTICS_SELECT_BY_WHERE_CONDITION;
    select_table_ptr->where_condition = "machine_id='" + machine_id_ + "' and project_name='" + project_name_ + "'";
    auto res = _db_manager_ptr->Select(select_table_ptr);
    auto select = std::static_pointer_cast<jrsdatabase::jrsselect::SelectTable>(select_table_ptr);
    if (select->machine_statisticss.has_value() && !select->machine_statisticss->empty() && res == jrscore::AOI_OK)
    {
        return select->machine_statisticss->front();
    }
    else
    {
        jrsdatabase::jrstable::TAOIMachineStatistics aoi_machine_statistics;
        aoi_machine_statistics.project_name = project_name_;
        aoi_machine_statistics.machine_id = machine_id_;
        res = _db_manager_ptr->Insert<jrsdatabase::jrstable::TAOIMachineStatistics>(aoi_machine_statistics);
        return (res == jrscore::AOI_OK) ? aoi_machine_statistics : jrsdatabase::jrstable::TAOIMachineStatistics{};
    }
}

std::optional<jrsdatabase::jrstable::TAOIMachine> jrsdata::ParamConvertor::GetMachineDataFromDatabase(const std::string& machine_id_)
{
    jrsdatabase::jrsselect::SelectorParamBasePtr select_table_ptr = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
    select_table_ptr->table_name = jrsdatabase::jrstable::T_AOI_MACHINE;
    select_table_ptr->select_name = jrsdatabase::jrsselect::T_AOI_MACHINE_SELECT_BY_WHERE_CONDITION;
    select_table_ptr->where_condition = "machine_id='" + machine_id_ + "'";
    auto res = _db_manager_ptr->Select(select_table_ptr);
    auto select = std::static_pointer_cast<jrsdatabase::jrsselect::SelectTable>(select_table_ptr);
    if (select->aoi_machines.has_value() && !select->aoi_machines->empty() && res == jrscore::AOI_OK)
    {
        return select->aoi_machines->front();
    }
    return std::nullopt; // 返回空值表示未找到对应的机器数据

}

int jrsdata::ParamConvertor::GetWorkOrderInfomationByProjectName(const std::string& project_name_, std::string& work_order_information)
{
    jrsdatabase::jrsselect::SelectorParamBasePtr select_table_ptr = std::make_shared<jrsdatabase::jrsselect::SelectTable>();
    select_table_ptr->table_name = jrsdatabase::jrstable::T_PROJECT;
    select_table_ptr->select_name = jrsdatabase::jrsselect::T_PROJECT_SELECT_BY_WHERE_CONDITION;
    select_table_ptr->where_condition = " project_name='" + project_name_ + "'";
    auto res = _db_manager_ptr->Select(select_table_ptr);
    auto select = std::static_pointer_cast<jrsdatabase::jrsselect::SelectTable>(select_table_ptr);
    if (select->projects.has_value() && res == jrscore::AOI_OK)
    {
        if (select->projects.value().size() > 0)
        {
            work_order_information = select->projects.value().at(0).work_order_information;
        }
    }
    return jrscore::Ok;
}