#include "trackflow.h"
#include "devicemanager.h"
namespace jrsworkflow
{

    TrackFlow::TrackFlow ( const std::shared_ptr<jrsdevice::<PERSON>ceManager>& device_manager_ptr_ )
        : device_manager_ptr ( device_manager_ptr_ )
    {
    }
    TrackFlow::~TrackFlow ()
    {
    }
    int TrackFlow::LoadMaterial ( int rail_index )
    {
        //TODO 增加上料超时时间设置，这里设置无穷大 by baron_zhang 2024.12.11
        rail_index = 0;
        return 0;
    }
    int TrackFlow::UnloadMaterial ( int rail_index )
    { 
        //TODO 增加下料超时时间设置，这里设置无穷大 by baron_zhang 2024.12.11
        rail_index = 0;
        return 0;
    }
    std::string TrackFlow::GetTrackErrStr()
    {
        std::string err_str = "";
        //! 将返回出来的错误信息拼接成一个字符串，返回的是一个vector
        
        return err_str;

    }
    int TrackFlow::StopTrack ()
    {

        return 0;
    }
    void TrackFlow::Init ()
    {
    }
}
