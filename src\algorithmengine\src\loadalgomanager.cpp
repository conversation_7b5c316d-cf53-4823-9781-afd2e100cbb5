//Custom
#include "loadalgomanager.h"
#include "fileoperation.h"

namespace jrsalgo 
{
    LoadAlgoManager::LoadAlgoManager ( const AlgoFactoryPtr& algo_factory_ptr_ , const nlohmann::json& config_json_)
        : algo_factory_ptr ( algo_factory_ptr_ )
        , algo_config_json(config_json_)
    {
        InitMember ();

    }
    LoadAlgoManager::~LoadAlgoManager ()
    {
    }
    const std::map<std::string, std::string>& LoadAlgoManager::GetAlgoNameMap()
    {
        return algo_name_lists;
    }
  
    const std::map<std::string/*算法名称*/, std::map<std::string/*算法中规格名称*/, std::vector<std::string>/*软件中规格名称*/>>& LoadAlgoManager::GetAlgoSpecMap()
    {
        return algo_spec_param_map;
    }

    void LoadAlgoManager::InitMember ()
    {
        ParseOperatorPluginInfo();
        LoadAlgo ();

    }
    void LoadAlgoManager::ParseOperatorPluginInfo()
    {
        try
        {
            auto current_work_directory = jtools::FileOperation::GetCurrentWorkingDirectory ();
            /*std::filesystem::path config_path = current_work_directory + "/config/algo/algoplugin/loadinfo/algoinfo.json";
            auto json_data = jrscore::ReadJson ( config_path.string () );*/
            auto json_data_object = jrscore::GetSpeficJsonValue (algo_config_json, "/algo" );

            for (const auto& [key , value_json] : json_data_object.items ())
            {
                ProcessPluginEntry ( current_work_directory , value_json );
            }
        }
        catch (const std::exception& e)
        {
            Log_ERROR ( "Error parsing operator plugin paths: " ,e.what());
        }
    }
    void LoadAlgoManager::ProcessPluginEntry ( const std::string& current_work_directory , const nlohmann::json& value_json )
    {
        
        ParseAlgoPaths(current_work_directory, value_json);
        ParseSpecparamMap(value_json);
    }
    void LoadAlgoManager::ParseAlgoPaths(const std::string& current_work_directory, const nlohmann::json& value_json)
    {
        const std::string build_type = GetBuildType();

        if (value_json.contains("drive_path") && value_json.contains("drive_name"))
        {
            std::string drive_path = GeneratePath(current_work_directory, value_json["drive_path"], value_json["drive_name"], build_type);
            drive_paths.emplace_back(drive_path);
        }
        else
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE, "算法配置文件json解析异常，不存在drive_path字段或drive_name,请检查修复");
        }

        if (value_json.contains("view_path") && value_json.contains("view_name"))
        {
            std::string view_path = GeneratePath(current_work_directory, value_json["view_path"], value_json["view_name"], build_type);
            view_paths.emplace_back(view_path);
        }
        else
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE, "算法配置文件json解析异常，不存在view_path字段或view_name,请检查修复");
        }
        if (value_json.contains("name_en") && value_json.contains("name_zh"))
        {
            std::string_view name_en = value_json["name_en"];
            std::string_view name_zh = value_json["name_zh"];
            algo_name_lists.emplace(name_en, name_zh);
        }
        else
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE, "算法配置文件json解析异常，不存在name_en字段或name_zh,请检查修复");

        }
    }

    void LoadAlgoManager::ParseSpecparamMap(const nlohmann::json& value_json)
    {
        
        std::string spefic_path_config =  "/params";
        auto spefic_algo_param_json = jrscore::GetSpeficJsonValue(value_json, spefic_path_config);
        if (spefic_algo_param_json.empty() )
        {
            return;
        }
        std::string_view name_en;
        if (value_json.contains("name_en"))
        {
           name_en = value_json["name_en"];
  
        }
        else
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE, "算法配置文件json解析异常，不存在name_en字段或name_zh,请检查修复");

        }
        auto algo_spec_param_json = spefic_algo_param_json[0];
        //！获取规格json
        if (algo_spec_param_json.contains("spec_params"))
        {
            auto algo_spec_param_items = algo_spec_param_json["spec_params"][0].items();
            std::map<std::string, std::vector<std::string>> single_algo_spec_param_map;
            for (const auto& [key, value] : algo_spec_param_items)
            {
                if (single_algo_spec_param_map.find(value) == single_algo_spec_param_map.end())
                {
                    single_algo_spec_param_map[value] = std::vector<std::string>();
                }
                single_algo_spec_param_map[value].push_back(key);
            }
            algo_spec_param_map.emplace(name_en, single_algo_spec_param_map);
        }
        else
        {
            PushErrorToStack(jrscore::AlgorithmError::E_AOI_ALG_PARSE_JSON_FAILURE, "算法配置文件json解析异常，不存在spec_params字段，请检查修复");
        }
        
    }


    std::string LoadAlgoManager::GeneratePath ( const std::string& base_path , const std::string sub_path , const std::string& name , const std::string& build_type )
    {
        std::filesystem::path full_path = base_path + "\\config\\" + sub_path + build_type + "\\" + name;
        return full_path.string ();
    }
    std::string LoadAlgoManager::GetBuildType () const
    {
        #ifdef _DEBUG
        return "debug";
        #else
        return "release";
        #endif
    }
    void LoadAlgoManager::LoadAlgo()
    {
        int res;
        res = algo_factory_ptr->LoadAlgoDrives(drive_paths);
        res = algo_factory_ptr->LoadAlgoViews(view_paths);
        if (res != jrscore::AOI_OK)
        {
            PushErrorToStack(res, "加载算失败！");
            JRSMessageBox_ERR("算子模块","算子加载失败，请查看算子配置！",jrscore::MessageButton::Ok);
            return;
        }
        algo_factory_ptr->BindViewAndDrive();
    }
        

}
