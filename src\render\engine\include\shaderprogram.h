/*********************************************************************
 * @brief  GL着色器程序封装类.
 *
 * @file   shaderprogram.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef SHADERPROGRAM_0708_H
#define SHADERPROGRAM_0708_H
 //#include "renderconstants.hpp" // ProgramType
#include "engineconstants.hpp" // ProgramType
// #include "windowinterface.h" // ROpenGLFunctions
#include "ropenglfunctions.hpp" // ROpenGLFunctions
#include <unordered_map>
#include <gl/GL.h>

class QOpenGLShaderProgram;
/**
 * @brief 着色器程序封装类
 * 该类用于管理OpenGL着色器程序的创建、绑定和资源管理
 * <AUTHOR>
 */
class ShaderProgram
{
    /**
     * @brief 顶点属性结构体
     * 该结构体包含顶点属性的布局、大小、类型、步幅和偏移量
     * <AUTHOR>
     */
    struct VertexAttribute
    {
        int layout; ///< 属性布局
        int size;   ///< 属性大小
        int type;   ///< 属性类型
        int stride; ///< 步幅
        int offset; ///< 偏移量
    };

public:
    ShaderProgram(ProgramType type);
    ~ShaderProgram();

    void bind();
    void release();
    /**
     * @brief 创建着色器程序
     *
     * @param vname 顶点着色器名称
     * @param fname 片段着色器名称
     * @param gname 几何着色器名称（可选）
     * @return 是否创建成功
     *   @retval true 创建成功
     *   @retval false 创建失败
     * <AUTHOR>
     */
    bool CreateProgram(const char* vname, const char* fname, const char* gname = nullptr);

    /**
     * @brief 获取着色器程序类型
     *
     * @return 着色器程序类型的引用
     * <AUTHOR>
     */
    const ProgramType& GetProgramType() const { return m_program_type; }

    /**
     * @brief 将顶点数据绑定到GPU
     *
     * @param function OpenGL函数对象
     * @param data 顶点数据
     * <AUTHOR>
     */
    void BindVertexDataToGPU(ROpenGLFunctions* function, void* data);

    /**
     * @brief 解除绑定
     *
     * @param function OpenGL函数对象
     * @note 不进行属性的解绑,会出现一些未知异常
     * <AUTHOR>
     */
    void UnbindVertexDataFromGPU(ROpenGLFunctions* function);

    /**
     * @brief 设置顶点属性（整型）
     *
     * @param location 属性位置
     * @param offset 属性偏移
     * @tparam Type 类型模板参数
     * <AUTHOR>
     */
    template <class Type>
    void SetVertexAttributes(int location, int* offset)
    {
        SetVertexAttribute(location, 1, GL_INT, (int)offset, sizeof(Type));
    }

    /**
     * @brief 设置顶点属性（浮点型）
     *
     * @param location 属性位置
     * @param offset 属性偏移
     * @tparam Type 类型模板参数
     * <AUTHOR>
     */
    template <class Type>
    void SetVertexAttributes(int location, float* offset)
    {
        SetVertexAttribute(location, 1, GL_FLOAT, (int)offset, sizeof(Type));
    }

    /**
     * @brief 设置顶点属性
     *
     * @param location 属性位置
     * @param size 属性大小
     * @param type 属性类型
     * @param offset 属性偏移
     * @param stride 步幅
     * <AUTHOR>
     */
    void SetVertexAttribute(int location, int size, int type, int offset, int stride);

    /**
     * @brief 设置Uniform值
     *
     * @param name Uniform名称
     * @param args 参数
     * @tparam Args 参数模板参数包
     * <AUTHOR>
     */
    template <typename... Args>
    void SetUniformValue(const char* name, Args... args)
    {
        qt_shader->setUniformValue(name, std::forward<Args>(args)...);
    }

private:
    QOpenGLShaderProgram* qt_shader;                        ///< OpenGL着色器程序指针
    ProgramType m_program_type;                             ///< 着色器程序类型
    static const int max_vertex_attribute = 8;              ///< 最大顶点属性数
    VertexAttribute vertexAttributes[max_vertex_attribute]; ///< 顶点属性数组
    int nVertexAttributeCount;                              ///< 顶点属性数量
};

class ShaderProgramManager
{
public:
    ShaderProgramManager();
    ~ShaderProgramManager();

    /**
     * @brief 获取着色器程序
     *
     * @param type 着色器程序类型
     * @return 指向相应着色器程序的指针
     * <AUTHOR>
     */
    ShaderProgram* GetProgram(const ProgramType& type);

private:
    // 禁止拷贝和赋值
    ShaderProgramManager(const ShaderProgramManager&) = delete;
    ShaderProgramManager& operator=(const ShaderProgramManager&) = delete;
    ShaderProgramManager(ShaderProgramManager&&) = delete;
    ShaderProgramManager& operator=(ShaderProgramManager&&) = delete;

private:
    std::unordered_map<int, ShaderProgram*> map_program; ///< 着色器程序映射表
};

/**
 * @brief 标准着色器程序类
 * 该类使用单例模式管理标准着色器程序对象
 * <AUTHOR>
 */
class StandardProgram : public ShaderProgramManager
{
public:
    /**
     * @brief 获取单例实例
     *
     * @return 标准着色器程序类的唯一实例
     * <AUTHOR>
     */
    static StandardProgram& GetInstance();

private:
    StandardProgram();
    ~StandardProgram();

    // 禁止拷贝和赋值
    StandardProgram(const StandardProgram&) = delete;
    StandardProgram& operator=(const StandardProgram&) = delete;
    StandardProgram(StandardProgram&&) = delete;
    StandardProgram& operator=(StandardProgram&&) = delete;
};

#endif //!SHADERPROGRAM_0708_H