/*****************************************************************//**
 * @file   modulehandle.h
 * @brief  
 * @details    
 * <AUTHOR>
 * @date 2024.1.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.22         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __MODULEHANDLE_H__
#define __MODULEHANDLE_H__

#include "callbackhelper.hpp"
#include "pubsubmanager.h"
#include "pluginexport.hpp"
namespace jrscore
{
    struct ModuleImplData;
    class JRS_AOI_PLUGIN_API ModuleHandle
    {
    public:
        explicit ModuleHandle(const std::string& module_name_);
        ModuleHandle () = delete;
        ~ModuleHandle ();
        inline int AddAdvertise(const std::string& topic_name_);
        template<typename... Args>
        inline int AddSubscriber (const std::string& topic_name_, const std::string& sub_name_, CallBackFunction<Args...> cb_ )
        {
      
            SubscribeCallbackHelperPtr helper = std::make_shared<SubScriberCallBackHelperImpl<Args...>>(cb_, module_name, topic_name_, sub_name_);
            return PubSubManager::GetInstance().AddSubscriber(module_name, topic_name_, helper);
        }
        template<typename... Args>
        int NotifyOne(const std::string& topic_name_, const std::string& sub_name_,Args... args)
        {
            std::vector<std::any> any_args = {std::forward<Args>(args)...}; 
            return PubSubManager::GetInstance().NotifyOne(module_name, topic_name_, sub_name_, any_args);

        }
        template<typename... Args>
        int NotifyAll(const std::string& topic_name_,Args... args)
        {   
            //std::lock_guard<std::recursive_mutex> lock ( pub_sub_mutex );
            std::vector<std::any> any_args = {std::forward<Args>(args)...}; 
            return PubSubManager::GetInstance().NotifyAll(module_name, topic_name_, any_args);

        }
        const std::string& GetMoudleName ();
    private:
        //ModuleImplData* p_data;
        std::string module_name;
        //std::recursive_mutex pub_sub_mutex;


    };
    using ModuleHandlePtr = std::shared_ptr<ModuleHandle>;


}

#endif // !__MODULEHANDLE_H__
