#!/bin/bash

echo "========================================"
echo "JRSAOI UML图表导出脚本"
echo "========================================"
echo

# 检查是否安装了mermaid-cli
if ! command -v mmdc &> /dev/null; then
    echo "错误: 未找到mermaid-cli工具"
    echo "请先安装: npm install -g @mermaid-js/mermaid-cli"
    echo
    exit 1
fi

# 创建images目录
mkdir -p images

echo "开始导出UML图表..."
echo

# 导出类图
echo "[1/4] 导出整体架构UML类图..."
if mmdc -i class-diagram.md -o images/class-diagram.png -w 1920 -H 1080; then
    echo "✓ 类图导出成功: images/class-diagram.png"
else
    echo "✗ 类图导出失败"
fi

# 导出时序图
echo "[2/4] 导出检测流程时序图..."
if mmdc -i sequence-diagram.md -o images/sequence-diagram.png -w 1920 -H 1080; then
    echo "✓ 时序图导出成功: images/sequence-diagram.png"
else
    echo "✗ 时序图导出失败"
fi

# 导出架构图
echo "[3/4] 导出系统分层架构图..."
if mmdc -i architecture-diagram.md -o images/architecture-diagram.png -w 1920 -H 1080; then
    echo "✓ 架构图导出成功: images/architecture-diagram.png"
else
    echo "✗ 架构图导出失败"
fi

# 导出ER图
echo "[4/4] 导出核心数据模型ER图..."
if mmdc -i er-diagram.md -o images/er-diagram.png -w 1920 -H 1080; then
    echo "✓ ER图导出成功: images/er-diagram.png"
else
    echo "✗ ER图导出失败"
fi

echo
echo "========================================"
echo "导出完成！"
echo "图片文件保存在 images/ 目录下"
echo "========================================"
echo

# 询问是否打开图片目录
read -p "是否打开图片目录? (y/n): " choice
case "$choice" in 
    y|Y ) 
        if command -v xdg-open &> /dev/null; then
            xdg-open images
        elif command -v open &> /dev/null; then
            open images
        else
            echo "请手动打开 images/ 目录查看图片"
        fi
        ;;
    * ) 
        echo "图片已保存在 images/ 目录"
        ;;
esac
