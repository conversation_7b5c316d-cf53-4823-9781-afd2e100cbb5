﻿#include "fileoperation.h"

#include "StringOperation.h"

#ifdef _WIN32
#undef UNICODE
#define WIN32_LEAN_AND_MEAN // 从 Windows 头文件中排除极少使用的内容
#define NOGDI               // 排除 宏ERROR
#include <windows.h>
#include <tchar.h>
#include <io.h>     //_access
#include <direct.h> //_mkdir
#include <ctime>
#else
#include <dirent.h>
#include <sys/stat.h>
#endif
#include <filesystem> //c++17
#include <regex>
#include <iostream>
#include <fstream>
#include <string>

#ifdef CreateDirectory
#undef CreateDirectory
#endif

namespace fs = std::filesystem;

bool jtools::FileOperation::IsValidPath(const std::string& path, bool exactPath)
{
    if (path.empty())
    {
        return false;
    }
    bool isValid = true;

    try
    {
        std::filesystem::path fullPath = std::filesystem::absolute(path);

        if (exactPath)
        {
            std::filesystem::path root = fullPath.root_path();
            isValid = !root.string().empty();
        }
        else
        {
            isValid = fullPath.is_absolute();
        }
    }
    catch (const std::exception&)
    {
        isValid = false;
    }

    return isValid;
}

bool jtools::FileOperation::IsPathExist(const char* path)
{
    struct stat st;
    return stat(path, &st) == 0;
}

bool jtools::FileOperation::IsDir(const std::string& path)
{
    return std::filesystem::is_directory(path);
    // #ifdef _WIN32
    //     DWORD attrib = GetFileAttributesA(path.c_str());
    //     return (attrib != INVALID_FILE_ATTRIBUTES && (attrib & FILE_ATTRIBUTE_DIRECTORY));
    // #else
    //     struct stat st;
    //     if (stat(path.c_str(), &st) == 0)
    //     {
    //         return S_ISDIR(st.st_mode);
    //     }
    //     return false;
    // #endif
}

bool jtools::FileOperation::IsFile(const std::string& path)
{
    std::filesystem::path p(path);
    return std::filesystem::is_regular_file(p);
}

bool jtools::FileOperation::IsVaildFileName(const std::string& path)
{
    if (path.empty())
    {
        return false;
    }

    // Windows下文件名中不能包含\/:*?"<>|这些字符
    std::regex invalidChars(R"([<>:"/\\|?*])");
    if (std::regex_search(path, invalidChars))
    {
        return false;
    }

    // 提取文件名
    std::string fileName = GetFileNameFromPath(path, false);

    std::string fileNameToUpper = StringOperation::StringToUpper(fileName);
    // 检查Windows保留名称（不区分大小写）
    static const std::vector<std::string> reservedNames = {
        "CON", "PRN", "AUX", "NUL",
        "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
        "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };

    for (const auto& reservedName : reservedNames)
    {
        if (reservedName == fileNameToUpper)
        {
            return false;
        }
    }
    return true;
}

bool jtools::FileOperation::IsFileExtensionSupport(const std::string& filePath, const std::string& strExtension)
{
    if (IsDir(filePath))
        return false;
    std::string fileExtension = GetFileNameAndExtension(filePath).second;
    if (fileExtension.empty())
        return false;
    StringOperation::StringToLower(fileExtension);
    size_t found = strExtension.find(fileExtension);
    return found != std::string::npos;
    return false;
}

bool jtools::FileOperation::JRSCreateDirectory(const std::string& path)
{
    try
    {
        fs::path fsPath(path);

        // 如果输入路径是一个文件路径，去除文件名
        if (fs::is_regular_file(fsPath))
        {
            fsPath.remove_filename();
        }
        if (!IsValidPath(path))
        {
            return false;
        }
        if (std::filesystem::exists(path)) {
            return true;
        }
        // if (!fs::is_directory(fsPath))
        // {
        //     return false;
        // }
        fs::create_directories(fsPath);
        return true;
    }
    catch (...)
    {
        return false;
    }
}

bool jtools::FileOperation::DeleteDirectory(const std::string& path)
{
    try
    {
        fs::remove_all(path);
    }
    catch (const std::exception&)
    {
        return false;
    }
    return true;
}

bool jtools::FileOperation::DeleteAllInDirectory(const std::string& path)
{
    try
    {
        // 确保路径存在并且是一个目录
        if (!std::filesystem::exists(path) || !std::filesystem::is_directory(path))
        {
            return false;
        }

        // 遍历目录下的所有文件和文件夹
        for (const auto& entry : std::filesystem::directory_iterator(path))
        {
            // 删除文件或文件夹
            std::filesystem::remove_all(entry.path());
        }
    }
    catch (...)
    {
        return false;
    }
    return true;
}

bool jtools::FileOperation::RemoveFile(const std::string& filepath)
{
    if (IsFile(filepath))
    {
        fs::remove(filepath); // 删除文件
    }
    return true;
}

bool jtools::FileOperation::CopyFileOrDirectory(const std::string& source_path, const std::string& destination_path)
{
    try
    {
        // 检查源文件或目录是否存在
        if (!fs::exists(source_path))
        {
            return false;
        }

        // 如果目标路径不存在，则创建
        // if (!fs::exists(destination_path))
        {
            JRSCreateDirectory(destination_path);
        }

        // 拷贝文件或目录
        fs::copy(source_path, destination_path, fs::copy_options::recursive | fs::copy_options::overwrite_existing);
    }
    catch (const std::exception&)
    {
        return false;
    }
    return true;
}

std::string jtools::FileOperation::GetCurrentWorkingDirectory()
{
    try
    {
        fs::path path = fs::current_path();
        return path.string();
    }
    catch (const std::exception&)
    {
        return "";
    }
}

bool jtools::FileOperation::GetTargetFilesInDir(std::vector<std::string>& vecTargetFiles, const std::string& path, const std::string& target, bool recursive)
{
    if (!IsDir(path))
    {
        return false;
    }

#ifdef _WIN32
    HANDLE hFind;
    WIN32_FIND_DATAA findFileData;
    std::string searchPath = path + "\\*.*";
    hFind = FindFirstFileA(searchPath.c_str(), &findFileData);
    if (hFind == INVALID_HANDLE_VALUE)
    {
        return false;
    }

    do
    {
        std::string fileName = findFileData.cFileName;
        if (strcmp(fileName.c_str(), ".") == 0 || strcmp(fileName.c_str(), "..") == 0)
        {
            continue;
        }

        if ((findFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) && recursive)
        {
            std::string subDirPath = path + "\\" + fileName;
            GetTargetFilesInDir(vecTargetFiles, subDirPath, target, recursive);
        }
        else
        {
            if (fileName.find(target) != std::string::npos)
            {
                vecTargetFiles.push_back(path + "\\" + fileName);
            }
        }
    } while (FindNextFileA(hFind, &findFileData));

    FindClose(hFind);
#else
    DIR* dir;
    struct dirent* entry;

    if ((dir = opendir(path.c_str())) == nullptr)
    {
        return false;
    }

    while ((entry = readdir(dir)) != nullptr)
    {
        std::string fileName = entry->d_name;
        if (fileName == "." || fileName == "..")
        {
            continue;
        }

        std::string filePath = path + "/" + fileName;
        if (isDir(filePath) && recursive)
        {
            getTargetFilesInDir(vecTargetFiles, filePath, target, recursive);
        }
        else
        {
            if (fileName.find(target) != std::string::npos)
            {
                vecTargetFiles.push_back(filePath);
            }
        }
    }

    closedir(dir);
#endif

    return true;
}

std::string jtools::FileOperation::GetFileNameFromPath(const std::string& filePath, bool isWithExtension, bool isExist)
{
    if (filePath.empty())
        return "";
    if (isExist && !IsFile(filePath))
        return "";

    size_t pos = filePath.find_last_of("/\\");
    // if (pos == std::string::npos)
    //     return ""; // 如果没有找到路径分隔符，则返回空字符串

    size_t dotPos = filePath.find_last_of('.');
    // if (dotPos == std::string::npos)
    // {
    //     return ""; // 如果没有找到文件扩展名，则返回空字符串
    // }
    if (isWithExtension)
    {
        return filePath.substr(pos + 1);
    }
    return filePath.substr(pos + 1, dotPos - pos - 1);
}

std::pair<std::string, std::string> jtools::FileOperation::GetFileNameAndExtension(const std::string& path)
{
    // 查找最后一个路径分隔符
    auto pos = path.find_last_of("/\\");

    // 获取文件名和扩展名
    std::string fileName = "";
    // 仅有文件名
    if (pos == std::string::npos)
    {
        fileName = path;
    }
    else
    {
        fileName = path.substr(pos + 1);
    }
    std::string extension = "";
    auto dotPos = fileName.find_last_of(".");
    if (dotPos != std::string::npos)
    {
        extension = fileName.substr(dotPos + 1);
        fileName = fileName.substr(0, dotPos);
    }

    // 返回结果
    return { fileName, extension };
}

bool jtools::FileOperation::RenameFileOrDirectory(const std::string& oldPath, const std::string& newPath)
{
    if (IsPathExist(newPath.c_str()) || !IsPathExist(oldPath.c_str()))
        return false;

    if (std::rename(oldPath.c_str(), newPath.c_str()) != 0)
    {
        // 如果 std::rename 返回非0值，说明操作失败
        std::cout << "Error renaming " << oldPath << " to " << newPath << ": " << std::strerror(errno) << std::endl;
        return false;
    }
    return true;
    // return rename(old_path.c_str(), new_path.c_str()) == 0;
}
bool jtools::FileOperation::ReadFileDatas(const std::string file_name, std::string& datas)
{
    datas = "";
    std::ifstream file(file_name, std::ios::binary);
    if (!file) {
        std::cerr << "无法打开文件: " << file_name << std::endl;
        return false;
    }

    // 使用 std::istreambuf_iterator 读取整个文件内容
    datas.assign((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    if (datas.empty())
    {
        return false;
    }
    return true;
}

std::vector<std::string> jtools::FileOperation::GetFileOrDirNames(const std::string& directory_path, bool is_read_dir_name)
{
    std::vector<std::string> res_list;
    if (fs::exists(directory_path) && fs::is_directory(directory_path)) {
        for (const auto& entry : fs::directory_iterator(directory_path)) {
            if (is_read_dir_name)
            {
                if (fs::is_directory(entry.status())) {
                    res_list.push_back(entry.path().filename().string());
                }
            }
            else
            {
                if (fs::is_regular_file(entry.status())) {
                    res_list.push_back(entry.path().filename().string());
                }
            }

        }
    }
    else {
        std::cerr << "Directory does not exist: " << directory_path << std::endl;
    }

    return res_list;
}

jtools::DiskSpaceInfo jtools::FileOperation::GetDiskSpace(const std::string& path)
{

    DiskSpaceInfo info{ 0, 0, false };

#ifdef _WIN32
    ULARGE_INTEGER free_bytes_available, total_bytes, total_free_bytes;
    if (GetDiskFreeSpaceExA(path.c_str(), &free_bytes_available, &total_bytes, &total_free_bytes))
    {
        info.total_bytes = total_bytes.QuadPart;
        info.available_bytes = free_bytes_available.QuadPart;
        info.success = true;
    }
#else
    struct statvfs stat;
    if (statvfs(path.c_str(), &stat) == 0) {
        info.total_bytes = static_cast<uint64_t>(stat.f_blocks) * stat.f_frsize;
        info.available_bytes = static_cast<uint64_t>(stat.f_bavail) * stat.f_frsize;
        info.success = true;
    }
#endif

    return info;

}
std::string jtools::FileOperation::ExtractDiskRoot(const std::string& path)
{
#ifdef _WIN32
    return GetDriveLetter(path);
#else
    return GetMountRoot(path);
#endif

}

bool jtools::FileOperation::CheckDiskCapacity(const std::string& path_, int capacity_size_ /*= 10*/)
{
    jtools::DiskSpaceInfo disk_space;
    auto disk_root = jtools::FileOperation::ExtractDiskRoot(path_);
    if (disk_root.empty())
    {
        std::cerr << "路径为空，获取路径盘符失败!" << std::endl;
        return false;
    }

    disk_space = jtools::FileOperation::GetDiskSpace(disk_root);
    if (!disk_space.success)
    {
        return false;
    }

    // 以 GB 为单位的容量阈值
    constexpr uint64_t kBytesPerGB = 1024ull * 1024 * 1024;
    uint64_t required_bytes = static_cast<uint64_t>(capacity_size_) * kBytesPerGB;

    return disk_space.available_bytes > required_bytes;
}

bool jtools::FileOperation::HasFileByNameOrExt(const std::string& folder_path_, const std::string& name_or_ext_)
{
    if (!fs::exists(folder_path_) || !fs::is_directory(folder_path_))
    {
        std::cerr << "路径无效: " << folder_path_ << std::endl;
        return false;
    }

    bool is_extension = (name_or_ext_.size() > 1 && name_or_ext_[0] == '.');

    for (const auto& entry : fs::directory_iterator(folder_path_))
    {
        if (!entry.is_regular_file()) continue;

        const auto& path = entry.path();
        if (is_extension)
        {
            if (path.extension() == name_or_ext_)
            {
                return true;  // 找到一个就返回 true
            }
        }
        else
        {
            if (path.filename() == name_or_ext_)
            {
                return true;  // 找到一个就返回 true
            }
        }
    }

    return false;  // 遍历完没找到
}

std::string jtools::FileOperation::GetDriveLetter(const std::string& path)
{
    if (path.size() >= 3 && path[1] == ':' && (path[2] == '\\' || path[2] == '/')) {
        return path.substr(0, 3); // "C:\"
    }
    return ""; // 无效路径
}

std::string jtools::FileOperation::GetMountRoot(const std::string& path)
{
    (void)path;
    return "/"; // 通常挂载在 "/"
}


