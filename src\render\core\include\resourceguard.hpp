/*********************************************************************
 * @brief  资源多线程保护.
 *
 * @file   resourceguard.hpp
 *
 * @date   2024.05.29
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include <functional>   //function
#include <mutex>        //lock_guard
#include <shared_mutex> //shared_mutex shared_lock
#include <vector>

template <typename T, typename M>
class ResourceOut
{
public:
    ResourceOut(const T& val_, M& mutex_)
        : val(val_), mutex(mutex_), lock(mutex) {
    }
    ~ResourceOut() {}
    const T& GetResource() const { return val; }

    ResourceOut(const ResourceOut&) = delete;
    ResourceOut& operator=(const ResourceOut&) = delete;
    ResourceOut(ResourceOut&&) = default;
    ResourceOut& operator=(ResourceOut&&) = default;

private:
    const T& val;
    M& mutex;
    std::shared_lock<M> lock;
};

template <typename T>
class ResourceDo
{
public:
    using MutexType = std::shared_mutex;

    ResourceOut<T, MutexType> GetReource() const
    {
        return ResourceOut<T, MutexType>(m_resources, m_mutex);
    }

    void SetResource(T&& resources)
    {
        std::lock_guard<MutexType> lock(m_mutex);
        m_resources = resources;
    }

    /**
     * @brief 获取资源并执行给定的函数.
     * @param function 要执行的函数.
     */
    template <typename RT>
    RT UseResourcesDoSomething(
        const std::function<RT(const T&)>& function) const
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources);
    }

    template <typename RT>
    RT UseResourcesDoSomething(const std::function<RT(T&)>& function) const
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources);
    }

    template <typename RT>
    RT UseResourcesDoSomething(const std::function<RT(T&)>& function)
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources);
    }

    template <typename RT, typename... Args>
    RT UseResourcesDoSomething(
        const std::function<RT(const T&, Args...)>& function,
        Args &&...args) const
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources, std::forward<Args>(args)...);
    }

    template <typename RT, typename... Args>
    RT UseResourcesDoSomething(
        const std::function<RT(const T&, Args...)>& function,
        Args... args) const
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources, std::forward<Args>(args)...);
    }
    template <typename RT, typename... Args>
    RT UseResourcesDoSomething(const std::function<RT(T&, Args...)>& function,
        Args &&...args)
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources, std::forward<Args>(args)...);
    }

    template <typename RT, typename... Args>
    RT UseResourcesDoSomething(const std::function<RT(T&, Args...)>& function,
        Args... args)
    {
        std::lock_guard<MutexType> lock(m_mutex);
        return function(m_resources, std::forward<Args>(args)...);
    }
    ResourceDo() = default;
    ResourceDo(const ResourceDo&) = delete;
    ResourceDo& operator=(const ResourceDo&) = delete;
    ResourceDo(ResourceDo&&) = default;
    ResourceDo& operator=(ResourceDo&&) = delete;

protected:
    mutable MutexType m_mutex; ///< 保护资源的互斥锁.
    T m_resources;             ///< 资源容器.
};

template <typename T>
class ResourceVector : public ResourceDo<std::vector<T>>
{
public:
    using CONTAINER_TYPE = std::vector<T>;
    using MutexType = std::shared_mutex;

    bool IsEmpty() const { return this->m_resources.empty(); }

    void SetResource(CONTAINER_TYPE& resources)
    {
        std::lock_guard<MutexType> lock(this->m_mutex);
        this->m_resources.swap(resources);
    }

    void AddResource(const CONTAINER_TYPE& resources)
    {
        std::lock_guard<MutexType> lock(this->m_mutex);
        this->m_resources.insert(this->m_resources.end(), resources.begin(),
            resources.end());
    }

    void AddResource(const T& resources)
    {
        std::lock_guard<MutexType> lock(this->m_mutex);
        this->m_resources.emplace_back(resources);
    }

    void ClearResource()
    {
        std::lock_guard<MutexType> lock(this->m_mutex);
        CONTAINER_TYPE().swap(this->m_resources);
    }

    ResourceVector() = default;
    ResourceVector(const ResourceVector&) = delete;
    ResourceVector& operator=(const ResourceVector&) = delete;
    ResourceVector(ResourceVector&&) = default;
    ResourceVector& operator=(ResourceVector&&) = default;
};