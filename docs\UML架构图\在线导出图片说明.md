# UML图表在线导出说明

如果你没有安装Mermaid CLI工具，可以使用在线方式导出图片。

## 方法一：使用Mermaid Live Editor（推荐）

### 步骤：
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 清空编辑器中的默认内容
3. 复制对应的Mermaid代码（从.md文件中的```mermaid到```之间的内容）
4. 粘贴到编辑器中
5. 等待图表渲染完成
6. 点击右上角的"Actions"按钮
7. 选择"Download PNG"或"Download SVG"

### 导出列表：
- **整体架构UML类图**: 复制 `class-diagram.md` 中的mermaid代码
- **检测流程时序图**: 复制 `sequence-diagram.md` 中的mermaid代码  
- **系统分层架构图**: 复制 `architecture-diagram.md` 中的mermaid代码
- **核心数据模型ER图**: 复制 `er-diagram.md` 中的mermaid代码

## 方法二：使用GitHub/GitLab渲染

### GitHub方式：
1. 将.md文件上传到GitHub仓库
2. 在GitHub中查看文件，Mermaid图表会自动渲染
3. 使用浏览器的截图功能或打印功能保存为图片

### GitLab方式：
1. 将.md文件上传到GitLab仓库
2. 在GitLab中查看文件，Mermaid图表会自动渲染
3. 使用浏览器的截图功能保存为图片

## 方法三：使用VS Code插件

### 安装插件：
1. 在VS Code中安装"Mermaid Preview"插件
2. 打开对应的.md文件
3. 按Ctrl+Shift+P，输入"Mermaid: Preview"
4. 在预览窗口中右键选择"Export as PNG/SVG"

### 推荐插件：
- **Mermaid Preview**: 预览和导出Mermaid图表
- **Markdown Preview Mermaid Support**: 在Markdown预览中支持Mermaid

## 方法四：使用在线Markdown编辑器

### Typora（付费软件）：
1. 打开Typora
2. 复制整个.md文件内容
3. 粘贴到Typora中
4. 图表会自动渲染
5. 右键图表选择"复制图片"或"导出"

### 其他在线编辑器：
- **StackEdit**: https://stackedit.io/
- **Dillinger**: https://dillinger.io/
- **HackMD**: https://hackmd.io/

## 导出建议

### 图片格式选择：
- **PNG**: 适合文档嵌入，文件较小
- **SVG**: 矢量格式，可无损缩放，适合打印
- **PDF**: 适合正式文档

### 分辨率设置：
- **屏幕显示**: 1920x1080 或 1366x768
- **打印用途**: 300 DPI，建议更高分辨率
- **网页展示**: 1200px宽度即可

### 文件命名建议：
- `jrsaoi-class-diagram.png` - 整体架构UML类图
- `jrsaoi-sequence-diagram.png` - 检测流程时序图
- `jrsaoi-architecture-diagram.png` - 系统分层架构图
- `jrsaoi-er-diagram.png` - 核心数据模型ER图

## 常见问题

### Q: 图表显示不完整怎么办？
A: 尝试调整浏览器窗口大小，或者在导出时设置更大的画布尺寸。

### Q: 图表文字太小看不清？
A: 可以在导出时设置更高的分辨率，或者使用SVG格式可以无损放大。

### Q: 某些图表渲染失败？
A: 检查Mermaid语法是否正确，可以在Mermaid Live Editor中验证。

### Q: 如何批量导出？
A: 建议使用本地的Mermaid CLI工具，运行提供的脚本文件。

## 本地安装Mermaid CLI（推荐）

如果经常需要导出图表，建议安装本地工具：

```bash
# 安装Node.js (如果未安装)
# 下载地址: https://nodejs.org/

# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 验证安装
mmdc --version

# 使用脚本批量导出
# Windows: 双击 export-images.bat
# Linux/Mac: bash export-images.sh
```

安装完成后，可以直接运行提供的导出脚本，一键生成所有图表的PNG文件。
