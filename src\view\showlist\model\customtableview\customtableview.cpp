#include "customtableview.h"

CustomTableView::CustomTableView(QWidget* parent) : QTableView(parent)
{
}

CustomTableView::~CustomTableView()
{
}

void CustomTableView::mousePressEvent(QMouseEvent* event)
{
    setCurrentIndex(QModelIndex()); // 初始化当前索引，避免点击无效区域时记录上一次的有效值
    QTableView::mousePressEvent(event); // 调用基类的鼠标点击事件处理
    QModelIndex index = currentIndex(); // 获取当前索引

    if (index.row() < 0 && index.column() < 0) // 如果点击表格空白处，直接返回
    {
        return;
    }

    if (event->button() == Qt::LeftButton) // 鼠标左键
    {
        emit CustomLeftClicked(index); // 触发自定义左键点击信号
    }
    else // 鼠标右键
    {
        emit CustomRightClicked(index); // 触发自定义右键点击信号
    }
}

void CustomTableView::keyPressEvent(QKeyEvent* event)
{
    switch (event->key()) // 根据按键类型触发不同的信号
    {
    case Qt::Key_Up: // 向上箭头键
        emit CustomUpClicked(); // 触发自定义向上按键信号
        break;
    case Qt::Key_Down: // 向下箭头键
        emit CustomDownClicked(); // 触发自定义向下按键信号
        break;
    }
}