#ifndef __JRSTABLEMANAGER_H__
#define __JRSTABLEMANAGER_H__


#include "datapch.h"
//#include "tablebase.h"

//std
#include <map>

namespace jrsdatabase {
    class TableManager
    {
    public:
        TableManager();
        ~TableManager();

        TableBasePtr GetTable(const std::string& name_);

        int InitTables(const std::shared_ptr<DB_Mysql>& conn_ptr);
    private:
        template<typename Table>
        int RegisterComponent(const std::string& name_);
        /** 初始化注册表   */
        void InitAOIMachine();
        void InitAOIMachineStatstics();
        void InitUser();
        void InitProject();
        void InitDetectType();
        void InitBoard();
        void InitSubboard();
        void InitDevice();
        void InitGroup();
        void InitDetectWindow();

    private:
        static std::atomic<bool> _is_create_table; /*<是否创建*/
        static std::map<std::string, TableBasePtr> _table_container;
    };
    using TableManagerPtr = std::shared_ptr<TableManager>;
}
#endif // !__JRSTABLEMANAGER_H__
