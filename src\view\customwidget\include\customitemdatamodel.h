/*****************************************************************
 * @file   customitemdatamodel.h
 * @brief   自定义数据模型，主要用于tableview，listview，treeview等，用于显示数据,可接受任意数据类型 
 * @details
 * <AUTHOR>
 * @date 2025.2.7
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.2.7          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef BCD049C0_ACB4_42B4_A9D5_66E9208C0214
#define BCD049C0_ACB4_42B4_A9D5_66E9208C0214
#ifndef __CUSTOMITEMDATAMODEL_H__
#define __CUSTOMITEMDATAMODEL_H__
//STD
//Custom
//Third
//QT
#include <QStandardItemModel>

namespace jrsaoi
{
    /**
     * .  行绘制颜色规则结构体 by zhangyuyu 2025.4.22  
     */
    struct RowColorRule
    {
        QString item_name;
        std::function<std::optional<QColor>(const QVariant&)> match;
    };
  class CustomDataModel : public QStandardItemModel
  {
      Q_OBJECT
      public :
          CustomDataModel(QObject* parent);
          /**
           * @fun SetHorizontalHeaderData 
           * @brief 设置水平表头
           * @param headers 表头数据
           * <AUTHOR>
           * @date 2025.2.7
           */
          void SetHorizontalHeaderData(const QStringList& headers);

          /**
           * @fun SetVerticalHeaderData 
           * @brief 设置垂直表头
           * @param headers 表头数据
           * <AUTHOR>
           * @date 2025.2.7
           */
          void SetVerticalHeaderData(const QStringList& headers);

          /**
           * @fun GetRowData 
           * @brief 获取行数据
           * @param row 行数
           * @return 返回行数据
           * <AUTHOR>
           * @date 2025.2.7
           */
          std::vector<QVariant> GetRowData(int row)const;

          /**
           * @fun SetData 
           * @brief 设置需要显示的数据,替换原有数据，将原本的数据清空再添加
           * @param data 输入的数据
           * <AUTHOR>
           * @date 2025.2.7
           */
          void SetData(const std::vector<std::vector<QVariant>>& data);

          /**
           * @fun AddRows 
           * @brief 以行为单位添加数据,在原有基础上添加
           * @param rows_data  要添加的数据
           * <AUTHOR>
           * @date 2025.2.7
           */
          void AddRows(const std::vector<std::vector<QVariant>>& rows_data);

          /**
           * @fun DeleteRows 
           * @brief 删除行数据
           * @param row 从哪一行开始
           * @param count 删除多少行
           * <AUTHOR>
           * @date 2025.2.7
           */
          void DeleteRows(int row, int count);

          /**
           * @fun UpdateRowData 
           * @brief 更新选中的行数据
           * @param row [IN] 选中的行
           * @param new_data[] [IN] 新数据
           * <AUTHOR>
           * @date 2025.2.12
           */
          void UpdateRowData(int row,const std::vector<QVariant>& new_data);
          void DeleteRowByColumnValue(int column,const QVariant& target_value);

        
          /**
           * @fun ClearAllData 
           * @brief 清空所有数据
           * <AUTHOR>
           * @date 2025.3.14
           */
          void ClearAllData();
          /**
           * @fun SetColorRules 
           * @brief 设置颜色规则
           * @param rule 颜色规则
           * <AUTHOR>
           * @date 2025.4.22
           */
          void SetColorRules(const RowColorRule& rule);
          int rowCount(const QModelIndex& parent = QModelIndex()) const override;
          int columnCount(const QModelIndex& parent = QModelIndex()) const override;
          QModelIndex index(int row, int column, const QModelIndex& parent = QModelIndex()) const override;
      protected:
          QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
          QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
          bool insertRows(int row, int count, const QModelIndex& parent = QModelIndex())override;
          bool removeRows(int row, int count, const QModelIndex& parent = QModelIndex()) override;
          QModelIndex parent(const QModelIndex& child) const override;

      private:
          /**
           * @fun AddData 
           * @brief 添加数据
           * @param index 索引 行列
           * @param value 数据
           * @param role itemmodel的角色
           * @return 添加成功返回true
           * <AUTHOR>
           * @date 2025.2.7
           */
          bool AddData(const QModelIndex& index, const QVariant& value, int role = Qt::EditRole);
          
   
          
      private:
          std::vector<std::vector<QVariant>> m_data; /**< 放入表格显示数据*/
          QStringList m_horizontal_headers; //! 水平表头
          QStringList m_vertical_headers;  //! 垂直表头
          RowColorRule m_color_rule;

  };

}
#endif CUSTOMITEMDATAMODEL_H


#endif /* BCD049C0_ACB4_42B4_A9D5_66E9208C0214 */
