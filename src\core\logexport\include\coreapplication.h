/*****************************************************************//**
 * @file   coreapplication.h
 * @brief  通用应用工具类
 * @details    用于定义工具类对象使用接口
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>YY<PERSON>hang      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef  __COREAPPLICATION_H__
#define __COREAPPLICATION_H__
//STD
#include <iostream>
//Custom
#include "errorhandler.h"
#include "logmanager.h"
#include "pluginexport.hpp"
namespace jrscore
{

    struct CoreAppImplData;
    class JRS_AOI_PLUGIN_API CoreApplication
    {
    public:
        CoreApplication ();
        ~CoreApplication ();
        
        /**
         * @fun GetInstance 
         * @brief 应用类使用单例模式
         * @return 返回单例
         * @date 2024.1.23
         * <AUTHOR>
         */
        static CoreApplication* GetInstance ();

        ErrorHandlerPtr GetErrorHandler ();
        
        LogManagerPtr GetLogManager ();
    private:
        CoreAppImplData* p_data;
        

        void EnsureDirectoryExists (const std::string& directory);
        std::string GetCurrentPath ();
        
    };


    #define AOICoreApp jrscore::CoreApplication::GetInstance()

    //默认日志器写入，文件名称默认为JrsAOI
    #define AOIAppLog(level,...)\
            AOICoreApp->GetLogManager()->Log(level,__VA_ARGS__)

    //输入错误码到日志收集器中
    #define PushErrorToStack(errorcode,des)\
            AOICoreApp->GetErrorHandler()->PushStackError(errorcode,des)

    //默认日志器写入带等级

    #define Log_WARN(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_WARN,__VA_ARGS__)

    #define Log_INFO(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_INFO,__VA_ARGS__)
    #define Log_ERROR(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)
    #define Log_DEBUG(...)\
                    AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_DEBUG,__VA_ARGS__)


    //指定日志文件名称及等级写入
    #define LogTo_INFO(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_INFO,__VA_ARGS__)
    #define LogTo_WARN(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_WARN,__VA_ARGS__)
    #define LogTo_DEBUG(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_DEBUG,__VA_ARGS__)
    #define LogTo_ERROR(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)

    //错误收集器，单独存放，用于快速排查问题
    #define Log_Error_Stack(...)\
            AOICoreApp->GetLogManager()->LogWithName("Error",jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)
}

#endif // ! __COREAPPLICATION_H__
