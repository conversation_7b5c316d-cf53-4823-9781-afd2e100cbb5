#include "CustomListModel.h"

CustomListModel::CustomListModel()
{
    m_header_list.append(QString::fromWCharArray(L"定位点"));
    m_vec_data.clear();
}

CustomListModel::~CustomListModel()
{
}

int CustomListModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return int(m_vec_data.size());
}

QVariant CustomListModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        if (role == Qt::DisplayRole)
        {
            return m_header_list.at(section);
        }
    }
    return QVariant();
}

QVariant CustomListModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.row() >= m_vec_data.size())
    {
        return QVariant();
    }
    if (role == Qt::TextAlignmentRole)
    {
        return Qt::AlignLeft;
    }
    if (role == Qt::DisplayRole || role == Qt::EditRole)
    {
        if (index.row() < m_vec_data.size())
        {
            switch (index.column())
            {
            case 0:
                return m_vec_data.at(index.row())->m_alignment.c_str();
                break;
            }
        }
    }
    return QVariant();
}

void CustomListModel::SetListData(std::vector<ListStruct*> vec_value)
{
    m_vec_data = vec_value;
}

void CustomListModel::AddListData(ListStruct* value)
{
    m_vec_data.push_back(value);
}
