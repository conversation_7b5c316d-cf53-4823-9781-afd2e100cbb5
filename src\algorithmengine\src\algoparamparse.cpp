﻿//Custom
#include "algoparamparse.h"
#include "operatorparambase.h"
#include "algogeometrydata.hpp"
//Thirdy
#pragma warning(push, 3)
#include "iguana/iguana.hpp"
#pragma warning(pop)
namespace jrsalgo
{
    struct SetAlgoParamProcessor
    {
        SetAlgoParamProcessor(std::shared_ptr<iguana::base>& param_ptr_, const std::string& param_name_)
            :param_ptr(param_ptr_)
            , param_name(param_name_)
        {
        }
        std::shared_ptr<iguana::base> param_ptr;
        std::string param_name;
        template <typename T>
        void SetFieldValue(const T& value) const {
            try
            {
                param_ptr->set_field_value(param_name, value);
            }
            catch (const std::exception& e)
            {
                std::cerr << "Set param value failed: " << e.what() << std::endl;
            }
        }
        void operator()(std::monostate) const
        {
            std::cout << "No value (monostate)." << std::endl;
        }
        void operator()(int value) const
        {
            std::cout << "Integer: " << value << std::endl;
            SetFieldValue(value);
        }

        void operator()(float value) const
        {
            std::cout << "Float: " << value << std::endl;
            SetFieldValue(value);
        }

        void operator()(bool value) const
        {
            std::cout << "Boolean: " << (value ? "true" : "false") << std::endl;
            SetFieldValue(value);
        }

        void operator()(double value) const
        {
            std::cout << "Double: " << value << std::endl;
            SetFieldValue(value);
        }
        void operator()(const JrsRect& value) const
        {
            std::cout << "JrsRect: " << value.width << std::endl;
            SetFieldValue(value);
        }
        void operator()(const std::string& value) const
        {
            std::cout << "String: " << value << std::endl;
            SetFieldValue(value);
        }

        void operator()(const std::vector<uint8_t>& value) const
        {
            std::cout << "std::vector<uint8_t>: ";
            SetFieldValue(value);
        }

        void operator()(const std::vector<std::vector<uint8_t>>& value) const
        {
            std::cout << "std::vector<std::vector<uint8_t>>: " << std::endl;
            SetFieldValue(value);
        }
        /*       void operator()(const jrsoperator::JrsImage& value)const
               {
                   std::cout << "jrsoperator::JrsImage: " << std::endl;
                   SetFieldValue(value);
               }*/
               /*   void operator()(const std::vector<jrsoperator::JrsImage>& value)const
                  {

                      std::cout << "std::vector<jrsoperator::JrsImage>: " << std::endl;
                      SetFieldValue(value);
                  }*/
        void operator()(const std::unordered_map<int, cv::Mat>& value)const
        {
            std::cout << "sstd::map<int,cv::Mat>: " << std::endl;
            auto temp_param_ptr = std::dynamic_pointer_cast<jrsoperator::OperatorParamBase> (param_ptr);
            if (param_name == "input_image")
            {
                temp_param_ptr->input_image = value;
            }
        }
        void operator()(const std::vector<jrsoperator::TemplColorParamsGroup>& value) const
        {
            std::cout << "std::vector<jrsoperator::TemplColorParamsGroup>: " << std::endl;
            auto temp_param_ptr = std::dynamic_pointer_cast<jrsoperator::OperatorParamBase> (param_ptr);

            if (param_name == "template_data")
            {
                temp_param_ptr->template_data = value;
            }
        }
        void operator()(const std::unordered_map<int, AlgoJudgeResult>& value) const
        {
            (void)value;
        }
        void operator()(const std::unordered_map<int, std::vector<JrsRect>>& value) const
        {
            (void)value;
        }
        void operator()(const std::unordered_map<std::string, ControlSpec>& value) const
        {
            (void)value;
        }
        void operator()(const cv::Mat& value) const
        {
            (void)value;
        }
        void operator()(const std::vector<JrsVec3f>& value) const
        {
            (void)value;
        }
        void operator()(const std::vector<JrsRect>& value) const
        {
            (void)value;
        }
        void operator()(const std::unordered_map<std::string, cv::Mat>& value) const
        {
            (void)value;
        }
        void operator()(const std::vector<std::vector<JrsVec3f>>& value)const
        {
            std::cout << "std::vector<std::vector<JrsVec3f>>: " << std::endl;
            SetFieldValue(value);
        }
    };


    struct GetAlgoParamProcessor
    {
        GetAlgoParamProcessor(std::shared_ptr<iguana::base>& param_ptr_, const std::string& param_name_)
            :param_ptr(param_ptr_)
            , param_name(param_name_)
        {
        }
        std::shared_ptr<iguana::base> param_ptr;
        std::string param_name;

        template <typename T>
        T GetFieldValue(const T& value) const {
            try
            {
                param_ptr->get_field_value<T>(param_name);
                //param_ptr->get_field_value(param_name);
            }
            catch (const std::exception& e)
            {
                std::cerr << "Set param value failed: " << e.what() << std::endl;
            }
        }
    };
    AlgoParamParse::AlgoParamParse()
    {
    }
    AlgoParamParse::~AlgoParamParse()
    {
    }

    std::shared_ptr<iguana::base> AlgoParamParse::CreateAlgoParamPtr(const std::string& algo_name_)
    {
        if (algo_name_.empty())
        {
            std::cerr << "when create Algo param ptr, input algo_name_ is empty! ";
            return nullptr;
        }
        try
        {

            static const std::string prefix = "jrsoperator::";
            static const std::string suffix = "Param";
            std::string full_class_name;
            full_class_name.reserve(prefix.size() + algo_name_.size() + suffix.size());
            full_class_name = prefix + algo_name_ + suffix;
            return iguana::create_instance(full_class_name);

        }
        catch (const std::exception& e)
        {
            std::cerr << "when create algo param ptr,occur exception :" << e.what() << std::endl;
            return nullptr;
        }
    }

    void AlgoParamParse::SetSpeficParamValue(std::shared_ptr<iguana::base>& param_ptr_, const std::string& param_name_, const jrsparam::AlgoParamType& param_value_)
    {
        SetAlgoParamProcessor set_algo_param_processor(param_ptr_, param_name_);
        std::visit(set_algo_param_processor, param_value_);
    }

    /*   jrsparam::AlgoParamType AlgoParamParse::GetSpeficParamValue(const std::shared_ptr<iguana::base>& param_ptr_, const std::string& param_name_)
       {

       }*/

    std::string AlgoParamParse::GetAlgoParamString(const std::shared_ptr<iguana::base>& param_ptr_)
    {
        std::string param_string;

        try
        {
            param_ptr_->to_json(param_string);
        }
        catch (const std::exception& e)
        {
            param_string = "";
            std::cerr << "Get algo param string failure:" << e.what() << std::endl;
        }

        return param_string;
    }

    void AlgoParamParse::SetAlgoParamFromString(std::shared_ptr<iguana::base>& param_ptr, const std::string_view& param_str_)
    {
        try
        {
            param_ptr->from_json(param_str_);

        }
        catch (const std::exception& e)
        {
            std::cerr << "Set algo param from string failure:" << e.what() << std::endl;
        }
    }

    void AlgoParamParse::InitMember()
    {
    }
}
