/*****************************************************************//**
 * @file   logshowmodel.h
 * @brief  日志显示数据类
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
 //Third
#ifndef __LOGSHOWMODEL_H__
#define __LOGSHOWMODEL_H__


#include "modelbase.h"
namespace jrsaoi
{
    class LogShowModel :
        public ModelBase
    {
    public:
        LogShowModel(const std::string& name);
        ~LogShowModel();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
    };
    using LogShowModelPtr = std::shared_ptr<LogShowModel>;
}
#endif // !__LOGSHOWMODEL_H__
