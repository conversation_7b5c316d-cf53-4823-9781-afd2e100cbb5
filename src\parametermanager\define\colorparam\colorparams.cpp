#include "colorparams.h"
#include "colorparamsserialization.h"
#include <cereal/cereal.hpp>
#include <cereal/archives/json.hpp>
#include <cereal/types/array.hpp>
#include <cereal/types/utility.hpp> 
#include <cereal/types/string.hpp>
#include <cereal/types/vector.hpp>
void ColorWheelThreshVal::Set1DHsTableFrom2DArray(unsigned char hs_table[180][256])
{
    std::vector<unsigned char>().swap(hs_table_one_dim);
    for (int i = 0; i < 180; ++i)
    {
        for (int j = 0; j < 256; ++j)
        {
            hs_table_one_dim.push_back(hs_table[i][j]);
        }
    }
}

std::string ColorWheelThreshVal::ToJson() const
{
    return serialization::ToJson(*this, "ColorWheelThreshVal");
}

ColorWheelThreshVal ColorWheelThreshVal::FromJson(const std::string& json_str)
{
    if(json_str.empty()) return ColorWheelThreshVal();
    return serialization::FromJson<ColorWheelThreshVal>(json_str, "ColorWheelThreshVal");
}
std::string PreProcessParams::ToJson() const
{
    return serialization::ToJson(*this, "PreProcessParams");
}

PreProcessParams PreProcessParams::FromJson(const std::string& json_str)
{
    if(json_str.empty()) return PreProcessParams();
    return serialization::FromJson<PreProcessParams>(json_str, "PreProcessParams");
}

std::string BinProcessParams::ToJson() const
{
    return serialization::ToJson(*this, "BinProcessParams");
}

BinProcessParams BinProcessParams::FromJson(const std::string& json_str)
{
    if(json_str.empty()) return BinProcessParams();
    return serialization::FromJson<BinProcessParams>(json_str, "BinProcessParams");
}

std::string ColorParams::ToJson() const
{
    return serialization::ToJson(*this, "ColorParams");
}

ColorParams ColorParams::FromJson(const std::string& json_str)
{
    if(json_str.empty()) return ColorParams();
    return serialization::FromJson<ColorParams>(json_str, "ColorParams");
}