#include "regionwidget.h"
#include "algoselectwidget.h"

//#include "projectparam.hpp" // DetectWindow

#include <QHBoxLayout>
#include <QPushButton>
#include <QSpacerItem>

RegionWidget::RegionWidget(const QString& window_name, QWidget* parent)
    : QGroupBox(window_name, parent)
    , layout(new QVBoxLayout(this))
    , btn_add(new QPushButton(QString::fromWCharArray(L"添加算法")))
{
    list_algo = QStringList() << QString::fromWCharArray(L"算法A")
        << QString::fromWCharArray(L"算法B")
        << QString::fromWCharArray(L"算法C");
    list_error = QStringList() << QString::fromWCharArray(L"缺陷1")
        << QString::fromWCharArray(L"缺陷2")
        << QString::fromWCharArray(L"缺陷3");
    this->setSizePolicy(QSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred));
    this->setLayout(layout);
    layout->addWidget(btn_add);

    connect(btn_add, &QPushButton::clicked, this, [=]() {
        SlotCreateAlgo("hhh");
        });
}

void RegionWidget::SetList(const QStringList& list_algo_, const QStringList& list_error_)
{
    list_algo = list_algo_;
    list_error = list_error_;
}

void RegionWidget::CreateAlgo(const QString& algo_widget_name)
{
    auto da = new AlgoSelectWidget();
    da->setObjectName(algo_widget_name);
    da->SetName(algo_widget_name);
    //da->SetError(list_error);
    //da->SetAlgo(list_algo);
    layout->insertWidget(0, da);

    //connect(da, &AlgoSelectWidget::SignalChangeAlgo, this, [=](const QString& algo_name) {
    //    emit SignalChangeAlgo(algo_widget_name, algo_name);
    //    });
    //connect(da, &AlgoSelectWidget::SignalChangeError, this, [=](const QString& error_name) {
    //    emit SignalChangeError(algo_widget_name, error_name);
    //    });
    //connect(da, &AlgoSelectWidget::SignalDelete, this, [=]() {
    //    DeleteAlgo(algo_widget_name);
    //    });
    //connect(da, &AlgoSelectWidget::SignalEnable, this, [=](bool state) {
    //    emit SignalEnableAlgoWidget(algo_widget_name, state);
    //    });
    //emit SignalCreateAlgoWidget(algo_widget_name);
}

void RegionWidget::DeleteAlgo(const QString& algo_widget_name)
{
    for (int i = 0; i < layout->count(); ++i)
    {
        QLayoutItem* item = layout->itemAt(i);
        if (!item) continue;

        QWidget* widget = item->widget();
        if (!widget) continue;

        auto algo_widget = dynamic_cast<AlgoSelectWidget*>(widget);
        if (!algo_widget) continue;

        // 如果部件的名称匹配，移除并删除该部件
        if (algo_widget->objectName() == algo_widget_name) {
            layout->removeWidget(widget);
            widget->deleteLater();
            emit SignalDeleteAlgoWidget(algo_widget_name);
            break;
        }

    }
}
void RegionWidget::ClearAlgo()
{
    for (int i = 0; i < layout->count(); ++i)
    {
        QLayoutItem* item = layout->itemAt(i);
        if (!item) continue;

        QWidget* widget = item->widget();
        if (!widget) continue;

        auto algo_widget = dynamic_cast<AlgoSelectWidget*>(widget);
        if (!algo_widget) continue;

        layout->removeWidget(widget);
        widget->deleteLater();
        break;
    }
}

void RegionWidget::SlotCreateAlgo(const QString& algo_widget_name)
{
    CreateAlgo(algo_widget_name);
}
