﻿#include "componentvaluedialog.h"

#include <QRadioButton>
ComponentDialog::ComponentDialog(QWidget* parent /*= nullptr*/)
    : QDialog(parent)
{
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(this);

    // 创建并添加输入元件名的控件
    //QLabel* labelName = new QLabel("请添加检测项:", this);
    //layout->addWidget(labelName);

    //lineEditName = new QLineEdit(this);
    //layout->addWidget(lineEditName);

    // 创建并添加输入元件料号的控件
    QLabel* labelPart = new QLabel("请选择检测工位:", this);
    layout->addWidget(labelPart);

    lineEditPart = new QLineEdit(this);
    layout->addWidget(lineEditPart);

    radio_rect = new QRadioButton();
    auto h_layout = new QHBoxLayout;
    radio_rect->setText("矩形");
    h_layout->addWidget(radio_rect);
    radio_circle = new QRadioButton();
    radio_circle->setText("圆形");
    h_layout->addWidget(radio_circle);
    radio_polygon = new QRadioButton();
    radio_polygon->setText("不规则图形");
    h_layout->addWidget(radio_polygon);
    //layout->addLayout(h_layout);
    radio_rect->setChecked(true);
    radio_polygon->setDisabled(true);
    radio_circle->setDisabled(true);
    // 创建确定和取消按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, this);
    layout->addWidget(buttonBox);

    // 连接按钮信号
    connect(buttonBox, &QDialogButtonBox::accepted, this, &ComponentDialog::OnAccept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);

}

QString ComponentDialog::GetComponentName() const
{
    return  "aoi_component";
    return lineEditName->text();
}

QString ComponentDialog::GetComponentPart() const
{
    return lineEditPart->text();
}

jrsdata::ComponentUnit::Shape ComponentDialog::GetComponentShape() const
{
    if (radio_circle->isChecked())
    {
        return jrsdata::ComponentUnit::Shape::Circle;
    }
    else if (radio_rect->isChecked())
    {
        return jrsdata::ComponentUnit::Shape::RECT;
    }
    else if (radio_polygon->isChecked())
    {
        //return jrsdata::ComponentUnit::Shape::Polygon;
    }
    return jrsdata::ComponentUnit::Shape::RECT;
}

void ComponentDialog::OnAccept()
{

    const QString invalidChars = R"(<>:"/\|?*)";
    //TODO 修改默认添加元件名称 by baron zhang 2025-07-10
    //QString name = lineEditName->text().trimmed();
    //TODO END
    QString name = "aoi_component";//这里默认检测项目名称为统一的AOI_Algo
    QString part = lineEditPart->text().trimmed();
    QStringList problems;

    // 判断是否为空或只有空格
    if (name.isEmpty())
        problems << "元件名不能为空或只包含空格";
    if (part.isEmpty())
        problems << "料号不能为空或只包含空格";

    std::string name_illegal, part_illegal;
    // 判断是否包含非法字符
    if (jrscore::AOITools::ContainsIllegalCharacters(name.toStdString(), name_illegal))
        problems << QString("元件名中包含非法字符：%1").arg(QString::fromStdString(name_illegal));
    if (jrscore::AOITools::ContainsIllegalCharacters(part.toStdString(), part_illegal))
        problems << QString("料号中包含非法字符：%1").arg(QString::fromStdString(part_illegal));

    if (!problems.isEmpty())
    {
        QMessageBox::warning(this, "非法输入", problems.join("\n"));
        return; // 阻止关闭对话框
    }

    accept(); // 合法才关闭对话框

}
