///*****************************************************************//**
// * @file   CustomListWidget.h
// * @brief  提示界面view类
// * @details    
// * <AUTHOR>
// * @date 2024.1.29
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.1.29         <td>V1.0              <td>HJC          <td><EMAIL> <td>
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/

#ifndef __CUSTOM_LIST_WIDGET_H__
#define __CUSTOM_LIST_WIDGET_H__

#include <functional>

//QT
#include <QListWidget>
//JRS
#include "coreapplication.h"
class QPushButton;

namespace jrsaoi
{
    struct ImplData;
    class CustomListWidget :public QListWidget
    {
        Q_OBJECT

    public:
        CustomListWidget(QWidget* parent = nullptr);
        ~CustomListWidget();

        void AddItem(const std::string& group_name);
        std::vector<std::string> GetAllItemNames();
        /**调用示例
QMetaObject::invokeMethod(reminder_view,
                    "ReminderMessageShow",
                    Qt::QueuedConnection,
                    Q_ARG(std::string, name),
                    Q_ARG(jrscore::LogLevel, static_cast<jrscore::LogLevel>(i % 6)),
                    Q_ARG(std::string, name_content)
                );
*/
/**
* 提示信息显示接口
*/
//Q_INVOKABLE void ReminderMessageShow(const std::string& logName, const jrscore::LogLevel level, const std::string& msg);
    signals:
        void SigCurrentSelectValidItemName(const std::string& item_name_);
    protected:
        void mouseDoubleClickEvent(QMouseEvent* event) override;
        void keyPressEvent(QKeyEvent* event) override;
    private slots:
        void SlotMultipleAddItem(QListWidgetItem* item_);/**<手动添加项目*/
        void SlotShowContextMenu(const QPoint& pos_);

    private:
        void InitMember();
        void InitView();
        void InitConnect();
        void AddListItem(const std::string& img_path_, const std::string& item_name_);
        QListWidgetItem* LastItem() const;
        QPushButton* AddButton(const QString& txt_, const QString& icon_path_ = "");
        ImplData* _impl_data;
    };

}
#endif // !__CUSTOM_LIST_WIDGET_H__
