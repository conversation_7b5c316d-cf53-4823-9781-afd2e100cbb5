/*****************************************************************//**
* @file   inputlineeditdialog.h
* @brief  文字输入对话框
*
* <AUTHOR>
* @date   2024.8.21
*********************************************************************/
#ifndef INPUTLINEEDITDIALOG_H
#define INPUTLINEEDITDIALOG_H
#include <QDialog>
#include "ui_inputlineeditdialog.h"

class InputLineEditDialog : public QDialog
{
    Q_OBJECT
public:
    InputLineEditDialog(QWidget* parent = Q_NULLPTR);
    InputLineEditDialog(QString title, QString name, QWidget* parent = Q_NULLPTR);
    ~InputLineEditDialog();
public:
    /**
     * @fun GetInputText
     * @brief 获取输入文本
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    QString GetInputText();
private slots:
    /**
     * @fun LineEditTextChanged
     * @brief 输入文本改变消息
     * @param
     * @date 2024.9.24
     * <AUTHOR>
     */
    void LineEditTextChanged(QString text);
private:
    Ui::InputLineEditDialog ui;
    QString m_input_text;
};
#endif