/*****************************************************************//**
 * @file   module.h
 * @brief  模块类，以模块为单位进行主题创建发布订阅
 * @details
 * <AUTHOR>
 * @date 2024.1.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.22         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __MODULE_H__
#define __MODULE_H__
 //STD
#include <iostream>
#include <map>
//Custom
#include "topic.h"
namespace jrscore {

    class Module
    {
    public:
        explicit Module(const std::string& module_name_param);
        ~Module();
        /**
         * @fun AddAdvertise
         * @brief 添加主题
         * @param topic_name 主题名称
         * @return 状态码
         * @date 2024.1.22
         * <AUTHOR>
         */
        int AddAdvertise(const std::string& topic_name);
        /**
         * @fun RemoveAdvertise
         * @brief 删除主题
         * @param topic_name 删除的主题名称
         * @return
         * @date 2024.1.22
         * <AUTHOR>
         */
        int RemoveAdvertise(const std::string& topic_name);
        /**
         * @fun AddSubscriber
         * @brief 添加订阅者
         * @param topic_name_ 订阅的主题名称
         * @param call_ 订阅主题回调
         * @return 状态码
         * @date 2024.1.22
         * <AUTHOR>
         */
        int AddSubscriber(const std::string& topic_name_, SubscribeCallbackHelperPtr& call_);

        /**
         * @fun RemoveSubscriber
         * @brief 移除订阅者
         * @param topic_name_ 移除的主题
         * @param call_ 回调函数
         * @return  状态码
         * @date 2024.1.22
         * <AUTHOR>
         */
        int RemoveSubscriber(const std::string& topic_name_, SubscribeCallbackHelperPtr& call_);
        /**
         * @fun NotifyOne
         * @brief 通知一个订阅者
         * @param topic 主题名称
         * @param sub_name 订阅者名称
         * @return 状态码
         * @date 2024.1.22
         * <AUTHOR>
         */
        int NotifyOne(const std::string& topic_name_, const std::string& sub_name_, const std::vector<std::any>& args);

        /**
         * @fun NotifyAll
         * @brief 通知所有订阅者
         * @param topic 通知的主题名称
         * @return  状态码
         * @date 2024.1.22
         * <AUTHOR>
         */
        int NotifyAll(const std::string& topic_name_, const std::vector<std::any>& args);
    private:
        std::string module_name;
        std::recursive_mutex topic_events_map_mutex;
        std::map <std::string, TopicPtr> topic_events_map;
        std::vector <std::string> pub_topic;
    };
    using ModulePtr = std::shared_ptr<Module>;
}
#endif // !__MODULE_H__
