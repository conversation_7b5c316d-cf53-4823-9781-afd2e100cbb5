﻿/*****************************************************************
 * @file   flowcontroller.h
 * @brief  流程控制器，主要负责流程控制
 * @details 控制流程的启动，停止，暂停，恢复，负责流程中各个子流程的切换
 * <AUTHOR>
 * @date 2024.11.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.21          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSFLOWCONTROLLER_H__
#define __JRSFLOWCONTROLLER_H__
 //PREBUILD
#include "workflowpch.h"
 //STD
#include <thread>
#include <mutex>
//#include "workflowinterfaces.h"

//Third

namespace jrslogic
{
    class JrsAoiImgsManager;
}
namespace jrsdata
{
    struct JrsImageBuffer;
    struct ProjectParam;
    class DataManager;
}
namespace jrsalgo
{
    class AlgorithmEngineManager;
}
namespace jrsdevice
{
    class DeviceManager;
}
struct BoardIDCoors;
namespace jrsworkflow
{

    class ConditionWaiter;
    class FlowController : public IFlowController
    {

    public:
        explicit FlowController(const std::shared_ptr<jrsdevice::DeviceManager>& device_ptr_
            , const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_
            , const std::shared_ptr<jrsdata::DataManager>& data_manager_ptr_
            , LogicFunType logic_invoke_);
        virtual ~FlowController();
        void Start(const std::shared_ptr<jrsdata::DataBase>& project_param_) override;
        void Stop() override;
        void Pause() override;
        void Resume() override;
        void AddBuffer(const jrsdata::JrsImageBuffer& imgs) override;
        std::string GetCurrentStateName()override;
        void SetWorkFlowParam(const WorkFlowParam& param)override;

        void SetControlPanelCallback(const jrsdata::InvokeControlPanelViewParamFun& cb_) override;

    private:
        void Init();

        /**
         * @fun Execute
         * @brief 执行流程入口
         * @param project_param_ [IN] 工程参数
         * <AUTHOR>
         * @date 2024.11.22
         */
        void Execute(const std::shared_ptr<jrsdata::ProjectParam>& project_param_);

        /**
         * @fun UpdateMachineFlowState
         * @brief 更新机器流程状态
         * @param [IN]
         * <AUTHOR>
         * @date 2024.11.25
         */
        void UpdateMachineFlowState(std::string event_name, bool is_running_ = true);

        /**
         * @fun ExecuteFlowWithInterruptCheck 
         * @brief 执行自动流程模板函数，检测是否中断
         * @param func [IN] 流程函数
         * @param ...args [IN] 流程函数参数
         * @return 成功返回AOI_OK，失败返回错误码
         * <AUTHOR>
         * @date 2025.1.16
         */
        template<typename Func,typename... Args>
        int ExecuteFlowWithInterruptCheck(const Func& func, Args&&... args)
        {
            if (!is_running)
            {
                return jrscore::AOI_OK;
            }
            return func(std::forward<Args>(args)...);
        

        }

        int WaitForCondition();
        /**
         * @fun LoadFlow 
         * @brief 上料流程
         * @return 成功返回AOI_OK，失败返回错误码
         * <AUTHOR>
         * @date 2025.1.16
         */
        int LoadFlow();

        /**
         * @fun UnLoadFlow 
         * @brief 下料流程
         * @return 成功返回AOI_OK，失败返回错误码
         * <AUTHOR>
         * @date 2025.1.16
         */
        int UnLoadFlow();

        /**
         * @fun InspectionFlow 
         * @brief 检测流程
         * @return 成功返回AOI_OK，失败返回错误码
         * <AUTHOR>
         * @date 2025.1.16
         */
        int InspectionFlow();

        /**
         * @fun SaveResultFlow 
         * @brief 保存结果流程
         * @return  成功返回AOI_OK，失败返回错误码
         * <AUTHOR>
         * @date 2025.1.16
         */
        int SaveResultFlow();

        /**
         * @fun InitBarcodeDevice 
         * @brief 初始化扫码枪，每次启动流程的时候初始化一下扫码枪
         * <AUTHOR>
         * @date 2025.6.9
         */
        int InitBarcodeDevice();
        /**
         * @fun SetDeviceBarcode 
         * @brief 设置硬件条码
         * <AUTHOR>
         * @date 2025.6.9
         */
        void SetDeviceBarcode(const InspectionResultBasePtr& insp_result_ptr_);

        /**
         * @fun BarcodeCallback 
         * @brief 扫码抢回调
         * @param bar_code [IN] 扫码枪扫到的条码
         * <AUTHOR>
         * @date 2025.6.9
         */
        void BarcodeCallback(const std::string& bar_code);

        /**
         * @fun GetSubBoardIdAndCoors 
         * @brief 获取子板ID和坐标信息
         * @param project_param_ [IN] 工程参数
         * @return 返回查找的子板ID和坐标信息
         * <AUTHOR>
         * @date 2025.6.9
         */
        std::vector<BoardIDCoors> GetSubBoardIdAndCoors(const jrsdata::ProjectParamPtr& project_param_);
        IVisionInspectionFlowPtr vision_inspection_flow_controller_ptr;  //! 视觉检测流程控制器
        ITrackPtr track_controller_ptr; //! 轨道流程控制器
        IResultStoragePtr result_storage_controller_ptr; //! 结果存储控制器

        std::shared_ptr<jrsdevice::DeviceManager> device_manager_ptr;//! 设备管理实例
        std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_manager_ptr; //! 算法引擎实例
        std::shared_ptr<jrsdata::DataManager> data_manager_ptr; //! 数据管理指针
        std::shared_ptr<jrsdata::ProjectParam> project_param_ptr; //! 工程参数
        std::unique_ptr<ConditionWaiter> condition_waiter_ptr; //! 条件等待器,用于控制需要等待的条件
        std::thread work_flow_thread; /**< 整体流程线程*/
        std::mutex work_flow_mtx;     /**< 整体流程锁*/
        std::atomic<bool> is_running;/**< 是否在流程中*/
        std::string barcode_device_str;/**<硬件扫描到的条码*/

        //! TODO:后期优化，将扫图流程独立出来，在流程中直接扫图 by zhangyuyu 2024.11.20
        LogicFunType logic_invoke;
        jrsdata::InvokeControlPanelViewParamFun control_panel_call_back; /**< 控制面板界面回调函数*/
        jrsdata::MachineStateViewParamPtr machine_flow_state; /**< 机台流程状态*/
        WorkFlowParam work_flow_param; /**< 工作流参数*/

        enum class BarcodeType :int
        {
            NONE = 0, /**< 无条码*/
            HARDWARE, /**< 硬件条码,对应运控设置里面的外置条码*/
            SOFTWARE, /**< 软件条码，对应运控设置里面的内置条码*/
        };
    };

}

#endif // __JRSFLOWCONTROLLER_H__
