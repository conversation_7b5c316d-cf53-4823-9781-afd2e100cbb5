﻿#ifndef COMPONENTLIBVIEW_H
#define COMPONENTLIBVIEW_H

#include "componentlibrary.h"
#include "viewbase.h"
#include "viewergraphicsviewimpl.h"

#include <QWidget>
#include <QTableWidget>

namespace Ui
{
    class ComponentLibView;
}
using namespace jrsaoi;
class ComponentLibView : public ViewBase
{
    Q_OBJECT

public:
    explicit ComponentLibView(const std::string &name, QWidget *parent = nullptr);
    ~ComponentLibView();
    /**
     * @fun Init
     * @brief 初始化界面和数据模型。
     * @return [int] 初始化结果，返回 jrscore::AOI_OK 表示成功。
     * @date 2025.03.16
     * <AUTHOR>
     */
    virtual int Init() override;
    /**
     * @fun UpdateView
     * @brief 根据事件参数更新界面。
     * @param param_ [const jrsdata::ViewParamBasePtr&] 事件参数。
     * @return [int] 更新结果，返回 jrscore::AOI_OK 表示成功。
     * @date 2025.03.16
     * <AUTHOR>
     */
    virtual int UpdateView(const jrsdata::ViewParamBasePtr &param_) override;
    /**
     * @fun Save
     * @brief 保存当前视图参数。
     * @param param_ [const jrsdata::ViewParamBasePtr&] 需要保存的参数。
     * @return [int] 保存结果，返回 jrscore::AOI_OK 表示成功。
     * @date 2025.03.16
     * <AUTHOR>
     */
    virtual int Save(const jrsdata::ViewParamBasePtr &param_) override;

    // 设置元件库名称
    int SetComponentLibName(std::string name);

    // 获取元件库名称
    std::string GetComponentLibName();

    // 修改元件库地址
    void SetComponentLibFolderPath(const std::string path);
signals:
    void SigComponent(const jrsdata::ViewParamBasePtr& param);

private:
    // 初始化UI
    void InitView();
    
    // 初始化信号槽连接
    void InitConnection();
    
    // 查询元件库里面的元件信息
    void QueryComponents();

    // 显示查询结果
    void ShowQueryResult(const std::vector<jrsdata::ComponentEntity>& components);

    // 往数据表里面添加数据
    void AddRowToTable(QTableWidget* tableWidget, const QStringList rowData);

    // 获取选中项的料号
    std::string GetCurSelecetPartName();

    // 显示料号的显示图和画检测框
    void DrawComponent(const std::string part_name);

    /**
         * @fun GetRotatedRectByPadDirection
         * @brief   根据pad方向获取旋转矩形   输入和输出 均为相对坐标
         * @param component_center_point_
         * @param pad_center_point_
         * @param detect_window_rect_
         * @param src_direction_
         * @param obj_direction_
         * @return
         * <AUTHOR>
         * @date 2025.3.5
         */
    cv::RotatedRect GetRotatedRectByPadDirection(
        const cv::Point2f& component_center_point_, /**< 元件中心坐标*/
        const cv::Point2f& pad_center_point_,       /**<pad 中心坐标*/
        const cv::RotatedRect& detect_window_rect_,/**< 检测框中心坐标*/
        const jrsdata::ComponentUnit::Direction& src_direction_,  /**< 原始方向*/
        const jrsdata::ComponentUnit::Direction& obj_direction_ = jrsdata::ComponentUnit::Direction::UP
    );


private:
    Ui::ComponentLibView *ui;

    jrsaoi::ComponentLibraryPtr component_lib_ptr;

    jrsdata::OperateViewParamPtr component_control_view_ptr;

    ViewerGraphicsViewImpl* img_view;
};

#endif // COMPONENTLIBVIEW_H
