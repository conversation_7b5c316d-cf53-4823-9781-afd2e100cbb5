﻿#include "dbmanager.h"

jrsdatabase::DBManager::DBManager()
    :/*_db_ptr(std::make_shared<DBHandel>())*/
    _table_ptr(std::make_shared<TableManager>())
{
}

jrsdatabase::DBManager::DBManager(std::shared_ptr<IDatabase<MySqlImp>> db_conn_ptr_)
    :_table_ptr(std::make_shared<TableManager>())
{
    _db_conn_ptr = db_conn_ptr_;
}

jrsdatabase::DBManager::~DBManager()
{
}

int jrsdatabase::DBManager::ExecuteSQL(const std::string& sql_)
{
    return _db_conn_ptr->ExecuteSQL(sql_);
}

int jrsdatabase::DBManager::CustomUpdate(const std::vector<jrsdatabase::CustomUpdate>& custom_data_)
{
    // 开始事务
 //_db_conn_ptr->BeginAffair();

    std::string sql = "";
    int cnt = 0;

    // 遍历更新数据
    for (size_t i = 0; i < custom_data_.size(); ++i)
    {
        const auto& custom_updata_data = custom_data_[i];
        // 拼接当前SQL
        sql = _db_conn_ptr->GetUpdateCustomFieldsDataSQL(custom_updata_data.table_name, custom_updata_data.fields_and_new_value_map, custom_updata_data.condition);
        ++cnt;
        auto res = _db_conn_ptr->ExecuteSQL(sql);
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR("数据更新失败:", sql);
        }
        // 每1000条提交一次
        //if (cnt == 1000 || i == custom_data_.size() - 1)  // 最后一批数据也需要提交
        //{
        //    if (res != jrscore::AOI_OK || _db_conn_ptr->CommitAffair() != jrscore::AOI_OK)
        //    {
        //        Log_ERROR("数据更新失败! 批次 %zu 更新失败.", i / 1000 + 1);
        //        _db_conn_ptr->Rollback();
        //        return jrscore::DataManagerError::E_JRS_DB_UNKNOWN;
        //    }
        //    cnt = 0;
        //    _db_conn_ptr->BeginAffair();

        //}
    }

    return jrscore::AOI_OK;
}

int jrsdatabase::DBManager::DeleteTables(const std::unordered_map<std::string, std::string>& tables_and_where_conditions_)
{
    auto get_sql_fun = [=](const std::string& table_, const std::string& where_conditions_) -> std::string
        {
            std::string sql = "delete from " + table_ + " where " + where_conditions_ + " ;";
            return sql;
        };
    for (const auto& [table_name_, where_condition_] : tables_and_where_conditions_)
    {
        auto sql = get_sql_fun(table_name_, where_condition_);
        auto res = ExecuteSQL(sql.data());
        if (res != jrscore::AOI_OK)
        {
            Log_ERROR(table_name_, "表删除失败", where_condition_, " error:", _db_conn_ptr->GetLastError());
        }
    }
    return jrscore::AOI_OK;
}

int jrsdatabase::DBManager::Select(const jrsselect::SelectorParamBasePtr& selector_ptr_)
{
    if (!selector_ptr_)
    {
        Log_Error_Stack("获取指针为空");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    auto table_temp_ptr = _table_ptr->GetTable(selector_ptr_->table_name);
    if (!table_temp_ptr && !_db_conn_ptr)
    {
        Log_Error_Stack("获取指针为空，请检查结构体是否已经注册");
        return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
    }
    return table_temp_ptr->Select(selector_ptr_, _db_conn_ptr);
}

int jrsdatabase::DBManager::InitMember()
{
    return 0;
}
/*int jrsdatabase::DBManager::InitDatabase()
{
    if (!_db_ptr)
    {
        Log_Error_Stack("数据库指针为空");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    std::string db_name = "jrs_database_" + jrscore::ControlCenterTools::GetCurrentDataTime("%Y_%m_%d");

    jrsdatabase::DatabaseConnectParam db_param( "127.0.0.1", "jrs", "Jrs123456", db_name,50 );
    auto res = _db_ptr->Create(db_param);
    if (res!=jrscore::AOI_OK)
    {
        LogRun_INFO("数据库", db_name, "创建失败!");
        return res;
    }
    InitTable();
    return jrscore::AOI_OK;
}
*/
int jrsdatabase::DBManager::InitTable()
{
    /*< 创建注册的所有表 */
    _table_ptr->InitTables(_db_conn_ptr);

    return 0;
}

/** Object Pool */
std::atomic<bool> jrsdatabase::DBManagers::_is_init_pool = false;
ObjectPool<jrsdatabase::DBManager> jrsdatabase::DBManagers::_db_obj_pool;
jrsdatabase::DBPtr jrsdatabase::DBManagers::_db_ptr = std::make_shared<jrsdatabase::DBHandel>();
jrsdatabase::DBManagers::DBManagers()
{
}

jrsdatabase::DBManagers::~DBManagers()

{
}

int jrsdatabase::DBManagers::InitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_)
{
    if ((db_conn_param_.max_connect < 1 || db_conn_param_.max_connect > 100))
    {
        return jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
    }
    if (_is_init_pool)
    {
        return jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_REPEAT_INIT;//初始化 init 信息
    }
    try {
        //先建表， 再搞其他的
        auto res = _db_ptr->Create(db_conn_param_);
        if (res != jrscore::AOI_OK)
        {
            Log_Error_Stack("创建数据库链接失败，请检查!");
            return res;
        }
    }
    catch (...) {
        std::cerr << "this init connect pool error!" << std::endl;
        return jrscore::DataManagerError::E_AOI_DB_CONNECTION_POOL_INIT;
    }
    int res = _db_obj_pool.InitObjectPool(db_conn_param_.max_connect, _db_ptr);
    if (res) {
        return res;
    }

    _is_init_pool = true;

    /** <初始化表 一次即可*/
    auto db_ptr = _db_obj_pool.AcquireObject();
    if (db_ptr)
    {
        res = db_ptr->InitTable();
        _db_obj_pool.ReturnBack(db_ptr);
    }
    else
    {
        Log_ERROR("数据库初始化失败。");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    return res;
}

int jrsdatabase::DBManagers::ReinitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_)
{
    _db_obj_pool.DestroyAllObjects();
    _is_init_pool = false;
    return InitDatabase(db_conn_param_);
}

int jrsdatabase::DBManagers::DisconnectDatabase()
{
    _db_obj_pool.DestroyAllObjects();
    _is_init_pool = false;
    return jrscore::AOI_OK;
}

bool jrsdatabase::DBManagers::GetDBState()
{
    return _is_init_pool;
}

int jrsdatabase::DBManagers::Select(const jrsdatabase::jrsselect::SelectorParamBasePtr& selector_ptr_)
{
    if (!GetDBState()) 
    {
        Log_ERROR("数据库未连接，请检查");
        return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
    }
        
    auto db_ptr = _db_obj_pool.AcquireObject();
    if (db_ptr)
    {
        auto res = db_ptr->Select(selector_ptr_);
        _db_obj_pool.ReturnBack(db_ptr);
        return res;
    }
    else
    {
        Log_ERROR("获取数据连接失败。");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
}

int jrsdatabase::DBManagers::CustomUpdate(const std::vector<jrsdatabase::CustomUpdate>& custom_data_)
{
    if (!GetDBState())
        return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
    auto db_ptr = _db_obj_pool.AcquireObject();
    if (db_ptr)
    {
        auto res = db_ptr->CustomUpdate(custom_data_);
        _db_obj_pool.ReturnBack(db_ptr);
        return res;
    }
    else
    {
        Log_ERROR("获取数据连接失败。");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
}

int jrsdatabase::DBManagers::DeleteTables(const std::unordered_map<std::string, std::string>& tables_and_where_conditions_)
{
    if (!GetDBState())
        return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
    auto db_ptr = _db_obj_pool.AcquireObject();
    if (db_ptr)
    {
        auto res = db_ptr->DeleteTables(tables_and_where_conditions_);
        _db_obj_pool.ReturnBack(db_ptr);
        return res;
    }
    else
    {
        Log_ERROR("获取数据连接失败。");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
}

int jrsdatabase::DBManagers::ExecuteSQL(const std::string& sql_)
{
    if (!GetDBState())
        return jrscore::DataManagerError::E_AOI_DB_DISCONNECT;
    auto db_ptr = _db_obj_pool.AcquireObject();
    if (db_ptr)
    {
        auto res = db_ptr->ExecuteSQL(sql_);
        _db_obj_pool.ReturnBack(db_ptr);
        return res;
    }
    else
    {
        Log_ERROR("获取数据连接失败。");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
}

int jrsdatabase::DBManagers::DeleteTable(const std::string& table_name_)
{
    std::string delete_sql_str = "TRUNCATE " + table_name_;
    return ExecuteSQL(delete_sql_str);  // 假设返回影响行数或 0 表示成功
}
