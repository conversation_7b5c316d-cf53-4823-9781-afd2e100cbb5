#include "InputLineEditDialog.h"
#include <QPushButton>

InputLineEditDialog::InputLineEditDialog(QWidget* parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    m_input_text.clear();
    connect(ui.input, SIGNAL(textChanged(QString)), this, SLOT(LineEditTextChanged(QString)));
    connect(ui.btn_ok, &QPushButton::clicked, this, &InputLineEditDialog::accept);
    connect(ui.btn_cancel, &QPushButton::clicked, this, &InputLineEditDialog::reject);
}

InputLineEditDialog::InputLineEditDialog(QString title, QString name, QWidget* parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setWindowTitle(title);
    ui.name->setText(name);
    m_input_text.clear();
    connect(ui.input, SIGNAL(textChanged(QString)), this, SLOT(LineEditTextChanged(QString)));
    connect(ui.btn_ok, &QPushButton::clicked, this, &InputLineEditDialog::accept);
    connect(ui.btn_cancel, &QPushButton::clicked, this, &InputLineEditDialog::reject);
}

InputLineEditDialog::~InputLineEditDialog()
{
}

QString InputLineEditDialog::GetInputText()
{
    return m_input_text;
}

void InputLineEditDialog::LineEditTextChanged(QString text)
{
    m_input_text = ui.input->text();
}