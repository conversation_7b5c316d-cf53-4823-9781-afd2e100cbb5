#include <QtGui/qevent.h>
#include <qgraphicsitem.h>
#include <qscrollbar.h>
#include "GraphicsView2D.h"

bool isCtrlDown = false;
bool lButtonDown = false;

GraphicsView2D::GraphicsView2D()
{
	setMouseTracking(true);
	CreateViewButton();
}

void GraphicsView2D::CreateViewButton()
{
	this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff); // 禁用水平滚动条
	this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);   // 禁用垂直滚动条
	//m_btn_pre_image = new QPushButton(this);
	//m_btn_pre_image->resize(30, 64);
	//m_btn_pre_image->setIcon(QIcon(":/image/left.png"));
	//m_btn_pre_image->setToolTip(tr("显示前一张"));

	//m_btn_next_image = new QPushButton(this);
	//m_btn_next_image->resize(30, 64);
	//m_btn_next_image->setIcon(QIcon(":/image/right.png"));
	//m_btn_next_image->setToolTip(tr("显示后一张"));

	//connect(m_btn_pre_image, &QPushButton::clicked, this, &GraphicsView2D::OnShowPreImage);
	//connect(m_btn_next_image, &QPushButton::clicked, this, &GraphicsView2D::OnShowNextImage);
}

void GraphicsView2D::OnShowPreImage(bool checked)
{
	(void)checked;
	emit SigShowPreImage();
}

void GraphicsView2D::OnShowNextImage(bool checked)
{
	(void)checked;
	emit SigShowNextImage();
}

void GraphicsView2D::mouseMoveEvent(QMouseEvent* event)
{
	QGraphicsView::mouseMoveEvent(event);

	if (!scene()->selectedItems().isEmpty() && lButtonDown)
	{
		//setDragMode(QGraphicsView::ScrollHandDrag);
		return;
	}

	if (isCtrlDown)
	{
		auto scenePoint = mapToScene(event->pos());
		emit SendSecenPosToGetGrayVal(scenePoint);
	}

	if (m_is_draw && lButtonDown)
	{
		auto draw_end_pos = mapToScene(event->pos());
		QRectF draw_rect = QRectF(m_down_start_pos, draw_end_pos).normalized();
		emit SigSetCurDrawRect(draw_rect);	
		return;
	}

	if (m_is_pan)
	{
		Translate(event->pos());
		event->accept();

		auto scenePoint = mapToScene(0, 0);
		SigLeftTopPosChanged(scenePoint);

		//return;
	}
	//event->ignore();
}

void GraphicsView2D::mouseDoubleClickEvent(QMouseEvent* event)
{
	QGraphicsView::mouseDoubleClickEvent(event);
	if (event->button() == Qt::LeftButton)
	{
		FitWin();
	}
}

void GraphicsView2D::mousePressEvent(QMouseEvent* event)
{
	QGraphicsView::mousePressEvent(event);

	if (event->button() == Qt::LeftButton)
	{
		lButtonDown = true;
		if (m_is_click_grab)
		{
			auto scenePoint = mapToScene(event->pos());
			emit SendSecenPosMoveCamera(scenePoint);
		}
		else if(m_is_draw)
		{
			m_down_start_pos = mapToScene(event->pos());
			setCursor(Qt::CrossCursor);
		}
		else
		{
			TogglePan(true, event->pos());
			event->accept();
		}
		return;
	}
	event->ignore();
}

void GraphicsView2D::mouseReleaseEvent(QMouseEvent* event)
{
	QGraphicsView::mouseReleaseEvent(event);

	if (event->button() == Qt::LeftButton)
	{
		lButtonDown = false;
		if (m_is_draw)
		{
			setCursor(Qt::ArrowCursor);
			emit SigEndDraw();
			m_is_draw = false;
		}
		else
		{
			TogglePan(false);
			event->accept();
			return;
		}
	}
	if (event->button() == Qt::RightButton)
	{
		setCursor(Qt::ArrowCursor);
		return;
	}

	event->ignore();
}

void GraphicsView2D::wheelEvent(QWheelEvent* event)
{
	QGraphicsView::wheelEvent(event);

	QPoint numDegrees = event->angleDelta() / 8;
	if (!numDegrees.isNull()) {
		QPoint numSteps = numDegrees / 15;
		Zoom(numSteps);

		auto scenePoint = mapToScene(0, 0);
		SigLeftTopPosChanged(scenePoint);
	}
	event->accept();
}

void GraphicsView2D::keyPressEvent(QKeyEvent* event)
{
	QGraphicsView::keyPressEvent(event);

	if (event->key() == Qt::Key_Control)
	{
		isCtrlDown = true;
	}
}

void GraphicsView2D::keyReleaseEvent(QKeyEvent* event)
{
	QGraphicsView::keyReleaseEvent(event);

	if (event->key() == Qt::Key_Control)
	{
		isCtrlDown = false;
	}
}

void GraphicsView2D::resizeEvent(QResizeEvent* event)
{
	(void)event;
	//QGraphicsView::resizeEvent(event);
	//auto rect = this->rect();
	//m_btn_pre_image->move(0, rect.center().y() - m_btn_pre_image->height() / 2);
	//m_btn_next_image->move(rect.width() - m_btn_next_image->width(), rect.center().y() - m_btn_next_image->height() / 2);
}

void GraphicsView2D::SetImageSize(const QSize size)
{
	if (m_img_size == size)
	{
		return;
	}
	else
	{
		m_img_size = size;
		//FitWin();
	}
}

void GraphicsView2D::SetClickGrab(const bool& status)
{
	if (m_is_click_grab == false && status == true)
	{
		emit SendSecenPosMoveCamera(QPointF(m_img_size.width() / 2.0, m_img_size.height() / 2.0));
	}
	m_is_click_grab = status;
}

void GraphicsView2D::SetDrawFlag(const bool& status)
{
	m_is_draw = status;
}

void GraphicsView2D::FitWin()
{
	this->resetTransform();
	this->setSceneRect(QRect(0, 0, m_img_size.width(), m_img_size.height()));
	this->fitInView(QRect(0, 0, m_img_size.width(), m_img_size.height()), Qt::KeepAspectRatio);
}

void GraphicsView2D::Translate(const QPoint& panTo)
{
	auto hBar = horizontalScrollBar();
	auto vBar = verticalScrollBar();
	auto delta = panTo - m_prev_pan;
	m_prev_pan = panTo;
	hBar->setValue(hBar->value() - delta.x());
	vBar->setValue(vBar->value() - delta.y());
}

void GraphicsView2D::TogglePan(bool pan, const QPoint& startPos)
{
	if (pan)
	{
		if (m_is_pan)
		{
			return;
		}
		m_is_pan = true;
		m_prev_pan = startPos;
		setCursor(Qt::ClosedHandCursor);
	}
	else
	{
		if (!m_is_pan)
		{
			return;
		}
		m_is_pan = false;
		m_prev_pan = QPoint(0, 0);
		setCursor(Qt::ArrowCursor);
	}
}

void GraphicsView2D::Zoom(QPoint factor)
{
	QRectF FOV = this->mapToScene(this->rect()).boundingRect();
	QRectF FOVImage = QRectF(FOV.left(), FOV.top(), FOV.width(), FOV.height());
	float scaleX = static_cast<float>(m_img_size.width()) / FOVImage.width();
	float scaleY = static_cast<float>(m_img_size.height()) / FOVImage.height();
	float minScale = scaleX > scaleY ? scaleY : scaleX;
	float maxScale = scaleX > scaleY ? scaleX : scaleY;
	if ((factor.y() > 0 && minScale > 100) || (factor.y() < 0 && maxScale < 1)) {
		return;
	}
	if (factor.y() > 0)
		scale(1.2, 1.2);
	else
		scale(0.8, 0.8);
}