﻿/*****************************************************************//**
 * @file   dataview.h
 * @brief  文件路径数据界面
 *
 * <AUTHOR>
 * @date   2024.10.17
 *********************************************************************/
#ifndef DATA_VIEW
#define DATA_VIEW
 // prebuild
#include "ui_dataview.h"
#include "dataviewdatastruct.h"
#include "pch.h"
//QT
#include <QWidget>
#include <QMap>
#include <QVector>
#include <QComboBox>
#include <QPushButton>
#include <QButtonGroup>
//CUSTOM
//#include "viewparam.hpp"
QT_BEGIN_NAMESPACE
namespace Ui
{
    class DataView;
};
QT_END_NAMESPACE

class DataView : public QWidget
{
    Q_OBJECT
private:
    struct ParamNameMap {
        QString china_name;
        std::string save_name;
    };
public:
    DataView(QWidget* parent = Q_NULLPTR);
    ~DataView();
    void SaveSettingFile();
    void UpdateView(const jrsdata::MachineParam& machine_param_);
    void UpdateView();
    void ConnectSlots();
    void DisconnectSlots();
signals:
    void SigUpdateMachineParam(const jrsdata::MachineParam& machine_param_);
private slots:
    void SlotDataChange(QString label, SettingData setting_data);
    void SlotClean();
    void SlotRead();

    void SlotRadioChecked(QAbstractButton*);

   



private:

    /**
    * @fun InitConnect
    * @brief 初始化信号连接
    * <AUTHOR>
    * @date 2025.1.12
    *
    **/
    void InitConnect();


    std::string SettingDataToJson(const SettingData& setting_);
    SettingData JsonToSettingData(const std::string& setting_);
    Ui::DataView* ui;
    QMap<QString, SettingData> map_directory;


    std::unordered_map<QString, std::string> _param_name_map;
    jrsdata::MachineParam _machine_param; /**<机台参数*/
    std::atomic<bool> _is_enable_save_param;


};
#endif
