﻿#ifndef _MOUSE_STATE_COMTROL_H
#define _MOUSE_STATE_COMTROL_H
/*****************************************************************
 * @file   mousestatecontrol.hpp
 * @brief  鼠标状态的控制，显示 及一些操作   ------------///! 预计拆分但还未使用
 * @details
 * <AUTHOR>
 * @date 2025.1.7
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.1.7          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
#include <iostream>
 //Custom
 //Third
struct MouseEventValue;
class GraphicsManager;
namespace jrsrender
{
    struct ImplData;
    class MouseStateControl
    {
    public:
        MouseStateControl(std::shared_ptr<GraphicsManager> graphics_manager_ptr_);
        ~MouseStateControl();

        // 处理图形添加事件
        void HandlerMouseGraphicsResponse(const MouseEventValue& value);
        void HandlerMouseGraphicsAdd(const MouseEventValue& value);
        void AddRect(const MouseEventValue& value);
        void AddCircle(const MouseEventValue& value);
        void AddPolygon(const MouseEventValue& value);
        void AddBezier(const MouseEventValue& value);
        void AddSG(const MouseEventValue& value);
        void AddMR(const MouseEventValue& value);
        void AddPad(const MouseEventValue& value);
        void AddSelectBatchPolygon(const MouseEventValue& value);

        // 处理窗口鼠标事件
        void HandlerRendermouseclicked(int type, int x, int y);
        void HandlerRendermousepress(int type, int x, int y);
        void HandlerRendermousemove(int type, int icx, int icy, int ilx, int ily, int ipx, int ipy);
        void HandlerRendermouserelease(int type, int cx, int cy, int px, int py);
        void HandlerRenderwheeldelta(int delta, int x, int y);
        void HandlerThumbnailmousemove(int type, float x, float y);
        void HandlerWindowsizechange(int w, int h);


    private:
        void InitMember();
        ImplData* _impl_data;
    };

    using MouseStateControlPtr = std::shared_ptr<MouseStateControl>;
}

#endif //!_MOUSE_STATE_COMTROL_H
