/*****************************************************************//**
 * @file   motionprotocal.h
 * @brief  生成运动控制指令， 目前AOI与运控软件是通过G代码形式进行通信的
 * @details
 * <AUTHOR>
 * @date 2024.8.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.5         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

//STD
//Custom
//Third

#ifndef __MOTIONPROTOCAL_H__
#define __MOTIONPROTOCAL_H__

#include <iostream>
#include <mutex>
#include <thread>
#include <chrono>
#include <sstream>

#include "nlohmann/json.hpp"

using JSON = nlohmann::json;

namespace jrsdevice
{


	class MotionProtocal
	{
	public:
		// 构建命令检查包
		JSON ConstructCheckerPack(const std::string sendMsg, const std::string check, const int checkTime, const bool justSend = false);

		// 机构消息解析
		JSON MotionMsgDecode(const std::string msg);

		// 构造机构返回数据包
		JSON ConstructFeedBackPack(const std::string check, const JSON data);

	private:
		// cmd : result, errMsg\r\n 格式的指令的解析
		JSON FormatCheck(const std::string cmd, const std::string motionMsgSepColon);

		// 位置消息解析
		JSON PosMsgDecode(const std::string check, const std::string msg);

		// 流程状态查询消息解析
		JSON StatusMsgDecode(const std::string check, const std::string msg);

		// 限位消息解析
		JSON AXISLIMITMsgDecode(const std::string check, const std::string msg);

		// 流程状态消息解析
		JSON PROCESSStatusMsgDecode(const std::string check, const std::string msg);

		// 运控当前配置消息解析
		JSON CURSETTINGMsgDecode(const std::string check, const std::string msg);

		// 输入状态消息解析
		JSON InputStatusMsgDecode(const std::string check, const std::string msg);

		// 输出状态消息解析
		JSON OutputStatusMsgDecode(const std::string check, const std::string msg);

		// 脚本执行失败的消息解析
		JSON ScriptErrorMsgDecode(const std::string check, const std::string msg);

		// 推送消息解析
		JSON PushMsgDecode(const std::string cmd, const std::string msg);

		// 脚本消息解析
		JSON ScriptMsgDecode(const std::string check, const std::string msg);

		// ASKSTOP ASKPAUSE PHOTOOK REPHOTO OFFSET SENDNC ASKSTART GROUPHOME 等带流程索引的消息解析
		JSON ProcessMsgDecode(const std::string check, const std::string msg);


		std::vector<std::string> SplitString(const std::string &s, char delimiter);
	};

	using MotionProtocalPtr = std::shared_ptr<MotionProtocal>;

}
#endif
