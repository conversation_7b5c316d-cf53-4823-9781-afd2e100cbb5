﻿//Custom
#include "custommessagebox.h"
#include "coreapplication.h"

////QT
#include <QIcon>
#include <QMessageBox>
#include <QEventLoop>
#include <QThread>
#include <QCoreApplication>
namespace jrsaoi
{
    struct ImplData
    {
        QIcon* icon;
        QMessageBox* message_box_multiple_thread; /**<多线程下的*/
        QMessageBox* message_box_main_thread;  /**<主线程下的*/
    };
    CustomMessageBox::CustomMessageBox(QWidget* parent)
        : MessageBase(parent), _parent(parent)
    {
        Init();
    }

    CustomMessageBox::~CustomMessageBox()
    {
    }
    jrscore::MessageButton CustomMessageBox::ShowMessage(const jrsaoi::MessageBase::Message& msg_)
    {
        if (QThread::currentThread() == QCoreApplication::instance()->thread())
        {
            _is_ready = false;
            auto res = DisplayMessageBoxMainThread(msg_);
            NotifyCondition();
            return res;
        }
        else
        {
            //入队列

            /**< 处理 */
            return ThreadOperator(msg_);
        }
    }



    void CustomMessageBox::Init()
    {
        InitMember();
        InitView();
        InitConnect();
    }

    void CustomMessageBox::InitMember()
    {
        _impl_data = new ImplData();
        _impl_data->message_box_multiple_thread = new QMessageBox(_parent);
        _impl_data->message_box_main_thread = new QMessageBox(_parent);
        _is_ready = true;
    }

    void CustomMessageBox::InitView()
    {
        _impl_data->message_box_multiple_thread->setWindowIcon(QIcon(":image/JRS.ico"));
        _impl_data->message_box_main_thread->setWindowIcon(QIcon(":image/JRS.ico"));

        _impl_data->message_box_multiple_thread->setWindowFlags(
            (_impl_data->message_box_multiple_thread->windowFlags() & ~Qt::WindowCloseButtonHint) | Qt::WindowStaysOnTopHint
        );
        _impl_data->message_box_main_thread->setWindowFlags(
            (_impl_data->message_box_multiple_thread->windowFlags() & ~Qt::WindowCloseButtonHint) | Qt::WindowStaysOnTopHint
        );
    }

    void CustomMessageBox::InitConnect()
    {
        QObject::connect(this, &CustomMessageBox::SigUpdateMessageBoxResualt,
            [&](const jrscore::MessageButton& res_btn_, const std::string& time_) {
                std::lock_guard<std::recursive_mutex> locker(_map_mtx);
                auto it = _mutexs_and_wait_conditions.find(time_);
                if (it != _mutexs_and_wait_conditions.end())
                {
                    auto& [result, mutex, condition] = it->second;
                    result = res_btn_;
                    QMutexLocker locker_temp(mutex.get());
                    condition->wakeOne();
                }
            });

    }

    jrscore::MessageButton CustomMessageBox::ThreadOperator(const jrsaoi::MessageBase::Message& msg_)
    {
        {
            std::lock_guard<std::recursive_mutex> locker(_map_mtx);
            _mutexs_and_wait_conditions[msg_.time] = { jrscore::MessageButton::NoButton,
                                                    std::make_unique<QMutex>(),
                                                    std::make_unique<QWaitCondition>() };
        }

        WaitForCondition();

        // 异步调用 ShowMessage
        QMetaObject::invokeMethod(
            this,
            "DisplayMessageBoxMultipleThread",
            Qt::QueuedConnection,
            Q_ARG(const jrsaoi::MessageBase::Message&, msg_)
        );

        // 等待信号返回
        {
            auto& [resuk, mutex, condition] = _mutexs_and_wait_conditions[msg_.time];
            (void)resuk;
            QMutexLocker locker_temp(mutex.get());
            condition->wait(mutex.get());
        }

        std::lock_guard<std::recursive_mutex> locker(_map_mtx);
        auto temp_res_btn = std::get<0>(_mutexs_and_wait_conditions[msg_.time]);
        _mutexs_and_wait_conditions.erase(msg_.time);

        NotifyCondition();
        return temp_res_btn;
    }

    jrscore::MessageButton CustomMessageBox::DisplayMessageBoxMainThread(const jrsaoi::MessageBase::Message& msg_)
    {
        /*if (msg_.level == jrscore::LogLevel::LEVEL_INFO)
        {
            _impl_data->message_box_main_thread->setIcon(QMessageBox::Information);
        }
        else if (msg_.level == jrscore::LogLevel::LEVEL_WARN)
        {
            _impl_data->message_box_main_thread->setIcon(QMessageBox::Warning);
        }
        else if (msg_.level == jrscore::LogLevel::LEVEL_ERROR)
        {
            _impl_data->message_box_main_thread->setIcon(QMessageBox::Critical);
        }
        else
        {
            _impl_data->message_box_main_thread->setIcon(QMessageBox::NoIcon);
        }
        _impl_data->message_box_main_thread->setWindowTitle(QString::fromStdString(msg_.title));
        _impl_data->message_box_main_thread->setText(QString::fromStdString(msg_.msg));
        _impl_data->message_box_main_thread->setStandardButtons(static_cast<QMessageBox::StandardButtons>(msg_.message_button));
        auto standard_button = _impl_data->message_box_main_thread->exec();
        return static_cast<jrscore::MessageButton> (standard_button);*/
        QMessageBox message_box;
        message_box.setWindowFlag(Qt::WindowStaysOnTopHint);
        if (msg_.level == jrscore::LogLevel::LEVEL_INFO)
            message_box.setIcon(QMessageBox::Information);
        else if (msg_.level == jrscore::LogLevel::LEVEL_WARN)
            message_box.setIcon(QMessageBox::Warning);
        else if (msg_.level == jrscore::LogLevel::LEVEL_ERROR)
            message_box.setIcon(QMessageBox::Critical);
        else
            message_box.setIcon(QMessageBox::NoIcon);

        message_box.setWindowTitle(QString::fromStdString(msg_.title));
        message_box.setText(QString::fromStdString(msg_.msg));
        message_box.setStandardButtons(static_cast<QMessageBox::StandardButtons>(msg_.message_button));

        auto standard_button = message_box.exec();
        return static_cast<jrscore::MessageButton>(standard_button);
    }

    void CustomMessageBox::WaitForCondition()
    {
        std::unique_lock<std::mutex> global_lock(_global_mtx);
        _condition_var.wait(global_lock, [this] { return _is_ready; });
        _is_ready = false;

    }

    void CustomMessageBox::NotifyCondition()
    {
        _is_ready = true;
        _condition_var.notify_one();

    }

    Q_INVOKABLE void CustomMessageBox::DisplayMessageBoxMultipleThread(const jrsaoi::MessageBase::Message& msg_)
    {
        if (msg_.level == jrscore::LogLevel::LEVEL_INFO)
        {
            _impl_data->message_box_multiple_thread->setIcon(QMessageBox::Information);
        }
        else if (msg_.level == jrscore::LogLevel::LEVEL_WARN)
        {
            _impl_data->message_box_multiple_thread->setIcon(QMessageBox::Warning);
        }
        else if (msg_.level == jrscore::LogLevel::LEVEL_ERROR)
        {
            _impl_data->message_box_multiple_thread->setIcon(QMessageBox::Critical);
        }
        else
        {
            _impl_data->message_box_multiple_thread->setIcon(QMessageBox::NoIcon);
        }
        _impl_data->message_box_multiple_thread->setWindowTitle(QString::fromStdString(msg_.title));
        _impl_data->message_box_multiple_thread->setText(QString::fromStdString(msg_.msg));
        _impl_data->message_box_multiple_thread->setStandardButtons(static_cast<QMessageBox::StandardButtons>(msg_.message_button));
        auto standard_button = _impl_data->message_box_multiple_thread->exec();
        auto res_btn = static_cast<jrscore::MessageButton> (standard_button);
        emit SigUpdateMessageBoxResualt(res_btn, msg_.time);
    }

}
