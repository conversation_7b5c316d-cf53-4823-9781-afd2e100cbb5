/*****************************************************************//**
 * @file   StatusBarView.h
 * @brief  自定义标题栏样式
 * @details
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __STATUSBARVIEW_H__
#define __STATUSBARVIEW_H__
 //QT
#pragma warning(push,1)
#include <QWidget>
#include <QMainWindow>
#include <QGridLayout>
#include <QPushButton>
#pragma warning(pop)

namespace jrsaoi
{
    class StatusBarView :public QWidget
    {
        Q_OBJECT
    public:
        StatusBarView(QWidget* parent = nullptr);
        ~StatusBarView();
    private:
        QHBoxLayout* layout_view;
        QLabel* status_camera;
        QLabel* status_x;
        QLabel* status_y;
        QLabel* status_z;
        QLabel* status_card_1;
        QLabel* status_card_2;
        QLabel* status_dlp_1;
        QLabel* status_dlp_2;
        QLabel* status_dlp_3;
        QLabel* status_dlp_4;
    private:
        /**
        * @fun Init
        * @brief 初始化
        * @date 2024.1.15
        * <AUTHOR>
        */
        void Init();
        /**
         * @fun InitMember
         * @brief
         * @date 2024.2.19
         * <AUTHOR>
         */
        void InitMember();
        /**
         * @fun InitView
         * @brief 初始化界面
         * @date 2024.1.15
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun GetLine 
         * @brief
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        QFrame* GetLine();
        /**
         * @fun GetLabel 
         * @brief
         * @param name
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        QLabel* GetLabel(std::string name);
        /**
         * @fun SetLabelColor 
         * @brief
         * @param label
         * @param status
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SetLabelColor(QLabel* label, bool status);
    };
}
#endif
