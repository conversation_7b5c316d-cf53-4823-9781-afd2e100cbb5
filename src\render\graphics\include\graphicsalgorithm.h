/*********************************************************************
 * @brief  图形处理中使用到的算法,主要是opencv.
 *
 * @file   graphicsalgorithm.h
 *
 * @date   2024.03.19
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef GRAPHICS_ALGO_DEFINE_H
#define GRAPHICS_ALGO_DEFINE_H

#include "algodefine.hpp"
 // #include "graphicsdefine.hpp"
#include "rvec.hpp"

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "opencv2/core/types.hpp"
#include "opencv2/imgproc.hpp"
#pragma warning(pop)

// 计算向量长度
template <typename T>
T VectorLength(const cv::Point_<T>& vector)
{
    return std::sqrt(vector.x * vector.x + vector.y * vector.y);
}

/**
 * @brief 计算二维向量的倾斜角
 * @param x 向量的x分量
 * @param y 向量的y分量
 * @return 向量的倾斜角，范围为(-180°, 180°]
 */
template <typename T>
double GetVectorAngle(T x, T y)
{
    double angleRad = atan2(y, x);
    return A_RAD_TO_DEG(angleRad);
}

/**
 * @brief 计算A到B的二维向量的倾斜角
 * @param xa a点的x分量
 * @param ya a点的y分量
 * @param xb b点的x分量
 * @param yb b点的y分量
 * @return 向量的倾斜角，范围为(-180°, 180°]
 */
template <typename T>
double GetVectorA2BAngle(T xa, T ya, T xb, T yb)
{
    double vectorX = xb - xa;
    double vectorY = yb - ya;

    // 计算向量与 x 轴正方向的夹角（弧度）
    double angleRad = atan2(vectorY, vectorX);

    return A_RAD_TO_DEG(angleRad);
}

/**
 * @brief 计算两点构成的向量与 x 轴正方向的夹角.
 *
 * @param x1 第一个点的 x 坐标
 * @param y1 第一个点的 y 坐标
 * @param x2 第二个点的 x 坐标
 * @param y2 第二个点的 y 坐标
 *
 * @return double 向量与 x 轴正方向的夹角（弧度）
 *
 * @date   2024.01.19
 * <AUTHOR>
 */
template <typename T>
T angleWithXAxis(T x1, T y1, T x2, T y2)
{
    // 计算向量的 x 和 y 分量
    double vectorX = x2 - x1;
    double vectorY = y2 - y1;

    // 计算向量与 x 轴正方向的夹角（弧度）
    double angleRad = atan2(vectorY, vectorX);

    return A_RAD_TO_DEG(angleRad);
}

// 计算两个向量的夹角（弧度）
template <typename T>
T calculateAngle(T avx, T avy, T bvx, T bvy)
{
    // 计算点积
    float dotProduct = avx * bvx + avy * bvy;

    // 计算向量模
    float magnitudeA = std::sqrt(avx * avx + avy * avy);
    float magnitudeB = std::sqrt(bvx * bvx + bvy * bvy);

    // 计算夹角（弧度）
    float cosTheta = dotProduct / (magnitudeA * magnitudeB);
    float angleRad = std::acos(cosTheta);

    return angleRad;
}

/**
 * @fun   ProjectVector
 * @brief 计算向量a在向量b上的投影向量.
 * @param a
 * @param b
 * @return
 *
 * @date   2024.01.19
 * <AUTHOR>
 */
template <typename T>
cv::Point_<T> ProjectVector(const cv::Point_<T>& a, const cv::Point_<T>& b)
{
    // 计算向量b的长度的平方
    double bLengthSquared = b.dot(b);

    // 计算投影向量的长度
    double projectionLength = a.dot(b) / bLengthSquared;

    // 计算投影向量
    cv::Point_<T> projection = b * projectionLength;

    return projection;
}
/**
 * @fun   calculateRotatedRectVertices
 * @brief 通过对角两点和旋转矩形倾斜角计算旋转矩形.
 * @param p1 对角1点
 * @param p2 对角2点
 * @param angle_deg 旋转矩形倾斜角
 * @return 旋转矩形
 *
 * @date   2024.01.19
 * <AUTHOR>
 */
template <typename T>
cv::RotatedRect calculateRotatedRectVertices(const cv::Point_<T>& p1, const cv::Point_<T>& p2, double angle_deg)
{
    cv::Point_<T> p3;
    if (angle_deg == 0 || angle_deg == 90 || angle_deg == 180 || angle_deg == 270)
    {
        p3 = cv::Point_<T>(p1.x, p2.y);
    }
    else
    {
        float k = std::tan(A_DEG_TO_RAD(angle_deg)); // *M_PI / 180.0);
        float A1, B1, C1, A2, B2, C2;
        // if (p1.x < p2.x)
        {
            A1 = k;
            B1 = -1;
            C1 = p1.y - k * p1.x;
            A2 = -1 / k;
            B2 = -1;
            C2 = p2.y - (-1 / k) * p2.x;
        }
        // else
        //{
        //     A2 = k;
        //     B2 = -1;
        //     C2 = p1.y - k * p1.x;
        //     A1 = -1 / k;
        //     B1 = -1;
        //     C1 = p2.y - (-1 / k) * p2.x;
        // }

        {
            auto m = A1 * B2 - A2 * B1;
            if (m == 0)
                return {};
            p3.x = (float)((B1 * C2 - B2 * C1) / m);
            p3.y = (float)((A2 * C1 - A1 * C2) / m);
        }
    }
    return cv::RotatedRect(p1, p3, p2);
}

/**
 * @brief 计算两个二维向量之间的夹角（以度为单位）
 * @param v1 第一个向量，使用cv::Point表示，分别表示x和y坐标
 * @param v2 第二个向量，使用cv::Point表示，分别表示x和y坐标
 * @return 夹角，逆时针为正，顺时针为负
 *
 * @date   2024.01.19
 * <AUTHOR>
 */
template <typename T>
T CalcVectorRotationAngle(const cv::Point_<T>& v1, const cv::Point_<T>& v2)
{

    // 计算向量模的乘积
    double theNorm = cv::norm(v1) * cv::norm(v2);

    // 计算叉乘
    double rho = v1.cross(v2);

    // 计算点乘
    double theta = v1.dot(v2);

    // 计算反余弦值
    double cosTheta = theta / theNorm;

    // 修正余弦值可能超过范围的情况
    cosTheta = std::max(-1.0, std::min(1.0, cosTheta));

    // 计算旋转角度
    T rotationAngle = (T)A_RAD_TO_DEG(std::acos(cosTheta));

    // 判断旋转方向
    if (rho < 0)
    {
        return -rotationAngle;
    }
    else
    {
        return rotationAngle;
    }
}

template <typename T>
cv::RotatedRect Rect2RotatedRect(const cv::Rect_<T>& rect)
{
    return cv::RotatedRect(cv::Point2f(rect.x + rect.width / 2.0f, rect.y + rect.height / 2.0f), cv::Size2f(rect.width, rect.height), 0);
}

template <typename T>
bool isRectIntersect(const cv::Rect_<T>& rect1, const cv::Rect_<T>& rect2)
{
    return (
        rect1.x <= rect2.x + rect2.width &&
        rect1.x + rect1.width >= rect2.x &&
        rect1.y <= rect2.y + rect2.height &&
        rect1.y + rect1.height >= rect2.y);
}
template <typename T>
bool isPointInRect(const cv::Rect_<T>& rect1, const cv::Point_<T>& p1)
{
    return (
        rect1.x <= p1.x &&
        rect1.x + rect1.width >= p1.x &&
        rect1.y <= p1.y &&
        rect1.y + rect1.height >= p1.y);
}

template <typename T>
bool isRectContain(const cv::Rect_<T>& rect1, const cv::Rect_<T>& rect2)
{
    return (
        rect1.x <= rect2.x &&
        rect1.x + rect1.width >= rect2.x + rect2.width &&
        rect1.y <= rect2.y &&
        rect1.y + rect1.height >= rect2.y + rect2.height);
}

bool isRectIntersecteWithRotatedRect(const cv::RotatedRect& rr1, const cv::RotatedRect& rr2);

template <typename T>
bool isRectIntersecteWithRotatedRect(const cv::Rect_<T>& rect, const cv::RotatedRect& rr)
{
    cv::RotatedRect rectAsRotatedRect(Rect2RotatedRect(rect));
    return isRectIntersecteWithRotatedRect(rectAsRotatedRect, rr);
}

template <typename T>
bool isRotatedRectInside(const cv::RotatedRect& rr, const cv::Rect_<T>& rect)
{
    cv::Point2f vp[4];
    rr.points(vp); // 获取四顶点坐标

    for (int i = 0; i < 4; i++)
    {
        if (!isPointInRect(rect, cv::Point_<T>(vp[i])))
        {
            return false;
        }
    }
    return true;
}

template <typename T>
bool isPointInRotatedRect(const cv::Point_<T>& p, const cv::RotatedRect& rr)
{
    cv::Point2f vp[4];
    rr.points(vp); // 获取四顶点坐标

    cv::Point_<T> AB = vp[1] - vp[0];
    cv::Point_<T> AM = p - vp[0];
    cv::Point_<T> BC = vp[2] - vp[1];
    cv::Point_<T> BM = p - vp[1];

    // Vector AB = createVector(vp[0], vp[1]);
    // Vector AM = createVector(vp[0], p);
    // Vector BC = createVector(vp[1], vp[2]);
    // Vector BM = createVector(vp[1], p);

    double dotABAM = AB.dot(AM); // dotProduct(AB, AM);
    double dotABAB = AB.dot(AB); // dotProduct(AB, AB);
    double dotBCBM = BC.dot(BM); // dotProduct(BC, BM);
    double dotBCBC = BC.dot(BC); // dotProduct(BC, BC);

    return 0 <= dotABAM && dotABAM <= dotABAB && 0 <= dotBCBM && dotBCBM <= dotBCBC;
}

/*判断roi是否合法*/
template <typename T>
bool isROILegal(const int& imageWidth, const int& imageHeight, const cv::Rect_<T>& roi)
{
    /*保护边界，在边界上也算通过*/
    return roi.x >= 0 && roi.y >= 0 && roi.width > 0 && roi.height > 0 /*宽高必须大于0*/
        && roi.x + roi.width <= imageWidth && roi.y + roi.height <= imageHeight;
    // cv::Rect imgrect(0, 0, imageWidth, imageHeight);
    // return imgrect.contains(roi.tl()) && imgrect.contains(roi.br());
}

double getDisPointToContour(const cv::Point2f& p, const std::vector<cv::Point2f>& contour, bool measureDist = true);

void SortRotatedRectPoints(cv::Point2f vetPoints[], cv::RotatedRect rect, int flag);

void Ellipsepot(int x0, int y0, int x, int y,
    std::vector<cv::Point>& vp1,
    std::vector<cv::Point>& vp2,
    std::vector<cv::Point>& vp3,
    std::vector<cv::Point>& vp4);

/**
 * @brief 用 Bresenham 算法绘制椭圆
 *
 * @param xc 椭圆中心的 x 坐标
 * @param yc 椭圆中心的 y 坐标
 * @param a 椭圆长轴的半径
 * @param b 椭圆短轴的半径
 *
 * @return void
 */
void Bresenham_Ellipse(int xc, int yc, int a, int b);

std::vector<float> generateRotatedEllipsePoints(float centerX, float centerY, float majorAxis, float minorAxis, double angle);

/**
 * @brief 计算阶乘的函数
 *
 * @param n 需要计算阶乘的整数
 * @return int 阶乘的结果
 */
int factorial(int n);
/**
 * @brief 计算二项式系数的函数
 *
 * @param n 总数
 * @param k 子集数
 * @return int 二项式系数
 */
int binomialCoefficient(int n, int k);

template <typename T>
void BezierCurve(T& point, const std::vector<T>& points, float t)
{
    if (points.empty())
    {
        return;
    }
    if (t < 0 || t > 1)
    {
        return;
    }

    int n = (int)points.size() - 1;

    point.x = 0;
    point.y = 0;

    for (int i = 0; i <= n; ++i)
    {
        float coeff = binomialCoefficient(n, i) * std::pow(1 - t, n - i) * std::pow(t, i);

        point.x += coeff * points[i].x;
        point.y += coeff * points[i].y;
    }
}

bool IsPointInPolygon(const cv::Point2f& pt, const std::vector<cv::Point2f>& contour);

double PointDisToPolygon(const cv::Point2f& pt, const std::vector<Vec2>& contour);

double PointDisToPolygon(const Vec2& pt, const std::vector<Vec2>& contour);
/**
 * @brief 通过旋转矩形的对角顶点和旋转角度构造旋转矩形。
 *
 * @param diagonal_pt1 旋转矩形的一个对角顶点
 * @param diagonal_pt2 旋转矩形的另一个对角顶点
 * @param angle 矩形的旋转角度，单位为度
 * @return cv::RotatedRect 生成的旋转矩形
 */
cv::RotatedRect CreateRotatedRectFromDiagonalPointsAndAngle(const cv::Point2f& diagonal_pt1, const cv::Point2f& diagonal_pt2, float angle);

cv::RotatedRect GetEffectiveRotateRect(const float& xstart, const float& ystart, const float& xend, const float& yend, const float& angle);


#endif // !GRAPHICS_ALGO_DEFINE_H