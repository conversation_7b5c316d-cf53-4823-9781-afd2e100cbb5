/*********************************************************************
 * @brief  定义在GL渲染中使用到的结构体. 已弃用
 *
 * @file   renderdefine.hpp
 *
 * @date   2024.05.29
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef RENDER_DEFINE_H
#define RENDER_DEFINE_H

#include "geometry2d.hpp"
#include <vector>
#include <string>

namespace rr
{
    struct Rect
    {
        float x = 0;
        float y = 0;
        float width = 0;
        float height = 0;
        float angle = 0;
        float r = 0;
        float g = 0;
        float b = 0;
        float a = 0;
        bool is_fill = false;
    };

    template <typename PointT>
    struct Polygon
    {
        std::vector<Line<PointT>> lines;
        float r = 0;
        float g = 0;
        float b = 0;
        float a = 0;
        bool is_fill = false;
    };

    using Polygoncvp2f = Polygon<cv::Point_<float>>;

    struct Message
    {
        float x = 0;
        float y = 0; // 文字基线位置(Baseline)
        float scale = 1;
        float r = 0;
        float g = 0;
        float b = 0;
        std::string text;
    };

    struct Texture
    {
        unsigned int texture_id = 0; // GLuint
        std::vector<float> vertexs;
        int x = 0;
        int y = 0;
        int z = 0;
    };

    struct Character
    {
        unsigned int texture_id = 0;   // 字形纹理ID
        unsigned int bitmap_width = 0; // 字形大小 Size.x
        unsigned int bitmap_rows = 0;  // 字形大小 Size.y
        int bitmap_left = 0;           // 字形基于基线和起点的位置 Bearing.x
        int bitmap_top = 0;            // 字形基于基线和起点的位置  Bearing.y
        int advance = 0;               // 起点到下一个字形起点的距离
    };
}
#endif // !RENDER_DEFINE_H