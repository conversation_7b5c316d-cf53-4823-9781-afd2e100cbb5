﻿project(JRSAOI)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#启用自动MOC（Meta-Object Compiler）功能。在使用Qt的信号槽机制时，需要使用MOC来生成额外的代码。开启此选项后，CMake会自动查找并运行MOC来处理相关的源文件。
set(CMAKE_AUTOMOC ON)
#启用自动UI编译功能。在使用Qt的UI文件（.ui）时，需要将其编译为C++代码。开启此选项后，CMake会自动查找并运行UIC来处理相关的UI文件。
set(CMAKE_AUTOUIC ON)
#启用自动RCC（Resource Compiler）功能。在使用Qt的资源文件（.qrc）时，需要将其编译为二进制资源文件。开启此选项后，CMake会自动查找并运行RCC来处理相关的资源文件。
set(CMAKE_AUTORCC ON)
# 內存泄漏检测工具
add_compile_definitions(VLD_FORCE_ENABLE)
#add_compile_definitions(NO_VLD)

# 启用延迟加载dll
# find_library(DELAYIMP_LIB DelayImp.lib)
 
# 文件打包     
#init sys view
set(sys_state_src
systemstateview/src/systemstatecontroller.cpp
systemstateview/src/systemstatemodel.cpp
systemstateview/src/systemstateview.cpp

)
set(sys_state_head
systemstateview/include/systemstatecontroller.h
systemstateview/include/systemstatemodel.h
systemstateview/include/systemstateview.h
)
set(init_sys_ui
systemstateview/ui/systemstateview.ui
)
## 主界面
set(mainwindow_src
    mainwindow/src/main.cpp 
    mainwindow/src/mainwindow.cpp
    mainwindow/src/crashhandler.cpp
    mainwindow/src/viewbase.cpp 
    mainwindow/src/controllerbase.cpp 
    mainwindow/src/modelbase.cpp 
    mainwindow/src/customtitleview.cpp 
    mainwindow/src/statusbarview.cpp 
    mainwindow/src/viewmanager.cpp 

)
set(mainwindow_head
    mainwindow/include/mainwindow.h  
    mainwindow/include/crashhandler.h
    mainwindow/include/viewfactory.hpp 
    mainwindow/include/viewbase.h 
    mainwindow/include/controllerbase.h 
    mainwindow/include/modelbase.h 
    mainwindow/include/customtitleview.h 
    mainwindow/include/statusbarview.h 
    mainwindow/include/viewmanager.h
    mainwindow/include/viewtool.hpp 
    mainwindow/include/customqvarientregister.hpp
)

#主控台
set(controlpanel_src
    controlpanel/src/machinestateview.cpp
    controlpanel/src/controlpanelview.cpp
    controlpanel/src/controlpanelcontroller.cpp
    controlpanel/src/controlpanelmodel.cpp
    controlpanel/src/autorunpanelview.cpp
)
set(controlpanel_head
    controlpanel/include/machinestateview.h
    controlpanel/include/controlpanelview.h
    controlpanel/include/controlpanelcontroller.h
    controlpanel/include/controlpanelmodel.h
    controlpanel/include/autorunpanelview.h
)

set(controlpanel_ui
    controlpanel/ui/controlpanelview.ui
    controlpanel/ui/machinestateview.ui
    controlpanel/ui/autorunpanelview.ui
)

#主操作界面
set(operate_src    
    operate/src/operatecontroller.cpp
    operate/src/operatemodel.cpp
    operate/src/operateview.cpp
#    operate/src/dlgcreatedetectwindow.cpp
)
set(operate_head
    operate/include/operatecontroller.h
    operate/include/operatemodel.h
    operate/include/operateview.h
#    operate/include/dlgcreatedetectwindow.h
)
set(operate_add_component_src
operate/src/addcomponentview.cpp
)
set(operate_add_component_head
operate/include/addcomponentview.h
)
set(operate_add_component_ui
operate/ui/addmarkview.ui
operate/ui/addbarcodeview.ui
)
set(operate_ui
    operate/ui/operateview.ui
   # operate/ui/motiondebugview.ui
    operate/ui/parametersettings.ui
   # operate/ui/trackstatus.ui
    operate/ui/projectview.ui
    operate/ui/projectlinkview.ui
    operate/ui/editdetectmodelview.ui
    operate/ui/componentlibview.ui
    operate/ui/savecomponentdialog.ui
    operate/ui/detectview.ui
    operate/ui/onedetectview.ui
    operate/ui/showmarkresultimagewidget.ui
    operate/ui/addcadview.ui
    operate/ui/templateview.ui
    operate/ui/newprojectview.ui
    operate/ui/importcadview.ui
    operate/ui/expansionmultipanel.ui
    operate/ui/inputlineeditdialog.ui
    #operate/ui/axismove.ui
    #operate/ui/trackdebug.ui
    operate/ui/newfileview.ui
    operate/ui/datasetting.ui
    operate/ui/dataview.ui
    operate/ui/checkwidget.ui
    operate/ui/dlgcreatetemplate.ui
    operate/ui/openprojectview.ui
    operate/ui/saveopenimageview.ui
    operate/ui/dlgcreatedetectwindow.ui
    operate/ui/dlgtemplatelistedit.ui
    operate/ui/multidetectwindow.ui
    operate/ui/algospecparam.ui
    operate/ui/subboardsort.ui

)
set(operate_model
    operate/operatemodel/addcadviewmodel/addcadviewmodel.h
    operate/operatemodel/addcadviewmodel/addcadviewmodel.cpp
    operate/operatemodel/customhistogram/customhistogram.h
    operate/operatemodel/customhistogram/customhistogram.cpp
    operate/operatemodel/horizontalbarchart/horizontalbarchart.h
    operate/operatemodel/horizontalbarchart/horizontalbarchart.cpp
    operate/operatemodel/detectresultmodel/detectresultmodel.h
    operate/operatemodel/detectresultmodel/detectresultmodel.cpp
    operate/operatemodel/imagetabledelegate/customstyle.hpp
    operate/operatemodel/imagetabledelegate/imagetabledelegate.h
    operate/operatemodel/imagetabledelegate/imagetabledelegate.cpp
    operate/operatemodel/templatelistwidget/templatelistwidget.cpp
    operate/operatemodel/templatelistwidget/templatelistwidget.h

)

# 颜色调试控件
#########################################

set(operate_colorwheel_src  
    operate/colorwheel/src/colorui.cpp
    operate/colorwheel/src/colorwidget.cpp
    operate/colorwheel/src/imagebinaryalgo.cpp
    operate/colorwheel/src/imagebinarywidget.cpp
    operate/colorwheel/src/imagepreprocessalgo.cpp
    operate/colorwheel/src/imagepreprocesswidget.cpp
    operate/colorwheel/src/imagepreview.cpp
    operate/colorwheel/src/imageprocessalgo.cpp
)

set(operate_colorwheel_include
    operate/colorwheel/include/colorui.h
    operate/colorwheel/include/imagebinaryalgo.h
    operate/colorwheel/include/imagebinarywidget.h
    operate/colorwheel/include/imagepreprocessalgo.h
    operate/colorwheel/include/imagepreprocesswidget.h
    operate/colorwheel/include/imagepreview.h
)

set(operate_colorwheel_interface
    operate/colorwheel/interface/colorwidget.h
    operate/colorwheel/interface/imageprocessalgo.h
)

set(operate_colorwheel_customwidget_src
    operate/colorwheel/customwidget/src/colorwheelctrl.cpp
    operate/colorwheel/customwidget/src/customlabel.cpp
    operate/colorwheel/customwidget/src/graywheel.cpp
    operate/colorwheel/customwidget/src/heightwheel.cpp
    operate/colorwheel/customwidget/src/histogramwidget.cpp
    operate/colorwheel/customwidget/src/rgbcolorwheel.cpp
    operate/colorwheel/customwidget/src/singleslider.cpp
    operate/colorwheel/customwidget/src/vwheel.cpp
    operate/colorwheel/customwidget/src/wigtcolorwheel.cpp
    operate/colorwheel/customwidget/src/wigtdouthresholdhistm.cpp
)

set(operate_colorwheel_customwidget_include
    operate/colorwheel/customwidget/include/colorwheelctrl.h
    operate/colorwheel/customwidget/include/customlabel.h
    operate/colorwheel/customwidget/include/graywheel.h
    operate/colorwheel/customwidget/include/heightwheel.h
    operate/colorwheel/customwidget/include/histogramwidget.h
    operate/colorwheel/customwidget/include/rgbcolorwheel.h
    operate/colorwheel/customwidget/include/singleslider.h
    operate/colorwheel/customwidget/include/vwheel.h
    operate/colorwheel/customwidget/include/wigtcolorwheel.h
    operate/colorwheel/customwidget/include/wigtdouthresholdhistm.h
)

set(operate_colorwheel_generaltool
    operate/colorwheel/generaltool/qcustomplot.cpp
    operate/colorwheel/generaltool/qcustomplot.h
    operate/colorwheel/generaltool/qimagetranform.cpp
    operate/colorwheel/generaltool/qimagetranform.h
)


################################################
#########子板排序#########
set(operate_subboard_sort
    operate/operatemodel/subboardsort/subboardsortmanager.h
    operate/operatemodel/subboardsort/subboardsortmanager.cpp

)
set(operate_subboard_sort_oder
    operate/operatemodel/subboardsort/order/subboardsortbase.h
    operate/operatemodel/subboardsort/order/subboardsortbase.cpp
)
set(operate_subboard_order_s  ## s规则
    operate/operatemodel/subboardsort/order/s_shape/left_bottom_to_right_top_s.hpp
    operate/operatemodel/subboardsort/order/s_shape/left_top_to_right_bottom_s.hpp
    operate/operatemodel/subboardsort/order/s_shape/right_bottom_to_left_top_s.hpp
    operate/operatemodel/subboardsort/order/s_shape/right_top_to_left_bottom_s.hpp
)
set(operate_subboard_order_z  ## z规则
    operate/operatemodel/subboardsort/order/z_shape/left_bottom_to_right_top_z.hpp
    operate/operatemodel/subboardsort/order/z_shape/left_top_to_right_bottom_z.hpp
    operate/operatemodel/subboardsort/order/z_shape/right_bottom_to_left_top_z.hpp
    operate/operatemodel/subboardsort/order/z_shape/right_top_to_left_bottom_z.hpp
)
set(operate_subboard_order_vertical_z  ## vertical z规则
    operate/operatemodel/subboardsort/order/vertical_z_shape/left_bottom_to_right_top_vertical_z.hpp
    operate/operatemodel/subboardsort/order/vertical_z_shape/left_top_to_right_bottom_vertical_z.hpp
    operate/operatemodel/subboardsort/order/vertical_z_shape/right_bottom_to_left_top_vertical_z.hpp
    operate/operatemodel/subboardsort/order/vertical_z_shape/right_top_to_left_bottom_vertical_z.hpp
)
set(operate_subboard_order_vertical_s  ## vertical z规则
    operate/operatemodel/subboardsort/order/vertical_s_shape/left_bottom_to_right_top_vertical_s.hpp
    operate/operatemodel/subboardsort/order/vertical_s_shape/left_top_to_right_bottom_vertical_s.hpp
    operate/operatemodel/subboardsort/order/vertical_s_shape/right_bottom_to_left_top_vertical_s.hpp
    operate/operatemodel/subboardsort/order/vertical_s_shape/right_top_to_left_bottom_vertical_s.hpp
)
set(operate_subboard_order_view  ## subboard view
    operate/operatemodel/subboardsort/view/subboardsortview.h
    operate/operatemodel/subboardsort/view/subboardsortview.cpp
)
#########################


set(operate_common_head #通用窗口
#operate/include/customsplashscreen.h
operate/include/inputlineeditdialog.h
)
set(operate_common_src
#operate/src/customsplashscreen.cpp
operate/src/inputlineeditdialog.cpp
)
set(operate_flow_src    #操作流程界面
operate/src/addcadview.cpp
operate/src/projectview.cpp
operate/src/newprojectview.cpp
operate/src/newfileview.cpp
operate/src/importcadview.cpp
operate/src/expansionmultipanel.cpp
operate/src/openprojectview.cpp
operate/src/saveopenimageview.cpp
operate/src/projectlinkview.cpp
)
set(operate_flow_head    
operate/include/addcadview.h
operate/include/projectview.h
operate/include/newprojectview.h
operate/include/newfileview.h
operate/include/importcadview.h
operate/include/expansionmultipanel.h
operate/include/openprojectview.h
operate/include/saveopenimageview.h
operate/include/readcadfile.hpp
operate/include/projectlinkview.h

)

set(operate_edit_src    #操作编辑界面
operate/src/editdetectmodelview.cpp
operate/src/templateview.cpp
operate/src/dlgcreatetemplate.cpp
operate/src/dlgcreatedetectwindow.cpp
operate/src/dlgtemplatelistedit.cpp
operate/src/multidetectwindow.cpp
operate/src/regionwidget.cpp
operate/src/regionlistwidget.cpp
operate/src/algoselectwidget.cpp
operate/src/regionlisttabwidget.cpp
operate/src/algospecparam.cpp
)
set(operate_edit_head  
operate/include/editdetectmodelview.h
operate/include/templateview.h
operate/include/dlgcreatetemplate.h
operate/include/dlgcreatedetectwindow.h
operate/include/dlgtemplatelistedit.h
operate/include/regionwidget.h
operate/include/regionlistwidget.h
operate/include/algoselectwidget.h
operate/include/regionlisttabwidget.h
operate/include/multidetectwindow.h
operate/include/algospecparam.h
)

# 历史元件库界面
set(operate_component_library_src
operate/src/componentlibrary.cpp
operate/src/componentlibview.cpp
operate/src/savecomponentdialog.cpp
operate/src/regionwidget.cpp
operate/src/regionlistwidget.cpp
operate/src/algoselectwidget.cpp
operate/src/regionlisttabwidget.cpp
)
set(operate_component_library_head
operate/include/componentlibrary.h
operate/include/componentlibview.h
operate/include/savecomponentdialog.h
)

set(operate_detect_src   #操作检测界面  

operate/src/detectview.cpp
operate/src/onedetectview.cpp
operate/src/showmarkresultimagewidget.cpp
operate/operateprocessed/detectprocessed/detectprocessed.cpp
)
set(operate_detect_head  
operate/include/detectview.h
operate/include/onedetectview.h
operate/include/showmarkresultimagewidget.h
operate/operateprocessed/detectprocessed/detectprocessed.h
)

set(operate_setting_src #操作设置界面  
#operate/src/axismove.cpp
operate/src/parametersettings.cpp
)
set(operate_setting_head
#operate/include/axismove.h
operate/include/parametersettings.h
)

set(operate_data_src #操作数据界面  
operate/src/datasetting.cpp
operate/src/dataview.cpp
operate/src/checkwidget.cpp
)
set(operate_data_head
operate/include/datasetting.h
operate/include/dataview.h
operate/include/dataviewdatastruct.h
operate/include/checkwidget.h

)
set(operate_debug_src #数据界面  
#operate/src/trackdebug.cpp
#operate/src/motiondebugview.cpp
#operate/src/trackstatus.cpp
)
set(operate_debug_head
#operate/include/trackdebug.h
#operate/include/motiondebugview.h
#operate/include/trackstatus.h
)

#对话框
set(dialog_head
    operate/dialog/include/componentvaluedialog.h
    operate/dialog/include/imagegroupdialog.h
)
set(dialog_src
    operate/dialog/src/imagegroupdialog.cpp
    operate/dialog/src/componentvaluedialog.cpp
)
set(dialog_ui
    operate/dialog/ui/imagegroupdialog.ui
)

#快捷工具栏
set(toolbar_src

    toolbar/src/toolbarcontroller.cpp
    toolbar/src/toolbarmodel.cpp
    toolbar/src/toolbarview.cpp

)
set(toolbar_head

    toolbar/include/toolbarcontroller.h
    toolbar/include/toolbarmodel.h
    toolbar/include/toolbarview.h
)

#日志显示界面
set(logshow_src

    logshow/src/logshowcontroller.cpp
    logshow/src/logshowmodel.cpp
    logshow/src/logshowview.cpp

)
set(logshow_head

    logshow/include/logshowcontroller.h
    logshow/include/logshowmodel.h
    logshow/include/logshowview.h
)

#左侧显示区域
set(showlist_head

    showlist/include/showlistcontroller.h
    showlist/include/showlistmodel.h
    showlist/include/showlistview.h
    showlist/include/editview.h
)

set(showlist_src
    showlist/src/showlistcontroller.cpp
    showlist/src/showlistmodel.cpp
    showlist/src/showlistview.cpp
    showlist/src/editview.cpp
)

set(showlist_ui

    showlist/ui/showlistview.ui
    showlist/ui/editview.ui

)

set(showlist_model
    showlist/model/tableviewmodel/tableviewmodel.h
    showlist/model/tableviewmodel/tableviewmodel.cpp
    showlist/model/customtableview/customtableview.h
    showlist/model/customtableview/customtableview.cpp
)

 
#2D 渲染界面
set(render2d_head
    render2d/include/layerconverter.hpp
    render2d/include/render2deventparam.hpp
    render2d/include/render2dcontroller.h
    render2d/include/render2dmodel.h
    render2d/include/render2dview.h
    render2d/include/subboardclone.h
)
set(render2d_multiple_board_head
    render2d/include/multipleboards/iregularmultipleboards.h
    render2d/include/multipleboards/multipleboardsbase.h
    render2d/include/multipleboards/regularmultipleboards.h
)
set(render2d_multiple_board_src
    render2d/src/multipleboards/iregularmultipleboards.cpp
    render2d/src/multipleboards/multipleboardsbase.cpp
    render2d/src/multipleboards/regularmultipleboards.cpp
)
set(render2d_pad_head
    render2d/include/pad/padoperator.hpp
    render2d/include/pad/addpadview.h
    )
set(render2d_pad_src
    render2d/src/pad/padoperator.cpp
    render2d/src/pad/addpadview.cpp
)

set(render2d_src
    render2d/src/render2dcontroller.cpp
    render2d/src/render2dmodel.cpp
    render2d/src/render2dview.cpp
    render2d/src/subboardclone.cpp
)
set(render_ui

    render2d/ui/render2dview.ui
    render2d/ui/addpadview.ui

)
# 在线调试模块
set(onlinedebug_head
    onlinedebug/include/onlinedebugcontroller.h
    onlinedebug/include/onlinedebugmodel.h
    onlinedebug/include/onlinedebugview.h
)
set(onlinedebug_src

    onlinedebug/src/onlinedebugcontroller.cpp
    onlinedebug/src/onlinedebugmodel.cpp
    onlinedebug/src/onlinedebugview.cpp
)
set(onlinedebug_ui
    onlinedebug/ui/onlinedebugview.ui

)
#界面事件管理中心
set(vieweventmanager_head

    vieweventmanager/include/eventcenter.h
    vieweventmanager/include/pubsubevent.h
)
set(vieweventmanager_src

    vieweventmanager/src/eventcenter.cpp
    vieweventmanager/src/pubsubevent.cpp

)
#参数单例
set(instance_head
    instance/include/paramupdatecenter.h
    instance/include/paramoperator.h
    instance/include/queryparam.hpp
)
set(instance_src
    instance/src/paramupdatecenter.cpp
    instance/src/paramoperator.cpp
)

#工具窗口
set(toolsview_head
    toolsview/include/customlistwidget.h
    toolsview/include/messagebase.h
    toolsview/include/custommessagebox.h
    toolsview/include/viewergraphicsviewimpl.h
    toolsview/include/qtools.hpp
)
set(toolsview_src
    toolsview/src/customlistwidget.cpp
    toolsview/src/messagebase.cpp
    toolsview/src/custommessagebox.cpp
    toolsview/src/viewergraphicsviewimpl.cpp
)

#设置窗口
set(settingview_head

settingview/include/settingcontroller.h
settingview/include/settingmodel.h
settingview/include/settingview.h

)
set(settingview_src
settingview/src/settingcontroller.cpp
settingview/src/settingmodel.cpp
settingview/src/settingview.cpp
)
set(settingview_view_head
settingview/include/systemparamview.h
settingview/include/commonparamview.h
)
set(settingview_view_src
settingview/src/systemparamview.cpp
settingview/src/commonparamview.cpp
)
set(settingview_ui
settingview/ui/commonparamview.ui
settingview/ui/systemparamview.ui
)

# 自定义widget
set(customwidget_head
 
    customwidget/include/texthorizontalalignedtabbar.h
    customwidget/include/graphicsView2d.h
    customwidget/include/nocoverbackgrounddelegate.hpp
    customwidget/include/customitemdatamodel.h
    customwidget/include/customtablelistview.h
    customwidget/include/customtabbar.h

)
set(customwidget_src
    customwidget/src/texthorizontalalignedtabbar.cpp
    customwidget/src/graphicsView2d.cpp
    customwidget/src/customitemdatamodel.cpp
    customwidget/src/customtablelistview.cpp
    customwidget/src/customtabbar.cpp
)

# 预编译头文件
set(source_pre_file

    prebuild/pch.cpp
    prebuild/pch.h

)

set(resc
    ${DIR_PROJECT_CURRENT}jrsresource/icon/MainWindowResource.qrc
    ${DIR_PROJECT_CURRENT}jrsresource/icon/icon.rc
)

#将ui路径添加到搜索路径中，否则找不到生成的ui_**.h文件
list(APPEND CMAKE_AUTOUIC_SEARCH_PATHS ust
    "mainwindow/ui"
    "operate/ui"
    "showlist/ui"
    "controlpanel/ui"
    "render2d/ui"
    "settingview/ui" 
    "systemstateview/ui"
    "operate/dialog/ui"
    "onlinedebug/ui"
)
source_group("initsys/src" FILES ${sys_state_src})
source_group("initsys/head" FILES ${sys_state_head})
source_group("initsys/ui" FILES ${init_sys_ui})
source_group("mainwindow/src" FILES ${mainwindow_src})
source_group("mainwindow/head" FILES ${mainwindow_head})
source_group("controlpanel/src" FILES ${controlpanel_src}) 
source_group("controlpanel/head" FILES ${controlpanel_head}) 
source_group("controlpanel/ui" FILES ${controlpanel_ui}) 

source_group("operate/src" FILES ${operate_src}) 
source_group("operate/head" FILES ${operate_head}) 
source_group("operate/ui" FILES ${operate_ui}) 
source_group("operate/model" FILES ${operate_model}) 
source_group("operate/colorwheel/include" FILES ${operate_colorwheel_include})
source_group("operate/colorwheel/src" FILES ${operate_colorwheel_src})
source_group("operate/colorwheel/interface" FILES ${operate_colorwheel_interface})
source_group("operate/colorwheel/customwidget/src" FILES ${operate_colorwheel_customwidget_src})
source_group("operate/colorwheel/customwidget/include" FILES ${operate_colorwheel_customwidget_include})
source_group("operate/colorwheel/generaltool" FILES ${operate_colorwheel_generaltool})

source_group("operate/subboardsort" FILES ${operate_subboard_sort})
source_group("operate/subboardsort/order" FILES ${operate_subboard_sort_oder})
source_group("operate/subboardsort/order/s" FILES ${operate_subboard_order_s})
source_group("operate/subboardsort/order/z" FILES ${operate_subboard_order_z})
source_group("operate/subboardsort/order/vertical_z" FILES ${operate_subboard_order_vertical_z})
source_group("operate/subboardsort/order/vertical_s" FILES ${operate_subboard_order_vertical_s})
source_group("operate/subboardsort/view" FILES ${operate_subboard_order_view})

source_group("operate/dataprocessed" FILES ${operate_dataprocessed}) 
source_group("operate/head/flow" FILES ${operate_flow_head}) 
source_group("operate/src/flow" FILES ${operate_flow_src}) 
source_group("operate/head/edit" FILES ${operate_edit_head}) 
source_group("operate/src/edit" FILES ${operate_edit_src}) 
source_group("operate/head/componentlib" FILES ${operate_component_library_head}) 
source_group("operate/src/componentlib" FILES ${operate_component_library_src}) 
source_group("operate/src/detect" FILES ${operate_detect_src}) 
source_group("operate/head/detect" FILES ${operate_detect_head}) 
source_group("operate/src/setting" FILES ${operate_setting_src}) 
source_group("operate/head/setting" FILES ${operate_setting_head}) 
source_group("operate/src/data" FILES ${operate_data_src}) 
source_group("operate/head/data" FILES ${operate_data_head}) 
source_group("operate/src/debug" FILES ${operate_debug_src}) 
source_group("operate/head/debug" FILES ${operate_debug_head}) 
source_group("operate/common/src" FILES ${operate_common_src}) 
source_group("operate/common/head" FILES ${operate_common_head}) 
source_group("operate/addcomponent/head" FILES ${operate_add_component_head}) 
source_group("operate/addcomponent/src" FILES ${operate_add_component_src}) 
source_group("operate/addcomponent/ui" FILES ${operate_add_component_ui}) 
source_group("toolbar/src" FILES ${toolbar_src}) 
source_group("toolbar/head" FILES ${toolbar_head}) 
source_group("logshow/src" FILES ${logshow_src}) 
source_group("logshow/head" FILES ${logshow_head}) 
source_group("showlist/src" FILES ${showlist_src}) 
source_group("showlist/head" FILES ${showlist_head}) 
source_group("showlist/ui" FILES ${showlist_ui}) 
source_group("showlist/model" FILES ${showlist_model}) 

source_group("render2d/src" FILES ${render2d_src})
source_group("render2d/head" FILES ${render2d_head})
source_group("render2d/multipleboards/src" FILES ${render2d_multiple_board_src})
source_group("render2d/multipleboards/head" FILES ${render2d_multiple_board_head})
source_group("render2d/pad/src" FILES ${render2d_pad_src})
source_group("render2d/pad/head" FILES ${render2d_pad_head})
source_group("render2d/ui" FILES ${render_ui})
source_group("onlinedebug/src" FILES ${onlinedebug_src})
source_group("onlinedebug/head" FILES ${onlinedebug_head})
source_group("onlinedebug/ui" FILES ${onlinedebug_ui})
source_group("vieweventmanager/head" FILES ${vieweventmanager_head})
source_group("vieweventmanager/src" FILES ${vieweventmanager_src})

source_group("dialog/head" FILES ${dialog_head})
source_group("dialog/src" FILES ${dialog_src})
source_group("dialog/ui" FILES ${dialog_ui})

source_group("tools/head" FILES ${toolsview_head})
source_group("tools/src" FILES ${toolsview_src})
source_group("instance/head" FILES ${instance_head})
source_group("instance/src" FILES ${instance_src})

source_group("tabbarstyle/head" FILES ${tabbarstyle_head})
source_group("tabbarstyle/src" FILES ${tabbarstyle_src})

source_group("customwidget/head" FILES ${customwidget_head})
source_group("customwidget/src" FILES ${customwidget_src})

source_group("setting/head" FILES ${settingview_head})
source_group("setting/src" FILES ${settingview_src})
source_group("setting/ui" FILES ${settingview_ui})
source_group("setting/head/view" FILES ${settingview_view_head})
source_group("setting/src/view" FILES ${settingview_view_src})
 
source_group("prebuild/src" FILES ${source_pre_file})

source_group("Resource File" FILES ${resc})

add_executable(${PROJECT_NAME}
    ${sys_state_src}
    ${sys_state_head}
    ${init_sys_ui}
    ${mainwindow_src}
    ${mainwindow_head}
    ${controlpanel_src}
    ${controlpanel_head}
    ${controlpanel_ui}

    ${operate_src}
    ${operate_head}
    ${operate_ui}
    ${operate_model}
    #${operate_operateprocessed}
    ${operate_colorwheel_src}
    ${operate_colorwheel_include}
    ${operate_colorwheel_interface}
    ${operate_colorwheel_customwidget_src}
    ${operate_colorwheel_customwidget_include}
    ${operate_colorwheel_generaltool}
    ${operate_flow_head}
    ${operate_flow_src}
    ${operate_edit_head}
    ${operate_edit_src}
    ${operate_component_library_head}
    ${operate_component_library_src}
    ${operate_detect_src}
    ${operate_detect_head}
    ${operate_setting_src}
    ${operate_setting_head}
    ${operate_data_src}
    ${operate_data_head}
    ${operate_debug_src}
    ${operate_debug_head}
    ${operate_common_head}
    ${operate_common_src}
    
    ${operate_add_component_head}
    ${operate_add_component_src}
    ${operate_add_component_ui}


    ${operate_subboard_sort}
    ${operate_subboard_order_view}
    ${operate_subboard_sort_oder}
    ${operate_subboard_order_s}
    ${operate_subboard_order_z}
    ${operate_subboard_order_vertical_z}
    ${operate_subboard_order_vertical_s}

    ${dialog_head}
    ${dialog_src}
    ${dialog_ui}


    ${toolbar_src}
    ${toolbar_head}
    ${logshow_src}
    ${logshow_head}
    ${showlist_src}
    ${showlist_head}
    ${showlist_ui}
    ${showlist_model}
    ${render2d_src}
    ${render2d_head}
    ${render2d_multiple_board_src}
    ${render2d_multiple_board_head}
    ${render2d_pad_src}
    ${render2d_pad_head}
    ${render_ui}
    ${onlinedebug_src}
    ${onlinedebug_head}
    ${onlinedebug_ui}
    ${vieweventmanager_head}
    ${vieweventmanager_src}

    ${toolsview_head}
    ${toolsview_src}

    ${instance_head}
    ${instance_src}

    ${tabbarstyle_head}
    ${tabbarstyle_src}
    ${customwidget_head}
    ${customwidget_src}

    ${settingview_head}
    ${settingview_src}
    ${settingview_view_head}
    ${settingview_view_src}
    ${settingview_ui}
    ${source_pre_file}
    ${resc}
    ${JRS_VERSIONINFO_RC}
   
)


#设置输出bin路径
set(EXECUTABLE_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

# 为指定的目标（target）添加链接目录
target_link_directories(${PROJECT_NAME} 
    PRIVATE
    # OPENCV
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>

    # Saribbon
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/saribbon/lib/debug>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${DIR_PROJECT_CURRENT}thirdparty/saribbon/lib/release>

    # vtk
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/jrsvtk3dshowmodule/lib/debug>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${DIR_PROJECT_CURRENT}thirdparty/jrsvtk3dshowmodule/lib/release>

    # uchartdet
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/uchartdet/lib/debug>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${DIR_PROJECT_CURRENT}thirdparty/uchartdet/lib/release>

    # cv2d
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/cv2d/lib/debug>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${DIR_PROJECT_CURRENT}thirdparty/cv2d/lib/release>

    # 成像模块
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/debug>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>
    $<$<CONFIG:RelWithDebInfo>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/relwithdebinfo>

    # 高度基准校正模块
    $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/heightbasecorrect/lib/debug>
    $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/heightbasecorrect/lib/release>

    # other
    ${DIR_PROJECT_CURRENT}thirdparty/vld/lib
    ${DIR_PROJECT_CURRENT}thirdparty/algo/ocv/lib

    # ${DIR_PROJECT_CURRENT}thirdparty/mark/lib
)


#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}
    #QT
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui 
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100>
    #Renderer
    render
    #core
    core
    #logic
    logicmanager
    #algorithmengine
    algorithmengine
    #vld
    vld
    #saribbon
    $<$<CONFIG:Debug>:SARibbonBard.lib>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:SARibbonBar.lib>

    # 3d显示控件
    # 2024/12/16 wangzhengkai 更换3D显示控件
    jrsvision3dediterlib
    #$<$<CONFIG:Debug>:jrsvision3dediterlib>
    #$<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:jrsvision3dediterlib>
    uchardet
    BasePlaneProject.lib

    #cv2d
    $<$<CONFIG:Debug>:cv2dd>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:cv2d>
    Version.lib

)

target_include_directories(${PROJECT_NAME} PUBLIC
    ${DIR_PROJECT_CURRENT}/src/render/interface/include
    ${DIR_PROJECT_CURRENT}/src/render/control/include
    ${DIR_PROJECT_CURRENT}/src/render/engine/include
    ${DIR_PROJECT_CURRENT}/src/core/common/include
    ${DIR_PROJECT_CURRENT}/src/core/pubsub/include
    ${DIR_PROJECT_CURRENT}/src/core/pubsub/src
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/dataparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/projectparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/viewparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/colorparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/projectprocess
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/algoprocess


    ${DIR_PROJECT_CURRENT}/src/algo/define/viewparam
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/addcadviewmodel
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/customhistogram
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/customlistmodel
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/horizontalbarchart
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/detectresultmodel
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/imagetabledelegate
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/templatelistwidget
    ${DIR_PROJECT_CURRENT}/src/view/operate/operateprocessed/detectprocessed
    ${DIR_PROJECT_CURRENT}/src/view/operate/dialog

    # subboard_order
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/view
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/order
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/order/s_shape
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/order/z_shape
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/order/vertical_z_shape
    ${DIR_PROJECT_CURRENT}/src/view/operate/operatemodel/subboardsort/order/vertical_s_shape

    ${DIR_PROJECT_CURRENT}/src/view/prebuild
    
    #QT tool
    ${DIR_PROJECT_CURRENT}/src/view/showlist/model/tableviewmodel
    ${DIR_PROJECT_CURRENT}/src/view/showlist/model/customtableview

    #third
    ${DIR_PROJECT_CURRENT}/thirdparty/cv2d/include/tool/include
    ${DIR_PROJECT_CURRENT}/thirdparty/tabbarstyle/include
    #${DIR_PROJECT_CURRENT}/thirdparty/iguana
    ${DIR_PROJECT_CURRENT}/thirdparty/jrsvtk3dshowmodule/include
    #${DIR_PROJECT_CURRENT}/thirdparty/uchartdet/include
    ${DIR_PROJECT_CURRENT}thirdparty/heightbasecorrect/include
    ${DIR_PROJECT_CURRENT}/thirdparty
)

#将头文件添加到包路径中，否则找不到头文件
target_include_directories(${PROJECT_NAME} PRIVATE 
    operate/dialog/include
    instance/include
    systemstateview/include
    mainwindow/include
    controlpanel/include
    operate/include
    operate/colorwheel/include
    operate/colorwheel/customwidget/include
    operate/colorwheel/generaltool
    operate/colorwheel/interface
    render2d/include
    render2d/include/multipleboards
    render2d/include/pad
    onlinedebug/include
    toolbar/include
    logshow/include 
    showlist/include
    vieweventmanager/include
    toolsview/include

    
    settingview/include
    customwidget/include
    ../../thirdparty/saribbon/include
    ../../thirdparty/vld/include
    ../../thirdparty/uchartdet/include
    ../../thirdparty
    ../../thirdparty/json/include
    ../algorithmengine/include
    ${OPENCV_INCLUDE_DIR}
)


# 设置链接器选项,Debug模式下打开控制台，Release下窗口模式
if(MSVC)
  set_target_properties(
    ${PROJECT_NAME}
    PROPERTIES LINK_FLAGS_DEBUG "/SUBSYSTEM:CONSOLE"
               LINK_FLAGS_RELEASE "/SUBSYSTEM:WINDOWS /ENTRY:mainCRTStartup")
endif()

# 启用预编译头
target_precompile_headers(${PROJECT_NAME} PRIVATE "${CMAKE_SOURCE_DIR}/src/view/prebuild/pch.h")

# 针对 Visual Studio 项目，确保 pch.cpp 正确配置
if(MSVC)
    set_source_files_properties(pch.cpp PROPERTIES COMPILE_FLAGS "/Ycpch.h")  # 编译 pch.cpp
    set_target_properties(${PROJECT_NAME} PROPERTIES
        COMPILE_PDB_NAME ${PROJECT_NAME}  # 可选：设置 PDB 文件名
    )
endif()
# 设置vs默认启动项
set_property(DIRECTORY ${CMAKE_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT ${PROJECT_NAME})
 

# 设置工作目录，假设是 
set_target_properties(${PROJECT_NAME} PROPERTIES
    VS_DEBUGGER_WORKING_DIRECTORY "$<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}bin/Debug>$<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}bin/Release>$<$<CONFIG:RelWithDebInfo>:${DIR_PROJECT_CURRENT}bin/RelWithDebInfo>"
)  


 #屏蔽生成文件警告
 set(MOC_FILES
    "${CMAKE_CURRENT_BINARY_DIR}/JRSAOI_autogen/mocs_compilation_Debug.cpp"
    "${CMAKE_CURRENT_BINARY_DIR}/JRSAOI_autogen/mocs_compilation_Release.cpp"
)
foreach(MOC_FILE ${MOC_FILES})
    set_source_files_properties(${MOC_FILE} PROPERTIES COMPILE_FLAGS "/wd4127")
endforeach()
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
