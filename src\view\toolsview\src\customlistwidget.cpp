#include "CustomListWidget.h"

//QT
#include <QPushButton>
#include <QMouseEvent>
#include <QHBoxLayout>
#include <QMenu>
#include <QAction>
namespace jrsaoi
{
    constexpr char DOUBLE_CLICK_ADD_ITEM[] = "双击此列添加项...";
    constexpr char ADD_ITEM_NAME[] = "请输入名称...";
    struct ImplData
    {
        QListWidgetItem* current_item;
        std::atomic<bool> is_edited_item;
    };
}

jrsaoi::CustomListWidget::CustomListWidget(QWidget* parent /*= nullptr*/)
    :QListWidget(parent)
    , _impl_data(new ImplData)
{
    InitMember();
    InitView();
    InitConnect();


}

jrsaoi::CustomListWidget::~CustomListWidget()
{

}


void jrsaoi::CustomListWidget::AddItem(const std::string& group_name)
{
    auto item_temp = LastItem();
    item_temp->setText(QString::fromStdString(group_name));
    item_temp->setIcon(QIcon(":/image/group.png"));
    AddListItem(":/image/adddevice.png", DOUBLE_CLICK_ADD_ITEM);
}


std::vector<std::string> jrsaoi::CustomListWidget::GetAllItemNames()
{
    std::vector<std::string> item_names;
    for (int i = 0; i < count(); ++i)
    {
        QListWidgetItem* item = this->item(i); // 获取第 i 项
        if (!item)
        {
            continue;
        }
        auto item_name = item->text().toStdString();
        if (item_name == DOUBLE_CLICK_ADD_ITEM || item_name == ADD_ITEM_NAME)
        {
            continue;
        }
        else
        {
            item_names.push_back(item_name);
        }
    }
    return item_names;
}

void jrsaoi::CustomListWidget::mouseDoubleClickEvent(QMouseEvent* event)
{
    QListWidget::mouseDoubleClickEvent(event);
    QPoint pos = event->pos();
    QListWidgetItem* item = itemAt(pos);

    if (item && item == LastItem())
    {
        _impl_data->current_item = item;
        item->setFlags(item->flags() | Qt::ItemIsEditable);
        SlotMultipleAddItem(item);
    }
}

void jrsaoi::CustomListWidget::keyPressEvent(QKeyEvent* event)
{
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter)
    {

    }
    else if (event->key() == Qt::Key_Escape)
    {
        _impl_data->current_item = nullptr;
        clearSelection();
    }
    else
    {
        QListWidget::keyPressEvent(event);
    }
}

void jrsaoi::CustomListWidget::SlotMultipleAddItem(QListWidgetItem* item_)
{
    if (item_ == _impl_data->current_item)
    {
        item_->setText(ADD_ITEM_NAME);
        item_->setIcon(QIcon(":/image/group.png"));
        AddListItem(":/image/adddevice.png", DOUBLE_CLICK_ADD_ITEM);
    }
}

void jrsaoi::CustomListWidget::SlotShowContextMenu(const QPoint& pos_)
{
    QListWidgetItem* item = this->itemAt(pos_);
    if (item)
    {
        QMenu context_menu(this);
        QAction* delete_action = context_menu.addAction(QIcon(":image/delete.png"), "删除");
        connect(delete_action, &QAction::triggered, this, [this, item]()
            {
                if (LastItem() != item)
                {
                    delete item;
                }
            });
        context_menu.exec(mapToGlobal(pos_));
    }
}

void jrsaoi::CustomListWidget::InitMember()
{
    _impl_data->is_edited_item = false;
}

void jrsaoi::CustomListWidget::InitView()
{
    this->setContextMenuPolicy(Qt::CustomContextMenu);
    AddListItem(":/image/adddevice.png", DOUBLE_CLICK_ADD_ITEM);
}

void jrsaoi::CustomListWidget::InitConnect()
{
    //connect(this, &QListWidget::itemChanged, this, &CustomListWidget::SlotItemChanged);
    connect(this, &QListWidget::customContextMenuRequested, this, &CustomListWidget::SlotShowContextMenu);
    connect(this, &QListWidget::currentItemChanged, this, [=](QListWidgetItem* current, QListWidgetItem* previous) {
        (void)previous;
        if (current)
        {
            auto current_item_name = current->text().toStdString();
            if (current_item_name != DOUBLE_CLICK_ADD_ITEM || current_item_name != ADD_ITEM_NAME)
            {
                emit SigCurrentSelectValidItemName(current_item_name);
            }
        }
        });
}

void jrsaoi::CustomListWidget::AddListItem(const std::string& img_path_, const std::string& item_name_)
{
    auto item = new QListWidgetItem(QIcon(QString::fromStdString(img_path_)), QString::fromStdString(item_name_));
    this->addItem(item);
}

QListWidgetItem* jrsaoi::CustomListWidget::LastItem() const
{
    return count() > 0 ? item(count() - 1) : nullptr;
}

QPushButton* jrsaoi::CustomListWidget::AddButton(const QString& txt_, const QString& icon_path_ /*= ""*/)
{
    QPushButton* btn = new QPushButton(this);
    QIcon icon(icon_path_);
    btn->setIcon(icon);
    btn->setIconSize(QSize(50, 50));
    btn->setText(txt_);
    return btn;
}
