
@startuml 模块架构

object "接口" as i1
object "状态管理器" as sm {
    响应其他管理器事件
    事件转发
    图形绘制事件
}
object "图形管理器" as gm {
    创建图形
    更新图形
    查找图形
    删除图形
    增加图层
    删除图层
    更新图层
    查找图层
}

object "ui管理器" as uim {
    接收ui控制
    视野控制
}

object "主ui" as mainui {
    传递qt信号
    布局
}

object "命令管理器" as cmdm {
    命令撤销
    命令重做
    图形创建命令
    图形删除命令
    图形修改命令
}

object "命令" as cmd {
    命令执行
    命令撤销
}

object "鼠标指针管理器" as cursorm {
    导入鼠标指针
    显示鼠标指针
}

object "gl管理器" as glr {
    获取opengl上下文
    渲染对象
}
object "缩略图导航" as tw {
    切换视野
}
object "标尺" as rw {
    显示场景刻度
    显示鼠标位置刻度
}

object "渲染对象" as ro {
    渲染
}

object "控制点" as cp {
    渲染
    触发
}
object "图形" as g {
    渲染
    触发控制点
}
object "图层" as l {
    修改绘制样式
}

i1 *--> sm
sm *--> gm
sm *--> uim
sm *--> cmdm
sm *--> cursorm

cmdm *--> cmd

gm *--> g
gm *--> l

g *--> cp

uim *--> mainui
uim *--> glr
uim *--> tw
uim *--> rw

gm *-->glr : 继承渲染对象进行渲染 

glr *--> ro

@enduml