﻿#include "systemstatemodel.h"

jrsaoi::SystemStateModel::SystemStateModel(const std::string& name_)
    :ModelBase(name_),
    _param_data(std::make_shared<jrsdata::SystemStateViewParam>()),
    _sys_state_param(std::make_shared<jrsdata::SystemStateParam>())
{
    _sys_state_param->topic_name = jrsaoi::SYSTEM_STATE_TOPIC_NAME;
    _sys_state_param->sub_name = jrsaoi::CONTROL_PANEL_VIEW_SUB_NAME;
    _sys_state_param->event_name = jrsaoi::UPDATE_SYSTEM_STATE_EVENT;
    _sys_state_param->module_name = jrsaoi::CONTROL_PANEL_MODULE_NAME;

    _param_data->check_item_name = jrscheckitem::CHECK_ALL_ITEMS;
    _param_data->module_name = name_;
    _param_data->topic_name = jrsaoi::SYSTEM_STATE_TOPIC_NAME;
    _param_data->sub_name = jrsaoi::SYSTEM_STATE_LOGIC_SUB_NAME;
    _param_data->invoke_module_name = jrsaoi::DEVICE_MODULE_NAME;
    _param_data->event_name = jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT;
    InitMember();
}

jrsaoi::SystemStateModel::~SystemStateModel()
{
}

int jrsaoi::SystemStateModel::Update(const jrsdata::ViewParamBasePtr& param_)
{
    if (!param_)
    {
        Log_ERROR("参数为空指针！");
        return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
    }

    std::lock_guard<std::mutex> lock(_mtx);
    _param_data->event_name = param_->event_name;
    int await_check_item_num = 0;
    UpdateCheckState(_param_data->check_items, _param_data->is_ok, await_check_item_num);

    int total_items = static_cast<int>(_param_data->check_items.size());
    _param_data->progress_rate = CalculateProgressRate(total_items, await_check_item_num);

    UpdateSystemState();
    return jrscore::AOI_OK;
}

void jrsaoi::SystemStateModel::UpdateCheckState(jrsdata::SystemStateMap& check_items_, bool& is_ok_, int& await_check_item_num_)
{
    is_ok_ = true;
    for (auto& [_, item] : check_items_)
    {
        switch (item.check_state)
        {
        case jrsdata::MachineCheckParamInfo::CheckState::FIALED:
            is_ok_ = false;
            item.err_info = GetErrorInfo(item.code, item.err_info);
            break;
        case jrsdata::MachineCheckParamInfo::CheckState::WAITTING:
        case jrsdata::MachineCheckParamInfo::CheckState::CHECKING:
            ++await_check_item_num_;
            break;
        default:
            break;
        }
    }
}

// 计算检查进度
int jrsaoi::SystemStateModel::CalculateProgressRate(int total_items_, int await_check_item_num_)
{
    return total_items_ == 0 ? 100 : static_cast<int>((total_items_ - await_check_item_num_) * 100.0 / total_items_);
}




int jrsaoi::SystemStateModel::Save(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    return jrscore::AOI_OK;
}

const jrsdata::SystemStateViewParamPtr& jrsaoi::SystemStateModel::GetModelData()
{
    return _param_data;
}

const jrsdata::SystemStateParamPtr& jrsaoi::SystemStateModel::GetSystemState()
{
    return _sys_state_param;
}

void jrsaoi::SystemStateModel::UpdateSystemState()
{
    const std::vector<std::pair<std::string, jrsdata::SystemStateParam::SystemItem>> check_items = {
        {jrscheckitem::SYSTEM_PARAM_CHECK_ITEM, jrsdata::SystemStateParam::SystemItem::PARAM_STATE},
        //{jrscheckitem::TRACK_CHECK_ITEM, jrsdata::SystemStateParam::SystemItem::MOTION_TRACK_STATE},
        //{jrscheckitem::MOTION_CHECK_ITEM, jrsdata::SystemStateParam::SystemItem::MOTION_AXIS_STATE},
        {jrscheckitem::LIGHT_SOURCE_ITEM, jrsdata::SystemStateParam::SystemItem::STRUCT_LIGHT_STATE},
    };
    // 更新系统状态
    UpdateSystemState(_param_data->check_items, check_items, _sys_state_param->current_system_states);
}


void jrsaoi::SystemStateModel::InitMember()
{
    std::vector<std::pair<std::string, std::string>> check_items = {
    {jrscheckitem::SYSTEM_PARAM_CHECK_ITEM, "配置文件初始化"},
    //{jrscheckitem::TRACK_CHECK_ITEM, "轨道初始化"},
    //{jrscheckitem::MOTION_CHECK_ITEM, "运控初始化"},
    {jrscheckitem::LIGHT_SOURCE_ITEM, "光源初始化"},
    {jrscheckitem::DATABASE_CHECK_ITEM, "数据库连接"},
    //{jrscheckitem::CAMERA_CHECK_ITEM, "相机初始化"},
    //{jrscheckitem::CALIBRATION_CHECK_ITEM, "标定初始化"},
    //{jrscheckitem::TWO_DIMESION_IMAGE_CHECK_ITEM, "2D图像初始化"},
    //{jrscheckitem::TREE_DIMETION_IMAGE_CHECK_ITEM, "3D图像初始化"}
    };

    for (const auto& item : check_items)
    {
        _param_data->check_items.insert(std::make_pair(item.first, jrsdata::MachineCheckParamInfo(item.second)));
    }
    InitSystemState(_sys_state_param->current_system_states);
}


// 初始化系统状态为 OK
void jrsaoi::SystemStateModel::InitSystemState(std::unordered_map<jrsdata::SystemStateParam::SystemItem, jrsdata::SystemStateParam::StateLevelInfo>& system_state)
{
    for (const auto& item : {
            jrsdata::SystemStateParam::SystemItem::PARAM_STATE,
            jrsdata::SystemStateParam::SystemItem::MOTION_TRACK_STATE,
            jrsdata::SystemStateParam::SystemItem::MOTION_AXIS_STATE,
            jrsdata::SystemStateParam::SystemItem::STRUCT_LIGHT_STATE
        })
    {
        system_state[item] = { jrsdata::SystemStateParam::StateLevel::OK, "" };
    }
}

// 获取错误信息
std::string jrsaoi::SystemStateModel::GetErrorInfo(int code, const std::string& err_info) {
    if (code == -1)
    {
        return err_info;
    }
    std::string error_info;
    auto error_struct = GET_ERROR_INFO(code);
    error_info = error_struct.err_description;
    return error_info;
}

// 更新系统状态
void jrsaoi::SystemStateModel::UpdateSystemState(const std::unordered_map<std::string, jrsdata::MachineCheckParamInfo>& check_items_data,
    const std::vector<std::pair<std::string, jrsdata::SystemStateParam::SystemItem>>& check_items_map,
    std::unordered_map<jrsdata::SystemStateParam::SystemItem, jrsdata::SystemStateParam::StateLevelInfo>& system_state)
{
    for (const auto& [check_item_name, system_item] : check_items_map)
    {
        const auto& check_item_data = check_items_data.at(check_item_name);
        if (check_item_data.code != jrscore::AOI_OK)
        {
            std::string error_info = GetErrorInfo(check_item_data.code, check_item_data.err_info);
            system_state[system_item] = { jrsdata::SystemStateParam::StateLevel::ERR, error_info };
        }
    }
}
