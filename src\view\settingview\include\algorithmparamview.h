﻿#pragma once
//STD
#include <any>
//QT
#include <QWidget>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QAction>
#include <QMessageBox>
#include <QFileDialog>
#include <QLineEdit>
#include <QSpinBox>

//Custom
#include "viewparam.hpp"
#include "ui_algorithmparamview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class algorithmparamClass; };
QT_END_NAMESPACE

namespace jrsaoi
{
    using ControlMap = std::variant<QLineEdit*, QCheckBox*>;

    class AlgorithmParamView : public QWidget
    {
        Q_OBJECT

    public:
        AlgorithmParamView(QWidget* parent = nullptr);
        ~AlgorithmParamView();
        /**
         * @fun UpdateView
         * @brief 从数据库获取数据更新界面
         * @date 2024.5.7
         * <AUTHOR>
         */
        void UpdateView(const jrsdata::AlgorithmParamSettingVec& algorithm_param_);



        /**
         * @fun GetSystemParam
         * @brief 获取当前系统参数
         * @return
         * @date 2024.7.9
         * <AUTHOR>
         */
        jrsdata::AlgorithmParamSettingVec GetAlgorithmParam();
    private slots:
        void SlotSaveAlgorithmParam();
        void SlotChooseDirPath();
    
    signals:
        void SigSaveAlgorithm(const jrsdata::AlgorithmParamSettingVec& algorithm_param_);

    private:
        /**
         * @fun InitConnect
         * @brief 初始化槽函数连接
         * @date 2024.5.7
         * <AUTHOR>
         */
        void InitConnect();

       


        /**
         * @fun InitMember
         * @brief 初始化成员属性
         * @date 2024.5.7
         * <AUTHOR>
         */
        void InitMember();

        /**
        * @fun SaveRemoteParam
        * @brief 保存数据
        * @date 2024.5.7
        * <AUTHOR>
        */
        void SaveSystemAlgorithm();
        /**
         * @fun UpdateStaticParam
         * @brief 更新静态参数界面
         * @date 2024.5.8
         * <AUTHOR>
         */
        void UpdateStaticParam();
        /**
         * @fun SetHideController
         * @brief 隐藏控件
         * @param system_param_
         * @date 2024.7.9
         * <AUTHOR>
         */
        void SetHideController(const jrsdata::AlgorithmParamSettingVec& algorithm_param_);

        std::map<std::string, QWidget*> ui_map_value; /**< 系统参数到控件按钮的映射 系统参数名称---对应的UI上控件*/
        std::map<std::string, QLineEdit*> map_shared_path;
        std::vector<QWidget*> ui_need_hide_controller;/**< 需要隐藏的控件 */
        Ui::algorithmparamClass* ui;
        jrsdata::AlgorithmParamSettingVec _algorithm_param;
    };
}