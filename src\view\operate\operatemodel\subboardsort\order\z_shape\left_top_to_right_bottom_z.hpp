/*****************************************************************
 * @file   left_top_to_right_bottom_z.hpp
 * @brief  : z字形排序
   *____

     /
    ____->

 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class ZLeftTopToRightBottom :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {
            int subboard_id = 1;
            for (auto& subboards : subboards_)
            {
                int col_size = (int)subboards.size();
                for (int col_num = col_size - 1;col_num >= 0;--col_num)
                {
                    UpdateSubboard(subboards[col_num], subboard_id++);
                }
            }
            return jrscore::AOI_OK;
        }
    };
}