#ifndef MOTIONERRORFUNC_H
#define MOTIONERRORFUNC_H

#pragma warning(push, 1)
#pragma warning(disable : 4127)
#pragma warning(disable: 4267)
#include "MotionErrorParam.h"
#include <unsupported/Eigen/NonLinearOptimization>
#pragma warning(pop)

//using namespace std;
using namespace Eigen;

// Generic functor
template <typename _Scalar, int NX = Dynamic, int NY = Dynamic>
struct Functor
{
	typedef _Scalar Scalar;
	enum
	{
		InputsAtCompileTime = NX,
		ValuesAtCompileTime = NY
	};
	typedef Matrix<Scalar, InputsAtCompileTime, 1> InputType;
	typedef Matrix<Scalar, ValuesAtCompileTime, 1> ValueType;
	typedef Matrix<Scalar, ValuesAtCompileTime, InputsAtCompileTime> JacobianType;

	Functor(const JrsPoint* points, const int& _ptNum) :
		pts(points),
		ptNumb(_ptNum)
	{
	}
	int values() const { return ptNumb; }
protected:
	int ptNumb;

public:
	const JrsPoint* pts;
};

/// @brief 运动误差函数
struct BezierCurve_functor : Functor<double>
{
	int x_n;                                              // x方向Bezier曲线阶数
	int y_n;                                              // y方向Bezier曲线阶数
	const JrsPoint* ts;                                   // xy方向的运动比例

	/// @brief
	/// @param points 实际运动到的位置
	/// @param _ts    xy方向的运动比例
	/// @param _ptNum 拟合点的个数
	/// @param _n_x   x方向Bezier曲线阶数
	/// @param _n_y   y方向Bezier曲线阶数
	BezierCurve_functor(const JrsPoint* points, const JrsPoint* _ts, const int& _ptNum, const int& _n_x, const int& _n_y)
		: Functor<double>(points, _ptNum)
	{
		x_n = _n_x;
		y_n = _n_y;
		ts = _ts;
	};

	/// @brief    计算Bezier曲线第k项的系数
	/// @param n  Bezier曲线阶数
	/// @param k  第k项索引
	/// @return   第k项的系数
	static double CalBinomialCoeff(int n, int k)
	{
		int res = 1;
		if (k > n - k)
		{
			k = n - k;
		}
		for (int i = 0; i < k; ++i)
		{
			res *= (n - i);
			res /= (i + 1);
		}
		return res;
	};

	/// @brief             计算实际运动到的位置
	/// @param t           xy的运动比例
	/// @param ctrlPts     xy方向运动的控制点
	/// @return            实际运动到的位置
	static JrsPoint CalculateBezierPoint(const double& t, const VectorXd& ctrlPts)
	{
		size_t n = ctrlPts.size() / 2 - 1;
		double x = 0, y = 0;
		for (int i = 0; i <= n; ++i)
		{
			double factor = CalBinomialCoeff((int)n, i) * pow(1 - t, (int)n - i) * pow(t, i);
			x += factor * ctrlPts[i * 2];
			y += factor * ctrlPts[i * 2 + 1];
		}
		return JrsPoint(x, y);
	};

	/// @brief             计算当前模型误差
	/// @param b           当前模型参数
	/// @param fvec        所有点的误差值
	/// @return
	int operator()(const VectorXd& b, VectorXd& fvec) const
	{
		assert(b.rows() == (x_n + y_n) * 2);
		for (int i = 0; i < ptNumb; i++)
		{
			Eigen::VectorXd bx = b.segment(0, x_n * 2);
			Eigen::VectorXd by = b.segment(x_n * 2, y_n * 2);
			auto point = CalculateBezierPoint(ts[i].x, bx) + CalculateBezierPoint(ts[i].y, by);
			fvec[i] = sqrt(pow(point.y - pts[i].y, 2) + pow(point.x - pts[i].x, 2));
		}
		return 0;
	};
};

#endif