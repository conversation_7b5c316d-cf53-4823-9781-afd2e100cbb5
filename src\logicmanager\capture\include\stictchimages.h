/**
 * @file stictchimages.h 图像拼接相关的工具类
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2024-08-14
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#pragma once

#pragma warning(push,3)
#pragma warning(disable: 4127)
#include <opencv2/opencv.hpp>
#pragma warning(pop)

#include <vector>

using std::vector;
using cv::Mat;

class StictchImagesTool
{
public:
	StictchImagesTool(const float& _resolution, const int& _fov_w, const int& _fov_h, const bool& _x_axis_neg, const bool& _y_axis_neg);
	~StictchImagesTool();
	
private:
	float resolution = 1.0;                     /**< 相机分辨率*/
    float resolution_x = 1.0;                     /**< 相机分辨率*/
    float resolution_y = 1.0;                     /**< 相机分辨率*/
	bool  x_axis_neg = false;                   /**< x轴是否与图像相反*/
	bool  y_axis_neg = false;                   /**< y轴是否与图像相反*/
	int   fov_w = 0;                            /**< 相机视场宽度*/
	int   fov_h = 0;                            /**< 相机视场高度*/

	float x_min = 0.0f;                         /**< 所有扫描位置x轴最小值*/
	float x_max = 0.0f;                         /**< 所有扫描位置x轴最大值*/    
	float y_min = 0.0f;                         /**< 所有扫描位置y轴最小值*/
	float y_max = 0.0f;                         /**< 所有扫描位置y轴最大值*/



public:
    /**
     * @brief 设置所有拍摄点的位置用于计算大图的尺寸
     * 
     * @param all_img_pos 所有拍摄点的位置
     */
	void SetAllFovPos(const vector<cv::Point2f>& all_img_pos);

    /**
     * @brief 获取大图的尺寸
     * 
     * @param out_img_w 大图的宽度
     * @param out_img_h 大图的高度
     */
	void GetBoardImageSize(int& out_img_w, int& out_img_h) const;

    /**
     * @brief 图像拼接
     * 
     * @param imgs_data 小图图像数据
     * @param board_image 拼接后的大图图像
     * @return int 错误码
     */
	int  StictchImages(const vector<std::pair<cv::Point2f, Mat>>& imgs_data, Mat& board_image,Mat& output_mask,const bool& is_merge);

     /**
     * @brief 计算羽化图像（拼接融合小图像，两张融合）
     *
     * @param input_img1   输入待拼接融合图像1
     * @param input_img2   输入待拼接融合图像2
     * @param feather_mask 输入羽化掩码
     * @param output_img   输出拼接后的小图图像
     * @return int 错误码
     */
    int CalculateFeatherImage(const cv::Mat& input_img1, const cv::Mat& input_img2, const cv::Mat& feather_mask,cv::Mat& output_img);

};