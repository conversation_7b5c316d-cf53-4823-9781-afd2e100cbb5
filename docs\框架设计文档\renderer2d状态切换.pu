
@startuml 状态切换

state "悬停状态" as s1 : 该状态下鼠标移动不会触发事件
state "多选状态" as s2 : 该状态下鼠标点击会选择多个节点,并可以执行框选节点操作
state "选择状态" as s3 : 该状态下鼠标点击会选择节点
state "响应状态" as s4 : 该状态鼠标移动会触发响应事件
state "编辑状态" as s5 : 该状态下鼠标按下会响应节点,鼠标移动会编辑节点
state "视野移动状态" as s6 : 该状态下鼠标移动会移动视野
state "多边形选择状态" as s7 : 该状态下鼠标左键按下作为多边形选择起点,\n左键点击增加多边形轮廓点,\n右键点击作为多边形选择终点,\n并以多边形框选节点
state "观察状态" as s8 : 该状态下进入全景预览,不会响应任何事件

[*] --> s1 : 默认状态
s1 --> s3 : 鼠标左键抬起
s1 --> s6 : 鼠标右键按下时移动,\n并且未触发其他状态
s1 --> s7 : 按下SHIFT
s2 --> s1 : 鼠标抬起后\n不存在选中的节点
s2 --> s4 : 鼠标抬起后\n存在选中的节点
s3 --> s2 : 按下CTRL
s3 --> s1 : 鼠标抬起后\n不存在选中的节点
s3 --> s4 : 鼠标抬起后\n存在选中的节点
s4 --> s3 : 鼠标按下时\n不存在响应节点
s4 --> s5 : 鼠标按下时\n存在响应节点
s4 --> s2 : 按下CTRL
s5 --> s1 : 鼠标抬起
s6 --> s1 : 鼠标抬起
s7 --> s1 : 执行多边形框选后\n不存在选中的节点
s7 --> s4 : 执行多边形框选后\n存在选中的节点

s1 --> s8 : 由软件主动设置观察模式
s8 --> s1 : 由软件主动退出观察模式

@enduml