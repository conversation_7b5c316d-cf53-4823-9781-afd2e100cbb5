#include "controlpointfactory.h"
#include "controlpointabstract.h"
#include "controlpointobject.h"
#include "graphicsabstract.hpp"
#include "graphicsalgorithm.h"

#include "customcursortype.hpp"

#include <iostream>

const int control_point_w = 2; /**< 控制点宽度 */
const int control_point_h = 2; /**< 控制点高度 */

enum CornerDirection
{
    RIGHT,
    RIGHT_UP,
    UP,
    LEFT_UP,
    LEFT,
    LEFT_DOWN,
    DOWN,
    RIGHT_DOWN,
};
/**
 * @brief 根据角度获取角方向
 * @param angle 输入的角度，范围为[-180°, 180°]
 * @return 角方向：
 *         0 表示右       (-22.5°, 22.5°]
 *         1 表示右上     (22.5°, 67.5°]
 *         2 表示上       (67.5°, 112.5°]
 *         3 表示左上     (112.5°, 157.5°]
 *         4 表示左       (157.5°, 180°] ∪ [-180°, -157.5°)
 *         5 表示左下     (-157.5°, -112.5°]
 *         6 表示下       (-112.5°, -67.5°]
 *         7 表示右下     (-67.5°, -22.5°]
 */
CornerDirection GetAngleCornerDirection(double angle)
{
    if (angle > -22.5 && angle <= 22.5)
    {
        return CornerDirection::RIGHT; // 右
    }
    else if (angle > 22.5 && angle <= 67.5)
    {
        return CornerDirection::RIGHT_UP; // 右上
    }
    else if (angle > 67.5 && angle <= 112.5)
    {
        return CornerDirection::UP; // 上
    }
    else if (angle > 112.5 && angle <= 157.5)
    {
        return CornerDirection::LEFT_UP; // 左上
    }
    else if (angle > -157.5 && angle <= -112.5)
    {
        return CornerDirection::LEFT_DOWN; // 左下
    }
    else if (angle > -112.5 && angle <= -67.5)
    {
        return CornerDirection::DOWN; // 下
    }
    else if (angle > -67.5 && angle <= -22.5)
    {
        return CornerDirection::RIGHT_DOWN; // 右下
    }
    else
    {
        return CornerDirection::LEFT; // 左
    }
}

CustomCursorType GetDirectionCursor(const CornerDirection& direction)
{
    switch (direction)
    {
    case CornerDirection::RIGHT:
    case CornerDirection::LEFT:
        return CustomCursorType::ResizeHorizontal;
    case CornerDirection::UP:
    case CornerDirection::DOWN:
        return CustomCursorType::ResizeVertical;
    case CornerDirection::RIGHT_UP:
    case CornerDirection::LEFT_DOWN:
        return CustomCursorType::ResizeDiagonal2;
    case CornerDirection::RIGHT_DOWN:
    case CornerDirection::LEFT_UP:
        return CustomCursorType::ResizeDiagonal1;
    }
    return CustomCursorType::Default;
}


std::shared_ptr<ControlPointAbstract> ControlPointFactory::CreateControlPoint(
    const ControlPointType& type, GraphicsAbstract* const obj)
{
    if (!obj)
        return std::shared_ptr<ControlPointAbstract>();

    auto boundingbox = obj->GetBoundingbox();
    if (boundingbox.size.empty())
    {
        return std::shared_ptr<ControlPointAbstract>();
    }

    switch (type)
    {
    case ControlPointType::MOVE_POINT:
    {
        cv::Point2f ovp[4];
        boundingbox.points(ovp);
        std::vector<Vec2> paths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y},
        {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };
        auto cpd = ControlPointDraw(boundingbox.center.x, boundingbox.center.y, control_point_w, control_point_h,
            static_cast<int>(CustomCursorType::Move), false, paths);
        auto attr = ControlAttributes(static_cast<int>(type), 0, -1);
        auto cp = std::make_shared<ControlPointMove>();
        cp->SetAttributes(attr);
        cp->SetDraw(cpd);
        return cp;
    }
    break;

    case ControlPointType::CONTOUR_POINT:
    case ControlPointType::BORDER_POINT:
    case ControlPointType::NONE_POINT:
    default:
        break;
    }
    return std::shared_ptr<ControlPointAbstract>();
}

std::vector<std::shared_ptr<ControlPointAbstract>> ControlPointFactory::CreateControlPointGroup(
    const ControlPointGroupType& grouptype, GraphicsAbstract* const obj)
{
    if (!obj)
        return std::vector<std::shared_ptr<ControlPointAbstract>>();

    auto boundingbox = obj->GetBoundingbox();
    if (boundingbox.size.empty())
    {
        return std::vector<std::shared_ptr<ControlPointAbstract>>();
    }

    std::vector<std::shared_ptr<ControlPointAbstract>> cps;
    switch (grouptype)
    {
    case ControlPointGroupType::SIZE_POINT:
    {
        auto& center = boundingbox.center;
        //auto angle = obj->a();
        cv::Point2f vertex[4];
        boundingbox.points(vertex);

        /*创建顶点控制点*/
        {
            bool is_render = false;
            int vertex_cursor[4]{};
            for (int i = 0; i < 4; ++i)
            {
                auto vector_angle = GetVectorAngle(vertex[i].x - center.x, -(vertex[i].y - center.y)); // 图像坐标系y轴反向
                auto dir = GetAngleCornerDirection(vector_angle);
                vertex_cursor[i] = static_cast<int>(GetDirectionCursor(dir));
            }
            for (int i = 0; i < 4; ++i)
            {
                auto cpd = ControlPointDraw(vertex[i].x, vertex[i].y, control_point_w, control_point_h,
                    vertex_cursor[i], is_render, {});
                auto attr = ControlAttributes(static_cast<int>(ControlPointType::CORNER_POINT_LEFT_DOWN) + i, i, -1);
                auto cp = std::make_shared<ControlPointSize>();
                cp->SetAttributes(attr);
                cp->SetDraw(cpd);
                cps.emplace_back(cp);
            }
        }
        /*创建腰控制点*/
        {
            bool is_render = false;
            std::vector<cv::Point2f> vcp;
            vcp.reserve(4);
            for (int i = 0; i < 4; ++i)
            {
                vcp.emplace_back((vertex[(i + 4) % 4] + vertex[(i + 5) % 4]) * 0.5f);
            }

            int vertex_waist[4]{};
            for (int i = 0; i < 4; ++i)
            {
                auto vector_angle = GetVectorAngle(vcp[i].x - center.x, -(vcp[i].y - center.y));
                auto dir = GetAngleCornerDirection(vector_angle);
                vertex_waist[i] = static_cast<int>(GetDirectionCursor(dir));
            }

            for (int i = 0; i < 4; ++i)
            {
                auto cpd = ControlPointDraw(vcp[i].x, vcp[i].y, control_point_w, control_point_h, 
                    vertex_waist[i], is_render, {});
                auto attr = ControlAttributes(static_cast<int>(ControlPointType::WAIST_POINT_LEFT) + i, i, -1);
                //cps.emplace_back(CreateControlPoint(attr, cpd));
                auto cp = std::make_shared<ControlPointSize>();
                cp->SetAttributes(attr);
                cp->SetDraw(cpd);
                cps.emplace_back(cp);
            }
        }
    }
    break;
    case ControlPointGroupType::CORNER_POINT:
    case ControlPointGroupType::WAIST_POINT:
    case ControlPointGroupType::ROTATE_POINT:
    case ControlPointGroupType::CONTOUR_POINT:
    case ControlPointGroupType::BORDER_POINT:
    case ControlPointGroupType::NONE_POINT:
        break;
    }
    return cps;
}

