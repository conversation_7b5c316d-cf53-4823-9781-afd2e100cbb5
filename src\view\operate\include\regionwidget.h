/*********************************************************************
 * @brief  检测框控件,进行检测框编辑.
 *
 * @file   regionwidget.h
 *
 * @date   2024.09.18
 * <AUTHOR>
 *********************************************************************/
#ifndef REGIONWIDGET_H
#define REGIONWIDGET_H

#include <QGroupBox>

namespace jrsdata
{
    struct DetectWindow;
}
class QBoxLayout;
class QPushButton;
class RegionWidget : public QGroupBox
{
    Q_OBJECT

public:
    RegionWidget(const QString& window_name, QWidget* parent = nullptr);

    void SetList(const QStringList& list_algo, const QStringList& list_error);
    void CreateAlgo(const QString& algo_widget_name);
    void DeleteAlgo(const QString& algo_widget_name);
    void ClearAlgo();
    
    void SetValue(const jrsdata::DetectWindow& region);
    void GetValue(jrsdata::DetectWindow& region);

signals:
    void SignalChangeAlgo(const QString& algo_widget_name, const QString& algo_name);
    void SignalChangeError(const QString& algo_widget_name, const QString& error_name);
    void SignalCreateAlgoWidget(const QString& algo_widget_name);
    void SignalDeleteAlgoWidget(const QString& algo_widget_name);
    void SignalEnableAlgoWidget(const QString& algo_widget_name, bool state);

protected slots:
    void SlotCreateAlgo(const QString& algo_widget_name);

private:
    QBoxLayout* layout;
    QPushButton* btn_add;
    QStringList list_algo;
    QStringList list_error;
};
#endif // REGIONWIDGET_H