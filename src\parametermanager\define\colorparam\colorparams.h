/*****************************************************************//**
 * @file   colorparam.h
 * @brief  颜色控件参数
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __COLORPARAMS_H__
#define __COLORPARAMS_H__


// std
#include <array>
#include <map>
#include <string>
#include "pluginexport.hpp"
// opencv
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#pragma warning(pop)

enum class ColorWheelDealType : int
{
    NONE, // 未做处理
    ENHANCE, // 图像增强
    BINARY // 二值化
};

// HSV颜色轮上的点
using ColorWheelPts = std::array<std::pair<double, double>, 6>;

// HSV调色参数
struct ColorWheelThreshVal
{
    int v_low = 0;
    int v_high = 255;
	//unsigned char hs_table[180][256];
    std::vector<unsigned char> hs_table_one_dim;
    ColorWheelPts pts;
    int type = 0;
    JRS_AOI_PLUGIN_API std::string ToJson() const;
    static ColorWheelThreshVal FromJson(const std::string& json_str);
    JRS_AOI_PLUGIN_API void Set1DHsTableFrom2DArray(unsigned char hs_table[180][256]);
};

// 二值化类型
enum BinaryType
{
    RGB_BINARY = 0,
    GRAY_BINARY = 1,
    HSV_BINARY = 2,
    HEIGHT_BINARY = 3
};
// 灰度转换参数
enum GrayType
{
    WIDGE = 0,
    MAX = 1,
    MIN = 2,
    MEAN = 3
};

// 图像组
struct ImageLabel
{
    std::string name;
    cv::Mat mat;
};

// 图像预处理参数
struct PreProcessParams
{
    float contrast_value = 1.0;
    float brightness_value = 1.0;
    float hue_value = 0;
    float saturation_value = 1.0;
    float gamma_value = 1.0;
    bool is_reverse = false;
    int image_id = 0;
    JRS_AOI_PLUGIN_API std::string ToJson() const;
    JRS_AOI_PLUGIN_API static PreProcessParams FromJson(const std::string& json_str);
};

// 图像二值化参数
struct BinProcessParams
{
    int binary_type_index = 0;
    int r_thre_min = 0;
    int r_thre_max = 255;
    int g_thre_min = 0;
    int g_thre_max = 255;
    int b_thre_min = 0;
    int b_thre_max = 255;

    int gray_type_index = 1;
    int gray_thre_min = 0;
    int gray_thre_max = 255;

    ColorWheelThreshVal hsv_paramas;

    int height_bin_nums = 50;
    float min_hei = 0.0;
    float max_hei = 255.0;
    int height_thre_min = 0;
    int height_thre_max = int(height_bin_nums);

    JRS_AOI_PLUGIN_API std::string ToJson() const;
    JRS_AOI_PLUGIN_API static BinProcessParams FromJson(const std::string& json_str);
};

// 调色参数
struct ColorParams
{
    ColorWheelDealType deal_type = ColorWheelDealType::NONE;
    PreProcessParams pre_process_params;
    BinProcessParams binary_params;
    JRS_AOI_PLUGIN_API std::string ToJson() const;
    JRS_AOI_PLUGIN_API static ColorParams FromJson(const std::string& json_str);
};
#endif // __COLORPARAMS_H__
