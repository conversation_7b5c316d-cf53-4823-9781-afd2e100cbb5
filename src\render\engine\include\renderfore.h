﻿/*********************************************************************
 * @brief  前景渲染.
 *
 * @file   renderfore.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include "renderabstract.hpp" // RenderAbstract
#include <string>
#include <chrono>

struct RenderMessage
{
    std::string message;  // 消息
    int       duration; // 持续时间(毫秒),-1表示永久显示
    int       size;    // 字体大小
    std::chrono::steady_clock::time_point time; // 消息显示时间
    RenderMessage(const std::string& message_, int duration_ = 2000, int size_ = 12)
        : message(message_), duration(duration_), size(size_) {
    }
    RenderMessage& operator=(const RenderMessage& message_) {
        message = message_.message;
        duration = message_.duration;
        size = message_.size;
        time = std::chrono::steady_clock::time_point();
        return *this;
    }
};

class Painter;
class RenderFore : public RenderAbstract
{
public:
    RenderFore();
    ~RenderFore();

    void Render() override;
    void Destroy() override;
    /**
     * @brief  设置渲染取色器颜色.
     * @param r_ 红色分量
     * @param g_ 绿色分量
     * @param b_ 蓝色分量
     * @param l_ 亮度
     */
    void SetHoverColor(int r_, int g_, int b_, int l_);
    /**
     * @brief  设置渲染缩放倍率.
     */
    void SetDrawZoom(float zoom_);
    /**
     * @brief  设置是否渲染系统信息
     */
    void SetDrawSystemInfo(bool is_draw_system_info_);
    /**
    * @brief  是否渲染十字线
    */
    void SetDrawCenterCrossLine(bool is_draw_center_cross_line_);
    /**
     * @brief 获取是否绘制十字线
     */
    bool GetIsDrawCenterCrossLine();
    /**
     * @brief  设置渲染消息
     */
    void SetMessage(const RenderMessage& message_);

private:
    void DrawCross(Painter* p);
    void DrawHoverColor(Painter* p);
    void DrawSystemInfo(Painter* p);
    void DrawMessage(Painter* p);

    void UpdateFps();

private:
    bool is_draw_center_cross_line;  ///<是否渲染十字线
    bool is_draw_system_info; ///< 是否渲染系统信息

    int r; ///< 取色器-红色分量
    int g; ///< 取色器-绿色分量
    int b; ///< 取色器-蓝色分量
    int l; ///< 取色器-亮度

    float fps;       ///< 帧率
    float cpu_usage; ///< CPU使用率
    float mem_usage; ///< 内存使用率
    float zoom;      ///< 缩放倍率

    int frame_count; ///< 帧计数
    std::chrono::steady_clock::time_point last_time;  ///< 上一次渲染时间

    RenderMessage message; ///< 消息
};
