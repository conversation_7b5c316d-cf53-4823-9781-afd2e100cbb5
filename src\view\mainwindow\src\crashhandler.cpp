#if defined(_WIN32) || defined(_WIN64)

#include "CrashHandler.h"
#include "coreapplication.h"
#include <QMessageBox>

void CrashHandler::Init() 
{
    SetUnhandledExceptionFilter(CrashHandler::ExceptionFilter);
}

LONG WINAPI CrashHandler::ExceptionFilter(EXCEPTION_POINTERS* pExceptionPointers) 
{
    GenerateBreakStackLog();
    WriteMiniDump(pExceptionPointers);
    QMessageBox::critical(nullptr, QString::fromWCharArray(L"异常"), QString::fromWCharArray(L"程序异常，请联系管理员！"));
    return EXCEPTION_EXECUTE_HANDLER;
}

void CrashHandler::WriteMiniDump(EXCEPTION_POINTERS* pExceptionPointers)
 {
    std::wstring dump_file_name = GetDumpFileName();

    HANDLE h_dump_file = CreateFileW(dump_file_name.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (h_dump_file == INVALID_HANDLE_VALUE) {
        std::wcerr << L"Failed to create dump file: " << dump_file_name << std::endl;
        return;
    }

    MINIDUMP_EXCEPTION_INFORMATION dump_info = {};
    dump_info.ThreadId = GetCurrentThreadId();
    dump_info.ExceptionPointers = pExceptionPointers;
    dump_info.ClientPointers = FALSE;

    BOOL success = MiniDumpWriteDump(
        GetCurrentProcess(),
        GetCurrentProcessId(),
        h_dump_file,
        static_cast<MINIDUMP_TYPE>(
            MiniDumpWithIndirectlyReferencedMemory |
            MiniDumpScanMemory |
            MiniDumpWithThreadInfo |
            MiniDumpWithUnloadedModules |
            MiniDumpWithHandleData |
            MiniDumpWithCodeSegs
            ),
        &dump_info,
        nullptr,
        nullptr
    );

    if (!success) {
        DWORD error_code = GetLastError();
        std::wcerr << L"MiniDumpWriteDump failed with error: " << error_code << std::endl;
        std::wcerr << L"可能是你的 DbgHelp.dll 版本太旧，请将新版 DbgHelp.dll 放入 EXE 同目录下。" << std::endl;
    }

    CloseHandle(h_dump_file);
}

std::wstring CrashHandler::GetDumpFileName() 
{
    SYSTEMTIME st;
    GetLocalTime(&st);

    std::filesystem::path current_path = std::filesystem::current_path();
    auto dump_path_dir = current_path.string() + "//log//dump//";
    std::filesystem::create_directories(dump_path_dir);

    std::wstringstream ss;
    ss << dump_path_dir.c_str()
       << L"Crash_" << st.wYear << L"-" << st.wMonth << L"-" << st.wDay << L"_"
       << st.wHour << L"-" << st.wMinute << L"-" << st.wSecond << L".dmp";
    return ss.str();
}

void CrashHandler::GenerateBreakStackLog()
{
    
    HANDLE process = GetCurrentProcess();
    SymSetOptions(SYMOPT_LOAD_LINES | SYMOPT_UNDNAME); // 启用源码行号和函数名去修饰

    if (!SymInitialize(process, nullptr, TRUE))
    {
        std::cerr << "SymInitialize failed." << std::endl;
        return;
    }

    const int max_frames = 64;
    void* stack[max_frames];
    USHORT frames = CaptureStackBackTrace(0, max_frames, stack, nullptr);

    SYMBOL_INFO* symbol = (SYMBOL_INFO*)calloc(sizeof(SYMBOL_INFO) + 256, 1);
    symbol->MaxNameLen = 255;
    symbol->SizeOfStruct = sizeof(SYMBOL_INFO);

    IMAGEHLP_LINE64 line_info = {};
    line_info.SizeOfStruct = sizeof(IMAGEHLP_LINE64);

 

    LogTo_ERROR("dump//Dumpfile", "===== Crash Stack Trace =====");

    for (USHORT i = 0; i < frames; ++i)
    {
        DWORD64 addr = (DWORD64)(stack[i]);

        if (SymFromAddr(process, addr, nullptr, symbol))
        {
            LogTo_ERROR("dump//Dumpfile",  "#" , i ,": " ,symbol->Name);

            DWORD displacement = 0;
            if (SymGetLineFromAddr64(process, addr, &displacement, &line_info))
            {
                LogTo_ERROR("dump//Dumpfile"," in " ,line_info.FileName ,":" , line_info.LineNumber);
            }

        }
        else
        {
            LogTo_ERROR("dump//Dumpfile","#" ,i , ": [Symbol resolve failed at address 0x"
                ,  addr ,  "]" );
        }
    }
    free(symbol);
    SymCleanup(process);
}
#endif
