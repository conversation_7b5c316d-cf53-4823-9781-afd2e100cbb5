/*********************************************************************
 * @brief  命令管理器.
 *
 * @file   commandmanager.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef COMMAND_MANAGER_H
#define COMMAND_MANAGER_H

#include <vector>

class CommandAbstract;

class CommandManager
{
public:
    explicit CommandManager();
    ~CommandManager();

    /**
     * @fun   Undo
     * @brief 撤销.
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void Undo();
    /**
     * @fun   Redo
     * @brief 重做.
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void Redo();
    /**
     * @brief 将命令加入命令数组
     * @note  如果存在撤销,会清空之后的命令
     * TODO   循环数组,将命令数量限制在一定范围内
     */
    void AddCommand(CommandAbstract* cmd);
    /**
     * @brief 清空命令数组
     */
    void ClearCommand();

private:
    bool isruning;                         ///< 是否正在执行命令
    int m_current_cmd_idx;                 ///< 当前命令索引
    std::vector<CommandAbstract*> m_cmds; ///< 命令数组 TODO 改成循环数组
};

#endif // !COMMAND_MANAGER_H
