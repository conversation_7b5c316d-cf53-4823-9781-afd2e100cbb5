/*********************************************************************
 * @brief  垂直虚拟相机.
 *
 * @file   visualcameratopdown.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include "visualcameraabstract.h"
#include "tools.hpp"
class VisualCameraTopDown : public VisualCameraAbstract
{
public:
    VisualCameraTopDown(QVector3D default_position_);
    ~VisualCameraTopDown();

    void Reset() override;
    void Move(const CameraDirection& d) override;
    void SetScaleMode(const CameraScaleMode& state) override;
    void SetResetMode(const CameraResetMode& state) override;
    /**
     * @brief 设置相机位置 覆盖父类方法
     */
    void SetCameraPosition(double x, double y) override;
    /**
     * @brief 设置视口大小 覆盖父类方法
     */
    void SetViewport(double width, double height) override;
    /**
     * @brief 设置画布大小
     */
    bool SetCanvas(int width, int height);
    /**
     * @brief 获取画布大小
     * @param width
     * @param height
     */
    void GetCanvas(int& width, int& height) const;
    /**
     * @brief 设置是否将画布限制在视野内
     */
    void SetLimitViewByCanvas(bool state);
    /**
     * @brief 获取是否将画布限制在视野内
     */
    bool GetLimitViewByCanvas();
    /**
     * @brief 判断画布尺寸是否有效
     */
    bool IsCanvasValid() const;

protected:
    double CalcScaleFactor(const double& viewport_width, const double& viewport_height, const double& canvas_width, const double& canvas_height);
    void CalcViewTopLeftPosition(double& left, double& top, const double& viewport_width_, const double& viewport_height_);
    void CalcViewBottomRightPosition(double& right, double& bottom, const double& viewport_width_, const double& viewport_height_);
    void CalcViewPosition(double& left, double& top, double& right, double& bottom, double viewport_width_ = 0, double viewport_height_ = 0);

private:
    void LimitViewByCanvasArea(double& x, double& y);
    bool LimitParallelScale(const double& viewport_width, const double& viewport_height);
    void LimitView();

    void PrintInfoDebug() const;
private:
    int canvas_width;  ///< 画布宽度
    int canvas_height; ///< 画布高度
    bool is_limit_view_by_canvas_area; ///< 是否将画布限制在视野内
    ////<是否调整缩放比
    AUTO_PROPERTY(bool, is_adjust_zoom, IsAdjustZoom);
};
