/*********************************************************************
 * @brief  虚拟相机基类.
 *
 * @file   visualcameraabstract.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef VISUALCAMERAABSTRACT_H
#define VISUALCAMERAABSTRACT_H

#include "controlconstants.hpp" // CameraDirection
#include <QMatrix4x4>

class VisualCameraAbstract
{
public:
    VisualCameraAbstract(QVector3D default_position_ = QVector3D(0, 0, 100));
    virtual ~VisualCameraAbstract();
    /**
     * @brief  获取相机视图矩阵
     */
    virtual QMatrix4x4* GetViewMatrix();
    /**
     * @brief  获取相机投影矩阵
     */
    virtual QMatrix4x4* GetProjectionMatrix();
    /**
     * @brief  重置相机位置
     */
    virtual void Reset();
    /**
     * @brief  相机向指定方向移动一个单位距离
     */
    virtual void Move(const CameraDirection& d);
    /**
     * @brief  设置相机缩放状态.
     */
    virtual void SetScaleMode(const CameraScaleMode& state);
    /**
     * @brief  设置相机重置状态.
     */
    virtual void SetResetMode(const CameraResetMode& state);

    /**
     * @brief  缩放相机视野.
     * @param  amount 缩放量
     */
    void Zoom(double amount);
    /**
     * @brief  设置相机缩放到指定比例.
     */
    void SetZoom(float zoom);
    /**
     * @brief  获取相机缩放比例.
     */
    double GetZoom() const;
    /**
     * @brief  获取相机缩放比例(内部倍率).
     * @note   内部倍率和外部倍率的转换方式是 外部 = 0.5 / 内部
     */
    double GetInnerZoom() const;
    /**
     * @brief  设置相机位置.
     * @note
     *   设置相机位置时需注意,标准相机坐标系y轴正方向为上,
     *   而opencv像素坐标系y轴正方向为下,因此需要将y坐标取反
     */
    virtual void SetCameraPosition(QVector3D p);
    virtual void SetCameraPosition(double x, double y, double z);
    virtual void SetCameraPosition(double x, double y);
    /**
     * @brief  获取相机位置.
     */
    void GetCameraPosition(double& x, double& y, double& z) const;
    void GetCameraPosition(double& x, double& y) const;
    QVector4D GetCameraPosition() const;
    /**
     * @brief  设置相机投影模式
     */
    void SetPerspective(bool state);
    /**
     * @brief  获取相机投影模式
     */
    inline bool GetPerspective() const { return perspective; }
    /**
     * @brief  设置相机移动速度
     */
    inline void SetMoveSpeed(float s) { movement_speed = s; }
    /**
     * @brief  设置相机视场角
     */
    void SetViewAngle(double angle);
    /**
     * @brief  获取相机视场角
     */
    inline double GetViewAngle() const { return view_angle; }
    /**
     * @brief  设置相机投影的缩放比例
     */
    void SetParallelScale(double scale);
    /**
     * @brief  获取相机投影的缩放比例
     */
    inline double GetParallelScale() const { return parallel_scale; }
    /**
     * @brief  设置相机投影的缩放比例的最小值
     */
    void SetParallelScaleMax(double max);
    /**
     * @brief  设置相机投影的缩放比例的最小最大值
     */
    void SetParallelScaleMinMax(double min, double max);
    /**
     * @brief 获取长宽比
     */
    inline double GetAspetRatio() const { return viewport_width / viewport_height; }
    /**
     * @brief  设置视口大小
     */
    virtual void SetViewport(double width, double height);
    /**
     * @brief  获取视口大小
     */
    void GetViewport(double& width, double& height) const;

protected:
    /**
     * @brief  更新相机方向向量
     */
    void UpdateVectors();

private:
    void PrintInfoDebug() const;
    bool perspective; ///< 是否为透视投影
    float movement_speed; ///< 相机移动速度
    float near_plane; ///< 近裁剪面
    float far_plane; ///< 远裁剪面
    float yaw; ///< 偏航角 水平旋转角
    float pitch; ///< 俯仰角 垂直旋转角
    double view_angle;     ///< 视场角(angle of view)
    double parallel_scale; ///< 平行投影的缩放比例
    double parallel_scale_min;
    double parallel_scale_max;

    double viewport_width; ///< 视口宽度
    double viewport_height; ///< 视口高度

    QVector3D default_position; ///< 默认相机位置
    QVector3D position;         ///< 相机位置
    QVector3D front_axis;       ///< 前轴
    QVector3D up_axis;          ///< 上轴
    QVector3D right_axis;       ///< 右轴
    QVector3D worldup_axis;     ///< 世界坐标系的up轴

    QMatrix4x4* view_matrix;       ///< 视图矩阵
    QMatrix4x4* projection_matrix; ///< 投影矩阵
};
#endif // !VISUALCAMERAABSTRACT_H
