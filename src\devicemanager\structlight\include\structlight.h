/*****************************************************************//**
 * @file   structlight.h
 * @brief   成像设备封装层
 * @details 主要用于初始化成像相关设备以及封装成像相关的接口
 * <AUTHOR>
 * @date 2024.7.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.31         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/


#ifndef __JRSSTRUCTLIGHT_H__
#define __JRSSTRUCTLIGHT_H__

 //STD
#include <iostream>
#include <mutex>
#include <thread>
#include <functional>
#include <vector>
//Custom
#include "pluginexport.hpp"
#include "image.hpp"
#include "deviceparam.hpp"
//Third


namespace jrsdevice
{

    // using CallBack =  std::function<void(const int& error_code )>;
    // using GrabImgsCallBack = std::function<void(const std::vector<cv::Mat> imgs, const int& error_code )>;
    class JrsStructlightContrlModule;
    struct ImgsBuffer;

    class JRS_AOI_PLUGIN_API StructLight
    {
    public:

        StructLight();
        ~StructLight();

        /**
        * @fun Init
        * @brief 初始化结构光部分
        * <AUTHOR>
        * @date 2024.7.31
        */
        int Init(std::string& init_error_result_);
        /**
         * @fun PushTriggerToQueue
         * @brief
         * @param trigger_type 触发结构光成像类型，有单张图，多张图等模式
         * @param fov_id  当前FOV的ID号
         * @return  成功AOI_OK，否则失败
         * <AUTHOR>
         * @date 2024.7.31
         */
        int  PushTriggerToQueue(const jrsdata::TriggerModeCapture& trigger_type, const int fov_id = 0);

        /**
         * @fun GetOneFovImage
         * @brief 获取单个FOV图片
         * @return 获取单个FOV中所有色彩图片vector
         * <AUTHOR>
         * @date 2024.7.31
         */
        jrsdata::JrsImageBuffer GetSingleBuffer();

        /**
         * @fun SetTriggerDoneCallback
         * @brief 设置采图触发完成回调
         * @param callback_trigger_done_ 采图触发完成
         * <AUTHOR>
         * @date 2024.8.6
         */
        void SetTriggerDoneCallback(jrsdata::Grab2DImgCallBack  callback_trigger_done_);

        /**
         * @fun SetMergeImageDoneCallbacek
         * @brief 设置图片合成完成回调
         * @param merge_done_callback_ 图片合成完成回调
         * <AUTHOR>
         * @date 2024.8.6
         */
        void SetMergeImageDoneCallbacek(jrsdata::CaptureCallBack  merge_done_callback_);

        /**
         * @fun GetStructLightParam
         * @brief 获取结构光的配置参数，主要有：相机分辨率，Z轴清晰拍照高度，相机视野大小
         * @return  返回获取的参数
         * <AUTHOR>
         * @date 2024.9.3
         */
        jrsdata::StructLightParam GetStructLightParam();


        /**
         * @fun SetIOTriggerFuc
         * @brief 设置IO触发函数
         * @param io_out_put_trigger_fuc  IO触发函数
         * <AUTHOR>
         * @date 2024.12.27
         */
        void SetIOTriggerFuc(std::function<void(const int& io_id, int& erro_code)> io_trigger_fuc);

        /**
         * @fun SetLightValByBoardMaterial
         * @brief 通过板子材质设置灯光和光机亮度值
         * @param board_material  板子材质
         * <AUTHOR>
         * @date 2025.6.9
         */
        int SetLightValByBoardMaterial(const jrsdata::BOARD_MATERIAL& board_material);

    private:

        /**
         * @fun InitDevices
         * @brief 初始化设备
         * @param config_path 设备配置文件
         * @return AOI_OK成功，否则失败
         * <AUTHOR>
         * @date 2024.7.31
         */
        int InitDevices(const std::string& config_path, jrsdata::InitDeivceResults& device_result_);

        /**
         * @fun GetNewBuffer
         * @brief 成像模块中成像结果回调
         * @param imgs 成像的结果图片
         * @param error_code_ 成像是否成功的结果码
         * <AUTHOR>
         * @date 2024.8.5
         */
        void NewBuffer(const ImgsBuffer& imgs, const int& error_code_);

        /**
         * @fun CaptureTriggerDone
         * @brief 采图触发完成
         * @param error_code 返回的错误码，触发成功则返回AOI_OK，否则失败
         * <AUTHOR>
         * @date 2024.8.6
         */
        void CaptureTriggerDone(const int& error_code);


        /**
         * @brief 将相机返回的图像转换为单个FOV的图像
         *
         * @param imgs 结构光成像模块的图像缓存
         * @param one_fov_imgs 单FOV图像
         * @return int 错误码
         */
         // int SLImagesBuffer2OneFovImgs(const ImgsBuffer& imgs, jrsdata::OneFovImgs& one_fov_imgs);
         // wangzhengkai 2025.3.6 将ImageBuffer拷贝功能放到结构光模块中
         

        /**
         * @brief 根据触发类型将图像数组转为单个FOV的图像.
         * 
         * @param imgs 图像数组
         * @param triger_type 触发类型
         * @param one_fov_imgs 单个FOV的图像
         * @return int 错误码
         * <AUTHOR>
         * @date 2025.3.6
         */
        int VectorImage2OneFovImgsByTriggerType(const std::vector<cv::Mat>& imgs, const jrsdata::TriggerModeCapture triger_type, jrsdata::OneFovImgs& one_fov_imgs);


        int GrabImgsThreadFunc(); /**< 图像获取线程函数 */

        std::unique_ptr<JrsStructlightContrlModule> struct_light_control; /**< 结构光成像实例 */
        std::mutex deque_mutex;
        jrsdata::Grab2DImgCallBack callback_trigger_done;  /**< 触发完成回调 */
        jrsdata::CaptureCallBack   merge_done_callback;    /**< 图片合成完成回调 */
        jrsdata::StructLightParam struct_light_param; /**< 结构光参数*/
        std::queue<jrsdata::OneFovImgs> grab_imgs_queue; /**< 图像队列 */
        std::thread* grab_imgs_thread = nullptr; /**< 图像获取线程 */
        std::condition_variable con_grab_imgs; /**< 图像获取线程条件变量 */
        bool kill_grab_imgs_thread = false; /**< 图像获取线程退出标志 */
    };
    using StructLightPtr = std::shared_ptr<StructLight>;

}

#endif // !__JRSSTRUCTLIGHT_H__
