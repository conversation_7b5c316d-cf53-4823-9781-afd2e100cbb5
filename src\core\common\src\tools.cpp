﻿//STD
#include <filesystem>
#include <chrono>
#include <functional>
#include <sstream>
#include <fstream>
#include <windows.h>
//Custom
#include "tools.h"

#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "opencv2/opencv.hpp"
#pragma warning(pop)

namespace jrscore
{
    void AOITools::EnsureDirectoryExists(const std::string& directory)
    {
        if (!std::filesystem::exists(directory))
        {
            std::filesystem::create_directories(directory);
        }
    }

    std::string AOITools::GetCurrentPath()
    {
        std::filesystem::path current_path = std::filesystem::current_path();
        return current_path.string() + "\\";
    }
    std::vector<std::string> AOITools::GetIPv4Addresses()
    {
        std::vector<std::string> ip_addresses;
        std::string ipconfig_content;

        FILE* fp = _popen("ipconfig", "r");
        if (NULL != fp)
        {
            char line[4096];
            while (NULL != fgets(line, sizeof(line), fp))
            {
                ipconfig_content += line;
            }
            _pclose(fp);
            size_t pos = 0;
            while ((pos = ipconfig_content.find("IPv4", pos)) != std::string::npos)
            {
                size_t ip_start = ipconfig_content.find(":", pos);
                if (ip_start != std::string::npos)
                {
                    size_t ip_end = ipconfig_content.find("\n", ip_start);
                    if (ip_end != std::string::npos)
                    {
                        std::string ip = ipconfig_content.substr(ip_start + 1, ip_end - ip_start - 1);
                        TrimString(ip);
                        ip_addresses.push_back(ip);
                    }
                }
                pos = ip_start;
            }
        }

        return ip_addresses;
    }

    std::string AOITools::GetLocalIP()
    {
        std::string ip("127.0.0.1");
        std::string ipconfig_content;

        FILE* fp = _popen("ipconfig", "r");
        if (NULL != fp)
        {
            char line[4096];
            while (NULL != fgets(line, sizeof(line), fp))
            {
                ipconfig_content += line;
            }

            auto p = ipconfig_content.rfind("IPv4");
            if (p != std::string::npos)
            {
                auto p2 = ipconfig_content.find(":", p);
                if (p2 != std::string::npos)
                {
                    auto p3 = ipconfig_content.find("\n", p2);
                    if (p3 != std::string::npos)
                    {
                        ip = ipconfig_content.substr(p2 + 1, p3 - p2 - 1);
                        TrimString(ip);
                    }
                }
            }
            _pclose(fp);
        }

        return ip;
    }

    void AOITools::TrimString(std::string& str)
    {
        if (!str.empty())
        {
            str.erase(0, str.find_first_not_of(" "));
            str.erase(str.find_last_not_of(" ") + 1);
        }
    }

    std::vector<std::string> AOITools::SplitString(const std::string& s, char delimiter)
    {
        std::vector<std::string> tokens;
        std::string token;
        std::istringstream tokenStream(s);
        while (std::getline(tokenStream, token, delimiter))
        {
            tokens.push_back(token);
        }
        return tokens;
    }

    std::string AOITools::JoinString(const std::vector<std::string> str_vec, char connector)
    {
        if (str_vec.empty())
        {
            return "";
        }
        std::string res_str = str_vec[0];
        for (size_t i = 1; i < str_vec.size(); ++i)
        {
            res_str += connector;
            res_str += str_vec[i];
        }
        return res_str;
    }

    bool AOITools::ReplaceString(std::string& res_str, const std::string target_str, const std::string& sub_str, int size)
    {
        try
        {
            size_t pos = res_str.find(target_str);

            if (pos != std::string::npos) {
                res_str.replace(pos - 1, size, sub_str);
            }
            else
            {
                return false;
            }
        }
        catch (const std::exception& e)
        {
            std::cerr << "ReplaceString" << " error:" << e.what() << std::endl;
            return false;
        }
        return true;
    }

    std::string AOITools::GetCurrentDataTime(const std::string& format/* = "%Y-%m-%d %H:%M:%S"*/, bool is_microsecond_ /*= false*/)
    {
        auto now = std::chrono::system_clock::now();
        std::time_t now_time_t = std::chrono::system_clock::to_time_t(now);
        std::tm now_tm;
        // *std::localtime(&now_time_t);
        if (localtime_s(&now_tm, &now_time_t) != 0)
        {
            std::cerr << "Failed to get local time" << std::endl;
        }
        // 提取微秒部分
        auto duration = now.time_since_epoch();
        auto micros = std::chrono::duration_cast<std::chrono::microseconds>(duration).count() % 1000000;

        std::stringstream ss;
        ss << std::put_time(&now_tm, format.c_str());  // 格式化时间
        if (is_microsecond_)
        {
            ss << "_" << std::setw(6) << std::setfill('0') << micros;  // 加上微秒部分
        }
        return ss.str();
    }

    std::string AOITools::CropString(const std::string& src_str, const std::string& crop_str)
    {
        if (src_str.empty() || crop_str.empty())
        {
            return "";
        }
        auto it = src_str.rfind(crop_str);
        return src_str.substr(0, it);
    }

    std::string AOITools::FormatInt(int number, int width, char fill)
    {
        std::stringstream ss;
        std::string result;
        if (width > 0) {
            if (number < 0) {
                result = std::to_string(number);
                result.erase(0, 1);
                ss << std::setw(width - 1) << std::setfill(fill) << result;
                return "-" + ss.str();
            }
            else {
                ss << std::setw(width) << std::setfill(fill) << number;
                return ss.str();
            }
        }
        else {
            ss << number;
            return ss.str();
        }
    }

    bool AOITools::WriteFile(const std::string& path, const std::string& content)
    {
        std::filesystem::path filePath(path);
        if (!std::filesystem::exists(filePath.parent_path())) {
            std::filesystem::create_directories(filePath.parent_path());
        }
        std::ofstream outFile(filePath);
        if (outFile) {
            outFile << content;
            outFile.close();
            std::cout << "File created and content written successfully." << std::endl;
            return true;
        }
        else {
            std::cerr << "Failed to create or open the file." << std::endl;
            return false;
        }
    }

    std::string AOITools::GetPrefixString(const std::string& ori_str_, const char& mark_char_)
    {
        std::string prefix_string = ori_str_;
        size_t pos = ori_str_.find_last_of(mark_char_);
        if (pos != std::string::npos)
        {
            prefix_string = ori_str_.substr(0, pos);
        }
        return prefix_string;
    }
    int AOITools::GetSuffixNumber(const std::string& ori_str_, const char& mark_char_)
    {
        size_t pos = ori_str_.find_last_of(mark_char_);
        if (pos != std::string::npos && pos + 1 < ori_str_.size())
        {
            std::string suffix_str = ori_str_.substr(pos + 1);
            try
            {
                return std::stoi(suffix_str);
            }
            catch (const std::exception& e)
            {
                (void)e;
                // 转换失败，返回一个默认值（比如 -1）或抛出异常
                return -1;
            }
        }
        return -1; // 如果没有找到分隔符或没有后缀
    }
    std::string AOITools::DoubleToString(double value, int precision)
    {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(precision) << value;
        return oss.str();
    }

    std::string AOITools::FloatToString(float value, int precision)
    {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(precision) << value;
        return oss.str();
    }

    std::string AOITools::FormatDuration(std::chrono::seconds duration)
    {
        long long total_seconds = duration.count();

        long long days = total_seconds / (24 * 60 * 60);
        total_seconds %= (24 * 60 * 60);

        long long hours = total_seconds / (60 * 60);
        total_seconds %= (60 * 60);

        long long minutes = total_seconds / 60;
        long long seconds = total_seconds % 60;

        return std::to_string(days) + "天 " +
            std::to_string(hours) + "时:" +
            std::to_string(minutes) + "分:" +
            std::to_string(seconds) + "秒";
    }
    std::string AOITools::CalculateTimeDifference(const std::string& start_time_, const std::string& end_time_, const std::string& format_ /*= "%Y-%m-%d %H:%M:%S"*/)
    {
        // 检查输入时间字符串是否有效
        if (start_time_.empty() || end_time_.empty()) {
            return "";  // 如果时间字符串为空，返回空字符串
        }

        // 定义解析时间的格式
        std::tm start_tm = {};
        std::tm end_tm = {};

        // 使用 std::get_time 解析时间字符串
        std::istringstream start_ss(start_time_);
        std::istringstream end_ss(end_time_);

        // 解析字符串到 std::tm 结构体
        start_ss >> std::get_time(&start_tm, format_.c_str());
        end_ss >> std::get_time(&end_tm, format_.c_str());

        // 如果解析失败，返回空字符串
        if (start_ss.fail() || end_ss.fail()) {
            return "";
        }

        // 将 std::tm 转换为 time_point
        auto start_time_point = std::chrono::system_clock::from_time_t(std::mktime(&start_tm));
        auto end_time_point = std::chrono::system_clock::from_time_t(std::mktime(&end_tm));

        // 检查 std::mktime 是否成功
        if (start_time_point == std::chrono::system_clock::from_time_t(-1) || end_time_point == std::chrono::system_clock::from_time_t(-1)) {
            return "";  // 如果转换失败，返回空字符串
        }

        // 计算时间差（秒）
        auto duration = end_time_point - start_time_point;
        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();

        // 如果时间差为负，表示结束时间早于开始时间，返回空字符串
        if (seconds < 0) {
            return "";
        }

        // 格式化为字符串
        std::ostringstream result;
        result << seconds;

        return result.str();
    }
    void AOITools::NormalizeAngle(double& angle)
    {
        // 确保角度在 0 到 360 之间
        if (angle < 0)
        {
            angle = fmod(angle, 360.0); // 取模运算
            if (angle < 0)
            {
                angle += 360.0; // 如果结果为负数，加上 360
            }
        }
        else if (angle >= 360)
        {
            angle = fmod(angle, 360.0); // 如果角度大于等于 360，取模运算
        }
    }

    cv::Mat AOITools::GetThumbnailMat(const cv::Mat& src_img_, int max_width /*= 500*/, int max_height /*= 500*/)
    {
        try {
            // 获取原图的宽度和高度
            int original_width = src_img_.cols;
            int original_height = src_img_.rows;

            // 计算宽高比
            float aspect_ratio = static_cast<float>(original_width) / original_height;

            int new_width = original_width;
            int new_height = original_height;

            // 根据最大宽度和最大高度调整图像大小
            if (original_width > max_width) {
                new_width = max_width;
                new_height = static_cast<int>(new_width / aspect_ratio);
            }
            if (new_height > max_height) {
                new_height = max_height;
                new_width = static_cast<int>(new_height * aspect_ratio);
            }

            // 缩放图片
            cv::Mat thumbnail;
            cv::resize(src_img_, thumbnail, cv::Size(new_width, new_height));

            return thumbnail;
        }
        catch (const std::exception& ex) {
            std::cerr << "[ERROR] 异常发生: " << ex.what() << std::endl;
            return cv::Mat();
        }
    }

    cv::RotatedRect AOITools::GetMinimumExternalRoatedRect(const std::vector<cv::RotatedRect>& rects_)
    {
        std::vector<cv::Point2f> all_points;

        // 收集所有 rect 的四个顶点
        for (const auto& rect : rects_)
        {
            cv::Point2f vertices[4];
            rect.points(vertices); // 获取矩形的四个角点
            all_points.insert(all_points.end(), vertices, vertices + 4);
        }

        // 如果没有点，返回空的 RotatedRect
        if (all_points.empty())
            return cv::RotatedRect();

        // 使用 minAreaRect 获取最小外接旋转矩形
        cv::RotatedRect min_rect = cv::minAreaRect(all_points);

        return min_rect;
    }

    bool AOITools::ContainsIllegalCharacters(const std::string& name_, std::string& illegal_chars_out_)
    {

        const std::string illegal_chars = R"(<>:"/\|?*)";
        illegal_chars_out_.clear();

        for (char ch : name_)
        {
            if (illegal_chars.find(ch) != std::string::npos && illegal_chars_out_.find(ch) == std::string::npos)
            {
                illegal_chars_out_ += ch;
            }
        }

        return !illegal_chars_out_.empty();

    }

}