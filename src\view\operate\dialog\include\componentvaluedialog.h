﻿/*********************************************************************
 * @brief  多联板克隆.
 *
 * @file   componentvaluedialog.h
 *
 * @date   2024.10.10
 * <AUTHOR>
 *********************************************************************/
#ifndef COMPONENTVALUEDIALOG_H
#define COMPONENTVALUEDIALOG_H

#include <QApplication>
#include <QDialog>
#include <QVBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QDialogButtonBox>
#include <QString>
#include <QMessageBox>
class QRadioButton;
/**
 * @brief 自定义对话框类，用于输入元件名和元件料号
 */
class ComponentDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ComponentDialog(QWidget* parent = nullptr);


    /**
     * @brief 获取用户输入的元件名
     * @return 元件名字符串
     */
    QString GetComponentName() const;


    /**
     * @brief 获取用户输入的元件料号
     * @return 元件料号字符串
     */
    QString GetComponentPart() const;

    jrsdata::ComponentUnit::Shape GetComponentShape() const;

private slots:
    void OnAccept();
private:
    QLineEdit* lineEditName = nullptr;   ///< 输入元件名的文本框
    QLineEdit* lineEditPart = nullptr;   ///< 输入元件料号的文本框
    QRadioButton* radio_rect = nullptr;  ///< 矩形
    QRadioButton* radio_circle = nullptr;///< 圆形
    QRadioButton* radio_polygon = nullptr;///< 不规则图形

};

#endif // COMPONENTVALUEDIALOG_H