﻿/*********************************************************************
 * @brief  图形识别id,为了避免以后替换生成方式,做了封装.
 *
 * @file   graphicsid.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef GRAPHICS_ID_H
#define GRAPHICS_ID_H

#include "graphicsapi.hpp"
#include <string>
#include <vector>

class GRAPHICS_API GraphicsID
{
public:
    /**
     * @brief 图形id数据
     * @note  隐藏成员变量
     */
    struct GraphicsIDTempData;

    GraphicsID(const std::string& id_);
    GraphicsID(const int& id_);
    GraphicsID();
    GraphicsID(const GraphicsID& other);
    GraphicsID(GraphicsID&& other) noexcept;
    GraphicsID& operator=(const GraphicsID& other);
    GraphicsID& operator=(GraphicsID&& other) noexcept;
    ~GraphicsID();
    /**
     * @brief  创建id
     */
    void Create();
    /**
     * @brief  设置id
     */
    void SetString(const std::string& id_);
    /**
     * @brief  设置id
     */
    void SetInt(const int& id_);
    /**
     * @brief  获取id
     */
    std::string GetString() const;
    /**
     * @brief  获取id的hash值
     */
    size_t Hash() const;

    bool IsEmpty() const;

    bool operator==(const GraphicsID& other) const;

    friend class GraphicsIDHash;
    friend class GraphicsIDEqual;

private:
    GraphicsIDTempData* data;
};

/**
 * @brief id集合
 */
class GraphicsIDSet : public std::vector<GraphicsID>
{
public:
    GraphicsIDSet();
    ~GraphicsIDSet() = default;

    void Insert(const GraphicsID& id);
    void Insert(const GraphicsIDSet& ids);
    void Insert(const std::vector<GraphicsID>& ids);
    void Erase(const GraphicsID& id);
    bool Contains(const GraphicsID& id);
    void Clear();
    size_t Size() const;

private:
    GraphicsIDSet(const GraphicsIDSet& other) = delete;
    GraphicsIDSet& operator=(const GraphicsIDSet& other) = delete;
    GraphicsIDSet(GraphicsIDSet&& other) = delete;
    GraphicsIDSet& operator=(GraphicsIDSet&& other) = delete;
};

class GraphicsIDHash
{
public:
    size_t operator()(const GraphicsID& p) const
    {
        return p.Hash();
    }
};

class GraphicsIDEqual
{
public:
    bool operator()(const GraphicsID& c1, const GraphicsID& c2) const
    {
        return c1 == c2;
    }
};

#endif // !GRAPHICS_ID_H
