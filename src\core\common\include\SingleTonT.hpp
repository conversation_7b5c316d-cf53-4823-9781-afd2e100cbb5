/*****************************************************************//**
 * @file   SingleTonT.hpp
 * @brief  创建单例模式模板类
 * @details    
 * <AUTHOR>
 * @date 2024.1.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.18         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __SINGLETONT_HPP__
#define __SINGLETONT_HPP__

namespace jrscore
{
    template <typename T>
    class SingleTonT
    {
    public:

        static T& GetInstance ()
        {
            static T s_instance;
            return s_instance;
        }

    protected:
        SingleTonT ()
        {
        }
        virtual ~SingleTonT ()
        {
        }

    private:
        SingleTonT (const SingleTonT&) = delete;
        const SingleTonT& operator = (const SingleTonT&) = delete;
    };

}
#endif // !__SINGLETONT_HPP__
