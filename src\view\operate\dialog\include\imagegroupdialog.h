#ifndef IMAGEGROUPDIALOG_H
#define IMAGEGROUPDIALOG_H

#include <QDialog>

namespace Ui {
class ImageGroupDialog;
}
namespace jrsaoi {
	struct ImplData;
	class ImageGroupDialog : public QDialog
	{
		Q_OBJECT

	public:
		explicit ImageGroupDialog(QWidget* parent = nullptr);
		~ImageGroupDialog();
	protected:
	private slots:
		void SloSaveImages();
	private:
		void InitMember();
		void InitView();
		void InitConnect();
		ImplData* impl_data;
	
	};
}
#endif // IMAGEGROUPDIALOG_H
