﻿#include "padgraphics.h"
#include "graphicsprocess.h"
#include "graphicsalgorithm.h"
#include "graphicsserialize.hpp"
#include "graphicseventparam.hpp"

#include "controlpointabstract.h"
#include "controlpointconstants.hpp"
#include "controlpointfactory.h"

#include "eventparam.hpp"
#include "customcursortype.hpp"
#include "painter.h"
#include "renderer.h"
#include "log.h"

#include <QPainter>

void PadGraphics::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void PadGraphics::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    obj->Update();


    Color c;
    float think_ness = 0.f;
    this->SetColorAndThickness(config, c, think_ness);
    // cv::RotatedRect boundingbox = obj->GetBoundingbox();
    // /*四顶点*/
    // cv::Point2f ovp[4];
    // boundingbox.points(ovp);

    // if (obj->paths.empty() || obj->IsNeedUpdate())
    // {
    //     std::vector<Vec2> tvPaths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };
    //     tvPaths.swap(obj->paths);
    //     obj->SetUpdated();
    // }
    p->DrawLines(obj->paths, c, true, think_ness);

    //printInfo(std::stringstream()
    //    << " graphics_id:" << obj->GetId().GetString()
    //    << " pad_group:" << obj->GetPadGroupPtr().lock().get()
    //    << " pad_id:" << obj->GetPadID() << "\n"
    //    //<< " parent position:[" << pd_gh->GetParent()->x() << "," << pd_gh->GetParent()->y() << "]"
    //    << " pad_center_position:[" << obj->x() << "," << obj->y() << "]"
    //    << " pad_location_center_position:[" << obj->LocalX() << "," << obj->LocalX() << "]"
    //    << "path:[" << obj->paths[0].x << "," << obj->paths[0].y << "]"
    //);
    if (obj->settings.GetIsFlag())
    {
        if (!obj->path_flags.empty())
        {
            auto flag_length = 20;
            auto& point = obj->path_flags[0];
            std::vector<Vec2> points{ point + Vec2(-flag_length,-flag_length), point + Vec2(flag_length,-flag_length),
             point + Vec2(flag_length,flag_length), point + Vec2(-flag_length,flag_length) };
            p->DrawLines(points, c, true, -1);
        }
        // std::vector<Vec2> points{ {(ovp[0].x + ovp[1].x) * 0.5f, (ovp[0].y + ovp[1].y) * 0.5f} };
        // p->DrawPoints(obj->path_flags, c, thinkness);
    }

    // 信息文本
    DrawInfo(r, obj);

    if (obj->settings.GetIsSelected())
    {
        DrawControlPoint(r, p, config);
    }
}

void PadGraphics::Update()
{
    if (IsNeedUpdate())
    {
        UpdateByTemp(this);
        UpdateControlPoint();
        UpdateDrawBuffer();
        SetUpdated();
    }
}

void PadGraphics::UpdateDrawBuffer()
{
    cv::RotatedRect boundingbox = this->GetBoundingbox();
    /*四顶点*/
    cv::Point2f vertex[4];
    boundingbox.points(vertex);
    std::vector<Vec2> tpaths
    {
         {vertex[0].x, vertex[0].y}, {vertex[1].x, vertex[1].y},{vertex[2].x, vertex[2].y}, {vertex[3].x, vertex[3].y}
    };
    tpaths.swap(this->paths);
    std::vector<Vec2> tpath_flags{ {(vertex[0].x + vertex[1].x) * 0.5f, (vertex[0].y + vertex[1].y) * 0.5f} };
    tpath_flags.swap(this->path_flags);
}

void PadGraphics::UpdateControlPoint()
{
    auto const obj = GetTemp(this, false);

    std::vector<std::shared_ptr<ControlPointAbstract>> cps;

    //std::vector<ControlPoint> cps;
    /*创建尺寸控制点*/
    if (obj->w() > 10 || obj->h() > 10)
    {
        auto tcps = ControlPointFactory::CreateControlPointGroup(ControlPointGroupType::SIZE_POINT, obj);
        //auto tcps = CreateSizeControlPoint(obj, static_cast<int>(CustomCursorType::HandClose));
        cps.insert(cps.end(), tcps.begin(), tcps.end());
    }
    ///*创建旋转控制点*/
    //if (0)
    //{
    //    auto tcps = CreateRotateControlPoint(obj, Qt::DragMoveCursor);
    //    cps.insert(cps.end(), tcps.begin(), tcps.end());
    //}

    /*创建中心拖拽控制点*/
    {
        auto cp = ControlPointFactory::CreateControlPoint(ControlPointType::MOVE_POINT, obj);
        //auto cp = CreateMoveControlPoint(obj, static_cast<int>(CustomCursorType::Move));
        cps.emplace_back(cp);
    }
    cps.swap(control_points);
}

void PadGraphics::DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config)
{
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        cp->Draw(r, p, config);
    }
}

int PadGraphics::TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam& param)
{
    if (!settings.GetIsSelected())
        return 2;

    double mindis = param.max_limit;
    std::shared_ptr<ControlPointAbstract> response_cp = nullptr;
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        auto dis = cp->TryResponse(param.x, param.y, (float)param.max_limit);
        if (dis > 0 && dis < mindis)
        {
            // printInfo(std::stringstream() << "响应距离:" << dis);
            mindis = dis;
            response_cp = cp;
        }
    }

    if (!response_cp)
        return 1;
    controlpoint = response_cp;
    return 0;
}

int PadGraphics::ResponseControlPoint(const ResponseEventParam& param)
{
    auto obj = GetAndUpdateTemp(this, param.istemp);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;
    obj->SetRequireUpdate();
    for (auto& cp : obj->control_points)
    {
        if (!cp) continue;
        if (cp->attributes == param.attr)
        {
            cp->Response(param, obj);
            return 0;
        }
    }
    return 1;
}

int PadGraphics::ResponseEvent(const MouseEventValue& value)
{
    //if (_pad_group.expired())
    //{
    //    return 1;
    //}
    //return _pad_group.lock()->ResponseEvent(value);  //禁用快捷方式
    (void)value;
    return 0;
}

std::shared_ptr<GraphicsAbstract> PadGraphics::Clone() const
{
    return std::make_shared<PadGraphics>(*this);
}

std::string PadGraphics::GetSerializedData()
{
    return createConfig(this);
}


int PadGraphics::GetDirection()
{
    if (_pad_group.expired())
    {
        return 0;
    }
    return static_cast<int>(_pad_group.lock()->GetDirection());
}
int PadGraphics::GetGroupType()
{
    if (_pad_group.expired())
    {
        return 0;
    }
    return static_cast<int>(_pad_group.lock()->GetGroupType());
}
void PadGraphics::DrawInfo(Renderer* r, PadGraphics* obj)
{
    cv::Rect2f tr = obj->GetBoundingbox().boundingRect2f();
    auto pd = r->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        QPainter painter(device);
        painter.beginNativePainting();

        QString text = QString("%1").arg(obj->GetPadID());
        QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);

        float text_rb_x = 0;
        float text_rb_y = 0;
        r->MouseToWorld(text_rb_x, text_rb_y);
        float text_tl_x = textsize.width();
        float text_tl_y = textsize.height();
        r->MouseToWorld(text_tl_x, text_tl_y);
        float text_o_x = abs(text_tl_x - text_rb_x);
        float text_o_y = abs(text_tl_y - text_rb_y);

        float tlx = obj->x() - abs(text_o_x * 0.5);
        float tly = obj->y() - abs(text_o_y * 0.5);
        r->WorldToMouse(tlx, tly);

        painter.drawText(QRectF(tlx, tly, textsize.width(), textsize.height()),
            Qt::AlignCenter | Qt::TextWordWrap, text);
        painter.endNativePainting();
    }
}

PadGraphics::PadGraphics(float x, float y, float width, float height, float angle, int pad_id_, std::weak_ptr<PadGraphicsGroup> parent_)
    : GraphicsAbstract(x, y, width, height, angle), _pad_id(pad_id_), _pad_group(parent_)
{
}

PadGraphics::PadGraphics(int pad_id_, std::weak_ptr<PadGraphicsGroup> parent_)
    : GraphicsAbstract(), _pad_id(pad_id_), _pad_group(parent_)
{
}

PadGraphics& PadGraphics::operator=(const PadGraphics& other)
{
    if (this != &other)
    {
        GraphicsAbstract::operator=(other);
        //_direction = other.GetDirection();
        _pad_group = other.GetPadGroupPtr();
        _pad_id = other.GetPadID();
    }
    return *this;
}

GraphicsFlag PadGraphics::GetFlag() const
{
    return GraphicsFlag::pad;
}
