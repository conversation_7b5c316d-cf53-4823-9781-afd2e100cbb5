@startuml 图形更新流程

actor 渲染器 as r
actor 渲染界面 as v 
actor 渲染控制器 as c
actor 渲染模型 as m
actor 事件中心 as ec

== 实际图形更新 == 
r -> v : TriggerGraphicsUpdate
v -> v : emit SignalGraphicsUpdate
v -> c : SlotGraphicsUpdate
c -> m : GraphicsUpdateProject
c -> m : ProjectUpdateGraphics
m -> c : TriggerCallBackProjectUpdateGraphics
c -> c : emit SignalRender2dUpdate
c -> ec : SlotOperator


== 默认图形更新 ==
r -> v : 图形更新
v -> c : 通知控制器图形更新
c -> c : 记录更新信息
c -> m : 通知模型更新数据库
m -> m : 更新数据库
m -> c : 通知控制器模型更新完成
c -> c : 将数据库信息转换成图形
c -> v : 通知界面更新图形
v -> r : 通知渲染器更新

@enduml