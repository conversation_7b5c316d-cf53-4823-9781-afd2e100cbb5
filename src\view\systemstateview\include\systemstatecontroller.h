﻿/*****************************************************************//**
 * @file   logshowcontroller.h
 * @brief  日志显示控制器
 * @details
 * <AUTHOR>
 * @date 2024.8.13
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.13          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __INITSYSCONTROLLER_H__
#define __INITSYSCONTROLLER_H__
 //STD
 // 
//Custom
#include "controllerbase.h"

namespace jrsaoi
{
    class SystemStateView;
    class SystemStateModel;
    class SystemStateController :public ControllerBase
    {
        Q_OBJECT
    public:
        SystemStateController(const std::string& name);
        ~SystemStateController();
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        virtual void SetView(ViewBase* view_param)override;
        virtual void SetModel(ModelBasePtr model_param)override;
    signals:
        void SigUpdateSystemState(const jrsdata::ViewParamBasePtr& param);
    private slots:
        void SlotInitDevice();
        void SlotCheckedChanged(bool flag_);
    private:
        int UpdateView();
        void InitConnect();
        SystemStateView* _view;
        std::shared_ptr<SystemStateModel> _model;

    };
    using SystemStateControllerPtr = std::shared_ptr<SystemStateController>;

}
#endif // !__LOGSHOWCONTROLLER_H__
