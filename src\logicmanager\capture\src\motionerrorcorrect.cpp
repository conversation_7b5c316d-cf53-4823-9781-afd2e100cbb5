#pragma warning(push, 1)
#pragma warning(disable : 4127)
#include <unsupported/Eigen/NonLinearOptimization>
#include "motionerrorcorrect.h"
#include "motionerrorfunc.h"
#pragma warning(pop)

using namespace Eigen;

MotionErrorCorrect::MotionErrorCorrect()
{
}

MotionErrorCorrect::MotionErrorCorrect(const MotionErrorParam &_param)
{
  param = _param;
  isInit = true;
}

MotionErrorCorrect::~MotionErrorCorrect()
{
}

JrsPoint MotionErrorCorrect::Run(const JrsPoint& inputPt) const
{
  double xt = (inputPt.x - param.x_start) / (param.x_end - param.x_start);
  double yt = (inputPt.y - param.y_start) / (param.y_end - param.y_start);

  JrsPoint outputPt = inputPt;
  if (xt >=0 && xt<=1 && yt >= 0 && yt <= 1)
  {
    VectorXd xb(param.x_ctrl_pts.size() * 2);
    for (size_t i = 0; i < param.x_ctrl_pts.size(); i++)
    {
      xb[i * 2] = param.x_ctrl_pts[i].x;
      xb[i * 2 + 1] = param.x_ctrl_pts[i].y;
    }

    VectorXd yb(param.y_ctrl_pts.size() * 2);
    for (size_t i = 0; i < param.y_ctrl_pts.size(); i++)
    {
      yb[i * 2] = param.y_ctrl_pts[i].x;
      yb[i * 2 + 1] = param.y_ctrl_pts[i].y;
    }

    outputPt = BezierCurve_functor::CalculateBezierPoint(xt, xb) +
      BezierCurve_functor::CalculateBezierPoint(yt, yb);
  }

  return outputPt;
}

void MotionErrorCorrect::Run(const double& input_x, const double& input_y, double& output_x, double& output_y) const
{
  if (isInit)
  {
    JrsPoint input(input_x, input_y);
    auto output = Run(input);
    output_x = output.x;
    output_y = output.y;
  }
  else
  {
    output_x = input_x;
    output_y = input_y;
  }
}

void MotionErrorCorrect::UpdateParam(const MotionErrorParam &_param)
{
  param = _param;
  isInit = true;
}

bool MotionErrorCorrect::IsValid() const
{
    return isInit;
}
