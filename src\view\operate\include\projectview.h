﻿/*****************************************************************//**
 * @file   projectview.h
 * @brief  工程界面
 *
 * <AUTHOR>
 * @date   2024.9.24
 *********************************************************************/
#ifndef PROJECTVIEW_H
#define PROJECTVIEW_H
 //QT
#include <QWidget>
// 无效文件移动到cpp jerx
#include "viewparam.hpp"
#include <string>

namespace Ui
{
    class ProjectView;
}

namespace jrsaoi
{
    struct ProjectImplData;
    class ProjectView : public QWidget
    {
        Q_OBJECT
    public:
        explicit ProjectView(QWidget* parent = nullptr);
        ~ProjectView();


        void UpdateView(const jrsdata::OperateViewParamPtr& param_);

    public slots:
        /**
         * @func  SlotUpdate
         * @brief 外部更新,统一用一个接口
         * <AUTHOR>
         */
        void SlotUpdate(const jrsdata::ViewParamBasePtr& param_);


        void SlotUpdateNewFileProductWidth(double track_width1, double track_width2);
    signals:
        /**
         * @brief 通知外部,统一用一个接口
         * <AUTHOR>
         */
        void SignalViewEvent(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @brief 更新内部的newprojectview
         * <AUTHOR>
         */
        void SignalUpdateNewProjectView(const jrsdata::ViewParamBasePtr&);
        /**
         * @fun SigMotionDebugTrigger
         * @brief
         * @param operateparam
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigMotionDebugTrigger(jrsdata::OperateViewParamPtr operateparam);
        /**
         * @fun SigGetPosition
         * @brief 获取轴位置
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigGetPosition();
        /**
         * @fun SigSaveProject
         * @brief 保存工程文件
         * <AUTHOR>
         * @date 2024.10.31
         */
        void SigSaveProject();

        /**
         * @fun SigOpenEntiretyImages
         * @brief 打开全部图片
         * @param group_name_
         * <AUTHOR>
         * @date 2024.11.20
         */
        void SigOpenEntiretyImages(const std::string& group_name_);
        void SigSaveEntiretyImages(const std::string& group_name_);
        void SigReadEntiretyImages(const std::string& group_name_);

        void SigGetProductWidth();

    private:
        /**
         * @fun Init
         * @brief 初始化
         * @date 2024.9.24
         * <AUTHOR>
         */
        void Init();
        /**
         * @fun InitMember
         * @brief 初始化变量
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitMember();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数链接
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
        /**
         * @fun InitView
         * @brief 初始化界面
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();

    private:
        Ui::ProjectView* ui;
        ProjectImplData* project_data;

        // 移动到project_data统一管理
    };
}
#endif // ProjectView_H
