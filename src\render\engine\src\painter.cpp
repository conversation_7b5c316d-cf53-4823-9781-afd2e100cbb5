#include "painter.h"
#include "renderer.h"
// #include "program.h" // ProgramType
#include "engineconstants.hpp" // ProgramType RenderType
#include "renderconstants.hpp"
#include "algodefine.hpp"

#include <qpainter.h>

uint32 Painter::texture_indices[6] = { 0, 2, 1, 0, 3, 2 };

Painter::<PERSON>(Renderer* r) : use_anti_aliasing(false), renderer(r) {}

Painter::~<PERSON>() {}

void Painter::DrawTexture(Texture2D* texture)
{
    const auto& w = (float)texture->width;
    const auto& h = (float)texture->height;

    const auto& x = texture->x;
    const auto& y = texture->y;
    const auto& z = texture->z;
    const auto& angle = texture->angle;

    if (texture->is_draw_center)
    {
        float hw = texture->width * 0.5f;
        float hh = texture->height * 0.5f;

        texture_vertices[0].position.set(-hw, -hh, z);
        texture_vertices[1].position.set(-hw, +hh, z);
        texture_vertices[2].position.set(+hw, +hh, z);
        texture_vertices[3].position.set(+hw, -hh, z);
    }
    else
    {
        texture_vertices[0].position.set(0, 0, z);
        texture_vertices[1].position.set(0, h, z);
        texture_vertices[2].position.set(w, h, z);
        texture_vertices[3].position.set(w, 0, z);
    }

    texture_vertices[0].texcoord = texture->uv[0];
    texture_vertices[1].texcoord = texture->uv[1];
    texture_vertices[2].texcoord = texture->uv[2];
    texture_vertices[3].texcoord = texture->uv[3];

    Vec2 offset(static_cast<float>(x), static_cast<float>(y));
    texture_vertices[0].offset = offset;
    texture_vertices[1].offset = offset;
    texture_vertices[2].offset = offset;
    texture_vertices[3].offset = offset;

    Vec2 size(w, h);
    texture_vertices[0].size = size;
    texture_vertices[1].size = size;
    texture_vertices[2].size = size;
    texture_vertices[3].size = size;

    float rad = (float)(A_DEG_TO_RAD(angle));
    texture_vertices[0].angle = rad;
    texture_vertices[1].angle = rad;
    texture_vertices[2].angle = rad;
    texture_vertices[3].angle = rad;

    renderer->SetProgram(ProgramType::TEXTURE);
    renderer->AppendRenderData(texture_vertices, 4, texture_indices, 6, texture->id, PrimType::PT_Triangles);
}

void Painter::DrawLine(const Vec2& p1, const Vec2& p2, const Color& color, float thickness)
{
    assert(renderer);

    renderer->SetProgram(ProgramType::GRAPHICS);
    this->PathLineTo(p1);
    // this->PathLineTo(p1 + 0.5f);
    this->PathLineTo(p2);
    this->DrawPolylineAA(color, false, thickness);
    this->PathClear();
}

void Painter::DrawLines(const Vec2* points, int point_count, const Color& color, bool closed, float thickness)
{
    assert(renderer);

    renderer->SetProgram(ProgramType::GRAPHICS);
    for (int i = 0; i < point_count; i++)
    {
        this->PathLineTo(points[i]);
    }
    if (closed && thickness < 0)
        this->DrawConvexPolyFilled(color);
    else
        this->DrawPolylineAA(color, closed, thickness);
    this->PathClear();
}

void Painter::DrawLines(const std::vector<Vec2>& points, const Color& color, bool closed, float thickness)
{
    if (points.size() < 2)
        return;

    paths = points;

    renderer->SetProgram(ProgramType::GRAPHICS);
    if (closed && thickness < 0)
        this->DrawConvexPolyFilled(color);
    else
        this->DrawPolylineAA(color, closed, thickness);
    this->PathClear();
}

//void Painter::DrawLines(const Color& color, bool closed, float thickness)
//{
//    renderer->SetProgram(ProgramType::GRAPHICS);
//    if (closed && thickness < 0)
//        this->DrawConvexPolyFilled(color);
//    else
//        this->DrawPolylineAA(color, closed, thickness);
//    this->PathClear();
//}

void Painter::DrawPolyline(const std::vector<Vec2>& points, const Color& color, bool closed)
{
    renderer->SetProgram(ProgramType::GRAPHICS);
    int points_count = (int)points.size();
    /*只有两个点时无法close jerx*/
    if (points_count == 2)
        closed = false;

    int idx_count = (closed == false) ? points_count : points_count + 1;
    int vtx_count = points_count;
    geometry_vertices.resize(vtx_count);
    geometry_indices.resize(idx_count);
    for (int i = 0; i < points_count; ++i)
    {
        geometry_vertices[i].position = points[i];
        geometry_vertices[i].color = color;
        geometry_indices[i] = i;
    }
    if (closed)
        geometry_indices[points_count] = 0;
    renderer->AppendRenderData(geometry_vertices.data(), vtx_count, geometry_indices.data(), idx_count, PrimType::PT_Lines);
    // renderer->AppendRenderData(&vGeometryVertices[0], vtx_count, &geometry_indices[0], idx_count, PrimType::PT_Lines);
}

void Painter::DrawRect(const Vec2& tl, const Vec2& br, const Color& color, float rounding, float thickness)
{
    assert(renderer);

    renderer->SetProgram(ProgramType::GRAPHICS);
    if (thickness < 0)
    {
        if (rounding <= 0)
        {
            this->PrimRect(tl, br, color);
        }
        else
        {
            this->PathRect(tl, br, rounding);
            this->DrawConvexPolyFilled(color);
            this->PathClear();
        }
    }
    else
    {
        this->PathRect(tl, br, rounding);
        this->DrawPolylineAA(color, true, thickness);
        this->PathClear();
    }
}

void Painter::DrawGrid(int blockSize, int regionWidth, int regionHeight, const Color& color, [[maybe_unused]] float thickness)
{
    if (regionWidth <= 0 || regionHeight <= 0 || blockSize <= 0)
        return;
    for (int x = 0; x < regionWidth; x += blockSize)
    {
        paths.push_back(Vec2(x, 0));
        paths.push_back(Vec2(x, regionHeight));

        if (x + blockSize >= regionWidth)
        {
            paths.push_back(Vec2(regionWidth, 0));
            paths.push_back(Vec2(regionWidth, regionHeight));
        }
    }
    for (int y = 0; y < regionHeight; y += blockSize)
    {
        paths.push_back(Vec2(0, y));
        paths.push_back(Vec2(regionWidth, y));

        if (y + blockSize >= regionHeight)
        {
            paths.push_back(Vec2(0, regionHeight));
            paths.push_back(Vec2(regionWidth, regionHeight));
        }
    }
    DrawPolyline(paths, color, false);
    // this->DrawPolylineAA(color, true, thickness);
    this->PathClear();
}

void Painter::DrawCircle(const Vec2& center, float radius, const Color& color, int segments, float thickness,
    std::vector<Vec2>* vPath)
{
    renderer->SetProgram(ProgramType::GRAPHICS);
    if (thickness < 0)
    {
        this->PathArcTo(center, radius, 0, (float)A_PI_2, segments);
        this->DrawConvexPolyFilled(color);
    }
    else
    {
        this->PathArcTo(center, radius - 0.5f, 0, (float)A_PI_2, segments);
        this->DrawPolylineAA(color, true, thickness);
    }
    if (vPath)
    {
        *vPath = paths;
    }
    this->PathClear();
}

void Painter::DrawRotatedEllipse(const Vec2& center, float major_axis, float minor_axis, double angle,
    const Color& color, int segments, float thickness, std::vector<Vec2>* points)
{
    renderer->SetProgram(ProgramType::GRAPHICS);
    /*生成椭圆顶点*/
    {
        paths.resize(segments);
        double sin_radians = sin(angle);
        double cos_radians = cos(angle);
        double min = 0, max = A_PI_2;

        for (int i = 0; i < segments; i++)
        {
            const double a = min + ((double)i / (double)segments) * (max - min);
            double sin_thetaRadians = sin(a);
            double cos_thetaRadians = cos(a);
            float x = static_cast<float>(center.x + major_axis * cos_thetaRadians * cos_radians - minor_axis * sin_thetaRadians * sin_radians);
            float y = static_cast<float>(center.y + major_axis * cos_thetaRadians * sin_radians + minor_axis * sin_thetaRadians * cos_radians);
            paths[i].x = x;
            paths[i].y = y;
        }
    }
    if (thickness < 0)
    {
        this->DrawConvexPolyFilled(color);
    }
    else
    {
        this->DrawPolylineAA(color, true, thickness);
    }
    if (points)
    {
        *points = paths;
    }
    this->PathClear();
}

void Painter::DrawPoints(const std::vector<Vec2>& points, const Color& color, float point_size)
{
    (void)point_size;
    const auto points_count = (int)points.size();
    int idx_count = points_count;
    int vtx_count = points_count;
    geometry_vertices.resize(vtx_count);
    geometry_indices.resize(idx_count);
    for (int i = 0; i < points_count; ++i)
    {
        geometry_vertices[i].position = points[i];
        geometry_vertices[i].color = color;
        geometry_indices[i] = i;
    }
    renderer->AppendRenderData(geometry_vertices.data(), vtx_count, geometry_indices.data(), idx_count, PrimType::PT_Points);
}

void Painter::PainterText(const char* text, int x, int y, const Color& color, float pen_width, int alignmode, int font_size, const char* font_name)
{
    auto pd = renderer->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        QPainter painter(device);
        painter.beginNativePainting();
        PainterText(&painter, text, x, y, color, pen_width, alignmode, font_size, font_name);
        painter.endNativePainting();
    }
}

void Painter::PainterText(QPainter* painter, const char* text, int x, int y, const Color& color, float pen_width, int alignmode, int font_size, const char* font_name)
{
    painter->save();
    if (font_size > 0 && font_name)
        painter->setFont(QFont(font_name, font_size));
    // QPainterPath path;
    // path.addText(x, y, painter->font(), QString(text));
    // QPen pen = painter->pen();
    // pen.setWidth(2);
    // pen.setColor(QColor(255 - color.r * 255, 255 - color.g * 255, 255 - color.b * 255));
    // painter->setPen(pen);
    // painter->drawPath(path);
    int tx = x, ty = y;
    switch (alignmode)
    {
    case TextAlignment::TEXT_ALIGN_CENTER:
        break;
    case TextAlignment::TEXT_ALIGN_LEFT_BUTTOM:
    default:
        break;
    }
    painter->setPen(QPen(QColor(color.r * 255, color.g * 255, color.b * 255, color.a * 255), pen_width));
    painter->drawText(tx, ty, QString::fromLocal8Bit(text));
    painter->restore();
}

void Painter::PathLineTo(const Vec2& pos)
{
    paths.push_back(pos);
}

void Painter::PathArcTo(const Vec2& center, float radius, float min, float max, int segments)
{
    for (int i = 0; i < segments; i++)
    {
        const float a = min + ((float)i / (float)segments) * (max - min);
        paths.emplace_back(Vec2(center.x - sinf(a) * radius, center.y + cosf(a) * radius));
    }
}

void Painter::PathArcToFast(const Vec2& center, float radius, int a_min_of_12, int a_max_of_12)
{
    static Vec2 circle[13];
    static bool has_build = false;

    /* 构建圆 */
    if (has_build == false)
    {
        for (int i = 0; i <= 12; i++)
        {
            float radian = float(i) / 12 * 6.28318f;
            circle[i].x = -sinf(radian);
            circle[i].y = cosf(radian);
        }
        has_build = true;
    }

    for (int i = a_min_of_12; i <= a_max_of_12; i++)
    {
        paths.emplace_back(Vec2(circle[i].x * radius + center.x, circle[i].y * radius + center.y));
    }
}

void Painter::PathRect(const Vec2& tl, const Vec2& br, float rounding)
{
    if (rounding <= 0.0f)
    {
        this->PathLineTo(tl);
        this->PathLineTo(Vec2(br.x, tl.y));
        this->PathLineTo(br);
        this->PathLineTo(Vec2(tl.x, br.y));
    }
    else
    {
        this->PathArcToFast(Vec2(tl.x + rounding, tl.y - rounding), rounding, 0, 3);
        this->PathArcToFast(Vec2(tl.x + rounding, br.y + rounding), rounding, 3, 6);
        this->PathArcToFast(Vec2(br.x - rounding, br.y + rounding), rounding, 6, 9);
        this->PathArcToFast(Vec2(br.x - rounding, tl.y - rounding), rounding, 9, 12);
    }
}

void Painter::PrimRect(const Vec2& tl, const Vec2& br, const Color& color)
{
    assert(renderer);

    geometry_vertices.resize(4);
    geometry_indices.resize(6);

    geometry_vertices[0].position.set(tl.x, tl.y, 0);
    geometry_vertices[1].position.set(br.x, tl.y, 0);
    geometry_vertices[2].position.set(br.x, br.y, 0);
    geometry_vertices[3].position.set(tl.x, br.y, 0);

    geometry_vertices[0].color = color;
    geometry_vertices[1].color = color;
    geometry_vertices[2].color = color;
    geometry_vertices[3].color = color;

    geometry_indices[0] = 0;
    geometry_indices[1] = 2;
    geometry_indices[2] = 1;
    geometry_indices[3] = 0;
    geometry_indices[4] = 3;
    geometry_indices[5] = 2;

    renderer->AppendRenderData(&geometry_vertices[0], 4, &geometry_indices[0], 6, PrimType::PT_Triangles);
    // pGraphicsContext->SetProgram(SPT_Color);
    // pGraphicsContext->SetBlendMode(StandardBlendMode::SBM_AlphaTexture);
    // pGraphicsContext->GetRenderer()->AppendRenderData(&vGeometryVertices[0], 4, &geometry_indices[0], 6,
    // PT_Triangles);
}

void Painter::DrawPolylineAA(const Color& color, bool closed, float thickness)
{
    if (paths.size() < 2)
        return;

    const int points_count = (int)paths.size();
    /*只有两个点时无法close jerx*/
    if (points_count == 2)
        closed = false;
    const int count = closed ? points_count : points_count - 1;

    const bool thick_line = thickness > 1.0f;

    /* 反走样 */
    const float AA_SIZE = 1.0f;
    const float alpha = 0.0f;

    const int idx_count = thick_line ? count * 18 : count * 12;
    const int vtx_count = thick_line ? points_count * 4 : points_count * 3;

    geometry_vertices.resize(vtx_count);
    geometry_indices.resize(idx_count);

    /* 临时缓冲区 */
    Vec2* temp_normals = (Vec2*)alloca(points_count * (thick_line ? 5 : 3) * sizeof(Vec2));
    Vec2* temp_points = temp_normals + points_count;

    /* 计算直线的法线 */
    for (int i1 = 0; i1 < count; i1++)
    {
        const int i2 = (i1 + 1) == points_count ? 0 : i1 + 1;
        Vec2 diff = paths[i2] - paths[i1];
        diff = diff.normalize();

        temp_normals[i1].x = diff.y;
        temp_normals[i1].y = -diff.x;
    }

    if (!closed)
    {
        temp_normals[points_count - 1] = temp_normals[points_count - 2];
    }

    if (!thick_line)
    {
        if (!closed)
        {
            temp_points[0] = paths[0] + temp_normals[0] * AA_SIZE;
            temp_points[1] = paths[0] - temp_normals[0] * AA_SIZE;
            temp_points[(points_count - 1) * 2 + 0] =
                paths[points_count - 1] + temp_normals[points_count - 1] * AA_SIZE;
            temp_points[(points_count - 1) * 2 + 1] =
                paths[points_count - 1] - temp_normals[points_count - 1] * AA_SIZE;
        }
        unsigned int idx1 = 0;

        for (int i1 = 0; i1 < count; i1++)
        {
            const int i2 = (i1 + 1) == points_count ? 0 : i1 + 1;
            unsigned int idx2 = (i1 + 1) == points_count ? 0 : idx1 + 3;

            /* 平均法向量，即两直线交点的法向量 */
            Vec2 dm = (temp_normals[i1] + temp_normals[i2]) * 0.5f;
            float dmr2 = dm.x * dm.x + dm.y * dm.y;
            if (dmr2 > 0.000001f)
            {
                float scale = 1.0f / dmr2;
                if (scale > 100.0f) scale = 100.0f;
                dm = dm * scale;
            }
            dm = dm * AA_SIZE;

            temp_points[i2 * 2 + 0] = paths[i2] + dm;
            temp_points[i2 * 2 + 1] = paths[i2] - dm;

            long long temp_index = i1 * 12;
            /* 索引数据 */
            geometry_indices[temp_index + 0] = idx2 + 0;
            geometry_indices[temp_index + 1] = idx1 + 0;
            geometry_indices[temp_index + 2] = idx1 + 2;

            geometry_indices[temp_index + 3] = idx1 + 2;
            geometry_indices[temp_index + 4] = idx2 + 2;
            geometry_indices[temp_index + 5] = idx2 + 0;

            geometry_indices[temp_index + 6] = idx2 + 1;
            geometry_indices[temp_index + 7] = idx1 + 1;
            geometry_indices[temp_index + 8] = idx1 + 0;

            geometry_indices[temp_index + 9] = idx1 + 0;
            geometry_indices[temp_index + 10] = idx2 + 0;
            geometry_indices[temp_index + 11] = idx2 + 1;

            idx1 = idx2;
        }

        /* 顶点数据 */
        for (int i = 0; i < points_count; i++)
        {
            long long temp_index = i * 3;
            geometry_vertices[temp_index + 0].position = paths[i];
            geometry_vertices[temp_index + 1].position = temp_points[i * 2 + 0];
            geometry_vertices[temp_index + 2].position = temp_points[i * 2 + 1];

            geometry_vertices[temp_index + 0].color = color;
            geometry_vertices[temp_index + 1].color = color;
            geometry_vertices[temp_index + 2].color = color;

            geometry_vertices[temp_index + 1].color.a = alpha;
            geometry_vertices[temp_index + 2].color.a = alpha;
        }
    }
    else
    {
        const float half_inner_thickness = (thickness - AA_SIZE) * 0.5f;
        if (closed == false)
        {
            temp_points[0] = paths[0] + temp_normals[0] * (half_inner_thickness + AA_SIZE);
            temp_points[1] = paths[0] + temp_normals[0] * (half_inner_thickness);
            temp_points[2] = paths[0] - temp_normals[0] * (half_inner_thickness);
            temp_points[3] = paths[0] - temp_normals[0] * (half_inner_thickness + AA_SIZE);
            temp_points[(points_count - 1) * 4 + 0] =
                paths[points_count - 1] + temp_normals[points_count - 1] * (half_inner_thickness + AA_SIZE);
            temp_points[(points_count - 1) * 4 + 1] =
                paths[points_count - 1] + temp_normals[points_count - 1] * (half_inner_thickness);
            temp_points[(points_count - 1) * 4 + 2] =
                paths[points_count - 1] - temp_normals[points_count - 1] * (half_inner_thickness);
            temp_points[(points_count - 1) * 4 + 3] =
                paths[points_count - 1] - temp_normals[points_count - 1] * (half_inner_thickness + AA_SIZE);
        }

        unsigned int idx1 = 0;

        for (int i1 = 0; i1 < count; i1++)
        {
            const int i2 = (i1 + 1) == points_count ? 0 : i1 + 1;
            unsigned int idx2 = (i1 + 1) == points_count ? 0 : idx1 + 4;

            /* 平均法向量，即两直线交点的法向量 */
            Vec2 dm = (temp_normals[i1] + temp_normals[i2]) * 0.5f;
            float dmr2 = dm.length();
            if (dmr2 > 0.000001f)
            {
                float scale = 1.0f / dmr2;
                if (scale > 100.0f)
                    scale = 100.0f;
                dm = dm * scale;
            }
            Vec2 dm_out = dm * (half_inner_thickness + AA_SIZE);
            Vec2 dm_in = dm * half_inner_thickness;

            /* 索引数据 */
            temp_points[i2 * 4 + 0] = paths[i2] + dm_out;
            temp_points[i2 * 4 + 1] = paths[i2] + dm_in;
            temp_points[i2 * 4 + 2] = paths[i2] - dm_in;
            temp_points[i2 * 4 + 3] = paths[i2] - dm_out;

            geometry_indices[i1 * 18 + 0] = idx2 + 1;
            geometry_indices[i1 * 18 + 1] = idx1 + 1;
            geometry_indices[i1 * 18 + 2] = idx1 + 2;

            geometry_indices[i1 * 18 + 3] = idx1 + 2;
            geometry_indices[i1 * 18 + 4] = idx2 + 2;
            geometry_indices[i1 * 18 + 5] = idx2 + 1;

            geometry_indices[i1 * 18 + 6] = idx2 + 1;
            geometry_indices[i1 * 18 + 7] = idx1 + 1;
            geometry_indices[i1 * 18 + 8] = idx1 + 0;

            geometry_indices[i1 * 18 + 9] = idx1 + 0;
            geometry_indices[i1 * 18 + 10] = idx2 + 0;
            geometry_indices[i1 * 18 + 11] = idx2 + 1;

            geometry_indices[i1 * 18 + 12] = idx2 + 2;
            geometry_indices[i1 * 18 + 13] = idx1 + 2;
            geometry_indices[i1 * 18 + 14] = idx1 + 3;

            geometry_indices[i1 * 18 + 15] = idx1 + 3;
            geometry_indices[i1 * 18 + 16] = idx2 + 3;
            geometry_indices[i1 * 18 + 17] = idx2 + 2;

            idx1 = idx2;
        }

        /* 顶点数据 */
        for (int i = 0; i < points_count; i++)
        {
            geometry_vertices[i * 4 + 0].position = temp_points[i * 4 + 0];
            geometry_vertices[i * 4 + 0].color = color;
            geometry_vertices[i * 4 + 1].position = temp_points[i * 4 + 1];
            geometry_vertices[i * 4 + 1].color = color;
            geometry_vertices[i * 4 + 2].position = temp_points[i * 4 + 2];
            geometry_vertices[i * 4 + 2].color = color;
            geometry_vertices[i * 4 + 3].position = temp_points[i * 4 + 3];
            geometry_vertices[i * 4 + 3].color = color;

            geometry_vertices[i * 4 + 0].color.a = alpha;
            geometry_vertices[i * 4 + 3].color.a = alpha;
        }
    }

    renderer->AppendRenderData(geometry_vertices.data(), vtx_count, geometry_indices.data(), idx_count, PrimType::PT_Triangles);
    // pGraphicsContext->SetProgram(SPT_Color);
    // pGraphicsContext->SetBlendMode(StandardBlendMode::SBM_AlphaTexture);
    // pGraphicsContext->GetRenderer()->AppendRenderData(&vGeometryVertices[0], vtx_count, &geometry_indices[0],
    // idx_count, PT_Triangles);
}

void Painter::DrawConvexPolyFilled(const Color& color)
{
    int points_count = (int)paths.size();
    int idx_count;
    int vtx_count;

    if (use_anti_aliasing)
    {
        const float AA_SIZE = 1.0f;
        const float alpha = 0.0f;

        idx_count = (points_count - 2) * 3 + points_count * 6;
        vtx_count = (points_count * 2);

        geometry_vertices.resize(vtx_count);
        geometry_indices.resize(idx_count);

        unsigned int vtx_inner_idx = 0;
        unsigned int vtx_outer_idx = 1;

        for (int i = 2; i < points_count; i++)
        {
            geometry_indices[(i - 2) * 3 + 0] = vtx_inner_idx;
            geometry_indices[(i - 2) * 3 + 1] = vtx_inner_idx + ((i - 1) << 1);
            geometry_indices[(i - 2) * 3 + 2] = vtx_inner_idx + (i << 1);
        }

        /* 计算法线 */
        Vec2* temp_normals = (Vec2*)alloca(points_count * sizeof(Vec2));
        for (int i0 = points_count - 1, i1 = 0; i1 < points_count; i0 = i1++)
        {
            const Vec2& p0 = paths[i0];
            const Vec2& p1 = paths[i1];
            Vec2 diff = p1 - p0;
            diff = diff.normalize();

            temp_normals[i0].x = diff.y;
            temp_normals[i0].y = -diff.x;
        }

        for (int i0 = points_count - 1, i1 = 0; i1 < points_count; i0 = i1++)
        {
            /* 平均法向量，即两直线交点的法向量 */
            Vec2 dm = (temp_normals[i0] + temp_normals[i1]) * 0.5f;
            float dmr2 = dm.length();

            if (dmr2 > 0.000001f)
            {
                float scale = 1.0f / dmr2;
                if (scale > 100.0f)
                    scale = 100.0f;
                dm = dm * scale;
            }
            dm = dm * AA_SIZE * 0.5f;

            /* 顶点数据 */
            geometry_vertices[i1 * 2 + 0].position = paths[i1] - dm;
            geometry_vertices[i1 * 2 + 1].position = paths[i1] + dm;

            geometry_vertices[i1 * 2 + 0].color = color;
            geometry_vertices[i1 * 2 + 1].color = color;
            geometry_vertices[i1 * 2 + 1].color.a = alpha;

            /* 索引数据 */
            geometry_indices[i1 * 6 + 0 + (points_count - 2) * 3] = vtx_inner_idx + (i1 << 1);
            geometry_indices[i1 * 6 + 1 + (points_count - 2) * 3] = vtx_inner_idx + (i0 << 1);
            geometry_indices[i1 * 6 + 2 + (points_count - 2) * 3] = vtx_outer_idx + (i0 << 1);
            geometry_indices[i1 * 6 + 3 + (points_count - 2) * 3] = vtx_outer_idx + (i0 << 1);
            geometry_indices[i1 * 6 + 4 + (points_count - 2) * 3] = vtx_outer_idx + (i1 << 1);
            geometry_indices[i1 * 6 + 5 + (points_count - 2) * 3] = vtx_inner_idx + (i1 << 1);
        }
    }
    else
    {
        idx_count = (points_count - 2) * 3;
        vtx_count = points_count;

        geometry_vertices.resize(vtx_count);
        geometry_indices.resize(idx_count);

        for (int i = 0; i < vtx_count; i++)
        {
            geometry_vertices[i].position = paths[i];
            geometry_vertices[i].color = color;
        }

        for (int i = 2; i < points_count; i++)
        {
            geometry_indices[(i - 2) * 3 + 0] = 0;
            geometry_indices[(i - 2) * 3 + 1] = i - 1;
            geometry_indices[(i - 2) * 3 + 2] = i;
        }
    }

    renderer->AppendRenderData(geometry_vertices.data(), vtx_count, geometry_indices.data(), idx_count, PrimType::PT_Triangles);
    // pGraphicsContext->SetProgram(SPT_Color);
    // pGraphicsContext->SetBlendMode(StandardBlendMode::SBM_AlphaTexture);
    // pGraphicsContext->GetRenderer()->AppendRenderData(&vGeometryVertices[0], vtx_count, &geometry_indices[0],
    // idx_count, PT_Triangles);
}

void Painter::PathClear()
{
    paths.resize(0);
}
