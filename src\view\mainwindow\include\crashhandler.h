
/*****************************************************************
 * @file   crashhandler.h
 * @brief 当程序崩溃时，生成dump文件
 * @details 主要功能有两点：1.生成dump文件 2.生成异常日志，异常日志包含详细的崩溃信息，可以定位到崩溃的cpp文件和函数和行号
 * <AUTHOR>
 * @date 2025.5.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.5.22          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
//STD
//Custom
//Third
#ifndef F48344C0_E0AE_4AEF_93D3_C971EAA1B3EE
#define F48344C0_E0AE_4AEF_93D3_C971EAA1B3EE
#if defined(_WIN32) || defined(_WIN64)

#include <Windows.h>
#include <DbgHelp.h>
#include <tchar.h>
#include <string>
#include <ctime>
#include <sstream>
#include <iostream>

#pragma comment(lib, "Dbghelp.lib")

class CrashHandler 
{
    public:
        static void Init();

    private:
        static LONG WINAPI ExceptionFilter(EXCEPTION_POINTERS* pExceptionPointers);
        static void WriteMiniDump(EXCEPTION_POINTERS* pExceptionPointers);
        static std::wstring GetDumpFileName();
        static void GenerateBreakStackLog();

};

#endif
#endif /* F48344C0_E0AE_4AEF_93D3_C971EAA1B3EE */
