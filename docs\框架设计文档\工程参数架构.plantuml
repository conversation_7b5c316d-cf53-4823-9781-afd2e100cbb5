@startuml 工程参数架构


' 定义 拍摄区域
class CaptureFovPos <<拍摄区域>> {
    +int id;
    +float x;
    +float y;
    +float z;
}

' 定义 规格
class DetectSpec<<规格>> {
    +std::string spec_name
    +std::string spec_type
    +float spec_upper
    +float spec_lower
    +bool is_upper
    +bool is_lower
}

' 定义 子检测框
class SubDetectWindow<<子检测框>> {
    +std::string name
    +int id
    +int cx
    +int cy
    +int width
    +int height
    +int detect_state
}

' 定义 检测算法
class DetectAlgorithm <<检测算法>>{
    +std::string detect_algorithm_name
    ' +std::string detect_spec_name
    +std::string param;
    +std::string color_param;
    +std::string light_param;
    ' +DetectAlgoType type; 
    +int light_image_id;
    +std::vector<int> template_image_ids;
    +std::vector<SubDetectWindow> algorithm_detect_windows;
}

' 定义 Template 结构体
class Template <<模板>>{
    +int id
    +int cols
    +int rows
    +int light_image_id;          ///< 模板对应的灯图id
    +std::vector<uint8_t> image;  ///< 模板图像
    +std::string color_params;    ///< 颜色参数
}

' 定义 检测框
class DetectWindow<<检测框>> {
    +std::string model_name;///< 模型名称
    +std::string name;///< 检测区域名称
    +std::string defect_name;///< 缺陷名称
    +std::string group_name;///< 缺陷组名称
    +int id;///< 检测框 id
    +int cx;///< 相对于CAD的X偏移量
    +int cy;///< 相对于CAD的Y偏移量
    +int width;///< 检测区域的宽度
    +int height;///< 检测区域的高度
    +int level;///< 检测等级
    +int detect_state;///< 检测状态
    +bool enable;///< 是否检测
    +std::vector<DetectAlgorithm> algorithms;///< 算法
    +float search_size;///< 检测框搜索范围
}

' 定义 模型
class DetectModel<<模型>> {
    +std::vector<DetectWindow> detect_model;
}

' 定义 料号
class PNDetectInfo<<料号>> {
    +std::vector<DetectSpec> specs
    +std::map<std::string/**< 元件组件名 */, DetectModel> detect_models
}

class ComponentModule<<元件组件>> {
    +int x
    +int y
    +int width
    +int height
    +ModuleType module_type
    +ModuleShape module_shape
    +std::string module_name
}

class Body <<本体>> {
    module_type = "本体"
}
class PadGroup<<焊盘组>> {
    module_type = "焊盘组"
}

' 定义 元件
class Component<<元件>> {
    +int sub_board_id
    +int component_id
    +std::string component_name
    +ComponentType component_type
    +std::string part_number
    +int x
    +int y
    ' +int width
    ' +int height
    +float z
    +float angle
    +bool enable
    +cv::Mat device_img
    +std::vector<int> fov_ids
    +std::vector<ComponentModule> component_modules
}

' 定义 条码
class Barcode <<条码>> {
    component_type = "条码"
    part_number = "条码"
}

' 定义 定位点
class Mark <<定位点>> {
    component_type = "定位点"
    part_number = "定位点"
}

' 定义 子板
class SubBoard <<子板>> {
    +std::string sub_name
    +int id
    +int col
    +int row
    +int x
    +int y
    +int width
    +int height
    +bool enable
    +std::vector<Component> component_info
    +std::vector<Mark> sub_mark
    +Mark bad_mark
    +Barcode barcode
}

' 定义 整板
class Board <<整板>> {
    +int width
    +int height
    +int cols
    +int rows
    +int num_sub_board
    +int layout
    +int material
    +double real_width
    +double real_height
    +double left_top_x
    +double left_top_y
    +double right_bottom_x
    +double right_bottom_y
    +std::vector<Mark> marks
    +std::vector<Barcode> barcodes
    +std::vector<SubBoard> sub_board
    +std::unordered_map<std::string, std::string> params
    +std::unordered_map<std::string /**< 料号名 */, PNDetectInfo> part_nums_and_detect_regions
}

' 定义 工程
class ProjectParam <<工程>> {
    +std::string project_name
    +Board board_info
    +std::vector<Template> temps
    +std::map<int /**< 图片类型 */, cv::Mat> entirety_board_imgs
}

' 定义类之间的关系
DetectModel "n" *-- "n" DetectWindow
PNDetectInfo "1" *-- "n" DetectModel
PNDetectInfo "n" *-- "n" DetectSpec
DetectWindow "n" *-- "n" DetectAlgorithm
DetectAlgorithm "n" *-- "n" SubDetectWindow
DetectAlgorithm "1" *-- "n" Template
ComponentModule <|-- Body
ComponentModule <|-- PadGroup
PNDetectInfo "1" *-- "n" ComponentModule
Component "n" *-- "n" ComponentModule
Component "n" *-- "n" CaptureFovPos
Component "1" *-- "n" PNDetectInfo
Component  <|--  Mark
Component  <|--  Barcode
SubBoard "1" *-- "n" Component
Board "1" *-- "n" PNDetectInfo
Board "1" *-- "n" SubBoard
ProjectParam "1" *-- "n" Template
ProjectParam "1" *-- "1" CaptureFovPos
ProjectParam "1" *-- "1" Board

@enduml

