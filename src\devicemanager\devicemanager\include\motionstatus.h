/*****************************************************************//**
 * @file   motionstatus.h
 * @brief  运控状态推送模块
 * @details 包含轴位置，通用IO状态、轨道IO状态等
 * <AUTHOR>
 * @date 2024.9.2
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.9.2         <td>V2.0              <td>zhaokunlong      <td>                      <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSMOTIONSTATUS_H__
#define __JRSMOTIONSTATUS_H__

 //STD
#include <iostream>
#include <sstream>
#include <thread>

 //Custom
#include "pluginexport.hpp"
#include "deviceparam.hpp"
#include "viewparam.hpp"
#include "motion.h"
#include "coreapplication.h"

// Third
#include "nlohmann/json.hpp"

using JSON = nlohmann::json;


namespace jrsdevice
{
    using MotionMsgCallBack = std::function<int(const jrsdata::DeviceParamPtr& msg)>;
    class JRS_AOI_PLUGIN_API MotionStatus
    {
        public:
            MotionStatus(std::shared_ptr<Motion>& motion);
            ~MotionStatus();
            /**
             * @fun SetMotionMsgCallBack
             * @brief 设置运控消息回调
             * @param callback_ 回调函数
             * <AUTHOR>
             * @date 2024.8.20
             */
            void SetMotionMsgCallBack(MotionMsgCallBack callback_);

            /**
             * @fun SetTrackSetting
             * @brief 设置运控轨道配置
             * @param track 轨道配置
             * <AUTHOR>
             * @date 2024.9.2
             */
            void SetDeviceTrack(JSON track);

            /**
             * @fun SetMotionSetting
             * @brief 运控轨道设置信息(进料方向、出料方向、模式等)
             * @param track 轨道配置
             * <AUTHOR>
             * @date 2024.9.2
             */
            void SetMotionSetting(jrsdata::MotionSetting motion);
        private:

            //! 运控状态数据获取处理函数
            void MotionStatusHander();

            //! 构建TrackInput
            jrsdata::TrackInput ConstructTrackInput(int trackIndex);

            //! 构建TrackOutput
            jrsdata::TrackOutput ContructTrackOutput(int trackIndex);

            // 根据name获取对象
            JSON GetObjFromName(JSON Obj, std::string name);

        private:  
            std::shared_ptr<Motion> motion_ptr;                     /**< 运控实例*/

            bool exit_;                                             /**< 线程运行标志 */
            std::thread motion_thread;                              /**< 运控状态数据获取线程 */

            jrsdata::DeviceParamPtr operate_view_param_ptr;         /**< 返回给操作界面的数据 */
            MotionMsgCallBack motion_msg_callback;                  /**< 运控状态信息回调 */

            jrsdata::MotionSetting motion_setting;                  /**< 运控轨道设置信息 */
            JSON track_setting;                                     /**< 轨道配置 */

            jrsdata::MotionSatus last_motion_Status;                /**< 记录上次的运控状态 */                 
    };
}

#endif // !__JRSMOTIONSTATUS_H__
