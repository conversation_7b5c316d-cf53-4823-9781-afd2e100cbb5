﻿/*****************************************************************//**
 * @file   newprojectview.h
 * @brief 新建工程界面
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef NEWPROJECTVIEW_H
#define NEWPROJECTVIEW_H
// prebuild
#include "pch.h"
 //QT
#pragma warning(push, 3)
#include <QWidget>
#pragma warning(pop)
//Custom
#include "newfileview.h"
#include "projectlinkview.h"
namespace Ui
{
    class NewProjectView;
}

namespace jrsaoi
{
    class NewProjectView : public QWidget
    {
        Q_OBJECT
    public:
        NewProjectView(QWidget* parent = Q_NULLPTR);
        ~NewProjectView();
        void UpdateProjectPath(const std::string& project_path_);
        void UpdateNewFileProductWidth(double track_width1, double track_width2);
    signals:
        void SigScanceBoardImags();
        /**
         * @fun SigConfirmBoardPos
         * @brief 确认板子位置信号
         * @param param 板子位置信息
         * <AUTHOR>
         * @date 2024.9.9
         */
        void SigConfirmBoardPos(const jrsdata::ProjectParamPtr& param);
        /**
         * @fun SignalCreateProject
         * @brief
         * @param param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SignalCreateProject(const jrsdata::ProjectParamPtr& param);
        /**
         * @fun SigMotionDebugTrigger
         * @brief
         * @param operateparam
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigMotionDebugTrigger(jrsdata::OperateViewParamPtr operateparam);
        /**
         * @fun SigGetPosition
         * @brief 获取轴位置
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigGetPosition();
        /**
         * @fun SigSaveProject
         * @brief
         * <AUTHOR>
         * @date 2024.10.31
         */
        void SigSaveProject();
        /**
            * @fun SigSaveEntiretyImages
            * @brief
            * @param group_name_
            * <AUTHOR>
            * @date 2024.11.4
            */
        void SigSaveEntiretyImages(const std::string& group_name_);

        void SigNewFileGetProductWidth();

        /**
         * @fun SigGetMultiProjectLists 
         * @brief 获取多工程列表
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SigGetMultiProjectLists();

        /**
         * @fun SigComfirmLinkProject 
         * @brief 确认工程关联
         * @param info [IN] 关联的工程名称
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SigComfirmLinkProject(const std::string& project_name);

        /**
         * @fun SigCancleLinkProject 
         * @brief 取消工程关联
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SigCancleLinkProject();




    public slots:
        /**
         * @fun SlotUpdateInfo
         * @brief 外部更新界面信息
         *
         * <AUTHOR>
         * @date 2024.09.10
         */
        void SlotUpdateInfo(const jrsdata::ViewParamBasePtr& param);

        /**
        * @fun SlotNewProject
        * @brief 新建工程按钮槽函数响应
        * <AUTHOR>
        * @date 2024.8.19
        */
        void SlotNewProject();
   
        /**
         * @fun SlotConfirmFileInfo
         * @brief 确认新建工程 槽函数
         * @param info  新建工程参数
         * <AUTHOR>
         * @date 2024.8.21
         */
        void SlotConfirmFileInfo(const jrsdata::ProjectEventInfo& info);
        /**
         * @fun UpdatePosition
         * @brief
         * @param param_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void UpdatePosition(jrsdata::OperateViewParamPtr param_);
        /**
         * @fun UpdateABPoints
         * @brief  更新A点B点
         * <AUTHOR>
         * @date 2024.12.6
         */
        void SlotUpdateABPoints();

        /**
         * @fun SlotGetProjectLists 
         * @brief 获取工程已经导入的工程名称列表
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SlotGetProjectLists();
    private:
        /**
         * @fun InitView
         * @brief 初始化界面
         * <AUTHOR>
         * @date 2024.8.19
         */
        void InitView();
        /**
         * @fun InitMember
         * @brief 初始化属性
         * <AUTHOR>
         * @date 2024.8.19
         */
        void InitMember();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数链接
         * <AUTHOR>
         * @date 2024.8.19
         */
        void InitConnect();
        /**
         * @fun CreateProject
         * @brief 创建工程
         * @param info 工程事件信息
         * <AUTHOR>
         * @date 2024.9.9
         */
        void CreateProject(const jrsdata::ProjectEventInfo& info);
        /**
         * @fun UpdatePositionAndBoardRealSize
         * @brief 更新位置和实际大小  单位 mm
         * <AUTHOR>
         * @date 2024.11.30
         */
        void UpdatePositionAndBoardRealSize();

        /**
         * @fun ShowLinkProjectView 
         * @brief 显示工程关联界面
         * @param project_lists [IN] 工程列表
         * @param linked_project_name [IN] 已经关联的工程名称
         * <AUTHOR>
         * @date 2025.6.8
         */
        void ShowLinkProjectView(const std::list<std::string>& project_lists,const std::string& linked_project_name);

    private:
        Ui::NewProjectView* ui;
        NewFileView* new_project_file_view; //!新建文件输入名称界面
        ProjectLinkView* project_link_view; //!工程关联界面
        bool _is_update_project;
        //jrsdata::ProjectParamPtr new_project_param_ptr; //！工程参数 没用的成员 jerx 
        jrsdata::OperateViewParamPtr param_ptr;        //！操作参数
        int position_index;
        jrsdata::ProjectEventInfo  _project_event_info;/**< 工程事件信息*/
        // TODO 移动位置
        std::string default_path = "D:/__Project/ProjectFile";
        const std::string default_ext = ".jrspro";

    };
}
#endif