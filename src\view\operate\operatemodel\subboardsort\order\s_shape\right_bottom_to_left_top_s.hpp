/*****************************************************************
 * @file   right_bottom_to_left_top_s.hpp
 * @brief
 * @details
 * <AUTHOR>
 * @date 2025.6.6
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.6          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace subboardsort
{
    class SRightBottomToLeftTop :public SubboardSortBase
    {

    public:
        int RegularSort(std::vector<std::vector<jrsdata::SubBoard>>& subboards_) override
        {

            int subboard_id = 1;
            int row_size = static_cast<int>(subboards_.size());
            int current_row = 0; /**< control row direction */
            for (int row_num = row_size - 1;row_num >= 0;--row_num)
            {

                auto& row_data = subboards_[row_num];
                if (current_row % 2 == 0)
                {
                    // 左到右
                    for (size_t col = 0; col < row_data.size(); ++col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
                else
                {
                    // 右到左
                    for (int col = static_cast<int>(row_data.size()) - 1; col >= 0; --col)
                    {
                        UpdateSubboard(row_data[col], subboard_id++);
                    }
                }
                ++current_row;
            }
            return jrscore::AOI_OK;

        }
    };
}
