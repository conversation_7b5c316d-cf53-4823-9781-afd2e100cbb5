# Renderer2d详细设计文档

## 1. 项目概述
- **目标**: 系统使用opengl进行图形及图片的渲染及控制
- **范围**: 系统主要功能包括图形渲染、图片渲染、ui控制、缩略图显示

---

## 2. 系统架构设计
- **架构图**: [架构图](./renderer2d模块架构.pu)
- **类图**: [类图](./renderer2d类图.pu)

---

## 3. 模块设计
- **模块名称**: 状态管理器(StateManager)
    - **功能描述**: 负责系统状态管理与事件转发，包括响应其他管理器的事件
    - **依赖关系**: 依赖图形管理器、UI管理器、命令管理器等模块

- **模块名称**: 图形管理器(GraphicsManager)
    - **功能描述**: 提供对图形和图层的完整管理功能，包括创建、删除、修改和查找操作
    - **依赖关系**: 继承渲染对象(RenderAbstract),因此可以加入渲染器(Renderer)进行渲染

- **模块名称**: ui管理器(Renderer2DManager)
    - **功能描述**: 处理用户界面相关的控制和交互，包括接收UI事件和视野管理
    - **依赖关系**: 依赖主ui(Renderer2DWidget),标尺ui(RulerWidget),缩略图ui(ThumbnailNavigationWidget)

- **模块名称**: 命令管理器(CommandManager)
    - **功能描述**: 负责管理用户命令的执行与撤销，包括创建图形、删除图形、修改图形的命令
    - **依赖关系**: 依赖图形管理器

- **模块名称**: 鼠标指针管理器(CustomCursorManager)
    - **功能描述**: 负责管理ui鼠标指针
    - **依赖关系**: 无

- **模块名称**: gl管理器(WindowInterface)
    - **功能描述**: 负责管理opengl上下文和渲染循环
    - **依赖关系**: 依赖渲染器(Renderer),着色器程序管理器(ShaderProgramManager),信号转发器(WindowSignalEmitter),虚拟相机(VisualCameraAbstract)

---

## 4. 数据设计
- **数据字典**:

| 字段名       | 数据类型 |  描述         |
|--------------|----------|--------------|
| GraphicsAbstract   | 智能指针      | 图形基类|
| GraphicsID   | 结构体      | 图形唯一标识|
| LayerConfig     | 结构体  | 图层配置信息 |
| ControlAttributes     | 结构体  | 控制点的配置信息 |
| z     | float  | 纹理堆叠高度  |

---
