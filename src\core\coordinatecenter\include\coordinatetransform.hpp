﻿/*********************************************************************
 * @brief  全局坐标转换.
 *
 * @file   coordinatetransform.hpp
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#ifndef COORDINATETRANSFORM_HPP
#define COORDINATETRANSFORM_HPP
 //STD
#include <atomic>
#include <algorithm>
// Custom
#include "../render/interface/include/algodefine.hpp" // 算法参数
#include "pluginexport.hpp"

#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include <Eigen/Dense>
#pragma warning(pop)
#undef min
#undef max
namespace jrscore
{

    struct CoordinateTransformAttribute
    {
        bool axis[2] = { true, false };      ///< 坐标轴方向(x,y) true表示方向相同,false表示方向相反
        float lefttop[2] = { 0.0, 0.0 };     ///< 物理左上角坐标(x,y)  单位 mm
        float rightbottom[2] = { 0.0, 0.0 }; ///< 物理右上角坐标(x,y)  单位 mm
        float resolution[2] = { 0.0, 0.0 };  ///< 分辨率(x,y) 应为正数 单位 mm/pixel
    };

    class JRS_AOI_PLUGIN_API CoordinateTransform
    {
    public:
        CoordinateTransform() : matrix(Eigen::Matrix3f::Identity()), size{ 0, 0 } {}
        ~CoordinateTransform() {}
        /**
         * @brief  初始化坐标转换.
         * @fun    Init
         * @param  a 坐标转换参数
         * @return 正常返回0,否则返回错误码
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        int Init(const CoordinateTransformAttribute& a)
        {
            is_init.store(false);
            {
                const auto& tlx = a.lefttop[0];
                const auto& rbx = a.rightbottom[0];
                const auto& tly = a.lefttop[1];
                const auto& rby = a.rightbottom[1];

                // mm/pix 转 pix/mm    
                float resolution_x = a.axis[0] ? 1.0f / a.resolution[0] : -1.0f / a.resolution[0];
                float resolution_y = a.axis[1] ? 1.0f / a.resolution[1] : -1.0f / a.resolution[1];
                if (resolution_x == 0 || resolution_y == 0)
                {
                    return 1;
                }

                // mm * (pix/mm) = pix
                int size_width = static_cast<int>(std::floor(abs((float)(tlx - rbx) * resolution_x)));
                int size_height = static_cast<int>(std::floor(abs((float)(tly - rby) * resolution_y)));
                if (size_width <= 0 || size_height <= 0)
                {
                    return 2;
                }

                size[0] = size_width;
                size[1] = size_height;

                // 左上角坐标
                float origin_x = a.axis[0] ? std::min(tlx, rbx) : std::max(tlx, rbx);
                float origin_y = a.axis[1] ? std::min(tly, rby) : std::max(tly, rby);

                // 构造转换矩阵
                Eigen::Matrix3f m;
                m << resolution_x, 0.0f, origin_x * -resolution_x,
                    0.0f, resolution_y, origin_y * -resolution_y,
                    0.0f, 0.0f, 1.0f;
                matrix = m;
            }
            is_init.store(true);
            return 0;
        }
        /**
         * @brief  获取像素区域尺寸.
         *
         * @fun    GetSize
         * @param  width
         * @param  height
         * @return 正常返回0,否则返回错误码
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        int GetSize(int& width, int& height)
        {
            if (!is_init)
            {
                width = 0;
                height = 0;
                return 1;
            }
            width = size[0];
            height = size[1];
            return 0;
        }
        /**
         * @brief  像素坐标转物理坐标.
         *
         * @fun    PixelToPhysical
         * @param  ix x(pix)
         * @param  iy y(pix)
         * @param  ox x(mm)
         * @param  oy y(mm)
         * @return 正常返回0,否则返回错误码
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        int PixelToPhysical(const int& ix, const int& iy, float& ox, float& oy)
        {
            if (!is_init)
            {
                ox = (float)ix;
                oy = (float)iy;
                return 1;
            }
            Eigen::Vector3f res = matrix.inverse() * Eigen::Vector3f((float)ix, (float)iy, 1);
            ox = (float)res(0);
            oy = (float)res(1);
            return 0;
        }
        /**
         * @brief  物理坐标转像素坐标.
         *
         * @fun    PhysicalToPixel
         * @param  ix x(mm)
         * @param  iy y(mm)
         * @param  ox x(pix)
         * @param  oy y(pix)
         * @return 正常返回0,否则返回错误码
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        int PhysicalToPixel(const float& ix, const float& iy, int& ox, int& oy)
        {
            if (!is_init)
            {
                ox = static_cast<int>(ix);
                oy = static_cast<int>(iy);
                return 1;
            }
            Eigen::Vector3f res = matrix * Eigen::Vector3f(ix, iy, 1);
            ox = static_cast<int>(std::round(res(0)));
            oy = static_cast<int>(std::round(res(1)));
            return 0;
        }
        /**
         * @brief  全局坐标转相对坐标.
         * @func   GlobalToLocal
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  reference_x 相对坐标系原点在全局坐标系的位置x
         * @param  reference_y 相对坐标系原点在全局坐标系的位置y
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        template<typename T>
        static void GlobalToLocal(const T& ix, const T& iy, T& ox, T& oy, const T& reference_x, const T& reference_y)
        {
            ox = ix - reference_x;
            oy = iy - reference_y;
        }
        /**
         * @brief  相对坐标转全局坐标.
         * @func   LocalToGlobal
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  reference_x 相对坐标系原点在全局坐标系的位置x
         * @param  reference_y 相对坐标系原点在全局坐标系的位置y
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        template<typename T>
        static void LocalToGlobal(const T& ix, const T& iy, T& ox, T& oy, const T& reference_x, const T& reference_y)
        {
            ox = ix + reference_x;
            oy = iy + reference_y;
        }
        /**
         * @brief  像素矩形中心坐标转左上角坐标.
         * @func   PixelRectCenterToTopLeft
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  w 矩形宽
         * @param  h 矩形高
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        static void PixelRectCenterToTopLeft(const int& ix, const int& iy, int& ox, int& oy, const int& w, const int& h)
        {
            ox = ix - static_cast<int>(std::round(w * 0.5));
            oy = iy - static_cast<int>(std::round(h * 0.5));
        }

        /**
         * @brief  像素坐标转FOV相对坐标.
         * @func   PixelToFOV
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  fovw FOV中心点坐标(mm)
         * @param  fovh FOV中心点坐标(mm)
         * @param  fovw FOV宽(pix)
         * @param  fovh FOV高(pix)
         * @return 正常返回0,否则返回错误码
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        int PixelToFOV(const int& ix, const int& iy, int& ox, int& oy, const float& fovx, const float& fovy, const int& fovw, const int& fovh)
        {
            int fov_pixel_x, fov_pixel_y;
            if (auto state = PhysicalToPixel(fovx, fovy, fov_pixel_x, fov_pixel_y); state != 0)
            {
                return state;
            }
            PixelRectCenterToTopLeft(fov_pixel_x, fov_pixel_y, fov_pixel_x, fov_pixel_y, fovw, fovh);
            GlobalToLocal(ix, iy, ox, oy, fov_pixel_x, fov_pixel_y);
            return 0;
        }

        /**
         * @brief  垂直镜像.
         * @fun    VerticalMirror
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  centerx
         * @param  centery
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        template<typename T>
        static void VerticalMirror(const T& ix, const T& iy, T& ox, T& oy, const T& centerx, const T& centery)
        {
            ox = centerx + centerx - ix;
            oy = iy;
        }

        /**
         * @brief  水平镜像.
         * @fun    HorizontalMirror
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  centerx
         * @param  centery
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        template<typename T>
        static void HorizontalMirror(const T& ix, const T& iy, T& ox, T& oy, const T& centerx, const T& centery)
        {
            oy = centery + centery - iy;
            ox = ix;
        }

        /**
         * @brief  顺时针旋转.
         * @fun    Rotate
         * @param  ix 输入x
         * @param  iy 输入y
         * @param  ox 输出x
         * @param  oy 输出y
         * @param  centerx 旋转中心x
         * @param  centery 旋转中心y
         * @param  angle 角度(°)
         * @return 不会出错,所以没有返回值
         *
         * @date   2024.08.06
         * <AUTHOR>
         */
        template<typename T>
        static void Rotate(const T& ix, const T& iy, T& ox, T& oy, const T& centerx, const T& centery, const float& angle)
        {
            double rad = A_DEG_TO_RAD(angle);
            double new_x = (ix - centerx) * cos(rad) - (iy - centery) * sin(rad) + centerx;
            double new_y = (ix - centerx) * sin(rad) + (iy - centery) * cos(rad) + centery;
            ox = static_cast<T>(new_x);
            oy = static_cast<T>(new_y);
        }
    private:
        CoordinateTransform(const CoordinateTransform&) = delete;
        CoordinateTransform& operator=(const CoordinateTransform&) = delete;
        CoordinateTransform(CoordinateTransform&&) = delete;
        CoordinateTransform& operator=(CoordinateTransform&&) = delete;

        int size[2];
        std::atomic_bool is_init = false;
        Eigen::Matrix3f matrix;
    };
    using CoordinateTransformPtr = std::shared_ptr<CoordinateTransform>;
}
#endif //! COORDINATETRANSFORM_HPP