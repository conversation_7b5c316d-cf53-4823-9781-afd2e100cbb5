/*****************************************************************
 * @file   onlinedebugmodel.h
 * @brief  在线调试model，用于处理在线调试的数据
 * @details
 * <AUTHOR>
 * @date 2025.3.10
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.3.10          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef BEE2F69C_D71A_483F_AB63_6294C9BE8839
#define BEE2F69C_D71A_483F_AB63_6294C9BE8839
//STD
//Custom
//Third
namespace jrsaoi
{
    class OnLineDebugModel:public ModelBase
    {
        public:
            OnLineDebugModel(const std::string& name );
            ~OnLineDebugModel();
            int  Update(const jrsdata::ViewParamBasePtr& param_)override;
            int  Save(const jrsdata::ViewParamBasePtr& param_)override;
        private:
             //! Fun
             //! Member
            jrsdata::OnlineDebugViewParamPtr online_debug_model_param;
            std::atomic<bool> is_waitting_debug_info; /**< 当前板子调试是否结束，结束后才会继续接受新的板子数据*/

    };
    using OnLineDebugModelPtr = std::shared_ptr<OnLineDebugModel>;
}
#endif /* BEE2F69C_D71A_483F_AB63_6294C9BE8839 */
