/*********************************************************************
 * @brief  渲染器画笔,主要用于生成常用图形的绘制数据.
 *
 * @file   painter.h
 *
 * @date   2024.05.02
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef PAINTER_0502_H
#define PAINTER_0502_H
#include "renderconstants.hpp"
#include <vector>

class Renderer;
class QPainter;

class Painter
{
public:
    /**
     * @brief 构造函数。
     * @param r 渲染器指针，用于绘制操作。
     */
    Painter(Renderer* r);
    ~Painter();

    /**
     * @brief 绘制纹理。
     * @param texture 纹理对象指针。
     */
    void DrawTexture(Texture2D* texture);

    /**
     * @brief 绘制一条线段。
     * @param p1 线段起点。
     * @param p2 线段终点。
     * @param color 线段颜色。
     * @param thickness 线段粗细，默认为1.0。
     */
    void DrawLine(const Vec2& p1, const Vec2& p2, const Color& color, float thickness = 1.0f);

    /**
     * @brief 绘制多个连续的线段。
     * @param points 线段的点数组。
     * @param point_count 点的数量。
     * @param color 线段颜色。
     * @param closed 是否闭合形成多边形。
     * @param thickness 线段粗细，默认为1.0。
     */
    void DrawLines(const Vec2* points, int point_count, const Color& color, bool closed, float thickness = 1.0f);

    /**
     * @brief 绘制多个连续的线段（使用std::vector容器）。
     * @param points 线段的点数组（vector容器）。
     * @param color 线段颜色。
     * @param closed 是否闭合形成多边形。
     * @param thickness 线段粗细，默认为1.0。
     */
    void DrawLines(const std::vector<Vec2>& points, const Color& color, bool closed, float thickness = 1.0f);

    /**
     * @brief 绘制多个连续的线段，不使用抗锯齿。
     * @note closed 参数未起效。
     * @param points 线段的点数组（vector容器）。
     * @param color 线段颜色。
     * @param closed 是否闭合形成多边形。 TODO 未起效
     */
    void DrawPolyline(const std::vector<Vec2>& points, const Color& color, bool closed);

    /**
     * @brief 绘制矩形。
     * @param tl 左上角点。
     * @param br 右下角点。
     * @param color 矩形颜色。
     * @param rounding 矩形的圆角半径，默认为0。
     * @param thickness 边框厚度，默认为1.0。
     */
    void DrawRect(const Vec2& tl, const Vec2& br, const Color& color, float rounding = 0, float thickness = 1.0f);

    /**
     * @brief 绘制网格。
     * @param blockSize 网格块大小。
     * @param regionWidth 区域宽度。
     * @param regionHeight 区域高度。
     * @param color 网格颜色。
     * @param thickness 线条厚度。
     */
    void DrawGrid(int blockSize, int regionWidth, int regionHeight, const Color& color, float thickness);

    /**
     * @brief 绘制圆。
     * @param center 圆心坐标。
     * @param radius 圆半径。
     * @param color 圆的颜色。
     * @param segments 圆的分段数，默认为12。
     * @param thickness 线条厚度，默认为1.0。
     * @param vPath 可选参数，存储圆的路径点。
     */
    void DrawCircle(const Vec2& center, float radius, const Color& color, int segments = 12, float thickness = 1.0f,
        std::vector<Vec2>* vPath = nullptr);

    /**
     * @brief 绘制旋转椭圆。
     * @param center 椭圆的中心点。
     * @param major_axis 椭圆的长轴。
     * @param minor_axis 椭圆的短轴。
     * @param angle 椭圆的旋转角度（以角度为单位）。
     * @param color 椭圆的颜色。
     * @param segments 椭圆的分段数，默认为12。
     * @param thickness 线条厚度，默认为1.0。
     * @param vPath 可选参数，存储椭圆的路径点。
     */
    void DrawRotatedEllipse(const Vec2& center, float major_axis, float minor_axis, double angle, const Color& color,
        int segments = 12, float thickness = 1.0f, std::vector<Vec2>* vPath = nullptr);

    /**
     * @brief 绘制多个点。
     * @param points 点的坐标数组。
     * @param color 点的颜色。
     * @param point_size 点的大小。
     */
    void DrawPoints(const std::vector<Vec2>& points, const Color& color, float point_size);

    /**
     * @enum TextAlignment
     * @brief 文本对齐方式的枚举。
     */
    enum TextAlignment
    {
        TEXT_ALIGN_LEFT_BUTTOM = 0, /**< 左下对齐。 */
        TEXT_ALIGN_CENTER = 1,      /**< 居中对齐。 */
    };

    /**
     * @brief 绘制文本。
     * @param text 文本内容。
     * @param x 文本的X坐标。
     * @param y 文本的Y坐标。
     * @param color 文本颜色。
     * @param pen_width 画笔粗细。
     * @param alignmode 文本对齐方式，默认为居中对齐。
     * @param font_size 字体大小，默认为-1使用默认字体大小。
     * @param font_name 字体名称，可选。
     */
    void PainterText(const char* text, int x, int y, const Color& color, float pen_width, int alignmode = TextAlignment::TEXT_ALIGN_CENTER, int font_size = -1, const char* font_name = nullptr);

    /**
     * @brief 使用QPainter绘制文本。
     * @param painter QPainter对象。
     * @param text 文本内容。
     * @param x 文本的X坐标。
     * @param y 文本的Y坐标。
     * @param color 文本颜色。
     * @param pen_width 画笔粗细。
     * @param alignmode 文本对齐方式。
     * @param font_size 字体大小，默认为-1使用默认字体大小。
     * @param font_name 字体名称，可选。
     */
    void PainterText(QPainter* painter, const char* text, int x, int y, const Color& color, float pen_width, int alignmode, int font_size = -1, const char* font_name = nullptr);

    /**
     * @brief 设置是否启用抗锯齿。
     * @param state 是否启用抗锯齿的布尔值。
     */
    inline void SetAntiAlaised(bool state) { use_anti_aliasing = state; }

private:
    void PathLineTo(const Vec2& pos);

    void PathArcTo(const Vec2& center, float radius, float min, float max, int segments = 10);

    void PathArcToFast(const Vec2& center, float radius, int a_min_of_12, int a_max_of_12);

    void PathRect(const Vec2& tl, const Vec2& br, float rounding = 0.0f);

    void PrimRect(const Vec2& tl, const Vec2& br, const Color& color);

    void DrawPolylineAA(const Color& color, bool closed, float thickness);

    void DrawConvexPolyFilled(const Color& color);

    void PathClear();

private:
    Renderer* renderer; ///< 渲染器

    bool use_anti_aliasing;        ///< 抗锯齿
    std::vector<Vec2> paths;       ///< 路径点

    static uint32 texture_indices[6];                 ///< 纹理索引-常量
    std::vector<uint32> geometry_indices;             ///< 几何索引
    SpriteVertexFormat texture_vertices[4];           ///< 纹理顶点
    std::vector<ColorVertexFormat> geometry_vertices; ///< 几何顶点
};

#endif // !PAINTER_0502_H