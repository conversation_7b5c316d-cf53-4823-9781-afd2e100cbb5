project(algorithmengine)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)

add_compile_definitions(
    JRS_AOI_PLUGIN_EXPORTS
    )
include_directories(
    include
)


# 文件打包
set(manager_src

    src/algorithmenginemanager.cpp
)
set_source_files_properties(
    src/algorithmenginemanager.cpp
    PROPERTIES COMPILE_FLAGS "/bigobj"
)
set(manager_heard

    include/algorithmenginemanager.h
)

set(engine_src

    src/algofactory.cpp
    src/loadalgomanager.cpp
    src/algoexecute.cpp
    src/algoparamparse.cpp

)
set(engine_head
    
    include/algofactory.h
    include/loadalgomanager.h
    include/algoexecute.h
    include/algoparamparse.h

)
# 算法界面基类
set(algoview_base
    src/operatorviewbase.cpp
    include/operatorviewbase.h


)



source_group("manager/src" FILES ${manager_src})
source_group("manager/head" FILES ${manager_heard})
source_group("engine/src" FILES ${engine_src}) 
source_group("engine/head" FILES ${engine_head})
source_group("alogviewbase/viewbase" FILES ${algoview_base})
add_library(${PROJECT_NAME} SHARED
            ${manager_src}
            ${manager_heard} 
            ${engine_src}
            ${engine_head}
            ${algoview_base}
            ${JRS_VERSIONINFO_RC}

            )
#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)
target_include_directories(${PROJECT_NAME} PUBLIC 
        ${DIR_PROJECT_CURRENT}/src/core/common/include
        ${DIR_PROJECT_CURRENT}thirdparty/json/include
        #${DIR_PROJECT_CURRENT}/thirdparty/iguana
        ${DIR_PROJECT_CURRENT}/src/parametermanager/define/colorparam
        ${DIR_PROJECT_CURRENT}/src/parametermanager/define/algoexecuteparam
        ${DIR_PROJECT_CURRENT}/src/parametermanager/define/image
        ${DIR_PROJECT_CURRENT}/src/parametermanager/define/algobase

        #TODO:delete所有算法统一导入注册
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/position/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/ocv/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/mark/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/barcode/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/bridge/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/baseplane/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/heightmeasure/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/block/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/solderchip/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/ocr/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/markshape/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/positionshape/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/location3d/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/lead/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/polaranti/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/edge/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/fpcpadposition/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/padlocation/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/positioncontour/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/nickelalignment/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/deeplearndeduction/include
        ${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/algodatacollect/include
		${DIR_PROJECT_CURRENT}/thirdparty/algo/algoplugin/halcontest/include
        ${DIR_PROJECT_CURRENT}/thirdparty/cereal/include



)

target_link_directories(${PROJECT_NAME} PRIVATE
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<CONFIG:Release>:${OPENCV_RELEASE_DIR}>
)
target_link_libraries(${PROJECT_NAME}
                        Qt5::Core
                        Qt5::Widgets
                        Qt5::Gui
                        core    
                        #opencv
                        $<$<CONFIG:Debug>:opencv_world4100d>
                        $<$<CONFIG:Release>:opencv_world4100>
                        #ylt
                        
)
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
