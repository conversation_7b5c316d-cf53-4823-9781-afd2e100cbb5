﻿#include "tableviewmodel.h"

void TableViewModel::SetDataModel(std::vector<ShowTableParamBasePtr> vector_data)
{
    beginResetModel();
    m_vec_data = vector_data;
    show_subboards.clear();
    show_part_numbers.clear();
    show_devices.clear();
    for (auto show_data : m_vec_data)
    {
        if (show_data->table_name.compare(T_SHOW_BOARD) == 0)
        {
            type = 1;
            show_subboards.push_back(std::dynamic_pointer_cast<SubBoardDataStruct>(show_data));
        }
        else if (show_data->table_name.compare(T_SHOW_PART_NUMBER) == 0)
        {
            type = 2;
            show_part_numbers.push_back(std::dynamic_pointer_cast<PartNumberDataStruct>(show_data));
        }
        else if (show_data->table_name.compare(T_SHOW_DEVICE) == 0)
        {
            type = 3;
            show_devices.push_back(std::dynamic_pointer_cast<DeviceDataStruct>(show_data));
        }
    }
    UpdateHeaders();
    endResetModel();
}

void TableViewModel::GetDataModel(std::vector<ShowTableParamBasePtr>& vector_data)
{
    vector_data = m_vec_data;
}

QStringList TableViewModel::GetHeaderLabels()
{
    return m_header_list;
}

bool TableViewModel::CheckOverValue(int row, int column) const
{
    // 检查列是否超出表头范围
    if (column > m_header_list.size())
    {
        return true;
    }

    // 根据数据类型检查行是否超出范围
    switch (type)
    {
    case 1: // 子板类型
        return row >= show_subboards.size();
    case 2: // 料号类型
        return row >= show_part_numbers.size();
    case 3: // 元件类型
        return row >= show_devices.size();
    default:
        return false; // 默认情况下，认为未超出范围
    }
}

QVariant TableViewModel::BackgroundColor(int row) const
{
    switch (type)
    {
    case 1:
        return result_color_map.at(show_subboards.at(row)->m_result_state);
    case 2:
        return result_color_map.at(show_part_numbers.at(row)->m_result_state);
    case 3:
        return result_color_map.at(show_devices.at(row)->m_result_state);
    }
    return QVariant();
}

QVariant TableViewModel::ItemData(int row, int column) const
{
    if (column == 0)
    {
        return int(row + 1);
    }
    switch (type)
    {
    case 1:
        return show_subboards.at(row)->GetStringList().at(column).c_str();
    case 2:
        return show_part_numbers.at(row)->GetStringList().at(column).c_str();
    case 3:
        return show_devices.at(row)->GetStringList().at(column).c_str();
    }
    return QVariant();
}

void TableViewModel::InitData()
{
    type = 0;
    m_vec_data.clear();
    m_header_list.clear();
    show_subboards.clear();
    show_part_numbers.clear();
    show_devices.clear();
    result_color_map.insert(std::make_pair(RESULT_STATE::RESULT_NONE, QVariant(QBrush(Qt::white))));
    result_color_map.insert(std::make_pair(RESULT_STATE::RESULT_NG, QVariant(QBrush(Qt::red))));
    result_color_map.insert(std::make_pair(RESULT_STATE::RESULT_OK, QVariant(QBrush(Qt::green))));
    result_color_map.insert(std::make_pair(RESULT_STATE::RESULT_UNTESTED, QVariant(QBrush(Qt::darkGray))));
}

void TableViewModel::UpdateHeaders()
{
    m_header_list.clear();
    switch (type)
    {
    case 1:
        m_header_list.append(QString::fromWCharArray(L"序号"));
        m_header_list.append(QString::fromWCharArray(L"子板名"));
        m_header_list.append(QString::fromWCharArray(L"角度"));
        m_header_list.append(QString::fromWCharArray(L"子板号"));
        break;
    case 2:
        m_header_list.append(QString::fromWCharArray(L"序号"));
        m_header_list.append(QString::fromWCharArray(L"料号"));
        m_header_list.append(QString::fromWCharArray(L"跟随料号"));
        m_header_list.append(QString::fromWCharArray(L"元件数"));
        break;
    case 3:
        m_header_list.append(QString::fromWCharArray(L"序号"));
        m_header_list.append(QString::fromWCharArray(L"名称"));
        m_header_list.append(QString::fromWCharArray(L"X"));
        m_header_list.append(QString::fromWCharArray(L"Y"));
        m_header_list.append(QString::fromWCharArray(L"角度"));
        m_header_list.append(QString::fromWCharArray(L"子板号"));
        break;
    }
}

TableViewModel::TableViewModel(QObject* parent)
    : QStandardItemModel(parent)
{
    InitData();
}

TableViewModel::~TableViewModel()
{
    delete& m_vec_data;//检测结果数据
    delete& m_header_list;//标题栏数据
}

QVariant TableViewModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        if (role == Qt::DisplayRole)
        {
            if (section < m_header_list.size())
            {
                return m_header_list.at(section);
            }
        }
    }
    return QVariant();
}

QModelIndex TableViewModel::index(int row, int column, const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return createIndex(row, column);
}

QModelIndex TableViewModel::parent(const QModelIndex& child) const
{
    Q_UNUSED(child);
    return QModelIndex();
}

int TableViewModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return int(m_vec_data.size());
}

int TableViewModel::columnCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return int(m_header_list.size());
}

QVariant TableViewModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || CheckOverValue(index.row(), index.column()))
    {
        return QVariant();
    }
    switch (role)
    {
    case Qt::TextAlignmentRole:
        return Qt::AlignCenter;
    case Qt::BackgroundRole:
        return BackgroundColor(index.row());
    case Qt::DisplayRole:
        return ItemData(index.row(), index.column());
    }
    return QVariant();
}