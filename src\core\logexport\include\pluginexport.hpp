/*****************************************************************//**
 * @file   pluginexport.hpp
 * @brief  导出宏定义
 * @details    
 * <AUTHOR>
 * @date 2024.1.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.18         <td>V1.0              <td>YYZhang      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __PLUGINEXPORT_HPP__
#define __PLUGINEXPORT_HPP__

#ifndef JRS_AOI_PLUGIN_API
#ifdef _WIN32
#if defined(JRS_AOI_PLUGIN_EXPORTS)
#define JRS_AOI_PLUGIN_API __declspec(dllexport)
#else
#define JRS_AOI_PLUGIN_API __declspec(dllimport)
#endif
#else
#ifndef __stdcall
#define __stdcall
#endif

#ifndef JRS_AOI_PLUGIN_API
#define  JRS_AOI_PLUGIN_API
#endif
#endif

#endif

#endif // !__PLUGINEXPORT_HPP__
