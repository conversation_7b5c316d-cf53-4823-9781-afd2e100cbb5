/*****************************************************************//**
 * @file   track.h
 * @brief  轨道调试模块
 * @details
 * <AUTHOR>
 * @date 2024.8.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.22         <td>V1.0              <td>zhaokunlong      <td><td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef TRACKDEBUG_H
#define TRACKDEBUG_H
 //prebuild
#include "pch.h"
// QT
//#include <QWidget>
#include <QMutex>
#include <QDialog>
#include <QVBoxLayout>
#pragma warning(push, 3)
#include "ui_trackdebug.h"
#pragma warning(pop)
 //CUSTOM
//#include <viewparam.hpp>
#include <algorithm>

namespace Ui {
    class TrackDebug;
}

class TrackDebug : public QWidget
{
    Q_OBJECT
public:
    explicit TrackDebug(QWidget* parent = nullptr);
    ~TrackDebug();
public slots:
    /**
     * @fun SlotPushButtonTrigger
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SlotPushButtonTrigger();
    /**
     * @fun UpdateView
     * @brief 更新界面参数
     * @param ptr
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateView(const jrsdata::OperateViewParamPtr ptr);
signals:
    /**
     * @fun SigMotionDebugTrigger
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigMotionDebugTrigger(jrsdata::OperateViewParamPtr operateparam);
private:
    /**
     * @fun InitView
     * @brief 初始化界面
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitView();
    /**
     * @fun InitConnect
     * @brief 初始化槽函数链接
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitConnect();
    /**
     * @fun UpdateCylinder
     * @brief 更新气缸信号显示
     * @param widget
     * @param state
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateCylinder(QLabel* widget, bool state);
    /**
     * @fun CreateJogParam
     * @brief 构建JOG运动参数
     * @param axis
     * @param flag
     * @param direction
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    jrsdata::MoveParam CreateJogParam(int axis, bool flag, int direction);
private:
    Ui::TrackDebug* ui;
    jrsdata::OperateViewParamPtr track_control_view_ptr;
    QMutex track_mutex;
    std::vector<jrsdata::TRACK_NUMBER> vec_track_numbers;
};
#endif // TRACKDEBUG_H
