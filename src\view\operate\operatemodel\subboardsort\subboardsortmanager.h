/*****************************************************************
 * @file   subboardsortmanager.h
 * @brief   子板排序管理
 * @details
 * <AUTHOR>
 * @date 2025.6.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.19          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "subboardsortbase.h"
 //Third
namespace jrsdata {
    class ProjectDataProcess;
    struct SubBoard;
}
struct ImplData;
namespace subboardsort
{
    class SubboardSortManager
    {
    public:
        SubboardSortManager();
        ~SubboardSortManager();
        /**
         * @fun GetSubboardSort
         * @brief subboard sort
         * @param project_data_process_
         * @param sort_type_
         * @return
         * <AUTHOR>
         * @date 2025.6.19
         */
        int SubboardSort(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_data_process_, jrsdata::SubboardSortParam sort_type_);
        /**
         * @fun GetSortedSubboards
         * @brief  Get the sorted subboards;
         * @return sorted subboards
         * <AUTHOR>
         * @date 2025.6.23
         */
        std::vector<jrsdata::SubBoard> GetSortedSubboards() const;
        /**
         * @fun UpdateSortedSubboardToProject
         * @brief  Update the sorted subboards to project
         * <AUTHOR>
         * @date 2025.6.23
         */
        void UpdateSortedSubboardToProject(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_data_process_);
        /**
         * @fun ClearSortedSubboards
         * @brief
         * <AUTHOR>
         * @date 2025.6.23
         */
        void ClearSortedSubboards() const;
    private:
        ImplData* _impl_data;
    };
} // namespace subboardsort
