#ifndef IMAGETABLEMODEL_H
#define IMAGETABLEMODEL_H
//QT
#include <QAbstractTableModel>
#include <QImage>
#include <QPixmap>
#include <QMap>

class ImageTableModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    ImageTableModel(QObject* parent = nullptr) : QAbstractTableModel(parent)
    {
        m_images.clear();
        //m_cols = 2;
    }
    int rowCount(const QModelIndex& parent = QModelIndex()) const override {
        Q_UNUSED(parent);
        //if (m_cols > 0)
        //{
        //    if ((int(m_images.size()) % m_cols) > 0)
        //    {
        //        return int(m_images.size()) / m_cols + 1;
        //    }
        //    return int(m_images.size()) / m_cols;
        //}
        return m_images.size();
    }
    int columnCount(const QModelIndex& parent = QModelIndex()) const override {
        Q_UNUSED(parent);
        return 1;
        //return m_cols;
    }
    QPixmap MatToQPixmap(const cv::Mat& mat) {
        cv::Mat rgbMat;
        cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB); // 转换为 RGB
        QImage image(rgbMat.data, int(rgbMat.cols), int(rgbMat.rows), int(rgbMat.step[0]), QImage::Format_RGB888);
        return QPixmap::fromImage(image);
    }
    QVariant data(const QModelIndex& index, int role) const override {
        try
        {
            if (!index.isValid())
            {
                return QVariant();
            }

            if (role == Qt::DecorationRole)
            {
                //int idx = index.row() * m_cols + index.column();
                int idx = index.row();
                QVector<int> image_keys = m_images.keys().toVector();
                if (idx >= 0 && idx < image_keys.size())
                {
                    QPixmap pixmap = m_images[image_keys[idx]];
                    return pixmap;
                }
            }
        }
        catch (...)
        {
        }
        return QVariant();
    }
    bool setData(const QModelIndex& index, const QVariant& value, int role = Qt::EditRole) override {
        if (index.isValid() && role == Qt::DecorationRole && index.column() == 0) {
            // 更新图片
            auto keys = m_images.keys();
            if (keys.size() > index.row())
            {
                m_images[keys[index.row()]] = value.value<QPixmap>();
                emit dataChanged(index, index); // 发出数据更改信号
            }
            return true;
        }
        return false;
    }
    // 删除元素
    bool removeRows(int row, int count, const QModelIndex& parent = QModelIndex()) override {
        if (row < 0 || row >= m_images.size() || count <= 0 || (row + count) > m_images.size())
            return false;

        beginRemoveRows(parent, row, row + count - 1); // 开始删除行
        auto keys = m_images.keys();
        if (keys.size() > row)
        {
            m_images.remove(keys[row]);
        }
        endRemoveRows(); // 结束删除行
        return true;
    }

    void CleanData()
    {
        beginResetModel();
        m_images.clear();
        endResetModel();
    }
    void AddMatData(int temp_id, cv::Mat result)
    {
        if (result.empty())
        {
            return;
        }

        beginInsertRows(QModelIndex(), m_images.size(), m_images.size());
        m_images.insert(temp_id, MatToQPixmap(result));
        endInsertRows();
    }
    void DeleteMatData(int temp_id)
    {
        //beginResetModel();
        m_images.remove(temp_id);
        //endResetModel();
    }
    QMap<int, QPixmap> GetModelData()
    {
        return m_images;
    }
    //void SetCols(int cols)
    //{
    //    m_cols = cols;
    //}
private:
    QMap<int, QPixmap> m_images;
    //int m_cols;
};
#endif