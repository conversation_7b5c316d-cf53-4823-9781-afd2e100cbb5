/*****************************************************************//**
 * @file   pubsubevent.h
 * @brief  AOI主界面 界面事件订阅发布
 * @details
 * <AUTHOR>
 * @date 2024.7.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.18         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSPUBSUBEVENT_H__
#define __JRSPUBSUBEVENT_H__
//prebuild
#include "pch.h"
#include "coreapplication.h"
#include "modulehandle.h"
//#include "viewparam.hpp"
namespace jrsaoi
{

    class PubSubEvent
    {
        public:
            PubSubEvent ();
            ~PubSubEvent ();
            int AdveristerEvent (jrscore::ModuleHandlePtr& moudle_handle_ptr,const std::string topic_name);
            int SubscriberEvent ( jrscore::ModuleHandlePtr& moudle_handle_ptr, const std::string& topic_name, const std::string& sub_name, jrscore::CallBackFunction<jrsdata::ViewParamBasePtr> cb );
            int PublishEvent ( jrscore::ModuleHandlePtr& module_handle_ptr, const std::string& topic_name, const std::string& sub_name, const jrsdata::ViewParamBasePtr& param);
        private:
    };
    using PubSubEventPtr = std::shared_ptr<PubSubEvent>;
}

#endif // !__JRSPUBSUBEVENT_H__
