//Custom
#include "motionstatus.h"

namespace jrsdevice
{
    MotionStatus::MotionStatus(std::shared_ptr<Motion>& motion)
        :operate_view_param_ptr(std::make_shared<jrsdata::DeviceParam>())
        , motion_ptr(motion)
        , exit_(false)
    {
        motion_thread = std::thread(&MotionStatus::MotionStatusHander, this);
    }

    MotionStatus::~MotionStatus()
    {

    }

    jrsdata::TrackInput MotionStatus::ConstructTrackInput(int trackIndex)
    {
        jrsdata::TrackInput track_input;
        if (!track_setting.empty() && track_setting.is_array() && track_setting.size() > trackIndex)
        {
            auto cur = track_setting[trackIndex];
            if (cur.contains("TrackInput") && operate_view_param_ptr->motion_param.motion_status.motioncard_input.size() > 0 && operate_view_param_ptr->motion_param.motion_status.iocard_input.size() > 0)
            {
                auto TrackInput = cur["TrackInput"];
                // 进料
                auto import_ = GetObjFromName(TrackInput, "import");
                if (import_ != nullptr)
                {
                    int CardsIndex = import_["CardsIndex"].get<int>();
                    track_input.import_index = import_["index"].get<int>();
                    if (track_input.import_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.import_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.import_index - 1);
                        }
                        else
                        {
                            track_input.import_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.import_index - 1);
                        }
                    }
                }

                // 减速
                auto slow = GetObjFromName(TrackInput, "slow");
                if (slow != nullptr)
                {
                    int CardsIndex = slow["CardsIndex"].get<int>();
                    track_input.slow_index = slow["index"].get<int>();
                    if (track_input.slow_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.slow_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.slow_index - 1);
                        }
                        else
                        {
                            track_input.slow_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.slow_index - 1);
                        }
                    }
                }

                // 停止
                auto stop = GetObjFromName(TrackInput, "stop");
                if (stop != nullptr)
                {
                    int CardsIndex = stop["CardsIndex"].get<int>();
                    track_input.stop_index = stop["index"].get<int>();
                    if (track_input.stop_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.stop_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.stop_index - 1);
                        }
                        else
                        {
                            track_input.stop_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.stop_index - 1);
                        }
                    }
                }

                // 出料
                auto export_ = GetObjFromName(TrackInput, "export");
                if (export_ != nullptr)
                {
                    int CardsIndex = export_["CardsIndex"].get<int>();
                    track_input.export_index = export_["index"].get<int>();
                    if (track_input.export_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.export_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.export_index - 1);
                        }
                        else
                        {
                            track_input.export_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.export_index - 1);
                        }
                    }
                }

                // 夹板气缸传感器1
                auto cylinder1_sensor = GetObjFromName(TrackInput, "cylinder1_sensor");
                if (cylinder1_sensor != nullptr)
                {
                    int CardsIndex = cylinder1_sensor["CardsIndex"].get<int>();
                    track_input.cylinder1_sensor__index = cylinder1_sensor["index"].get<int>();
                    if (track_input.cylinder1_sensor__index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.cylinder1_sensor_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.cylinder1_sensor__index - 1);
                        }
                        else
                        {
                            track_input.cylinder1_sensor_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.cylinder1_sensor__index - 1);
                        }
                    }
                }

                // 夹板气缸传感器2
                auto cylinder2_sensor = GetObjFromName(TrackInput, "cylinder2_sensor");
                if (cylinder2_sensor != nullptr)
                {
                    int CardsIndex = cylinder2_sensor["CardsIndex"].get<int>();
                    track_input.cylinder2_sensor__index = cylinder2_sensor["index"].get<int>();
                    if (track_input.cylinder2_sensor__index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.cylinder2_sensor_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.cylinder2_sensor__index - 1);
                        }
                        else
                        {
                            track_input.cylinder2_sensor_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.cylinder2_sensor__index - 1);
                        }
                    }
                }

                // 左挡板气缸传感器
                auto shield1_sensor = GetObjFromName(TrackInput, "left_shield_sensor");
                if (shield1_sensor != nullptr)
                {
                    int CardsIndex = shield1_sensor["CardsIndex"].get<int>();
                    track_input.shield1_sensor__index = shield1_sensor["index"].get<int>();
                    if (track_input.shield1_sensor__index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.shield1_sensor_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.shield1_sensor__index - 1);
                        }
                        else
                        {
                            track_input.shield1_sensor_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.shield1_sensor__index - 1);
                        }
                    }
                }

                // 右挡板气缸传感器
                auto shield2_sensor = GetObjFromName(TrackInput, "right_shield_sensor");
                if (shield2_sensor != nullptr)
                {
                    int CardsIndex = shield2_sensor["CardsIndex"].get<int>();
                    track_input.shield2_sensor__index = shield2_sensor["index"].get<int>();
                    if (track_input.shield2_sensor__index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.shield2_sensor_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.shield2_sensor__index - 1);
                        }
                        else
                        {
                            track_input.shield2_sensor_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.shield2_sensor__index - 1);
                        }
                    }
                }

                // 前机出料
                auto previous_ask = GetObjFromName(TrackInput, "previous_ask");
                if (previous_ask != nullptr)
                {
                    int CardsIndex = previous_ask["CardsIndex"].get<int>();
                    track_input.previous_ask_index = previous_ask["index"].get<int>();
                    if (track_input.previous_ask_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.previous_ask_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.previous_ask_index - 1);
                        }
                        else
                        {
                            track_input.previous_ask_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.previous_ask_index - 1);
                        }
                    }
                }

                // 后机要料
                auto next_ask = GetObjFromName(TrackInput, "next_ask");
                if (next_ask != nullptr)
                {
                    int CardsIndex = next_ask["CardsIndex"].get<int>();
                    track_input.next_ask_index = next_ask["index"].get<int>();
                    if (track_input.next_ask_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_input.next_ask_status = operate_view_param_ptr->motion_param.motion_status.motioncard_input.at((long long)track_input.next_ask_index - 1);
                        }
                        else
                        {
                            track_input.next_ask_status = operate_view_param_ptr->motion_param.motion_status.iocard_input.at((long long)track_input.next_ask_index - 1);
                        }
                    }
                }
            }
        }
        return track_input;
    }

    jrsdata::TrackOutput MotionStatus::ContructTrackOutput(int trackIndex)
    {
        jrsdata::TrackOutput track_output;
        if (!track_setting.empty() && track_setting.is_array() && track_setting.size() > trackIndex)
        {
            auto cur = track_setting[trackIndex];
            if (cur.contains("TrackOutput") && operate_view_param_ptr->motion_param.motion_status.motioncard_output.size() > 0 && operate_view_param_ptr->motion_param.motion_status.iocard_output.size() > 0)
            {
                auto TrackOutput = cur["TrackOutput"];
                // 夹板气缸1
                auto cylinder1 = GetObjFromName(TrackOutput, "cylinder1");
                if (cylinder1 != nullptr)
                {
                    int CardsIndex = cylinder1["CardsIndex"].get<int>();
                    track_output.cylinder1_index = cylinder1["index"].get<int>();
                    if (track_output.cylinder1_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.cylinder1_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.cylinder1_index - 1);
                        }
                        else
                        {
                            track_output.cylinder1_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.cylinder1_index - 1);
                        }
                    }
                }

                // 夹板气缸2(有的设备有,有的没有)
                auto cylinder2 = GetObjFromName(TrackOutput, "cylinder2");
                if (cylinder2 != nullptr)
                {
                    int CardsIndex = cylinder2["CardsIndex"].get<int>();
                    track_output.cylinder2_index = cylinder2["index"].get<int>();
                    if (track_output.cylinder2_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.cylinder1_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.cylinder2_index - 1);
                        }
                        else
                        {
                            track_output.cylinder1_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.cylinder2_index - 1);
                        }
                    }
                }

                // 挡板气缸1(有的设备有,有的没有)
                auto shield1 = GetObjFromName(TrackOutput, "shield1");
                if (shield1 != nullptr)
                {
                    int CardsIndex = shield1["CardsIndex"].get<int>();
                    track_output.shield1_index = shield1["index"].get<int>();
                    if (track_output.shield1_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.shield1_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.shield1_index - 1);
                        }
                        else
                        {
                            track_output.shield1_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.shield1_index - 1);
                        }
                    }
                }

                // 挡板气缸2(有的设备有,有的没有)
                auto shield2 = GetObjFromName(TrackOutput, "shield2");
                if (shield2 != nullptr)
                {
                    int CardsIndex = shield2["CardsIndex"].get<int>();
                    track_output.shield2_index = shield2["index"].get<int>();
                    if (track_output.shield2_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.shield2_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.shield2_index - 1);
                        }
                        else
                        {
                            track_output.shield2_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.shield2_index - 1);
                        }
                    }
                }

                // 向前机要板
                auto previous_output = GetObjFromName(TrackOutput, "previous_output");
                if (previous_output != nullptr)
                {
                    int CardsIndex = previous_output["CardsIndex"].get<int>();
                    track_output.previous_output_index = previous_output["index"].get<int>();
                    if (track_output.previous_output_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.previous_output_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.previous_output_index - 1);
                        }
                        else
                        {
                            track_output.previous_output_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.previous_output_index - 1);
                        }
                    }
                }

                // 向后机输出有板
                auto next_output = GetObjFromName(TrackOutput, "next_output");
                if (next_output != nullptr)
                {
                    int CardsIndex = next_output["CardsIndex"].get<int>();
                    track_output.next_output_index = next_output["index"].get<int>();
                    if (track_output.next_output_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.next_output_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.next_output_index - 1);
                        }
                        else
                        {
                            track_output.next_output_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.next_output_index - 1);
                        }
                    }
                }

                // 轨道1后序OK
                auto detect_ok = GetObjFromName(TrackOutput, "detect_ok");
                if (detect_ok != nullptr)
                {
                    int CardsIndex = detect_ok["CardsIndex"].get<int>();
                    track_output.detect_ok_index = detect_ok["index"].get<int>();
                    if (track_output.detect_ok_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.detect_ok_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.detect_ok_index - 1);
                        }
                        else
                        {
                            track_output.detect_ok_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.detect_ok_index - 1);
                        }
                    }
                }

                // 轨道1后序NG
                auto detect_ng = GetObjFromName(TrackOutput, "detect_ng");
                if (detect_ng != nullptr)
                {
                    int CardsIndex = detect_ng["CardsIndex"].get<int>();
                    track_output.detect_ng_index = detect_ng["index"].get<int>();
                    if (track_output.detect_ng_index != -1)
                    {
                        if (CardsIndex == 0)
                        {
                            track_output.detect_ng_status = operate_view_param_ptr->motion_param.motion_status.motioncard_output.at((long long)track_output.detect_ng_index - 1);
                        }
                        else
                        {
                            track_output.detect_ng_status = operate_view_param_ptr->motion_param.motion_status.iocard_output.at((long long)track_output.detect_ng_index - 1);
                        }
                    }
                }
            }
        }
        return track_output;
    }
    JSON MotionStatus::GetObjFromName(JSON Obj, std::string name)
    {
        if (Obj.is_array())
        {
            for (int i = 0; i < (int)Obj.size(); i++)
            {
                if (Obj[i]["name"].get<std::string>() == name)
                {
                    return Obj[i];
                }
            }
        }
        return JSON();
    }
}