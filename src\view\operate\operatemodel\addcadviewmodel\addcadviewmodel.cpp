#include "addcadviewmodel.h"

void AddCadViewModel::SetDataModel(std::vector<std::vector<std::string>>& vector_data, int column_count)
{
    beginResetModel();
    m_vec_data = vector_data;
    m_column_count = column_count;
    endResetModel();
}
void AddCadViewModel::GetDataModel(std::vector<std::vector<std::string>>& vector_data)
{
    vector_data = m_vec_data;
}
QStringList AddCadViewModel::GetHeaderLabels()
{
    m_header_list.clear();
    for (int i = 0; i < m_column_count; i++)
    {
        m_header_list.push_back(QString::number(i + 1));
    }
    return m_header_list;
}
AddCadViewModel::AddCadViewModel(QObject* parent)
    : QStandardItemModel(parent)
{
    m_vec_data.clear();
    m_header_list.clear();
    m_column_count = 0;
    for (int i = 0; i < 8; i++)
    {
        m_header_list.push_back(QString::number(i + 1));
    }
}
AddCadViewModel::~AddCadViewModel()
{
}
QVariant AddCadViewModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal)
    {
        if (role == Qt::DisplayRole)
        {
            if (section < m_header_list.size())
            {
                return m_header_list.at(section);
            }
        }
    }
    return QVariant();
}
QModelIndex AddCadViewModel::index(int row, int column, const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return createIndex(row, column);
}
QModelIndex AddCadViewModel::parent(const QModelIndex& child) const
{
    Q_UNUSED(child);
    return QModelIndex();
}
int AddCadViewModel::rowCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return int(m_vec_data.size());
}
int AddCadViewModel::columnCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent);
    return m_header_list.count();
}
QVariant AddCadViewModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.row() >= m_vec_data.size())
    {
        return QVariant();
    }
    if (role == Qt::TextAlignmentRole)
    {
        return Qt::AlignCenter;
    }
    if (role == Qt::DisplayRole || role == Qt::EditRole)
    {
        if (index.row() < m_vec_data.size())
        {
            if (index.column() < m_vec_data.at(index.row()).size())
            {
                QString value = m_vec_data.at(index.row()).at(index.column()).c_str();
                return value;
            }
        }
    }
    return QVariant();
}