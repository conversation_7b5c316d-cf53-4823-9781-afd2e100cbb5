#include <fstream>
#include <iostream>

#pragma warning(push, 1)
#include <QHBoxLayout>
#include <QComboBox>
#include <QWidget>

//#include "coloralgo.h"
#include "colorwheelctrl.h"
#include "wigtcolorwheel.h"
#include "wigtdouthresholdhistm.h"
#pragma warning(pop)



using std::string;
using std::vector;
using cv::Mat;

ColorWheelCtrl::ColorWheelCtrl(QWidget *parent)
: QWidget{parent}
{
	color_wheel = new WigtColorWheel(this);
    double_thr_histogram = new VChanelWheel(this);

	//color_algo = new ColorAlgo();
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->addWidget(color_wheel);
    layout->addWidget(double_thr_histogram);
    this->setLayout(layout);
	  	 
    connect(color_wheel, &WigtColorWheel::ColorMapChanged, this, &ColorWheelCtrl::UpdateHSThresholdVal);  
    connect(double_thr_histogram, &VChanelWheel::UpateThreValue, this, &ColorWheelCtrl::UpdateVThresholdVal);  
}

void ColorWheelCtrl::RestoreColorWheel()
{
    if (color_wheel)
    {
        color_wheel->RestoreColorWheel();
    }
}


void ColorWheelCtrl::ApplyThresholdValsToTestImg()
{
    if (m_has_test_img)
    {
        bool use_v_thr = threshold_val_.type & 1;
        bool use_hs_thr = threshold_val_.type & 2;

        m_output_img = cv::Mat(m_v_img.size(), CV_8UC1, cv::Scalar(255));
        if (use_v_thr && (!m_v_img.empty()))
        {
            cv::inRange(m_v_img, cv::Scalar(threshold_val_.v_low), cv::Scalar(threshold_val_.v_high), m_output_img);
        }

        if (use_hs_thr && (!m_h_img.empty()) && (!m_s_img.empty()))
        {
            if (threshold_val_.type & 2)
            {
                int rows = m_output_img.rows;
                int cols = m_output_img.cols;
                int index = 0;
                for (int r = 0; r < rows; r++)
                {
                    for (int c = 0; c < cols; c++)
                    {
                        if (m_output_img.data[index] == 255)
                        {
                            m_output_img.data[index] = hs_table[m_h_img.data[index]][m_s_img.data[index]];
                        }
                        index++;
                    }
                }
            }
        }
    }
    else
    {
        m_output_img = cv::Mat();
    }
}

void ColorWheelCtrl::UpdateVThresholdVal(const int& left_Val, const int& right_val)
{
    threshold_val_.v_low = left_Val;
    threshold_val_.v_high = right_val;
    threshold_val_.type = threshold_val_.type | 1;
	emit UpdateHsvParams(threshold_val_);

   /* ApplyThresholdValsToTestImg();

    if (threshold_val_changed_func)
    {
        threshold_val_changed_func(threshold_val, cv::Mat());
    }*/
}

void ColorWheelCtrl::UpdateHSThresholdVal(const cv::Mat& colorMap, const std::array<QPointF, 6>& axisPoints)
{
    memcpy(&hs_table[0], colorMap.data, colorMap.total() * colorMap.elemSize());

    for (size_t i = 0; i < 6; i++)
    {
        threshold_val_.pts[i] = std::pair<double,double>{axisPoints[i].x(),axisPoints[i].y()};
    }
    threshold_val_.Set1DHsTableFrom2DArray(hs_table);

    threshold_val_.type = threshold_val_.type | 2;


    emit UpdateHsvParams(threshold_val_);

    /*ApplyThresholdValsToTestImg();

    if (threshold_val_changed_func)
    {
        threshold_val_changed_func(threshold_val, cv::Mat());
    } */
}



int ColorWheelCtrl::SetTestImage(const cv::Mat &img)
{
    if (img.empty())
    {
        m_h_img = cv::Mat();
        m_s_img = cv::Mat();
        m_v_img = cv::Mat();
        m_has_test_img = false;
        return -1;
    }
    else if (img.channels() == 1)
    {
        img.copyTo(m_v_img);
        m_h_img = cv::Mat();
        m_s_img = cv::Mat();
    }
    else if (img.channels()==3)
    {
        cv::Mat hsv;
        cv::cvtColor(img, hsv, cv::COLOR_BGR2HSV);
        std::vector<cv::Mat> split_images;
        cv::split(hsv, split_images);
        m_h_img = split_images[0];
        m_s_img = split_images[1];
        m_v_img = split_images[2];
    }

    int histSize = 256;
    float range[] = { 0, 256 };
    const float* histRange = { range };
    bool uniform = true, accumulate = false;
    cv::Mat hist;

    cv::calcHist(&m_v_img, 1, 0, cv::Mat(), hist, 1, &histSize, &histRange, uniform, accumulate);
    hist = hist / double(m_v_img.total());

    std::vector<float> vec(hist.total());
    std::memcpy(vec.data(), hist.data, hist.total() * hist.elemSize());

    double_thr_histogram->SetGrayHistValueSlot(vec);
    
    m_has_test_img = true;

    return 0;
}

int ColorWheelCtrl::SetThresholdValChangedCallback(ThresholdValChangedFunc func)
{
    threshold_val_changed_func = func;
    return 0;
}

int ColorWheelCtrl::Threshold(const cv::Mat& input_img, 
    const ColorWheelThreshVal& threshold_val_c, 
    cv::Mat& output_img)
{
    threshold_val_ = threshold_val_c;
    if (input_img.empty())
    {
        return -1;
    }

    output_img = cv::Mat(input_img.size(), CV_8UC1, cv::Scalar(255));

    if (input_img.channels() == 1)
    {
        // V通道二值化
        if (threshold_val_.type & 1)
        {
            cv::inRange(input_img, cv::Scalar(threshold_val_.v_low), cv::Scalar(threshold_val_.v_high), output_img);
        }
    }
    else if(input_img.channels() == 3)
    {
        cv::Mat hsv;
        cv::cvtColor(input_img, hsv, cv::COLOR_BGR2HSV);
        std::vector<cv::Mat> split_images;
        cv::split(hsv, split_images);
        Mat h = split_images[0];
        Mat s = split_images[1];
        Mat v = split_images[2];

        // V通道二值化
        if (threshold_val_.type & 1)
        {
            cv::inRange(v, cv::Scalar(threshold_val_.v_low), cv::Scalar(threshold_val_.v_high), output_img);
        }

        // HS二值化
        if (threshold_val_.type & 2)
        {
            int rows = output_img.rows;
            int cols = output_img.cols;
            int index = 0;
            for(int r = 0; r<rows;r++)
            {
                for(int c = 0; c < cols; c++)
                {
                    if (output_img.data[index] == 255)
                    {
                        output_img.data[index] = hs_table[h.data[index]][s.data[index]];
                    }                    
                    index++;
                }
            }      
        }
    }
    else
    {
        return -2;
    }
    return 0;
}

int ColorWheelCtrl::SetCurrentValState( ColorWheelThreshVal& params)
{
    threshold_val_ = params;
	double_thr_histogram->SetThreValue(threshold_val_.v_low, threshold_val_.v_high);
	std::array<QPointF, 6> color_pt;
    bool judge_all_zeros = true;
	for (const auto& pt : threshold_val_.pts) 
    {
        if (pt.first != 0.0 || pt.second != 0.0)
        {
            judge_all_zeros =  false;
        }
    }
    if (judge_all_zeros) return 0;
    for (size_t i = 0; i < threshold_val_.pts.size(); ++i) 
    {
        color_pt[i] = QPointF(threshold_val_.pts[i].first, threshold_val_.pts[i].second);
    }
	color_wheel->SetAxisPos(color_pt);
    return 0;
}
