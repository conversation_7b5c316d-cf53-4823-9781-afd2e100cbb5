#include "imagetabledelegate.h"
#include <QSize>
#include <QRect>
#include <QStyle>

ImageTableDelegate::ImageTableDelegate(QObject* parent)
    : QStyledItemDelegate(parent)
{
}

void ImageTableDelegate::paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const
{
    // 保存当前绘图状态
    painter->save();

    // 获取项的矩形区域
    QRect rect = option.rect;

    // 获取颜色索引
    int color_index = GetColorIndex(index);

    // 获取颜色
    auto color = color_map.find(color_index);
    if (color != color_map.end())
    {
        painter->fillRect(rect, color->second);
    }

    // 绘制选中或悬浮状态的边框
    DrawSelectionBorder(painter, option, rect);

    // 调用基类的绘制方法
    QStyledItemDelegate::paint(painter, option, index);

    // 恢复绘图状态
    painter->restore();
}

int ImageTableDelegate::GetColorIndex(const QModelIndex& index) const
{
    if (!index.data(Qt::UserRole + 6).value<bool>()) // 检查是否启用
    {
        return 0; // 返回默认颜色索引
    }

    int execute_state = index.data(Qt::UserRole + 7).value<int>(); // 获取执行状态
    switch (execute_state)
    {
    case 0:
        return 1; // 状态0对应的颜色索引
    case -1:
        return 2; // 状态-1对应的颜色索引
    case 1:
        return 3; // 状态1对应的颜色索引
    default:
        return 0; // 默认返回默认颜色索引
    }
}

void ImageTableDelegate::DrawSelectionBorder(QPainter* painter, const QStyleOptionViewItem& option, const QRect& rect) const
{
    if (option.state & (QStyle::State_Selected | QStyle::State_MouseOver)) // 检查是否选中或悬浮
    {
        QPen pen(Qt::blue, 2, Qt::DashLine); // 设置边框样式
        painter->setPen(pen);
        painter->drawRect(rect.adjusted(1, 1, -1, -1)); // 绘制边框，避免覆盖背景边缘
    }
}