﻿#include "multidetectwindow.h"
#include "ui_multidetectwindow.h"

MultiDetectWindow::MultiDetectWindow(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::MultiDetectWindow)
{
    ui->setupUi(this);
    InitConnection();
}

void MultiDetectWindow::IniAlgorithmView(const std::vector<std::pair<std::string, std::string>>& param_)
{
    param = param_;
    ui->algo_name_list->clear();
    for (size_t i = 0; i < param.size(); i++)
    {
        ui->algo_name_list->addItem(QString::fromStdString(param[i].second));
    }
}

void MultiDetectWindow::InitConnection()
{
    // 绑定itemChanged信号到槽函数
    QObject::connect(ui->location_listWidget, &QListWidget::itemChanged, [this](QListWidgetItem* item) {
        (void)item;
        emit SigAlgoListChange(GetCurSelectParam());
        });
    QObject::connect(ui->body_listWidget, &QListWidget::itemChanged, [this](QListWidgetItem* item) {
        (void)item;
        emit SigAlgoListChange(GetCurSelectParam());
        });
    QObject::connect(ui->pad_listWidget, &QListWidget::itemChanged, [this](QListWidgetItem* item) {
        (void)item;
        emit SigAlgoListChange(GetCurSelectParam());
        });

    // 添加默认算法
    connect(ui->add_type_btn, &QPushButton::clicked, this, [=]() {
        if (ui->component_type->currentIndex() == 0)
        {
            AddCheckBox(ui->location_listWidget, ui->algo_name_list->currentText());
        }
        else if (ui->component_type->currentIndex() == 1)
        {
            AddCheckBox(ui->body_listWidget, ui->algo_name_list->currentText());
        }
        else if (ui->component_type->currentIndex() == 2)
        {
            AddCheckBox(ui->pad_listWidget, ui->algo_name_list->currentText());
        }
        });

    // 删除默认算法
    connect(ui->delete_type_btn, &QPushButton::clicked, this, [=]() {
        if (ui->component_type->currentIndex() == 0)
        {
            QList<QListWidgetItem*> selectedItems = ui->location_listWidget->selectedItems();
            for (int i = 0; i < selectedItems.size(); i++)
            {
                DeleteCheckBox(ui->location_listWidget, selectedItems[i]->text());
            }
        }
        else if (ui->component_type->currentIndex() == 1)
        {
            QList<QListWidgetItem*> selectedItems = ui->body_listWidget->selectedItems();
            for (int i = 0; i < selectedItems.size(); i++)
            {
                DeleteCheckBox(ui->body_listWidget, selectedItems[i]->text());
            }
        }
        else if (ui->component_type->currentIndex() == 2)
        {
            QList<QListWidgetItem*> selectedItems = ui->pad_listWidget->selectedItems();
            for (int i = 0; i < selectedItems.size(); i++)
            {
                DeleteCheckBox(ui->pad_listWidget, selectedItems[i]->text());
            }
        }
        });
    
    
    connect(ui->add_btn, &QPushButton::clicked, this, [=]() {
        // 构建一个jrsdata::OperateViewParam
        auto param = std::make_shared<jrsdata::OperateViewParam>();
        param->event_name = jrsaoi::OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME;
        param->module_name = jrsaoi::OPERATE_MODULE_NAME;
        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        jrsdata::MultiAlgoParam multi_algo_param;
        
        // 添加元件算法名
        auto location_list = GetCheckedNameList(ui->location_listWidget);
        for (int i = 0; i < location_list.size(); i++)
        {
            multi_algo_param.location_algo_names.push_back(GetAlgoName(location_list[i].toStdString()));
        }

        // 添加本体算法名
        auto body_list = GetCheckedNameList(ui->body_listWidget);
        for (int i = 0; i < body_list.size(); i++)
        {
            multi_algo_param.body_algo_names.push_back(GetAlgoName(body_list[i].toStdString()));
        }

        // 添加pad算法名
        auto pad_list = GetCheckedNameList(ui->pad_listWidget);
        for (int i = 0; i < pad_list.size(); i++)
        {
            multi_algo_param.pad_algo_names.push_back(GetAlgoName(pad_list[i].toStdString()));
        }

        param->multi_algo_param = multi_algo_param;
        emit SigUpdate(param);

        this->close();
        });

    // 关闭
    connect(ui->close_btn, &QPushButton::clicked, this, [=]() {
        this->close();
        });
    // 清空选择
    connect(ui->clear_btn, &QPushButton::clicked, this, [=]() {
        SetCheckState(ui->location_listWidget, Qt::CheckState::Unchecked);
        SetCheckState(ui->body_listWidget, Qt::CheckState::Unchecked);
        SetCheckState(ui->pad_listWidget, Qt::CheckState::Unchecked);
        });

}

void MultiDetectWindow::InitView(const std::string& param_)
{
    if (param_ == "")
    {
        InitViewWithDefaultParam();
    }
    else
    {
        InitViewWithParam(param_);
    }
}

void MultiDetectWindow::InitViewWithParam(const std::string& param_)
{
    try
    {
        auto param_obj = JSON::parse(param_);

        // 元件
        ui->location_listWidget->clear();
        if (param_obj.contains("location_algo_list"))
        {
            auto& algo_arr = param_obj["location_algo_list"];
            if (algo_arr.is_array())
            {
                for (size_t i = 0; i < algo_arr.size(); i++)
                {
                    std::string name = "";
                    Qt::CheckState state = Qt::CheckState::Unchecked;
                    if (algo_arr[i].contains("name"))
                    {
                        name = algo_arr[i]["name"].get<std::string>();
                        state = algo_arr[i]["state"].get<bool>() ? Qt::CheckState::Checked : Qt::CheckState::Unchecked;
                    }
                    if (name != "")
                    {
                        std::string display_name = GetAlgoDisplayName(name);
                        if (display_name != "")
                        {
                            AddCheckBox(ui->location_listWidget, QString::fromStdString(display_name), state);
                        }
                    }
                }
            }
        }

        // 本体
        ui->body_listWidget->clear();
        if (param_obj.contains("body_algo_list"))
        {
            auto& algo_arr = param_obj["body_algo_list"];
            if (algo_arr.is_array())
            {
                for (size_t i = 0; i < algo_arr.size(); i++)
                {
                    std::string name = "";
                    Qt::CheckState state = Qt::CheckState::Unchecked;
                    if (algo_arr[i].contains("name"))
                    {
                        name = algo_arr[i]["name"].get<std::string>();
                        state = algo_arr[i]["state"].get<bool>() ? Qt::CheckState::Checked : Qt::CheckState::Unchecked;
                    }
                    if (name != "")
                    {
                        std::string display_name = GetAlgoDisplayName(name);
                        if (display_name != "")
                        {
                            AddCheckBox(ui->body_listWidget, QString::fromStdString(display_name), state);
                        }
                    }
                }
            }
        }

        // PAD
        ui->pad_listWidget->clear();
        if (param_obj.contains("pad_algo_list"))
        {
            auto& algo_arr = param_obj["pad_algo_list"];
            if (algo_arr.is_array())
            {
                for (size_t i = 0; i < algo_arr.size(); i++)
                {
                    std::string name = "";
                    Qt::CheckState state = Qt::CheckState::Unchecked;
                    if (algo_arr[i].contains("name"))
                    {
                        name = algo_arr[i]["name"].get<std::string>();
                        state = algo_arr[i]["state"].get<bool>() ? Qt::CheckState::Checked : Qt::CheckState::Unchecked;
                    }
                    if (name != "")
                    {
                        std::string display_name = GetAlgoDisplayName(name);
                        if (display_name != "")
                        {
                            AddCheckBox(ui->pad_listWidget, QString::fromStdString(display_name), state);
                        }
                    }
                }
            }
        }

    }
    catch (const std::exception&)
    {

    }
}

void MultiDetectWindow::InitViewWithDefaultParam()
{
    // 元件
    AddCheckBox(ui->location_listWidget,QString::fromStdString(GetAlgoDisplayName("PositionOperator")));
    AddCheckBox(ui->location_listWidget, QString::fromStdString(GetAlgoDisplayName("PositionShapeOperator")));
    AddCheckBox(ui->location_listWidget, QString::fromStdString(GetAlgoDisplayName("Location3dOperator")));
    AddCheckBox(ui->location_listWidget, QString::fromStdString(GetAlgoDisplayName("BasePlaneOperator")));

    // 本体
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("PositionOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("PositionShapeOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("Location3dOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("HeightMeasureOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("OcvOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("OcrOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("PolarantiOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("BlockOperator")));
    AddCheckBox(ui->body_listWidget, QString::fromStdString(GetAlgoDisplayName("BarcodeOperator")));

    // PAD
    AddCheckBox(ui->pad_listWidget, QString::fromStdString(GetAlgoDisplayName("LeadOperator")));
    AddCheckBox(ui->pad_listWidget, QString::fromStdString(GetAlgoDisplayName("SolderChipOperator")));
    AddCheckBox(ui->pad_listWidget, QString::fromStdString(GetAlgoDisplayName("BridgeOperator")));
    AddCheckBox(ui->pad_listWidget, QString::fromStdString(GetAlgoDisplayName("BlockOperator")));
}

std::string MultiDetectWindow::GetAlgoDisplayName(const std::string name)
{
    for (size_t i = 0; i < param.size(); i++)
    {
        if (param[i].first == name) 
        {
            return param[i].second;
        }
    }
    return std::string();
}

std::string MultiDetectWindow::GetAlgoName(std::string displayname)
{
    for (size_t i = 0; i < param.size(); i++)
    {
        if (param[i].second == displayname)
        {
            return param[i].first;
        }
    }
    return std::string();
}

void MultiDetectWindow::AddCheckBox(QListWidget* list, QString name, Qt::CheckState state)
{
    // 检查是否存在
    bool found = false;
    for (int i = 0; i < list->count(); ++i) 
    {
        QListWidgetItem* item = list->item(i);
        if (item->text() == name) 
        {
            found = true;
            break;
        }
    }

    if (!found)
    {
        // 添加带复选框的项
        QListWidgetItem* item1 = new QListWidgetItem(name);
        item1->setCheckState(state);
        list->addItem(item1);
    }
}

void MultiDetectWindow::DeleteCheckBox(QListWidget* list, QString name)
{
    for (int i = 0; i < list->count(); ) 
    {  
        // 注意：这里不使用 ++i，因为删除后索引会变化
        QListWidgetItem* item = list->item(i);
        if (item->text() == name) 
        {
            // 删除项并释放内存
            delete list->takeItem(i);
            // 注意：这里不增加i，因为删除后下一项会移动到当前索引

            emit SigAlgoListChange(GetCurSelectParam());
        }
        else 
        {
            ++i; // 只有不删除时才增加索引
        }
    }
}

QStringList MultiDetectWindow::GetCheckedNameList(QListWidget* list)
{
    QStringList list_arr;
    for (int i = 0; i < list->count(); i++)
    {
        QListWidgetItem* item = list->item(i);
        if (item->checkState() == Qt::CheckState::Checked)
        {
            list_arr.append(item->text());
        }
    }
    return list_arr;
}

JSON MultiDetectWindow::GetAlgoSelectPackge(QListWidget* list)
{
    JSON list_arr;
    for (int i = 0; i < list->count(); i++)
    {
        QListWidgetItem* item = list->item(i);
        JSON temp;
        temp["name"] = GetAlgoName(item->text().toStdString()); // 保存算法name，而不是中文名
        temp["state"] = item->checkState() == Qt::CheckState::Checked;
        list_arr.push_back(temp);
    }
    return list_arr;
}

void MultiDetectWindow::SetCheckState(QListWidget* list, Qt::CheckState state)
{
    for (int i = 0; i < list->count(); i++)
    {
        QListWidgetItem* item = list->item(i);
        item->setCheckState(state);
    }
}

std::string MultiDetectWindow::GetCurSelectParam()
{
    JSON param_obj;

    // 元件
    param_obj["location_algo_list"] = GetAlgoSelectPackge(ui->location_listWidget);

    // 本体
    param_obj["body_algo_list"] = GetAlgoSelectPackge(ui->body_listWidget);

    // PAD
    param_obj["pad_algo_list"] = GetAlgoSelectPackge(ui->pad_listWidget);

    return param_obj.dump();
}

MultiDetectWindow::~MultiDetectWindow()
{
    delete ui;
}
