#include "commandmanager.h"
#include "commandabstract.hpp"
#include "log.h"
// #include <sstream>
CommandManager::CommandManager()
    : m_current_cmd_idx(0), isruning(false)
{
}

CommandManager::~CommandManager()
{
    ClearCommand();
}

void CommandManager::Undo()
{
    printInfo("");
    if (isruning)
        return;
    /*左移,然后触发命令*/
    if (m_current_cmd_idx <= 0)
        return;
    isruning = true;
    m_current_cmd_idx--;
    m_cmds[m_current_cmd_idx]->revoke();
    isruning = false;
    printInfo((std::stringstream() << "m_current_cmd_idx:" << m_current_cmd_idx).str().c_str());
}

void CommandManager::Redo()
{
    printInfo("");
    if (isruning)
        return;
    // std::lock_guard<std::mutex> lock(mutex);
    /*右移,然后触发命令*/
    if (m_current_cmd_idx >= (int)m_cmds.size())
        return;
    isruning = true;
    m_cmds[m_current_cmd_idx]->excute();
    m_current_cmd_idx++;
    isruning = false;
    printInfo((std::stringstream() << "m_current_cmd_idx:" << m_current_cmd_idx).str().c_str());
}

void CommandManager::AddCommand(CommandAbstract* cmd)
{
    if (isruning)
        return;
    //printInfo(std::stringstream() << "add command success: current cmd num:" << m_cmds.size());
    // std::lock_guard<std::mutex> lock(mutex);
    m_cmds.insert(m_cmds.begin() + m_current_cmd_idx, cmd);
    m_cmds.erase(m_cmds.begin() + m_current_cmd_idx + 1, m_cmds.end()); // 清除插入位置之后的元素
    m_current_cmd_idx++;
}

void CommandManager::ClearCommand()
{
    if (isruning)
        return;
    // std::lock_guard<std::mutex> lock(mutex);
    for (auto& cmd : m_cmds)
    {
        if (!cmd)
            continue;
        delete cmd;
        cmd = nullptr;
    }
    std::vector<CommandAbstract*>().swap(m_cmds);
    m_current_cmd_idx = 0;
    isruning = false;
}