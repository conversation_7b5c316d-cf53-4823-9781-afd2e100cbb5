﻿project(render)

Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#启用自动MOC（Meta-Object Compiler）功能。在使用Qt的信号槽机制时，需要使用MOC来生成额外的代码。开启此选项后，CMake会自动查找并运行MOC来处理相关的源文件。
set(CMAKE_AUTOMOC ON)
#启用自动UI编译功能。在使用Qt的UI文件（.ui）时，需要将其编译为C++代码。开启此选项后，CMake会自动查找并运行UIC来处理相关的UI文件。
set(CMAKE_AUTOUIC ON)
#启用自动RCC（Resource Compiler）功能。在使用Qt的资源文件（.qrc）时，需要将其编译为二进制资源文件。开启此选项后，CMake会自动查找并运行RCC来处理相关的资源文件。
set(CMAKE_AUTORCC ON)

set(MAKE_DLL 1) #设置当前编译成dll还是exe
set(CMAKE_DEBUG_POSTFIX "d") # 设置调试版本后缀

# 內存泄漏检测工具
add_compile_definitions(
    # VLD_FORCE_ENABLE
)

# 设置宏 
add_compile_definitions(
    RENDERER_2D_EXPORTS
    THUMBNAIL_NAVIGATION_EXPORTS
)

#将ui路径添加到搜索路径中，否则找不到生成的ui_**.h文件
list(APPEND CMAKE_AUTOUIC_SEARCH_PATHS 
    "control/ui"
)

# 设置头文件目录
set(INTERFACE_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/interface/include)
set(ENGINE_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/engine/include)
set(ENGINE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/engine/)

set(CONTROL_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/control/include)
set(GRAPHICS_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/graphics/include)
set(CORE_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/core/include)
set(PAD_HEADER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/interface/pad/include)

# 设置源文件目录
set(PAD_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/interface/pad/src)
set(INTERFACE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/interface/src)
set(ENGINE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/engine/src)
set(CONTROL_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/control/src)
set(GRAPHICS_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/graphics/src)
set(CORE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/core/src)

# 设置UI目录
set(CONTROL_UI_DIR ${CMAKE_CURRENT_SOURCE_DIR}/control/ui)

# # 获取指定目录下的所有xx文件
# file(GLOB INTERFACE_HEADER_FILES "${INTERFACE_HEADER_DIR}/*.h" "${INTERFACE_HEADER_DIR}/*.hpp")
# file(GLOB INTERFACE_SOURCE_FILES "${INTERFACE_SOURCE_DIR}/*.cpp")

# file(GLOB ENGINE_HEADER_FILES "${ENGINE_HEADER_DIR}/*.h" "${ENGINE_HEADER_DIR}/*.hpp")
# file(GLOB ENGINE_SOURCE_FILES "${ENGINE_SOURCE_DIR}/*.cpp")

# file(GLOB CONTROL_HEADER_FILES "${CONTROL_HEADER_DIR}/*.h" "${CONTROL_HEADER_DIR}/*.hpp")
# file(GLOB CONTROL_SOURCE_FILES "${CONTROL_SOURCE_DIR}/*.cpp")
# file(GLOB CONTROL_UI_FILES "${CONTROL_UI_DIR}/*.ui")

# file(GLOB GRAPHICS_HEADER_FILES "${GRAPHICS_HEADER_DIR}/*.h" "${GRAPHICS_HEADER_DIR}/*.hpp")
# file(GLOB GRAPHICS_SOURCE_FILES "${GRAPHICS_SOURCE_DIR}/*.cpp")

# file(GLOB CORE_HEADER_FILES "${CORE_HEADER_DIR}/*.h" "${CORE_HEADER_DIR}/*.hpp")
# file(GLOB CORE_SOURCE_FILES "${CORE_SOURCE_DIR}/*.cpp")

# 设置头文件
set(INTERFACE_HEADER_FILES
    ${INTERFACE_HEADER_DIR}/algodefine.hpp
    ${INTERFACE_HEADER_DIR}/controlconstants.hpp
    ${INTERFACE_HEADER_DIR}/controlpointconstants.hpp
    ${INTERFACE_HEADER_DIR}/customgraphicscontrolpoint.h
    ${INTERFACE_HEADER_DIR}/customgraphicsobject.h
    ${INTERFACE_HEADER_DIR}/graphicsabstract.hpp
    ${INTERFACE_HEADER_DIR}/graphicsapi.hpp
    ${INTERFACE_HEADER_DIR}/graphicsattributes.hpp
    ${INTERFACE_HEADER_DIR}/graphicsconstants.hpp
    ${INTERFACE_HEADER_DIR}/graphicsid.h
    ${INTERFACE_HEADER_DIR}/graphicsobject.h
    ${INTERFACE_HEADER_DIR}/graphicstools.h
    ${INTERFACE_HEADER_DIR}/renderer2d.h
    ${INTERFACE_HEADER_DIR}/rvec.hpp
    ${INTERFACE_HEADER_DIR}/transientobjectcontrol.hpp
)

set(ENGINE_HEADER_FILES
    ${ENGINE_HEADER_DIR}/engineconstants.hpp
    ${ENGINE_HEADER_DIR}/painter.h
    ${ENGINE_HEADER_DIR}/shaderprogram.h
    ${ENGINE_HEADER_DIR}/renderabstract.hpp
    ${ENGINE_HEADER_DIR}/renderbackground.h
    ${ENGINE_HEADER_DIR}/renderconstants.hpp
    ${ENGINE_HEADER_DIR}/renderer.h
    ${ENGINE_HEADER_DIR}/renderfore.h
    ${ENGINE_HEADER_DIR}/rendertest.h
    ${ENGINE_HEADER_DIR}/rendertexture.h
    ${ENGINE_HEADER_DIR}/shadersourceconstants.hpp
    ${ENGINE_HEADER_DIR}/visualcameraabstract.h
    ${ENGINE_HEADER_DIR}/visualcameratopdown.h
    ${ENGINE_HEADER_DIR}/windowinterface.h
)
set(ENGINE_IMAGE_CPU_RENDER_HEADER_FILES
    ${ENGINE_DIR}imagerender/include/viewergraphicsviewimpl.h
)
set(ENGINE_IMAGE_CPU_RENDER_SRC_FILES
    ${ENGINE_DIR}imagerender/src/viewergraphicsviewimpl.cpp
)
set(CONTROL_HEADER_FILES
    ${CONTROL_HEADER_DIR}/commandabstract.hpp
    ${CONTROL_HEADER_DIR}/commandmanager.h
    ${CONTROL_HEADER_DIR}/commandobject.h
    ${CONTROL_HEADER_DIR}/glwindow.h
    ${CONTROL_HEADER_DIR}/graphicsmanager.h
    ${CONTROL_HEADER_DIR}/renderer2dmanager.h
    ${CONTROL_HEADER_DIR}/renderer2dwidget.h
    ${CONTROL_HEADER_DIR}/ropenglfunctions.hpp
    ${CONTROL_HEADER_DIR}/rulerwidget.h
    ${CONTROL_HEADER_DIR}/statemanager.h
    ${CONTROL_HEADER_DIR}/thumbnailnavigation.h
    ${CONTROL_HEADER_DIR}/thumbnailnavigationwidget.h
    ${CONTROL_HEADER_DIR}/windowsignalemitter.h

)

set(CONTROL_HEADER_MOUSE_FILES
${CONTROL_HEADER_DIR}/mousestate/mousestatecontrol.hpp
${CONTROL_HEADER_DIR}/mousestate/customcursormanager.h
#${CONTROL_HEADER_DIR}/mousestate/customcursortype.hpp
)
set(CONTROL_SRC_MOUSE_FILES
${CONTROL_SOURCE_DIR}/mousestate/mousestatecontrol.cpp
${CONTROL_SOURCE_DIR}/mousestate/customcursormanager.cpp
)

set(GRAPHICS_HEADER_FILES
    ${GRAPHICS_HEADER_DIR}/controlpointabstract.h
    ${GRAPHICS_HEADER_DIR}/controlpointfactory.h
    ${GRAPHICS_HEADER_DIR}/controlpointobject.h
    ${GRAPHICS_HEADER_DIR}/graphicsalgorithm.h
    ${GRAPHICS_HEADER_DIR}/graphicsprocess.h
    ${GRAPHICS_HEADER_DIR}/graphicsserialize.hpp
    ${GRAPHICS_HEADER_DIR}/graphicseventparam.hpp
)

set(CORE_HEADER_FILES
    ${CORE_HEADER_DIR}/debouncer.hpp
    ${CORE_HEADER_DIR}/delegate.hpp
    ${CORE_HEADER_DIR}/log.h
    ${CORE_HEADER_DIR}/qtfileoperation.hpp
    ${CORE_HEADER_DIR}/randomhelper.hpp
    ${CORE_HEADER_DIR}/resourceguard.hpp
    ${CORE_HEADER_DIR}/systemmonitor.hpp
    ${CORE_HEADER_DIR}/tools.hpp
    ${CORE_HEADER_DIR}/graphicsparam.hpp
)

set(PAD_HEADER_FILES
    ${PAD_HEADER_DIR}/padgraphics.h
    ${PAD_HEADER_DIR}/padgroup.h
    ${PAD_HEADER_DIR}/padmanager.h
)


# 设置源文件
set(INTERFACE_SOURCE_FILES
    ${INTERFACE_SOURCE_DIR}/customgraphicscontrolpoint.cpp
    ${INTERFACE_SOURCE_DIR}/customgraphicsobject.cpp
    ${INTERFACE_SOURCE_DIR}/graphicsid.cpp
    ${INTERFACE_SOURCE_DIR}/graphicsobject.cpp
    ${INTERFACE_SOURCE_DIR}/graphicstools.cpp
    ${INTERFACE_SOURCE_DIR}/renderer2d.cpp
)

set(ENGINE_SOURCE_FILES
    ${ENGINE_SOURCE_DIR}/painter.cpp
    ${ENGINE_SOURCE_DIR}/shaderprogram.cpp
    ${ENGINE_SOURCE_DIR}/renderbackground.cpp
    ${ENGINE_SOURCE_DIR}/renderer.cpp
    ${ENGINE_SOURCE_DIR}/renderfore.cpp
    ${ENGINE_SOURCE_DIR}/rendertest.cpp
    ${ENGINE_SOURCE_DIR}/rendertexture.cpp
    ${ENGINE_SOURCE_DIR}/visualcameraabstract.cpp
    ${ENGINE_SOURCE_DIR}/visualcameratopdown.cpp
    ${ENGINE_SOURCE_DIR}/windowinterface.cpp
)

set(CONTROL_SOURCE_FILES
    ${CONTROL_SOURCE_DIR}/commandmanager.cpp
    ${CONTROL_SOURCE_DIR}/commandobject.cpp
    ${CONTROL_SOURCE_DIR}/glwindow.cpp
    ${CONTROL_SOURCE_DIR}/graphicsmanager.cpp
    ${CONTROL_SOURCE_DIR}/renderer2dmanager.cpp
    ${CONTROL_SOURCE_DIR}/renderer2dwidget.cpp
    ${CONTROL_SOURCE_DIR}/rulerwidget.cpp
    ${CONTROL_SOURCE_DIR}/statemanager.cpp
    ${CONTROL_SOURCE_DIR}/thumbnailnavigation.cpp
    ${CONTROL_SOURCE_DIR}/thumbnailnavigationwidget.cpp
    ${CONTROL_SOURCE_DIR}/windowsignalemitter.cpp
)

set(GRAPHICS_SOURCE_FILES
    ${GRAPHICS_SOURCE_DIR}/controlpointabstract.cpp
    ${GRAPHICS_SOURCE_DIR}/controlpointfactory.cpp
    ${GRAPHICS_SOURCE_DIR}/controlpointobject.cpp
    ${GRAPHICS_SOURCE_DIR}/graphicsalgorithm.cpp
    ${GRAPHICS_SOURCE_DIR}/graphicsprocess.cpp
)
set(PAD_SOURCE_FILES
    ${PAD_SOURCE_DIR}/padgraphics.cpp
    ${PAD_SOURCE_DIR}/padgroup.cpp
    ${PAD_SOURCE_DIR}/padmanager.cpp
)


set(CORE_SOURCE_FILES
    ${CORE_SOURCE_DIR}/log.cpp
    ${CORE_SOURCE_DIR}/tools.cpp
)

# 设置UI文件
set(CONTROL_UI_FILES
    ${CONTROL_UI_DIR}/renderer2dwidget.ui
)
set(RESOURCE_FILES
    ${DIR_PROJECT_CURRENT}jrsresource/icon/MainWindowResource.qrc
)
# 文件筛选器
source_group("interface/head" FILES ${INTERFACE_HEADER_FILES})
source_group("interface/src" FILES ${INTERFACE_SOURCE_FILES}) 

source_group("interface/pad/head" FILES ${PAD_HEADER_FILES})
source_group("interface/pad/src" FILES ${PAD_SOURCE_FILES}) 



source_group("engine/head" FILES ${ENGINE_HEADER_FILES})
source_group("engine/src" FILES ${ENGINE_SOURCE_FILES}) 
source_group("engine/image_cpu_render/head" FILES ${ENGINE_IMAGE_CPU_RENDER_HEADER_FILES})
source_group("engine/image_cpu_render/src" FILES ${ENGINE_IMAGE_CPU_RENDER_SRC_FILES}) 

source_group("control/head" FILES ${CONTROL_HEADER_FILES})
source_group("control/src"  FILES ${CONTROL_SOURCE_FILES})

source_group("control/head/mousestate" FILES ${CONTROL_HEADER_MOUSE_FILES})
source_group("control/src/mousestate" FILES ${CONTROL_SRC_MOUSE_FILES}) 

source_group("control/ui" FILES ${CONTROL_UI_FILES}) 

source_group("graphics/head" FILES ${GRAPHICS_HEADER_FILES})
source_group("graphics/src" FILES ${GRAPHICS_SOURCE_FILES}) 

source_group("core/head" FILES ${CORE_HEADER_FILES})
source_group("core/src" FILES ${CORE_SOURCE_FILES}) 

set (PROJECT_SOURCE     
    ${INTERFACE_HEADER_FILES}
    ${INTERFACE_SOURCE_FILES} 
    
    ${GRAPHICS_HEADER_FILES}
    ${GRAPHICS_SOURCE_FILES}
    ${ENGINE_HEADER_FILES}
    ${ENGINE_SOURCE_FILES}

    ${ENGINE_IMAGE_CPU_RENDER_HEADER_FILES}
    ${ENGINE_IMAGE_CPU_RENDER_SRC_FILES}

    ${CORE_HEADER_FILES}
    ${CORE_SOURCE_FILES}




    ${CONTROL_HEADER_FILES}
    ${CONTROL_SOURCE_FILES}

    ${CONTROL_HEADER_MOUSE_FILES}
    ${CONTROL_SRC_MOUSE_FILES}

    ${CONTROL_UI_FILES}
    ${RESOURCE_FILES}
    ${JRS_VERSIONINFO_RC}

    ${PAD_HEADER_FILES}
    ${PAD_SOURCE_FILES}

)

# 添加可执行文件
if(MAKE_DLL)
    # set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${DIR_PROJECT_CURRENT}/bin)
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${DIR_PROJECT_CURRENT}/bin) # lib 位置
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${DIR_PROJECT_CURRENT}/bin) # dll 位置
    add_library(${PROJECT_NAME} SHARED
        ${PROJECT_SOURCE}
    )
else()
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${DIR_PROJECT_CURRENT}/bin)
    add_executable(${PROJECT_NAME} 
        ${PROJECT_SOURCE}
    )
endif()

# 为当前目标以及链接该目标的其他目标设置头文件
target_include_directories(${PROJECT_NAME} PUBLIC 
    ${INTERFACE_HEADER_DIR}
    ${PAD_HEADER_DIR}
    ${CORE_HEADER_DIR}
)
# 为指定的目标设置包含路径
target_include_directories(${PROJECT_NAME} PRIVATE 
    ${ENGINE_DIR}imagerender/include
    ${ENGINE_HEADER_DIR}
    ${CONTROL_HEADER_DIR}
    ${CONTROL_HEADER_DIR}/mousestate
    ${GRAPHICS_HEADER_DIR}
    ${CONTROL_UI_DIR}
    # opencv
    ${OPENCV_INCLUDE_DIR}
    # other
    ${CMAKE_SOURCE_DIR}/thirdparty/cereal/include
    ${CMAKE_SOURCE_DIR}/thirdparty/uuid_v4
    ${CMAKE_SOURCE_DIR}/thirdparty/
    #core
    ${DIR_PROJECT_CURRENT}/src/core/common/include

)

# 为指定的目标添加链接目录
target_link_directories(${PROJECT_NAME} PRIVATE
    # opencv
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<CONFIG:Release>:${OPENCV_RELEASE_DIR}>
    $<$<CONFIG:RelWithDebInfo>:${OPENCV_RELEASE_DIR}>
    # vld
    ${DIR_PROJECT_CURRENT}thirdparty/vld/lib
    # other
)

#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}
    # QT
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui
    # opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<CONFIG:Release>:opencv_world4100>
    $<$<CONFIG:RelWithDebInfo>:opencv_world4100>
    # pdh
    pdh
    # vld
    vld
    # core
    core
)

# set(INSTALL_DIR ${CMAKE_SOURCE_DIR}/install)

#   # 安装目标：安装DLL和LIB文件到指定路径下
# install(TARGETS ${PROJECT_NAME} ${PROJECT_NAME}
#     RUNTIME DESTINATION ${INSTALL_DIR}/bin    # 对应DLL
#     LIBRARY DESTINATION ${INSTALL_DIR}/lib    # 对应共享库
#     ARCHIVE DESTINATION ${INSTALL_DIR}/lib    # 对应静态库
# )

# # 安装头文件到指定路径的include目录
# install(DIRECTORY ${INTERFACE_HEADER_DIR}
#     DESTINATION ${INSTALL_DIR}
#     FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"   # 匹配 .h 和 .hpp 文件
# )

# # 安装头文件到指定路径的include目录
# install(DIRECTORY ${INTERFACE_HEADER_DIR}
#     DESTINATION ${INSTALL_DIR}
#     FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"   # 匹配 .h 和 .hpp 文件
# )

# # 安装头文件到指定路径的include目录
# install(DIRECTORY ${PAD_HEADER_DIR}
#     DESTINATION ${INSTALL_DIR}
#     FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"   # 匹配 .h 和 .hpp 文件
# )
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
