#include "trackdebug.h"

TrackDebug::TrackDebug(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::TrackDebug)
{
    ui->setupUi(this);
    InitView();
    InitConnect();
    track_control_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
    vec_track_numbers = { jrsdata::TRACK_1,jrsdata::TRACK_2,jrsdata::TRACK_3,jrsdata::TRACK_4,jrsdata::TRACK_5,
                    jrsdata::TRACK_6,jrsdata::TRACK_7,jrsdata::TRACK_8,jrsdata::TRACK_9,jrsdata::TRACK_10 };
}
TrackDebug::~TrackDebug()
{
    delete ui;
}
void TrackDebug::SlotPushButtonTrigger()
{
    track_control_view_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
    emit SigMotionDebugTrigger(track_control_view_ptr);
}
void TrackDebug::UpdateView(const jrsdata::OperateViewParamPtr ptr)
{
    jrsdata::TRACK_NUMBER track_id = vec_track_numbers.at(ui->track->currentIndex());
    if (ptr->device_param.motion_param.motion_status.track_status.count(track_id) == 0)
    {
        return;
    }
    // 更新传感器状态(如果没有传感器则更新对应的控制输出)
    jrsdata::TrackInput input = ptr->device_param.motion_param.motion_status.track_status[track_id].track_input;
    jrsdata::TrackOutput output = ptr->device_param.motion_param.motion_status.track_status[track_id].track_output;
    // 顶升气缸
    if (input.cylinder1_sensor__index > 0)
    {
        UpdateCylinder(ui->cylinder_up, input.cylinder1_sensor_status == "1");
    }
    else
    {
        UpdateCylinder(ui->cylinder_up, output.cylinder1_status == "1");
    }
    if (input.cylinder2_sensor__index > 0)
    {
        UpdateCylinder(ui->cylinder_down, input.cylinder2_sensor_status == "1");
    }
    else
    {
        UpdateCylinder(ui->cylinder_down, output.cylinder2_status == "1");
    }
    // 挡板气缸
    if (input.shield1_sensor__index > 0)
    {
        UpdateCylinder(ui->shield_up, input.shield1_sensor_status == "1");
    }
    else
    {
        UpdateCylinder(ui->shield_up, output.shield1_status == "1");
    }
    if (input.shield2_sensor__index > 0)
    {
        UpdateCylinder(ui->shield_down, input.shield2_sensor_status == "1");
    }
    else
    {
        UpdateCylinder(ui->shield_down, output.shield2_status == "1");
    }
}
void TrackDebug::InitView()
{
    // 输入信号
    ui->cylinder_up->setPixmap(QPixmap(":/image/gray.png").scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    ui->cylinder_down->setPixmap(QPixmap(":/image/gray.png").scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    ui->shield_up->setPixmap(QPixmap(":/image/gray.png").scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    ui->shield_down->setPixmap(QPixmap(":/image/gray.png").scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}
void TrackDebug::InitConnect()
{
    // 夹板上升
    QObject::connect(ui->cylinder_on, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::Cylinder;
        track_param.track_state = true;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "CylinderOutput";
        SlotPushButtonTrigger();
        });
    // 夹板下降
    QObject::connect(ui->cylinder_off, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::Cylinder;
        track_param.track_state = false;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "CylinderOutput";
        SlotPushButtonTrigger();
        });
    // 挡板顶升
    QObject::connect(ui->shield_on, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::Shield;
        track_param.track_state = true;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "CylinderOutput";
        SlotPushButtonTrigger();
        });
    // 挡板下降
    QObject::connect(ui->shield_off, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::Shield;
        track_param.track_state = false;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "CylinderOutput";
        SlotPushButtonTrigger();
        });
    // 上料
    QObject::connect(ui->loading, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::Load;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "LoadAndUnLoad";
        SlotPushButtonTrigger();
        });
    // 下料
    QObject::connect(ui->unloading, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::UnLoad;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "LoadAndUnLoad";
        SlotPushButtonTrigger();
        });
    // 上下料启动
    QObject::connect(ui->startdebug, &QPushButton::clicked, this, [=]() {
        QMutexLocker locker(&track_mutex);
        jrsdata::TrackControlParam track_param;
        track_param.track_index = ui->track->currentIndex();
        track_param.track_type = jrsdata::TrackControlType::LoadAndUnLoad;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "LoadAndUnLoad";
        SlotPushButtonTrigger();
        });
    // 正转JOG
    QObject::connect(ui->forward_rotation, &QPushButton::clicked, this, [=]() {
        track_control_view_ptr->device_param.motion_param.motion_move = CreateJogParam(ui->track->currentIndex() + 7, true, 1);
        SlotPushButtonTrigger();
        });
    // 反转JOG
    QObject::connect(ui->reverse_rotation, &QPushButton::clicked, this, [=]() {
        track_control_view_ptr->device_param.motion_param.motion_move = CreateJogParam(ui->track->currentIndex() + 7, true, -1);
        SlotPushButtonTrigger();
        });
    // 停止JOG
    QObject::connect(ui->stop_axis, &QPushButton::clicked, this, [=]() {
        track_control_view_ptr->device_param.motion_param.motion_move = CreateJogParam(ui->track->currentIndex() + 7, false, -1);
        SlotPushButtonTrigger();
        });
}
void TrackDebug::UpdateCylinder(QLabel* widget, bool state)
{
    widget->setPixmap((state ? QPixmap(":/image/green.png") : QPixmap(":/image/gray.png")).scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}
jrsdata::MoveParam TrackDebug::CreateJogParam(int axis, bool flag, int direction)
{
    jrsdata::MoveParam param;
    param.axis_index = axis;
    param.axis_jog = flag;
    param.axis_jog_direction = direction;
    param.axis_speed = 100;
    param.axis_movetype = jrsdata::MoveType::Jog;
    track_control_view_ptr->device_param.event_name = "Jog";
    return param;
}