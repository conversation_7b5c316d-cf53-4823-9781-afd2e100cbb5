﻿#include "render2dcontroller.h"
#include "render2dview.h" // GraphicsEditParam
#include "render2dmodel.h"
//#include "projectoperator.h" // ParamOperator
#include "graphicsobject.h" // RectGraphics
#include "padgraphics.h" // PadGraphics
#include "padgroup.h" // PadGraphicsGroup
#include "graphicsmanager.h" // GraphicsManager

#include "layerconverter.hpp"

#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "coordinatetransformationtool.h"

#pragma warning(pop)
#include "generalimagetool.h" // 裁图工具
#include "baseground.h"
#include "multipleboardsbase.h" /**< 多联板*/
#include "regularmultipleboards.h"/**< 规则多联板 */
#include "iregularmultipleboards.h"/**< 规则多联板 */

#include "graphicsabstract.hpp"

#include "graphicsparam.hpp"
//TODO：合并到一起  HJC 待优化
#include "padoperator.hpp" /**< pad 添加*/
#include "addpadview.h"
#include "cvtools.h"

namespace jrsaoi
{
    using namespace jrsdata;
    using namespace jrstool;

    /**
     * @note
     * 大图默认设置到底层,将当前需要渲染的大图设置到当前渲染图片层
     * 临时性图片，放到临时层
     */
    constexpr const int image_layer_bottom = 0; ///< 图像层级-底层
    constexpr const int image_layer_top = 99;   ///< 图像层级-顶层

    constexpr const int image_layer_temp = 3;   ///< 图像层级-临时层
    constexpr const int image_layer_key = 10;   ///< 图像层级-临时层
    constexpr const int image_layer_current_show = 1; ///< 图像层级-当前渲染图片层

    constexpr const int dynamic_adjust_image_set = 1; ///< 动态调试图集
    constexpr const int default_image_set = 0;        ///< 默认图集



    Render2dController::Render2dController(const std::string& name)
        : ControllerBase(name)
        , render_2d_view(nullptr)
        , _add_pad_view(new AddPadView(nullptr))
        , model(nullptr)
        , _regular_multiple_boards_ptr(std::make_shared<jrsaoi::RegularMultipleBoards>())
        , _irregular_multiple_boards_ptr(std::make_shared<jrsaoi::IrregularMultipleBoards>())
        , _pad_operator_ptr(std::make_shared<jrsaoi::PadOperator>())
        , event_param({})
        , _current_img_set(default_image_set)
        , _is_auto_flow_work(false)
    {
        Init();
    }

    Render2dController::~Render2dController()
    {
        if (_add_pad_view)
        {
            delete _add_pad_view;
            _add_pad_view = nullptr;
        }
    }

    int Render2dController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            RETURN_ERROR(jrscore::RenderError::E_AOI_REMNDER_POINTER_TRANFER_FAILURE, "Render view 更新界面时输入参数为空！");
        }
        if (auto render_view_param = std::dynamic_pointer_cast<jrsdata::RenderViewParam>(param_); render_view_param != nullptr)
        {
            HandlerenderEvent(render_view_param);
        }
        else if (auto render_event_param = std::dynamic_pointer_cast<jrsdata::RenderEventParam>(param_); render_event_param != nullptr)
        {
            Handleviewevent(render_event_param);
        }
        else if (auto algo_event_param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_); algo_event_param != nullptr)
        {
            Handlealgoevent(algo_event_param);
        }
        else if (auto project_event_param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_); project_event_param != nullptr)
        {
            Handleprojectevent(project_event_param);
        }
        else if (auto component_list_view_param = std::dynamic_pointer_cast<jrsdata::ComponentListViewParam>(param_); component_list_view_param != nullptr)
        {
            Handlecomponentlistviewevent(component_list_view_param);
            emit SignalMoveCameraToSelectedGraphics();
        }
        else if (auto online_debug_view_param = std::dynamic_pointer_cast<jrsdata::OnlineDebugViewParam>(param_); online_debug_view_param != nullptr)
        {
            model->Update(param_);  //先刷新工程
            HandleOnlineDebugEvent(online_debug_view_param);
            emit SignalMoveCameraToSelectedGraphics();
        }
        else if (auto operate_param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_); operate_param != nullptr)
        {
            HandleOperateParamEvent(operate_param);
        }
        else if (param_->topic_name == jrsaoi::CONTROL_PANEL_TOPIC_NAME || jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME)  /**<开启 | 关闭自动流程*/
        {
            ControlPanelEventOperate(param_);
        }
        return jrscore::AOI_OK;
    }

    int Render2dController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }

    void Render2dController::SetView(ViewBase* view_param)
    {
        render_2d_view = dynamic_cast<Render2dView*>(view_param);
        if (!render_2d_view)
            return;
        InitCallBackView();
    }

    void Render2dController::SetModel(ModelBasePtr model_param)
    {
        model = std::dynamic_pointer_cast<Render2dModel>(model_param);
        if (!model)
            return;
        InitCallBackModel();
    }

    void Render2dController::SlotGraphicsCreated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        if (ghs.empty())
            return;
        event_param.update_operator_param.type = UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR;
        model->GraphicsUpdateProject(ghs, event_param.update_operator_param);
        model->ProjectUpdateGraphics();

        TriggerProjectUpdate("create", ghs);
    }

    void Render2dController::SlotGraphicsUpdated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool is_update_graphics_) //图像更新
    {
        if (ghs.empty())
            return;
        /** <临时 TODO　２Ｄ需要重构　ＨＪＣ */
        if (ghs.size() == 1 && ghs.front()->settings.GetLayer() == LayerConverter::ToString(Layer::temp_mark))
        {
            HandleGraphicsUpdateTempMark(ghs.front());
        }
        event_param.update_operator_param.type = UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR;
        auto update_gh = model->GraphicsUpdateProject(ghs, event_param.update_operator_param);
        if (is_update_graphics_)
        {
            model->ProjectUpdateGraphics();
            update_gh.insert(update_gh.end(), ghs.begin(), ghs.end());
            TriggerProjectUpdate("update", update_gh);
        }
    }

    void Render2dController::SlotGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)// 选择图像
    {
        if (ghs.size() == 1 && ghs[0])
        {
            auto& gh = ghs[0];
            HandleGraphicsSelectedSingle(gh); // 更新状态
            TriggerProjectUpdate("select", { gh }); // 往外发送选中事件
        }
    }

    void Render2dController::SlotGraphicsDelete(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        if (ghs.empty())
            return;
        event_param.update_operator_param.type = UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR;
        model->GraphicsUpdateProject(ghs, event_param.update_operator_param);
        model->ProjectUpdateGraphics();
        if (model->IsDelete())
        {
            TriggerProjectUpdate("delete", ghs);
        }
    }

    void Render2dController::SlotRegionSelected(float center_x, float center_y, float w, float h)
    {
        if (event_param.cad_edit_param.cad_event_param.step != jrsdata::CadEventParam::Step::NONE)
        {
            HandleCADEditRegionSelected(center_x, center_y, w, h);
        }
        if (event_param.sub_edit_param.multi_event_param.create_type != jrsdata::MultiBoardEventParam::CreateType::NONE)
        {
            HandleMultipleBoardRegionSelected(center_x, center_y, w, h);
        }
        if (event_param.sub_edit_param.multi_event_param.multi_select_type != jrsdata::MultiBoardEventParam::MultiSelectType::NONE)
        {
            HandleEditSubRegionRegionSelected(center_x, center_y, w, h);
        }
        if (event_param.pad_edit_param.pad_event_param.step != jrsdata::PadEventParam::Step::NONE)
        {
            HandlePadEditRegionSelected(center_x, center_y, w, h);
        }
        if (!event_param.render_create_param.IsEmpty())
        {
            HandleCreateGraphicsRegionSelected(center_x, center_y, w, h);
        }
        if (event_param.temp_region_edit_param.edit_mode != 0)
        {
            HandleDrawTempRegionDone(center_x, center_y, w, h);
        }
        if (event_param.show_3d_region_edit_param.edit_mode != 0)
        {
            HandleDrawShow3DRegionDone(center_x, center_y, w, h);
        }
    }

    int Render2dController::SetRenderState(const VisionMode& state)
    {
        emit SignalRenderStateChange(static_cast<int>(state));
        return 0;
    }

    int Render2dController::SetCurrentLayer(const std::string& layer)
    {
        emit SignalCurrentLayerChange(layer);
        return 0;
    }

    int Render2dController::SetCurrentLayer(const Layer& layer)
    {
        return SetCurrentLayer(LayerConverter::ToString(layer));
    }

    void Render2dController::ReadGraphics(GraphicsPtr& gh, const std::string& name)
    {
        emit SignalGetGraphicsSingle(gh, name);
    }

    void Render2dController::ShowImageWithGraphics(const std::vector<std::pair<GraphicsPtr, cv::Mat>>& images)
    {
        std::vector<ImageShowParam> isps;
        for (auto& img : images)
        {
            auto& gh = img.first;

            ImageShowParam isp;
            isp.image = img.second;
            isp.angle = -gh->a();
            isp.x = gh->x();
            isp.y = gh->y();
            isp.z = image_layer_temp;
            isps.emplace_back(std::move(isp));
        }
        //emit SignalClearImage(image_layer_temp);
        emit SignalShowImages(isps);
    }

    std::optional < std::reference_wrapper<Render2dEventParam>> Render2dController::GetCurrentRenderEventParam()
    {
        return event_param;
    }

    void Render2dController::Init()
    {
        InitEventHandler();
        InitConnect();
    }

    void Render2dController::InitCallBackView()
    {
        // 关联 Render2dView 的信号和 Render2dController 的槽
        connect(render_2d_view, &Render2dView::SignalGraphicsCreated, this, &Render2dController::SlotGraphicsCreated);
        connect(render_2d_view, &Render2dView::SignalGraphicsUpdated, this, &Render2dController::SlotGraphicsUpdated);
        connect(render_2d_view, &Render2dView::SignalGraphicsSelected, this, &Render2dController::SlotGraphicsSelected);
        connect(render_2d_view, &Render2dView::SignalGraphicsDelete, this, &Render2dController::SlotGraphicsDelete);
        connect(render_2d_view, &Render2dView::SignalRegionSelected, this, &Render2dController::SlotRegionSelected);

        // 关联 Render2dController 的信号和 Render2dView 的槽
        connect(this, &Render2dController::SignalRenderStateChange, render_2d_view, &Render2dView::SlotRenderStateChange);
        connect(this, &Render2dController::SignalRenderCreateGraphicsModeChange, render_2d_view, &Render2dView::SlotRenderCreateGraphicsModeChange);
        connect(this, &Render2dController::SignalAddImage, render_2d_view, &Render2dView::SlotAddImage);
        connect(this, &Render2dController::SignalShowImages, render_2d_view, &Render2dView::SlotShowImages);
        connect(this, &Render2dController::SignalShowImageChangeWithStr, render_2d_view, &Render2dView::SlotShowImageChangeWithStr);
        connect(this, &Render2dController::SignalClearImage, render_2d_view, &Render2dView::SlotClearImage);
        connect(this, &Render2dController::SignalDrawAngleChange, render_2d_view, &Render2dView::SlotDrawAngleChange);
        connect(this, &Render2dController::SignalRenderCanvasSizeChange, render_2d_view, &Render2dView::SlotRenderCanvasSizeChange);
        connect(this, &Render2dController::SignalCurrentLayerChange, render_2d_view, &Render2dView::SlotCurrentLayerChange);
        connect(this, &Render2dController::SignalCameraScaleModeChange, render_2d_view, &Render2dView::SlotCameraScaleModeChange);
        connect(this, &Render2dController::SignalCameraResetModeChange, render_2d_view, &Render2dView::SlotCameraResetModeChange);
        connect(this, &Render2dController::SignalResetCamera, render_2d_view, &Render2dView::SlotResetCamera);
        connect(this, &Render2dController::SignalMoveCamera, render_2d_view, &Render2dView::SlotMoveCamera);
        connect(this, &Render2dController::SignalMoveCameraToGraphics, render_2d_view, &Render2dView::SlotMoveCameraToGraphics);
        connect(this, &Render2dController::SignalMoveCameraToGraphicsWithID, render_2d_view, &Render2dView::SlotMoveCameraToGraphicsWithID);
        connect(this, &Render2dController::SignalMoveCameraToSelectedGraphics, render_2d_view, &Render2dView::SlotMoveCameraToSelectedGraphics);
        connect(this, &Render2dController::SignalThumbnailShow, render_2d_view, &Render2dView::SlotThumbnailShow);
        connect(this, &Render2dController::SignalAddGraphics, render_2d_view, &Render2dView::SlotAddGraphics);
        connect(this, &Render2dController::SignalAddGraphicsSingle, render_2d_view, &Render2dView::SlotAddGraphicsSingle);
        connect(this, &Render2dController::SignalGraphicsSelect, render_2d_view, &Render2dView::SlotGraphicsSelect);
        connect(this, &Render2dController::SignalGraphicsSelectSingle, render_2d_view, &Render2dView::SlotGraphicsSelectSingle);
        connect(this, &Render2dController::SignalGraphicsSelectSingleWithID, render_2d_view, &Render2dView::SlotGraphicsSelectSingleWithID);
        connect(this, &Render2dController::SignalGraphicsAttributeEditSingleSelected, render_2d_view, &Render2dView::SlotGraphicsAttributeEditSingleSelected);
        connect(this, &Render2dController::SignalGraphicsAttributeEditByGraphicsPtr, render_2d_view, &Render2dView::SlotGraphicsAttributeEditByGraphicsPtr);
        // connect(this, &Render2dController::SignalGraphicsCreateToView, render_2d_view, &Render2dView::SlotGraphicsCreateToView);
        connect(this, &Render2dController::SignalAddGraphicsLayerConfig, render_2d_view, &Render2dView::SlotAddGraphicsLayerConfig);
        connect(this, &Render2dController::SignalShowGraphicsLayer, render_2d_view, &Render2dView::SlotShowGraphicsLayer);

        connect(this, &Render2dController::SignalClearLayerGraphics, render_2d_view, &Render2dView::SlotClearLayerGraphics);
        connect(this, &Render2dController::SignalClearGraphics, render_2d_view, &Render2dView::SlotClearGraphics);
        connect(this, &Render2dController::SignalClearGraphicsExceptLayer, render_2d_view, &Render2dView::SlotClearGraphicsExceptLayer);
        connect(this, &Render2dController::SignalClearPadGroups, render_2d_view, &Render2dView::SlotClearPadGroup);

        connect(this, &Render2dController::SignalGetGraphics, render_2d_view, &Render2dView::SlotGetGraphics);
        connect(this, &Render2dController::SignalGetGraphicsSingle, render_2d_view, &Render2dView::SlotGetGraphicsSingle);
        connect(this, &Render2dController::SignalGetCurrentSelectedGraphics, render_2d_view, &Render2dView::SlotGetCurrentSelectedGraphics);
        connect(this, &Render2dController::SignalGetCurrentSelectedGraphicsSingle, render_2d_view, &Render2dView::SlotGetCurrentSelectedGraphicsSingle);
        connect(this, &Render2dController::SignalCreateGraphics, render_2d_view, &Render2dView::SlotCreateGraphics);
        connect(this, &Render2dController::SignalShow3DView, render_2d_view, &Render2dView::SlotShow3DView);

        //TODO 增加界面的撤销操作  by yaoying_zhang 2024.12.04
        connect(this, &Render2dController::SignalRevoke, render_2d_view, &Render2dView::SlotRevoke);
        //TODO 增加界面的恢复操作  by yaoying_zhang 2024.12.04
        connect(this, &Render2dController::SignalRecover, render_2d_view, &Render2dView::SlotRecover);
        connect(this, &Render2dController::SignalShowCenterCrossLine, render_2d_view, &Render2dView::SlotShowCenterCrossLine);
        connect(this, &Render2dController::SignalAddResultGraphicsShapes, render_2d_view, &Render2dView::SlotAddGraphicsShapes);
        connect(this, &Render2dController::SignalClearResultGraphicsShapes, render_2d_view, &Render2dView::SlotClearGraphicsShapes);


        // connect(model, &Render2dModel::SignalProjectUpdateGraphics, this, &Render2dController::SlotProjectUpdateGraphics);
    }

    void Render2dController::InitCallBackModel()
    {
        model->SetCallBackProjectUpdateGraphics(std::bind(static_cast<void (Render2dController::*)(const std::vector<std::shared_ptr<GraphicsAbstract>>&)>(&Render2dController::SlotProjectUpdateGraphics), this, std::placeholders::_1));
        model->SetCallBackCreateGraphics(std::bind(&Render2dController::SignalCreateGraphics, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4, std::placeholders::_5));
        model->SetCallBackCurrentSelectParam(std::bind(&Render2dController::SlotCurrentSelectParam, this, std::placeholders::_1));

        model->SetCallBackClearGraphics(std::bind(&Render2dController::HandleClearGraphics, this, std::placeholders::_1, std::placeholders::_2));
        model->SetCallBackGetGraphics(std::bind(&Render2dController::SignalGetGraphics, this, std::placeholders::_1, std::placeholders::_2));

        /**<设置多连板 回调函数, 用于操作render 界面*/
        _regular_multiple_boards_ptr->SetAddGraphicsCallBack(std::bind(&Render2dController::SignalAddGraphics,
            this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
        _regular_multiple_boards_ptr->SetClearLayerGraphicsCallBack(std::bind(&Render2dController::SignalClearLayerGraphics, this, std::placeholders::_1, std::placeholders::_2));
        _regular_multiple_boards_ptr->SetProjectUpdateGraphicsCallBack(std::bind(static_cast<void (Render2dController::*)()>(&Render2dController::SlotProjectUpdateGraphics), this));
        _regular_multiple_boards_ptr->SetRenderCurrentLayerCallBack(std::bind(static_cast<int (Render2dController::*)(const Layer&)>(&Render2dController::SetCurrentLayer),
            this, std::placeholders::_1));
        _regular_multiple_boards_ptr->SetRenderVisionModeCallBack(std::bind(&Render2dController::SetRenderState, this, std::placeholders::_1));
        _regular_multiple_boards_ptr->SetGetGraphicsCallBack(std::bind(&Render2dController::ReadGraphics, this, std::placeholders::_1, std::placeholders::_2));
        _regular_multiple_boards_ptr->SetGetCurrentSelectParam(std::bind(&Render2dController::GetCurrentRenderEventParam, this));

        /**<设置不规则多连板回调函数*/
        _irregular_multiple_boards_ptr->SetAddGraphicsCallBack(std::bind(&Render2dController::SignalAddGraphics,
            this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
        _irregular_multiple_boards_ptr->SetClearLayerGraphicsCallBack(std::bind(&Render2dController::SignalClearLayerGraphics, this, std::placeholders::_1, std::placeholders::_2));
        _irregular_multiple_boards_ptr->SetProjectUpdateGraphicsCallBack(std::bind(static_cast<void (Render2dController::*)()>(&Render2dController::SlotProjectUpdateGraphics), this));
        _irregular_multiple_boards_ptr->SetRenderCurrentLayerCallBack(std::bind(static_cast<int (Render2dController::*)(const Layer&)>(&Render2dController::SetCurrentLayer),
            this, std::placeholders::_1));
        _irregular_multiple_boards_ptr->SetRenderVisionModeCallBack(std::bind(&Render2dController::SetRenderState, this, std::placeholders::_1));
        _irregular_multiple_boards_ptr->SetGetGraphicsCallBack(std::bind(&Render2dController::ReadGraphics, this, std::placeholders::_1, std::placeholders::_2));
        _irregular_multiple_boards_ptr->SetGetCurrentSelectParam(std::bind(&Render2dController::GetCurrentRenderEventParam, this));
    }

    void Render2dController::InitEventHandler()
    {
#pragma region 需要新增的快捷键功能

        view_handlers[jrsaoi::SHORTCUT_ACT_REVOKE] = [&](const jrsdata::RenderEventParamPtr)
            {
                Log_INFO(__FUNCTION__, " 撤销");
                emit SignalRevoke();
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_RECOVER] = [&](const jrsdata::RenderEventParamPtr)
            {
                Log_INFO(__FUNCTION__, " 恢复");
                emit SignalRecover();
            };
        event_handlers["act_arrow_indicates_device"] = [&](const jrsdata::RenderViewParamPtr)
            {
                Log_INFO(__FUNCTION__, " 箭头指示元件");
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_ROTATEION_90] = [&](const jrsdata::RenderEventParamPtr param)
            {
                param->multi_param_ptr->operate_param->operate_type = jrsdata::MultiBoardEventParam::OperateParam::OperateType::BOARD;
                model->DealCADShortcutOperate(param, event_param.update_operator_param.select_param);
                TriggerProjectUpdate("update_all", {});
            };

        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_LEFT_RIGHT_MIRROR] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 左右镜像");
                param->multi_param_ptr->operate_param->operate_type = jrsdata::MultiBoardEventParam::OperateParam::OperateType::BOARD;
                model->DealCADShortcutOperate(param, event_param.update_operator_param.select_param);
                TriggerProjectUpdate("update_all", {});
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_UP_DOWN_MIRROR] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 上下镜像");
                param->multi_param_ptr->operate_param->operate_type = jrsdata::MultiBoardEventParam::OperateParam::OperateType::BOARD;
                model->DealCADShortcutOperate(param, event_param.update_operator_param.select_param);
                TriggerProjectUpdate("update_all", {});
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_CONVERSION] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 逆时针顺时针转换");
                param->multi_param_ptr->operate_param->operate_type = jrsdata::MultiBoardEventParam::OperateParam::OperateType::COMPONENT;
                model->DealCADShortcutOperate(param, event_param.update_operator_param.select_param);
                TriggerProjectUpdate("update_all", {});
                //model->AllCADComponentRotate(jrsdata::MultiBoardEventParam::TransformType::CONVERT);
                //DealCADShortcutOperate(param);
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_ALL_ELEMENTS_ROTATE_90] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 所有元件旋转90度");
                param->multi_param_ptr->operate_param->operate_type = jrsdata::MultiBoardEventParam::OperateParam::OperateType::COMPONENT;
                model->DealCADShortcutOperate(param, event_param.update_operator_param.select_param);
                TriggerProjectUpdate("update_all", {});
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_CAD_MOVE] = [&](const jrsdata::RenderEventParamPtr param)  //TODO 增加CAD的整体移动 by yaoying_zhang 2024-12-03
            {
                Log_INFO(__FUNCTION__, " CAD整体移动");
                emit SignalRenderStateChange(static_cast<int>(VisionMode::MOVE_ALL_GRAPHICS));
                TriggerProjectUpdate("update_all", {});
            };
        view_handlers[SHORTCUT_ACT_COPY_PAD_LEFT_RIGHT_MIRROR] = [&](const jrsdata::RenderEventParamPtr param)
            {
                _pad_operator_ptr->Update(param, event_param);
                SlotProjectUpdateGraphics();
            };

        view_handlers[SHORTCUT_ACT_COPY_PAD_ROTATE_90_DUPLICATE] = [&](const jrsdata::RenderEventParamPtr param)
            {
                _pad_operator_ptr->Update(param, event_param);
                SlotProjectUpdateGraphics();
            };

        view_handlers[SHORTCUT_ACT_COPY_PAD_TOP_BOTTOM_MIRROR] = [&](const jrsdata::RenderEventParamPtr param)
            {
                _pad_operator_ptr->Update(param, event_param);
                SlotProjectUpdateGraphics();

            };
        view_handlers[SHORTCUT_ACT_COPY_PAD_ROTATE_180_DUPLICATE] = [&](const jrsdata::RenderEventParamPtr param)
            {
                _pad_operator_ptr->Update(param, event_param);
                SlotProjectUpdateGraphics();

            };

        view_handlers[jrsaoi::SHORTCUT_ACT_COPY_COMPONENT] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 复制元件");
                GraphicsPtr gh;
                emit SignalGetCurrentSelectedGraphicsSingle(gh, "");
                if (!gh)
                    return;
                SimulateKeyCtrlC();

            };
        view_handlers[jrsaoi::SHORTCUT_ACT_DELETE_COMPONENT] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, "删除元件");

                GraphicsPtrVec selected_graphics;
                emit SignalGetCurrentSelectedGraphics(selected_graphics, "");

                if (selected_graphics.empty())
                    return;
                GraphicsPtr target_ptr = nullptr;
                // 找到第一个存在父级的元件
                for (const auto& gh : selected_graphics)
                {
                    auto parent_attr = gh->GetParent();
                    if (parent_attr)
                    {
                        // 向上查找直到根
                        GraphicsID root_id;
                        while (parent_attr)
                        {
                            root_id = parent_attr->GetId();
                            parent_attr = parent_attr->GetParent();
                        }

                        // 获取根节点图元
                        GraphicsPtr root_ptr = nullptr;
                        emit SignalGetGraphicsSingle(root_ptr, root_id);

                        target_ptr = root_ptr ? root_ptr : gh;
                        break;
                    }
                    else
                    {
                        // 没有父级则直接作为目标
                        target_ptr = gh;
                    }
                }
                if (target_ptr)
                    SlotGraphicsDelete({ target_ptr });
                //SimulateDel();
            };
        view_handlers["act_device_paste"] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 粘贴元件+流水号");
                SimulateKeyCtrlV();
            };

        view_handlers[jrsaoi::SHORTCUT_ACT_IMAGE_SIZE_100] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 100%显示");
                emit SignalCameraScaleModeChange(static_cast<int>(CameraScaleMode::TRUE_SCALE));
                //emit SignalCameraResetModeChange(static_cast<int>(CameraResetMode::AlignCenter));
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_IMAGE_PANORAMA] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 全屏显示");
                emit SignalCameraScaleModeChange(static_cast<int>(CameraScaleMode::AUTO_SCALE));
                emit SignalCameraResetModeChange(static_cast<int>(CameraResetMode::AlignCenter));
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_IMAGE_CENTER] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 图形居中");
                emit SignalMoveCameraToSelectedGraphics();
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_IMAGE_ZOOM_IN] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 放大");
                emit SignalMoveCamera(static_cast<int>(CameraDirection::Rear));
            };
        view_handlers[jrsaoi::SHORTCUT_ACT_IMAGE_ZOOM_OUT] = [&](const jrsdata::RenderEventParamPtr param)
            {
                Log_INFO(__FUNCTION__, " 缩小");
                emit SignalMoveCamera(static_cast<int>(CameraDirection::Front));
            };

        view_handlers["act_select_area_3d"] = [&](const jrsdata::RenderEventParamPtr)
            {
                event_param.show_3d_region_edit_param.edit_mode = 1;
                emit SignalRenderStateChange(static_cast<int>(VisionMode::MANUAL_CREATE_GRAPHICS));
                Log_INFO(__FUNCTION__, " 框选区域3D图");
            };
        view_handlers["act_whole_board_3d"] = [&](const jrsdata::RenderEventParamPtr)
            {
                cv::Mat height_image;
                std::vector<cv::Mat> texture_images;
                model->GetBoardShow3DImage(height_image, texture_images);
                PlaneProjectByHist1(height_image, height_image, 0.01f, 0.01f);
                emit SignalShow3DView(height_image, 0.01f, 0.01f, texture_images);
                Log_INFO(__FUNCTION__, " 整板大图3D图");
            };
        view_handlers["act_selected_device_3d"] = [&](const jrsdata::RenderEventParamPtr param)
            {
                const auto& component_name = event_param.update_operator_param.select_param.component_name;
                const auto& subboard_name = event_param.update_operator_param.select_param.subboard_name;
                if (component_name == "")
                {
                    JRSMessageBox_WARN("警告", "未选中任何元件", jrscore::MessageButton::Ok);
                    return;
                }
                cv::Rect crop_rect;
                model->GetComponentDetectRegionSmallestRect(subboard_name, component_name, crop_rect);
                cv::Mat height_image;
                std::vector<cv::Mat> texture_images;
                model->GetSelectShow3DImages(crop_rect, height_image, texture_images);
                PlaneProjectByHist1(height_image, height_image, 0.01f, 0.01f);
                emit SignalShow3DView(height_image, 0.01f, 0.01f, texture_images);
                Log_INFO(__FUNCTION__, " 显示选中元件3D图");
            };
        event_handlers[jrsaoi::RENDER2D_AFFINE_TRANSTORM_EVENT_NAME] = [&](const jrsdata::RenderViewParamPtr& param)
            {
                model->EntiretyBoardTransform(param->affine_transform_matrix);
                model->ProjectUpdateGraphics();
            };
        event_handlers[jrsaoi::REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME] = [&](const jrsdata::RenderViewParamPtr& param)
            {
                (void)param;
                model->ProjectUpdateGraphics();
            };
        /**<TODO 临时方法，待优化  HJC 2025/3/20*/
        event_handlers[jrsaoi::REQUEST_RENDER2D_UPDATE_SELECT_EVENT_NAME] = [&](const jrsdata::RenderViewParamPtr& param)
            {
                (void)param;
                model->ProjectUpdateGraphics();
                GraphicsPtr gh;
                SignalGetCurrentSelectedGraphicsSingle(gh, "");
                if (gh)
                {
                    this->TriggerProjectUpdate("select", { gh });
                }
            };

        event_handlers[jrsaoi::REQUEST_RENDER2D_UPDATE_PROJECT_EVENT_NAME] = [&](const jrsdata::RenderViewParamPtr& param)
            {
                (void)param;
                model->ProjectUpdateGraphics();
                TriggerProjectUpdate("update_all", {});
            };

#pragma endregion
#pragma region 视野
        view_handlers["act_show_index_picture"] = [&](const jrsdata::RenderEventParamPtr)
            {
                Log_INFO(__FUNCTION__, " 显示索引图");
                emit SignalThumbnailShow();
            };

        event_handlers["act_body_device_show"] = [&](const jrsdata::RenderViewParamPtr)
            {
                Log_INFO(__FUNCTION__, " 本体框");
                std::string layer = LayerConverter::ToString(Layer::component);
                emit SignalShowGraphicsLayer(layer);
            };
        event_handlers["act_pad_show"] = [&](const jrsdata::RenderViewParamPtr)
            {
                Log_INFO(__FUNCTION__, " 显示焊盘");
                std::string layer = LayerConverter::ToString(Layer::pad);
                emit SignalShowGraphicsLayer(layer);
            };
        event_handlers["act_detect_show"] = [&](const jrsdata::RenderViewParamPtr)
            {
                Log_INFO(__FUNCTION__, " 显示检测框");
                std::string layer = LayerConverter::ToString(Layer::region);
                emit SignalShowGraphicsLayer(layer);
            };

#pragma endregion 视野

#pragma region 元件选中修改
        component_handlers[jrsaoi::SHOWLIST_SELECT_COMPONENT_UPDATE] = [&](const jrsdata::ComponentListViewParamPtr param)
            {
                SelectedSpeficComponent(param->component_name, param->subboard_name, param->component_part_number);
            };
        component_handlers[jrsaoi::SHOWLIST_SELECT_SUBBOARD_UPDATE] = [&](const jrsdata::ComponentListViewParamPtr param)
            {
                GraphicsPtr gh;
                ReadGraphics(gh, param->subboard_name);
                if (!gh)
                {
                    return;
                }

                // 响应选中信号,但是不触发回调,因此主动调用选中处理函数
                emit SignalGraphicsSelectSingle(gh, false);
                //emit SignalMoveCameraToGraphics(gh);
                HandleGraphicsSelectedSingle(gh);
                TriggerProjectUpdate("select", { gh });
            };
        online_debug_handlers[jrsaoi::ONLINEDEBUG_CHANGE_DEBUG_COMPONENT_EVENT_NAME] = [&](const jrsdata::OnlineDebugViewParamPtr param)
            {
                const auto& online_debug_param_ptr = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param);
                if (!online_debug_param_ptr)
                {
                    return;
                }

                _current_img_set = dynamic_adjust_image_set;

                std::unordered_map<int, cv::Mat> key_and_imgs;
                auto& imgs = online_debug_param_ptr->current_debug_info.current_component_fov_img;
                cv::Size image_size;
                for (const auto& [key, val] : imgs.imgs)
                {
                    image_size = val.size();
                    key_and_imgs[static_cast<int>(key)] = val;
                }
                cv::Point2f center_point = online_debug_param_ptr->current_debug_info.current_component_fov_img.pos;
                emit SignalRenderCanvasSizeChange(image_size.width, image_size.height);
                QMetaObject::invokeMethod(
                    this,
                    [key_and_imgs, center_point, this]() {
                        GraphicsImage graphics_img;
                        graphics_img.key_and_imgs = key_and_imgs;
                        graphics_img.center_point = center_point;
                        graphics_img.z = image_layer_bottom;
                        graphics_img.current_show_img = 0;
                        graphics_img.set_key = _current_img_set;
                        //graphics_img.is_resize_canvas = true;
                        graphics_img.is_move_camera = false; /**< 禁止移动相机 */
                        render_2d_view->SlotCreateImages(graphics_img);
                    },
                    Qt::QueuedConnection
                );

                //model->ProjectUpdateGraphics();
                SelectedSpeficComponent(online_debug_param_ptr->current_debug_info.current_component_name, online_debug_param_ptr->current_debug_info.current_subboard_name, online_debug_param_ptr->current_debug_info.current_part_number);

            };

        auto project_func = [&](const jrsdata::ProjectEventParamPtr param)
            {
                ResetEvent();

                model->Update(param);
                int width, height;
                model->GetBoardSize(width, height);
                emit SignalRenderCanvasSizeChange(width, height);

                // 选中一个作为默认子板
                {
                    std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
                    render_2d_view->GetLayerGraphics(ghs, LayerConverter::ToString(Layer::subboard));
                    if (ghs.empty())
                        return;
                    event_param.update_operator_param.select_object.subboard_graphics = ghs[0];
                    event_param.update_operator_param.select_param.subboard_name = ghs[0]->GetId().GetString();
                }

                TriggerProjectUpdate("update_all", {});
                if (param->event_name == jrsaoi::PROJECT_READ_EVENT_NAME)
                {
                    /**< 打开工程时导入一次图片*/
                    ShowEntiretyImages();
                }
                /**<全屏显示 */
                emit SignalCameraScaleModeChange(static_cast<int>(CameraScaleMode::AUTO_SCALE));
                emit SignalCameraResetModeChange(static_cast<int>(CameraResetMode::AlignCenter));
            };


        project_handlers[PROJECT_READ_EVENT_NAME] = project_func;
        project_handlers[jrsaoi::IMPORT_CAD_EVENT_NAME] = project_func;

        event_handlers["change_component_name"] = [&](const jrsdata::RenderViewParamPtr param)
            {
                // TODO 目前只能改一个,不是多联板更改
                auto new_name = "";
                auto old_name = "";
                auto& project_param_instance = model->GetProjectParamInstance().GetProjectDataProcessInstance();
                auto component = project_param_instance->ReadCADRef(old_name);
                if (!component.has_value())
                    return;
                component->get().component_name = new_name;
                //project_param_instance.UpdateComponent(*component);
            };
#pragma endregion 图形修改

#pragma region 显示
        project_handlers[jrsaoi::ENTIRETY_IMAGE_READ] = [&](const jrsdata::ProjectEventParamPtr param)
            {
                ShowEntiretyImages();
            };
        project_handlers[jrsaoi::CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME] = [&](const jrsdata::ProjectEventParamPtr param)
            {//<清除整板大图
                emit SignalClearImage(-1, -1);
                model->Update(param);

            };
        event_handlers["show_scan_image"] = [&](const jrsdata::RenderViewParamPtr param)
            {
                if (param->image_buffer.boarder_imgs.imgs.size() != 0)
                {
                    SignalShowCenterCrossLine(false);
                    event_param.render_2d_controller_param.is_update_entirty_board_images = true;
                    //! 当大图都合成完成后，更新工程中大图 by zhangyuyu 2024.11.11
                    model->Update(param);
                    ShowEntiretyImages();
                }
                else if (param->image_buffer.one_fov_imgs.imgs.size() != 0
                    /*  && !event_param.render_2d_controller_param.is_update_entirty_board_images*/)
                {

                    SignalShowCenterCrossLine(true);
                    auto image_index = render_2d_view->GetCurrentImageIndex();
                    cv::Mat img = param->image_buffer.one_fov_imgs.imgs[static_cast<jrsdata::LightImageType>(image_index)];

                    QMetaObject::invokeMethod(
                        this,
                        [img, this]() {
                            GraphicsImage graphics_img;
                            graphics_img.key_and_imgs = { {image_layer_key,img} };
                            graphics_img.current_show_img = image_layer_key;
                            graphics_img.z = image_layer_temp;
                            graphics_img.set_key = default_image_set;/**< 单个FOV 实时采图 */
                            render_2d_view->SlotCreateImages(graphics_img);
                        },
                        Qt::QueuedConnection
                    );
                }

            };

        // 切换显示的大图
        view_handlers[CHANGE_RENDER_SHOW_IMG_TYPE] = [&](const jrsdata::RenderEventParamPtr param)
            {
                if (!param)
                    return;
                emit SignalClearImage(_current_img_set, image_layer_key);
                auto show_img_type_str = jtools::StringOperation::Trim(param->select_param.show_img_type);
                /**<当前是否处于动态调试，如果动态调试，就使用动态调试的图层，如果不属于动态调试就使用默认集合*/
                emit SignalShowImageChangeWithStr(_current_img_set, show_img_type_str);
            };
#pragma endregion 显示

#pragma region 属性修改

        event_handlers[EDIT_SUBBOARD_REGION_RECT] = [&](const jrsdata::RenderViewParamPtr param)
            {
                if (event_param.update_operator_param.select_param.subboard_name.empty())
                {
                    return;
                }

                event_param.sub_edit_param.multi_event_param.multi_select_type = jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_RECT;
            };

        event_handlers["draw_angle"] = [&](const jrsdata::RenderViewParamPtr param)
            {
                emit SignalDrawAngleChange(param->draw_angle);
            };

        view_handlers[SELECT_RENDER2D_LAYER_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                SetCurrentLayer(param->select_param.layer_name);
            };

        view_handlers[RENDER2D_CHANGE_MODE_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                emit SignalRenderStateChange(param->vision_mode);
            };

        algo_handlers[WINDOW_CHANGE_MODEL_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                if (param->detect_win.model_name.empty())
                {
                    return;
                }

                //event_param.update_operator_param.model_name = "body";
                event_param.update_operator_param.model_name = param->detect_win.model_name;
                //event_param.update_operator_param.window_name = "";
                //event_param.update_operator_param.algo_name = "";
            };

#pragma endregion 属性修改

#pragma region 请求事件
        algo_handlers[REQUEST_RENDER2D_CREATE_REGION_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                auto& param_operator = event_param.update_operator_param;
                auto parent = param_operator.select_object.component_graphics;
                if (param_operator.unit_type != jrsdata::ComponentUnit::Type::BODY)
                {
                    parent = param_operator.select_object.unit_graphics;
                }
                if (parent.expired())
                {
                    return;
                }
                param_operator.model_name = param->detect_win.model_name;
                param_operator.defect_name = param->detect_win.defect_name;
                param_operator.algo_name = param->algorithm_param.detect_algorithm_name;
                param_operator.sub_win_type = param->sub_wind_type;
                HandleRequestCreateGraphics(LayerConverter::ToString(Layer::region), parent);
                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
            };


        algo_handlers[REQUEST_RENDER2D_CREATE_SUB_REGION_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                auto& param_operator = event_param.update_operator_param;
                if (param_operator.select_object.window_graphics.expired())
                {
                    return;
                }
                HandleRequestCreateGraphics(LayerConverter::ToString(Layer::subregion), param_operator.select_object.window_graphics);
                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
            };

        algo_handlers[REQUEST_REGION_IMAGE_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                const auto& id = param->detect_win.name;
                const auto& model_name = param->detect_win.model_name;
                auto& param_operator = event_param.update_operator_param;
                auto& select_param = param_operator.select_param;
                std::string name;
                {
                    name = model->GetNameInRender(
                        select_param.subboard_name, select_param.component_name, select_param.unit_name, model_name, id);
                }
                GraphicsPtr gh;
                ReadGraphics(gh, name);
                if (!gh)
                {
                    return;
                }
                HandleGraphicsCropImage(gh);
            };

        algo_handlers[REQUEST_DRAW_TEMP_REGION_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                event_param.temp_region_edit_param.edit_mode = 1;
            };


        //algo_handlers[RENDER2D_SHOW_IMAGE_ALGO_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
        //    {
        //        const auto& id = param->detect_win.name;
        //        const auto& model_name = param->detect_win.model_name;
        //        auto& param_operator = event_param.update_operator_param;
        //        auto& select_param = param_operator.select_param;
        //        std::string name;
        //        {
        //            name = model->GetNameInRender(
        //                select_param.subboard_name, select_param.component_name, select_param.unit_name, model_name, id);
        //        }
        //        GraphicsPtr gh;
        //        ReadGraphics(gh, name);
        //        if (!gh)
        //        {
        //            return;
        //        }

        //        emit SignalClearImage(image_layer_key);
        //        ShowImageWithGraphics({ {gh,param->region_mat} });
        //    };

        algo_handlers[REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                /***< 函数待优化 TODO*/
                auto& param_operator = event_param.update_operator_param;
                auto& select_param = param_operator.select_param;
                const auto& model_name = param->detect_win.model_name;
                /**<元件切换*/
                if (param->detect_win.name.empty() && !param->detect_win.model_name.empty())  //选中元件，或者pad 而非检测框切换
                {
                    GraphicsPtr cur_select_gh;
                    GraphicsPtr object_select_gh;

                    if (param_operator.model_name == model_name)
                    {
                        //选择当前元件的本体框

                        emit SignalGetCurrentSelectedGraphicsSingle(cur_select_gh, "");
                        if (!cur_select_gh)
                        {


                            std::string name;
                            auto unit_name = model->GetUnitNameByModelName(param_operator.part_name, model_name);
                            //选择pad或者其他元件的本体框
                            name = model->GetNameInRender(
                                select_param.subboard_name, select_param.component_name, unit_name);
                            ReadGraphics(object_select_gh, name);
                            if (!object_select_gh)
                            {
                                Log_ERROR("选择图形失败，请检查Name是否有误:", name);
                                return;
                            }
                        }
                        else
                        {
                            if (LayerConverter::FromString(cur_select_gh->settings.GetLayer()) != Layer::region)
                            {
                                Log_WARN("已选中当前本体信息");
                                return;
                            }
                            if (!cur_select_gh->GetParent())
                            {
                                Log_ERROR("选择基类图形失败，请检查");
                                return;
                            }
                            emit SignalGetGraphicsSingle(object_select_gh, cur_select_gh->GetParent()->GetId());
                        }

                    }
                    else
                    {
                        std::string name;
                        auto unit_name = model->GetUnitNameByModelName(param_operator.part_name, model_name);
                        param_operator.model_name = model_name;
                        if (param_operator.model_name == "body" || param_operator.model_name == "location")
                        {
                            //选择pad或者其他元件的本体框
                            name = model->GetNameInRender(
                                select_param.subboard_name, select_param.component_name);
                        }
                        else
                        {
                            //选择pad或者其他元件的本体框
                            name = model->GetNameInRender(
                                select_param.subboard_name, select_param.component_name, unit_name);
                        }

                        ReadGraphics(object_select_gh, name);
                        if (!object_select_gh)
                        {
                            Log_ERROR("选择图形失败，请检查Name是否有误:", name);
                            return;
                        }
                    }

                    HandleGraphicsSelectedSingle(object_select_gh);//选择当前元件本体
                    /** 选择当前界面全部通知 */
                    this->TriggerProjectUpdate("select", { object_select_gh });
                    return;
                }


                /**< 检测框切换 */
                select_param.window_name = param->detect_win.name;
                param_operator.model_name = param->detect_win.model_name;

                switch (param->data_operate_type)
                {
                case jrsdata::DataUpdateType::SELECT_DATA:
                {
                    std::string unit_name;
                    if (param->cur_select_component_unit)
                    {
                        unit_name = param->cur_select_component_unit->unit_name;
                    }
                    else
                    {
                        unit_name = param_operator.select_param.unit_name;
                    }
                    std::string name;
                    {
                        name = model->GetNameInRender(
                            select_param.subboard_name, select_param.component_name, unit_name, param_operator.model_name, select_param.window_name);
                    }
                    GraphicsPtr gh;
                    ReadGraphics(gh, name);
                    if (!gh)
                    {
                        return;
                    }
                    /** < 清除搜索框图形 */
                    emit SignalClearLayerGraphics(LayerConverter::ToString(Layer::search_region), false);
                    /** <查找搜索框图形 */
                    auto detect_window = model->GetDetctWindowByCurrentSelect(param_operator);
                    if (detect_window.has_value())
                    {
                        auto search_window = model->CreateSearchWindow(select_param.subboard_name, select_param.component_name, select_param.unit_name, select_param.window_name, gh, detect_window->search_size);
                        emit SignalAddGraphics({ search_window }, "", false);
                    }
                    else
                    {
                        Log_ERROR("显示搜索框失败，请检查");
                    }
                    emit SignalGraphicsSelectSingle(gh, false);
                    HandleGraphicsSelectedSingle(gh);
                    /** 选择当前界面全部通知 */
                    this->TriggerProjectUpdate("select", { gh });
                    break;
                }
                case jrsdata::DataUpdateType::DELETE_DATA:
                {
                    std::string name;
                    {
                        name = model->GetNameInRender(
                            select_param.subboard_name, select_param.component_name,
                            select_param.unit_name, param_operator.model_name,
                            select_param.window_name);
                    }
                    GraphicsPtr gh;
                    ReadGraphics(gh, name);
                    if (!gh)
                    {
                        return;
                    }
                    //emit SignalGetGraphicsSingle(gh, id);
                    std::vector<std::shared_ptr<GraphicsAbstract>> ghs{ gh };
                    SlotGraphicsDelete(ghs);
                    //TriggerGraphicsSelectedSingle(gh);
                    break;
                }
                }
            };

        algo_handlers[REQUEST_RENDER_UPDATE_SELECT_LIGHT_TYPE_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                if (!param)
                    return;
                emit SignalClearImage(_current_img_set, image_layer_key);
                render_2d_view->SlotShowImageChange(_current_img_set, static_cast<int>(param->light_type));
            };


        algo_handlers[REQUEST_RENDER2D_DRAW_DETECT_RESULT_EVENT_NANE] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                if (!param)
                    return;
                if (param->detect_win_results.empty())
                {
                    return;
                }
                AddDetectRegionWindows(param->detect_win_results);

            };
        algo_handlers[jrsaoi::RENDER2D_COMPONENT_DETECT_RESULT_EVENT_NAME] = [&](const jrsdata::AlgoEventParamPtr param)
            {
                if (!param)
                    return;
                if (_current_img_set != default_image_set)  //如果不是默认图片集，不渲染结果
                {
                    return;
                }
                QMetaObject::invokeMethod(this,
                    "ShowDetectResult",
                    Qt::QueuedConnection,
                    Q_ARG(const jrsdata::ViewParamBasePtr&, param)
                );

                //ShowDetectResult(param->component_detect_results.value());
            };


#pragma endregion 请求事件

#pragma region 其他事件

        view_handlers[ALL_CAD_ROTATE_90_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {

                auto& project_param_instance = model->GetProjectParamInstance().GetProjectDataProcessInstance();
                auto& subboards = project_param_instance->GetProjectParam()->board_info.sub_board;
                for (auto& subboard : subboards)
                {
                    for (auto& component : subboard.component_info)
                    {
                        component.angle += 90;
                    }
                }
                model->ProjectUpdateGraphics();
            };

        view_handlers[CREATE_SUBBADMARK_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                if (event_param.update_operator_param.select_object.subboard_graphics.expired())
                {
                    return;
                }
                event_param.update_operator_param.part_name = "bad_mark";
                HandleRequestCreateGraphics(LayerConverter::ToString(Layer::badmark), event_param.update_operator_param.select_object.subboard_graphics);
                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
            };




        view_handlers[jrsaoi::SHORTCUT_ACT_REQUEST_RENDER_CREATE_SUBBADMARK_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param) //TODO 增加CAD的整体移动 by yaoying_zhang 2024-12-03
            {
                Log_INFO(__FUNCTION__, "增加坏板标记");
                if (event_param.update_operator_param.select_object.subboard_graphics.expired())
                {
                    return;
                }
                event_param.update_operator_param.part_name = LayerConverter::ToString(Layer::badmark);
                HandleRequestCreateGraphics(LayerConverter::ToString(Layer::badmark), event_param.update_operator_param.select_object.subboard_graphics);
                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  

            };
        operate_handlers[OPERATE_REQUEST_RENDER_FRAME_SELECT_REGION_EVENT_NAME] = [&](const jrsdata::OperateViewParamPtr param)
            {
                auto& add_component_param = param->add_component_view_param;
                switch (add_component_param->component_type)
                {
                case jrsdata::Component::Type::CAD:
                {
                    if (event_param.update_operator_param.select_object.subboard_graphics.expired())
                    {
                        JRSMessageBox_WARN("增加元件", "请先选中一个子板， 再添加元件", jrscore::MessageButton::Ok);
                        return;
                    }

                }
                break;
                case jrsdata::Component::Type::BARCODE:
                {
                    event_param.update_operator_param.part_name = LayerConverter::ToString(Layer::barcode);
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::barcode), event_param.update_operator_param.select_object.subboard_graphics);
                }break;
                case jrsdata::Component::Type::SUB_BARCODE: /**《子板数据*/
                {
                    if (event_param.update_operator_param.select_object.subboard_graphics.expired())
                    {
                        JRSMessageBox_WARN("增加子板条码", "请先选中一个子板， 再添加元件", jrscore::MessageButton::Ok);
                        return;
                    }

                    event_param.update_operator_param.part_name = LayerConverter::ToString(Layer::subbarcode);
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::subbarcode), event_param.update_operator_param.select_object.subboard_graphics);
                }break;
                case jrsdata::Component::Type::CARRIER_BARCODE:
                {
                    //
                }break;
                case jrsdata::Component::Type::COVERPLATE_BARCODE:
                {

                }
                break;
                case jrsdata::Component::Type::MARK:
                {
                    event_param.update_operator_param.part_name = "mark";
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::mark));
                }break;
                case jrsdata::Component::Type::SUB_MARK:
                {
                    if (event_param.update_operator_param.select_object.subboard_graphics.expired())
                    {
                        JRSMessageBox_WARN("增加子板定位点", "请先选中一个子板， 再添加元件", jrscore::MessageButton::Ok);
                        return;
                    }
                    event_param.update_operator_param.part_name = "submark";
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::submark), event_param.update_operator_param.select_object.subboard_graphics);
                }break;
                default:
                    return;
                }

                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
            };

        view_handlers[COMPONENT_EDIT_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                auto& cad_edit_param = event_param.cad_edit_param;
                cad_edit_param.cad_event_param = param->cad_param;
                switch (cad_edit_param.cad_event_param.step)
                {
                case jrsdata::CadEventParam::Step::CONFIRM_CUR_POSITION_FOR_ALIGNMENT:
                {
                    auto& subptr = event_param.update_operator_param.select_object.subboard_graphics;
                    auto sub = subptr.lock();
                    if (!sub)
                        return;
                    SetCurrentLayer(Layer::component);
                    cad_edit_param.cad_parent_subboard_name = sub->GetId().GetString();

                    //生成矩阵
                    cv::Mat matrix;
                    cad_edit_param.ori_graphics_ids.clear();
                    {
                        cad_edit_param.ori_points.clear();
                        cad_edit_param.cur_points.clear();

                        std::vector<jrstool::PointLabel<int>> ori, cur;
                        for (auto graphic_position : cad_edit_param.graphic_and_positions)
                        {
                            cad_edit_param.ori_graphics_ids.push_back(graphic_position.first.GetString());
                            auto [ori_point, cur_point] = graphic_position.second;
                            ori.emplace_back(jrstool::PointLabel<int>(ori_point, 100.0f));
                            cur.emplace_back(jrstool::PointLabel<int>(cur_point, 100.0f));

                        }

                        {
                            jrstool::CoordinatrTransform<int>::GetAffineMatrix(ori, cur, matrix);
                        }
                    }

                    auto& project_param_instance = model->GetProjectParamInstance().GetProjectDataProcessInstance();
                    /**< 更新所有子板信息，而非当前选中的子板*/
                    for (auto& subboard : project_param_instance->GetProjectParam()->board_info.sub_board)
                    {
                        model->AlignmentSubboard(subboard, matrix);
                    }
                    //auto subboard = project_param_instance->ReadSubBoardRef(cad_edit_param.cad_parent_subboard_name);
                    //if (!subboard.has_value())
                    //	return;
                    //model->AlignmentSubboard(subboard->get(), matrix);
                    model->ProjectUpdateGraphics();

                    //更新当前点位置 
                    std::vector<GraphicsPtr> ghs;
                    emit SignalGetGraphics(ghs, cad_edit_param.ori_graphics_ids);
                    /**< 元件定位更新  */
                    std::string update_component_name = "";
                    for (auto gh : ghs)
                    {
                        auto& [ori_point, cur_point] = cad_edit_param.graphic_and_positions[gh->GetId()];
                        if (ori_point.x != gh->x() && gh->y() != ori_point.y)
                        {
                            auto names = jrscore::AOITools::SplitString(gh->GetId().GetString(), ';');
                            if (names.size() == 2)
                            {
                                update_component_name = names[1];
                            }
                            ori_point = { gh->x(),gh->y() };
                        }
                    }
                    //删除 框选位置 By  2024/12/09 HJC
                    emit SignalClearLayerGraphics(LayerConverter::ToString(Layer::temp_mark));

                    if (update_component_name.empty())
                    {
                        break;
                    }
                    //定位成功 将component名称发送到operate
                    jrsdata::RenderEventParamPtr param_ptr = std::make_shared<jrsdata::RenderEventParam>();
                    param_ptr->cad_param.component_name = update_component_name;
                    param_ptr->topic_name = jrsaoi::RENDER_TRIGGER_TOPIC_NAME;
                    param_ptr->sub_name = jrsaoi::RENDER_SUB_NAME;
                    param_ptr->module_name = jrsaoi::OPERATE_MODULE_NAME;
                    param_ptr->invoke_module_name = jrsaoi::OPERATE_MODULE_NAME;
                    param_ptr->event_name = jrsaoi::RENDER2D_CAD_UPDATE_POSITION_EVENT_NAME;
                    emit SignalRender2dUpdate(param_ptr);

                }
                break;
                case jrsdata::CadEventParam::Step::CLEAR_POSITION_FOR_ALIGNMENT:
                {
                    SetCurrentLayer(Layer::component);
                    cad_edit_param = CADEditParam();
                    emit SignalClearLayerGraphics(LayerConverter::ToString(Layer::temp_mark));
                    JRSMessageBox_INFO("提示", "定位信息清除成功！", jrscore::MessageButton::Ok);
                }
                break;
                case jrsdata::CadEventParam::Step::SELECT_CUR_POSITION_FOR_ALIGNMENT:
                {
                    GraphicsPtr gh;
                    emit SignalGetCurrentSelectedGraphicsSingle(gh, "");
                    if (!gh)
                    {
                        cad_edit_param.cad_event_param.step = CadEventParam::Step::NONE;
                        JRSMessageBox_INFO("提示", "请先选择一个元件再进行定位", jrscore::MessageButton::Ok);
                        return;
                    }

                    SetCurrentLayer(Layer::temp_mark);
                    HandleGraphicsCADSelectedSingleEvent(gh);
                    SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC 
                }
                break;
                case jrsdata::CadEventParam::Step::SELECT_COMPONENT_CAD_POSITIONED:
                {
                    auto& id = param->cad_param.component_name;
                    auto& project_param_instance = model->GetProjectParamInstance().GetProjectDataProcessInstance();

                    std::optional<std::reference_wrapper<jrsdata::Component>> component;
                    component = project_param_instance->ReadCADRef(id);
                    if (!component.has_value())
                    {
                        return;
                    }
                    auto name = model->GetNameInRender(component->get().subboard_name, component->get().component_name);
                    GraphicsPtr gh;
                    ReadGraphics(gh, name);
                    if (!gh)
                    {
                        return;
                    }
                    emit SignalGraphicsSelectSingle(gh, false);
                    HandleGraphicsSelectedSingle(gh);
                    TriggerProjectUpdate("select", { gh });
                    emit SignalMoveCameraToSelectedGraphics();
                }
                break;
                case jrsdata::CadEventParam::Step::DELETE_CAD:
                {
                    SetCurrentLayer(Layer::component);
                    SetRenderState(VisionMode::HOVER);
                }
                break;
                case jrsdata::CadEventParam::Step::MANUAL_ADD_CAD:
                {

                    event_param.render_create_param.create_name = cad_edit_param.cad_event_param.component_name;
                    event_param.update_operator_param.part_name = cad_edit_param.cad_event_param.part_name;
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::component), event_param.update_operator_param.select_object.subboard_graphics);
                    //SignalRenderCreateGraphicsModeChange(0);
                    SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //进入手动画框模式
                    cad_edit_param.cad_event_param.step = jrsdata::CadEventParam::Step::NONE;
                }
                break;
                case jrsdata::CadEventParam::Step::AI_ADD_CAD:
                {
                    SetCurrentLayer(Layer::component);
                }
                break;
                default:
                    break;
                }
            };
        //子板事件  非多联板事件
        view_handlers[SUBBOARD_SELECT_SUB_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                auto& multi_param = param->multi_param;
                auto& sub_edit_param = event_param.sub_edit_param;
                auto& select_param = event_param.update_operator_param.select_param;
                if (select_param.subboard_name.empty())
                {
                    select_param.subboard_name = model->GetDefaultSubboardName();
                    if (select_param.subboard_name.empty())
                    {
                        JRSMessageBox_INFO("子板名称为空", "子板名为空，无法绘制子板", jrscore::MessageButton::Ok);
                        return;
                    }
                }
                sub_edit_param.multi_event_param.multi_select_type = multi_param.multi_select_type;
                //SetRenderState(VisionMode::HOVER);
                SetCurrentLayer(LayerConverter::ToString(Layer::subboard));
                SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
            };

        //view_handlers[MULTI_BOARD_SELECT_FLAG_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
        //    {
        //        auto& multi_param = param->multi_param;
        //        event_param.sub_edit_param.multi_event_param.step = multi_param.step;
        //        SetCurrentLayer(Layer::temp_mark);
        //        SetRenderState(VisionMode::HOVER);
        //    };
        /** <多联板操作事件 */
        view_handlers[MULTI_BOARD_OPERATE_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                if (!param || !param->multi_param_ptr)
                {
                    return;
                }
                if (param->multi_param_ptr->event == jrsdata::MultiBoardEventParam::Event::MULTIPLE_BOARD)
                {

                    MultipleBoardCreate(param);

                }
                else if (param->multi_param_ptr->event == jrsdata::MultiBoardEventParam::Event::BOARD_SORT)
                {
                    emit SignalClearGraphics(false);
                    if (param->multi_param_ptr->subboard_sort_param.has_value())
                    {
                        model->UpdateTemporalGraphicsBySubboards(param->multi_param_ptr->subboard_sort_param->sorted_subboards);
                    }
                }
            };

        view_handlers[CREATE_PAD_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                auto& param_operator = event_param.update_operator_param;
                if (param_operator.select_object.component_graphics.expired() || param_operator.select_object.component_graphics.lock()->settings.GetLayer() != LayerConverter::ToString(Layer::component))
                {
                    JRSMessageBox_WARN("提示", "添加pad前请先选择一个元件", jrscore::MessageButton::Ok);
                    return;
                }
                if (param->pad_param.step == jrsdata::PadEventParam::Step::CREATE)
                {
                    event_param.pad_edit_param.render_create_param.Reset();/**<清空所有数据，防止出现野指针*/
                    event_param.pad_edit_param.pad_event_param = param->pad_param;
                    param_operator.unit_type = jrsdata::ComponentUnit::Type::PAD;
                    HandleRequestCreateGraphics(LayerConverter::ToString(Layer::pad), param_operator.select_object.component_graphics);
                    SetRenderState(VisionMode::MANUAL_CREATE_GRAPHICS);  //!  进入编辑模式-  2024-1-6 HJC  
                    ///**< 当前元件的角度绘制 */
                   /*  auto current_component_gh = param_operator.select_object.component_graphics.lock();
                  if (current_component_gh)
                  {
                      SignalDrawAngleChange(current_component_gh->a());
                  }*/


                  /** < 清理掉上次的数据 */
                    event_param.pad_edit_param.end_gh = nullptr;
                    event_param.pad_edit_param.first_gh = nullptr;
                }
                else
                {
                    ShowPadAlterView();
                }
            };

        view_handlers[EDIT_UPDATE_RENDER_EVENT_NAME] = [&](const jrsdata::RenderEventParamPtr param)
            {
                (void)param;
                //auto select_param = event_param.update_operator_param.select_param;
                //std::string graphics_name = "";
                //if (param->edit_data_update_ptr->update_widget == jrsdata::EditViewData::UpdateWidgets::Component)
                //{
                //    graphics_name = model->GetNameInRender(select_param.subboard_name, select_param.component_name);
                //}
                //else if (param->edit_data_update_ptr->update_widget == jrsdata::EditViewData::UpdateWidgets::PAD)
                //{
                //    graphics_name = model->GetNameInRender(select_param.subboard_name, select_param.component_name, select_param.unit_name);
                //}
                //else if (param->edit_data_update_ptr->update_widget == jrsdata::EditViewData::UpdateWidgets::DETECT_WINDOW)
                //{
                //    graphics_name = model->GetNameInRender(select_param.subboard_name, select_param.component_name,
                //        select_param.unit_name, event_param.update_operator_param.model_name, select_param.window_name);
                //}

                //GraphicsPtr gh;
                //ReadGraphics(gh, graphics_name);
                model->ProjectUpdateGraphics();

            };

#pragma endregion 其他事件
    }

    void Render2dController::InitConnect()
    {
        connect(_add_pad_view, &AddPadView::SigPadNumber, this, [=](int pad_numbers_)
            {
                bool is_update = false;
                //获取当前选中的pad
                std::vector<GraphicsPtr> ghs;
                emit SignalGetCurrentSelectedGraphics(ghs, "");
                for (auto& gh : ghs)
                {
                    auto pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(gh);
                    if (pad_group && pad_group->GetGroupType() == PadGraphicsGroup::PadGroupType::ARRAY)
                    {
                        pad_group->SetArrayPadsNumber(pad_numbers_);
                        is_update = true;
                    }
                }
                if (!is_update)
                {
                    JRSMessageBox_INFO("提示", "pad数量更新失败，请选择一个Pad再进行编辑", jrscore::MessageButton::Ok);
                }
            });
    }

    void Render2dController::HandlerenderEvent(const jrsdata::RenderViewParamPtr& param)
    {
        auto it = event_handlers.find(param->event_name);
        if (it != event_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::Handleviewevent(const jrsdata::RenderEventParamPtr& param)
    {
        auto it = view_handlers.find(param->event_name);
        if (it != view_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::Handlealgoevent(const jrsdata::AlgoEventParamPtr& param)
    {
        auto it = algo_handlers.find(param->event_name);
        if (it != algo_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::Handleprojectevent(const jrsdata::ProjectEventParamPtr& param)
    {
        auto it = project_handlers.find(param->event_name);
        if (it != project_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::Handlecomponentlistviewevent(const jrsdata::ComponentListViewParamPtr& param)
    {
        auto it = component_handlers.find(param->event_name);
        if (it != component_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::HandleOnlineDebugEvent(const jrsdata::OnlineDebugViewParamPtr& param)
    {
        auto it = online_debug_handlers.find(param->event_name);
        if (it != online_debug_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::HandleOperateParamEvent(const jrsdata::OperateViewParamPtr& param)
    {
        auto it = operate_handlers.find(param->event_name);
        if (it != operate_handlers.end())
        {
            it->second(param);
        }
    }

    void Render2dController::SlotProjectUpdateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        //clear graphics  except temp_mark!  TODO HJC
        emit SignalClearGraphicsExceptLayer(LayerConverter::ToString(Layer::temp_mark), false);
        //emit SignalClearGraphicsExceptLayer(LayerConverter::ToString(Layer::temp_mark), false);
        emit SignalAddGraphics(ghs, "", false);
        if (!event_param.update_operator_param.current_id.empty())
            emit SignalGraphicsSelectSingleWithID(event_param.update_operator_param.current_id, false);
        GetRefFromIndex();
    }

    void Render2dController::SlotProjectUpdateGraphics()
    {
        TriggerProjectUpdate("update_all", {});
        emit SignalClearGraphics(false);
        model->ProjectUpdateGraphics();
    }

    //void Render2dController::SlotCurrentSelectGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    //{

    //    //emit SignalGetCurrentSelectedGraphics(ghs, Layer::component);
    //}

    void Render2dController::SlotCurrentSelectParam(UpdateOperatorParam& param_)
    {
        param_ = event_param.update_operator_param;
    }

    void Render2dController::HandleClearGraphics(const std::string& except_layer_, bool invoke_callback)
    {

        if (except_layer_.empty())
        {
            emit SignalClearGraphics(invoke_callback);
        }
        else if (except_layer_ == "pad_group")
        {
            emit SignalClearPadGroups();
        }
        else
        {
            emit SignalClearGraphicsExceptLayer(except_layer_, invoke_callback);
        }
        /** <清除所有结果图形 */
        emit SignalClearResultGraphicsShapes();
    }

    void Render2dController::HandleRequestCreateGraphics(const std::string& layer, std::weak_ptr<GraphicsAbstract> parent)
    {
        auto state = SetCurrentLayer(layer);
        if (state != jrscore::AOI_OK)
            return;
        switch (LayerConverter::FromString(layer))
        {
        case Layer::component:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::CAD;
            break;
        case Layer::mark:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::MARK;
            break;
        case Layer::barcode:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::BARCODE;
            break;
        case Layer::submark:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::SUB_MARK;
            break;
        case Layer::subbarcode:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::SUB_BARCODE;
            break;
        case Layer::badmark:
            event_param.update_operator_param.component_type = jrsdata::Component::Type::SUB_BADMARK;
            break;
        case Layer::pad:
            event_param.update_operator_param.unit_type = jrsdata::ComponentUnit::Type::PAD;
            break;
        }
        event_param.render_create_param.create_layer = layer;
        if (!parent.expired())
        {
            event_param.render_create_param.parent = parent;
            event_param.render_create_param.region_parent_layer = parent.lock()->GetId().GetString();
        }
    }

    void Render2dController::HandleGraphicsCropImage(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        auto index = render_2d_view->GetCurrentImageIndex();
        auto img_optional = model->GetProjectImageWithIndex(index);
        if (!img_optional.has_value())
            return;

        auto& img = img_optional.value();
        auto boundingbox = gh->GetBoundingbox();
        cv::Mat crop_image;
        CropAndPasteTool::CropImage(img, boundingbox, crop_image);

        auto param = std::make_shared<AlgoEventParam>();
        {
            param->region_mat = crop_image;
            param->light_type = static_cast<jrsdata::LightImageType>(index);
            param->event_name = RENDER2D_CROP_IMAGE_EVENT_NAME;
            param->topic_name = jrsaoi::RENDER_UPDATE_PROJECT_TOPIC_NAME;
            param->sub_name = "all";
            param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
            param->invoke_module_name = jrsaoi::OPERATE_MODULE_NAME;
        }
        emit SignalRender2dUpdate(param);
    }

    void Render2dController::HandleGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        if (!gh)
        {
            return;
        }
        // auto& render_edit_param = event_param.render_edit_param;
        auto& update_operator_param = event_param.update_operator_param;
        //auto& project_param_instance = model->GetProjectParamInstance();
        update_operator_param.type = UpdateOperatorParam::UpdateOperatorType::SELECT_OPERATOR;
        //update_operator_param.current_id = gh->GetId().GetString();

        model->SelectGraphicsUpdateOperator(update_operator_param, gh);

        ///!By:HJC cad  mark bad 刷新工程 2025/1/17
        if (LayerConverter::FromString(update_operator_param.current_layer) == Layer::component ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::mark ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::barcode ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::pad ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::subboard ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::region ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::subbarcode ||
            LayerConverter::FromString(update_operator_param.current_layer) == Layer::badmark

            )
        {
            model->ProjectUpdateGraphics();  //!防止切换元件不切换检测框
        }
    }


    void Render2dController::HandleGraphicsCADSelectedSingleEvent(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        if (!gh)
            return;

        event_param.cad_edit_param.current_graphics_id = gh->GetId();

        // 查找是否已经存在
        auto graphic_it = event_param.cad_edit_param.graphic_and_positions.find(gh->GetId());
        if (graphic_it == event_param.cad_edit_param.graphic_and_positions.end())
        {
            // 插入新数据
            event_param.cad_edit_param.graphic_and_positions.emplace(
                gh->GetId(), std::make_tuple(cv::Point2f{ gh->x(), gh->y() }, cv::Point2f{ INT_MIN, INT_MIN })
            );
            event_param.cad_edit_param.ori_graphics_ids.emplace_back(gh->GetId());
        }
        else
        {
            // **正确方式** 修改 `ori_point`
            std::get<0>(graphic_it->second) = cv::Point2f{ gh->x(), gh->y() };
        }
    }

    void Render2dController::HandleSubEditGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        (void)gh;
        auto& type = event_param.sub_edit_param.multi_event_param.multi_select_type;
        switch (type)
        {
        case jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_RECT:
        case jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_POLYGON:
        {
            event_param.sub_edit_param.multi_event_param.multi_select_type = MultiBoardEventParam::MultiSelectType::NONE;
            // TODO 生成多联板区域
        }break;
        case jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_CLICK:
        {
            event_param.sub_edit_param.multi_event_param.multi_select_type = MultiBoardEventParam::MultiSelectType::NONE;
            SetCurrentLayer(Layer::subboard);
        }break;
        }
    }

    void Render2dController::HandleCADEditRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto& step = event_param.cad_edit_param.cad_event_param.step;
        switch (step)
        {
        case jrsdata::CadEventParam::Step::SELECT_CUR_POSITION_FOR_ALIGNMENT:
        {
            step = jrsdata::CadEventParam::Step::NONE;

            auto temp_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
            emit SignalAddGraphicsSingle(temp_graphics, LayerConverter::ToString(Layer::temp_mark), false);
            HandleGraphicsUpdateTempMark(temp_graphics);
        }
        break;
        }
    }

    void Render2dController::HandleGraphicsUpdateTempMark(const std::shared_ptr<GraphicsAbstract>& gh)
    {
        if (!event_param.cad_edit_param.current_graphics_id.IsEmpty())
        {
            auto map_it = event_param.cad_edit_param.graphic_and_positions.find(event_param.cad_edit_param.current_graphics_id);
            if (map_it != event_param.cad_edit_param.graphic_and_positions.end())
            {
                // **正确方式** 修改 `cur_point`
                std::get<1>(map_it->second) = cv::Point2f{ gh->x(), gh->y() };//更新移动后的位置
            }
        }
    }

    void Render2dController::HandleMultipleBoardRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto& multiple_board_event_param = event_param.sub_edit_param.multi_event_param;
        if (multiple_board_event_param.create_type == jrsdata::MultiBoardEventParam::CreateType::REGULAR)
        {
            auto step = multiple_board_event_param.regular_multiple_board_param->step;

            switch (step)
            {
            case jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_COL_FLAG:
            {
                auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
                std::string id = event_param.update_operator_param.select_param.component_name + "_x_direction";
                new_graphics->SetId(id);
                emit SignalAddGraphicsSingle(new_graphics, LayerConverter::ToString(Layer::temp_mark), false);
                event_param.sub_edit_param.multi_event_param.regular_multiple_board_param->temp_mark_and_rect_map[id] = cv::Rect2f(center_x - w / 2.0, center_y - h / 2.0, w, h);
            }
            break;
            case jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_ROW_FLAG:
            {
                auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
                std::string id = event_param.update_operator_param.select_param.component_name + "_y_direction";
                new_graphics->SetId(id);
                emit SignalAddGraphicsSingle(new_graphics, LayerConverter::ToString(Layer::temp_mark), false);
                event_param.sub_edit_param.multi_event_param.regular_multiple_board_param->temp_mark_and_rect_map[id] = cv::Rect2f(center_x - w / 2.0, center_y - h / 2.0, w, h);
            }
            break;
            }
        }
        else if (multiple_board_event_param.create_type == jrsdata::MultiBoardEventParam::CreateType::IREGULAR)
        {
            auto step = multiple_board_event_param.irregular_multiple_board_param->step;
            if (step == jrsdata::MultiBoardEventParam::IrregularParam::Step::SELECT_SUBBOARD_LOCATION)
            {
                auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
                std::string id = event_param.update_operator_param.select_param.component_name + "_temp_location";
                new_graphics->SetId(id);
                emit SignalAddGraphicsSingle(new_graphics, LayerConverter::ToString(Layer::temp_mark), false);
                multiple_board_event_param.irregular_multiple_board_param->graphics_name = id;
            }
        }
        event_param.sub_edit_param.multi_event_param.create_type = MultiBoardEventParam::CreateType::NONE;


    }

    /*void Render2dController::HandleSubEditRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto& step = event_param.sub_edit_param.multi_event_param.step;
        switch (step)
        {
        case jrsdata::MultiBoardEventParam::Step::SELECT_TEMPLATE_FLAG:
        {
            auto temp_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
            emit SignalAddGraphicsSingle(temp_graphics, LayerConverter::ToString(Layer::temp_mark), false);
            event_param.sub_edit_param.flag_id = temp_graphics->GetId().GetString();
        }
        break;
        case jrsdata::MultiBoardEventParam::Step::SELECT_COL_FLAG:
        {
            auto temp_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
            emit SignalAddGraphicsSingle(temp_graphics, LayerConverter::ToString(Layer::temp_mark), false);
            event_param.sub_edit_param.flag_cols_id = temp_graphics->GetId().GetString();
        }
        break;
        case jrsdata::MultiBoardEventParam::Step::SELECT_ROW_FLAG:
        {
            auto temp_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
            emit SignalAddGraphicsSingle(temp_graphics, LayerConverter::ToString(Layer::temp_mark), false);
            event_param.sub_edit_param.flag_rows_id = temp_graphics->GetId().GetString();
        }
        break;
        default:
            return;
        }
        step = jrsdata::MultiBoardEventParam::Step::NONE;
    }*/

    void Render2dController::HandleEditSubRegionRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto& type = event_param.sub_edit_param.multi_event_param.multi_select_type;
        switch (type)
        {
        case jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_RECT:
        {
            event_param.sub_edit_param.multi_event_param.multi_select_type = MultiBoardEventParam::MultiSelectType::NONE;
            auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
            new_graphics->SetId(event_param.update_operator_param.select_param.subboard_name);
            new_graphics->settings.SetLayer(LayerConverter::ToString(Layer::subboard));
            SlotGraphicsUpdated({ new_graphics });
            //SlotGraphicsCreated({ new_graphics });
        }
        break;
        case jrsdata::MultiBoardEventParam::MultiSelectType::SELECT_POLYGON:
        {
            //TODO 
            event_param.sub_edit_param.multi_event_param.multi_select_type = MultiBoardEventParam::MultiSelectType::NONE;
        }
        //SlotGraphicsCreated
        break;
        }
    }

    void Render2dController::HandleCreateGraphicsRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
        new_graphics->settings.SetLayer(event_param.render_create_param.create_layer);
        if (!event_param.render_create_param.create_name.empty())
        {
            new_graphics->SetId(event_param.render_create_param.create_name);
        }
        auto parent = event_param.render_create_param.GetParent();
        if (parent)
        {
            new_graphics->SetParent(parent);
        }

        SlotGraphicsCreated({ new_graphics });

        emit SignalGraphicsSelectSingleWithID(new_graphics->GetId().GetString(), true);

        //if (!event_param.render_create_param.create_name.empty())
        //{
        //    emit SignalGraphicsSelectSingleWithID(event_param.render_create_param.create_name, true);
        //}
        event_param.render_create_param.Reset();
    }

    void Render2dController::HandleDrawTempRegionDone(float center_x, float center_y, float w, float h)
    {
        event_param.temp_region_edit_param.edit_mode = 0;

        auto param = std::make_shared<jrsdata::GraphicsUpdateProjectEventParam>();
        param->temp_region = cv::Rect(center_x - w / 2, center_y - h / 2, w, h);

        param->topic_name = jrsaoi::RENDER_TRIGGER_TOPIC_NAME;
        param->sub_name = RENDER_SUB_NAME;
        param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        param->invoke_module_name = jrsaoi::OPERATE_MODULE_NAME;
        param->event_name = RENDER2D_DRAW_TEMP_REGION_DONE_EVENT_NAME;

        emit SignalRender2dUpdate(param);
    }

    void Render2dController::HandleDrawShow3DRegionDone(float center_x, float center_y, float w, float h)
    {
        event_param.show_3d_region_edit_param.edit_mode = 0;
        cv::Rect crop_rect(center_x - w / 2, center_y - h / 2, w, h);
        cv::Mat height_image;
        std::vector<cv::Mat> texture_images;
        model->GetSelectShow3DImages(crop_rect, height_image, texture_images);
        PlaneProjectByHist1(height_image, height_image, 0.010f, 0.010f);
        emit SignalShow3DView(height_image, 0.01f, 0.01f, texture_images);
    }

    void Render2dController::HandlePadEditRegionSelected(float center_x, float center_y, float w, float h)
    {
        auto& pad_edit_param = event_param.pad_edit_param;
        if (pad_edit_param.render_create_param.IsEmpty())
        {
            pad_edit_param.render_create_param = event_param.render_create_param;
            event_param.render_create_param.Reset();
        }
        auto new_graphics = std::make_shared<RectGraphics>(center_x, center_y, w, h, 0);
        new_graphics->settings.SetLayer(pad_edit_param.render_create_param.create_layer);
        if (!pad_edit_param.render_create_param.create_name.empty())
        {
            new_graphics->SetId(pad_edit_param.render_create_param.create_name);
        }
        auto parent = pad_edit_param.render_create_param.GetParent(); // pad必须有父项
        if (!parent)
        {
            return;
        }
        new_graphics->SetParent(parent);

        std::vector<std::shared_ptr<GraphicsAbstract>> pad_ghs;
        auto& pad_event_param = pad_edit_param.pad_event_param;
        switch (pad_event_param.create_type)
        {
        case ComponentUnit::PadType::SINGLE:
        {
            std::shared_ptr<GraphicsAbstract> gh;
            SignalCreateGraphics(gh, static_cast<int>(GraphicsFlag::pad_group), LayerConverter::ToString(Layer::pad), "", parent);
            auto pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(gh);

            auto sig_gh = std::make_shared<PadGraphics>(center_x, center_y, w, h, 0, 0, pad_group);
            sig_gh->settings.SetLayer(pad_edit_param.render_create_param.create_layer);
            sig_gh->SetParent(parent);

            //pad_group->SetParentGraphics(parent);
            pad_group->AddSubGraphics(sig_gh);
            pad_group->SetGroupType(PadGraphicsGroup::PadGroupType::SINGLE);
            pad_group->DoAlign();

            pad_ghs.emplace_back(sig_gh);
        }
        break;
        case ComponentUnit::PadType::ARRAY:
        {
            // 创建一个新的 PadGraphics 对象
            auto temp_array_gh = std::make_shared<PadGraphics>(center_x, center_y, w, h, 0, 0, std::weak_ptr<PadGraphicsGroup>());
            temp_array_gh->settings.SetLayer(pad_edit_param.render_create_param.create_layer);
            temp_array_gh->SetParent(parent);

            // 初始化 first_gh_weak 和 end_gh_weak
            if (!pad_edit_param.first_gh)
            {
                pad_edit_param.first_gh = temp_array_gh;
                //event_param.render_create_param.create_layer = "";
                return;
            }
            if (!pad_edit_param.end_gh)
            {
                pad_edit_param.end_gh = temp_array_gh;
            }

            // 创建并初始化 PadGraphicsGroup
            std::shared_ptr<GraphicsAbstract> gh;
            SignalCreateGraphics(gh, static_cast<int>(GraphicsFlag::pad_group), LayerConverter::ToString(Layer::pad), "", parent);
            auto pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(gh);

            if (!pad_group)
            {
                Log_ERROR("Failed to create PadGraphicsGroup.");
                event_param.render_create_param.create_layer = "";
                return;
            }

            // 获取 first_gh 和 end_gh
            auto first_gh = pad_edit_param.first_gh;
            auto end_gh = pad_edit_param.end_gh;

            if (!first_gh || !end_gh)
            {
                Log_ERROR("Failed to get valid first or end PadGraphics.");
                event_param.render_create_param.create_layer = "";
                return;
            }

            // 为 first_gh 和 end_gh 设置 pad_group
            auto first_pad_gh = std::dynamic_pointer_cast<PadGraphics>(first_gh);
            auto end_pad_gh = std::dynamic_pointer_cast<PadGraphics>(end_gh);

            if (first_pad_gh && end_pad_gh)
            {
                first_pad_gh->SetPadGroupPtr(pad_group);
                end_pad_gh->SetPadGroupPtr(pad_group);
            }
            else
            {
                Log_ERROR("Invalid PadGraphics for first or end graphics.");
                event_param.render_create_param.create_layer = "";
                return;
            }

            // 配置 pad_group
            //pad_group->SetParentGraphics(parent);
            pad_group->AddSubGraphics(first_gh);
            pad_group->AddSubGraphics(end_gh);

            pad_group->SetGroupType(PadGraphicsGroup::PadGroupType::ARRAY);
            pad_group->DoAlign();

            // 获取方向并保存到 pad_ghs
            //auto dir = pad_group->GetDirection();
            pad_ghs.push_back(first_gh);
            pad_ghs.push_back(end_gh);
            //显示
            ShowPadAlterView(pad_group);

        }
        break;
        case ComponentUnit::PadType::MIRROR:
        {
            std::shared_ptr<GraphicsAbstract> gh;
            SignalCreateGraphics(gh, static_cast<int>(GraphicsFlag::pad_group), LayerConverter::ToString(Layer::pad), "", parent);
            auto pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(gh);

            pad_group->SetDirection(PadGraphicsGroup::PadDirection::UNKNOWN);
            pad_group->SetGroupType(PadGraphicsGroup::PadGroupType::MIRROR);
            //pad_group->SetParentGraphics(parent);

            auto pad_group_clone = std::dynamic_pointer_cast<PadGraphicsGroup>(pad_group->Clone());


            float p_x = parent->x();
            float p_y = parent->y();
            float xoffset = p_x - center_x;
            float yoffset = p_y - center_y;

            std::vector<std::pair<float, float>> offsets = {
            { xoffset, yoffset },
            { -xoffset,-yoffset }  //對稱效果
            };
            for (auto& offset : offsets)
            {
                auto array_gh = std::make_shared<PadGraphics>(p_x + offset.first, p_y + offset.second, w, h, 0, 0, pad_group);

                array_gh->settings.SetLayer(new_graphics->settings.GetLayer());
                array_gh->SetParent(parent);
                if (pad_group->_sub_graphics.empty())
                {
                    pad_group->AddSubGraphics(array_gh);
                }
                else
                {
                    array_gh->SetPadGroupPtr(pad_group_clone);
                    pad_group_clone->AddSubGraphics(array_gh);
                }

                pad_ghs.emplace_back(array_gh);
            }
            pad_group_clone->DoAlign(false);
        }
        break;
        //case ComponentUnit::PadType::SINGLE:
        //{
        //    pad_ghs.emplace_back(new_graphics);
        //}
        //break;
        default:
            event_param.render_create_param.create_layer = "";
            return;
        }

        if (pad_ghs.empty())
        {
            event_param.render_create_param.create_layer = "";
            pad_edit_param.render_create_param.Reset();
            return;
        }
        pad_edit_param.render_create_param.Reset();
        SlotGraphicsCreated(pad_ghs);

        emit SignalGraphicsSelectSingleWithID(pad_ghs[0]->GetId().GetString(), true);


        event_param.pad_edit_param.pad_event_param.step = jrsdata::PadEventParam::Step::NONE;
        event_param.render_create_param.create_layer = "";
    }

    void Render2dController::TriggerProjectUpdate(std::string operator_name, const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
    {
        auto param = std::make_shared<jrsdata::GraphicsUpdateProjectEventParam>();

        for (auto& gh : ghs)
        {
            if (!gh)
            {
                continue;
            }
            auto& layer = gh->settings.GetLayer();
            auto actual_name = model->GetGraphicsActualName(gh);
            param->graphics_map[layer].push_back(actual_name);//TODO:需要修改这些参数，删除多个元件等信息  参数传递 必须包含子板信息、元件信息、等

            //TODO :更改类型，获取component 的具体类型，不可以使用名字单一判断 HJC 2024/12/16
            if (gh->settings.GetLayer() == LayerConverter::ToString(Layer::search_region) || gh->settings.GetLayer() == LayerConverter::ToString(Layer::subboard))
            {
                continue;
            }
            std::pair<jrsdata::Component::Type, std::string> type_and_component_name;
            type_and_component_name =
            {
                event_param.update_operator_param.component_type ,
                event_param.update_operator_param.select_param.component_name
            };
            param->graphics_and_select_units = { layer ,{actual_name,
                event_param.update_operator_param.select_param.subboard_name,
                type_and_component_name,
                event_param.update_operator_param.select_param.unit_name,
                event_param.update_operator_param.select_param.window_name,
                event_param.update_operator_param.select_param.subwindow_name}
            };
            if (param->graphics_and_select_units.second.window_name.empty()
                && LayerConverter::FromString(layer) == Layer::region &&
                operator_name == "create")
            {
                param->graphics_and_select_units.second.window_name = actual_name;
            }
        }

        if (operator_name == "update_all")
        {
            param->event_name = jrsaoi::PROJECT_UPDATE_EVENT_NAME; // 全部更新
        }
        else if (operator_name == "create")
        {
            param->event_name = jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME; // 添加
        }
        else if (operator_name == "delete")
        {
            param->event_name = jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME; // 删除
        }
        else if (operator_name == "select")
        {
            param->event_name = jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME; // 选择
        }
        else if (operator_name == "update")
        {
            param->event_name = jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME; // 更新
        }
        else
        {
            return;
        }
        param->topic_name = jrsaoi::RENDER_UPDATE_PROJECT_TOPIC_NAME;
        param->sub_name = "all";
        param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        param->invoke_module_name = jrsaoi::OPERATE_MODULE_NAME;
        //param->invoke_module_name = jrsaoi::SHOWLIST_MODULE_NAME;
        emit SignalRender2dUpdate(param);
    }

    void Render2dController::GetIndexFromRef()
    {
        auto& update_operator_param = event_param.update_operator_param;
        //auto& render_edit_param = event_param.render_edit_param;
        auto& param = update_operator_param.select_param;
        auto& object = update_operator_param.select_object;
        std::vector<std::weak_ptr<GraphicsAbstract>> weak_ghs{
            object.subboard_graphics,
            object.component_graphics,
            object.unit_graphics,
            object.window_graphics,
            object.subwindow_graphics,
            //render_edit_param.g_current_mark,
            //render_edit_param.g_current_barcode,
            //render_edit_param.g_current_subboard,
            //render_edit_param.g_current_cad,
            //render_edit_param.g_current_pad,
            //render_edit_param.g_current_sub_mark ,
            //render_edit_param.g_current_sub_barcode,
            //render_edit_param.g_current_region ,
            //render_edit_param.g_current_sub_region
        };
        std::vector<std::string*> ref_ids{
            &param.subboard_name,
            &param.component_name,
            &param.unit_name,
            &param.window_name,
            &param.subwindow_name,
            // &update_operator_param.mark_name,
            // &update_operator_param.barcode_name,
            // &update_operator_param.subboard_name,
            // &update_operator_param.component_name,
            // &update_operator_param.pad_name,
            // &update_operator_param.submark_name,
            // &update_operator_param.subbarcode_name,
            // &update_operator_param.window_name,
            //&update_operator_param.subwindow_name
        };
        if (weak_ghs.size() != ref_ids.size())
            return;

        for (int i = 0; i < weak_ghs.size(); ++i)
        {
            if (auto shared_ptr = weak_ghs[i].lock())
            {
                *ref_ids[i] = shared_ptr->GetId().GetString();
            }
            else
            {
                *ref_ids[i] = "";
            }
        }
    }

    void Render2dController::GetRefFromIndex()
    {
        auto& update_operator_param = event_param.update_operator_param;
        //auto& render_edit_param = event_param.render_edit_param;
        auto& param = update_operator_param.select_param;
        auto& object = update_operator_param.select_object;

        std::vector<GraphicsID> ids{
            param.subboard_name,
            model->GetNameInRender(param.subboard_name,param.component_name),
            model->GetNameInRender(param.subboard_name,param.component_name,param.unit_name),
            model->GetNameInRender(param.subboard_name,param.component_name,param.unit_name,update_operator_param.model_name,param.window_name),
            model->GetNameInRender(param.subboard_name,param.component_name,param.unit_name,update_operator_param.model_name,param.window_name,param.subwindow_name),
            //param.component_name,
            //param.unit_name,
            //param.window_name,
            //param.subwindow_name,
        };

        std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
        emit SignalGetGraphics(ghs, ids);
        if (ghs.size() != ids.size())
            return;

        int index = 0;
        object.subboard_graphics = ghs[index++];
        object.component_graphics = ghs[index++];
        object.unit_graphics = ghs[index++];
        object.window_graphics = ghs[index++];
        object.subwindow_graphics = ghs[index++];
        // render_edit_param.g_current_mark = ghs[index++];
        // render_edit_param.g_current_barcode = ghs[index++];
        // render_edit_param.g_current_subboard = ghs[index++];
        // render_edit_param.g_current_cad = ghs[index++];
        // render_edit_param.g_current_pad = ghs[index++];
        // render_edit_param.g_current_sub_mark = ghs[index++];
        // render_edit_param.g_current_sub_barcode = ghs[index++];
        // render_edit_param.g_current_region = ghs[index++];
        // render_edit_param.g_current_sub_region = ghs[index++];
    }

    void Render2dController::ResetEvent()
    {
        event_param.render_create_param = RenderCreateParam();
        // event_param.render_edit_param = RenderEditParam();
        event_param.sub_edit_param = SubEditParam();
        event_param.cad_edit_param = CADEditParam();
        event_param.algo_event_param = nullptr;
    }

    void Render2dController::CropImage(cv::Mat& crop_image, const cv::RotatedRect& boundingbox, int image_index)
    {
        auto img_optional = model->GetProjectImageWithIndex(image_index);
        if (!img_optional.has_value())
            return;

        auto& img = img_optional.value();
        CropAndPasteTool::CropImage(img, boundingbox, crop_image);
    }

    //void Render2dController::DealCADShortcutOperate(const jrsdata::RenderEventParamPtr param)
    //{
    //    auto& multi_param = param->multi_param;
    //    ///**<*/
    //    //std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
    //    //render_2d_view->GetLayerGraphics(ghs, LayerConverter::ToString(Layer::subboard));
    //    //if (ghs.empty())
    //    //    return;
    //    model->SubBoardTransform(event_param.update_operator_param.select_param.subboard_name, static_cast<int>(multi_param.transform_type));
    //}


    void Render2dController::SimulateKeyCtrlC()
    {
        //这里模拟键盘Ctrl+C
        QKeyEvent* keyPress = new QKeyEvent(QEvent::KeyPress, Qt::Key_C, Qt::ControlModifier);
        QKeyEvent* keyRelease = new QKeyEvent(QEvent::KeyRelease, Qt::Key_C, Qt::ControlModifier);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyPress);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyRelease);
    }

    void Render2dController::SimulateKeyCtrlV()
    {
        //这里模拟键盘Ctrl+V
        QKeyEvent* keyPress = new QKeyEvent(QEvent::KeyPress, Qt::Key_V, Qt::ControlModifier);
        QKeyEvent* keyRelease = new QKeyEvent(QEvent::KeyRelease, Qt::Key_V, Qt::ControlModifier);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyPress);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyRelease);
    }

    void Render2dController::SimulateDel()
    {
        // 模拟发送Delete键
        QKeyEvent* keyPress = new QKeyEvent(QEvent::KeyPress, Qt::Key_Delete, Qt::NoModifier);
        QKeyEvent* keyRelease = new QKeyEvent(QEvent::KeyRelease, Qt::Key_Delete, Qt::NoModifier);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyPress);
        QCoreApplication::sendEvent(QApplication::focusWidget(), keyRelease);
    }
    std::shared_ptr<GraphicsAbstract> Render2dController::GetDetectRegionWindow(const cv::RotatedRect& rect_, const std::string& componet_and_algo_name_)
    {
        std::shared_ptr<RectGraphics> detect_region = std::make_shared<RectGraphics>(rect_.center.x, rect_.center.y,
            rect_.size.width, rect_.size.height, rect_.angle);
        detect_region->SetId(componet_and_algo_name_);
        detect_region->settings.SetLayer(LayerConverter::ToString(Layer::detect_region));

        //RenderConfig render_config;
        //if (is_ok_)
        //{
        //    render_config = { 0, 255, 0,255,1 };
        //}
        //else
        //{
        //    render_config = { 255, 0, 0,255,1 };
        //}
        //detect_region->settings.SetPrivateStyle(render_config, render_config);
        return detect_region;
    }

    void Render2dController::AddDetectRegionWindows(const std::vector<std::tuple<bool, cv::RotatedRect, std::string, cv::Mat>>& detect_regions_)
    {
        //SignalClearLayerGraphics(LayerConverter::ToString(Layer::detect_region), false);
        emit SignalClearResultGraphicsShapes();
        std::vector<GraphicsID> ids;
        //int cnt = 0;
        GraphicsPtrVec detect_region_ghs;
        std::unordered_map<std::string, bool> detect_window_name_and_result_;
        for (auto& detect_region : detect_regions_)
        {
            auto& [state, rect, detect_name, res_img] = detect_region;
            std::string subboard_name = event_param.update_operator_param.select_param.subboard_name;
            std::string component_name = event_param.update_operator_param.select_param.component_name;
            std::string unit_name = event_param.update_operator_param.select_param.unit_name;
            std::string model_name = event_param.update_operator_param.model_name;
            std::string window_name = detect_name;

            /** <pad 名称拼接  TODO！软件需要统一！HJC 2025/1/21 */
            if (detect_name.find("pad") != std::string::npos)
            {
                auto sub_strs = jrscore::AOITools::SplitString(detect_name, ';');
                if (sub_strs.size() == 2)
                {
                    window_name = sub_strs[0];
                    auto unit_name_split_strs = jrscore::AOITools::SplitString(event_param.update_operator_param.select_param.unit_name, '_');
                    if (unit_name_split_strs.size() >= 3)
                    {
                        unit_name_split_strs[unit_name_split_strs.size() - 1] = sub_strs[sub_strs.size() - 1]; //获取 unit ID
                    }
                    unit_name = jrscore::AOITools::JoinString(unit_name_split_strs, '_');
                }
            }
            auto gh_name = model->GetNameInRender(subboard_name, component_name, unit_name, model_name, window_name);
            ids.push_back(gh_name);
            detect_window_name_and_result_[gh_name] = state;

            std::shared_ptr<GraphicsAbstract> gh;
            emit SignalGetGraphicsSingle(gh, { gh_name });
            if (gh && !res_img.empty())
            {
                /** <添加渲染图像 */
                //GraphicsShape res_img_temp(GraphicsShape::Type::Image, { 0,0,0,255 }, { gh->x(),gh->y() }, { 0,0 }, 0);
                //res_img_temp.image = res_img;
                //emit SignalAddResultGraphicsShapes(res_img_temp);
                /** <添加轮廓点 */
                cv::Point2f origin_point{ gh->x() - gh->w() / 2,gh->y() - gh->h() / 2 };
                auto contours = jcvtools::JrsCVTools::ConvertMarkToContours(res_img);
                GraphicsShape res_contour_temp(GraphicsShape::Type::Polygon, { 0,255,0,255 }, { 0,0 });;
                res_contour_temp.polygon_points = model->MarkImageToContours(res_img, origin_point);
                emit SignalAddResultGraphicsShapes(res_contour_temp);
            }


            GraphicsShape reuslt_gh_shape(GraphicsShape::Type::Rect, { 255,0,0,255 }, rect.center, rect.size, rect.angle);
            emit SignalAddResultGraphicsShapes(reuslt_gh_shape);
        }
        ShowDetectResult(ids, detect_window_name_and_result_);
    }

    void Render2dController::ShowDetectResult(const std::vector<GraphicsID>& ids_, const std::unordered_map<std::string, bool>& name_and_state_result_)
    {
        std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
        emit SignalGetGraphics(ghs, ids_);
        if (ghs.size() != ids_.size())
            return;
        for (auto& gh : ghs)
        {
            if (!gh)
            {
                continue;
            }
            auto name_state_pair = name_and_state_result_.find(gh->GetId().GetString());
            if (name_state_pair == name_and_state_result_.end())
            {
                continue;
            }

            RenderConfig forcus_render_config; // 不选是暗红色，选中为红色。目前不好实现。TODO： 3/20 HJC
            RenderConfig display_render_config; // 不选是暗红色，选中为红色。目前不好实现。TODO： 3/20 HJC
            if (name_state_pair->second)
            {
                forcus_render_config = { 0, 255, 0,255,1 };
                display_render_config = { 0, 150, 0,255,1 };
            }
            else
            {
                forcus_render_config = { 255, 0, 0,255,1 };
                display_render_config = { 150, 0, 0,255,1 };
            }
            gh->settings.SetPrivateStyle(display_render_config, forcus_render_config);
        }
        emit SignalAddGraphics(ghs, "", false);
    }

    Q_INVOKABLE void Render2dController::ShowDetectResult(const jrsdata::ViewParamBasePtr& param_)
    {
        auto algo_param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
        if (!algo_param || !algo_param->component_detect_results.has_value())
        {
            Log_ERROR("component_detect_results is null");
            return;
        }
        std::vector<GraphicsID> ids;
        std::unordered_map<std::string, bool> cad_name_and_state_map;
        for (auto& component_detect : algo_param->component_detect_results.value())
        {
            auto subboard_and_component_name = model->GetNameInRender(component_detect.sub_board_name, component_detect.component_name);
            cad_name_and_state_map[subboard_and_component_name] = component_detect.result_status;
            ids.push_back(subboard_and_component_name);
        }
        ShowDetectResult(ids, cad_name_and_state_map);
    }
    void Render2dController::ControlPanelEventOperate(const jrsdata::ViewParamBasePtr& param_)
    {
        model->Update(param_);
        if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME)
        {
            _is_auto_flow_work = true;
            if (_current_img_set == dynamic_adjust_image_set)//处于动态调试中，不能更改
            {
                return;
            }
            for (auto& light : jrsdata::all_img_types)
            {
                if (light == jrsdata::LightImageType::RGB || light == jrsdata::LightImageType::OTHERS)
                {
                    continue;
                }
                emit SignalClearImage(default_image_set, static_cast<int>(light));  //清除所有图像数据
            }
            emit SignalCameraScaleModeChange(static_cast<int>(CameraScaleMode::AUTO_SCALE));
            emit SignalCameraResetModeChange(static_cast<int>(CameraResetMode::AlignCenter));
            render_2d_view->setDisabled(true);

            //ShowEntiretyImages(true);
        }
        else if (param_->event_name == jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME)
        {
            _is_auto_flow_work = false;
            if (_current_img_set != dynamic_adjust_image_set)  //动态调试未开启
            {
                model->ProjectUpdateGraphics();
                render_2d_view->setDisabled(false);
                ShowEntiretyImages();
            }
        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            emit SignalClearImage(_current_img_set, -1);//清楚动态调试图片
            _current_img_set = default_image_set;
            emit SignalCameraScaleModeChange(static_cast<int>(CameraScaleMode::AUTO_SCALE));
            emit SignalCameraResetModeChange(static_cast<int>(CameraResetMode::AlignCenter));
            ///<关闭后切换显示图片


            if (_is_auto_flow_work) //自动流程过程中 结束在线调试需要锁死界面，防止错误操作
            {
                render_2d_view->setDisabled(true);
                //更改画布大小
                cv::Size board_size;
                model->GetBoardSize(board_size.width, board_size.height);
                render_2d_view->SlotRenderCanvasSizeChange(board_size.width, board_size.height);
                render_2d_view->SlotShowImageChange(_current_img_set, static_cast<int>(jrsdata::LightImageType::RGB));
            }
            else
            {
                ShowEntiretyImages();
            }
            model->ProjectUpdateGraphics();
        }
        else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
        {
            _current_img_set = dynamic_adjust_image_set;
            //隐藏所有图像
            render_2d_view->SlotShowImageChange(_current_img_set, static_cast<int>(jrsdata::LightImageType::RGB));
            render_2d_view->SlotClearGraphics(false);// 清楚所有图像
            if (_is_auto_flow_work) //自动流程过程中 开启动态调试需要解除界面锁死
            {
                render_2d_view->setDisabled(false);
            }


        }
    }
    void Render2dController::ShowEntiretyImages(bool is_load_all_images_)
    {
        auto image_map = model->GetProjectImage();
        if (!image_map.has_value())
            return;

        emit SignalClearImage(-1, -1);//清楚所有大图

        std::unordered_map<int, cv::Mat> key_and_imgs;
        auto& imgs = *image_map.value();

        for (const auto& [key, val] : imgs)
        {
            key_and_imgs[key] = val;
            if (!is_load_all_images_)
                break;
        }

        QMetaObject::invokeMethod(
            this,
            [key_and_imgs, this]() {
                GraphicsImage graphics_img;
                graphics_img.key_and_imgs = key_and_imgs;
                graphics_img.z = image_layer_bottom;
                graphics_img.current_show_img = 0;
                graphics_img.set_key = default_image_set;
                render_2d_view->SlotCreateImages(graphics_img);
            },
            Qt::QueuedConnection
        );
    }
    void Render2dController::ShowPadAlterView(std::shared_ptr<GraphicsAbstract> gh_group_)
    {
        if (gh_group_)
        {
            _add_pad_view->SlotUpdateAddView(gh_group_);
        }
        else
        {
            bool is_valid_group = false;
            std::vector<std::shared_ptr<GraphicsAbstract>> current_select_ghs;
            emit SignalGetCurrentSelectedGraphics(current_select_ghs, "");
            for (auto& cur_gh : current_select_ghs)
            {
                auto gh_group = std::dynamic_pointer_cast<PadGraphicsGroup>(cur_gh);
                if (gh_group)
                {
                    is_valid_group = true;
                    _add_pad_view->SlotUpdateAddView(gh_group);
                    break;
                }
            }
            if (!is_valid_group)
            {
                JRSMessageBox_INFO("提示", "请先选择一个Pad再进行编辑", jrscore::MessageButton::Ok);
                return;
            }
        }
        if (_add_pad_view->isVisible())
        {
            _add_pad_view->raise();  // 提升到顶层
            _add_pad_view->activateWindow();  // 激活窗口，使其获得焦点
        }
        else
        {
            _add_pad_view->show();

        }

    }
    void Render2dController::SelectedSpeficComponent(const std::string& component_name_, const std::string& sub_board_name_, const std::string& part_number_name_)
    {
        (void)sub_board_name_;
        auto& id = component_name_;

        auto& project_param_instance = model->GetProjectParamInstance().GetProjectDataProcessInstance();
        std::optional<std::reference_wrapper<jrsdata::Component>> component;
        if (part_number_name_ == "mark")
        {
            component = project_param_instance->ReadMarkRef(id);
        }
        else if (part_number_name_ == "barcode")
        {
            component = project_param_instance->ReadBarcodeRef(id);
        }
        else if (part_number_name_ == "subbarcode")
        {
            component = project_param_instance->ReadSubBoardBarcodeRef(id);
        }
        else if (part_number_name_ == "badmark")
        {
            component = project_param_instance->ReadSubBadMarkRef(id);
        }
        else
        {
            component = project_param_instance->ReadCADRef(id);
        }


        if (!component.has_value())
        {
            return;
        }

        auto name = model->GetNameInRender(component->get().subboard_name, component->get().component_name);
        GraphicsPtr gh;
        ReadGraphics(gh, name);
        if (!gh)
        {
            return;
        }

        // 响应选中信号,但是不触发回调,因此主动调用选中处理函数
        emit SignalGraphicsSelectSingle(gh, false);
        //emit SignalMoveCameraToGraphics(gh);
        HandleGraphicsSelectedSingle(gh);
        TriggerProjectUpdate("select", { gh });

    }

    void Render2dController::MultipleBoardCreate(const jrsdata::RenderEventParamPtr& param_)
    {
        auto multi_param_ptr = param_->multi_param_ptr;
        if (multi_param_ptr->create_type == jrsdata::MultiBoardEventParam::CreateType::REGULAR)
        {
            auto res = _regular_multiple_boards_ptr->MultipleBoardsUpdate(multi_param_ptr);
            if (multi_param_ptr->regular_multiple_board_param == std::nullopt)
            {
                /** <清空数据 */
                event_param.sub_edit_param.multi_event_param.regular_multiple_board_param = std::nullopt;
            }
            if (!event_param.sub_edit_param.multi_event_param.regular_multiple_board_param)
            {
                event_param.sub_edit_param.multi_event_param.regular_multiple_board_param = multi_param_ptr->regular_multiple_board_param;
            }
            event_param.sub_edit_param.multi_event_param.create_type = multi_param_ptr->create_type;
            event_param.sub_edit_param.multi_event_param.regular_multiple_board_param->step = multi_param_ptr->regular_multiple_board_param->step;
            if (res == jrscore::AOI_OK && !param_->multi_param_ptr->regular_multiple_board_param->component_and_images.second.empty())
            {
                param_->topic_name = jrsaoi::RENDER_TRIGGER_TOPIC_NAME;
                param_->sub_name = jrsaoi::RENDER_SUB_NAME;
                param_->module_name = jrsaoi::OPERATE_MODULE_NAME;
                param_->invoke_module_name = jrsaoi::OPERATE_MODULE_NAME;
                emit SignalRender2dUpdate(param_);
            }

        }
        else if (multi_param_ptr->create_type == jrsdata::MultiBoardEventParam::CreateType::IREGULAR)
        {
            event_param.sub_edit_param.multi_event_param.create_type = multi_param_ptr->create_type;
            if (!event_param.sub_edit_param.multi_event_param.irregular_multiple_board_param)
            {
                event_param.sub_edit_param.multi_event_param.irregular_multiple_board_param = multi_param_ptr->irregular_multiple_board_param;
            }
            else
            {
                event_param.sub_edit_param.multi_event_param.irregular_multiple_board_param->step = multi_param_ptr->irregular_multiple_board_param->step;
            }
            _irregular_multiple_boards_ptr->MultipleBoardsUpdate(multi_param_ptr);
        }

    }

}