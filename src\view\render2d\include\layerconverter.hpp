﻿/*********************************************************************
 * @brief  层级枚举<->字符串转换.
 *
 * @file   layerconverter.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#ifndef LAYER_CONVERTER_HPP
#define LAYER_CONVERTER_HPP

#include <string>
#include <stdexcept>
#include <unordered_map>

namespace jrsaoi
{
    enum class Layer : int
    {
        unknown = -1, ///< 为避免在使用时出现未定义的值,主动定义一个未知值
        component,    ///< 元件
        pad,          ///< 元件焊盘
        mark,         ///< 基准点
        region,       ///< 检测区域
        subregion,    ///< 子检测区域
        barcode,      ///< 条形码
        subbarcode,   ///< 子板条形码
        submark,      ///< 子板基准点
        badmark,   ///< 子板坏板标记
        subboard,     ///< 子板
        board,        ///< 整板
        temp_mark,    ///< 临时基准点
        search_region,  ///< 搜索范围 
        detect_region,  ///< 检测区块
    };

    class LayerConverter
    {
    public:
        static constexpr const char* ToString(const Layer& layer)
        {
            switch (layer)
            {
            case Layer::component:
                return "component";
            case Layer::pad:
                return "pad";
            case Layer::mark:
                return "mark";
            case Layer::region:
                return "region";
            case Layer::subregion:
                return "subregion";
            case Layer::barcode:
                return "barcode";
            case Layer::subbarcode:
                return "subbarcode";
            case Layer::submark:
                return "submark";
            case Layer::badmark:
                return "badmark";
            case Layer::subboard:
                return "subboard";
            case Layer::board:
                return "board";
            case Layer::temp_mark:
                return "temp_mark";
            case Layer::search_region:
                return "search_region";
            case Layer::detect_region:
                return "detect_region";
            case Layer::unknown:
                return "unknown";
            default:
                return "unknown";
            }
        }
        static Layer FromString(const std::string& layer)
        {
            static const std::unordered_map<std::string, Layer> layerMap = {
                {"component", Layer::component},
                {"pad", Layer::pad},
                {"mark", Layer::mark},
                {"region", Layer::region},
                {"subregion", Layer::subregion},
                {"barcode", Layer::barcode},
                {"subbarcode", Layer::subbarcode},
                {"submark", Layer::submark},
                {"badmark", Layer::badmark},
                {"subboard", Layer::subboard},
                {"board", Layer::board},
                {"temp_mark", Layer::temp_mark},
                {"search_region", Layer::search_region},
                {"detect_region", Layer::search_region},
            };

            auto it = layerMap.find(layer);
            if (it != layerMap.end())
            {
                return it->second;
            }
            return Layer::unknown; // 未找到，返回unknown
        }
    };
}
#endif // !LAYER_CONVERTER_HPP