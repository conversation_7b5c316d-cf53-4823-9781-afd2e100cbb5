
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#include <QPixmap>
#include <QtWidgets/qboxlayout.h>
#include <QComboBox>
#include <QCheckBox>
#include <QtWidgets/qpushbutton.h>
#include <QWidget>
#include <QLineEdit>
#include <QPainter>
#include <QMouseEvent>
#include <QHBoxLayout>
#include <vector>
#include <algorithm>
#include "vwheel.h"

#include "histogramwidget.h"

#pragma warning(pop)


void VChanelWheel::SetThreValue(int min, int max)
{
	gray_histogramwidget->SetHistThre(min, max);
}
VChanelWheel::~VChanelWheel()
{
}
void VChanelWheel::UpateThreValueSlot(int min, int max)
{
	emit UpateThreValue(min, max);
}
VChanelWheel::VChanelWheel(QWidget* parent) 
    : QWidget(parent)
{
	QVBoxLayout* hist_layout = new QVBoxLayout(this);
	gray_histogramwidget = new CustomPlotWidget(256, Qt::gray);
	gray_histogramwidget->xAxis->setRange(0, 255);

	hist_layout->addWidget(gray_histogramwidget); 
	connect(gray_histogramwidget, &CustomPlotWidget::UpdateThre,
		this, &VChanelWheel::UpateThreValueSlot);
}
void VChanelWheel::SetGrayHistValueSlot(std::vector<float>& gray_hist)
{
	gray_histogramwidget->SetHistValue(gray_hist);
}