/*****************************************************************//**
 * @file   controlconsolecontroller.h
 * @brief  主界面上控制面板的控制器类
 * @details  主界面上软件运行停止/运行状态显示/打开硬件/信息/切换用户面板的控制器类
 * <AUTHOR>
 * @date 2024.1.17
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.17         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __CONTROLCONDOLECONTROLLER_H__
#define __CONTROLCONDOLECONTROLLER_H__

#include "controllerbase.h"
namespace jrsaoi
{
    class ControlPanelView;
    class ControlPanelModel;

    class ControlPanelController :public ControllerBase
    {
        Q_OBJECT
    public:
        ControlPanelController(const std::string& name);
        ~ControlPanelController();
        /**
         * @fun Update
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun SetView
         * @brief
         * @param view_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual void SetView(ViewBase* view_param);
        /**
         * @fun SetModel
         * @brief
         * @param model_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual void SetModel(ModelBasePtr model_param);
    private:
        /**
         * @fun InitMember
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitMember();
        /**
         * @fun InitConnect
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
    public slots:

        void SlotAutoPanelUpdate(const jrsdata::ViewParamBasePtr& param_);

    signals:
        /**
         * @fun SigControlPanelUpdate
         * @param param_ [IN] 界面层参数，这里主要是运行面板模块参数
         * <AUTHOR>
         * @date 2024.10.26
         */
        void SigControlPanelUpdate(const jrsdata::ViewParamBasePtr& param_);
    private:
        /**
         * @fun AutoPanelUpdateToRender2D
         * @brief 自动面板更新到render2d
         * @param param_
         * <AUTHOR>
         * @date 2025.1.22
         */
        void AutoPanelUpdateToRender2D(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun AutoPanelUpdateToOperate
         * @brief 自动模板更新到operate
         * @param param_
         * <AUTHOR>
         * @date 2025.5.16
         */
        void AutoPanelUpdateToOperate(const jrsdata::ViewParamBasePtr& param_);

        ControlPanelView* auto_run_panel_view;
        std::shared_ptr<ControlPanelModel> model;
    };
    using ControlPanelControllerPtr = std::shared_ptr<ControlPanelController>;
}
#endif // !__CONTROLCONDOLECONTROLLER_H__
