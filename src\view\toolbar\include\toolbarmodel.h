/*****************************************************************//**
 * @file   toolbarmodel.h
 * @brief  快捷工具栏model类
 * @details    
 * <AUTHOR>
 * @date 2024.1.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __TOOLBARMODEL_H__
#define __TOOLBARMODEL_H__


#include "modelbase.h"
namespace jrsaoi
{
class ToolBarModel :
    public ModelBase
{
public:
    ToolBarModel (const std::string& name);
    ~ToolBarModel ();
    virtual int Update (const jrsdata::ViewParamBasePtr& param_) override;
    virtual int Save (const jrsdata::ViewParamBasePtr& param_)override;

};
using ToolBarModelPtr = std::shared_ptr<ToolBarModel>;
}
#endif // !__TOOLBARMODEL_H__
