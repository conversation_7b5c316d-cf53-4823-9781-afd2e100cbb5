/*****************************************************************//**
 * @file   vwheel.h
 * @brief  v通道竖向柱状图
 * @details
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSVWHEEL_H__
#define __JRSVWHEEL_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include<qwidget.h>
#pragma warning(pop)


#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
class CustomPlotWidget;
class QButtonGroup;
class QRadioButton;
class VChanelWheel : public QWidget
{
    Q_OBJECT

public:
    VChanelWheel(QWidget* parent = nullptr);
    void SetThreValue(int min, int max);
    ~VChanelWheel();
signals:
    void UpateThreValue(int min, int max);
public slots:
    void SetGrayHistValueSlot(std::vector<float>& gray_hist);
    void UpateThreValueSlot(int min, int max);
private:
    CustomPlotWidget* gray_histogramwidget = nullptr;
};
#endif