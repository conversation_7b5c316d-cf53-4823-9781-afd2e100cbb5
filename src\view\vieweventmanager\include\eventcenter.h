﻿/*****************************************************************//**
 * @file   eventcenter.h
 * @brief  AOI主界面事件触发处理中心
 * @details
 * <AUTHOR>
 * @date 2024.7.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.18         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSEVENTCENTER_H__
#define __JRSEVENTCENTER_H__
 //QT
#include <QObject>
//Custom
#include "controllerbase.h"
#include "modulehandle.h"
#include "viewmanager.h"
#include "image.hpp"
namespace jrsaoi
{
    struct DataImpl;
    class EventCenter :public QObject
    {
        Q_OBJECT
    public:
        EventCenter(ViewManagerPtr& view_manager_ptr_);
        ~EventCenter();
        /**
        * @fun Notify
        * @brief
        * @param param
        * @return
        * <AUTHOR>
        * @date 2024.7.22
        */
        int Notify(const jrsdata::ViewParamBasePtr& param);
    signals:
        //**
        // * @fun SigUpdateItem
        // * @brief
        // * @param param
        // * @date 2024.9.24
        // * <AUTHOR>
        // */
        //void SigUpdateItem(const jrsdata::ViewParamBasePtr& param);
    private slots:
        /**
         * @fun SlotOperator
         * @brief
         * @param param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotOperator(const jrsdata::ViewParamBasePtr& param);
    private:
        /**
         * @fun Init
         * @brief 初始化
         * @return
         * <AUTHOR>
         * @date 2024.7.22
         */
        int Init();
        /**
         * @fun InitMember
         * @brief 初始化成员变量
         * <AUTHOR>
         * @date 2024.7.22
         */
        void InitMember();
        /**
         * @fun InitPubSub
         * @brief 初始化订阅发布
         * <AUTHOR>
         * @date 2024.7.22
         */
        void InitPubSub();
        /**
         * @fun InitShortcutPubSub
         * @brief 初始化快捷界面按钮发布订阅
         * <AUTHOR>
         * @date 2024.8.17
         */
        void InitShortcutPubSub();
        /**
         * @fun InitOperatePubSub
         * @brief 初始化操作界面发布订阅
         * <AUTHOR>
         * @date 2024.8.17
         */
        void InitOperatePubSub();
        /**
         * @fun InitRenderPubSub
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitRenderPubSub();
        /**
         * @fun InitShowListPubSub
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitShowListPubSub();

        /**
         * @fun InitControlPanelPubSub
         * @brief 初始化运行面板订阅发布内容
         * <AUTHOR>
         * @date 2024.10.26
         */
        void InitControlPanelPubSub();
        /**
         * @fun SystemStatePubSub
         * @brief 系统状态
         * <AUTHOR>
         * @date 2024.10.21
         */
        void InitSystemStatePubSub();
        /**
         * @fun InitDetectResultUpdatePubSub
         * @brief  检测结果订阅
         * <AUTHOR>
         * @date 2025.1.22
         */
        void InitDetectResultUpdatePubSub();
        /**
         * @fun InitOnlineDebugPubSub 
         * @brief 在线调试界面订阅发布初始化
         * <AUTHOR>
         * @date 2025.4.15
         */
        void InitOnlineDebugPubSub();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数链接
         * <AUTHOR>
         * @date 2024.8.16
         */
        void InitConnect();
        /**
         * @fun RegisterAdvertise
         * @brief 注册发布主题
         * @param moudle_handle 发布订阅处理模块实例
         * @param topic_name 发布主题名称
         * @return AOI_OK成功，其他失败
         * <AUTHOR>
         * @date 2024.7.22
         */
        int RegisterAdvertise(jrscore::ModuleHandlePtr& moudle_handle, const std::string& topic_name);
        /**
         * @fun RegisterSubscriber
         * @brief 注册订阅者，主要用于注册界面端的订阅者
         * @param moudle_handle 发布订阅处理模块实列
         * @param topic_name 发布主题名称
         * @param sub_name 订阅者名称
         * @param subscriber_instance_name 订阅者响应模块
         * @return AOI_OK成功，其他失败
         * <AUTHOR>
         * @date 2024.7.22
         */
        int RegisterViewSubscriber(jrscore::ModuleHandlePtr& moudle_handle, const std::string& topic_name, const std::string& sub_name, const std::string& subscriber_instance_name);
        /**
         * @fun GenerateModuleInstance
         * @brief 获取发布订阅处理模块实例
         * @param module_name 实例名称，比如快捷按钮处理模块
         * @return
         * <AUTHOR>
         * @date 2024.7.22
         */
        jrscore::ModuleHandlePtr GenerateModuleInstance(const std::string& module_name);
        /**
         * @fun ProjectCallback
         * @brief 工程模块更新需要跟界面端交互的回调函数
         * @param project_param_ 工程参数
         * @return 成功：AOI_OK，其他失败
         * <AUTHOR>
         * @date 2024.8.18
         */
        int ProjectCallback(const jrsdata::ProjectEventParamPtr& project_param_);
        /**
         * @fun RenderCallback
         * @brief
         * @param img_buffer
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        int RenderCallback(const jrsdata::JrsImageBuffer& img_buffer);
        /**
         * @fun MotionMsgCallback
         * @brief 设备模块更新需要跟界面端交互的回调函数
         * @param device_param_ 设备状态信息参数
         * @return 成功：AOI_OK，其他失败
         * <AUTHOR>
         * @date 2024.8.20
         */
        int MotionMsgCallback(const jrsdata::DeviceParamPtr& device_param_);
        /**
         * @fun DeviceInitParamCallback 
         * @brief 设备初始化参数毁掉
         * @param device_param_ 设备状态信息参数
         * @return 成功：AOI_OK，其他失败
         * <AUTHOR>
         * @date 2025.3.6
         */
        int DeviceInitParamCallback(const jrsdata::DeviceParamPtr& device_param_);
        /**
         * @fun MotionConfigCallBack
         * @brief 运控配置文件信息回调函数
         * @param param_
         * @return 成功：AOI_OK，其他失败
         * <AUTHOR>
         * @date 2024.8.26
         */
        int OperateViewCallBack(const jrsdata::OperateViewParamPtr& param_);
        /**
        * @fun SettingParamCallback
        * @brief 设置参数回调
        * @param setting_param_ 设备状态信息参数
        * @return 成功：AOI_OK，其他失败
        * <AUTHOR>
        * @date 2024.8.20
        */
        int SettingParamCallback(const jrsdata::SettingViewParamPtr& setting_param_);
        /**
         * @fun SystemStateParamCallback
         * @brief
         * @param sys_state_param_
         * @return
         * <AUTHOR>
         * @date 2024.10.21
         */
        int SystemStateParamCallback(const jrsdata::SystemStateViewParamPtr& sys_state_param_);
        /**
         * @fun UpdateInstanceParam
         *
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.6
         */
        int UpdateInstanceParam(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun AutoRunPanelCallback
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.20
         */
        int AutoRunPanelCallback(const jrsdata::ControlPanelViewParamPtr& param_);
        /**
         * @fun DetectResultCallback
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.1.22
         */
        int DetectResultCallback(const jrsdata::AlgoEventParamPtr& param_);
        /**
         * @fun OnlineDebugViewCallback 
         * @brief 底层在线调试参数(包含各类结果以及元件信息)回调
         * @param online_param_ [IN] 在线调试参数
         * @return 成功返回AOI_OK,失败返回错误码
         * <AUTHOR>
         * @date 2025.4.14
         */
        int OnlineDebugViewCallback(const jrsdata::OnlineDebugParmPtr& online_param_);
    private:
        std::recursive_mutex   pub_sub_mutex;
        std::mutex   _operate_mutex;  //! OperateUpdate时更新参数使用 涉及多线程调用
        ViewManagerPtr view_manager_ptr;
        DataImpl* data_impl;
        std::recursive_mutex init_sys_mutex;
    };
    using EventCenterPtr = std::shared_ptr<EventCenter>;
}
#endif // !__JRSEVENTCENTER_H__
