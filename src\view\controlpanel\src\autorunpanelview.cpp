﻿#include "autorunpanelview.h"  
//qt
#include "ui_autorunpanelview.h"
#include <QPushButton>
namespace jrsaoi
{
    struct ImplData
    {
        jrsdata::AutoRunPanelViewParamPtr param_ptr;
        Ui::autorunpanelview* ui;
    
    };

    AutoRunPanelView::AutoRunPanelView(QWidget* parent /*= nullptr*/)
        : QWidget(parent),
        _impl_data(new ImplData())
    {
        Init();
    }

    AutoRunPanelView::~AutoRunPanelView()
    {
        delete _impl_data->ui;
    }


    int AutoRunPanelView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        _impl_data->param_ptr = std::dynamic_pointer_cast<jrsdata::AutoRunPanelViewParam>(param_);

        return jrscore::AOI_OK;
    }

    void AutoRunPanelView::SlotFlowSwitch()
    {
        if (_impl_data->param_ptr->flow_switch)
        {
            _impl_data->param_ptr->event_name = jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME;
            _impl_data->param_ptr->flow_switch = false;
            _impl_data->ui->toolButton_start_stop->setIcon(QIcon(":/image/run.png"));
            //_impl_data->param_ptr->machine_state_param->current_stop_state = jrsdata::MachineStateViewParam::StopState::IDLE;
        }
        else
        {
            _impl_data->param_ptr->event_name = jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME;
            _impl_data->param_ptr->flow_switch = true;
            _impl_data->ui->toolButton_start_stop->setIcon(QIcon(":/image/stop.png"));
            //_impl_data->param_ptr->machine_state_param->current_detect_state = { jrsdata::MachineStateViewParam::BoardDetectState::WAITING,jrsdata::MachineStateViewParam::DetectFlowState::REQUIRE_BOARD };
        }
        emit SigUpdateAutoRunPanelView(_impl_data->param_ptr);
    }

    int AutoRunPanelView::Init()
    {
        InitMember();
        InitView();
        InitConnect();
        return jrscore::AOI_OK;

    }


    void AutoRunPanelView::InitView()
    {
        _impl_data->ui->setupUi(this);
        _impl_data->ui->toolButton_start_stop->setIcon(QIcon(":/image/run.png"));
        _impl_data->ui->toolButton_start_stop->setIconSize(QSize(60, 60));
        _impl_data->ui->toolButton_state_reset->setIcon(QIcon(":/image/reset.png"));
        _impl_data->ui->toolButton_state_reset->setIconSize(QSize(60, 60));
        _impl_data->ui->toolButton_state_reset_error->setIcon(QIcon(":/image/reseterror.png"));
        _impl_data->ui->toolButton_state_reset_error->setIconSize(QSize(60, 60));
    }


    void AutoRunPanelView::InitMember()
    {
        _impl_data->ui = new Ui::autorunpanelview();
        _impl_data->param_ptr = std::make_shared< jrsdata::AutoRunPanelViewParam >();
        _impl_data->param_ptr->flow_switch = false;
        _impl_data->param_ptr->topic_name = jrsaoi::CONTROL_PANEL_TOPIC_NAME;
        _impl_data->param_ptr->sub_name = jrsaoi::CONTROL_PANEL_LOGIC_SUB_NAME;
        _impl_data->param_ptr->invoke_module_name = jrsaoi::WORKFLOW_MODULE_NAME;

    }


    void AutoRunPanelView::InitConnect()
    {
        connect(_impl_data->ui->toolButton_start_stop, &QPushButton::clicked, this, &AutoRunPanelView::SlotFlowSwitch);
    }

};
