/*****************************************************************//**
 * @file   mainwindow.h
 * @brief  主界面
 * @details 各个子界面都存放在此处展示给用户
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //QT
#pragma warning(push, 3)
#include <QMainWindow>
#include <QPushButton>
#pragma warning(pop)

//Custom 
#include "viewmanager.h"
#include "eventcenter.h"
#include "systemstatecontroller.h"
#include "messagebase.h"

//Third
#include "SARibbonMainWindow.h"
#include "renderer2d.h"

QT_FORWARD_DECLARE_CLASS(QSplitter)
QT_BEGIN_NAMESPACE

namespace jrsaoi
{
    class CustomTitleView;
    class StatusBarView;
    class ViewManager;
};

QT_END_NAMESPACE

class MainWindow : public SARibbonMainWindow
{
    Q_OBJECT
public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow();

    jrsaoi::ViewBase* GetInitView();
public slots:
    /**
     * @fun HideLeftDockWidget
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void HideLeftDockWidget();
    /**
     * @fun HideRightDockWidget
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void HideRightDockWidget();

signals:
    void SigActionTrigger(const jrsdata::ViewParamBasePtr&);

private:
    //-----------------属性-------------------------
    //Ui::MainWindowClass *ui;
    QString app_name;
    struct ViewMainWindow
    {
        QDockWidget* dock_left;
        QDockWidget* dock_center;
        QDockWidget* dock_right;
        jrsaoi::CustomTitleView* title_widget;
        QWidget* main_show_widget;
        //QPushButton* left_hide_btn;
        QPushButton* right_hide_btn;
        QSplitter* center_splitter;
        QSplitter* dock_splitter;
        jrsaoi::ViewManagerPtr view_manager_ptr;
        jrsaoi::MessageBase* message_box_ptr;

    };
    ViewMainWindow view_main_window;
    jrsaoi::EventCenterPtr event_center;/**< 界面事件处理中心*/
    bool is_left_hide{ false };
    bool is_right_hide{ false };
    //-----------------方法-------------------------
    /**
     * @fun Init
     * @brief 初始化主界面内容
     * @date 2024.1.15
     * <AUTHOR>
     */
    void Init();
    /**
     * @fun InitView
     * @brief 初始化主界面UI
     * @date 2024.1.15
     * <AUTHOR>
     */
    void InitView();
    /**
     * @fun InitTitleView
     * @brief 初始化标题栏
     * @date 2024.5.22
     * <AUTHOR>
     */
    void InitTitleView();
    /**
     * @fun InitMember
     * @brief 初始化成员变量
     * @date 2024.1.16
     * <AUTHOR>
     */
    void InitMember();
    /**
     * @fun InitLog
     * @brief 初始化日志器
     * @date 2024.6.11
     * <AUTHOR>
     */
    void InitLog();
    /**
     * @fun InitError
     * @brief 初始化错误码收集器
     * @date 2024.6.11
     * <AUTHOR>
     */
    void InitError();
    /**
     * @fun InitLayout
     * @brief 初始化界面布局
     * @date 2024.1.16
     * <AUTHOR>
     */
    void InitLayout();
    /**
     * @fun InitLayoutLeftDock
     * @brief 初始化左边dock容器布局
     * @date 2024.6.11
     * <AUTHOR>
     */
    void InitLayoutLeftDock();
    /**
     * @fun InitLayoutCenterDock
     * @brief 初始化中间dock容器布局
     * @date 2024.6.11
     * <AUTHOR>
     */
    void InitLayoutCenterDock();
    /**
     * @fun InitLayoutRightDock
     * @brief 初始化右边dock容器布局
     * @date 2024.6.11
     * <AUTHOR>
     */
    void InitLayoutRightDock();
    /**
     * @fun InitConnect
     * @brief 初始信号链接
     * @date 2024.1.16
     * <AUTHOR>
     */
    void InitConnect();
    /**
     * @fun InitMessage
     * @brief 初始化提示框
     * <AUTHOR>
     * @date 2024.12.3
     */
    void InitMessage();

    jrscore::MessageButton ShowMessageCallBack(const jrscore::LogLevel& level_, const std::string& title_, const std::string& msg_, const int& message_btn_);

protected:
    void closeEvent(QCloseEvent* event) override;

private:
    std::thread test1;
    std::thread test2;
    std::thread test3;

signals:
    void SigCloseAllDevice(const jrsdata::ViewParamBasePtr& param_);
};
