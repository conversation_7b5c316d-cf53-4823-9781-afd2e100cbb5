﻿/*********************************************************************
 * @brief  常用字符串函数.
 *
 * @file   stringoperation.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef JTOOLS_STRINGOPERATION_H
#define JTOOLS_STRINGOPERATION_H
#include "jtoolsapi.hpp"

#include <string>
#include <vector>

namespace jtools
{
    class JT_EXPORTS StringOperation
    {
    public:
        /**
         * @brief  整个字符串中所有字母字符转大写.
         *
         * @fun    StringToUpper
         * @param  input
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string StringToUpper(const std::string& input);

        /**
         * @brief  整个字符串中所有字母字符转小写.
         *
         * @fun    StringToLower
         * @param  input
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string StringToLower(const std::string& input);

        /**
         * @brief  按照分割符号将字符串分割.
         *
         * @fun    StringSplit
         * @param  vecSplitResult
         * @param  str
         * @param  split 分割符号
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static void StringSplit(std::vector<std::string>& vecSplitResult, const std::string& str, const char split);

        /**
         * @fun SplitString 
         * @brief 分割字符串
         * @param str [IN] 待分割的字符串
         * @param delimiter [IN] 分隔符
         * @return 返回分割后的字符串vector
         * <AUTHOR>
         * @date 2025.5.10
         */
        static std::vector<std::string> SplitString(const std::string& str, char delimiter);

        /**
         * @brief  将字符串反转.
         *
         * @fun    ReserveString
         * @param  str
         * @return 反转后的字符串
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string ReserveString(const std::string& str);

        /**
         * @brief  将字符串中的所有子串替换.
         *
         * @fun    ReplaceString
         * @param  str
         * @param  from 被替换的子字符串
         * @param  to 替换后的子字符串
         * @return 替换后的字符串
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string ReplaceString(const std::string& str, const std::string& from, const std::string& to);

        /**
         * @brief  去除字符串首尾的空白字符.
         * @fun    Trim
         * @param  str
         * @return 处理后的字符串
         *
         * @date   2024.04.03
         * <AUTHOR>
         */
        static std::string Trim(const std::string& str);
        /**
         * @fun GetPrefixAndNumber
         * @brief  将字符串分割为前缀和数字
         * @param src_str_
         * @return
         * <AUTHOR>
         * @date 2025.1.2
         */
        static std::pair<std::string, int> GetPrefixAndNumber(const std::string& src_str_);
      
    };
}

#endif // JTOOLS_STRINGOPERATION_H