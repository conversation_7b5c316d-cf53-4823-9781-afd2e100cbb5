﻿#include "utf8convert.hpp"
#include "dlgcreatedetectwindow.h"
#include "coreapplication.h"
#include "ui_dlgcreatedetectwindow.h"

DlgCreateDetectWindow::DlgCreateDetectWindow(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::DlgCreateDetectWindow)
{
    ui->setupUi(this);

    ui->comboBox_sub_win_type->addItem("单个框", (int)SubWindowType::ONE_WINDOW);
    ui->comboBox_sub_win_type->addItem("十字框", (int)SubWindowType::CROSS_WINDOWS);
    ui->comboBox_sub_win_type->addItem("四角框", (int)SubWindowType::FOUR_CORNERS_WINDOWS);
    ui->comboBox_sub_win_type->addItem("水平对称框", (int)SubWindowType::HORIZONTAL_SYMMETRY_WINDOWS);
    ui->comboBox_sub_win_type->addItem("垂直对称框", (int)SubWindowType::VERTICAL_SYMMETRY_WINDOWS);

    connect(ui->comboBox_algo_type_list, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [&](int index)
        {
            auto algo_type = ui->comboBox_algo_type_list->itemText(index).toLocal8Bit().toStdString();
            SetAlgoListShowByAlgoType(algo_type);
        });

    connect(ui->comboBox_algo_name_list, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [&](int index)
        {
            auto algo_name = ui->comboBox_algo_name_list->itemData(index).toString().toLocal8Bit().toStdString();
            SetDeflautDefectByAlgo(algo_name);
            SetDrawSubWindowEnableByAlgo(algo_name);
        });

    draw_sub_window_enable_map = std::map<std::string, bool>
    {
        {"MarkOperator", false},
        {"PositionOperator", false},
        {"OcvOperator", false},
        {"BarcodeOperator", false},
        {"BridgeOperator", false},
        {"HeightMeasureOperator",true},
    };
}

DlgCreateDetectWindow::~DlgCreateDetectWindow()
{
    delete ui;
}

void DlgCreateDetectWindow::SetAlgoListShowByAlgoType(std::string _algo_type)
{
    auto it = algo_name_list.find(_algo_type);
    if (it == algo_name_list.end())
    {
        QString msg = QString("不存在算法模块: %1").arg(_algo_type.c_str());
        JRSMessageBox_WARN("警告", msg.toStdString(), jrscore::MessageButton::Ok);

        return;
    }

    ui->comboBox_algo_type_list->setCurrentText(QString::fromStdString(it->first.c_str()));
    ui->comboBox_algo_type_list->setEnabled(false);
    ui->comboBox_algo_name_list->clear();
    for (size_t i = 0; i < it->second.size(); i++)
    {
        ui->comboBox_algo_name_list->addItem(QString::fromStdString(it->second[i].second.c_str()), QString::fromStdString(it->second[i].first.c_str()));
    }
}

void DlgCreateDetectWindow::SetAlgoList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list)
{
    algo_name_list = _algo_name_list;
    ui->comboBox_algo_type_list->clear();
    for (const auto& elem : algo_name_list)
    {
        ui->comboBox_algo_type_list->addItem(QString::fromStdString(elem.first.c_str()));
    }

    SetAlgoListShowByAlgoType(ui->comboBox_algo_type_list->currentText().toStdString());

}

void DlgCreateDetectWindow::SetDefectList(const std::vector<std::string>& _defect_list)
{
    defect_name_lists = _defect_list;


    ui->comboBox_defect_type_list->clear();
    for (const auto& elem : _defect_list)
    {
        ui->comboBox_defect_type_list->addItem(QString::fromStdString(elem));
    }
}

void DlgCreateDetectWindow::GetDetectWindowInfo(std::string& algo_name, std::string& defect_name, SubWindowType& sub_window_type)
{
    algo_name = ui->comboBox_algo_name_list->currentData().toString().toLocal8Bit().toStdString();
    defect_name = ui->comboBox_defect_type_list->currentText().toStdString();
    if (ui->checkBox_add_sub_win->isChecked())
    {
        sub_window_type = (SubWindowType)ui->comboBox_sub_win_type->itemData(ui->comboBox_sub_win_type->currentIndex()).toInt();
    }
    else
    {
        sub_window_type = SubWindowType::NO_WINDOW;
    }

}

void DlgCreateDetectWindow::InitAlogSpeficDefectName()
{
    default_defect_map = std::map<std::string, std::string>
    {
        {"MarkOperator",defect_name_lists[0]},
        {"PositionOperator",defect_name_lists[0]},
        {"OcvOperator",defect_name_lists[1]},
        {"BarcodeOperator",defect_name_lists[1]},
        {"BridgeOperator",defect_name_lists[2]},
    };
}

void DlgCreateDetectWindow::SetDeflautDefectByAlgo(std::string algo_name)
{
    auto it = default_defect_map.find(algo_name);
    if (it == default_defect_map.end())
    {
        ui->comboBox_defect_type_list->setCurrentIndex(0);
    }
    else
    {
        for (int i = 0; i < ui->comboBox_defect_type_list->count(); i++)
        {
            if (ui->comboBox_defect_type_list->itemText(i) == QString::fromLocal8Bit(it->second.c_str()))
            {
                ui->comboBox_defect_type_list->setCurrentIndex(i);
            }
        }
    }
}

void DlgCreateDetectWindow::SetDrawSubWindowEnableByAlgo(std::string algo_name)
{
    auto it = draw_sub_window_enable_map.find(algo_name);
    if (it == draw_sub_window_enable_map.end() || it->second == false)
    {
        ui->checkBox_add_sub_win->setEnabled(false);
        ui->checkBox_add_sub_win->setChecked(false);
    }
    else
    {
        ui->checkBox_add_sub_win->setEnabled(true);
        ui->checkBox_add_sub_win->setChecked(it->second);
        int index = ui->comboBox_sub_win_type->findData((int)SubWindowType::FOUR_CORNERS_WINDOWS);
        ui->comboBox_sub_win_type->setCurrentIndex(index);
    }
}
