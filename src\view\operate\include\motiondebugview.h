/*****************************************************************//**
 * @file   motiondebugview.h
 * @brief
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef JRSMOTIONDEBUGVIEW_H
#define JRSMOTIONDEBUGVIEW_H

//prebuild
#include "pch.h"
#pragma warning(push, 3)
 // QT
//#include <QWidget>
#include <QMutex>
#include <QDialog>  
#include <QVBoxLayout>
#include <QSettings>
#include <QScreen>
#include <QRect>
#pragma warning(push, 3)
#include "ui_motiondebugview.h"
#pragma warning(pop)
 //CUSTOM
//#include <viewparam.hpp>
#include <algorithm>
#include "axismove.h"
#include "trackdebug.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MotiondebugView; };
QT_END_NAMESPACE

namespace jrsaoi
{
    class MotiondebugView : public QWidget
    {
        Q_OBJECT
    public:
        MotiondebugView(QWidget* parent = nullptr);
        ~MotiondebugView();
    public slots:
        /**
         * @fun SlotPushButtonTrigger
         * @brief
         * @param operateparam
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotPushButtonTrigger(jrsdata::OperateViewParamPtr operateparam);
        /**
         * @fun UpdateView 
         * @brief 更新界面参数
         * @param ptr
         * @date 2024.9.24
         * <AUTHOR>
         */
        void UpdateView(const jrsdata::OperateViewParamPtr ptr);
    signals:
        /**
         * @fun SigMotionDebugTrigger
         * @brief 
         * @param ptr
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigMotionDebugTrigger(jrsdata::OperateViewParamPtr operateparam);
        /**
         * @fun SigCurrentAxisPos
         * @brief 
         * @param ptr
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigCurrentAxisPos(const jrsdata::CurrentAxisPos& current_pos_);
    private:
        /**
         * @fun InitView
         * @brief 
         * @param ptr
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun InitConnect
         * @brief 初始信号链接
         * @param ptr
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
    private:
        Ui::MotiondebugView* ui;
        AxisMove* axis_move_widget;                 //* 轴移动界面*/
        TrackDebug* track_debug_widget;             //* 轨道调试页面*/
        QDialog* dialog;
        QMutex track_mutex;
        jrsdata::OperateViewParamPtr motiondebug_view_ptr;
    };
}
#endif // !__JRSMOTIONDEBUGVIEW_H