/*********************************************************************
 * @brief  命令派生类.
 *
 * @file   commandobject.h
 * @note   目前仅包含图形相关的命令. TODO 包含其他命令
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef COMMANDOBJECT_0708_H
#define COMMANDOBJECT_0708_H

#include "commandabstract.hpp" // CommandAbstract
#include <memory>
#include <vector>

class GraphicsAbstract;
class GraphicsManager;

/**
 * @brief  图形创建命令
 */
class CommandCreateGraphics : public CommandAbstract
{
public:
    CommandCreateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_);
    ~CommandCreateGraphics();
    void excute() override;
    void revoke() override;

private:
    std::vector<std::shared_ptr<GraphicsAbstract>> ghs_clone;
    GraphicsManager* gm;
};

/**
 * @brief  图形删除命令
 */
class CommandDeleteGraphics : public CommandAbstract
{
public:
    CommandDeleteGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_);
    ~CommandDeleteGraphics();
    void excute() override;
    void revoke() override;

private:
    std::vector<std::shared_ptr<GraphicsAbstract>> ghs_clone;
    GraphicsManager* gm;
};

/**
 * @brief  图形编辑命令
 */
class CommandEditGraphics : public CommandAbstract
{
public:
    CommandEditGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_);
    ~CommandEditGraphics();
    void excute() override;
    void revoke() override;

private:
    std::vector<std::shared_ptr<GraphicsAbstract>> m_input_graphics;  ///< 记录输入图形的原状态
    std::vector<std::shared_ptr<GraphicsAbstract>> m_revoke_graphics; ///< 记录撤销时图形的状态,以便可以重新执行
    GraphicsManager* gm;
};

#endif // !COMMANDOBJECT_0708_H