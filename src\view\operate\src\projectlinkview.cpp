#include "projectlinkview.h"
#include "coreapplication.h"
ProjectLinkView::ProjectLinkView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ProjectLinkViewClass())
{
    ui->setupUi(this);
    //! 界面置顶
    setWindowFlags(Qt::WindowCloseButtonHint | Qt::Window | Qt::WindowStaysOnTopHint);
    setWindowModality(Qt::ApplicationModal);  // 模拟模态
    InitConnect();
}

ProjectLinkView::~ProjectLinkView()
{
    delete ui;
}

void ProjectLinkView::SetProjectLists(const std::list<std::string>& project_lists)
{
    ui->listWidget_projcet_lists->clear();

    for (const auto& project : project_lists)
    {
        ui->listWidget_projcet_lists->addItem(QString::fromStdString(project));
    }
}

void ProjectLinkView::SetLinkedProjectName(const std::string& linked_project_name_)
{
    ui->lineEdit_link_project_name->clear();
    ui->lineEdit_link_project_name->setText(QString::fromStdString(linked_project_name_));
}

void ProjectLinkView::SlotConfirmLinkProject()
{
    auto current_selected_project_name = ui->listWidget_projcet_lists->currentItem()->text().toStdString();
    ui->lineEdit_link_project_name->clear();
    ui->lineEdit_link_project_name->setText(QString::fromStdString(current_selected_project_name));
    emit SigComfirmLinkProject(current_selected_project_name);
    JRSMessageBox_INFO("OperateView", "工程关联成功", jrscore::MessageButton::Ok);

}
void ProjectLinkView::SlotCancleLinkProject()
{
    ui->lineEdit_link_project_name->clear();
    emit SigCancleLinkProject();
    JRSMessageBox_INFO("OperateView", "取消关联成功", jrscore::MessageButton::Ok);

}

void ProjectLinkView::InitConnect()
{
    connect(ui->pushButton_link,&QPushButton::clicked,this,&ProjectLinkView::SlotConfirmLinkProject);
    connect(ui->pushButton_cancle_link,&QPushButton::clicked,this,&ProjectLinkView::SlotCancleLinkProject);
}

