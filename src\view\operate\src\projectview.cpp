﻿//QT
#pragma warning(push, 3)

#include <QVBoxLayout>
#include "ui_projectview.h"

#pragma warning(pop)
//Custom
#include "trackstatus.h"
#include "projectview.h"
#include "newprojectview.h"
#include "importcadview.h"
#include "expansionmultipanel.h"
#include "viewparam.hpp"
#include "viewdefine.h"
#include "openprojectview.h"
#include "saveopenimageview.h"
#include "jsonoperator.hpp"
namespace jrsaoi
{
    struct ProjectImplData
    {
        NewProjectView* new_project_view;            /**< 新建工程界面 */
        //ImportCadView* import_cad_view;              /**< 导入CAD界面 */
        //ExpansionMultiPanel* expand_mult_board_view; /**< 多联板设置界面 */
        OpenProjectView* open_project_view;          /**< 打开工程界面 */
        OpenProjectView* append_project_view;          /**< 追加工程界面 ，主要用于多工程的添加*/
        SaveOpenImageView* save_open_image_view;     /**< 保存打开图片界面 */
        jrsdata::ProjectParamPtr project_param_ptr;
        jrsdata::OperateViewParamPtr param_ptr;

        ProjectImplData(QWidget* parent)
            : new_project_view(new NewProjectView(parent))
            //, import_cad_view(new ImportCadView(parent))
           // , expand_mult_board_view(new ExpansionMultiPanel(parent))
            , project_param_ptr(std::make_shared<jrsdata::ProjectParam>())
            , param_ptr(std::make_shared<jrsdata::OperateViewParam>())
            , open_project_view(new OpenProjectView())
            , append_project_view(new OpenProjectView())
            , save_open_image_view(new SaveOpenImageView())
        {
        }
    };

    ProjectView::ProjectView(QWidget* parent)
        : QWidget(parent), ui(new Ui::ProjectView)
        , project_data(nullptr)
        // , param_ptr(std::make_shared<jrsdata::OperateViewParam>())
    {
        ui->setupUi(this);
        Init();
    }

    ProjectView::~ProjectView()
    {
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
        if (project_data)
        {
            delete project_data;
            project_data = nullptr;
        }
    }

    void ProjectView::UpdateView(const jrsdata::OperateViewParamPtr& param_)
    {
        if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME)
        {
            project_data->open_project_view->SlotUpdateProjectPath(project_data->project_param_ptr->file_param.file_path);
            project_data->open_project_view->show();
        }
        else if (param_->event_name == jrsaoi::APPEND_PROJECT_EVENT_NAME)
        {
            project_data->append_project_view->SlotUpdateProjectPath(project_data->project_param_ptr->file_param.file_path);
            project_data->append_project_view->show();
        }
        else if (param_->event_name == jrsaoi::PROJECT_CREATE_EVENT_NAME)
        {
            project_data->new_project_view->SlotNewProject();
        }
        else if (param_->event_name == jrsaoi::PROJECT_SAVE_EVENT_NAME)
        {
            emit SigSaveProject();
        }
        else if (param_->event_name == jrsaoi::PROJECT_SAVE_AS_EVENT_NAME)
        {
            //另存为
        }
        else if (param_->event_name == jrsaoi::PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME)
        {
            /**<更新工程路径图片 */
            //std::string project_path_str = jrscore::AOITools::CropString(project_data->project_param_ptr->file_param.file_path, "ProjectFile/");
            auto json_path_str = project_data->param_ptr->machine_param.machine_params_data[jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH].param_value;
            auto path_str = ParseJson(json_path_str, "directory");
            project_data->save_open_image_view->SetCurrentProjectPathAndName(path_str,
                project_data->project_param_ptr->project_name);
            project_data->save_open_image_view->show();
        }
        else if (param_->event_name == jrsaoi::MACHINE_PARAM_UPDATE_EVENT || param_->event_name == jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME)
        {
            //更新 机台配置参数
            project_data->param_ptr->machine_param = param_->machine_param;
            auto json_path_str = project_data->param_ptr->machine_param.machine_params_data[jrssettingparam::jrsmachineparam::MACHINE_PARAM_PROJECT_PATH].param_value;
            auto path_str = ParseJson(json_path_str, "directory");
            project_data->new_project_view->UpdateProjectPath(path_str);
            project_data->project_param_ptr->file_param.file_path = path_str;
        }
        else if (param_->event_name == jrsaoi::OPERATE_MOTION_SETTING_UPDATE)
        {
            project_data->new_project_view->SlotUpdateInfo(param_);
        }
    }

    void ProjectView::SlotUpdate(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == jrsaoi::RENDER2D_SUB_BOARD_SELECT_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::RenderEventParam>(param_);
            if (!param)
                return;
            //project_data->import_cad_view->SelectSubName(QString::fromStdString(param->select_param.subboard_name));
        }
        else if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME) /**< 读取工程参数 */
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            project_data->project_param_ptr = param->project_param;  //更新当前层 project 数据
            //project_data->import_cad_view->SlotClearPositionComponents();
            emit SignalUpdateNewProjectView(param_);
        }
        else if (auto param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_); param != nullptr)
        {
            project_data->new_project_view->UpdatePosition(param);
        }
        else if (param_->event_name == jrsaoi::ENTIRETY_IMAGE_READ)
        {
            //读取到大图后关闭 图片打开
            project_data->save_open_image_view->close();
            JRSMessageBox_INFO("提示", "图片打开成功", jrscore::MessageButton::Ok);
        }
        else if (param_->event_name == jrsaoi::RENDER2D_CAD_UPDATE_POSITION_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::RenderEventParam>(param_);
           // project_data->import_cad_view->SlotAddPositionComponent(QString::fromStdString(param_temp->cad_param.component_name));
        }
        else if (param_->event_name == jrsaoi::MULTI_BOARD_OPERATE_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::RenderEventParam>(param_);
            //project_data->expand_mult_board_view->UpdateView(param_temp->multi_param_ptr);
        }
        else if (param_->event_name == jrsaoi::OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME)
        {
            if (!param_)
            {
                return;
            }
            project_data->new_project_view->SlotUpdateInfo(param_);

        }
    }

    void  ProjectView::SlotUpdateNewFileProductWidth(double track_width1, double track_width2)
    {
        project_data->new_project_view->UpdateNewFileProductWidth(track_width1, track_width2);
    }

    void ProjectView::Init()
    {
        //InitMember();
        InitView();
        InitConnect();
    }

    void ProjectView::InitConnect()
    {
        connect(this, &ProjectView::SignalUpdateNewProjectView, project_data->new_project_view, &NewProjectView::SlotUpdateInfo);

        connect(project_data->new_project_view, &NewProjectView::SigScanceBoardImags, this, [=]()
            {
                /**<清除Render2D界面图片释放内存*/
                auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
                param_temp->event_name = jrsaoi::CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME;
                param_temp->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param_temp->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                emit SignalViewEvent(param_temp);
                auto param = std::make_shared<jrsdata::ProjectEventParam>();
                param->event_name = jrsaoi::SCANCE_BOARD_EVENT_NAME;
                param->module_name = jrsaoi::VIEW_MODULE_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
                param->invoke_module_name = jrsaoi::LOGIC_MODULE_NAME;
                emit SignalViewEvent(param);
            });

        connect(project_data->new_project_view, &NewProjectView::SignalCreateProject, this, [=](const jrsdata::ProjectParamPtr& param_)
            {
                /*
                * 工程创建时,仅初始化工程结构体,不保存到本地
                */
                auto param = std::make_shared<jrsdata::ProjectEventParam>(param_);
                param->event_name = jrsaoi::PROJECT_CREATE_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = OPERATE_RENDER_SUB_NAME;
                emit SignalViewEvent(param);
            });

        //connect(project_data->new_project_view, &NewProjectView::SignalReadProject, this, [=](const jrsdata::ProjectEventInfo& info)
        //    {
        //        /*
        //        * 工程读取
        //        */
        //        auto param = std::make_shared<jrsdata::ProjectEventParam>();
        //        //param->project_param->file_param.file_type = jrsdata::FileType::BIN;
        //        //param->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        //        param->project_param->file_param.file_name = info.project_name;
        //        param->project_param->file_param.file_path = info.project_path;
        //        param->project_param->project_name = jtools::FileOperation::GetFileNameFromPath(info.project_name, false);

        //        param->event_name = jrsaoi::PROJECT_READ_EVENT_NAME;
        //        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        //        param->sub_name = OPERATE_LOGIC_SUB_NAME;
        //        param->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        //        emit SignalViewEvent(param);
        //    });

        connect(project_data->new_project_view, &NewProjectView::SigConfirmBoardPos, this, [=](const jrsdata::ProjectParamPtr& param_)
            {
                auto param = std::make_shared<jrsdata::ProjectEventParam>(param_);
                //! 因为不需要更新到其他模块，所以不需要添加主题和订阅者名称
                param->event_name = jrsaoi::CONFIRM_BOARD_POS_EVENT_NAME;

                emit SignalViewEvent(param);
            });

        connect(project_data->new_project_view, &NewProjectView::SigNewFileGetProductWidth, this, [=]()
            {
                emit SigGetProductWidth();
            });
        connect(project_data->new_project_view, &NewProjectView::SigGetMultiProjectLists, this, [&]()
            {
                auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
                //！因为不需要更新到其他模块，所以不需要添加主题和订阅者名称
                param_temp->event_name = jrsaoi::OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME;
                emit SignalViewEvent(param_temp);
            });
        connect(project_data->new_project_view, &NewProjectView::SigComfirmLinkProject, this, [&](const std::string& project_name)
            {
                auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
                //！因为不需要更新到其他模块，所以不需要添加主题和订阅者名称
                param_temp->event_name = jrsaoi::OPERATE_CONFIRM_LINK_PROJECT_EVENT_NAME;
                param_temp->project_event_info.link_project_name = project_name;
                emit SignalViewEvent(param_temp);
            });
        connect(project_data->new_project_view, &NewProjectView::SigCancleLinkProject, this, [&]()
            {
                auto param_temp = std::make_shared<jrsdata::ProjectEventParam>();
                //！因为不需要更新到其他模块，所以不需要添加主题和订阅者名称
                param_temp->event_name = jrsaoi::OPERATE_CANCLE_LINK_PROJECT_EVENT_NAME;
                emit SignalViewEvent(param_temp);
            });



     /*   connect(project_data->import_cad_view, &ImportCadView::SigImportCadCompleted, this, [=](std::vector<jrsdata::CadStruct> param_cad_)
            {
                auto param = std::make_shared<jrsdata::CADEventParam>();
                param->cad_info = param_cad_;
                param->event_name = jrsaoi::IMPORT_CAD_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
                param->invoke_module_name = jrsaoi::PROJECT_MODULE_NAME;
                emit SignalViewEvent(param);
            });*/

     /*   connect(project_data->import_cad_view, &ImportCadView::SignalCadEditStep, this, [=](int state)
            {
                auto param = std::make_shared<jrsdata::RenderEventParam>();
                param->cad_param.step = jrsdata::CadEventParam::Step(state);
                param->event_name = jrsaoi::COMPONENT_EDIT_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
                emit SignalViewEvent(param);
            });*/

       /* connect(project_data->import_cad_view, &ImportCadView::SignalCadEditParam, this, [=](const jrsdata::CadEventParam& param_)
            {
                auto param = std::make_shared<jrsdata::RenderEventParam>();
                param->cad_param = param_;
                param->event_name = jrsaoi::COMPONENT_EDIT_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
                emit SignalViewEvent(param);
            });

        connect(project_data->import_cad_view, &ImportCadView::SignalSelectSubMode, this, [=](int state)
            {
                auto param = std::make_shared<jrsdata::RenderEventParam>();
                param->multi_param.multi_select_type = jrsdata::MultiBoardEventParam::MultiSelectType(state);
                param->event_name = jrsaoi::SUBBOARD_SELECT_SUB_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
                emit SignalViewEvent(param);
            });


        connect(project_data->expand_mult_board_view, &ExpansionMultiPanel::SigUpdateMultipleBoards, this, [=](const jrsdata::MultiBoardEventParamPtr& param_ptr)
            {
                if (param_ptr->event == jrsdata::MultiBoardEventParam::Event::BOARD_SORT)
                {
                    auto param = std::make_shared<jrsdata::OperateViewParam>();
                    param->event_name = jrsaoi::BOARD_SORT_EVENT_NAME;
                    param->multi_board_event_param_ptr = param_ptr;
                    emit SignalViewEvent(param);

                }
                else
                {
                    auto param = std::make_shared<jrsdata::RenderEventParam>();
                    param->multi_param_ptr = param_ptr;
                    param->event_name = jrsaoi::MULTI_BOARD_OPERATE_EVENT_NAME;
                    param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                    param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
                    param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
                    emit SignalViewEvent(param);
                }
            });*/

        connect(project_data->new_project_view, &NewProjectView::SigMotionDebugTrigger, this, [&](const jrsdata::OperateViewParamPtr param)
            {
                project_data->param_ptr->device_param = param->device_param;
                emit SigMotionDebugTrigger(project_data->param_ptr);
            });

        connect(project_data->new_project_view, &NewProjectView::SigGetPosition, this, &ProjectView::SigGetPosition);
        connect(project_data->new_project_view, &NewProjectView::SigSaveProject, this, &ProjectView::SigSaveProject);
        connect(project_data->new_project_view, &NewProjectView::SigSaveEntiretyImages, this, &ProjectView::SigSaveEntiretyImages);

        // open project view actions
        connect(project_data->open_project_view, &OpenProjectView::SignalSelectProjectFile, this, [&](const jrsdata::ProjectEventInfo& info)
            {
                auto param = std::make_shared<jrsdata::ProjectEventParam>();
                param->project_param->file_param.file_name = info.project_name;
                param->project_param->file_param.file_path = info.project_path;
                param->project_param->project_name = jtools::FileOperation::GetFileNameFromPath(info.project_name, false);

                param->event_name = jrsaoi::PROJECT_READ_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = OPERATE_LOGIC_SUB_NAME;
                param->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
                emit SignalViewEvent(param);
            });
        connect(project_data->append_project_view, &OpenProjectView::SignalSelectProjectFile, this, [&](const jrsdata::ProjectEventInfo& info)
            {
                auto param = std::make_shared<jrsdata::ProjectEventParam>();
                param->project_param->file_param.file_name = info.project_name;
                param->project_param->file_param.file_path = info.project_path;
                param->project_param->project_name = jtools::FileOperation::GetFileNameFromPath(info.project_name, false);

                param->event_name = jrsaoi::APPEND_PROJECT_EVENT_NAME;
                param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
                param->sub_name = OPERATE_LOGIC_SUB_NAME;
                param->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
                emit SignalViewEvent(param);
            });

        // save open image view actions
        connect(project_data->save_open_image_view, &SaveOpenImageView::SignalOpenImageGroup, this, &ProjectView::SigOpenEntiretyImages);
        connect(project_data->save_open_image_view, &SaveOpenImageView::SignalSaveImageGroup, this, &ProjectView::SigSaveEntiretyImages);

        //connect(this,)
    }

    void ProjectView::InitView()
    {
        project_data = new ProjectImplData(this);

        ui->verticalLayout->addWidget(project_data->new_project_view);
       // ui->verticalLayout->addWidget(project_data->import_cad_view);
       // ui->verticalLayout->addWidget(project_data->expand_mult_board_view);
    }
}