﻿/*********************************************************************
 * @brief  着色器代码.
 *
 * @file   shadersourceconstants.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef SHADERSOURCECONSTANTS_HPP
#define SHADERSOURCECONSTANTS_HPP

#pragma region 着色器代码

const char* const VERTEXSHADER_TEXTURE =
R"(#version 330 core
layout(location = 0) in vec3 aPos;
layout(location = 1) in vec2 aTexCoord;
layout(location = 2) in vec2 aOffset;
layout(location = 3) in float aAngle;
out vec2 texcoord;
//uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

mat4 translate(float x, float y) {
    mat4 translation = mat4(1.0f);
    translation[3][0] = x;
    translation[3][1] = y;
    return translation;
}

mat4 rotate(float theta) {
    mat4 rotation = mat4(1.0f);
    if(theta!=0) {
        float cosTheta = cos(theta);
        float sinTheta = sin(theta);
        rotation[0][0] = cosTheta;
        rotation[0][1] = -sinTheta;
        rotation[1][0] = sinTheta;
        rotation[1][1] = cosTheta;
    }
    return rotation;
}

void main()
{
    // 生成model矩阵
    mat4 model = translate(aOffset.x, -aOffset.y) * rotate(aAngle);

    gl_Position = projection * view * model * vec4(aPos.x, -aPos.y, aPos.z, 1.0f); // y轴方向相反
    //gl_Position = projection * view  * vec4(aPos.x, -aPos.y, aPos.z, 1.0f);

    texcoord = aTexCoord;

    //gl_Position =  vec4(aPos.x, -aPos.y, aPos.z, 1.0f);
    //TexCoord = vec2(aTexCoord.x, 1 - aTexCoord.y); // y轴方向相反
})";

const char* const FRAGMENTSOURCE_TEXTURE =
R"(#version 330 core
in vec2 texcoord;
uniform sampler2D ourTexture;
void main()
{
    vec4 rgba = texture(ourTexture, texcoord);
    gl_FragColor = vec4(rgba.bgr,rgba.a); //opencv的颜色通道顺序相反
})";

const char* const VERTEXSHADER_TEXTURE_MULTI =
R"(#version 330 core
layout(location = 0) in vec3 aPos;
layout(location = 1) in vec2 aTexCoord;
layout(location = 2) in vec2 aOffset;
layout(location = 3) in vec2 aSize;
layout(location = 4) in float aAngle;
layout(location = 5) in float Texindex;

flat out int texindex;
out vec2 texcoord;
uniform mat4 view;
uniform mat4 projection;

mat4 translate(float x, float y) {
    mat4 translation = mat4(1.0f);
    translation[3][0] = x;
    translation[3][1] = y;
    return translation;
}

mat4 rotate(float theta) {
    mat4 rotation = mat4(1.0f);
    if(theta!=0) {
        float cosTheta = cos(theta);
        float sinTheta = sin(theta);
        rotation[0][0] = cosTheta;
        rotation[0][1] = -sinTheta;
        rotation[1][0] = sinTheta;
        rotation[1][1] = cosTheta;
    }
    return rotation;
}

void main()
{
    // 生成model矩阵
    // vec posoffset = aSize/2;
    // mat4 model = translate(aOffset.x, -aOffset.y) * rotate(aAngle);
    mat4 model;
    if(aAngle>0)
    {    
        model = translate(aOffset.x, -aOffset.y)
                * translate(aSize.x/2,-aSize.y/2) 
                * rotate(aAngle)
                * translate(-aSize.x/2,aSize.y/2);
    }
    else
    {
        model = translate(aOffset.x, -aOffset.y);
    }
    // mat4 model = mat4(1.0f);
                                                        //将像素坐标按照正常方向渲染
    // gl_Position = projection * view  * vec4(aPos.x, -aPos.y, 1, 1.0f);
    gl_Position = projection * view * model * vec4(aPos.x, -aPos.y, aPos.z/100.0f, 1.0f);

    texcoord = aTexCoord;
    texindex = int(Texindex);
})";

const char* const FRAGMENTSOURCE_TEXTURE_MULTI =
R"(#version 330 core
in vec2 texcoord;

flat in int texindex;

uniform sampler2D Texture0;
uniform sampler2D Texture1;
uniform sampler2D Texture2;
uniform sampler2D Texture3;
uniform sampler2D Texture4;
uniform sampler2D Texture5;
uniform sampler2D Texture6;
uniform sampler2D Texture7;
uniform sampler2D Texture8;
uniform sampler2D Texture9;
uniform sampler2D Texture10;
uniform sampler2D Texture11;
uniform sampler2D Texture12;
uniform sampler2D Texture13;
uniform sampler2D Texture14;
uniform sampler2D Texture15;

vec4 SampleTexture(int index)
{
    switch( index )
    {
    case 0: return texture(Texture0, texcoord);
    case 1: return texture(Texture1, texcoord);
    case 2: return texture(Texture2, texcoord);
    case 3: return texture(Texture3, texcoord);
    case 4: return texture(Texture4, texcoord);
    case 5: return texture(Texture5, texcoord);
    case 6: return texture(Texture6, texcoord);
    case 7: return texture(Texture7, texcoord);
    case 8: return texture(Texture8, texcoord);
    case 9: return texture(Texture9, texcoord);
    case 10: return texture(Texture10, texcoord);
    case 11: return texture(Texture11, texcoord);
    case 12: return texture(Texture12, texcoord);
    case 13: return texture(Texture13, texcoord);
    case 14: return texture(Texture14, texcoord);
    case 15: return texture(Texture15, texcoord);
    default: return vec4(1.0, 1.0, 1.0, 1.0);
    }
}

void main()
{
    vec4 rgba = SampleTexture(texindex);
    if(rgba.a < 0.1)
        discard;  //丢弃片段
    gl_FragColor = rgba;
    // gl_FragColor = vec4(rgba.bgr,rgba.a); //opencv的颜色通道顺序相反
})";

const char* const VERTEXSHADER_GRAPHICS =
R"(#version 330 core
layout(location = 0) in vec3 aPos;
layout(location = 1) in vec4 aColor;
out vec4 ourColor;
//uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;
void main()
{
                                                //将像素坐标按照正常方向渲染
    gl_Position = projection * view * vec4(aPos.x, -aPos.y, aPos.z, 1.0f);
    ourColor = aColor;
})";

const char* const FRAGMENTSOURCE_COLOR =
R"(#version 330 core
out vec4 FragColor;
in vec4 ourColor;

void main()
{
    FragColor = ourColor;
})";


const char* const LineSegmentVertexShader = R"(
#version 330 core

layout(location = 0) in vec3 aPos;          // 顶点位置
layout(location = 1) in vec3 aColor;        // 颜色
layout(location = 2) in float aLineLength;  // 线段总长度

out vec3 vertexColor;       // 传递颜色到片段着色器
out float fragDistance;     // 当前片段距离起点的长度

uniform mat4 projection;
uniform mat4 view;
uniform mat4 model;

void main()
{
    gl_Position = projection * view * model * vec4(aPos, 1.0);
    vertexColor = aColor;

    // 假设 `aLineLength` 是从起点累计的长度，直接传递
    fragDistance = aLineLength;
}
)";
const char* const  LineSegmentFragmentShader = R"(
#version 330 core

in vec3 vertexColor;   // 从顶点着色器传递的颜色
in float fragDistance; // 当前点距离线段起点的长度

out vec4 FragColor;    // 最终输出颜色

uniform float dashSize;   // 虚线段的长度
uniform float gapSize;    // 虚线间隔大小
uniform int lineStyle;    // 线条样式：0 - 直线，1 - 虚线，2 - 点虚线

void main()
{
    if (lineStyle == 0)
    {
        // 直线：直接渲染
        FragColor = vec4(vertexColor, 1.0);
    }
    else
    {
        float totalSize = dashSize + gapSize;   // 虚线的一个周期
        float segmentPosition = mod(fragDistance, totalSize); // 当前点在线段周期中的位置

        if (lineStyle == 1)
        {
            // 虚线：在 dashSize 范围内渲染，其他丢弃
            if (segmentPosition > dashSize)
                discard;
        }
        else if (lineStyle == 2)
        {
            // 点虚线：仅在 dashSize 范围内的中心点附近渲染
            float halfDash = dashSize * 0.5;
            if (segmentPosition < halfDash - 0.1 || segmentPosition > halfDash + 0.1)
                discard;
        }

        // 渲染当前片段
        FragColor = vec4(vertexColor, 1.0);
    }
}

)";
#pragma endregion

#endif //! SHADERSOURCECONSTANTS_HPP