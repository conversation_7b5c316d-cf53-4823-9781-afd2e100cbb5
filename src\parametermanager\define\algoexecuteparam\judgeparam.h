/*****************************************************************//**
 * @file   judgeparam.h
 * @brief  zh-cn: 定义判断参数类
 * @details 定义判断参数类，用于管理判断参数，并提供相应的操作接口。
 * <AUTHOR>
 * @date 2024.11.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.19        <td>V1.0              <td>xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef JUDGEPARAM_H
#define JUDGEPARAM_H

 // std
#include <string>
#include <vector>

// custom
#include <iguana/iguana.hpp>

/**
* @struct JudgeParam
* @brief  算法判定参数
* @date 2024.11.19
* <AUTHOR>
*/
struct JudgeParam
{
    bool judge_res = false; ///< 判定结果
    double algo_res = 9999.0f; ///< 算法结果
    std::vector<std::string> char_cur_values = {}; ///< 字符测量值
    std::string spec_defect_name = ""; ///< spec判定项名称
    bool use_spec_judge = true; ///< spec判定项是否使用
    JudgeParam() :judge_res(false), algo_res(0), char_cur_values(), use_spec_judge(true) {}
    JudgeParam(double algo_res_, bool use_spec_judge_ = true) : algo_res(algo_res_), use_spec_judge(use_spec_judge_) {}
    JudgeParam(std::vector<std::string> char_cur_values_, bool use_spec_judge_ = true) :char_cur_values(char_cur_values_), use_spec_judge(use_spec_judge_) {}

    static JudgeParam CreateFromAlgoRes(double algo_res, bool use_spec_judge = true)
    {
        JudgeParam param;
        param.algo_res = algo_res;
        param.use_spec_judge = use_spec_judge;
        return param;
    }

    static JudgeParam CreateFromCharValues(const std::vector<std::string>& values,
        bool use_spec_judge = true)
    {
        JudgeParam param;
        param.char_cur_values = values;
        param.use_spec_judge = use_spec_judge;
        return param;
    }

    void SetSpecDefectName(const std::string& name) { spec_defect_name = name; }
};

/**
* @struct AlgoJudgeResult
* @brief  判定项结果以及检测框判断结果
* @date 2024.12.18
* <AUTHOR>
*/
struct AlgoJudgeResult
{
    std::unordered_map<std::string, JudgeParam> judge_param; ///< 算法判定参数 std::string 测量项名称  JudgeParam 判定结果
    bool detect_rect_status = false; ///< 检测框判断结果
    AlgoJudgeResult() :judge_param(), detect_rect_status(false) {}
};

#endif // JUDGEPARAM_H

