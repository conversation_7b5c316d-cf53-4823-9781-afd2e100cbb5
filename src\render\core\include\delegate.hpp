﻿/*********************************************************************
 * @brief  简单实现了一下类C#的事件委托.
 *
 * @file   delegate.hpp
 *
 * @date   2024.06.13
 * <AUTHOR>
 *********************************************************************/

#pragma once

#ifndef DELEGATE_H
#define DELEGATE_H
#include <functional> //function
#include <vector>

template <typename... Args>
class Event
{
public:
    // 定义一个EventHandler类型，用于存储事件处理函数
    using EventHandler = std::function<void(Args...)>;

    // 订阅事件处理函数
    inline void subscribe(const EventHandler& handler)
    {
        // 如果事件处理函数为空，则返回
        if (!handler)
        {
            return;
        }
        // 如果事件处理函数不在handlers中，则将其添加到handlers中
        if (std::find_if(handlers.begin(), handlers.end(),
            [&](const EventHandler& h)
            { return compare(h, handler); }) == handlers.end())
        {
            handlers.push_back(handler);
        }
    }

    // 触发事件
    inline void trigger(Args... args)
    {
        // 遍历handlers，调用每个事件处理函数
        for (auto& handler : handlers)
        {
            handler(args...);
        }
    }

    // 比较两个事件处理函数是否相同
    inline bool compare(const EventHandler& a, const EventHandler& b)
    {
        // 比较两个事件处理函数的target_type和target<void (*)(Args...)>是否相同
        return (a && b) &&
            a.target_type().name() == b.target_type().name() &&
            *a.template target<void (*)(Args...)>() == *b.template target<void (*)(Args...)>();
    }

    // 重载+=运算符，用于订阅事件处理函数
    Event& operator+=(const EventHandler& handler)
    {
        // 调用subscribe函数订阅事件处理函数
        subscribe(handler);
        return *this;
    }

    // 重载-=运算符，用于取消订阅事件处理函数
    Event& operator-=(const EventHandler& handler)
    {
        // 从handlers中移除与handler相同的事件处理函数
        handlers.erase(std::remove_if(handlers.begin(), handlers.end(),
            [&](const EventHandler& h)
            { return compare(h, handler); }),
            handlers.end());
        return *this;
    }

    // 重载()运算符，用于触发事件
    void operator()(Args... args)
    {
        // 调用trigger函数触发事件
        trigger(args...);
    }

private:
    // 存储事件处理函数的容器
    std::vector<EventHandler> handlers;
};
#endif // !DELEGATE_H