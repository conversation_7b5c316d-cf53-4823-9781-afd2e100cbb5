﻿/*****************************************************************//**
 * @file   showlistcontroller.h
 * @brief  产品列表Controller
 *
 * <AUTHOR>
 * @date   2024.9.25
 *********************************************************************/
#ifndef __SHOWLISTCONTROLLER_H__
#define __SHOWLISTCONTROLLER_H__
#include "controllerbase.h"
#include "viewparam.hpp"
#include <editdetectmodelview.h>
namespace jrsaoi
{
    class ShowListView;
    class ShowListModel;
    class EditView;
    class ShowListController :public ControllerBase
    {
        Q_OBJECT
    public:
        ShowListController(const std::string& name);
        ~ShowListController();
        /**
         * @fun Update
         * @brief 重写ControllerBase的Update
         * @param param_
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save
         * @brief 重写ControllerBase的Save
         * @param param_
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun SetView
         * @brief 初始化ShowListView并连接信号槽
         * @param view_param
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual void SetView(ViewBase* view_param);
        /**
         * @fun SetModel
         * @brief 初始化ShowListModel
         * @param model_param
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual void SetModel(ModelBasePtr model_param);
    signals:
        /**
         * @fun ShowListRenderTrigger
         * @brief 发送CAD数据变化到显示界面
         * @param param
         * @date 2024.9.19
         * <AUTHOR>
         */
        void ShowListRenderTrigger(const jrsdata::ViewParamBasePtr& param);
    private slots:
        /**
         * @fun FindTriggerSlots
         * @brief 筛选元件信号槽函数
         * @param param
         * @date 2024.9.19
         * <AUTHOR>
         */
        void FindTriggerSlots(const jrsdata::QueryListViewParam param);
        /**
         * @fun UpdateShowListViewParamSlots
         * @brief 刷新元件列表参数
         * @param param
         * @date 2025.2.20
         * <AUTHOR>
         */
        void UpdateShowListViewParamSlots(jrsdata::ShowListViewParam param);

        /**
         * @fun SlotViewUpdateData
         * @brief  数据更新
         * @param param_
         * <AUTHOR>
         * @date 2025.3.17
         */
        void SlotViewUpdateData(const jrsdata::ViewParamBasePtr& param_);
    private:
        /// 显示列表视图的指针
        ShowListView* show_list_view;
        /// 焊盘视图的指针
       // EditView* _edit_view;
        EditDetectModelView* edit_detect_model_view = nullptr;
        /// 显示列表模型的共享指针
        std::shared_ptr<ShowListModel> model;
    };
    using ShowListControllerPtr = std::shared_ptr<ShowListController>;
}
#endif // !__SHOWLISTCONTROLLER_H__
