#include "controlpointobject.h"

#include "graphicsprocess.h"
#include "graphicsalgorithm.h"
#include "graphicseventparam.hpp"
#include "controlpointconstants.hpp"
#include "graphicsabstract.hpp"


void ControlPointSize::Response(const ResponseEventParam& param, GraphicsAbstract* const obj)
{
     auto rad = A_DEG_TO_RAD(obj->a());
    float cosA = (float)std::cos(rad);
    float sinA = (float)std::sin(rad);
    /*方向向量*/
    cv::Point2f dirVec(cosA, sinA);
    /*垂直向量*/
    cv::Point2f verticalVec(-sinA, cosA);
    /*移动向量*/
    cv::Point2f moveVec(param.xstart - param.xend, param.ystart - param.yend);

    switch (static_cast<ControlPointType>(param.attr.type))
    {
    case ControlPointType::CORNER_POINT_LEFT_DOWN:
        RectDoLeft(obj, dirVec, moveVec);
        RectDoDown(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_LEFT_TOP:
        RectDoLeft(obj, dirVec, moveVec);
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_TOP:
        RectDoRight(obj, dirVec, moveVec);
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::CORNER_POINT_RIGHT_DOWN:
        RectDoRight(obj, dirVec, moveVec);
        RectDoDown(obj, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_LEFT:
        RectDoLeft(obj, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_TOP:
        RectDoTop(obj, verticalVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_RIGHT:
        RectDoRight(obj, dirVec, moveVec);
        break;
    case ControlPointType::WAIST_POINT_DOWN:
        RectDoDown(obj, verticalVec, moveVec);
        break;
    }

    {
        /*允许越界*/
        obj->SetWH(abs(obj->w()), abs(obj->h()));
    }
}


void ControlPointMove::Response(const ResponseEventParam& param, GraphicsAbstract* const obj)
{
    switch (static_cast<ControlPointType>(param.attr.type))
    {
    case ControlPointType::MOVE_POINT:
    {
        float xo = param.xend - param.xstart;
        float yo = param.yend - param.ystart;
        obj->SetXY(obj->x() + xo, obj->y() + yo);
    }
    break;
    }
}

double ControlPointMove::TryResponse(const float& x, const float& y, const float& min_dis) const
{
    double dis = PointDisToPolygon(Vec2(x, y), cpd.contours);
    if (dis > 0)
    {
        return min_dis-0.01; // 尽量最后触发
    }
    return -1.0;
}

void ControlPointRotate::Response(const ResponseEventParam& param, GraphicsAbstract* const obj)
{
    const auto& a = obj->a();
    switch (static_cast<ControlPointType>(param.attr.type))
    {
    case ControlPointType::ROTATE_POINT:
    {
        const int gap = 10; // 旋转最小间隔
        auto ra = CalcVectorRotationAngle(cv::Point2f(obj->x() - param.xstart, obj->y() - param.ystart),
            cv::Point2f(obj->x() - param.xend, obj->y() - param.yend));
        int rounda = ((int)ra / gap) * gap;
        obj->SetA(a + rounda);
    }
    break;
    }
}
