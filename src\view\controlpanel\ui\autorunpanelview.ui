<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>autorunpanelview</class>
 <widget class="QWidget" name="autorunpanelview">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>248</width>
    <height>200</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>200</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>AutoRunPanelView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>60</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0" rowspan="2" colspan="2">
       <widget class="QToolButton" name="toolButton_start_stop">
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="text">
         <string>开始/停止</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0" colspan="2">
       <widget class="QToolButton" name="toolButton_state_reset">
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="text">
         <string>状态复位</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>15</width>
          <height>30</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="5" column="0" colspan="2">
       <widget class="QToolButton" name="toolButton_state_reset_error">
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="text">
         <string>清除报警</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>15</width>
          <height>30</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
