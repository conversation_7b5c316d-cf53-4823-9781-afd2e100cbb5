/*****************************************************************//**
 * @file   motionworker.h
 * @brief 目前封装的用于运控的TCP通讯客户端. TODO:后期要单独将TCP通信功能独立封装
 * @details
 * <AUTHOR>
 * @date 2024.8.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.5         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
//STD
//Custom
//Third
#ifndef __MOTIONWORKER_H__
#define __MOTIONWORKER_H__

// STD
#include <queue>
#include <iostream>

// thirdparty
#include <asio.hpp>

using asio::io_context;
using asio::steady_timer;
using asio::ip::tcp;

namespace jrsdevice
{
  class MotionWorker
  {
    public:
      MotionWorker();
      ~MotionWorker();

      // 初始化
      int InitClient(const std::string ip = "127.0.0.1", const int port = 88888);

      // 连接状态
      bool GetIsConnected();

      // 获取错误
      std::string GetLastError();

      // 发送消息
      bool Send(const std::string msg);

      // 接收消息(默认100ms超时)
      std::string Receive(const int timeout = 100);

    private:
      std::vector<std::string> SplitString(const std::string &str, const std::string delimiter);

    private:
      // 套接字对象
      io_context io_context_;
      tcp::socket socket_;

      // 服务端IP
      std::string server_address;

      // 服务端端口号
      int server_port;

      // 错误消息
      std::string last_error;

      // 连接状态
      bool connected_;

      // 接收的消息队列
      std::queue<std::string> motion_msg;
  };

  using MotionWorkerPtr = std::shared_ptr<MotionWorker>;

}
#endif
