# PAD 创建

## 需求

### 1. 方位
- 上
- 下
- 左
- 右
- 中

### 2. 模式 
- 单个
- 对称
- 整排单边
- 整排对称
- 整排四边
- 内部阵列 (仅该模式存在方位:中)

### 3. 操作
- 单个删除
- 框选成组
- 弹窗输入数量
- 自动计算方位

## 实现
### 要点
1. pad图形作为组存在
2. 每个pad组包含多个图形
1. pad组提供对齐,生成
1. pad组需要有基准位置,用于辅助生成

### 数据类型定义

```
class PadGraphicsGroup
{
    int num; ///< 子图形数量
    std::vector<GraphicsPtr> ghs;///< 子图形序列
    int direction; ///< 方向
}

class Pad
{
    std::string pad_name;
    int x;
    int y;
    int width;
    int height;
    int group_id;
    std::vector<std::pair<int,int>> contours;
    std::string component_name;
}
```