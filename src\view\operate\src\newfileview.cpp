﻿// prebuild
#include "pch.h"
//Custom
#include "newfileview.h"
#include "coreapplication.h"
#include "ui_newfileview.h"
//Define
//#include "viewparam.hpp"
//Tool
#include "fileoperation.h"
//QT
#pragma warning(push, 3)
#include <QPushButton>
#pragma warning(pop)

namespace jrsaoi
{
    NewFileView::NewFileView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::NewFileView())
    {
        ui->setupUi(this);
        this->setWindowFlags(this->windowFlags() & ~Qt::WindowMinMaxButtonsHint);
        InitConnect();
    }
    NewFileView::~NewFileView()
    {
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
    }
    void NewFileView::UpdateNewFileProductWidth(double track_width1, double track_width2)
    {
        (double)track_width1;
        (double)track_width2;

        //double t1 = track_width1;
        //double t2 = track_width2;

        //ui->doubleSpinBox_product_width->setValue(track_width2);
        //ui->doubleSpinBox_product_width->setValue(track_width1);
    }

    void NewFileView::SlotProjectName(const QString& project_name_)
    {
        auto project_name_str = project_name_.toStdString();
        if (!project_name_str.empty() && !jtools::FileOperation::IsVaildFileName(project_name_str))
        {
            ui->lineEdit_project_name->setText(tr("名称存在非法字符"));
            ui->lineEdit_project_name->selectAll();
        }
    }


    void NewFileView::InitConnect()
    {
        qRegisterMetaType<jrsdata::ProjectEventInfo>("jrsdata::ProjectEventInfo");
        connect(ui->pushButton_confirm, &QPushButton::clicked, this, &NewFileView::SlotConfirm);
        //connect(ui->pushButton_confirm_scan_img, &QPushButton::clicked, this, &NewFileView::SlotConfirm);
        connect(ui->pushButton_cancle, &QPushButton::clicked, this, &NewFileView::close);
        connect(ui->lineEdit_project_name, &QLineEdit::textChanged, this, &NewFileView::SlotProjectName);
        //connect(ui->pushButton_get_product_width, &QPushButton::clicked, this, &NewFileView::SlotGetProductWidth);

    }
    void NewFileView::SlotConfirm()
    {
        auto event_param = jrsdata::ProjectEventInfo();
        auto& file_info_temp = event_param.info;
        auto& project_name = event_param.project_name;
        project_name = ui->lineEdit_project_name->text().toStdString();
        if (project_name.empty())
        {
            Log_ERROR("新建工程名称为空，请重新输入！");
            return;
        }
        if (!jtools::FileOperation::IsVaildFileName(project_name))
        {
            Log_ERROR("新建工程名称存在非法字符，请重新输入！");
            return;
        }
        this->close();
        event_param.step = jrsdata::ProjectEventInfo::Step::CREATE;
        file_info_temp.height_board = 2500; //ui->doubleSpinBox_product_width->value();
        file_info_temp.width_board = 2500; //ui->doubleSpinBox_product_length->value();
        file_info_temp.is_scan = false;//(sender() == ui->pushButton_confirm_scan_img);

        emit SigConfirmFileInfo(event_param);
    }

    void NewFileView::SlotGetProductWidth()
    {
        emit SigGetProductWidth();
    }
}