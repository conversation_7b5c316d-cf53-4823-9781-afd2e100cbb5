/*****************************************************************
 * @file   nocoverbackgrounddelegate.hpp
 * @brief  
 * @details
 * <AUTHOR>
 * @date 2025.5.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.5.5          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
//STD
//Custom
//Third
#include <QItemDelegate>
#include <QPen>
#include <QPainter>
#include <QRect>
#include <QStyleOptionViewItem>
#include <QStyle>
#include <QModelIndex>

/**
 * @brief 使tableview被选中项背景不被覆盖
 */
class NoCoverBackgroundDelegate : public QItemDelegate
{
public:
    void drawFocus(QPainter* painter, const QStyleOptionViewItem& option, const QRect& rect) const override
    {
        //std::cout << __FUNCTION__ << " option.state " << option.state << "" << std::endl;
        if (option.state & QStyle::State_HasFocus)
        {
            QPen pen;
            pen.setColor(Qt::blue);
            pen.setStyle(Qt::SolidLine);
            pen.setWidth(8);
            painter->setPen(pen);
            painter->drawLine(rect.x(), rect.y(), rect.x() + rect.width(), rect.y());
            painter->drawLine(rect.x(), rect.y() + rect.height(), rect.x() + rect.width(), rect.y() + rect.height());
            //std::cout << __FUNCTION__ << " option.state " << option.state << "" << std::endl;
        }
        // 调用基类以绘制项目
        QItemDelegate::drawFocus(painter, option, rect);
    }

    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const override
    {
        //std::cout << __FUNCTION__ << " option.state " << option.state << " index.row() " << index.row() << std::endl;
        // 复制原始选项以不更改原始数据
        QStyleOptionViewItem customOption = option;
        // 清除选中状态下的前景和背景颜色更改
        if (option.state & QStyle::State_Selected)
        {
            customOption.state &= ~QStyle::State_Selected; // 清除选中状态
            customOption.state |= QStyle::State_HasFocus; // 添加焦点状态
            //std::cout << __FUNCTION__ << " customOption.state " << customOption.state << " index.row() " << index.row() << std::endl;

            //customOption.font.setBold(true); // 设置字体加粗（更显眼,但是无法对齐,不美观）
        }

        // 调用基类以绘制项目
        QItemDelegate::paint(painter, customOption, index);
    }
};