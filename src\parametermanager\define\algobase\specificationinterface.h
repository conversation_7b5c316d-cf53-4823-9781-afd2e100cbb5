/*****************************************************************//**
 * @file   specificationinterface.h
 * @brief  zh-cn: 定义规格管控界面类，用于管理规格组件组，包括控件和类型，并提供相应的操作接口。
 * @details 该类主要用于创建规格界面并提供接口获取和设置参数值，包括创建规格界面、更新已有界面的值、获取界面当前值、设置指定参数的算法结果、批量设置多个参数的算法结果、设置字符规格的当前值、设置字符规格的标准值、设置缺陷状态等功能。
 * <AUTHOR>
 * @date 2024.12.11
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.11        <td>V1.0              <td>xailor       <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef SPECIFICATIONINTERFACE_H
#define SPECIFICATIONINTERFACE_H

// std
#include <map>
#include <memory>
#include <string>
#include <vector>

// custom
#include "specificationwidget.h"   
#include "judgeparam.h"
/**
* @class SpecificationInterface
* @brief 创建规格ui 并提供接口获取和设置参数值
* @details 该类主要用于创建规格界面并提供接口获取和设置参数值，包括创建规格界面、更新已有界面的值、获取界面当前值、设置指定参数的算法结果、批量设置多个参数的算法结果、设置字符规格的当前值、设置字符规格的标准值、设置缺陷状态等功能。
* @date 2024.12.11
* <AUTHOR>
*/
class SpecificationInterface
{
public:
    SpecificationInterface() = default;
    ~SpecificationInterface() = default;

    /**
    * @fun CreatSpecWidget
    * @brief 创建并显示规格界面
    * @details 该类主要用于创建规格界面并提供接口获取和设置参数值，包括创建规格界面、更新已有界面的值、获取界面当前值、设置指定参数的算法结果、批量设置多个参数的算法结果、设置字符规格的当前值、设置字符规格的标准值、设置缺陷状态等功能。
    * @param specification_map 用于创建规格界面以及界面参数的映射表
    * @param is_show_widget 是否显示界面，默认不显示 离线测试规格使用
    * @date 2024.12.11
    * <AUTHOR>
    */
    std::shared_ptr<SpecificationWidget> CreatSpecWidget(
    const std::unordered_map<std::string, ControlSpec>& specification_map,bool is_show_widget = false);

    /**
    * @fun UpdateSpecWidgetValues
    * @brief 更新已有界面的值
    * @param specification_map 用于创建规格界面以及界面参数的映射表
    * @date 2024.12.11
    * <AUTHOR>
    */
    void UpdateSpecWidgetValues(
    const std::unordered_map<std::string, ControlSpec>& specification_map);
    /**
   * @fun UpdateSpecValues
   * @brief 更新算法结果和判定状态到规格界面参数
   * @param specification_map 用于创建规格界面以及界面参数的映射表
   * @date 2024.12.11
   * <AUTHOR>
   */
    void UpdateSpecValues(const std::unordered_map<std::string, JudgeParam>& judge_params,
        std::unordered_map<std::string, ControlSpec>& specification_map);
    /**
    * @fun GetSpecWidgetValues
    * @brief 获取当前规格界面的值
    * @date 2024.12.11
    * <AUTHOR>
    */
    std::unordered_map<std::string, ControlSpec> GetSpecWidgetValues();
    /**
   * @fun SetCharStdValues
   * @brief 设置字符规格的标准值
   * @param spec_map 用于创建规格界面以及界面参数的映射表
   * @param param_name 参数名称
   * @param char_values 字符标准值
   * @date 2024.12.12
   * <AUTHOR>
   */
    void SetCharStdValues(std::unordered_map<std::string, ControlSpec>& spec_map,
        const std::string& param_name, const std::vector<std::string>& char_std_values);
    /**
    * @fun RestorateSpecStatus
    * @brief 恢复规格到默认状态
    * @param specification_map 用于创建规格界面以及界面参数的映射表
    * @date 2025.01.06
    * <AUTHOR>
    */
    void RestorateSpecStatus(std::unordered_map<std::string, ControlSpec>& specification_map);

private:
    /**
    * @fun CreateWidgetParams
    * @brief 用于类型转换
    * @param specification_map 用于创建规格界面以及界面参数的映射表
    * @date 2024.12.11
    * <AUTHOR>
    */
    std::vector<SpecificationWidgetParam> CreateWidgetParams(
        const std::unordered_map<std::string, ControlSpec>& specification_map);
    /**
   * @fun CreateComponentValues
   * @brief 用于类型转换
   * @param specification_map 用于创建规格界面以及界面参数的映射表
   * @date 2024.12.11
   * <AUTHOR>
   */
    SpecificationComponentDatas CreateComponentValues(
        const std::unordered_map<std::string, ControlSpec>& specification_map);
    std::vector<SpecificationWidgetParam> m_spec_widget_params;
    std::shared_ptr<SpecificationWidget> m_spec_ptr;
};

#endif // SPECIFICATIONINTERFACE_H