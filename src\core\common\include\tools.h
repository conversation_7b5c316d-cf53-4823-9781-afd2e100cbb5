﻿/*****************************************************************
 * @file   tools.h
 * @brief 工具类，主要实现常用的功能
 * @note  TODO：弃用，删除，分别放到不同的工具类中 by zhangyuyu 2025.1.8
 * <AUTHOR>
 * @date 2024.9.9
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.9.9          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __TOOLS_H__
#define __TOOLS_H__

 //STD
#include <iostream>
//Custom
#include "pluginexport.hpp"
namespace cv
{
    class Mat;
    class RotatedRect;
}
namespace jrscore
{
    class JRS_AOI_PLUGIN_API AOITools
    {
    public:
        static void EnsureDirectoryExists(const std::string& directory);
        static std::string GetCurrentPath();
        /**
         * @fun GetIPv4Addresses
         * @brief
         * @return 所有IP的地址
         * @date 2024.8.16
         * <AUTHOR>
         */
        static std::vector<std::string> GetIPv4Addresses();
        static std::string GetLocalIP();
        /**
        * @fun TrimString
        * @brief 去除子串串中两端的空格
        * @param str
        * @date 2024.4.28
        * <AUTHOR>
        */
        static void TrimString(std::string& str);

        static std::vector<std::string> SplitString(const std::string& s, char delimiter);
        /**
         * @fun JoinString
         * @brief
         * @param str_vec
         * @param connector
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static std::string JoinString(const std::vector<std::string> str_vec, char connector);
        /**
         * @fun ReplaceString
         * @brief
         * @param res_str
         * @param target_str
         * @param sub_str
         * @param size
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static bool ReplaceString(std::string& res_str, const std::string target_str, const std::string& sub_str, int size);
        /**
         * @fun GetCurrentDataTime
         * @brief 根据格式获取当前时间
         * @param format
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static std::string GetCurrentDataTime(const std::string& format = "%Y-%m-%d %H:%M:%S", bool is_microsecond_ = false);

        /**
         * @fun SubString
         * @brief
         * @param src_str
         * @param sub_str
         * @return
         * <AUTHOR>
         * @date 2024.11.22
         */
        static std::string CropString(const std::string& src_str, const std::string& crop_str);
        /**
         * @fun FormatInt
         * @brief 数字格式化输出
         * @param number
         * @param width
         * @param fill
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static std::string FormatInt(int number, int width = 0, char fill = '0');
        /**
         * @fun WriteFile
         * @brief 文件写入
         * @param path
         * @param content
         * @return
         * @date 2024.8.16
         * <AUTHOR>
         */
        static bool WriteFile(const std::string& path, const std::string& content);

        /**
         * @fun GetPrefixString
         * @brief  获取前缀字符串
         * @param ori_str_
         * @return
         * <AUTHOR>
         * @date 2025.1.17
         */
        static std::string GetPrefixString(const std::string& ori_str_, const char& mark_char_ = '_');
        /**
        * @fun GetSuffixNumber
        * @brief  获取后缀数字
        * @param ori_str_
        * @return
        * <AUTHOR>
        * @date 2025.1.17
        */
        static int GetSuffixNumber(const std::string& ori_str_, const char& mark_char_ = '_');
        /**
         * @fun DoubleToString
         * @brief 小数转字符串，小数精度precision
         * @param value
         * @param precision
         * @return
         * @date 2025.2.19
         * <AUTHOR>
         */
        static std::string DoubleToString(double value, int precision);
        /**
         * @fun FloatToString
         * @brief 小数转字符串，小数精度precision
         * @param value
         * @param precision
         * @return
         * @date 2025.5.19
         * <AUTHOR>
         */
        static std::string FloatToString(float value, int precision);
        /**
         * @fun FormatDuration
         * @brief 格式化运行时间
         * @param duration
         * @return
         * @date 2025.2.19
         * <AUTHOR>
         */
        static std::string FormatDuration(std::chrono::seconds duration);
        /**
         * @fun CalculateTimeDifference
         * @brief  计算时间差 返回耗时多少秒
         * @param start_time_
         * @param end_time_
         * @param format_
         * @return
         * <AUTHOR>
         * @date 2025.2.21
         */
        static std::string CalculateTimeDifference(const std::string& start_time_, const std::string& end_time_, const std::string& format_ = "%Y-%m-%d %H:%M:%S");
        /**
          * @fun NormalizeAngle
          * @brief  确保角度在[0-360]之间
          * @param angle
          * @return
          * <AUTHOR>
          * @date 2025.2.21
          */
        static void  NormalizeAngle(double& angle);

        /**
          * @fun GetThumbnailMat
          * @brief  获取缩略图
          * @return
          * <AUTHOR>
          * @date 2025.4.8
          */
        static cv::Mat GetThumbnailMat(const cv::Mat& src_img_, int max_width = 500, int max_height = 500);


        /**
          * @fun GetMinimumExternalRoatedRect
          * @brief  获取缩略图
          * @return
          * <AUTHOR>
          * @date 2025.4.8
          */
        static cv::RotatedRect GetMinimumExternalRoatedRect(const std::vector<cv::RotatedRect>& rects_);



        /**
         * @fun ContainsIllegalCharacters
         * @brief  检查名称是否含有不合法字符
         * @param name_ 被检查的名称
         * @param illegal_chars_out_ 不合法字符
         * @return
         * <AUTHOR>
         * @date 2025.5.22
         */
        static   bool ContainsIllegalCharacters(const std::string& name_, std::string& illegal_chars_out_);


    };
};
#endif // !__TOOLS_H__