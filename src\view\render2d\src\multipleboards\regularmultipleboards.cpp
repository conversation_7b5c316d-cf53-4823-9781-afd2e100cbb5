﻿#include "regularmultipleboards.h"
#include "subboardclone.h"
#include "graphicsobject.h"
#include "layerconverter.hpp"
#include "tools.h"
int jrsaoi::RegularMultipleBoards::MultipleBoardsUpdate(jrsdata::MultiBoardEventParamPtr param_)
{
    if (!param_ && (!param_->regular_multiple_board_param.has_value()))
    {
        return -1;
    }
    _regular_multiple_board_param_ptr = std::make_shared <jrsdata::MultiBoardEventParam::RegularParam>(param_->regular_multiple_board_param.value());
    auto step = _regular_multiple_board_param_ptr->step;
    switch (step)
    {
    case jrsdata::MultiBoardEventParam::RegularParam::Step::NONE:
        break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_COMPONENT:
        if (_set_current_layer_callback)
        {
            _set_current_layer_callback(Layer::component);
        }
        break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::MARK_COMPONENT_AS_TEMPLATE:
    {
        MarkComponentAsTemplate(*param_->regular_multiple_board_param);
    }
    break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_COL_FLAG:
    case jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_ROW_FLAG:
        if (_set_current_layer_callback && _set_vision_mode_callback)
        {
            _set_current_layer_callback(Layer::temp_mark);
            _set_vision_mode_callback(VisionMode::MANUAL_CREATE_GRAPHICS);
            return jrscore::AOI_OK;
        }
        break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::IDENTIFY_REMOTE_SUBBOARD:
        //获取到行列  然后识别远端元件信息
        IdentifyRemoteSubboard(*param_->regular_multiple_board_param);
        break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::GENERATE_MULTIPLE_BOARD:
        //生成多联板信息
        GenerateCloneSubboards(*param_->regular_multiple_board_param);
        break;
    case jrsdata::MultiBoardEventParam::RegularParam::Step::CANCEL_GENERATE_MULTIPLE_BOARD:
        //生成多联板信息
        CancelGenerateCloneSubboards(*param_->regular_multiple_board_param);
        //param_->regular_multiple_board_param = std::nullopt;
        break;
    default:
        break;
    }
    param_->create_type = jrsdata::MultiBoardEventParam::CreateType::NONE;
    return 0;
}

int jrsaoi::RegularMultipleBoards::MarkComponentAsTemplate(jrsdata::MultiBoardEventParam::RegularParam& param_)
{
    if (!_get_current_select_param)
    {
        Log_ERROR("获取选择的元件数据失败，无法进行其他操作！");
        return -1;
    }
    auto current_render_event_param = _get_current_select_param();
    if (!current_render_event_param.has_value())
    {
        Log_ERROR("获取render 数据失败！");
        return -1;
    }
    const auto component_optional = _project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(current_render_event_param->get().update_operator_param.select_param.component_name,
        current_render_event_param->get().update_operator_param.select_param.subboard_name, jrsdata::Component::Type::CAD);
    if (!component_optional.has_value())
    {
        return -1;
    }
    const auto  component_ref = component_optional->get();
    const auto component_units = _project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnit(component_ref.component_part_number, jrsdata::ComponentUnit::Type::BODY);
    if (component_units.empty())
    {
        return -1;
    }
    auto component_unit = component_units.front();
    auto x = component_ref.x - component_unit.width / 2;
    auto y = component_ref.y - component_unit.height / 2;
    auto w = component_unit.width;
    auto h = component_unit.height;
    //// 裁剪区域
    auto template_images = ImageCapture({ x, y,w,h });
    param_.component_and_images = { component_ref ,template_images };
    return 0;
}

cv::Point2f jrsaoi::RegularMultipleBoards::CalculateDistance(std::pair<cv::Rect2f, std::unordered_map<jrsdata::LightImageType, cv::Mat>> origin_image_info_, cv::Rect2f temp_mark_rect_)
{
    //1、截取临时图像
    auto temp_mark_images = ImageCapture({ temp_mark_rect_.x - temp_mark_rect_.width / 2 ,temp_mark_rect_.y - temp_mark_rect_.height / 2,temp_mark_rect_.width,temp_mark_rect_.height });
    //2、模板匹配除实际坐标
    (void)temp_mark_images;
    //3、计算出 dx,dy
    float dx = temp_mark_rect_.x - origin_image_info_.first.x;
    float dy = temp_mark_rect_.y - origin_image_info_.first.y;
    return { dx,dy };
}


int jrsaoi::RegularMultipleBoards::IdentifyRemoteSubboard(jrsdata::MultiBoardEventParam::RegularParam& param_)
{
    //更新临时框坐标信息
    if (!_get_current_select_param)
    {
        Log_ERROR("获取选择的元件数据失败，无法进行其他操作！");
        return -1;
    }
    auto current_render_event_param = _get_current_select_param();
    if (!current_render_event_param.has_value())
    {
        Log_ERROR("获取render 数据失败！");
        return -1;
    }
    for (auto& [id, rect] : current_render_event_param->get().sub_edit_param.multi_event_param.regular_multiple_board_param->temp_mark_and_rect_map)
    {
        std::shared_ptr<GraphicsAbstract> gh;
        _get_graphics_callback(gh, id);
        if (!gh)
        {
            break; //获取临时框位置出错，请检查
        }
        param_.temp_mark_and_rect_map[id] = { gh->x() /*- gh->w() / 2*/,gh->y() /*- gh->h() / 2*/,gh->w(),gh->h() };
    }
    //坐标更新结束

    const auto component_units = _project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnit(param_.component_and_images.first.component_part_number, jrsdata::ComponentUnit::Type::BODY);
    if (component_units.empty())
    {
        return -1;
    }
    auto component = param_.component_and_images.first;
    auto component_unit = component_units.front();
    auto subboard = _project_param_instance.GetProjectDataProcessInstance()->ReadSubBoard(param_.component_and_images.first.subboard_name);

    cv::Point2f origin_subboard_center = { subboard->x,subboard->y };

    std::unordered_map<std::string, cv::Point2f> distance_points;

    //计算最远端的x，y偏移
    cv::Point2f  x_direction_offset = { 0.0f, 0.0f };
    cv::Point2f  y_direction_offset = { 0.0f, 0.0f };
    for (const auto& [id_, rect_] : param_.temp_mark_and_rect_map)
    {
        auto point = CalculateDistance({ {component.x/* - component_unit.width / 2*/,component.y/* - component_unit.height / 2*/,component_unit.width, component_unit.height},
            param_.component_and_images.second }, rect_);
        if (id_.find("x_direction") != std::string::npos)
        {
            x_direction_offset = point;
        }
        else if (id_.find("y_direction") != std::string::npos)
        {
            y_direction_offset = point;
        }
        else
        {
            Log_ERROR("数值信息匹配失败，请检查");
            return -1;
        }
    }
    if (param_.rows > 1)
    {
        // y_direction_offset.y /= (param_.rows - 1);
        y_direction_offset /= (param_.rows - 1);
    }
    if (param_.cols > 1)
    {
        //x_direction_offset.x /= (param_.cols - 1);
        x_direction_offset /= (param_.cols - 1);
    }
    //找到板子中心位置
    std::unordered_map<std::string, cv::Vec3f> subboards_center;
    for (int row = 0; row < param_.rows; ++row)
    {
        for (int col = 0; col < param_.cols; ++col)
        {
            if (row == 0 && col == 0)
            {
                continue;
            }
            cv::Point2f subboard_center = origin_subboard_center
                + row * y_direction_offset
                + col * x_direction_offset;
            subboards_center[std::to_string(row) + "," + std::to_string(col)] = { subboard_center.x, subboard_center.y, 0.0 };
        }
    }
    //克隆出所有板子信息
    param_.temp_clone_subboards = SubboardClone(subboard, subboards_center);
    current_render_event_param->get().sub_edit_param.multi_event_param.regular_multiple_board_param->temp_mark_and_rect_map.clear();

    return 0;
}

std::vector<jrsdata::SubBoard> jrsaoi::RegularMultipleBoards::SubboardClone(const std::optional<jrsdata::SubBoard>& subboard_,
    const std::unordered_map<std::string, cv::Vec3f>& clone_subboard_centers_)
{
    std::vector<jrsdata::SubBoard> clone_subboards;
    if (!subboard_.has_value())
        return clone_subboards;
    if (subboard_->component_info.empty())
        return clone_subboards;

    if (_clear_layer_callback)
    {
        _clear_layer_callback(LayerConverter::ToString(Layer::temp_mark), false);
    }
    auto project_process = _project_param_instance.GetProjectDataProcessInstance();

    int current_subboard_id = project_process->GetSubboardMaxId();

    std::vector<std::shared_ptr<GraphicsAbstract>> temp_ghs;
    for (auto [row_col_str, center_point] : clone_subboard_centers_)
    {

        auto& x = center_point[0];
        auto& y = center_point[1];

        jrsdata::SubBoard new_sub(*subboard_);
        //auto split_sub_str = jrscore::AOITools::SplitString(row_col_str, ',');
        new_sub.id = ++current_subboard_id;
        //if (split_sub_str.size() == 2)
        //{
        //    new_sub.subboard_name = "subboard_row-" + split_sub_str[0] + "_col-" + split_sub_str[1]; // 分配新的id    待实现 数字自增
        //}
        new_sub.subboard_name = project_process->GetProjectName() + "_" + std::to_string(current_subboard_id);
        new_sub.x = x;
        new_sub.y = y;
        //!创建临时图形！
        auto new_subboard_temp_graphic = std::make_shared<RectGraphics>((float)new_sub.x, (float)new_sub.y,
            (float)new_sub.width, (float)new_sub.height, 0.0f);

        new_subboard_temp_graphic->SetId(new_sub.subboard_name);
        temp_ghs.emplace_back(new_subboard_temp_graphic);
        auto new_sub_id_str = std::to_string(new_sub.id);

        float xoffset = subboard_->x - new_sub.x;
        float yoffset = subboard_->y - new_sub.y;

        // 元件拷贝
        for (auto& c : new_sub.component_info)
        {
            c.subboard_name = new_sub.subboard_name;
            c.x -= xoffset;
            c.y -= yoffset;

            auto& name = c.component_name; // 元件id需要外部生成
            name = jrscore::AOITools::GetPrefixString(name) + "_" + new_sub_id_str;

            //添加元件图形
            auto new_component_temp_graphic = std::make_shared<RectGraphics>((float)c.x, (float)c.y,
                10.0f, 10.0f, 0.0f);
            new_component_temp_graphic->SetId(c.subboard_name + ";" + c.component_name);
            temp_ghs.emplace_back(new_component_temp_graphic);
        }
        /** <拷贝mark信息 */
        for (auto& mark : new_sub.sub_mark)
        {
            mark.subboard_name = new_sub.subboard_name;
            project_process->GenerateComponentNameBySubboardID(mark, new_sub.id);
            mark.x -= xoffset;
            mark.y -= yoffset;
        }
        /** <拷贝barcode信息 */
        new_sub.barcode.x -= xoffset;
        new_sub.barcode.y -= yoffset;
        new_sub.barcode.subboard_name = new_sub.subboard_name;
        project_process->GenerateComponentNameBySubboardID(new_sub.barcode, new_sub.id);

        /** <拷贝badmark */
        new_sub.bad_mark.x -= xoffset;
        new_sub.bad_mark.y -= yoffset;
        new_sub.bad_mark.subboard_name = new_sub.subboard_name;
        project_process->GenerateComponentNameBySubboardID(new_sub.bad_mark, new_sub.id);

        clone_subboards.push_back(new_sub);
    }
    if (_add_graphics_callback)
    {
        _add_graphics_callback(temp_ghs, LayerConverter::ToString(Layer::temp_mark), false);
    }
    return clone_subboards;
}

int jrsaoi::RegularMultipleBoards::GenerateCloneSubboards(jrsdata::MultiBoardEventParam::RegularParam& param_)
{
    //清除temp_mark 层数据
    for (auto subboard : param_.temp_clone_subboards)
    {
        _project_param_instance.GetProjectDataProcessInstance()->ReplaceSubboard(subboard);
    }
    //工程更新到图像
    if (_project_update_graphics_callback)
    {
        _project_update_graphics_callback();
    }

    param_.temp_clone_subboards.clear(); // 数据清除

    return jrscore::AOI_OK;
}

int jrsaoi::RegularMultipleBoards::CancelGenerateCloneSubboards(jrsdata::MultiBoardEventParam::RegularParam& param_)
{
    // 数据清除
    _clear_layer_callback(LayerConverter::ToString(Layer::temp_mark), false);
    param_.temp_clone_subboards.clear();
    return 0;
}

int jrsaoi::RegularMultipleBoards::ShowImage(std::string window_name_, const cv::RotatedRect& rect_)
{
    auto images = _project_param_instance.GetProjectDataProcessInstance()->ReadImage();
    if (!images.has_value() || images.value()->empty())
    {
        return -1;
    }
    cv::Mat src_image_ = images.value()->at(0);
    cv::namedWindow(window_name_, cv::WINDOW_NORMAL);

    // 获取旋转矩形的4个角点
    cv::Point2f vertices[4];
    rect_.points(vertices);  // 将矩形的角点存储在vertices数组中

    // 将旋转矩形的4个角点连接成一个多边形
    for (int i = 0; i < 4; i++)
    {
        // 画线连接每两个连续的点
        cv::line(src_image_, vertices[i], vertices[(i + 1) % 4], cv::Scalar(0, 255, 0), 2);
    }

    // 如果需要，可以画出矩形的中心点
    cv::circle(src_image_, rect_.center, 3, cv::Scalar(0, 0, 255), -1);

    cv::imshow(window_name_, src_image_);
    cv::waitKey(0);
    return 0;
}

jrsaoi::RegularMultipleBoards::RegularMultipleBoards()
    :_regular_multiple_board_param_ptr(std::make_shared<jrsdata::MultiBoardEventParam::RegularParam>())
{
}

jrsaoi::RegularMultipleBoards::~RegularMultipleBoards()
{
}
std::unordered_map<jrsdata::LightImageType, cv::Mat> jrsaoi::RegularMultipleBoards::ImageCapture(cv::Rect2f rect_)
{
    std::unordered_map<jrsdata::LightImageType, cv::Mat> template_imgs;
    auto images = _project_param_instance.GetProjectDataProcessInstance()->ReadImage();
    if (!images.has_value())
    {
        return template_imgs;
    }
    for (const auto& [type, img] : *images.value())
    {
        cv::Rect2f safe_rect = rect_ & cv::Rect2f(0, 0, img.cols * 1.0f, img.rows * 1.0f);
        if (safe_rect.width > 0 && safe_rect.height > 0)
        {
            template_imgs[static_cast<jrsdata::LightImageType>(type)] = img(safe_rect);
        }
        else
        {
            template_imgs[static_cast<jrsdata::LightImageType>(type)] = cv::Mat();
        }
    }
    return template_imgs;
}


