#include "graphicsalgorithm.h"

bool isRectIntersecteWithRotatedRect(const cv::RotatedRect& rr1, const cv::RotatedRect& rr2)
{
    std::vector<cv::Point2f> intersection;
    int result = cv::rotatedRectangleIntersection(rr1, rr2, intersection);
    // 检查交集结果
    return result != cv::INTERSECT_NONE;
}

double getDisPointToContour(const cv::Point2f& p, const std::vector<cv::Point2f>& contour, bool measureDist)
{
    return cv::pointPolygonTest(contour, p, measureDist);
}

void SortRotatedRectPoints(cv::Point2f vetPoints[], cv::RotatedRect rect, int flag)
{
    rect.points(vetPoints);

    cv::Point2f curpoint;
    if (flag == 0)
    {
        // 按X轴排序
        for (int i = 0; i < 4; ++i)
        {
            for (int k = i + 1; k < 4; ++k)
            {
                if (vetPoints[i].x > vetPoints[k].x)
                {
                    curpoint = vetPoints[i];
                    vetPoints[i] = vetPoints[k];
                    vetPoints[k] = curpoint;
                }
            }
        }

        // 判断X坐标前两个定义左上左下角
        if (vetPoints[0].y > vetPoints[1].y)
        {
            curpoint = vetPoints[0];
            vetPoints[0] = vetPoints[1];
            vetPoints[1] = vetPoints[3];
            vetPoints[3] = curpoint;
        }
        else
        {
            curpoint = vetPoints[3];
            vetPoints[3] = vetPoints[1];
            vetPoints[1] = curpoint;
        }

        // 判断X坐标后两个定义右上右下角
        if (vetPoints[1].y > vetPoints[2].y)
        {
            curpoint = vetPoints[1];
            vetPoints[1] = vetPoints[2];
            vetPoints[2] = curpoint;
        }
    }
    else
    {
        // 根据Rect的坐标点，Y轴最大的为P[0]，p[0]围着center顺时针旋转,
        // 旋转角度为负的话即是P[0]在左下角，为正P[0]是右下角
        // 重新排序坐标点
        if (rect.angle <= 0)
        {
            curpoint = vetPoints[0];
            vetPoints[0] = vetPoints[2];
            vetPoints[2] = curpoint;
            curpoint = vetPoints[1];
            vetPoints[1] = vetPoints[3];
            vetPoints[3] = curpoint;
        }
        else if (rect.angle > 0)
        {
            curpoint = vetPoints[0];
            vetPoints[0] = vetPoints[1];
            vetPoints[1] = vetPoints[2];
            vetPoints[2] = vetPoints[3];
            vetPoints[3] = curpoint;
        }
    }
}

void Ellipsepot(int x0, int y0, int x, int y, std::vector<cv::Point>& vp1, std::vector<cv::Point>& vp2, std::vector<cv::Point>& vp3, std::vector<cv::Point>& vp4)
{
    // 1
    vp1.emplace_back((x0 + x), (y0 + y));
    // 2
    vp2.emplace_back((x0 + x), (y0 - y));
    // 3
    vp3.emplace_back((x0 - x), (y0 - y));
    // 4
    vp4.emplace_back((x0 - x), (y0 + y));
}

void Bresenham_Ellipse(int xc, int yc, int a, int b)
{
    std::vector<cv::Point> vp1, vp2, vp3, vp4;
    int sqa = a * a;
    int sqb = b * b;

    int x = 0;
    int y = b;
    int d = 2 * sqb - 2 * b * sqa + sqa;

    // 绘制椭圆的起始点
    Ellipsepot(xc, yc, x, y, vp1, vp2, vp3, vp4);

    // 计算 P_x，椭圆的长轴与短轴的交点，用于判断下半部分的绘制范围
    int P_x = (int)((double)sqa / sqrt((double)((double)sqa + sqb)));

    while (x <= P_x)
    {
        if (d < 0)
        {
            d += 2 * sqb * (2 * x + 3);
        }
        else
        {
            d += 2 * sqb * (2 * x + 3) - 4 * sqa * (y - 1);
            y--;
        }

        x++;
        Ellipsepot(xc, yc, x, y, vp1, vp2, vp3, vp4);
    }

    // 更新 d 以绘制椭圆下半部分
    d = sqb * (x * x + x) + sqa * (y * y - y) - sqa * sqb;

    while (y >= 0)
    {
        Ellipsepot(xc, yc, x, y, vp1, vp2, vp3, vp4);
        y--;

        if (d < 0)
        {
            x++;
            d = d - 2 * sqa * y - sqa + 2 * sqb * x + 2 * sqb;
        }
        else
        {
            d = d - 2 * sqa * y - sqa;
        }
    }
}

std::vector<float> generateRotatedEllipsePoints(float centerX, float centerY, float majorAxis, float minorAxis, double angle)
{
    std::vector<float> ellipsePoints(720);
    // ellipsePoints.resize(720);

    // 将角度转换为弧度
    double radians = A_DEG_TO_RAD(angle);
    double sin_radians = sin(radians);
    double cos_radians = cos(radians);

    // 生成椭圆上的点
    for (int theta = 0; theta < 360; ++theta)
    {
        double thetaRadians = A_DEG_TO_RAD((double)theta);
        double sin_thetaRadians = sin(thetaRadians);
        double cos_thetaRadians = cos(thetaRadians);
        double x = centerX + majorAxis * cos_thetaRadians * cos_radians - minorAxis * sin_thetaRadians * sin_radians;
        double y = centerY + majorAxis * cos_thetaRadians * sin_radians + minorAxis * sin_thetaRadians * cos_radians;
        // ellipsePoints.emplace_back(static_cast<int>(x), static_cast<int>(y));
        ellipsePoints[static_cast<long long>(theta) << 1] = static_cast<float>(x);
        ellipsePoints[(static_cast<long long>(theta) << 1) + 1] = static_cast<float>(y);
    }

    return ellipsePoints;
}

/**
 * @brief 计算阶乘的函数
 *
 * @param n 需要计算阶乘的整数
 * @return int 阶乘的结果
 */
int factorial(int n)
{
    return (n <= 1) ? 1 : n * factorial(n - 1);
}

/**
 * @brief 计算二项式系数的函数
 *
 * @param n 总数
 * @param k 子集数
 * @return int 二项式系数
 */
int binomialCoefficient(int n, int k)
{
    return factorial(n) / (factorial(k) * factorial(n - k));
}

bool IsPointInPolygon(const cv::Point2f& pt, const std::vector<cv::Point2f>& contour)
{
    std::vector<cv::Point2f> contourPoints;
    for (const auto& point : contour)
    {
        contourPoints.push_back(cv::Point2f(point.x, point.y));
    }
    return cv::pointPolygonTest(contourPoints, pt, false) >= 0;
}

double PointDisToPolygon(const cv::Point2f& pt, const std::vector<Vec2>& contour)
{
    std::vector<cv::Point2f> contourPoints;
    for (const auto& point : contour)
    {
        contourPoints.push_back(cv::Point2f(point.x, point.y));
    }
    return cv::pointPolygonTest(contourPoints, pt, true);
}

double PointDisToPolygon(const Vec2& pt, const std::vector<Vec2>& contour)
{
    std::vector<cv::Point2f> contourPoints;
    for (const auto& point : contour)
    {
        contourPoints.push_back(cv::Point2f(point.x, point.y));
    }
    return cv::pointPolygonTest(contourPoints, cv::Point2f(pt.x, pt.y), true);
}

cv::RotatedRect CreateRotatedRectFromDiagonalPointsAndAngle(const cv::Point2f& diagonal_pt1, const cv::Point2f& diagonal_pt2, float angle)
{
    // 计算矩形的中心点
    cv::Point2f center = (diagonal_pt1 + diagonal_pt2) * 0.5f;

    // 计算矩形的对角线长度
    float diag_length = std::hypot(diagonal_pt2.x - diagonal_pt1.x, diagonal_pt2.y - diagonal_pt1.y);

    float rad = A_DEG_TO_RAD(angle);
    // 根据旋转角度计算宽度和高度
    float width = std::abs(diag_length * std::cos(rad));
    float height = std::abs(diag_length * std::sin(rad));

    // 创建并返回旋转矩形
    return cv::RotatedRect(center, cv::Size2f(width, height), -angle);
}

cv::RotatedRect GetEffectiveRotateRect(const float& xstart, const float& ystart, const float& xend, const float& yend, const float& angle)
{
    float x, y, w, h;

    x = (xend + xstart) * 0.5f;
    y = (yend + ystart) * 0.5f;

    if (angle == 0)
    {
        w = abs(xend - xstart);
        h = abs(yend - ystart);
    }
    else
    {
        auto radius = A_DEG_TO_RAD(angle);
        float cosA = (float)std::cos(radius);
        float sinA = (float)std::sin(radius);
        /*方向向量*/
        cv::Point2f dirVec(cosA, sinA);
        /*垂直向量*/
        cv::Point2f verticalVec(-sinA, cosA);
        auto start_2_end_vector = cv::Point2f(xend - xstart, yend - ystart);
        auto w_vector = ProjectVector(start_2_end_vector, dirVec);
        auto h_vector = ProjectVector(start_2_end_vector, verticalVec);
        w = VectorLength(w_vector);
        h = VectorLength(h_vector);
    }
    return cv::RotatedRect(cv::Point2f(x, y), cv::Size2f(w, h), angle);
}
