﻿/*********************************************************************
 * @brief  图形属性类.
 *
 * @file   graphicsattributes.hpp
 *
 * @date   2024.05.30
 * <AUTHOR>
**********************************************************************/
#ifndef GRAPHICS_ATTRIBUTES_HPP
#define GRAPHICS_ATTRIBUTES_HPP

#include "graphicsapi.hpp"
#include "graphicsid.h"
#include "algodefine.hpp" // A_DEG_TO_RAD

#pragma warning(push, 1)
#include "opencv2/core/types.hpp"
#pragma warning(pop)

#include <memory>
#include <string>
#include <cmath>
#include <vector>

/**
 * @brief 图形参数.
 * @note  TODO 目前图形父子之间的坐标系关系只有平移和旋转,且非常零散,需要重新整理.
 */
template <typename _T>
class GRAPHICS_API GraphicsAttributes : public std::enable_shared_from_this<GraphicsAttributes<_T>>
{
public:
    GraphicsAttributes() : cx(0), cy(0), width(0), height(0), angle(0) {}
    GraphicsAttributes(_T x, _T y, _T w, _T h, _T a) : cx(x), cy(y), width(w), height(h), angle(a) {}
    GraphicsAttributes(_T x, _T y, _T w, _T h, _T a, const GraphicsID& id_)
        : cx(x), cy(y), width(w), height(h), angle(a), id(id_)
    {
    }
    // 拷贝构造函数 浅拷贝
    GraphicsAttributes(const GraphicsAttributes& other)
        : cx(other.cx), cy(other.cy), width(other.width), height(other.height), angle(other.angle), id(other.id)
        , parent_weak(other.parent_weak), children(other.children)
    {
    }
    virtual ~GraphicsAttributes();

    // 赋值运算符 浅拷贝
    GraphicsAttributes& operator=(const GraphicsAttributes& other)
    {
        if (this != &other)
        {
            cx = other.cx;
            cy = other.cy;
            width = other.width;
            height = other.height;
            angle = other.angle;
            parent_weak = other.parent_weak;
            children = other.children;
            id = other.id;
            SetRequireUpdate();
        }
        return *this;
    }

    inline _T x() const;
    inline _T y() const;
    inline _T w() const { return width; }
    inline _T h() const { return height; }
    inline _T a() const;
    inline _T LocalX() const { return cx; }
    inline _T LocalY() const { return cy; }
    inline _T LocalA() const { return angle; }

    inline cv::Point_<_T> GetCenter() const { return { x(), y() }; }
    inline cv::Size_<_T> GetSize() const { return { w(), h() }; }
    inline cv::RotatedRect GetBoundingbox() const;
    /**
     * @brief 设置相对值
     */
    inline void SetValue(_T x, _T y, _T w, _T h, _T a);
    inline void SetX(_T val);
    inline void SetY(_T val);
    inline void SetW(_T val);
    inline void SetH(_T val);
    inline void SetA(_T val);
    inline void SetXY(_T x, _T y);
    inline void SetWH(_T w, _T h);
    inline void SetLocalXY(_T x, _T y);
    inline void SetLocalA(_T val);

    /*id*/
    inline const GraphicsID& GetId() const { return id; }
    void SetId(const GraphicsID& val) { id = val; }
    void SetId(const std::string& val) { id.SetString(val); }
    void CreateId() { id.Create(); }
    bool IsIdEmpty() const { return id.IsEmpty(); }

    /*计算*/
    inline int manhattanLength() const;
    inline double DistanceTo(const GraphicsAttributes& other) const;

    /*父子关系处理*/
    inline bool IsHaveParent() const { return !parent_weak.expired(); }
    inline const std::shared_ptr<GraphicsAttributes> GetParent() const { return parent_weak.lock(); }
    inline void SetParent(const std::shared_ptr<GraphicsAttributes>& val);
    inline void UnsetParent();
    inline bool IsHaveChild() const { return !children.empty(); }
    inline const std::vector<std::weak_ptr<GraphicsAttributes>>& GetChildren() const { return children; }
    inline void AddChild(const std::shared_ptr<GraphicsAttributes>& val);
    inline void RemoveChild(const std::shared_ptr<GraphicsAttributes>& val);
    inline void ClearChildren();

    inline bool IsNeedUpdate() const { return update; }
    inline bool IsUpdateFromChild() const { return child_update; }
protected:
    inline void SetRequireUpdate();
    inline void SetUpdated() { update = false; }
    inline void SetChildRequireUpdate() { child_update = true; }
    inline void SetChildUpdated() { child_update = false; }

private:
    inline void GlobalXYToLocal(_T& x, _T& y, const std::shared_ptr<GraphicsAttributes>& parent) const;
    inline bool IsCircularParent(const std::shared_ptr<GraphicsAttributes>& newParent, const std::shared_ptr<GraphicsAttributes>& obj);

private:
    GraphicsAttributes(const GraphicsAttributes&& other) = delete;

private:
    _T cx;     ///<  中心x
    _T cy;     ///<  中心y
    _T width;  ///<  宽
    _T height; ///<  高
    _T angle;  ///<  旋转角度(°)

    GraphicsID id; ///< 图形ID

    std::weak_ptr<GraphicsAttributes> parent_weak;  ///< 父对象
    std::vector<std::weak_ptr<GraphicsAttributes>> children; ///< 子对象
    bool update = true; ///< 是否需要更新
    bool child_update = false; ///< 子对象是否更新
};

template<typename _T>
inline GraphicsAttributes<_T>::~GraphicsAttributes()
{
    if (IsHaveChild())
    {
        ClearChildren();
    }
}

template <typename _T>
inline _T GraphicsAttributes<_T>::x() const
{
    if (!IsHaveParent())
        return cx;
    auto parent = parent_weak.lock();
    auto a = parent->a();
    double radians = A_DEG_TO_RAD(a);
    double cos_a = std::cos(radians);
    double sin_a = std::sin(radians);
    double x_r = cx * cos_a - cy * sin_a;
    return (_T)x_r + parent->x();
}

template <typename _T>
inline _T GraphicsAttributes<_T>::y() const
{
    if (!IsHaveParent())
        return cy;
    auto parent = parent_weak.lock();
    auto a = parent->a();
    double radians = A_DEG_TO_RAD(a);
    double cos_a = std::cos(radians);
    double sin_a = std::sin(radians);
    double y_r = cx * sin_a + cy * cos_a;
    return (_T)y_r + parent->y();
}

template <typename _T>
inline _T GraphicsAttributes<_T>::a() const
{
    if (!IsHaveParent())
        return angle;
    auto parent = parent_weak.lock();
    return angle + parent->a();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetValue(_T x, _T y, _T w, _T h, _T a)
{
    cx = x;
    cy = y;
    width = w;
    height = h;
    angle = a;
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetX(_T val)
{
    auto parent = parent_weak.lock();
    if (!parent)
        cx = val;
    else
    {
        // 传一层就可以，为啥？？？ 中间没有涉及到值的修改
        auto nx = val;
        auto ny = this->y();
        GlobalXYToLocal(nx, ny, parent);
        cx = nx;
        cy = ny;

        // cx = val;
        // cy = this->y();
        // GlobalXYToLocal(cx, cy);
    }
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetY(_T val)
{
    auto parent = parent_weak.lock();
    if (!parent)
        cy = val;
    else
    {
        cx = this->x();
        cy = val;
        GlobalXYToLocal(cx, cy, parent);
    }
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetW(_T val)
{
    width = val;
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetH(_T val)
{
    height = val;
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetA(_T val)
{
    if (isnan(val))
    {
        val = 0;
    }

    if (!IsHaveParent())
    {
        return SetLocalA(val);
    }

    auto parent = parent_weak.lock();
    // angle = val - parent->a();
    SetLocalA(val - parent->a());
    // angle = std::fmod(angle + 360.0f, 360.0f);
    // SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetXY(_T x, _T y)
{
    auto parent = parent_weak.lock();
    if (!parent)
    {
        return SetLocalXY(x, y);
    }

    cx = x;
    cy = y;
    GlobalXYToLocal(cx, cy, parent);

    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetWH(_T w, _T h)
{
    width = w;
    height = h;
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetLocalXY(_T x, _T y)
{
    cx = x;
    cy = y;
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetLocalA(_T val)
{
    angle = std::fmod(val + 360.0f, 360.0f);
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetRequireUpdate()
{
    update = true;
    // 传递给所有子对象
    for (auto& childptr : children)
    {
        auto child = childptr.lock();
        if (child)
        {
            child->SetRequireUpdate();
        }
    }
    if (IsHaveParent())
    {
        auto parent = parent_weak.lock();
        if (parent)
        {
            parent->SetChildRequireUpdate();
        }
    }
}

template <typename _T>
inline int GraphicsAttributes<_T>::manhattanLength() const
{
    return std::abs(x) + std::abs(y);
}

template <typename _T>
inline double
GraphicsAttributes<_T>::DistanceTo(const GraphicsAttributes& other) const
{
    return std::sqrt(std::pow(other.x - x, 2) + std::pow(other.y - y, 2));
}

template <typename _T>
inline void GraphicsAttributes<_T>::GlobalXYToLocal(_T& x, _T& y, const std::shared_ptr<GraphicsAttributes>& parent) const
{
    auto nx = x - parent->x();
    auto ny = y - parent->y();

    auto a = -parent->a();

    double radians = A_DEG_TO_RAD(a);
    double cos_a = std::cos(radians);
    double sin_a = std::sin(radians);
    double x_r = nx * cos_a - ny * sin_a;
    double y_r = nx * sin_a + ny * cos_a;
    x = (_T)x_r;
    y = (_T)y_r;
}

template <typename _T>
inline bool GraphicsAttributes<_T>::IsCircularParent(const std::shared_ptr<GraphicsAttributes>& newParent, const std::shared_ptr<GraphicsAttributes>& obj)
{
    auto current = newParent;
    while (current)
    {
        if (current == obj)
        {
            return true;
        }
        current = current->parent_weak.lock();
    }
    return false;
}

template <typename _T>
inline void GraphicsAttributes<_T>::SetParent(const std::shared_ptr<GraphicsAttributes>& val)
{
    if (!val || IsCircularParent(val, shared_from_this()))
        return;

    if (IsHaveParent())
    {
        UnsetParent();
    }
    parent_weak = val;
    val->AddChild(shared_from_this());

    GlobalXYToLocal(this->cx, this->cy, val);
    this->angle -= val->a();

    // 2025.01.08 wangzhengkai 添加当角度旋转到一定范围时将宽高调换解决旋转矩形变形问题
    _T temp_angle = this->angle;
    while (temp_angle < 0 || temp_angle>360)
    {
        if (temp_angle < 0)
        {
            temp_angle += 360;
        }

        if (temp_angle > 360)
        {
            temp_angle -= 360;
        }
    }

    if ((45 < temp_angle && temp_angle < 135) ||
        (255 < temp_angle && temp_angle < 315))
    {
        std::swap(width, height);
    }
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::UnsetParent()
{
    auto parent = parent_weak.lock();
    if (!parent)
        return;
    // parent->RemoveChild(shared_from_this());
    auto nx = this->x();
    auto ny = this->y();
    auto na = this->a();

    this->cx = nx;
    this->cy = ny;
    this->angle = na;
    parent_weak.reset();
    SetRequireUpdate();
}

template <typename _T>
inline void GraphicsAttributes<_T>::AddChild(const std::shared_ptr<GraphicsAttributes>& val)
{
    if (!val)
        return;
    auto it = std::find_if(this->children.begin(), this->children.end(),
        [&val](auto& ele) { return val == ele.lock(); });
    if (it == this->children.end())
    {
        this->children.push_back(val);
    }
}

template <typename _T>
inline void GraphicsAttributes<_T>::RemoveChild(const std::shared_ptr<GraphicsAttributes>& val)
{
    if (!val)
        return;

    // 符合要求的元素被移动到最后
    auto it = std::remove_if(children.begin(), children.end(),
        [&val](const std::weak_ptr<GraphicsAttributes>& weakPtr)
        {
            if (weakPtr.expired()) return false;

            if (auto sharedPtr = weakPtr.lock(); weakPtr.lock() == val)
            {
                sharedPtr->UnsetParent();
                return true;
            }
            return false;
        });

    // 删除符合条件的元素
    if (it != children.end())
    {
        children.erase(it, children.end());
    }
}

template <typename _T>
inline void GraphicsAttributes<_T>::ClearChildren()
{
    //TODO 
    for (auto& child : children)
    {
        if (child.expired())
            continue;
        child.lock()->UnsetParent();
    }
    std::vector<std::weak_ptr<GraphicsAttributes>>().swap(children);
}

template <typename _T>
inline cv::RotatedRect GraphicsAttributes<_T>::GetBoundingbox() const
{
    return cv::RotatedRect(GetCenter(), GetSize(), a());
}

#endif // !GRAPHICS_ATTRIBUTES_HPP