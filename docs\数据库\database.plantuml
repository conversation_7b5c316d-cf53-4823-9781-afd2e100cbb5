﻿@startuml 数据库结构
' 用户表
struct TUser {
    std::string user_name;   /**< 操作员名称 */
    std::string password;    /**< 密码 */
    std::string create_time; /**< 操作时间 */
    int permission_level;    /**< 权限等级 */
}
' AOI系统参数
struct TAOIMachine {
    std::string machine_id;              /**< 机器编号，每台机器的唯一标识 */
    std::string fixture_id;              /**< 线体ID */
    std::string work_station_name;       /**< 工作站名 */
    std::string station_id;              /**< 设备站别 */
    std::string soft_version;            /**< 版本号 */

}
' 主板参数
struct TBoard {
    /**<AOI 写入数据*/
    std::string board_barcode;                      /**< 总板条码 */
    std::string board_start_detect_time;            /**< 开始检测时间 */
    std::string board_end_detect_time;              /**< 结束检测时间 */

    std::string board_img_path;                     /**< 整版图片路径 */
    std::string ng_img_path;                        /**< NG图片路径 */
    std::string transform_matrix;                   /**< 旋转矩阵 */

    int img_height;                                 /**< 图像高度 */
    int img_width;                                  /**< 图像宽度 */

    bool board_is_front;                            /**< 板子是否正面*/
    int subboard_cols;                              /**< 子板列数 */
    int subboard_rows;                              /**< 子板行数 */
    int num_sub_board;                              /**< 子板个数 */
    int layout;                                     /**< 子板布局方式，如鸳鸯板，太极板子，复制板子 （枚举） */
    int material;                                   /**< 整板材质 （黑板，白板，胶板） */

    int board_detect_result;                        /**< 总板检测结果：0 NG板，1 pass板，-1 测试板（测试版不进行数据统计） */
    int board_devices;                              /**< 总元件数目 */
    int board_detected_devices;                     /**< 已检测元件数目 */
    int board_no_detected_devices;                  /**< 未检测元件数目 */
    int board_masked_subboards;                     /**< 屏蔽子板数目 */
    int board_masked_subboard_devices;              /**< 屏蔽子板上的元件总数 */

    /**< AOI 写入 & center controller 修改*/
    int board_ng_devices;                           /**< 不良元件数目 */
    int board_no_judgment_subboards;                /**< 待复判子板数 */

    std::string project_name;                       /**< 项目名称 */
    std::string user_name;                          /**< 操作员名称 */
    std::string machine_id;                         /**< 机器ID */
    /**<center controller 写入参数*/
    std::string board_rejudgment_time;              /**< 人工复判时间*/
    bool board_rejudgment_result;                   /**< 人工复判结果*/
    int board_misjudge_devices;                     /**< 误判元件数 */
    /**<自增字段无需赋值*/
    int board_id;                                   /**< 总板ID 自增  无需赋值*/

}

' 子板数据
struct TSubboard {
    /**<AOI 写入数据*/
    int subboard_col;                        /**< 列号 */
    int subboard_row;                        /**< 行号 */
    int subboard_x;                          /**< 子板左上角x坐标，整图中的像素坐标 */
    int subboard_y;                          /**< 子板左上角y坐标，整图中的像素坐标 */
    int subboard_width;                      /**< 子板宽度 */
    int subboard_height;                     /**< 子板高度 */
    std::string subboard_barcode;            /**< 子板条码 */
    bool subboard_result;                    /**< 机器检测结果 */
    std::string subboard_detect_time;        /**< 检测完成时间 */
    int subboard_devices;                    /**< 总元件数 */
    int subboard_detect_devices;             /**< 已检测元件数 */
    int subboard_no_detect_devices;          /**< 未检测元件数 */
    int subboard_pass_devices;               /**< 已检测通过元件数 */
    
    bool subboard_is_detection;              /**< 该子板是否被屏蔽 */
    int subboard_id;                         /**< 子板ID */
    int board_id;                            /**< 总板ID */

    /**< AOI写入 & center controller修改*/
    int subboard_ng_devices;                 /**< 已检测不合格元件数 */
    int subboard_no_judgment_devices;        /**< 已检测ng待复判元件数 */
    
    /**<center controller 写入数据*/
    std::string subboard_rejudgment_time;    /**< 复判时间 */
    bool subboard_rejudgment_result;         /**< 复判结果 */
    int subboard_misjudge_devices;           /**< 已检测误判元件数 */


}

' 元件表
struct TDevice {
    /**<AOI 写入数据*/
    std::string device_name;                       /**< 元件名称 */
    std::string device_part_no;                    /**< 元件料号 */
    std::string device_type;                       /**< 元件类型 */
    int device_x;                                  /**< x坐标 ：像素 */
    int device_y;                                  /**< y坐标 ：像素 */
    int device_angle;                              /**< 角度 ：像素 */
    int device_width;                              /**< 宽度 ：像素 */
    int device_height;                             /**< 高度 ：像素 */
    bool device_result;                            /**< 机器检测结果 */
    std::string device_img_path;                   /**< 元件图片路径 */
    std::string device_detect_time;                /**< 检测完成时间 */
    std::string device_flaw_type_ids;              /**< 元件缺陷名称列表 */
    -std::string device_group;                      /**< 元件Group,TODO: HJC 8-18 是否需要该字段待定 */
    int cnt_detect_windows;                        /**< 共有多少检测框 */
    int ng_cnt_detect_windows;                     /**< 一共有多少检测框ng */
    bool device_is_detection;                      /**< 是否检测 */

    int device_id;                                 /**< 元件ID */
    int subboard_id;                               /**< 子板ID */
    int board_id;                                  /**< 总板ID */

    /**<center controller  写入数据*/
    bool device_rejudgment_result;                 /**< 复判结果 */
    bool device_is_rejudged;                       /**< 是否复判 默认为 false */
    std::string device_rejudgment_flaw_type_ids;   /**< 复判缺陷 */
    std::string device_rejudgment_time;            /**< 复判时间 */
}


' 分组
struct TGroup
{
    /**< AOI 写入数据*/
    int subboard_id;           /**< 子板ID */
    int device_id;             /**< 元件ID */
    int group_id;              /**< 组ID */
    std::string group_name;    /**< 检测组名称 */
    int group_usage;           /**< 检测组类型 */
    bool group_result;         /**< 检测组总结果 */
    int board_id;              /**< 总板ID */

}
' ng类型
struct TDetectType
{
    /**<只需查找 无需写入*/
    int detect_type_id;                         /**< 缺陷编号ID   */
    std::string detect_type_name;               /**< 缺陷类型名称 */
    std::string detect_type_description;        /**< 缺陷类型描述 */
    std::string map_default_code;               /**< 映射错误代码 */
    std::string map_default_name;               /**< 映射名称     */
}

' 算法检测框
struct TDetectWindow {
    /**< TODO: AOI需赋值字段*/
    std::string detect_window_name;            /**< 检测框名称 */
    int detect_window_usage;                   /**< 检测框类型 */
    bool detect_window_result;                 /**< 检测框结果 */
    int detect_window_detect_code;             /**< 缺陷类型编号 */
    bool detect_window_is_child_window;        /**< 是否在group组内的检测框 */
    -int detect_window_result_id;               /**< 检测框结果ID     TODO: 是否需要？ 待定*/
    -std::string window_result_name;            /**< 检测框结果名称   TODO: 是否需要？ 待定*/
    /** {"detect_value":0,"lower_limit":-2147483648, "standard_value":-2147483648,"upper_limit":-2147483648}*/
    std::string window_result_data;            /**< 检测结果数据   */
    int template_img_id;                       /**< 模版图片ID */
    int light_img_id;                          /**< 灯光图片ID */
    int detect_window_level;                   /**< 检测等级 */
    int detect_window_x;                       /**< x坐标*/
    int detect_window_y;                       /**< y坐标*/
    int detect_window_width;                   /**< 宽度 */
    int detect_window_height;                  /**< 高度 */
    int detect_window_angle;                   /**< 旋转角度*/
    std::string color_param;                   /**< 颜色参数 */
    std::string light_param;                   /**< 灯光配置 */
    int detect_window_state;                   /**< 检测状态（ok，ng，未检测，不检测，检测异常） */
    bool detect_window_is_detection;           /**< 是否检测 */

    int detect_window_id;                       /**< 检测框ID */
    int subboard_id;                            /**< 子板ID   */
    int device_id;                              /**< 元件ID   */
    int group_id;                               /**< 组ID     */
    int algorithm_id;                           /**< 算法ID   */
    int board_id;                               /**< 总板ID */
    /**< center controller 写入数据*/
    bool detect_window_is_rejudged;             /**< 检测框是否复判 */
    int detect_window_rejudged_type_id;         /**< 检测框复判类型ID */
    
}
' 表间关系
TUser "1" --* "n" TBoard
TAOIMachine "1"--* "n" TBoard
TSubboard "1" --* "n" TBoard
TSubboard "1" *-- "n" TDevice
TGroup "n" --* "1" TDevice
TGroup "1" *-- "n" TDetectWindow
TDevice"1" *-- "n"TDetectType