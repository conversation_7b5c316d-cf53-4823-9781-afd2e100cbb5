///*****************************************************************//**
// * @file   toolbarcontroller.h
// * @brief  快捷工具栏controller类
// * @details    
// * <AUTHOR>
// * @date 2024.1.29
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/
//
//#ifndef __TOOLBARCONTROLLER_H__
//#define __TOOLBARCONTROLLER_H__
//
//#include "controllerbase.h"
//#include "toolbarmodel.h"
//#include "toolbarview.h"
//namespace jrsaoi
//{
//    class ToolBarController :public ControllerBase
//    {
//        public:
//            ToolBarController (const std::string& name);
//            ~ToolBarController ();
//            virtual int Update (const jrsdata::ViewParamBasePtr& param_) override;
//            virtual int Save (const jrsdata::ViewParamBasePtr& param_) override;
//
//            virtual void SetView (ViewBase* view_param)override;
//            virtual void SetModel (ModelBasePtr model_param)override;
//         private:
//             //ToolBarView* view;
//             ToolBarModelPtr model;
//        
//    };
//    using ToolBarControllerPtr = std::shared_ptr<ToolBarController>;
//
//}
//
//#endif // !__TOOLBARCONTROLLER_H__
