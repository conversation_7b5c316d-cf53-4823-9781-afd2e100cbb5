#include "resultstorageflow.h"
#include "dataparam.h"

namespace jrsworkflow
{
    ResultStorageFlow::ResultStorageFlow (const jrsdata::DataManagerPtr& data_manager_ptr_)
        :data_manager_ptr(data_manager_ptr_)
    {
    }
    ResultStorageFlow::~ResultStorageFlow ()
    {
    }
    int ResultStorageFlow::SaveResult ( const InspectionResultBasePtr& insp_result_ptr_)
    {
        auto res_insp = std::dynamic_pointer_cast<FlowInspectionResultParam>(insp_result_ptr_);
        res_insp->detect_result_param->event_name = jrsaoi::DETECT_RESULT_PARAM_SAVE_EVENT;
       return data_manager_ptr->EventHandler(res_insp->detect_result_param);
    
    }
    int ResultStorageFlow::StopSaveResult ()
    {
        return 0;
    }
    //！这边应该填的是查询数据库的结构体
    int ResultStorageFlow::GetDataFromDataBase(jrsdata::QueryDatabaseResult& query_data)
    {
        auto query_data_result_ptr = std::make_shared<jrsdata::QueryDatabaseResult>();
        query_data_result_ptr->event_name = query_data.event_name;
        query_data_result_ptr->query_custom_subboard_barcode = query_data.query_custom_subboard_barcode;
        data_manager_ptr->EventHandler(query_data_result_ptr);
        query_data.query_custom_subboard_barcode.output = query_data_result_ptr->query_custom_subboard_barcode.output;
        return 0;
    }
    void ResultStorageFlow::Init ()
    {
    }
}
