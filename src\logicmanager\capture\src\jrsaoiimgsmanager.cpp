#include "jrsaoiImgsmanager.h"


namespace jrslogic
{

    jrslogic::JrsAoiImgsManager::JrsAoiImgsManager(const float& _resolution, const int& _fov_w, const int& _fov_h, const bool& _x_axis_neg, const bool& _y_axis_ne)
        : stitch_tool(_resolution, _fov_w, _fov_h, _x_axis_neg, _y_axis_ne)
    {
    }

    jrslogic::JrsAoiImgsManager::~JrsAoiImgsManager()
    {

    }

    int jrslogic::JrsAoiImgsManager::ClearFovImgs()
    {
        fov_imgs.clear();
        return 0;
    }

    int JrsAoiImgsManager::ClearBoardImgs()
    {
        board_imgs.clear();
        return 0;
    }

    int jrslogic::JrsAoiImgsManager::SetSystemParam(float &_resolution, const int &_fov_w, const int &_fov_h, const bool &_x_axis_neg, const bool &_y_axis_neg)
    {
        stitch_tool = StictchImagesTool(_resolution, _fov_w, _fov_h, _x_axis_neg, _y_axis_neg);
        return 0;
    }

    int jrslogic::JrsAoiImgsManager::SetAllFovPos(const vector<Point2f> &pos)
    {
        stitch_tool.SetAllFovPos(pos);
        stitch_tool.GetBoardImageSize(board_w, board_h);
        board_imgs.clear();   
        return 0;
    }

    int jrslogic::JrsAoiImgsManager::AddFovImg(const jrsdata::OneFovImgs& img_info, const bool& stitch_flag)
    {
        fov_imgs.push_back(img_info);
        if (stitch_flag)
        {
            for(auto& img : img_info.imgs)
            {
                auto& cur_board_img = board_imgs[img.first];
                auto& output_mask = board_mask[img.first];
                if (cur_board_img.empty())
                {
                    cur_board_img = Mat::zeros(board_h, board_w, img.second.type());
                    output_mask = Mat::zeros(board_h, board_w, CV_8UC1);   
                }
                vector<std::pair<cv::Point2f, Mat>> imgs_data{std::pair<cv::Point2f, Mat>{img_info.pos, img.second}};
                if (stitch_tool.StictchImages(imgs_data, cur_board_img,output_mask,true) != 0)
                {
                    return -1;
                }
            }
        } 
        return 0;
    }

    int jrslogic::JrsAoiImgsManager::StichFovImgs()
    { 
        int stich_status = 0;
        // 检查整版图尺寸
        for(auto& board_img : board_imgs)
        {
            if (!board_img.second.empty())
            {
                if (board_img.second.rows != board_h || board_img.second.cols != board_w)
                {
                    cv::resize(board_img.second,board_img.second, cv::Size(board_w, board_h));
                }  

                if (board_img.second.channels() == 3)
                {
                    board_img.second.setTo(cv::Scalar(0, 0, 0));
                }
                else if (board_img.second.channels() == 1)
                {
                    board_img.second.setTo(cv::Scalar(0));
                }      
            }     
        }

        for(const auto& fov_img : fov_imgs)
        {
            for(auto& img : fov_img.imgs)
            {
                auto& cur_board_img = board_imgs[img.first];
                auto& output_mask = board_mask[img.first];
                if (cur_board_img.empty())
                {
                    cur_board_img = Mat::zeros(board_h, board_w, img.second.type());
                    output_mask = Mat::zeros(board_h, board_w, CV_8UC1);
                }
                vector<std::pair<cv::Point2f, Mat>> imgs_data{std::pair<cv::Point2f, Mat>{fov_img.pos, img.second}};
                if (stitch_tool.StictchImages(imgs_data, cur_board_img,output_mask,true) != 0)
                {
                    stich_status = -1;
                    continue;
                }
            }
        }
        return stich_status;
    }

    const vector<jrsdata::OneFovImgs>* JrsAoiImgsManager::GetFovImgs()
    {
        return &fov_imgs;
    }

    const map<jrsdata::LightImageType, Mat> * jrslogic::JrsAoiImgsManager::GetBoardImgs()
    {
        return &board_imgs;
    }

    const Mat* JrsAoiImgsManager::GetBoardImg(const jrsdata::LightImageType& light_type)
    {
        if (board_imgs.find(light_type) == board_imgs.end())
        {
            return nullptr;
        }

       return &board_imgs[light_type];
    }
}
