#include "pcbpathplan.h"
// 测试图片颜色
const std::vector<cv::Scalar> vecColor{
    cv::Scalar(123, 234, 45),  // 淡绿色
    cv::Scalar(200, 100, 150), // 浅紫色
    cv::Scalar(50, 100, 200),  // 深蓝色
    cv::Scalar(255, 0, 0),     // 蓝色
    cv::Scalar(0, 255, 0),     // 绿色
    cv::Scalar(0, 0, 255),     // 红色
    cv::Scalar(255, 255, 0),   // 黄色
    cv::Scalar(0, 255, 255),   // 青色
    cv::Scalar(255, 0, 255),   // 品红色
    cv::Scalar(128, 128, 128), // 灰色
    cv::Scalar(128, 0, 0),     // 深蓝色
    cv::Scalar(0, 128, 0),     // 深绿色
    cv::Scalar(0, 0, 128),     // 深红色
    cv::Scalar(128, 128, 0),   // 橄榄色
    cv::Scalar(0, 128, 128),   // 深青色
    cv::Scalar(128, 0, 128),   // 深品红色
    cv::Scalar(192, 192, 192), // 银色
    cv::Scalar(255, 165, 0),   // 橙色
    cv::Scalar(255, 20, 147),  // 粉红色
    cv::Scalar(75, 0, 130),    // 靛蓝色
    cv::Scalar(173, 255, 47),  // 黄绿色
    cv::Scalar(220, 20, 60),   // 猩红色
    cv::Scalar(30, 144, 255),  // 道奇蓝
    cv::Scalar(255, 105, 180), // 热粉红色
    cv::Scalar(72, 209, 204),  // 适中的青色
    cv::Scalar(128, 0, 0),     // 栗色
    cv::Scalar(0, 255, 127),   // 春绿色
    cv::Scalar(210, 105, 30),  // 巧克力色
    cv::Scalar(139, 69, 19),   // 马鞍棕色
    cv::Scalar(255, 69, 0)     // 橙红色
};

std::shared_ptr<PCBPathPlanning::OutputParamsBase> PCBPathPlanning::PathPlan(const PCBPathPlanning::InputParamsBase &input)
{
    auto start = std::chrono::system_clock::now();
    fovid_ = 1; // FOV的id从1开始
    auto output_params = std::make_shared<OutputParams>();

    // 检查输入参数的合法性
    if (!ValidateInputParameters(input, output_params))
    {
        return output_params;
    }

    const InputParams *param = dynamic_cast<const InputParams *>(&input);
    output_params->result = true;

    //! 0、网格模式只拍图
    if (param->mode_position == PositionPlanMode::GRID)
    {
        HandleGridMode(*param, output_params);
        return output_params;
    }

    // ！ 1、分类矩形框，找出大矩形集合，小矩形集合
    std::vector<ObjectInput> big_rectangles, small_rectangles;
    ClassifyRectangles(param->rotate_rects, param->fov_w, param->fov_h, param->big_fov_ratio, big_rectangles, small_rectangles);
    if (big_rectangles.size() == 0 && small_rectangles.size() == 0)
    {
        return output_params;
    }

    //! 2、大矩形集合的FOV规划
    std::vector<Fov> big_fovs = PlanFovsForBigFov(big_rectangles, small_rectangles, *param);

    //! 3、小矩形集合的FOV规划
    bool result = true;
    std::vector<Fov> small_fovs = PlanFovsForSmallFov(small_rectangles, *param, result);
    output_params->result = result;

    //! 4、合并大矩形集合和小矩形集合
    output_params->fovs.insert(output_params->fovs.end(), big_fovs.begin(), big_fovs.end());
    output_params->fovs.insert(output_params->fovs.end(), small_fovs.begin(), small_fovs.end());

    //! 5、调整FOV中心点为里面所有矩形的最小外接矩形(只有不跨FOV的才调整)
    AdjustFOVCenter(output_params->fovs, *param);

    //! 6、路径排序
    if (param->optimal_path)
    {
        std::vector<Fov> fovs_copy = output_params->fovs;

        // 应用最优路径
        SortFovs(output_params->fovs, param->mode_path, GetBestPhotoPosition(fovs_copy, *param),param->axis_first, param->fov_h / 4);
    }
    else
    {
        SortFovs(output_params->fovs, param->mode_path, param->photo_start, param->axis_first, param->fov_h / 4);
    }
    auto milisec = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start).count();
    std::cout << "FOV个数:" << std::to_string(output_params->fovs.size()) << ",FOV规划耗时 = " << std::to_string(milisec) << std::endl;

    //! 7、绘制FOV
    if (param->draw_map)
    {
        DrawFovs(output_params->fovs, param);
    }

    return output_params;
}

bool PCBPathPlanning::IsCoveredByFov(const cv::RotatedRect &rect, const cv::Point2f &fovCenter, int fovW, int fovH)
{
    cv::Rect2f fov_rect(fovCenter.x - fovW / 2, fovCenter.y - fovH / 2, (float)fovW, (float)fovH);
    cv::Point2f vertices[4];
    rect.points(vertices);
    for (size_t i = 0; i < 4; i++)
    {
        if (!fov_rect.contains(vertices[i]))
        {
            return false;
        }
    }

    return true;
}

void PCBPathPlanning::HandleGridMode(const InputParams &params, std::shared_ptr<OutputParams> output_params)
{
    // 网格FOV规划
    output_params->fovs = PlanFovsForGrid(params);
    // 排序
    SortFovs(output_params->fovs, params.mode_path, params.photo_start, params.axis_first, 20);
    // 绘图
    if (params.draw_map)
    {
        DrawFovs(output_params->fovs, &params);
    }
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::PlanFovsForGrid(const InputParams &param)
{
    // 建构一个大的矩形框
    std::vector<ObjectInput> big_rectangle;
    std::vector<ObjectInput> small_rectangles;
    ObjectInput big;
    big.name = "big";
    cv::Point2f center(1.0f * param.region_w / 2, 1.0f * param.region_h / 2);
    big.rotate_rect = cv::RotatedRect(center, cv::Size2f(1.0f * param.region_w, 1.0f * param.region_h), 0);
    big_rectangle.push_back(big);
    return PlanFovsForBigFov(big_rectangle, small_rectangles, param);
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::PlanFovsForBigFov(const std::vector<ObjectInput> &big_rectangles, std::vector<ObjectInput> &small_rectangles, const InputParams& param)
{
    std::vector<Fov> big_fovs;
    if (param.fov_w <= 0 || param.fov_h <= 0 || param.region_w <= 0 || param.region_h <= 0)
    {
        return big_fovs;
    }

    for (const auto &rect : big_rectangles)
    {
        // 计算FOV的布局信息
        FovLayout layout = CalculateFovLayout(rect, param.fov_w, param.fov_h);

        // 找到覆盖最多小矩形的FOV中心点
        cv::Point2f best_center = FindBestFovCenter(rect, small_rectangles, layout);

        // 生成FOV并添加到结果中
        GenerateFovsForBigRectangle(rect, small_rectangles, param, layout, best_center, big_fovs);
    }
    return big_fovs;
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::PlanFovsForSmallFov(const std::vector<ObjectInput> &small_rectangles, const InputParams &param, bool &result)
{
    std::vector<Fov> fovs;
    auto rectangles_copy = small_rectangles; // 拷贝一份
    result = true;
    bool first_line = true;
    int offset = 10;                                                  // 视野偏移,确保能拍全
    float max_x = FindMostPoint(rectangles_copy, Direction::Right).x;  // 矩形集合里面最大的x
    float max_y = FindMostPoint(rectangles_copy, Direction::Bottom).y; // 矩形集合里面最大的y

    // 异常处理(max_expend为允许超出板子的最大范围)
    int max_expend = param.max_offset;
    if (max_x > param.region_w + max_expend || max_y > param.region_h + max_expend)
    {
        result = false;
        return fovs;
    }

    // mark点的fov规划
    if (param.is_mark)
    {
        return PlanFovsForMarkPoints(rectangles_copy, param);
    }

    const int max_iterations = 1000; // 最大迭代次数
    int iteration_count = 0;
    while (!rectangles_copy.empty() && iteration_count < max_iterations)
    {
        // 当前行第1个FOV中心点
        float currentX = FindMostPoint(rectangles_copy, Direction::Left).x - offset + param.fov_w / 2;
        float currentY = FindMostPoint(rectangles_copy, Direction::Top).y - offset + param.fov_h / 2;
        // FOV中心点异常处理
        if (currentX < -max_expend + param.fov_w / 2 || currentY < -max_expend + param.fov_h / 2)
        {
            result = false;
            fovs.clear();
            return fovs;
        }
        if (currentY > max_y - param.fov_h / 2 + max_expend)
        {
            // y超出最大则上移(最后一行会出现)
            currentY = max_y - param.fov_h / 2 + offset + max_expend;
        }

        // 找到当前行所有的矩形框cur_row_rectangles(矩形框最大的y应当小于currentY + param.fov_h / 2)
        std::vector<ObjectInput> cur_row_rectangles;
        std::vector<ObjectInput> remaining;
        for (const auto &obj : rectangles_copy)
        {
            // 计算旋转矩形最大的y(不考虑旋转角度)
            float max_rotated_y = obj.rotate_rect.center.y + obj.rotate_rect.size.height / 2;
            if (max_rotated_y < currentY + param.fov_h / 2)
            {
                cur_row_rectangles.push_back(obj);
            }
            else
            {
                remaining.push_back(obj);
            }
        }
        first_line = true;
        while (!cur_row_rectangles.empty())
        {
            if (first_line)
            {
                currentX = FindMostPoint(cur_row_rectangles, Direction::Left).x - offset + param.fov_w / 2;
                first_line = false;
            }
            // 当前FOV(currentY需要根据currentX再微调整，找到cur_row_rectangles里面x在currentX - fovW / 2 ~ currentX + fovW / 2范围矩形集合，找到这个集合里面最小的y)
            std::vector<ObjectInput> cur_fov_rectangles;
            for (const auto &obj : cur_row_rectangles)
            {
                if (obj.rotate_rect.center.x + obj.rotate_rect.size.width / 2 < currentX + param.fov_w / 2 && obj.rotate_rect.center.x - obj.rotate_rect.size.width / 2 > currentX - param.fov_w / 2)
                {
                    cur_fov_rectangles.push_back(obj);
                }
            }
            currentY = FindMostPoint(cur_fov_rectangles, Direction::Top).y - offset + param.fov_h / 2;
            if (currentY > max_y - param.fov_h / 2 + max_expend)
            {
                // y超出最大则上移(最后一行会出现)
                currentY = max_y - param.fov_h / 2 + offset + max_expend;
            }

            Fov cur_fov;
            if (currentX > max_x - param.fov_w / 2 + max_expend)
            {
                // x超出则左移(最后一列会出现)
                currentX = max_x - param.fov_w / 2 + offset + max_expend;
            }
            cur_fov.fov_path = FovPath(fovid_, cv::Point2f(currentX, currentY));
            std::vector<ObjectInput> remainingT;
            for (const auto &obj : cur_row_rectangles)
            {
                if (IsCoveredByFov(obj.rotate_rect, cv::Point2f(currentX, currentY), param.fov_w, param.fov_h))
                {
                    // 矩形框在当前FOV视野,将其添加到这个FOV
                    ObjectOutput cur;
                    cur.fovid = fovid_;
                    cur.name = obj.name;
                    cur.fovids.push_back(fovid_);
                    cur.rotate_rect = obj.rotate_rect;
                    cur_fov.covered_rectangles.push_back(cur);
                }
                else
                {
                    remainingT.push_back(obj);
                }
            }
            cur_row_rectangles = remainingT;

            /**需要再次微调FOV中心点, 使其尽可能也包围remaining里面的矩形*/

            // 1)根据当前FOV的最小外接矩形(不带角度)构建一个输入ObjectInput
            ObjectInput min_rect;
            min_rect.name = "min_rect";
            min_rect.rotate_rect = FindMinBoundingRect(cur_fov.covered_rectangles);
            auto layout = CalculateFovLayout(min_rect, param.fov_w, param.fov_h);

            // 2)找到覆盖最多小矩形的FOV中心点
            cur_fov.fov_path.center = FindBestFovCenter(min_rect, remaining, layout);

            // 再从remaining里面剔除当前FOV包含的矩形框
            for (auto it = remaining.begin(); it != remaining.end();)
            {
                if (IsCoveredByFov(it->rotate_rect, cur_fov.fov_path.center, param.fov_w, param.fov_h))
                {
                    ObjectOutput cur;
                    cur.fovid = fovid_;
                    cur.name = it->name;
                    cur.fovids.push_back(fovid_);
                    cur.rotate_rect = it->rotate_rect;
                    cur_fov.covered_rectangles.push_back(cur);
                    // 从remaining中移除当前对象
                    it = remaining.erase(it);
                }
                else
                {
                    it++;
                }
            }

            currentX = FindMostPoint(cur_row_rectangles, Direction::Left).x - offset + param.fov_w / 2;
            if (currentX > max_x - param.fov_w / 2 + max_expend)
            {
                // x超出则左移(最后一列会出现)
                currentX = max_x - param.fov_w / 2 + offset + max_expend;
            }
            if (cur_fov.covered_rectangles.size() > 0)
            {
                fovs.push_back(cur_fov);
                fovid_++;
            }
        }

        rectangles_copy = remaining;
        iteration_count++;
    }

    if (iteration_count >= max_iterations)
    {
        fovs.clear();
        result = false;
    }

    return fovs;
}

void PCBPathPlanning::AdjustFOVCenter(std::vector<Fov>& fovs, const InputParams& param)
{
    int max_expend = param.max_offset;
    for (size_t i = 0; i < fovs.size(); i++)
    {
        if (!fovs[i].have_big_rectangles && !fovs[i].covered_rectangles.empty())
        {
            auto& covered_rectangles = fovs[i].covered_rectangles;

            // 1. 收集所有矩形的顶点
            std::vector<cv::Point2f> all_points;
            for (const auto& obj : covered_rectangles)
            {
                cv::Point2f vertices[4];
                obj.rotate_rect.points(vertices);
                all_points.insert(all_points.end(), vertices, vertices + 4);
            }

            // 2. 计算轴对齐的最小外接矩形（AABB）
            if (!all_points.empty())
            {
                // 使用 cv::boundingRect（返回 cv::Rect，不带角度）
                cv::Rect aabb = cv::boundingRect(all_points);
                cv::Point2f new_center(aabb.x + aabb.width / 2.0f, aabb.y + aabb.height / 2.0f);

                // 确保中心点不会导致FOV超出板子区域(考虑最大外扩)
                new_center.x = std::clamp(new_center.x, param.fov_w / 2.0f - max_expend, param.region_w - param.fov_w / 2.0f + max_expend);
                new_center.y = std::clamp(new_center.y, param.fov_h / 2.0f - max_expend, param.region_h - param.fov_h / 2.0f + max_expend);

                // 更新FOV中心点
                fovs[i].fov_path.center = new_center;
            }
        }
    }
}
PCBPathPlanning::PhotoStartPosition PCBPathPlanning::GetBestPhotoPosition(std::vector<Fov>& fovs_copy, const InputParams& param)
{
    // key为FOV规划总距离,value为拍照起点位置
    std::vector<std::pair<double, PhotoStartPosition>> path_options;

    //! 左上
    SortFovs(fovs_copy, param.mode_path, PhotoStartPosition::LeftTop, param.axis_first, param.fov_h / 4);
    path_options.emplace_back(CalculateFOVPath(fovs_copy), PhotoStartPosition::LeftTop);

    //! 右上
    SortFovs(fovs_copy, param.mode_path, PhotoStartPosition::RightTop, param.axis_first, param.fov_h / 4);
    path_options.emplace_back(CalculateFOVPath(fovs_copy), PhotoStartPosition::RightTop);

    //! 左下
    SortFovs(fovs_copy, param.mode_path, PhotoStartPosition::LeftBottom, param.axis_first, param.fov_h / 4);
    path_options.emplace_back(CalculateFOVPath(fovs_copy), PhotoStartPosition::LeftBottom);

    //! 右下
    SortFovs(fovs_copy, param.mode_path, PhotoStartPosition::RightBottom, param.axis_first, param.fov_h / 4);
    path_options.emplace_back(CalculateFOVPath(fovs_copy), PhotoStartPosition::RightBottom);

    // 选择最短路径的起点位置
    auto best_option = std::min_element(path_options.begin(), path_options.end(),
        [](const auto& a, const auto& b) { return a.first < b.first; });
    return best_option->second;
}
bool PCBPathPlanning::ValidateInputParameters(const InputParamsBase &input, std::shared_ptr<OutputParams> output_params)
{
    const InputParams *params = dynamic_cast<const InputParams *>(&input);
    if (!params)
    {
        output_params->result = false;
        return false;
    }
    if (params->big_fov_ratio <= 0 || params->region_w <= 0 || params->region_h <= 0 || params->fov_w <= 0 || params->fov_h <= 0)
    {
        output_params->result = false;
        return false;
    }
    return true;
}

void PCBPathPlanning::ClassifyRectangles(const std::vector<ObjectInput> &rectangles, int fov_width, int fov_height, float big_fov_ratio, std::vector<ObjectInput> &big_rectangles, std::vector<ObjectInput> &small_rectangles)
{
    for (const auto &rect : rectangles)
    {
        if (rect.rotate_rect.size.width > 0 && rect.rotate_rect.size.height > 0)
        {
            if (rect.rotate_rect.size.width > fov_width * big_fov_ratio || rect.rotate_rect.size.height > fov_height * big_fov_ratio)
            {
                big_rectangles.push_back(rect);
            }
            else
            {
                small_rectangles.push_back(rect);
            }
        }
    }
}

PCBPathPlanning::FovLayout PCBPathPlanning::CalculateFovLayout(const ObjectInput &rect, int fov_width, int fov_height)
{
    const int overlap = 10; // 重叠量
    FovLayout layout;

    layout.num_fovs_x = static_cast<int>(std::ceil((rect.rotate_rect.size.width - overlap) / (fov_width - overlap)));
    layout.num_fovs_y = static_cast<int>(std::ceil((rect.rotate_rect.size.height - overlap) / (fov_height - overlap)));
    layout.max_fov_width = layout.num_fovs_x * fov_width - (layout.num_fovs_x - 1) * overlap;
    layout.max_fov_height = layout.num_fovs_y * fov_height - (layout.num_fovs_y - 1) * overlap;

    return layout;
}

cv::Point2f PCBPathPlanning::FindBestFovCenter(const ObjectInput &rect, const std::vector<ObjectInput> &small_rectangles, const FovLayout &layout)
{
    const int step = 100; // 步进量
    cv::Point2f best_center;
    int max_covered_count = 0;

    float max_x = rect.rotate_rect.center.x - rect.rotate_rect.size.width / 2 + layout.max_fov_width / 2;
    float min_x = rect.rotate_rect.center.x + rect.rotate_rect.size.width / 2 - layout.max_fov_width / 2;
    float max_y = rect.rotate_rect.center.y - rect.rotate_rect.size.height / 2 + layout.max_fov_height / 2;
    float min_y = rect.rotate_rect.center.y + rect.rotate_rect.size.height / 2 - layout.max_fov_height / 2;

    for (float x = min_x; x <= max_x; x += step)
    {
        for (float y = min_y; y <= max_y; y += step)
        {
            cv::Point2f center(x + 10, y + 10); // 偏移量
            int count = CountCoveredRectangles(small_rectangles, center, layout.max_fov_width, layout.max_fov_height);
            if (count > max_covered_count)
            {
                max_covered_count = count;
                best_center = center;
            }
        }
    }
    // 如果没有覆盖则使用当前大矩形的中心点
    if (max_covered_count == 0)
    {
        best_center = cv::Point2f(rect.rotate_rect.center.x, rect.rotate_rect.center.y);
    }

    return best_center;
}

int PCBPathPlanning::CountCoveredRectangles(const std::vector<ObjectInput> &rectangles, const cv::Point2f &center, int fov_width, int fov_height)
{
    int count = 0;
    for (const auto &rect : rectangles)
    {
        if (IsCoveredByFov(rect.rotate_rect, center, fov_width, fov_height))
        {
            count++;
        }
    }
    return count;
}

void PCBPathPlanning::GenerateFovsForBigRectangle(const ObjectInput &rect, std::vector<ObjectInput> &small_rectangles, const InputParams& param, const FovLayout &layout, const cv::Point2f &best_center, std::vector<Fov> &big_fovs)
{
    int max_expend = param.max_offset;
    const int overlap = 10; // 重叠量
    int fov_width = param.fov_w;
    int fov_height = param.fov_h;
    float start_x = best_center.x - layout.max_fov_width / 2 + fov_width / 2;
    float start_y = best_center.y - layout.max_fov_height / 2 + fov_height / 2;

    int start_fov_id = fovid_;
    for (int j = 0; j < layout.num_fovs_y; ++j)
    {
        for (int i = 0; i < layout.num_fovs_x; ++i)
        {
            // 计算当前FOV的中心点
            float center_x = start_x + i * (fov_width - overlap);
            float center_y = start_y + j * (fov_height - overlap);

            // 限制在板子区域(考虑最大外扩)
            center_x = std::clamp(center_x, fov_width / 2.0f - max_expend, param.region_w - fov_width / 2.0f + max_expend);
            center_y = std::clamp(center_y, fov_height / 2.0f - max_expend, param.region_h - fov_height / 2.0f + max_expend);
            cv::Point2f center(center_x, center_y);

            // 创建FOV
            Fov fov;
            fov.fov_path = FovPath(start_fov_id + i + j * layout.num_fovs_x, center);
            fov.have_big_rectangles = true;

            // 添加小矩形
            AddCoveredRectangles(fov, small_rectangles, fov_width, fov_height);

            // 添加大矩形
            AddBigRectangle(fov, rect, start_fov_id, layout.num_fovs_x * layout.num_fovs_y);

            // 将FOV添加到结果中
            big_fovs.push_back(fov);
            fovid_++;
        }
    }
}

void PCBPathPlanning::AddCoveredRectangles(Fov &fov, std::vector<ObjectInput> &small_rectangles, int fov_width, int fov_height)
{
    for (auto it = small_rectangles.begin(); it != small_rectangles.end();)
    {
        if (IsCoveredByFov(it->rotate_rect, fov.fov_path.center, fov_width, fov_height))
        {
            ObjectOutput out;
            out.fovid = fov.fov_path.fovid;
            out.name = it->name;
            out.rotate_rect = it->rotate_rect;
            out.fovids.push_back(fov.fov_path.fovid);
            fov.covered_rectangles.push_back(out);

            it = small_rectangles.erase(it);
        }
        else
        {
            it++;
        }
    }
}

void PCBPathPlanning::AddBigRectangle(Fov &fov, const ObjectInput &rect, int start_fov_id, int total_fovs)
{
    ObjectOutput out;
    out.fovid = fov.fov_path.fovid;
    out.name = rect.name;
    out.rotate_rect = rect.rotate_rect;
    for (int k = 0; k < total_fovs; k++)
    {
        out.fovids.push_back(start_fov_id + k);
    }
    fov.covered_rectangles.push_back(out);
}

void PCBPathPlanning::AdjustGroupOrder(std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first)
{
    if (axis_first == PhotoAxisFirst::XAxis)
    {
        if (photo_start == LeftBottom || photo_start == RightBottom)
        {
            std::reverse(grouped_fovs.begin(), grouped_fovs.end());
        }
    }
    else if (axis_first == PhotoAxisFirst::YAxis)
    {
        if (photo_start == RightTop || photo_start == RightBottom)
        {
            std::reverse(grouped_fovs.begin(), grouped_fovs.end());
        }
    }
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::SortFovsInSnakeMode(const std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first)
{
    std::vector<Fov> path;
    if (grouped_fovs.empty())
        return path;

    // 1. 根据起点确定首行方向
    bool current_direction;
    if (axis_first == XAxis)
    {
        current_direction = (photo_start == LeftTop || photo_start == LeftBottom);
    }
    else
    {
        current_direction = (photo_start == LeftTop || photo_start == RightTop);
    }

    cv::Point2f last_pos = grouped_fovs[0][0].fov_path.center; // 初始位置

    for (size_t i = 0; i < grouped_fovs.size(); ++i)
    {
        const auto &row = grouped_fovs[i];

        // 2. 从第二行开始动态决策方向
        if (i > 0)
        {
            double dist_forward = cv::norm(last_pos - row.front().fov_path.center);
            double dist_backward = cv::norm(last_pos - row.back().fov_path.center);
            current_direction = (dist_forward <= dist_backward);
        }

        // 3. 按当前方向排序
        auto sorted_row = row;
        if (axis_first == XAxis)
        {
            std::sort(sorted_row.begin(), sorted_row.end(),current_direction ? CompareFovByXAsc : CompareFovByXDesc);
        }
        else
        {
            std::sort(sorted_row.begin(), sorted_row.end(),current_direction ? CompareFovByYAsc : CompareFovByYDesc);
        }

        // 4. 更新路径
        path.insert(path.end(), sorted_row.begin(), sorted_row.end());
        last_pos = sorted_row.back().fov_path.center;
    }
    return path;
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::SortFovsInZMode(const std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first)
{
    std::vector<Fov> sorted_fovs;
    for (const auto &row : grouped_fovs)
    {
        std::vector<Fov> sorted_row = row;

        if (axis_first == PhotoAxisFirst::XAxis)
        {
            SortRowByX(sorted_row, true); // 默认从左到右排序
        }
        else if (axis_first == PhotoAxisFirst::YAxis)
        {
            SortRowByY(sorted_row, true); // 默认从上到下排序
        }

        // 根据起点位置决定是否反转行
        bool reverse_row = false;
        if (axis_first == PhotoAxisFirst::XAxis)
        {
            reverse_row = (photo_start == RightTop) || (photo_start == RightBottom);
        }
        else if (axis_first == PhotoAxisFirst::YAxis)
        {
            reverse_row = (photo_start == RightBottom) || (photo_start == LeftBottom);
        }

        if (reverse_row)
        {
            std::reverse(sorted_row.begin(), sorted_row.end());
        }

        sorted_fovs.insert(sorted_fovs.end(), sorted_row.begin(), sorted_row.end());
    }
    return sorted_fovs;
}

std::vector<PCBPathPlanning::Fov> PCBPathPlanning::PlanFovsForMarkPoints(const std::vector<ObjectInput> &rectangles, const InputParams &params)
{
    std::vector<Fov> fovs;
    const int offset = 10; // 视野偏移量

    for (const auto &rect : rectangles)
    {
        Fov fov = CreateFovForMarkPoint(rect, params, offset);
        fovs.push_back(fov);
        fovid_++;
    }

    return fovs;
}

PCBPathPlanning::Fov PCBPathPlanning::CreateFovForMarkPoint(const ObjectInput &rect, const InputParams &params, int offset)
{
    Fov fov;
    float center_x = rect.rotate_rect.center.x;
    float center_y = rect.rotate_rect.center.y;

    // 调整FOV中心点，确保不超出区域范围
    center_x = std::clamp(center_x, params.fov_w / 2.0f + offset, params.region_w - params.fov_w / 2.0f - offset);
    center_y = std::clamp(center_y, params.fov_h / 2.0f + offset, params.region_h - params.fov_h / 2.0f - offset);

    fov.fov_path = FovPath(fovid_, cv::Point2f(center_x, center_y));

    ObjectOutput obj_output;
    obj_output.fovid = fovid_;
    obj_output.name = rect.name;
    obj_output.fovids.push_back(fovid_);
    obj_output.rotate_rect = rect.rotate_rect;
    fov.covered_rectangles.push_back(obj_output);

    return fov;
}

std::pair<std::vector<PCBPathPlanning::ObjectInput>, std::vector<PCBPathPlanning::ObjectInput>> PCBPathPlanning::FindCoveredRectangles(const std::vector<ObjectInput> &rectangles, float current_x, float current_y, int fov_width, int fov_height)
{
    std::vector<ObjectInput> covered_rects;
    std::vector<ObjectInput> uncovered_rects;

    for (const auto &rect : rectangles)
    {
        if (IsCoveredByFov(rect.rotate_rect, cv::Point2f(current_x, current_y), fov_width, fov_height))
        {
            covered_rects.push_back(rect);
        }
        else
        {
            uncovered_rects.push_back(rect);
        }
    }

    return {covered_rects, uncovered_rects};
}

PCBPathPlanning::Fov PCBPathPlanning::CreateFovForCurrentRow(const std::vector<ObjectInput> &covered_rects, float current_x, float current_y, const InputParams &params)
{
    Fov fov;
    fov.fov_path = FovPath(fovid_, cv::Point2f(current_x, current_y));
    (void)params;

    for (const auto &rect : covered_rects)
    {
        ObjectOutput obj_output;
        obj_output.fovid = fovid_;
        obj_output.name = rect.name;
        obj_output.fovids.push_back(fovid_);
        obj_output.rotate_rect = rect.rotate_rect;
        fov.covered_rectangles.push_back(obj_output);
    }

    return fov;
}

void PCBPathPlanning::RemoveCoveredRectanglesFromRemaining(const std::vector<ObjectInput> &covered_rects, std::vector<ObjectInput> &remaining)
{
    for (const auto &rect : covered_rects)
    {
        auto it = std::find(remaining.begin(), remaining.end(), rect);
        if (it != remaining.end())
        {
            remaining.erase(it);
        }
    }
}

float PCBPathPlanning::GetMaxYOfRotatedRect(const cv::RotatedRect &rect)
{
    cv::Point2f vertices[4];
    rect.points(vertices);
    float max_y = vertices[0].y;
    for (int i = 1; i < 4; ++i)
    {
        max_y = std::max(max_y, vertices[i].y);
    }
    return max_y;
}

cv::Point2f PCBPathPlanning::FindMostPoint(const std::vector<ObjectInput> &rects, Direction direction)
{
    if (rects.empty())
    {
        return cv::Point2f(0, 0);
    }
    auto most = rects[0].rotate_rect.center;
    float mostX = 0;
    float mostY = 0;
    switch (direction)
    {
    case Direction::Left:
    {
        mostX = most.x - rects[0].rotate_rect.size.width / 2;
    }
    break;
    case Direction::Right:
    {
        mostX = most.x + rects[0].rotate_rect.size.width / 2;
    }
    break;
    case Direction::Top:
    {
        mostY = most.y - rects[0].rotate_rect.size.height / 2;
    }
    break;
    case Direction::Bottom:
    {
        mostY = most.y + rects[0].rotate_rect.size.height / 2;
    }
    break;
    }
    ObjectInput result;
    for (const auto &rect : rects)
    {
        switch (direction)
        {
        case Direction::Left:
        {
            float temp = rect.rotate_rect.center.x - rect.rotate_rect.size.width / 2;
            if (temp <= mostX)
            {
                mostX = temp;
                result = rect;
            }
        }
        break;
        case Direction::Right:
        {
            float temp = rect.rotate_rect.center.x + rect.rotate_rect.size.width / 2;
            if (temp >= mostX)
            {
                mostX = temp;
                result = rect;
            }
        }
        break;
        case Direction::Top:
        {
            float temp = rect.rotate_rect.center.y - rect.rotate_rect.size.height / 2;
            if (temp <= mostY)
            {
                mostY = temp;
                result = rect;
            }
        }
        break;
        case Direction::Bottom:
        {
            float temp = rect.rotate_rect.center.y + rect.rotate_rect.size.height / 2;
            if (temp >= mostY)
            {
                mostY = temp;
                result = rect;
            }
        }
        break;
        default:
            break;
        }
    }

    return cv::Point2f(mostX, mostY);
}

cv::RotatedRect PCBPathPlanning::FindMinBoundingRect(const std::vector<ObjectInput> &rects)
{
    if (rects.empty())
    {
        return cv::RotatedRect();
    }
    std::vector<cv::Point2f> all_points;
    for (const auto& obj : rects)
    {
        cv::Point2f vertices[4];
        obj.rotate_rect.points(vertices);
        all_points.insert(all_points.end(), vertices, vertices + 4);
    }

    // 使用 cv::boundingRect（返回 cv::Rect，不带角度）
    cv::Rect aabb = cv::boundingRect(all_points);

    // 获取Rect的中心点
    cv::Point2f center(aabb.x + aabb.width * 0.5f, aabb.y + aabb.height * 0.5f);

    // 创建RotatedRect，角度设为0
    return cv::RotatedRect(center, cv::Size2f(aabb.width * 1.0f, aabb.height * 1.0f), 0.0f);
}

cv::RotatedRect PCBPathPlanning::FindMinBoundingRect(const std::vector<ObjectOutput>& rects)
{
    if (rects.empty())
    {
        return cv::RotatedRect();
    }
    std::vector<cv::Point2f> all_points;
    for (const auto& obj : rects)
    {
        cv::Point2f vertices[4];
        obj.rotate_rect.points(vertices);
        all_points.insert(all_points.end(), vertices, vertices + 4);
    }

    // 使用 cv::boundingRect（返回 cv::Rect，不带角度）
    cv::Rect aabb = cv::boundingRect(all_points);

    // 获取Rect的中心点
    cv::Point2f center(aabb.x + aabb.width * 0.5f, aabb.y + aabb.height * 0.5f);

    // 创建RotatedRect，角度设为0
    return cv::RotatedRect(center, cv::Size2f(aabb.width * 1.0f, aabb.height * 1.0f), 0.0f);
}

void PCBPathPlanning::SortFovs(std::vector<Fov>& fovs, PathPlanMode mode,PhotoStartPosition photo_start,PhotoAxisFirst axis_first, int diff) 
{
    if (fovs.empty()) return;

    //! 1、Z形排序
    if (mode == Z_MODE) 
    {
        auto grouped_fovs = GroupFovsByRows(fovs, diff, axis_first);
        AdjustGroupOrder(grouped_fovs, photo_start, axis_first);
        fovs = SortFovsInZMode(grouped_fovs, photo_start, axis_first);
        return;
    }

    //! 2、S型排序
    std::vector<Fov> sorted_fovs;
    std::vector<bool> visited(fovs.size(), false);
    bool primary_x = (axis_first == XAxis);

    // 找到FOV起始点
    size_t start_idx = FindStartFovIndex(fovs, photo_start, axis_first, diff);
    sorted_fovs.push_back(fovs[start_idx]);
    visited[start_idx] = true;

    // 主循环
    while (sorted_fovs.size() < fovs.size()) 
    {
        Fov& last = sorted_fovs.back();
        size_t next_idx = FindNextFovIndex(fovs, visited, last, primary_x);

        if (next_idx == fovs.size())
        {
            // 回退机制：选择任意未访问点
            for (size_t i = 0; i < visited.size(); i++) 
            {
                if (!visited[i]) {
                    next_idx = i;
                    break;
                }
            }
            if (next_idx == fovs.size()) break;
        }

        sorted_fovs.push_back(fovs[next_idx]);
        visited[next_idx] = true;
    }

    fovs = sorted_fovs;
}

size_t PCBPathPlanning::FindStartFovIndex(const std::vector<Fov>& fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first, int diff)
{
    if (fovs.empty()) return 0;

    // 第一步：按主轴方向分组
    auto grouped_fovs = GroupFovsByRows(fovs, diff, axis_first);
    if (grouped_fovs.empty()) return 0;

    // 第二步：确定目标行
    size_t target_row = 0;
    if (axis_first == PhotoAxisFirst::XAxis)
    {
        if (photo_start == LeftBottom || photo_start == RightBottom)
        {
            target_row = grouped_fovs.size() - 1; // 最后一行
        }
    }
    else if (axis_first == PhotoAxisFirst::YAxis)
    {
        if (photo_start == RightTop || photo_start == RightBottom)
        {
            target_row = grouped_fovs.size() - 1; // 最后一行
        }
    }

    // 第三步：在目标行中确定目标FOV
    if (grouped_fovs[target_row].empty()) return 0;

    // 先根据主轴方向对目标行进行排序
    if (axis_first == PhotoAxisFirst::XAxis)
    {
        std::sort(grouped_fovs[target_row].begin(), grouped_fovs[target_row].end(), CompareFovByXAsc);
    }
    else if (axis_first == PhotoAxisFirst::YAxis)
    {
        std::sort(grouped_fovs[target_row].begin(), grouped_fovs[target_row].end(), CompareFovByYAsc);
    }

    size_t target_fov = 0;
    if (photo_start == RightTop || photo_start == RightBottom)
    {
        target_fov = grouped_fovs[target_row].size() - 1; // 行尾
    }

    // 第四步：返回原始fovs中的索引
    int target_id = grouped_fovs[target_row][target_fov].fov_path.fovid;
    for (size_t i = 0; i < fovs.size(); ++i)
    {
        if (fovs[i].fov_path.fovid == target_id)
        {
            return i;
        }
    }

    return 0; // 默认返回第一个
}

size_t PCBPathPlanning::FindNextFovIndex(const std::vector<Fov>& fovs,const std::vector<bool>& visited,const Fov& last, bool primary_x) 
{
    size_t best_idx = fovs.size();
    float best_distance = std::numeric_limits<float>::max();
    const cv::Point2f& last_center = last.fov_path.center;

    // 计算主轴和次轴方向的权重
    const float primary_weight = 0.3f;
    const float secondary_weight = 1.0f;

    for (size_t i = 0; i < fovs.size(); i++) 
    {
        if (visited[i]) continue;

        const cv::Point2f& current_center = fovs[i].fov_path.center;
        float dx = current_center.x - last_center.x;
        float dy = current_center.y - last_center.y;

        // 加权距离计算，优先考虑主轴方向
        float distance = primary_x
            ? primary_weight * std::abs(dx) + secondary_weight * std::abs(dy)
            : primary_weight * std::abs(dy) + secondary_weight * std::abs(dx);

        // 考虑移动方向 - 倾向于保持当前主轴方向
        bool same_direction = primary_x
            ? (dx * (current_center.x - last_center.x) >= 0)
            : (dy * (current_center.y - last_center.y) >= 0);

        if (same_direction) 
        {
            distance *= 0.8f; // 同方向奖励
        }

        if (distance < best_distance) 
        {
            best_distance = distance;
            best_idx = i;
        }
    }

    return best_idx;
}

void PCBPathPlanning::DrawFovs(std::vector<Fov> &fovs, const InputParams *param)
{
    // 创建一个空白图像，用于绘制
    cv::Mat image = cv::Mat::zeros(param->region_h, param->region_w, CV_8UC3);

    // 遍历每个 FOV
    int index = 0;
    cv::Point2f last_center;
    std::string fov_list = "FOV Move:";
    for (const auto &fov : fovs)
    {
        if (index == vecColor.size() - 1)
        {
            index = 0;
        }
        // 计算 FOV 的左上角坐标
        cv::Point2f center = fov.fov_path.center;
        int topLeftX = static_cast<int>(center.x - param->fov_w / 2);
        int topLeftY = static_cast<int>(center.y - param->fov_h / 2);

        // 绘制 FOV 矩形框
        cv::rectangle(image,
                      cv::Point(topLeftX, topLeftY),
                      cv::Point(topLeftX + param->fov_w, topLeftY + param->fov_h),
                      vecColor[index],
                      50); // 边框宽度
        // 绘制FOV中心
        cv::circle(image, center, 50, vecColor[index], 20);
        cv::putText(image, std::to_string(fov.fov_path.fovid), center, cv::FONT_HERSHEY_SIMPLEX, 10.0, vecColor[index], 6);
        // 绘制FOV轨迹
        if (index > 0)
        {
            cv::line(image, last_center, center, cv::Scalar(255, 255, 255), 10);
        }
        last_center = center;
        index++;
        fov_list += std::to_string(fov.fov_path.fovid) + "->";

        // 遍历 FOV 中的每个元件
        for (const auto &obj : param->rotate_rects)
        {
            // 假设元件的旋转矩形已被转换为轴对齐的边界框
            cv::RotatedRect rotatedRect = obj.rotate_rect;
            // 填充元件矩形框，蓝色填充
            cv::rectangle(image,
                          cv::Point2f(rotatedRect.center.x - rotatedRect.size.width / 2, rotatedRect.center.y - rotatedRect.size.height / 2),
                          cv::Point2f(rotatedRect.center.x + rotatedRect.size.width / 2, rotatedRect.center.y + rotatedRect.size.height / 2),
                          cv::Scalar(255, 255, 255), // 白色
                          2);
        }
    }
    cv::putText(image, fov_list, cv::Point(100, param->region_h - 200), cv::FONT_HERSHEY_SIMPLEX, 5.0, cv::Scalar(255, 255, 255), 6);

    // 保存图像到指定路径
    // ResizeImagePreserveAspectRatio(image, image, 5000, 5000);
    cv::imwrite(param->map_path, image);
}

void PCBPathPlanning::ResizeImagePreserveAspectRatio(const cv::Mat &src, cv::Mat &dst, int width, int height)
{
    // 获取图像的宽度和高度
    int imageWidth = src.cols;
    int imageHeight = src.rows;

    // 判断输入图像是否需要缩放
    if (imageWidth <= width && imageHeight <= height)
    {
        dst = src.clone();
        return; // 图像宽高都小于区域大小，无需缩放
    }

    // 计算宽度和高度的缩放比例
    float scaleWidth = static_cast<float>(width) / imageWidth;
    float scaleHeight = static_cast<float>(height) / imageHeight;

    // 选择较小的缩放比例作为最终的缩放比例，以保持图像的长宽比不变
    float scaleFactor = std::min(scaleWidth, scaleHeight);

    cv::resize(src, dst, {}, scaleFactor, scaleFactor);
}

void PCBPathPlanning::SortRowByX(std::vector<Fov> &row, bool ascending)
{
    std::sort(row.begin(), row.end(), ascending ? CompareFovByXAsc : CompareFovByXDesc);
}

void PCBPathPlanning::SortRowByY(std::vector<Fov> &row, bool ascending)
{
    std::sort(row.begin(), row.end(), ascending ? CompareFovByYAsc : CompareFovByYDesc);
}

double PCBPathPlanning::CalculateFOVPath(std::vector<Fov>& fovs)
{
    if (fovs.empty() || fovs.size() == 1) 
    {
        return 0.0f;  // 空路径或单点路径距离为0
    }

    double total_distance = 0.0f;
    cv::Point2f prev_pos = fovs[0].fov_path.center;

    // 计算相邻FOV中心点的累计距离
    for (size_t i = 1; i < fovs.size(); ++i) 
    {
        const cv::Point2f& current_pos = fovs[i].fov_path.center;
        total_distance += cv::norm(current_pos - prev_pos);
        prev_pos = current_pos;
    }

    return total_distance;
}

std::vector<std::vector<PCBPathPlanning::Fov>> PCBPathPlanning::GroupFovsByRows(const std::vector<Fov> &fovs, int d, PhotoAxisFirst axis_first)
{
    std::vector<std::vector<Fov>> rows;
    if (fovs.empty())
        return rows;

    // 根据轴优先级进行排序
    std::vector<Fov> sortedFovs = fovs;
    if (axis_first == PhotoAxisFirst::YAxis)
    {
        std::sort(sortedFovs.begin(), sortedFovs.end(), CompareFovByXAsc);
    }
    else if (axis_first == PhotoAxisFirst::XAxis)
    {
        std::sort(sortedFovs.begin(), sortedFovs.end(), CompareFovByYAsc);
    }
    else
    {
        // 暂时写死，只能按照X轴/Y轴排序
        return rows;
    }

    std::vector<Fov> currentRow;
    float currentSecondaryValue = axis_first == PhotoAxisFirst::YAxis
                                      ? sortedFovs[0].fov_path.center.x
                                      : sortedFovs[0].fov_path.center.y;

    for (const auto &fov : sortedFovs)
    {
        float diff = axis_first == PhotoAxisFirst::YAxis
                         ? std::abs(fov.fov_path.center.x - currentSecondaryValue)
                         : std::abs(fov.fov_path.center.y - currentSecondaryValue);

        if (diff <= d)
        {
            currentRow.push_back(fov);
        }
        else
        {
            rows.push_back(currentRow);
            currentRow.clear();
            currentRow.push_back(fov);
            currentSecondaryValue = axis_first == PhotoAxisFirst::YAxis
                                        ? fov.fov_path.center.x
                                        : fov.fov_path.center.y;
        }
    }
    if (!currentRow.empty())
    {
        rows.push_back(currentRow);
    }
    return rows;
}