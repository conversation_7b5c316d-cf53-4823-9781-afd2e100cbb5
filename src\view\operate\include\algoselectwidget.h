/*********************************************************************
 * @brief  检测算法选择控件,进行检测算法的选择及缺陷编辑.
 *
 * @file   algoselectwidget.h
 *
 * @date   2024.09.18
 * <AUTHOR>
 *********************************************************************/
#ifndef ALGOSELECTWIDGET_H
#define ALGOSELECTWIDGET_H
#include <QFrame>
#include <QSpinBox>

//#include "imagetabledelegate.h"

class QLabel;
class QComboBox;
class QCheckBox;
class QPushButton;

/**
 * @brief  检测框创建参数.
 *
 * @date   2024.09.24
 * <AUTHOR>
 */
struct DetectWindowItemValue
{
    QString model_name = "";         ///< 检测框所属检测类型名称
    QString window_name = "";        ///< 检测框名称
    QString parent_window_name = ""; ///< 上级检测框名称
    QString group_name = "";         ///< 组名
    QString defect_type_name = "";   ///< 当前选择缺陷
    QString algo_name = "";          ///< 当前选择算法
    bool    is_enable = true;        ///< 是否启用
    int     excute_state = 0;        ///< 执行状态  
    float   serach_size = 0.0f;     ///< 外扩区域比例
};


/**
 * @note   继承QWidget无法使stylesheet生效,改成QFrame就可以,非常奇怪
 *
 * @date   2024.09.23
 * <AUTHOR>
 */
class AlgoSelectWidget : public QFrame
{
    Q_OBJECT

public:
    struct AlgoName
    {
        std::string code_name;    // 代码层面的名称
        std::string display_name; // 界面显示名称
        AlgoName(const std::string& _code_name, const std::string& _display_name) 
            : code_name(_code_name), display_name(_display_name) {}
    };

    AlgoSelectWidget(QWidget* parent = nullptr, std::vector<AlgoName> algo_list = {}, std::vector<std::string> error_list = {});
    void SetValue(const DetectWindowItemValue& value);
    void SetName(const QString& item);
    void SetSerachSize(float size);
    void SetParentWinName(const QString& item);
    void SetGroup(const QString& item);
    void SetDefectName(const QString& item);
    void SetAlgoName(const QString& item);
    void SetEnable(bool state);
    void UpdateCurValueFromUi();
    void UndoChange();
    void GetCurValueFromUi(DetectWindowItemValue& value);
    void GetCurValueBeforChange(DetectWindowItemValue& value);
    void UpdateParentWinIndexMap(const std::map<std::string, int>& index_map);
    void SetEditEnable(bool state);

signals:
    void SignalValueChanged(DetectWindowItemValue& value);
    void SignalDelete(const QString& window_name);

private:
    //ImageTableDelegate* item_delegate = nullptr;
    QLabel*    label_index = nullptr;
    QLabel*    label_algo_name = nullptr;
    QComboBox* combo_defect_name = nullptr;
    QCheckBox* check_enable = nullptr;
    QLabel*    label_group  = nullptr;
    QComboBox* comb_parant_index = nullptr;
    QDoubleSpinBox *  spin_box_padding_size = nullptr;

    QString name;
    QString algo_name;  
    QString group_name;
    QString defect_type_name;
    QString parent_win_name;
    bool    enable = true;
    float   serach_size = 0.0f;
    std::vector<AlgoName> algo_name_list;
    bool    edit_enable = false;
    
};
#endif // ALGOSELECTWIDGET_H