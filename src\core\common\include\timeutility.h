﻿/*********************************************************************
 * @brief  常用时间函数.
 *
 * @file   timeutility.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef JTOOLS_TIMEUTILITY_H
#define JTOOLS_TIMEUTILITY_H
#include "jtoolsapi.hpp"
#include "coreapplication.h"
#include <chrono>
#include <ctime>
#include <string>
#include <iostream>
#include <iomanip>

namespace jtools
{
    /** @brief  计时器 ，用于计算代码段执行时间*/
    /** @details
     *  1. 使用方法：
     *      {
     *          ScopedTimer timer("label");
     *          // 代码段
     *      }
     *  2. 析构时自动输出耗时信息
     *  3. 可选择是否自动打印和是否记录日志
     */
    class ScopedTimer
    {
        public:
            ScopedTimer(const std::string& label, bool is_log_= false, bool auto_print = true)
                : m_label(label), m_start(Micros()), m_auto_print(auto_print),is_log(is_log_)
                {
                }

            ~ScopedTimer()
            {
                uint64_t elapsed = Micros() - m_start;
                std::string log = m_label + "耗时: ";
                if (elapsed < 1000)
                    log += std::to_string(elapsed) + "us.";
                else
                    log += std::to_string((double)elapsed / 1000.0) + "ms.";
                //!结合成一个完整的字符串
                if (is_log)
                {
                    
                    LogTo_INFO("AlgoTime", log);
                }

                if (m_auto_print)
                {
                    std::cout<<log<<std::endl;
                }
            }

        private:
            std::string m_label;
            uint64_t m_start;
            bool m_auto_print;
            bool is_log;

            static inline uint64_t Micros()
            {
                return static_cast<uint64_t>(
                    std::chrono::duration_cast<std::chrono::microseconds>(
                        std::chrono::high_resolution_clock::now().time_since_epoch()
                    ).count()
                    );
            }
    };
    class JT_EXPORTS TimeUtility
    {
    public:
        using ClockType = std::chrono::system_clock; // 系统时钟
        // using ClockType = std::chrono::high_resolution_clock; // 高精度时钟

        /**
         * @brief  获取当前时间戳,精度秒级.
         *
         * @fun    GetCurrentTimeStamp
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::time_t GetCurrentTimeStamp();
        /**
         * @brief  获取当前时间点,精度更高.
         *
         * @fun    GetCurrentTimePoint
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::chrono::high_resolution_clock::time_point GetCurrentTimePoint();
        /**
         * @brief  获取当前时间的格式化字符串.
         *
         * @fun    GetCurrentTimeString
         * @param  format
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::string GetCurrentTimeString(const char *format = "%Y-%m-%d %H:%M:%S");
        /**
         * @brief  获取当前时间的格式化字符串,追加微秒.
         *
         * @fun    GetCurrentTimeString
         * @param  format
         * @return 微秒以(.%06ld)的格式追加到格式化字符串的后面
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::string GetCurrentTimeStringWithUS(const char *format = "%Y-%m-%d %H:%M:%S");
        /**
         * @brief  时间点转时间戳.
         *
         * @fun    TimePointToTimeStamp
         * @param  timePoint
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::time_t TimePointToTimeStamp(ClockType::time_point timePoint);
        /**
         * @brief  时间戳转时间点.
         *
         * @fun    TimeStampToTimePoint
         * @param  timeStamp
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static ClockType::time_point TimeStampToTimePoint(std::time_t timeStamp);
        /**
         * @brief  时间戳格式化.
         *
         * @fun    TimeStampToString
         * @param  timeStamp
         * @param  format
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::string TimeStampToString(std::time_t timeStamp, const char *format = "%Y-%m-%d %H:%M:%S");
        /**
         * @brief  时间点格式化.
         *
         * @fun    TimePointToString
         * @param  timePoint
         * @param  format
         * @return
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static std::string TimePointToString(ClockType::time_point timePoint, const char *format = "%Y-%m-%d %H:%M:%S");

        /**
         * @brief  获取两个时间点之间的时间差.
         *
         * @fun    GetTimeDiff
         * @param  start
         * @param  end
         * @param  unit 时间差的单位，可以是 "seconds", "milliseconds", "microseconds", "nanoseconds", "minutes", "hours"
         * @return 时间差,单位由unit决定,无效的unit返回0
         *
         * @date   2024.08.05
         * <AUTHOR>
         */
        static double GetTimeDiff(const std::chrono::high_resolution_clock::time_point &start,
                                  const std::chrono::high_resolution_clock::time_point &end,
                                  const std::string &unit);

        // 时区转换
        static std::string TimeZoneConvert(std::string timeStr, std::string fromZone, std::string toZone);
        // 获取当前时区
        static std::string GetCurrentTimeZone();

        // 使当前线程休眠
        static void Sleep(std::chrono::milliseconds ms);
    };
}

#endif // JTOOLS_TIMEUTILITY_H
