﻿/*****************************************************************//**
 * @file   editdetectmodelview.h
 * @brief  检测界面
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef EDITDETECTMODELVIEW_H
#define EDITDETECTMODELVIEW_H
 // prebuild
#include "pch.h"
//QT
#include <QWidget>
#include <QButtonGroup>
//CUSTOM
//#include "viewparam.hpp"
#include "dlgcreatedetectwindow.h"
#include "algoselectwidget.h"
#include "componentlibview.h"
#include "multidetectwindow.h"
#include "algospecparam.h"
// ThirdParty
#include "nlohmann/json.hpp"
using JSON = nlohmann::json;

// 这个不应该放在.h文件里 jerx 
class ColorModule;
class DetectModelWidget;

namespace Ui {
    class EditDetectModelView;
}
namespace jrsoperator
{
    class OcvOperatorView;
    class OcvOperatorParam;
}
namespace jrsaoi
{
    class TemplateView;
}
/**
 * @struct DetWinParam
 * @brief  检测框参数
 * @note   临时放在这里,需要移动到其他地方
 */
struct DetWinParam
{
    std::string window_name;
    std::vector<std::string> sub_window_name;
};

enum UpdateSpecParamType
{
    STD_TYPE = 0,   /* 规格的标准类型*/
    OFFSET_TYPE     /* 规格的偏移类型*/
};


struct EditAlgorithmViewDefaultParam
{
    std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_name_list;
    std::map<std::string, std::map<std::string, std::vector<std::string>>> algo_spec_param_map;
    std::vector<std::string> defect_list;
    std::map<std::string, QWidget*> algo_view_list;
    // 相机解析度
    float resolution_x = 0.0f;
    float resolution_y = 0.0f;
};

class EditDetectModelView : public QWidget
{
    Q_OBJECT
public:
    explicit EditDetectModelView(QWidget* parent = nullptr);
    ~EditDetectModelView();
public:
    /**
     * @fun SlotUpdate
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SlotUpdate(jrsdata::ViewParamBasePtr param);

    /**
     * @fun  SlotCreateAlgoView
     * @brief 创建算法编辑界面.
     * @param widget 算法编辑控件
     * @data 2024.10.21
     * <AUTHOR>
     */
    void UpdateAlgoView(jrsdata::ViewParamBasePtr param);

    void ShowTemplateView();

    int SetEditAlgorithmViewDefaultParam(const EditAlgorithmViewDefaultParam& _param);

    int SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list);
    QWidget* GetTemplateView() { return (QWidget*)(template_view); }
signals:
    /**
     * @fun SigEditAlgoUpdate
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigEditAlgoUpdate(jrsdata::ViewParamBasePtr param);

    /**
     * @fun SigUpdateOperator
     * @brief 通用对外信号
     * @date 2025.3.18
     * <AUTHOR>
     */
    void SigUpdateOperator(const jrsdata::ViewParamBasePtr& param_);

protected slots:
    void SlotCreateRegion();
    void SlotDeleteCurSelectedDetectWindow();
    void SlotBindLogicalOR();
    void SlotUnbindLogical();
    /**
     * @fun SlotApplySpecParam 
     * @brief 应用规格参数
     * <AUTHOR>
     * @date 2025.5.14
     */
    void SlotApplySpecParam();

    /**
     * @fun SlotApplyStdValueParam
     * @brief 应用分母参数
     * <AUTHOR>
     * @date 2025.5.19
     */
    void SlotApplyStdValueParam();

private:
    /**
     * @fun init
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void Init();
    /**
     * @fun ConnectSlots
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void ConnectSlots();

    void DisconnectSlots();
    /**
     * @fun RequestDrawDetectWindow
     * @brief 请求绘制检测框
     * @date 2024.9.24
     * <AUTHOR>
     */
    void RequestDrawDetectWindow();
    /**
     * @fun RequestDrawSubRegion
     * @brief 请求绘制子检测框
     * @date 2024.9.24
     * <AUTHOR>
     */
    void RequestDrawSubRegion();

    /**
     * @fun AddTemplate
     * @brief 添加模板算法,临时 //TODO 删除
     * @date 2024.9.24
     * <AUTHOR>
     */
    void AddTemplate();


    /**
     * @fun  ClearAlgoView
     * @brief 清除算法界面 .
     * @data 2024.11.07
     * <AUTHOR>
     */
    void ClearAlgoView();

    // 获取元件库地址
    std::string GetComponentPath();

    // 获取元件库分类选择的名称
    std::string GetComponentSelectName();

    // 更新元件库分类选择的名称
    void UpdateComponentSelectName(std::string name);

    // 获取自动加框的算法
    std::string GetMultiAlgoList();

    // 保存自动加框的算法
    void SetMultiAlgoList(std::string algo);

    // 获取算法比例参数
    std::string GetAlgoRatioList();

    // 保存算法比例参数
    void SetAlgoRatioList(std::string algo_ration);

    // 设置算法规格值和分母(type = 0表示设置规格,type = 1表示设置分母)
    void FastUpdateSpecParam(std::string current_spec_str, UpdateSpecParamType type);

    const std::unordered_map<jrsdata::ComponentUnit::Type, QString> TypeToQStringMap = {
    {jrsdata::ComponentUnit::Type::BODY,"body"},
    {jrsdata::ComponentUnit::Type::PAD, "pad"},
    {jrsdata::ComponentUnit::Type::HOLE, "hole"},
    };

    /**
     * @fun TypeToQString
     * @brief 将算法组类型转换成字符串
     * @param type [IN] 算法组类型
     * @return  返回对应的字符串
     * <AUTHOR>
     * @date 2025.3.18
     */
    inline QString TypeToQString(jrsdata::ComponentUnit::Type type)
    {
        auto it = TypeToQStringMap.find(type);
        return (it != TypeToQStringMap.end()) ? it->second : "UNKNOWN";
    };

    /**
     * @fun GetComponentUnitType
     * @brief 根据元件算法组名称获取元件组类型
     * @param component_group_name
     * @return
     * <AUTHOR>
     * @date 2025.3.18
     */
    jrsdata::ComponentUnit::Type GetComponentUnitType(const std::string& component_group_name, const jrsdata::PNDetectInfo* part_number_info_);

    /**
     * @fun SetDetectWindowListState
     * @brief 设置当前选中算法检测框列表结果状态
     * <AUTHOR>
     * @date 2025.3.30
     */
    void SetDetectWindowListState(jrsdata::ViewParamBasePtr update_param_);

    /**
     * @fun RefereshAlgoList 
     * @brief 刷新算法列表
     * @param  param_ [IN] 算法参数信息
     * <AUTHOR>
     * @date 2025.4.24
     */
    void RefereshAlgoList(const jrsdata::AlgoEventParamPtr& param_ );
private:
    Ui::EditDetectModelView* ui;
    DetectModelWidget*       det_model_tab = nullptr;       ///< 检测框列表管理界面
    jrsaoi::TemplateView*    template_view = nullptr;       ///< 模板界面
    DlgCreateDetectWindow*   dlg_create_detect_window = nullptr;   ///< 创建检测框窗口
    ComponentLibView*       component_view = nullptr;           ///< 历史元件库操作界面
    QDialog*                dialog = nullptr;                   ///< 历史元件库操作界面弹框
    MultiDetectWindow* multi_detect_window = nullptr;           ///< 多个检测框弹框
    AlgoSpecParam* algo_spec = nullptr;                         ///< 参数管控弹框
    jrsdata::OperateViewParamPtr view_param;

    std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_name_list;
    std::map<std::string/*算法名称*/, std::map<std::string/*算法中规格名称*/, std::vector<std::string>/*软件中规格名称*/>> algo_spec_param_map; /**< 算法规格名称和软件中规格管控的参数名称映射表*/

    float resolution_x = 0.0f;
    float resolution_y = 0.0f;

    std::vector<std::string> defect_list;
    std::map<std::string, QWidget*> algo_view_list;

    std::string cur_detect_window_name = "";
    std::string cur_component_name = "";
    std::string cur_component_part_num = "";
    std::string current_detect_model_name{};
    DetectWindowItemValue DetectWindow2DetectWindowItemValue(const jrsdata::DetectWindow& detect);
    jrsdata::DetectWindow DetectWindowItemValue2DetectWindow(const DetectWindowItemValue& detect_item);

    jrsdata::DetectWindow cur_select_detect_win;   ///< 当前选中的检测框

    std::optional<std::string> FindAlgoChineseName(const std::string& group_name, const std::string& en_name);


    std::optional<std::string> FindAlgoEnglishName(const std::string& group_name, const std::string& zh_name);

};
#endif // editdetectmodelview_h
