<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddPadView</class>
 <widget class="QWidget" name="AddPadView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>212</width>
    <height>135</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>添加Pad</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="1" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_array">
      <attribute name="title">
       <string>阵列</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="label">
             <property name="text">
              <string>Pad个数</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="pad_number"/>
           </item>
          </layout>
         </item>
         <item row="2" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <widget class="QPushButton" name="btn_pad_preview">
             <property name="text">
              <string>预览</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_pad_generate">
             <property name="text">
              <string>生成</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>注：pad删除时，最后一个无法删除</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_matrix">
      <attribute name="title">
       <string>矩阵</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_15">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="label_15">
             <property name="text">
              <string>Pad行数：</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="pad_number_2"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_17">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="label_16">
             <property name="text">
              <string>Pad列数：</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="pad_number_3"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_16">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <widget class="QPushButton" name="btn_pad_preview_2">
             <property name="text">
              <string>预览</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_pad_generate_2">
             <property name="text">
              <string>生成</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QLabel" name="label_8">
     <property name="text">
      <string>*备注：先预览，再生成</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
