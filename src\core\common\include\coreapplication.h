/*****************************************************************//**
 * @file   coreapplication.h
 * @brief  通用应用工具类
 * @details    用于定义工具类对象使用接口
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef  __COREAPPLICATION_H__
#define __COREAPPLICATION_H__
 //STD
#include <iostream>
//Custom
#include "errorhandler.h"
#include "logmanager.h"
#include "messagemanager.h"
#include "pluginexport.hpp"
namespace jrscore
{

    struct CoreAppImplData;
    class JRS_AOI_PLUGIN_API CoreApplication
    {
    public:
        CoreApplication();
        ~CoreApplication();

        /**
         * @fun GetInstance
         * @brief 应用类使用单例模式
         * @return 返回单例
         * @date 2024.1.23
         * <AUTHOR>
         */
        static CoreApplication* GetInstance();

        ErrorHandlerPtr GetErrorHandler();

        LogManagerPtr GetLogManager();

        MessageManagerPtr GetMessageManager();
    private:
        CoreAppImplData* p_data;


    };


#define AOICoreApp jrscore::CoreApplication::GetInstance()

    //默认日志器写入，文件名称默认为JrsAOI
#define AOIAppLog(level,...)\
            AOICoreApp->GetLogManager()->Log(level,__VA_ARGS__)

    //输入错误码到日志收集器中，将会将错误码内容和错误描述信息都保存到日志中
#define PushErrorToStack(errorcode,des)\
            AOICoreApp->GetErrorHandler()->PushStackError(errorcode,des) 
    /** < 获取错误信息 */
#define GET_ERROR_INFO(err) \
            AOICoreApp->GetErrorHandler()->GetErrorInfo(err)

    // 报错然后返回异常值
#define RETURN_ERROR(error_code, msg) \
            PushErrorToStack(error_code, msg); return error_code; 

#define JrsMessageBox(what,flush_straegy)\
            AOICoreApp->GetErrorHandler()->FlushStackError (what,flush_straegy)

    //默认日志器写入带等级
#define Log_WARN(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_WARN,__VA_ARGS__)

#define Log_INFO(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_INFO,__VA_ARGS__)
#define Log_ERROR(...)\
                AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)
#define Log_DEBUG(...)\
                    AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_DEBUG,__VA_ARGS__)


/** @brief  workflow日志单独存放，为了便于自动运行时发生异常定位 by zhangyuyu 2024.12.31*/
#define LogAutoRun_INFO(...)\
            AOICoreApp->GetLogManager()->LogWithName("AutoRun",jrscore::LogLevel::LEVEL_INFO,__VA_ARGS__)
#define LogAutoRun_WARN(...)\
            AOICoreApp->GetLogManager()->LogWithName("AutoRun",jrscore::LogLevel::LEVEL_WARN,__VA_ARGS__)
#define LogAutoRun_DEBUG(...)\
            AOICoreApp->GetLogManager()->LogWithName("AutoRun",jrscore::LogLevel::LEVEL_DEBUG,__VA_ARGS__)
#define LogAutoRun_ERROR(...)\
            AOICoreApp->GetLogManager()->LogWithName("AutoRun",jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)


//指定日志文件名称及等级写入
#define LogTo_INFO(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_INFO,__VA_ARGS__)
#define LogTo_WARN(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_WARN,__VA_ARGS__)
#define LogTo_DEBUG(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_DEBUG,__VA_ARGS__)
#define LogTo_ERROR(logname,...)\
            AOICoreApp->GetLogManager()->LogWithName(logname,jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)

    /**
    错误收集器，单独存放，用于快速排查问题
    一般用于返回错误码时回调使用，正常日志记录不使用
    如需要保存错误日志，调用:PushErrorToStack
    如没有错误码，则直接使用Log_Error进行错误记录或单独添加一个新文件进行错误保存
    如：AOICoreApp->GetLogManager()->LogWithName("NewErrorFile",jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)
    **/
#define Log_Error_Stack(...)\
            AOICoreApp->GetLogManager()->LogWithName("Error",jrscore::LogLevel::LEVEL_ERROR,__VA_ARGS__)

    /**
      消息提示框 用于全局调用，线程安全，以三种类型显示 INFO WARN ERROR
      调用实例：
      jrscore::MessageButton res_btn=JRSMessageBox_INFO("title","content",jrscore::MessageButton::OK|jrscore::MessageButton::Cancel);
      **/
#define JRSMessageBox_INFO(title,text,button)\
    AOICoreApp->GetMessageManager()->ShowMessageBox(jrscore::LogLevel::LEVEL_INFO, title, text, button)
#define JRSMessageBox_WARN(title,text,button)\
    AOICoreApp->GetMessageManager()->ShowMessageBox(jrscore::LogLevel::LEVEL_WARN, title, text, button)
#define JRSMessageBox_ERR(title,text,button) \
    AOICoreApp->GetMessageManager()->ShowMessageBox(jrscore::LogLevel::LEVEL_ERROR, title, text, button)
}

#endif // ! __COREAPPLICATION_H__
