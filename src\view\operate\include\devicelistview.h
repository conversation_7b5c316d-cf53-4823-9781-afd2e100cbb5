﻿/*****************************************************************//**
 * @file   DeviceListView.h
 * @brief  检测界面
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef DEVICELISTVIEW_H
#define DEVICELISTVIEW_H

 //QT
#include <QWidget>
#include <QButtonGroup>

// CUSTOM

#pragma warning(push, 3)
#include "ui_devicelistview.h"
#pragma warning(pop)


namespace Ui {
    class DeviceListView;
}

class DeviceListView : public QWidget
{
    Q_OBJECT

public:
    explicit DeviceListView(QWidget* parent = nullptr);
    ~DeviceListView();

private:
    Ui::DeviceListView* ui;

};

#endif // DeviceListView_H
