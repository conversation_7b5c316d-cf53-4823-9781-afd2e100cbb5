﻿#include <iostream>

#include <Eigen/Dense>

#include "cvtools.h"

double Rad2Deg(const double rad)
{
    return rad * 180.0 / M_PI;
}

double Deg2Rad(const double deg)
{
    return deg * M_PI / 180.0;
}

namespace jcvtools
{


    JrsHomMat2D::JrsHomMat2D(const cv::Mat& mat)
    {
        matrix = Eigen::Matrix3d::Identity();
        if (mat.rows == 3 && mat.cols == 3)
        {
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    if (mat.type() == CV_32FC1)
                    {
                        matrix(i, j) = mat.at<float>(i, j);

                    }
                    else
                    {
                        matrix(i, j) = mat.at<double>(i, j);
                    }
                }
            }
        }
        else if (mat.rows == 2 && mat.cols == 3)
        {
            for (int i = 0; i < 2; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    if (mat.type() == CV_32FC1)
                    {
                        matrix(i, j) = mat.at<float>(i, j);

                    }
                    else
                    {
                        matrix(i, j) = mat.at<double>(i, j);
                    }
                }
            }
        }
        else
        {
            std::cerr << "Error: JrsHomMat2D constructor: input matrix must be 3x3\n";
            return;
        }
    }

    Eigen::Matrix3d JrsHomMat2D::GenTranslationMatrix(double tx, double ty)
    {
        Eigen::Matrix3d matrix = Eigen::Matrix3d::Identity();
        matrix(0, 2) = tx;
        matrix(1, 2) = ty;
        return matrix;
    }

    Eigen::Matrix3d JrsHomMat2D::GenRotationMatrix(double angle_rad)
    {
        double cos_theta = std::cos(angle_rad);
        double sin_theta = std::sin(angle_rad);
        Eigen::Matrix3d matrix = Eigen::Matrix3d::Identity();
        matrix(0, 0) = cos_theta;
        matrix(0, 1) = -sin_theta;
        matrix(1, 0) = sin_theta;
        matrix(1, 1) = cos_theta;
        return matrix;
    }

    Eigen::Matrix3d JrsHomMat2D::GenScaleMatrix(double sx, double sy)
    {
        Eigen::Matrix3d matrix = Eigen::Matrix3d::Identity();
        matrix(0, 0) = sx;
        matrix(1, 1) = sy;
        return matrix;
    }

    void JrsHomMat2D::HomMat2dIdentity()
    {
        matrix = Eigen::Matrix3d::Identity();
    }
    void JrsHomMat2D::AddHomMat2dRotate(double angle, double center_x, double center_y)
    {
        double phi = angle * M_PI / 180.0;
        auto matrix_translate1 = GenTranslationMatrix(-center_x, -center_y);
        auto matrix_rotate = GenRotationMatrix(phi);
        auto matrix_translate2 = GenTranslationMatrix(center_x, center_y);
        matrix = matrix_translate2 * matrix_rotate * matrix_translate1 * matrix;
    }
    void JrsHomMat2D::AddHomMat2dScale(double scale_x, double scale_y)
    {
        auto matrix_scale = GenScaleMatrix(scale_x, scale_y);
        matrix = matrix_scale * matrix;
    }

    void JrsHomMat2D::AddHomMat2dTranslate(double tx, double ty)
    {
        auto matrix_translate = GenTranslationMatrix(tx, ty);
        matrix = matrix_translate * matrix;

    }

    JrsHomMat2D JrsHomMat2D::HomMat2dInvert() const
    {
        return JrsHomMat2D(matrix.inverse());
    }

    void JrsHomMat2D::HomMat2dCompose(const JrsHomMat2D& other)
    {
        matrix = other.matrix * matrix;
    }

    cv::RotatedRect JrsHomMat2D::AffineTransRotatedRect(const cv::RotatedRect& rect) const
    {
        std::vector<cv::Point2f> vertices;
        rect.points(vertices);
        auto transformed_vertices = AffineTransPoints(vertices);
        cv::RotatedRect res_rotated_rect;
        try
        {
            res_rotated_rect = cv::minAreaRect(transformed_vertices);
        }
        catch (const cv::Exception& e)
        {
            std::cerr << "OpenCV Exception: " << e.what() << std::endl;
        }
        catch (const std::exception& e)
        {
            (void)e;
            //Log_ERROR("Exception caught in minAreaRect: ", e.what());
        }
        catch (...)
        {
            std::cerr << "Unknown Exception caught!" << std::endl;
        }
        return res_rotated_rect;
    }

    cv::RotatedRect JrsHomMat2D::AffineTransRect(const cv::Rect& rect) const
    {
        std::vector<cv::Point2f> vertices;
        vertices.push_back(cv::Point2f(rect.x, rect.y));
        vertices.push_back(cv::Point2f(rect.x + rect.width, rect.y));
        vertices.push_back(cv::Point2f(rect.x + rect.width, rect.y + rect.height));
        vertices.push_back(cv::Point2f(rect.x, rect.y + rect.height));
        auto transformed_vertices = AffineTransPoints(vertices);
        return cv::minAreaRect(transformed_vertices);
    }

    cv::Point2f JrsHomMat2D::AffineTransPoint(const cv::Point2f& point) const
    {
        cv::Point2f transformed_pt;
        transformed_pt.x = matrix(0, 0) * point.x + matrix(0, 1) * point.y + matrix(0, 2);
        transformed_pt.y = matrix(1, 0) * point.x + matrix(1, 1) * point.y + matrix(1, 2);
        return transformed_pt;
    }

    std::vector<cv::Point2f> JrsHomMat2D::AffineTransPoints(const std::vector<cv::Point2f>& points) const
    {
        std::vector<cv::Point2f> transformed_points;
        for (const auto& point : points)
        {
            transformed_points.push_back(AffineTransPoint(point));
        }
        return transformed_points;
    }

    void JrsHomMat2D::PrintMatrix() const
    {
        std::cout << "Transformation Matrix:\n" << matrix << "\n";
    }

    cv::Mat JrsHomMat2D::toMat() const
    {
        cv::Mat mat(2, 3, CV_64F);
        for (int i = 0; i < 2; i++)
        {
            for (int j = 0; j < 3; j++)
            {
                mat.at<double>(i, j) = matrix(i, j);
            }
        }
        return mat;
    }

    std::vector<std::vector<cv::Point>> JrsCVTools::ConvertMarkToContours(const cv::Mat& mark)
    {
        std::vector<std::vector<cv::Point>> contours;
        std::vector<cv::Vec4i> hierarchy;
        try
        {
            cv::Mat gray;
            if (mark.channels() == 3)
                cv::cvtColor(mark, gray, cv::COLOR_BGR2GRAY);
            else
                gray = mark.clone();
            // 转成二值图
            cv::Mat binary;
            if (IsBinaryImage(gray))
            {
                binary = gray.clone();
            }
            else
            {
                cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY | cv::THRESH_OTSU);
            }
            cv::findContours(binary, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE);
        }
        catch (const std::exception& e)
        {
            std::cerr << "数据转换失败" << e.what() << std::endl;
        }

        return contours;
    }

    bool JrsCVTools::IsBinaryImage(const cv::Mat& image)
    {
        if (image.channels() != 1)
            return false;

        double min_val, max_val;
        cv::minMaxLoc(image, &min_val, &max_val);

        return (min_val == 0.0 || min_val == 255.0) &&
            (max_val == 0.0 || max_val == 255.0);
    }

}