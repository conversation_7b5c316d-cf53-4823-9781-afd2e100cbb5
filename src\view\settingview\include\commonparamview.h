﻿#pragma once

//prebuild
#include "pch.h"
//STD
#include <any>
//QT
//#include <QWidget>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QAction>
#include <QFileDialog>
#include <QLineEdit>
#include <QSpinBox>
//Custom
//#include "viewparam.hpp"
#include "ui_commonparamview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class commonparamviewClass; };
QT_END_NAMESPACE

namespace jrsaoi
{
    using ControlMap = std::variant<QLineEdit*, QCheckBox*>;
    class CommonParamView : public QWidget
    {
        Q_OBJECT
    public:
        CommonParamView(QWidget* parent = nullptr);
        ~CommonParamView();
        /**
         * @fun UpdateView
         * @brief 从数据库获取数据更新界面
         * @date 2024.5.7
         * <AUTHOR>
         */
        void UpdateView(const jrsdata::SettingParamMap& common_params_);
        /**
         * @fun GetSystemParam
         * @brief 获取当前系统参数
         * @return
         * @date 2024.7.9
         * <AUTHOR>
         */
        jrsdata::SettingParamMap GetParam();
    private slots:
        /**
         * @fun SlotSaveParam
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotSaveParam();
        /**
         * @fun SlotChooseDirPath 
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotChooseDirPath();
    signals:
        /**
         * @fun SigSaveParam
         * @brief
         * @param common_params_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigSaveParam(const jrsdata::SettingParamMap& common_params_);
        /**
         * @fun SigUpdateParam 
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigUpdateParam();
    private:
        /**
        * @fun InitMember
        * @brief 初始化成员属性
        * @date 2024.5.7
        * <AUTHOR>
        */
        void InitMember();
        /**
         * @fun InitView 
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数连接
         * @date 2024.5.7
         * <AUTHOR>
         */
        void InitConnect();
        /**
         * @fun SaveRemoteParam
         * @brief 保存数据
         * @date 2024.5.7
         * <AUTHOR>
         */
        void SaveParam();
        /**
         * @fun UpdateStaticParam
         * @brief 更新静态参数界面
         * @date 2024.5.8
         * <AUTHOR>
         */
        void UpdateStaticParam();
        /**
         * @fun SetHideController
         * @brief 隐藏控件
         * @param system_param_
         * @date 2024.7.9
         * <AUTHOR>
         */
        void SetHideController(const jrsdata::SettingParamMap& common_params_);
    private:
        Ui::commonparamviewClass* ui;
        jrsdata::SettingParamMap _common_params;
        std::vector<QWidget*> ui_need_hide_controller;/**< 需要隐藏的控件 */
        std::map<std::string, QWidget*> ui_map_value; /**< 系统参数到控件按钮的映射 系统参数名称---对应的UI上控件*/
        std::map<std::string, QLineEdit*> map_shared_path;
    };
}