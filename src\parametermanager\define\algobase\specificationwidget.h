/*****************************************************************//**
 * @file   specificationwidget.h
 * @brief  zh-cn: 定义规格管控界面类
 * @details 定义规格管控界面类，用于管理规格组件组，包括控件和类型，并提供相应的操作接口。  
 * <AUTHOR>
 * @date 2024.10.07
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.07        <td>V1.0              <td>xailor       <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef JUDGEPARAMWIDGETMANAGER_H
#define JUDGEPARAMWIDGETMANAGER_H

// std
#include <string>
#include <vector>

// qt
#include <QWidget>

// custom
#include "specificationwidgetparam.h"

class QComboBox;
class QPushButton;
class QVBoxLayout;
class ComponentWidget;
class SpecificationConfigWidget;
struct ComponentGroup;
class SpecificationWidget : public QWidget
{
	Q_OBJECT
public:
    SpecificationWidget(QWidget* parent = nullptr);
    /**
     * @fun AddSpecificationComponent 
     * @brief  增加规格组件
     * @param  widgets 规格组件参数
     * @date 2024.10.07
     * <AUTHOR>
     */
    void AddSpecificationComponent(SpecificationWidgetParams widgets);
    /**
     * @fun RemoveSpecificationComponent 
     * @brief  删除规格组件
     * @param  widgets 规格组件参数
     * @date 2024.10.07
     * <AUTHOR>
     */
    void RemoveSpecificationComponent(SpecificationWidgetParams widgets);
    /**
     * @fun SetSpecificationComponentValues 
     * @brief  设置规格组件值
     * @param  widgets 规格组件参数
     * @date 2024.10.07
     * <AUTHOR>
     */
    void SetSpecificationComponentValues(SpecificationWidgetParams widgets,
        SpecificationComponentDatas& values);    
    /**
     * @fun GetSpecificationComponentValues 
     * @brief  获取规格组件值
     * @param  widgets 规格组件参数
     * @date 2024.10.07
     * <AUTHOR>
     */
    SpecificationComponentDatas GetSpecificationComponentValues(SpecificationWidgetParams widgets);
    /**
   * @fun ShowWidgets
   * @brief  显示当前窗口
   * @param  widgets 规格组件参数
   * @date 2024.10.07
   * <AUTHOR>
   */
    void ShowWidgets();
	/**
     * @fun Clear 
     * @brief  清除所有规格组件值
     * @param  widgets 规格组件参数
     * @date 2024.10.30
     * <AUTHOR>
     */
    void Clear(SpecificationWidgetParams widgets);
    /**
     * @fun Clone
     * @brief  克隆当前规格
     * @date 2024.10.30
     * @date 2024.11.21 存在信号传递bug，需要修改，暂不要使用
     * <AUTHOR>
     */
    SpecificationWidget* Clone();
    /**
     * @fun    SetSpecificationComponentValue
     * @brief  设置单个规格组件的值
     * @date   2024.11.20
     * <AUTHOR>
     */
    void SetSpecificationComponentValue(SpecificationWidgetParam widget, 
        SpecificaParamBasePtr& value);
    /**
     * @fun    GetSpecificationComponentValue
     * @brief  获取单个规格组件的值
     * @date   2024.11.20
     * <AUTHOR>
     */
    SpecificaParamBasePtr GetSpecificationComponentValue(SpecificationWidgetParam widget);
    /**
     * @fun    GetSpecificationParams
     * @brief  获取当前规格管控组的所有规格参数 包括快速设置和保存的规格
     * @date   2024.12.11
     * <AUTHOR>
     */
    VecSpecificationParams GetSpecificationParams();
    ///**
    //  * @fun    CreatSpecificationComponent
    //  * @brief  通过映射表创建规格组件
    //  * @date   2024.12.09
    //  * <AUTHOR>
    //  */
    //SpecificationWidgetParams CreatSpecificationComponent(const std::unordered_map<std::string, 
    //    std::pair<ComponentsType, SpecificaTransType>> specification_map);
    ~SpecificationWidget();
signals:
	/**
     * @fun UpdateCurrentValue 
     * @brief 更新当前组件的信号          
     * @param  widgets  
     * @date 2024.10.20
     * <AUTHOR>
     */
   void UpdateCurrentValue();

public slots:
    /**
     * @fun ParamsControlListSlot
     * @brief  快速规格设置控件列表槽函数
     * @date 2024.10.07
     * <AUTHOR>
     */
    void ParamsControlListSlot();
    /**
     * @fun AddParamsValueFromParamsControlList
     * @brief  从快速规格设置控件列表增加规格控件值
     * @date 2024.10.07
     * <AUTHOR>
     */
    void AddParamsValueFromParamsControlList(FastSpecificationParam m_cur_specifica_config);
    /**
     * @fun OnAddParamsValue
     * @brief  保存前增加规格控件值
     * @date 2024.10.07
     * <AUTHOR>
     */
	void OnAddParamsValue();
    /**
     * @fun OnUpdateParamsValue
     * @brief  更新规格控件值
     * @date 2024.10.07
     * <AUTHOR>
     */
	void OnUpdateParamsValue();
    /**
     * @fun OnDelParamsValue
     * @brief  删除规格控件值
     * @date 2024.10.07
     * <AUTHOR>
     */
    void OnDelParamsValue();
    /**
     * @fun ComboxChange
     * @brief  切换不同规格标准时规格界面显示不同的值
     * @date 2024.10.07
     * <AUTHOR>
     */
    void ComboxChange();
    /**
    * @fun UpdateCurrentValueSlot
    * @brief  从单个规格组件更新规格UI的槽函数 用于发出规格已经修改的信号
    * @date 2024.10.20
    * <AUTHOR>
    */
    void UpdateCurrentValueSlot();
private:
    void InitMember();
    void InitUi();
    void InitConnect();
    SpecificationConfigWidget* m_spe_config_widget;  // 规格快速设置ui
    SpecificationWidgetParams m_specifications_widget_params; // 传入增加的规格控件
    QComboBox* m_pre_specification; // 已经保存的规格
    QPushButton* add_specification; 
	QPushButton* update_specification;
    QPushButton* del_specification;
    QPushButton* params_control; // 用于调用快速设置规格
	QPushButton* ai_model_control; // 用于调用AI模型设置
    QVBoxLayout* reslution_layout; // 整体布局
    std::unordered_map<std::string,ComponentGroup> m_component_group; // 规格组件组 包含控件和类型
    VecSpecificationParams m_vec_specitfications; // 预加载规格管控组数据
};


#endif