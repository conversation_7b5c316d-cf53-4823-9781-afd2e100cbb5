#include "scanprocess.h"
#include "devicemanager.h"
#include "structlight.h"
#include "jrsaoiimgsmanager.h"
#include "motion.h"
#include "coreapplication.h"
#include "fileoperation.h"

namespace jrslogic
{
    ScanProcess::ScanProcess(const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_)
        :device_manager_ptr(device_manager_ptr_)
        , is_running(false)
        , is_paused(false)
        , is_stopped(false)
        , waittting(true)
    {

        /*auto trigger_callback_temp = std::bind(&ScanProcess::TriggerDone, this, std::placeholders::_1);
        auto merge_callback_temp = std::bind(&ScanProcess::MergeDone, this, std::placeholders::_1, std::placeholders::_2);
        device_manager_ptr->GetStructLightInstance()->SetTriggerDoneCallback(trigger_callback_temp);
        device_manager_ptr->GetStructLightInstance()->SetMergeImageDoneCallbacek(merge_callback_temp);*/
    }
    ScanProcess::~ScanProcess()
    {
        if (scan_thread.joinable())
        {
            scan_thread.join();

        }
    }
    void ScanProcess::StartCapture(bool is_auto_run)
    {
        is_auto_running = is_auto_run;
        auto trigger_callback_temp = std::bind(&ScanProcess::TriggerDone, this, std::placeholders::_1);
        auto merge_callback_temp = std::bind(&ScanProcess::MergeDone, this, std::placeholders::_1, std::placeholders::_2);
        device_manager_ptr->GetStructLightInstance()->SetTriggerDoneCallback(trigger_callback_temp);
        device_manager_ptr->GetStructLightInstance()->SetMergeImageDoneCallbacek(merge_callback_temp);
        is_stopped = false;
        is_paused = false;
        aoi_imgs_manager_ptr->ClearFovImgs();
        std::thread scan_thread_ = std::thread(&ScanProcess::Run, this);
        scan_thread_.detach();
        is_running = true;
    }
    void ScanProcess::PauseCapture()
    {
        is_paused = true;
    }
    void ScanProcess::ResumeCapture()
    {
        is_paused = false;
        cv_scan.notify_all();
    }
    void ScanProcess::StopCapture()
    {
        is_running = false;
        is_stopped = true;
        cv_scan.notify_all();
        if (scan_thread.joinable())
        {
            scan_thread.join();

        }
    }
    void ScanProcess::SetJrsAoiImgsManager(const std::shared_ptr<jrslogic::JrsAoiImgsManager>& _aoi_imgs_manager_ptr)
    {
        aoi_imgs_manager_ptr = _aoi_imgs_manager_ptr;
    }

    void ScanProcess::SetCapturePosList(const jrsdata::CaptureFovPosList& pos_lists_)
    {
        std::unique_lock<std::mutex> lock(mtx_scan);

        capture_fov_pos_list = pos_lists_;
        cv_scan.notify_all();
        lock.unlock();
    }
    void ScanProcess::SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback)
    {
        render_imgs_callback = img_buffer_callback;
    }
    void ScanProcess::SetWorkFlowCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback_)
    {
        work_flow_imgs_callback = img_buffer_callback_;
    }
    void ScanProcess::Init()
    {

    }
    void ScanProcess::Run()
    {
        //while (true)
        {

           
        }
    }
    void ScanProcess::TriggerDone(const int& result_)
    {
        //TODO 这里虽然已经触发成功，但是并不一定标志这数据已经采集成功，这时候如果运控继续移动，采集的图像就是有问题的 by baron_zhang 2024-12-12
        if (result_ != jrscore::AOI_OK)
        {
            Log_ERROR("采图触发失败！");
        }

        waittting = false;
        cv_scan.notify_one();
    }
    void ScanProcess::MergeDone(const jrsdata::OneFovImgs& imgs, const int& result_)
    {


        if (result_ != jrscore::AOI_OK)
        {
            Log_ERROR("成像失败！");
            return;
        }

        fov_count++;
        jrsdata::OneFovImgs one_fov_img;
        one_fov_img = imgs;


        for (size_t i = 0; i < capture_fov_pos_list.fov_pos_list.size(); i++)
        {
            if (capture_fov_pos_list.fov_pos_list[i].id == imgs.fov_id)
            {
                one_fov_img.pos.x = capture_fov_pos_list.fov_pos_list[i].x_fov_coordinate;
                one_fov_img.pos.y = capture_fov_pos_list.fov_pos_list[i].y_fov_coordinate;
                break;
            }
        }

        if (-1 == aoi_imgs_manager_ptr->AddFovImg(one_fov_img, !is_auto_running))
        {
            Log_ERROR("添加图像失败！");
        }
        //if (!is_auto_running)
        //{
        /*    if (-1 == aoi_imgs_manager_ptr->AddFovImg(one_fov_img, true))
            {
                Log_ERROR("图像拼接失败！");
            }*/
        //}

         Log_INFO("当前FOV_Num：", fov_count);
         /*  Log_INFO("全部FOV_Num：", fov_num);*/
        if (fov_count < fov_num)
        {
            jrsdata::JrsImageBuffer jrsbuffer;
            jrsbuffer.one_fov_imgs = one_fov_img;
            if (is_auto_running)
            {
                work_flow_imgs_callback(jrsbuffer);

            }
            else 
            {
                render_imgs_callback(jrsbuffer);

            }
        }
        else
        {

            //auto rgbImg = aoi_imgs_manager_ptr->GetBoardImg(jrsdata::ImgType::RGB);
            //! 获取所有的色彩的图像  by zhangyuyu 2024.9.24
            auto all_imgs = aoi_imgs_manager_ptr->GetBoardImgs();
            jrsdata::JrsImageBuffer jrsbuffer;
            //jrsbuffer.boarder_imgs.imgs.insert({ jrsdata::ImgType::RGB ,*rgbImg }); 
            jrsbuffer.boarder_imgs.imgs = *all_imgs;

            //Log_INFO("图像合成大图", jrsbuffer.boarder_imgs.imgs[jrsdata::LightImageType::RGB].cols, jrsbuffer.boarder_imgs.imgs[jrsdata::LightImageType::RGB].rows );

            jrsbuffer.one_fov_imgs = one_fov_img;

            if (is_auto_running)
            {
                work_flow_imgs_callback(jrsbuffer);

            }
            else
            {
                aoi_imgs_manager_ptr->ClearFovImgs();/*<清除单个FOV数据释放内存*/
                render_imgs_callback(jrsbuffer);
                aoi_imgs_manager_ptr->ClearBoardImgs(); /**< 发完后清除大图 释放内存*/

            }
      
        }
    }
}
