/*****************************************************************
 * @file   multiboardparam.hpp
 * @brief   多连扳的一些操作 所需结构体
 * @details
 * <AUTHOR>
 * @date 2025.6.22
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.6.22          <td>V2.0             <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#pragma once
 //STD

 //Custom
#include "projectparam.hpp"
 //Third

namespace jrsdata {
    struct SubboardSortParam
    {
        enum class Event
        {
            NONE,
            REGULAR_SORT,
            IRREGULAR_SORT,
            CONFIRM_SORT,
            CANCEL_SORT,
        };
        enum class SortShape
        {
            S, /**< S-Shape */
            Z, /**< S-Shape */
            Vertical_S, /**< Vertical S-Shape */
            Vertical_Z, /**< Vertical Z-Shape */
        };
        enum class SubBoardDirection
        {
            LeftTopToRightBottom, /**< Sort from top left to bottom right */
            RightTopToLeftBottom, /**< Sort from top right to bottom left */
            LeftBottomToRightTop, /**< Sort from bottom left to top right */
            RightBottomToLeftTop, /**< Sort from bottom right to top left */
        };
        SortShape sort_shape = SortShape::S; /**< Sorting shape */
        SubBoardDirection subboard_direction = SubBoardDirection::LeftTopToRightBottom; /**< Sorting direction */
        std::vector<jrsdata::SubBoard> sorted_subboards; /**the subboards that has been sorted*/
        Event event = Event::NONE; /**< 事件类型 */
    };
    /**< 多联板事件参数 */
    struct MultiBoardEventParam
    {
        enum class  Event
        {
            NONE,
            MULTIPLE_BOARD,/**<多连扳事件*/
            BOARD_SORT,/**<板子排序事件*/
            BOARD_OPERATE,/**< board operate event*/
        };
        enum class CreateType :int
        {
            NONE = 0,
            REGULAR,
            IREGULAR,
        };
        /**< 规则多联板  */
        struct RegularParam
        {
            enum class Step
            {
                NONE = 0,
                SELECT_COMPONENT,                 /**< 选择一个元件 **/
                MARK_COMPONENT_AS_TEMPLATE,       /**< 标记该元件为模板 **/
                SELECT_COL_FLAG,                  ///< 选择列标记
                SELECT_ROW_FLAG,                  ///< 选择行标记
                IDENTIFY_REMOTE_SUBBOARD,         /**< 识别远端位置 */
                GENERATE_MULTIPLE_BOARD,          /**<  生成多联板*/
                CANCEL_GENERATE_MULTIPLE_BOARD,   /**<  取消生成多联板*/
            };
            int cols = 0;  /*< 行数 */
            int rows = 0;  /*< 列数 */
            Step step;     /**< 操作事件 */
            std::pair < jrsdata::Component, std::unordered_map<jrsdata::LightImageType, cv::Mat>> component_and_images; /**< 元件信息和图像信息 */
            std::unordered_map<std::string /**<临时框名称*/, cv::Rect2f > temp_mark_and_rect_map;
            std::vector<jrsdata::SubBoard> temp_clone_subboards;  /**< 临时存储子板信息 */
            RegularParam()
                :cols(0), rows(0), step(Step::NONE), component_and_images({})
                , temp_mark_and_rect_map({}), temp_clone_subboards({})
            {
            }
            RegularParam(int cols_, int rows_, Step step_,
                std::pair < jrsdata::Component, std::unordered_map<jrsdata::LightImageType, cv::Mat>> component_and_images_
                , std::unordered_map<std::string /**<临时框名称*/, cv::Rect2f >temp_mark_and_rect_map_
                , std::vector<jrsdata::SubBoard> temp_clone_subboards_
            )
                :cols(cols_), rows(rows_), step(step_), component_and_images(component_and_images_),
                temp_mark_and_rect_map(temp_mark_and_rect_map_), temp_clone_subboards(temp_clone_subboards_)
            {
            }
            // 拷贝构造函数
            RegularParam(const RegularParam& other)
                : cols(other.cols), rows(other.rows), step(other.step),
                component_and_images(other.component_and_images)
                , temp_mark_and_rect_map(other.temp_mark_and_rect_map)
                , temp_clone_subboards(other.temp_clone_subboards)
            {
            }

            // 拷贝赋值操作符
            RegularParam& operator=(const RegularParam& other)
            {
                if (this != &other)  // 防止自赋值
                {
                    cols = other.cols;
                    rows = other.rows;
                    step = other.step;
                    component_and_images = other.component_and_images;
                    temp_mark_and_rect_map = other.temp_mark_and_rect_map;
                    temp_clone_subboards = other.temp_clone_subboards;
                }
                return *this;
            }

            // 也可以提供移动构造和移动赋值操作符，如果需要性能优化
            RegularParam(RegularParam&& other) noexcept
                : cols(std::move(other.cols)), rows(std::move(other.rows)),
                step(std::move(other.step)),
                component_and_images(std::move(other.component_and_images)),
                temp_mark_and_rect_map(std::move(other.temp_mark_and_rect_map)),
                temp_clone_subboards(std::move(other.temp_clone_subboards))
            {
            }

            RegularParam& operator=(RegularParam&& other) noexcept
            {
                if (this != &other)
                {
                    cols = std::move(other.cols);
                    rows = std::move(other.rows);
                    step = std::move(other.step);
                    component_and_images = std::move(other.component_and_images);
                    temp_mark_and_rect_map = std::move(other.temp_mark_and_rect_map);
                    temp_clone_subboards = std::move(other.temp_clone_subboards);
                }
                return *this;
            }

            //// 可选的：处理 std::optional<RegularParam> 赋值
            //RegularParam(std::optional<RegularParam> param_)
            //{
            //    if (param_.has_value())
            //    {
            //        cols = param_->cols;
            //        rows = param_->rows;
            //        step = param_->step;
            //        component_and_images = param_->component_and_images;
            //    }
            //}
        };
        CreateType create_type; /**< 创建类型： 规则|不规则*/
        std::optional<RegularParam> regular_multiple_board_param; /**< 规则多联板 */
        struct IrregularParam
        {
            enum class Step
            {
                NONE = 0,
                GENERATE_MULTI_BOARD,  ///<生成多连扳
                CONFIRM_SUBBOARD_COMPONENT,/**< 确认子板元件*/
                SELECT_SUBBOARD_LOCATION,  ///<框选子板定位框位置
                CONFIRM_SUBBOARD_LOCATION, ///< 确认子板定位
            };
            Step step;
            int cols = 0;  /*< 行数 */
            int rows = 0;  /*< 列数 */
            std::string graphics_name;/**< temp_图形名称 */
        };

        std::optional<IrregularParam> irregular_multiple_board_param;

        struct OperateParam
        {
            enum class OperateObject
            {
                None = 0,
                SUBBOARD,/**< 对子板操作*/
                ENTIRETY_BOARD,/**< 对整版操作*/
            };
            enum class OperateType
            {
                None = 0,
                BOARD,     /**< 对板子的操作包含板子内的元件*/
                COMPONENT, /**< 只对元件操作，板子不操作*/
            };
            enum class TransformType
            {
                None = 0,
                ROTATE_90, /**<旋转90度*/
                CONVERT, /**< 角度倒置*/
                VERTICAL_MIRROR, ///< 垂直镜像
                HORIZONTAL_MIRROR, ///< 水平镜像
            };
            OperateObject operate_object; /**<操作对象*/
            OperateType operate_type;/**< 操作类型*/
            TransformType transform_type; /**< 变换类型*/
        };
        std::optional<OperateParam> operate_param;/**<操作参数*/

        std::optional<jrsdata::SubboardSortParam> subboard_sort_param; /**<子板排序操作*/

        Event event = Event::NONE; /**< 事件类型 */

        //TODO: 以下 删除参数即将删除  HJC
        enum class Step
        {
            NONE = 0,
            SELECT_TEMPLATE_FLAG, ///< 选择模板标记
            SELECT_COL_FLAG, ///< 选择列标记
            SELECT_ROW_FLAG, ///< 选择行标记
        };
        enum class MultiSelectType
        {
            NONE = 0,
            SELECT_CLICK, ///< 点击选择
            SELECT_RECT,  ///< 矩形选择
            SELECT_POLYGON, ///< 多边形选择
        };

        enum class MultiCopyType
        {
            NONE = 0,
            ARRAY, ///< 阵列复制
            ROTATE, ///< 旋转复制
        };


        Step step = Step::NONE;
        MultiSelectType multi_select_type = MultiSelectType::NONE;
        MultiCopyType copy_mode = MultiCopyType::NONE;
        int cols = 0;  /*< 行数 */
        int rows = 0;  /*< 列数 */
    };
    using RegularParamPtr = std::shared_ptr<jrsdata::MultiBoardEventParam::RegularParam>;
    using MultiBoardEventParamPtr = std::shared_ptr<jrsdata::MultiBoardEventParam>;
    const std::unordered_map<MultiBoardEventParam::MultiSelectType, std::string> MultiBoardEventParamMultiSelectTypeMap
    {
        {MultiBoardEventParam::MultiSelectType::SELECT_CLICK,"点击选择"},
        {MultiBoardEventParam::MultiSelectType::SELECT_RECT,"矩形选择"},
        {MultiBoardEventParam::MultiSelectType::SELECT_POLYGON,"多边形选择"},
    };
    const std::unordered_map<MultiBoardEventParam::MultiCopyType, std::string> MultiBoardEventParamMultiCopyTypeMap
    {
        {MultiBoardEventParam::MultiCopyType::ARRAY,"阵列复制"},
        {MultiBoardEventParam::MultiCopyType::ROTATE,"旋转复制"},
    };


}
