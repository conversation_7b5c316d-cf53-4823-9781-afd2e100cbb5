﻿/**
* @file   render2dview.h
* @brief  渲染界面view实例
* @details
* <AUTHOR>
* @date 2024.7.16
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Description
* <tr><td>2024.7.16         <td>V2.0              <td>z<PERSON><PERSON>yu      <td>zhang<PERSON><EMAIL> <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/
#ifndef __JRSRENDER2DVIEW_H__
#define __JRSRENDER2DVIEW_H__
// QT
// Custom

#include "pch.h"
#include "jrs3dvisionwindow.h"
#include "image.hpp"

class Renderer2D;
class GraphicsManager;
class GraphicsAbstract;
// 2024/12/16 wangzhengkai 更换3D显示控件
struct GraphicsShape;
struct GraphicsImage;
class LayerConfig;
class GraphicsID;

QT_BEGIN_NAMESPACE
namespace Ui
{
    class Render2dView;
};
QT_END_NAMESPACE


namespace jrstool
{
    template <typename T>
    class PointLabel;
};


namespace jrsaoi
{
    /*
    * 将接口合并成一个
    */
    enum GraphicAttributeEditType
    {
        NONE,
        edit_x,
        edit_y,
        edit_w,
        edit_h,
        edit_a,
    };
    struct ImageShowParam;
    class Render2dView : public ViewBase
    {
        Q_OBJECT
    public:
        Render2dView(const std::string& name, QWidget* parent = nullptr);
        ~Render2dView();
        virtual int Init() override;
        virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_) override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;

        /**
         * @brief  获取当前图像索引
         * @return 图像索引
         * <AUTHOR>
         */
        int GetCurrentImageIndex() { return static_cast<int>(current_image_index); }
        /**
         * @brief  获取当前状态
         * @return 状态
         * <AUTHOR>
         */
        int GetCurrentVisionMode();
        /**
         * @brief  获取指定图层图形
         */
        int GetLayerGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, std::string layer = "");
        /**
         * @brief  获取图形管理器
         */
        std::shared_ptr<GraphicsManager> GetGraphicsManager() const;

    signals:
        void SignalGraphicsCreated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SignalGraphicsUpdated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool is_update_graphics_ = true);
        void SignalGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SignalGraphicsDelete(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SignalRegionSelected(float x, float y, float w, float h);

    public slots:
        void SlotRenderStateChange(int state);
        void SlotRenderCreateGraphicsModeChange(int mode);
        void SlotAddImage(const cv::Mat& image, int img_index, int x, int y, int z, float angle, bool is_draw_center);
        void SlotShowImages(const std::vector<ImageShowParam>& params);
        void SlotShowImageChange(const uint8_t& set_key_, int key_);
        void SlotShowImageChangeWithStr(const uint8_t& set_key_, const std::string& img_type_str);
        void SlotClearImage(const int& set_key_, int key_);
        void SlotDrawAngleChange(int angle);
        void SlotRenderCanvasSizeChange(int width, int height);
        void SlotCurrentLayerChange(const std::string& layer);
        void SlotCameraScaleModeChange(int mode);
        void SlotCameraResetModeChange(int mode);
        void SlotResetCamera();
        void SlotMoveCamera(int direction);
        void SlotMoveCameraToGraphics(const std::shared_ptr<GraphicsAbstract>& gh);
        void SlotMoveCameraToGraphicsWithID(const GraphicsID& id);
        void SlotMoveCameraToSelectedGraphics();
        void SlotThumbnailShow();
        void SlotAddGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback);
        void SlotAddGraphicsSingle(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer, bool invoke_callback);
        void SlotGraphicsSelect(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state);
        void SlotGraphicsSelectSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback);
        void SlotGraphicsSelectSingleWithID(const GraphicsID& id, bool invoke_callback);
        void SlotGraphicsAttributeEditSingleSelected(double val, int type);
        void SlotGraphicsAttributeEditByGraphicsPtr(const std::shared_ptr<GraphicsAbstract>& gh_ptr, double val, int type);
        //void SlotGraphicsCreateToView(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const RenderEditParam& render_edit_param);
        void SlotAddGraphicsLayerConfig(const std::string& layer, std::shared_ptr<LayerConfig> config);
        void SlotShowGraphicsLayer(const std::string& layer);
        void SlotClearLayerGraphics(const std::string& layer, bool invoke_callback_);
        void SlotClearGraphics(bool invoke_callback);
        void SlotClearGraphicsExceptLayer(const std::string&, bool invoke_callback);
        void SlotClearPadGroup();
        void SlotGetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids);
        void SlotGetGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id);
        void SlotGetCurrentSelectedGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer);
        void SlotGetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer);
        /** <graphics 添加图形 */
        void SlotAddGraphicsShapes(const GraphicsShape& graphics_shape_);
        void SlotClearGraphicsShapes();
        /**
         * @fun SlotCreateGraphics
         * @brief  创建图形
         * @param gh
         * @param graphics_type
         * @param layer
         * @param group_name_ 指定组名，不指定时会自动生成组名
         * <AUTHOR>
         * @date 2025.3.6
         */
        void SlotCreateGraphics(std::shared_ptr<GraphicsAbstract>& gh, int graphics_type,
            const std::string& layer, const std::string& group_name_, const  std::shared_ptr<GraphicsAbstract>& father_graphics_);

        void SlotShow3DView(const cv::Mat& model_data, const double& rx, const double& ry, const std::vector<cv::Mat>& texture_datas);

        void SlotCreateTexture(int key, const cv::Mat& img_, int create_layer, int img_index, bool resize_canvas, int current_show_image_key_);

        void SlotCreateImages(const GraphicsImage& graphics_img_);
        /**
       * @fun SlotRevoke
       * @brief 撤销操作
       * <AUTHOR>
       * @date 2024.12.04
       */
        void SlotRevoke();

        /**
       * @fun SlotRecover
       * @brief 恢复操作
       * <AUTHOR>
       * @date 2024.12.04
       */
        void SlotRecover();
        /**
         * @fun SlotShowCenterCrossLine
         * @brief   显示中心线
         * @param state
         * <AUTHOR>
         * @date 2025.1.17
         */
        void SlotShowCenterCrossLine(bool state);

    private:
        /**
         * @fun InitMemeber
         * @brief 初始化变量
         * <AUTHOR>
         * @date 2024.9.26
         */
        void InitMemeber();
        /**
         * @fun InitView
         * @brief 初始化UI界面
         * <AUTHOR>
         * @date 2024.7.17
         */
        void InitView();
        /**
         * @fun InitCallback
         * @brief 初始化回调函数
         * <AUTHOR>
         * @date 2024.7.23
         */
        void InitCallback();

        /**
         * @fun GraphicsCreateCallback
         * @brief 图形创建时回调
         * @param ghs
         * @date 2024.9.20
         * <AUTHOR>
         */
        void GraphicsCreateCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        /**
         * @fun GraphicsUpdateCallback
         * @brief 图形更新时回调
         * <AUTHOR>
         * @date 2024.7.23
         */
        void GraphicsUpdateCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool is_update_graphics_ = true);
        /**
         * @fun GraphicsDeleteCallback
         * @brief 图形删除时回调
         * <AUTHOR>
         * @date 2024.7.23
         */
        void GraphicsDeleteCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        /**
         * @fun GraphicsSelectedCallback
         * @brief 图形选中时回调
         * @param ghs 选中的图形
         * @return
         * <AUTHOR>
         * @date 2024.7.23
         */
        void GraphicsSelectedCallback(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        /**
         * @fun RegionSelectedCallback
         * @brief 区域选择时回调
         * <AUTHOR>
         * @date 2024.08.19
         */
        void RegionSelectedCallback(float x, float y, float w, float h);
    private:
        Ui::Render2dView* ui; ///< ui,现在没派上用场
        jrs3dvision::Jrs3dVisionWindow* jrs_3d_vision_window; ///< vtk3d渲染界面
        std::shared_ptr<Renderer2D> render_manager; ///< opengl2d渲染界面

        jrsdata::LightImageType current_image_index; ///< 当前显示的图像索引
        std::unordered_map<jrsdata::LightImageType, unsigned int> image_index_map; //! 当前需要渲染的工程图片的图像颜色类型和渲染层中的颜色序号映射表 by zhangyuyu 2024.9.24
        std::unordered_map<std::string, jrsdata::LightImageType> image_type_map;//! 从界面上选择图片显示类型到图片类型的映射  by zhangyuyu 2024.9.24
    };
}
#endif // !__JRSRENDER2D_H__
