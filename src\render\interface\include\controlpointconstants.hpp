/*********************************************************************
 * @brief  定义控制点.
 *
 * @file   controlpointconstants.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
**********************************************************************/
#pragma once

#ifndef CONTROL_POINT_CONSTANTS_H
#define CONTROL_POINT_CONSTANTS_H

#include "rvec.hpp"
#include <vector>
/**
 * @brief  控制点类型.
 */
enum class ControlPointType
{
    NONE_POINT = -1,
    CORNER_POINT_LEFT_DOWN,
    CORNER_POINT_LEFT_TOP,
    CORNER_POINT_RIGHT_TOP,
    CORNER_POINT_RIGHT_DOWN,
    WAIST_POINT_LEFT,
    WAIST_POINT_TOP,
    WAIST_POINT_RIGHT,
    WAIST_POINT_DOWN,
    MOVE_POINT,
    ROTATE_POINT,
    CONTOUR_POINT,
    BORDER_POINT
};
/**
 * @brief  控制点组类型.
 */
enum class ControlPointGroupType
{
    NONE_POINT = -1,
    SIZE_POINT,
    CORNER_POINT,
    WAIST_POINT,
    ROTATE_POINT,
    CONTOUR_POINT,
    BORDER_POINT
};

/**
 * @brief 控制点绘制参数.
 * @note HEX的使用方式
 *  uchar blue = hexValue & 0xFF;
 *  uchar green = (hexValue >> 8) & 0xFF;
 *  uchar red = (hexValue >> 16) & 0xFF;
 */
struct ControlPointDraw
{
    float cx = -1;
    float cy = -1;
    int width = -1;
    int height = -1;
    int cursor_shape = -1;
    // unsigned int fill_color_HEX = 0xDFF8EB;
    // ControlPointType type = NONE_POINT;
    bool is_render = false;
    std::vector<Vec2> contours;
    ControlPointDraw(float cx = -1.0f, float cy = -1.0f, int width = -1, int height = -1,
        int cursor_shape = -1, bool is_render = false, std::vector<Vec2> contours = {})
        : cx(cx), cy(cy), width(width), height(height), cursor_shape(cursor_shape),
        is_render(is_render), contours(std::move(contours))
    {}
};

/**
 * @brief 控制点属性
 */
struct ControlAttributes
{
    int type = static_cast<int>(ControlPointType::NONE_POINT); ///< 控制点类型
    int id = -1; ///< 控制点索引
    int link_id = -1; ///< 控制点分组
    ControlAttributes() = default;
    ControlAttributes(int type_, int id_, int link_id = -1) : type(type_), id(id_), link_id(link_id) {}
    bool operator==(const ControlAttributes& other) const
    {
        return this->type == other.type &&
            this->id == other.id &&
        this->link_id == other.link_id;
    }
    bool operator!=(const ControlAttributes& other) const
    {
        return !(*this == other);
    }
};

// class GraphicsAbstract;

class ControlPoint
{
public:
    ControlPoint(const ControlAttributes& attributes_, const ControlPointDraw& cpd_)
        : attributes(attributes_), cpd(cpd_)
    {}

    ControlPoint(int type_, int id_, const ControlPointDraw& cpd_)
        : attributes(type_, id_), cpd(cpd_)
    {}

    ControlPoint(ControlPointType type_, int id_, const ControlPointDraw& cpd_)
        : attributes(static_cast<int>(type_), id_), cpd(cpd_)
    {}

    static ControlPoint Create(ControlPointType type_, int id_, const ControlPointDraw& cpd_)
    {
        return ControlPoint(type_, id_, cpd_);
    }
    static ControlPoint Create(int type_, int id_, const ControlPointDraw& cpd_)
    {
        return ControlPoint(type_, id_, cpd_);
    }
    ControlAttributes attributes;
    ControlPointDraw cpd;
    // GraphicsAbstract *const dographics;
};

#endif // !CONTROL_POINT_CONSTANTS_H
