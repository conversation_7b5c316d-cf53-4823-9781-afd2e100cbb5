#ifndef __PCBPATHPLAN_H__
#define __PCBPATHPLAN_H__

// 自定义
#include "pathplanbase.h"

class JRS_AOI_PLUGIN_API PCBPathPlanning : public PathPlanningBase
{
public:
    // 单个元件输出信息(PCB专有)
    struct ObjectOutput
    {
        std::string name;        /**<需要被分配的信息，即元件的名称 */
        int fovid;               /**< 被分配的FOV的 ID*/
        std::vector<int> fovids; /**<被分配目标被分配到的FOV集合 */
        cv::RotatedRect rotate_rect; /**< 外接矩形*/
    };
    // 单个元件输入信息(PCB专有)
    struct ObjectInput
    {
        std::string name;            /**< 需要被分配的元件信息，即元件的名称*/
        cv::RotatedRect rotate_rect; /**< 外接矩形*/
        // 重载 == 运算符
        bool operator==(const ObjectInput &other) const
        {
            return name == other.name; // 仅根据 name 判断是否相等
        }
    };
    // fov坐标信息(PCB专有)
    struct FovPath
    {
        int fovid = -1;     /**< 分配出来的FOV的ID信息*/
        cv::Point2f center; /**< 中心坐标*/
        FovPath() : fovid(-1), center(0, 0)
        {
        }
        FovPath(int _fovid, cv::Point2f _center) : fovid(_fovid), center(_center)
        {
        }
    };
    // fov规划信息(PCB专有)
    struct Fov
    {
        FovPath fov_path;                             /**< FOV坐标*/
        std::vector<ObjectOutput> covered_rectangles; /**< FOV里面的元件集合*/
        bool have_big_rectangles;                     /**< 包含了一个超出最大范围的矩形,因此位置不能动*/

        Fov() : have_big_rectangles(false) {};
        Fov(FovPath _fovPath, const std::vector<ObjectOutput> &_covered_rectangles, const bool &_haveBig)
            : fov_path(_fovPath), covered_rectangles(_covered_rectangles), have_big_rectangles(_haveBig)
        {
        }
        bool operator==(const Fov &other) const
        {
            return fov_path.center == other.fov_path.center;
        }
    };

    // 重写输入参数的结构体
    struct InputParams : public PathPlanningBase::InputParamsBase
    {
        int region_w;                          ///< 规划区域宽 单位 pix
        int region_h;                          ///< 规划区域高 单位 pix
        int fov_w;                             ///< fov宽 单位 pix
        int fov_h;                             ///< fov高 单位 pix
        PositionPlanMode mode_position;        ///< 位置规划模式(网格或矩形覆盖)
        PathPlanMode mode_path;                ///< 路径规划模式(蛇形或Z字型)
        PhotoStartPosition photo_start;        ///< 拍照起始位置(左上、左下、右上、右下)
        PhotoAxisFirst axis_first;             ///< 拍照先走哪个轴(X轴、Y轴)
        std::vector<ObjectInput> rotate_rects; ///< 规划物体外接矩形
        cv::Point2f start;                     ///< 规划起点 起点不纳入路径点
        bool draw_map;                         ///< 是否绘制路径规划图
        std::string map_path;                  ///< 路径规划图保存位置
        float big_fov_ratio;                   ///< 大视野FOV界定比例系数
        bool is_mark;                          ///< 是否是mark
        bool optimal_path;                     ///< 是否启用最优路径(忽略拍照起始位置，自动计算合适的位置，但是依然考虑拍照先走哪个轴)
        int max_offset;                        ///< FOV规划时允许超出板子的最大范围

        InputParams() : region_w(0),
                        region_h(0),
                        fov_w(0),
                        fov_h(0),
                        mode_position(GRID),
                        mode_path(SNAKE_MODE),
                        photo_start(PhotoStartPosition::LeftTop),
                        axis_first(PhotoAxisFirst::XAxis),
                        rotate_rects(),
                        start(),
                        draw_map(false),
                        map_path("./map.png"),
                        big_fov_ratio(1.0f),
                        is_mark(false),
                        optimal_path(true),
                        max_offset(1000)
        {
        }
    };

    // 重写输出参数的结构体
    struct OutputParams : public PathPlanningBase::OutputParamsBase
    {
        std::vector<Fov> fovs; // fov集合
        bool result;           // 规划结果
    };

    // FOV布局信息结构体，用于描述大矩形对应的FOV布局
    struct FovLayout
    {
        int num_fovs_x;     // 水平方向的FOV数量
        int num_fovs_y;     // 垂直方向的FOV数量
        int max_fov_width;  // 最大FOV宽度（包含重叠部分）
        int max_fov_height; // 最大FOV高度（包含重叠部分）
    };

    // 重写路径规划函数
    std::shared_ptr<OutputParamsBase> PathPlan(const InputParamsBase &input) override;

private:
    // 判断一个矩形是否被FOV覆盖
    bool IsCoveredByFov(const cv::RotatedRect &rect, const cv::Point2f &fovCenter, int fovW, int fovH);

    // 处理网格模式的FOV
    void HandleGridMode(const InputParams &params, std::shared_ptr<OutputParams> output_params);

    // 网格FOV规划
    std::vector<Fov> PlanFovsForGrid(const InputParams &param);

    // 大视野FOV规划
    std::vector<Fov> PlanFovsForBigFov(const std::vector<ObjectInput> &big_rectangles, std::vector<ObjectInput> &small_rectangles, const InputParams& param);

    // 小视野FOV规划(result为规划结果,考虑到可能死在while循环里面,如果检测到死循环则强制结束)
    std::vector<Fov> PlanFovsForSmallFov(const std::vector<ObjectInput> &small_rectangles, const InputParams &param, bool &result);

    // 调整FOV中心点
    void AdjustFOVCenter(std::vector<Fov> & fovs, const InputParams& param);

    // 获取最佳的拍照起点
    PhotoStartPosition GetBestPhotoPosition(std::vector<Fov>& fovs, const InputParams& param);

    // 输入参数检查
    bool ValidateInputParameters(const InputParamsBase &input, std::shared_ptr<OutputParams> output_params);

    // 矩形框分类
    void ClassifyRectangles(const std::vector<ObjectInput> &rectangles, int fov_width, int fov_height, float big_fov_ratio, std::vector<ObjectInput> &big_rectangles, std::vector<ObjectInput> &small_rectangles);

    // 计算FOV布局
    FovLayout CalculateFovLayout(const ObjectInput &rect, int fov_width, int fov_height);

    // 找到覆盖最多小矩形的FOV中心点
    cv::Point2f FindBestFovCenter(const ObjectInput &rect, const std::vector<ObjectInput> &small_rectangles, const FovLayout &layout);

    // 计算FOV覆盖的矩形数量
    int CountCoveredRectangles(const std::vector<ObjectInput> &rectangles, const cv::Point2f &center, int fov_width, int fov_height);

    // 生成FOV并添加到结果中
    void GenerateFovsForBigRectangle(const ObjectInput &rect, std::vector<ObjectInput> &small_rectangles, const InputParams& param, const FovLayout &layout, const cv::Point2f &best_center, std::vector<Fov> &big_fovs);

    // 添加fov覆盖的小矩形
    void AddCoveredRectangles(Fov &fov, std::vector<ObjectInput> &small_rectangles, int fov_width, int fov_height);

    // 添加跨fov的大矩形
    void AddBigRectangle(Fov &fov, const ObjectInput &rect, int start_fov_id, int total_fovs);

    // 根据起点位置调整分组顺序
    void AdjustGroupOrder(std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first);

    // S形FOVS整理
    std::vector<Fov> SortFovsInSnakeMode(const std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first);

    // Z形FOVS整理
    std::vector<Fov> SortFovsInZMode(const std::vector<std::vector<Fov>> &grouped_fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first);

    // mark点的fov规划
    std::vector<Fov> PlanFovsForMarkPoints(const std::vector<ObjectInput> &rectangles, const InputParams &params);

    // 创建mark点的fov
    Fov CreateFovForMarkPoint(const ObjectInput &rect, const InputParams &params, int offset);

    // 找到当前fov覆盖的矩形
    std::pair<std::vector<ObjectInput>, std::vector<ObjectInput>> FindCoveredRectangles(const std::vector<ObjectInput> &rectangles, float current_x, float current_y, int fov_width, int fov_height);

    // 将覆盖的矩形添加进fov
    Fov CreateFovForCurrentRow(const std::vector<ObjectInput> &covered_rects, float current_x, float current_y, const InputParams &params);

    // 从剩余矩形里面移除已经覆盖的矩形
    void RemoveCoveredRectanglesFromRemaining(const std::vector<ObjectInput> &covered_rects, std::vector<ObjectInput> &remaining);

    // 获取旋转矩形的最大y
    float GetMaxYOfRotatedRect(const cv::RotatedRect &rect);

    // 找到元件集合里面最左侧/右侧/顶部/底部的矩形的X或者Y
    cv::Point2f FindMostPoint(const std::vector<ObjectInput> &rects, Direction direction = Direction::Left);

    // 找到元件集合的最小外接矩形(输出的矩形不带角度)
    cv::RotatedRect FindMinBoundingRect(const std::vector<ObjectInput> &rects);
    cv::RotatedRect FindMinBoundingRect(const std::vector<ObjectOutput>& rects);

    // FOV排序
    void SortFovs(std::vector<Fov> &fov, PathPlanMode mode = SNAKE_MODE, PhotoStartPosition photo_start = LeftTop, PhotoAxisFirst axis_first = XAxis, int diff = 500);

    // 找到FOV拍照起点位置
    size_t FindStartFovIndex(const std::vector<Fov>& fovs, PhotoStartPosition photo_start, PhotoAxisFirst axis_first, int diff);

    // 找到下一个FOV的位置
    size_t FindNextFovIndex(const std::vector<Fov>& fovs, const std::vector<bool>& visited, const Fov& last, bool primary_x);

    // 绘制FOV矩形框
    void DrawFovs(std::vector<Fov> &fovs, const InputParams *param);

    // 等比例缩放图片
    void ResizeImagePreserveAspectRatio(const cv::Mat &src, cv::Mat &dst, int width, int height);

    // 比较函数，用于按X或Y排序
    void SortRowByX(std::vector<Fov> &row, bool ascending);
    void SortRowByY(std::vector<Fov> &row, bool ascending);

    // 计算FOV路径的距离
    double CalculateFOVPath(std::vector<Fov>& fovs);

    static bool CompareFovByXAsc(const Fov &a, const Fov &b)
    {
        return a.fov_path.center.x < b.fov_path.center.x;
    }
    static bool CompareFovByYAsc(const Fov &a, const Fov &b)
    {
        return a.fov_path.center.y < b.fov_path.center.y;
    }
    static bool CompareFovByXDesc(const Fov &a, const Fov &b)
    {
        return a.fov_path.center.x > b.fov_path.center.x;
    }
    static bool CompareFovByYDesc(const Fov &a, const Fov &b)
    {
        return a.fov_path.center.y > b.fov_path.center.y;
    }

    // 将FOV集合按偏差进行行/列分组
    std::vector<std::vector<Fov>> GroupFovsByRows(const std::vector<Fov> &fovs, int diff, PhotoAxisFirst axis_first);

private:
    int fovid_;
};

#endif //!__PCBPATHPLAN_H__