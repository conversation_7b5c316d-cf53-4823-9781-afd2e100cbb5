<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EditDetectModelView</class>
 <widget class="QWidget" name="EditDetectModelView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>451</width>
    <height>768</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>EditDetectModelView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <property name="spacing">
    <number>3</number>
   </property>
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout_2" stretch="3,1,10">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout" stretch="0,10">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,0">
         <property name="spacing">
          <number>16</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>6</number>
         </property>
         <item>
          <widget class="QPushButton" name="btn_add_regions">
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>自动加框</string>
           </property>
           <property name="iconSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_add_region">
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>手动加框</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_bind_logical_or">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>逻辑组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_delete_region">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>删除算法</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_unbind_logical">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>删除逻辑</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QVBoxLayout" name="tab_region_layout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QFrame" name="algo_param_frame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="leftMargin">
         <number>2</number>
        </property>
        <property name="topMargin">
         <number>2</number>
        </property>
        <property name="rightMargin">
         <number>2</number>
        </property>
        <property name="bottomMargin">
         <number>2</number>
        </property>
        <property name="spacing">
         <number>2</number>
        </property>
        <item row="0" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QLabel" name="label">
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>参数管控:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="param_ratio_name">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="select_algo_ratio_btn">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(0, 170, 255);</string>
            </property>
            <property name="text">
             <string>应用</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="param_ratio_manager_btn">
            <property name="styleSheet">
             <string notr="true">background-color: rgb(0, 170, 255);</string>
            </property>
            <property name="text">
             <string>参数管控列表</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="std_value_btn">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(0, 170, 255);</string>
            </property>
            <property name="text">
             <string>设置分母</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QStackedWidget" name="stackedWidget_algo_view_list">
       <property name="currentIndex">
        <number>1</number>
       </property>
       <widget class="QWidget" name="page"/>
       <widget class="QWidget" name="page_2"/>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
