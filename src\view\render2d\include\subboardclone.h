/*********************************************************************
 * @brief  多联板克隆.
 *
 * @file   subboardclone.h
 *
 * @date   2024.10.10
 * <AUTHOR>
 *********************************************************************/
#ifndef SUBBOARD_CLONE_H
#define SUBBOARD_CLONE_H

#include "render2deventparam.hpp"
namespace jrsaoi
{
    class MutliCopy
    {
    public:
        /**
         * @brief  创建子板.
         * @param[in]  param  创建子板参数.
         */
        static int CreateSub(SubBoardCloneParam& param);
    };
}
#endif // SUBBOARD_CLONE_H