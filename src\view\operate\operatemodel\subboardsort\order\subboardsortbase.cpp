
#include "subboardsortbase.h"

namespace subboardsort
{
    void SubboardSortBase::UpdateSubboardInfoOfComponents(std::vector<jrsdata::Component>& components_, const std::string& subboard_name_, int subboard_id_)
    {
        auto new_sub_id_str = std::to_string(subboard_id_);
        for (auto& component : components_)
        {
            if (component.component_name.empty())///< 元件名称为空则跳过
            {
                continue;
            }
            auto& name = component.component_name; // 元件id需要外部生成
            name = jrscore::AOITools::GetPrefixString(name) + "_" + new_sub_id_str;
            component.subboard_name = subboard_name_;
        }
    }

    void SubboardSortBase::UpdateSubboardInfoOfComponent(jrsdata::Component& component_, const std::string& subboard_name_, int subboard_id_)
    {
        if (component_.component_name.empty() && component_.subboard_name.empty())///< 元件名称为空则跳过
        {
            return;
        }
        auto& name = component_.component_name; // 元件id需要外部生成
        name = jrscore::AOITools::GetPrefixString(name) + "_" + std::to_string(subboard_id_);;
        component_.subboard_name = subboard_name_;
    }

    void SubboardSortBase::UpdateSubboard(jrsdata::SubBoard& subboard_, int subboard_id_)
    {
        subboard_.id = subboard_id_;
        subboard_.subboard_name = jrscore::AOITools::GetPrefixString(subboard_.subboard_name) + "_" + std::to_string(subboard_id_);
        UpdateSubboardInfoOfComponents(subboard_.component_info, subboard_.subboard_name, subboard_id_);

        UpdateSubboardInfoOfComponent(subboard_.barcode, subboard_.subboard_name, subboard_id_);
        UpdateSubboardInfoOfComponent(subboard_.bad_mark, subboard_.subboard_name, subboard_id_);
        UpdateSubboardInfoOfComponents(subboard_.sub_mark, subboard_.subboard_name, subboard_id_);
    }


}