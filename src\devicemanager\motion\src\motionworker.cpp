#include "motionworker.h"


namespace jrsdevice 
{


	MotionWorker::MotionWorker()
		: io_context_(),
		  socket_(io_context_)
	{
		connected_ = false;
		last_error = "";
		server_address = "127.0.0.1";
		server_port = 88888;
	}

	MotionWorker::~MotionWorker()
	{
		asio::error_code ec;
		if (socket_.is_open())
		{
			socket_.shutdown(tcp::socket::shutdown_both, ec); // 先关闭读写
			socket_.close(ec);                                // 再关闭socket
		}
		io_context_.stop(); // 停止io_context（如果运行）
	}

	int MotionWorker::InitClient(const std::string ip, const int port)
	{
		// 解析服务器地址和端口
		tcp::resolver resolver(io_context_);
		auto endpoints = resolver.resolve(ip, std::to_string(port));
		try
		{
			server_address = ip;
			server_port = port;
			// 连接到服务器
			asio::connect(socket_, endpoints);
			connected_ = true;
		}
		catch (const std::exception &e)
		{
			connected_ = false;
			last_error = e.what();
			return -1;
		}

		return 0;
	}

	bool MotionWorker::GetIsConnected()
	{
		return connected_;
	}

	std::string MotionWorker::GetLastError()
	{
		return last_error;
	}

	bool MotionWorker::Send(const std::string msg)
	{
		if (!connected_)
		{
			if (InitClient(server_address, server_port) != 0)
			{
				return false;
			}
		}
		try
		{
			asio::write(socket_, asio::buffer(msg));
		}
		catch (const asio::system_error &e)
		{
			last_error = e.what();
			return false;
		}
		return true;
	}

	std::string MotionWorker::Receive(const int timeout)
	{
		std::string receive = "";
		// 连接判断
		if (!connected_ || !socket_.is_open())
		{
			if (InitClient(server_address, server_port) != 0)
			{
				return receive;
			}
		}

		// 对粘包进行处理
		if (motion_msg.size() > 0)
		{
			receive = motion_msg.front();
			motion_msg.pop();
			return receive;
		}

		// 设置超时（非阻塞方式,这样read_some函数就不会阻塞）
		socket_.non_blocking(true); // 设置为非阻塞模式

		char reply[1024 * 100];
		size_t length = 0;
		auto start = std::chrono::system_clock::now();
		asio::error_code error;
		while (true)
		{
			length = socket_.read_some(asio::buffer(reply), error);

			if (!error)
			{
				receive.append(reply, length);

				// 检查是否接收到完整的消息  
				if (receive.length() > 2 && receive.substr(receive.length() - 2) == "\r\n")
				{
					break;
				}
			}
			else if (error == asio::error::would_block || error == asio::error::try_again)
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(1));
				// 超时也退出
				auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start).count();
				if (duration > timeout)
				{
					break;
				}
			}
			else
			{
				// 发生错误（可能是socket被关闭）
				connected_ = false;
				last_error = error.message();
				break;
			}
		}

		std::string msg = receive;
		if (msg == "" || msg == "\r\n")
		{
			return receive;
		}

		std::vector<std::string> strSplit = SplitString(msg, "\r\n");
		receive = strSplit[0];
		// 处理粘包
		if (strSplit.size() > 1)
		{
			for (int i = 1; i < strSplit.size(); i++)
			{
				if (strSplit[i] != "" && strSplit[i] != "\r\n")
				{
					motion_msg.push(strSplit[i]);
				}
			}
		}
		
		return receive;
	}

	std::vector<std::string> MotionWorker::SplitString(const std::string &str, const std::string delimiter)
	{
		std::vector<std::string> lines;
		size_t start = 0;
		size_t end = 0;

		while ((end = str.find(delimiter, start)) != std::string::npos)
		{
			lines.push_back(str.substr(start, end - start));
			start = end + delimiter.length(); // 跳过\r\n
		}

		// 添加最后一个元素（如果有的话）
		if (start < str.length())
		{
			lines.push_back(str.substr(start));
		}

		return lines;
	}

}
