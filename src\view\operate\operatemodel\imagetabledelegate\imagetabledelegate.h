/*****************************************************************//**
 * @file   imagedelegate.h
 * @brief 模板图片列表Item图片按照等比例缩放
 *
 * <AUTHOR>
 * @date   2024.9.9
 *********************************************************************/
#ifndef IMAGETABLEDELEGATE_H
#define IMAGETABLEDELEGATE_H

#include <map>

#include <QStyledItemDelegate>
#include <QPainter>
#include <QImage>

class ImageTableDelegate : public QStyledItemDelegate
{
    Q_OBJECT
public:
    /**
     * @fun ImageTableDelegate
     * @brief 构造函数，初始化 ImageTableDelegate
     * @details 初始化自定义的表格项委托，设置父对象
     * @param parent 父对象
     * @date 2025.02.25
     * <AUTHOR>
     */
    ImageTableDelegate(QObject* parent = nullptr);
    /**
     * @fun paint
     * @brief 自定义绘制表格项等比例缩放
     * @details 根据项的状态绘制背景颜色，并在选中或悬浮时绘制边框
     * @param painter QPainter 对象，用于绘制
     * @param option 项的样式选项
     * @param index 项的模型索引
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const override;
    /**
     * @fun GetColorIndex
     * @brief 根据项的状态获取颜色索引
     * @param index 项的模型索引
     * @return int 颜色索引
     * @date 2025.02.25
     * <AUTHOR>
     */
    int GetColorIndex(const QModelIndex& index) const;
    /**
     * @fun DrawSelectionBorder
     * @brief 绘制选中或悬浮状态的边框
     * @param painter QPainter 对象，用于绘制
     * @param option 项的样式选项
     * @param rect 项的矩形区域
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void DrawSelectionBorder(QPainter* painter, const QStyleOptionViewItem& option, const QRect& rect) const;

private:
    /// 颜色集合
    const std::map<int, QColor> color_map =
    {
        {0, QColor(200, 200, 200)},
        {1, QColor(255, 255, 255)},
        {2, QColor(255, 0, 0)},
        {3, QColor(0, 255, 0)},
        {4, QColor(0, 0, 255)},
    };
};
#endif