# 子仓库列表,分别是：
# 2.0三方文件仓库
# 2.0算法仓库
# 2.0结构光成像配置仓库
set(sub_repositories
    "aoi2.0-thridparty http://gitlab.szjieruisi.com:8075/ipd/ipd-3daoi/3d-aoi-2.0/aoi2.0-thridparty.git main ${CMAKE_SOURCE_DIR}/thirdparty"
    "AlgoPublish http://gitlab.szjieruisi.com:8075/ipd/ipd-3daoi/3d-aoi-2.0/algopublish.git main ${CMAKE_SOURCE_DIR}/thirdparty/algo/algoplugin"
    "structlightcameraconfig http://gitlab.szjieruisi.com:8075/ipd/ipd-3daoi/3d-aoi-2.0/structlightcameraconfig.git main ${CMAKE_SOURCE_DIR}/config/structlightconfig"
    
)

option(UPDATE_SUBREPOSITORIES "Update subrepositories during CMake configure" OFF)

# 函数：检查并更新仓库
function(check_and_update_repository NAME REPO_URL BRANCH LOCAL_PATH)
    # 检查本地路径是否存在
    if (NOT EXISTS ${LOCAL_PATH})
        message(STATUS "Cloning ${NAME} into ${LOCAL_PATH}")
        execute_process(
            COMMAND git clone --branch ${BRANCH} ${REPO_URL} ${LOCAL_PATH}
            RESULT_VARIABLE CLONE_RESULT
            OUTPUT_VARIABLE CLONE_OUTPUT
            ERROR_VARIABLE CLONE_ERROR
        )
        if (NOT CLONE_RESULT EQUAL 0)
            message(FATAL_ERROR "Failed to clone ${NAME} from ${REPO_URL}: ${CLONE_ERROR}")
        endif ()
    else ()
        # 拉取最新的状态
        message(STATUS "Updating ${NAME} in ${LOCAL_PATH}")
        execute_process(
            COMMAND git pull origin ${BRANCH}
            WORKING_DIRECTORY ${LOCAL_PATH}
            RESULT_VARIABLE PULL_RESULT
            OUTPUT_VARIABLE PULL_OUTPUT
            ERROR_VARIABLE PULL_ERROR
        )
        if (NOT PULL_RESULT EQUAL 0)
            message(FATAL_ERROR "Failed to pull updates for ${NAME}: ${PULL_ERROR}")
        else ()
            message(STATUS "${NAME} has been updated: ${PULL_OUTPUT}")
        endif ()
    endif ()
endfunction()

# 只有当选项开启时才更新子仓库
if(UPDATE_SUBREPOSITORIES)
    # 遍历并处理所有子仓库
# 遍历并处理所有子仓库
foreach(repository ${sub_repositories})
    message(STATUS "Processing repository: ${repository}")

    # 确保子仓库信息被正确分割成多个部分
    string(REPLACE " " ";" repository_parts ${repository})
    list(LENGTH repository_parts repo_parts_len)

    # 检查分割后的仓库信息长度
    if (repo_parts_len EQUAL 4)
        list(GET repository_parts 0 NAME)
        list(GET repository_parts 1 REPO_URL)
        list(GET repository_parts 2 BRANCH)
        list(GET repository_parts 3 LOCAL_PATH)

        check_and_update_repository(${NAME} ${REPO_URL} ${BRANCH} ${LOCAL_PATH})
    else ()
        message(FATAL_ERROR "Invalid repository format for ${repository}")
    endif ()
endforeach()
else()
    #message(STATUS "Skipping subrepository updates (UPDATE_SUBREPOSITORIES is OFF)")
endif()