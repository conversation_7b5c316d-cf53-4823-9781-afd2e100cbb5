/*****************************************************************//**
 * @file   viewtool.hpp
 * @brief  界面工具类，用于实现一些小工具，如打开文件等
 * @details
 * <AUTHOR>
 * @date 2024.7.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.23         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSVIEWTOOL_H__
#define __JRSVIEWTOOL_H__

#include <QFileDialog>

#ifdef Q_OS_WIN
#include <windows.h>
#endif
 // 打开单个文件
inline QString OpenSingleFile(const QString& title = "Open File", const QStringList& filter = { "All Files (*)" }, const QString& default_path = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setFileMode(QFileDialog::ExistingFile);
    dialog.setNameFilters(filter);
    dialog.setDirectory(default_path);

    if (dialog.exec())
    {
        return dialog.selectedFiles().first();
    }
    else
    {
        return QString();
    }
}

// 打开多个文件
inline QStringList openMultipleFiles(const QString& filter = "All Files (*)", const QString& defaultPath = "") {
    QFileDialog dialog;
    dialog.setWindowTitle("Open Files");
    dialog.setFileMode(QFileDialog::ExistingFiles);
    dialog.setNameFilter(filter);
    dialog.setDirectory(defaultPath);

    if (dialog.exec()) {
        return dialog.selectedFiles();
    }
    else {
        return QStringList();
    }
}

// 打开文件夹
inline QString openFolder(const QString& defaultPath = "") {
    QFileDialog dialog;
    dialog.setWindowTitle("Open Folder");
    dialog.setFileMode(QFileDialog::Directory);
    dialog.setOption(QFileDialog::ShowDirsOnly);
    dialog.setDirectory(defaultPath);

    if (dialog.exec()) {
        return dialog.selectedFiles().first();
    }
    else {
        return QString();
    }
}

// 保存单个文件
inline QString saveSingleFile(const QString& title = "Open File", const QString& filter = "All Files (*)", const QString& defaultPath = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setAcceptMode(QFileDialog::AcceptSave);
    dialog.setNameFilter(filter);
    dialog.setDirectory(defaultPath);

    if (dialog.exec()) {
        return dialog.selectedFiles().first();
    }
    else {
        return QString();
    }
}

/**
 * @fun GetExeVersion 
 * @brief 获取exe版本信息
 * @return  返回版本信息
 * <AUTHOR>
 * @date 2025.5.17
 */
inline QString GetExeVersion()
{
    #ifdef Q_OS_WIN
    TCHAR szFileName[MAX_PATH] = {0};
    GetModuleFileName(nullptr, szFileName, MAX_PATH);

    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(szFileName, &verHandle);
    if (verSize == 0) return "Unknown";

    std::vector<char> verData(verSize);
    if (!GetFileVersionInfo(szFileName, verHandle, verSize, verData.data()))
        return "Unknown";

    VS_FIXEDFILEINFO* fileInfo = nullptr;
    UINT size = 0;
    if (!VerQueryValue(verData.data(), TEXT("\\"), reinterpret_cast<LPVOID*>(&fileInfo), &size))
        return "Unknown";

    if (size == 0) return "Unknown";

    int major = HIWORD(fileInfo->dwFileVersionMS);
    int minor = LOWORD(fileInfo->dwFileVersionMS);
    int patch = HIWORD(fileInfo->dwFileVersionLS);
    int build = LOWORD(fileInfo->dwFileVersionLS);

    return QString("%1.%2.%3.%4").arg(major).arg(minor).arg(patch).arg(build);
    #else
    return "Unsupported OS";
    #endif
}
#endif // !__JRSVIEWTOOL_H__
