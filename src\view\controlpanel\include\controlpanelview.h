﻿/*****************************************************************//**
 * @file   controlpanelview.h
 * @brief  主界面上控制面板view类
 * @details   主界面上软件运行停止/运行状态显示/打开硬件/信息/切换用户面板的view类
 * <AUTHOR>
 * @date 2024.1.17
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.17         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __CONTROLCONSOLEVIEW_H__
#define __CONTROLCONSOLEVIEW_H__

#include "viewbase.h"
QT_BEGIN_NAMESPACE
namespace Ui { class controlpanelview; };
QT_END_NAMESPACE

namespace jrsaoi
{
    struct ImplData;
    class ControlPanelView : public ViewBase
    {
        Q_OBJECT

    public:
        ControlPanelView(const std::string& name, QWidget* parent = nullptr);
        ~ControlPanelView();
        virtual int Init() override;
        Q_INVOKABLE virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_)override;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
    public slots:
        /**
         * 自动运行面板更新
         */
        void SlotAutoRunPanelUpdate(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun SlotOnlineDebug 
         * @brief 在线调试按钮槽函数
         * <AUTHOR>
         * @date 2025.4.8
         */
        void SlotOnlineDebug();

    signals:
        /**
         * 发送到event 的事件
         */
        void SigControlPanelUpdate(const jrsdata::ViewParamBasePtr& param_);
    private:
        //Member
        ImplData* _impl_data;
        //Fun
        void InitView();
        void InitMember();
        void InitConnect();
    };
}

#endif // !__CONTROLCONSOLEVIEW_H__
