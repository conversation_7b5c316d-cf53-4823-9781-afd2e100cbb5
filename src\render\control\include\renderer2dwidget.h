﻿/**********************************************************************
 * @brief  渲染器主UI.
 *
 * @file   renderer2dwidget.h
 * @note   这个类的主要意义是拦截事件然后对事件进行分发
 * @date   2024.07.08
 * <AUTHOR>
 * @todo   去除ui文件
 *********************************************************************/
#pragma once

#ifndef RENDERER_2D_WIDGET_H
#define RENDERER_2D_WIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class Renderer2DWidgetClass;
};
QT_END_NAMESPACE

class Renderer2DWidget : public QWidget
{
    Q_OBJECT

public:
    explicit Renderer2DWidget(QWidget* parent = nullptr);
    ~Renderer2DWidget();
    void AddImageRender(QWidget* widget_);
    void AddRenderer(QWidget* widget);
    void AddRulerHorizontal(QWidget* widget);
    void AddRulerVertical(QWidget* widget);
signals:
    void SigUpdateZoom();
    void SignalChangeUseState();
protected:
    /**
     * @note   事件过滤器
     */
    bool eventFilter(QObject* target, QEvent* event) override;

    void showEvent(QShowEvent* event) override;
    void moveEvent(QMoveEvent* event)override;
private:
    Ui::Renderer2DWidgetClass* ui;
    QWidget* _image_render;        ///<图像渲染
    QWidget* renderer;            ///< 渲染器
    QWidget* ruler_horizontal;    ///< 水平标尺
    QWidget* ruler_vertical;      ///< 垂直标尺
};

#endif // !RENDERER_2D_WIDGET_H
