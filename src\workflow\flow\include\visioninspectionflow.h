/*****************************************************************
 * @file   visioninspectionflow.h
 * @brief  视觉检测流程
 * @details 在ARP中主要功能是MARK矫正+拍照+检测，进入该流程后调用相机拍照，在拍照的同时对接收到的FOV数据进行检测
 * <AUTHOR>
 * @date 2024.11.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.21          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSVISIONINSPECTIONFLOW_H__
#define __JRSVISIONINSPECTIONFLOW_H__
 //PREBUILD
#include "workflowpch.h"
 //STD
#include <functional>
//Custom
//#include "workflowinterfaces.h"
#include "projectdataprocess.h"
#include "parameterprocess.hpp"
#include "calcfov.h"
#include "fovplan.h"
#include "pcbpathplan.h"
#include "jrsthreadpool.hpp"
//Third
#include "opencv2/opencv.hpp"

namespace jrsalgo
{
    class AlgorithmEngineManager;
}

namespace jrsworkflow
{

    class VisionInspectionFlow : public IVisionInspectionFlow
    {
    public:
        explicit VisionInspectionFlow(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_, LogicFunType logic_invoke_);
        virtual ~VisionInspectionFlow();
        int StartInspection(const std::shared_ptr<jrsdata::ProjectParam>& project_param_) override;
        void StopInspection() override;
        void AddBuffer(const jrsdata::JrsImageBuffer& imgs) override;
        InspectionResultBasePtr GetInspectionResult() override;
        void StopAllTasks() override;
        void WaitAllTasks() override;
        void SetWorkFlowParam(const WorkFlowParam & param_)override;
    private:

        //Member
        void Init();

        /**
        * @fun AssignComponentToFov
        * @brief 将元件分配到指定的 FOV中
        * @param fov_centers [OUT] 分配结果
        * @param board       [IN] 当前板子信息
        * @param fov_w       [IN] FOV的宽
        * @param fov_h       [IN] FOV的高
        * <AUTHOR>
        * @date 2024.11.3
        */
        bool AssignComponentToFov(std::vector<PCBPathPlanning::Fov>& fov_centers, const jrsdata::Board& board,  int fov_w,  int fov_h,bool is_mark);
        
        /*
        * @fun CalculateDetectComponents
        * @brief 计算需要检测的元件,因为有些元件没有检测框，所以不用检测
        * @param board [IN] 当前板子信息
        * @return 元件列表
        * <AUTHOR>
        * @date 2025.4.1
        */
        std::vector<jrsdata::Component> CalculateDetectComponents(const jrsdata::Board& board);
        /**
         * @fun DetectFOV
         * @brief 检测FOV中元件
         * @param fov_img [IN] 当前FOV的图像
         * <AUTHOR>
         * @date 2024.11.24
         */
        void DetectFOV(const jrsdata::JrsImageBuffer& fov_img);
  
         /**
          * @fun DetectComponentsInFov
          * @brief 检测FOV中的所有元件
          * @param fov_img_ [IN] 当前FOV的图像
          * @param fov_center_ [IN] 当前FOV的中心点
          * @param components_in_fov_ [IN] 当前FOV中的所有元件
          * <AUTHOR>
          * @date 2024.11.24
          */
        void DetectComponentsInFov(const jrsdata::JrsImageBuffer& fov_img_, const cv::Point2f& fov_center_, const std::vector<jrsdata::Component>& components_in_fov_);

        /**
          * @fun DetectComponent
          * @brief 检测单个元件
          * @param component [IN] 当前元件
          * <AUTHOR>
          * @date 2024.12.16
          */
        void DetectComponent(const jrsdata::JrsImageBuffer& fov_img_, const jrsdata::Component& component, int fov_left_top_x, int fov_left_top_y);
   
        constexpr const char* TypeToString(jrsdata::Component::Type type)
        {
            constexpr const char* type_strings[] = {
                "CAD",
                "MARK",
                "BARCODE",
                "BADMARK",
                "SUB_MARK",
                "SUB_BARCODE",
                "SUB_BADMARK",
                "CARRIER_BARCODE",
                "COVERPLATE_BARCODE"
            };
            return type_strings[static_cast<uint8_t>(type)];
        }

        /**
         * @fun MarkCorrection 
         * @brief Mark矫正
         * @return 成功返回AOI_OK,失败返回错误码
         * <AUTHOR>
         * @date 2024.12.31
         */
        int MarkCorrection();
 
        /**
         * @fun ComponentInspection 
         * @brief 元件检测
         * @return 成功返回AOI_OK,失败返回错误码
         * <AUTHOR>
         * @date 2025.1.2
         */
        int ComponentInspection();

      
        /**
         * @fun SaveAlgoResult 
         * @brief 获取算法检测结果,用于保存
         * @param algo_detect_result_[IN]
         * @param detect_window_info_[IN]
         * <AUTHOR>
         * @date 2025.1.2
         */
        void SaveAlgoResult(const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const jrsdata::DetectWindow detect_window_info_,
            jrsdata::DetectWindowResult& detect_window_result);
        /**
         * @fun SaveAlgoWindowResult 
         * @brief 保存检测框的Rect
         * @param algo_detect_result_
         * @param detect_window_info_
         * @param detect_window_result
         * @date 2025.5.12
         * <AUTHOR>
         */
        void SaveAlgoWindowResult(const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const jrsdata::DetectWindow detect_window_info_,
            jrsdata::DetectWindowResult& detect_window_result);
        /**
        * @fun SaveAlgoExecuteParamInfo
        * @brief 保存算法执行参数信息，用于给算法部门调试现场数据
        * @param device_result_[IN] 元件检测结果
        * @param algo_detect_result_[IN] 算法检测结果
        * <AUTHOR>
        * @date 2025.1.13
        */
        void SaveAlgoExecuteParamInfo(jrsdata::DeviceResult& device_rsult_, const jrsoperator::OperatorParamBasePtr& algo_detect_result_,
            const std::string& algo_name_,const std::string sub_board_name_,const std::string algo_param_);

        /**
         * @fun ParapareComponentInspectionParam 
         * @brief  准备元件检测参数
         * @param component_value[IN] 当前检测的元件
         * @param detect_win_execute_param[OUT] 检测窗执行参数
         * @param matrix_to_src_image[OUT] 图像转换矩阵，用于将FOV中的检测框信息转换到原图上
         * @param execute_mode_info [OUT]执行模式
         * <AUTHOR>
         * @date 2025.2.21
         */
        void ParapareComponentInspectionParam(const jrsdata::Component& component_value, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param,cv::Mat& matrix_to_src_image,jrsparam::ExecuteModeInfo& execute_mode_info);
        
        std::pair<cv::Mat ,std::vector<cv::Mat>>GetLoactionModelMat(const jrsdata::Component& component_value,std::unordered_map<std::string,jrsdata::DetectModel>&detect_models, 
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,cv::Mat& res_src_matrix, jrsdata::DeviceResult& component_result);
        /**
         * @fun ProcessDetectWindow 
         * @brief 处理当前元件的所有检测框
         * @param component_value [IN] 当前检测的元件
         * @param detect_win_execute_param[OUT] 元件的所有检测框参数
         * @param component_result [OUT] 元件的检测结果
         * <AUTHOR>
         * @date 2025.2.21
         */
        void ProcessDetectWindows(std::unordered_map<std::string, jrsdata::DetectModel> detect_models, const jrsdata::Component& component_value,
            const cv::Mat correction_matrix, std::vector<cv::Mat>& base_plane_mask_,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param, jrsdata::DeviceResult& component_result,
            const cv::Mat& res_src_matrix);
        bool ExecuteDetections(const std::vector<std::vector<jrsdata::DetectWindow>>& sorted_detect_wins,
            std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
            const cv::Mat& correction_matrix,
            const cv::Mat& trans_to_board_matrix,
            std::vector<cv::Mat>& base_plane_mask_,
            std::unordered_map<int, cv::Mat>& result_trans_matrixes,
            const jrsdata::Component& component_temp, jrsdata::DeviceResult& component_result);
        /**
         * @fun PlanDetectPath
         * @brief 规划检测路径，这个路径是为了自动检测时拍照路径
         * @param components [IN] 所有元件信息
         * <AUTHOR>
         * @date 2025.1.22
         */
        bool PlanDetectPath(const std::vector<jrsdata::Component>& components,const jrsdata::Board& board,int fov_w,int fov_h, bool ismark = false);

        /**
        * @fun SetComponentFovId
        * @brief 设置每个元件的FOV ID,用于fov图片传输过来时，可以快速找到这个fov中有哪些元件需要检测
        * @param components [IN] 所有元件信息
        * <AUTHOR>
        * @date 2025.1.22
        */
        void SetComponentFovId( std::vector<jrsdata::Component>& components);

        /**
         * @fun BoardInspectionFinished 
         * @brief 板子检测结束
         * <AUTHOR>
         * @date 2025.2.21
         */
        void BoardInspectionFinished();
        
        /**
         * @fun ComponentInspectionFinished 
         * @brief
         * @param component_value [IN] 当前的元件
         * @param component_result [OUT] 当前的元件检测结果
         * @param fov_img [IN] 当前的FOV图像
         * @param fov_left_top_x [IN] FOV左上角的x坐标(在大图中的坐标像素)
         * @param fov_left_top_y [IN] FOV左上角的y坐标(在大图中的坐标像素)
         * <AUTHOR>
         * @date 2025.2.21
         */
        void ComponentInspectionFinished(const jrsdata::Component& component_value,jrsdata::DeviceResult& component_result, const jrsdata::JrsImageBuffer& fov_img,int fov_left_top_x, int fov_left_top_y);
        
        /**
         * @fun SaveSubBarcode 
         * @brief 保存子板barcode
         * @param result_ptr [IN] 检测结果
         * @param sub_id [IN] 子板ID
         * @param algo_name [IN] 算法名称
         * <AUTHOR>
         * @date 2025.4.1
         */
        void SaveSubBarcode(std::shared_ptr<jrsoperator::OperatorParamBase> result_ptr,int sub_id,const std::string& algo_name);
        
        /**
         * @fun SaveBadSubboardId 
         * @brief 保存坏板id信息
         * @param result_ptr [IN] 检测结果
         * @param sub_id [IN] 子板ID
         * @param algo_name [IN] 算法名称
         * <AUTHOR>
         * @date 2025.5.8
         */
        void SaveBadSubboardId(std::shared_ptr<jrsoperator::OperatorParamBase> result_ptr,int sub_id,const std::string& algo_name);

        /**
         * @fun GetStartPosition
         * @brief 根据(x,y)在板子上位置，确定该位置靠近板子的哪个角
         * @param x [IN] 位置x
         * @param y [IN] 位置y
         * @param board_width [IN] 板子宽度
         * @param board_height [IN] 板子高度
         * <AUTHOR>
         * @date 2025.5.29
         */
        PCBPathPlanning::PhotoStartPosition GetStartPosition(int x, int y, int board_width, int board_height);

        //Member
        int fov_count;
        WorkFlowParam work_flow_param; /**< 工作流参数*/
        PCBPathPlanning::OutputParams fov_out; /**< fov 规划的结果*/
        LogicFunType logic_invoke; /**< 逻辑层回调函数，用于拍图*/ //TODO:后期需要将拍图功能封装，直接在检测流程中调用 by zhangyuyu 2024.11.23 
        ICorrectFlowPtr correct_flow_controller_ptr;//! 矫正流程控制器
        JrsThreadPoolPtr jrs_thread_pool;   //! 增加流程执行中的变量 by baron_zhang 2024.12.17
        FlowInspectionResultParamPtr flow_inspection_result; /**< 检测结果,以整板为单位 */
        jrsparam::ProjectDataProcessPtr project_data_process_ptr;/**< 工程数据处理指针*/
        jrsparam::ParameterProcessPtr parameter_process_ptr; /**< 参数解析处理指针*/
        std::condition_variable detect_cv; /**<板子检测变量*/
        std::atomic<bool> is_detect_finish;/**< 整板检测是否结束 */
        std::atomic<bool> is_mark_inspection; /**< 表示是否为mark检测 */
        std::unordered_map<int/*FOV的ID*/, std::vector<jrsdata::Component>/*当前FOV中的所有元件*/> assign_component_res;/**< 元件在哪个FOV中的分配结果*/
        std::vector<PCBPathPlanning::Fov> inspection_fov_centers; //! 检测框的FOV路径
        std::vector<PCBPathPlanning::Fov> mark_fov_centers; //! mark的FOV路径
        std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_manager; /**< 算法引擎管理指针*/
        std::shared_ptr<jrsdata::ProjectParam> project_param_ptr; //! 工程参数
        std::mutex result_mutex;
        std::mutex detect_mtx;     /**< 检测锁*/

        //保存所有元件检测结果值 key:元件名 value：元件中所有检测框结果(key:检测框名，value:检测框结果)
        std::unordered_map<std::string, std::unordered_map<std::string, jrsoperator::OperatorParamBasePtr>> work_flow_component_result;


        PCBPathPlanning::Fov last_mark_fov;
    };



}

#endif // !__JRSVISIONINSPECTIONFLOW_H__