﻿/*********************************************************************
 * @brief  状态管理器,用于汇总并控制其余管理器.
 *
 * @file   statemanager.h
 *
 * @date   2024.03.11
 * <AUTHOR>
**********************************************************************/
#pragma once

#ifndef STATE_MANAGER_0311_H
#define STATE_MANAGER_0311_H

#include "graphicsid.h" // GraphicsID
#include "delegate.hpp" // Event
#include "controlconstants.hpp" // VisionMode
#include "graphicsparam.hpp"
#include <vector>
#include <memory>

namespace cv { class Mat; };
class QWidget;
class GraphicsManager;
class ControlPointManager;
class Renderer2DManager;
class CommandManager;
class CustomCursorManager;
class GraphicsAbstract;
class LayerConfig;
struct MouseEventValue;

class StateManager
{
public:
    explicit StateManager();
    ~StateManager();

    QWidget* GetWidget();
    void Undo();
    void Redo();

    void Update();
    void Clear();
    /**
     * @fun    ClearGraphics
     * @brief  清空图形.
     * @date   2024.01.16
     * <AUTHOR>
     */
    void ClearGraphics(bool invoke_callback = true);
    /**
     * @fun ClearGraphics
     * @brief 除了except_layer 以外的图层全部清除掉
     * @param except_layer
     * @param invoke_callback
     * <AUTHOR>
     * @date 2024.12.9
     */
    void ClearGraphics(const std::string& except_layer, bool invoke_callback = true);
    /**
     * @fun    DeleteGraphicsWithLayer
     * @brief  清空指定图层下的图形.
     *
     * @param  layer 指定图层
     * @date   2024.01.16
     * <AUTHOR>
     */
    void DeleteGraphicsWithLayer(const std::string& layer, bool invoke_callback_);
    /**
     * @fun DeletePadGroups
     * @brief 清除掉所有的pad组
     * <AUTHOR>
     * @date 2025.2.26
     */
    void DeletePadGroups();
    /**
     * @fun    GetAllGraphics
     * @brief  取得所有图形.
     *
     * @note
     * 这里做了拷贝以避免同步问题,因此对本拷贝做的任何操作无法应用
     * 应通过id号取得图形后进行覆盖
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetAllGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs) const;
    /**
     * @fun    GetAllGraphics
     * @brief  取得所有图形的序列化字符串.
     *
     * @param  str [out]序列化字符串
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetAllGraphics(std::string& str) const;
    /**
     * @fun    GetLayerGraphics
     * @brief  取得指定图层下的图形.
     *
     * @param  ghs [out]图形序列
     * @param  layer 指定图层,为空表示使用当前图层
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetLayerGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer = "") const;
    /**
     * @fun    GetCurrentSelectedGraphics
     * @brief  取得当前选中图形.
     *
     * @param  ghs [out]图形序列
     * @param  layer 指定图层,为空表示使用当前图层
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetCurrentSelectedGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer = "");
    /**
     * @fun    GetCurrentSelectedGraphicsSingle
     * @brief  取得当前选中图形.
     * @param  gh [out]图形
     * @param  layer 指定图层,为空表示使用当前图层
     * @return 正常返回0,否则返回错误码
     *
     * @note
     *   如果当前选中的图形数量不是1,也返回nullptr
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer = "");
    /**
     * @fun    GetGraphics
     * @brief  按id获取图形.
     *
     * @param  ghs [out]图形序列
     * @param  ids 图形id序列
     * @return 正常返回0,否则返回错误码
     * @note
     * 批量处理时使用哈希表优化
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids) const;
    /**
     * @fun    GetGraphics
     * @brief  按id获取图形.
     *
     * @param  gh [out]图形
     * @param  id 图形id
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetGraphics(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id) const;
    /**
     * @fun    DeleteGraphics
     * @brief  按id删除图形.
     *
     * @param  ids 图形id序列
     * @return 正常返回0,否则返回错误码
     * @note
     *   批量处理时使用哈希表优化
     * @date   2024.01.16
     * <AUTHOR>
     */
    int DeleteGraphics(const std::vector<GraphicsID>& ids);
    /**
     * @fun    AddGraphics
     * @brief  添加图形.
     *
     * @param  ghs [in]图形序列
     * @param  layer  图层名称 不为空时将图形全部添加到该图层
     * @param  invoke_callback 是否执行回调函数
     * @return 正常返回0,否则返回错误码
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int AddGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer = "", bool invoke_callback = true);
    /**
     * @fun    AddGraphics
     * @brief  添加图形.
     *
     * @param  gh [in]图形
     * @param  layer  图层名称 不为空时将图形全部添加到该图层
     * @param  invoke_callback 是否执行回调函数
     * @return 正常返回0,否则返回错误码
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int AddGraphics(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer = "", bool invoke_callback = true);
    /**
     * @fun    AddGraphics
     * @brief  以字符串反序列化添加图形.
     * @param  str 字符串(json)
     * @param  invoke_callback 是否执行回调函数
     * @return 正常返回0,否则返回错误码
     * @date   2024.01.16
     * <AUTHOR>
     */
    int AddGraphics(const std::string& str, bool invoke_callback = true);
    /**
      * @fun    CopyGraphics
      * @brief  复制图形到指定位置.
      * @param  new_gh [out]复制结果图形
      * @param  gh 待拷贝图形
      * @param  x 位置x
      * @param  y 位置y
      * @return 正常返回0,否则返回错误码
      * @date   2024.08.19
      * <AUTHOR>
      */
    int CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, std::shared_ptr<GraphicsAbstract>& gh, float x, float y);
    /**
     * @fun    CopyGraphics
     * @brief  复制图形到指定位置.
     * @param  new_gh [out]复制结果图形
     * @param  id 待拷贝图形索引
     * @param  x 位置x
     * @param  y 位置y
     * @return 正常返回0,否则返回错误码
     * @date   2024.08.19
     * <AUTHOR>
     */
    int CopyGraphics(std::shared_ptr<GraphicsAbstract>& new_gh, const GraphicsID& id, float x, float y);
    /**
     * @fun    SetGraphicsSelected
     * @brief  设置图形是否选中.
     *
     * @param  ghs [in]图形序列,内部会对图形进行修改,因此传递引用
     * @param  state 选中状态
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state);
    /**
     * @fun    SetGraphicsSelectedSingle
     * @brief  设置单个图形选中.
     * @param  gh 图形
     * @note
     *   清除其他选中状态
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback);
    /**
     * @fun    CreateGraphicsTool
     * @brief  创建图形工具.
     * @param  graphics_type 图形类型 参见 GraphicsFlag
     * @param  layer 图层名称,为空时使用当前图层
     * @return 返回图形
     * @date   2024.11.20
     * <AUTHOR>
     */
    std::shared_ptr<GraphicsAbstract> CreateGraphicsTool(int graphics_type, const std::string& layer,
        const std::string& group_name_, const std::shared_ptr<GraphicsAbstract>& father_graphics_);
    /**
     * @fun    GetAllLayerConfig
     * @brief  取得所有图层.
     *
     * @return 所有图层
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    const std::unordered_map<std::string, std::shared_ptr<LayerConfig>>& GetAllLayerConfig() const;
    /**
     * @fun    AddGraphicsLayerConfig
     * @brief  新增图层,图层已存在时覆盖.
     *
     * @param  layer  图层名称
     * @param  config 图层配置
     * @date   2024.01.16
     * <AUTHOR>
     */
    void AddGraphicsLayerConfig(const std::string& layer, std::shared_ptr<LayerConfig> config);
    /**
     * @fun    GetLayerConfig
     * @brief  根据名称取得图层配置.
     * @param  layer  图层名称
     * @param  result 图层配置 输出
     * @return 正常返回0,否则返回错误码
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetLayerConfig(std::weak_ptr<LayerConfig>& result, const std::string& layer) const;
    /**
     * @fun    ClearLayerConfig
     * @brief  清空所有图层.
     *
     * @note
     * 清空后会自动生成默认\控制\临时三个图层
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void ClearLayerConfig();
    /**
     * @fun    SetCurrentLayer
     * @brief  设置当前可编辑图层.
     *
     * @param  layer   图层名称
     * @return 正常返回0,否则返回错误码
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int SetCurrentLayer(const std::string& layer);
    /**
     * @fun    GetCurrentLayer
     * @brief  获取当前可编辑图层.
     * @return 当前可编辑图层名称
     * @date   2024.01.16
     * <AUTHOR>
     */
    std::string GetCurrentLayer() const;
    /**
    * @fun    UpdateLayerShowMessage
    * @brief  更新层级显示信息.
    * @return
    * @date   2025.04.18
    * <AUTHOR>
    */
    void UpdateLayerShowMessage() const;
    /**
     * @fun    SetManualDefaultDrawAngle
     * @brief  设置默认绘制角度.
     * @param  angle 单位：deg(°),逆时针
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetManualDefaultDrawAngle(float angle);
    /**
     * @fun    GetManualDefaultDrawAngle
     * @brief  获取默认绘制角度.
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    float GetManualDefaultDrawAngle();

    /**
     * @fun    ClearTexture
     * @brief  删除图片.
     * @param  z 堆叠高度 删除指定堆叠高度及以上的所有图片
     * @note
     * z只允许非负数,0等同于清空所有图形,输入小于0的值默认为0
     * @date   2024.01.16
     * <AUTHOR>
     */
    void ClearTexture(int z = 0);
    /**
     * @fun    CreateTexture
     * @brief  将图片添加到场景指定位置.
     * @param  id 纹理id 输出
     * @param  image 图片
     * @param  x 水平方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
     * @param  y 垂直方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
     * @param  z 堆叠高度 z越大处于越上层 只允许非负数
     * @param  angle 旋转角度 单位：deg(°)
     * @param  is_draw_center 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
     *
     * @return 正常返回0
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int CreateTexture(unsigned int& id, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center);
    /**
* @brief  将图像贴到界面上. CPU渲染
* @fun    CreateImage
* @param  image
* @param  x 水平方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
* @param  y 垂直方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
* @param  z 堆叠高度 z越大处于越上层 只允许非负数
* @param  angle 旋转角度 单位：deg(°)
* @param  is_draw_center 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
*
* @date   2025-2-9
* <AUTHOR>
*/
    int CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center, int current_show_image_key_);

    int CreateImages(const GraphicsImage& graphics_img_);
    /**
     * @fun AddGraphicsShapes
     * @brief
     * @param graphics_shape_
     * @return
     * <AUTHOR>
     * @date 2025.3.20
     */
    int AddGraphicsShapes(const GraphicsShape& graphics_shape_);
    /**
     * @fun ClearGraphicsShapes
     * @brief 清除所有图形
     * @return
     * <AUTHOR>
     * @date 2025.3.20
     */
    int ClearGraphicsShapes();
    /**
     * @fun ClearImage
     * @brief  根据key值清除图像
     * @param key_ -1清除所有图像
     * @return
     * <AUTHOR>
     * @date 2025.2.9
     */
    int ClearImage(const int& set_key_, int key_);
    /**
     * @fun ShowImage
     * @brief  根据key值显示图像
     * @param key_
     * @return
     * <AUTHOR>
     * @date 2025.2.9
     */
    int ShowImage(const uint8_t& set_key_, int key_);
    /**
     * @brief  将纹理移动到指定堆叠高度.
     * @fun    SetTextureZ
     * @param  id 纹理id
     * @param  z 堆叠高度
     * @param  make_unique 是否保证在当前堆叠高度下的唯一性
     * @return 正常返回0
     * @note
     *  设置make_unique会将指定堆叠高度下的纹理往下移动,
     *  保证移动后的纹理为当前堆叠高度下的唯一纹理
     * @date   2024.09.20
     * <AUTHOR>
     */
    int SetTextureZ(unsigned int id, int z, bool make_unique = true);
    /**
     * @brief  设置缩略图显示/隐藏
     * @fun    SetThumbnailShow
     * @return 正常返回0
     * @date   2024.09.20
     * <AUTHOR>
     */
    int SetThumbnailShow();
    /**
     * @fun    SetCanvasSize
     * @brief  设置画布尺寸.
     * @param  width    画布宽度
     * @param  height   画布高度
     * @return 正常返回0
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int SetCanvasSize(int width, int height);
    /**
     * @fun    GetCanvasSize
     * @brief  获取画布尺寸.
     * @param  width    画布宽度
     * @param  height   画布高度
     * @return 正常返回0
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetCanvasSize(int& width, int& height);

    /**
     * @fun    MoveCamera
     * @brief  相机移动偏移量距离
     * @param  xoffset   x方向偏移量
     * @param  yoffset   y方向偏移量
     * @date   2024.01.16
     * <AUTHOR>
     */
    void MoveCamera(float xoffset, float yoffset);
    /**
     * @fun    MoveCamera
     * @brief  相机向指定方向移动一个单位距离
     * @param  direction   参见 CameraDirection
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void MoveCamera(int direction);
    /**
     * @fun    MoveCamera
     * @brief  相机移动到指定位置.
     * @param  x 像素x
     * @param  y 像素y
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void MoveCameraTo(float x, float y);
    /**
     * @fun    MoveCameraTo
     * @brief  相机移动到指定位置.
     * @param  gh 相机移动到该图形中心
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void MoveCameraTo(std::shared_ptr<GraphicsAbstract> gh);
    void MoveCameraTo(const std::string& graphicsid);
    /**
     * @fun    ResetCamera
     * @brief  重置相机位置.
     * @param  type   参见 CameraResetMode
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void ResetCamera(int type);
    /**
     * @fun    SetZoom
     * @brief  设置相机缩放比例.
     * @param  zoom   比例 1表示1:1,通常范围(0,2),具体范围受设置的画布大小限制
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetZoom(float zoom);
    /**
     * @fun    SetZoomState
     * @brief  设置相机缩放模式.
     * @param  type   参见 CameraScaleMode
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetZoomState(int type);
    /**
     * @fun    SetLimitViewByCanvas
     * @brief  设置是否将视图限制在画布范围内
     *
     * @param  state 状态
     * @return 正常返回0
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    int SetLimitViewByCanvas(bool state);
    /**
     * @fun    SetState
     * @brief  设置当前状态.
     *
     * @param  state   参见 VisionMode
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetState(const int& state);
    /**
     * @brief  设置当前状态
     */
    void SetState(const VisionMode& state);
    /**
     * @brief  设置当前状态为默认状态
     */
    void ResetState();
    /**<
        回退上一个状态
    */
    void BackLastState();
    /**
     * @brief  获取当前状态
     */
    inline VisionMode GetState() const { return m_state; }
    /**
     * @brief  获取当前创建图形状态
     */
    inline CreateGraphicsMode GetCreateGraphicsMode() const { return m_createmode; }
    /**
     * @fun    SetCreateGraphicsMode
     * @brief  设置当前创建图形模式.
     * @param  mode   参见 CreateGraphicsMode
     *
     * @note   设置非NONE模式后会自动将当前状态切换为添加图形
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetCreateGraphicsMode(const CreateGraphicsMode& mode);
    /**
     * @fun    SetSelectGraphicsMode
     * @brief  设置图形选择模式.
     * @param  mode 参见 SelectGraphicsMode
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetSelectGraphicsMode(const int& mode);
    /**
     * @fun    GetSelectGraphicsMode
     * @brief  获取当前图形选择模式.
     * @return 参见 SelectGraphicsMode
     * @date   2024.01.16
     * <AUTHOR>
     */
    int GetSelectGraphicsMode() const;
    /**
     * @brief 设置显示调试信息(默认显示在界面左上角)
     * @param state true显示 false不显示
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetShowDebugInfo(bool state);

    /**
     * @fun SetShowCenterCrossLine
     * @brief 是否显示界面上的十字线
     * @param state
     * <AUTHOR>
     * @date 2025.1.17
     */
    void SetShowCenterCrossLine(bool state);

    /**
     * @fun    SetRulerScale
     * @brief  设置标尺的显示倍率.
     * @param  scale    倍率
     * @note   设置后实际显示的标尺值会乘上该倍率
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetRulerDisplayScale(double scale);
    /**
     * @fun    SetRulerPrecision
     * @brief  设置标尺的精确度.
     * @param  precision    小数点位数
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetRulerPrecision(int precision);
    /**
     * @fun    SetThumbnailNavigation
     * @brief  设置缩略图导航显示的图片.
     *
     * @param  image 显示图片(真实图片,或者经过等比例压缩的缩略图)
     * @param  image_true_w 真实图片宽度
     * @param  image_true_h 真实图片高度
     * @return 正常返回0
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    int SetThumbnailNavigation(const cv::Mat& image, int image_true_w, int image_true_h);

    /**
     * @fun    SetCallbackGraphicscreate
     * @brief  设置图形新增时的回调函数.
     * @param  callback 回调函数(参数分别表示 图形序列)
     *
     * @date   2024.08.29
     * <AUTHOR>
     */
    void SetCallbackGraphicscreate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback);
    /**
     * @fun    SetCallbackGraphicsupdate
     * @brief  设置图形更新时的回调函数.
     * @param  callback 回调函数(参数分别表示 图形序列)
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetCallbackGraphicsupdate(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&, bool)> callback);
    /**
     * @fun    SetCallbackGraphicsdelete
     * @brief  设置图形删除时的回调函数.
     * @param  callback 回调函数(参数分别表示 图形序列)
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetCallbackGraphicsdelete(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback);
    /**
     * @fun    SetCallbackGraphicsselected
     * @brief  设置图形选中时的回调函数.
     * @param  callback 回调函数(参数分别表示 图形序列)
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetCallbackGraphicsselected(std::function<void(const std::vector<std::shared_ptr<GraphicsAbstract>>&)> callback);
    /**
     * @fun    SetCallbackRegionselected
     * @brief  设置区域选中时的回调函数.
     * @param  callback 回调函数(参数分别表示 区域x, 区域y, 区域宽, 区域高)
     * @date   2024.08.14
     * <AUTHOR>
     */
    void SetCallbackRegionselected(std::function<void(float x, float y, float w, float h)> callback);

    //inline int GetVisionModeState() const { return static_cast<int>(m_state); }

    // inline GraphicsManager* GetGraphicsManager() const { return m_graphicsmanager.get(); }
    inline std::shared_ptr<GraphicsManager> GetGraphicsManager() const { return m_graphicsmanager; }

protected:
    inline Renderer2DManager* GetRenderer2DManager() const { return m_renderer2dmanager; }
    inline CommandManager* GetCommandManager() const { return m_cmdmanager; }
    inline CustomCursorManager* GetCustomCursorManager() const { return m_cursormanager; }

    // 处理图形添加事件
    void HandlerMouseGraphicsResponse(const MouseEventValue& value);
    void HandlerMouseGraphicsAdd(const MouseEventValue& value);
    void AddRect(const MouseEventValue& value);
    void AddCircle(const MouseEventValue& value);
    void AddPolygon(const MouseEventValue& value);
    void AddBezier(const MouseEventValue& value);
    void AddSG(const MouseEventValue& value);
    void AddMR(const MouseEventValue& value);
    void AddPad(const MouseEventValue& value);
    void AddSelectBatchPolygon(const MouseEventValue& value);

    // 处理窗口鼠标事件
    void HandlerRendermouseclicked(int type, int x, int y);
    void HandlerRendermousepress(int type, int x, int y);
    void HandlerRendermousemove(int type, int icx, int icy, int ilx, int ily, int ipx, int ipy);
    void HandlerRendermouserelease(int type, int cx, int cy, int px, int py);
    void HandlerRenderwheeldelta(int delta, int x, int y);
    void HandlerThumbnailmousemove(int type, float x, float y);
    void HandlerWindowsizechange(int w, int h);

    // 处理图形界面回调
    void HandlerGraphicsdraw();
    // void HandlerGraphicsupdate(const std::vector<std::shared_ptr<GraphicsAbstract>>&);
    void HandlerGraphicschange(const std::vector<std::shared_ptr<GraphicsAbstract>>&);
    void HandlerGraphicsselected(const std::vector<std::shared_ptr<GraphicsAbstract>>&);
    void HandlerGraphicsadd(const std::vector<std::shared_ptr<GraphicsAbstract>>&);
    void HandlerGraphicsdelete(const std::vector<std::shared_ptr<GraphicsAbstract>>&);
    void HandlerCursorchange(int type);
    void HandlerCameramove();

private:
    /**
     * @brief 注册回调
     */
    void RegisterSlot();
    /**
     * @brief 注册快捷键
     */
    void RegisterShortcut();
    /**
     * @brief 初始化默认属性
     */
    void InitDefaultAttriute();

    void OutState(const VisionMode& state);
    void InState(const VisionMode& state);
    bool IsAddGraphicsState(const VisionMode& state);

private:
    std::shared_ptr<GraphicsManager> m_graphicsmanager; ///< 图形管理器,继承渲染基类,委托给渲染器管理,因此使用智能指针
    Renderer2DManager* m_renderer2dmanager;             ///< 渲染界面管理器
    CommandManager* m_cmdmanager;                       ///< 命令管理器
    CustomCursorManager* m_cursormanager;               ///< 自定义鼠标指针管理器

    VisionMode m_state;                                 ///< 当前显示状态 改成VisionMode方便状态监控
    VisionMode _last_state;                          ///< 记录上一次的状态
    CreateGraphicsMode m_createmode;                    ///< 当前创建图形模式
    WheelMode m_wheelmode;                              ///< 滚轮缩放模式，TODO
    // int m_camerastate;                                  ///< 当前相机状态,控制当前使用的相机类型

    std::atomic<bool> _is_update_select_object;
    Event<float, float, float, float> callback_regionselected;  ///< 区域选择回调
};

#endif // !STATE_MANAGER_0311_H