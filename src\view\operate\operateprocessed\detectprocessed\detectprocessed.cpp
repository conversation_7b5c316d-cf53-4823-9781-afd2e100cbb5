#include "DetectProcessed.h"

const double MAX_BOARD_RESULT_COUNT = 10;

DetectProcessed::DetectProcessed(QObject* parent)
    : QObject(parent)
{
    m_detect_result_model = new DetectResultModel(nullptr);
    m_show_result_type = SHOW_DETECT_NG_TYPE::SHOW_DEVICE_TYPE;
    m_project_name = "";
    m_ticket_num = "";
    m_version = "";
    m_total_devices = 0;
    m_test_count = 0;
    m_scan_time = 0.0;
    m_loop_time = 0.0;
    m_fov_scan = 0.0;
    m_fov_detect = 0.0;
    m_aoi_ok_count = 0.0;
    m_aoi_ng_count = 0.0;
    m_sub_board_aoi_good_count = 0.0;
    m_sub_board_aoi_ng_count = 0.0;
    vec_board_result.clear();
    vec_ng_devices_ring.clear();
    vec_ng_types_ring.clear();
}

DetectProcessed::~DetectProcessed()
{
}

void DetectProcessed::UpdateResultRatio()
{
    vec_board_result_ring_datas.clear();
    vec_board_result_ring_datas.push_back(RingData(QString::fromWCharArray(L"直通率").toStdString(), m_aoi_ok_count));
    vec_board_result_ring_datas.push_back(RingData(QString::fromWCharArray(L"NG率").toStdString(), m_aoi_ng_count));
    vec_board_result_ring_datas.push_back(RingData(QString::fromWCharArray(L"子板直通率").toStdString(), m_sub_board_aoi_good_count));
    vec_board_result_ring_datas.push_back(RingData(QString::fromWCharArray(L"子板NG率").toStdString(), m_sub_board_aoi_ng_count));
}

void DetectProcessed::AddOneBoardResult(std::string barcode, jrsdata::DetectResult result, int track_id, long total_device_count, long test_device_count, long ng_device_count, std::string take_phone_time, std::string circle_time)
{
    while (vec_board_result.size() >= MAX_BOARD_RESULT_COUNT)
    {
        vec_board_result.erase(vec_board_result.begin());
    }
    long id = long(vec_board_result.size()) + 1;
    DetectResultStruct* detect_result = new DetectResultStruct();
    detect_result->m_id = id;
    detect_result->m_barcode = barcode;
    detect_result->m_result = result;
    detect_result->m_track_id = track_id;
    detect_result->m_total_device_count = total_device_count;
    detect_result->m_test_device_count = test_device_count;
    detect_result->m_ng_device_count = ng_device_count;
    detect_result->m_take_phone_time = take_phone_time;
    detect_result->m_circle_time = circle_time;
    vec_board_result.push_back(detect_result);
}