﻿project(core)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#导出宏
add_compile_definitions(
    JRS_AOI_PLUGIN_EXPORTS
    JT_API_EXPORTS
    )

include(${CMAKE_SOURCE_DIR}/cmake/findeigen3.cmake)

# demo 示例文件筛选器
set(common_src

)
set (common_head
    common/include/callbackhelper.hpp
    common/include/pluginexport.hpp
    common/include/SingleTonT.hpp
)

set(database_head
    database/include/bdatabase.h
    database/include/connectpool.h
    database/include/idatabase.h
    database/include/mysqlimp.hpp
)

set(pubsub_head
    pubsub/src/module.h
    pubsub/src/pubsubmanager.h
    pubsub/src/topic.h
    pubsub/include/modulehandle.h
)
set(pubsub_src
	pubsub/src/module.cpp
    pubsub/src/pubsubmanager.cpp
    pubsub/src/topic.cpp
    pubsub/src/modulehandle.cpp
)

set(coreapp_head
    common/include/coreapplication.h
)
set(coreapp_src
    common/src/coreapplication.cpp
)
set(log_head
	common/include/abstractlogger.h
	common/include/jrslogtypedefines.h
	common/include/logmanager.h
	common/src/spdlogger.h
)
set(log_src
	common/src/logmanager.cpp
	common/src/spdlogger.cpp
)


set(error_head
	common/include/errorhandler.h
)
set(error_src
	common/src/errorhandler.cpp
)

set(jtools_head_dir ${CMAKE_CURRENT_SOURCE_DIR}/common/include)
set(jtools_src_dir ${CMAKE_CURRENT_SOURCE_DIR}/common/src)

# FOV相关目录
set(fov_head_dir ${CMAKE_CURRENT_SOURCE_DIR}/dofov/include)
set(fov_src_dir ${CMAKE_CURRENT_SOURCE_DIR}/dofov/src)

set(jtools_head
    ${jtools_head_dir}/fileoperation.h
    ${jtools_head_dir}/jtools.hpp
    ${jtools_head_dir}/directedgraph.h
    ${jtools_head_dir}/jtoolsapi.hpp
    ${jtools_head_dir}/jtools_module.hpp
    ${jtools_head_dir}/randomgenerator.h
    ${jtools_head_dir}/stringoperation.h
    ${jtools_head_dir}/timeutility.h
    ${jtools_head_dir}/cvtools.h

	${jtools_head_dir}/tools.h
    ${jtools_head_dir}/pluginloader.hpp
    ${jtools_head_dir}/pluginfactory.hpp
    ${jtools_head_dir}/utf8convert.hpp
    ${jtools_head_dir}/generalimagetool.h
    ${jtools_head_dir}/jsonoperator.hpp   
    #TODO 增加线程池的封装函数 by baron_zhang 2024-12-16
    ${jtools_head_dir}/jrsthreadpool.hpp   

    #${jtools_head_dir}/algorithmtools.hpp   
)

set(jtools_src
    # ${jtools_src_dir}/demo.cpp  # 示例文件
    ${jtools_src_dir}/fileoperation.cpp
    ${jtools_src_dir}/randomgenerator.cpp
    ${jtools_src_dir}/stringoperation.cpp
    ${jtools_src_dir}/timeutility.cpp
	${jtools_src_dir}/tools.cpp
    ${jtools_src_dir}/generalimagetool.cpp
    ${jtools_src_dir}/cvtools.cpp
)

# 坐标模块
set(coord_head
    coordinatecenter/include/coordinatetransform.hpp
    coordinatecenter/include/coordinatetransformationtool.h
)

# fov计算
file(GLOB_RECURSE fov_head ${fov_head_dir}/*.h)
file(GLOB_RECURSE fov_src ${fov_src_dir}/*.cpp)

set(message_head
	common/include/messagemanager.h
)
set(message_src
    common/src/messagemanager.cpp
)
source_group("common/src" FILES ${common_src})
source_group("common/head" FILES ${common_head})
source_group("common/utils/coreapp/head" FILES ${coreapp_head})
source_group("common/utils/coreapp/src" FILES ${coreapp_src})
source_group("common/utils/errorcode/head" FILES ${error_head})
source_group("common/utils/errorcode/src" FILES ${error_src})
source_group("common/utils/logger/head" FILES ${log_head})
source_group("common/utils/logger/src" FILES ${log_src})
source_group("common/utils/tools/head" FILES ${jtools_head})
source_group("common/utils/tools/src" FILES ${jtools_src})
source_group("common/utils/message/head" FILES ${message_head})
source_group("common/utils/message/src" FILES ${message_src})

source_group("database/head" FILES ${database_head})

source_group("pubsub/src" FILES ${pubsub_src})
source_group("pubsub/head" FILES ${pubsub_head})

source_group("coordinatecenter/head" FILES ${coord_head})

source_group("fov/head" FILES ${fov_head})
source_group("fov/src" FILES ${fov_src})


add_library(${PROJECT_NAME} SHARED
    ${common_head}
    ${common_src}
    ${database_head}
    ${database_src}
    ${pubsub_head}
    ${pubsub_src}
    ${coreapp_head}
    ${coreapp_src}
    ${error_head}
    ${error_src}
    ${log_head}
    ${log_src}
    ${jtools_head}
    ${jtools_src}
    ${coord_head}
    ${fov_head}
    ${fov_src}
    ${message_head}
    ${message_src}
    ${JRS_VERSIONINFO_RC}

)

#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)

# 将头文件目录添加到包含路径中
target_include_directories(${PROJECT_NAME} PRIVATE #增加范围限制
    database/include
	pubsub/include
	pubsub/src
	common/include
	dofov/include
    coordinatecenter/include
    ${jtools_head_dir}
    
	../../thirdparty/spdlog/include
    ${DIR_PROJECT_CURRENT}thirdparty/json/include
)

target_include_directories(${PROJECT_NAME} PUBLIC      
    ${EIGEN3_INCLUDE_DIR}
    ${OPENCV_INCLUDE_DIR}
    ${DIR_PROJECT_CURRENT}src/parametermanager/define/parambase
)  
# 为指定的目标（target）添加链接目录
target_link_directories(${PROJECT_NAME} PRIVATE
    #OPENCV
    $<$<CONFIG:Debug>:${OPENCV_DEBUG_DIR}>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${OPENCV_RELEASE_DIR}>
    # ${CMAKE_SOURCE_DIR}/thirdparty/vtk3dviewer/lib
)

target_link_libraries(${PROJECT_NAME} PUBLIC        
    ormpp
)

target_link_libraries(${PROJECT_NAME} PRIVATE        
    #opencv
    $<$<CONFIG:Debug>:opencv_world4100d>
    $<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:opencv_world4100>
)
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")
