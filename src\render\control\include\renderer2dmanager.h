﻿/**********************************************************************
 * @brief  UI管理.
 *
 * @file   renderer2dmanager.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef RENDERER2DMANAGER_H
#define RENDERER2DMANAGER_H
#include "delegate.hpp"         // Event
#include "renderabstract.hpp"   // RenderAbstractPtr
#include <functional>           // std::function
#include <string>

namespace cv { class Mat; };
enum class RenderType;
struct RenderMessage;
class GLWindow;
class Renderer2DWidget;
class ThumbnailNavigationWidget;
class VisualCameraAbstract;
class GraphicsManager;
class GraphicsAbstract;
class RulerWidget;
struct GraphicsShape;
struct GraphicsImage;
class QWidget;
class QTimer;
class ViewerGraphicsViewImpl;
class Renderer2DManager
{
public:
    explicit Renderer2DManager();
    ~Renderer2DManager();

    QWidget* GetWidget();
    QWidget* const GetGLWindow() const;

    void Update();
    /**
     * @brief  设置是否始终追踪鼠标事件
     */
    void SetMouseTracking(bool enable);
    /**
     * @brief  设置鼠标指针样式
     */
     // void SetCursor(int cursor_type);

    /**
     * @brief  设置界面周围刻度尺的范围.
     *
     * @fun    SetRulerValue
     * @param  xmin 横向刻度尺的最小值
     * @param  xmax 横向刻度尺的最大值
     * @param  ymin 纵向刻度尺的最小值
     * @param  ymax 纵向刻度尺的最大值
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    void SetRulerValue(const float& xmin, const float& xmax, const float& ymin, const float& ymax);

    /**
     * @fun    SetRulerScale
     * @brief  设置标尺的比例.
     * @param  scale    比例
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetRulerScale(double scale);

    /**
     * @brief  设置标尺是否使用倍率
     */
    void SetRulerChangeUseScale();

    /**
     * @fun    SetRulerPrecision
     * @brief  设置标尺的精确度.
     * @param  precision    小数点位数
     *
     * @date   2024.01.16
     * <AUTHOR>
     */
    void SetRulerPrecision(int precision);
    /**
     * @brief  设置缩略图导航显示的图片.
     *
     * @fun    SetThumbnailNavigation
     * @param  image 显示图片(真实图片,或者经过等比例压缩的缩略图)
     * @param  image_true_w 真实图片宽度
     * @param  image_true_h 真实图片高度
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    bool SetThumbnailNavigation(const cv::Mat& image, int image_true_w, int image_true_h);
    /**
     * @brief  清除缩略图导航显示的图片.
     *
     * @fun    ClearThumbnailNavigation
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    void ClearThumbnailNavigation();
    /**
     * @brief  设置缩略图导航视野大小.
     *
     * @fun    SetThumbnailViewport
     * @param  left
     * @param  top
     * @param  right
     * @param  bottom
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    void SetThumbnailViewport(float left, float top, float right, float bottom);
    /**
     * @brief  设置显示/隐藏缩略图导航
     *
     * @param  state 是否显示
     *
     * @date   2024.09.19
     * <AUTHOR>
     */
    void SetThumbnailShow(bool state);
    bool GetThumbnailShow() const;
    /**
     * @brief  将渲染对象设置到指定渲染器中.
     *
     * @fun    SetRendererObject
     * @param  ro   渲染对象
     * @param  type 渲染器类型 WindowInterface::RenderType
     *
     * @date   2024.06.04
     * <AUTHOR>
     */
    void SetRendererObject(RenderAbstractPtr ro, const RenderType& type);
    /**
     * @brief  清除指定图层以上的图层.
     *
     * @fun    ClearTexture
     * @param  z
     *
     * @note   一般情况图层是由下而上逐渐堆叠,
     *         处理时也是优先处理最上层,和堆栈一样
     *
     * @date   2024.06.04
     * <AUTHOR>
     */
    void ClearTexture(int z);
    /**
     * @brief  将图像贴到界面上. GPU渲染
     *
     * @fun    CreateTexture
     * @param  image
     * @param  x 水平方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
     * @param  y 垂直方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
     * @param  z 堆叠高度 z越大处于越上层 只允许非负数
     * @param  angle 旋转角度 单位：deg(°)
     * @param  is_draw_center 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
     *
     * @date   2024.06.04
     * <AUTHOR>
     */
    unsigned int CreateTexture(const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center);
    /**
   * @brief  将图像贴到界面上. CPU渲染
   *
   * @fun    CreateImage
   * @param  image
   * @param  x 水平方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
   * @param  y 垂直方向位置，当 is_draw_center 为 true 表示图像中心位置，否则表示图像左上角位置
   * @param  z 堆叠高度 z越大处于越上层 只允许非负数
   * @param  angle 旋转角度 单位：deg(°)
   * @param  is_draw_center 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
   *
   * @date   2024.06.04
   * <AUTHOR>
   */
    int CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center, int current_show_image_key_);
    /**
     * @brief . 创建多个图像
     *
     * @param mats_
     * @param x
     * @param y
     * @param z
     * @param angle
     * @param is_draw_center
     * @return
     */
    int CreateImages(const GraphicsImage& graphics_img_);
    /**
     * @fun AddGraphicsShapes
     * @brief
     * @param graphics_shape_
     * @return
     * <AUTHOR>
     * @date 2025.3.20
     */
    int AddGraphicsShapes(const GraphicsShape& graphics_shape_);
    /**
     * @fun ClearGraphicsShapes
     * @brief 清除所有图形
     * @return
     * <AUTHOR>
     * @date 2025.3.20
     */
    int ClearGraphicsShapes();
    /**
     * @fun ClearImage
     * @brief 清除图片
     * @param key_ -1时 直接清除所有图片
     * @return
     * <AUTHOR>
     * @date 2025.2.9
     */
    int ClearImage(const int& set_key_, int key_);
    /**
     * @fun ShowImage
     * @brief  根据key值显示图片
     * @param key_
     * @return
     * <AUTHOR>
     * @date 2025.2.9
     */
    int ShowImage(const uint8_t& set_key_, int key_);

    int SetTextureZ(int z, const std::vector<unsigned int>& ids);
    int FindTextureZ(int& z, unsigned int id);
    int FindZTexture(std::vector<unsigned int>& v, int z);

    void MoveCamera(float xoffset, float yoffset);
    void MoveCamera(int direction);
    void ResetCamera(int type);
    void MoveCameraTo(float x, float y);

    void SetZoom(float zoom);
    float GetZoom();
    void SetZoomState(int state);
    //set view size
    bool SetCanvasSize(int width, int height);
    bool GetCanvasSize(int& width, int& height);
    bool SetLimitViewByCanvas(bool state);
    bool GetLimitViewByCanvas();

    void GetGLWindowSize(int& width, int& height);
    void HoverColor(int x, int y);
    void SetShowDebugInfo(bool state);
    void SetShowCenterCrossLine(bool is_draw_);
    bool GetIsShowCenterCrossLine();
    void SetShowMessage(const std::string& message_, int duration_ = 2000, int size_ = 12);

    void SetCallbackRendermouseclicked(std::function<void(int, int, int)>);
    void SetCallbackRendermousepress(std::function<void(int, int, int)>);
    void SetCallbackRendermouserelease(std::function<void(int, int, int, int, int)>);
    void SetCallbackRendermousemove(std::function<void(int, int, int, int, int, int, int)>);
    void SetCallbackRenderwheeldelta(std::function<void(int, int, int)>);

    void SetCallbackThumbnailmousemove(std::function<void(int, float, float)>);
    void SetCallbackThumbnailmouseenter(std::function<void()>);
    void SetCallbackThumbnailmouseleave(std::function<void()>);
    void SetCallbackWindowsizechange(std::function<void(int, int)>);
    void SetCallbackCameramove(std::function<void()>);
    void SetCallbackCursorchange(std::function<void(int)>);

protected:
    void HandlerTimerOut();
    // void TriggerCallbackscenechange(int, int, int);
    void TriggerCallbackCameramove();

private:
    void RegisterSlot();


private:
    /**
     *  更新 graphics camera位置
     */
    void UpdateImageRenderCameraMove();
    /**
     * @fun UpdateImagerRenderZoom
     * @brief 更新 graphics render 放缩比，放缩比以gl render为准
     * <AUTHOR>
     * @date 2025.2.6
     */
    void UpdateImagerRenderZoom();



    ViewerGraphicsViewImpl* _image_render;   ///<图像渲染窗口
    Renderer2DWidget* widget;               ///< 主UI窗口
    GLWindow* m_render_window;              ///< GL渲染窗口
    ThumbnailNavigationWidget* m_thumbnail; ///< 缩略图预览窗口
    RulerWidget* horizontalRuler;           ///< 水平标尺
    RulerWidget* verticalRuler;             ///< 垂直标尺
    VisualCameraAbstract* main_camera;      ///< 主虚拟相机,用于控制主视图
    RenderAbstractPtr ro_texture;           ///< 图片渲染对象
    RenderAbstractPtr ro_foreground;        ///< 前景渲染对象
    RenderAbstractPtr ro_background;        ///< 背景渲染对象

    QTimer* timer_key_handle;                 ///< 处理按键事件的计时器
    std::unordered_map<int, bool> m_map_keys; ///< 记录当前被按下按键的哈希表,用于执行连续按键命令

    Event<int, int, int>               callback_rendermouseclicked;     ///< 渲染窗口鼠标点击事件，参数为 (鼠标按键属性, x, y)
    Event<int, int, int>               callback_rendermousepress;       ///< 渲染窗口鼠标按下事件，参数为 (鼠标按键属性, x, y)
    Event<int, int, int, int, int>     callback_rendermouserelease;     ///< 渲染窗口鼠标释放事件，参数为 (鼠标按键属性, current_x, current_y, press_x, press_y)
    Event<int, int, int, int, int, int, int> callback_rendermousemove;  ///< 渲染窗口鼠标移动事件，参数为 (鼠标按键属性, current_x, current_y, last_x, last_y, press_x, press_y)
    Event<int, int, int>               callback_renderwheeldelta;       ///< 渲染窗口鼠标滚轮事件，参数为 (delta, x, y)
    Event<int, int>                    callback_windowsizechange;       ///< 窗口大小改变事件，参数为 (width, height)
    Event<int, float, float>           callback_thumbnailmousemove;     ///< 缩略图鼠标移动事件，参数为 (id, x, y)
    Event<>                            callback_thumbnailmouseenter;    ///< 缩略图鼠标进入事件，不带参数
    Event<>                            callback_thumbnailmouseleave;    ///< 缩略图鼠标离开事件，不带参数
    Event<>                            callback_cameramove;             ///< 相机移动事件，不带参数
    Event<int>                         callback_cursorchange;           ///< 切换鼠标指针，参数为 (cursor_type)
};

#endif //! RENDERER2DMANAGER_H