/*****************************************************************//**
 * @file   generalimagetool.h
 * @brief  AOI通用图像工具类
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __GENERALIMAGETOOL_H__
#define __GENERALIMAGETOOL_H__
#include "jtoolsapi.hpp"

#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include <opencv2/opencv.hpp>
#pragma warning(pop)

namespace jrstool
{	
	struct CropParams
	{
		const cv::Mat& input;
		const cv::RotatedRect rotate;
		const std::vector< cv::Point>& contour;
	    cv::Mat rect_mat;
		cv::Mat rect_mat_with_label;
	};
	/**
    * @class  GeneralCropTool
    * @brief  AOI通用裁图工具
    * @date   2024.08.19
    * <AUTHOR>
    */
	class JT_EXPORTS CropAndPasteTool
	{
	public:
		/**
        * @fun    CropImage
        * @brief  使用旋转矩形裁图
		* @param  input 大图
		* @param  rotate 旋转矩形
		* @param  contour 轮廓点（必须全部在旋转矩形内部）
		* @param  rect_mat 裁剪后的图像（内存连续）
		* @param  rect_mat_with_label 裁剪后的标签图像（内存连续）
        * @date   2024.08.19
        * <AUTHOR>
        */
		static int CropImage(const  cv::Mat& input, const  cv::RotatedRect& rotate,
			const std::vector< cv::Point>& contour,cv::Mat& rect_mat,cv::Mat& rect_mat_with_label);
		/**
        * @fun    CropImage
        * @brief  使用旋转矩形裁图
		* @param  input 大图
		* @param  CropParams 裁图结构体
        * @date   2024.08.19
        * <AUTHOR>
        */
		static int CropImage(CropParams& params);
		/**
        * @fun    CropImage
        * @brief  使用矩形裁图
		* @param  input 大图
		* @param  rect 矩形
		* @param  rect_mat 裁剪后的图像（内存连续）
        * @date   2024.08.20
        * <AUTHOR>
        */
		static int CropImage(const  cv::Mat& input, const  cv::Rect& rect,cv::Mat& rect_mat);
		/**
        * @fun    CropImage
        * @brief  使用旋转矩形裁图
		* @param  input 大图
		* @param  rotate 旋转矩形
		* @param  rect_mat 裁剪后的图像（内存连续）
        * @date   2024.08.20
        * <AUTHOR>
        */
		static int CropImage(const  cv::Mat& input, const  cv::RotatedRect& rotate,cv::Mat& rect_mat);
		/**
        * @fun    PasteImage
        * @brief  将小图黏贴到大图的rotate区域内
		* @param  input_small 小图
		* @param  rotate 大图中待黏贴区域
		* @param  rect_mat 黏贴后的大图
        * @date   2024.08.20
        * <AUTHOR>
        */
		static int PasteImage(const  cv::Mat& input_small, const  cv::RotatedRect& rotate,cv::Mat& out_big);
		/**
        * @fun    PasteImage
        * @brief  将小图黏贴到大图的rect区域内
		* @param  input_small 小图
		* @param  rect 大图中待黏贴区域
		* @param  rect_mat 黏贴后的大图
        * @date   2024.08.20
        * <AUTHOR>
        */
		static int PasteImage(const  cv::Mat& input_small, const  cv::Rect& rect,cv::Mat& out_big);

		/**
		 * @fun CompressImage 
		 * @brief 图像压缩
		 * @param input_image 输入图像
		 * @param format 输出格式
		 * @param quality 输出质量
		 * @return 图像结果
		 * <AUTHOR>
		 * @date 2025.1.12
		 */
		static cv::Mat GenerateLowResolutionImage(const cv::Mat& input_image, float scale_factor);

	public:
		CropAndPasteTool();
		~CropAndPasteTool();
	private:
		/**
        * @fun    GetRotateRigion
        * @brief  获取旋转矩形区域
        * @date   2024.08.20
        * <AUTHOR>
        */
        static int GetRotateRigion(const  cv::RotatedRect& rotate, 
			const  cv::Mat& input_image,  cv::Mat& output_image);
		/**
        * @fun    JudgePointInImage
        * @brief  判断点是否在图像内
        * @date   2024.08.19
        * <AUTHOR>
        */
		static bool JudgePointOutImage(int max_x,
			int max_y,const cv::Point2f& test_point);
	};	
}


#endif