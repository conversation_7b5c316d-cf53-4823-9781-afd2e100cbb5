/*****************************************************************
 * @file   algoexecuteparam.hpp
 * @brief   软件中调用算法执行时的参数
 * @details 软件执行流程中调用结构体将算法执行时的参数封装到该结构体中，然后在算法引擎中进行转换成各自的数据类型
 * <AUTHOR>
 * @date 2024.12.3
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.3          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
 //STD
 //Custom
 //Third
#ifndef __JRSALGOEXECUTEPARM_HPP__
#define __JRSALGOEXECUTEPARM_HPP__

//STD
#include <iostream>
#include <variant>
#include <vector>

//Custom
#include "algoimagedata.h" //丢弃使用
#pragma warning(push, 0)
#pragma warning(disable : 4100 2220 )

#include "algogeometrydata.hpp"


#include "judgeparam.h"
#include "image.hpp"
//Third
#include <opencv2/opencv.hpp>
#pragma warning(pop)
#include "nlohmann/json.hpp"

namespace jrsoperator
{
    struct TemplColorParamsGroup;
}
namespace jrsparam
{
    /**
     * @brief 算法执行参数
     *
     */
    struct ExecuteAlgoParam
    {
        std::string algo_name; /**< 算法的名称*/
        std::string algo_param;/**< 运行的算法参数*/
        JrsRect component_rect; /**< 算法执行时元件的ROI区域*/
        JrsRect original_detect_rect;    /**< 算法执行时检测检测框位置 未放大之前的位置 by zhangyuyu 2025.1.22 */
        JrsRect expand_detect_win_rect; /**< 放大后的检测区域，用于算法内部处理，相对于截图区域左上角的坐标 by zhangyuyu 2025.1.22 */
        /**  @brief 修改AOI端算法执行时算法参数的输入结构 by zhangyuyu 2024.12.3 */
        std::unordered_map<int, cv::Mat> input_img; /**< 需要执行的输入图片*/
        std::vector<jrsoperator::TemplColorParamsGroup> template_data; /**< 模板数据，其中包括模板的灯光图ID，调色参数，模板的图片*/
        /////////   TODO 新添加新版算法参数  by baron_zhang 2024.12.20    ///////////
         ////////// 输入 //////////
        double x_resolution;                                              /**< x方向分辨率 */
        double y_resolution;                                              /**< y方向分辨率 */
        std::unordered_map<int, std::vector<JrsRect>> pad_rects;/**< pad框位置 {方向,框组} */
        std::unordered_map<int, std::vector<JrsRect>> original_sub_win_rects;   /**< 子检测框位置 {id:子检测框序号,框组} 检测框内部的检测框,相对于detect_rect 未外扩之前 */
        std::unordered_map<int, std::vector<JrsRect>> expand_sub_win_rects;/**< 外扩后的子检测框区域 用于算法内部处理，相对于截图区域左上角的坐标 by zhangyuyu 2025.1.22*/
        std::unordered_map<int, std::vector<cv::Mat>> input_mask_image;  /**< 算法检测输入mask */
        std::unordered_map<int, cv::Mat> correction_matrixes;            /**< 检测位置矫正矩阵*/
   
    };

    /** @brief 算法执行结果参数结构体 by zhangyuyu 2025.5.7*/
    struct AlgoExecuteResultParam
    {
        std::vector<JrsRect> result_algo_output_rects; /**< 算法输出的rect */
        std::unordered_map<int, AlgoJudgeResult> result_algo_spec_result; /**< 规格判定结果 */
        float result_x_coordinate; /**< 算法输出x中心坐标 */
        float result_y_coordinate; /**< 算法输出y中心坐标 */
        float result_angle;        /**< 算法输出的角度 */
        float result_score;        /**< 算法输出的分数结果*/
        std::string result_str; /**< 结果字符串 */
        bool result_algo_status; /**< OK(true)/NG(false) */

        // 默认构造函数
        AlgoExecuteResultParam()
            : result_algo_output_rects{}
            , result_algo_spec_result{}
            , result_x_coordinate{0.f}
            , result_y_coordinate{0.f}
            , result_angle{0.f}
            , result_score{0.f}
            , result_str{}
            , result_algo_status(true)
        {
        }

        // 带参数构造函数
        AlgoExecuteResultParam(const std::vector<JrsRect>& rects,
            const std::unordered_map<int, AlgoJudgeResult>& specs,
            float x, float y, float a,
            float score,
            const std::string& results,
            bool status)
            : result_algo_output_rects(rects)
            , result_algo_spec_result(specs)
            , result_x_coordinate(x)
            , result_y_coordinate(y)
            , result_angle(a)
            , result_score{score}
            , result_str(results)
            , result_algo_status(status)
        {
        }

        // 拷贝构造函数
        AlgoExecuteResultParam(const AlgoExecuteResultParam& other)
            : result_algo_output_rects(other.result_algo_output_rects)
            , result_algo_spec_result(other.result_algo_spec_result)
            , result_x_coordinate(other.result_x_coordinate)
            , result_y_coordinate(other.result_y_coordinate)
            , result_angle(other.result_angle)
            , result_score{other.result_score}
            , result_str(other.result_str)
            , result_algo_status(other.result_algo_status)
        {
        }

        // 移动构造函数
        AlgoExecuteResultParam(AlgoExecuteResultParam&& other) noexcept
            : result_algo_output_rects(std::move(other.result_algo_output_rects))
            , result_algo_spec_result(std::move(other.result_algo_spec_result))
            , result_x_coordinate(other.result_x_coordinate)
            , result_y_coordinate(other.result_y_coordinate)
            , result_angle(other.result_angle)
            , result_score{other.result_score}
            , result_str(std::move(other.result_str))
            , result_algo_status(other.result_algo_status)
        {
        }

        // 拷贝赋值运算符
        AlgoExecuteResultParam& operator=(const AlgoExecuteResultParam& other) 
        {
            if (this != &other) {
                result_algo_output_rects = other.result_algo_output_rects;
                result_algo_spec_result = other.result_algo_spec_result;
                result_x_coordinate = other.result_x_coordinate;
                result_y_coordinate = other.result_y_coordinate;
                result_angle = other.result_angle;
                result_score = other.result_score;
                result_str = other.result_str;
                result_algo_status = other.result_algo_status;
            }
            return *this;
        }

        // 移动赋值运算符
        AlgoExecuteResultParam& operator=(AlgoExecuteResultParam&& other) noexcept {
            if (this != &other) {
                result_algo_output_rects = std::move(other.result_algo_output_rects);
                result_algo_spec_result = std::move(other.result_algo_spec_result);
                result_x_coordinate = other.result_x_coordinate;
                result_y_coordinate = other.result_y_coordinate;
                result_angle = other.result_angle;
                result_score = other.result_score;
                result_str = std::move(other.result_str);
                result_algo_status = other.result_algo_status;
            }
            return *this;
        }
    };
    // 算法执行时的Rect信息，包含：整体的截图区域，cad区域，检测框区域，pad区域，子检测框区域  by zhangyuyu  将其从projectparam.hpp 中分离出来
    struct AlgoExcuteRectsParam
    {

        JrsRect crop_img_rect;    ///< 整体的截图区域 (绝对位置)
        JrsRect component_rect;   ///< cad区域 （相对于截图区域左上角的坐标）
        JrsRect original_detect_win_rect;  ///< 检测框区域（相对于截图区域左上角的坐标）未外扩的检测框区域 by zhangyuyu 2025.1.22
        JrsRect expand_detect_win_rect; /**< 放大后的检测区域，用于算法内部处理，相对于截图区域左上角的坐标 by zhangyuyu 2025.1.22 */
        std::unordered_map<int, std::vector<JrsRect>> pad_rects; ///< pad区域（相对于截图区域左上角的坐标）
        std::unordered_map<int, std::vector<JrsRect>> original_sub_win_rects; ///< 子检测框区域（相对于截图区域左上角的坐标） 未外扩的子检测框区域 by zhangyuyu 2025.1.22
        std::unordered_map<int, std::vector<JrsRect>> expand_sub_win_rects;/**< 放大后的子检测框区域 用于算法内部处理，相对于截图区域左上角的坐标 by zhangyuyu 2025.1.22*/
    };
    YLT_REFL(AlgoExcuteRectsParam, crop_img_rect, component_rect, original_detect_win_rect, expand_detect_win_rect, pad_rects, original_sub_win_rects, expand_sub_win_rects)

    /**
     * .算法执行模式，主要有自动模式和手动建模模式
     * 手动建模时截图直接从工程大图中截图，自动流程时，截图从输入的图片中截图
     * by zhangyuyu 2024.12.27  
     */
    enum class ExecuteMode :int
    {

        AutoMode,//! 自动运行模式
        ManualMode //! 手动运行模式
    };
    struct ExecuteModeInfo 
    {
        ExecuteMode execute_mode;//! 运行模式
        int fov_left_top_x;  //! 大图的左上角坐标x(在整板图中)，大图指的是元件坐落的图片，可能是单张FOV，也可能是多张FOV图，也可能是整板图片
        int fov_left_top_y;  //! 大图的左上角坐标y(在整板图中)
        std::map<jrsdata::LightImageType, cv::Mat> src_img; //! 用于截图的原图
        ExecuteModeInfo()
            : execute_mode{ ExecuteMode::ManualMode }, fov_left_top_x{ 0 }, fov_left_top_y{ 0 }, src_img{}
        {
        }
    };
    /**< 算法参数类型 */
    using AlgoParamType = std::variant<
        std::monostate,
        int,
        float,
        bool,
        double,
        JrsRect,
        cv::Mat,
        std::string,
        std::vector<JrsRect>,
        std::vector<uint8_t>,
        std::vector<JrsVec3f>,
        std::vector<std::vector<uint8_t>>,
        std::unordered_map<int, cv::Mat>,
        std::vector<jrsoperator::TemplColorParamsGroup>,
        std::unordered_map<int, AlgoJudgeResult>,
        //TODO 新增加算法参数类型 by baron_zhang 2024.12.20
        std::unordered_map<int, std::vector<JrsRect>>,
        std::unordered_map<std::string, cv::Mat>,
        std::vector<std::vector<JrsVec3f>>

    >;

    template <typename T>
    T ConvertVariantTo(const AlgoParamType& param)
    {
        if (std::holds_alternative<T>(param))
        {
            return std::get<T>(param);
        }

        throw std::bad_variant_access{};
    }
} 
// namespace jrsalgo

#endif // !__JRSALGOEXECUTEPARM_HPP__
