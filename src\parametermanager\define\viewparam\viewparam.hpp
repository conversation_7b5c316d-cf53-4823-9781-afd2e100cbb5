/*****************************************************************//**
 * @file   viewparam.hpp
 * @brief  界面参数定义
 * @details
 * <AUTHOR>
 * @date 2024.1.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.31         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __PARAM_HPP__
#define __PARAM_HPP__
 //STD
#include <iostream>
#include <memory>

//Custom
#include "parambase.hpp"
#include "projectparam.hpp"
#include "deviceparam.hpp"
#include "detectresultparam.hpp"
#include "image.hpp"
#include "datadefine.hpp"
#include "jtools.hpp"
#include "viewdefine.h"
#include "settingparam.h"
#include "multiboardparam.hpp"
//Third
#pragma warning(push, 3)
#include <opencv2/core/types_c.h>
#pragma warning(pop)

namespace jrsoperator
{
    struct OperatorParamBase;
    using OperatorParamBasePtr = std::shared_ptr<OperatorParamBase>;
}
namespace jrsparam
{
    struct AlgoExcuteRectsParam;
}

class QWidget;

namespace jrsdata
{

    struct AddComponentViewParam/**＜　创建ｍａｒｋ　ｂａｄｍａｒｋ　ｂａｒｏｃｄｅ*/
    {
        enum class AddMarkType
        {
            DirectCreate,/**< 直接创建 */
            MarkAlgorithm,/**< 使用定位点算法创建 */
            ConvertMark /**< 将其他元件转换为mark点*/
        };
        //struct MarkAlgorParam
        //{
        //    cv::Point2f mark_point;/**<mark点*/
        //    cv::Point2f mark_size;/**<mark大小*/
        //    cv::Point2f mark_angle;/**<mark角度*/
        //    cv::Point2f mark_center;/**<mark中心*/
        //};
        jrsdata::Component::Type component_type;  /**<创建类型*/
        AddMarkType add_mark_type;/**<mark 创建类型*/
    };

    enum class DataUpdateType
    {
        NONE = 0,
        ADD_DATA,
        DELETE_DATA,
        UPDATE_DATA,
        SELECT_DATA,
    };

    enum class OperateWindowType
    {
        NONE,
        COMPONENT_CAD,
        COMP_DETECT_WINDOW,
        COMP_DETECT_SHOW_WINDOW,
        COMP_SUB_DETECT_WINDOW,
        PAD,
        PAD_DETECT_WINDOW,
        PAD_SUB_DETECT_WINDOW,
    };

    struct DetectTableParam
    {
        int id;//序号
        std::string main_barcode;//条码
        std::string detect_time;//检测时间
        int track_id;//轨道号
        DetectResult detect_result;//结果
    };
    using DetectTableParamVec = std::vector<DetectTableParam>;
    struct DetectResultRatioParam
    {
        int board_pass_number;              /**< 统计 主板直通个数*/
        int board_ng_number;                /**< 统计 主板的NG个数*/
        int board_misjudge_number;          /**< 统计 主板的误判个数*/

        int subboard_pass_number;           /**< 统计 子板的直通个数*/
        int subboard_ng_number;             /**< 统计 子板的Ng个数*/
        int subboard_misjudge_number;       /**< 统计 子板的误判个数*/

        int component_pass_number;          /**< 统计 原件的pass个数*/
        int component_ng_number;            /**< 统计 原件的ng个数*/
        int component_misjudge_number;      /**< 统计 原件的误判个数*/
    };
    struct DetectResultTypeParam
    {
        std::string name;//名称或错误类型
        int count;//数量
        double percent;//百分比
    };
    using DetectResultTypeParamVec = std::vector<DetectResultTypeParam>;
    struct DetectStatisticsViewParam
    {
        std::optional<DetectResultViewParam> detect_result_param;//工程名称等
        std::optional<DetectTableParam> detect_table_param_board;//主板检测结果
        std::optional<DetectTableParamVec> detect_table_param_subboard;//子板检测结果
        std::optional<DetectResultRatioParam> detect_result_ratio_param;//直通率图
        std::optional<DetectResultTypeParamVec> detect_result_device_name_param_vec;//检测缺陷元件名表
        std::optional<DetectResultTypeParamVec> detect_result_device_type_param_vec;//检测缺陷错误类型表
    };
    using DetectStatisticsViewParamPtr = std::shared_ptr<DetectStatisticsViewParam>;
    /**<所有的设置参数 */
    struct SettingViewParam : public ViewParamBase
    {
        SystemParam sys_param;  /**  系统参数  */
        CommonParam comm_param; /**< 普通参数  */
        MachineParam machine_param; /**< 机台参数 */
        SettingViewParam()
            : sys_param({}), comm_param({}), machine_param({})
        {
        }
    };
    JRSREFLECTION(SettingViewParam, sys_param, comm_param, machine_param);
    /**< 机台检测参数 */
    struct MachineStateViewParam : public ViewParamBase
    {
        /** <BOARD 检测状态 */
        enum BoardDetectState
        {
            CHECKING,  /**< 检测中 */
            PASS,      /**< 检测完成，结果为pass*/
            NG,        /**< 检测完成，结果为NG*/
            WAITING,   /**< 等待检测中*/
            GOOD,      /**< 检测完成，结果为GOOD*/
            SKIP,      /**< MARK识别失败,状态为SKIP*/
        };
        /** <停止状态 */
        enum StopState
        {
            IDLE,       /**< 闲置*/
            ALARM,      /**< 报警*/
            E_STOP,     /**< 急停*/
        };
        /** <检测流程状态 */
        enum DetectFlowState
        {
            REQUIRE_BOARD,    /**< 需要上板 */
            ENTER_BOARD,      /**< 正在进板*/
            DETECT_FOV,      /**< 正在检测FOV*/
            OUT_BOARD,       /**< 正在出板*/
        };
        std::optional<std::pair<BoardDetectState, DetectFlowState>>  current_detect_state;      /**< 当前检测状态 包含板子检测和流程检测状态*/
        std::optional<StopState> current_stop_state;                                            /**< 当前停止状态 */
        bool is_auto_run;                                                                      /**< 是否启用自动运行 */
        int total_fov;                                                                          /**< Fov数量*/
        int detected_fov;                                                                       /**< 已检测Fov数量*/

        MachineStateViewParam()
            : current_detect_state(std::nullopt)
            , current_stop_state(std::nullopt)
            , is_auto_run(false)
            , total_fov(0)
            , detected_fov(0)

        {
        }
    };
    using BoardDetectAndDetectFlowState = std::optional<std::pair<jrsdata::MachineStateViewParam::BoardDetectState, jrsdata::MachineStateViewParam::DetectFlowState>>;
    using MachineStateViewParamPtr = std::shared_ptr<MachineStateViewParam>;
    /** <* 自动运行面板参数 */
    struct AutoRunPanelViewParam :public ViewParamBase
    {
        bool flow_switch;
    };
    using AutoRunPanelViewParamPtr = std::shared_ptr<AutoRunPanelViewParam>;
    /**
     * .  运行面板参数 by zhangyuyu 2024.10.26
     */
    struct ControlPanelViewParam : public ViewParamBase
    {
        MachineStateViewParamPtr machine_state_param;                                 /**< 机台状态参数*/
        AutoRunPanelViewParamPtr autorun_panel_param;                                 /**< 自动运行面板参数*/
        std::shared_ptr<SettingViewParam> setting_param_ptr;                          /**< 设置参数*/
        ProjectParamPtr current_project_param;                                        /**< 当前执行的工程参数*/
        std::map<std::string/*工程名称*/, ProjectParamPtr> multi_project_param_map;   /**< 多工程参数*/
        bool is_load_link_project; /**< 是否导入关联工程 by zhangyuyu*/
        ControlPanelViewParam()
            : current_project_param(nullptr)
            , machine_state_param(nullptr)
            , autorun_panel_param(nullptr)
            , multi_project_param_map{}
            , is_load_link_project{ false }
        {
        }
    };
    struct ShowListViewParam
    {
        int m_subboard_id;
        bool m_update_subboard_data;
        int m_part_number_id;
        bool m_update_part_number_data;
        int m_device_id;
        bool m_update_device_data;
        ShowListViewParam() :
            m_subboard_id(-1),
            m_update_subboard_data(false),
            m_part_number_id(-1),
            m_update_part_number_data(false),
            m_device_id(-1),
            m_update_device_data(false)
        {
        }
        // 自定义赋值操作符
        ShowListViewParam& operator=(const ShowListViewParam& other)
        {
            if (this != &other)
            { // 防止自我赋值
                m_subboard_id = other.m_subboard_id;
                m_update_subboard_data = other.m_update_subboard_data;
                m_part_number_id = other.m_part_number_id;
                m_update_part_number_data = other.m_update_part_number_data;
                m_device_id = other.m_device_id;
                m_update_device_data = other.m_update_device_data;
            }
            return *this;
        }
    };
    struct QueryListViewParam
    {
        int m_subboard_id;
        bool m_update_subboard_data;
        int m_part_number_id;
        bool m_update_part_number_data;
        int m_device_id;
        bool m_update_device_data;
        std::string m_query_condition;
        int m_type;
        SHOW_TYPE m_show_type;
        QueryListViewParam()
        {
            m_subboard_id = -1;
            m_update_subboard_data = false;
            m_part_number_id = -1;
            m_update_part_number_data = false;
            m_device_id = -1;
            m_update_device_data = false;
            m_query_condition = "";
            m_type = 0;
            m_show_type = SHOW_TYPE::SHOW_ALL_DEVICE;
        }
    };

    struct GraphicsInfo
    {
        double x_coordinate; /**< graphic x 坐标 */
        double y_coordinate; /**< graphic y 坐标 */
        double width;        /**< graphic 宽度 */
        double height;       /**< graphic 高度 */
        double angle;        /**< graphic 旋转角度 */
        std::string graphics_id;//唯一标识
        std::string new_id;  //新的唯一标识
    };
    struct RenderViewParam :public ViewParamBase
    {
        //ProjectParamPtr project_param; /**< 工程参数*/
        cv::Mat affine_transform_matrix;  /**<仿射变换矩阵*/
        cv::Mat show_image; /**< 显示图片 */
        std::string read_local_img_path; /**< 读取本地图片路径 */
        std::unordered_map<int, std::string> read_local_img_path_hash;  /**< 读取本地图片路径哈希表 */
        GraphicsInfo selected_item_info; /**< 选中item信息 */
        std::string add_graphic_name; /**< 添加的图形类型 如：矩形，圆形，不规则图形*/
        double draw_angle;   /**< graphic 绘制角度*/
        cv::Mat show_3d_image; /**< 3D 显示模型文件*/
        cv::Mat show_2d_image; /**< 3D 显示的2D纹理图*/
        jrsdata::JrsImageBuffer image_buffer; /**< 图像缓存 */
        LightImageType show_2d_type;    /**< 渲染界面*/
        RenderViewParam()
            : show_image(cv::Mat())
            , affine_transform_matrix(cv::Mat())
            , read_local_img_path("")
            , selected_item_info({})
            , show_3d_image(cv::Mat())
            , show_2d_image(cv::Mat())
            , draw_angle(0)
            , show_2d_type{ LightImageType::RGB }
            //, project_param({})
        {
        }
    };
    enum class SettingType
    {
        Undefined,
        Motion,
        Project
    };
    struct Point
    {
        double x;
        double y;
        Point(double x_, double y_)
            :x(x_)
            , y(y_)
        {
        }
        Point()
            :x(0)
            , y(0)
        {
        }
    };
    // ! 轨道停板位置
    struct BoardStopPosition
    {
        Point left;
        Point right;
        BoardStopPosition()
        {
        }
    };
    // 运控配置文件参数
    struct MotionSetting
    {
        std::string config_name;            /**< 配置文件名称 */
        int barcode_mode;                    /**< 条码枪模式模式[0:无条码枪,1:外置条码枪,2:内置条码枪] */
        int cycleCount;                     /**< 老化次数 */
        int enterDirection;                 /**< 进料方向[0:左进,1:右进] */
        int leaveDirection;                 /**< 出料方向[0:左出,1:右出] */
        int trackCout;                      /**< 轨道数量 */
        int trackSelection;                 /**< 双轨设置[0:1-3定轨,1:1-4定轨] */
        bool autotrack;                     /**< 启用自动板宽 */
        int trackMode;                      /**< 轨道模式[0:在线模式,1:单机模式,2:老化模式] */
        bool fixedTest;                     /**< 定板测试 */
        std::vector<bool> transportMode;    /**< 第1个为true表示轨道1直通模式,第2个为true表示轨道2直通模式 */
        std::vector<bool> unLoadMode;       /**< 第1个为true表示等待测试结果出板,第2个为true表示无后站强制出板 */
        std::vector<double> board_width_base;   /**< 第1个为轨道1板宽零点基准值,第2个为轨道2板宽零点基准值 */
        std::vector<BoardStopPosition> board_stop;      /**< 轨道停板位置 */
        MotionSetting()
            : barcode_mode(0)
            , cycleCount(0)
            , enterDirection(0)
            , leaveDirection(0)
            , trackCout(1)
            , trackSelection(0)
            , trackMode(1)
            , autotrack(false)
            , transportMode(std::vector<bool>())
            , fixedTest{ false }
            , unLoadMode(std::vector<bool>())
            , board_width_base(std::vector<double>())
        {
        }
    };
    // 配置文件
    struct ConfigSetting
    {
        std::string event_name;
        SettingType setting_type;
        MotionSetting motion_setting;
        ConfigSetting()
            :event_name("")
            , setting_type(SettingType::Undefined)
        {
        }
    };

    // 算法规格比例结构体DetectSpec
    struct AlgoSpecRatioParam
    {
        AlgoSpecRatioParam()
        {
            x_offset_ratio = 33.0f;
            y_offset_ratio = 33.0f;
            rotate_ratio = 60.0f;
            score_ratio = 70.0f;
            area_ratio = 20.0f;
            height_measurement = 100.0f;
            relative_height = 100.0f;
            weld_length_ratio = 67.0f;
            weld_width_ratio = 67.0f;
            weld_fullness_ratio = 67.0f;
            bridge_length_ratio = 100.0f;
            block_ratio = 66.0f;
            enable = false;
        };
        float x_offset_ratio;         /** 偏移X(%) */
        float y_offset_ratio;         /** 偏移Y(%) */
        float rotate_ratio;           /** 旋转(%) */
        float score_ratio;            /** 分数(%) */
        float area_ratio;             /** 面积百分比(%) */
        float height_measurement;     /** 高度量测 */
        float relative_height;        /** 相对高度 */
        float weld_length_ratio;      /** 焊接长度(%) */
        float weld_width_ratio;       /** 焊接宽度(%) */
        float weld_fullness_ratio;    /** 焊接饱满度(%) */
        float bridge_length_ratio;    /** 锡桥长度(%) */
        float block_ratio;            /** 区块百分比(%) */

        bool enable;                 /**参数是否有效 */
    };
    JRSREFLECTION(AlgoSpecRatioParam, x_offset_ratio, y_offset_ratio, rotate_ratio, score_ratio, area_ratio, height_measurement, relative_height, weld_length_ratio, weld_width_ratio, weld_fullness_ratio, bridge_length_ratio, block_ratio, enable)

        // 算法规格分母结构体
        struct AlgoSpecDenominatorParam
    {
        AlgoSpecDenominatorParam()
        {
            x_offset = 2.0f;
            y_offset = 2.0f;
            rotate_offset = 10.0f;
            score_offset = 100.0f;
            area_offset = 100.0f;
            height_measurement_offset = 0.0f;
            relative_height_offset = 0.0f;
            weld_length_offset = 0.5f;
            weld_width_offset = 0.5f;
            weld_fullness_offset = 100.0f;
            bridge_length_offset = 0.2f;
            block_offset = 100.0f;
            enable = false;
        };
        float x_offset;                     /** 尺寸偏移X */
        float y_offset;                     /** 尺寸偏移Y */
        float rotate_offset;                /** 角度旋转偏移 */
        float score_offset;                 /** 分数偏移 */
        float area_offset;                  /** 面积百分比偏移 */
        float height_measurement_offset;    /** 高度量测偏移 */
        float relative_height_offset;       /** 相对高度偏移 */
        float weld_length_offset;           /** 焊接长度偏移 */
        float weld_width_offset;            /** 焊接宽度偏移 */
        float weld_fullness_offset;         /** 焊接饱满度偏移 */
        float bridge_length_offset;         /** 锡桥长度偏移 */
        float block_offset;                 /** 区块百分比偏移 */

        bool enable;                        /**参数是否有效 */
    };
    JRSREFLECTION(AlgoSpecDenominatorParam, x_offset, y_offset, rotate_offset, score_offset, area_offset, height_measurement_offset, relative_height_offset, weld_length_offset, weld_width_offset, weld_fullness_offset, bridge_length_offset, block_offset, enable)

        // 算法规格参数管控结构体
        struct AlgoSpecManagerParam
    {
        AlgoSpecRatioParam ratio_param;                 /** 算法规格比例 */
        AlgoSpecDenominatorParam denominator_param;     /** 算法规格分母 */
    };

    //！ 维修站需要的数据结构
    struct RepairData
    {
        std::string event_name; /**< 事件名称*/
        std::string project_name; /**< 工程名称*/
        std::unordered_map<int, cv::Mat> compress_entirety_board_imgs;//! 低分辨率大图
        std::vector<BriefComponentInfo> brief_component_info_list;//! 简要元件信息
    };

    //! 元件库里面单个元件的结构体
    struct ComponentEntity : jrsdata::DataBase
    {
        std::vector<jrsdata::Template> mask_img;        /** 模板图 */
        jrsdata::Template display_img;     /** 展示图 */
        int width;                         /** 元件宽度:像素 */
        int height;                        /** 元件高度:像素 */
        jrsdata::PNDetectInfo detect_info; /** 检测区域信息 */
        std::string part_name;             /** 元件料号名称 */
        std::string bind_part_name;        /** 元件库元件名称(料号跟随) */

        float offset_x;                     /** 展示图中心点与元件中心点的偏移x */
        float offset_y;                     /** 展示图中心点与元件中心点的偏移y */

        // 默认构造函数
        ComponentEntity()
            : width(0), height(0), offset_x(0.0f), offset_y(0.0f)
        {
            this->file_param.file_type = jrsdata::FileType::BIN;
            this->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        }
    };
    JRSREFLECTION(ComponentEntity, mask_img, display_img, width, height, detect_info, part_name, bind_part_name, offset_x, offset_y);

    struct ComponentEntitys : jrsdata::DataBase
    {
        std::vector<ComponentEntity> components_param;  /** 元件库数据 */
        std::string component_lib_path;                 /** 元件库地址 */
    };
    JRSREFLECTION(ComponentEntitys, components_param, component_lib_path);

    struct GraphicsViewShape
    {
        enum Type
        {
            None,
            Rect,
            Circle,
            Line,
            Text,
            Image,
            Region,
            Polygon,  /**< 额外添加 Polygon 以支持多边形 */
        };

        Type type;              /**< 图形类型 */
        cv::Scalar color;       /**< 颜色 (BGR 格式) */

        cv::Point2f center_position; /**< 位置（用于 Rect、Circle、Text） */
        cv::Size size;         /**< 尺寸（用于 Rect、Image） */
        int radius;            /**< 半径（用于 Circle） */
        int thickness;         /**< 线宽（用于 Line、Circle、Polygon 边框） */
        std::string text;      /**< 文字内容（用于 Text） */
        int font_face;         /**< 字体（用于 Text） */
        double font_scale;     /**< 字体大小（用于 Text） */

        std::vector<cv::Point2f> polygon_points; /**< 用于存储多边形顶点（用于 Polygon） */
        GraphicsViewShape()
            : type(Rect), color(cv::Scalar(0, 0, 0)), center_position(0.f, 0.f),
            size(0, 0), radius(0), thickness(1),
            font_face(cv::FONT_HERSHEY_SIMPLEX), font_scale(1.0)
        {
        }
        GraphicsViewShape(Type type_, cv::Scalar color_, cv::Point2f center_position_,
            cv::Size size_ = cv::Size(), int radius_ = 0, int thickness_ = 1,
            std::string text_ = "", int font_face_ = cv::FONT_HERSHEY_SIMPLEX,
            double font_scale_ = 1.0,
            std::vector<cv::Point2f> polygon_points_ = {})
            : type(type_), color(color_), center_position(center_position_),
            size(size_), radius(radius_), thickness(thickness_), text(std::move(text_)),
            font_face(font_face_), font_scale(font_scale_),
            polygon_points(std::move(polygon_points_))
        {
        }
    };

    //! 多算法添加结构体
    struct MultiAlgoParam
    {
        std::vector<std::string> body_algo_names;   /** 本体算法名集合 */
        std::vector<std::string> location_algo_names;   /** 元件算法名集合 */
        std::vector<std::string> pad_algo_names;   /** pad算法名集合 */

        std::string algo_list;                      /** JSON字符串,存储了自定义算法列表 */
    };

    // 检测框操作类型
    enum class DetectOperateType
    {
        RECTS_COPY = 0,                 /** 检测框复制(单个或多个) */
        COMPONENT_RECTS_COPY,           /** 元件检测框复制 */
        HORIZONTAL_MIRROR_COPY,         /** 水平镜像复制 */
        VERTICAL_MIRROR_COPY,           /** 垂直镜像复制 */
        ROTATE_90_COPY,                 /** 旋转90度复制 */
        ROTATE_180_COPY                 /** 旋转180度复制 */
    };

    //! 检测框复制结构体
    struct DetectCopyParam
    {
        std::vector<jrsdata::DetectWindow> cur_select_detect_win;                   /** 当前选中的检测框 */
        std::vector <std::vector<jrsdata::Template>> cur_select_detect_templete;    /** 当前选中的检测框对应的模版图 */
        ComponentEntity select_entity;                                              /** 要复制的元件库元件(只使用其检测框) */
        DetectOperateType detect_operate_type;                                      /** 检测框操作类型 */
        DetectCopyParam()
            :cur_select_detect_win({}),
            detect_operate_type(DetectOperateType::RECTS_COPY)
        {

        }
    };


    struct OperateViewParam :public ViewParamBase
    {
        MachineParam machine_param; /**< 机台参数信息 */
        ProjectParam project_param;
        DeviceParam device_param;
        ConfigSetting config_setting_param;
        ShowListViewParam show_list_params;//元件名列表参数
        RepairData repair_data; //! 维修站需要的数据
        ComponentEntitys entitys; // 元件库数据
        DetectCopyParam detect_copy_param; // 检测框复制参数
        MultiAlgoParam multi_algo_param; // 多算法数据
        std::unordered_map<std::string, ComponentDetectResult> component_detect_results; /**< 元件的检测状态OK/NG key:元件名称，value:元件的结果状态*/
        std::list<std::string> algo_name_lists;
        DetectStatisticsViewParam detect_statistics_view_param;
        std::optional<AddComponentViewParam> add_component_view_param;
        std::optional <MultiBoardEventParamPtr> multi_board_event_param_ptr; /**< 多联板事件参数 */
        OperateViewParam()
            : device_param({})
            , project_param({})
            , algo_name_lists({})
            , machine_param({})
            , detect_statistics_view_param({})
            , add_component_view_param({})
            //, detect_result_param({})
            //, detect_table_param_board({})
        {
        }
    };
    struct CADEventParam :public ViewParamBase
    {
        std::vector<jrsdata::CadStruct> cad_info;
        ProjectParamPtr project_param;
    };
    struct NewFileInfo
    {
        double height_board;       /**< 产品高度 (单位mm)*/
        double width_board;        /**< 产品宽度 （单位mm）*/
        bool is_scan;             /**< 是否立即执行扫图*/
        NewFileInfo()
            : height_board{ }
            , width_board{ }
            , is_scan{ false }
        {
        }
    };
    struct ProjectEventInfo
    {
        enum class Step
        {
            CREATE = 0,
            OPEN,
            SAVE,
        };
        Step step = Step::CREATE;
        std::string project_name;  /**< 工程名称*/
        std::string project_path;  /**< 工程路径*/
        std::list<std::string> project_name_lists;/**< 已经导入的工程名称列表 zhangyuyu 2025.6.8*/
        std::string link_project_name; /**< 关联的工程名称*/
        NewFileInfo info;
    };

    struct ProjectEventParam :public ViewParamBase
    {
        ProjectParamPtr project_param;
        ProjectEventInfo project_event_info;
        //std::vector<ComponentSaveInfo> standard_info; //!建模时保存的标准元件信息,主要是图片 by zhangyuyu 2025.1.9
        ProjectEventParam()
            :project_param(std::make_shared<ProjectParam>()), project_event_info{} //standard_info{}
        {
        }
        ProjectEventParam(const ProjectParamPtr& val)
            :project_param(val)
        {
        }
    };

    struct CadEventParam
    {
        enum class Step
        {
            NONE = 0,
            SELECT_COMPONENT_CAD_POSITIONED,
            SELECT_ORI_POSITION_FOR_ALIGNMENT,
            SELECT_CUR_POSITION_FOR_ALIGNMENT,
            CONFIRM_CUR_POSITION_FOR_ALIGNMENT,
            CLEAR_POSITION_FOR_ALIGNMENT,
            MANUAL_ADD_CAD,
            AI_ADD_CAD,
            DELETE_CAD,
        };
        Step step = Step::NONE;
        std::string component_name;
        std::string part_name;
    };
    struct PadEventParam
    {
        enum class Step
        {
            NONE = 0,
            CREATE,
            ALTER
        };
        enum class PadShape
        {
            RECT, // 矩形
            CIRCLE, // 圆形
        };
        Step step = Step::NONE;
        ComponentUnit::PadType create_type = ComponentUnit::PadType::MIRROR;
        PadShape shape = PadShape::RECT;
        ComponentUnit::Direction direction;
        std::string group_name;
    };

    const std::unordered_map<ComponentUnit::PadType, std::string> PadEventParamPadCreateTypeMap
    {
        //{ ComponentUnit::PadType::SINGLE,"单个" },
        {ComponentUnit::PadType::MIRROR,"对称"},
        {ComponentUnit::PadType::ARRAY,"阵列"},
        //{ComponentUnit::PadType::MIRROR_SYMMETRY_ARRAY,"镜像阵列"},
        //{ComponentUnit::PadType::INSIDE,"内部"},
    };
    const std::unordered_map<PadEventParam::PadShape, std::string> PadEventParamPadShapeMap
    {
        {PadEventParam::PadShape::RECT,"矩形"},
        {PadEventParam::PadShape::CIRCLE,"圆形"},
    };

    struct SelectEventParam
    {
        std::string component_name;
        std::string part_name;
        std::string model_name;
        std::string subboard_name;
        std::string window_name;
        //std::string mark_name;
        std::string layer_name;
        std::string show_img_type;
        jrsdata::LightImageType light_type;
    };
    struct CreateGraphicsParam
    {
        std::string name;
        std::string layer;
        std::string group_name;
    };
    /**< 渲染界面事件参数 */
    struct RenderEventParam :public ViewParamBase
    {
        MultiBoardEventParamPtr multi_param_ptr; ///< 多联板事件参数
        MultiBoardEventParam multi_param; ///< 多联板事件参数 /**TODO: 即将删除改字段！！！！* HJC*/
        CadEventParam cad_param;          ///< CAD事件参数
        SelectEventParam select_param;    ///< 选中事件参数
        CreateGraphicsParam create_graphics_param; ///< 创建图形参数
        PadEventParam pad_param;                   ///< PAD创建参数
        EditViewDataPtr edit_data_update_ptr;      ///< 编辑界面数据更新
        int vision_mode;                           ///< 渲染界面状态
    };

    /**< 算法界面事件参数 */
    struct AlgoEventParam : public ViewParamBase
    {
        DataUpdateType             data_operate_type = DataUpdateType::NONE;

        int                        template_id;
        std::string                template_color_param;
        jrsdata::LightImageType    light_type;
        cv::Mat                    region_mat;
        std::map<jrsdata::LightImageType, cv::Mat> current_selected_detect_all_light_img;//! 当前选中的检测区域的所有灯光图 by zhangyuyu 2024.12.4
        std::vector<Template>      templates;
        DetectWindow               detect_win;
        DetectAlgorithm            algorithm_param;
        int                        sub_wind_type = 0;
        std::vector<std::string>   delete_detect_win_name_list;

        const PNDetectInfo* cur_select_spec_and_detect_region = nullptr;
        const DetectWindow* cur_select_detect_win = nullptr;
        const Component* cur_select_component = nullptr;
        const ComponentUnit* cur_select_component_unit = nullptr;
        DetectWindow current_update_result_state;/**< 当前准备更新的检测框结果状态参数，主要用于更新检测框的结果状态 by zhangyuyu 2025.3.30*/
        std::vector<std::pair<int, cv::RotatedRect>> detect_rects; //! 在截图中检测框的具体位置

        std::vector<const Component*> same_part_numb_components;

        std::vector<std::tuple<bool, cv::RotatedRect, std::string, cv::Mat>> detect_win_results;

        QWidget* cur_algo_view = nullptr;

        std::string sub_window_name;

        std::shared_ptr<jrsoperator::OperatorParamBase> operator_param;
        std::optional<std::vector<jrsdata::ComponentDetectResult>> component_detect_results;
    };

    // /**
    //  * @brief 渲染界面更新参数
    //  */
    // struct UpdateOperatorParam
    // {
    //     enum class UpdateOperatorType
    //     {
    //         UPDATE_OPERATOR,
    //         CREATE_OPERATOR,
    //         DELETE_OPERATOR,
    //     };
    //     UpdateOperatorType type;
    //     std::string mark_name;         ///< 基准点
    //     std::string barcode_name;      ///< 条码
    //     std::string subboard_name;     ///< 子板
    //     std::string component_name;    ///< 元件
    //     std::string pad_name;          ///< 焊盘
    //     std::string submark_name;      ///< 基准点-子板
    //     std::string subbarcode_name;   ///< 条码-子板
    //     std::string window_name;       ///< 检测框
    //     std::string algo_name;         ///< 算法-目前与检测框同名
    //     std::string subwindow_name;    ///< 子检测框
    //     std::string part_name;         ///< 料号
    //     std::string model_name;        ///< 模型
    // };

    /**< 图形更新触发工程更新事件参数 */
    struct SelectUnitsName
    {
        std::string current_name;   /**< 当前选择的名称*/
        std::string subboard_name;  /**< 子板名称*/
        std::pair<jrsdata::Component::Type, std::string> type_and_component_name;   /**< 元件名称*/
        std::string unit_name;      /**< 项目名称*/
        std::string window_name;    /**< 检测框名称*/
        std::string subwindow_name; /**<子检测框名称*/
    };
    struct GraphicsUpdateProjectEventParam : public ViewParamBase
    {
        std::unordered_map<std::string, std::vector<std::string>> graphics_map; ///< {图层:{ids}}
        std::pair<std::string, SelectUnitsName> graphics_and_select_units; ///< {图层:{SelectUnitsName}}
        cv::Rect temp_region;
        ShowListViewParam show_list_params;//元件名列表参数
        std::vector<SubBoardDataStructPtr> m_sub_board_datas;
    };
    /**<系统状态界面参数*/
    struct SystemStateViewParam : public ViewParamBase
    {
        std::string check_item_name;
        SystemStateMap check_items;
        int progress_rate; /**< 加载比 0-100 */
        bool is_ok;  /**< 是否全部OK */
        SystemStateViewParam()
            :check_items({})
            , progress_rate(0)
            , is_ok(true)
        {
        }
    };

    struct EntiretyBoard
    {
    };
    /**< 检测结果界面参数 */

    struct ComponentListViewParam : public ViewParamBase
    {
        enum class UpdateType :int
        {
            NONE,
            X,
            Y,
            Width,
            Height,
            Angle
        };
        //jrsdata::Component component_info;
        UpdateType update_type;  /**<更新类型*/

        float body_x;
        float body_y;
        float body_width;
        float body_height;
        float body_angle;
        std::string subboard_name; /**< 子板名称用来标记元件属于那个子板*/
        std::string component_name;
        std::string component_part_number;

        ComponentListViewParam()
            :body_x(0), body_y(0), body_width(0), body_height(0), body_angle(0), subboard_name(""),
            component_name(""), component_part_number(""), update_type(UpdateType::NONE)
        {
        }
    };

    /**********************************在线调试参数*****************************************/
    struct CurrentDebugInfo
    {
        std::string current_component_name; /**< 当前元件名称*/
        std::string current_subboard_name; /**< 当前元件子板名称*/
        std::string current_part_number; /**< 当前元件料号*/
        OneFovImgs current_component_fov_img; /**< 当前元件fov图片*/
        CurrentDebugInfo()
            : current_component_name("")
            , current_subboard_name("")
            , current_part_number("")
            , current_component_fov_img({})
        {
        }
        // 自定义赋值操作符
        CurrentDebugInfo& operator=(const CurrentDebugInfo& other)
        {
            if (this != &other)
            { // 防止自我赋值
                current_component_name = other.current_component_name;
                current_subboard_name = other.current_subboard_name;
                current_part_number = other.current_part_number;
                current_component_fov_img = other.current_component_fov_img;
            }
            return *this;
        }
    };
    struct OnlineDebugViewParam :public ViewParamBase
    {


        OnlineDebugParmPtr online_debug_param; /**< 在线调试参数*/
        CurrentDebugInfo current_debug_info; /**< 当前调试的元件信息*/
        bool is_waitting_debug_info;
        std::atomic<bool> is_stop_debug;/**< 是否停机调试*/
        OnlineDebugViewParam()
            :online_debug_param({})
            , current_debug_info({})
            , is_waitting_debug_info(true)
            , is_stop_debug(true)
        {
        }
    };

    using SystemStateViewParamPtr = std::shared_ptr<SystemStateViewParam>;
    using RenderViewParamPtr = std::shared_ptr<RenderViewParam>;
    using ComponentListViewParamPtr = std::shared_ptr<ComponentListViewParam>;
    using OperateViewParamPtr = std::shared_ptr<OperateViewParam>;
    using GraphicsUpdateProjectEventParamPtr = std::shared_ptr<GraphicsUpdateProjectEventParam>;

    using ProjectEventParamPtr = std::shared_ptr<ProjectEventParam>;
    using InvokeProjectFun = std::function<int(const ProjectEventParamPtr&)>;

    using RenderEventParamPtr = std::shared_ptr<RenderEventParam>;
    using InvokeViewParamBaseFun = std::function<int(const ViewParamBasePtr&)>;
    using AlgoEventParamPtr = std::shared_ptr<AlgoEventParam>;
    using SettingViewParamPtr = std::shared_ptr<SettingViewParam>;
    using ControlPanelViewParamPtr = std::shared_ptr<ControlPanelViewParam>;
    using OnlineDebugViewParamPtr = std::shared_ptr<OnlineDebugViewParam>;

    using InvokeSettingViewParamFun = std::function<int(const SettingViewParamPtr&)>;
    using InvokeSystemStateParamFun = std::function<int(const jrsdata::SystemStateViewParamPtr&)>;
    using InvokeControlPanelViewParamFun = std::function<int(const ControlPanelViewParamPtr&)>;
    using InvokeOperateViewParamFun = std::function<int(const OperateViewParamPtr&)>;
    using InvokeAlgoEventParamFun = std::function<int(const AlgoEventParamPtr&)>;
    using InvokeOnlineDebugViewParamFun = std::function<int(const OnlineDebugParmPtr&)>;
    //struct  TemplateOperatorParam : public OperatorParamBase
    //{
    //    DataUpdateType          data_operate_type;
    //    std::vector<Template>   template_param;
    //};
    //using TemplateOperatorParamPtr = std::shared_ptr<TemplateOperatorParam>;
}
#endif // !__PARAM_HPP__