#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "imagebinaryalgo.h"

#pragma warning(pop)

void BinaryAlgo::GetHsTableAs2DArray(std::vector<unsigned char>& hs_table_one_dim,
    unsigned char hs_table[180][256])
{
    if(hs_table_one_dim.size() != 180 * 256)
    {
        return;
    }
    for (int i = 0; i < 180; ++i)
    {
        for (int j = 0; j < 256; ++j)
        {
            hs_table[i][j] = hs_table_one_dim[i * 256 + j];
        }
    }
}

int BinaryAlgo::GetBinaryImage(const Mat& image, 
    BinProcessParams& params, Mat& binary)
{
    if (params.binary_type_index == RGB_BINARY)
    {
        if (params.r_thre_min == 0 && params.r_thre_max == 255 &&
            params.g_thre_min == 0 && params.g_thre_max == 255 &&
            params.b_thre_min == 0 && params.b_thre_max == 255 ) return 0;
        m_image_is_binary = true;
		GetRgbBinary(image, 
            params.r_thre_min,  params.r_thre_max, 
            params.g_thre_min,  params.g_thre_max, 
            params.b_thre_min,  params.b_thre_max);
    }
    else if (params.binary_type_index == GRAY_BINARY)
    {
        if (params.gray_thre_min == 0&&params.gray_thre_max == 255) return 0;
        m_image_is_binary = true;
        cv::Mat gray;
        if (params.gray_type_index == WIDGE)
        {
            gray = ConvertToGrayWidge(image);
        }
        else if (params.gray_type_index == MAX)
        {
			gray = ConvertToGrayMax(image);
        }
		else if (params.gray_type_index == MIN)
        {
			 gray = ConvertToGrayMin(image);
        }
		else if (params.gray_type_index == MEAN)
        {
			 gray = ConvertToGrayAvg(image);
		}  
		else 
        {
            return -1;
        }
		GetGrayBinary(gray, 
             params.gray_thre_min,  params.gray_thre_max);
    }
	else if (params.binary_type_index == HSV_BINARY)
    {
        if (params.hsv_paramas.type == 0) return 0;
        m_image_is_binary = true;
        GetHsvBinary(image, params.hsv_paramas);
    }
	else if (params.binary_type_index == HEIGHT_BINARY)
    {

        m_image_is_binary = true;
        GetHeightBinary(image, params.height_bin_nums,
            int(params.height_thre_min), int(params.height_thre_max),
            params.min_hei, params.max_hei);
    }
    else
    {
        return -1;
    }
    binary = binary_;
    return 0;
}

bool BinaryAlgo::GetImageIsBinary() const
{
    return m_image_is_binary;
}

int BinaryAlgo::GetRgbHist(const cv::Mat& rgb_image,
    vecf& r_hist, vecf& g_hist, vecf& b_hist)
{
    if (rgb_image.empty() || rgb_image.channels() != 3)
    {
        r_hist = CalculateNormalizedHistogram(rgb_image, 256);
        g_hist = CalculateNormalizedHistogram(rgb_image, 256);
        b_hist = CalculateNormalizedHistogram(rgb_image, 256);
    }
    else
    {
        Mat3 image_splits;
        cv::split(rgb_image, image_splits);
        r_hist = CalculateNormalizedHistogram(image_splits[2], 256);
        g_hist = CalculateNormalizedHistogram(image_splits[1], 256);
        b_hist = CalculateNormalizedHistogram(image_splits[0], 256);
    }	
    return 0;
}

int BinaryAlgo::GetGrayHist(const cv::Mat& rgb_image,
    vecf& gray_hist, int mode,cv::Mat& gray)
{
    if (rgb_image.empty()) return 0;
    if (mode == 0)
    {
        gray = ConvertToGrayWidge(rgb_image);
    }
    else if (mode == 1)
    {
        gray = ConvertToGrayMax(rgb_image);
    }
	 else if (mode == 2)
    {
        gray = ConvertToGrayMin(rgb_image);
    }
	 else if (mode == 3)
    {
        gray = ConvertToGrayAvg(rgb_image);
    }
     else
    {
        return -1;
    }
	gray_hist = CalculateNormalizedHistogram(gray, 256);
    if (gray.channels() != 3)
    {
        cv::cvtColor(gray, gray, cv::COLOR_GRAY2BGR);
    }
    return 0;
}

int BinaryAlgo::GetHeightHist(const cv::Mat& height_image, vecf& height_hist, 
		int bin_nums,float min_height,float max_height)
{
    if (height_image.empty()) return -1;
    if(height_image.type()!=CV_32FC1) return 0;

    height_hist.assign(bin_nums, 0.0f);
    float bin_width = (max_height - min_height) / bin_nums;
    auto height_copy = height_image.clone();
    if (height_copy.channels() == 3)
    {
        cv::cvtColor(height_copy, height_copy, cv::COLOR_BGR2GRAY);
    } 
    for (int y = 0; y < height_copy.rows; ++y)
    {
        const float* row_ptr = height_copy.ptr<float>(y);
        for (int x = 0; x < height_copy.cols; ++x)
        {
            float pixel_value = row_ptr[x];
            if (pixel_value >= min_height && pixel_value <= max_height)
            {
                int bin_index = static_cast<int>((pixel_value - min_height) / bin_width);
                if (bin_index >= 0 && bin_index < bin_nums)
                {
                    height_hist[bin_index] += 1.0f;
                }
            }
        }
    }


    float total_pixels = float(cv::countNonZero((height_image >= min_height) & (height_image <= max_height)));
    if (total_pixels > 0) 
    {
        for (float& value : height_hist)
        {
            value /= total_pixels;
        }
    }
    return 0;
}

cv::Mat BinaryAlgo::ConvertToGrayWidge(const cv::Mat& image)
{
    if(image.empty()) return cv::Mat();
    if (image.channels() != 3) return image;
    else
    {
        cv::Mat gray(image.size(), CV_8UC1);
        if (image.empty()) return cv::Mat();
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        return gray;
    }
}

cv::Mat BinaryAlgo::ConvertToGrayMax(const cv::Mat& image)
{
    if(image.channels()==1) return image;
    else
    {
        cv::Mat gray(image.size(), CV_8UC1);

        for (int i = 0; i < image.rows; ++i) {
            for (int j = 0; j < image.cols; ++j) {
                cv::Vec3b rgb = image.at<cv::Vec3b>(i, j);
                gray.at<uchar>(i, j) = std::max({ rgb[0], rgb[1], rgb[2] });
            }
        }
        return gray;
    }
}

cv::Mat BinaryAlgo::ConvertToGrayMin(const cv::Mat& image)
{
    if (image.channels() == 1) return image;
    else
    {
        cv::Mat gray(image.size(), CV_8UC1);

        for (int i = 0; i < image.rows; ++i) {
            for (int j = 0; j < image.cols; ++j) {
                cv::Vec3b rgb = image.at<cv::Vec3b>(i, j);
                gray.at<uchar>(i, j) = std::min({ rgb[0], rgb[1], rgb[2] });
            }
        }

        return gray;
    }
    
}

cv::Mat BinaryAlgo::ConvertToGrayAvg(const cv::Mat& image)
{
    if(image.channels() == 1)   return image;
    else
    {
        cv::Mat gray(image.size(), CV_8UC1);

        for (int i = 0; i < image.rows; ++i) {
            for (int j = 0; j < image.cols; ++j) {
                cv::Vec3b rgb = image.at<cv::Vec3b>(i, j);
                gray.at<uchar>(i, j) = (rgb[0] + rgb[1] + rgb[2]) / 3;
            }
        }

        return gray;
    }
   
}

std::vector<float> BinaryAlgo::CalculateNormalizedHistogram(const cv::Mat& image, int hist_size)
{
	float range[] = {0, 256};
    const float* hist_range = {range};
    cv::Mat hist;
    cv::calcHist(&image, 1, 0, cv::Mat(), hist, 1, &hist_size, &hist_range);
    cv::Mat normalized_hist;
    cv::normalize(hist, normalized_hist, 0, 1, cv::NORM_MINMAX);
    std::vector<float> hist_vector;
    hist_vector.reserve(hist_size);
    for (int i = 0; i < hist_size; ++i)
    {
        hist_vector.push_back(normalized_hist.at<float>(i));
    }
    return hist_vector;
}

int BinaryAlgo::GetRgbBinary(const Mat& rgb_image, int r_min, int r_max, int g_min, int g_max, int b_min, int b_max)
{
    if (rgb_image.empty() || rgb_image.channels() != 3) 
    {
        return -1; 
    }

    Mat r_binary, g_binary, b_binary;
    
    cv::inRange(rgb_image, cv::Scalar(b_min, g_min, r_min),
        cv::Scalar(b_max, g_max, r_max), binary_);

    return 0; 
}

int BinaryAlgo::GetHsvBinary(const Mat& input_img,ColorWheelThreshVal& params)
{
	if (input_img.empty())
    {
        return -1;
    }

    binary_ = cv::Mat(input_img.size(), CV_8UC1, cv::Scalar(255));

    if (input_img.channels() == 1)
    {
        // Vͨ����ֵ��
        if (params.type & 1)
        {
            cv::inRange(input_img, cv::Scalar(params.v_low), cv::Scalar(params.v_high), binary_);
        }
    }
    else if (input_img.channels() == 3)
    {
        cv::Mat hsv;
        cv::cvtColor(input_img, hsv, cv::COLOR_BGR2HSV);
        std::vector<cv::Mat> split_images;
        cv::split(hsv, split_images);
        Mat h = split_images[0];
        Mat s = split_images[1];
        Mat v = split_images[2];

        if (params.type & 1)
        {
            cv::inRange(v, cv::Scalar(params.v_low), cv::Scalar(params.v_high), binary_);
        }
        unsigned char hs_table[180][256];
        GetHsTableAs2DArray(params.hs_table_one_dim, hs_table);
        Mat mat_table = Mat(180, 256, CV_8UC1, &hs_table, 0);
        if (params.type & 2)
        {
            for (int i = 0; i < h.rows; i++) {
                uchar* h_channel_ptr = h.ptr<uchar>(i);
                uchar* s_channel_ptr = s.ptr<uchar>(i);
                for (int j = 0; j < h.cols; j++) {
                    uchar h_channel_value = h_channel_ptr[j];
                    uchar s_channel_value = s_channel_ptr[j];
                    cv::Point current_point(static_cast<int>(s_channel_value),
                        static_cast<int>(h_channel_value));

                    if (mat_table.at<uchar>(current_point) != 255)
                    {
                        binary_.at<uchar>(i, j) = 0;
                    }
                }

            }
        }
        else
        {
            return -2;
        }
        return 0;
    }
    return 0;
}

int BinaryAlgo::GetGrayBinary(const Mat& gray_image, int gray_min, int gray_max)
{
	 if (gray_image.empty() || gray_image.channels() != 1) 
     {
        return -1; 
     }
    
    inRange(gray_image, cv::Scalar(gray_min), 
        cv::Scalar(gray_max), binary_);
    return 0;
}

int BinaryAlgo::GetHeightBinary(const Mat& height_image, int bin_nums ,
		int bin_min, int bin_max,float hei_min,float hei_max)
{
	if (height_image.empty()) 
     {
        return -1; 
     }

    float bin_width = (hei_max - hei_min) / bin_nums;

    float bin_min_value = hei_min + bin_min * bin_width;
    float bin_max_value = hei_min + bin_max * bin_width;

    if (height_image.channels() == 1)
    {
        inRange(height_image, cv::Scalar(bin_min_value), cv::Scalar(bin_max_value),
            binary_);
    }
    else
    {
        inRange(height_image, cv::Scalar(bin_min_value, bin_min_value, bin_min_value), cv::Scalar(bin_max_value, bin_max_value, bin_max_value),
            binary_);
    }

    return 0;
}
