#pragma once
//PREBUILD
#include "datapch.h"
//#include "tablebase.h"

namespace jrsdatabase {
    class BoardTable : public jrsdatabase::TableBase
    {
    public:
        BoardTable(const std::string& table_name_);
        ~BoardTable();

        virtual int Create(const std::shared_ptr<DB_Mysql>& conn_ptr_) override;
        virtual int Drop(const std::shared_ptr<DB_Mysql>& conn_ptr_) override;
        virtual int Show(jrstable::TableParamBasePtr& db_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;

        virtual int Insert(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;
        virtual int Insert(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;

        virtual int Update(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;
        virtual int Update(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;

        virtual int Replace(const jrstable::TableParamBasePtr& table_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;
        virtual int Replace(const std::vector<jrstable::TableParamBasePtr>& tables_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;

        /**< 查询接口 待完善  */
        virtual int Select(const jrsselect::SelectorParamBasePtr& selector_ptr_, const std::shared_ptr<DB_Mysql>& conn_ptr_) override;

    private:
        /** 更改时间 string 类型的字段 */
        int InitIndex(const std::shared_ptr<DB_Mysql>& conn_ptr_);
        int InitFields(const std::shared_ptr<DB_Mysql>& conn_ptr_);
        int InitDataFields(const std::shared_ptr<DB_Mysql>& conn_ptr_);
        int InitVarcharFields(const std::shared_ptr<DB_Mysql>& conn_ptr_);

        std::mutex _mtx;
    };
}