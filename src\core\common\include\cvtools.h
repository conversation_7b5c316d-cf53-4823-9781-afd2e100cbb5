#ifndef CVTOOLS_H
#define CVTOOLS_H

#pragma warning(disable : 4244 4127)

#include "jtoolsapi.hpp"

#include <opencv2/opencv.hpp>
#include <Eigen/Dense>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace jcvtools
{
    class JT_EXPORTS JrsHomMat2D
    {
    private:
        Eigen::Matrix3d matrix;

        /**
         * @fun  GenTranslationMatrix
         * @brief 生成平移矩阵.
         * @param tx x轴平移.
         * @param ty y轴平移.
         * @data 2024.12.30
         * @return 平移矩阵.
         * <AUTHOR>
         */
        static Eigen::Matrix3d GenTranslationMatrix(double tx, double ty);
        /**
         * @fun  GenRotationMatrix
         * @brief 生成顺时针旋转矩阵.
         * @param angle_rad 旋转角度，弧度制.
         * @data 2024.12.30
         * @return 顺时针旋转矩阵.
         * <AUTHOR>
         */
        static Eigen::Matrix3d GenRotationMatrix(double angle_rad);

        /**
         * @fun  GenScaleMatrix
         * @brief 生成放缩矩阵.
         * @param sx x轴缩放.
         * @param sy y轴缩放.
         * @data 2024.12.30
         * @return 放缩矩阵.
         * <AUTHOR>
         */
        static Eigen::Matrix3d GenScaleMatrix(double sx, double sy);

    public:
        JrsHomMat2D() : matrix(Eigen::Matrix3d::Identity()) {}
        JrsHomMat2D(const cv::Mat& mat);
        explicit JrsHomMat2D(const Eigen::Matrix3d& mat) : matrix(mat) {}

        /**
         * @fun  HomMat2dIdentity
         * @brief 生成单位矩阵.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void HomMat2dIdentity();

        /**
         * @fun  AddHomMat2dRotate
         * @brief 添加顺时针旋转矩阵.
         * @param angle 旋转角度，角度制.
         * @param center_x 中心点x坐标.
         * @param center_y 中心点y坐标.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void AddHomMat2dRotate(double angle, double center_x, double center_y);

        /**
         * @fun  AddHomMat2dScale
         * @brief 添加放缩矩阵.
         * @param scale_x x轴缩放.
         * @param scale_y y轴缩放.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void AddHomMat2dScale(double scale_x, double scale_y);

        /**
         * @fun  AddHomMat2dTranslate
         * @brief 添加平移矩阵.
         * @param tx x轴平移.
         * @param ty y轴平移.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void AddHomMat2dTranslate(double tx, double ty);

        /**
         * @fun  HomMat2dCompose
         * @brief 变换矩阵组合.
         * @param other 另一个变换矩阵.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void HomMat2dCompose(const JrsHomMat2D& other);

        /**
         * @fun  HomMat2dInvert
         * @brief 求取逆变换矩阵.
         * @data 2024.12.30
         * @return 逆变换矩阵.
         * <AUTHOR>
         */
        JrsHomMat2D HomMat2dInvert() const;

        /**
         * @fun  AffineTransRotatedRect
         * @brief 旋转矩形仿射变换.
         * @param rect 旋转矩形.
         * @data 2024.12.30
         * @return 仿射变换后的旋转矩形.
         * <AUTHOR>
         */
        cv::RotatedRect AffineTransRotatedRect(const cv::RotatedRect& rect) const;

        /**
         * @fun  AffineTransRect
         * @brief 矩形仿射变换.
         * @param rect 矩形.
         * @data 2024.12.30
         * @return 仿射变换后的矩形.
         * <AUTHOR>
         */
        cv::RotatedRect AffineTransRect(const cv::Rect& rect) const;

        /**
         * @fun  AffineTransPoint
         * @brief 点仿射变换.
         * @param point 点.
         * @data 2024.12.30
         * @return 仿射变换后的点.
         * <AUTHOR>
         */
        cv::Point2f AffineTransPoint(const cv::Point2f& point) const;

        /**
         * @fun  AffineTransPoints
         * @brief 点集仿射变换.
         * @param points 点集.
         * @data 2024.12.30
         * @return 仿射变换后的点集.
         * <AUTHOR>
         */
        std::vector<cv::Point2f> AffineTransPoints(const std::vector<cv::Point2f>& points) const;

        /**
         * @fun  PrintMatrix
         * @brief 打印变换矩阵.
         * @data 2024.12.30
         * <AUTHOR>
         */
        void PrintMatrix() const;

        /**
         * @fun  toMat
         * @brief 转换为cv::Mat.
         * @data 2024.12.30
         * @return cv::Mat.
         * <AUTHOR>
         */
        cv::Mat toMat() const;
    };

    class JT_EXPORTS JrsCVTools {
    public:
        /**
         * @fun ConvertMarkToContours
         * @brief 将mark 图片 转换成轮廓
         * @param mark 二进制图片
         * @return 轮廓点
         * <AUTHOR>
         * @date 2025.5.26
         */
        static std::vector<std::vector<cv::Point>> ConvertMarkToContours(const cv::Mat& mark);
        /**
         * @fun IsBinaryImage
         * @brief 判断图片死否为二值化图片
         * @param image
         * @return
         * <AUTHOR>
         * @date 2025.5.26
         */
        static bool IsBinaryImage(const cv::Mat& image);

    };
}

#endif