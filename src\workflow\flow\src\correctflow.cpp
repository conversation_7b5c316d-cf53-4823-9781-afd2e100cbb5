﻿//STD
#include <regex>

//Custom
#include "correctflow.h"
#include "projectparam.hpp"
#include "coordinatetransform.hpp"
#include "algorithmenginemanager.h"
#include "algoexecuteparam.hpp"
#include "cvtools.h"
#include "operatorparambase.h"
#include "algoexecuteparamprocess.h"
namespace jrsworkflow
{

    CorrectFlow::CorrectFlow(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_, LogicFunType logic_invoke_)
        :algo_engine_manager_ptr(algo_engine_manager_)
        , logic_invoke(logic_invoke_)
        , project_data_process_ptr(std::make_shared<jrsparam::ProjectDataProcess>())
    {
    }
    CorrectFlow::~CorrectFlow()
    {
    }
    int CorrectFlow::CorrectProject(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
    {
        mark_fov_count = 0;
        project_ptr = project_param_;
        project_data_process_ptr->SetProjectParam(project_ptr);
        project_data_process_ptr->SetResolution(work_flow_param.resolution_x, work_flow_param.resolution_y);
        
        mark_correct_finish.store(false);
        src_mark_coordinate.clear();
        dst_mark_coordinate.clear();
        logic_invoke(mark_fov_centers, true);
        std::unique_lock<std::mutex> lock(mark_correct_mtx);
        mark_correct_cv.wait(lock, [this]
            {
                return mark_correct_finish.load();
            });
        mark_correct_finish.store(false);

        if (!ValidateMark(src_mark_coordinate, dst_mark_coordinate))
        {
            return jrscore::WorkFlowError::E_AOI_WORKFLOW_MARK_FAILURE;

        }
        cv::Mat matrix;//旋转平移矩阵
        jrstool::CoordinatrTransform<float>::GetAffineMatrix(src_mark_coordinate, dst_mark_coordinate, matrix);
        project_data_process_ptr->AlignmentBoard(project_ptr->board_info, matrix);
        return jrscore::AOI_OK;
    }
    int CorrectFlow::StopCorrect()
    {
        mark_correct_finish.store(true);
        mark_correct_cv.notify_one();
        return 0;
    }
    void CorrectFlow::SetWorkFlowParam(const WorkFlowParam& param_)
    {
        work_flow_param = param_;
    }
    void CorrectFlow::SetMarkInfo(const std::vector<PCBPathPlanning::Fov>& fov_centers, const PCBPathPlanning::OutputParams& fov_out_, const std::unordered_map<int/*FOV的ID*/, std::vector<jrsdata::Component>>& mark_point_info_)
    {
        mark_fov_centers = fov_centers;
        assign_mark_point = mark_point_info_;
        fov_out = fov_out_;
    }
    void CorrectFlow::AddMarkFovBuffer(const jrsdata::JrsImageBuffer& fov_img)
    {
        mark_fov_count.fetch_add(1,std::memory_order_relaxed);;

        auto iter = assign_mark_point.find(fov_img.one_fov_imgs.fov_id);
        if (iter == assign_mark_point.end())
        {
            Log_ERROR("流程运行异常，传回的图片FOV ID错乱！");
            mark_correct_finish.store(true);
            mark_correct_cv.notify_one();
            return;
        }
        auto res_components = iter->second;

        /** 获取当前 FOV在大图中的中心坐标 */
        cv::Point2f fov_center;
        for (auto& value : fov_out.fovs)
        {
            if (value.fov_path.fovid == fov_img.one_fov_imgs.fov_id)
            {
                fov_center = value.fov_path.center;
                break;
            }
        }




        jrsdata::ComponentUnit mark_unit;

        for (auto& component_value : res_components)
        {
            auto& units = project_ptr->board_info.part_nums_and_detect_regions[component_value.component_part_number].units;
            if (!units.empty())
            {
                mark_unit = std::ref(units.at(0));
            }
            for (auto& windows_info : project_ptr->board_info.part_nums_and_detect_regions[component_value.component_part_number].detect_models)
            {

                for (auto& value_window : windows_info.second.detect_model)
                {

                    cv::Mat matrix_to_src_image;
                    float mark_src_x = value_window.cx + component_value.x;
                    float mark_src_y = value_window.cy + component_value.y;
                    float mark_dst_x;
                    float mark_dst_y;
                    jrsparam::ExecuteAlgoParam exect_param;
                    jrsparam::ExecuteModeInfo executeinfo;

                    executeinfo.src_img = fov_img.one_fov_imgs.imgs;
                    int fov_img_width = fov_img.one_fov_imgs.imgs.find(static_cast<jrsdata::LightImageType>(0))->second.cols;
                    int fov_img_height = fov_img.one_fov_imgs.imgs.find(static_cast<jrsdata::LightImageType>(0))->second.rows;

                    executeinfo.execute_mode = jrsparam::ExecuteMode::AutoMode;
                    jrscore::CoordinateTransform::PixelRectCenterToTopLeft(static_cast<int>(fov_center.x), static_cast<int>(fov_center.y), executeinfo.fov_left_top_x, executeinfo.fov_left_top_y, fov_img_width, fov_img_height);
                    project_data_process_ptr->GetDetectWindowExecuteParam(component_value, mark_unit, value_window, exect_param, matrix_to_src_image, executeinfo);
                    auto res_dete = algo_engine_manager_ptr->ExecuteSpecificAlgoDrive(exect_param);
                    if (!res_dete)
                    {
                        Log_ERROR("MARK：", component_value.component_name, "检测异常，返回nullptr结果！");
                        src_mark_coordinate.clear();
                        dst_mark_coordinate.clear();
                        continue;
                    }

                    auto result_algo_item_value = algo_engine_manager_ptr->GetAlgoExecuteResultParam(res_dete, exect_param.algo_name);
                    {
                        //!将mark的结果保存下来，到界面上去显示
                        std::lock_guard<std::mutex> lock(mark_result_mtx);
                        jrsdata::ComponentDetectResult component_status_result_value;
                        component_status_result_value.component_name = component_value.component_name;
                        component_status_result_value.part_number = component_value.component_part_number;
                        component_status_result_value.subboard_id = "0";
                        component_status_result_value.sub_board_name = component_value.subboard_name;
                        component_status_result_value.result_status = result_algo_item_value.result_algo_status;
                        component_status_result_value.defect_name = value_window.defect_name;
                        component_status_result_value.fov_ids = component_value.fov_ids;
                        component_status_result_value.detect_window_results[value_window.name] = res_dete;
                        flow_inspection_result->detect_result_param->component_status_result.emplace_back(component_status_result_value);

                    }
                    //!保存mark数据
                    SaveMarkExecuteParamInfo(component_value,res_dete,exect_param.algo_name,exect_param.algo_param);
                    if (!result_algo_item_value.result_algo_status)
                    {
                        Log_ERROR("MARK：", component_value.component_name, "分数为：", result_algo_item_value.result_score, "检测失败");
                        src_mark_coordinate.clear();
                        dst_mark_coordinate.clear();
                        continue;
                    }
                    cv::Point2f detect_res;
                    detect_res.x = result_algo_item_value.result_x_coordinate;
                    detect_res.y = result_algo_item_value.result_y_coordinate;
                    jcvtools::JrsHomMat2D hom_2d(matrix_to_src_image);
                    cv::Point2f transform_res = hom_2d.AffineTransPoint(detect_res);
                    mark_dst_x = transform_res.x;
                    mark_dst_y = transform_res.y;

                    jrstool::PointLabel<float> src_;
                    jrstool::PointLabel<float> dst_;
                    src_.pt_.x = mark_src_x;
                    src_.pt_.y = mark_src_y;

                    dst_.pt_.x = mark_dst_x;
                    dst_.pt_.y = mark_dst_y;

                    src_mark_coordinate.emplace_back(src_);
                    dst_mark_coordinate.emplace_back(dst_);

                }
            }
        }


        if (fov_out.fovs.size() == mark_fov_count.load())
        {
            mark_correct_finish.store(true);
            mark_correct_cv.notify_one();
        }


    }
 
    void CorrectFlow::SetFlowInspectionResultPtr(const FlowInspectionResultParamPtr& flow_inspection_result_)
    {
        flow_inspection_result = flow_inspection_result_;
    }

    void CorrectFlow::Init()
    {
    }
    void CorrectFlow::InitMember()
    {
    }
    void CorrectFlow::SaveMarkExecuteParamInfo(const jrsdata::Component& component_value, const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const std::string& algo_name_, const std::string& algo_param_)
    {
        auto algo_execute_info = std::make_shared<jrsdata::ComponentSaveInfo>();
        algo_execute_info->algo_execute_rect_param = algo_engine_manager_ptr->GetAlgoExecuteRectInfo(algo_detect_result_);
        algo_execute_info->algo_param = algo_param_;
        //! 将时间中的":"字符用“_”替换
        auto board_start_time = jtools::StringOperation::ReplaceString(flow_inspection_result->detect_result_param->start_detect_time, ":", "_");
        algo_execute_info->current_time = board_start_time;
        algo_execute_info->input_img = algo_detect_result_->input_image;
        algo_execute_info->project_name = project_data_process_ptr->GetProjectParam()->project_name;
        algo_execute_info->component_name = component_value.component_name;
        algo_execute_info->algo_name = algo_name_;
        algo_execute_info->subboard_name = component_value.subboard_name;
        algo_execute_info->hom_matrix = algo_detect_result_->hom_matrix;
        algo_execute_info->result_img = algo_detect_result_->result_image_group;
        algo_execute_info->output_mask_image = algo_detect_result_->output_mask_image;
        algo_execute_info->resolution_x = algo_detect_result_->x_resolution;
        algo_execute_info->resolution_y = algo_detect_result_->y_resolution;
        //! 模板信息
        for (auto& temp_value : algo_detect_result_->template_data)
        {
            jrsdata::TemplateInfo temp_info;
            temp_info.light_id = static_cast<int>(temp_value.id);
            temp_info.template_image = temp_value.template_img;
            temp_info.template_color_param = temp_value.color_params.ToJson();
            algo_execute_info->template_info_vector.push_back(temp_info);
        }
        flow_inspection_result->detect_result_param->workflow_component_algo_info_vector.emplace_back(algo_execute_info);
    }
    bool CorrectFlow::ValidateMark(const std::vector<jrstool::PointLabel<float>>& src, const std::vector<jrstool::PointLabel<float>>& dst, float threshold)
    {
        if (src.size() != dst.size()|| dst.empty()||src.empty()||src.size()!= fov_out.fovs.size())
        {
            return false;
        }
        //! 计算src和dst的相对位置关系是否超过阈值
        for (size_t i = 0; i < src.size(); ++i)
        {
            for (size_t j = i + 1; j < src.size(); ++j)
            {
                float distance_src = jrstool::CoordinatrTransform<float>::GetBetweenDistance(src[i], src[j]);
                float distance_dst = jrstool::CoordinatrTransform<float>::GetBetweenDistance(dst[i], dst[j]);
                if (std::abs(distance_src - distance_dst) > threshold)
                {

                    Log_ERROR("第", i, "个mark和第", j, "个mark的相对位置关系超出阈值，模板mark之间距离：", distance_src, ",算法检测出来mark之间距离：", distance_dst);
                    return false;
                }
            }
        }
        return true;
    }
}
