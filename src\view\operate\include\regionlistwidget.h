/*********************************************************************
 * @brief  检测框列表控件,管理检测框增删改查.
 *
 * @file   regionlistwidget.h
 *
 * @date   2024.09.19
 * <AUTHOR>
 *********************************************************************/
#ifndef REGIONLISTWIDGET_H
#define REGIONLISTWIDGET_H
#include <QListWidget>
#include "algoselectwidget.h"
#include "imagetabledelegate.h"

namespace jrsdata
{
    struct DetectWindow;
}

class DetectWindowListWidget : public QListWidget
{
    Q_OBJECT

public:
    DetectWindowListWidget(const QString& _model_name, const QString& list_widget_property, QWidget* parent = nullptr);
    /**
     * @brief  增加检测框.
     *
     * @fun    CreateDetectWindowItem
     * @param  window_name 名称
     * @param  value 检测框参数
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    void CreateDetectWindowItem(const DetectWindowItemValue& value, const bool& set_selected = false);


    int  SetDetectWindowItemValue(const DetectWindowItemValue& value, QListWidgetItem* item);


    int  GetDetectWindowItemValue(const QListWidgetItem* item, DetectWindowItemValue& value);

    /**
     * @fun GetAllDetectWindowItemValue
     * @brief 获取当前算法组下所有检测框的列表信息
     * @return  返回列表下所有检测框信息
     * <AUTHOR>
     * @date 2025.3.20
     */
    std::vector<DetectWindowItemValue> GetAllDetectWindowItemValue();
    /**
     * @brief  更新检测框.
     *
     * @fun    UpdateDetectWindowItem
     * @param  window_name 名称
     * @param  value 检测框参数
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    void UpdateDetectWindowItem(const QString& window_name, const DetectWindowItemValue& value);
    /**
     * @brief  删除检测框.
     *
     * @fun    DeleteDetectWindowItem
     * @param  window_name 名称
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    void DeleteDetectWindowItem(const QString& window_name);
    /**
     * @brief  查找检测框.
     *
     * @fun    ReadDetectWindowItem
     * @param  window_name 名称
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    QListWidgetItem* ReadDetectWindowItem(const QString& window_name);

    /**
     * @brief  绑定选中检测框到指定组.
     *
     * @fun    BindSelectedDetectWindowItems
     * @param  group_name 组名称
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    void BindSelectedDetectWindowItems(const QString& group_name);
    /**
     * @brief  解绑选中检测框.
     *
     * @fun    UnBindSelectedDetectWindowItems
     *
     * @date   2024.09.24
     * <AUTHOR>
     */
    void UnBindSelectedDetectWindowItems();

    /**
     * @fun  SetAlgoList
     * @brief 设置算法列表.
     * @param _algo_list
     * @data 2024.10.29
     * <AUTHOR>
     */
    void SetAlgoList(const std::vector<std::pair<std::string, std::string>> _algo_list);

    /**
     * @fun  SetDefectTypeList
     * @brief 设置缺陷类型列表.
     * @param _defect_type_list
     * @data 2024.10.29
     * <AUTHOR>
     */
    void SetDefectTypeList(const std::vector<std::string> _defect_type_list);

    /**
     * @fun  GetModelName
     * @brief 获取检测模块名称.
     * @data 2024.10.29
     * @return
     * <AUTHOR>
     */
    QString GetModelName();


    /**
     * @fun  DeleteSelectedWidget
     * @brief 删除选中检测框.
     * @param delete_list 删除列表
     * @data 2024.11.03
     * <AUTHOR>
     */
    void DeleteSelectedWidget(std::vector<std::string>& delete_list);


    void SelectDetectWin(const QString& window_name);


    std::string GetCurSelectName() const;

signals:
    void SignalDetectWinValueChanged(DetectWindowItemValue& det_win_info); ///< 检测框值改变
    void SignalRegionDelete(const QString& window_name); ///< 检测框删除
    void SignalSelectedDetectWinChanged(DetectWindowItemValue& det_info); ///< 选中检测框改变

private slots:
    void SlotSelectedChanged();

private:

    ImageTableDelegate* item_delegate = nullptr;

    QString model_name = "";
    std::vector<std::pair<std::string, std::string>> algo_list;
    std::vector<std::string> defect_type_list;
    std::string cur_select_name;
    std::map<std::string, int> show_index_map;

    //bool SetValue(QWidget* widget, const DetectWindowItemValue& value);
    void SelectCreatedRegion(const QString& window_name);
    void UpdateEditStatus();

};
#endif // REGIONLISTWIDGET_H