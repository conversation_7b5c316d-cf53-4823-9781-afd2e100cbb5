﻿#include "motionprotocal.h"

namespace jrsdevice 
{


	JSON MotionProtocal::ConstructCheckerPack(const std::string sendMsg, const std::string check, const int checkTime, const bool justSend)
	{
		JSON pack;
		pack["sendMsg"] = sendMsg;
		pack["check"] = check;
		pack["checkTime"] = checkTime;
		pack["justSend"] = justSend;
		pack["sendTime"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
		return pack;
	}

	JSON MotionProtocal::MotionMsgDecode(const std::string msg)
	{
		auto msgSplit = SplitString(msg, ':');

		if (msgSplit.size() != 2)
		{
			return PosMsgDecode("error","1");
		}
		if (msgSplit[0] == "POS")
		{
			return PosMsgDecode(msgSplit[0], msgSplit[1]);
		}
		else if (msgSplit[0] == "ASKSTATUS")
		{
			return StatusMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "AXISLIMIT")
		{
			return AXISLIMITMsgDecode(msgSplit[0], msgSplit[1]);
		}
		else if (msgSplit[0] == "PROCESSSTATUS")
		{
			return PROCESSStatusMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "CURSETTING")
		{
			return CURSETTINGMsgDecode(msgSplit[0], msg.substr(std::strlen(msgSplit[0].c_str()) + 1));
		}
		else if (msgSplit[0] == "INPUTSTATUS")
		{
			return InputStatusMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "OUTPUTSTATUS")
		{
			return OutputStatusMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "ERRORLIST")
		{
			return ScriptErrorMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "SCRIPTFUNCTION")
		{
			return ScriptMsgDecode(msgSplit[0],msgSplit[1]);
		}
		// ASKSTOP ASKPAUSE PHOTOOK REPHOTO OFFSET SENDNC ASKSTART GROUPHOME
		else if(msgSplit[0] == "ASKSTOP" || msgSplit[0] == "ASKPAUSE" ||msgSplit[0] == "PHOTOOK" ||msgSplit[0] == "REPHOTO" ||
			msgSplit[0] == "OFFSET" ||msgSplit[0] == "SENDNC" ||msgSplit[0] == "ASKSTART" || msgSplit[0] == "GROUPHOME")
		{
			return ProcessMsgDecode(msgSplit[0],msgSplit[1]);
		}
		else if (msgSplit[0] == "ERROR" || msgSplit[0] == "TIPS" || msgSplit[0] == "MSG" || msgSplit[0] == "STATUS"|| msgSplit[0] == "ARRIVE")
		{
			return PushMsgDecode(msgSplit[0], msgSplit[1]);
		}
		return FormatCheck(msgSplit[0], msgSplit[1]);
	}

	JSON MotionProtocal::ConstructFeedBackPack(const std::string check, const JSON data)
	{
		JSON json;
		json["CHECK"] = check;
		json["DATA"] = data;
		return json;
	}

	JSON MotionProtocal::FormatCheck(const std::string cmd, const std::string motionMsgSepColon)
	{
		bool result = true;
		std::string errMsg = "";
		if (motionMsgSepColon == "")
		{
			errMsg = "指令格式错误:" + cmd;
			result = false;
		}
		else
		{
			auto errMSGSplit = SplitString(motionMsgSepColon, ',');
			if (errMSGSplit[0] == "OK")
			{
			}
			else
			{
				result = false;
				if (errMSGSplit.size() > 1)
				{
					errMsg = errMSGSplit[1];
				}
				else
				{
					errMsg = "指令格式错误:" + cmd;
				}
			}
		}

		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		return ConstructFeedBackPack(cmd, res);
	}

	JSON MotionProtocal::PosMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台位置协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (posMsg.size() < 3)
		{
			errMsg = "平台位置协议识别失败，请确认机构发送的轴个数是否正确";
			result = false;
		}
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["pos"] = posMsg;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::StatusMsgDecode(const std::string check, const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台流程状态协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (posMsg.size() < 1)
		{
			errMsg = "平台流程状态协议识别失败";
			result = false;
		}
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["status"] = posMsg;
		return ConstructFeedBackPack(check + ":" + posMsg.at(1), res);
	}

	JSON MotionProtocal::AXISLIMITMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台限位协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (posMsg.size() < 3)
		{
			errMsg = "平台限位协议识别失败，请确认机构发送的轴个数是否正确";
			result = false;
		}
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["limit"] = posMsg;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::PROCESSStatusMsgDecode(const std::string check, const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台流程状态协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["status"] = posMsg;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::CURSETTINGMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台配置协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["setting"] = msg.substr(3);
		return ConstructFeedBackPack(check, res);
	}
	JSON MotionProtocal::InputStatusMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台输入协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (posMsg.size() < 3)
		{
			errMsg = "平台输入协议识别失败，请确认机构发送的输入个数是否正确";
			result = false;
		}
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["input"] = posMsg;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::OutputStatusMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台输出协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (posMsg.size() < 3)
		{
			errMsg = "平台输出协议识别失败，请确认机构发送的输出个数是否正确";
			result = false;
		}
		// 删除首元素
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
			posMsg.erase(posMsg.begin());
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["output"] = posMsg;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::ScriptErrorMsgDecode(const std::string check,const std::string msg)
	{
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台脚本错误协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
		}
		std::vector<std::string> list;
		if (posMsg.size() == 2)
		{
			list = SplitString(posMsg[1], '|');
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["list"] = list;
		return ConstructFeedBackPack(check, res);
	}

	JSON MotionProtocal::ScriptMsgDecode(const std::string check,const std::string msg)
	{
		// SCRIPTFUNCTION:OK,56,\r\n
		// SCRIPTFUNCTION:NG,56,errmsg\r\n
		bool result = true;
		std::string errMsg = "";
		if (msg == "")
		{
			errMsg = "平台脚本协议识别失败";
			result = false;
		}
		auto posMsg = SplitString(msg, ',');
		if (!posMsg.empty())
		{
			result = posMsg.at(0) == "OK" ? true : false;
		}
		if (posMsg.size() > 2)
		{
			errMsg = posMsg[2];
		}
		JSON res;
		res["result"] = result;
		res["errMsg"] = errMsg;
		res["mode"] = std::stoi(posMsg.size() > 1 ? posMsg[1] : "0");
		return ConstructFeedBackPack(check + ":" + posMsg[1], res);
	}

	JSON MotionProtocal::ProcessMsgDecode(const std::string check, const std::string msg)
	{
		// 结构类似脚本函数返回
		// ASKSTOP:OK,1,\r\n
		// ASKSTOP:NG,1,errmsg\r\n
		return ScriptMsgDecode(check, msg);
	}

	JSON MotionProtocal::PushMsgDecode(const std::string cmd, const std::string msg)
	{
		JSON res;
		res["result"] = true;
		res["errMsg"] = "";
		res["content"] = msg;
		return ConstructFeedBackPack(cmd, res);
	}

	std::vector<std::string> MotionProtocal::SplitString(const std::string &s, char delimiter)
	{
		std::vector<std::string> tokens;
		std::string token;
		std::size_t start = 0;
		std::size_t end = 0;

		while ((end = s.find(delimiter, start)) != std::string::npos)
		{
			token = s.substr(start, end - start);
			tokens.push_back(token);
			start = end + 1;
		}

		// 添加最后一个token（如果有的话）
		if (start < s.length())
		{
			tokens.push_back(s.substr(start));
		}

		return tokens;
	}

}
