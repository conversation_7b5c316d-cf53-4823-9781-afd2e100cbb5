//C++
#include <memory>
#include <regex>
//Custom
#include "visioninspectionflow.h"
#include "correctflow.h"
#include "fovplan.h"
#include "coordinatetransform.hpp"
#include "algoexecuteparam.hpp"
#include "algorithmenginemanager.h"
#include "generalimagetool.h"
#include "colorparams.h"
#include "tools.h"
#include "operatorparambase.h"
#include "timeutility.h"
#include "coordinatetransformationtool.h"
#include "algoexecuteparamprocess.h"
#include "cvtools.h"
#include "jsonoperator.hpp"
#include <nlohmann/json.hpp>
//Third
#include "iguana/iguana.hpp"
#undef min
#undef max
namespace jrsworkflow
{
     const unsigned thread_pool_count = std::thread::hardware_concurrency();
    //constexpr int thread_pool_count = 6;

    VisionInspectionFlow::VisionInspectionFlow(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_, LogicFunType logic_invoke_)
        : algo_engine_manager(algo_engine_manager_)
        , logic_invoke(logic_invoke_)
        , is_detect_finish(false)
        , flow_inspection_result(std::make_shared<FlowInspectionResultParam>())
        , project_data_process_ptr(std::make_shared<jrsparam::ProjectDataProcess>())
        , parameter_process_ptr(std::make_shared<jrsparam::ParameterProcess>())

    {
        //在这里初始化线程池数量 by baron_zhang 2024-12-16
        jrs_thread_pool = std::make_shared<JrsThreadPool>(static_cast<int>(thread_pool_count-4));

        correct_flow_controller_ptr = std::make_shared<CorrectFlow>(algo_engine_manager, logic_invoke);
    }

    VisionInspectionFlow::~VisionInspectionFlow()
    {
    }

    int VisionInspectionFlow::StartInspection(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
    {
        LogAutoRun_INFO("检测开始");

        //! 检测前参数准备
        project_param_ptr = std::make_shared<jrsdata::ProjectParam>(*project_param_); //! 工程参数
        project_data_process_ptr->SetProjectParam(project_param_ptr);
        project_data_process_ptr->SetResolution(work_flow_param.resolution_x,work_flow_param.resolution_y);
        //! 新的板子过来重置检测结果
        flow_inspection_result.reset(new FlowInspectionResultParam());
        work_flow_component_result.clear();
        flow_inspection_result->detect_result_param->detect_result = true;
        fov_count = 0;
        is_detect_finish.store(false);
        is_mark_inspection.store(true);

        //! 开始检测时间
        flow_inspection_result->detect_result_param->start_detect_time = jrscore::AOITools::GetCurrentDataTime();
        //! MARK矫正
        LogAutoRun_INFO("MARK矫正开始");
        auto res_mark = MarkCorrection();
        is_mark_inspection.store(false);
        LogAutoRun_INFO("MARK矫正结束");

        if (is_detect_finish.load())
        {
            return jrscore::AOI_OK;
        }

       if (res_mark != jrscore::AOI_OK)
       {
           LogAutoRun_ERROR("MARK 识别失败！");
           auto res_select = JRSMessageBox_ERR("WorkFlow", "MARK 识别失败！\n 是否继续检测？",jrscore::MessageButton::Yes|jrscore::MessageButton::No);
           flow_inspection_result->detect_result_param->detect_result = false;
           flow_inspection_result->detect_result_param->board_detect_status = jrsdata::BoardDetectStatus::MARK_FAILURE;
           if (res_select == jrscore::MessageButton::No)
           {
               is_detect_finish.store(true);
               detect_cv.notify_one();
               return res_mark;
       
           }
           else
           {
               is_detect_finish.store(true);
               detect_cv.notify_one();
               return res_mark;
           }
       }

        //! 元件检测
       LogAutoRun_INFO("元件检测开始");
        auto res_inspection = ComponentInspection();
        LogAutoRun_INFO("元件检测结束");

        if (res_inspection != jrscore::AOI_OK)
        {
            LogAutoRun_INFO("元件检测异常，路径规划失败！");
            JRSMessageBox_ERR("WorkFlow", "元件检测异常，路径规划失败!", jrscore::MessageButton::Yes);
            flow_inspection_result->detect_result_param->detect_result = false;
            return res_inspection;
        }




        return jrscore::AOI_OK;
    }


    void VisionInspectionFlow::AddBuffer(const jrsdata::JrsImageBuffer& imgs)
    {
        LogAutoRun_INFO("AddBuffer执行");

        //! MARK检测流程
        if (is_mark_inspection)
        {
            auto correct_ptr = std::dynamic_pointer_cast<CorrectFlow>(correct_flow_controller_ptr);
            correct_ptr->AddMarkFovBuffer(imgs);
            return;
        }
        //! 元件检测流程
        else
        {
            fov_count++;
            DetectFOV(imgs);

            //! 目前通过接收到的FOV数量来判断是否已经完成所的FOV的检测
            if (fov_out.fovs.size() == fov_count)
            {
                is_detect_finish.store(true);
                detect_cv.notify_one();
            }


        }


    }

    InspectionResultBasePtr VisionInspectionFlow::GetInspectionResult()
    {

        return flow_inspection_result;
    }

    void VisionInspectionFlow::Init()
    {
    }

    bool VisionInspectionFlow::AssignComponentToFov(std::vector<PCBPathPlanning::Fov>& fov_centers, const jrsdata::Board& board, int fov_w, int fov_h, bool is_mark)
    {
        //! 工程中所有的元件信息
        fov_out = PCBPathPlanning::OutputParams{};
        std::vector<jrsdata::Component> components;
        //TODO 修改判断是Mark还是元件 by baron_zhang 2024-12-11
        if (is_mark)
        {
            components = board.marks;
        }
        else
        {
            components = CalculateDetectComponents(board);

        }

        LogAutoRun_INFO("路径规划开始");
        //! 规划自动流程拍照路径
        auto plan_result = PlanDetectPath(components, board, fov_w, fov_h, is_mark);
        LogAutoRun_INFO("路径规划结束");
        //! 设置元件的FOV编号
        SetComponentFovId(components);

        //! 将规划出来的fov中心坐标拿出来，用于相机拍照时移动
        fov_centers = fov_out.fovs;
        return plan_result;
    }

    std::vector<jrsdata::Component> VisionInspectionFlow::CalculateDetectComponents(const jrsdata::Board& board)
    {
        std::vector<jrsdata::Component> components;

        //! 获取所有的元件
        for (auto& subboard : board.sub_board)
        {
            //! 只将启用检测的元件加入到检测元件的列表中
            std::copy_if(subboard.component_info.begin(), subboard.component_info.end(),
                std::back_inserter(components),
                [](const jrsdata::Component& comp)
                {
                    return comp.enable;
                });
            //! 添加启用的子板 bad_mark（单个）
            if (subboard.bad_mark.enable)
            {
                components.push_back(subboard.bad_mark);
            }

            //! 添加启用的子板 barcode（单个）
            if (subboard.barcode.enable)
            {
                components.push_back(subboard.barcode);
            }
        }

        //! 剔除没有检测框的元件
        for (auto it = components.begin(); it != components.end();)
        {
            auto& component_value = *it;
            auto cur_component_part_num = project_data_process_ptr->ReadPNDetectInfoRef(component_value.component_part_number);
            if (cur_component_part_num.has_value())
            {
                bool is_erase_component = true;
                for (auto& model_ : cur_component_part_num->get().detect_models)
                {
                    if (!model_.second.detect_model.empty())
                    {
                        is_erase_component = false;
                        break;
                    }
                }

                if (is_erase_component)
                {
                    //! 如果 detect_models 为空，移除当前元素
                    it = components.erase(it);
                }
                else
                {
                    ++it; //! 如果不移除，正常迭代
                }
            }
            else
            {
                //! 如果此元件没有找到对应的料号，则移除
                LogAutoRun_INFO("分配元件过程中，没有找到元件对应的料号！","该原件名和料号为：",it->component_name,",",it->component_part_number);
                it = components.erase(it);
            }
        }
        return components;
    }

    void VisionInspectionFlow::DetectFOV(const jrsdata::JrsImageBuffer& fov_img)
    {
        if (assign_component_res.empty())
        {
            LogAutoRun_INFO("流程运行异常，分配的原件结果为空！");
            return;
        }

        auto iter = assign_component_res.find(fov_img.one_fov_imgs.fov_id);
        if (iter == assign_component_res.end())
        {
            LogAutoRun_INFO("流程运行异常，传回的图片FOV ID错乱！");

            return;
        }
        /** 获取当前FOV中的所有元件 */
        auto res_components = iter->second;

        /** 获取当前 FOV在大图中的中心坐标 */
        cv::Point2f fov_center;
        for (auto& value : fov_out.fovs)
        {
            if (value.fov_path.fovid == fov_img.one_fov_imgs.fov_id)
            {
                fov_center = value.fov_path.center;
                break;
            }
        }
        DetectComponentsInFov(fov_img, fov_center, res_components);

    }

    void VisionInspectionFlow::DetectComponentsInFov(const jrsdata::JrsImageBuffer& fov_img, const cv::Point2f& fov_center, const std::vector<jrsdata::Component>& res_components)
    {
        auto fov_id = fov_img.one_fov_imgs.fov_id;
        if (fov_img.one_fov_imgs.imgs.empty())
        {
            LogAutoRun_INFO("传入的FOV图像为空,当前的FOV　ID：", fov_id);
            return;
        }
        LogAutoRun_INFO("执行当前FOV,ID为：", fov_id);
        /** 将fov的中心坐标，转换成左上角坐标 */
        int fov_left_top_x, fov_left_top_y;
        jrscore::CoordinateTransform::PixelRectCenterToTopLeft(static_cast<int>(fov_center.x), static_cast<int>(fov_center.y), fov_left_top_x, fov_left_top_y, work_flow_param.camera_fov_w, work_flow_param.camera_fov_h);

        /** 以元件为单位进行算法调用检测，获取单个元件 */
        for (auto component_value : res_components)
        {
            
            component_value.fov_ids.push_back(fov_id);
            jrs_thread_pool->Enqueue(&VisionInspectionFlow::DetectComponent, this, fov_img, component_value, fov_left_top_x, fov_left_top_y);
            //DetectComponent(fov_img, component_value, fov_left_top_x, fov_left_top_y);

        }
    }

    void VisionInspectionFlow::DetectComponent(const jrsdata::JrsImageBuffer& fov_img, const jrsdata::Component& component_value, int fov_left_top_x, int fov_left_top_y)
    {
        /** 1.设置当前检测元件的检测结果信息 */
        jrsdata::DeviceResult component_result;
        component_result.t_device.device_name = component_value.component_name;
        component_result.t_device.device_part_no = component_value.component_part_number;
        component_result.t_device.device_type = TypeToString(component_value.component_type);   
        component_result.t_device.device_angle = component_value.angle;
        component_result.t_device.subboard_id = project_data_process_ptr->GetSubBoardIdBySubBoardName(component_value.subboard_name);
        component_result.t_device.device_result = true;
        //！2 获取当前元件的检测区域信息
        std::map<std::string, jrsparam::ExecuteAlgoParam> detect_win_execute_param;
        cv::Mat matrix_to_src_image;
        jrsparam::ExecuteModeInfo execute_mode_info;
        execute_mode_info.src_img = fov_img.one_fov_imgs.imgs;
        execute_mode_info.fov_left_top_x = fov_left_top_x;
        execute_mode_info.fov_left_top_y = fov_left_top_y;
        execute_mode_info.execute_mode = jrsparam::ExecuteMode::AutoMode;

        ParapareComponentInspectionParam(component_value,detect_win_execute_param,matrix_to_src_image,execute_mode_info);
         //! 获取当前元件的料号信息
        auto cur_component_part_num = project_data_process_ptr->ReadPNDetectInfoRef(component_value.component_part_number);
        if (!cur_component_part_num)
        {
            LogAutoRun_INFO("流程中获取料号信息异常！","料号为：",component_value.component_part_number);
            return;
        }       
        auto detect_models = cur_component_part_num->get().detect_models;
        //TODO 添加location定位计算结果
        auto [correction_matrix,base_plane_temp]= GetLoactionModelMat(component_value, detect_models, detect_win_execute_param, matrix_to_src_image, component_result);
        //!3 执行所有检测框检测
        ProcessDetectWindows(detect_models,component_value, correction_matrix, base_plane_temp, detect_win_execute_param,component_result, matrix_to_src_image);

        //!4当前元件检测完成
        ComponentInspectionFinished(component_value,component_result, fov_img, fov_left_top_x, fov_left_top_y);
        
    }
    int VisionInspectionFlow::MarkCorrection()
    {

        auto correct_ptr = std::dynamic_pointer_cast<CorrectFlow>(correct_flow_controller_ptr);
        auto assign_result = AssignComponentToFov(mark_fov_centers, project_param_ptr->board_info, work_flow_param.camera_fov_w, work_flow_param.camera_fov_h, true);
        
        if (!assign_result)
        {
            PushErrorToStack(jrscore::WorkFlowError::E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE,"MARK分配失败！");
            return jrscore::WorkFlowError::E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE;
        }

        correct_ptr->SetMarkInfo(mark_fov_centers, fov_out, assign_component_res);
        correct_ptr->SetFlowInspectionResultPtr(flow_inspection_result);
        auto res_correction = correct_flow_controller_ptr->CorrectProject(project_param_ptr);

        return res_correction;
    }

    int VisionInspectionFlow::ComponentInspection()
    {

        //! 将整板上所有的原件分配到每个FOV中
        auto assign_result = AssignComponentToFov(inspection_fov_centers, project_param_ptr->board_info, work_flow_param.camera_fov_w, work_flow_param.camera_fov_h, false);
        
        if (!assign_result)
        {
            return jrscore::WorkFlowError::E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE;
        }
        flow_inspection_result->detect_result_param->fov_start_time = jrscore::AOITools::GetCurrentDataTime();
        logic_invoke(inspection_fov_centers, true);
        std::unique_lock<std::mutex> lock(detect_mtx);
        detect_cv.wait(lock, [this]
            {
                return is_detect_finish.load();
            });
        is_detect_finish.store(false);
        flow_inspection_result->detect_result_param->fov_end_time = jrscore::AOITools::GetCurrentDataTime();
        flow_inspection_result->detect_result_param->finish_detect_time = jrscore::AOITools::GetCurrentDataTime();

        return jrscore::AOI_OK;
    }

    void VisionInspectionFlow::SaveAlgoResult(const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const jrsdata::DetectWindow detect_window_info_, jrsdata::DetectWindowResult& detect_window_result)
    {
        try
        {
            detect_window_result.t_detect_window.detect_window_name = detect_window_info_.name;
            detect_window_result.t_detect_window.algorithm_name = detect_window_info_.algorithms[0].detect_algorithm_name;
            detect_window_result.t_detect_window.detect_window_result = true;
            SaveAlgoWindowResult(algo_detect_result_, detect_window_info_, detect_window_result);
            //!获取当前算法使用的灯光id
            for (auto& value : algo_detect_result_->template_data)
            {
                //!算法使用的灯光id是跟着模板走的,模板是有时可能为多个，这里取灯光图就取第一个即可 by zhangyuyu 2025.1.17
                detect_window_result.t_detect_window.light_img_id = (int)value.id;
                break;
            }
            //! 从输出规格结果中获取当前检测框的每个子检测的结果：除了特殊的算法如引脚外有多组子检测结果，其他的都是一组子检测结果 by zhangyuyu 2025.1.2
            //！将算法的检测结果保存到algorithm_data的json中，
            nlohmann::json algorithm_data;
            nlohmann::json algorithm_array;
            //jrscore::AddJsonAlgoNameArray(algorithm_array, detect_window_info_.algorithms[0].detect_algorithm_name);
            for (auto& res_value : algo_detect_result_->spec_value_params)
            {
                std::string single_detect_item_name; //! 算法的每个检测项的名称,如：x坐标 ,y坐标，面积等 by zhangyuyu 2025.1.2
                std::optional<double> single_detect_item_min;//! 算法的每个检测项的判定下限 by zhangyuyu 2025.1.2
                std::optional<double> single_detect_item_max;//! 算法的每个检测项的判定上限 by zhangyuyu 2025.1.2
                std::optional<double> single_detect_item_std_value;//! 算法的每个检测项的标准值 by zhangyuyu 2025.1.2
                double single_detect_item_result = 0.0; //! 算法的每个检测项的检测结果 by zhangyuyu 2025.1.2
                bool single_judge_status_result = false;
                //! 获取每个子检测的单个检测项检测结果，如：X坐标，Y坐标，面积等
                for (auto& spec_value : res_value.second.judge_param)
                {
                    single_detect_item_name = spec_value.first;


                    auto& cur_spec_item_value = algo_detect_result_->spec_params[single_detect_item_name];
                    //! TODO :等算法参数更新后，这里需要改放开
                    //auto& current_spec_is_judge = cur_spec_item_value.current_spec_is_judge;
                    auto& current_spec_min = cur_spec_item_value.spec_min;
                    auto& current_spec_max = cur_spec_item_value.spec_max;
                    auto& current_spec_std_value = cur_spec_item_value.std_value;

                    //! 如果建模的时候没有启用这个检测项的判定，那么就跳过，不保存，也不计算
                   /* if (!current_spec_is_judge || !*current_spec_is_judge)
                    {
                        continue;
                    }*/
                    single_detect_item_min = current_spec_min.value_or(std::numeric_limits<double>::lowest());
                    single_detect_item_max = current_spec_max.value_or(std::numeric_limits<double>::max());
                    single_detect_item_std_value = current_spec_std_value.value_or(std::numeric_limits<double>::max());
                    single_detect_item_result = res_value.second.judge_param[single_detect_item_name].algo_res;
                    single_judge_status_result = res_value.second.judge_param[single_detect_item_name].judge_res;
                    if (!spec_value.second.spec_defect_name.empty())
                    {
                        detect_window_result.t_detect_window.detect_window_result_flaw_type_name += spec_value.second.spec_defect_name.c_str();
                        detect_window_result.t_detect_window.detect_window_result_flaw_type_name.append(";");
                    }
                    //! 算法检测检测结果中有一个子检测的检测结果为false，则当前的算法检测结果为false,否则为true
                    if (!res_value.second.detect_rect_status)
                    {
                        detect_window_result.t_detect_window.detect_window_result = false;
                    }
                    nlohmann::json single_json_data;
                    jrscore::AddKeyValueToObj(single_json_data, "name", single_detect_item_name);
                    if (single_detect_item_min.has_value())
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "min", *single_detect_item_min);
                    }
                    else
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "min", "null");
                    }
                    if (single_detect_item_max.has_value())
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "max", *single_detect_item_max);
                    }
                    else
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "max", "null");
                    }
                    if (single_detect_item_std_value.has_value())
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "std_value", *single_detect_item_std_value);
                    }
                    else
                    {
                        jrscore::AddKeyValueToObj(single_json_data, "std_value", "null");
                    }
                    jrscore::AddKeyValueToObj(single_json_data, "result", single_detect_item_result);
                    jrscore::AddKeyValueToObj(single_json_data, "status", single_judge_status_result);
                    jrscore::AddObjectToArray(algorithm_array, single_json_data);
                    jrscore::AddKeyValueToObj(algorithm_data, detect_window_info_.algorithms[0].detect_algorithm_name, algorithm_array);
                }
            }
            detect_window_result.t_detect_window.window_result_data = jrscore::JsonToString(algorithm_data);
            //! 如果算法每个判定项判定后没有输出缺陷名称，则使用建模时人工指定的缺陷名称作为检测框的缺陷输出
            if (detect_window_result.t_detect_window.detect_window_result_flaw_type_name.empty())
            {
                detect_window_result.t_detect_window.detect_window_result_flaw_type_name = detect_window_info_.defect_name;
            }
            /**< 时间进行临时赋值 By:HJC 2025/1/16 */
            detect_window_result.t_detect_window.window_rejudgement_time = jrscore::AOITools::GetCurrentDataTime();
        }
        catch (const nlohmann::json::parse_error& e) {
            std::cerr << "Parse error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::type_error& e) {
            std::cerr << "Type error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::out_of_range& e) {
            std::cerr << "Out of range error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::invalid_iterator& e) {
            std::cerr << "Invalid iterator error: " << e.what() << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Other exception: " << e.what() << std::endl;
        }
    }

    void VisionInspectionFlow::SaveAlgoWindowResult(const jrsoperator::OperatorParamBasePtr& algo_detect_result_, const jrsdata::DetectWindow detect_window_info_, jrsdata::DetectWindowResult& detect_window_result)
    {
        try
        {
            nlohmann::json rect_json_arrays;
            if (algo_detect_result_->spec_value_params.size() > 1)
            {
                for (auto& direct_rects : algo_detect_result_->ori_sub_detect_rects)
                {
                    for (auto& rect_result : direct_rects.second)
                    {
                        nlohmann::json single_rect_json;
                        jrscore::AddKeyValueToObj(single_rect_json, "id", rect_result.id);
                        jrscore::AddKeyValueToObj(single_rect_json, "cx", rect_result.cx);
                        jrscore::AddKeyValueToObj(single_rect_json, "cy", rect_result.cy);
                        jrscore::AddKeyValueToObj(single_rect_json, "width", rect_result.width);
                        jrscore::AddKeyValueToObj(single_rect_json, "height", rect_result.height);
                        jrscore::AddKeyValueToObj(single_rect_json, "angle", rect_result.angle);
                        jrscore::AddKeyValueToObj(single_rect_json, "status", rect_result.status);
                        jrscore::AddObjectToArray(rect_json_arrays, single_rect_json);
                    }
                }
            }
            else if (algo_detect_result_->spec_value_params.size() == 1)
            {
                nlohmann::json single_rect_json;
                jrscore::AddKeyValueToObj(single_rect_json, "id", algo_detect_result_->ori_detect_rect.id);
                jrscore::AddKeyValueToObj(single_rect_json, "cx", algo_detect_result_->ori_detect_rect.cx);
                jrscore::AddKeyValueToObj(single_rect_json, "cy", algo_detect_result_->ori_detect_rect.cy);
                jrscore::AddKeyValueToObj(single_rect_json, "width", algo_detect_result_->ori_detect_rect.width);
                jrscore::AddKeyValueToObj(single_rect_json, "height", algo_detect_result_->ori_detect_rect.height);
                jrscore::AddKeyValueToObj(single_rect_json, "angle", algo_detect_result_->ori_detect_rect.angle);
                jrscore::AddKeyValueToObj(single_rect_json, "status", algo_detect_result_->ori_detect_rect.status);
                jrscore::AddObjectToArray(rect_json_arrays, single_rect_json);
            }
            detect_window_result.t_detect_window.algo_result_position =jrscore::JsonToString(rect_json_arrays);
        }
        catch (const nlohmann::json::parse_error& e) {
            std::cerr << "Parse error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::type_error& e) {
            std::cerr << "Type error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::out_of_range& e) {
            std::cerr << "Out of range error: " << e.what() << std::endl;
        }
        catch (const nlohmann::json::invalid_iterator& e) {
            std::cerr << "Invalid iterator error: " << e.what() << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Other exception: " << e.what() << std::endl;
        }
    }

    void VisionInspectionFlow::SaveAlgoExecuteParamInfo(jrsdata::DeviceResult& device_rsult_, const jrsoperator::OperatorParamBasePtr& algo_detect_result_,
        const std::string& algo_name_, const std::string sub_board_name_, const std::string algo_param_)
    {
        auto result_status = algo_engine_manager->GetAlgoExecuteResultStatus(algo_detect_result_);
        /** 获取机台参数是否ok和ng的都保存 */
        auto is_all_status_save = parameter_process_ptr->GetSettingParamValueByName<bool>(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_IS_SAVE_ALL_DETECT_DATA_BOOL);
        //! 如果是保存所有检测结果的话则直接保存，如果不是的话，则是result_status为false的时候才保存,帮我实现这个判断
        if (is_all_status_save || !result_status)
        {
            auto algo_execute_info = std::make_shared<jrsdata::ComponentSaveInfo>();
            algo_execute_info->algo_result_status = result_status;
            algo_execute_info->algo_execute_rect_param = algo_engine_manager->GetAlgoExecuteRectInfo(algo_detect_result_);
            algo_execute_info->algo_param = algo_param_;
            //! 将时间中的":"字符用“_”替换
            auto board_start_time = jtools::StringOperation::ReplaceString(flow_inspection_result->detect_result_param->start_detect_time,":","_");
            algo_execute_info->barcode = flow_inspection_result->detect_result_param->board_code;
            algo_execute_info->current_time = board_start_time;
            algo_execute_info->input_img = algo_detect_result_->input_image;
            algo_execute_info->project_name = project_data_process_ptr->GetProjectParam()->project_name;
            algo_execute_info->component_name = device_rsult_.t_device.device_name;
            algo_execute_info->algo_name = algo_name_;
            algo_execute_info->subboard_name = sub_board_name_;
            algo_execute_info->hom_matrix = algo_detect_result_->hom_matrix;
            algo_execute_info->result_img = algo_detect_result_->result_image_group;
            algo_execute_info->output_mask_image = algo_detect_result_->output_mask_image;
            algo_execute_info->resolution_x = algo_detect_result_->x_resolution;
            algo_execute_info->resolution_y = algo_detect_result_->y_resolution;
            //! 模板信息
            for (auto& temp_value : algo_detect_result_->template_data)
            {
                jrsdata::TemplateInfo temp_info;
                temp_info.light_id = static_cast<int>(temp_value.id);
                temp_info.template_image = temp_value.template_img;
                temp_info.template_color_param = temp_value.color_params.ToJson();
                algo_execute_info->template_info_vector.push_back(temp_info);
            }
            std::lock_guard<std::mutex> lock(result_mutex);
            flow_inspection_result->detect_result_param->workflow_component_algo_info_vector.emplace_back(algo_execute_info);
        }
   


    }

    void VisionInspectionFlow::ParapareComponentInspectionParam(const jrsdata::Component& component_value, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param, cv::Mat& matrix_to_src_image, jrsparam::ExecuteModeInfo& execute_mode_info)
    {

        project_data_process_ptr->GetComponentExecuteParam(
            component_value, detect_win_execute_param, matrix_to_src_image, execute_mode_info);

        if (detect_win_execute_param.empty()) {
            LogAutoRun_INFO("当前元件：", component_value.component_name,"没有添加检测算法！");
        }
    }

    std::pair<cv::Mat,std::vector<cv::Mat>> VisionInspectionFlow::GetLoactionModelMat(const jrsdata::Component& component_value, std::unordered_map<std::string, jrsdata::DetectModel>& detect_models, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params, cv::Mat& res_src_matrix, jrsdata::DeviceResult& component_result)
    {
        cv::Mat correction_matrix = cv::Mat::eye(2, 3, CV_32F);
        std::vector<cv::Mat> base_plane_temp;
        auto iter_location_detect_model = detect_models.find("location");
        if (iter_location_detect_model == detect_models.end())
        {
            LogAutoRun_INFO("未找到location检测框");
            return {correction_matrix,base_plane_temp};
        }
        if (iter_location_detect_model->second.detect_model.empty())
        {
            LogAutoRun_INFO("location组下没有算法");
            return  {correction_matrix,base_plane_temp};
        }
        if (detect_win_exec_params.empty())
        {
            LogAutoRun_INFO("元件检测框信息为空,无法检测");
            return  {correction_matrix,base_plane_temp};
        }
        std::vector<std::vector<jrsdata::DetectWindow>> sorted_detect_wins;
        if (project_data_process_ptr->SortDetectWinsByDepedent(iter_location_detect_model->second.detect_model, sorted_detect_wins) != 0)
        {
            LogAutoRun_INFO("检测框运行排序失败！");
            return  {correction_matrix,base_plane_temp};
        }

        std::unordered_map<int, cv::Mat> correction_matrixes;
        auto locate_result = ExecuteDetections(sorted_detect_wins, detect_win_exec_params, correction_matrix,res_src_matrix
            ,base_plane_temp, correction_matrixes, component_value, component_result);

        detect_models.erase("location");
        //! 如果定位算法NG，则恢复单位矩阵，不矫正
        if (!locate_result)
        {
            return  {correction_matrix,base_plane_temp};
        }


        if (correction_matrixes.empty())
        {
            return  {correction_matrix,base_plane_temp};
        }
        else
        {
            return {correction_matrixes.begin()->second,base_plane_temp};
        }
    }

    void VisionInspectionFlow::ProcessDetectWindows(std::unordered_map<std::string, jrsdata::DetectModel> detect_models,const jrsdata::Component& component_value, const cv::Mat correction_matrix , std::vector<cv::Mat>& base_plane_mask_, 
        std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param, jrsdata::DeviceResult& component_result, const cv::Mat& res_src_matrix)
    {
        //!将元件自动流程中检测的图片存放在检测结果中， by zhangyuyu 2025.1.13
       //!因为所有的算法输入图片都是一样的，所以随便取一个算法的输入图片即可
        auto it = detect_win_execute_param.begin();
        component_result.device_images = it->second.input_img;
        //!  获取当前元件的检测区域信息,保存到结果中，用于维修站的绘制 by zhangyuyu 2025.2.25
        component_result.t_device.device_x = static_cast<int>(it->second.expand_detect_win_rect.cx);
        component_result.t_device.device_y = static_cast<int>(it->second.expand_detect_win_rect.cy);
        component_result.t_device.device_width = static_cast<int>(it->second.component_rect.width);
        component_result.t_device.device_height = static_cast<int>(it->second.component_rect.height);

        for (auto& detect_model : detect_models)
        {
            std::vector<jrsdata::DetectWindow> detect_wins;
            detect_wins = detect_model.second.detect_model;
            std::vector<std::vector<jrsdata::DetectWindow>> sorted_detect_wins;
            if (project_data_process_ptr->SortDetectWinsByDepedent(detect_wins, sorted_detect_wins) != 0)
            {
                LogAutoRun_INFO("检测框运行排序失败！");
                return;
            }
            std::unordered_map<int, cv::Mat> correction_matrixes;

            ExecuteDetections(sorted_detect_wins, detect_win_execute_param, correction_matrix, res_src_matrix
                , base_plane_mask_,correction_matrixes, component_value, component_result);

        }
    }

    bool VisionInspectionFlow::ExecuteDetections(const std::vector<std::vector<jrsdata::DetectWindow>>& sorted_detect_wins, 
        std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params, 
        const cv::Mat& correction_matrix, const cv::Mat& trans_to_board_matrix, 
        std::vector<cv::Mat>& base_plane_mask_,
        std::unordered_map<int, cv::Mat>& result_trans_matrixes, const jrsdata::Component& component_temp,
        jrsdata::DeviceResult& component_result)
    {
        bool execute_result_status = true;

        for (auto& wins : sorted_detect_wins)
        {

            std::unordered_map<int, cv::Mat> correction_matrixes;
          

            //! 每个检测框进行检测 by zhangyuyu 2025.1.2
            for (auto& detect_win : wins)
            {
                if (detect_win.enable)
                {
                    jrsdata::DetectWindowResult detect_window_result_temp;//! 存放单个检测框的结果
                    /****
                    * @brief 一个元件中有多组的算法检测结果，每一组中有多个检测框，当前一组中只有一个检测框，
                             后期添加组的概念就会有多个 by zhangyuyu 2025.1.2
                    ****/
                    jrsdata::GroupResult detect_window_group_result;

                    //! TODO:目前没有算法组，固给予固定值，后期增加 by zhangyuyu 2025.1.3
                    detect_window_group_result.t_group.group_name = "default";
                    detect_window_group_result.t_group.subboard_id = project_data_process_ptr->GetSubBoardIdBySubBoardName(component_temp.subboard_name);
                    /** 获取当前检测框的算法执行参数*/
                    auto exect_param = detect_win_exec_params.find(detect_win.name);
                    if (exect_param != detect_win_exec_params.end())
                    {
                        //! 如果是第一个检测框，则使用定位算法的矫正矩阵
                        if (correction_matrixes.empty())
                        {
                            for (auto& elem : exect_param->second.correction_matrixes)
                            {
                                elem.second = correction_matrix.clone();
                            }
                        }
                        else
                        {
                            for (auto& elem : exect_param->second.correction_matrixes)
                            {
                                if (correction_matrixes.find(elem.first) != correction_matrixes.end())
                                {
                                    elem.second = correction_matrixes.find(elem.first)->second.clone();
                                }
                                else
                                {
                                    elem.second = correction_matrixes.begin()->second.clone();
                                }
                            }
                        }

                        exect_param->second.input_mask_image[1] = base_plane_mask_;
                        auto res_detect = algo_engine_manager->ExecuteSpecificAlgoDrive(exect_param->second);
                        if (!res_detect)
                        {
                         
                            LogAutoRun_INFO("算法检测异常，算法名称为：",exect_param->second.algo_name);
                            LogAutoRun_INFO("Algorithm execution failed for detection window: " + detect_win.name);

                            continue;
                        }
                        res_detect->transform_hom_matrix = trans_to_board_matrix;
                        correction_matrixes = res_detect->hom_matrix;
                        //! 如果 base_plane_mask 为空，且两个检测结果都非空，则添加基面数据 by zhangyuyu 2025.4.25
                        if (base_plane_mask_.empty() &&
                            !res_detect->output_mask_image.empty() &&
                            !res_detect->height_mat.empty())
                        {
                            base_plane_mask_.push_back(res_detect->output_mask_image);
                            base_plane_mask_.push_back(res_detect->height_mat);
                        }
                        SaveAlgoExecuteParamInfo(component_result, res_detect, detect_win.algorithms[0].detect_algorithm_name, component_temp.subboard_name,exect_param->second.algo_param);
                        SaveAlgoResult(res_detect, detect_win, detect_window_result_temp);
                        
                        {
                            std::lock_guard<std::mutex> lock(result_mutex);
                            work_flow_component_result[component_temp.component_name][detect_win.name] = res_detect;
                        }
                        detect_window_group_result.detect_windows.emplace_back(detect_window_result_temp);
                        
                        //! TODO:当前是临时方案，同元件检测时，先将sub_barcode和sub_bad_mark状态保存，最后整版检测完后，统一处理 
                        //! 后期需要修改，将这些与元件同时检测的特殊元件处理独立出去，作为功能类别 by zhangyuyu 2025.5.7
                        if (component_temp.component_type == jrsdata::Component::Type::SUB_BARCODE)
                        {
                            SaveSubBarcode(res_detect, detect_window_group_result.t_group.subboard_id, exect_param->second.algo_name);
                        }
                        if (component_temp.component_type == jrsdata::Component::Type::SUB_BADMARK)
                        {
                            SaveBadSubboardId(res_detect, detect_window_group_result.t_group.subboard_id, exect_param->second.algo_name);
                        }
                        if (execute_result_status)
                        {
                            execute_result_status = detect_window_result_temp.t_detect_window.detect_window_result;
                        }
                    }
                    component_result.groups.emplace_back(detect_window_group_result);

                }


            }
            result_trans_matrixes = correction_matrixes;
        }
        return execute_result_status;
    }

    bool VisionInspectionFlow::PlanDetectPath(const std::vector<jrsdata::Component>& components, const jrsdata::Board& board, int fov_w, int fov_h, bool ismark)
    {
        //! 计算每个元件的所有检测框的最小外接矩形存入分配FOV的数据结构中
        PCBPathPlanning::InputParams param;
        for (auto& component_value : components)
        {
            //! 修改获取元件最大外接矩形代码 by zhangyuyu 2025.1.20
            std::map<std::string, jrsparam::AlgoExcuteRectsParam> algo_execute_rects_params;
            jrsparam::ExecuteModeInfo execute_mode_info;
            execute_mode_info.execute_mode = jrsparam::ExecuteMode::ManualMode;
            cv::RotatedRect component_bounding_rect;//! 元件最大外接矩形
            project_data_process_ptr->GetSignelComponentExcuteRectsParam(component_value, algo_execute_rects_params, component_bounding_rect, execute_mode_info);
            jcvtools::JrsHomMat2D hom_matrix;
            hom_matrix.AddHomMat2dRotate(component_value.angle,component_value.x,component_value.y);
            component_bounding_rect = hom_matrix.AffineTransRotatedRect(component_bounding_rect);
            auto temp_rect = component_bounding_rect.boundingRect();
            component_bounding_rect.center = cv::Point2f(temp_rect.x + temp_rect.width / 2, temp_rect.y + temp_rect.height / 2);
            component_bounding_rect.size = cv::Size2f(temp_rect.width, temp_rect.height);
            component_bounding_rect.angle = 0;

            //! 元件分配的时的数据结构赋值
            PCBPathPlanning::ObjectInput object_input;
            object_input.rotate_rect = component_bounding_rect;


            //!元件名
            object_input.name = component_value.component_name;
            param.rotate_rects.emplace_back(object_input);
        }
        param.fov_h = fov_h;
        param.fov_w = fov_w;
        param.region_h = board.height;
        param.region_w = board.width;
        param.photo_start = PCBPathPlanning::PhotoStartPosition::LeftTop;       // 左上角
        auto path_pattern = parameter_process_ptr->GetSettingParamValueByName<int>(jrsdata::ParamLevel::MACHINE, jrssettingparam::jrsmachineparam::MACHINE_PARAM_PATH_PATTERN_INT);
        PCBPathPlanning::PathPattern mode = (PCBPathPlanning::PathPattern)path_pattern;
        if (mode == PCBPathPlanning::PathPattern::XAxis_SNAKE)
        {
            param.axis_first = PCBPathPlanning::PhotoAxisFirst::XAxis;              // X轴优先
            param.mode_path = PCBPathPlanning::PathPlanMode::SNAKE_MODE;            // S形
        }
        else if (mode == PCBPathPlanning::PathPattern::YAxis_SNAKE)
        {
            param.axis_first = PCBPathPlanning::PhotoAxisFirst::YAxis;              // Y轴优先
            param.mode_path = PCBPathPlanning::PathPlanMode::SNAKE_MODE;            // S形
        }
        else if (mode == PCBPathPlanning::PathPattern::XAxis_Z)
        {
            param.axis_first = PCBPathPlanning::PhotoAxisFirst::XAxis;              // X轴优先
            param.mode_path = PCBPathPlanning::PathPlanMode::Z_MODE;                // Z形
        }
        else if (mode == PCBPathPlanning::PathPattern::YAxis_Z)
        {
            param.axis_first = PCBPathPlanning::PhotoAxisFirst::YAxis;              // Y轴优先
            param.mode_path = PCBPathPlanning::PathPlanMode::Z_MODE;                // Z形
        }

        param.mode_position = PCBPathPlanning::PositionPlanMode::ROW_MAJOR_MBR; // 矩形覆盖
        param.draw_map = false;     // 是否绘制map(TODO: 目前OPENCV绘图特别慢，后面可能改为生成矢量SVG图，用浏览器打开看)
        param.is_mark = ismark;     // 是否是mark
        param.big_fov_ratio = 1.0;
        if (!ismark)
        {
            // 扫图根据距离mark最近的点确定拍照起点
            param.optimal_path = false;  // 不启用最优路径确定起点,采用指定起点
            param.photo_start = GetStartPosition((int)last_mark_fov.fov_path.center.x, (int)last_mark_fov.fov_path.center.y, board.width, board.height);
        }

        //! 规划FOV位置，这里根据元件的最小外接矩形进行规划
        PCBPathPlanning fov_plan;

        // 调用 PathPlan 函数
        auto result = fov_plan.PathPlan(param);

        // 尝试将结果转换为 PCBPathPlanning::OutputParams
        auto outputParams = std::dynamic_pointer_cast<PCBPathPlanning::OutputParams>(result);
        if (outputParams)
        {
            fov_out.fovs = outputParams->fovs;
            // 记录最后一个mark的FOV
            if (ismark && !fov_out.fovs.empty())
            {
                last_mark_fov = fov_out.fovs.back();
            }
        }
        else
        {
            LogAutoRun_INFO("Failed to cast OutputParamsBase to PCBPathPlanning::OutputParams");
           
        }
        return outputParams->result;
    }

    void VisionInspectionFlow::SetComponentFovId(std::vector<jrsdata::Component>& components)
    {
        //! 遍历所有fov
        assign_component_res.clear();
        for (const auto& fov : fov_out.fovs)
        {
            //！ 遍历当前 FOV 中所有的最小外接矩形
            for (const auto& covered_rectangle : fov.covered_rectangles)
            {
                //！ 查找与当前外接矩形匹配的元件信息，每个外接矩形，对应一个元件，一一对应的
                auto component_it = std::find_if(components.begin(), components.end(),
                    [&covered_rectangle](auto& component)
                    {
                        return covered_rectangle.name == component.component_name;
                    });

                if (component_it != components.end())
                {
                    //! 更新元件的 FOV ID
                    //! 如果covered_rectangle.fovid>0说明只在一个FOV中，否则说明当前元件在多个 FOV中
                    if (covered_rectangle.fovid >= 0)
                    {
                        assign_component_res[covered_rectangle.fovid].push_back(*component_it);

                        //component_it->component_name;
                        component_it->fov_ids = { covered_rectangle.fovid };
                    }
                    else
                    {
                        //TODO:跨FOV元件需要完善 
                        component_it->fov_ids = covered_rectangle.fovids;
                    }
                }
            }
        }
    }

    void VisionInspectionFlow::BoardInspectionFinished()
    {
        //! 统计所有元件的检测结果，得出整板的检测结果
        for (auto& component_result:flow_inspection_result->detect_result_param->component_result_vector)
        {
            auto component_type_enum = component_result.t_device.device_type;
            if (component_type_enum == TypeToString(jrsdata::Component::Type::SUB_BADMARK))
            {
                continue;
            }

            if (!component_result.t_device.device_result)
            {
                flow_inspection_result->detect_result_param->detect_result = false;
                break;
            }
        }
        
        //！TODO:先默认用当前时间作为barcode，后面增加二维码逻辑后替换 by zhangyuyu 2025.2.18
        if (flow_inspection_result->detect_result_param->board_code.empty())
        {
            //! 如果子板barcode不为空，则使用任一子板的结果作为整板barcode
            const auto& sub_boarcode_any_iter = flow_inspection_result->detect_result_param->subboard_id_and_barcode.begin();
            if (sub_boarcode_any_iter != flow_inspection_result->detect_result_param->subboard_id_and_barcode.end())
            {
                flow_inspection_result->detect_result_param->board_code = sub_boarcode_any_iter->second;
            }
            else
            {
                //! 如果子板barcode为空，则使用整板开始检测的时间作为板子barcode
                flow_inspection_result->detect_result_param->board_code = jtools::StringOperation::ReplaceString(flow_inspection_result->detect_result_param->start_detect_time, ":", "_");
            }

        }

        //! 如果开启动态调试并当前处于等待调试信息状态的话，则将板子信息保存下来(如果mark失败的话，则不保存)
        if (work_flow_param.is_enable_online_debug.load()
            && !flow_inspection_result->detect_result_param->detect_result
            && work_flow_param.finished_online_debug.load()
            && flow_inspection_result->detect_result_param->board_detect_status == jrsdata::BoardDetectStatus::NORMAL)
        {
            //! 将NG信息给到动态调试后，将调试完成状态设置为false
            work_flow_param.finished_online_debug.store(false);
            flow_inspection_result->detect_result_param->online_debug_param_ptr->work_flow_board_info = project_param_ptr->board_info;
            for (auto& value : flow_inspection_result->detect_result_param->online_debug_param_ptr->ng_component_detect_results)
            {
                auto iter = work_flow_component_result.find(value.first);
                if (iter != work_flow_component_result.end())
                {
                    value.second.detect_window_results = iter->second;

                }
            }
        }

        flow_inspection_result->detect_result_param->track_id = 1;
        auto fov_counts = inspection_fov_centers.size();
        if (fov_counts == 0)
        {
            return;
        }
        //! 计算fov拍照时间
        auto board_time = jrscore::AOITools::CalculateTimeDifference(flow_inspection_result->detect_result_param->fov_start_time, flow_inspection_result->detect_result_param->fov_end_time);
        //! 计算平均每个FOV的检测时间
        if (board_time.empty())
        {
            return;
        }
        auto fov_time_avg = jrscore::AOITools::DoubleToString((static_cast<double>(std::stoll(board_time)) / fov_counts), 2);
        flow_inspection_result->detect_result_param->one_fov_take_time = fov_time_avg;

        //! 删除bad子板的数据
    
        if (!flow_inspection_result->detect_result_param->subboard_bad_info.empty())
        {
            const auto& bad_subboard_ids = flow_inspection_result->detect_result_param->subboard_bad_info;

            auto& components_result = flow_inspection_result->detect_result_param->component_result_vector;

            components_result.erase(
                std::remove_if(components_result.begin(), components_result.end(),
                    [&bad_subboard_ids](const auto& comp_result)
                    {
                        return bad_subboard_ids.find(comp_result.t_device.subboard_id) != bad_subboard_ids.end();
                    }),
                components_result.end());

        }
    }

    void VisionInspectionFlow::ComponentInspectionFinished(const jrsdata::Component& component_value,jrsdata::DeviceResult& component_result ,const jrsdata::JrsImageBuffer& fov_img, int fov_left_top_x, int fov_left_top_y)
    {
        //!当前原件检测完成时间
        component_result.t_device.device_detect_time = jrscore::AOITools::GetCurrentDataTime();
        //!复判时间与检测时间相同，复判站复判完之后重新更新该时间  By:HJC 2025/1/3
        component_result.t_device.device_rejudgment_time = jrscore::AOITools::GetCurrentDataTime();
        component_result.t_device.device_is_detection = true; /**< 标记该元件已检测：By HJC 2025/2/17*/

        //! 元件缺陷名称赋值和元件检测true/false赋值 by zhangyuyu 2025.2.21
        for (auto& detect_window_result_group : component_result.groups)
        {
            
            for (auto& detect_window_reslut_value : detect_window_result_group.detect_windows)
            {
                //！ 如果有一个检测框为false，则元件结果为false
                if (!detect_window_reslut_value.t_detect_window.detect_window_result)
                {
                    component_result.t_device.device_result = false;
                }
                //! 如果检测框缺陷名称不为空，则赋值给元件
                if (!detect_window_reslut_value.t_detect_window.detect_window_result_flaw_type_name.empty())
                {
                    component_result.t_device.device_detect_flaw_type_names += detect_window_reslut_value.t_detect_window.detect_window_result_flaw_type_name.c_str();
                    component_result.t_device.device_detect_flaw_type_names.append(";");
                }
            }
        }
     
        //! 将元件检测结果存储到整板流程检测结果变量中
        {
            std::lock_guard<std::mutex> lock(result_mutex);
            jrsdata::ComponentDetectResult component_status_result_value;//! 元件的检测结果状态OK/NG ,用于更新render上的元件状态
            component_status_result_value.component_name = component_value.component_name;
            component_status_result_value.part_number = component_value.component_part_number;
            component_status_result_value.subboard_id = std::to_string(component_result.t_device.subboard_id);
            component_status_result_value.sub_board_name = component_value.subboard_name;
            component_status_result_value.result_status = component_result.t_device.device_result;
            component_status_result_value.defect_name = component_result.t_device.device_detect_flaw_type_names;
            component_status_result_value.fov_ids = component_value.fov_ids;
            flow_inspection_result->detect_result_param->component_result_vector.emplace_back(component_result);
            flow_inspection_result->detect_result_param->component_status_result.emplace_back(component_status_result_value);
        
            //! 如果当前元件是NG的，则将其添加到在线调试列表中
            if (!component_status_result_value.result_status && work_flow_param.is_enable_online_debug.load() && work_flow_param.finished_online_debug.load())
            {
                jrsdata::OneFovImgs single_fov_img;
                single_fov_img.fov_id = fov_img.one_fov_imgs.fov_id;
                single_fov_img.imgs = fov_img.one_fov_imgs.imgs;
                single_fov_img.pos = cv::Point2f(fov_left_top_x, fov_left_top_y);
                flow_inspection_result->detect_result_param->online_debug_param_ptr->ng_fov_imgs.emplace(single_fov_img.fov_id,single_fov_img);
                flow_inspection_result->detect_result_param->online_debug_param_ptr->ng_component_detect_results.emplace(component_status_result_value.component_name,component_status_result_value);
            }
        }
    }

    void VisionInspectionFlow::SaveSubBarcode(std::shared_ptr<jrsoperator::OperatorParamBase> result_ptr,int sub_id, const std::string& algo_name)
    {
        auto algo_result_item = algo_engine_manager->GetAlgoExecuteResultParam(result_ptr,algo_name);
        flow_inspection_result->detect_result_param->subboard_id_and_barcode.emplace(std::make_pair(sub_id, algo_result_item.result_str));

    }

    void VisionInspectionFlow::SaveBadSubboardId(std::shared_ptr<jrsoperator::OperatorParamBase> result_ptr, int sub_id, const std::string& algo_name)
    {
        auto algo_result_item = algo_engine_manager->GetAlgoExecuteResultParam(result_ptr, algo_name);
        
        //! 坏板mark识别成功，则为坏板
        if (algo_result_item.result_algo_status)
        {
            flow_inspection_result->detect_result_param->subboard_bad_info.emplace(std::make_pair(sub_id, algo_result_item.result_algo_status));
        }
    }

    PCBPathPlanning::PhotoStartPosition VisionInspectionFlow::GetStartPosition(int x, int y, int board_width, int board_height)
    {
        if (x < board_width / 2 && y < board_height / 2)
        {
            return PCBPathPlanning::PhotoStartPosition::LeftTop;
        }
        else if (x < board_width / 2 && y > board_height / 2)
        {
            return PCBPathPlanning::PhotoStartPosition::LeftBottom;
        }
        else if (x > board_width / 2 && y < board_height / 2)
        {
            return PCBPathPlanning::PhotoStartPosition::RightTop;
        }
        else if (x > board_width / 2 && y > board_height / 2)
        {
            return PCBPathPlanning::PhotoStartPosition::RightBottom;
        }
        return PCBPathPlanning::PhotoStartPosition::LeftTop;
    }

    void VisionInspectionFlow::StopInspection()
    {
        correct_flow_controller_ptr->StopCorrect();
        is_detect_finish.store(true);
        detect_cv.notify_one();
    }

    void VisionInspectionFlow::StopAllTasks()
    {
        jrs_thread_pool->StopAllTasks();
    }

    void VisionInspectionFlow::WaitAllTasks()
    {
        //jrs_thread_pool->WaitForAllTasks();
        jrs_thread_pool->WaitForAllTasks();
        //! 当前板子检测完成
        BoardInspectionFinished();
    }

    void VisionInspectionFlow::SetWorkFlowParam(const WorkFlowParam& param_)
    {
        work_flow_param = param_;
        parameter_process_ptr->SetSettingParams(work_flow_param.setting_param_ptr);
        if (correct_flow_controller_ptr)
        {
            correct_flow_controller_ptr->SetWorkFlowParam(work_flow_param);
        }

    }

}
