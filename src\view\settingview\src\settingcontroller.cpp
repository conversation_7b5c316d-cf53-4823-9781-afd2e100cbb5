﻿//Custom
#include "settingcontroller.h"
#include "settingmodel.h"
#include "settingview.h"

namespace jrsaoi
{
    SettingController::SettingController(const std::string& name_)
        :ControllerBase(name_)
        ,_view(nullptr)
        ,_model(nullptr)
    {

    }
    SettingController::~SettingController()
    {
    }   
   
    int SettingController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (_model == nullptr)
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }

        _model->Update(param_);

        auto model_data = _model->GetModelData();

        return _view->UpdateView(model_data);
    }

    int SettingController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        /** 先更新 */
        auto res = _model->Update(param_);
        // 将数据更新到_model中
        res = _model->Save(param_);
        SigSettingParam(_model->GetModelData());
        return  res;
    }
 
    void SettingController::SetView(ViewBase* view_param)
    {
        if (view_param == nullptr)
        {
            return;
        }
        _view = static_cast<SettingView*>(view_param);
        InitConnect();
    }
    void SettingController::SetModel(ModelBasePtr model_param)
    {
        if (model_param == nullptr)
        {
            return ;
        }
        _model = std::dynamic_pointer_cast<SettingModel>(model_param);

        return ;
    }


    void SettingController::InitConnect()
    {
        connect(_view, &SettingView::SigRefresh, this,&SettingController::SigSettingParam);
        connect(_view, &SettingView::SigSave, this,&SettingController::SlotSave);
    }

    void SettingController::SlotSave(const jrsdata::ViewParamBasePtr& data_)
    {
        Save(data_);
    }



}
