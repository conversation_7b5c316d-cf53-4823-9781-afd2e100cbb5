<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AxisMove</class>
 <widget class="QWidget" name="AxisMove">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>692</width>
    <height>207</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="3,4">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="leftFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="leftLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_3">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <spacer name="horizontalSpacer_11">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout">
            <property name="spacing">
             <number>10</number>
            </property>
            <item row="2" column="0">
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="2" column="1">
             <widget class="QPushButton" name="bottom">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>42</width>
                <height>42</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QPushButton" name="center">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>42</width>
                <height>42</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QPushButton" name="right">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>42</width>
                <height>42</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="left">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>42</width>
                <height>42</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QPushButton" name="top">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>42</width>
                <height>42</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QPushButton" name="modeSelect">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 170, 255);</string>
              </property>
              <property name="text">
               <string>完整</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="axisSelect">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 170, 255);</string>
              </property>
              <property name="text">
               <string>X-Y</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLineEdit" name="step_2">
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>10</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="minimumSize">
             <size>
              <width>20</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>20</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>X:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="xpos">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_11">
            <property name="minimumSize">
             <size>
              <width>20</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>20</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Y:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="ypos">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_12">
            <property name="minimumSize">
             <size>
              <width>20</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>20</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Z:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="zpos">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="rightFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_5">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="rightLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_10">
          <property name="spacing">
           <number>10</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <widget class="QFrame" name="frame_4">
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::Box</enum>
              </property>
              <layout class="QGridLayout" name="gridLayout_2">
               <property name="leftMargin">
                <number>3</number>
               </property>
               <property name="topMargin">
                <number>3</number>
               </property>
               <property name="rightMargin">
                <number>3</number>
               </property>
               <property name="bottomMargin">
                <number>3</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
               <item row="2" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_7">
                 <item>
                  <spacer name="horizontalSpacer_15">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="step10">
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>10mm</string>
                   </property>
                   <property name="checked">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="step2">
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>2mm</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_16">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="3" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_8">
                 <item>
                  <spacer name="horizontalSpacer_17">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_5">
                   <item>
                    <widget class="QLabel" name="label_6">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="sizeIncrement">
                      <size>
                       <width>80</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>9</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>步进: </string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="step">
                   <property name="minimumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>10</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_18">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="1" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_6">
                 <item>
                  <spacer name="horizontalSpacer_13">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="step20">
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>20mm</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="step5">
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>5mm</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_14">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="0" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_9">
                 <item>
                  <spacer name="horizontalSpacer_2">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_8">
                   <property name="minimumSize">
                    <size>
                     <width>40</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>40</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>步进</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_19">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_5">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QFrame" name="frame_5">
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::Box</enum>
              </property>
              <layout class="QGridLayout" name="gridLayout_3">
               <property name="leftMargin">
                <number>3</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>3</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
               <item row="3" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_11">
                 <property name="bottomMargin">
                  <number>3</number>
                 </property>
                 <item>
                  <spacer name="horizontalSpacer_22">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_12">
                   <item>
                    <widget class="QLabel" name="label_7">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="sizeIncrement">
                      <size>
                       <width>80</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>9</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>Z速度: </string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="zSpeed">
                   <property name="minimumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>20</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_23">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="2" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_13">
                 <property name="topMargin">
                  <number>3</number>
                 </property>
                 <property name="bottomMargin">
                  <number>3</number>
                 </property>
                 <item>
                  <spacer name="horizontalSpacer_24">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_15">
                   <item>
                    <widget class="QLabel" name="label_10">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="sizeIncrement">
                      <size>
                       <width>80</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>9</pointsize>
                      </font>
                     </property>
                     <property name="text">
                      <string>X-Y速度: </string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="xySpeed">
                   <property name="minimumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>微软雅黑</family>
                     <pointsize>9</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>100</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_25">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="1" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_14"/>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <property name="spacing">
             <number>3</number>
            </property>
            <item>
             <widget class="QPushButton" name="stopBtn">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>50</width>
                <height>50</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="clearBtn">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>50</width>
                <height>50</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="homeBtn">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>56</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>50</width>
                <height>50</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <property name="spacing">
           <number>3</number>
          </property>
          <item>
           <widget class="QLabel" name="label_5">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>X限位:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="xlimit">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_13">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Y限位:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="ylimit">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_14">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Z限位:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="zlimit">
            <property name="font">
             <font>
              <family>Microsoft YaHei</family>
              <pointsize>8</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
