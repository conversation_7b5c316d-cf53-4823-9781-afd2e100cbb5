#ifndef CUSTOMQVARIANTREGISTER_HPP
#define CUSTOMQVARIANTREGISTER_HPP

#include <QMetaType>
#include <opencv2/opencv.hpp>

#include "parambase.hpp"
#include "viewparam.hpp"
#include "dataviewdatastruct.h"
#include "algorithmenginemanager.h"
Q_DECLARE_METATYPE(cv::Mat)

class CustomQVariantRegister 
{
public:
    static void RegisterType()
    {
        static bool isRegistered = false;
        if (!isRegistered) 
        {
            qRegisterMetaType<cv::Mat>("cv::Mat");
            qRegisterMetaType<jrsdata::ViewParamBasePtr>("jrsdata::ViewParamBasePtr");
            qRegisterMetaType<jrsdata::OperateViewParamPtr>("jrsdata::OperateViewParamPtr");
            qRegisterMetaType<jrsdata::OnlineDebugViewParamPtr>("jrsdata::OnlineDebugViewParamPtr");
            qRegisterMetaType<std::shared_ptr<jrsalgo::AlgorithmEngineManager>>("std::shared_ptr<jrsalgo::AlgorithmEngineManager>");

            qRegisterMetaType<SettingData>("SettingData");
            isRegistered = true;
        }
    }
};



#endif // CUSTOMQVARIANTREGISTER_HPP
