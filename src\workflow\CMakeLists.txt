project(workflow)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)


# 文件打包
set(manager_src

    workflowmanager/src/workflowmanager.cpp

)
set (manager_head
    workflowmanager/include/workflowmanager.h
)

# flow
set(flow_src

    flow/src/flowcontroller.cpp
    flow/src/conditionwaiter.cpp
    
)
set(flow_head
    flow/include/flowcontroller.h
    flow/include/conditionwaiter.h
    flow/include/workflowinterfaces.h

    )

# correctflow
set(correctflow_src
    flow/src/correctflow.cpp
)
set(correctflow_head
    flow/include/correctflow.h
)
# visionipspectionflow
set(visionipspectionflow_src
    flow/src/visioninspectionflow.cpp
    flow/src/convertboardids.cpp
)

set(visionipspectionflow_head
    flow/include/visioninspectionflow.h
    flow/include/convertboardids.h
)

# trackflow
set(trackflow_src
    flow/src/trackflow.cpp
)

set(trackflow_head
    flow/include/trackflow.h
)

# resultstorageflow
set(resultstorageflow_src
    flow/src/resultstorageflow.cpp
)

set(resultstorageflow_head
    flow/include/resultstorageflow.h
)

# 预编译头文件
set(source_pre_file

    prebuild/workflowpch.cpp
    prebuild/workflowpch.h

)
source_group("workflowmanager/src" FILES ${manager_src})
source_group("workflowmanager/head" FILES ${manager_head})
source_group("flow" FILES ${flow_src} ${flow_head})
source_group("flow/correctflow" FILES ${correctflow_src} ${correctflow_head})
source_group("flow/visionipspectionflow" FILES ${visionipspectionflow_src} ${visionipspectionflow_head})
source_group("flow/trackflow" FILES ${trackflow_src} ${trackflow_head})
source_group("flow/resultstorageflow" FILES ${resultstorageflow_src} ${resultstorageflow_head})
source_group("prebuild/src" FILES ${source_pre_file})
add_library(${PROJECT_NAME} SHARED
            ${manager_src}
            ${manager_head} 
            ${flow_src}
            ${flow_head}
            ${correctflow_src}
            ${correctflow_head}
            ${visionipspectionflow_src}
            ${visionipspectionflow_head}
            ${trackflow_src}
            ${trackflow_head}
            ${resultstorageflow_src}
            ${resultstorageflow_head}
            ${source_pre_file}
            ${JRS_VERSIONINFO_RC}

               
)
#设置输出bin路径
set(LIBRARY_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)


target_link_directories(${PROJECT_NAME} 
    PRIVATE
    #OPENCV

)

target_link_directories(${PROJECT_NAME} PUBLIC
        
        # 成像模块
        $<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/debug>
        $<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>
        $<$<CONFIG:RelWithDebInfo>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>

        #$<$<CONFIG:Debug>:${DIR_PROJECT_CURRENT}thirdparty/colorwheel/lib/debug>
        #$<$<CONFIG:Release>:${DIR_PROJECT_CURRENT}thirdparty/colorwheel/lib/release>
        #$<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:${DIR_PROJECT_CURRENT}thirdparty/jrsstructlightcamera/lib/release>


)
#用于将库文件或其他目标链接到指定的目标上
target_link_libraries(${PROJECT_NAME}

    #core 
    core
    #datamanager
    datamanager
    #devicemanager
    devicemanager
    #algorithmengine
    algorithmengine
    # 成像
    $<$<CONFIG:Debug>:jrsstructlightcontrlmodule>
    #$<$<OR:$<CONFIG:Release>,$<CONFIG:RelWithDebInfo>>:jrsstructlightcontrlmodule>
    $<$<CONFIG:Release>:jrsstructlightcontrlmodule>
    $<$<CONFIG:RelWithDebInfo>:jrsstructlightcontrlmodule>

    #$<$<CONFIG:Debug>:colorwheel>
    #$<$<CONFIG:Release>:colorwheel>


)

# 引入头文件
target_include_directories(${PROJECT_NAME} PUBLIC
    ${DIR_PROJECT_CURRENT}/src/core/common/include
    ${DIR_PROJECT_CURRENT}src/core/database/include
    ${DIR_PROJECT_CURRENT}src/core/coordinatecenter/include
    ${DIR_PROJECT_CURRENT}src/core/dofov/include
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/image
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/projectparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/viewparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/parambase
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/deviceparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/define/resultparam
    ${DIR_PROJECT_CURRENT}/src/parametermanager/process/algoprocess
    ${DIR_PROJECT_CURRENT}/src/datamanager/include
    ${DIR_PROJECT_CURRENT}/src/workflow/workflowmanager/include
    ${DIR_PROJECT_CURRENT}/src/workflow/flow/include
    ${DIR_PROJECT_CURRENT}/src/workflow/prebuild
    ${DIR_PROJECT_CURRENT}/src/devicemanager/devicemanager/include
    ${DIR_PROJECT_CURRENT}/src/devicemanager/structlight/include
    ${DIR_PROJECT_CURRENT}/src/devicemanager/motion/include
    ${DIR_PROJECT_CURRENT}/src/algorithmengine/include
    #thirdparty
    ${DIR_PROJECT_CURRENT}/thirdparty/json/include
    #${DIR_PROJECT_CURRENT}/thirdparty/colorwheel/include

)

# 启用预编译头
target_precompile_headers(${PROJECT_NAME} PRIVATE "${CMAKE_SOURCE_DIR}/src/workflow/prebuild/workflowpch.h")

# 针对 Visual Studio 项目，确保 pch.cpp 正确配置
if(MSVC)
    set_source_files_properties(workflowpch.cpp PROPERTIES COMPILE_FLAGS "/Ycpch.h")  # 编译 pch.cpp
    set_target_properties(${PROJECT_NAME} PROPERTIES
        COMPILE_PDB_NAME ${PROJECT_NAME}  # 可选：设置 PDB 文件名
    )
endif()
#设置工程生成debug调试信息
target_link_options(${PROJECT_NAME} PRIVATE "$<$<CONFIG:RELEASE>:/DEBUG>")

