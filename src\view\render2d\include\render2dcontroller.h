﻿/*****************************************************************//**
 * @file   render2dcontroller.h
 * @brief  2d渲染界面控制器
 * @details
 * <AUTHOR>
 * @date 2024.7.16
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.16         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSRENDER2DCONTROLLER_H__
#define __JRSRENDER2DCONTROLLER_H__

#include <QKeyEvent>
#include <QCoreApplication>
#include <QApplication>

#include "controllerbase.h"
#include "render2deventparam.hpp"

struct GraphicsShape;


namespace jrsaoi
{
    enum class Layer;
    class Render2dModel;
    class Render2dView;
    class MultipleBoardsBase;
    class PadOperator;
    class AddPadView;

    class Render2dController : public ControllerBase
    {
        Q_OBJECT
    public:
        Render2dController(const std::string& name);
        ~Render2dController();
        int Update(const jrsdata::ViewParamBasePtr& param_)override;
        int Save(const jrsdata::ViewParamBasePtr& param_)override;
        void SetView(ViewBase* view_param)override;
        void SetModel(ModelBasePtr model_param)override;

    public slots:
        void SlotGraphicsCreated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SlotGraphicsUpdated(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool is_update_graphics_ = true);
        void SlotGraphicsSelected(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SlotGraphicsDelete(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SlotRegionSelected(float x, float y, float w, float h);

    signals:
        void SignalRender2dUpdate(const jrsdata::ViewParamBasePtr& param_); ///< 往外发信号

        void SignalRenderStateChange(int state);
        void SignalRenderCreateGraphicsModeChange(int mode);
        void SignalAddImage(const cv::Mat& image, int img_index, int x, int y, int z, float angle, bool is_draw_center);
        void SignalShowImages(const std::vector<ImageShowParam>& params);
        void SignalShowImageChangeWithStr(const uint8_t& set_key_, const std::string& img_type_str);
        void SignalClearImage(const int& set_key_, int z);
        void SignalDrawAngleChange(int angle);
        void SignalRenderCanvasSizeChange(int width, int height);
        void SignalCurrentLayerChange(const std::string& layer);
        void SignalCameraScaleModeChange(int mode);
        void SignalCameraResetModeChange(int mode);
        void SignalResetCamera();
        void SignalMoveCamera(int direction);
        void SignalMoveCameraToGraphics(const std::shared_ptr<GraphicsAbstract>& gh);
        void SignalMoveCameraToGraphicsWithID(const GraphicsID& id);
        void SignalMoveCameraToSelectedGraphics();
        void SignalThumbnailShow();
        void SignalAddGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer, bool invoke_callback);
        void SignalAddGraphicsSingle(const std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer, bool invoke_callback);
        void SignalGraphicsSelect(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, bool state);
        void SignalGraphicsSelectSingle(const std::shared_ptr<GraphicsAbstract>& gh, bool invoke_callback);
        void SignalGraphicsSelectSingleWithID(const GraphicsID& id, bool invoke_callback);
        void SignalGraphicsAttributeEditSingleSelected(/*const std::string& component_name_, */double val, int type);
        void SignalGraphicsAttributeEditByGraphicsPtr(const std::shared_ptr<GraphicsAbstract>& gh_ptr, double val, int type);
        // void SignalGraphicsCreateToView(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const RenderEditParam& render_edit_param);
        void SignalAddGraphicsLayerConfig(const std::string& layer, std::shared_ptr<LayerConfig> config);
        void SignalShowGraphicsLayer(const std::string& layer);

        void SignalClearLayerGraphics(const std::string& layer, bool invoke_callback_ = true);
        void SignalClearGraphics(bool invoke_callback);
        void SignalClearGraphicsExceptLayer(const std::string& except_layer_, bool invoke_callback);
        void SignalClearPadGroups();

        void SignalGetGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::vector<GraphicsID>& ids);
        void SignalGetGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const GraphicsID& id);
        void SignalGetCurrentSelectedGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const std::string& layer);
        void SignalGetCurrentSelectedGraphicsSingle(std::shared_ptr<GraphicsAbstract>& gh, const std::string& layer);
        void SignalCreateGraphics(std::shared_ptr<GraphicsAbstract>& gh, int graphics_type,
            const std::string& layer, const std::string& group_name = "", const GraphicsPtr& father_graphics_ = nullptr);

        void SignalShow3DView(const cv::Mat& model_data, const double& rx, const double& ry, const std::vector<cv::Mat>& texture_data);
        /** <对于结果的管理*/
        void SignalAddResultGraphicsShapes(const GraphicsShape& graphics_shape_);
        void SignalClearResultGraphicsShapes();

        //TODO 增加撤销的信号  by yaoying_zhang 2024.12.04
        void SignalRevoke();
        //TODO 增加恢复的信号 by yaoying_zhang 2024.12.04
        void SignalRecover();
        /**
         * @fun SignalShowCenterCrossLine
         * @brief
         * @param state
         * <AUTHOR>
         * @date 2025.1.17
         */
        void SignalShowCenterCrossLine(bool state);
    protected:
        /**
         * @fun SetRenderState
         * @brief 设置渲染界面状态
         * @param state 状态
         * @return AOI_OK成功，其他失败
         * <AUTHOR>
         * @date 2024.7.21
         */
        int SetRenderState(const VisionMode& state);
        /**
         * @fun SetCurrentLayer
         * @brief 设置当前操作层,指的是图形层，与图像层无关
         * @param layer 当前层
         * @return  AOI_OK成功，其他失败
         * <AUTHOR>
         * @date 2024.7.22
         */
        int SetCurrentLayer(const std::string& layer);
        int SetCurrentLayer(const Layer& layer);

        void ReadGraphics(GraphicsPtr& gh, const std::string& name);

        void ShowImageWithGraphics(const std::vector<std::pair<GraphicsPtr, cv::Mat>>& images);
        std::optional < std::reference_wrapper<Render2dEventParam>> GetCurrentRenderEventParam();
    private:
        void Init();
        void InitCallBackView();
        void InitCallBackModel();
        void InitEventHandler();
        void InitConnect();

        void HandlerenderEvent(const jrsdata::RenderViewParamPtr& param);

        void Handleviewevent(const jrsdata::RenderEventParamPtr& param);

        void Handlealgoevent(const jrsdata::AlgoEventParamPtr& param);

        void Handleprojectevent(const jrsdata::ProjectEventParamPtr& param);

        void Handlecomponentlistviewevent(const jrsdata::ComponentListViewParamPtr& param);
        /**
         * @fun HandleOnlineDebugEvent
         * @brief render处理在线调试事件
         * @param param [IN] 事件参数
         * <AUTHOR>
         * @date 2025.4.15
         */
        void HandleOnlineDebugEvent(const jrsdata::OnlineDebugViewParamPtr& param);

        void HandleOperateParamEvent(const jrsdata::OperateViewParamPtr& param);


        void SlotProjectUpdateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SlotProjectUpdateGraphics();
        //void SlotCurrentSelectGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        void SlotCurrentSelectParam(UpdateOperatorParam& param_);
        /**
         * @fun HandleClearGraphics
         * @brief 清除数据
         * @param except_layer_
         * @param invoke_callback
         * <AUTHOR>
         * @date 2025.2.26
         */
        void HandleClearGraphics(const std::string& except_layer_, bool invoke_callback);
        /**
         * @fun HandleRequestCreateGraphics
         * @brief
         * @param layer
         * @param parent
         * @date 2024.9.20
         * <AUTHOR>
         */
        void HandleRequestCreateGraphics(const std::string& layer, std::weak_ptr<GraphicsAbstract> parent = std::weak_ptr<GraphicsAbstract>());
        //void HandleRequestCreateGraphics(const std::string& layer, const std::string& parent_name);
        void HandleGraphicsCropImage(const std::shared_ptr<GraphicsAbstract>& gh);
        void HandleGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleCAD(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSinglePAD(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleMark(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleSubMark(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleBarcode(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleSubBarcode(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleWindow(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleGraphicsSelectedSingleSubWindow(const std::shared_ptr<GraphicsAbstract>& gh);
        //void HandleCADEditGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh);
        void HandleGraphicsCADSelectedSingleEvent(const std::shared_ptr<GraphicsAbstract>& gh);
        void HandleSubEditGraphicsSelectedSingle(const std::shared_ptr<GraphicsAbstract>& gh);
        /**
         * @brief 处理区域选择事件-CAD图形编辑
         */
        void HandleCADEditRegionSelected(float center_x, float center_y, float w, float h);
        /**
         * @fun HandleGraphicsUpdateTempMark
         * @brief 更新临时mark点的位置
         * @param gh
         * <AUTHOR>
         * @date 2025.3.11
         */
        void HandleGraphicsUpdateTempMark(const std::shared_ptr<GraphicsAbstract>& gh);
        /**
         * @fun HandleMultipleBoardRegionSelected
         * @brief 处理区域选择事件-多联板编辑
         * @param center_x
         * @param center_y
         * @param w
         * @param h
         * <AUTHOR>
         * @date 2024.12.26
         */
        void HandleMultipleBoardRegionSelected(float center_x, float center_y, float w, float h);
        ///**
        // * @brief 处理区域选择事件-多联板编辑
        // */
        //void HandleSubEditRegionSelected(float center_x, float center_y, float w, float h);
        /**
         * @brief 处理区域选择事件-编辑子板区域
         */
        void HandleEditSubRegionRegionSelected(float center_x, float center_y, float w, float h);
        /**
         * @brief 处理区域选择事件-创建图形
         */
        void HandleCreateGraphicsRegionSelected(float center_x, float center_y, float w, float h);
        /**
         * @brief 处理区域选择事件-焊盘编辑
         */
        void HandlePadEditRegionSelected(float center_x, float center_y, float w, float h);

        void HandleDrawTempRegionDone(float center_x, float center_y, float w, float h);

        void HandleDrawShow3DRegionDone(float center_x, float center_y, float w, float h);

        /**
         * @brief 触发给外部发送图形更新信号
         */
        void TriggerProjectUpdate(std::string operator_name, const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs);
        /**
         * @brief 触发给外部发送图形单选信号
         */
         //void TriggerGraphicsSelectedSingle();
         /**
          * @brief 从图形索引获取图形引用
          * @note  使用引用方便直接访问图形
          */
        void GetIndexFromRef();
        /**
         * @brief 从图形引用获取图形索引
         * @note  使用索引避免图形刷新后引用失效
         */
        void GetRefFromIndex();
        /**
         * @brief 重置事件参数
         */
        void ResetEvent();

        void CropImage(cv::Mat& crop_image, const cv::RotatedRect& boundingbox, int image_index);

        /**
          * @fun DealCADShortcutOperate
          * @brief
          * @param layer
          * @param parent
          * @date 2024.11.28
          * <AUTHOR>
          */
        void DealCADShortcutOperate(const jrsdata::RenderEventParamPtr param);


        /**
         * @fun SimulateKeyCtrlC
         * @brief   模拟 Ctrl+C
         * @param param
         * @date 2024.12.04
         * <AUTHOR>
         */
        void SimulateKeyCtrlC();

        /**
         * @fun SimulateKeyCtrlV
         * @brief   模拟 Ctrl+V
         * @param param
         * @date 2024.12.04
         * <AUTHOR>
         */
        void SimulateKeyCtrlV();


        /**
         * @fun SimulateDel
         * @brief   模拟 Delte
         * @param param
         * @date 2024.12.04
         * <AUTHOR>
         */
        void SimulateDel();

        /**
         * @fun GetDetectRegionWindow
         * @brief  增加检测区域框
         * @param rect_  绝对坐标
         * @param is_ok_  OK|NG
         * <AUTHOR>
         * @date 2025.1.15
         */

        std::shared_ptr<GraphicsAbstract> GetDetectRegionWindow(const cv::RotatedRect& rect_,
            const std::string& componet_and_algo_name_);

        /**
         * @fun AddDetectRegionWindows
         * @brief  批量添加检测区域
         * @param detect_regions_ 绝对坐标和状态
         * <AUTHOR>
         * @date 2025.1.20
         */
        void AddDetectRegionWindows(const  std::vector<std::tuple<bool, cv::RotatedRect, std::string, cv::Mat>>& detect_regions_);

        /**
         * @fun ShowDetectResult
         * @brief 显示检测结果
         * @param detect_window_result
         * <AUTHOR>
         * @date 2025.1.21
         */
        void ShowDetectResult(const std::vector<GraphicsID>& ids_, const std::unordered_map<std::string, bool>& name_and_state_result_);

        /**
         * @fun ShowDetectResult
         * @brief  自动流程修改CAD框的颜色
         * @param subboard_and_component_name_
         * <AUTHOR>
         * @date 2025.1.21
         */
        Q_INVOKABLE void ShowDetectResult(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun ControlPanelEventOperate
         * @brief 处理控制面板上的操作事件
         * @param param_
         * <AUTHOR>
         * @date 2025.1.22
         */
        void ControlPanelEventOperate(const jrsdata::ViewParamBasePtr& param_);
        /**
        * @fun ShowEntiretyImages
        * @brief 显示整板大图
        * @param is_load_all_images_ true  加载所有的图片， false 加载一张图片
        * <AUTHOR>
        * @date 2025.1.22
        */
        void ShowEntiretyImages(bool is_load_all_images_ = true);

        /**
         * @fun ShowPadAlterView
         * @brief  打开pad 添加窗口
         * @param gh_group_
         * <AUTHOR>
         * @date 2025.3.3
         */
        void ShowPadAlterView(std::shared_ptr<GraphicsAbstract> gh_group_ = nullptr);

        /**
         * @fun SelectedSpeficComponent
         * @brief 选中指定的元件
         * @param component_name_[IN] 元件名称
         * @param sub_board_name_[IN] 子板名称
         * @param part_number_name_[IN] 料号名称
         * <AUTHOR>
         * @date 2025.4.15
         */
        void SelectedSpeficComponent(const std::string& component_name_, const std::string& sub_board_name_, const std::string& part_number_name_);

        /**
         * @fun MultipleBoardCreate
         * @brief 多连扳拓展
         * <AUTHOR>
         * @date 2025.6.23
         */
        void MultipleBoardCreate(const jrsdata::RenderEventParamPtr& param_);
    private:
        Render2dView* render_2d_view; ///< view
        AddPadView* _add_pad_view; ///< add pad view
        std::shared_ptr<Render2dModel> model; ///< model

        std::unordered_map<std::string, std::function<void(const jrsdata::OperateViewParamPtr)>> operate_handlers;  ///< OperateViewParamPtr 事件处理
        std::unordered_map<std::string, std::function<void(const jrsdata::OnlineDebugViewParamPtr)>> online_debug_handlers; /**< OnlineDebugViewParamPtr 事件处理*/
        std::unordered_map<std::string, std::function<void(const jrsdata::ComponentListViewParamPtr)>> component_handlers;  ///< ComponentListViewParamPtr 事件处理
        std::unordered_map<std::string, std::function<void(const jrsdata::RenderViewParamPtr)>> event_handlers;     ///< RenderViewParam事件处理
        std::unordered_map<std::string, std::function<void(const jrsdata::RenderEventParamPtr)>> view_handlers;     ///< RenderEventParam事件处理
        std::unordered_map<std::string, std::function<void(const jrsdata::AlgoEventParamPtr)>> algo_handlers;       ///< AlgoEventParam事件处理
        std::unordered_map<std::string, std::function<void(const jrsdata::ProjectEventParamPtr)>> project_handlers; ///< ProjectEventParam事件处理

        std::shared_ptr<jrsaoi::PadOperator> _pad_operator_ptr;
        std::shared_ptr<MultipleBoardsBase> _regular_multiple_boards_ptr;  /**<规则多连扳   */
        std::shared_ptr<MultipleBoardsBase> _irregular_multiple_boards_ptr;/**<不规则多连板 */

        Render2dEventParam event_param;  ///< 事件参数结构体
        std::atomic <uint8_t> _current_img_set;  /**<当前图像集合*/
        std::atomic<bool> _is_auto_flow_work;  /**< 当前是否属于自动流程*/
    };

    using Render2dControllerPtr = std::shared_ptr<Render2dController>;

}
#endif // !__JRSRENDER2DCONTROLLER_H__
