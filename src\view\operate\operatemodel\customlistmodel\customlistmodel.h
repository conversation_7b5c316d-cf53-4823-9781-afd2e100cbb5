﻿/*****************************************************************//**
 * @file   CustomListModel.h
 * @brief 定位点列表数据模型
 *
 * <AUTHOR>
 * @date   2024.2.18
 *********************************************************************/
#ifndef CUSTOMLISTMODEL_H
#define CUSTOMLISTMODEL_H
 //QT
#include <QObject>
#include <QAbstractListModel>
 //STD
#include <vector>
//CUSTOM
#include "datadefine.hpp"

class CustomListModel : public QAbstractListModel
{
    Q_OBJECT

public:
    CustomListModel();
    ~CustomListModel();

    //必须实现的函数
    int rowCount(const QModelIndex& parent) const;
    QVariant data(const QModelIndex& index, int role) const;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const Q_DECL_OVERRIDE;

    void SetListData(std::vector<ListStruct*> vec_value);
    void AddListData(ListStruct* value);
private:
    std::vector<ListStruct*> m_vec_data;
    QStringList m_header_list;//标题栏数据

};
#endif