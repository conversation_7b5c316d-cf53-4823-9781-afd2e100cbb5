/*********************************************************************
 * @brief  渲染器常量.
 *
 * @file   renderconstants.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef RENDERCONSTANTS_HPP
#define RENDERCONSTANTS_HPP
#include "rvec.hpp"
#include <vector>
 /**
  * @brief 表示精灵的顶点格式，用于图形渲染。
  *
  * 该结构体包含精灵的位置信息、纹理坐标、偏移量、尺寸、旋转角度以及纹理索引等信息。
  */
struct SpriteVertexFormat
{
    Vec3 position;  ///< 顶点的位置
    Vec2 texcoord;  ///< 纹理的坐标
    Vec2 offset;    ///< 顶点的偏移量
    Vec2 size;      ///< 精灵的尺寸
    float angle = 0; ///< 精灵的旋转角度，默认值为0
    float texindex = 0; ///< 纹理索引，默认值为0
};

/**
 * @brief 表示颜色顶点格式，用于颜色渲染。
 *
 * 该结构体包含顶点的位置信息以及对应的颜色。
 */
struct ColorVertexFormat
{
    Vec3 position;  ///< 顶点的位置
    Color color;    ///< 顶点的颜色
};

/**
 * @brief 绘制缓存
 */
struct DrawBuffer
{
    std::vector<uint32> geometry_indices;             ///< 几何索引
    std::vector<ColorVertexFormat> geometry_vertices; ///< 几何顶点
    int type; ///< 渲染类型
};

/**
 * @brief 表示2D纹理的渲染参数。
 */
struct Texture2D
{
    bool is_draw_center;  ///< 控制图像绘制方式，true 表示以中心为基准绘制，false 表示以左上角为基准绘制
    unsigned int id;      ///< 纹理储存id
    unsigned int format;  ///< 纹理格式

    int width;   ///< 纹理宽度
    int height;  ///< 纹理高度
    int x;       ///< 纹理在屏幕上的x坐标
    int y;       ///< 纹理在屏幕上的y坐标
    int z;       ///< 纹理在屏幕上的z坐标
    float angle; ///< 纹理旋转角度

    Vec2 uv[4];  ///< 纹理坐标,默认

    Texture2D()
        : is_draw_center(false)
        , id(0), format(0), width(0), height(0), x(0), y(0), z(0), angle(0), uv()
    {}
};
#endif //! RENDERCONSTANTS_HPP