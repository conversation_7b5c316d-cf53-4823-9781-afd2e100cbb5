﻿/*****************************************************************
* @file   dbparam.hpp
* @brief  存入数据库的数据，定义的数据表与查询等.
* @details
* <AUTHOR>
* @date 2024.8.18
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
* <tr><td>2024.8.8          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/

#ifndef __DBSTRUCTBASE_HPP__
#define __DBSTRUCTBASE_HPP__

#include <iostream>
#include <array>
#include <vector>
#include "mysqlimp.hpp"

//#include "iguana/iguana/iguana.hpp"
namespace jrsdatabase
{
    /**< 前侧声明 */
    template <typename T>
    class IDatabase;
    class MySqlImp;
    using DB_Mysql = IDatabase<MySqlImp>;
}
namespace jrsdatabase
{
    /** 数据库参数 */
    struct DatabaseConnectParam//:jrsdata::DataBase
    {
        std::string db_ip;
        std::string db_name;
        std::string db_user_name;
        std::string db_pwd;
        int max_connect;
        DatabaseConnectParam() : db_ip("")
            , db_name("")
            , db_user_name("")
            , db_pwd("")
            , max_connect(0)
        {
            // this->file_type = jrsdata::FileType::JSON;
        }
        DatabaseConnectParam(
            std::string db_ip_,
            std::string db_user_name_,
            std::string db_pwd_,
            std::string db_name_,
            int max_connect_) : db_ip(db_ip_)
            , db_name(db_name_)
            , db_user_name(db_user_name_)
            , db_pwd(db_pwd_)
            , max_connect(max_connect_)
        {
            //this->file_type = jrsdata::FileType::JSON;
        }
    };
    JRSREFLECTION(DatabaseConnectParam, db_ip, db_name, db_user_name, db_pwd, max_connect);
    //#if __cplusplus <= 202002L
    //    YLT_REFL(DatabaseConnectParam, db_ip, db_name) //define meta data
    //#endif
    namespace jrstable
    {
        /**< TODO: 表注册 */
        static constexpr char DEMO_TABLE_NAME[] = "demo_table_name";    /**< demo 表 */
        static constexpr char T_USER[] = "t_user";                      /**< 用户表 */
        static constexpr char T_PROJECT[] = "t_project";                /**< 项目表 */
        static constexpr char T_AOI_MACHINE[] = "t_aoi_machine";          /**< AOI 系统表 */
        static constexpr char T_AOI_MACHINE_STATISTICS[] = "t_aoi_machine_statistics"; /**< 机台统计表 */
        static constexpr char T_BOARD[] = "t_board";                    /**< 总板表 */
        static constexpr char T_SUBBOARD[] = "t_subboard";              /**< 子板表 */
        static constexpr char T_DEVICE[] = "t_device";                  /**< 元件表 */
        static constexpr char T_GROUP[] = "t_group";                    /**< 组表 */
        static constexpr char T_DETECT_TYPE[] = "t_detect_type";        /**< 缺陷类型表 */
        static constexpr char T_DETECT_WINDOW[] = "t_detect_window";    /**< 检测框表 */
        /** 数据表基类 */
        struct TableParamBase
        {
            TableParamBase() = default;
            virtual ~TableParamBase() = default;
            std::string table_name;
        };
        using TableParamBasePtr = std::shared_ptr<TableParamBase>;

#pragma region TProject 表

        /**<项目表*/
        struct TProject :TableParamBase
        {
            /**< TODO: 需 AOI 赋值*/
            std::string project_name;           /**< 工程名称 */
            std::string board_product_model;    /**< 机种名称/产品料号 */
            std::string work_order_information; /**< 工单信息 */

            int statistics_board_pass_number;                       /**< 轨道编号 */
            int statistics_board_ng_number;                       /**< 轨道编号 */
            int statistics_board_misjudge_number;                       /**< 轨道编号 */

            int statistics_subboard_pass_number;                       /**< 轨道编号 */
            int statistics_subboard_ng_number;                       /**< 轨道编号 */
            int statistics_subboard_misjudge_number;                       /**< 轨道编号 */

            int statistics_component_pass_number;                       /**< 轨道编号 */
            int statistics_component_ng_number;                       /**< 轨道编号 */
            int statistics_component_misjudge_number;                       /**< 轨道编号 */

            TProject()
                : project_name(""),
                board_product_model(""),
                work_order_information(""),
                statistics_board_pass_number(0),
                statistics_board_ng_number(0),
                statistics_board_misjudge_number(0),
                statistics_subboard_pass_number(0),
                statistics_subboard_ng_number(0),
                statistics_subboard_misjudge_number(0),
                statistics_component_pass_number(0),
                statistics_component_ng_number(0),
                statistics_component_misjudge_number(0)
            {
                this->table_name = jrstable::T_PROJECT;  // 表名初始化
            }

            // 参数构造函数
            TProject(const std::string& project_name_,
                const std::string& board_product_model_,
                const std::string& work_order_information_,
                int statistics_board_pass_number_,
                int statistics_board_ng_number_,
                int statistics_board_misjudge_number_,
                int statistics_subboard_pass_number_,
                int statistics_subboard_ng_number_,
                int statistics_subboard_misjudge_number_,
                int statistics_component_pass_number_,
                int statistics_component_ng_number_,
                int statistics_component_misjudge_number_)
                : project_name(project_name_),
                board_product_model(board_product_model_),
                work_order_information(work_order_information_),
                statistics_board_pass_number(statistics_board_pass_number_),
                statistics_board_ng_number(statistics_board_ng_number_),
                statistics_board_misjudge_number(statistics_board_misjudge_number_),
                statistics_subboard_pass_number(statistics_subboard_pass_number_),
                statistics_subboard_ng_number(statistics_subboard_ng_number_),
                statistics_subboard_misjudge_number(statistics_subboard_misjudge_number_),
                statistics_component_pass_number(statistics_component_pass_number_),
                statistics_component_ng_number(statistics_component_ng_number_),
                statistics_component_misjudge_number(statistics_component_misjudge_number_)
            {
                this->table_name = jrstable::T_PROJECT;  // 表名初始化
            }

            static constexpr std::string_view get_alias_struct_name(TProject*)
            {
                return jrstable::T_PROJECT;
            }

        };
        using TProjectVector = std::vector<jrsdatabase::jrstable::TProject>;
        JRSREGISTER_CONFLICT_KEY(TProject, project_name);
        JRSREFLECTION(TProject, project_name, board_product_model, work_order_information,
            statistics_board_pass_number, statistics_board_ng_number, statistics_board_misjudge_number,
            statistics_subboard_pass_number, statistics_subboard_ng_number, statistics_subboard_misjudge_number,
            statistics_component_pass_number, statistics_component_ng_number, statistics_component_misjudge_number
        );
#pragma endregion
#pragma region TUser 表
        /**< 用户表 */
        struct TUser :TableParamBase
        {
            std::string user_name;   /**< 操作员名称 */
            std::string password;    /**< 密码 */
            std::string create_time; /**< 操作时间 */
            int permission_level;    /**< 权限等级 */

            TUser()
                :user_name{},
                password{},
                create_time(""),
                permission_level(0)
            {
                this->table_name = jrstable::T_USER;
            }

            // 参数构造函数
            TUser(
                const std::string& name,
                const std::string& pass,
                const std::string& time,
                int permission_level_
            )
                :
                user_name(name),
                password(pass),
                create_time(time),
                permission_level(permission_level_)
            {
                this->table_name = jrstable::T_USER;
            }
            static constexpr std::string_view get_alias_struct_name(TUser*)
            {
                return jrstable::T_USER;
            }
        };
        using TUserVector = std::vector<jrsdatabase::jrstable::TUser>;
        JRSREGISTER_CONFLICT_KEY(TUser, user_name);
        JRSREFLECTION(TUser, user_name, password, create_time, permission_level);
#pragma endregion
#pragma region TAOIMachine 系统表
        /**<系统信息*/
        struct TAOIMachine :TableParamBase
        {
            /**< TODO: 需 AOI 赋值*/
            std::string machine_id;              /**< 机器编号，每台机器的唯一标识 */
            std::string work_station_name;       /**< 工作站名      */
            std::string soft_version;            /**< 版本号        */
            std::string start_time;              /**< 软件开机时间  */
            std::string project_name;            /**< 当前检测的工程*/
            std::string one_fov_take_time;       /**< 一个fov耗时   */
            std::string company_no;
            std::string machine_no;
            std::string site_no;
            std::string thread_no;

            TAOIMachine() : machine_id{}, work_station_name(""), soft_version(""), one_fov_take_time(""), project_name(""), start_time(""),
                company_no(""), machine_no(""), site_no(""), thread_no("")
            {
                this->table_name = jrstable::T_AOI_MACHINE;
            }
            TAOIMachine(const std::string& m_id, const std::string& work_station, const std::string& version,
                const std::string& start_time, const std::string& project_name_, const std::string& one_fov_take_time_,
                const std::string& company_no_, const std::string& machine_no_, const std::string& site_no_, const std::string& thread_no_)
                : machine_id(m_id), work_station_name(work_station),
                soft_version(version), start_time(start_time),
                project_name(project_name_), one_fov_take_time(one_fov_take_time_),
                company_no(company_no_), machine_no(machine_no_),
                site_no(site_no_), thread_no(thread_no_)
            {
                this->table_name = jrstable::T_AOI_MACHINE;
            }
            static constexpr std::string_view get_alias_struct_name(TAOIMachine*)
            {
                return jrstable::T_AOI_MACHINE;
            }
        };
        using TAOIMachineVector = std::vector<jrsdatabase::jrstable::TAOIMachine>;
        JRSREGISTER_CONFLICT_KEY(TAOIMachine, machine_id);
        JRSREFLECTION(TAOIMachine, machine_id, work_station_name, soft_version, start_time, project_name, one_fov_take_time, company_no, machine_no, site_no, thread_no);
#pragma endregion
#pragma region TAOIMachineStatistics 统计表
        /**<机台统计信息*/
        struct TAOIMachineStatistics :TableParamBase
        {
            /**< TODO: 需 AOI 赋值*/
            std::string machine_id;             /**< 机器编号，每台机器的唯一标识 */
            std::string project_name;           /**< 机器编号，每台机器的唯一标识 */

            int board_pass_number;              /**< 统计 主板直通个数*/
            int board_ng_number;                /**< 统计 主板的NG个数*/
            int board_misjudge_number;          /**< 统计 主板的误判个数*/

            int subboard_pass_number;           /**< 统计 子板的直通个数*/
            int subboard_ng_number;             /**< 统计 子板的Ng个数*/
            int subboard_misjudge_number;       /**< 统计 子板的误判个数*/

            int component_pass_number;          /**< 统计 原件的pass个数*/
            int component_ng_number;            /**< 统计 原件的ng个数*/
            int component_misjudge_number;      /**< 统计 原件的误判个数*/
            std::string ng_json_data;           /**< ng 数据统计*/

            TAOIMachineStatistics() : machine_id{}, project_name(""), board_pass_number(0), board_ng_number(0), board_misjudge_number(0),
                subboard_pass_number(0), subboard_ng_number(0), subboard_misjudge_number(0), component_pass_number(0), component_ng_number(0),
                component_misjudge_number(0), ng_json_data("")
            {
                this->table_name = jrstable::T_AOI_MACHINE_STATISTICS;
            }
            TAOIMachineStatistics(const std::string& machine_id_, const std::string& project_name_, int board_pass_number_,
                int board_ng_number_, int board_misjudge_number_, int subboard_pass_number_, int subboard_ng_number_,
                int subboard_misjudge_number_, int component_pass_number_, int component_ng_number_, int component_misjudge_number_,
                const std::string& ng_json_data_)
                : machine_id(machine_id_), project_name(project_name_), board_pass_number(board_pass_number_),
                board_ng_number(board_ng_number_), board_misjudge_number(board_misjudge_number_), subboard_pass_number(subboard_pass_number_),
                subboard_ng_number(subboard_ng_number_), subboard_misjudge_number(subboard_misjudge_number_), component_pass_number(component_pass_number_),
                component_ng_number(component_ng_number_), component_misjudge_number(component_misjudge_number_), ng_json_data(ng_json_data_)
            {
                this->table_name = jrstable::T_AOI_MACHINE_STATISTICS;  // 表名的初始化
            }
            static constexpr std::string_view get_alias_struct_name(TAOIMachineStatistics*)
            {
                return jrstable::T_AOI_MACHINE_STATISTICS;
            }
        };
        using TAOIMachineStatisticsVector = std::vector<jrsdatabase::jrstable::TAOIMachineStatistics>;
        JRSREFLECTION_KEY(TAOIMachineStatistics, machine_id, project_name);
        JRSREFLECTION(TAOIMachineStatistics, machine_id, project_name, board_pass_number,
            board_ng_number, board_misjudge_number,
            subboard_pass_number, subboard_ng_number, subboard_misjudge_number,
            component_pass_number, component_ng_number, component_misjudge_number, ng_json_data
        );

#pragma endregion

#pragma region TBoard 表
        /**< 板子记录信息 */
        struct TBoard :TableParamBase {
            /**<AOI 写入数据*/
            std::string board_barcode;                      /**< 总板条码 */
            std::string board_start_detect_time;            /**< 开始检测时间 */
            std::string board_end_detect_time;              /**< 结束检测时间 */
            int track_id;                                   /**< 轨道编号 */
            bool board_void;                                /**< 板子是否作废*/
            std::string board_img_path;                     /**< 整版图片路径 */
            std::string ng_img_path;                        /**< NG图片路径 */
            std::string transform_matrix;                   /**< 旋转矩阵 */

            int img_height;                                 /**< 图像高度 */
            int img_width;                                  /**< 图像宽度 */

            bool board_is_front;                            /**< 板子是否正面*/
            int subboard_cols;                              /**< 子板列数 */
            int subboard_rows;                              /**< 子板行数 */
            int num_sub_board;                              /**< 子板个数 */
            int layout;                                     /**< 子板布局方式，如鸳鸯板，太极板子，复制板子 （枚举） */
            int material;                                   /**< 整板材质 （黑板，白板，胶板） */

            int board_detect_result;                        /**< 总板检测结果：0 NG板，1 pass板，-1 测试板（测试版不进行数据统计） */
            int board_devices;                              /**< 总元件数目 */
            int board_detected_devices;                     /**< 已检测元件数目 */
            int board_no_detected_devices;                  /**< 未检测元件数目 */
            int board_masked_subboards;                     /**< 屏蔽子板数目 */
            int board_masked_subboard_devices;              /**< 屏蔽子板上的元件总数 */

            /**< AOI 写入 & center controller 修改*/
            int board_ng_devices;                           /**< 不良元件数目 */
            int board_no_judgment_subboards;                /**< 待复判子板数 */
            std::string project_name;                       /**< 项目名称 */
            std::string user_name;                          /**< 操作员名称 */
            std::string machine_id;                         /**< 机器ID */

            /**<center controller 写入参数*/
            bool board_is_rejudgement;                      /**< 板子是否复判*/
            int board_ai_rejudgment_result;                 /**<ai 复判结果 */
            std::string board_ai_rejudgment_flaw_type_names;   /**<ai 复判缺陷名称 */
            std::string board_rejudgment_time;              /**< 人工复判时间*/
            int board_rejudgment_result;                   /**< 人工复判结果*/
            int board_misjudge_devices;                     /**< 误判元件数 */
            /**<自增字段无需赋值*/
            int board_id;                                   /**< 总板ID 自增  无需赋值*/
            TBoard()
                : board_barcode{}, board_start_detect_time{}, board_end_detect_time{},
                board_img_path{}, ng_img_path{}, transform_matrix{}, track_id(-1), board_void(false),
                img_height(0), img_width(0), subboard_cols(0), subboard_rows(0),
                num_sub_board(0), layout(0), material(0), board_detect_result(0),
                board_devices(0), board_detected_devices(0), board_no_detected_devices(0),
                board_ng_devices(0), board_misjudge_devices(0), board_masked_subboards(0),
                board_masked_subboard_devices(0), board_no_judgment_subboards(0), board_is_rejudgement(false),
                board_rejudgment_time{}, board_rejudgment_result(false),
                project_name({}), user_name({}), machine_id({}), board_id(0),
                board_ai_rejudgment_result(0), board_ai_rejudgment_flaw_type_names("")
            {
                this->table_name = jrstable::T_BOARD;
            }
            TBoard(
                const std::string& board_barcode_,
                const std::string& board_start_detect_time_,
                const std::string& board_end_detect_time_,
                const std::string& board_img_path_,
                const std::string& ng_img_path_,
                const std::string& transform_matrix_,
                bool board_void_,
                int track_,
                int img_height_, int img_width_,
                int subboard_cols_, int subboard_rows_,
                int num_sub_board_, int layout_, int material_,
                int board_detect_result_, int board_devices_,
                int board_detected_devices_, int board_no_detected_devices_,
                int board_ng_devices_, int board_misjudge_devices_,
                int board_masked_subboards_, int board_masked_subboard_devices_,
                int board_no_judgment_subboards_, bool board_is_rejudgement_, const std::string& board_rejudgment_time_,
                int board_rejudgment_result_, std::string project_name_, std::string user_name_, std::string machine_id_,
                int board_id_, int board_ai_rejudgment_result_, std::string board_ai_rejudgment_flaw_type_names_)
                : board_barcode(board_barcode_), board_start_detect_time(board_start_detect_time_),
                board_end_detect_time(board_end_detect_time_), board_void(board_void_), track_id(track_),
                board_img_path(board_img_path_), ng_img_path(ng_img_path_),
                transform_matrix(transform_matrix_), img_height(img_height_),
                img_width(img_width_), subboard_cols(subboard_cols_),
                subboard_rows(subboard_rows_), num_sub_board(num_sub_board_),
                layout(layout_), material(material_), board_detect_result(board_detect_result_),
                board_devices(board_devices_), board_detected_devices(board_detected_devices_),
                board_no_detected_devices(board_no_detected_devices_),
                board_ng_devices(board_ng_devices_), board_misjudge_devices(board_misjudge_devices_),
                board_masked_subboards(board_masked_subboards_),
                board_masked_subboard_devices(board_masked_subboard_devices_),
                board_no_judgment_subboards(board_no_judgment_subboards_),
                board_is_rejudgement(board_is_rejudgement_),
                board_rejudgment_time(board_rejudgment_time_),
                board_rejudgment_result(board_rejudgment_result_),
                project_name(project_name_), user_name(user_name_),
                machine_id(machine_id_), board_id(board_id_),
                board_ai_rejudgment_result(board_ai_rejudgment_result_), board_ai_rejudgment_flaw_type_names(board_ai_rejudgment_flaw_type_names_)
            {
                this->table_name = jrstable::T_BOARD;
            }
            bool operator < (const TBoard& board_) const
            {
                return (this->board_id < board_.board_id);
            }
            bool operator <= (const TBoard& board_) const
            {
                return (this->board_id <= board_.board_id);
            }
            bool operator > (const TBoard& board_) const
            {
                return (this->board_id > board_.board_id);
            }
            bool operator >= (const TBoard& board_) const
            {
                return (this->board_id >= board_.board_id);
            }
            bool operator == (const TBoard& board_) const
            {
                return (this->board_id == board_.board_id);
            }
            bool operator != (const TBoard& board_) const
            {
                return (this->board_id != board_.board_id);
            }

            TBoard(const TBoard& other)
                : board_barcode(other.board_barcode),
                board_start_detect_time(other.board_start_detect_time),
                board_end_detect_time(other.board_end_detect_time),
                track_id(other.track_id),
                board_void(other.board_void),
                board_img_path(other.board_img_path),
                ng_img_path(other.ng_img_path),
                transform_matrix(other.transform_matrix),
                img_height(other.img_height),
                img_width(other.img_width),
                subboard_cols(other.subboard_cols),
                subboard_rows(other.subboard_rows),
                num_sub_board(other.num_sub_board),
                layout(other.layout),
                material(other.material),
                board_detect_result(other.board_detect_result),
                board_devices(other.board_devices),
                board_detected_devices(other.board_detected_devices),
                board_no_detected_devices(other.board_no_detected_devices),
                board_ng_devices(other.board_ng_devices),
                board_misjudge_devices(other.board_misjudge_devices),
                board_masked_subboards(other.board_masked_subboards),
                board_masked_subboard_devices(other.board_masked_subboard_devices),
                board_no_judgment_subboards(other.board_no_judgment_subboards),
                board_is_rejudgement(other.board_is_rejudgement),
                board_rejudgment_time(other.board_rejudgment_time),
                board_rejudgment_result(other.board_rejudgment_result),
                project_name(other.project_name),
                user_name(other.user_name),
                machine_id(other.machine_id),
                board_id(other.board_id),
                board_ai_rejudgment_result(other.board_ai_rejudgment_result),
                board_ai_rejudgment_flaw_type_names(other.board_ai_rejudgment_flaw_type_names)

            {
                this->table_name = jrstable::T_BOARD;
            }
            static constexpr std::string_view get_alias_struct_name(TBoard*)
            {
                return jrstable::T_BOARD;
            }
        };
        using TBoardVector = std::vector<TBoard>;
        JRSREGISTER_AUTO_KEY(TBoard, board_id);
        JRSREFLECTION(TBoard, board_id, board_barcode, board_start_detect_time, board_end_detect_time, board_void, track_id, board_img_path, ng_img_path, transform_matrix,
            img_height, img_width, subboard_cols, subboard_rows, num_sub_board, layout, material, board_detect_result, board_devices,
            board_detected_devices, board_no_detected_devices, board_ng_devices, board_misjudge_devices, board_masked_subboards,
            board_masked_subboard_devices, board_no_judgment_subboards, board_is_rejudgement, board_rejudgment_time, board_rejudgment_result,
            project_name, user_name, machine_id, board_ai_rejudgment_result, board_ai_rejudgment_flaw_type_names
        );
#pragma endregion

#pragma region TSubboard 表
        struct TSubboard : TableParamBase {
            /**< TODO: AOI需要赋值字段 */
           /**<AOI 写入数据*/
            int subboard_col;                        /**< 列号 */
            int subboard_row;                        /**< 行号 */
            int subboard_x;                          /**< 子板左上角x坐标，整图中的像素坐标 */
            int subboard_y;                          /**< 子板左上角y坐标，整图中的像素坐标 */
            int subboard_width;                      /**< 子板宽度 */
            int subboard_height;                     /**< 子板高度 */
            std::string subboard_barcode;            /**< 子板条码 */
            int subboard_result;                    /**< 机器检测结果 */
            std::string subboard_detect_time;        /**< 检测完成时间 */
            int subboard_devices;                    /**< 总元件数 */
            int subboard_detect_devices;             /**< 已检测元件数 */
            int subboard_no_detect_devices;          /**< 未检测元件数 */
            int subboard_pass_devices;               /**< 已检测通过元件数 */

            bool subboard_is_detection;              /**< 该子板是否被屏蔽 */
            int subboard_id;                         /**< 子板ID */
            int board_id;                            /**< 总板ID */

            /**< TODO:AOI写入 & center controller修改*/
            int subboard_ng_devices;                 /**< 已检测不合格元件数 */
            int subboard_no_judgment_devices;        /**< 已检测ng待复判元件数 */

            /**< TODO:center controller 写入数据*/
            std::string subboard_rejudgment_time;    /**< 复判时间 */
            bool subboard_is_rejudgement;
            int subboard_rejudgment_result;         /**< 复判结果 */
            int subboard_misjudge_devices;           /**< 已检测误判元件数 */
            int subboard_ai_rejudgment_result;         /**<ai 复判结果 */
            std::string subboard_ai_rejudgment_flaw_type_names;   /**<ai 复判缺陷 */

            // 默认构造函数
            TSubboard()
                : subboard_id(0), board_id(0), subboard_col(0), subboard_row(0),
                subboard_x(0), subboard_y(0), subboard_width(0), subboard_height(0),
                subboard_barcode{}, subboard_result(0), subboard_detect_time(""),
                subboard_rejudgment_time(""), subboard_rejudgment_result(0),
                subboard_devices(0), subboard_detect_devices(0), subboard_no_detect_devices(0),
                subboard_pass_devices(0), subboard_ng_devices(0), subboard_misjudge_devices(0), subboard_is_rejudgement(0),
                subboard_no_judgment_devices(0), subboard_is_detection(false), subboard_ai_rejudgment_result(0), subboard_ai_rejudgment_flaw_type_names("")
            {
                this->table_name = jrstable::T_SUBBOARD;
            }
            // 参数构造函数
            TSubboard(int subboard_id_, int board_id_, int subboard_col_, int subboard_row_,
                int subboard_x_, int subboard_y_, int subboard_width_, int subboard_height_,
                const std::string& subboard_barcode_, int subboard_result_,
                const std::string& subboard_detect_time_, const std::string& subboard_rejudgment_time_,
                int subboard_rejudgment_result_, int subboard_devices_, int subboard_detect_devices_,
                int subboard_no_detect_devices_, int subboard_pass_devices_, int subboard_ng_devices_,
                int subboard_misjudge_devices_, bool subboard_is_rejudgement_, int subboard_no_judgment_devices_,
                bool subboard_is_detection_, bool subboard_ai_rejudgment_result_, const std::string& subboard_ai_rejudgment_flaw_type_names_)
                : subboard_id(subboard_id_), board_id(board_id_), subboard_col(subboard_col_),
                subboard_row(subboard_row_), subboard_x(subboard_x_), subboard_y(subboard_y_),
                subboard_width(subboard_width_), subboard_height(subboard_height_),
                subboard_barcode(subboard_barcode_), subboard_result(subboard_result_),
                subboard_detect_time(subboard_detect_time_), subboard_rejudgment_time(subboard_rejudgment_time_),
                subboard_rejudgment_result(subboard_rejudgment_result_), subboard_devices(subboard_devices_),
                subboard_detect_devices(subboard_detect_devices_), subboard_no_detect_devices(subboard_no_detect_devices_),
                subboard_pass_devices(subboard_pass_devices_), subboard_ng_devices(subboard_ng_devices_),
                subboard_misjudge_devices(subboard_misjudge_devices_), subboard_no_judgment_devices(subboard_no_judgment_devices_),
                subboard_is_detection(subboard_is_detection_), subboard_is_rejudgement(subboard_is_rejudgement_),
                subboard_ai_rejudgment_result(subboard_ai_rejudgment_result_), subboard_ai_rejudgment_flaw_type_names(subboard_ai_rejudgment_flaw_type_names_)
            {
                this->table_name = jrstable::T_SUBBOARD;
            }
            bool operator < (const TSubboard& subboard_) const
            {
                return (this->subboard_id < subboard_.subboard_id);
            }
            bool operator <= (const TSubboard& subboard_) const
            {
                return (this->subboard_id <= subboard_.subboard_id);
            }
            bool operator > (const TSubboard& subboard_) const
            {
                return (this->subboard_id > subboard_.subboard_id);
            }
            bool operator >= (const TSubboard& subboard_) const
            {
                return (this->subboard_id >= subboard_.subboard_id);
            }
            bool operator == (const TSubboard& subboard_) const
            {
                return (this->subboard_id == subboard_.subboard_id);
            }
            bool operator != (const TSubboard& subboard_) const
            {
                return (this->subboard_id != subboard_.subboard_id);
            }
            static constexpr std::string_view get_alias_struct_name(TSubboard*)
            {
                return jrstable::T_SUBBOARD;
            }

        };
        JRSREFLECTION_KEY(TSubboard, subboard_id, board_id);
        JRSREFLECTION(TSubboard, subboard_id, board_id, subboard_col, subboard_row,
            subboard_x, subboard_y, subboard_width, subboard_height, subboard_barcode, subboard_result,
            subboard_detect_time, subboard_rejudgment_time, subboard_is_rejudgement, subboard_rejudgment_result, subboard_devices,
            subboard_detect_devices, subboard_no_detect_devices, subboard_pass_devices, subboard_ng_devices,
            subboard_misjudge_devices, subboard_no_judgment_devices, subboard_is_detection, subboard_ai_rejudgment_result, subboard_ai_rejudgment_flaw_type_names);
        using TSubboardVector = std::vector<TSubboard>;
#pragma endregion

#pragma region TDEVICE 表
        /**< 元件表 */
        struct TDevice :TableParamBase
        {
            /**<AOI 写入数据*/
            std::string device_name;                       /**< 元件名称 */
            std::string device_part_no;                    /**< 元件料号 */
            std::string device_type;                       /**< 元件类型 */
            int device_x;                                  /**< x坐标 ：像素 */
            int device_y;                                  /**< y坐标 ：像素 */
            int device_angle;                              /**< 角度 ：像素 */
            int device_width;                              /**< 宽度 ：像素 */
            int device_height;                             /**< 高度 ：像素 */
            int device_result;                            /**< 机器检测结果 */
            std::string device_img_path;                   /**< 元件图片路径 */
            std::string device_detect_time;                /**< 检测完成时间 */
            std::string device_detect_flaw_type_names;     /**< 元件缺陷名称列表 */
            std::string device_group;                      /**< 元件Group,TODO: HJC 8-18 是否需要该字段待定 */

            int cnt_detect_windows;                        /**< 共有多少检测框 */
            int ng_cnt_detect_windows;                     /**< 一共有多少检测框ng */
            bool device_is_detection;                      /**< 是否检测 */

            int device_id;                                 /**< 元件ID */
            int subboard_id;                               /**< 子板ID */
            int board_id;                                  /**< 总板ID */

            /**<center controller  写入数据*/
            int device_ai_rejudgment_result;               /**<AI 复盘结果*/
            int device_rejudgment_result;                 /**< 复判结果 */
            bool device_is_rejudged;                       /**< 是否复判 默认为 false */
            std::string device_rejudgment_flaw_type_names;   /**< 复判缺陷 */
            std::string device_ai_rejudgment_flaw_type_names;   /**< 复判缺陷 */
            std::string device_rejudgment_time;            /**< 复判时间 */
            TDevice()
                : device_id(0), subboard_id(0), board_id(0),
                device_name{}, device_part_no{}, device_type{},
                device_x(0), device_y(0), device_angle(0),
                device_width(0), device_height(0),
                device_result(false), device_is_rejudged(false), device_ai_rejudgment_result(false),
                device_rejudgment_result(false), device_is_detection(false),
                device_img_path{}, device_detect_time(""),
                device_detect_flaw_type_names(""), device_rejudgment_flaw_type_names(""),
                device_rejudgment_time(""), device_group(""),
                cnt_detect_windows(0), ng_cnt_detect_windows(0), device_ai_rejudgment_flaw_type_names("")
            {
                this->table_name = jrstable::T_DEVICE;
            }
            TDevice(int device_id_, int subboard_id_, int board_id_,
                const std::string& device_name_,
                const std::string& device_part_no_,
                const std::string& device_type_,
                int device_x_, int device_y_, int device_angle_,
                int device_width_, int device_height_,
                int device_result_, bool device_is_rejudged_,
                int device_rejudgment_result_, bool device_is_detection_,
                const std::string& device_img_path_,
                const std::string& device_detect_time_,
                const std::string& device_flaw_type_ids_,
                const std::string& device_rejudgment_flaw_type_names_,
                const std::string& device_rejudgment_time_,
                const std::string& device_group_,
                int cnt_detect_windows_, int ng_cnt_detect_windows_)
                : device_id(device_id_), subboard_id(subboard_id_), board_id(board_id_),
                device_name(device_name_), device_part_no(device_part_no_),
                device_type(device_type_), device_x(device_x_), device_y(device_y_),
                device_angle(device_angle_), device_width(device_width_),
                device_height(device_height_), device_result(device_result_),
                device_is_rejudged(device_is_rejudged_), device_rejudgment_result(device_rejudgment_result_),
                device_is_detection(device_is_detection_), device_img_path(device_img_path_),
                device_detect_time(device_detect_time_), device_detect_flaw_type_names(device_flaw_type_ids_),
                device_rejudgment_flaw_type_names(device_rejudgment_flaw_type_names_),
                device_rejudgment_time(device_rejudgment_time_), device_group(device_group_),
                cnt_detect_windows(cnt_detect_windows_), ng_cnt_detect_windows(ng_cnt_detect_windows_)
            {
                this->table_name = jrstable::T_DEVICE;
            }
            static constexpr std::string_view get_alias_struct_name(TDevice*)
            {
                return jrstable::T_DEVICE;
            }
        };
        JRSREFLECTION_KEY(TDevice, board_id, subboard_id, device_id);
        JRSREFLECTION(TDevice, device_id, subboard_id, board_id, device_name,
            device_part_no, device_type, device_x, device_y, device_angle, device_width,
            device_height, device_result, device_is_detection,
            device_img_path, device_detect_time, device_detect_flaw_type_names,
            device_is_rejudged, device_rejudgment_result, device_rejudgment_flaw_type_names,
            device_ai_rejudgment_result, device_ai_rejudgment_flaw_type_names,
            device_rejudgment_time, device_group, cnt_detect_windows, ng_cnt_detect_windows
        );
        using TDeviceVector = std::vector<TDevice>;
#pragma endregion

#pragma region TGROUP 表
        /**< 检测框组表 */
        struct TGroup :TableParamBase
        {
            enum class Usage
            {
                UNDEFINED = -1,
                DEFAULT,
                MEASURE_DIST,
                LEAD_GROUP,
                POLARITY_GROUP,
                MEASURE_HEIGHT,
                ALIGNMENT_GROUP,
                OCV_GROUP,
                PAD_GROUP,
                VOLUME_GROUP,
                HEIGHTAREA_GROUP,
                HEIGHTGRADIENT_GROUP,
                DISTANCEANGLE_GROUP,
                CUSTOM_OCV_GROUP,
                COUNT,
            };
            /**< AOI 写入数据*/
            int subboard_id;           /**< 子板ID */
            int device_id;             /**< 元件ID */
            int group_id;              /**< 组ID */
            std::string group_name;    /**< 检测组名称 */
            int group_usage;           /**< 检测组类型 */
            int group_result;         /**< 检测组总结果 */
            int board_id;              /**< 总板ID */
            TGroup()
                : board_id(0), subboard_id(0), device_id(0), group_id(0),
                group_name(""), group_usage(static_cast<int>(Usage::UNDEFINED)), group_result(false)
            {
                this->table_name = jrstable::T_GROUP;
            }
            TGroup(int board_id_, int subboard_id_, int device_id_, int group_id_,
                const std::string& group_name_, int group_usage_, int group_result_)
                : board_id(board_id_), subboard_id(subboard_id_), device_id(device_id_),
                group_id(group_id_), group_name(group_name_), group_usage(group_usage_), group_result(group_result_)
            {
                this->table_name = jrstable::T_GROUP;
            }
            friend std::ostream& operator<<(std::ostream& out, const TGroup& info)
            {
                out << "\r\n" << " board_id " << info.board_id << "\r\n"
                    << " subboard_id " << info.subboard_id << "\r\n"
                    << " device_id " << info.device_id << "\r\n"
                    << " group_id " << info.group_id << "\r\n"
                    << " group_name " << info.group_name.c_str() << "\r\n"
                    << " group_usage " << info.group_usage << "\r\n"
                    << " group_result " << info.group_result << "\r\n";
                return out;
            }
            static constexpr std::string_view get_alias_struct_name(TGroup*)
            {
                return jrstable::T_GROUP;
            }
        };

        JRSREFLECTION_KEY(TGroup, board_id, subboard_id, device_id, group_id);
        JRSREFLECTION(TGroup, board_id, subboard_id, device_id, group_id, group_name, group_usage, group_result);
        using TGroupVector = std::vector<TGroup>;

#pragma endregion

#pragma region DetectType 表
        struct TDetectType : TableParamBase
        {
            int detect_type_id;                         /**< 缺陷编号ID   */
            std::string detect_type_name;               /**< 缺陷类型名称 */
            std::string detect_type_description;        /**< 缺陷类型描述 */
            std::string map_default_code;               /**< 映射错误代码 */
            std::string map_default_name;               /**< 映射名称     */

            TDetectType()
                : detect_type_id(0),
                detect_type_name(""),
                detect_type_description(""),
                map_default_code(""),
                map_default_name("")
            {
                this->table_name = jrstable::T_DETECT_TYPE;
            }
            TDetectType(
                int id,
                std::string name,
                std::string description,
                std::string default_code,
                std::string default_name
            )
                : detect_type_id(id),
                detect_type_name(name),
                detect_type_description(description),
                map_default_code(default_code),
                map_default_name(default_name)
            {
                this->table_name = jrstable::T_DETECT_TYPE;
            }
            static constexpr std::string_view get_alias_struct_name(TDetectType*)
            {
                return jrstable::T_DETECT_TYPE;
            }
        };
        using TDetectTypeVector = std::vector<TDetectType>;
        JRSREFLECTION(TDetectType, detect_type_id, detect_type_name, detect_type_description, map_default_code, map_default_name);
#pragma endregion

#pragma region SubDetectWindow 表
        /**< 检测框表 */
        struct TDetectWindow :TableParamBase {
            /**< TODO: AOI需赋值字段*/
            std::string detect_window_name;            /**< 检测框名称 */
            int detect_window_usage;                   /**< 检测框类型 */
            int detect_window_result;                 /**< 检测框结果 */
            int detect_window_detect_code;             /**< 缺陷类型编号 */
            bool detect_window_is_child_window;        /**< 是否在group组内的检测框 */
            std::string detect_window_result_flaw_type_name;     /**< 检测框结果 缺陷类型ID*/
            std::string window_result_name;            /**< 检测框结果名称   TODO: 是否需要？ 待定*/
            /** {"detect_value":0,"lower_limit":-2147483648, "standard_value":-2147483648,"upper_limit":-2147483648}*/
            std::string window_result_data;            /**< 检测结果数据   */
            std::string algo_result_position;          /**< 算法测量结果位置坐标*/
            std::string algo_result_matrix;            /**< 算法输出旋转矩阵*/
            int template_img_id;                       /**< 模版图片ID */
            int light_img_id;                          /**< 灯光图片ID */
            int detect_window_level;                   /**< 检测等级 */
            int detect_window_x;                       /**< x坐标*/
            int detect_window_y;                       /**< y坐标*/
            int detect_window_width;                   /**< 宽度 */
            int detect_window_height;                  /**< 高度 */
            int detect_window_angle;                   /**< 旋转角度*/
            std::string color_param;                   /**< 颜色参数 */
            std::string light_param;                   /**< 灯光配置 */
            int detect_window_state;                   /**< 检测状态（ok，ng，未检测，不检测，检测异常） */
            bool detect_window_is_detection;           /**< 是否检测 */

            int detect_window_id;                       /**< 检测框ID */
            int subboard_id;                            /**< 子板ID   */
            int device_id;                              /**< 元件ID   */
            int group_id;                               /**< 组ID     */
            std::string algorithm_name;                           /**< 算法ID   */
            int board_id;                               /**< 总板ID */
            /**< TODO:center controller 写入数据*/
            bool detect_window_is_rejudged;             /**< 检测框是否复判 */

            int detect_window_ai_rejudged_result;      /**< 检测框AI复盘结果*/
            std::string detect_window_ai_rejudged_type_name;      /**< 检测框AI复盘id*/
            int window_rejudgement_result;             /**< 复判结果*/
            std::string detect_window_rejudged_type_name;         /**< 检测框复判类型ID */
            std::string window_rejudgement_time;        /**< 复判时间*/
            TDetectWindow()
                : detect_window_id(0),
                board_id(0),
                subboard_id(0),
                device_id(0),
                group_id(0),
                algorithm_name(""),
                detect_window_name{},
                detect_window_usage(0),
                detect_window_result(false),
                detect_window_detect_code(0),
                detect_window_is_child_window(false),
                detect_window_result_flaw_type_name(""),
                window_result_name{},
                window_result_data(""),
                template_img_id(0),
                light_img_id(0),
                detect_window_level(0),
                detect_window_width(0),
                detect_window_height(0),
                color_param(""),
                light_param(""),
                detect_window_state(0),
                detect_window_is_detection(false),
                detect_window_is_rejudged(false),
                detect_window_rejudged_type_name(""),
                detect_window_ai_rejudged_result(false),      /**< 检测框AI复盘结果*/
                detect_window_ai_rejudged_type_name(""),
                window_rejudgement_result(false),
                window_rejudgement_time(""),
                algo_result_position(""),
                algo_result_matrix("")
            {
                this->table_name = jrstable::T_DETECT_WINDOW;
            }
            TDetectWindow(
                int id, int board, int subboard, int device, int group, std::string algo_name,
                const std::string& name, int usage, int result, int detect_code,
                bool is_child_window, std::string flaw_name, const std::string& result_name,
                const std::string& data, int template_img, int light_img, int level, /*int cx, int cy,*/
                int width, int height, const std::string& color, const std::string& light,
                int state, bool is_detection, bool is_rejudged, std::string rejudged_type_name,
                int window_rejudgement_result_, std::string window_rejudgement_time_,
                const std::string& algo_result_position, const std::string& algo_result_matrix
            )
                : detect_window_id(id),
                board_id(board),
                subboard_id(subboard),
                device_id(device),
                group_id(group),
                algorithm_name(algo_name),
                detect_window_name(name),
                detect_window_usage(usage),
                detect_window_result(result),
                detect_window_detect_code(detect_code),
                detect_window_is_child_window(is_child_window),
                detect_window_result_flaw_type_name(flaw_name),
                window_result_name(result_name),
                window_result_data(data),
                algo_result_position(algo_result_position),
                algo_result_matrix(algo_result_matrix),
                template_img_id(template_img),
                light_img_id(light_img),
                detect_window_level(level),
                detect_window_width(width),
                detect_window_height(height),
                color_param(color),
                light_param(light),
                detect_window_state(state),
                detect_window_is_detection(is_detection),
                detect_window_is_rejudged(is_rejudged),
                detect_window_rejudged_type_name(rejudged_type_name),
                detect_window_ai_rejudged_result(detect_window_ai_rejudged_result),
                detect_window_ai_rejudged_type_name(detect_window_ai_rejudged_type_name),
                window_rejudgement_result(window_rejudgement_result_),
                window_rejudgement_time(window_rejudgement_time_)
            {
                this->table_name = jrstable::T_DETECT_WINDOW;
            }
            static constexpr std::string_view get_alias_struct_name(TDetectWindow*)
            {
                return jrstable::T_DETECT_WINDOW;
            }
        };
        using TDetectWindowVector = std::vector<TDetectWindow>;
        JRSREFLECTION_KEY(TDetectWindow, board_id, subboard_id, device_id, group_id, detect_window_id);
        JRSREFLECTION(TDetectWindow, detect_window_id, board_id,
            subboard_id, device_id, group_id, algorithm_name, detect_window_name, detect_window_usage,
            detect_window_result, detect_window_detect_code, detect_window_is_child_window,
            detect_window_result_flaw_type_name, window_result_name, window_result_data, algo_result_position, algo_result_matrix,
            template_img_id, light_img_id, detect_window_level, detect_window_width, detect_window_height,
            color_param, light_param, detect_window_state, detect_window_is_detection, detect_window_is_rejudged,
            detect_window_rejudged_type_name, window_rejudgement_result, detect_window_ai_rejudged_result, detect_window_ai_rejudged_type_name, window_rejudgement_time);
#pragma endregion
    }

    namespace jrsselect
    {
        /**<查询单个表中的数据 */
        struct SelectorParamBase :jrstable::TableParamBase
        {
            std::string select_name;			/**< 查询谁 */
            std::string where_condition;        /**< 查询条件*/
            SelectorParamBase() = default;
            virtual ~SelectorParamBase() = default;
        };
        using SelectorParamBasePtr = std::shared_ptr<SelectorParamBase>;

        /**<查询板子 */
        struct SelectTable :SelectorParamBase
        {
            std::optional<jrstable::TBoardVector> boards;
            std::optional <jrstable::TSubboardVector> subboards;
            std::optional <jrstable::TDeviceVector>  devices;
            std::optional <jrstable::TGroupVector> groups;
            std::optional <jrstable::TDetectWindowVector> detect_windows;
            std::optional <jrstable::TDetectTypeVector> detect_types;
            std::optional <jrstable::TProjectVector> projects;
            std::optional <jrstable::TUserVector> users;
            std::optional <jrstable::TAOIMachineVector> aoi_machines;
            std::optional <jrstable::TAOIMachineStatisticsVector> machine_statisticss;
            SelectTable()
            {
                this->table_name = "";
                this->select_name = "";
                this->where_condition = ""; /**< 查询所有数据 */
            }
        };
        using SelectTablePtr = std::shared_ptr<SelectTable>;

        /**<自定义查询数据 */
        struct SelectCustom :SelectorParamBase
        {
            struct SelectCustomSubboardBarcode
            {
                int board_id;               /**< 整板ID */
                //std::string board_barcode; /**< 整板二维码 */
                //std::string project_name;   /**< 工程名称 */
                std::unordered_map<int, std::string> subboard_id_and_barcode; /**<子板id 和子板二维码*/
            };
            std::optional<SelectCustomSubboardBarcode> select_subboard_barcodes;  /**<查询到的子板二维码*/
        };
        using SelectCustomPtr = std::shared_ptr<SelectCustom>;

        //数据统计信息

        /**< TODO:select_name 查询时使用 */
        static constexpr char T_BOARD_SELECT_BY_WHERE_CONDITION[] = "t_board_select_by_where_condition";
        static constexpr char T_AOI_MACHINE_SELECT_BY_WHERE_CONDITION[] = "t_aoi_machine_select_by_where_condition";
        static constexpr char T_AOI_MACHINE_STATISTICS_SELECT_BY_WHERE_CONDITION[] = "t_aoi_machine_statistics_select_by_where_condition";
        static constexpr char T_DETECT_TYPE_SELECT_BY_WHERE_CONDITION[] = "t_detect_type_select_by_where_condition";
        static constexpr char T_DETECT_WINDOW_SELECT_BY_WHERE_CONDITION[] = "t_detect_window_select_by_where_condition";
        static constexpr char T_DEVICE_SELECT_BY_WHERE_CONDITION[] = "t_device_select_by_where_condition";
        static constexpr char T_PROJECT_SELECT_BY_WHERE_CONDITION[] = "t_project_select_by_where_condition";
        static constexpr char T_GROUP_SELECT_BY_WHERE_CONDITION[] = "t_group_select_by_where_condition";
        /** <子板查询 */
        static constexpr char T_SUBBOARD_SELECT_BY_WHERE_CONDITION[] = "t_subboard_select_by_where_condition";
        static constexpr char T_SELECT_SUBBARCODES_OF_ENTIRETY_BOARD_BY_SUBBOARD_BARCODE[]
            = "t_select_subbarcodes_of_entirety_board_by_subboard_barcode";/**<通过子板二维码 查询这个整板下的所有子板二维码*/



        static constexpr char T_USER_SELECT_BY_WHERE_CONDITION[] = "t_user_select_by_where_condition";
    };
    /** 自定义更新字段 2025/2/11 HJC*/
    struct CustomUpdate :jrstable::TableParamBase
    {
        std::map<std::string, std::string> fields_and_new_value_map; /**< 更新的字段和值 */
        std::string condition;/**< 条件 */
    };
    using CustomUpdatePtr = std::shared_ptr<CustomUpdate>;

    struct TablesDataOperator /**<对数据的操作 By HJC 2024/12/20*/
    {
        /**TODO: 删除如何扩展 有待考虑 */
        enum class OperatorType
        {
            Insert = 0,  //插入数据
            Replace,     //有则更新，没有则插入
            Update,      //更新数据
        };
        //std::optional<std::pair<OperatorType, CustomUpdate>> custom_update;
        std::optional<std::pair<OperatorType, jrstable::TBoardVector>> boards;
        std::optional<std::pair<OperatorType, jrstable::TSubboardVector>> subboards;
        std::optional<std::pair<OperatorType, jrstable::TDeviceVector>>  devices;
        std::optional<std::pair<OperatorType, jrstable::TGroupVector>> groups;
        std::optional<std::pair<OperatorType, jrstable::TDetectWindowVector>> detect_windows;
        std::optional<std::pair<OperatorType, jrstable::TDetectTypeVector>> detect_types;
        std::optional<std::pair<OperatorType, jrstable::TProjectVector>> projects;
        std::optional<std::pair<OperatorType, jrstable::TUserVector>> users;
        std::optional<std::pair<OperatorType, jrstable::TAOIMachine>> aoi_syss;
        std::optional<std::pair<OperatorType, jrstable::TAOIMachineStatistics>> system_params;
    };
    using TablesDataOperatorPtr = std::shared_ptr<TablesDataOperator>;
}
// 判断类型是否为 std::vector<T>
template<typename T>
struct is_vector : std::false_type {};

template<typename T, typename A>
struct is_vector<std::vector<T, A>> : std::true_type {};

// SFINAE 检查是否有 table_name 成员
template<typename T>
auto get_table_name_safe(const T& obj) -> decltype(obj.table_name)
{
    return obj.table_name;
}

inline std::string get_table_name_safe(...) {
    return "未知表";
}

#endif //!__DBSTRUCTBASE_HPP__
