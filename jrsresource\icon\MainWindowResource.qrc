<RCC>
    <qresource prefix="/">
        <file>image/JRS.ico</file>
        <file>image/add_device.png</file>
        <file>image/add_graphics.png</file>
        <file>image/adddevice.png</file>
        <file>image/AOI.png</file>
        <file>image/axismove.png</file>
        <file>image/CAD.png</file>
        <file>image/cad_angle+90.png</file>
        <file>image/cad_clock_wise.png</file>
        <file>image/cad_left_right_mirror.png</file>
        <file>image/cad_rotate_90.png</file>
        <file>image/cad_top_bottom_mirror.png</file>
        <file>image/center.png</file>
        <file>image/center_view.png</file>
        <file>image/choose_folder.png</file>
        <file>image/close_log.png</file>
        <file>image/copy.png</file>
        <file>image/copy1.png</file>
        <file>image/copy12.png</file>
        <file>image/debug.png</file>
        <file>image/deletedevice.png</file>
        <file>image/dete_copy0.png</file>
        <file>image/dete_copy90.png</file>
        <file>image/dete_copy180.png</file>
        <file>image/dete_copyvertical.png</file>
        <file>image/dete_deleteall.png</file>
        <file>image/dete_deleteone.png</file>
        <file>image/detect_1.png</file>
        <file>image/detect_2.png</file>
        <file>image/detect_3.png</file>
        <file>image/detect_rect.png</file>
        <file>image/detect_right.png</file>
        <file>image/detect_right_short.png</file>
        <file>image/device_1.png</file>
        <file>image/device_2.png</file>
        <file>image/device_3.png</file>
        <file>image/device_add.png</file>
        <file>image/device_copy.png</file>
        <file>image/device_delete.png</file>
        <file>image/device_paste.png</file>
        <file>image/device_rotate.png</file>
        <file>image/devicemanager.png</file>
        <file>image/downmove.png</file>
        <file>image/fatal.png</file>
        <file>image/fatal_warning.png</file>
        <file>image/fold.png</file>
        <file>image/gray.png</file>
        <file>image/green.png</file>
        <file>image/hardware_bright.png</file>
        <file>image/hardware_capture.png</file>
        <file>image/hardware_control.png</file>
        <file>image/hardware_find_img.png</file>
        <file>image/hardware_lighting.png</file>
        <file>image/hardware_stop_capture.png</file>
        <file>image/help.png</file>
        <file>image/image.png</file>
        <file>image/img_1_1.png</file>
        <file>image/img_down_size.png</file>
        <file>image/img_max.png</file>
        <file>image/img_up_size.png</file>
        <file>image/index.png</file>
        <file>image/information.png</file>
        <file>image/inputboard.png</file>
        <file>image/JRS.jpg</file>
        <file>image/leftbottommove.png</file>
        <file>image/leftmove.png</file>
        <file>image/lefttopmove.png</file>
        <file>image/login.png</file>
        <file>image/min.png</file>
        <file>image/motion.png</file>
        <file>image/newfile.png</file>
        <file>image/open.png</file>
        <file>image/optimizecad.png</file>
        <file>image/paste.png</file>
        <file>image/permissionsmanager.png</file>
        <file>image/photograph.png</file>
        <file>image/project.png</file>
        <file>image/rail_down.png</file>
        <file>image/rail_in.png</file>
        <file>image/rail_out.png</file>
        <file>image/rail_stop.png</file>
        <file>image/rail_up.png</file>
        <file>image/realtime.png</file>
        <file>image/recover.png</file>
        <file>image/red.png</file>
        <file>image/refresh.png</file>
        <file>image/refresh_3.png</file>
        <file>image/reset.png</file>
        <file>image/reset1.png</file>
        <file>image/reseterror.png</file>
        <file>image/return.png</file>
        <file>image/revoke.png</file>
        <file>image/rightbottommove.png</file>
        <file>image/rightmove.png</file>
        <file>image/righttopmove.png</file>
        <file>image/rotate90.png</file>
        <file>image/rotate90_2.png.png</file>
        <file>image/rotate90_3.png</file>
        <file>image/rotate90_4.png</file>
        <file>image/rotate90_42.png</file>
        <file>image/rotate-180.png</file>
        <file>image/run.png</file>
        <file>image/run_1.png</file>
        <file>image/run_2.png</file>
        <file>image/run_3.png</file>
        <file>image/setting.png</file>
        <file>image/shot_1.png</file>
        <file>image/shot_2.png</file>
        <file>image/test_detebox.png</file>
        <file>image/test_device.png</file>
        <file>image/test_device_located.png</file>
        <file>image/test_materialcode.png</file>
        <file>image/test_materialcode_located.png</file>
        <file>image/tool_3d_show.png</file>
        <file>image/tool_color_adjust.png</file>
        <file>image/tool_show.png</file>
        <file>image/track.png</file>
        <file>image/unfold.png</file>
        <file>image/updown.png</file>
        <file>image/upmove.png</file>
        <file>image/zero.png</file>
        <file>image/antiundoaction.png</file>
        <file>image/undoaction.png</file>
        <file>image/saveas.png</file>
        <file>image/save.png</file>
        <file>image/newproj.png</file>
        <file>image/openproj.png</file>
        <file>image/model.png</file>
        <file>image/realtimeing.png</file>
        <file>image/3D_2.png</file>
        <file>image/3D_3.png</file>
        <file>image/3D_1.png</file>
        <file>image/saveas.svg</file>
        <file>image/save.svg</file>
        <file>image/close.png</file>
        <file>image/stop.png</file>
        <file>image/affirm.png</file>
        <file>image/cancel.png</file>
        <file>image/delete.png</file>
        <file>image/group.png</file>
        <file>image/lock.png</file>
        <file>image/unlock.png</file>
        <file>image/stop2.png</file>
        <file>image/initial.png</file>
        <file>image/clear.png</file>
        <file>image/group.png</file>
        <file>image/loadpicture.png</file>
        <file>image/savepicture.png</file>
        <file>image/pos_align.png</file>
        <file>image/add_pos.png</file>
        <file>image/add_barcode.png</file>
        <file>image/add_bad_signal.png</file>
        <file>image/all_move.png</file>
        <file>image/run_and_save.png</file>
        <file>image/down.png</file>
        <file>image/up.png</file>
        <file>image/coponent_detect_copy.png</file>
        <file>image/copy_action.png</file>
        <file>image/showmarkresult.png</file>
        <file>image/board_ai.png</file>
        <file>image/body_ai.png</file>
        <file>image/subboard_ai.png</file>
        <file>image/appendproject.png</file>

        <file>image/left_bottom_to_right_top_s.png</file>
        <file>image/left_bottom_to_right_top_vertical_s.png</file>
        <file>image/left_bottom_to_right_top_vertical_z.png</file>
        <file>image/left_bottom_to_right_top_z.png</file>
        <file>image/left_top_to_right_bottom_s.png</file>
        <file>image/left_top_to_right_bottom_vertical_s.png</file>
        <file>image/left_top_to_right_bottom_vertical_z.png</file>
        <file>image/left_top_to_right_bottom_z.png</file>
        <file>image/right_bottom_to_left_top_s.png</file>
        <file>image/right_bottom_to_left_top_vertical_s.png</file>
        <file>image/right_bottom_to_left_top_vertical_z.png</file>
        <file>image/right_bottom_to_left_top_z.png</file>
        <file>image/right_top_to_left_bottom_s.png</file>
        <file>image/right_top_to_left_bottom_vertical_s.png</file>
        <file>image/right_top_to_left_bottom_vertical_z.png</file>
        <file>image/right_top_to_left_bottom_z.png</file>
    </qresource>
    <qresource prefix="/render">
        <file>renderimage/Busy.ani</file>
        <file>renderimage/HandClose.cur</file>
        <file>renderimage/HandWriting.cur</file>
        <file>renderimage/Move.cur</file>
        <file>renderimage/MoveAll.cur</file>
        <file>renderimage/Person Select.cur</file>
        <file>renderimage/Pickup.cur</file>
        <file>renderimage/ResizeDiagonal1.cur</file>
        <file>renderimage/ResizeDiagonal2.cur</file>
        <file>renderimage/ResizeHorizontal.cur</file>
        <file>renderimage/ResizeVertical.cur</file>
        <file>renderimage/Rotate.cur</file>
        <file>renderimage/SelectAlternate.cur</file>
        <file>renderimage/SelectHelp.cur</file>
        <file>renderimage/SelectLink.cur</file>
        <file>renderimage/SelectMove.cur</file>
        <file>renderimage/SelectNormal.cur</file>
        <file>renderimage/SelectPosition.cur</file>
        <file>renderimage/SelectPrecision.cur</file>
        <file>renderimage/SelectText.cur</file>
        <file>renderimage/Unavailable.cur</file>
        <file>renderimage/WorkingInBackground.ani</file>
    </qresource>
</RCC>
