﻿/**
* @file   render2dview.h
* @brief  渲染界面view实例
* @details
* <AUTHOR>
* @date 2024.7.16
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Description
* <tr><td>2024.7.16         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/
#ifndef __JRS_ADD_PAD_VIEW_H__
#define __JRS_ADD_PAD_VIEW_H__
// QT
// Custom
#include "viewbase.h"



QT_BEGIN_NAMESPACE
namespace Ui
{
    class AddPadView;
};
QT_END_NAMESPACE
class GraphicsAbstract;

namespace jrsaoi
{

    class AddPadView :public QWidget
    {
        Q_OBJECT
    public:
        explicit AddPadView(QWidget* parent = nullptr);
        ~AddPadView();
    public slots:
        void SlotUpdateAddView(std::shared_ptr<GraphicsAbstract> pad_group_);
    signals:
        void SigPadNumber(int pad_number_);
    private:
        int Init();
        int InitConnect();
        int InitView();

        jrsdata::ComponentUnit::PadType _pad_type;
        std::shared_ptr<GraphicsAbstract> _pad_group;
        Ui::AddPadView* ui;
    };
}
#endif // !__JRS_ADD_PAD_VIEW_H__
