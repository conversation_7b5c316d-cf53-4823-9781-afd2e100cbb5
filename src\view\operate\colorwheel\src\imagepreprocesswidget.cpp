#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
//QT
#include <QtWidgets>
#include <QHBoxLayout>
#include <QVBoxLayout>

//Cutom
#include "imagepreprocesswidget.h"
#include "imagepreview.h"
#include "singleslider.h"
#include "imagepreprocessalgo.h"

#pragma warning(pop)
ImagePreProcessWindow::ImagePreProcessWindow(QWidget* parent) 
    : QWidget(parent)
{
    SetupUi();
    InitConnect();    
}

int ImagePreProcessWindow::SetPreProcessParamsChangedCallback(PreProcessParamsChangedFunc func)
{
    pre_process_params_changed_func_ = func;
    return 0;
}

int ImagePreProcessWindow::SetProcessImage(const cv::Mat& params)
{
	if (params.empty()) return -1;
    m_input_image = params.clone();
    process_image_group_.clear();
	ImageProcessor run;
    {
		process_image_group_.push_back(params);
        if (params.channels() == 3)
        {
			Mats3 true_splits_rgb = run.GetRgbChannel(params);
	        Mats3 true_splits_hsv = run.GetHsvChannel(params);
            process_image_group_.push_back(true_splits_rgb[0]);
            process_image_group_.push_back(true_splits_rgb[1]);
            process_image_group_.push_back(true_splits_rgb[2]);
	        process_image_group_.push_back(true_splits_hsv[0]);
            process_image_group_.push_back(true_splits_hsv[1]);
            process_image_group_.push_back(true_splits_hsv[2]);
        }
        else
        {
			process_image_group_.push_back(params);
			process_image_group_.push_back(params);
			process_image_group_.push_back(params);
			process_image_group_.push_back(params);
			process_image_group_.push_back(params);
			process_image_group_.push_back(params);
        }
    }
    return 0;
}

int ImagePreProcessWindow::SetPreProcessParmas(PreProcessParams& params,bool is_origin_image)
{
    pre_process_params_ = params;
	
    m_is_origin_mat = is_origin_image;

	invert_checkbox_->setChecked(params.is_reverse);
	brightness_lineedit_->setText(QString::number(params.brightness_value, 'f', 2));
    contrast_lineedit_->setText(QString::number(params.contrast_value, 'f', 2));
    hue_lineedit_->setText(QString::number(params.hue_value, 'f', 2));
    saturation_lineedit_->setText(QString::number(params.saturation_value, 'f', 2));
    gamma_lineedit_->setText(QString::number(params.gamma_value, 'f', 2));

    brightness_slider_->setValue(DoubleToMapRange(params.brightness_value, 0, 2));
    contrast_slider_->setValue(DoubleToMapRange(params.contrast_value, 0, 2));
    hue_slider_->setValue(DoubleToMapRange(params.hue_value, -180, 180));
    saturation_slider_->setValue(DoubleToMapRange(params.saturation_value, 0, 3));
    gamma_slider_->setValue(DoubleToMapRange(params.gamma_value, 0, 10));
    
    is_initialize = false;
    m_is_origin_mat = true;
    return 0;
}

int ImagePreProcessWindow::GetPreProcessParam(PreProcessParams&
    pre_process_params)
{
	pre_process_params.brightness_value = brightness_lineedit_->text().toFloat(); 
	pre_process_params.contrast_value = contrast_lineedit_->text().toFloat();  
    pre_process_params.hue_value = hue_lineedit_->text().toFloat(); 
	pre_process_params.saturation_value = saturation_lineedit_->text().toFloat(); 
	pre_process_params.gamma_value = gamma_lineedit_->text().toFloat();
	pre_process_params.is_reverse = invert_checkbox_->checkState();
    return 0;
}

cv::Mat ImagePreProcessWindow::GetPreProcessImage()
{
    return process_image_group_[id_];
}

ImagePreProcessWindow::~ImagePreProcessWindow() {}

void ImagePreProcessWindow::MapRangeToDouble(int value, QLineEdit* slider,double min, double max)
{
    auto cur_value = min + (max - min) * (value / 1000.0);
    slider->setText(QString::number(cur_value));
}

void ImagePreProcessWindow::GetImageId(int id)
{
    is_initialize = true;
    id_ = id;
    pre_process_params_.image_id = id_;
    is_initialize = false;

	if (pre_process_params_changed_func_)
    {
        pre_process_params_changed_func_(pre_process_params_, result_);
    }
}

void ImagePreProcessWindow::UpdataProcessParams()
{
	pre_process_params_.brightness_value = brightness_lineedit_->text().toFloat();
	pre_process_params_.contrast_value = contrast_lineedit_->text().toFloat();
    pre_process_params_.hue_value = hue_lineedit_->text().toFloat();
	pre_process_params_.saturation_value = saturation_lineedit_->text().toFloat();
	pre_process_params_.gamma_value = gamma_lineedit_->text().toFloat();
	pre_process_params_.is_reverse = invert_checkbox_->checkState();

	if (pre_process_params_changed_func_ && m_is_origin_mat) 
    {
        pre_process_params_changed_func_(pre_process_params_, result_);
    }
}

void ImagePreProcessWindow::RestoreParams()
{
	brightness_slider_->setValue(DoubleToMapRange(1.0, 0, 2));
    contrast_slider_->setValue(DoubleToMapRange(1.0, 0, 2));
    hue_slider_->setValue(DoubleToMapRange(0.0, -180, 180));
    saturation_slider_->setValue(DoubleToMapRange(1.0, 0, 3));
    gamma_slider_->setValue(DoubleToMapRange(1.0, 0, 10));

	pre_process_params_.brightness_value = 1.0;
	pre_process_params_.contrast_value = 1.0;
    pre_process_params_.hue_value = 0.0;
	pre_process_params_.saturation_value = 1.0;
	pre_process_params_.gamma_value = 1.0;
	pre_process_params_.is_reverse = false;

    brightness_lineedit_->setText("1.0");
    contrast_lineedit_->setText("1.0");
    hue_lineedit_->setText("0.0");
    saturation_lineedit_->setText("1.0");
    gamma_lineedit_->setText("1.0");
    invert_checkbox_->setChecked(false);
	if (pre_process_params_changed_func_)
    {
        pre_process_params_changed_func_(pre_process_params_,result_);
    }
}

void ImagePreProcessWindow::SetupUi() 
{
    // 创建主布局
    QVBoxLayout* main_layout = new QVBoxLayout(this);
    main_layout->setSpacing(10); // 调整组件间距
    main_layout->setContentsMargins(1, 1, 1, 1);
    
    // 1. 工具栏面板
    QGroupBox* toolbar_group = new QGroupBox(tr("工具栏"));
    toolbar_group->setContentsMargins(0, 0, 0, 0);
    QHBoxLayout* toolbar_layout = new QHBoxLayout(toolbar_group);
    toolbar_layout->setContentsMargins(8, 20, 8, 8); // 与图像取色面板对齐
    
    preview_button_ = new QPushButton(tr("预览图"));
    restore_button_ = new QPushButton(tr("还原"));
    invert_checkbox_ = new QCheckBox(tr("图像取反"));
    
    // 设置按钮最小宽度，使界面更协调
    preview_button_->setMinimumWidth(80);
    restore_button_->setMinimumWidth(80);
    
    toolbar_layout->addWidget(preview_button_);
    toolbar_layout->addWidget(restore_button_);
    toolbar_layout->addStretch(); // 添加弹簧
    toolbar_layout->addWidget(invert_checkbox_);

    // 2. 图像增强面板
    QGroupBox* enhance_group = new QGroupBox(tr("图像增强"));
    QGridLayout* enhance_layout = new QGridLayout(enhance_group);
    enhance_layout->setSpacing(8);  // 增加网格间距
    enhance_layout->setContentsMargins(8, 20, 8, 8);  // 调整边距
    
    // 统一设置标签样式
    QVector<QLabel*> labels;
    labels << new QLabel(tr("亮度:"))
           << new QLabel(tr("对比度:"))
           << new QLabel(tr("色调:"))
           << new QLabel(tr("饱和度:"))
           << new QLabel(tr("伽玛值:"));
           
    for(auto label : labels) {
        label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        label->setMinimumWidth(60);
        label->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
    }

    // 创建并配置数值输入框
    const int line_edit_width = 60;
    QVector<QLineEdit*> line_edits = {
        brightness_lineedit_ = new QLineEdit("1.0"),
        contrast_lineedit_ = new QLineEdit("1.0"),
        hue_lineedit_ = new QLineEdit("0.0"),
        saturation_lineedit_ = new QLineEdit("1.0"),
        gamma_lineedit_ = new QLineEdit("1.0")
    };

    // 统一设置输入框样式
    for(auto edit : line_edits) {
        edit->setAlignment(Qt::AlignCenter);
        edit->setMinimumWidth(line_edit_width);
        edit->setStyleSheet("QLineEdit { padding: 4px; }"); // 增加内边距
        edit->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
    }

    // 创建并配置滑动条
    QVector<CustomSlider*> sliders = {
        brightness_slider_ = new CustomSlider(),
        contrast_slider_ = new CustomSlider(),
        hue_slider_ = new CustomSlider(),
        saturation_slider_ = new CustomSlider(),
        gamma_slider_ = new CustomSlider()
    };

    // 统一设置滑动条样式
    for(auto slider : sliders) {
        slider->setOrientation(Qt::Horizontal);
        slider->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    }

    // 布局网格
    for(int i = 0; i < 5; i++) {
        enhance_layout->addWidget(labels[i], i, 0);
        enhance_layout->addWidget(line_edits[i], i, 1);
        enhance_layout->addWidget(sliders[i], i, 2);
    }

    // 设置网格的列拉伸因子
    enhance_layout->setColumnStretch(0, 1);  // 标签列
    enhance_layout->setColumnStretch(1, 1);  // 数值列
    enhance_layout->setColumnStretch(2, 4);  // 滑动条列

    // 添加到主布局
    main_layout->addWidget(toolbar_group);
    main_layout->addWidget(enhance_group);
    main_layout->addStretch(); // 底部弹簧

    // 设置整体边距
    setContentsMargins(1, 1, 1, 1);

    RestoreParams();
}

void ImagePreProcessWindow::InitConnect()
{
	connect(brightness_slider_, &QSlider::valueChanged, [this](int value) {
            MapRangeToDouble(value, brightness_lineedit_,0.0, 2.0);});
    connect(contrast_slider_, &QSlider::valueChanged, this,[this](int value) {
            MapRangeToDouble(value, contrast_lineedit_,0.0, 2.0);});
    connect(hue_slider_, &QSlider::valueChanged, this,[this](int value) {
            MapRangeToDouble(value, hue_lineedit_,-180,180);});
    connect(saturation_slider_, &QSlider::valueChanged, this,[this](int value) {
            MapRangeToDouble(value, saturation_lineedit_,0.0, 3.0);});
    connect(gamma_slider_, &QSlider::valueChanged, this,[this](int value) {
            MapRangeToDouble(value, gamma_lineedit_,0.01,10.0);});

	connect(brightness_slider_, &QSlider::valueChanged, this,&ImagePreProcessWindow::UpdataProcessParams);
    connect(contrast_slider_, &QSlider::valueChanged, this,&ImagePreProcessWindow::UpdataProcessParams);
    connect(hue_slider_, &QSlider::valueChanged, this,&ImagePreProcessWindow::UpdataProcessParams);
    connect(saturation_slider_, &QSlider::valueChanged, this,&ImagePreProcessWindow::UpdataProcessParams);
    connect(gamma_slider_, &QSlider::valueChanged, this,&ImagePreProcessWindow::UpdataProcessParams);

	connect(preview_button_, &QPushButton::clicked,
          this, &ImagePreProcessWindow::OnPreviewButtonClicked);
	connect(restore_button_, &QPushButton::clicked,
          this, &ImagePreProcessWindow::RestoreParams);
	connect(invert_checkbox_, &QCheckBox::stateChanged, 
        this, &ImagePreProcessWindow::UpdataProcessParams);
}

int ImagePreProcessWindow::DoubleToMapRange(double value, int min, int max)
{
    double normalized_value = (value - min) / static_cast<double>(max - min);
    return static_cast<int>(normalized_value * 1000.0);
}

void ImagePreProcessWindow::OnPreviewButtonClicked()
{	
    preview_window_ = new PreviewWindow(this);
	connect(preview_window_, &PreviewWindow::UpdateCurrentId,
          this, &ImagePreProcessWindow::GetImageId);
	connect(this, &ImagePreProcessWindow::UpdateSelectId,
          preview_window_, &PreviewWindow::GetCurrentId);

	emit UpdateSelectId(id_);

	preview_window_->SetProcessImages(process_image_group_);
    preview_window_->setAttribute(Qt::WA_DeleteOnClose);
    preview_window_->show(); 
}
