﻿//STD
#include <iostream>
//QT
#pragma warning(push, 3)
#include <QTextEdit>
#include <QStatusBar>
#include <QShortcut>
#include <QRadioButton>
#include <QButtonGroup>
#include <QGridLayout>
#include <QLabel>
#include <QApplication>
#include <QTimer>
#include <QDateTime>
#pragma warning(pop)
//Custom
#include "customtitleview.h"
#include "coreapplication.h"
#include "viewdefine.h"
#include "viewtool.hpp"
#include "image.hpp"
#include "componentvaluedialog.h"
//Third
#include "SARibbonMainWindow.h"
#include "SAFramelessHelper.h"
#include "SARibbonApplicationButton.h"
#include "SARibbonBar.h"
#include "SARibbonButtonGroupWidget.h"
#include "SARibbonCategory.h"
#include "SARibbonCheckBox.h"
#include "SARibbonColorToolButton.h"
#include "SARibbonComboBox.h"
#include "SARibbonCustomizeDialog.h"
#include "SARibbonCustomizeWidget.h"
#include "SARibbonGallery.h"
#include "SARibbonLineEdit.h"
#include "SARibbonMenu.h"
#include "SARibbonPannel.h"
#include "SARibbonQuickAccessBar.h"
#include "SARibbonToolButton.h"

namespace jrsaoi
{
    const static int ONE_ACTION_WIDTH = 34;
    const static int TWO_ACTION_WIDTH = 64;
    const static int THREE_ACTION_WIDTH = 94;
    const static int ONE_ACTION_HEIGHT = 28;

    CustomTitleView::CustomTitleView(SARibbonMainWindow* parent)
        :parent_view(parent)
        , menu_application_btn(nullptr)
    {
        Init();
    }

    CustomTitleView::~CustomTitleView()
    {
    }

    void CustomTitleView::SlotActionRenderInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::RenderEventParamPtr render_param = std::make_shared<jrsdata::RenderEventParam>();
        render_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        render_param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        render_param->sub_name = jrsaoi::SHORTCUT_RENDER_SUB_NAME;
        render_param->event_name = action_name;
        /**<整版子板复制*/
        render_param->multi_param_ptr = std::make_shared<jrsdata::MultiBoardEventParam>();
        jrsdata::MultiBoardEventParam::OperateParam operate_param;
        if (QAbstractButton* checked_btn = subboard_board_group_btn->checkedButton()) {
            QString btn_name = checked_btn->objectName();
            if (btn_name == "subboard_radio_btn") {
                operate_param.operate_object = jrsdata::MultiBoardEventParam::OperateParam::OperateObject::SUBBOARD;
            }
            else if (btn_name == "board_radio_btn") {
                operate_param.operate_object = jrsdata::MultiBoardEventParam::OperateParam::OperateObject::ENTIRETY_BOARD;
            }
        }
        render_param->multi_param_ptr->operate_param = operate_param;


        if (!HandleTransformActions(action_name, render_param))
        {
            HandleComponentActions(action_name, render_param);
        }

        Log_INFO(action_name, "按钮触发");
        emit SigActionTrigger(render_param);
    }

    bool CustomTitleView::HandleTransformActions(const std::string& action_name, jrsdata::RenderEventParamPtr render_param)
    {
        using TransformType = jrsdata::MultiBoardEventParam::OperateParam::TransformType;

        if (action_name == jrsaoi::SHORTCUT_ACT_CAD_ROTATEION_90)
        {
            render_param->multi_param_ptr->operate_param->transform_type = TransformType::ROTATE_90;
        }
        else if (action_name == jrsaoi::SHORTCUT_ACT_CAD_LEFT_RIGHT_MIRROR)
        {
            render_param->multi_param_ptr->operate_param->transform_type = TransformType::VERTICAL_MIRROR;
        }
        else if (action_name == jrsaoi::SHORTCUT_ACT_CAD_UP_DOWN_MIRROR)
        {
            render_param->multi_param_ptr->operate_param->transform_type = TransformType::HORIZONTAL_MIRROR;
        }
        else if (action_name == jrsaoi::SHORTCUT_ACT_CAD_CONVERSION)
        {
            render_param->multi_param_ptr->operate_param->transform_type = TransformType::CONVERT;
        }
        else if (action_name == jrsaoi::SHORTCUT_ACT_CAD_ALL_ELEMENTS_ROTATE_90)
        {
            render_param->multi_param_ptr->operate_param->transform_type = TransformType::ROTATE_90;
        }
        else
        {
            return false; // 未处理
        }
        return true; // 已处理
    }

    void CustomTitleView::HandleComponentActions(const std::string& action_name, jrsdata::RenderEventParamPtr render_param)
    {
        if (action_name == jrsaoi::SHORTCUT_ACT_ADD_COMPONENT)
        {
            //HandleAddComponentAction(render_param);
        }
        else if (action_name == jrsaoi::SHORTCUT_ACT_DELETE_COMPONENT || action_name == jrsaoi::SHORTCUT_ACT_COPY_COMPONENT)
        {
            render_param->event_name = action_name;
        }
    }

    //void CustomTitleView::HandleAddComponentAction(jrsdata::RenderEventParamPtr render_param)
    //{
    //    auto dialog = new ComponentDialog(this);
    //    if (dialog->exec() == QDialog::Accepted)
    //    {
    //        render_param->event_name = jrsaoi::COMPONENT_EDIT_EVENT_NAME;
    //        jrsdata::CadEventParam param;
    //        param.component_name = dialog->GetComponentName().toLocal8Bit().toStdString();
    //        param.part_name = dialog->GetComponentPart().toLocal8Bit().toStdString();
    //        param.step = jrsdata::CadEventParam::Step::MANUAL_ADD_CAD;
    //        render_param->cad_param = param;
    //    }
    //    else
    //    {
    //        // 如果用户取消对话框，不触发任何事件
    //        render_param->event_name.clear();
    //    }
    //}

    void CustomTitleView::SlotActionComponentInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::RenderEventParamPtr render_param = std::make_shared<jrsdata::RenderEventParam>();
        render_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        render_param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        render_param->sub_name = jrsaoi::SHORTCUT_RENDER_SUB_NAME;
        render_param->event_name = action_name;

    }

    void CustomTitleView::SlotActionProjectInvokeFun()
    {
        auto param = std::make_shared<jrsdata::ProjectEventParam>();
        //param->project_param->file_param.file_type = jrsdata::FileType::BIN;
        //param->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        param->event_name = jrsaoi::PROJECT_SAVE_EVENT_NAME;
        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_RENDER_SUB_NAME;
        Log_INFO(sender()->objectName().toStdString(), "按钮触发");
        emit SigActionTrigger(param);
    }

    void CustomTitleView::SlotActionOperateInvokeFun()
    {
        //TODO:
        auto action_name = sender()->objectName().toStdString();
        jrsdata::OperateViewParamPtr operate_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        operate_view_ptr->module_name = VIEW_MODULE_NAME;
        operate_view_ptr->sub_name = SHORTCUT_OPERATER_SUB_NAME;
        operate_view_ptr->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        operate_view_ptr->event_name = action_name;
        emit SigActionTrigger(operate_view_ptr);
    }

    void CustomTitleView::SlotActionOnlineDebugInvokeFun()
    {
    }

    void CustomTitleView::SlotActionDeviceInvokeFun()
    {
        //TODO
    }

    void CustomTitleView::SlotActionPermissionInvokeFun()
    {
        //TODO
    }

    void CustomTitleView::SlotActionMotionInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::OperateViewParamPtr motiondebug_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        motiondebug_view_ptr->module_name = VIEW_MODULE_NAME;
        motiondebug_view_ptr->sub_name = jrsaoi::SHORTCUT_OPERATER_SUB_NAME;
        motiondebug_view_ptr->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        motiondebug_view_ptr->event_name = action_name;
        emit SigActionTrigger(motiondebug_view_ptr);
    }

    void CustomTitleView::SlotActionAxisMoveFun()
    {
        QString action_name = sender()->objectName();
        if (action_name.isEmpty())
        {
            Log_ERROR("Action name is empty!");
            return;
        }

        jrsdata::TrackControlParam track_param;
        if (!ParseActionName(action_name, track_param))
        {
            Log_ERROR("Failed to parse action name: {}", action_name.toStdString());
            return;
        }

        auto track_control_view_ptr = CreateTrackControlViewPtr(track_param, action_name);
        if (!track_control_view_ptr)
        {
            Log_ERROR("Failed to create track control view pointer.");
            return;
        }

        emit SigActionTrigger(track_control_view_ptr);
    }

    bool CustomTitleView::ParseActionName(const QString& action_name, jrsdata::TrackControlParam& track_param)
    {
        const QString input_name = "act_input_board_";
        const QString output_name = "act_output_board_";
        const QString reset_name = "act_reset_track_";
        const QString infeed_name = "act_product_return_infeed_sensor_";

        QString index_id;
        if (action_name.startsWith(input_name))  // 进板
        {
            Log_INFO(action_name.toStdString(), "进板");
            index_id = action_name.mid(input_name.length());
            track_param.track_type = jrsdata::TrackControlType::Load;
        }
        else if (action_name.startsWith(output_name))  // 出板
        {
            Log_INFO(action_name.toStdString(), "出板");
            index_id = action_name.mid(output_name.length());
            track_param.track_type = jrsdata::TrackControlType::UnLoad;
        }
        else if (action_name.startsWith(reset_name))  // 复位轨道
        {
            Log_INFO(action_name.toStdString(), "复位轨道");
            index_id = action_name.mid(reset_name.length());
            track_param.track_type = jrsdata::TrackControlType::InitTrack;
        }
        else if (action_name.startsWith(infeed_name))  // 产品回到进板感应器
        {
            Log_INFO(action_name.toStdString(), "产品回到进板感应器");
            index_id = action_name.mid(infeed_name.length());
            track_param.track_type = jrsdata::TrackControlType::RebackInputSignal;
        }
        else
        {
            return false;
        }

        bool ok;
        track_param.track_index = index_id.toInt(&ok);
        return ok;
    }

    std::shared_ptr<jrsdata::OperateViewParam> CustomTitleView::CreateTrackControlViewPtr(const jrsdata::TrackControlParam& track_param, const QString& action_name)
    {
        (void)track_param;
        auto track_control_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        track_control_view_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        track_control_view_ptr->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        track_control_view_ptr->sub_name = jrsaoi::SHORTCUT_LOGIC_SUB_NAME;
        track_control_view_ptr->invoke_module_name = jrsaoi::DEVICE_MODULE_NAME;
        //track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "LoadAndUnLoad";
        track_control_view_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
        track_control_view_ptr->event_name = action_name.toStdString();
        return track_control_view_ptr;
    }

    void CustomTitleView::SlotComboboxImageTypeChange(const QString& current_text)
    {
        auto param = std::make_shared<jrsdata::RenderEventParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        param->sub_name = "all";
        param->event_name = jrsaoi::CHANGE_RENDER_SHOW_IMG_TYPE;
        param->select_param.show_img_type = current_text.toStdString();
        param->select_param.light_type = (jrsdata::LightImageType)image_color_select->currentData().toInt();
        emit SigActionTrigger(param);
    }

    void CustomTitleView::SlotActionLogicInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::OperateViewParamPtr param = std::make_shared<jrsdata::OperateViewParam>();
        param->module_name = VIEW_MODULE_NAME;
        param->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        param->sub_name = SHORTCUT_LOGIC_SUB_NAME;
        param->invoke_module_name = LOGIC_MODULE_NAME;
        param->event_name = action_name;
        emit SigActionTrigger(param);
    }

    void CustomTitleView::SlotActionSettingViewParamFun()
    {
        auto action_name = sender()->objectName().toStdString();
        auto setting_ptr = std::make_shared<jrsdata::SettingViewParam>();
        setting_ptr->module_name = VIEW_MODULE_NAME;
        setting_ptr->sub_name = SETTING_SUB_NAME;
        setting_ptr->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        setting_ptr->event_name = action_name;
        emit SigActionTrigger(setting_ptr);
    }

    void CustomTitleView::SlotGraphicUpdateFun(const jrsdata::ViewParamBasePtr& item_info)
    {
        auto item_info_render = std::static_pointer_cast<jrsdata::RenderViewParam>(item_info);
        if (!item_info_render)
        {
            return;
        }
        for (auto& value : device_pos_info_spinbox)
        {
            auto it = setters.find(value.first);
            if (it != setters.end())
            {
                it->second(value.second, item_info_render->selected_item_info);
            }
        }
    }

    void CustomTitleView::Init()
    {
        InitMember();
        InitView();
        QSize iconSize(32, 32);
    }

    void CustomTitleView::InitView()
    {
        setIconSize(QSize(32, 32));
        SARibbonBar* ribbon = parent_view->ribbonBar();
        ribbon->setTabBarHeight(50);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyle);
        ribbon->setContentsMargins(0, 0, 0, 0);
        CreateRibbonApplicationButton();
        CreateQuickAccessBar();
        CreateRightButtonGroup();
        /** 主面板 */
        SARibbonCategory* main_panel = ribbon->addCategoryPage(tr("主面板"));
        main_panel->setContentsMargins(0, 0, 0, 0);
        main_panel->setObjectName(("main_panel"));
        CreateMainPanel(main_panel);
        CreateRebackPanel(main_panel);
        CreateImagePanel(main_panel);
        //CreateCADPanel(main_panel);
        CreateDeviceControlPanel(main_panel);
        //CreateDetectPanel(main_panel);
        //CreateAIPanel(main_panel);  删除AI识别
        CreateTestPanel(main_panel);
        CreateControlPanel(main_panel);
        Create3DPanel(main_panel);
        CreateDetectTestShowPanel(main_panel);
        //CreateDeviceShowPanel(main_panel);
        //CreateMotionControlPanel(main_panel, 0);  删除控制面板的输送控制
        //CreateMotionControlPanel(main_panel, 1);
        //CreateDetectResultSignalPanel(main_panel);
        CreateModelPanel(main_panel);
        //CreateMarkShowPanel(main_panel);
        CreateLayerShowPanel(main_panel);
        CreateTimeShowPanel(main_panel);
    }

    void CustomTitleView::InitMember()
    {
        invoke_fun_render = std::bind(&CustomTitleView::SlotActionRenderInvokeFun, this);
        invoke_fun_project = std::bind(&CustomTitleView::SlotActionProjectInvokeFun, this);
        invoke_fun_operate = std::bind(&CustomTitleView::SlotActionOperateInvokeFun, this);
        invoke_fun_onlinedebug = std::bind(&CustomTitleView::SlotActionOnlineDebugInvokeFun, this);
        invoke_fun_motion = std::bind(&CustomTitleView::SlotActionMotionInvokeFun, this);
        invoke_fun_permission = std::bind(&CustomTitleView::SlotActionPermissionInvokeFun, this);
        invoke_fun_axis_move = std::bind(&CustomTitleView::SlotActionAxisMoveFun, this);
        invoke_fun_logic = std::bind(&CustomTitleView::SlotActionLogicInvokeFun, this);
        invoke_fun_setting_param = std::bind(&CustomTitleView::SlotActionSettingViewParamFun, this);
    }

    void CustomTitleView::CreateRibbonApplicationButton()
    {
        SARibbonBar* ribbon = parent_view->ribbonBar();
        if (!ribbon)
        {
            return;
        }
        ribbon->setContentsMargins(0, 0, 0, 0);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyle);
        QAbstractButton* btn = ribbon->applicationButton();
        if (!btn)
        {
            btn = new SARibbonApplicationButton(this);
            ribbon->setApplicationButton(btn);
        }
        ribbon->applicationButton()->setText(("  &AOI  "));
        if (!menu_application_btn)
        {
            menu_application_btn = new SARibbonMenu(this);
            auto menu_open = CreateMenu("打开", ":/image/open.png");
            menu_application_btn->addMenu(menu_open);
        }
        SARibbonApplicationButton* app_btn = qobject_cast<SARibbonApplicationButton*>(btn);
        if (!app_btn)
        {
            return;
        }
        app_btn->setMenu(menu_application_btn);
    }

    QAction* CustomTitleView::CreateAction(const QString& text, const QString& iconurl, const QString& obj_name, InvokeFun invoke_fun_)
    {
        QAction* act = new QAction(this);
        act->setText(text);
        act->setIcon(QIcon(iconurl));
        act->setObjectName(obj_name);
        connect(act, &QAction::triggered, this, invoke_fun_);
        return act;
    }

    QAction* CustomTitleView::CreateAction(const QString& text, const QString& iconurl, const QString& obj_name)
    {
        QAction* act = new QAction(this);
        act->setText(text);
        act->setIcon(QIcon(iconurl));
        act->setObjectName(obj_name);
        return act;
    }

    QMenu* CustomTitleView::CreateMenu(const QString& text, const QString& iconurl)
    {
        QMenu* menu = new QMenu("    " + text + "    ", this);
        menu->setIcon(QIcon(iconurl));
        return menu;
    }

    void CustomTitleView::CreateQuickAccessBar()
    {
        SARibbonQuickAccessBar* quickAccessBar = parent_view->ribbonBar()->quickAccessBar();
        quickAccessBar->addAction(CreateAction("设置", ":/image/setting.png", SHOW_SETTING_VIEW_EVENT_NAME, invoke_fun_setting_param));
        quickAccessBar->addSeparator();
    }

    void CustomTitleView::CreateRightButtonGroup()
    {
        
        auto action_help_fun = [&]()
            {
                QString version_info = GetExeVersion();
                std::string str_version = std::string("\n ===============")
                    + "\n AOI version: " + version_info.toStdString()
                    + "\n Author: JRS"
                    + "\n Email: <EMAIL>"
                    + "\n ===============";
                JRSMessageBox_INFO("infomation", str_version.c_str(), jrscore::MessageButton::Ok);
            };
        SARibbonBar* ribbon = parent_view->ribbonBar();
        if (!ribbon)
        {
            return;
        }
        ribbon->setContentsMargins(0, 0, 0, 0);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyle);
        SARibbonButtonGroupWidget* rightBar = ribbon->rightButtonGroup();
        QAction* action_help = CreateAction(tr("help"), ":/image/help.png", "act_help", action_help_fun);
        rightBar->addAction(action_help);
    }

    void CustomTitleView::CreateMainPanel(SARibbonCategory* page)
    {
        SARibbonPannel* main_pannel_style = page->addPannel(("主面板"));
        main_pannel_style->addAction(CreateAction(tr("新建"), ":/image/newproj.png", jrsaoi::PROJECT_CREATE_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(CreateAction(tr("打开"), ":/image/openproj.png", jrsaoi::PROJECT_READ_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(CreateAction(tr("添加多工程"), ":/image/appendproject.png", jrsaoi::APPEND_PROJECT_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(CreateAction(tr("保存"), ":/image/save.png", jrsaoi::PROJECT_SAVE_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(CreateAction(tr("另存为"), ":/image/saveas.png", jrsaoi::PROJECT_SAVE_AS_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(CreateAction(tr("图片"), ":/image/loadpicture.png", jrsaoi::PROJECT_IMAGE_SAVE_AND_LOAD_EVENT_NAME, invoke_fun_operate), QToolButton::InstantPopup, SARibbonPannelItem::Large);
    }

    void CustomTitleView::CreateRebackPanel(SARibbonCategory* page)
    {
        SARibbonPannel* undo_pannel_style = page->addPannel(("撤销"));
        undo_pannel_style->addAction(CreateAction(tr("撤销"), ":/image/undoaction.png", jrsaoi::SHORTCUT_ACT_REVOKE, invoke_fun_render), QToolButton::InstantPopup, SARibbonPannelItem::Large);
        undo_pannel_style->addAction(CreateAction(tr("恢复"), ":/image/antiundoaction.png", jrsaoi::SHORTCUT_ACT_RECOVER, invoke_fun_render), QToolButton::InstantPopup, SARibbonPannelItem::Large);
    }

    SARibbonButtonGroupWidget* CustomTitleView::CreateButtonGroup(QWidget* parent, const QList<QAction*>& actions, int item_height, int min_width)
    {
        SARibbonButtonGroupWidget* btn_group = new SARibbonButtonGroupWidget(parent);
        btn_group->setItemMargin(0);
        btn_group->setMinimumHeight(30);
        btn_group->setMinimumWidth(min_width);
        btn_group->setItemHeight(item_height);

        for (auto* action : actions)
        {
            btn_group->addAction(action);
        }

        return btn_group;
    }

    void CustomTitleView::CreateImagePanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("图片"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("100%显示"), ":/image/img_1_1.png", jrsaoi::SHORTCUT_ACT_IMAGE_SIZE_100, invoke_fun_render),
            CreateAction(tr("全屏显示"), ":/image/img_max.png", jrsaoi::SHORTCUT_ACT_IMAGE_PANORAMA, invoke_fun_render),
            CreateAction(tr("图形居中"), ":/image/center_view.png", jrsaoi::SHORTCUT_ACT_IMAGE_CENTER, invoke_fun_render)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("放大"), ":/image/img_up_size.png", jrsaoi::SHORTCUT_ACT_IMAGE_ZOOM_IN, invoke_fun_render),
            CreateAction(tr("缩小"), ":/image/img_down_size.png", jrsaoi::SHORTCUT_ACT_IMAGE_ZOOM_OUT, invoke_fun_render)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateDetectPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("检测框"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("左右镜像复制"), ":/image/cad_left_right_mirror.png", SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR, invoke_fun_operate),
            CreateAction(tr("上下镜像复制"), ":/image/cad_top_bottom_mirror.png", SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR, invoke_fun_operate),
            CreateAction(tr("90度旋转复制"), ":/image/rotate90.png", SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE, invoke_fun_operate),
            CreateAction(tr("180度旋转复制"), ":/image/rotate-180.png", SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE, invoke_fun_operate),
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("检测框复制"), ":/image/copy.png", SHORTCUT_ACT_DETECT_COPY, invoke_fun_operate),
            CreateAction(tr("元件检测框复制"), ":/image/coponent_detect_copy.png", SHORTCUT_ACT_COMPONENT_DETECT_COPY, invoke_fun_operate),
            CreateAction(tr("粘贴"), ":/image/copy_action.png", SHORTCUT_ACT_DETECT_ACTION, invoke_fun_operate),
            CreateAction(tr("所有检测框90度旋转"), ":/image/rotate90.png", SHORTCUT_ACT_ROTATE_90_ALL, invoke_fun_operate)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH * 2), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH * 2), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateAIPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("AI识别"));
        QList<QAction*> group_actions_top = {
            CreateAction(tr("子板元件识别"), ":/image/subboard_ai.png", SHORTCUT_ACT_SUBBOARD_AI, invoke_fun_operate),
            CreateAction(tr("整板元件识别"), ":/image/board_ai.png", SHORTCUT_ACT_BOARD_AI, invoke_fun_operate)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("元件识别"), ":/image/body_ai.png", SHORTCUT_ACT_BODY_AI, invoke_fun_operate)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateCADPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("CAD"));

        // 创建单选按钮的 Lambda
        auto createRadioButton = [ribbon_pannel](const QString& text, const QString& object_name, bool checked) -> QRadioButton* {
            QRadioButton* btn = new QRadioButton(ribbon_pannel);
            btn->setText(text);
            btn->setObjectName(object_name);
            btn->setWindowTitle(text);
            btn->setChecked(checked);
            btn->setEnabled(true);
            return btn;
            };

        // 创建单选按钮
        QRadioButton* subboard_btn = createRadioButton(tr("子板"), "subboard_radio_btn", true);
        QRadioButton* board_btn = createRadioButton(tr("整板"), "board_radio_btn", false);

        // 创建按钮组并绑定信号
        subboard_board_group_btn = new QButtonGroup(ribbon_pannel);
        subboard_board_group_btn->addButton(subboard_btn);
        subboard_board_group_btn->addButton(board_btn);

        // 顶部功能按钮
        QList<QAction*> group_actions_top = {
            CreateAction(tr("左右镜像"), ":/image/cad_left_right_mirror.png", jrsaoi::SHORTCUT_ACT_CAD_LEFT_RIGHT_MIRROR, invoke_fun_render),
            CreateAction(tr("上下镜像"), ":/image/cad_top_bottom_mirror.png", jrsaoi::SHORTCUT_ACT_CAD_UP_DOWN_MIRROR, invoke_fun_render),
            CreateAction(tr("单个子板旋转90度"), ":/image/cad_rotate_90.png", jrsaoi::SHORTCUT_ACT_CAD_ROTATEION_90, invoke_fun_render)
        };

        // 底部功能按钮
        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("逆时针顺时针转换"), ":/image/cad_clock_wise.png", jrsaoi::SHORTCUT_ACT_CAD_CONVERSION, invoke_fun_render),
            CreateAction(tr("所有元件旋转90度"), ":/image/cad_angle+90.png", jrsaoi::SHORTCUT_ACT_CAD_ALL_ELEMENTS_ROTATE_90, invoke_fun_render),
            CreateAction(tr("整体移动"), ":/image/all_move.png", jrsaoi::SHORTCUT_ACT_CAD_MOVE, invoke_fun_render)
        };

        // 创建功能控件组并添加单选按钮
        auto group_widget_1 = CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH);
        group_widget_1->layout()->addWidget(subboard_btn);

        auto group_widget_2 = CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH);
        group_widget_2->layout()->addWidget(board_btn);

        // 添加到面板
        ribbon_pannel->addWidget(group_widget_1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(group_widget_2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDeviceControlPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("检测项"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("新增定位点"), ":/image/add_pos.png", jrsaoi::SHORTCUT_ACT_CREATE_MARK_EVENT_NAME, invoke_fun_operate),
            CreateAction(tr("新增条码"), ":/image/add_barcode.png", jrsaoi::SHORTCUT_ACT_CREATE_BARCODE_EVENT_NAME, invoke_fun_operate),
            CreateAction(tr("新增坏板标记"), ":/image/add_bad_signal.png", SHORTCUT_ACT_REQUEST_RENDER_CREATE_SUBBADMARK_EVENT_NAME, invoke_fun_render),

        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("添加元件"), ":/image/adddevice.png", jrsaoi::SHORTCUT_ACT_ADD_COMPONENT, invoke_fun_operate),
            CreateAction(tr("删除元件"), ":/image/deletedevice.png", jrsaoi::SHORTCUT_ACT_DELETE_COMPONENT, invoke_fun_render)
        };

       // ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateTestPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("测试"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("检测框测试"), ":/image/run_1.png", jrsaoi::ACT_TEST_DETECT_WINDOW, invoke_fun_operate),
            CreateAction(tr("元件测试"), ":/image/run_2.png", jrsaoi::ACT_TEST_COMPONET, invoke_fun_operate),
            CreateAction(tr("检测并保存算法参数"), ":/image/run_and_save.png", jrsaoi::SHORTCUT_ACT_RUN_COMPONENT_SAVE_ALGO_INFO_EVENT, invoke_fun_operate),
            CreateAction(tr("料号测试"), ":/image/run_3.png", jrsaoi::ACT_TEST_PART_NUM, invoke_fun_operate),
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("元件定位后拉正"), ":/image/shot_1.png",jrsaoi::SHORTCUT_ACT_COMPONENT_LOCATED_RUN_EVENT, invoke_fun_operate),
            CreateAction(tr("料号定位后拉正"), ":/image/shot_2.png", jrsaoi::SHORTCUT_ACT_PARTNUMBER_LOCATED_RUN_EVENT, invoke_fun_operate),
            CreateAction(tr("定位点对齐"), ":/image/pos_align.png", jrsaoi::ACT_TEST_POS_ALIGN, invoke_fun_operate)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH), SARibbonPannelItem::Medium);
        //ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, THREE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateControlPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("控制"));

        QAction* take_photo = CreateAction(tr("实时拍照"), ":/image/realtimeing.png", jrsaoi::START_CONTINUE_GRAB_EVENT_NAME);
        connect(take_photo, &QAction::triggered, [=]()
            {
                jrsdata::OperateViewParamPtr param = std::make_shared<jrsdata::OperateViewParam>();
                param->module_name = VIEW_MODULE_NAME;
                param->sub_name = SHORTCUT_LOGIC_SUB_NAME;
                param->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
                param->invoke_module_name = LOGIC_MODULE_NAME;

                if (take_photo->objectName() == jrsaoi::START_CONTINUE_GRAB_EVENT_NAME)
                {
                    take_photo->setObjectName(jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME);
                    take_photo->setText(tr("停止实时"));
                    take_photo->setIcon(QIcon(":/image/realtime.png"));
                    param->event_name = jrsaoi::START_CONTINUE_GRAB_EVENT_NAME;
                    Log_INFO(__FUNCTION__, "实时拍照");
                }
                else
                {
                    take_photo->setObjectName(jrsaoi::START_CONTINUE_GRAB_EVENT_NAME);
                    take_photo->setText(tr("实时拍照"));
                    take_photo->setIcon(QIcon(":/image/realtimeing.png"));
                    param->event_name = jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME;
                    Log_INFO(__FUNCTION__, "停止实时");
                }
                emit SigActionTrigger(param);
            });

        QList<QAction*> group_actions_top = { take_photo,
            CreateAction(tr("当前位置拍照并保存图片"), ":/image/photograph.png", "act_take_picture_and_save", invoke_fun_logic) };

       /* QList<QAction*> group_actions_bottom = {
            CreateAction(tr("轴控制"), ":/image/motion.png", jrsaoi::SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME, invoke_fun_motion),
            CreateAction(tr("显示索引图"), ":/image/index.png", "act_show_index_picture", invoke_fun_render)
        };*/

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        //ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::Create3DPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("3D"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("选中元件3D图"), ":/image/3D_1.png", "act_selected_device_3d", invoke_fun_render),
            CreateAction(tr("框选区域3D图"), ":/image/3D_2.png", "act_select_area_3d", invoke_fun_render)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("整板大图3D图"), ":/image/3D_3.png", "act_whole_board_3d", invoke_fun_render)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateDeviceShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("元件显示"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("本体框"), ":/image/device_1.png", "act_body_device_show", invoke_fun_render),
            CreateAction(tr("显示焊盘"), ":/image/device_2.png", "act_pad_show", invoke_fun_render)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("显示检测框"), ":/image/device_3.png", "act_detect_show", invoke_fun_render)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateDetectTestShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("检测框测试"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("显示轮廓"), ":/image/detect_1.png", "act_display_rectangle", invoke_fun_render),
            CreateAction(tr("显示填充"), ":/image/detect_2.png", "act_display_padding", invoke_fun_render)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("显示数值"), ":/image/detect_3.png", "act_display_number_value", invoke_fun_render)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateDetectResultSignalPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("结果标识"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("轮廓框选元件"), ":/image/detect_rect.png", "act_rectangle_select_device", invoke_fun_render),
            CreateAction(tr("箭头指示元件"), ":/image/detect_right_short.png", "act_arrow_indicates_device", invoke_fun_render)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(tr("标签指示元件"), ":/image/detect_right.png", "act_label_indicates_device", invoke_fun_render)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateMotionControlPanel(SARibbonCategory* page, int index)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("输送控制"));

        QString input_name = QString("进板 %1").arg(index + 1);
        QString output_name = QString("出板 %1").arg(index + 1);
        QString reset_name = QString("复位轨道 %1").arg(index + 1);
        QString feedback_name = QString("产品回到进板感应器 %1").arg(index + 1);

        QList<QAction*> group_actions_top = {
            CreateAction(input_name, ":/image/inputboard.png", QString("act_input_board_%1").arg(index), invoke_fun_axis_move),
            CreateAction(output_name, ":/image/return.png", QString("act_output_board_%1").arg(index), invoke_fun_axis_move)
        };

        QList<QAction*> group_actions_bottom = {
            CreateAction(reset_name, ":/image/refresh_3.png", QString("act_reset_track_%1").arg(index), invoke_fun_axis_move),
            CreateAction(feedback_name, ":/image/return.png", QString("act_product_return_infeed_sensor_%1").arg(index), invoke_fun_axis_move)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_bottom, ONE_ACTION_HEIGHT, TWO_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateModelPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("模板编辑"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("模板编辑"), ":/image/model.png", jrsaoi::SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME, invoke_fun_operate)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateMarkShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(tr("Mark结果"));

        QList<QAction*> group_actions_top = {
            CreateAction(tr("Mark结果"), ":/image/showmarkresult.png", jrsaoi::SHORTCUT_ACT_SHOW_MARK_RESULT_EVENT_NAME, invoke_fun_operate)
        };

        ribbon_pannel->addWidget(CreateButtonGroup(ribbon_pannel, group_actions_top, ONE_ACTION_HEIGHT, ONE_ACTION_WIDTH), SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateLayerShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("图层显示"));
        //!图像颜色选择：目前有原图，真彩图，低角度图，高度图共4种
        image_color_select = new SARibbonComboBox(this);
        image_color_select->setObjectName("image_color_select");
        image_color_select->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
        image_color_select->addItem("     原图   ", QVariant::fromValue((int)jrsdata::LightImageType::RGB));
        image_color_select->addItem("    真彩图      ", QVariant::fromValue((int)jrsdata::LightImageType::WHITE));
        image_color_select->addItem("   低角度光图  ", QVariant::fromValue((int)jrsdata::LightImageType::LOWWHITE));
        image_color_select->addItem("    高度图  ", QVariant::fromValue((int)jrsdata::LightImageType::HEIGHT));
        image_color_select->setFixedHeight(30);
        connect(image_color_select, QOverload<const QString&>::of(&SARibbonComboBox::textActivated), this, &CustomTitleView::SlotComboboxImageTypeChange);
        ribbon_pannel->addSmallWidget(image_color_select);
    }

    void CustomTitleView::CreateTimeShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* time_pannel = page->addPannel("");
        QLabel* label_time = new QLabel(this);
        label_time->setObjectName("label_time");
        label_time->setAlignment(Qt::AlignCenter);
        label_time->setStyleSheet(R"(
        QLabel#label_time {
            font-size: 25px;            /* 设置字体大小 */
            font-weight: bold;         /* 设置字体加粗 */
            color: darkblue;           /* 设置字体颜色为深蓝色 */
        }
    )");
        QTimer* timer = new QTimer(this);
        connect(timer, &QTimer::timeout, this, [label_time]()
            {
                QDateTime current_time = QDateTime::currentDateTime();
                QString time_str = current_time.toString("yyyy-MM-dd HH:mm:ss");
                label_time->setText(time_str);
            });
        timer->start(1000);
        time_pannel->addLargeWidget(label_time);
    }
}