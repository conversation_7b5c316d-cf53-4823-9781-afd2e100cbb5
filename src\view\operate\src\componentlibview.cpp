﻿#include "componentlibview.h"
#include "ui_componentlibview.h"
#include "cvtools.h"


ComponentLibView::ComponentLibView(const std::string& name, QWidget* parent)
    :ViewBase(name, parent)
    , ui(new Ui::ComponentLibView), img_view(nullptr)
{
    ui->setupUi(this);
    component_lib_ptr = std::make_shared<jrsaoi::ComponentLibrary>(name);

    component_control_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
    component_control_view_ptr->module_name = OPERATE_MODULE_NAME;
    component_control_view_ptr->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
    component_control_view_ptr->sub_name = OPERATE_LOGIC_SUB_NAME;

    Init();
}

ComponentLibView::~ComponentLibView()
{
    delete ui;
}

int ComponentLibView::Init()
{
    InitView();
    InitConnection();
    return 0;
}

int ComponentLibView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
{
    auto param = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
    if (!param)
    {
        return -1;
    }
    if (param->event_name == OPERATE_COMPONENT_GET_CURSELLECT_COMPONENT_EVENT_NAME || 
        param->event_name == OPERATE_COMPONENT_GET_ALL_COMPONENT_EVENT_NAME)
    {
        component_lib_ptr->AddComponent(param->entitys.components_param);
    }
    else if (param->event_name == OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME)
    {
        component_lib_ptr->AddComponent(param->entitys.components_param, AddType::Overwrite, false);
    }
    else if (param->event_name == OPERATE_COMPONENT_LOAD_CURSELLECT_COMPONENT_EVENT_NAME && param->entitys.components_param.size() > 0)
    {
        jrsdata::ComponentEntity component;
        component_lib_ptr->QueryComponent(component, param->entitys.components_param.at(0).part_name);
        if (component.part_name != "")
        {
            // 加载元件库
            param->event_name = OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME;
            param->entitys.components_param.clear();
            param->entitys.components_param.push_back(component);
            emit SigComponent(param);
        }
        else
        {
            // 弹框提示
            JRSMessageBox_INFO("提示", "元件库元件名称为空", jrscore::MessageButton::Ok);
        }
    }
    return 0;
}

int ComponentLibView::Save(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    return 0;
}

int ComponentLibView::SetComponentLibName(std::string name)
{
    std::string lib_name = name != "" ? name : ui->componentFolder->currentText().toStdString();
    ui->componentFolder->setCurrentText(QString::fromStdString(lib_name));
    return 0;
}

std::string ComponentLibView::GetComponentLibName()
{
    return component_lib_ptr->GetComponentName();
}

void ComponentLibView::SetComponentLibFolderPath(const std::string path)
{
    std::string new_path = path;
    if (new_path.back() != '/')
    {
        new_path = new_path + "/";
    }
    if (component_lib_ptr->GetComponentLibFolderPath() == new_path)
    {
        return;
    }
    component_lib_ptr->SetComponentLibFolderPath(path);
    // 重新初始化界面
    InitView();
}

void ComponentLibView::InitView()
{
    // 获取获取元件库里面所有的元件库分类
    ui->componentFolder->clear();
    std::vector<std::string> lists = component_lib_ptr->GetComponentFolders();
    for (int i = 0; i < lists.size(); i++)
    {
        ui->componentFolder->addItem(QString::fromStdString(lists[i]));
    }
    if (lists.size() == 0)
    {
        ui->componentFolder->addItem("Default");
    }
    ui->componentFolder->setCurrentIndex(-1);

    // tableWidget初始化
    QStringList headers = { "序号", "元件名称", "PAD数" , "宽度", "高度"};
    ui->tableWidget->setColumnCount(headers.size());
    std::vector<int> colunm_width = { 40,120,50,50,50 };
    for (int i = 0; i < colunm_width.size(); ++i) 
    {
        ui->tableWidget->setColumnWidth(i, colunm_width[i]);
    }
    ui->tableWidget->setHorizontalHeaderLabels(headers);
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);

    // 初始化图片显示
    if (img_view == nullptr)
    {
        img_view = new ViewerGraphicsViewImpl(this);
        QVBoxLayout* layout = new QVBoxLayout(ui->frame_show);
        layout->addWidget(img_view);
        ui->frame_show->setLayout(layout);
        ui->frame_show->show();
    }
}

void ComponentLibView::InitConnection()
{
    // 查询元件
    connect(ui->query_components_btn, &QPushButton::clicked, this, [=]() {
        QueryComponents();
        });

    // 关联元件库
    connect(ui->apply_component_btn, &QPushButton::clicked, this, [=]() {
        std::string part_name = GetCurSelecetPartName();
        if (part_name != "")
        {
            jrsdata::ComponentEntity component;
            component_lib_ptr->QueryComponent(component, part_name);
            component_control_view_ptr->entitys.components_param.clear();
            component_control_view_ptr->entitys.components_param.push_back(component);
            component_control_view_ptr->event_name = OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME;
            emit SigComponent(component_control_view_ptr);
        }
        });

    // 加载检测框
    connect(ui->load_component_detect_btn, &QPushButton::clicked, this, [=]() {
        std::string part_name = GetCurSelecetPartName();
        if (part_name != "")
        {
            jrsdata::ComponentEntity component;
            component_lib_ptr->QueryComponent(component, part_name);
            component_control_view_ptr->entitys.components_param.clear();
            component_control_view_ptr->entitys.components_param.push_back(component);
            component_control_view_ptr->event_name = OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME;
            emit SigComponent(component_control_view_ptr);
        }
        });

    // 加载所有元件
    connect(ui->load_components_btn, &QPushButton::clicked, this, [this]() {
        std::vector<jrsdata::ComponentEntity> entitys;
        component_lib_ptr->QueryComponents(entitys);
        if (entitys.size() > 0)
        {
            component_control_view_ptr->entitys.components_param = entitys;
            component_control_view_ptr->event_name = OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME;
            emit SigComponent(component_control_view_ptr);
        }
        });

    // 保存所有元件(发信号出去,外部接收到信号获取showlist里面料号列表所有信息,传回并通过UpdateView更新)
    connect(ui->save_components_btn, &QPushButton::clicked, this, [=]() {
        component_control_view_ptr->event_name = OPERATE_COMPONENT_GET_ALL_COMPONENT_EVENT_NAME;
        emit SigComponent(component_control_view_ptr);
        });

    // 发送信号到
    connect(component_lib_ptr.get(), &jrsaoi::ComponentLibrary::SigComponent, this, [this](const jrsdata::ViewParamBasePtr& param) {
        emit SigComponent(param);
        });

    // 元件库分类选择发生变化
    connect(ui->componentFolder, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [this](int index) {
        if (index < 0)
        {
            return;
        }
        // 切换元件库
        component_lib_ptr->ClearComponents(false);
        component_lib_ptr->SetComponentName(ui->componentFolder->currentText().toStdString());
        // 保存元件库地址
        component_control_view_ptr->event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;
        emit SigComponent(component_control_view_ptr);
        });

    // 重载按钮
    connect(ui->reload_btn, &QPushButton::clicked, this, [=]() {
        component_lib_ptr->ClearComponents(false);
        component_lib_ptr->SetComponentName(ui->componentFolder->currentText().toStdString());
        });
}
void ComponentLibView::QueryComponents()
{
    jrsaoi::MultiConditions multi_contition;
    multi_contition.width = ui->component_width->text().toInt(0);
    multi_contition.height = ui->component_height->text().toInt(0);
    multi_contition.pad_count = ui->component_pad_count->text().toInt(0);
    multi_contition.part_name = ui->component_part_num->text().toStdString();
    std::vector<jrsdata::ComponentEntity> components;
    component_lib_ptr->QueryComponents(components, multi_contition);

    // 显示结果
    ShowQueryResult(components);
}

void ComponentLibView::ShowQueryResult(const std::vector<jrsdata::ComponentEntity>& components)
{
    ui->tableWidget->setRowCount(0);
    for (int i = 0; i < components.size(); i++)
    {
        QStringList list;
        list << QString::number(i + 1);
        list << QString::fromStdString(components[i].part_name);
        list << QString::number(component_lib_ptr->GetPADCount(components[i])); 
        list << QString::number(components[i].width);
        list << QString::number(components[i].height);
        AddRowToTable(ui->tableWidget, list);
    }
    QObject::connect(ui->tableWidget, &QTableWidget::itemSelectionChanged, this, [=]() 
        {
        DrawComponent(GetCurSelecetPartName());
        });
}

void ComponentLibView::AddRowToTable(QTableWidget* tableWidget, const QStringList rowData)
{
    int rowCount = tableWidget->rowCount();
    tableWidget->insertRow(rowCount);  // 在表格末尾添加一行

    for (int column = 0; column < rowData.size(); ++column) 
    {
        tableWidget->setItem(rowCount, column, new QTableWidgetItem(rowData[column]));
    }
}

std::string ComponentLibView::GetCurSelecetPartName()
{
    int currentRow = ui->tableWidget->currentRow();
    if (currentRow != -1)
    {
        QTableWidgetItem* item = ui->tableWidget->item(currentRow, 1);
        if (item)
        {
            QString itemText = item->text();
            return itemText.toStdString();
        }
    }
    return std::string();
}

void ComponentLibView::DrawComponent(const std::string part_name)
{
    auto ProcessMirrorPad = [&](jrsdata::DetectWindow& detectwindow, jrsdata::ComponentUnit::Direction direction) {
        if (!detectwindow.model_name._Starts_with("pad"))
        {
            return;
        }

        cv::Point2f component_center(0, 0);
        cv::Point2f unit_center(detectwindow.cx, detectwindow.cy);
        cv::RotatedRect detect_window_rotated(
            { detectwindow.cx, detectwindow.cy },
            { detectwindow.width, detectwindow.height },
            0
        );

        auto new_rect = GetRotatedRectByPadDirection(component_center, unit_center, detect_window_rotated, direction);

        detectwindow.cx = new_rect.center.x;
        detectwindow.cy = new_rect.center.y;
        detectwindow.width = new_rect.size.width;
        detectwindow.height = new_rect.size.height;
        };

    // 显示图片
    jrsdata::ComponentEntity component;
    component_lib_ptr->QueryComponent(component, part_name);
    cv::Mat img_show = component.display_img.GetMatImage();
    img_view->CreateImage(0, img_show, 0, 0, 0, 0, true);
    img_view->ClearGraphicsShapes();
    // 检测框
    auto detect_models = component.detect_info.detect_models;
    auto units = component.detect_info.units;
    for (size_t i = 0; i < units.size(); i++)
    {
        // 组件中心点坐标
        float center_x = units[i].x - component.offset_x;
        float center_y = units[i].y - component.offset_y;

        // 组件组名称
        std::string unit_group_name = units[i].unit_group_name;
        auto it = detect_models.find(unit_group_name);
        if (it != detect_models.end())
        {
            std::vector<jrsdata::DetectWindow> detect_model = it->second.detect_model;
            for (size_t j = 0; j < detect_model.size(); j++)
            {
                // 检测框相对于元件中心点的偏移
                float offset_x = center_x + detect_model[j].cx;
                float offset_y = center_y + detect_model[j].cy;

                // 检测框尺寸
                float detect_width = detect_model[j].width;
                float detect_height = detect_model[j].height;

                // PAD的检测框需要调整方向
                if (it->first._Starts_with("pad"))
                {
                    ProcessMirrorPad(detect_model[j], units[i].direction);
                    detect_width = detect_model[j].width;
                    detect_height = detect_model[j].height;
                }

                // 绘制检测框
                jrsdata::GraphicsViewShape graphic;
                graphic.type = jrsdata::GraphicsViewShape::Type::Rect;
                graphic.center_position = cv::Point2f(offset_x, offset_y);
                graphic.size = cv::Size(detect_width, detect_height);
                graphic.color = cv::Scalar(50, 131, 73);
                graphic.thickness = 1;
                img_view->AddGraphicsShapes(graphic);
            }
        }

        // 绘制本体和PAD
        jrsdata::GraphicsViewShape graphic;
        graphic.type = jrsdata::GraphicsViewShape::Type::Rect;
        graphic.center_position = cv::Point2f(center_x, center_y);
        graphic.size = cv::Size(units[i].width, units[i].height);
        graphic.color = cv::Scalar(10,139,139);
        graphic.thickness = 1;
        img_view->AddGraphicsShapes(graphic);
    }

    img_view->ShowImageByKey(0);
}

cv::RotatedRect ComponentLibView::GetRotatedRectByPadDirection(const cv::Point2f& component_center_point_, const cv::Point2f& pad_center_point_, const cv::RotatedRect& detect_window_rect_, const jrsdata::ComponentUnit::Direction& src_direction_, const jrsdata::ComponentUnit::Direction& obj_direction_)
{
    //根据原方向和目标方向的距离  计算角度。
    if (src_direction_ == jrsdata::ComponentUnit::Direction::UNKNOWN || obj_direction_ == jrsdata::ComponentUnit::Direction::UNKNOWN)
        return  cv::RotatedRect();
    cv::Size2f res_rect;
    int angle = (static_cast<int>(obj_direction_) - static_cast<int>(src_direction_)) * 90;

    if (angle < 0)
    {
        angle += 360;
    }

    // 计算 ComponentUnit 的全局中心坐标
    cv::Point2f unit_global_center_point = component_center_point_ + pad_center_point_;

    // 计算 Detectsrc_detect_window_ 的全局中心坐标
    cv::Point2f detect_global_center = unit_global_center_point + detect_window_rect_.center;

    jcvtools::JrsHomMat2D jrs_hom_mat_2d;
    jrs_hom_mat_2d.AddHomMat2dRotate(angle, component_center_point_.x, component_center_point_.y);
    auto rotated_detect_center = jrs_hom_mat_2d.AffineTransPoint(detect_global_center);
    auto rotated_unit_center = jrs_hom_mat_2d.AffineTransPoint(unit_global_center_point);

    // 计算相对于 ComponentUnit 的新坐标
    cv::Point2f relative_rotated_detect_center_point = rotated_detect_center - rotated_unit_center;
    cv::Size2f rotate_size = detect_window_rect_.size;
    if (angle == 90 || angle == 270)
    {
        rotate_size.width = detect_window_rect_.size.height;
        rotate_size.height = detect_window_rect_.size.width;
    }

    return cv::RotatedRect{ relative_rotated_detect_center_point, rotate_size ,0 };
}

