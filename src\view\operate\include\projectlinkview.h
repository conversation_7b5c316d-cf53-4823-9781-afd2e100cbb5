#pragma once

#include <QWidget>
#include "ui_projectlinkview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class ProjectLinkViewClass; };
QT_END_NAMESPACE

class ProjectLinkView : public QWidget
{
    Q_OBJECT

    public:
        ProjectLinkView(QWidget *parent = nullptr);
        ~ProjectLinkView();

        /**
         * @fun SetProjectLists 
         * @brief 设置工程列表
         * @param project_lists [IN] 工程列表
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SetProjectLists(const std::list<std::string>& project_lists);
        /**
         * @fun SetLinkedProjectName 
         * @brief
         * @param linked_project_name_
         * <AUTHOR>
         * @date 2025.6.9
         */
        void SetLinkedProjectName(const std::string& linked_project_name_);
    signals:

        /**
        * @fun SigComfirmLinkProject
        * @brief 确认工程关联
        * @param project_name [IN] 关联的工程名称
        * <AUTHOR>
        * @date 2025.6.8
        */
        void SigComfirmLinkProject(const std::string& project_name);
        /**
         * @fun SigCancleLinkProject 
         * @brief
         * <AUTHOR>
         * @date 2025.6.8
         */
        void SigCancleLinkProject();
    public slots:
        void SlotConfirmLinkProject();
        void SlotCancleLinkProject();
    private:
        Ui::ProjectLinkViewClass *ui;

        void InitConnect();
};

