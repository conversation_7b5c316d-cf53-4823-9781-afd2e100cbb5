#include "ShowMarkResultImageWidget.h"

#include <QFont>
#include <QGraphicsPixmapItem>
#include <QGraphicsTextItem>

#include <iostream>
#include <algorithm>

#include "generalimagetool.h"
#include "coreapplication.h"

ShowMarkResultImageWidget::ShowMarkResultImageWidget(QWidget* parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    max_width = 0;
    max_height = 0;
    InitView();
}

ShowMarkResultImageWidget::~ShowMarkResultImageWidget()
{
}

void ShowMarkResultImageWidget::InitView()
{
    setWindowFlags(Qt::WindowCloseButtonHint | Qt::Window | Qt::WindowStaysOnTopHint);
    m_graphics_scene_mark = new QGraphicsScene();
    m_graphics_view_mark = new ViewerGraphicsViewImpl();
    m_graphics_view_mark->setScene(m_graphics_scene_mark);
    ui.horizontalLayout->addWidget(m_graphics_view_mark);
    font = QFont("Arial", 10);
    text_color = Qt::white;
}

void ShowMarkResultImageWidget::SetMarkResult(std::map<std::string, std::unordered_map<int, cv::Mat>>& mark_results, std::map<std::string/*mark名称*/, float/*mark 分数*/>& mark_scores,
    std::map<std::string/*mark名称*/, std::tuple<float/*x*/, float/*y*/, float/*width*/, float/*height*/>/*矩形框*/> mark_rects, int& mark_status)
{
    ShowDialog(mark_status);
    max_width = 0;
    max_height = 0;
    m_graphics_scene_mark->clear();

    int current_x = 0;
    int current_y = 0;
    int id = 0;
    int max_column_count = 2;//固定一行最多显示2个

    for (const auto& [mark_name, mark_result] : mark_results)
    {
        for (const auto& [light_id, mark_image] : mark_result)
        {
            if (mark_image.empty())
            {
                continue;
            }
            float out_left = 0.0;
            float out_top = 0.0;
            auto mark_image_rect = CreateRectImage(mark_image, mark_name, mark_rects, out_left, out_top);
            if (mark_image_rect.empty())
            {
                continue;
            }
            auto pix_map = CvMatToQPixmap(mark_image_rect);
            if (pix_map.isNull())
            {
                continue;
            }
            CreatePixmapItem(pix_map, current_x, current_y);
            CreateEllipseItem(mark_name, mark_rects, out_left, out_top, current_x, current_y);
            std::string show_text = CreateShowString(mark_name, mark_scores);
            CreateTextItem(show_text, current_x, current_y);
            max_width = std::max(max_width, (current_x + pix_map.width()));
            max_height = std::max(max_height, (current_y + pix_map.height()));
            id++;
            if (id >= max_column_count)
            {
                id = 0;
                current_x = 0;
                current_y += pix_map.height();
            }
            else
            {
                current_x += pix_map.width();
            }
            break;
        }
    }

    //将视图的中心设置到场景的中心
    m_graphics_view_mark->centerOn(max_width / 2, max_height / 2);

    //调整视图以适配所有内容
    m_graphics_view_mark->fitInView(QRectF(0, 0, max_width, max_height), Qt::KeepAspectRatio);

    m_graphics_scene_mark->setSceneRect(0, 0, max_width, max_height);
}
void ShowMarkResultImageWidget::CreatePixmapItem(QPixmap& pixmap, int current_x, int current_y)
{
    QGraphicsPixmapItem* pixmap_item = m_graphics_scene_mark->addPixmap(pixmap);
    if (pixmap_item != nullptr)
    {
        pixmap_item->setPos(current_x, current_y);
    }
}

void ShowMarkResultImageWidget::CreateTextItem(std::string text, int current_x, int current_y)
{
    QGraphicsTextItem* text_item = m_graphics_scene_mark->addText(text.c_str());
    if (text_item != nullptr)
    {
        text_item->setFont(font);
        text_item->setDefaultTextColor(text_color);
        text_item->setPos(current_x, current_y);
        text_item->setFlag(QGraphicsItem::ItemIgnoresTransformations);
        text_item->setZValue(10000);
        text_item->setOpacity(0.7);
    }
}

std::string ShowMarkResultImageWidget::CreateShowString(std::string mark_name, std::map<std::string, float>& mark_scores)
{
    std::string show_text = mark_name.c_str();
    if (mark_scores.count(mark_name) > 0)
    {
        show_text += " ";
        show_text += jrscore::AOITools::FloatToString(mark_scores[mark_name], 2);
    }
    return show_text;
}

cv::Mat ShowMarkResultImageWidget::CreateRectImage(cv::Mat src, std::string mark_name, std::map<std::string,
    std::tuple<float, float, float, float>> mark_rects, float& out_left, float& out_top)
{
    out_left = 0;
    out_top = 0;
    if (src.empty())
    {
        return src;
    }
    try
    {
        // 定义要截取的矩形区域 (x, y, width, height)
        if (mark_rects.count(mark_name) > 0)
        {
            float image_width = src.cols;
            float image_height = src.rows;
            auto& [x, y, width, height] = mark_rects[mark_name];
            if (x >= image_width || y >= image_height || x < 0 || y < 0)
            {
                return src;
            }
            float left = x - width;
            float top = y - height;
            float right = x + width;
            float bottom = y + height;
            left = std::max(float(0.0), left);
            top = std::max(float(0.0), top);
            right = std::min(right, image_width);
            bottom = std::min(bottom, image_height);
            right = std::max(left, right);
            bottom = std::max(top, bottom);

            float after_width = right - left;
            float after_height = bottom - top;
            float before_width = width * 2;
            float before_height = height * 2;
            if (after_width < before_width)
            {
                if (right == image_width)
                {
                    left = std::max(float(0.0), (right - before_width));
                }
                else if (left == 0.0)
                {
                    right = std::min(before_width, image_width);
                }
            }
            if (after_height < before_height)
            {
                if (top == 0.0)
                {
                    bottom = std::min(before_height, image_height);
                }
                else if (bottom == image_height)
                {
                    top = std::max(float(0.0), (bottom - before_height));
                }
            }
            cv::Rect rect(left, top, right - left, bottom - top);
            if (rect.width <= 0 || rect.height <= 0)
            {
                return src;
            }
            cv::Mat mark_image;
            if (jrstool::CropAndPasteTool::CropImage(src, rect, mark_image) == 0)
            {
                out_left = rect.x;
                out_top = rect.y;
                return mark_image;
            }
            return src;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return src;
    }
    catch (...) {
        std::cerr << "Unknown exception occurred." << std::endl;
        return src;
    }
    return src;
}

bool ShowMarkResultImageWidget::CreateEllipseItem(std::string mark_name, std::map<std::string, std::tuple<float, float, float, float>> mark_rects, float& out_left, float& out_top, int& current_x, int& current_y)
{
    if (mark_rects.count(mark_name) > 0 && m_graphics_scene_mark != nullptr)
    {
        auto& [x, y, width, height] = mark_rects[mark_name];
        float ellipse_x = x - width / 2.0 - out_left + current_x;
        float ellipse_y = y - height / 2.0 - out_top + current_y;
        QGraphicsEllipseItem* ellipse_item = m_graphics_scene_mark->addEllipse(ellipse_x, ellipse_y, width, height);
        ellipse_item->setPen(QPen(Qt::green, 2)); // 设置线的颜色和宽度
        ellipse_item->setZValue(1000);

        QGraphicsLineItem* h_line = m_graphics_scene_mark->addLine(ellipse_x, ellipse_y + height / 2.0, ellipse_x + width, ellipse_y + height / 2.0);
        h_line->setPen(QPen(Qt::blue, 2)); // 设置线的颜色和宽度
        h_line->setZValue(1000);
        // 垂直线
        QGraphicsLineItem* v_line = m_graphics_scene_mark->addLine(ellipse_x + width / 2.0, ellipse_y, ellipse_x + width / 2.0, ellipse_y + height);
        v_line->setPen(QPen(Qt::blue, 2)); // 设置线的颜色和宽度
        v_line->setZValue(1000);
        return true;
    }
    return false;
}

void ShowMarkResultImageWidget::ShowDialog(int mark_status)
{
    if (mark_status == 1)// int(jrsdata::BoardDetectStatus::MARK_FAILURE)) mark 识别失败 把mark图片窗口显示出来
    {
        if (!this->isVisible())
        {
            this->show();
        }
    }
}

void ShowMarkResultImageWidget::ImageFitViewSlot()
{
    m_graphics_view_mark->fitInView(0, 0, max_width, max_height, Qt::KeepAspectRatio);
}

QPixmap ShowMarkResultImageWidget::CvMatToQPixmap(const cv::Mat& src)
{
    switch (src.type())
    {
        // 8-bit, 4 channel
    case CV_8UC4:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_ARGB32);

        return QPixmap::fromImage(image);
    }

    // 8-bit, 3 channel
    case CV_8UC3:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_RGB888);

        return QPixmap::fromImage(image.rgbSwapped());
    }

    // 8-bit, 1 channel
    case CV_8UC1:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return QPixmap::fromImage(image);
    }

    // 32-bit, 1 channel
    case CV_32FC1:
    {
        //cv::Mat* imgNormalize = new cv::Mat(src.rows, src.cols, CV_8UC1);
        cv::Mat imgNormalize;
        cv::normalize(src, imgNormalize, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        QImage image(imgNormalize.data,
            imgNormalize.cols, imgNormalize.rows,
            static_cast<int>(imgNormalize.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return QPixmap::fromImage(image);
    }

    default:
        break;
    }

    return QPixmap();
}

void ShowMarkResultImageWidget::showEvent(QShowEvent* show)
{
    (void)show;
    ImageFitViewSlot();
}
