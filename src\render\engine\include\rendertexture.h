/*********************************************************************
 * @brief  纹理渲染.
 *
 * @file   texturerender.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include "renderabstract.hpp" // RenderAbstractPtr

#include <vector>

namespace cv
{
    class Mat;
}
struct Texture2D;

class RenderTexture : public RenderAbstract
{
public:
    RenderTexture();
    ~RenderTexture();

    void Render() override;
    void Destroy() override;

    void Clear();
    void ClearTexture(int z = 0);
    void DeleteTexture(Texture2D* ptr);
    void DeleteTexture(unsigned int texId);
    unsigned int CreateTextureWithCVMat(const cv::Mat& mat, int x, int y, int z, float angle, bool is_draw_center);

    int SetTextureZ(int z, const std::vector<unsigned int>& ids);
    int FindTextureZ(int& z, unsigned int id);
    int FindZTexture(std::vector<unsigned int>& v, int z);

protected:
    /**
     * @brief 将cvmat转换为纹理之前的处理
     */
    bool PrepareCvMatForTexture(unsigned int& format, cv::Mat& src, const cv::Mat& dst);
    bool AddTexture(unsigned int id, unsigned int format, int width, int height, int x, int y, int z, float angle, bool is_draw_center);

private:
    static bool TextureCompareByZ(Texture2D* a, Texture2D* b);
    void SortTextures();

private:
    std::vector<Texture2D*> textures; ///< 纹理容器,这里实际储存的是纹理属性,纹理的真实存放位置在gpu内存
};
