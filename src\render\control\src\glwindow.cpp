#include "glwindow.h"

#include <qevent.h>
#include <qnamespace.h> // Qt::FocusPolicy

GLWindow::GLWindow(QWidget* parent) : QOpenGLWidget(parent), WindowInterface(parent)
{
    setWindowFlags(Qt::FramelessWindowHint /*| Qt::WindowStaysOnTopHint */ | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground); // 半透明背景
    this->setFocusPolicy(Qt::StrongFocus); // 为了确保可接收到鼠标事件
}
GLWindow::~GLWindow()
{
}

void GLWindow::DoMakeCurrent()
{
    // TODO 
    QOpenGLWidget::makeCurrent();
}

void GLWindow::DoDoneCurrent()
{
    QOpenGLWidget::doneCurrent();
}

void GLWindow::RequestUpdate()
{
    update();
}

void GLWindow::onResizeGL(int w, int h)
{
    processResizeEvent(w, h);
}

void GLWindow::wheelEvent(QWheelEvent* event)
{
    QPoint angle = event->angleDelta();
    int numDegrees = angle.y();
    processWheelEvent(numDegrees, event->position().x(), event->position().y());
}

void GLWindow::keyPressEvent(QKeyEvent* event)
{

    processKeyPressEvent(event->key(), event->isAutoRepeat());
}

void GLWindow::keyReleaseEvent(QKeyEvent* event)
{
    processKeyReleaseEvent(event->key(), event->isAutoRepeat());
}

//void GLWindow::resizeEvent(QResizeEvent* event)
//{
//    QWidget::resizeEvent(event);
//    this->resize(event->size());
//}

void GLWindow::moveEvent(QMoveEvent* event)
{
    QWidget::moveEvent(event);
    this->move(event->pos());
}
