﻿#include "algoselectwidget.h"

#include <QPushButton>
#include <QComboBox>
#include <QCheckBox>
#include <QLabel>
#include <QEvent>
#include <QHBoxLayout>


class NoWheelEventFilter : public QObject {
protected:
    bool eventFilter(QObject* obj, QEvent* event) override
    {
        if (event->type() == QEvent::Wheel)
        {
            // 禁止滚轮事件
            return true;
        }
        return QObject::eventFilter(obj, event);
    }
};


AlgoSelectWidget::AlgoSelectWidget(QWidget* parent, std::vector<AlgoName> algo_list, std::vector<std::string> error_list)
    : QFrame(parent)
    , label_algo_name(new QLabel())
    , combo_defect_name(new QComboBox())
    , label_group(new QLabel())
    , label_index(new QLabel())
    , check_enable(new QCheckBox())
    , comb_parant_index(new QComboBox())
    , spin_box_padding_size(new QDoubleSpinBox())
{
    //item_delegate = new ImageTableDelegate(this);
    //this->setItemDelegate(item_delegate);

    label_index->setText(tr("1"));
    label_index->setFixedSize(10, 20);

    label_group->setTextInteractionFlags(Qt::NoTextInteraction);
    label_group->setStyleSheet("QLabel {border: 1px solid black; }");
    label_group->setFixedSize(40, 20);
    check_enable->setFixedSize(16, 16);
    check_enable->setChecked(true);

    label_algo_name->setFixedHeight(20);
    label_algo_name->setFixedWidth(80);
    combo_defect_name->setFixedHeight(20);
    combo_defect_name->setFixedWidth(70);
    combo_defect_name->setSizeAdjustPolicy(QComboBox::SizeAdjustPolicy::AdjustToContents);
    comb_parant_index->addItem("0");
    spin_box_padding_size->setFixedHeight(20);
    spin_box_padding_size->setFixedWidth(50);
    spin_box_padding_size->setMinimum(0);
    spin_box_padding_size->setMaximum(50);
    spin_box_padding_size->setSingleStep(0.1);

    for (auto& error : error_list)
    {
        combo_defect_name->addItem(QString::fromStdString(error));
    }

    NoWheelEventFilter* filter1 = new NoWheelEventFilter();
    NoWheelEventFilter* filter2 = new NoWheelEventFilter();
    comb_parant_index->installEventFilter(filter1);
    combo_defect_name->installEventFilter(filter2);
    auto layout = new QHBoxLayout(this);
    layout->setContentsMargins(3, 5, 3, 5);
    auto frame = new QFrame();
    layout->addWidget(frame);
    {
        auto tlayout = new QHBoxLayout(frame);
        tlayout->setContentsMargins(0, 0, 0, 0);
        tlayout->addWidget(label_index);
        tlayout->addWidget(check_enable);
        tlayout->addWidget(label_algo_name);
        tlayout->addWidget(combo_defect_name);
        tlayout->addWidget(comb_parant_index);
        tlayout->addWidget(spin_box_padding_size);
        tlayout->addWidget(label_group);
    }

    algo_name_list = algo_list;

    SetEditEnable(edit_enable);

    connect(combo_defect_name, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [=]()
        {
            DetectWindowItemValue value;
            GetCurValueFromUi(value);
            defect_type_name = value.defect_type_name;
            emit SignalValueChanged(value);
        });
    connect(check_enable, &QCheckBox::stateChanged, this, [=]()
        {
            DetectWindowItemValue value;

            switch (check_enable->checkState())
            {
            case Qt::CheckState::Unchecked:
            {
                combo_defect_name->setEnabled(false);
                comb_parant_index->setEnabled(false);
                break;
            }
            case Qt::CheckState::Checked:
            {
                combo_defect_name->setEnabled(true);
                comb_parant_index->setEnabled(true);
                break;
            }
            default:
                break;
            }
            GetCurValueFromUi(value);
            enable = value.is_enable;
            emit SignalValueChanged(value);
        });

    connect(comb_parant_index, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [=]()
        {
            auto cur_select_parent_name = comb_parant_index->currentData().toString();
            if (cur_select_parent_name != parent_win_name)
            {
                DetectWindowItemValue value;
                GetCurValueFromUi(value);
                parent_win_name = value.parent_window_name;
                emit SignalValueChanged(value);
            }
        });
    connect(spin_box_padding_size, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=]()
        {
            DetectWindowItemValue value;
            GetCurValueFromUi(value);
            serach_size = value.serach_size;
            emit SignalValueChanged(value);
        });
}

void AlgoSelectWidget::SetValue(const DetectWindowItemValue& value)
{
    SetName(value.window_name);
    SetGroup(value.group_name);
    SetAlgoName(value.algo_name);
    SetDefectName(value.defect_type_name);
    SetEnable(value.is_enable);
    SetParentWinName(value.parent_window_name);
    SetSerachSize(value.serach_size);
}

void AlgoSelectWidget::SetName(const QString& item)
{
    name = item;
}

void AlgoSelectWidget::SetSerachSize(float size)
{
    serach_size = size;
    spin_box_padding_size->setValue(size);
}

void AlgoSelectWidget::SetParentWinName(const QString& item)
{
    parent_win_name = item;
    int index = comb_parant_index->findData(item);
    if (index != -1)
    {
        comb_parant_index->setCurrentIndex(index);
    }
    else
    {
        index = comb_parant_index->findData("");
        if (index != -1)
        {
            comb_parant_index->setCurrentIndex(index);
        }
    }
}

void AlgoSelectWidget::SetGroup(const QString& item)
{
    label_group->setText(item);
    group_name = item;
}
void AlgoSelectWidget::SetDefectName(const QString& item)
{
    combo_defect_name->setCurrentText(item);
    defect_type_name = item;
}

void AlgoSelectWidget::SetAlgoName(const QString& item)
{
    //for (size_t i = 0; i < algo_name_list.size(); i++)
    //{
    //    if (algo_name_list[i].code_name == item.toLocal8Bit().toStdString())
    //    {   
    //        QString display_name = QString::fromLocal8Bit(algo_name_list[i].display_name.c_str());
    //        label_algo_name->setText(display_name);
    //        
    //    }
    //}
    label_algo_name->setText(item);
    label_algo_name->setProperty("code_name", item);
    algo_name = item;
}

void AlgoSelectWidget::SetEnable(bool state)
{
    check_enable->setChecked(state);
    enable = state;
}

void AlgoSelectWidget::UpdateCurValueFromUi()
{
    algo_name = label_algo_name->text();
    parent_win_name = comb_parant_index->currentData().toString();
    defect_type_name = combo_defect_name->currentText();
    enable = check_enable->isChecked();
    group_name = this->label_group->text();
}

void AlgoSelectWidget::UndoChange()
{
    SetDefectName(defect_type_name);
    SetAlgoName(algo_name);
    SetEnable(enable);
    SetGroup(group_name);
    SetParentWinName(parent_win_name);
}

void AlgoSelectWidget::GetCurValueFromUi(DetectWindowItemValue& value)
{
    value.algo_name = label_algo_name->property("code_name").toString();
    value.window_name = name;
    value.parent_window_name = comb_parant_index->currentData().toString();
    value.defect_type_name = combo_defect_name->currentText();
    value.is_enable = check_enable->isChecked();
    value.group_name = this->label_group->text();
    value.serach_size = spin_box_padding_size->value();
}

void AlgoSelectWidget::GetCurValueBeforChange(DetectWindowItemValue& value)
{
    value.algo_name = label_algo_name->property("code_name").toString();
    value.window_name = name;
    value.parent_window_name = comb_parant_index->currentData().toString();
    value.defect_type_name = combo_defect_name->currentText();
    value.is_enable = check_enable->isChecked();
    value.group_name = this->label_group->text();
}

void AlgoSelectWidget::UpdateParentWinIndexMap(const std::map<std::string, int>& index_map)
{
    comb_parant_index->blockSignals(true); // 阻止信号
    comb_parant_index->clear();
    for (const auto& item : index_map)
    {
        comb_parant_index->addItem(QString::number(item.second), QString::fromLocal8Bit(item.first.c_str()));
    }
    auto std_string = parent_win_name.toLocal8Bit().toStdString();
    auto iter = index_map.find(parent_win_name.toLocal8Bit().toStdString());
    if (iter != index_map.end())
    {
        comb_parant_index->setCurrentIndex(iter->second);
    }
    comb_parant_index->blockSignals(false); // 恢复信号

    SetParentWinName(parent_win_name);

    auto temp = index_map.find(name.toLocal8Bit().toStdString());
    if (temp != index_map.end())
    {
        label_index->setText(QString::number(temp->second));
    }
}

void AlgoSelectWidget::SetEditEnable(bool state)
{
    check_enable->setEnabled(state);
    combo_defect_name->setEnabled(state);
    comb_parant_index->setEnabled(state);
    spin_box_padding_size->setEnabled(state);

    edit_enable = state;
}
