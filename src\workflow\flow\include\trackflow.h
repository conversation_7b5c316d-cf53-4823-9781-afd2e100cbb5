/*****************************************************************
 * @file   trackflow.h
 * @brief  轨道控制流程
 * @details 主要功能是在检测过程中对轨道进行控制：如上料、下料、待料等
 * <AUTHOR>
 * @date 2024.11.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.21          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef  __JRSTRACKFLOW_H__
#define __JRSTRACKFLOW_H__

//PREBUILD
#include "workflowpch.h"
//STD
//Custom
//#include "workflowinterfaces.h"
//Third
namespace jrsdevice
{
    class DeviceManager;
}
namespace jrsworkflow 
{
    class TrackFlow : public ITrackFlow
    {
        public :
            explicit TrackFlow ( const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_ );
            ~TrackFlow ();
            int LoadMaterial ( int rail_index ) override;
            int UnloadMaterial ( int rail_index ) override;
            std::string GetTrackErrStr() override;
            int StopTrack ()override;
        private:
            void Init ();
            std::shared_ptr<jrsdevice::DeviceManager> device_manager_ptr;
    };

}



#endif // ! __JRSTRACKFLOW_H__