/*********************************************************************
 * @brief  定义一些2d渲染结构体. 已弃用
 *
 * @file   geometry2d.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef GEOMETRY_2D_H
#define GEOMETRY_2D_H

namespace cv
{
    template<typename _Tp> class Point_;
};

template<typename PointT>
class Line
{
public:
    PointT pt1, pt2;//直线上两点

    //构造函数，参数初始化为0
    Line() :pt1(0, 0), pt2(0, 0) {}

    //传两个点，初始化直线方程
    Line(const PointT& pt1_, const PointT& pt2_) :pt1(pt1_), pt2(pt2_) {}

    //析构
    ~Line() {}
};

using Linecvp2f = Line<cv::Point_<float>>;

#endif // !GEOMETRY_2D_H