/*****************************************************************//**
 * @file   errorhandler.h
 * @brief  错误处理工具类
 * @details 用于定义错误工具接口类
 * <AUTHOR>
 * @date 2024.1.23
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.23         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __ERRORHANDLER_H__
#define __ERRORHANDLER_H__

 //STD
#include <string>
#include <memory>
#include <queue>
#include <stack>
#include <map>
#include <functional>
#include <mutex>

//Custom
#include "pluginexport.hpp"

namespace jrscore
{

    /**
         * @brief 核心库错误码/通用错误码
         * @detail
         *    规格约束
         *     一 错误码名称：<错误标识>_<软件名称>_<模块名称>_<错误类型描述>_<错误类型描述>_<错误类型描述>
         *            1.1 错误标识通常为固定标识 E
         *            1.2 软件名称通常为固定标识 AOI
         *            1.3 模块名称与错误码描述文件一一对应，如CORE库模块名称为 CORE
         *            1.4 错误类型描述，此处较宽泛，但最多不建议超过长度3
         *               可为形容词（UNKNOWN）、动词+名词+形容词（LOAD_FILE_FAILED）等形式
         *     二 错误码位排布
         *        考虑Int对应最大16进制数为 0x7FFFFFFF  自16进制高地址位起，取1~2位代表软件标识, 即对于JRSAOI，视为 10，
         *                                                                  取3~4位代表模块标识, 则对于核心库模块    ，视为 01,
         *                                                                  第5位暂为保留位,默认为0
         *                                                                  取6~8位代表错误码，  则对于核心库未知错误，视为001，
         *
         *      一般核心库错误码可视为通用错误码
         *
         */
    using AOIErrorCode = int;
    constexpr int AOI_OK = 0;
    /*** @brief 错误描述信息 zhangyuyu 2024.1.23*/
    struct  ErrorInfo
    {
        //AOIErrorCode error_code;      /**< 错误码         */
        std::string err_string;         /**< 错误码字符信息 */
        std::string err_description;    /**< 错误码描述信息 */
        std::string module_name;        /**< 模块名称 */
        std::string what;               /**< 调用端详细描述 */

    };
    /** 定义错误码宏 */
#define DEFINE_ERROR_CODE(name, value) \
    static const int name = value; 


    /*** @brief  核心库模块错误码，模块标识为:01 zhangyuyu 2024.1.23*/
    class CoreError
    {

    public:

        DEFINE_ERROR_CODE(E_AOI_CORE_UNKNOWN, 0x10010001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_CORE_POINTER_EMPTY, 0x10010002);/**< 指针为空  */
        DEFINE_ERROR_CODE(E_AOI_CORE_REPEAT_INIT, 0x10010003);/**< 重复初始化  */
        DEFINE_ERROR_CODE(E_AOI_CORE_INIT_FAIL, 0x10010004);/**< 初始化失败  */
        DEFINE_ERROR_CODE(E_AOI_CORE_NO_SUBSCRIBER, 0x10010005);/**< 当前订阅者不存在  */
        DEFINE_ERROR_CODE(E_AOI_CORE_PARAM_NULL, 0x10010006);/**< 当前参数为空  */

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetCoreErrorMap()
        {
            return error_core_map;
        }

    private:

        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_core_map =
        {
            {E_AOI_CORE_UNKNOWN, {"E_AOI_CORE_UNKNOWN","未知错误","Core",""}},
            {E_AOI_CORE_POINTER_EMPTY, {"E_AOI_CORE_POINTER_EMPTY","空指针","Core",""}},
            {E_AOI_CORE_REPEAT_INIT, {"E_AOI_CORE_REPEAT_INIT","重复初始化","Core",""}},
            {E_AOI_CORE_INIT_FAIL, {"E_AOI_CORE_INIT_FAIL","初始化失败","Core",""}},
            {E_AOI_CORE_NO_SUBSCRIBER, {"E_AOI_CORE_NO_SUBSCRIBER","当前订阅者不存在","Core",""}}

        };

        static_assert(
            (static_cast<int>(E_AOI_CORE_NO_SUBSCRIBER) - static_cast<int>(E_AOI_CORE_UNKNOWN)) == 4,
            "Core ErrorCount not equal !"
            );

    };

    /*** @brief  界面模块错误码，模块标识为:02 zhangyuyu 2024.1.23*/
    class ViewError
    {
    public:
        DEFINE_ERROR_CODE(E_AOI_VIEW_UNKNOWN, 0x10020001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_VIEW_INIT_FAILURE, 0x10020002);/**< 界面初始化失败 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_EMPTY_POINTER, 0x10020003);/**< 空指针 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_RENDER_MULTI_ITEMS, 0x10020004);/**< 选择多个图形 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_UNDEFINE_ENUM, 0x10020005);/**< 未定义的枚举值 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_SET_RENDER_FAILURE, 0x10020006);/**< 设置渲染界面参数失败 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_BOARD_SIZE_INVAILD, 0x10020007);/**< 板子尺寸无效 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_PARSE_ALGOPARAM_FAILURE, 0x10020008);/**< 解析算法参数失败 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_READ_ENTIRY_IMAGE_FAILURE, 0x10020009);/**< 读取整板大图失败 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_ALGO_EXECUTE_FAILURE, 0x10020010);/**< 算法执行失败 */
        DEFINE_ERROR_CODE(E_AOI_VIEW_PROJECT_NOT_IMPORT, 0x10020011);/**< 未导入工程 */

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetViewErrorMap()
        {
            return error_view_map;
        }
    private:

        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_view_map =
        {
            {E_AOI_VIEW_UNKNOWN, {"E_AOI_CORE_UNKNOWN","未知错误","View",""}},
            {E_AOI_VIEW_INIT_FAILURE, {"E_AOI_VIEW_INIT_FAILURE","初始化失败","View",""}},
            {E_AOI_VIEW_EMPTY_POINTER, {"E_AOI_VIEW_EMPTY_POINTER","空指针","View",""}},
            {E_AOI_VIEW_RENDER_MULTI_ITEMS, {"E_AOI_VIEW_RENDER_MULTI_ITEMS","选择多个图形","View",""}},
            {E_AOI_VIEW_UNDEFINE_ENUM, {"E_AOI_VIEW_UNDEFINE_ENUM","未定义的枚举值","View",""}},
            {E_AOI_VIEW_SET_RENDER_FAILURE, {"E_AOI_VIEW_SET_RENDER_FAILURE","设置渲染参数失败","View",""}},
            {E_AOI_VIEW_BOARD_SIZE_INVAILD, {"E_AOI_VIEW_BOARD_SIZE_INVAILD","板子尺寸无效","View",""}},
            {E_AOI_VIEW_PARSE_ALGOPARAM_FAILURE, {"E_AOI_VIEW_PARSE_ALGOPARAM_FAILURE","解析算法参数失败","View",""}},
            {E_AOI_VIEW_READ_ENTIRY_IMAGE_FAILURE, {"E_AOI_VIEW_READ_ENTIRY_IMAGE_FAILURE","读取整板大图失败","View",""}},
            {E_AOI_VIEW_ALGO_EXECUTE_FAILURE, {"E_AOI_VIEW_ALGO_EXECUTE_FAILURE","算法执行失败","View",""}},
            {E_AOI_VIEW_ALGO_EXECUTE_FAILURE, {"E_AOI_VIEW_PROJECT_NOT_IMPORT","工程未导入","View",""}},


        };

        static_assert(
            (static_cast<int>(E_AOI_VIEW_RENDER_MULTI_ITEMS) - static_cast<int>(E_AOI_VIEW_UNKNOWN)) == 3,
            "View ErrorCount not equal !"
            );
    };


    /*** @brief  设备模块错误码，模块标识为:03 zhangyuyu 2024.1.23*/
    class DeviceError
    {
    public:


        DEFINE_ERROR_CODE(E_AOI_DEVICE_UNKNOWN, 0x10030001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_INIT_FAILURE, 0x10030002);/**< 初始化失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_CAPTURE_EXCEPTION, 0x10030003);/**< 采集图片异常 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_CAPTURE_TRIGGER_FAILURE, 0x10030004);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_MOTION_RESET_FAILURE, 0x10030005);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_TRACK_RESET_FAILURE, 0x10030006);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_STRUCT_LIGHT_RESET_FAILURE, 0x10030007);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_2D_IMAGE_RESET_FAILURE, 0x10030008);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_3D_IMAGE_RESET_FAILURE, 0x10030009);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_CALIBRATION_RESET_FAILURE, 0x1003000A);/**< 触发采集图片失败 */
        DEFINE_ERROR_CODE(E_AOI_DEVICE_BASEPLANE_WARP_FAILURE, 0x1003000B);/**< 基面校正失败 */
        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetDeviceErrorMap()
        {
            return error_device_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_device_map =
        {
            {E_AOI_DEVICE_UNKNOWN, {"E_AOI_DEVICE_UNKNOWN","未知错误","Device",""}},
            {E_AOI_DEVICE_INIT_FAILURE, {"E_AOI_DEVICE_INIT_FAILURE","初始化失败","Device",""}},
            {E_AOI_DEVICE_CAPTURE_EXCEPTION, {"E_AOI_DEVICE_CAPTURE_EXCEPTION","采集图片异常","Device",""}},
            {E_AOI_DEVICE_CAPTURE_TRIGGER_FAILURE, {"E_AOI_DEVICE_CAPTURE_TRIGGER_FAILURE","触发采集图片失败","Device",""}},
            {E_AOI_DEVICE_MOTION_RESET_FAILURE, {"E_AOI_DEVICE_MOTION_RESET_FAILURE","运控回零失败","Device",""}},
            {E_AOI_DEVICE_TRACK_RESET_FAILURE, {"E_AOI_DEVICE_TRACK_RESET_FAILURE","轨道初始化失败","Device",""}},
            {E_AOI_DEVICE_STRUCT_LIGHT_RESET_FAILURE, {"E_AOI_DEVICE_STRUCT_LIGHT_RESET_FAILURE","结构光初始化失败","Device",""}},
            {E_AOI_DEVICE_2D_IMAGE_RESET_FAILURE, {"E_AOI_DEVICE_2D_IMAGE_RESET_FAILURE","2D图像检测失败","Device",""}},
            {E_AOI_DEVICE_3D_IMAGE_RESET_FAILURE, {"E_AOI_DEVICE_3D_IMAGE_RESET_FAILURE","3D图像检测失败","Device",""}},
            {E_AOI_DEVICE_CALIBRATION_RESET_FAILURE, {"E_AOI_DEVICE_CALIBRATION_RESET_FAILURE","标定文件检测失败","Device",""}},
            {E_AOI_DEVICE_BASEPLANE_WARP_FAILURE, {"E_AOI_DEVICE_BASEPLANE_WARP_FAILURE","基面校正失败","Device",""}},

        };

        static_assert(
            (static_cast<int>(E_AOI_DEVICE_INIT_FAILURE) - static_cast<int>(E_AOI_DEVICE_UNKNOWN)) == 1,
            "Device ErrorCount not equal !"
            );


    };

    /*** @brief  算法引擎模块错误码，模块标识为:04 zhangyuyu 2024.1.23*/
    class AlgorithmError
    {
    public:
        DEFINE_ERROR_CODE(E_AOI_ALG_UNKNOWN, 0x10040001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_ALG_LOAD_PLUGIN_FAIL, 0x10040002);/**< 插件导入失败 */
        DEFINE_ERROR_CODE(E_AOI_ALG_NOT_FOUND_PLUGIN, 0x10040003);/**< 没有发现插件 */
        DEFINE_ERROR_CODE(E_AOI_ALG_LOAD_REPEAT, 0x10040004);/**< 算子重复加载 */
        DEFINE_ERROR_CODE(E_AOI_ALG_PARSE_JSON_FAILURE, 0x10040005);/**< 解析算法json配置文件失败 */
        DEFINE_ERROR_CODE(E_AOI_ALG_ALGO_NOT_FOUND, 0x10040006);/**< 没有找到指定算法 */


        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetAlogErrorMap()
        {
            return error_alog_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_alog_map =
        {
            {E_AOI_ALG_UNKNOWN, {"E_AOI_ALG_UNKNOWN","未知错误","Algorithm",""}},
            {E_AOI_ALG_LOAD_PLUGIN_FAIL, {"E_AOI_ALG_LOAD_PLUGIN_FAIL","插件导入失败","Algorithm",""}},
            {E_AOI_ALG_NOT_FOUND_PLUGIN, {"E_AOI_ALG_NOT_FOUND_PLUGIN","没有发现插件","Algorithm",""}},
            {E_AOI_ALG_LOAD_REPEAT, {"E_AOI_ALG_LOAD_REPEAT","算子重复加载","Algorithm",""}},
            {E_AOI_ALG_PARSE_JSON_FAILURE, {"E_AOI_ALG_PARSE_JSON_FAILURE","解析算法json配置文件失败","Algorithm",""}},
            {E_AOI_ALG_ALGO_NOT_FOUND, {"E_AOI_ALG_ALGO_NOT_FOUND","没有找到指定算法，算法名称或加载异常","Algorithm",""}},
        };

        static_assert(
            (static_cast<int>(E_AOI_ALG_UNKNOWN) - static_cast<int>(E_AOI_ALG_UNKNOWN)) == 0,
            "Algorithm ErrorCount not equal !"
            );


    };

    /*** @brief  流程运行模块错误码，模块标识为:05 zhangyuyu 2024.1.23*/
    class WorkFlowError
    {
    public:
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_UNKNOWN, 0x10050001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_LOAD_FAILURE, 0x10050002);/**< 上料失败  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_UNLOAD_FAILURE, 0x10050003);/**< 下料失败  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_INSPECTION_FAILURE, 0x10050004);/**< 检测失败  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_MARK_FAILURE, 0x10050005);/**< MARK识别失败  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE, 0x10050006);/**< 路径规划异常  */
        DEFINE_ERROR_CODE(E_AOI_WORKFLOW_BARCODE_DEVICE_INIT_FAILURE, 0x10050007);/**< 扫码枪初始化失败  */


    public:
        

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetWorkFlowErrorMap()
        {
            return error_workflow_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_workflow_map =
        {
            {E_AOI_WORKFLOW_UNKNOWN, {"E_AOI_WORKFLOW_UNKNOWN","未知错误","WorkFlow",""}},
            {E_AOI_WORKFLOW_LOAD_FAILURE, {"E_AOI_WORKFLOW_LOAD_FAILURE","上料失败","WorkFlow",""}},
            {E_AOI_WORKFLOW_UNLOAD_FAILURE, {"E_AOI_WORKFLOW_UNLOAD_FAILURE","下料失败","WorkFlow",""}},
            {E_AOI_WORKFLOW_INSPECTION_FAILURE, {"E_AOI_WORKFLOW_INSPECTION_FAILURE","检测失败","WorkFlow",""}},
            { E_AOI_WORKFLOW_MARK_FAILURE, {"E_AOI_WORKFLOW_INSPECTION_FAILURE","mark识别失败","WorkFlow",""}},
            { E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE, {"E_AOI_WORKFLOW_PLAN_CALCUATE_FAILURE","路径规划异常","WorkFlow",""}},
            { E_AOI_WORKFLOW_BARCODE_DEVICE_INIT_FAILURE, {"E_AOI_WORKFLOW_BARCODE_DEVICE_INIT_FAILURE","扫码枪初始化失败","WorkFlow",""}}
            

        };

        static_assert(
            (static_cast<int>(E_AOI_WORKFLOW_UNKNOWN) - static_cast<int>(E_AOI_WORKFLOW_UNKNOWN)) == 0,
            "Project ErrorCount not equal !"
            );
    };


    /*** @brief  轨道模块错误码，模块标识为:06 zhangyuyu 2024.1.23*/
    class RailError
    {
    public:
        static const int E_AOI_RAIL_UNKNOWN = 0x10060001; /**< 未知错误  */
    };


    /*** @brief  工程模块错误码，模块标识为:07 zhangyuyu 2024.1.23*/
    class ProjectError
    {
    public:
        DEFINE_ERROR_CODE(E_AOI_PROJECT_UNKNOWN, 0x10070001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_PROJECT_NOT_SAVE, 0x10070002);/**< 工程未保存  */
        DEFINE_ERROR_CODE(E_AOI_PROJECT_POINTER_EMPTY, 0x10070003);/**< 工程参数指针为空  */



        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetProjectErrorMap()
        {
            return error_project_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_project_map =
        {
            {E_AOI_PROJECT_UNKNOWN, {"E_AOI_PROJECT_UNKNOWN","未知错误","Project",""}},
            {E_AOI_PROJECT_NOT_SAVE, {"E_AOI_PROJECT_NOT_SAVE","工程未保存","Project",""}},
            {E_AOI_PROJECT_POINTER_EMPTY, {"E_AOI_PROJECT_POINTER_EMPTY","工程参数指针为空","Project",""}},

        };

        static_assert(
            (static_cast<int>(E_AOI_PROJECT_UNKNOWN) - static_cast<int>(E_AOI_PROJECT_UNKNOWN)) == 0,
            "Project ErrorCount not equal !"
            );

    };

    /*** @brief  标定模块错误码，模块标识为:08 zhangyuyu 2024.1.23*/
    class CalibrationError
    {
    public:
        static const int E_AOI_CALIBRATION_UNKNOWN = 0x10080001; /**< 未知错误  */
    };

    /*** @brief  数据管理模块错误码，模块标识为:09 zhangyuyu 2024.1.23*/
    class DataManagerError
    {
    public:
        static const int E_AOI_DATA_UNKNOWN = 0x10090001; /**< 未知错误  */
        static const int E_AOI_DB_UER_OR_PASSWORD_ERROR = 0x10090002;  /*< 用户名或密码错误 */
        static const int E_AOI_DB_CONNECT_OUT = 0x10090003;    /**< 连接超时  */
        static const int E_AOI_DB_DISCONNECT = 0x10090004;     /**< 断开连接  */
        static const int E_AOI_DB_MAX_PACKET = 0x10090005;     /**< 数据过大  */
        static const int E_AOI_DB_MYSQL_CODE = 0x10090006;     /**< MYSQL语句错误  */
        static const int E_AOI_DB_UNFIND_DB = 0x10090007;      /**< 没有找到数据库  */
        static const int E_AOI_DB_UNFIND_TABLE = 0x10090008;   /**< 没有找到表  */
        static const int E_AOI_DB_UNFIND_COLUMN = 0x10090009;  /**< 表中没有这一列  */
        static const int E_AOI_DB_PARAM_EMPTY = 0x1009000A;    /**< 参数有为空  */
        //static const int E_AOI_DB_RESULT_EMPTY = 0x1009000B;   /**< 查询结果为空  */
        static const int E_AOI_DB_DUPLICATE_KEY_NAME = 0x1009000C;   /**< 主键重复  */
        static const int E_AOI_DB_EVENT_ALREADY_EXISTS = 0x1009000D;   /**< 事件重复创建  */
        static const int E_AOI_DB_CONNECTION_POOL_INIT = 0x10090010;   /**< 连接池初始化失败*/
        static const int E_AOI_DB_SERVICE_PTR_UNINIT = 0x10090011;   /**< 数据库服务指针未初始化*/
        static const int E_AOI_DB_CONNECTION_POOL_REPEAT_INIT = 0x10090012; /**< 连接池重复初始化*/
        static const int E_AOI_DB_FIND_IMAGE = 0x10090013;   /**< 未找到图片*/
        static const int E_AOI_DB_SAVE_FAILURE = 0x10090014;   /**< 数据库保存数据失败*/
        static const int E_AOI_DB_SYSTEM_PARAM_READ_FAILURE = 0x10090015;    /**< 参数有为空  */
        static const int E_AOI_DB_MACHINE_PARAM_READ_FAILURE = 0x10090016;    /**< 机台参数读取失败  */
        static const int E_AOI_DB_CREATE_DIR_FAILURE = 0x10090017;    /**< 创建文件夹失败  */
        static const int E_AOI_DB_SAVE_FILE_FAILURE = 0x10090018;    /**< 保存文件失败  */
        static const int E_AOI_DB_EXPORT_ALGO_REPORT_FAILURE = 0x10090019;    /**< 导出算法报表失败  */

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetDataManagerErrorMap()
        {
            return error_database_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_database_map =
        {
            {E_AOI_DATA_UNKNOWN, {"E_AOI_DATA_UNKNOWN","数据库未知错误 ","Database",""}},
            {E_AOI_DB_UER_OR_PASSWORD_ERROR, {"E_AOI_DB_UER_OR_PASSWORD_ERROR ","连接数据库用户名或密码有误 ","Database",""}},
            {E_AOI_DB_CONNECT_OUT, {"E_AOI_DB_CONNECT_OUT ","连接数据库超时，请检查网络问题 ","Database",""}},
            {E_AOI_DB_DISCONNECT, {"E_AOI_DB_DISCONNECT ","数据库断开连接，请检查网络 ","Database",""}},
            {E_AOI_DB_MAX_PACKET, {"E_AOI_DB_MAX_PACKET ","传输数据包过大，请调整数据库配置 ","Database",""}},
            {E_AOI_DB_MYSQL_CODE, {"E_AOI_DB_MYSQL_CODE ","SQL语句异常，请检查sql语句 ","Database",""}},
            {E_AOI_DB_UNFIND_DB, {"E_AOI_DB_UNFIND_DB ","未找到数据库，请确认数据库是否存在 ","Database",""}},
            {E_AOI_DB_UNFIND_TABLE, {"E_AOI_DB_UNFIND_TABLE ","未找到数据表，请检查数据表是否存在 ","Database",""}},
            {E_AOI_DB_UNFIND_COLUMN, {"E_AOI_DB_UNFIND_COLUMN ","未找到相关列，请检查sql语句 ","Database",""}},
            {E_AOI_DB_PARAM_EMPTY, {"E_AOI_DB_PARAM_EMPTY ","传入参数不能为空，请检查传入参数 ","Database",""}},
            {E_AOI_DB_EVENT_ALREADY_EXISTS, {"E_AOI_DB_EVENT_ALREADY_EXISTS ","事件重复创建，请检查数据库初始化 ","Database",""}},
            {E_AOI_DB_DUPLICATE_KEY_NAME, {"E_AOI_DB_DUPLICATE_KEY_NAME ","表中主键或索引重复设置 ","Database",""}},
            {E_AOI_DB_CONNECTION_POOL_INIT, {"E_AOI_DB_CONNECTION_POOL_INIT ","线程池初始化失败，数据库连接 ","Database",""}},
            {E_AOI_DB_CONNECTION_POOL_REPEAT_INIT, {"E_AOI_DB_CONNECTION_POOL_REPEAT_INIT ","线程池重复初始化 ","Database",""}},
            {E_AOI_DB_SERVICE_PTR_UNINIT, {"E_AOI_DB_SERVICE_PTR_UNINIT ","数据库逻辑层指针未初始化 ","DatabaseService",""}},
            {E_AOI_DB_FIND_IMAGE, {"E_AOI_DB_FIND_IMAGE ","未找到图片 ","DatabaseSerivce",""}},
            {E_AOI_DB_SAVE_FAILURE, {"E_AOI_DB_SAVE_FAILURE ","数据库保存数据失败 ","DatabaseSerivce",""}},
            {E_AOI_DB_SYSTEM_PARAM_READ_FAILURE, {"E_AOI_DB_SYSTEM_PARAM_READ_FAILURE ","系统参数读取失败 ","DatabaseSerivce",""}},
            {E_AOI_DB_MACHINE_PARAM_READ_FAILURE, {"E_AOI_DB_MACHINE_PARAM_READ_FAILURE ","机台参数读取失败 ","DatabaseSerivce",""}},
            {E_AOI_DB_CREATE_DIR_FAILURE, {"E_AOI_DB_CREATE_DIR_FAILURE ","创建文件夹失败 ","Database",""}},
            {E_AOI_DB_SAVE_FILE_FAILURE, {"E_AOI_DB_SAVE_FILE_FAILURE ","保存文件失败 ","Database",""}},
            {E_AOI_DB_EXPORT_ALGO_REPORT_FAILURE, {"E_AOI_DB_EXPORT_ALGO_REPORT_FAILURE ","导出算法报表失败 ","Database",""}},
        };
    };



    /*** @brief  渲染模块错误码，模块标识为:10 zhangyuyu 2024.1.23*/
    class RenderError
    {
    public:

        DEFINE_ERROR_CODE(E_AOI_REMNDER_UNKNOWN, 0x10100001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_REMNDER_NOT_SELECTED_ITEM, 0x10100002);/**< 没有选中的元件  */
        DEFINE_ERROR_CODE(E_AOI_REMNDER_MULTI_SELECTED_ITEMS, 0x10100003);/**< 选中多个元件  */
        DEFINE_ERROR_CODE(E_AOI_REMNDER_POINTER_TRANFER_FAILURE, 0x10100004);/**< 指针转换失败  */
        DEFINE_ERROR_CODE(E_AOI_REMNDER_NO_EVENT, 0x10100005);/**< 没有响应的事件  */

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetRenderErrorMap()
        {
            return error_render_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_render_map =
        {
            {E_AOI_REMNDER_UNKNOWN, {"E_AOI_REMNDER_UNKNOWN","未知错误","Render",""}},
            {E_AOI_REMNDER_NOT_SELECTED_ITEM, {"E_AOI_REMNDER_NOT_SELECTED_ITEM","没有选中的元件","Render",""}},
            {E_AOI_REMNDER_MULTI_SELECTED_ITEMS, {"E_AOI_REMNDER_MULTI_SELECTED_ITEMS","选中多个元件","Render",""}},
            {E_AOI_REMNDER_POINTER_TRANFER_FAILURE, {"E_AOI_REMNDER_POINTER_TRANFER_FAILURE","指针转换失败","Render",""}},
            {E_AOI_REMNDER_NO_EVENT, {"E_AOI_REMNDER_NO_EVENT","没有找到输入的事件处理函数，请检查事件名称","Render",""}},


        };

        static_assert(
            (static_cast<int>(E_AOI_REMNDER_NO_EVENT) - static_cast<int>(E_AOI_REMNDER_UNKNOWN)) == 4,
            "Render ErrorCount not equal !"
            );
    };


    /*** @brief  通用错误码，模块标识为:11 zhangyuyu 2024.1.23*/
    class CommonError
    {
    public:
        DEFINE_ERROR_CODE(E_AOI_COMMON_UNKNOWN, 0x10110001);/**< 未知错误  */
        DEFINE_ERROR_CODE(E_AOI_POINTER_EMPTY, 0x10110002);/**< 指针为空错误  */

        static inline const std::unordered_map<AOIErrorCode, ErrorInfo>& GetCommonErrorMap()
        {
            return error_common_map;
        }
    private:
        static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_common_map =
        {
            {E_AOI_COMMON_UNKNOWN, {"E_AOI_COMMON_UNKNOWN","未知错误","COMMON",""}},
            { E_AOI_POINTER_EMPTY, {"E_AOI_POINTER_EMPTY","指针为空","COMMON",""}}

        };
    };

    /*** @brief  运控错误码，模块标识为:12 zhaokunlong 2024.11.12*/
#define DEFINE_Motion_ERROR_CODE(name, value) \
    static const int name = value; 
    class MotionError
    {
    public:
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_NoError, 0x10120000);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_UNKNOWN, 0x10120001);
        // 移动失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_X_MoveFail, 0x10120002);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Y_MoveFail, 0x10120003);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Z_MoveFail, 0x10120004);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_A_MoveFail, 0x10120005);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_B_MoveFail, 0x10120006);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_C_MoveFail, 0x10120007);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_D_MoveFail, 0x10120008);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_E_MoveFail, 0x10120009);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XX_MoveFail, 0x1012000A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YX_MoveFail, 0x1012000B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZX_MoveFail, 0x1012000C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AX_MoveFail, 0x1012000D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BX_MoveFail, 0x1012000E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CX_MoveFail, 0x1012000F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DX_MoveFail, 0x10120010);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EX_MoveFail, 0x10120011);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XY_MoveFail, 0x10120012);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YY_MoveFail, 0x10120013);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZY_MoveFail, 0x10120014);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AY_MoveFail, 0x10120015);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BY_MoveFail, 0x10120016);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CY_MoveFail, 0x10120017);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DY_MoveFail, 0x10120018);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EY_MoveFail, 0x10120019);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XZ_MoveFail, 0x1012001A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YZ_MoveFail, 0x1012001B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZZ_MoveFail, 0x1012001C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AZ_MoveFail, 0x1012001D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BZ_MoveFail, 0x1012001E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CZ_MoveFail, 0x1012001F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DZ_MoveFail, 0x10120020);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EZ_MoveFail, 0x10120021);

        // 回零失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_X_HomeFail, 0x10120032);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Y_HomeFail, 0x10120033);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Z_HomeFail, 0x10120034);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_A_HomeFail, 0x10120035);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_B_HomeFail, 0x10120036);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_C_HomeFail, 0x10120037);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_D_HomeFail, 0x10120038);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_E_HomeFail, 0x10120039);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XX_HomeFail, 0x1012003A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YX_HomeFail, 0x1012003B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZX_HomeFail, 0x1012003C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AX_HomeFail, 0x1012003D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BX_HomeFail, 0x1012003E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CX_HomeFail, 0x1012003F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DX_HomeFail, 0x10120040);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EX_HomeFail, 0x10120041);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XY_HomeFail, 0x10120042);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YY_HomeFail, 0x10120043);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZY_HomeFail, 0x10120044);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AY_HomeFail, 0x10120045);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BY_HomeFail, 0x10120046);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CY_HomeFail, 0x10120047);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DY_HomeFail, 0x10120048);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EY_HomeFail, 0x10120049);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XZ_HomeFail, 0x1012004A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YZ_HomeFail, 0x1012004B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZZ_HomeFail, 0x1012004C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AZ_HomeFail, 0x1012004D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BZ_HomeFail, 0x1012004E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CZ_HomeFail, 0x1012004F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DZ_HomeFail, 0x10120050);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EZ_HomeFail, 0x10120051);

        // JOG失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_X_JogFail, 0x10120062);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Y_JogFail, 0x10120063);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Z_JogFail, 0x10120064);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_A_JogFail, 0x10120065);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_B_JogFail, 0x10120066);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_C_JogFail, 0x10120067);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_D_JogFail, 0x10120068);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_E_JogFail, 0x10120069);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XX_JogFail, 0x1012006A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YX_JogFail, 0x1012006B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZX_JogFail, 0x1012006C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AX_JogFail, 0x1012006D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BX_JogFail, 0x1012006E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CX_JogFail, 0x1012006F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DX_JogFail, 0x10120070);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EX_JogFail, 0x10120071);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XY_JogFail, 0x10120072);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YY_JogFail, 0x10120073);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZY_JogFail, 0x10120074);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AY_JogFail, 0x10120075);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BY_JogFail, 0x10120076);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CY_JogFail, 0x10120077);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DY_JogFail, 0x10120078);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EY_JogFail, 0x10120079);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XZ_JogFail, 0x1012007A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YZ_JogFail, 0x1012007B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZZ_JogFail, 0x1012007C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AZ_JogFail, 0x1012007D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BZ_JogFail, 0x1012007E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CZ_JogFail, 0x1012007F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DZ_JogFail, 0x10120080);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EZ_JogFail, 0x10120081);

        // 目标位置超限
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_X_Over_Limit, 0x10120092);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Y_Over_Limit, 0x10120093);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Z_Over_Limit, 0x10120094);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_A_Over_Limit, 0x10120095);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_B_Over_Limit, 0x10120096);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_C_Over_Limit, 0x10120097);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_D_Over_Limit, 0x10120098);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_E_Over_Limit, 0x10120099);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XX_Over_Limit, 0x1012009A);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YX_Over_Limit, 0x1012009B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZX_Over_Limit, 0x1012009C);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AX_Over_Limit, 0x1012009D);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BX_Over_Limit, 0x1012009E);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CX_Over_Limit, 0x1012009F);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DX_Over_Limit, 0x101200A0);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EX_Over_Limit, 0x101200A1);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XY_Over_Limit, 0x101200A2);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YY_Over_Limit, 0x101200A3);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZY_Over_Limit, 0x101200A4);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AY_Over_Limit, 0x101200A5);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BY_Over_Limit, 0x101200A6);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CY_Over_Limit, 0x101200A7);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DY_Over_Limit, 0x101200A8);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EY_Over_Limit, 0x101200A9);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XZ_Over_Limit, 0x101200AA);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YZ_Over_Limit, 0x101200AB);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZZ_Over_Limit, 0x101200AC);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AZ_Over_Limit, 0x101200AD);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BZ_Over_Limit, 0x101200AE);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CZ_Over_Limit, 0x101200BF);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DZ_Over_Limit, 0x101200B0);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EZ_Over_Limit, 0x101200B1);

        // 移动超时
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_X_MoveTimeOut, 0x101200C2);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Y_MoveTimeOut, 0x101200C3);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Z_MoveTimeOut, 0x101200C4);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_A_MoveTimeOut, 0x101200C5);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_B_MoveTimeOut, 0x101200C6);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_C_MoveTimeOut, 0x101200C7);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_D_MoveTimeOut, 0x101200C8);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_E_MoveTimeOut, 0x101200C9);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XX_MoveTimeOut, 0x101200CA);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YX_MoveTimeOut, 0x101200CB);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZX_MoveTimeOut, 0x101200CC);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AX_MoveTimeOut, 0x101200CD);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BX_MoveTimeOut, 0x101200CE);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CX_MoveTimeOut, 0x101200CF);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DX_MoveTimeOut, 0x101200D0);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EX_MoveTimeOut, 0x101200D1);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XY_MoveTimeOut, 0x101200D2);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YY_MoveTimeOut, 0x101200D3);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZY_MoveTimeOut, 0x101200D4);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AY_MoveTimeOut, 0x101200D5);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BY_MoveTimeOut, 0x101200D6);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CY_MoveTimeOut, 0x101200D7);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DY_MoveTimeOut, 0x101200D8);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EY_MoveTimeOut, 0x101200D9);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_XZ_MoveTimeOut, 0x101200DA);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_YZ_MoveTimeOut, 0x101200DB);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ZZ_MoveTimeOut, 0x101200DC);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_AZ_MoveTimeOut, 0x101200DD);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_BZ_MoveTimeOut, 0x101200DE);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_CZ_MoveTimeOut, 0x101200DF);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_DZ_MoveTimeOut, 0x101200E0);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_EZ_MoveTimeOut, 0x101200E1);

        // 协议参数格式错误
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ErrorFormat, 0x10120100);

        // 指令执行失败,捕获到异常
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Exception, 0x10120101);

        // 未找到轴
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_NotFound_Axis, 0x10120102);

        // 未找到IO
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_NotFound_IO, 0x10120103);

        // 流程正在运行
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Process_Running, 0x10120104);

        // G代码解析失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Gcode_Error, 0x10120105);

        // 控制卡连接失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Connect_Fail, 0x10120106);

        // 切换配方失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ChangeRecipe_Fail, 0x10120107);

        // 初始化NC执行失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_InitialNC_Fail, 0x10120108);

        // 插补执行失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Interpolation_Error, 0x10120109);

        // 控制卡离线
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_MotionCard_Outline, 0x1012010A);

        // 111~305用于脚本,对应mode[0,500]
        // [0,49]   ->3DAOI
        // [50,99]  ->V2000
        /**---------------------------------3DAOI--------------------------------- */
        // 打断流程失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Interrupt_Rail1_Process_Fail, 0x10120111);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Interrupt_Rail2_Process_Fail, 0x10120112);

        // 初始化检查失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Initial_Rail1_Check_Fail, 0x10120113);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Initial_Rail2_Check_Fail, 0x10120114);

        // 传输模式失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail1_Trans_Fail, 0x10120115);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail2_Trans_Fail, 0x10120116);

        // 上料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail1_Load_Fail, 0x10120117);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail2_Load_Fail, 0x10120118);

        // 下料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail1_UnLoad_Fail, 0x10120119);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Rail2_UnLoad_Fail, 0x1012011A);

        /**---------------------------------V2000--------------------------------- */

        // 初始化检查失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Initial_Rail1_Check_Fail, 0x10120143);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Initial_Rail2_Check_Fail, 0x10120144);

        // 2D上料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail1_Load2D_Fail, 0x10120145);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail2_Load2D_Fail, 0x10120146);

        // 2D下料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail1_UnLoad2D_Fail, 0x10120147);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail2_UnLoad2D_Fail, 0x10120148);

        // 3D上料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail1_Load3D_Fail, 0x10120149);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail2_Load3D_Fail, 0x1012014A);

        // 3D下料失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail1_UnLoad3D_Fail, 0x1012014B);
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_V2000_Rail2_UnLoad3D_Fail, 0x1012014C);

        // 当前模式正在执行中
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Scripts_Mode_Excuting, 0x10120400);

        // 同轨道上下料不能同时执行
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Scripts_NotAlowed, 0x10120401);

        // 轴SRVON操作失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_SRVON_Fail, 0x10120402);

        // 脚本执行失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_Script_Execute_Fail, 0x10120403);

        // 解析配置文件失败
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_ParseConfigureFile_Fail, 0x10120404);

        // 重新拍照条件不满足
        DEFINE_Motion_ERROR_CODE(E_CONTROL_Motion_REPHOTO_Fail, 0x10120405);


        static std::unordered_map<AOIErrorCode, ErrorInfo>& GetMotionErrorMap()
        {
            return error_motion_map;
        }

            // 根据错误码字符信息查找错误码
            static int GetErrorCodeFromErrorString(std::string err)
            {
                for (const auto& pair : error_motion_map) 
                {
                    AOIErrorCode code = pair.first;
                    ErrorInfo info = pair.second;
                    if(info.err_string == err)
                    {
                        return code;
                    }
                }
                return E_CONTROL_Motion_UNKNOWN;
            }

            // 根据错误码字符信息查找错误描述
            static std::string GetErrorDescriptionFromErrorString(std::string err)
            {
                for (const auto& pair : error_motion_map)
                {
                    ErrorInfo info = pair.second;
                    if (info.err_string == err)
                    {
                        return info.err_description;
                    }
                }
                return "";
            }
        private:
            static inline std::unordered_map<AOIErrorCode, ErrorInfo> error_motion_map =
            {
                {E_CONTROL_Motion_NoError, {"E_CONTROL_Motion_NoError","没有错误 ","Motion",""}},
                {E_CONTROL_Motion_UNKNOWN, {"E_CONTROL_Motion_UNKNOWN","Motion未知错误 ","Motion",""}},

            {E_CONTROL_Motion_X_MoveFail, {"E_CONTROL_Motion_X_MoveFail","X轴移动失败","Motion",""}},
            {E_CONTROL_Motion_Y_MoveFail, {"E_CONTROL_Motion_Y_MoveFail","Y轴移动失败","Motion",""}},
            {E_CONTROL_Motion_Z_MoveFail, {"E_CONTROL_Motion_Z_MoveFail","Z轴移动失败","Motion",""}},
            {E_CONTROL_Motion_A_MoveFail, {"E_CONTROL_Motion_A_MoveFail","A轴移动失败","Motion",""}},
            {E_CONTROL_Motion_B_MoveFail, {"E_CONTROL_Motion_B_MoveFail","B轴移动失败","Motion",""}},
            {E_CONTROL_Motion_C_MoveFail, {"E_CONTROL_Motion_C_MoveFail","C轴移动失败","Motion",""}},
            {E_CONTROL_Motion_D_MoveFail, {"E_CONTROL_Motion_D_MoveFail","D轴移动失败","Motion",""}},
            {E_CONTROL_Motion_E_MoveFail, {"E_CONTROL_Motion_E_MoveFail","E轴移动失败","Motion",""}},
            {E_CONTROL_Motion_XX_MoveFail, {"E_CONTROL_Motion_XX_MoveFail","XX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_YX_MoveFail, {"E_CONTROL_Motion_YX_MoveFail","YX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_ZX_MoveFail, {"E_CONTROL_Motion_ZX_MoveFail","ZX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_AX_MoveFail, {"E_CONTROL_Motion_AX_MoveFail","AX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_BX_MoveFail, {"E_CONTROL_Motion_BX_MoveFail","BX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_CX_MoveFail, {"E_CONTROL_Motion_CX_MoveFail","CX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_DX_MoveFail, {"E_CONTROL_Motion_DX_MoveFail","DX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_EX_MoveFail, {"E_CONTROL_Motion_EX_MoveFail","EX轴移动失败","Motion",""}},
            {E_CONTROL_Motion_XY_MoveFail, {"E_CONTROL_Motion_XY_MoveFail","XY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_YY_MoveFail, {"E_CONTROL_Motion_YY_MoveFail","YY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_ZY_MoveFail, {"E_CONTROL_Motion_ZY_MoveFail","ZY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_AY_MoveFail, {"E_CONTROL_Motion_AY_MoveFail","AY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_BY_MoveFail, {"E_CONTROL_Motion_BY_MoveFail","BY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_CY_MoveFail, {"E_CONTROL_Motion_CY_MoveFail","CY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_DY_MoveFail, {"E_CONTROL_Motion_DY_MoveFail","DY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_EY_MoveFail, {"E_CONTROL_Motion_EY_MoveFail","EY轴移动失败","Motion",""}},
            {E_CONTROL_Motion_XZ_MoveFail, {"E_CONTROL_Motion_XZ_MoveFail","XZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_YZ_MoveFail, {"E_CONTROL_Motion_YZ_MoveFail","YZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_ZZ_MoveFail, {"E_CONTROL_Motion_ZZ_MoveFail","ZZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_AZ_MoveFail, {"E_CONTROL_Motion_AZ_MoveFail","AZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_BZ_MoveFail, {"E_CONTROL_Motion_BZ_MoveFail","BZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_CZ_MoveFail, {"E_CONTROL_Motion_CZ_MoveFail","CZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_DZ_MoveFail, {"E_CONTROL_Motion_DZ_MoveFail","DZ轴移动失败","Motion",""}},
            {E_CONTROL_Motion_EZ_MoveFail, {"E_CONTROL_Motion_EZ_MoveFail","EZ轴移动失败","Motion",""}},

            {E_CONTROL_Motion_X_HomeFail, {"E_CONTROL_Motion_X_HomeFail","X轴回零失败","Motion",""}},
            {E_CONTROL_Motion_Y_HomeFail, {"E_CONTROL_Motion_Y_HomeFail","Y轴回零失败","Motion",""}},
            {E_CONTROL_Motion_Z_HomeFail, {"E_CONTROL_Motion_Z_HomeFail","Z轴回零失败","Motion",""}},
            {E_CONTROL_Motion_A_HomeFail, {"E_CONTROL_Motion_A_HomeFail","A轴回零失败","Motion",""}},
            {E_CONTROL_Motion_B_HomeFail, {"E_CONTROL_Motion_B_HomeFail","B轴回零失败","Motion",""}},
            {E_CONTROL_Motion_C_HomeFail, {"E_CONTROL_Motion_C_HomeFail","C轴回零失败","Motion",""}},
            {E_CONTROL_Motion_D_HomeFail, {"E_CONTROL_Motion_D_HomeFail","D轴回零失败","Motion",""}},
            {E_CONTROL_Motion_E_HomeFail, {"E_CONTROL_Motion_E_HomeFail","E轴回零失败","Motion",""}},
            {E_CONTROL_Motion_XX_HomeFail, {"E_CONTROL_Motion_XX_HomeFail","XX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_YX_HomeFail, {"E_CONTROL_Motion_YX_HomeFail","YX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_ZX_HomeFail, {"E_CONTROL_Motion_ZX_HomeFail","ZX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_AX_HomeFail, {"E_CONTROL_Motion_AX_HomeFail","AX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_BX_HomeFail, {"E_CONTROL_Motion_BX_HomeFail","BX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_CX_HomeFail, {"E_CONTROL_Motion_CX_HomeFail","CX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_DX_HomeFail, {"E_CONTROL_Motion_DX_HomeFail","DX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_EX_HomeFail, {"E_CONTROL_Motion_EX_HomeFail","EX轴回零失败","Motion",""}},
            {E_CONTROL_Motion_XY_HomeFail, {"E_CONTROL_Motion_XY_HomeFail","XY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_YY_HomeFail, {"E_CONTROL_Motion_YY_HomeFail","YY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_ZY_HomeFail, {"E_CONTROL_Motion_ZY_HomeFail","ZY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_AY_HomeFail, {"E_CONTROL_Motion_AY_HomeFail","AY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_BY_HomeFail, {"E_CONTROL_Motion_BY_HomeFail","BY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_CY_HomeFail, {"E_CONTROL_Motion_CY_HomeFail","CY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_DY_HomeFail, {"E_CONTROL_Motion_DY_HomeFail","DY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_EY_HomeFail, {"E_CONTROL_Motion_EY_HomeFail","EY轴回零失败","Motion",""}},
            {E_CONTROL_Motion_XZ_HomeFail, {"E_CONTROL_Motion_XZ_HomeFail","XZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_YZ_HomeFail, {"E_CONTROL_Motion_YZ_HomeFail","YZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_ZZ_HomeFail, {"E_CONTROL_Motion_ZZ_HomeFail","ZZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_AZ_HomeFail, {"E_CONTROL_Motion_AZ_HomeFail","AZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_BZ_HomeFail, {"E_CONTROL_Motion_BZ_HomeFail","BZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_CZ_HomeFail, {"E_CONTROL_Motion_CZ_HomeFail","CZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_DZ_HomeFail, {"E_CONTROL_Motion_DZ_HomeFail","DZ轴回零失败","Motion",""}},
            {E_CONTROL_Motion_EZ_HomeFail, {"E_CONTROL_Motion_EZ_HomeFail","EZ轴回零失败","Motion",""}},

            {E_CONTROL_Motion_X_Over_Limit, {"E_CONTROL_Motion_X_Over_Limit","X轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_Y_Over_Limit, {"E_CONTROL_Motion_Y_Over_Limit","Y轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_Z_Over_Limit, {"E_CONTROL_Motion_Z_Over_Limit","Z轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_A_Over_Limit, {"E_CONTROL_Motion_A_Over_Limit","A轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_B_Over_Limit, {"E_CONTROL_Motion_B_Over_Limit","B轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_C_Over_Limit, {"E_CONTROL_Motion_C_Over_Limit","C轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_D_Over_Limit, {"E_CONTROL_Motion_D_Over_Limit","D轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_E_Over_Limit, {"E_CONTROL_Motion_E_Over_Limit","E轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_XX_Over_Limit, {"E_CONTROL_Motion_XX_Over_Limit","XX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_YX_Over_Limit, {"E_CONTROL_Motion_YX_Over_Limit","YX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_ZX_Over_Limit, {"E_CONTROL_Motion_ZX_Over_Limit","ZX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_AX_Over_Limit, {"E_CONTROL_Motion_AX_Over_Limit","AX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_BX_Over_Limit, {"E_CONTROL_Motion_BX_Over_Limit","BX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_CX_Over_Limit, {"E_CONTROL_Motion_CX_Over_Limit","CX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_DX_Over_Limit, {"E_CONTROL_Motion_DX_Over_Limit","DX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_EX_Over_Limit, {"E_CONTROL_Motion_EX_Over_Limit","EX轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_XY_Over_Limit, {"E_CONTROL_Motion_XY_Over_Limit","XY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_YY_Over_Limit, {"E_CONTROL_Motion_YY_Over_Limit","YY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_ZY_Over_Limit, {"E_CONTROL_Motion_ZY_Over_Limit","ZY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_AY_Over_Limit, {"E_CONTROL_Motion_AY_Over_Limit","AY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_BY_Over_Limit, {"E_CONTROL_Motion_BY_Over_Limit","BY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_CY_Over_Limit, {"E_CONTROL_Motion_CY_Over_Limit","CY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_DY_Over_Limit, {"E_CONTROL_Motion_DY_Over_Limit","DY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_EY_Over_Limit, {"E_CONTROL_Motion_EY_Over_Limit","EY轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_XZ_Over_Limit, {"E_CONTROL_Motion_XZ_Over_Limit","XZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_YZ_Over_Limit, {"E_CONTROL_Motion_YZ_Over_Limit","YZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_ZZ_Over_Limit, {"E_CONTROL_Motion_ZZ_Over_Limit","ZZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_AZ_Over_Limit, {"E_CONTROL_Motion_AZ_Over_Limit","AZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_BZ_Over_Limit, {"E_CONTROL_Motion_BZ_Over_Limit","BZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_CZ_Over_Limit, {"E_CONTROL_Motion_CZ_Over_Limit","CZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_DZ_Over_Limit, {"E_CONTROL_Motion_DZ_Over_Limit","DZ轴目标位置超限","Motion",""}},
            {E_CONTROL_Motion_EZ_Over_Limit, {"E_CONTROL_Motion_EZ_Over_Limit","EZ轴目标位置超限","Motion",""}},

            {E_CONTROL_Motion_ErrorFormat, {"E_CONTROL_Motion_ErrorFormat","协议格式错误 ","Motion",""}},
            {E_CONTROL_Motion_Exception, {"E_CONTROL_Motion_Exception","捕获到异常 ","Motion",""}},
            {E_CONTROL_Motion_NotFound_Axis, {"E_CONTROL_Motion_NotFound_Axis","未找到轴 ","Motion",""}},
            {E_CONTROL_Motion_NotFound_IO, {"E_CONTROL_Motion_NotFound_IO","未找到IO ","Motion",""}},
            {E_CONTROL_Motion_Process_Running, {"E_CONTROL_Motion_Process_Running","流程正在运行中 ","Motion",""}},
            {E_CONTROL_Motion_Gcode_Error, {"E_CONTROL_Motion_Gcode_Error","G代码解析错误 ","Motion",""}},
            {E_CONTROL_Motion_Connect_Fail, {"E_CONTROL_Motion_Connect_Fail","控制卡连接失败 ","Motion",""}},
            {E_CONTROL_Motion_ChangeRecipe_Fail, {"E_CONTROL_Motion_ChangeRecipe_Fail","切换配方失败 ","Motion",""}},
            {E_CONTROL_Motion_InitialNC_Fail, {"E_CONTROL_Motion_InitialNC_Fail","初始化NC执行失败 ","Motion",""}},
            {E_CONTROL_Motion_Interpolation_Error, {"E_CONTROL_Motion_Interpolation_Error","插补执行失败 ","Motion",""}},
            {E_CONTROL_Motion_MotionCard_Outline, {"E_CONTROL_Motion_MotionCard_Outline","控制卡离线","Motion",""}},



            {E_CONTROL_Motion_X_MoveTimeOut, {"E_CONTROL_Motion_X_MoveTimeOut","X轴移动超时","Motion",""}},
            {E_CONTROL_Motion_Y_MoveTimeOut, {"E_CONTROL_Motion_Y_MoveTimeOut","Y轴移动超时","Motion",""}},
            {E_CONTROL_Motion_Z_MoveTimeOut, {"E_CONTROL_Motion_Z_MoveTimeOut","Z轴移动超时","Motion",""}},
            {E_CONTROL_Motion_A_MoveTimeOut, {"E_CONTROL_Motion_A_MoveTimeOut","A轴移动超时","Motion",""}},
            {E_CONTROL_Motion_B_MoveTimeOut, {"E_CONTROL_Motion_B_MoveTimeOut","B轴移动超时","Motion",""}},
            {E_CONTROL_Motion_C_MoveTimeOut, {"E_CONTROL_Motion_C_MoveTimeOut","C轴移动超时","Motion",""}},
            {E_CONTROL_Motion_D_MoveTimeOut, {"E_CONTROL_Motion_D_MoveTimeOut","D轴移动超时","Motion",""}},
            {E_CONTROL_Motion_E_MoveTimeOut, {"E_CONTROL_Motion_E_MoveTimeOut","E轴移动超时","Motion",""}},
            {E_CONTROL_Motion_XX_MoveTimeOut, {"E_CONTROL_Motion_XX_MoveTimeOut","XX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_YX_MoveTimeOut, {"E_CONTROL_Motion_YX_MoveTimeOut","YX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_ZX_MoveTimeOut, {"E_CONTROL_Motion_ZX_MoveTimeOut","ZX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_AX_MoveTimeOut, {"E_CONTROL_Motion_AX_MoveTimeOut","AX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_BX_MoveTimeOut, {"E_CONTROL_Motion_BX_MoveTimeOut","BX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_CX_MoveTimeOut, {"E_CONTROL_Motion_CX_MoveTimeOut","CX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_DX_MoveTimeOut, {"E_CONTROL_Motion_DX_MoveTimeOut","DX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_EX_MoveTimeOut, {"E_CONTROL_Motion_EX_MoveTimeOut","EX轴移动超时","Motion",""}},
            {E_CONTROL_Motion_XY_MoveTimeOut, {"E_CONTROL_Motion_XY_MoveTimeOut","XY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_YY_MoveTimeOut, {"E_CONTROL_Motion_YY_MoveTimeOut","YY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_ZY_MoveTimeOut, {"E_CONTROL_Motion_ZY_MoveTimeOut","ZY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_AY_MoveTimeOut, {"E_CONTROL_Motion_AY_MoveTimeOut","AY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_BY_MoveTimeOut, {"E_CONTROL_Motion_BY_MoveTimeOut","BY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_CY_MoveTimeOut, {"E_CONTROL_Motion_CY_MoveTimeOut","CY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_DY_MoveTimeOut, {"E_CONTROL_Motion_DY_MoveTimeOut","DY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_EY_MoveTimeOut, {"E_CONTROL_Motion_EY_MoveTimeOut","EY轴移动超时","Motion",""}},
            {E_CONTROL_Motion_XZ_MoveTimeOut, {"E_CONTROL_Motion_XZ_MoveTimeOut","XZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_YZ_MoveTimeOut, {"E_CONTROL_Motion_YZ_MoveTimeOut","YZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_ZZ_MoveTimeOut, {"E_CONTROL_Motion_ZZ_MoveTimeOut","ZZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_AZ_MoveTimeOut, {"E_CONTROL_Motion_AZ_MoveTimeOut","AZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_BZ_MoveTimeOut, {"E_CONTROL_Motion_BZ_MoveTimeOut","BZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_CZ_MoveTimeOut, {"E_CONTROL_Motion_CZ_MoveTimeOut","CZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_DZ_MoveTimeOut, {"E_CONTROL_Motion_DZ_MoveTimeOut","DZ轴移动超时","Motion",""}},
            {E_CONTROL_Motion_EZ_MoveTimeOut, {"E_CONTROL_Motion_EZ_MoveTimeOut","EZ轴移动超时","Motion",""}},


            {E_CONTROL_Motion_Initial_Rail1_Check_Fail, {"E_CONTROL_Motion_Initial_Rail1_Check_Fail","轨道1初始化检查失败","Motion",""}},
            {E_CONTROL_Motion_Initial_Rail2_Check_Fail, {"E_CONTROL_Motion_Initial_Rail2_Check_Fail","轨道2初始化检查失败","Motion",""}},
            {E_CONTROL_Motion_Interrupt_Rail1_Process_Fail, {"E_CONTROL_Motion_Interrupt_Rail1_Process_Fail","轨道1打断流程失败","Motion",""}},
            {E_CONTROL_Motion_Interrupt_Rail2_Process_Fail, {"E_CONTROL_Motion_Interrupt_Rail2_Process_Fail","轨道2打断流程失败","Motion",""}},
            {E_CONTROL_Motion_Rail1_Trans_Fail, {"E_CONTROL_Motion_Rail1_Trans_Fail","轨道1传输模式失败","Motion",""}},
            {E_CONTROL_Motion_Rail2_Trans_Fail, {"E_CONTROL_Motion_Rail2_Trans_Fail","轨道2传输模式失败","Motion",""}},
            {E_CONTROL_Motion_Rail1_Load_Fail, {"E_CONTROL_Motion_Rail1_Load_Fail","轨道1上料失败","Motion",""}},
            {E_CONTROL_Motion_Rail2_Load_Fail, {"E_CONTROL_Motion_Rail2_Load_Fail","轨道2上料失败","Motion",""}},
            {E_CONTROL_Motion_Rail1_UnLoad_Fail, {"E_CONTROL_Motion_Rail1_UnLoad_Fail","轨道1下料失败","Motion",""}},
            {E_CONTROL_Motion_Rail2_UnLoad_Fail, {"E_CONTROL_Motion_Rail2_UnLoad_Fail","轨道2下料失败","Motion",""}},

            {E_CONTROL_Motion_V2000_Initial_Rail1_Check_Fail, {"E_CONTROL_Motion_V2000_Initial_Rail1_Check_Fail","V2000轨道1初始化检查失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Initial_Rail2_Check_Fail, {"E_CONTROL_Motion_V2000_Initial_Rail2_Check_Fail","V2000轨道2初始化检查失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail1_Load2D_Fail, {"E_CONTROL_Motion_V2000_Rail1_Load2D_Fail","V2000轨道1 2D上料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail2_Load2D_Fail, {"E_CONTROL_Motion_V2000_Rail2_Load2D_Fail","V2000轨道2 2D上料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail1_UnLoad2D_Fail, {"E_CONTROL_Motion_V2000_Rail1_UnLoad2D_Fail","V2000轨道1 2D下料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail2_UnLoad2D_Fail, {"E_CONTROL_Motion_V2000_Rail2_UnLoad2D_Fail","V2000轨道2 2D下料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail1_Load3D_Fail, {"E_CONTROL_Motion_V2000_Rail1_Load3D_Fail","V2000轨道1 3D上料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail2_Load3D_Fail, {"E_CONTROL_Motion_V2000_Rail2_Load3D_Fail","V2000轨道2 3D上料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail1_UnLoad3D_Fail, {"E_CONTROL_Motion_V2000_Rail1_UnLoad3D_Fail","V2000轨道1 3D下料失败","Motion",""}},
            {E_CONTROL_Motion_V2000_Rail2_UnLoad3D_Fail, {"E_CONTROL_Motion_V2000_Rail2_UnLoad3D_Fail","V2000轨道2 3D下料失败","Motion",""}},

            {E_CONTROL_Motion_Scripts_Mode_Excuting, {"E_CONTROL_Motion_Scripts_Mode_Excuting","当前脚本模式正在执行中","Motion",""}},
            {E_CONTROL_Motion_Scripts_NotAlowed, {"E_CONTROL_Motion_Scripts_NotAlowed","同轨道上下料不能同时执行","Motion",""}},

            {E_CONTROL_Motion_SRVON_Fail, {"E_CONTROL_Motion_SRVON_Fail","轴SRVON操作失败","Motion",""}},
            {E_CONTROL_Motion_Script_Execute_Fail, {"E_CONTROL_Motion_Script_Execute_Fail","脚本执行错误","Motion",""}},
            {E_CONTROL_Motion_ParseConfigureFile_Fail, {"E_CONTROL_Motion_ParseConfigureFile_Fail","解析配置文件失败","Motion",""}},
            {E_CONTROL_Motion_REPHOTO_Fail, {"E_CONTROL_Motion_REPHOTO_Fail","重新拍照的条件不满足,只有自动流程在等待PHOTOOK过程中才可以触发","Motion",""}},

            {E_CONTROL_Motion_X_JogFail, {"E_CONTROL_Motion_X_JogFail","X轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_Y_JogFail, {"E_CONTROL_Motion_Y_JogFail","Y轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_Z_JogFail, {"E_CONTROL_Motion_Z_JogFail","Z轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_A_JogFail, {"E_CONTROL_Motion_A_JogFail","A轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_B_JogFail, {"E_CONTROL_Motion_B_JogFail","B轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_C_JogFail, {"E_CONTROL_Motion_C_JogFail","C轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_D_JogFail, {"E_CONTROL_Motion_D_JogFail","D轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_E_JogFail, {"E_CONTROL_Motion_E_JogFail","E轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_XX_JogFail, {"E_CONTROL_Motion_XX_JogFail","XX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_YX_JogFail, {"E_CONTROL_Motion_YX_JogFail","YX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_ZX_JogFail, {"E_CONTROL_Motion_ZX_JogFail","ZX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_AX_JogFail, {"E_CONTROL_Motion_AX_JogFail","AX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_BX_JogFail, {"E_CONTROL_Motion_BX_JogFail","BX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_CX_JogFail, {"E_CONTROL_Motion_CX_JogFail","CX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_DX_JogFail, {"E_CONTROL_Motion_DX_JogFail","DX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_EX_JogFail, {"E_CONTROL_Motion_EX_JogFail","EX轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_XY_JogFail, {"E_CONTROL_Motion_XY_JogFail","XY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_YY_JogFail, {"E_CONTROL_Motion_YY_JogFail","YY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_ZY_JogFail, {"E_CONTROL_Motion_ZY_JogFail","ZY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_AY_JogFail, {"E_CONTROL_Motion_AY_JogFail","AY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_BY_JogFail, {"E_CONTROL_Motion_BY_JogFail","BY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_CY_JogFail, {"E_CONTROL_Motion_CY_JogFail","CY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_DY_JogFail, {"E_CONTROL_Motion_DY_JogFail","DY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_EY_JogFail, {"E_CONTROL_Motion_EY_JogFail","EY轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_XZ_JogFail, {"E_CONTROL_Motion_XZ_JogFail","XZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_YZ_JogFail, {"E_CONTROL_Motion_YZ_JogFail","YZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_ZZ_JogFail, {"E_CONTROL_Motion_ZZ_JogFail","ZZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_AZ_JogFail, {"E_CONTROL_Motion_AZ_JogFail","AZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_BZ_JogFail, {"E_CONTROL_Motion_BZ_JogFail","BZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_CZ_JogFail, {"E_CONTROL_Motion_CZ_JogFail","CZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_DZ_JogFail, {"E_CONTROL_Motion_DZ_JogFail","DZ轴JOG失败","Motion",""}},
            {E_CONTROL_Motion_EZ_JogFail, {"E_CONTROL_Motion_EZ_JogFail","EZ轴JOG失败","Motion",""}},

        };
    };



    struct ErrorImplData;


    class JRS_AOI_PLUGIN_API ErrorHandler
    {
    public:
        ErrorHandler();
        ~ErrorHandler();

        /*** @brief 错误输出模式 zhangyuyu 2024.1.23*/
        enum class FlushStrategy
        {
            FLUSHNONE = 0x0000, /**< 无处理 */
            FLUSHTOMESSAGEBOX = 0x0001, /**< 弹窗显示 */
            FLUSHTOERRORVIEW = 0x0002, /**< 输出到错误显示界面 */
            FLUSHNORMAL = FLUSHTOMESSAGEBOX | FLUSHTOERRORVIEW
        };

        /**
         * @fun PushStackError
         * @brief 错误信息推送到错误收集器中
         * @param err 错误码
         * @param what 对于错误的详细描述
         * @date 2024.1.23
         * <AUTHOR>
         */
        void PushStackError(const AOIErrorCode err, const std::string& what);

        /**
         * @fun FlushStackError
         * @brief
         * @param straegy
         * @date 2024.1.23
         * <AUTHOR>
         */
        void FlushStackError(const std::string& what, FlushStrategy straegy = FlushStrategy::FLUSHNORMAL);

        /*** 生成错误，并触发回调 zhangyuyu 2024.1.23*/
        using ErrorHandleGenerateCallBack = std::function<void(const ErrorInfo info)>;

        /*** @brief 刷新处理缓存区域所有错误 zhangyuyu 2024.1.23*/
        using ErrorHandleFlushCallBack = std::function<void(const std::string, FlushStrategy flush_type_)>;

        /**
         * @fun SetErrorGenerateCallBack
         * @brief 设置错误处理回调函数
         * @param cb 回调函数
         * @date 2024.1.23
         * <AUTHOR>
         */
        void SetErrorGenerateCallBack(ErrorHandleGenerateCallBack cb_);

        /**
         * @fun SetErrorFlushCallBack
         * @brief 设置错误处理回调函数
         * @param cb_
         * @date 2024.1.23
         * <AUTHOR>
         */
        void SetErrorFlushCallBack(ErrorHandleFlushCallBack cb_);

        ErrorInfo GetErrorInfo(const AOIErrorCode err_);
    private:
        ErrorImplData* p_data;

    };
    using ErrorHandlerPtr = std::shared_ptr<ErrorHandler>;

}
#endif // !__ERRORHANDLER_H__
