﻿#include "padmanager.h"
#include "graphicsmanager.h"
#include "padgroup.h"
PadManager::Pad<PERSON>anager(GraphicsManager* const graphics_manager_)
    :_graphics_manager(graphics_manager_), _component_pad_group_map({}) //,_name_map_pad_groups({})
{

}

PadManager::~PadManager()
{
}

std::string PadManager::GetNewPadGroupName(const GraphicsID& graphics_id_)
{
    int group_max_id = 0;
    const auto& _id_map_pad_group = _component_pad_group_map[graphics_id_];
    for (const auto& [id_, group_] : _id_map_pad_group)
    {
        if (id_ > group_max_id)
        {
            group_max_id = id_;
        }
    }
    return JoinPadGroupName(group_max_id + 1);
}

int PadManager::UpdatePadGroupName(const std::string& group_name_, std::shared_ptr<GraphicsAbstract> gh_)
{
    auto gh_group = std::dynamic_pointer_cast<PadGraphicsGroup>(gh_);

    if (gh_group->GetParentGraphicsPtr().expired())
    {
        //父类指针
        return -1;
    }
    auto father_graphics_ptr = gh_group->GetParentGraphicsPtr().lock();
    const auto& father_graphics_id = father_graphics_ptr->GetId();

    gh_group->SetPadGroupID(GetNewPadGroupID(father_graphics_id, group_name_));
    DeletePadGroup(gh_);

    auto id = SplitPadGroupName(group_name_);

    _component_pad_group_map[father_graphics_id][id].push_back(gh_);
    //_name_map_pad_groups[id].push_back(gh_);

    return 0;
}

int PadManager::GetNewPadGroupID(const GraphicsID& graphics_id_, const std::string& group_name_)
{
    auto group_id = SplitPadGroupName(group_name_);
    if (group_id == -1)
    {
        return -1;  //无效
    }
    int max_pad_id = 0;
    const auto& pad_groups = _component_pad_group_map[graphics_id_][group_id];//_name_map_pad_groups[group_id];
    for (const auto& pad_group : pad_groups)
    {
        auto pad_group_ptr = std::dynamic_pointer_cast<PadGraphicsGroup>(pad_group.lock());
        if (pad_group_ptr && pad_group_ptr->GetPadGroupID() > max_pad_id)
        {
            max_pad_id = pad_group_ptr->GetPadGroupID();
        }
    }

    return max_pad_id + 1;
}

int PadManager::AddPadGroup(std::shared_ptr<GraphicsAbstract> graphics_, const std::string& pad_group_)
{
    auto pad_group_gh = std::dynamic_pointer_cast<PadGraphicsGroup>(graphics_);
    if (!pad_group_gh)
    {
        return -1;
    }

    std::string pad_group_name = pad_group_.empty() ? pad_group_gh->GetPadGroupName() : pad_group_;
    int group_id = SplitPadGroupName(pad_group_name);
    if (pad_group_gh->GetParentGraphicsPtr().expired())
    {
        //父类指针
        return -1;
    }
    auto father_graphics_ptr = pad_group_gh->GetParentGraphicsPtr().lock();

    const auto& father_graphics_id = father_graphics_ptr->GetId();

    // 如果 group_id 仍然无效，则创建新的 PadGroupName
    if (group_id == -1)
    {
        pad_group_name = GetNewPadGroupName(father_graphics_id);
        pad_group_gh->SetPadGroupName(pad_group_name);
        group_id = SplitPadGroupName(pad_group_name);
        if (group_id == -1)
        {
            return -2;  // 新的分组 ID 仍然无效
        }
    }
    else
    {
        pad_group_gh->SetPadGroupName(pad_group_name);
    }
    _component_pad_group_map[father_graphics_id][group_id].emplace_back(graphics_);
    //_name_map_pad_groups[group_id].emplace_back(graphics_);

    // 如果 PadGroupID 仍然未设置，则生成新的 PadGroupID  
    if (pad_group_gh->GetPadGroupID() == -1)
    {
        pad_group_gh->SetPadGroupID(GetNewPadGroupID(father_graphics_id, pad_group_name));
    }

    return 0;
}

std::vector<std::weak_ptr<GraphicsAbstract>> PadManager::GetPadGroups(const GraphicsID& graphics_id_, const std::string& group_name_)
{
    int  group_id = SplitPadGroupName(group_name_);
    if (group_id == -1)
    {
        return std::vector<std::weak_ptr<GraphicsAbstract>>();
    }
    return _component_pad_group_map[graphics_id_][group_id];
}

int PadManager::DeletePadGroup(std::shared_ptr<GraphicsAbstract> graphics)
{
    auto pad_group_gh = std::dynamic_pointer_cast<PadGraphicsGroup>(graphics);
    if (!pad_group_gh)
    {
        return -1;
    }

    int group_id = SplitPadGroupName(pad_group_gh->GetPadGroupName());
    if (group_id == -1)
    {
        return -1;
    }
    if (pad_group_gh->GetParentGraphicsPtr().expired())
    {
        //父类指针
        return -1;
    }
    auto father_graphics_ptr = pad_group_gh->GetParentGraphicsPtr().lock();
    const auto& father_graphics_id = father_graphics_ptr->GetId();
    auto parent_it = _component_pad_group_map.find(father_graphics_id);
    if (parent_it == _component_pad_group_map.end()) {
        return -1;
    }

    auto group_it = parent_it->second.find(group_id);
    if (group_it == parent_it->second.end()) {
        return -1;
    }

    auto& group = group_it->second;
    render::Tools::RemoveExpiredWeakPtrs(group);

    if (group.empty()) {
        parent_it->second.erase(group_id);
        if (parent_it->second.empty()) //如果没有pad group了，则删除
        {
            _component_pad_group_map.erase(parent_it);
        }
    }

    return 0;
}
GraphicsManager* const PadManager::GetGraphicsManager()
{
    return _graphics_manager;
}

int PadManager::CleareAllPadGroup()
{
    _component_pad_group_map.clear();
    return 0;
}

std::string PadManager::JoinPadGroupName(int pad_group_id_)
{
    return "pad_group_id_" + std::to_string(pad_group_id_);
}

int PadManager::SplitPadGroupName(const std::string& pad_group_name_)
{
    std::string prefix = "pad_group_id_";
    if (pad_group_name_.rfind(prefix, 0) == 0)
    {
        return std::stoi(pad_group_name_.substr(prefix.length()));
    }
    return -1;
}
