﻿#include "systemstateview.h"
#include <QColor>

#include <QVBoxLayout>
namespace jrsaoi {
    struct ImplData {
        jrsdata::SystemStateViewParamPtr param;
        std::shared_ptr < std::unordered_map<std::string, std::string>> mask_display_items;  /**< 屏蔽显示项目 */
        Ui::systemstateviewClass* ui;
        ImplData()
            : param(nullptr), mask_display_items(nullptr), ui(new Ui::systemstateviewClass)
        {

        }
    };
}
jrsaoi::SystemStateView::SystemStateView(const std::string& name, QWidget* parent /*= nullptr*/)
    :ViewBase(name, parent)
    , _impl_data(new ImplData())
{
    Init();
}

jrsaoi::SystemStateView::~SystemStateView()
{
    delete _impl_data->ui;
    delete _impl_data;
}

int jrsaoi::SystemStateView::Init()
{
    InitMember();
    InitView();
    InitConnect();
    return jrscore::AOI_OK;
}

Q_INVOKABLE int jrsaoi::SystemStateView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
{
    if (!param_)
    {
        Log_ERROR("参数为空指针！");
        return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
    }
    auto temp_param = std::static_pointer_cast<jrsdata::SystemStateViewParam>(param_);
    _impl_data->param = temp_param;
    if (param_->event_name == jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT || param_->event_name == jrsaoi::DATABASE_CONNECT_EVENT)
    {
        UpdateState(temp_param);
    }
    return 0;
}

int jrsaoi::SystemStateView::Save(const jrsdata::ViewParamBasePtr& param_)
{
    (void)param_;
    return jrscore::AOI_OK;
}

bool jrsaoi::SystemStateView::GetIsChecked()
{
    return true;
}

void jrsaoi::SystemStateView::InitView()
{
    _impl_data->ui->setupUi(this);
    QPixmap back_img(":/image/login.png");
    QPalette palette;
    palette.setBrush(this->backgroundRole(), QBrush(back_img));
    this->setPalette(palette);
    this->setAutoFillBackground(true);
    this->setFixedSize(back_img.size());
    _impl_data->ui->text_description->setHidden(true);
    InitTableView();

    _impl_data->ui->btn_to_main_window->setStyleSheet(
        "QPushButton{ background-color:red; color:white; }"
    );
    _impl_data->ui->btn_to_main_window->setFixedSize(100, 50);
    _impl_data->ui->btn_to_main_window->setHidden(true);

    QIcon icon(":/image/close.png");
    int size_icon = 30;
    QPixmap pixmap = icon.pixmap(size_icon, size_icon).scaledToWidth(size_icon, Qt::SmoothTransformation);
    _impl_data->ui->btn_close->setIcon(QIcon(pixmap));
    _impl_data->ui->btn_close->setIconSize(QSize(size_icon, size_icon));
    _impl_data->ui->btn_close->setFlat(true);
    _impl_data->ui->btn_close->setHidden(true);
    ChangeProcessBarStyle(true);
    _impl_data->ui->progress_bar->setValue(0);
    _impl_data->ui->progress_bar->setFixedHeight(22);
    /** 去除滚动条 */
    _impl_data->ui->text_description->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    _impl_data->ui->text_description->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    //// 隐藏标题栏
    this->setWindowFlags(Qt::FramelessWindowHint);
    //this->setWindowFlags(this->windowFlags() | Qt::WindowStaysOnTopHint);
    this->setWindowModality(Qt::ApplicationModal);
    //暂时取消屏蔽-By:HJC 2025/3/25
    _impl_data->mask_display_items->insert({ jrscheckitem::DATABASE_CHECK_ITEM,jrscheckitem::DATABASE_CHECK_ITEM });
}

void jrsaoi::SystemStateView::InitTableView()
{
    _impl_data->ui->table_check_items->setStyleSheet("QTableWidget {"
        "    background: transparent;}"
        "QHeaderView::section { background-color: transparent;}"
        "QTableWidget QTableCornerButton::section{"
        "	background:transparent; "
        "}"
    );
    _impl_data->ui->text_description->setStyleSheet("QTextBrowser {"
        "    background: rgba(255,255,255,0.5);}"
    );

    // 使表头和行头也透明
    QHeaderView* header = _impl_data->ui->table_check_items->horizontalHeader();
    header->setStyleSheet("background: transparent;");

    QHeaderView* verticalHeader = _impl_data->ui->table_check_items->verticalHeader();
    verticalHeader->setStyleSheet("background: transparent;");

    _impl_data->ui->table_check_items->setEditTriggers(QAbstractItemView::NoEditTriggers);
    _impl_data->ui->table_check_items->setColumnCount(4);
    _impl_data->ui->table_check_items->setColumnHidden(0, true);
    QStringList headers = { "Key","检测项", "状态","错误信息" };
    _impl_data->ui->table_check_items->setHorizontalHeaderLabels(headers);
    _impl_data->ui->table_check_items->setSelectionMode(QAbstractItemView::SingleSelection);
    _impl_data->ui->table_check_items->setSelectionBehavior(QAbstractItemView::SelectRows);
    _impl_data->ui->table_check_items->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    _impl_data->ui->table_check_items->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    _impl_data->ui->table_check_items->setFixedSize(300, 300);
    _impl_data->ui->table_check_items->setShowGrid(false);
    //_impl_data->ui->table_check_items->setAttribute(Qt::WA_TranslucentBackground);
}

void jrsaoi::SystemStateView::InitMember()
{
    _impl_data->ui = new Ui::systemstateviewClass();
    _impl_data->param = std::make_shared<jrsdata::SystemStateViewParam>();
    _impl_data->mask_display_items = std::make_shared<std::unordered_map<std::string, std::string>>();
}

void jrsaoi::SystemStateView::InitConnect()
{
    connect(_impl_data->ui->btn_close, &QPushButton::clicked, this, [=]() {
        emit SigCheckedChanged(false);
        });
    connect(_impl_data->ui->btn_to_main_window, &QPushButton::clicked, this, [=]() {
        emit SigCheckedChanged(true);
        this->close();
        });
    connect(_impl_data->ui->table_check_items, &QTableWidget::cellClicked, this, &jrsaoi::SystemStateView::SlotClickCell);
}

void jrsaoi::SystemStateView::UpdateState(const jrsdata::SystemStateViewParamPtr& param_)
{
    (void)param_;
    UpdateCheckItemContent();
    UpdateProgressBar();
    UpdatePushButton();
    QCoreApplication::processEvents();
}

void jrsaoi::SystemStateView::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    emit SigInitDevice();
}

void jrsaoi::SystemStateView::SlotClickCell(int row_, int col_)
{
    (void)col_;
    std::string key_str = _impl_data->ui->table_check_items->item(row_, 0)->text().toStdString();
    auto check_item_it = _impl_data->param->check_items.find(key_str);
    if (check_item_it != _impl_data->param->check_items.end() && check_item_it->second.code != jrscore::AOI_OK)
    {
        _impl_data->ui->text_description->setHidden(false);
        QString description = QString::fromStdString(check_item_it->second.description).replace("\n", "<br>");

        _impl_data->ui->text_description->setText("<font face='Microsoft YaHei'size=\"4\" color=\"red\"><b>" + description
            + "</b></front>");
    }
    else
    {
        _impl_data->ui->text_description->setHidden(true);
    }
}

void jrsaoi::SystemStateView::UpdateCheckItemContent()
{
    int row = 0;
    auto& items_state = _impl_data->param->check_items;
    _impl_data->ui->table_check_items->setRowCount(static_cast<int>(items_state.size() - _impl_data->mask_display_items->size()));
    for (auto& item : items_state)
    {
        if (_impl_data->mask_display_items->find(item.first) != _impl_data->mask_display_items->end())
        {
            continue;
        }

        auto state_pair = GetStateName(item.second.check_state);
        QTableWidgetItem* table_key_item_name = new QTableWidgetItem(QString::fromStdString(item.first));
        _impl_data->ui->table_check_items->setItem(row, 0, table_key_item_name);
        table_key_item_name->setForeground(state_pair.second);

        QString item_name = QString::fromStdString(item.second.item_name);
        QTableWidgetItem* table_item_name = new QTableWidgetItem(item_name);
        _impl_data->ui->table_check_items->setItem(row, 1, table_item_name);
        table_item_name->setForeground(state_pair.second);

        QTableWidgetItem* table_item_state = new QTableWidgetItem(QString::fromStdString(state_pair.first));
        _impl_data->ui->table_check_items->setItem(row, 2, table_item_state);
        table_item_state->setForeground(state_pair.second);
        QTableWidgetItem* table_item_info = new QTableWidgetItem(QString::fromStdString(item.second.err_info));
        _impl_data->ui->table_check_items->setItem(row, 3, table_item_info);
        table_item_info->setForeground(state_pair.second);

        _impl_data->ui->table_check_items->setRowHeight(row, 40);
        ++row;
    }
    AdjustTableSize();
    _impl_data->ui->text_description->setMaximumSize(_impl_data->ui->table_check_items->size());
}

void jrsaoi::SystemStateView::UpdateProgressBar()
{
    _impl_data->ui->progress_bar->setValue(_impl_data->param->progress_rate);
    if (!_impl_data->param->is_ok)
    {
        ChangeProcessBarStyle(false);
    }
    else
    {
        ChangeProcessBarStyle(true);
    }
}

void jrsaoi::SystemStateView::UpdatePushButton()
{
    if (_impl_data->ui->progress_bar->value() == 100)
    {
        if (!_impl_data->param->is_ok)
        {
            _impl_data->ui->btn_to_main_window->setHidden(false);
            _impl_data->ui->btn_close->setHidden(false);
        }
        else
        {
            emit SigCheckedChanged(true);
            this->close();
        }

    }
}

void jrsaoi::SystemStateView::AdjustTableSize()
{
    _impl_data->ui->table_check_items->resizeColumnsToContents();
    _impl_data->ui->table_check_items->resizeRowsToContents();
    _impl_data->ui->table_check_items->adjustSize();
    int totalWidth = _impl_data->ui->table_check_items->verticalHeader()->width() + _impl_data->ui->table_check_items->horizontalHeader()->length();
    int totalHeight = _impl_data->ui->table_check_items->horizontalHeader()->height() + _impl_data->ui->table_check_items->rowHeight(0) * _impl_data->ui->table_check_items->rowCount();
    _impl_data->ui->table_check_items->setFixedSize(totalWidth + 2, totalHeight + 2);
}

void jrsaoi::SystemStateView::ChangeProcessBarStyle(bool flag_)
{
    if (!flag_)
    {
        _impl_data->ui->progress_bar->setStyleSheet(
            "QProgressBar {"
            "    border: 1px solid #A0A0A0;"      // 边框灰色
            "	 text-align:center;"		      /*文本位置*/
            "    color:white; "
            "    border-radius:10px;"             // 圆角
            "    background-color: #D3D3D3;"      // 背景灰色
            "}"
            "QProgressBar::chunk {"
            "	 background:qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 #01FAFF, stop:1  #d63838);"
            "	 border-radius:10px; "
            "}"
        );
    }
    else
    {
        _impl_data->ui->progress_bar->setStyleSheet("QProgressBar {"
            "    border: 1px solid #A0A0A0;"      // 边框灰色
            "	 text-align:center;"		      /*文本位置*/
            "    color:white; "
            "    border-radius:10px;"             // 圆角
            "    background-color: #D3D3D3;"      // 背景灰色
            "}"
            "QProgressBar::chunk {"
            "	 background:qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 #01FAFF, stop:1  #059610);"
            "	 border-radius:10px; "
            "}");  // 清除样式表
    }
}

std::pair<std::string, QColor> jrsaoi::SystemStateView::GetStateName(jrsdata::MachineCheckParamInfo::CheckState state_)
{
    std::pair<std::string, QColor> state_info;
    switch (state_)
    {
    case jrsdata::MachineCheckParamInfo::CheckState::OK:
        state_info = { "成功" , Qt::green };
        break;
    case jrsdata::MachineCheckParamInfo::CheckState::CHECKING:
        state_info = { "加载中" , Qt::blue };
        break;
    case jrsdata::MachineCheckParamInfo::CheckState::FIALED:
        state_info = { "失败" , Qt::red };
        break;
    case jrsdata::MachineCheckParamInfo::CheckState::WAITTING:
        state_info = { "等待" , Qt::black };
        break;
    default:
        state_info = { "未知状态" , Qt::gray };
        break;
    }
    return state_info;
}
