﻿////QT
//#include <QPushButton>
//#include <QGridLayout>
//#include <QGroupBox>
////Custom
//#include "toolbarview.h"
//
//namespace jrsaoi
//{
//    ToolBarView::ToolBarView (const std::string& name, QWidget* parent) :ViewBase (name, parent)
//    {
//        Init ();
//    }
//    ToolBarView::~ToolBarView ()
//    {
//
//    }
//    void ToolBarView::Init ()
//    {
//        InitMember ();
//        InitView ();
//    }
//    bool ToolBarView::Save (const jrsdata::ViewParamBasePtr& param_)
//    {
//        return false;
//    }
//    void ToolBarView::UpdateView (const jrsdata::ViewParamBasePtr& param_)
//    {
//
//    }
//
//    void ToolBarView::InitView ()
//    {
//
//        QHBoxLayout* line_1_layout = new QHBoxLayout (this);
//        
//        //图片快捷键
//        QGroupBox* img_box = new QGroupBox ("图片",this);
//        QHBoxLayout* img_lay_out = new QHBoxLayout (img_box) ;
//        CreatePushButton (img_lay_out, ":/image/img_up_size.png", "放大", invoke_fun_slot);
//        CreatePushButton (img_lay_out, ":/image/img_down_size.png", "缩小", invoke_fun_slot);
//        CreatePushButton (img_lay_out, ":/image/img_1_1.png", "1:1恢复", invoke_fun_slot);
//        CreatePushButton (img_lay_out, ":/image/img_max.png", "最大化", invoke_fun_slot);
//        img_lay_out->setContentsMargins (0, 1, 0, 0);
//        
//        //检测框快捷
//        QGroupBox* detect_box = new QGroupBox ("检测框",this);
//        QHBoxLayout* detect_lay_out = new QHBoxLayout (detect_box);
//        CreatePushButton (detect_lay_out, ":/image/add_device.png", "添加检测框", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_deleteone.png", "删除检测框", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_deleteall.png", "删除所有检测框", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_copy0.png", "水平复制", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_copyvertical.png", "垂直复制", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_copy180.png", "四面复制", invoke_fun_slot);
//        CreatePushButton (detect_lay_out, ":/image/dete_copy90.png", "90°复制", invoke_fun_slot);
//        detect_lay_out->setContentsMargins (0, 1, 0, 0);
//
//        //CAD快捷键
//        QGroupBox* cad_box = new QGroupBox ("CAD", this);
//        QHBoxLayout* cad_lay_out = new QHBoxLayout (cad_box);
//        CreatePushButton (cad_lay_out, ":/image/cad_rotate_90.png", "旋转90°", invoke_fun_slot);
//        CreatePushButton (cad_lay_out, ":/image/cad_left_right_mirror.png", "左右镜像", invoke_fun_slot);
//        CreatePushButton (cad_lay_out, ":/image/cad_clock_wise.png", "顺逆时针", invoke_fun_slot);
//        CreatePushButton (cad_lay_out, ":/image/cad_angle+90.png", "角度+90°", invoke_fun_slot);
//        cad_lay_out->setContentsMargins (0, 1, 0, 0);
//
//        //元件快捷键
//        QGroupBox* device_box = new QGroupBox ("元件", this);
//        QHBoxLayout* device_lay_out = new QHBoxLayout (device_box);
//        CreatePushButton (device_lay_out, ":/image/cad_rotate_90.png", "旋转90°", invoke_fun_slot);
//        CreatePushButton (device_lay_out, ":/image/device_copy.png", "复制元件", invoke_fun_slot);
//        CreatePushButton (device_lay_out, ":/image/device_paste.png", "粘贴元件", invoke_fun_slot);
//        CreatePushButton (device_lay_out, ":/image/device_add.png", "添加元件", invoke_fun_slot);
//        CreatePushButton (device_lay_out, ":/image/device_delete.png", "删除元件", invoke_fun_slot);
//
//        device_lay_out->setContentsMargins (0, 1, 0, 0);
//
//        //测试快捷键
//        QGroupBox* test_box = new QGroupBox ("测试", this);
//        QHBoxLayout* test_lay_out = new QHBoxLayout (test_box);
//        CreatePushButton (test_lay_out, ":/image/test_detebox.png", "检测框", invoke_fun_slot);
//        CreatePushButton (test_lay_out, ":/image/test_device.png", "元件", invoke_fun_slot);
//        CreatePushButton (test_lay_out, ":/image/test_materialcode.png", "料号", invoke_fun_slot);
//        CreatePushButton (test_lay_out, ":/image/test_device_located.png", "元件定位", invoke_fun_slot);
//        CreatePushButton (test_lay_out, ":/image/test_materialcode_located.png", "料号定位", invoke_fun_slot);
//        test_lay_out->setContentsMargins (0, 1, 0, 0);
//
//
//        //硬件快捷键
//        QGroupBox* hardware_box = new QGroupBox ("硬件", this);
//        QHBoxLayout* hardware_lay_out = new QHBoxLayout (hardware_box);
//        CreatePushButton (hardware_lay_out, ":/image/hardware_capture.png", "采图", invoke_fun_slot);
//        CreatePushButton (hardware_lay_out, ":/image/hardware_stop_capture.png", "停止采图", invoke_fun_slot);
//        CreatePushButton (hardware_lay_out, ":/image/hardwarE_AOI.png", "运动控制", invoke_fun_slot);
//        CreatePushButton (hardware_lay_out, ":/image/hardware_bright.png", "灯光", invoke_fun_slot);
//        CreatePushButton (hardware_lay_out, ":/image/hardware_find_img.png", "寻图", invoke_fun_slot);
//        CreatePushButton (hardware_lay_out, ":/image/hardware_lighting.png", "光源", invoke_fun_slot);
//        hardware_lay_out->setContentsMargins (0, 1, 0, 0);
//
//        //工具快捷键
//        QGroupBox* tool_box = new QGroupBox ("工具", this);
//        QHBoxLayout* tool_lay_out = new QHBoxLayout (tool_box);
//        CreatePushButton (tool_lay_out, ":/image/tool_3d_show.png", "3D显示", invoke_fun_slot);
//        CreatePushButton (tool_lay_out, ":/image/tool_color_adjust.png", "颜色调配", invoke_fun_slot);
//        CreatePushButton (tool_lay_out, ":/image/tool_show.png", "显示", invoke_fun_slot);
//        tool_lay_out->setContentsMargins (0, 1, 0, 0);
//
//        //轨道快捷键
//        QGroupBox* rail_box = new QGroupBox ("轨道", this);
//        QHBoxLayout* rail_lay_out = new QHBoxLayout (rail_box);
//        CreatePushButton (rail_lay_out, ":/image/rail_in.png", "进料", invoke_fun_slot);
//        CreatePushButton (rail_lay_out, ":/image/rail_out.png", "出料", invoke_fun_slot);
//        CreatePushButton (rail_lay_out, ":/image/rail_up.png", "顶升", invoke_fun_slot);
//        CreatePushButton (rail_lay_out, ":/image/rail_down.png", "下降", invoke_fun_slot);
//        rail_lay_out->setContentsMargins (0, 1, 0, 0);
//
//
//
//        line_1_layout->addWidget (img_box);
//        line_1_layout->addWidget (detect_box);
//        line_1_layout->addWidget (cad_box);
//        line_1_layout->addWidget (device_box);
//        line_1_layout->addWidget (test_box);
//        line_1_layout->addWidget (hardware_box);
//        line_1_layout->addWidget (tool_box);
//        line_1_layout->addWidget (rail_box);
//        line_1_layout->addStretch();
//        line_1_layout->setContentsMargins (0, 0, 0, 0);
//    }
//    void ToolBarView::InitMember ()
//    {
//        invoke_fun_slot = std::bind (&ToolBarView::SlotShortcutsInvokeFun, this);
//    }
//    void ToolBarView::CreatePushButton (QHBoxLayout*& layout_,const QString& icon_path, const QString& tool_tip,
//                                        InvokeFun fun_)
//    {
//        int size_icon = 20;
//        QPushButton* button = new QPushButton (this);
//
//        button->setContentsMargins (0, 0, 0, 0);
//        QIcon icon (icon_path);
//        QPixmap pixmap = icon.pixmap (size_icon, size_icon).scaledToWidth (size_icon, Qt::SmoothTransformation);
//        button->setIcon (QIcon (pixmap));
//        button->setIconSize (QSize (size_icon, size_icon));
//        button->setToolTip (tool_tip);
//        button->setFlat (true);
//        layout_->addWidget (button);
//        layout_->setSpacing (0);
//        connect (button, &QPushButton::clicked, fun_);
//    }
//
//    void ToolBarView::SlotShortcutsInvokeFun ()
//    {
//        //TODO 添加各类快捷对应响应内容
//        std::cout<< "test";
//    }
//}
