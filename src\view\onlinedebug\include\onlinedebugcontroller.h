/*****************************************************************
 * @file   onlinedebugcontroller.h
 * @brief  在线调试控制器，用于和外部交互
 * @details 
 * <AUTHOR>
 * @date 2025.3.10
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.3.10          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
#ifndef B619986A_758E_49E7_A34D_30107615D30C
#define B619986A_758E_49E7_A34D_30107615D30C
//STD
//Custom
#include "pch.h"
//Third
namespace jrsaoi
{
    class OnLineDebugView;
    class OnLineDebugModel;

    class OnLineDebugController:public ControllerBase
    {
        Q_OBJECT
        public:
            OnLineDebugController(const std::string& name);
            ~OnLineDebugController();
            int Update(const jrsdata::ViewParamBasePtr& param_) override;
            int Save(const jrsdata::ViewParamBasePtr& param_) override;
            void SetView(ViewBase* view_param) override;
            void SetModel(ModelBasePtr model_param) override;
        signals:
            void SigUpdateOnline(const jrsdata::ViewParamBasePtr& param_);
        private slots:
            void SlotUpdateOnline(const jrsdata::ViewParamBasePtr& param_);
        private:
            std::string name_;
            OnLineDebugView* online_debug_view;
            std::shared_ptr<OnLineDebugModel> online_debug_model;
            std::atomic<bool> is_debugging;
    };
    using OnLineDebugControllerPtr = std::shared_ptr<OnLineDebugController>;
}

#endif /* B619986A_758E_49E7_A34D_30107615D30C */
