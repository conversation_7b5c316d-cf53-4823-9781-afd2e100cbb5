#ifndef MOTIONERRORCORRECT_H
#define MOTIONERRORCORRECT_H

#include "MotionErrorParam.h"

/// @brief 运动误差矫正
class MotionErrorCorrect
{
	public:
		MotionErrorCorrect();
		MotionErrorCorrect(const MotionErrorParam& _param);
		~MotionErrorCorrect();

		/// @brief 计算运动误差矫正后的坐标
		/// @param inputPt
		/// @return
		JrsPoint Run(const JrsPoint& inputPt) const;
		void Run(const double& input_x, const double& input_y,  double& output_x, double& output_y)const;

		void UpdateParam(const MotionErrorParam& _param);
		bool IsValid() const;

	private:
		MotionErrorParam param;                                     // 误差模型参数
		bool             isInit = false;
};

#endif