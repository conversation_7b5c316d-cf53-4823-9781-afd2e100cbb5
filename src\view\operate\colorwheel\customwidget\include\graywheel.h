/*****************************************************************//**
 * @file   graywheel.h
 * @brief  灰度二值化控件
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSGRAYWHEEL_H__
#define __JRSGRAYWHEEL_H__
#if _MSC_VER >= 1600 //VS2015>VS>VS2010, MSVC VER= 10.0 -14.0
#pragma execution_character_set("utf-8")
#endif
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include"../include/histogramwidget.h"

#pragma warning(pop)
class CustomPlotWidget;
class QButtonGroup;
class QRadioButton;
class GrayWheel : public QWidget
{
    Q_OBJECT

public:
    GrayWheel(QWidget* parent = nullptr);
    ~GrayWheel();

signals:
    void UpataGrayTypeIndex(int index);
	void SetGrayThre(int min_thre, int max_thre);
public slots:
    void SetGrayHistValue(std::vector<float>& gray_hist);
    void UpataGrayTypeIndexSlot(int index);
	void SetGrayThreSlot(int min_thre, int max_thre);

private:
    CustomPlotWidget* gray_histogramwidget = nullptr;
    QButtonGroup* gray_type_ = nullptr;

    QRadioButton* weight_gray_ = nullptr;
    QRadioButton* max_gray_ = nullptr;
    QRadioButton* min_gray_ = nullptr;
    QRadioButton* mean_gray_ = nullptr;
	int gray_type_index_ = 0;
};
#endif