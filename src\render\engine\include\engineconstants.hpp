/*********************************************************************
 * @brief  渲染引擎常量.
 *
 * @file   engineconstants.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef ENGINECONSTANTS_HPP
#define ENGINECONSTANTS_HPP

 /**
  * @brief 渲染器类型
  */
enum class RenderType
{
    RT_Background,
    RT_Graphics,
    RT_Foreground,
    SIZE
};

/**
 * @brief 刷新模式
 */
enum class RefreshMode
{
    RM_REAL_TIME,    // 实时刷新
    RM_EVENT_DRIVEN, // 事件驱动
};

/**
 * @brief 着色器程序类型枚举
 * 枚举描述不同的着色器程序类型
 * <AUTHOR>
 */
enum class ProgramType
{
    TEXTURE, ///< 纹理着色器程序
    GRAPHICS ///< 图形着色器程序
};

#endif // !ENGINECONSTANTS_HPP