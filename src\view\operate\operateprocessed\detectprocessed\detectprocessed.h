﻿/*****************************************************************//**
 * @file   DetectProcessed.h
 * @brief 检测界面数据处理
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef DETECTPROCESSED_H
#define DETECTPROCESSED_H

 //QT

 //Custom
#include "detectresultmodel.h"
#include "datadefine.hpp"

class DetectProcessed : public QObject
{
    Q_OBJECT

public:
    DetectProcessed(QObject* parent);
    ~DetectProcessed();

public:
    /**
     * @fun UpdateResultRatio
     * @brief 更新竖向柱状图结果
     * @date 2024.2.6
     * <AUTHOR>
     */
    void UpdateResultRatio();
    /**
     * @fun AddOneBoardResult
     * @brief 添加一个结果到结果展示界面
     * @param barcode
     * @param result
     * @date 2024.2.6
     * <AUTHOR>
     */
    void AddOneBoardResult(std::string barcode, jrsdata::DetectResult result, int track_id, long total_device_count, long test_device_count, long ng_device_count, std::string take_phone_time, std::string circle_time);
public:
    DetectResultModel* m_detect_result_model;               /// 结果表Model
    SHOW_DETECT_NG_TYPE m_show_result_type;                 /// 缺陷统计方式 0=元件 1=错误类型 2=定位点
    std::vector<DetectResultStruct*> vec_board_result;      /// 主板结果表数据
    std::vector<RingData> vec_board_result_ring_datas;      /// 检测结果竖向柱状图数据
    std::vector<RingData> vec_ng_devices_ring;              /// 缺陷表元件数据
    std::vector<RingData> vec_ng_types_ring;                /// 缺陷表类型数据
    std::string m_project_name;                             /// 工程名
    std::string m_ticket_num;                               /// 工单号
    std::string m_version;                                  /// 版本号
    std::string m_start_run_time;                           /// 开机时间
    std::string m_runing_time;                              /// 运行时间
    std::string m_scan_time;                                /// 扫描时间
    std::string m_loop_time;                                /// 循环时间
    int m_total_devices;                                    /// 总元件
    int m_test_count;                                       /// 测试数目
    int m_ng_devices_count;                                 /// NG元件数
    double m_fov_scan;                                      /// FOV扫描时间
    double m_fov_detect;                                    /// FOV检测时间
    double m_aoi_ok_count;                                  /// 直通数
    double m_aoi_ng_count;                                  /// NG数
    double m_sub_board_aoi_good_count;                      /// 子板直通数
    double m_sub_board_aoi_ng_count;                        /// 子板NG数
};
#endif
