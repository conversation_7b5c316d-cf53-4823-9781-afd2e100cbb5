/*****************************************************************/ /**
* @file   trackbase.h
* @brief  轨道调度基类
* @details 
* <AUTHOR>
* @date 2024.10.1
* <table>
* <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
* <tr><td>2024.9.2         <td>V2.0              <td>zhaokunlong      <td>                      <td> 3D AOI V2.0
* </table>
* @copyright 版权 CopyRight (C), 2023-2024.
*********************************************************************/

#ifndef __JRSTRACKBASE_H__
#define __JRSTRACKBASE_H__

// Custom
#include "motion.h"

namespace jrsdevice
{
    // 拍照回调,提供轨道索引以及工位信息
    using PhotoCallBack = std::function<int(const jrsdevice::TrackIndex index, jrsdevice::WorkStation station)>;
    class JRS_AOI_PLUGIN_API TrackBase
    {

    public:
        TrackBase(std::shared_ptr<Motion> &motion, bool &running);
        ~TrackBase();

        /**
         * @fun SetPhotoCallBack
         * @brief 设置拍照轨道回调
         * @param callback_
         * <AUTHOR>
         * @date 2024.9.2
         */
        void SetPhotoCallBack(PhotoCallBack callback_);

        // 设置上料失败回调
        void SetLoadFailCallBack(PhotoCallBack callback_);

        // 设置下料失败回调
        void SetUnLoadFailCallBack(PhotoCallBack callback_);

        // 添加轨道流程
        void AddTrackProcess(jrsdevice::TrackIndex trackIndex, jrsdevice::TrackProcess process);

        // 移除轨道流程
        void RemoveTrackProcess(jrsdevice::TrackIndex trackIndex);

        // 移除所有轨道流程
        void RemoveAllTrack();

    private:
        // 上料线程
        void LoadThread();

        // 下料线程
        void UnLoadThread();

        // 拍照线程
        void PhotoThread();

    private:
        std::shared_ptr<Motion> motion_ptr; /**< 运控实例*/
        bool process_running;               /**< 流程运行中 */
        std::map<jrsdevice::TrackIndex, std::vector<jrsdevice::TrackProcess>> track_process;    /**< key:轨道索引,value:工位流程(可以有多个) */
        std::mutex track_process_mutex;
        std::thread track_load_thread;   /**< 上料线程 */
        std::thread track_unload_thread; /**< 下料线程 */
        std::thread track_photo_thread;  /**< 拍照线程 */
        bool exit_;                      /**< 线程运行标志 */
        PhotoCallBack photo_callback;    /**< 拍照的轨道回调 */
        PhotoCallBack load_fail_callback;    /**< 上料失败回调 */
        PhotoCallBack unload_fail_callback;    /**< 下料失败回调 */
    };
}

#endif // !__JRSTRACKBASE_H__
