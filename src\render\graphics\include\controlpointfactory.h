/**********************************************************************
 * @brief  控制点工厂类.
 *
 * @file   controlpointfactory.h
 *
 * @date   2024.07.08
 * <AUTHOR>
**********************************************************************/

#ifndef CONTROLPOINTFACTORY_H
#define CONTROLPOINTFACTORY_H

#include "controlpointconstants.hpp"

#include <memory>
#include <vector>

class ControlPointAbstract;
class GraphicsAbstract;
struct ControlAttributes;
struct ControlPointDraw;

class ControlPointFactory
{
public:
    ControlPointFactory() = default;
    ~ControlPointFactory() = default;
    /**
     * @brief   创建控制点.
     * @param   type 控制点类型
     * @param   obj  图形对象
     * @return  控制点
     */
    static std::shared_ptr<ControlPointAbstract> CreateControlPoint(
        const ControlPointType& type, GraphicsAbstract* const obj);
    /**
     * @brief   创建控制点组.
     * @param   type 控制点组类型
     * @param   obj  图形对象
     * @return  控制点组
     */
    static std::vector<std::shared_ptr<ControlPointAbstract>> CreateControlPointGroup(
        const ControlPointGroupType& grouptype, GraphicsAbstract* const obj);
};

#endif //! CONTROLPOINTFACTORY_H