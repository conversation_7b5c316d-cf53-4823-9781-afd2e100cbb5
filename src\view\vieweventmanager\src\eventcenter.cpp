﻿// Custom
#include "eventcenter.h"
#include "pubsubevent.h"
#include "viewdefine.h"
#include "render2dcontroller.h"
#include "showlistcontroller.h"
#include "operatecontroller.h"
#include "settingcontroller.h"
#include "systemstatecontroller.h"
#include "controlpanelcontroller.h"
#include "onlinedebugcontroller.h"
#include "logicmanager.h"
#include "jtools.hpp"

#include "paramupdatecenter.h"


namespace jrsaoi
{

    struct DataImpl
    {
        jrscore::ModuleHandlePtr moudle_handle_view; /**< 界面事件响应处理模块 */
        jrslogic::LogicManagerPtr logicmanager_ptr;
        PubSubEventPtr pubsub_event_ptr;
        jrsdata::SystemStateParamPtr system_state_ptr;
        jrsaoi::InstanceParamUpdateCenterPtr instance_param_update_ptr;
        DataImpl()
        {
        }
        ~DataImpl()
        {
        }
    };

    EventCenter::EventCenter(ViewManagerPtr& view_manager_ptr_)
        : data_impl(new DataImpl()), view_manager_ptr(view_manager_ptr_)
    {

        Init();

    }
    EventCenter::~EventCenter()
    {
        if (data_impl)
        {
            delete data_impl;
            data_impl = nullptr;
        }
    }

    int EventCenter::Init()
    {
        InitMember();
        InitPubSub();
        InitConnect();

        return jrscore::AOI_OK;
    }

    void EventCenter::InitMember()
    {
        data_impl->moudle_handle_view = GenerateModuleInstance(jrsaoi::VIEW_MODULE_NAME);
        data_impl->logicmanager_ptr = std::make_shared<jrslogic::LogicManager>();

        // 工程回调
        data_impl->logicmanager_ptr->SetProjectCallback(std::bind(&EventCenter::ProjectCallback, this, std::placeholders::_1));

        //设备参数回调
        data_impl->logicmanager_ptr->SetInitDeviceCallBack(std::bind(&EventCenter::DeviceInitParamCallback, this, std::placeholders::_1));

        // operator 界面回调信息  包含运控回调 by HJC 2025/1/13 
        data_impl->logicmanager_ptr->OperateViewCallBack(std::bind(&EventCenter::OperateViewCallBack, this, std::placeholders::_1));
        // 检测结果回调
        data_impl->logicmanager_ptr->DetectResultUpdateCallBack(std::bind(&EventCenter::DetectResultCallback, this, std::placeholders::_1));

        //! 在线调试回调，主要用于将底层自动流程检测的结果信息传递给调试界面 by zhangyuyu 2025.4.14
        data_impl->logicmanager_ptr->SetOnlineDebugInfoCallBack(std::bind(&EventCenter::OnlineDebugViewCallback, this, std::placeholders::_1));
        // 设置设置参数信息回调
        data_impl->logicmanager_ptr->SetSettingParamCallBack(std::bind(&EventCenter::SettingParamCallback, this, std::placeholders::_1));
        // 自动运行回调
        data_impl->logicmanager_ptr->SetAutoRunPanelParamCallBack(std::bind(&EventCenter::AutoRunPanelCallback, this, std::placeholders::_1));


        //初始化回调
        data_impl->logicmanager_ptr->SetSystemStateParamCallback(std::bind(&EventCenter::SystemStateParamCallback, this, std::placeholders::_1));
        data_impl->logicmanager_ptr->SetRenderCallback(std::bind(&EventCenter::RenderCallback, this, std::placeholders::_1));

        //界面层单例参数更新
        data_impl->instance_param_update_ptr = std::make_shared<jrsaoi::InstanceParamUpdateCenter>();
        data_impl->instance_param_update_ptr->SetNotifyCallBack(std::bind(&EventCenter::Notify, this, std::placeholders::_1));

    }

    void EventCenter::InitPubSub()
    {
        InitShortcutPubSub();

        InitOperatePubSub();

        InitRenderPubSub();

        InitShowListPubSub();

        InitControlPanelPubSub();

        InitSystemStatePubSub();

        InitDetectResultUpdatePubSub();

        InitOnlineDebugPubSub();
    }

    void EventCenter::InitShortcutPubSub()
    {
        /** <注册快捷按钮主题事件 */
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME);

        /** <渲染界面订阅快捷按钮事件 */
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME, jrsaoi::SHORTCUT_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME, jrsaoi::SHORTCUT_OPERATER_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME, jrsaoi::OPERATE_AXIS_MOVE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        //! 逻辑层订阅快捷按钮

        //! 逻辑层运控事件处理函数
        auto call_back_ = std::bind(&jrslogic::LogicManager::HandleMotion, data_impl->logicmanager_ptr, std::placeholders::_1);
        auto call_back_logic_event = std::bind(&jrslogic::LogicManager::EventHandler, data_impl->logicmanager_ptr, std::placeholders::_1);

        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(SHORTCUT_TRIGGER_TOPIC_NAME, SHORTCUT_LOGIC_MOTION_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_));
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(SHORTCUT_TRIGGER_TOPIC_NAME, SHORTCUT_LOGIC_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_event));
        /**< to setting module name*/
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME,
            jrsaoi::SETTING_SUB_NAME, jrsaoi::DATA_MODULE_NAME);


    }

    void EventCenter::InitOperatePubSub()
    {
        //! 注册操作界面按钮响应主题事件
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::OPERATE_TRIGGER_TOPIC_NAME);
        //! 注册逻辑层主题
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::LOGIC_TOPIC_NAME);
        //! 注册元件执行主题
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::OPERATE_EXECUTE_COMPONENT_TOPIC_NAME);

        //! 逻辑层订阅操作界面
        auto call_back_ = std::bind(&jrslogic::LogicManager::HandleMotion, data_impl->logicmanager_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(jrsaoi::OPERATE_TRIGGER_TOPIC_NAME, jrsaoi::OPERATE_LOGIC_MOTION_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_));

        /** <操作界面更新数据事件 */
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::OPERATE_TRIGGER_TOPIC_NAME, jrsaoi::OPERATE_UPDATE_MOTION_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);

        //! 逻辑层操作模块更新订阅
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::LOGIC_TOPIC_NAME, jrsaoi::OPERATE_UPDATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);

        auto call_back_logic_handle = std::bind(&jrslogic::LogicManager::EventHandler, data_impl->logicmanager_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(jrsaoi::OPERATE_TRIGGER_TOPIC_NAME, jrsaoi::OPERATE_LOGIC_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_handle));

        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::OPERATE_TRIGGER_TOPIC_NAME, jrsaoi::OPERATE_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);

        /** < showlist界面订阅元件执行 */
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::OPERATE_EXECUTE_COMPONENT_TOPIC_NAME, jrsaoi::OPERATE_EXECUTE_COMPONENT_SHOWLIST_SUB_NAME, jrsaoi::SHOWLIST_MODULE_NAME);

        /** < onlinedebug界面订阅元件执行主题*/
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::OPERATE_EXECUTE_COMPONENT_TOPIC_NAME, jrsaoi::OPERATE_EXECUTE_COMPONENT_ONLINE_DEBUG_SUB_NAME, jrsaoi::ONLINEDEBUG_MODULE_NAME);
    }

    void EventCenter::InitRenderPubSub()
    {
        /*工程->操作*/
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::PROJECT_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::PROJECT_TOPIC_NAME, jrsaoi::RENDER_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::PROJECT_TOPIC_NAME, jrsaoi::PROJECT_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);

        /*操作->工程*/
        // RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::OPERATE_PROJECT_SUB_NAME);
        // RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::OPERATE_PROJECT_SUB_NAME, jrsaoi::PROJECT_RENDER_SUB_NAME, jrsaoi::PROJECT_MODULE_NAME);

        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::LOGIC_TOPIC_NAME, jrsaoi::LOGIC_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);

        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::RENDER_TRIGGER_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::RENDER_TRIGGER_TOPIC_NAME, jrsaoi::RENDER_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::RENDER_TRIGGER_TOPIC_NAME, jrsaoi::SHOWLIST_CHANGE_SUB_NAME, jrsaoi::SHOWLIST_MODULE_NAME);

        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::RENDER_UPDATE_PROJECT_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::RENDER_UPDATE_PROJECT_TOPIC_NAME, jrsaoi::RENDER_SUB_NAME, jrsaoi::SHOWLIST_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::RENDER_UPDATE_PROJECT_TOPIC_NAME, jrsaoi::OPERATE_LOGIC_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
    }

    void EventCenter::InitShowListPubSub()
    {
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME, jrsaoi::PROJECT_SHOWLIST_SUB_NAME, jrsaoi::SHOWLIST_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME, jrsaoi::SHOWLIST_CHANGE_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME, jrsaoi::SHOWLIST_OPERATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);

    }

    void EventCenter::InitSystemStatePubSub()
    {
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::SYSTEM_STATE_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SYSTEM_STATE_TOPIC_NAME, jrsaoi::SYSTEM_STATE_VIEW_SUB_NAME, jrsaoi::SYSTEM_STATE_MODULE_NAME);
        auto call_back_logic_handle = std::bind(&jrslogic::LogicManager::EventHandler, data_impl->logicmanager_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(jrsaoi::SYSTEM_STATE_TOPIC_NAME, jrsaoi::SYSTEM_STATE_LOGIC_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_handle));
        auto call_back_param_instance_handle = std::bind(&jrsaoi::InstanceParamUpdateCenter::EventHandler, data_impl->instance_param_update_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(jrsaoi::SYSTEM_STATE_TOPIC_NAME, jrsaoi::PARAMS_UPDATE_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_param_instance_handle));
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::SYSTEM_STATE_TOPIC_NAME, jrsaoi::CONTROL_PANEL_VIEW_SUB_NAME, jrsaoi::CONTROL_PANEL_MODULE_NAME);
    }

    void EventCenter::InitDetectResultUpdatePubSub()
    {
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::DETECT_RESULT_UPDATE_TOPIC_NAME);
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::DETECT_RESULT_ONLINE_DEBUG_TOPIC_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::DETECT_RESULT_UPDATE_TOPIC_NAME, jrsaoi::DETECT_RESULT_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::DETECT_RESULT_ONLINE_DEBUG_TOPIC_NAME, jrsaoi::DETECT_RESULT_ONLINE_DEBUG_VIEW_SUB_NAME, jrsaoi::ONLINEDEBUG_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::DETECT_RESULT_ONLINE_DEBUG_TOPIC_NAME, jrsaoi::DETECT_RESULT_OPERATE_VIEW_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
    }

    void EventCenter::InitOnlineDebugPubSub()
    {
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::ONLINEDEBUG_TOPIC_NAME);
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::ONLINEDEBUG_FINISH_TOPIC_NAME);
        auto call_back_logic_event = std::bind(&jrslogic::LogicManager::EventHandler, data_impl->logicmanager_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(ONLINEDEBUG_FINISH_TOPIC_NAME, ONLINEDEBUG_FINISH_WORKFLOW_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_event));
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(ONLINEDEBUG_TOPIC_NAME, ONLINEDEBUG_WORKFLOW_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_event));
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::ONLINEDEBUG_TOPIC_NAME, jrsaoi::ONLINEDEBUG_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::ONLINEDEBUG_TOPIC_NAME, jrsaoi::ONLINEDEBUG_OPERATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::ONLINEDEBUG_FINISH_TOPIC_NAME, jrsaoi::ONLINEDEBUG_FINISH_OPERATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);

    }

    void EventCenter::InitControlPanelPubSub()
    {
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_TOPIC_NAME);

        //! 控制面板模块注册在线调试topic注册 by zhangyuyu 2025.4.8
        RegisterAdvertise(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME);
        auto call_back_logic_event = std::bind(&jrslogic::LogicManager::EventHandler, data_impl->logicmanager_ptr, std::placeholders::_1);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(CONTROL_PANEL_TOPIC_NAME, CONTROL_PANEL_LOGIC_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_event));
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_TOPIC_NAME, jrsaoi::CONTROL_PANEL_VIEW_SUB_NAME, jrsaoi::CONTROL_PANEL_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_TOPIC_NAME, jrsaoi::CONTROL_PANEL_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_TOPIC_NAME, jrsaoi::CONTROL_PANEL_OPERATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        //! 在线调试模块订阅控制面板在线调试状态切换主题 by zhangyuyu 2025.4.8
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME, jrsaoi::CONTROL_PANEL_ONLINE_DEBUG_ONLINE_DEBUG_VIEW_SUB_NAME, jrsaoi::ONLINEDEBUG_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME, jrsaoi::CONTROL_PANEL_ONLINE_DEBUG_OPERATE_SUB_NAME, jrsaoi::OPERATE_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME, jrsaoi::CONTROL_PANEL_ONLINE_DEBUG_SHOW_LIST_SUB_NAME, jrsaoi::SHOWLIST_MODULE_NAME);
        RegisterViewSubscriber(data_impl->moudle_handle_view, jrsaoi::CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME, jrsaoi::CONTROL_PANEL_ONLINE_RENDER_SUB_NAME, jrsaoi::RENDER2D_MODULE_NAME);
        data_impl->moudle_handle_view->AddSubscriber<jrsdata::ViewParamBasePtr>(CONTROL_PANEL_ONLIE_DEBUG_TOPIC_NAME, CONTROL_PANEL_LOGIC_SUB_NAME, std::function<int(jrsdata::ViewParamBasePtr)>(call_back_logic_event));

    }

    void EventCenter::InitConnect()
    {
        connect(std::static_pointer_cast<jrsaoi::Render2dController>(view_manager_ptr->GetController(jrsaoi::RENDER2D_MODULE_NAME)).get(),
            &Render2dController::SignalRender2dUpdate, this, &EventCenter::SlotOperator);

        connect(std::static_pointer_cast<jrsaoi::OperateController>(view_manager_ptr->GetController(jrsaoi::OPERATE_MODULE_NAME)).get(),
            &OperateController::SigUpdateOperator, this, &EventCenter::SlotOperator);

        connect(std::static_pointer_cast<jrsaoi::ControlPanelController>(view_manager_ptr->GetController(jrsaoi::CONTROL_PANEL_MODULE_NAME)).get(),
            &ControlPanelController::SigControlPanelUpdate, this, &EventCenter::SlotOperator);

        connect(std::static_pointer_cast<jrsaoi::SettingController>(view_manager_ptr->GetController(jrsaoi::DATA_MODULE_NAME)).get(),
            &SettingController::SigSettingParam, this, &EventCenter::SlotOperator);

        // 元件列表点击事件
        connect(std::static_pointer_cast<jrsaoi::ShowListController>(view_manager_ptr->GetController(jrsaoi::SHOWLIST_MODULE_NAME)).get(),
            &ShowListController::ShowListRenderTrigger, this, &EventCenter::SlotOperator);
        //系统初始化
        connect(std::static_pointer_cast<jrsaoi::SystemStateController>(view_manager_ptr->GetController(jrsaoi::SYSTEM_STATE_MODULE_NAME)).get(),
            &SystemStateController::SigUpdateSystemState, this, &EventCenter::SlotOperator);
        connect(std::static_pointer_cast<jrsaoi::OnLineDebugController>(view_manager_ptr->GetController(jrsaoi::ONLINEDEBUG_MODULE_NAME)).get(),
            &OnLineDebugController::SigUpdateOnline, this, &EventCenter::SlotOperator);

    }

    int EventCenter::RegisterAdvertise(jrscore::ModuleHandlePtr& moudle_handle, const std::string& topic_name)
    {
        moudle_handle->AddAdvertise(topic_name);
        return jrscore::AOI_OK;
    }
    /**< 注册 view 界面 */
    int EventCenter::RegisterViewSubscriber(jrscore::ModuleHandlePtr& moudle_handle, const std::string& topic_name, const std::string& sub_name, const std::string& subscriber_instance_name)
    {   
        // 🔑 关键：通过模块名称获取Controller实例
        auto controller = view_manager_ptr->GetController(subscriber_instance_name);
        if (controller)
        {    
            // 🔑 关键：通过模块名称获取Controller实例
            auto call_back = std::bind(&ControllerBase::Update, controller, std::placeholders::_1);
            // 将回调函数注册到PubSub系统
            moudle_handle->AddSubscriber<jrsdata::ViewParamBasePtr>(topic_name, sub_name, std::function<int(jrsdata::ViewParamBasePtr)>(call_back));
        }
        else
        {
            Log_ERROR("获取控制器：", subscriber_instance_name, " 失败");
        }

        return jrscore::AOI_OK;
    }

    jrscore::ModuleHandlePtr EventCenter::GenerateModuleInstance(const std::string& module_name)
    {
        auto handle = std::make_shared<jrscore::ModuleHandle>(module_name);
        return handle;
    }

    int EventCenter::ProjectCallback(const jrsdata::ProjectEventParamPtr& project_param_)
    {
        auto param = std::make_shared<jrsdata::ProjectEventParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->event_name = project_param_->event_name;
        param->project_param = project_param_->project_param;/**< 工程参数 */
        UpdateInstanceParam(param);
        param->topic_name = jrsaoi::PROJECT_TOPIC_NAME;
        param->sub_name = "all";
        Notify(param);
        return 0;
    }

    int EventCenter::RenderCallback(const jrsdata::JrsImageBuffer& img_buffer)
    {
        jrsdata::RenderViewParamPtr render_param_temp = std::make_shared<jrsdata::RenderViewParam>();
        render_param_temp->module_name = jrsaoi::VIEW_MODULE_NAME;
        render_param_temp->topic_name = jrsaoi::LOGIC_TOPIC_NAME;
        render_param_temp->sub_name = LOGIC_RENDER_SUB_NAME;
        render_param_temp->event_name = "show_scan_image";
        render_param_temp->image_buffer = img_buffer;
        Log_INFO("逻辑模块回调运行");
        Notify(render_param_temp);
        return 0;
    }

    int EventCenter::MotionMsgCallback(const jrsdata::DeviceParamPtr& device_param_)
    {
        jrsdata::OperateViewParamPtr operate_view_param = std::make_shared<jrsdata::OperateViewParam>();
        operate_view_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param->sub_name = jrsaoi::OPERATE_UPDATE_MOTION_SUB_NAME;
        operate_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param->event_name = device_param_->event_name;
        operate_view_param->device_param = *device_param_;
        Notify(operate_view_param);
        return 0;
    }

    int EventCenter::DeviceInitParamCallback(const jrsdata::DeviceParamPtr& device_param_)
    {
       
        jrsdata::OperateViewParamPtr operate_view_param = std::make_shared<jrsdata::OperateViewParam>();
        operate_view_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_view_param->sub_name = jrsaoi::OPERATE_UPDATE_SUB_NAME;
        operate_view_param->topic_name = jrsaoi::LOGIC_TOPIC_NAME;
        operate_view_param->event_name = device_param_->event_name;
        operate_view_param->device_param = *device_param_;
        Notify(operate_view_param);


        //! 需要等结构光初始化完成后再设置operate中的算法引擎，因为需要分辨率
        auto temp_operate = std::static_pointer_cast<jrsaoi::OperateController>(view_manager_ptr->GetController(jrsaoi::OPERATE_MODULE_NAME)).get();
        QMetaObject::invokeMethod(
            this,
            [=]() {
                temp_operate->SetAlgoEngine(data_impl->logicmanager_ptr->GetAlgoEngineManager());
            },
            Qt::QueuedConnection
        );

      

        return 0;
    }

    int EventCenter::OperateViewCallBack(const jrsdata::OperateViewParamPtr& param_)
    {
        //std::lock_guard<std::mutex> lock(_operate_mutex);
        jrsdata::OperateViewParamPtr operate_view_param = std::make_shared<jrsdata::OperateViewParam>();
        param_->module_name = jrsaoi::VIEW_MODULE_NAME;
        param_->sub_name = jrsaoi::OPERATE_UPDATE_MOTION_SUB_NAME;
        param_->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        Notify(param_);
        return 0;
    }

    int EventCenter::SettingParamCallback(const jrsdata::SettingViewParamPtr& setting_param_)
    {
        auto setting_param = std::make_shared<jrsdata::SettingViewParam>();
        setting_param->module_name = jrsaoi::DATA_MODULE_NAME;
        setting_param->event_name = setting_param_->event_name;
        setting_param->comm_param = setting_param_->comm_param;
        setting_param->sys_param = setting_param_->sys_param;
        setting_param->machine_param = setting_param_->machine_param;
        UpdateInstanceParam(setting_param);
        setting_param->sub_name = jrsaoi::SETTING_SUB_NAME;
        setting_param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;

        Notify(setting_param);
        return 0;
    }

    int EventCenter::SystemStateParamCallback(const jrsdata::SystemStateViewParamPtr& sys_state_param_)
    {
        jrsdata::SystemStateViewParamPtr param = std::make_shared<jrsdata::SystemStateViewParam>();
        param->event_name = /*jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT;*/ sys_state_param_->event_name;
        param->check_items = sys_state_param_->check_items;
        param->module_name = jrsaoi::SYSTEM_STATE_MODULE_NAME;
        UpdateInstanceParam(param);
        param->topic_name = jrsaoi::SYSTEM_STATE_TOPIC_NAME;
        param->sub_name = jrsaoi::SYSTEM_STATE_VIEW_SUB_NAME;
        Notify(param);// 通知其他层
        return 0;
    }

    int EventCenter::UpdateInstanceParam(const jrsdata::ViewParamBasePtr& param_)
    {
        param_->topic_name = SYSTEM_STATE_TOPIC_NAME;
        param_->sub_name = PARAMS_UPDATE_SUB_NAME;
        return Notify(param_);
    }

    int EventCenter::AutoRunPanelCallback(const jrsdata::ControlPanelViewParamPtr& param_)
    {
        jrsdata::ControlPanelViewParamPtr param = std::make_shared<jrsdata::ControlPanelViewParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::CONTROL_PANEL_TOPIC_NAME;
        param->sub_name = jrsaoi::CONTROL_PANEL_VIEW_SUB_NAME;
        param->event_name = param_->event_name;
        param->machine_state_param = param_->machine_state_param;
        return Notify(param);
    }

    int EventCenter::DetectResultCallback(const jrsdata::AlgoEventParamPtr& param_)
    {
        auto param = std::make_shared<jrsdata::AlgoEventParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::DETECT_RESULT_UPDATE_TOPIC_NAME;
        param->sub_name = "all";
        param->event_name = param_->event_name;
        param->component_detect_results = param_->component_detect_results;
        return Notify(param);
    }

    int EventCenter::OnlineDebugViewCallback(const jrsdata::OnlineDebugParmPtr& online_param_)
    {
        auto param = std::make_shared<jrsdata::OnlineDebugViewParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::DETECT_RESULT_ONLINE_DEBUG_TOPIC_NAME;
        param->sub_name = jrsaoi::DETECT_RESULT_ALL_SUB_NAME;
        param->event_name = jrsaoi::DETECT_RESULT_ONLINE_DEBUG_INFO_EVENT_NAME;
        param->online_debug_param = online_param_;
        return Notify(param);
    }

    int EventCenter::Notify(const jrsdata::ViewParamBasePtr& param)
    {
        if (!data_impl)
        {
            return -1;
        }
        if (!data_impl->moudle_handle_view)
        {
            return -1;
        }
        if (param->sub_name == "all") // 或者改成空的时候通知全部
        {
            return data_impl->moudle_handle_view->NotifyAll(param->topic_name, param);
        }
        return data_impl->moudle_handle_view->NotifyOne(param->topic_name, param->sub_name, param);// 根据topic_name和sub_name查找订阅者
    }

    void EventCenter::SlotOperator(const jrsdata::ViewParamBasePtr& param)
    {
        //std::cout << __FUNCTION__ << " topic_name " << param->topic_name.c_str() << std::endl;
        //std::cout << __FUNCTION__ << " module_name " << param->module_name.c_str() << std::endl;
        //std::cout << __FUNCTION__ << " sub_name " << param->sub_name.c_str() << std::endl;
        //std::cout << __FUNCTION__ << " event_name " << param->event_name.c_str() << std::endl;
        //std::cout << __FUNCTION__ << " invoke_module_name " << param->invoke_module_name.c_str() << std::endl;
        Notify(param);
    }

}
