/*********************************************************************
 * @brief  定义图形相关事件的参数.
 *
 * @file   graphicseventparam.hpp
 *
 * @date   2024.11.15
 * <AUTHOR>
**********************************************************************/
#ifndef GRAPHICSEVENTPARAM_HPP
#define GRAPHICSEVENTPARAM_HPP

#include "controlpointconstants.hpp" // ControlAttributes ControlPoint

/**
 * @brief 控制点响应事件参数
 */
class ResponseEventParam
{
public:
    ResponseEventParam(const float& xstart_, const float& ystart_, const float& xend_, const float& yend_, const ControlAttributes& attr_, bool istemp_ = false)
        : xstart(xstart_), ystart(ystart_), xend(xend_), yend(yend_), attr(attr_), istemp(istemp_)
    {}
    ~ResponseEventParam() = default;

    bool  istemp;  ///< 是否是临时响应
    float xstart;  ///< 控制点响应的起始X坐标
    float ystart;  ///< 控制点响应的起始Y坐标
    float xend;    ///< 控制点响应的结束X坐标
    float yend;    ///< 控制点响应的结束Y坐标
    ControlAttributes attr;  ///< 控制点的属性
};
class TryResponseEventParam
{
public:
    TryResponseEventParam(const float& x_, const float& y_, const double& max_limit_)
        :x(x_), y(y_), max_limit(max_limit_)
    {}
    ~TryResponseEventParam() = default;

    float x;               ///< 响应的X坐标
    float y;               ///< 响应的Y坐标
    double max_limit;      ///< 响应最大范围
};

#endif //! GRAPHICSEVENTPARAM_HPP