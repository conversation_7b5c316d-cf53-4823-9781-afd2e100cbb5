﻿/*****************************************************************
 * @file   newfileview.h
 * @brief  新建工程文件界面
 * @details 主要有输入工程名称，产品长度，产品宽度功能
 * <AUTHOR>
 * @date 2024.8.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.19          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSNEWFILEVIEW_H__
#define __JRSNEWFILEVIEW_H__
 //QT
#pragma warning(push, 3)
#include <QWidget>
#pragma warning(pop)
QT_BEGIN_NAMESPACE
namespace Ui { class NewFileView; };
QT_END_NAMESPACE

namespace jrsdata { struct ProjectEventInfo; };
namespace jrsaoi
{
    class NewFileView : public QWidget
    {
        Q_OBJECT
    public:
        NewFileView(QWidget* parent = nullptr);
        ~NewFileView();
        void UpdateNewFileProductWidth(double track_width1, double track_width2);
    signals:
        /**
         * @fun SigConfirmFileInfo
         * @brief
         * @param info
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigConfirmFileInfo(const jrsdata::ProjectEventInfo& info);

        void SigGetProductWidth();
    private slots:
        /**
        * @fun SlotConfirm
        * @brief 确认新建工程
        * <AUTHOR>
        * @date 2024.8.20
        */
        void SlotConfirm();

        /**
         * @fun SlotProjectName
         * @brief
         * @param project_name_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotProjectName(const QString& project_name_);

        /**
       * @fun SlotGetProductWidth
       * @brief 获取板宽
       * @param project_name_
       * @date 2024.11.25
       * <AUTHOR>
       */
        void SlotGetProductWidth();

        /**
         * @fun InitConnect
         * @brief 初始信号链接
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
    private:
        Ui::NewFileView* ui;
    };
}
#endif // !__JRSNEWFILEVIEW_H__
