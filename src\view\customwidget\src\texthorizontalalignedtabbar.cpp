#include "texthorizontalalignedtabbar.h"


TextHorizontalAlignedTabBar::TextHorizontalAlignedTabBar()
{

}

QSize TextHorizontalAlignedTabBar::tabSizeHint(int index) const
{
    QSize s = QTabBar::tabSizeHint(index);
    s.transpose();
    return s;
}

void TextHorizontalAlignedTabBar::paintEvent(QPaintEvent*)
{
    QStylePainter painter(this);
    QStyleOptionTab opt;
    for (int i = 0;i < count();i++)
    {
        initStyleOption(&opt, i);
        painter.drawControl(QStyle::CE_TabBarTabShape, opt); // 替换 QSytle
        painter.save();

        QSize s = opt.rect.size();
        s.transpose();
        QRect r(QPoint(), s);
        r.moveCenter(opt.rect.center());
        opt.rect = r;

        QPoint c = tabRect(i).center();
        painter.translate(c);
        painter.rotate(90);
        painter.translate(-c);
        painter.drawControl(QStyle::CE_TabBarTab<PERSON>abel, opt);
        painter.restore();

    }

}