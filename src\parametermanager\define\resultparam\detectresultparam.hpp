/*****************************************************************
 * @file   detectresultparam.hpp
 * @brief  流程整板检测结果的数据结构
 * @details 主要用于自动流程中算法检测完整板所有元件后，将所有的结果保存下来，然后传给数据层，将所有结果保存到数据库中
 * <AUTHOR>
 * @date 2024.11.14
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.14          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSDETECTRESULTPARAM_HPP__
#define __JRSDETECTRESULTPARAM_HPP__
 //STD
#include <iostream>
#include <vector>
#include <unordered_map>
#include <tuple>
//Custom
#include "parambase.hpp"
#include "image.hpp"
#include "projectparam.hpp"
namespace jrsoperator
{
    struct OperatorParamBase;
    using OperatorParamBasePtr = std::shared_ptr<OperatorParamBase>;
}
namespace jrsdata
{

    /** 元件检测结果,主要作用是用于更新右侧显示列表区域 */
    struct ComponentDetectResult
    {
        std::string component_name; //! 元件名称
        std::string subboard_id;//! 子板id
        std::string sub_board_name;//! 子板名称
        std::string part_number; //!料号名称
        std::string defect_name; //! 缺陷名称
        bool result_status;  //! 元件检测状态OK还是NG
        std::vector<int> fov_ids;   //! 元件所在的FOV ids,如果当前元件在多个FOV中,则需要传入多个FOV id
        std::unordered_map<std::string, jrsoperator::OperatorParamBasePtr> detect_window_results; //! 检测框检测结果
        /** 默认构造函数 */
        ComponentDetectResult() :
            result_status(false),
            component_name{},
            part_number{},
            subboard_id{},
            defect_name{},
            fov_ids{}
        {
        }
        /** 带初始化参数的构造函数 */
        ComponentDetectResult(std::string comp_name, std::string subboard_id_, std::string sub_board_name_, std::string part_num, std::string defect_name_, bool status)
            : component_name(std::move(comp_name)),
            part_number(std::move(part_num)),
            subboard_id(std::move(subboard_id_)),
            sub_board_name(std::move(sub_board_name_)),
            defect_name(std::move(defect_name_)),
            result_status(status)
        {
        }

        /** 额外支持记录失败的检测框 */
        ComponentDetectResult(std::string comp_name, std::string subboard_id_, std::string sub_board_name_, std::string part_num, std::string defect_name_, bool status, std::vector<std::string> failed_windows)
            : component_name(std::move(comp_name)),
            part_number(std::move(part_num)),
            subboard_id(std::move(subboard_id_)),
            sub_board_name(std::move(sub_board_name_)),
            defect_name(std::move(defect_name_)),
            result_status(status)
        {
        }
        std::vector<JrsVariant> ToVariantVector() const
        {
            return
            {
                jrsdata::JrsVariant{component_name},
                jrsdata::JrsVariant{subboard_id},
                jrsdata::JrsVariant{defect_name},
                jrsdata::JrsVariant{result_status}
            };
        }

    };

    struct DetectResultViewParam
    {
        /**< 检测结果 */
        std::string project_name;/**< 工程名称*/
        std::string worker_num;/**< 工单号*/
        std::string machine_start_time; /**< 机器开始时间*/
        std::string machine_running_time; /**< 机器运行时间*/
        int total_component_num; /**< 总元件数*/
        int detect_component_num; /**< 检测元件数*/
        int ok_component_num; /**< 检测OK数*/
        int ng_component_num; /**< 检测NG数*/
        std::string photograph_take_time; /**< 拍照时间 单位 s*/
        std::string loop_take_time; /**< 循环时间 单位 s*/
        int mark_status = -1;//-1 初始化 0 NORMAL mark识别正常 1 mark 识别失败
        std::map<std::string/*mark名称*/, std::unordered_map<int/*light id*/, cv::Mat>> mark_imgs; /**<mark信息*/
        std::vector<jrsdata::ComponentDetectResult> mark_results;
        std::map<std::string/*mark名称*/, float/*mark 分数*/> mark_scores;
        std::map<std::string/*mark名称*/, std::tuple<float/*x*/, float/*y*/, float/*width*/, float/*height*/>/*矩形框*/> mark_result_rect;
    };

    /**
     * .  by zhangyuyu 2025.4.14
     * 在线调试时需要保存的参数
     *
     */
    struct OnlineDebugParm
    {
        std::map<int, OneFovImgs> ng_fov_imgs;/**< ng元件所在的FOV图片 key:fov的id,value:fov的图片4个光源图都保存下来*/
        std::map<std::string, ComponentDetectResult> ng_component_detect_results;/**< ng元件的检测结果 key：元件名称，value:元件检测结果*/
        Board work_flow_board_info;/**< 流程运行中的板子信息*/
        OnlineDebugParm()
            : ng_fov_imgs({})
            , ng_component_detect_results({})
            , work_flow_board_info({})
        {
        }
    };


    /**
     * 获取从数据库中查询的结果信息.
     */
    struct QueryDatabaseResult :ViewParamBase
    {
        struct QueryCustomSubboardBarcode
        {
            struct Input
            {
                std::string subboard_barcode;
            };
            struct Output
            {
                int board_id;               /**< 整板ID */
                std::unordered_map<int, std::string> subboard_id_and_barcode; /**<子板id 和子板二维码*/
            };
            Input input; /**< 输入参数 */
            Output output; /**< 输出参数 */
        };

        QueryCustomSubboardBarcode query_custom_subboard_barcode; /**<查询出来的结果*/
    };
    using QueryDatabaseResultPtr = std::shared_ptr<QueryDatabaseResult>;

    using DetectResultViewParamPtr = std::shared_ptr<DetectResultViewParam>;
    using ComponentDetectResultPtr = std::shared_ptr<ComponentDetectResult>;
    using OnlineDebugParmPtr = std::shared_ptr<OnlineDebugParm>;
}

#endif // !__JRSDETECTRESULTPARAM_HPP__