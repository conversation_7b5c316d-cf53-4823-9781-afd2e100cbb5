#ifndef __IMAGEPROCESSALGO_H__
#define __IMAGEPROCESSALGO_H__

#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// opencv
#include <opencv2/opencv.hpp>

// custom
#include "colorparams.h"
#pragma warning(pop)

namespace colorwheel
{
  /**
   * @brief 标记结果图像是否为二值图像 true 二值图 false 非二值图
   */
  extern bool res_image_is_binary;

  /**
   * @brief 获取预处理结果
   * @param input_image 输入图像
   * @param params 颜色参数
   * @param output_img 输出图像
   * @return 成功返回0，失败返回非0值
   */
  int GetPreProcessResult(const cv::Mat& input_image, const ColorParams& params,
      cv::Mat& output_img);

  /**
   * @brief 应用颜色参数到图像
   * @param color_param 颜色参数
   * @param image 输入图像
   * @return 处理后的图像
   */
  cv::Mat ApplyColorParam(const ColorParams& color_param,
    const cv::Mat& image);
  
  /**
   * @brief 使用二值图像作为掩码处理源图像
   * @param source_image 源图像
   * @param binary_mask 二值掩码图像
   * @return 掩码处理后的图像
   */
  cv::Mat MaskImageByBinary(const cv::Mat& source_image, const cv::Mat& binary_mask);
}

#endif // __IMAGEPROCESSALGO_H__