﻿#include "renderer2dmanager.h"
#include "renderer2dwidget.h"
#include "glwindow.h"
#include "rulerwidget.h"
#include "thumbnailnavigationwidget.h"
#include "visualcameratopdown.h"
#include "windowsignalemitter.h"
#include "renderer.h"

#include "renderbackground.h"
#include "renderfore.h"
#include "rendertexture.h"

#include "customcursortype.hpp"
#include "engineconstants.hpp"

#include "log.h"


#include <QTimer>
#include <QScreen> // screen
#include <QApplication> // desktop
#include <QDesktopWidget> // winId

// #define TEST_RENDER_FPS
#ifdef TEST_RENDER_FPS
#include "rendertest.h"
#endif
#include "graphicsparam.hpp"
#include "viewergraphicsviewimpl.h"

Renderer2DManager::Renderer2DManager()
    : widget(new Renderer2DWidget())
    , m_render_window(new GLWindow(widget))
    , m_thumbnail(new ThumbnailNavigationWidget(120, 120, m_render_window))
    , horizontalRuler(new RulerWidget(Qt::Orientation::Horizontal))
    , verticalRuler(new RulerWidget(Qt::Orientation::Vertical))
    , main_camera(new VisualCameraTopDown({ 0, 0, 500.0f }))
    , ro_texture(std::make_shared<RenderTexture>())
    , ro_foreground(std::make_shared<RenderFore>())
    , ro_background(std::make_shared<RenderBackground>())
    , timer_key_handle(new QTimer(widget))
    , _image_render(new ViewerGraphicsViewImpl())
{
    if (m_render_window)
    {
        SetRendererObject(ro_background, RenderType::RT_Background);
        SetRendererObject(ro_texture, RenderType::RT_Background);
        SetRendererObject(ro_foreground, RenderType::RT_Foreground);

#ifdef TEST_RENDER_FPS
        SetRendererObject(std::make_shared<RenderTest>(), RenderType::RT_Background);
#endif
        m_render_window->SetMainVisualCamera(main_camera);
        widget->AddRenderer(m_render_window->asWidget());
    }
    if (_image_render)
    {

        _image_render->setMouseTracking(true);
        widget->AddImageRender(_image_render);

    }
    if (horizontalRuler)
    {
        //verticalRuler->SetScale(0.002);
        //verticalRuler->SetPrecision(3);
        verticalRuler->SetRulerTextAlignment(RulerTextAlignment::BELOW);
        widget->AddRulerHorizontal(horizontalRuler);
        horizontalRuler->setMouseTracking(true);
    }
    if (verticalRuler)
    {
        verticalRuler->SetRulerTextAlignment(RulerTextAlignment::ABOVE);
        widget->AddRulerVertical(verticalRuler);
        verticalRuler->setMouseTracking(true);
    }
    RegisterSlot();
}

Renderer2DManager::~Renderer2DManager()
{
    if (main_camera)
    {
        delete main_camera;
        main_camera = nullptr;
    }
    /*qt控件会自行释放内存*/
}

QWidget* Renderer2DManager::GetWidget()
{
    return widget;
}

QWidget* const Renderer2DManager::GetGLWindow() const
{
    return m_render_window;
}

void Renderer2DManager::Update()
{
    m_render_window->update();
    // TODO 缩略图更新图片
}

void Renderer2DManager::SetMouseTracking(bool enable)
{
    m_render_window->setMouseTracking(enable);
}

// void Renderer2DManager::SetCursor(int type)
// {
//     if (type > 0)
//     {
//         widget->setCursor(QCursor(Qt::CursorShape(type)));
//     }
//     else
//     {
//         widget->unsetCursor();
//     }
// }

void Renderer2DManager::SetRulerValue(const float& xmin, const float& xmax, const float& ymin, const float& ymax)
{
    horizontalRuler->SetRange(xmin, xmax);
    verticalRuler->SetRange(ymin, ymax);
}

void Renderer2DManager::SetRulerScale(double scale)
{
    horizontalRuler->SetScale(scale);
    verticalRuler->SetScale(scale);
}

void Renderer2DManager::SetRulerChangeUseScale()
{
    horizontalRuler->ChangeUseScale();
    verticalRuler->ChangeUseScale();
}

void Renderer2DManager::SetRulerPrecision(int precision)
{
    if (precision < 0)
        return;
    horizontalRuler->SetPrecision(precision);
    verticalRuler->SetPrecision(precision);
}

bool Renderer2DManager::SetThumbnailNavigation(const cv::Mat& image, int image_true_w, int image_true_h)
{
    if (!m_thumbnail)
        return false;

    return m_thumbnail->SetImage(image, image_true_w, image_true_h);
}

void Renderer2DManager::ClearThumbnailNavigation()
{
    if (!m_thumbnail)
        return;
    return m_thumbnail->ClearImage();
}

void Renderer2DManager::SetThumbnailViewport(float left, float top, float right, float bottom)
{
    if (!m_thumbnail)
        return;
    m_thumbnail->SetViewport(left, top, right, bottom);
    // m_thumbnail->SetRenderRegionWithImageSize(x, y, width, height);
}

void Renderer2DManager::SetThumbnailShow(bool state)
{
    if (!m_thumbnail)
        return;
    state ? m_thumbnail->show() : m_thumbnail->hide();
}

bool Renderer2DManager::GetThumbnailShow() const
{
    if (!m_thumbnail)
        return false;
    return m_thumbnail->isVisible();
}

void Renderer2DManager::SetRendererObject(RenderAbstractPtr ro, const RenderType& type)
{
    m_render_window->AddObject(ro, type);
    // static_cast<RenderType>(type));
}

void Renderer2DManager::ClearTexture(int z)
{
    auto ro = std::dynamic_pointer_cast<RenderTexture>(ro_texture);
    if (!ro)
        return;

    if (z > 0)
    {
        ro->ClearTexture(z);
    }
    else
    {
        ClearThumbnailNavigation();
        ro->Clear();
    }
}

unsigned int Renderer2DManager::CreateTexture(const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center)
{
    auto ro = std::dynamic_pointer_cast<RenderTexture>(ro_texture);
    auto id = ro->CreateTextureWithCVMat(image, x, y, z, angle, is_draw_center);

    // m_render_window->update();
    return id;
}

int Renderer2DManager::CreateImage(int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center,
    int current_show_image_key_)
{
    return _image_render->CreateImageAsync(0, key_, image, x, y, z, angle, is_draw_center, current_show_image_key_);
}
int Renderer2DManager::CreateImages(const GraphicsImage& graphics_img_)
{
    return _image_render->CreateImagesAsync(graphics_img_);
}
int Renderer2DManager::AddGraphicsShapes(const GraphicsShape& graphics_shape_)
{
    return _image_render->AddGraphicsShapes(graphics_shape_);
}
int Renderer2DManager::ClearGraphicsShapes()
{
    return _image_render->ClearGraphicsShapes();
}
int Renderer2DManager::ClearImage(const int& set_key_, int key_)
{
    return _image_render->ClearImage(set_key_, key_);
}

int Renderer2DManager::ShowImage(const uint8_t& set_key_, int key_)
{
    return _image_render->ShowImageByKey(set_key_, key_);
}

int Renderer2DManager::SetTextureZ(int z, const std::vector<unsigned int>& ids)
{
    // assert(ro_texture);
    auto ro = std::dynamic_pointer_cast<RenderTexture>(ro_texture);
    return ro->SetTextureZ(z, ids);
}

int Renderer2DManager::FindTextureZ(int& z, unsigned int id)
{
    // assert(ro_texture);
    auto ro = std::dynamic_pointer_cast<RenderTexture>(ro_texture);
    return ro->FindTextureZ(z, id);
}

int Renderer2DManager::FindZTexture(std::vector<unsigned int>& v, int z)
{
    // assert(ro_texture);
    auto ro = std::dynamic_pointer_cast<RenderTexture>(ro_texture);
    return ro->FindZTexture(v, z);
}

void Renderer2DManager::MoveCamera(int direction)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        camera->Move(static_cast<CameraDirection>(direction));
        //UpdateImagerRenderZoom();
    }
    // m_render_window->MoveCamera(direction);
    TriggerCallbackCameramove();
}

void Renderer2DManager::MoveCamera(float xoffset, float yoffset)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        auto p = camera->GetCameraPosition();
        camera->SetCameraPosition(p.x() + xoffset, p.y() - yoffset);

    }
    // m_render_window->MoveCamera(xoffset, yoffset);
    TriggerCallbackCameramove();
}

void Renderer2DManager::ResetCamera(int type)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        camera->SetResetMode(static_cast<CameraResetMode>(type));
    }
    // m_render_window->ResetCamera(type);
    TriggerCallbackCameramove();
}

void Renderer2DManager::MoveCameraTo(float x, float y)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        camera->SetCameraPosition(x, -y);
    }
    // m_render_window->MoveCameraTo(x, y);
    TriggerCallbackCameramove();
}

void Renderer2DManager::SetZoom(float zoom)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        camera->SetZoom(zoom);
    }
    // m_render_window->SetZoom(zoom);
    TriggerCallbackCameramove();
}

float Renderer2DManager::GetZoom()
{
    return main_camera->GetZoom();
}

void Renderer2DManager::SetZoomState(int state)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        camera->SetScaleMode(static_cast<CameraScaleMode>(state));
    }

    // m_render_window->SetZoomState(state);
    TriggerCallbackCameramove();
}

bool Renderer2DManager::SetCanvasSize(int width, int height)
{
    if (width <= 0 || height <= 0)
        return false;

    if (main_camera)
    {
        auto& camera = main_camera;
        auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
        if (!topdown)
            return false;
        if (!topdown->SetCanvas(width, height))
            return false;
    }
    else
    {
        return false;
    }
    // m_render_window->SetCanvasSize(width, height);
    auto state = m_thumbnail->SetRegion(width, height);
    if (!state)
    {
        return false;
    }

    TriggerCallbackCameramove();
    return true;
}

bool Renderer2DManager::GetCanvasSize(int& width, int& height)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
        if (!topdown)
            return false;
        topdown->GetCanvas(width, height);
        return (width > 0 && height > 0);
    }
    else
    {
        return false;
    }
}

bool Renderer2DManager::SetLimitViewByCanvas(bool state)
{
    if (main_camera)
    {
        auto& camera = main_camera;
        auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
        if (!topdown)
            return false;
        if (!topdown->IsCanvasValid())
            return false;
        topdown->SetLimitViewByCanvas(state);
        return true;
    }
    else
    {
        return false;
    }
}

bool Renderer2DManager::GetLimitViewByCanvas()
{
    if (!main_camera)
        return false;
    auto& camera = main_camera;
    auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
    if (!topdown)
        return false;
    if (!topdown->IsCanvasValid())
        return false;
    return topdown->GetLimitViewByCanvas();
}

void Renderer2DManager::GetGLWindowSize(int& width, int& height)
{
    width = m_render_window->qtWidth();
    height = m_render_window->qtHeight();
}

void Renderer2DManager::HoverColor([[maybe_unused]] int x, [[maybe_unused]] int y)
{
    auto globalpoint = QCursor::pos();
    // auto globalpoint = m_render_window->mapToGlobal(QCursor::pos());
    QPixmap pixmap = m_render_window->screen()->grabWindow(QApplication::desktop()->winId(), globalpoint.x(), globalpoint.y(), 1, 1);

    int red, green, blue, gray;
    red = green = blue = gray = 0;
    if (!pixmap.isNull())
    {
        QImage image = pixmap.toImage();

        if (!image.isNull())
        {
            QColor color = image.pixel(0, 0);
            red = color.red();
            green = color.green();
            blue = color.blue();
            gray = qGray(red, green, blue);
        }
    }
    auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
    ro->SetHoverColor(red, green, blue, gray);

    m_render_window->update();
}

void Renderer2DManager::SetShowDebugInfo(bool state)
{
    auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
    if (!ro)
        return;
    ro->SetDrawSystemInfo(state);
}

void Renderer2DManager::SetShowCenterCrossLine(bool is_draw_)
{
    auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
    if (!ro)
        return;
    ro->SetDrawCenterCrossLine(is_draw_);
}

bool Renderer2DManager::GetIsShowCenterCrossLine()
{
    auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
    if (!ro)
        return false;
    return ro->GetIsDrawCenterCrossLine();
}

void Renderer2DManager::SetShowMessage(const std::string& message_, int duration_, int size_)
{
    auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
    if (!ro)
        return;
    RenderMessage message(message_, duration_, size_);
    ro->SetMessage(message);
}

void Renderer2DManager::SetCallbackRendermouseclicked(
    std::function<void(int, int, int)> callback)
{
    callback_rendermouseclicked += callback;
}

void Renderer2DManager::SetCallbackRendermousepress(
    std::function<void(int, int, int)> callback)
{
    callback_rendermousepress += callback;
}

void Renderer2DManager::SetCallbackRendermouserelease(
    std::function<void(int, int, int, int, int)> callback)
{
    callback_rendermouserelease += callback;
}

void Renderer2DManager::SetCallbackRendermousemove(
    std::function<void(int, int, int, int, int, int, int)> callback)
{
    callback_rendermousemove += callback;
}

void Renderer2DManager::SetCallbackRenderwheeldelta(
    std::function<void(int, int, int)> callback)
{
    callback_renderwheeldelta += callback;
}

void Renderer2DManager::SetCallbackThumbnailmousemove(
    std::function<void(int, float, float)> callback)
{
    callback_thumbnailmousemove += callback;
}

void Renderer2DManager::SetCallbackThumbnailmouseenter(std::function<void()> callback)
{
    callback_thumbnailmouseenter += callback;
}

void Renderer2DManager::SetCallbackThumbnailmouseleave(std::function<void()> callback)
{
    callback_thumbnailmouseleave += callback;
}

void Renderer2DManager::SetCallbackWindowsizechange(
    std::function<void(int, int)> callback)
{
    callback_windowsizechange += callback;
}

void Renderer2DManager::SetCallbackCameramove(std::function<void()> callback)
{
    callback_cameramove += callback;
}

void Renderer2DManager::SetCallbackCursorchange(std::function<void(int)> callback)
{
    callback_cursorchange += callback;
}

// void Renderer2DManager::TriggerCallbackscenechange(int type, int x, int y)
//{
//     if (!callbackscenechange) return;
//     callbackscenechange(type, x, y);
// }
void Renderer2DManager::RegisterSlot()
{
    QObject::connect(timer_key_handle, &QTimer::timeout, widget,
        [=]()
        {
            this->HandlerTimerOut();
        });

    auto emitter = m_render_window->signalEmitter();
    QObject::connect(emitter, &WindowSignalEmitter::signal_mouse_clicked, widget,
        [=](int type, int x, int y)
        {
            // 避免出现判断A调用B的情况
            auto& function = this->callback_rendermouseclicked;
            function(type, x, y);
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_mouse_press, widget,
        [=](int type, int x, int y)
        {
            auto& function = this->callback_rendermousepress;
            function(type, x, y);
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_mouse_release, widget,
        [=](int type, int cx, int cy, int px, int py)
        {
            auto& function = this->callback_rendermouserelease;
            function(type, cx, cy, px, py);
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_mouse_move, widget,
        [=](int type, int cx, int cy, int lx, int ly, int px, int py)
        {
            auto& function = this->callback_rendermousemove;
            function(type, cx, cy, lx, ly, px, py);
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_wheel_delta, widget,
        [=](int delta, int x, int y)
        {
            auto& function = this->callback_renderwheeldelta;
            function(delta, x, y);
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_key_press, widget,
        [=](int key, bool isautorepeat)
        {
            if (!isautorepeat)
            {
                m_map_keys[key] = true; // Add key to map
                if (!timer_key_handle->isActive())
                {
                    timer_key_handle->start(10);
                }
            }
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_key_release, widget,
        [=](int key, bool isautorepeat)
        {
            if (!isautorepeat)
            {
                m_map_keys.erase(key);
                if (m_map_keys.empty() && timer_key_handle->isActive())
                {
                    timer_key_handle->stop();
                }
            }
        });

    QObject::connect(emitter, &WindowSignalEmitter::signal_window_resize, widget,
        [=](int width, int height)
        {
            auto& thumbnail = m_thumbnail;

            const int gap = 10; // 与屏幕边界间隔

            QPoint pos = widget->pos();
            pos.setX(pos.x() + width - thumbnail->width() - gap);
            pos.setY(pos.y() + height - thumbnail->height() - gap);
            thumbnail->move(pos);

            thumbnail->raise();

            TriggerCallbackCameramove();
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_window_leave, widget,
        [=]()
        {
            auto& function = this->callback_cursorchange;
            function(static_cast<int>(CustomCursorType::Reset));
        });
    QObject::connect(emitter, &WindowSignalEmitter::signal_window_enter, widget,
        [=]()
        {
            //auto& function = this->callback_cursorchange;
            //function(static_cast<int>(CustomCursorType::Default));
        });
    // 缩略图事件
    QObject::connect(m_thumbnail, &ThumbnailNavigationWidget::SignalViewportCenterChange, widget,
        [=](float x, float y)
        {
            auto& function = this->callback_thumbnailmousemove;
            function(0, x, y);
        });
    QObject::connect(m_thumbnail, &ThumbnailNavigationWidget::SignalMouseEnter, widget,
        [=]()
        {
            auto& function = this->callback_thumbnailmouseenter;
            function();
        });
    QObject::connect(m_thumbnail, &ThumbnailNavigationWidget::SignalMouseLeave, widget,
        [=]()
        {
            auto& function = this->callback_thumbnailmouseleave;
            function();
        });

    QObject::connect(widget, &Renderer2DWidget::SignalChangeUseState, widget,
        [=]()
        {
            SetRulerChangeUseScale();
        });
    //QObject::connect(widget, &Renderer2DWidget::SigUpdateZoom, widget,
    //    [=]()
    //    {
    //        //UpdateImagerRenderZoom();
    //    });
}

void Renderer2DManager::UpdateImagerRenderZoom()
{
    auto zoom = main_camera->GetZoom();
    _image_render->SlotSetZoom(zoom);
    //QMetaObject::invokeMethod(_image_render,
    //    "SlotSetZoom",
    //    Qt::QueuedConnection,
    //    Q_ARG(const float&, zoom)
    //);
}
void Renderer2DManager::UpdateImageRenderCameraMove()
{
    auto view_matrix = main_camera->GetViewMatrix();
    if (!view_matrix)
    {
        return;
    }
    float point_x = (*view_matrix)(0, 3);
    float point_y = (*view_matrix)(1, 3);
    _image_render->SlotMoveCamera(point_x, point_y);
    /* QMetaObject::invokeMethod(_image_render,
         "SlotMoveCamera",
         Qt::QueuedConnection,
         Q_ARG(const double&, point_x),
         Q_ARG(const double&, point_y)
     ); */
}
void Renderer2DManager::HandlerTimerOut()
{
    if (m_map_keys.empty())
    {
        if (timer_key_handle->isActive())
        {
            timer_key_handle->stop();
        }
        return;
    }

    if (m_map_keys.count(Qt::Key_Q))
    { // 放大图片
        MoveCamera(static_cast<int>(CameraDirection::Rear));
    }
    if (m_map_keys.count(Qt::Key_E))
    { // 缩小图片
        MoveCamera(static_cast<int>(CameraDirection::Front));
    }
    if (m_map_keys.count(Qt::Key_W))
    { // 上
        MoveCamera(static_cast<int>(CameraDirection::Up));
    }
    if (m_map_keys.count(Qt::Key_S))
    { // 下
        MoveCamera(static_cast<int>(CameraDirection::Down));
    }
    if (m_map_keys.count(Qt::Key_A))
    { // 左
        MoveCamera(static_cast<int>(CameraDirection::Left));
    }
    if (m_map_keys.count(Qt::Key_D))
    { // 右
        MoveCamera(static_cast<int>(CameraDirection::Right));
    }
}

void Renderer2DManager::TriggerCallbackCameramove()
{
    // 临时测试用
    if (main_camera)
    {
        UpdateImagerRenderZoom();
        UpdateImageRenderCameraMove();
        auto zoom = main_camera->GetZoom();
        auto ro = std::dynamic_pointer_cast<RenderFore>(ro_foreground);
        ro->SetDrawZoom(zoom);
    }
    callback_cameramove();
}
