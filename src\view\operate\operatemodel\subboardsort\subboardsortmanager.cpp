#include "subboardsortmanager.h"

#include "projectdataprocess.h"

//sort type
#include "left_top_to_right_bottom_s.hpp"
#include "left_bottom_to_right_top_s.hpp"
#include "right_top_to_left_bottom_s.hpp"
#include "right_bottom_to_left_top_s.hpp"

#include "left_top_to_right_bottom_z.hpp"
#include "left_bottom_to_right_top_z.hpp"
#include "right_top_to_left_bottom_z.hpp"
#include "right_bottom_to_left_top_z.hpp"

#include "left_top_to_right_bottom_vertical_s.hpp"
#include "left_bottom_to_right_top_vertical_s.hpp"
#include "right_top_to_left_bottom_vertical_s.hpp"
#include "right_bottom_to_left_top_vertical_s.hpp"

#include "left_top_to_right_bottom_vertical_z.hpp"
#include "left_bottom_to_right_top_vertical_z.hpp"
#include "right_top_to_left_bottom_vertical_z.hpp"
#include "right_bottom_to_left_top_vertical_z.hpp"

using SubboardSortCreator = std::function<subboardsort::SubboardSortBasePtr()>;

struct ImplData
{
    std::vector<jrsdata::SubBoard> subboards;
    std::map<std::pair<jrsdata::SubboardSortParam::SortShape, jrsdata::SubboardSortParam::SubBoardDirection>, SubboardSortCreator>  sort_factory;
};

subboardsort::SubboardSortManager::SubboardSortManager()
    : _impl_data(new ImplData())
{
    _impl_data->sort_factory = {
    {{jrsdata::SubboardSortParam::SortShape::S, jrsdata::SubboardSortParam::SubBoardDirection::LeftTopToRightBottom}, [] { return std::make_shared<subboardsort::SLeftTopToRightBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::S, jrsdata::SubboardSortParam::SubBoardDirection::LeftBottomToRightTop}, [] { return std::make_shared<subboardsort::SLeftBottomToRightTop>(); }},
    {{jrsdata::SubboardSortParam::SortShape::S, jrsdata::SubboardSortParam::SubBoardDirection::RightTopToLeftBottom}, [] { return std::make_shared<subboardsort::SRightTopToLeftBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::S, jrsdata::SubboardSortParam::SubBoardDirection::RightBottomToLeftTop}, [] { return std::make_shared<subboardsort::SRightBottomToLeftTop>(); }},

    {{jrsdata::SubboardSortParam::SortShape::Z, jrsdata::SubboardSortParam::SubBoardDirection::LeftTopToRightBottom}, [] { return std::make_shared<subboardsort::ZLeftTopToRightBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Z, jrsdata::SubboardSortParam::SubBoardDirection::LeftBottomToRightTop}, [] { return std::make_shared<subboardsort::ZLeftBottomToRightTop>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Z, jrsdata::SubboardSortParam::SubBoardDirection::RightTopToLeftBottom}, [] { return std::make_shared<subboardsort::ZRightTopToLeftBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Z, jrsdata::SubboardSortParam::SubBoardDirection::RightBottomToLeftTop}, [] { return std::make_shared<subboardsort::ZRightBottomToLeftTop>(); }},

    {{jrsdata::SubboardSortParam::SortShape::Vertical_S, jrsdata::SubboardSortParam::SubBoardDirection::LeftTopToRightBottom}, [] { return std::make_shared<subboardsort::SVerticalLeftTopToRightBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_S, jrsdata::SubboardSortParam::SubBoardDirection::LeftBottomToRightTop}, [] { return std::make_shared<subboardsort::SVerticalLeftBottomToRightTop>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_S, jrsdata::SubboardSortParam::SubBoardDirection::RightTopToLeftBottom}, [] { return std::make_shared<subboardsort::SVerticalRightTopToLeftBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_S, jrsdata::SubboardSortParam::SubBoardDirection::RightBottomToLeftTop}, [] { return std::make_shared<subboardsort::SVerticalRightBottomToLeftTop>(); }},

    {{jrsdata::SubboardSortParam::SortShape::Vertical_Z, jrsdata::SubboardSortParam::SubBoardDirection::LeftTopToRightBottom}, [] { return std::make_shared<subboardsort::ZVerticalLeftTopToRightBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_Z, jrsdata::SubboardSortParam::SubBoardDirection::LeftBottomToRightTop}, [] { return std::make_shared<subboardsort::ZVerticalLeftBottomToRightTop>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_Z, jrsdata::SubboardSortParam::SubBoardDirection::RightTopToLeftBottom}, [] { return std::make_shared<subboardsort::ZVerticalRightTopToLeftBottom>(); }},
    {{jrsdata::SubboardSortParam::SortShape::Vertical_Z, jrsdata::SubboardSortParam::SubBoardDirection::RightBottomToLeftTop}, [] { return std::make_shared<subboardsort::ZVerticalRightBottomToLeftTop>(); }}
    };
}

subboardsort::SubboardSortManager::~SubboardSortManager()
{
    if (_impl_data)
    {
        delete _impl_data;
        _impl_data = NULL;
    }
}

int subboardsort::SubboardSortManager::SubboardSort(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_data_process_,
    jrsdata::SubboardSortParam sort_type_)
{
    auto project_param_ptr = project_data_process_->GetProjectParam();
    auto subboard_groups = project_data_process_->CalculateRowsAndCols(project_param_ptr);

    if (subboard_groups.empty())
    {
        LogTo_ERROR("subboard sort", "子板排序失败,没有子板信息");
        return -1;
    }

    auto sort_key = std::make_pair(sort_type_.sort_shape, sort_type_.subboard_direction);
    auto it = _impl_data->sort_factory.find(sort_key);

    if (it == _impl_data->sort_factory.end())
    {
        LogTo_ERROR("subboard sort", "子板排序类型未定义: shape=%d, direction=%d",
            static_cast<int>(sort_type_.sort_shape), static_cast<int>(sort_type_.subboard_direction));
        return -1;
    }

    auto subboard_sort_ptr = it->second(); // 创建对应排序器

    auto res = subboard_sort_ptr->RegularSort(subboard_groups);
    if (res != jrscore::AOI_OK)
    {
        LogTo_ERROR("subboard sort", "排序器执行失败");
        return res;
    }
    // 需要确认再更新
    _impl_data->subboards.clear();
    _impl_data->subboards.reserve(project_param_ptr->board_info.sub_board.size());
    for (auto& group : subboard_groups)
    {
        for (auto& sub : group)
        {
            _impl_data->subboards.emplace_back(sub);
        }
    }

    return jrscore::AOI_OK;
}

std::vector<jrsdata::SubBoard> subboardsort::SubboardSortManager::GetSortedSubboards() const
{
    return _impl_data->subboards;
}

void subboardsort::SubboardSortManager::UpdateSortedSubboardToProject(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_data_process_)
{
    if (!project_data_process_)
    {
        JRSMessageBox_INFO("警告", "更新子板顺序失败, project_data_process_ 为空", jrscore::MessageButton::Ok);
        return;
    }
    for (const auto& subboard : _impl_data->subboards)
    {
        project_data_process_->UpdateSubBoard(subboard); // 更新子板顺序信息
    }
}

void subboardsort::SubboardSortManager::ClearSortedSubboards() const
{
    _impl_data->subboards.clear();
}

