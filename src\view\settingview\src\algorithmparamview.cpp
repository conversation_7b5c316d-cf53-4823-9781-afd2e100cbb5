﻿#include "algorithmparamview.h"

jrsaoi::AlgorithmParamView::AlgorithmParamView(QWidget* parent)
	:QWidget(parent)
{
}

jrsaoi::AlgorithmParamView::~AlgorithmParamView()
{
}

void jrsaoi::AlgorithmParamView::UpdateView(const jrsdata::AlgorithmParamSettingVec& algorithm_params_)
{
	if (algorithm_params_.empty())
	{

	}
}

jrsdata::AlgorithmParamSettingVec jrsaoi::AlgorithmParamView::GetAlgorithmParam()
{
	return jrsdata::AlgorithmParamSettingVec();
}

void jrsaoi::AlgorithmParamView::SlotChooseDirPath()
{
}

void jrsaoi::AlgorithmParamView::InitConnect()
{
}

void jrsaoi::AlgorithmParamView::SlotSaveAlgorithmParam()
{
}

void jrsaoi::AlgorithmParamView::InitMember()
{
}

void jrsaoi::AlgorithmParamView::SaveSystemAlgorithm()
{
}

void jrsaoi::AlgorithmParamView::UpdateStaticParam()
{
}

void jrsaoi::AlgorithmParamView::SetHideController(const jrsdata::AlgorithmParamSettingVec& algorithm_params_)
{
	if (algorithm_params_.empty())
	{

	}
}
