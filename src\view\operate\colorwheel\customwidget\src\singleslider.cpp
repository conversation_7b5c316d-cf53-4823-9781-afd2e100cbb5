#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include "singleslider.h"
#pragma warning(pop)
void CustomSlider::paintEvent(QPaintEvent* event)
{
    {
        QStyleOptionSlider opt;
        initStyleOption(&opt);

        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);

        QRect groove_rect = style()->subControlRect(QStyle::CC_Slider, &opt, QStyle::SC_SliderGroove, this);
        QRect handle_rect = style()->subControlRect(QStyle::CC_Slider, &opt, QStyle::SC_SliderHandle, this);

        painter.setBrush(Qt::white);
        painter.drawRect(groove_rect);

        QRect sliderRect = groove_rect;
        sliderRect.setRight(handle_rect.left());
        painter.setBrush(QBrush(Qt::blue));
        painter.drawRect(sliderRect);

        /*painter.setBrush(Qt::white);
        painter.drawEllipse(handle_rect);*/

        QSlider::paintEvent(event);
    }
}