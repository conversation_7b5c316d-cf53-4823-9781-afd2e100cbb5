﻿#include "renderfore.h"
#include "painter.h"
#include "renderer.h"
#include "log.h"
#include "systemmonitor.hpp"
#include "tools.hpp"

#include <QPainter>
#include <QPainterPath>
#include <QDebug>
// #include <sstream>
//#include <iostream>

const std::vector<Vec2> cross_path{
    {-1, 0},
    {1, 0},
    {0, -1},
    {0, 1},
};
const Color cross_color = Color(0.1f, 0.3f, 0.5f, 0.5f);
const Color text_color = Color(1.0f, 0.99f, 0.27f, 1.0f);
const Color contour_color = Color(125, 125, 125);

#undef min
#undef max
#undef DrawText
void DrawText(QPainter& painter, const QString& text, const float& x, const float& y, [[maybe_unused]] const Color& text_color_, [[maybe_unused]] const Color& contour_color_)
{
    if (!painter.isActive() || text.isEmpty())
        return;

    auto clamp = [](float v) {
        return std::clamp(v, 0.0f, 1.0f);
        };

    QColor contour_qcolor(clamp(contour_color_.r) * 255,
        clamp(contour_color_.g) * 255,
        clamp(contour_color_.b) * 255,
        clamp(contour_color_.a) * 255);

    QColor text_qcolor(clamp(text_color_.r) * 255,
        clamp(text_color_.g) * 255,
        clamp(text_color_.b) * 255,
        clamp(text_color_.a) * 255);

    if (painter.font().family().isEmpty()) {
        painter.setFont(QFont("Arial", 10));
    }

    painter.save();

    QPainterPath path;
    path.addText(x, y, painter.font(), text);

    painter.strokePath(path, QPen(contour_qcolor, 2));
    painter.fillPath(path, text_qcolor);

    painter.restore();
}

RenderFore::RenderFore()
    : RenderAbstract()
    , is_draw_system_info(false)
    , fps(0), cpu_usage(0), mem_usage(0), zoom(0)
    , r(0), g(0), b(0), l(0)
    , frame_count(0)
    , last_time()
    , message("启动")
    , is_draw_center_cross_line(false)
{
}

RenderFore::~RenderFore()
{
}

void RenderFore::Render()
{
    if (!IsHaveRenerer())
        return;

    UpdateFps();

    Painter p(renderer);

    //**< 去除中心线  By:HJC 2025/1/16*/
    DrawCross(&p);
    DrawHoverColor(&p);

    if (is_draw_system_info)
    {
        DrawSystemInfo(&p);
    }
    DrawMessage(&p);
}

void RenderFore::Destroy()
{
}

void RenderFore::SetHoverColor(int r_, int g_, int b_, int l_)
{
    r = r_;
    g = g_;
    b = b_;
    l = l_;
}

void RenderFore::SetDrawZoom(float zoom_)
{
    zoom = zoom_;
}

void RenderFore::SetDrawSystemInfo(bool is_draw_system_info_)
{
    is_draw_system_info = is_draw_system_info_;
}

void RenderFore::SetDrawCenterCrossLine(bool is_draw_center_cross_line_)
{
    is_draw_center_cross_line = is_draw_center_cross_line_;
}

bool RenderFore::GetIsDrawCenterCrossLine()
{
    return is_draw_center_cross_line;
}

void RenderFore::SetMessage(const RenderMessage& message_)
{
    message = message_;
}

void RenderFore::DrawCross(Painter* p)
{
    if (is_draw_center_cross_line)
        p->DrawPolyline(cross_path, Color(0.1f, 0.3f, 0.5f, 1.0f), false);
}

void RenderFore::DrawHoverColor(Painter* p)
{
    (void)p;
    auto pd = renderer->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        const int rect_width = 15;
        const int gap = 5;
        // auto w = device->width();
        auto h = device->height();
        QPainter painter(device);
        painter.setFont(QFont("Arial", 8, QFont::Bold));
        painter.beginNativePainting();
        {
            auto text = QString("R %1 G %2 B %3")
                .arg(r, 3, 10, QLatin1Char(' '))
                .arg(g, 3, 10, QLatin1Char(' '))
                .arg(b, 3, 10, QLatin1Char(' '));
            DrawText(painter, text, rect_width + gap * 2, h - gap * 2 - 8, text_color, contour_color);
            // QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);
            // painter.drawText(QRectF(30, h - 25, textsize.width(), textsize.height()),
            //     Qt::AlignCenter | Qt::TextWordWrap, text);
        }
        {
            auto text = QString("L %4").arg(l, 3, 10, QLatin1Char(' '));
            DrawText(painter, text, rect_width + gap * 2, h - gap, text_color, contour_color);
            // QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);
            // painter.drawText(QRectF(30, h - rect_width, textsize.width(), textsize.height()),
            //     Qt::AlignCenter | Qt::TextWordWrap, text);
        }
        painter.endNativePainting();
        painter.setBrush(QBrush(QColor(r, g, b)));
        painter.drawRect(gap, h - rect_width - gap, rect_width, rect_width);
    }
}

void RenderFore::DrawSystemInfo(Painter*)
{
    auto pd = renderer->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        const int x = 10;
        const int y = 10;
        const int lineHeight = 15;
        int i = 0;
        QPainter painter(device);
        painter.beginNativePainting();
        painter.setFont(QFont("Arial", 8, QFont::Bold));
        DrawText(painter, QString::fromLocal8Bit(render::Tools::GetCurrentTimeString().c_str()), x, y, text_color, contour_color);
        DrawText(painter, QString("FPS: %1").arg(fps, 0, 'f', 2), x, y + lineHeight * ++i, text_color, contour_color);
        DrawText(painter, QString("Memory Usage: %1%").arg(mem_usage, 0, 'f', 2), x, y + lineHeight * ++i, text_color, contour_color);
        DrawText(painter, QString("CPU Usage: %1%").arg(cpu_usage, 0, 'f', 2), x, y + lineHeight * ++i, text_color, contour_color);
        DrawText(painter, QString("ratio: %1%").arg(zoom, 0, 'f', 2), x, y + lineHeight * ++i, text_color, contour_color);
        DrawText(painter, QString("inverse ratio: %1%").arg(1.0f / zoom, 0, 'f', 2), x, y + lineHeight * ++i, text_color, contour_color);
        // painter.drawText(x, y, QString::fromLocal8Bit(getCurrentTimeAsString().c_str()));
        // painter.drawText(x, y + lineHeight * 2, QString("Memory Usage: %1%").arg(mem_usage, 0, 'f', 2));
        // painter.drawText(x, y + lineHeight * 3, QString("CPU Usage: %1%").arg(cpu_usage, 0, 'f', 2));
        // painter.drawText(x, y + lineHeight * 4, QString("ratio: %1%").arg(zoom, 0, 'f', 2));
        // painter.drawText(x, y + lineHeight * 5, QString("inverse ratio: %1%").arg(1.0f / zoom, 0, 'f', 2));
        painter.endNativePainting();
    }

}

void RenderFore::DrawMessage(Painter*)
{
    if (message.message.empty())
        return;

    float opacity = 1.0f;
    if (message.duration > 0)
    {
        if (message.time == std::chrono::steady_clock::time_point())
            message.time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - message.time).count();
        opacity = std::max(0.0, 1.0 - ((double)duration / message.duration));
        if (opacity <= 0.01f)
        {
            message.message.clear();
            return;
        }
    }

    auto pd = renderer->GetPaintDeviceRAII();
    auto device = pd.device;
    if (device)
    {
        const int x = device->width() / 2;
        const int y = 35;
        //const int lineHeight = 15;
        //(void)lineHeight;
        QPainter painter(device);
        painter.beginNativePainting();
        painter.setFont(QFont("微软雅黑", message.size, QFont::Bold));

        QString text = QString::fromUtf8(message.message.data());
        QSize textsize = QFontMetrics(painter.font()).size(Qt::TextSingleLine, text);
        painter.setOpacity(opacity);
        DrawText(painter, text, x - textsize.width() / 2, y, text_color, contour_color);
        painter.endNativePainting();
    }
}

void RenderFore::UpdateFps()
{
    // static int frameCount = 0;
    // static auto lastTime = std::chrono::steady_clock::now();

    ++frame_count;

    if (last_time == std::chrono::steady_clock::time_point())
        last_time = std::chrono::steady_clock::now();

    auto current_time = std::chrono::steady_clock::now();
    std::chrono::duration<double, std::milli> elapsed = current_time - last_time;

    // 200ms更新一次
    if (elapsed.count() >= 200.0)
    {
        fps = frame_count / elapsed.count() * 1000.0;
        mem_usage = GetMemoryUsage();
        cpu_usage = GetCpuUsage();

        frame_count = 0;
        last_time = current_time;
    }
}
