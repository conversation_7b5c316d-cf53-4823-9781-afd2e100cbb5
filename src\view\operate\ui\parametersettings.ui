<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ParameterSettings</class>
 <widget class="QWidget" name="ParameterSettings">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>770</width>
    <height>869</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_99">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>运行参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_98">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QFrame" name="frame_36">
         <property name="frameShape">
          <enum>QFrame::Box</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_65" rowstretch="0,0,0,0,0,0,0,0,0">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="6" column="0">
           <widget class="QFrame" name="frame_43">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_56">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_58">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_46">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_52">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>抛
料
检
测</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_57">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_62">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_24">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_28">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_5">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>抛料检测:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QCheckBox" name="cb_enable_rejection_test">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>启用抛料测试</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_4">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_22">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_26">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="2">
                    <widget class="QLabel" name="label_26">
                     <property name="minimumSize">
                      <size>
                       <width>20</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>20</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>mm</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QDoubleSpinBox" name="edt_minimum_height">
                     <property name="maximum">
                      <double>99999999.989999994635582</double>
                     </property>
                     <property name="value">
                      <double>0.100000000000000</double>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_24">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>最小高度:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QFrame" name="frame_23">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_25">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_28">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>最小面积:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <widget class="QLabel" name="label_29">
                     <property name="minimumSize">
                      <size>
                       <width>20</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>20</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>mm²</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QDoubleSpinBox" name="edt_minimum_area">
                     <property name="maximum">
                      <double>99999999.989999994635582</double>
                     </property>
                     <property name="value">
                      <double>0.250000000000000</double>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_2">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QFrame" name="frame_19">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_24">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="2">
                    <widget class="QLabel" name="label_27">
                     <property name="minimumSize">
                      <size>
                       <width>20</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>20</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>mm</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QDoubleSpinBox" name="edt_mask_expansion">
                     <property name="maximum">
                      <double>99999999.989999994635582</double>
                     </property>
                     <property name="value">
                      <double>0.600000000000000</double>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_25">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>遮罩外扩:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_3">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="Line" name="line_2">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="7" column="0">
           <widget class="Line" name="line_11">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="8" column="0">
           <widget class="QFrame" name="frame_44">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_55">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_60">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_47">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_53">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>光
源</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_59">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_3">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_2">
                  <property name="frameShape">
                   <enum>QFrame::StyledPanel</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_6">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_6">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>产品切换:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="cb_product_switch">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>绿板</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>红板</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>篮板</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_5">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_3">
                  <property name="frameShape">
                   <enum>QFrame::StyledPanel</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_7">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <spacer name="horizontalSpacer_8">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="4">
                    <spacer name="horizontalSpacer_7">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="6">
                    <spacer name="horizontalSpacer_6">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="7">
                    <widget class="QPushButton" name="btn_apply">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>应用</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QPushButton" name="btn_led_settings">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>LED设置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="5">
                    <widget class="QPushButton" name="btn_dlp_settings">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>DLP设置</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QFrame" name="frame_39">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_51">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_50">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_42">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_48">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>坏
板</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_49">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_59">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_54">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>70</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>坏板设置:</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item row="0" column="2">
                 <spacer name="horizontalSpacer_27">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item row="0" column="1">
                 <widget class="QRadioButton" name="rb_bad_board_setting_percentage_dynamic_shielding">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>百分比动态屏蔽</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QRadioButton" name="rb_bad_board_setting_manual_specification">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>手动指定</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QRadioButton" name="rb_bad_board_setting_bad_board_mark">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>坏板标记</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="4">
                 <widget class="QPushButton" name="btn_set_bad_board">
                  <property name="minimumSize">
                   <size>
                    <width>80</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>80</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="text">
                   <string>设置坏板</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="4">
                 <widget class="QPushButton" name="btn_simulation_test">
                  <property name="minimumSize">
                   <size>
                    <width>80</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>80</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="text">
                   <string>模拟测试</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="4">
                 <widget class="QDoubleSpinBox" name="percentage_dynamic_shielding">
                  <property name="minimumSize">
                   <size>
                    <width>80</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>80</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="maximum">
                   <double>100.000000000000000</double>
                  </property>
                  <property name="value">
                   <double>60.000000000000000</double>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="Line" name="line">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QFrame" name="frame_37">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_49">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_45">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_40">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_41">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>检
测
模
式</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_46">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_57">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_5">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_9">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>路径规划:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="rb_optimized_path_planning">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>优化路径</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="rb_standard_path_planning_mode">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>标准模式</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_32">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QFrame" name="frame_7">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_11">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_19">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>坏板标记:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="rb_bad_board_mark_priority_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>优先识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="rb_bad_board_mark_same_component_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>同元件识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_30">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="4" column="0">
                 <widget class="QFrame" name="frame_8">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_13">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="rb_sub_board_positioning_point_same_component_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>同元件识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_20">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>子板定位点:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="rb_sub_board_positioning_point_priority_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>优先识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_29">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QFrame" name="frame_6">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_10">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_2">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>条码:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="rb_barcode_optimization_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>优先识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="rb_barcode_same_component_recognition">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>同元件识别</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_31">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_10">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_15">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_4">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>轨迹模式:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_42">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="path_pattern">
                     <property name="minimumSize">
                      <size>
                       <width>100</width>
                       <height>0</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>X轴优先-S型</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>Y轴优先-S型</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>X轴优先-Z型</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>Y轴优先-Z型</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QFrame" name="frame_38">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_50">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_48">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_41">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_43">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>检
测
区
域</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_47">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_58">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="2" column="1">
                 <widget class="QRadioButton" name="rb_inspection_area_manually_set_area">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>手动设置区域</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QRadioButton" name="rb_inspection_area_sub_board_area">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>子板区域</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="3">
                 <widget class="QPushButton" name="btn_set_detection_area">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="text">
                   <string>设置检测区域</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QRadioButton" name="rb_inspection_area_scanning_area">
                  <property name="minimumSize">
                   <size>
                    <width>90</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>90</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>扫图区域</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_55">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>70</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>检测区域:</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item row="2" column="2">
                 <spacer name="horizontalSpacer_28">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="5" column="0">
           <widget class="Line" name="line_10">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>轨道参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_100">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QFrame" name="frame_71">
         <property name="frameShape">
          <enum>QFrame::Box</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="2" column="0">
           <widget class="QFrame" name="frame_41">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_64">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_63">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_66">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_57">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>板
宽</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_64">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_67">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="horizontalSpacing">
                 <number>3</number>
                </property>
                <property name="verticalSpacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_33">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_37">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="1" column="1">
                    <widget class="QLabel" name="label_44">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>自动板宽:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="2">
                    <widget class="QCheckBox" name="track_auto_width">
                     <property name="minimumSize">
                      <size>
                       <width>90</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>90</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>启动</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="4">
                    <widget class="QPushButton" name="track_home">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>回零</string>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="3">
                    <spacer name="horizontalSpacer_41">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_34">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_38">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_45">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>设置板宽1:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QPushButton" name="track_width1_set">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>设置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="5">
                    <widget class="QPushButton" name="track_width1_get">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>当前板宽</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <widget class="QLabel" name="label_46">
                     <property name="minimumSize">
                      <size>
                       <width>20</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>20</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>mm</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QLineEdit" name="track_width1">
                     <property name="minimumSize">
                      <size>
                       <width>50</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>50</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>300</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_43">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QFrame" name="frame_35">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_39">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_47">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>设置板宽2:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QPushButton" name="track_width2_set">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>设置</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QLineEdit" name="track_width2">
                     <property name="minimumSize">
                      <size>
                       <width>50</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>50</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>300</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="5">
                    <widget class="QPushButton" name="track_width2_get">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>当前板宽</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <widget class="QLabel" name="label_58">
                     <property name="minimumSize">
                      <size>
                       <width>20</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>20</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>mm</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_44">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QFrame" name="frame_40">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_68">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_65">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_69">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_59">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>输
送
模
组</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_66">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_70">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_67">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_71">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_60">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>轨道配置:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="track_type">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>单轨设备</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>双轨设备</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_45">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_46">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_68">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_72" rowstretch="0">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_61">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>双轨设置:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="track_module_13">
                     <property name="minimumSize">
                      <size>
                       <width>65</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>65</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>1-3定轨</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="track_module_14">
                     <property name="minimumSize">
                      <size>
                       <width>65</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>65</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>1-4定轨</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_47">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QFrame" name="frame_69">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_73">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="5">
                    <widget class="QPushButton" name="track1_rightbottom_btn">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>右下</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QLineEdit" name="track1_rightbottom">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>0,0</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QLineEdit" name="track1_leftbottom">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>0,0</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <widget class="QPushButton" name="track1_leftbottom_btn">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>左下</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_62">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>轨道1停板:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_48">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QFrame" name="frame_70">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_74">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <item row="0" column="5">
                    <widget class="QPushButton" name="track2_righttop_btn">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>右上</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <widget class="QPushButton" name="track2_lefttop_btn">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>左上</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_63">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>轨道2停板:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QLineEdit" name="track2_lefttop">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>0,0</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QLineEdit" name="track2_righttop">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>60</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>0,0</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <spacer name="horizontalSpacer_49">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="Line" name="line_4">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="Line" name="line_9">
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QFrame" name="frame_42">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QGridLayout" name="gridLayout_63">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="horizontalSpacing">
              <number>0</number>
             </property>
             <property name="verticalSpacing">
              <number>9</number>
             </property>
             <item row="0" column="0">
              <widget class="QFrame" name="frame_61">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_48">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <property name="spacing">
                 <number>0</number>
                </property>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_56">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background:lightblue;</string>
                  </property>
                  <property name="text">
                   <string>输
送
模
式</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QFrame" name="frame_62">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Plain</enum>
               </property>
               <layout class="QGridLayout" name="gridLayout_4">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>3</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>3</number>
                </property>
                <property name="spacing">
                 <number>3</number>
                </property>
                <item row="0" column="0">
                 <widget class="QFrame" name="frame_25">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_29">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_17">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>模式设置:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QLabel" name="label_30">
                     <property name="minimumSize">
                      <size>
                       <width>110</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>110</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>老化模式测试次数:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="track_mode">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>在线模式</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>单机模式</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>老化模式</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item row="0" column="4">
                    <widget class="QLineEdit" name="track_repeat">
                     <property name="minimumSize">
                      <size>
                       <width>50</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>50</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="inputMethodHints">
                      <set>Qt::ImhNone</set>
                     </property>
                     <property name="inputMask">
                      <string/>
                     </property>
                     <property name="text">
                      <string>10</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_10">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QFrame" name="frame_26">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_30">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="track_direction">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>左进左出</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>左进右出</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>右进左出</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>右进右出</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QCheckBox" name="track_fixed_test">
                     <property name="minimumSize">
                      <size>
                       <width>115</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>115</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>定板测试</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_32">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>进出板方向:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_20">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QFrame" name="frame_27">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_31">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="3">
                    <widget class="QCheckBox" name="track_trans2">
                     <property name="minimumSize">
                      <size>
                       <width>115</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>115</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>输送2直通</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QCheckBox" name="track_trans1">
                     <property name="minimumSize">
                      <size>
                       <width>75</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>75</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>输送1直通</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_33">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>直通模式:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_22">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="5" column="0">
                 <widget class="QFrame" name="frame_11">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_12">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_34">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>硬件条码枪:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_23">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="track_barcode">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>无硬件条码</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>外置条码</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>内置条码</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="4" column="0">
                 <widget class="QFrame" name="frame_28">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_32">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_24">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="basedon_test_result">
                     <property name="text">
                      <string>按测试结果出板</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_35">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>检测结果:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="basedon_review_result">
                     <property name="text">
                      <string>按复判结果出板</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QFrame" name="frame_29">
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_33">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="1">
                    <widget class="QRadioButton" name="track_normal">
                     <property name="text">
                      <string>正常出板</string>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_25">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_38">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>出板方式:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="3">
                    <widget class="QRadioButton" name="track_force_without_station">
                     <property name="text">
                      <string>无后站强制出板</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item row="6" column="0">
                 <widget class="QFrame" name="frame_32">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_36">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="label_39">
                     <property name="minimumSize">
                      <size>
                       <width>70</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>70</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>进阶设置:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="2">
                    <spacer name="horizontalSpacer_26">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item row="0" column="1">
                    <widget class="QComboBox" name="cb_advanced_settings">
                     <property name="minimumSize">
                      <size>
                       <width>105</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>105</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <item>
                      <property name="text">
                       <string>无</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>同进出模式（在线模式）</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>三段式模式（在线模式；长度≤250mm；选配）</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>超长板模式（在线模式；长度≥500mm；选配）</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>设备参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_104">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="DataSetting" name="data_setting" native="true">
         <layout class="QGridLayout" name="gridLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="verticalSpacing">
           <number>3</number>
          </property>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>路径参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_103">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="DataView" name="data_view" native="true">
         <layout class="QGridLayout" name="gridLayout_102">
          <property name="leftMargin">
           <number>6</number>
          </property>
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="rightMargin">
           <number>4</number>
          </property>
          <property name="bottomMargin">
           <number>6</number>
          </property>
          <property name="spacing">
           <number>3</number>
          </property>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DataView</class>
   <extends>QWidget</extends>
   <header>dataview.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DataSetting</class>
   <extends>QWidget</extends>
   <header>datasetting.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
