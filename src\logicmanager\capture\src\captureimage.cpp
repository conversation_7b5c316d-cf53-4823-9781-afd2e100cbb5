//STD
#include <functional>

//Custom
#include "captureimage.h"
#include "devicemanager.h"
#include "structlight.h"
#include "coreapplication.h"
namespace jrslogic 
{
    CaptureImage::CaptureImage ( const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_ )
        :device_manager_ptr (device_manager_ptr_)
    {
        Init ();
    }
    jrsdata::JrsImageBuffer CaptureImage::GetSingleBuffer ()
    {
        return device_manager_ptr->GetStructLightInstance ()->GetSingleBuffer ();
    }
    int CaptureImage::TriggerSingleCaptureBuffer ( const jrsdata::TriggerModeCapture& trigger_mode , const int& fov_id )
    {
        return device_manager_ptr->GetStructLightInstance ()->PushTriggerToQueue (trigger_mode,fov_id );
    }

    int CaptureImage::StartContinueTrigger(const jrsdata::TriggerModeCapture &trigger_mode)
    {
        if (continue_trigger)
        {
            return -1;
        }
        
        InitCallback();
        continue_trigger = true;

        std::thread scan_thread_ = std::thread ( &CaptureImage::ContinueTriggerFunc , this, trigger_mode);
        scan_thread_.detach();

        return 0;
    }

    int CaptureImage::StopContinueTrigger()
    {
        continue_trigger = false;
        cv_continue_grab.notify_all();
        return 0;
    }

    int CaptureImage::ContinueTriggerFunc(const jrsdata::TriggerModeCapture &trigger_mode)
    {
        while (true)
        {
            TriggerSingleCaptureBuffer(trigger_mode);
            std::unique_lock<std::mutex> lock ( mtx_continue_grab );
            waittting = true;
            cv_continue_grab.wait ( lock , [this]
            {
                return (!waittting)||(!continue_trigger);

            } );  

            if(!continue_trigger)
            {
                break;
            }
        }
        
        return 0;
    }

    void CaptureImage::SetCaptureTriggerDoneCallBack(jrsdata::Grab2DImgCallBack callback_)
    {
        trigger_done_callback = callback_;
    }
    void CaptureImage::SetMergeImageDoneCallBack ( jrsdata::CaptureCallBack callback_ )
    {
        merge_done_callback = callback_;
    }
    void CaptureImage::SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback)
    {
        render_imgs_callback = img_buffer_callback;
    }

    CaptureImage::~CaptureImage()
    {

    }
    void CaptureImage::Init ()
    {
        InitMemeber ();
        InitCallback ();
    }
    void CaptureImage::InitMemeber ()
    {
    }
    void CaptureImage::InitCallback ()
    {
        auto caputer_trigger_done_callback_temp = std::bind ( &CaptureImage::CaptureTriggerDone ,this, std::placeholders::_1 );
        auto merger_image_done_callback_temp = std::bind (&CaptureImage::MergeImageDone,this,std::placeholders::_1,std::placeholders::_2);
        device_manager_ptr->GetStructLightInstance ()->SetTriggerDoneCallback ( caputer_trigger_done_callback_temp );
        device_manager_ptr->GetStructLightInstance ()->SetMergeImageDoneCallbacek ( merger_image_done_callback_temp );
    }
    void CaptureImage::CaptureTriggerDone ( const int& result )
    {
        if (result != jrscore::AOI_OK)
        {
            PushErrorToStack (jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_TRIGGER_FAILURE,"采图触发失败");
        }

        if (trigger_done_callback)
        {
            trigger_done_callback (result);
            Log_INFO ("采图触发完成");
        }
    }
    void CaptureImage::MergeImageDone (const jrsdata::OneFovImgs& imgs,  const int& result )
    {
        if (result != jrscore::AOI_OK)
        {
            PushErrorToStack ( jrscore::DeviceError::E_AOI_DEVICE_CAPTURE_EXCEPTION , "合成图片失败" );
        }
        

        if (render_imgs_callback)
        {
            jrsdata::JrsImageBuffer img_buffer;
            img_buffer.one_fov_imgs = imgs;
            render_imgs_callback(img_buffer);
            Log_INFO ("采图合并完成");
        }

        if (continue_trigger)
        {
            waittting = false;
            cv_continue_grab.notify_all();
        }
    }
}
