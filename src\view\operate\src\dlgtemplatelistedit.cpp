#include "dlgtemplatelistedit.h"
#include "ui_dlgtemplatelistedit.h"

DlgTemplateListEdit::DlgTemplateListEdit(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::DlgTemplateListEdit)
{
    ui->setupUi(this);
    connect(ui->pushButton_template_del, &QPushButton::clicked, this, &DlgTemplateListEdit::SigDeleteTemplate);
}

DlgTemplateListEdit::~DlgTemplateListEdit()
{
    delete ui;
}

void DlgTemplateListEdit::SetTemplateListWidget(QWidget* widget)
{
    ui->widget->layout()->addWidget(widget);
}
