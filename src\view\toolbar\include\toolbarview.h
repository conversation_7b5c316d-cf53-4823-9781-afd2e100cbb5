///*****************************************************************//**
// * @file   toolbarview.h
// * @brief  工具快捷界面view类
// * @details    
// * <AUTHOR>
// * @date 2024.1.29
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/
//
//#ifndef __TOOLBARVIEW_H__
//#define __TOOLBARVIEW_H__
//
//#include "viewbase.h"
//#include <functional>
//
//QT_FORWARD_DECLARE_CLASS (QHBoxLayout);
//
//namespace jrsaoi
//{
//    class ToolBarView :public ViewBase
//    {
//
//        Q_OBJECT
//        public:
//            ToolBarView (const std::string& name,QWidget *parent = nullptr);
//            ~ToolBarView ();
//            virtual void Init () override;
//            virtual void UpdateView (const jrsdata::ViewParamBasePtr& param_)override;
//            virtual int Save (const jrsdata::ViewParamBasePtr& param_)override;
//        public slots:
//            
//            void SlotShortcutsInvokeFun ();
//            
//        private:
//            void InitView ();
//            void InitMember ();
//
//            using InvokeFun = std::function<void ()>;
//            void CreatePushButton (QHBoxLayout*& layout_,const QString& icon_path, const QString& tool_tip,InvokeFun fun_);
//
//            InvokeFun invoke_fun_slot;
//
//    
//            
//    };
//}
//#endif // !__TOOLBARVIEW_H__
