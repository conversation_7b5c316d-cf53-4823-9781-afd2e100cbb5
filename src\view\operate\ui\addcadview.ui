<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddCadView</class>
 <widget class="QDialog" name="AddCadView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1122</width>
    <height>660</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>AddCadView</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>6</number>
      </property>
      <property name="verticalSpacing">
       <number>0</number>
      </property>
      <item row="3" column="1">
       <widget class="QWidget" name="widget_5" native="true">
        <layout class="QGridLayout" name="gridLayout_6">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item row="0" column="3">
          <widget class="QComboBox" name="cb_x">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_15">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>Angle：</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="label_16">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>PartNo：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="15">
          <widget class="QComboBox" name="cbSizeUnit">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
           <property name="currentText">
            <string>mm</string>
           </property>
           <item>
            <property name="text">
             <string>mm</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>um</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="label_14">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>Y：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="cb_name">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QComboBox" name="cb_angle">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QComboBox" name="cb_part_no">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_12">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>Name：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QComboBox" name="cb_y">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="label_13">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>X：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="14">
          <widget class="QLabel" name="label_7">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>单位：</string>
           </property>
          </widget>
         </item>
         <item row="1" column="5">
          <widget class="QComboBox" name="cb_subboard_id">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="label_17">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>子板ID：</string>
           </property>
          </widget>
         </item>
         <item row="1" column="15">
          <widget class="QComboBox" name="cb_component_id">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="14">
          <widget class="QLabel" name="label_18">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>元件库：</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="QWidget" name="widget_3" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="4">
          <widget class="QSpinBox" name="line_end">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximum">
            <number>999999999</number>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>结束行：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QSpinBox" name="line_start">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>28</height>
            </size>
           </property>
           <property name="maximum">
            <number>999999999</number>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>开始行：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="btn_select_start">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string>选中开始行</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QPushButton" name="btn_select_end">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string>选中结束行</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="edit_file">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>28</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QPushButton" name="btn_select_file">
        <property name="minimumSize">
         <size>
          <width>78</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>78</width>
          <height>28</height>
         </size>
        </property>
        <property name="text">
         <string>选择文件</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_6">
        <property name="minimumSize">
         <size>
          <width>78</width>
          <height>62</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>78</width>
          <height>62</height>
         </size>
        </property>
        <property name="text">
         <string>当前列数据：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>CAD文档：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="widget_2" native="true">
     <layout class="QGridLayout" name="gridLayout_3">
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>6</number>
      </property>
      <item row="2" column="0">
       <widget class="QPushButton" name="btn_report">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>28</height>
         </size>
        </property>
        <property name="text">
         <string>导入CAD</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QTableView" name="tw_result">
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
