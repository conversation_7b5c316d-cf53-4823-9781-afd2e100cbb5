﻿//#include "padgroup.h"
//#include "graphicsmanager.h"
//#include "graphicsalgorithm.h"
//#include "graphicsserialize.hpp"
//#include "graphicseventparam.hpp"
//
//#include "controlpointabstract.h"
//#include "controlpointconstants.hpp"
//#include "controlpointfactory.h"
//
//#include "eventparam.hpp"
//// #include "customcursortype.hpp"
//#include "painter.h"
//#include "renderer.h"
//#include "log.h"
//
//template<typename T>
//void RemoveExpiredWeakPtrs(std::vector<std::weak_ptr<T>>& weak_ptr_vector) {
//    // 使用 erase-remove idiom 删除失效的 weak_ptr
//    weak_ptr_vector.erase(
//        std::remove_if(weak_ptr_vector.begin(), weak_ptr_vector.end(),
//            [](const std::weak_ptr<T>& weakPtr) {
//                return weakPtr.expired(); // 检查是否失效
//            }),
//        weak_ptr_vector.end());
//}
//
//template<typename T>
//void RemoveVectorElements(std::vector<T>& source, const std::vector<T>& to_remove)
//{
//    source.erase(std::remove_if(source.begin(), source.end(),
//        [&to_remove](const T& element)
//        {
//            return std::find(to_remove.begin(), to_remove.end(), element) != to_remove.end();
//        }), source.end());
//}
//
//PadGraphicsGroup& PadGraphicsGroup::operator=(const PadGraphicsGroup& other)
//{
//    if (this != &other)
//    {
//        GraphicsAbstract::operator=(other);
//        // this->parentgraphics = other.parentgraphics;
//        // this->subgraphics = other.subgraphics;
//        // this->subnum = other.subnum;
//        this->direction = other.direction;
//    }
//    return *this;
//}
//
//void PadGraphicsGroup::Draw(Renderer* r, const LayerConfig* config)
//{
//    if (!r || !config)
//        return;
//
//    Painter p(r);
//    Draw(r, &p, config);
//}
//
//void PadGraphicsGroup::Draw(Renderer* r, Painter* p, const LayerConfig* config)
//{
//    if (!r || !p || !config)
//        return;
//
//    auto const obj = GetTemp(this, true);
//    if (!obj)
//        return;
//
//    obj->Update(); // 在绘制前再刷新,减少刷新次数
//
//    if (obj->settings.GetIsSelected()) // 选中才显示
//    {
//        Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);
//
//        auto& thinkness = config->_display_style.true_line_width;
//        p->DrawLines(obj->paths, c, true, thinkness);
//    }
//    /* if (update_sub)
//     {
//         update_sub = false;
//
//         std::vector<std::shared_ptr<GraphicsAbstract>> update_ghs;
//         for (auto& sub : obj->subgraphics)
//         {
//             if (sub.expired()) continue;
//             update_ghs.emplace_back(sub.lock());
//         }
//         gm->UpdateGraphics(update_ghs, "", true);
//
//     }*/
//}
//
//void PadGraphicsGroup::Update()
//{
//    UpdateState();
//    if (IsNeedUpdate())
//    {
//        UpdateByTemp(this);
//        UpdateBoundingBox();
//        UpdateControlPoint();
//        UpdateDrawBuffer();
//        SetUpdated();
//    }
//}
//
//void PadGraphicsGroup::UpdateDrawBuffer()
//{
//    cv::RotatedRect boundingbox = this->GetBoundingbox();
//    /*四顶点*/
//    cv::Point2f ovp[4];
//    boundingbox.points(ovp);
//
//    std::vector<Vec2> tvPaths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };
//    tvPaths.swap(this->paths);
//    this->SetUpdated();
//}
//
//void PadGraphicsGroup::UpdateControlPoint()
//{
//    //auto const obj = GetTemp(this, false);
//
//    //std::vector<std::shared_ptr<ControlPointAbstract>> cps;
//    ///*创建中心拖拽控制点*/
//    //{
//    //    auto cp = ControlPointFactory::CreateControlPoint(ControlPointType::MOVE_POINT, obj);
//    //    cps.emplace_back(cp);
//    //}
//    //cps.swap(control_points);
//
//    // for (auto& gh : obj->subgraphics)
//    // {
//    //     gh->UpdateControlPoint();
//    // }
//}
//
//void PadGraphicsGroup::DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config)
//{
//    for (auto& cp : control_points)
//    {
//        if (!cp) continue;
//        cp->Draw(r, p, config);
//    }
//
//    // for (auto& gh : this->subgraphics)
//    // {
//    //     gh->DrawControlPoint(r, p, config);
//    // }
//}
//
//int PadGraphicsGroup::TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam& param)
//{
//    if (!settings.GetIsSelected())
//        return 2;
//
//    double mindis = param.max_limit;
//    std::shared_ptr<ControlPointAbstract> response_cp = nullptr;
//    for (auto& cp : control_points)
//    {
//        if (!cp) continue;
//        auto dis = cp->TryResponse(param.x, param.y, (float)param.max_limit);
//        if (dis > 0 && dis < mindis)
//        {
//            // printInfo(std::stringstream() << "响应距离:" << dis);
//            mindis = dis;
//            response_cp = cp;
//        }
//    }
//
//    if (!response_cp)
//        return 1;
//    controlpoint = response_cp;
//    // attr = response_cp->attributes;
//    return 0;
//}
//
//int PadGraphicsGroup::ResponseControlPoint(const ResponseEventParam& param)
//{
//    auto obj = GetAndUpdateTemp(this, false);
//    if (!obj)
//        return GraphicsAbstract::GRAPHICS_NULL;
//    // for (auto& cp : control_points)
//    // {
//    //     if (!cp) continue;
//    //     if (cp->attributes == param.attr)
//    //     {
//    //         cp->Response(param, obj);
//    //         break;
//    //     }
//    // }
//    // 
//    (void)param;
//    //更新所有图形
//    //for (auto& weak_gh : obj->subgraphics)
//    //{
//    //    auto gh = weak_gh.lock();
//    //    gh->ResponseControlPoint(param);
//    //}
//    return 1;
//}
//
//int PadGraphicsGroup::ResponseEvent(const MouseEventValue& value)
//{
//    switch (value.state)
//    {
//    case MouseEventValue::MouseState::wheel:
//    {
//        if (value.type > 0)
//        {
//            CreateSubGraphics();
//        }
//        else if (value.type < 0)
//        {
//            DoAlign();
//            DeleteSubGraphics();
//        }
//    }
//    break;
//    }
//    this->subgraphics.clear();
//    //DoAlign(true);
//    return 0;
//}
//
//void PadGraphicsGroup::SetParentGraphics(std::shared_ptr<GraphicsAbstract> parent_)
//{
//    this->parentgraphics = parent_;
//}
//
//void PadGraphicsGroup::AddSubGraphics(std::shared_ptr<GraphicsAbstract> sub_)
//{
//    PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
//    auto it = std::find_if(subgraphics.begin(), subgraphics.end(), [&sub_](auto& val) { return val.lock() == sub_; });
//    if (it == subgraphics.end())
//    {
//        subgraphics.emplace_back(sub_);
//    }
//    SetRequireUpdate();
//    PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
//}
//
//void PadGraphicsGroup::AddSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_)
//{
//    printInfo(std::stringstream() << "");
//    //auto shared_from_this_ = shared_from_this();
//    for (auto& gh : sub_)
//    {
//        // gh->SetParent(shared_from_this_);
//        AddSubGraphics(gh);
//    }
//
//    SetRequireUpdate();
//    // UpdateBoundingBox();
//}
//
//void PadGraphicsGroup::RemoveSubGraphics(std::shared_ptr<GraphicsAbstract> sub_)
//{
//    // RemoveChild(sub_);
//    auto it = std::find_if(subgraphics.begin(), subgraphics.end(), [&sub_](auto& val) { return val.lock() == sub_; });
//    if (it != subgraphics.end())
//    {
//        subgraphics.erase(it);
//        // UpdateBoundingBox();
//    }
//    //isalign = false;
//    SetRequireUpdate();
//}
//
//void PadGraphicsGroup::RemoveSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_)
//{
//    // for (auto& gh : sub_)
//    // {
//    //     RemoveChild(gh);
//    // }
//    {
//        auto& vec = subgraphics;
//        vec.erase(std::remove_if(vec.begin(), vec.end(),
//            [&sub_](const std::weak_ptr<GraphicsAbstract>& element)
//            {
//                return std::find(sub_.begin(), sub_.end(), element.lock()) != sub_.end();
//            }), vec.end());
//    }
//    // RemoveVectorElements(subgraphics, weak_ghs);
//    // UpdateBoundingBox();
//
//    SetRequireUpdate();
//}
//
//void PadGraphicsGroup::CreateSubGraphics()
//{
//    if (subgraphics.size() < 1 || !gm)
//        return;
//    auto gh = subgraphics[0].lock()->Clone();
//    gh->CreateId();
//    gm->UpdateGraphics(gh);
//    AddSubGraphics(gh);
//}
//
//void PadGraphicsGroup::DeleteSubGraphics()
//{
//    if (subgraphics.size() < 1 || !gm)
//        return;
//    size_t middle_index = subgraphics.size() / 2;
//    auto delete_gh = subgraphics[middle_index].lock();
//    printInfo(std::stringstream() << "delete_gh_id:" << delete_gh->GetId().GetString() << "   " << *delete_gh);
//    //gm->DeleteGraphics(gh->GetId());
//    subgraphics.erase(subgraphics.begin() + middle_index);
//    for (auto& weak_gh : subgraphics)
//    {
//        auto gh = weak_gh.lock();
//        printInfo(std::stringstream() << "gh_id:" << gh->GetId().GetString() << "   " << *gh);
//    }
//    gm->DeleteGraphics(delete_gh->GetId());
//    SetRequireUpdate();
//}
//
//PadGraphicsGroup::PadDirection PadGraphicsGroup::GetDirection()
//{
//    return direction;
//}
//
//
//PadGraphicsGroup::PadDirection GetPadDirection(double angle)
//{
//    if (angle >= 45 && angle < 135)
//    {
//        return PadGraphicsGroup::PadDirection::UP; // 上       [45°, 135°)
//    }
//    else if (angle >= -45 && angle < 45)
//    {
//        return PadGraphicsGroup::PadDirection::RIGHT; // 右       [-45°, 45°)
//    }
//    else if (angle >= -135 && angle < -45)
//    {
//        return PadGraphicsGroup::PadDirection::DOWN; // 下       [-135°, -45°)
//    }
//    else
//    {
//        return PadGraphicsGroup::PadDirection::LEFT; // 左       [135°, 180°] 和 [-180°, -135°)
//    }
//}
//
//
//void PadGraphicsGroup::SetDirection(PadDirection direction_)
//{
//    direction = direction_;
//}
////TODO: 矫正函数需要移动到外面   12/25 HJC
//void PadGraphicsGroup::DoAlign()
//{
//
//    if (parentgraphics.expired())
//        return;
//    PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
//    RemoveExpiredWeakPtrs(this->subgraphics);
//    if (direction == PadDirection::UNKNOWN)
//    {
//
//        // 2025.01.08 wangzhengkai 将方向计算修改用相对坐标进行计算
//        float xmin = FLT_MAX, xmax = -FLT_MAX, ymin = FLT_MAX, ymax = -FLT_MAX;
//        for (auto& weak_gh : subgraphics)
//        {
//            auto gh = weak_gh.lock();
//            xmin = std::min(xmin, gh->LocalX());
//            xmax = std::max(xmax, gh->LocalX());
//            ymin = std::min(ymin, gh->LocalY());
//            ymax = std::max(ymax, gh->LocalY());
//        }
//        float center_x = (xmax + xmin) / 2;
//        float center_y = (ymax + ymin) / 2;
//        std::pair<float, float> parent_vector = { 0.0f, 0.0f };
//        std::pair<float, float> direction_vector = { center_x, -center_y };
//        auto direction_angle = GetVectorA2BAngle(parent_vector.first, parent_vector.second, direction_vector.first, direction_vector.second);
//        direction = GetPadDirection(direction_angle);
//    }
//    //return;
//    if (subgraphics.size() < 2)
//        return;
//    switch (direction)
//    {
//    case PadDirection::UP:
//    case PadDirection::DOWN:
//    {
//        std::sort(subgraphics.begin(), subgraphics.end(),
//            [](const std::weak_ptr<GraphicsAbstract>& lhs, const std::weak_ptr<GraphicsAbstract>& rhs)
//            {
//                return lhs.lock()->x() < rhs.lock()->x();
//            });
//
//        auto front_graphics = subgraphics.front().lock();
//        auto back_graphics = subgraphics.back().lock();
//        auto w = front_graphics->w();
//        auto h = front_graphics->h();
//        auto first_x = front_graphics->x();;
//        auto end_x = back_graphics->x();
//        auto first_y = front_graphics->y();
//        auto sub_gh_num = subgraphics.size();
//        auto gap = (end_x - first_x) / (sub_gh_num - 1);
//
//        for (int i = 0; i < sub_gh_num; ++i) {
//            auto pad = subgraphics[i].lock();
//            pad->SetY(first_y);
//            if (i == sub_gh_num - 1)
//            {
//                pad->SetX(end_x);
//            }
//            else
//            {
//                pad->SetX(first_x);
//            }
//            first_x += gap;
//            pad->SetWH(w, h);
//
//            auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(pad);
//            if (pad_gh)
//            {
//                pad_gh->pad_id = i + 1;
//            }
//        }
//    }
//    break;
//    case PadDirection::RIGHT:
//    case PadDirection::LEFT:
//    {
//        std::sort(subgraphics.begin(), subgraphics.end(),
//            [](const std::weak_ptr<GraphicsAbstract>& lhs, const std::weak_ptr<GraphicsAbstract>& rhs)
//            {
//                return lhs.lock()->y() < rhs.lock()->y();
//            });
//        auto front_graphics = subgraphics.front().lock();
//        auto back_graphics = subgraphics.back().lock();
//        auto w = front_graphics->w();
//        auto h = front_graphics->h();
//        auto first_x = front_graphics->x();;
//        auto end_y = back_graphics->y();
//        auto first_y = front_graphics->y();
//        auto sub_gh_num = subgraphics.size();
//
//        auto gap = (end_y - first_y) / (sub_gh_num - 1);
//
//        for (int i = 0; i < sub_gh_num; ++i) {
//            auto pad = subgraphics[i].lock();
//            pad->SetX(first_x);
//            if (i == sub_gh_num - 1)
//            {
//                pad->SetY(end_y);
//            }
//            else
//            {
//                pad->SetY(first_y);
//            }
//            first_y += gap;
//            pad->SetWH(w, h);
//
//            auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(pad);
//            if (pad_gh)
//            {
//                pad_gh->pad_id = i + 1;
//            }
//        }
//    }
//    break;
//    default:
//        return;
//    }
//    isalign = true;
//    PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
//}
//
//void PadGraphicsGroup::UpdateBoundingBox()
//{
//    if (!isalign)
//    {
//        DoAlign();
//    }
//
//    float min_x = std::numeric_limits<float>::max();
//    float min_y = std::numeric_limits<float>::max();
//    float max_x = std::numeric_limits<float>::lowest();
//    float max_y = std::numeric_limits<float>::lowest();
//
//    // if (!this->IsHaveChild())
//    if (this->subgraphics.empty())
//    {
//        this->SetWH(0, 0); // 空集合时，默认设置外接矩形
//        return;
//    }
//
//    for (auto& weak_gh : this->subgraphics)
//    {
//        auto gh = weak_gh.lock();
//        auto box = gh->GetBoundingbox().boundingRect2f();
//        min_x = std::min(min_x, box.x);
//        min_y = std::min(min_y, box.y);
//        max_x = std::max(max_x, box.x + box.width);
//        max_y = std::max(max_y, box.y + box.height);
//    }
//    float w = max_x - min_x + 50;
//    float h = max_y - min_y + 50;
//    this->SetXY((min_x + max_x) * 0.5f, (min_y + max_y) * 0.5f);
//    this->SetWH(w, h);
//}
//
//void PadGraphicsGroup::UpdateState()
//{
//    RemoveExpiredWeakPtrs(this->subgraphics);
//
//    bool select_sub = false;
//
//    for (auto& gh : this->subgraphics)
//    {
//
//        if (!gh.expired() && gh.lock()->settings.GetIsSelected())
//        {
//            //printInfo(std::stringstream() << "选中的gh:" << *gh.lock());
//            select_sub = true;
//            break;
//        }
//    }
//    this->settings.SetIsSelected(select_sub);
//
//    for (auto& weak_gh : this->subgraphics)
//    {
//        auto gh = weak_gh.lock();
//        if (gh->IsNeedUpdate())
//        {
//            isalign = false;
//            this->SetRequireUpdate();
//            break;
//        }
//    }
//}
//
//void PadGraphicsGroup::PrintCurrentPadGroupInfo(const std::string& function_name_, int line_)
//{
//    printInfo(std::stringstream() << "[" << function_name_ << "][" << line_ << "] address:" << this << " subboard_size:" << subgraphics.size());
//    for (auto sub_gh : subgraphics)
//    {
//        auto pd_gh = std::dynamic_pointer_cast<PadGraphics>(sub_gh.lock());
//        if (!pd_gh)
//            continue;
//        printInfo(std::stringstream() << "pad_address:" << sub_gh.lock().get()
//            << " graphics_id:" << sub_gh.lock()->GetId().GetString()
//            << " pad_group:" << pd_gh->pad_group.lock().get()
//            << " pad_id:" << pd_gh->pad_id
//            << " pad_center_position:[" << sub_gh.lock()->x() << "," << sub_gh.lock()->y() << "]");
//    }
//}
