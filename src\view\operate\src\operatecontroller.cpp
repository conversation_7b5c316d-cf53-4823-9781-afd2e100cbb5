﻿//C++
#include <regex>

//Custom
#include "operatecontroller.h"
#include "operatemodel.h"
#include "operateview.h"
#include "algorithmenginemanager.h"
#include "operatorviewbase.h"
#include "dataparam.h"
#include "jsonoperator.hpp"
#include "algoexecuteparam.hpp"
#include "coordinatetransform.hpp"
#include "directedgraph.h"
#include "coordinatetransformationtool.h"
#include "operatorparambase.h"
#include "coreapplication.h"
#include "timeutility.h"
#include "cvtools.h"
   //Third
#include "iguana/iguana.hpp"

namespace jrsaoi
{
    OperateController::OperateController(const std::string& name) :ControllerBase(name)
    {

    }
    OperateController::~OperateController()
    {
    }
    int OperateController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        auto res = model->Update(param_);

        if (res == jrscore::AOI_OK)
        {

            if (param_->event_name.compare(jrsaoi::OPERATE_DETECT_STATISICS_UPDATE) == 0)
            {
                ConvertDetectResult(param_);
            }
            else if (param_->event_name == jrsaoi::APPEND_PROJECT_EVENT_NAME)
            {
                auto project_temp_ = model->GetProjectParam();

                if (!project_temp_)
                {
                    JRSMessageBox_ERR("OperateView", "未打开工程，请先打开工程后再添加多工程！", jrscore::MessageButton::Ok);

                    return jrscore::ViewError::E_AOI_VIEW_PROJECT_NOT_IMPORT;
                }
            }
            QMetaObject::invokeMethod(operate_view,
                "UpdateView",
                Qt::QueuedConnection,
                Q_ARG(const jrsdata::ViewParamBasePtr&, param_)
            );
            if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME && param_->sub_name == "all")
            {
                SlotUpdateCoordinate();//更新解析度
            }

        }


        return res;
    }
    int OperateController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    void OperateController::SetView(ViewBase* view_param)
    {
        operate_view = static_cast<OperateView*>(view_param);
        connect(operate_view, &OperateView::SigUpdateOperator, this, &OperateController::SlotViewOperator);
        if (model.get())
        {
            connect(model.get(), &OperateModel::SigUpdateOperator, this, &OperateController::SlotModelOperator);
        }
        //connect(this, &OperateController::SigShowAxisMoveDialog, operate_view, &OperateView::SigUpdateView);
        //connect(this, &OperateController::SigUpdateCfgView, operate_view, &OperateView::SigUpdateView);
    }
    void OperateController::SetModel(ModelBasePtr model_param)
    {
        model = std::dynamic_pointer_cast<OperateModel>(model_param);
    }

    void OperateController::SetAlgoEngine(const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_ptr_)
    {
        algo_engine_ptr = algo_engine_ptr_;
        const auto& algo_name_lists = algo_engine_ptr->GetAlgoNameMap();
        const auto& algo_spec_param_map_temp = algo_engine_ptr->GetAlgoSpecMap();
        QStringList qstr_det_models = { tr("body"),tr("pad") };

        // 算法参数列表
        std::vector<std::string> det_models;
        for (auto& model_name : qstr_det_models)
        {
            det_models.push_back(model_name.toLocal8Bit().toStdString().c_str());
        }
        std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_names_map;
        std::map<std::string, QWidget*> algo_views_map;

        for (auto& model_name : det_models)
        {
            for (auto& algo_name : algo_name_lists)
            {
                {
                    algo_names_map[model_name].push_back({ algo_name.first, algo_name.second });
                }
            }
        }
        // 缺陷列表
        QList<std::string> qstr_defect_lists = {
             ("缺料")
            ,("偏移")
            ,("错件")
            ,("极性")
            ,("翘起")
            ,("旋转")
            ,("少锡")
            ,("虚焊")
            ,("锡桥")
            ,("损件")
            ,("锡珠")
            ,("异物")
            ,("双镍片")
            ,("锡包")
            ,("镍片变形")
            ,("镍片浮高")
            ,("焊盘翘起")
            ,("引脚外漏")
        };
        std::vector<std::string> defect_lists;
        for (auto& qstr_defect : qstr_defect_lists)
        {
            defect_lists.push_back(qstr_defect);
        }

        // 获取所有算法默认参数
        std::map<std::string, std::string> algo_default_params_map;
        for (auto& algo_name : algo_name_lists)
        {
            auto algo_view = algo_engine_ptr->GetSpecificAlgoView(algo_name.first);

            if (!algo_view)
            {
                algo_names_map.erase(algo_name.first);
                Log_ERROR("获取算子：", algo_name.first, "界面失败，请查看导入该算子界面是否成功！");
                JRSMessageBox_ERR("OperateView", algo_name.first + "界面获取失败，请查看导入该算子界面是否成功", jrscore::MessageButton::Ok);
                continue;
            }

            algo_view->SetSaveCallBack(std::bind(&OperateModel::AlgoParamChangedCallbackFunc, model, std::placeholders::_1));
            algo_view->setProperty("algo_name", QString::fromStdString(algo_name.second.c_str()));

            auto algo_param_ptr = algo_view->GetViewParam();
            if (algo_param_ptr)
            {
                auto operator_ptr = std::dynamic_pointer_cast<iguana::base>(algo_param_ptr);
                if (!operator_ptr)
                {
                    Log_ERROR(algo_name.first, "：algo_param_ptr 转换 operator_ptr 失败，指针为空!");
                    continue;
                }
                std::string algo_param_str;
                operator_ptr->to_json(algo_param_str);
                algo_default_params_map[algo_name.first] = algo_param_str;
            }
            else
            {
                Log_ERROR("算子：", algo_name.first, "参数转换失败！");
                JRSMessageBox_ERR("OperateView", algo_name.first + "算法参数转换失败", jrscore::MessageButton::Ok);
            }
            algo_views_map.insert({ algo_name.first , algo_view });
        }
        model->SetAlgorithmAndDefectList(algo_names_map, defect_lists);
        model->SetAlgoritmDefaultParam(algo_default_params_map);
        EditAlgorithmViewDefaultParam edit_view_default_param;
        edit_view_default_param.algo_name_list = algo_names_map;
        edit_view_default_param.defect_list = defect_lists;
        edit_view_default_param.algo_view_list = algo_views_map;
        edit_view_default_param.algo_spec_param_map = algo_spec_param_map_temp;
        float resolution_x = 0.0f;
        float resolution_y = 0.0f;
        model->GetResolution(resolution_x, resolution_y);
        if (resolution_x > 0 && resolution_y > 0)
        {
            edit_view_default_param.resolution_x = resolution_x;
            edit_view_default_param.resolution_y = resolution_y;
        }
        operate_view->SetEditAlgorithmViewDefaultParam(edit_view_default_param);
    }

    void OperateController::SlotModelOperator(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name == OPERATE_UPDATE_DET_WIN_VIEW_EVENT_NAME)//|| 
            //param_->event_name == RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME)
        {
            UpdateDetectWinEditView(param_);
        }
        else if (param_->event_name == OPERATE_UPDATE_DETECT_MODEL_VIEW_EVENT_NAME ||
            param_->event_name == RENDER2D_SELECT_UPDATE_DET_WIN_VIEW_EVENT_NAME ||
            param_->event_name == OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME ||
            param_->event_name == OPERATE_UPDATE_PAD_WIN_PARAM_EVENT_NAME ||
            param_->event_name == OPERATE_CREATE_TEMPLATE_BY_DRAW_REGION_EVENT_NAME ||
            param_->event_name == OPERATE_UPDATE_IPE_PROCESS_IMAGE_EVENT_NAME)
        {
            emit operate_view->SignalUpdateEditDetectModelView(param_);
            auto param_temp = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //! 切换元件并选中算法模型组后，刷新整个元件的算法检测结果状态
            if (param_temp->cur_select_component == nullptr)
            {
                return;
            }
            UpdateSelectedComponentAlgoResult(param_temp);


        }
        else if (param_->event_name == jrsaoi::ACT_TEST_DETECT_WINDOW)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            if (param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "当前未选中任何算法！", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedDetectWinRun(param);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_COMPONET)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //!< 单元件检测不需要判断当前是否选中检测、框或组件 wangzhengkai 2025.02.24
            if (param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "未选中元件/算法为空！", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedComponentRun(param, false, false);

        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_RUN_COMPONENT_SAVE_ALGO_INFO_EVENT)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //!< 单元件检测不需要判断当前是否选中检测、框或组件 wangzhengkai 2025.02.24
            if (param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "未选中元件/算法为空！", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedComponentRun(param, true);
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_LOCATED_RUN_EVENT)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //!< 单元件检测不需要判断当前是否选中检测、框或组件 wangzhengkai 2025.02.24
            if (param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "未选中元件/算法为空", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedComponentRun(param, false, true);
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_PARTNUMBER_LOCATED_RUN_EVENT)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //!< 同料号检测不需要判断当前是否选中元件、检测、框或组件 wangzhengkai 2025.02.24
            if (
                param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "未选中元件料号/算法为空！", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedPartNumbRun(param, true);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_PART_NUM)
        {
            auto param = std::static_pointer_cast<jrsdata::AlgoEventParam>(param_);
            //!< 同料号检测不需要判断当前是否选中元件、检测、框或组件 wangzhengkai 2025.02.24
            if (
                param->cur_select_component == nullptr
                || param->cur_select_component_unit == nullptr
                || param->cur_select_detect_win == nullptr
                || param->cur_select_spec_and_detect_region == nullptr
                || param->cur_select_detect_win->algorithms.empty())
            {
                JRSMessageBox_WARN("算法执行", "未选中元件料号/算法为空！", jrscore::MessageButton::Ok);
                return;
            }
            CurSelectedPartNumbRun(param);
        }
        else if (param_->event_name == jrsaoi::ACT_TEST_POS_ALIGN)
        {
            //! 获取mark点信息
            auto& project_data_instance = jrsaoi::ParamOperator::GetInstance().GetProjectDataProcessInstance();
            std::vector<jrsdata::Component> mark_components = project_data_instance->GetProjectParam()->board_info.marks;
            std::vector<jrstool::PointLabel<float>> src_mark_coordinate, dst_mark_coordinate;
            jrsdata::ComponentUnit mark_unit;
            if (mark_components.size() < 2)
            {
                JRSMessageBox_ERR("mark点异常", "mark点数量低于两个，无法矫正！", jrscore::MessageButton::Ok);
                return;
            }
            //! 每个mark算法单独运行
            for (auto& component_value : mark_components)
            {
                auto& units = project_data_instance->GetProjectParam()->board_info.part_nums_and_detect_regions[component_value.component_part_number].units;
                if (!units.empty())
                {
                    mark_unit = std::ref(units.at(0));
                }
                for (auto& windows_info : project_data_instance->GetProjectParam()->board_info.part_nums_and_detect_regions[component_value.component_part_number].detect_models)
                {

                    for (auto& value_window : windows_info.second.detect_model)
                    {
                        cv::Mat matrix_to_src_image;
                        float mark_src_x = value_window.cx + component_value.x;
                        float mark_src_y = value_window.cy + component_value.y;
                        float mark_dst_x;
                        float mark_dst_y;
                        auto res_dete = ExecuteSingleDetectWin(component_value, mark_unit, value_window, matrix_to_src_image);

                        if (!res_dete)
                        {
                            Log_ERROR("Mark匹配失败：", component_value.component_name, "score:nullptr");
                            JRSMessageBox_ERR("Operate", "Mark算法执行异常，结果返回为空!\nMARK:" + component_value.component_name + "\n" + "score:nullptr", jrscore::MessageButton::Ok);
                            return;
                        }
                        auto result_algo_item_value = algo_engine_ptr->GetAlgoExecuteResultParam(res_dete, value_window.algorithms[0].detect_algorithm_name);

                        if (!result_algo_item_value.result_algo_status)
                        {
                            Log_ERROR("Mark匹配失败：", component_value.component_name, "score:", result_algo_item_value.result_score);
                            JRSMessageBox_ERR("Operate", "Mark匹配失败\nMARK:" + component_value.component_name + "\n" + "score:" + std::to_string(result_algo_item_value.result_score), jrscore::MessageButton::Ok);
                            return;
                        }

                        cv::Point2f detect_res;

                        detect_res.x = result_algo_item_value.result_x_coordinate;
                        detect_res.y = result_algo_item_value.result_y_coordinate;
                        jcvtools::JrsHomMat2D hom_2d(matrix_to_src_image);
                        cv::Point2f transform_res = hom_2d.AffineTransPoint(detect_res);
                        mark_dst_x = transform_res.x;
                        mark_dst_y = transform_res.y;

                        jrstool::PointLabel<float> src_;
                        jrstool::PointLabel<float> dst_;
                        src_.pt_.x = mark_src_x;
                        src_.pt_.y = mark_src_y;

                        dst_.pt_.x = mark_dst_x;
                        dst_.pt_.y = mark_dst_y;

                        src_mark_coordinate.emplace_back(src_);
                        dst_mark_coordinate.emplace_back(dst_);



                    }
                }
            }

            //在这里将仿射变换的矩阵传给render2D进行仿射变换
            cv::Mat matrix;//旋转平移矩阵
            jrstool::CoordinatrTransform<float>::GetAffineMatrix(src_mark_coordinate, dst_mark_coordinate, matrix);
            auto render_view_param = std::make_shared<jrsdata::RenderViewParam>();
            render_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
            render_view_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
            render_view_param->event_name = jrsaoi::RENDER2D_AFFINE_TRANSTORM_EVENT_NAME;
            render_view_param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
            render_view_param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
            render_view_param->affine_transform_matrix = matrix;
            emit SigUpdateOperator(render_view_param);
            return;

        }
        else if (param_->event_name == jrsaoi::REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME)
        {
            auto render_view_param = std::make_shared<jrsdata::RenderViewParam>();
            render_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
            render_view_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
            render_view_param->event_name = jrsaoi::REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME;
            render_view_param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
            render_view_param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
            emit SigUpdateOperator(render_view_param);
            return;
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_TOP_BOTTOM_MIRROR ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_LEFT_RIGHT_MIRROR ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_90_DUPLICATE ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COPY_BODY_ROTATE_180_DUPLICATE ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_DETECT_COPY ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_COMPONENT_DETECT_COPY ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_DETECT_ACTION ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_ROTATE_90_ALL
            )
        {
            //刷新检测框
            UpdateDetectWinView(param_->event_name);
            return;
        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_SUBBOARD_AI ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_BOARD_AI ||
            param_->event_name == jrsaoi::SHORTCUT_ACT_BODY_AI)
        {
            jrsdata::Component ai_area;

            //! 添加AI元件
            if (param_->event_name == jrsaoi::SHORTCUT_ACT_SUBBOARD_AI)
            {
                if (model->CreateSubboardComponent(ai_area) != 0)
                {
                    return;
                }
            }
            else if (param_->event_name == jrsaoi::SHORTCUT_ACT_BODY_AI)
            {
                float value = 1024.0f;
                if (model->CreateAIComponent(ai_area, value, value) != 0)
                {
                    return;
                }
            }

            //! 添加AI算法
            jrsdata::MultiAlgoParam multi_algo_param;
            multi_algo_param.body_algo_names.push_back("DeeplearndeductionOperator");
            jrsdata::ComponentEntity entity;
            //auto& detect_info = model->ReadPNDetectInfo(ai_area.component_part_number);
           /* if (detect_info.has_value())
            {
                entity.part_name = ai_area.component_part_number;
                entity.bind_part_name = ai_area.component_part_number;
                entity.detect_info = detect_info.value();
                model->MultiAlgoAdd(entity, multi_algo_param);

                UpdateDetectWinView(OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME);
            }
            else
            {
                return;
            }*/

            //! 调用AI算法
            cv::Mat matrix_to_src_image_;
           // auto& detect_models = detect_info.value().detect_models;
           /*auto& it = detect_models.find("body");
            if (it == detect_models.end())
            {
                return;
            }
            auto& detect_model = it->second.detect_model;
            if (detect_model.size() == 0)
            {
                return;
            }
            auto& algo_result = ExecuteSingleDetectWin(ai_area, detect_info.value().units[0], detect_model[0], matrix_to_src_image_);
            if (!algo_result)
            {
                Log_ERROR("执行算法异常，返回为nullptr!");
                return;
            }
            //! 解析算法结果
            auto& result_algo_item_value = algo_engine_ptr->GetAlgoExecuteResultParam(algo_result, detect_model[0].algorithms[0].detect_algorithm_name);

            //! 添加本体和PAD
            */
            return;
        }
        emit SigUpdateOperator(param_);
    }


    void OperateController::SlotUpdateCoordinate()
    {
        auto project_event_ptr = std::make_shared<jrsdata::ProjectEventParam>();
        project_event_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        project_event_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        project_event_ptr->event_name = jrsaoi::UPDATE_COORDINATE_EVENT_NAME;
        project_event_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        project_event_ptr->invoke_module_name = jrsaoi::LOGIC_MODULE_NAME;
        project_event_ptr->project_param = model->GetProjectParam();
        emit SigUpdateOperator(project_event_ptr);
    }

    void OperateController::SlotViewOperator(const jrsdata::ViewParamBasePtr& param_)
    {
        model->Update(param_);
       // auto res = model->Update(param_);
        if (param_->topic_name == "")
        {
            return;
        }
        /*if (param_->event_name == jrsaoi::SCANCE_BOARD_EVENT_NAME)
        {
            auto& param_project_temp = std::static_pointer_cast<jrsdata::ProjectEventParam>(param_);
            param_project_temp->project_param = model->GetProjectParam();
        }*/
        else if (param_->event_name == jrsaoi::PROJECT_CREATE_EVENT_NAME) //创建事件 to 读取事件，用于render层解析
        {
            param_->event_name = jrsaoi::PROJECT_READ_EVENT_NAME;
        }
        else if (param_->event_name == jrsaoi::REQUEST_UPDATE_DET_WIN_PARAM_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
            if (!param)
            {
                return;
            }
            UpdateDetectWinEditView(param_);
        }
        else if (param_->event_name == jrsaoi::OPERATE_UPDATE_TEMPLATE_PARAM_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
            model->SetCurSelectedToAlgoEventParam(param);
            emit operate_view->SignalUpdateEditDetectModelView(param_);
        }
        else if (param_->event_name == jrsaoi::PROJECT_SAVE_EVENT_NAME)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            param->project_param = model->GetProjectParam();

            if (!param->project_param)
            {
                Log_ERROR("获取工程数据失败，系统内没有工程可以保存");
                return;
            }
            if (param->project_param->is_save)
            {
                auto select_res = JRSMessageBox_ERR("Operate", "当前图片已经保存，是否覆盖？", jrscore::MessageButton::Yes | jrscore::MessageButton::No);
                if (select_res == jrscore::MessageButton::Yes)
                {
                    param->project_param->is_save = false;
                }
            }
        }
        else if (param_->event_name == jrsaoi::ENTIRETY_IMAGE_SAVE)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            auto project_param = model->GetProjectParam();
            if (!project_param)
            {
                Log_ERROR("获取工程数据失败，系统内没有数据可以保存");
                return;
            }
            project_param->current_group_name = param->project_param->current_group_name;
            param->project_param = project_param;
        }
        else if (param_->event_name == jrsaoi::ENTIRETY_IMAGE_READ)
        {
            auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
            //model->Update(param);
            param->project_param = model->GetProjectParam();
            if (!param->project_param)
            {
                Log_ERROR("获取工程数据失败，请先打开工程或创建工程");
                return;
            }

        }
        else if (param_->event_name == jrsaoi::IMPORT_CAD_EVENT_NAME)
        {
            auto cad_event_param = std::static_pointer_cast<jrsdata::CADEventParam>(param_);
            cad_event_param->project_param = model->GetProjectParam();
            if (!cad_event_param->project_param)
            {
                Log_ERROR("获取工程数据失败，请先打开工程或创建工程");
                JRSMessageBox_ERR("Operate", "当前获取的工程为空，请先打开工程或创建工程！", jrscore::MessageButton::Ok);
                return;
            }
        }
        else if (param_->event_name == jrsaoi::CONFIRM_BOARD_POS_EVENT_NAME)
        {
            SlotUpdateCoordinate();
        }
        else if (param_->event_name == jrsaoi::OPERATE_SAVE_REAPIR_COMPRESS_IMG_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            param_temp->repair_data.compress_entirety_board_imgs = model->GetProjectCompressImg();
            param_temp->repair_data.project_name = model->GetProjectParam()->project_name;
        }
        else if (param_->event_name == jrsaoi::OPERATE_SAVE_REAPIR_BRIEF_COMPONENT_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            param_temp->repair_data.brief_component_info_list = model->GetAllBriefComponentInfo();
            param_temp->repair_data.project_name = model->GetProjectParam()->project_name;

        }
        else if (param_->event_name == OPERATE_COMPONENT_GET_ALL_COMPONENT_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);

            // 获取所有的元件信息
            jrsdata::ComponentEntitys entitys;
            model->GetComponentEntitys(entitys);
            if (entitys.components_param.size() == 0)
            {
                return;
            }
            param_temp->entitys = entitys;
            emit operate_view->SigUpdateView(param_temp);
            return;
        }
        else if (param_->event_name == jrsaoi::OPERATE_COMPONENT_GET_CURSELLECT_COMPONENT_EVENT_NAME
            || param_->event_name == jrsaoi::OPERATE_COMPONENT_LOAD_CURSELLECT_COMPONENT_EVENT_NAME)
        {
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            jrsdata::ComponentEntitys entitys;
            jrsdata::ComponentEntity entity;
            GetCurSelectComponentEntity(param_, entity);

            // 定位点、条码、坏板不保存到元件库
            std::string part_name = entity.part_name;
            if (part_name.find("mark") == std::string::npos && part_name.find("barcode") == std::string::npos)
            {
                entitys.components_param.push_back(entity);
                param_temp->entitys = entitys;

                emit operate_view->SigUpdateView(param_temp);
            }
            return;
        }
        else if (param_->event_name == OPERATE_COMPONENT_APPLY_COMPONENTS_EVENT_NAME)
        {
            // 加载元件库
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            for (size_t i = 0; i < param_temp->entitys.components_param.size(); i++)
            {
                int result = model->LoadComponentByComponentEntity(param_temp->entitys.components_param.at(i), param_temp->entitys.components_param.size() == 1);
                if (result < 0)
                {
                    return;
                }
            }

            UpdateDetectWinView(param_->event_name);

            return;
        }
        else if (param_->event_name == OPERATE_COMPONENT_MULTIALGO_ADD_EVENT_NAME)
        {
            // 添加多个检测框
            auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
            jrsdata::ComponentEntity entity;
            GetCurSelectComponentEntity(param_, entity);
            model->MultiAlgoAdd(entity, param_temp->multi_algo_param);

            UpdateDetectWinView(param_->event_name);

            return;
        }
        else if (param_->event_name == jrsaoi::OPERATE_GET_LOADED_PROJECT_INFO_EVENT_NAME)
        {
            operate_view->SignalUpdateProjectView(param_);
            return;
        }
        //! 只在内部使用的事件，不需要传递到外部的直接return
        else if (param_->event_name == jrsaoi::OPERATE_CANCLE_LINK_PROJECT_EVENT_NAME ||
            param_->event_name == jrsaoi::OPERATE_CONFIRM_LINK_PROJECT_EVENT_NAME)
        {
            return;
        }
        else if (param_->event_name == jrsaoi::PROJECT_READ_EVENT_NAME && param_->sub_name == jrsaoi::OPERATE_LOGIC_SUB_NAME
            || param_->event_name == jrsaoi::PROJECT_UPDATE_EVENT_NAME)
        {
            //! 读新工程的时候，需要将算法列表清除一下
            emit operate_view->SignalUpdateEditDetectModelView(param_);

        }
        emit SigUpdateOperator(param_);
    }

    int OperateController::UpdateDetectWinEditView(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
        switch (param->data_operate_type)
        {
        case jrsdata::DataUpdateType::ADD_DATA:
        {
            //if (param->cur_select_detect_win->algorithms.size() > 0)
            //{
                //param->cur_algo_view = SlotGetAlgoView(param->cur_select_detect_win->algorithms[0].detect_algorithm_name);
            //}
            break;
        }
        case jrsdata::DataUpdateType::DELETE_DATA:
        {
            break;
        }
        case jrsdata::DataUpdateType::SELECT_DATA:
        case jrsdata::DataUpdateType::UPDATE_DATA:
        {

            if (param->cur_select_detect_win == nullptr || param->cur_select_detect_win->algorithms.empty())
            {
                Log_ERROR("不存在有效算法");
                return -1;
            }
            //TODO:从保存的结果中获取当前选中的算法结果
            OperatorParamBasePtr algo_result{ nullptr };
            OperatorParamBasePtr algo_param{ nullptr };
            if (param->cur_select_component)
            {
                algo_result = GetDetectWindowDetectResult(param->cur_select_component->component_name, param->cur_select_detect_win->name);

                UpdateAlgoResultToRender(algo_result, param->cur_select_component->subboard_name, param->cur_select_component->component_name, param->cur_select_detect_win->name);
            }
            algo_param = GetSpeficAlgoParam(*param->cur_select_detect_win);

            //! 如果存在已经检测过的结果，则将参数中的检测结果换成换成中的检测结果
            if (algo_result && algo_param)
            {
                algo_param->output_detect_rects = algo_result->output_detect_rects;
                algo_param->spec_value_params = algo_result->spec_value_params;
            }
            param->current_update_result_state = *param->cur_select_detect_win;
            param->operator_param = algo_param;
            break;
        }
        default:
            break;
        }
        const std::string old_event_name = param_->event_name;
        param_->event_name = OPERATE_UPDATE_DET_WIN_VIEW_EVENT_NAME;
        auto param_temp = std::dynamic_pointer_cast<jrsdata::AlgoEventParam>(param_);
        model->SetCurSelectedToAlgoEventParam(param_temp);
        emit operate_view->SignalUpdateEditDetectModelView(param_);
        param_->event_name = old_event_name;
        return 0;
    }

    void OperateController::UpdateAlgoResultToView(const jrsdata::AlgoEventParamPtr& param_, const std::shared_ptr<jrsoperator::OperatorParamBase> algo_result)
    {
        param_->operator_param = algo_result;
        param_->event_name = jrsaoi::OPERATE_UPDATE_ALGO_DETECT_RESULT_EVENT_NAME;
        if (param_->cur_select_component_unit->unit_type == jrsdata::ComponentUnit::Type::PAD)
        {
            param_->operator_param->algo_result_index = param_->cur_select_component_unit->show_id;
        }
        else
        {
            param_->operator_param->algo_result_index = param_->cur_select_component_unit->id;
        }
        operate_view->UpdateView(param_);
    }



    void OperateController::CurSelectedDetectWinRun(const jrsdata::AlgoEventParamPtr& param_)
    {

        cv::Mat matrix_to_src_image;
        auto res_detect = ExecuteSingleDetectWin(*param_->cur_select_component, *param_->cur_select_component_unit, *param_->cur_select_detect_win, matrix_to_src_image);

        if (!res_detect)
        {
            Log_ERROR("算法检测结果异常");
            return;
        }
        param_->current_update_result_state = *param_->cur_select_detect_win;
        UpdateAlgoResultToView(param_, res_detect);
        UpdateAlgoResultToRender(res_detect, param_->cur_select_component->subboard_name, param_->cur_select_component->component_name, param_->cur_select_detect_win->name);


    }

    void OperateController::CurSelectedComponentRun(const jrsdata::AlgoEventParamPtr& param_, bool is_save_algo_info, bool is_location)
    {

        //! 元件检测之前先将之前的保存状态清除
        model->EraseSpeficComponentResult(param_->cur_select_component->component_name);

        ExecuteSingleComponent(*param_->cur_select_component, *param_->cur_select_spec_and_detect_region, is_save_algo_info, is_location);

        UpdateSelectedComponentAlgoResult(param_);
    }

    int OperateController::SortDetectWinsByDepedent(const std::vector<jrsdata::DetectWindow>& detect_wins, std::vector<std::vector<jrsdata::DetectWindow>>& sorted_wins)
    {
        jtools::DirectedGraph directed_graph;
        for (auto& win : detect_wins)
        {
            jtools::Node node(win.name, win.parent_win_name);
            directed_graph.AddNode(node);
        }
        std::vector<std::vector<std::string>> all_paths, cycles;
        directed_graph.TraverseAndDetectCycle(all_paths, cycles);
        if (cycles.size() > 0)
        {
            Log_ERROR("检测框之间存在循环依赖关系");
            return -1;
        }

        for (auto& path : all_paths)
        {
            std::vector<jrsdata::DetectWindow> wins;
            for (auto& name : path)
            {
                for (auto& win : detect_wins)
                {
                    if (win.name == name)
                    {
                        wins.emplace_back(win);
                        break;
                    }
                }
            }
            sorted_wins.emplace_back(wins);
        }
        return 0;
    }

    OperatorParamBasePtr OperateController::ExecuteSingleDetectWin(const jrsdata::Component& component,
        const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win,
        cv::Mat& matrix_to_src_image_)
    {
        (void)component_unit;
        //! 算法检测参数 key:算法检测框名称(唯一性) value:检测参数
        std::map<std::string, jrsparam::ExecuteAlgoParam> detect_win_exec_params;
        if (model->GetComponentExecuteParam(component, detect_win_exec_params, matrix_to_src_image_) != 0)
        {
            Log_ERROR("获取当前选择的检测窗口执行参数失败");
            JRSMessageBox_ERR("Operate", "获取当前选择的检测窗口执行参数失败", jrscore::MessageButton::Ok);

            return nullptr;
        }
        auto cur_detect_win_exec_param_iter = detect_win_exec_params.find(detect_win.name);
        if (cur_detect_win_exec_param_iter == detect_win_exec_params.end())
        {
            Log_ERROR("当前选择的检测窗口执行参数不存在");
            JRSMessageBox_ERR("Operate", "当前选择的检测窗口执行参数不存在", jrscore::MessageButton::Ok);

            return nullptr;
        }
        auto res_detect = algo_engine_ptr->ExecuteSpecificAlgoDrive(cur_detect_win_exec_param_iter->second);

        if (!res_detect || !res_detect->error_message.empty())
        {
            std::string err_msg;

            if (!res_detect)
            {
                err_msg = "算法执行异常，返回结果为空";
            }
            else
            {
                err_msg = "算法执行失败，错误信息：" + res_detect->error_message;
            }

            Log_ERROR(err_msg);
            JRSMessageBox_ERR("Operate", err_msg, jrscore::MessageButton::Ok);

            //! 如果为空返回nullptr，否则返回原结果
            return res_detect ? res_detect : nullptr;
        }
        res_detect->transform_hom_matrix = matrix_to_src_image_;

        SetDetectWindowDetectResult(component, detect_win.name, res_detect);
        return res_detect;
    }

    void  OperateController::ExecuteSingleComponent(const jrsdata::Component& component, const jrsdata::PNDetectInfo& pn_detect_info,
        bool is_save_algo_info, bool is_location)
    {
        if (!algo_engine_ptr)
        {
            Log_ERROR("算法法引擎指针为空！");
            return;
        }
        jrsdata::Component component_temp = component;
        auto start_time = jtools::TimeUtility::GetCurrentTimeString("%Y_%m_%d_%H_%M_%S");
        cv::Mat res_src_martix;//! 平移旋转矩阵，用于将检测结果转换到原图上
        //! 算法检测参数 key:算法检测框名称(唯一性) value:检测参数
        std::map<std::string, jrsparam::ExecuteAlgoParam> detect_win_exec_params;


        //!获取当前元件算法执行参数
        model->GetComponentExecuteParam(component_temp, detect_win_exec_params, res_src_martix);
        auto detect_models = pn_detect_info.detect_models;
        //! 执行定位组算法
        auto [locate_martix, base_plane_mask_temp] = GetLocationModelMat(component_temp, detect_models, detect_win_exec_params, res_src_martix, start_time, is_save_algo_info);
        if (is_location)
        {
            auto componet_in_crop_img_iter = detect_win_exec_params.begin();
            if (componet_in_crop_img_iter != detect_win_exec_params.end())
            {
                auto component_in_crop_img_pos = componet_in_crop_img_iter->second.component_rect;
                model->ModifyComponentCoordinate(locate_martix, res_src_martix, component_in_crop_img_pos, component_temp);

            }
            model->UpdateSingleComponentInfo(component_temp);
            auto render_view_param = std::make_shared<jrsdata::RenderViewParam>();
            render_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
            render_view_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
            render_view_param->event_name = jrsaoi::REQUEST_RENDER2D_UPDATE_SEARCH_WINDOW_EVENT_NAME;
            render_view_param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
            render_view_param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
            emit SigUpdateOperator(render_view_param);

        }

        ProcessDetectionWindows(component_temp, detect_models, detect_win_exec_params, res_src_martix, locate_martix, base_plane_mask_temp, start_time, is_save_algo_info);

    }

    void OperateController::CurSelectedPartNumbRun(const jrsdata::AlgoEventParamPtr& param_, bool is_location)
    {

        for (auto& component : param_->same_part_numb_components)
        {
            //! 元件检测之前先将之前的保存状态清除
            model->EraseSpeficComponentResult(component->component_name);
            if (component->enable)
            {
                ExecuteSingleComponent(*component, *param_->cur_select_spec_and_detect_region, false, is_location);
            }
        }

        UpdateSelectedComponentAlgoResult(param_);


    }

    void OperateController::SaveAlgoExecuteParam(const std::shared_ptr<jrsoperator::OperatorParamBase>& detect_result, const jrsdata::Component& component_, const std::string& algo_name_, const std::string& start_time_, const std::string& algo_param_)
    {


        auto result_status = algo_engine_ptr->GetAlgoExecuteResultStatus(detect_result);
        auto algo_execute_info = std::make_shared<jrsdata::ComponentSaveInfo>();
        algo_execute_info->algo_execute_rect_param = algo_engine_ptr->GetAlgoExecuteRectInfo(detect_result);
        algo_execute_info->algo_param = algo_param_;
        algo_execute_info->current_time = start_time_;
        algo_execute_info->input_img = detect_result->input_image;
        algo_execute_info->algo_result_status = result_status;
        auto& project_data_instance = jrsaoi::ParamOperator::GetInstance().GetProjectDataProcessInstance();
        algo_execute_info->project_name = project_data_instance->GetProjectName();
        algo_execute_info->component_name = component_.component_name;
        algo_execute_info->algo_name = algo_name_;
        algo_execute_info->subboard_name = component_.subboard_name;
        algo_execute_info->hom_matrix = detect_result->hom_matrix;
        algo_execute_info->result_img = detect_result->result_image_group;
        algo_execute_info->output_mask_image = detect_result->output_mask_image;
        algo_execute_info->resolution_x = detect_result->x_resolution;
        algo_execute_info->resolution_y = detect_result->y_resolution;
        //! 模板信息
        for (auto& temp_value : detect_result->template_data)
        {
            jrsdata::TemplateInfo temp_info;
            temp_info.light_id = static_cast<int>(temp_value.id);
            temp_info.template_image = temp_value.template_img;
            temp_info.template_color_param = temp_value.color_params.ToJson();
            algo_execute_info->template_info_vector.push_back(temp_info);
        }

        //! 事件参数赋值
        algo_execute_info->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        algo_execute_info->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
        algo_execute_info->event_name = jrsaoi::OPERATE_SAVE_ALGO_EXECUTE_INFO_EVENT_NAME;
        algo_execute_info->module_name = jrsaoi::OPERATE_MODULE_NAME;
        algo_execute_info->invoke_module_name = jrsaoi::DATA_MODULE_NAME;
        emit SigUpdateOperator(algo_execute_info);

    }

    OperatorParamBasePtr OperateController::GetDetectWindowDetectResult(const std::string& component_name, const std::string& detect_window_name)
    {
        return model->GetDetectWindowDetectResult(component_name, detect_window_name);
    }

    void OperateController::SetDetectWindowDetectResult(const jrsdata::Component& component, const std::string& detect_window_name, OperatorParamBasePtr algo_result)
    {
        model->SetDetectWindowDetectResult(component, detect_window_name, algo_result);
        SaveComponentDetectStatus(component);
    }

    void OperateController::SaveComponentDetectStatus(const jrsdata::Component& component)
    {


        model->SaveComponentDetectStatus(component);
        auto operate_param_temp = std::make_shared<jrsdata::OperateViewParam>();
        operate_param_temp->module_name = jrsaoi::VIEW_MODULE_NAME;
        operate_param_temp->topic_name = jrsaoi::OPERATE_EXECUTE_COMPONENT_TOPIC_NAME;
        operate_param_temp->sub_name = jrsaoi::OPERATE_EXECUTE_COMPONENT_ALL_SUB_NAME;
        operate_param_temp->event_name = jrsaoi::OPERATE_UPDATE_COMPONENT_RESULT_STATUS_EVENT_NAME;

        operate_param_temp->component_detect_results = model->GetAllComponentResultStatus();
        emit SigUpdateOperator(operate_param_temp);
    }

    void OperateController::UpdateAlgoResultToRender(const OperatorParamBasePtr& result_algo, const std::string& subboard_name, const std::string& component_name, const std::string& detect_window_name)
    {
        if (!result_algo || result_algo->transform_hom_matrix.empty())
        {

            return;
        }
        //! 渲染界面需要渲染指定的检测框结果，因为只绘制当前选中的检测框结果，
        //! 所以需要检测框名+矩形编号(矩形编号主要在pad检测的时候才会用到)才能定位到
        std::string render_index_name = detect_window_name;
        std::vector<std::tuple<bool, cv::RotatedRect, std::string, cv::Mat>> detect_status_region;
        for (auto& res : result_algo->output_detect_rects)
        {
            cv::Mat res_mask_img;
            auto render_index_name_temp = render_index_name;
            if (render_index_name.find("pad") != std::string::npos)
            {
                auto unit_id = model->GetUnitIDByUnitShowID(subboard_name, component_name, detect_window_name, res.id);
                if (unit_id == -1)
                {
                    Log_ERROR("Pad框映射显示ID失败");
                }
                render_index_name_temp = render_index_name + ";" + std::to_string(unit_id);
                //auto res_img_group_it = result_algo->result_image_group.find(res.id);
                //if (res_img_group_it != result_algo->result_image_group.end())
                //{
                //    res_mask_img = res_img_group_it->second;
                //}

            }
            auto res_img_group_it = result_algo->intermediate_result.find(std::to_string(res.id));
            if (res_img_group_it != result_algo->intermediate_result.end())
            {
                res_mask_img = res_img_group_it->second;
            }
            jcvtools::JrsHomMat2D hom_mat(result_algo->transform_hom_matrix);
            auto rect_in_src_image = hom_mat.AffineTransRotatedRect(res.ToCvRotatedRect());
            detect_status_region.push_back(std::tuple(res.status, rect_in_src_image, render_index_name_temp, res_mask_img));
        }

        auto temp_param = std::make_shared<jrsdata::AlgoEventParam>();

        temp_param->detect_win_results = detect_status_region;
        temp_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        temp_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        temp_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        temp_param->event_name = jrsaoi::REQUEST_RENDER2D_DRAW_DETECT_RESULT_EVENT_NANE;

        emit SigUpdateOperator(temp_param);

    }

    void OperateController::GetCurSelectComponentEntity(const jrsdata::ViewParamBasePtr& param_, jrsdata::ComponentEntity& entity)
    {
        (void)param_;
        model->GetCurSelectComponentEntity(entity);
    }

    std::pair<cv::Mat, std::vector<cv::Mat>> OperateController::GetLocationModelMat(const jrsdata::Component& component_temp, std::unordered_map<std::string, jrsdata::DetectModel>& detect_models, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
        cv::Mat& res_src_matrix, const std::string& start_time, bool is_save_algo_info)
    {

        cv::Mat correction_matrix = cv::Mat::eye(2, 3, CV_32F);
        std::vector<cv::Mat >base_plane_mask_temp;

        auto iter_location_detect_model = detect_models.find("location");
        if (iter_location_detect_model == detect_models.end())
        {
            Log_INFO("未找到location检测框");
            return { correction_matrix,base_plane_mask_temp };
        }
        if (iter_location_detect_model->second.detect_model.empty())
        {
            Log_INFO("location组下没有算法");
            return { correction_matrix,base_plane_mask_temp };
        }
        if (detect_win_exec_params.empty())
        {
            Log_ERROR("元件检测框信息为空,无法检测");
            return { correction_matrix,base_plane_mask_temp };
        }
        std::vector<std::vector<jrsdata::DetectWindow>> sorted_detect_wins;
        if (SortDetectWinsByDepedent(iter_location_detect_model->second.detect_model, sorted_detect_wins) != 0)
        {
            Log_ERROR("检测框运行排序失败！");
            return { correction_matrix,base_plane_mask_temp };
        }

        std::unordered_map<int, cv::Mat> result_trans_matrixes;
        auto locate_result = ExecuteDetections(sorted_detect_wins, detect_win_exec_params, correction_matrix, res_src_matrix, base_plane_mask_temp, result_trans_matrixes, component_temp, start_time, is_save_algo_info);

        detect_models.erase("location");
        //! 如果定位算法NG，则恢复单位矩阵，不矫正
        if (!locate_result)
        {
            return { correction_matrix,base_plane_mask_temp };
        }


        if (result_trans_matrixes.empty())
        {
            return { correction_matrix,base_plane_mask_temp };
        }
        else
        {
            return { result_trans_matrixes.begin()->second,base_plane_mask_temp };
        }

    }

    void OperateController::ProcessDetectionWindows(jrsdata::Component& component_temp, std::unordered_map<std::string, jrsdata::DetectModel>& detect_models,
        std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params, cv::Mat& res_src_matrix, cv::Mat& locate_matrix, std::vector<cv::Mat>& base_plane_mask_,
        const std::string& start_time, bool is_save_algo_info)
    {
        for (auto& detect_model : detect_models)
        {
            std::vector<std::vector<jrsdata::DetectWindow>> sorted_detect_wins;
            if (SortDetectWinsByDepedent(detect_model.second.detect_model, sorted_detect_wins) != 0) {
                Log_ERROR("Failed to sort detection windows!");
                continue;
            }
            std::unordered_map<int, cv::Mat> result_trans_matrixes;
            ExecuteDetections(sorted_detect_wins, detect_win_exec_params, locate_matrix, res_src_matrix, base_plane_mask_, result_trans_matrixes, component_temp, start_time, is_save_algo_info);
        }
    }

    bool  OperateController::ExecuteDetections(const std::vector<std::vector<jrsdata::DetectWindow>>& sorted_detect_wins, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_exec_params,
        const cv::Mat& correction_matrix,
        const cv::Mat& trans_to_board_matrix,
        std::vector<cv::Mat>& base_plane_mask,
        std::unordered_map<int, cv::Mat>& result_trans_matrixes,
        const jrsdata::Component& component_temp, const std::string& start_time, bool is_save_algo_info)
    {

        bool execute_result_status = true;
        for (const auto& wins : sorted_detect_wins)
        {
            std::unordered_map<int, cv::Mat> correction_matrixes;
            // bool is_correction_matrixes_init = false;

            for (const auto& detect_win : wins)
            {
                //! 检测框没有启用则不检测
                if (detect_win.enable)
                {
                    auto exect_param = detect_win_exec_params.find(detect_win.name);
                    if (exect_param == detect_win_exec_params.end())
                    {
                        continue;
                    }

                    //! 如果是第一个检测框，则使用定位算法的矫正矩阵
                    if (correction_matrixes.empty())
                    {
                        for (auto& elem : exect_param->second.correction_matrixes)
                        {
                            elem.second = correction_matrix.clone();
                        }
                    }
                    else
                    {
                        for (auto& elem : exect_param->second.correction_matrixes)
                        {
                            if (correction_matrixes.find(elem.first) != correction_matrixes.end())
                            {
                                elem.second = correction_matrixes.find(elem.first)->second.clone();
                            }
                            else
                            {
                                elem.second = correction_matrixes.begin()->second.clone();
                            }
                        }
                    }



                    exect_param->second.input_mask_image[1] = base_plane_mask;


                    auto res_detect = algo_engine_ptr->ExecuteSpecificAlgoDrive(exect_param->second);
                    if (!res_detect)
                    {
                        Log_ERROR("Algorithm execution failed for detection window: " + detect_win.name);
                        continue;
                    }

                    if (is_save_algo_info)
                    {
                        SaveAlgoExecuteParam(res_detect, component_temp, exect_param->second.algo_name, start_time, exect_param->second.algo_param);
                    }

                    correction_matrixes = res_detect->hom_matrix;
                    //! 如果 base_plane_mask 为空，且两个检测结果都非空，则添加基面数据 by zhangyuyu 2025.4.25
                    if (base_plane_mask.empty() &&
                        !res_detect->output_mask_image.empty() &&
                        !res_detect->height_mat.empty())
                    {
                        base_plane_mask.push_back(res_detect->output_mask_image);
                        base_plane_mask.push_back(res_detect->height_mat);
                    }
                    res_detect->transform_hom_matrix = trans_to_board_matrix;
                    SetDetectWindowDetectResult(component_temp, detect_win.name, res_detect);
                    if (execute_result_status)
                    {
                        execute_result_status = algo_engine_ptr->GetAlgoExecuteResultStatus(res_detect);
                    }
                }
            }
            result_trans_matrixes = correction_matrixes;
        }
        return execute_result_status;
    }

    void OperateController::UpdateSelectedComponentAlgoResult(const jrsdata::AlgoEventParamPtr& param_)
    {
        if (!param_->cur_select_component || !param_->cur_select_spec_and_detect_region || !param_->cur_select_detect_win || !param_->cur_select_component_unit)
        {
            return;
        }
        //! 所有料号检测完成之后只需要更新当前选中元件算法界面，其他元件当选中切换时再更新 by zhangyuyu 2025.1.19
        auto spefic_component_result = model->GetSpeficComponentResultStatus(param_->cur_select_component->component_name);
        OperatorParamBasePtr algo_param = nullptr;

        if (spefic_component_result)
        {
            for (auto& value : spefic_component_result.value())
            {

                //if (value.first == param_->cur_select_detect_win->name)
              /*  {
                    UpdateAlgoResultToView(param_, value.second.algo_result);

                }*/
                for (auto& detect_model : param_->cur_select_spec_and_detect_region->detect_models)
                {
                    for (auto& detect_win : detect_model.second.detect_model)
                    {
                        if (detect_win.name == value.first)
                        {
                            param_->current_update_result_state = detect_win;
                            algo_param = GetSpeficAlgoParam(detect_win);

                            //! 如果存在已经检测过的结果，则将参数中的检测结果换成换成中的检测结果
                            if (algo_param)
                            {
                                algo_param->intermediate_result = value.second.algo_result->intermediate_result;
                                algo_param->result_image_group = value.second.algo_result->result_image_group;
                                algo_param->output_detect_rects = value.second.algo_result->output_detect_rects;
                                algo_param->spec_value_params = value.second.algo_result->spec_value_params;
                                algo_param->transform_hom_matrix = value.second.algo_result->transform_hom_matrix;
                            }
                            UpdateAlgoResultToView(param_, algo_param);
                            if (value.first == param_->cur_select_detect_win->name)
                            {
                                UpdateAlgoResultToRender(algo_param, param_->cur_select_component->subboard_name, param_->cur_select_component->component_name, param_->cur_select_detect_win->name);

                            }
                        }
                    }
                }

            }
        }
    }



    OperatorParamBasePtr OperateController::GetSpeficAlgoParam(const jrsdata::DetectWindow& cur_select_detect_win)
    {
        if (cur_select_detect_win.algorithms.empty())
        {
            Log_ERROR("当前检测窗口中没有算法配置");
            return nullptr;
        }
        OperatorParamBasePtr algo_param = nullptr;
        const auto& algo_info = cur_select_detect_win.algorithms[0];
        if (algo_info.detect_algorithm_name.empty())
        {
            Log_ERROR("算法名称异常，为空");
            return nullptr;
        }

        algo_param = algo_engine_ptr->GetSpecificAlgoParamPtr(algo_info.detect_algorithm_name);
        if (algo_param == nullptr)
        {
            return algo_param;
        }
        auto operator_ptr = std::dynamic_pointer_cast<iguana::base>(algo_param);
        if (!algo_info.param.empty())
        {
            try
            {
                operator_ptr->from_json(algo_info.param);

            }
            catch (std::runtime_error& err)
            {
                Log_ERROR("解析算法参数失败，算法参数改变，不匹配！", err.what());
                JRSMessageBox_ERR("算法", "算发参数解析失败", jrscore::MessageButton::Ok);

            }
        }
        return algo_param;
    }

    void OperateController::UpdateDetectWinView(std::string event_name)
    {
        //! 添加完成之后刷新检测框列表
        auto algo_param_algo_update = std::make_shared<jrsdata::AlgoEventParam>();
        model->SetCurSelectedToAlgoEventParam(algo_param_algo_update);
        algo_param_algo_update->event_name = event_name;
        emit operate_view->SignalUpdateEditDetectModelView(algo_param_algo_update);
        // 通知刷新
        auto render_view_param = std::make_shared<jrsdata::RenderViewParam>();
        render_view_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        render_view_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        render_view_param->event_name = jrsaoi::REQUEST_RENDER2D_UPDATE_SELECT_EVENT_NAME;
        render_view_param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        render_view_param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;
        emit SigUpdateOperator(render_view_param);
    }

    void OperateController::ConvertDetectResult(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!algo_engine_ptr)
        {
            Log_ERROR("算法法引擎指针为空！");
            return;
        }
        auto param_temp = std::dynamic_pointer_cast<jrsdata::OperateViewParam>(param_);
        if (param_temp != nullptr)
        {
            if (param_temp->detect_statistics_view_param.detect_result_param.has_value())
            {
                for (auto& mark_result : param_temp->detect_statistics_view_param.detect_result_param->mark_results)
                {
                    for (auto& one_mark_result : mark_result.detect_window_results)
                    {
                        auto rs = jrsaoi::ParamOperator::GetInstance().GetProjectDataProcessInstance()->ReadDetectWindow(mark_result.part_number, one_mark_result.first);
                        for (auto& algo_temp : rs->algorithms)
                        {
                            auto result_algo_item_value = algo_engine_ptr->GetAlgoExecuteResultParam(one_mark_result.second, algo_temp.detect_algorithm_name);
                            for (auto& rect_data : one_mark_result.second->output_detect_rects)
                            {
                                param_temp->detect_statistics_view_param.detect_result_param->mark_result_rect.insert(std::make_pair(mark_result.component_name, std::make_tuple(rect_data.cx, rect_data.cy, rect_data.width, rect_data.height)));
                                break;
                            }
                            param_temp->detect_statistics_view_param.detect_result_param->mark_scores.insert(std::make_pair(mark_result.component_name, result_algo_item_value.result_score));
                        }
                    }
                }
            }
        }
    }

}

