/*****************************************************************//**
 * @file   translate.h
 * @brief  转换
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSTRANSLATE_H__
#define __JRSTRANSLATE_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <opencv2/opencv.hpp>
#include <QImage>
#pragma warning(pop)
class QLabel;
namespace imagetrans
{
	QImage MatToQImage(const cv::Mat& mat);
    void SetImageToLabel(QLabel* label, const cv::Mat& image);
}
#endif