﻿/*********************************************************************
 * @brief  图形界面事件参数.
 *
 * @file   render2deventparam.hpp
 *
 * @date   2024.10.10
 * <AUTHOR>
 *********************************************************************/
#ifndef RENDER2DEVENTPARAM_HPP
#define RENDER2DEVENTPARAM_HPP
 //prebuild
#include "pch.h"
#include "controlconstants.hpp"
#include "graphicsid.h" // GraphicsID
//#include "projectparam.hpp"
#include "graphicsabstract.hpp"  // GraphicsAbstract
//#include "viewparam.hpp" 
#include "opencv2/opencv.hpp"
namespace jrsdata
{
    /**
     * . 图形编辑参数 by zhangyuyu 2024.9.24
     */
    struct GraphicsEditParam : public jrsdata::ViewParamBase
    {
        std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
        GraphicsEditParam(std::vector<std::shared_ptr<GraphicsAbstract>>&& ghs) noexcept
            : ghs(std::move(ghs))
        {
        }
        GraphicsEditParam(GraphicsEditParam&& other) noexcept
            : ghs(std::move(other.ghs))
        {
        }
        GraphicsEditParam& operator=(GraphicsEditParam&& other) noexcept
        {
            if (this != &other)
            {
                ghs = std::move(other.ghs);
            }
            return *this;
        }
        GraphicsEditParam(const GraphicsEditParam&) = delete;
        GraphicsEditParam& operator=(const GraphicsEditParam&) = delete;
    };
};

namespace jrsaoi
{
    /**
     * @brief  图片渲染参数.
     *
     * @date   2024.09.25
     * <AUTHOR>
     */
    struct ImageShowParam
    {
        cv::Mat image; /**< 显示图片内容*/
        int x;         /**< 渲染界面上图形x坐标(像素)*/
        int y;         /**< 渲染界面上图形y坐标(像素)*/
        int z;         /**< 渲染界面上图形属于那一层，是渲染空间特有的参数*/
        float angle;   /**< 渲染界面上图形角度(°)*/
        ImageShowParam()
            : x(0), y(0), z(0), angle(0.0f), image()
        {
        }
    };
    struct RenderSelectParam
    {
        std::string subboard_name;
        std::string component_name;
        std::string unit_name;
        std::string window_name;
        std::string subwindow_name;
        void Clear()
        {
            subboard_name = "";
            component_name = "";
            unit_name = "";
            window_name = "";
            subwindow_name = "";
        }
    };
    struct RenderSelectObject
    {
        std::weak_ptr<GraphicsAbstract> subboard_graphics;
        std::weak_ptr<GraphicsAbstract> component_graphics;
        std::weak_ptr<GraphicsAbstract> unit_graphics;
        std::weak_ptr<GraphicsAbstract> window_graphics;
        std::weak_ptr<GraphicsAbstract> subwindow_graphics;
    };
    struct UpdateOperatorParam
    {
        enum class UpdateOperatorType/**< 元件框操作类型*/
        {
            UPDATE_OPERATOR,
            CREATE_OPERATOR,
            DELETE_OPERATOR,
            SELECT_OPERATOR,
        };
        UpdateOperatorType type;
        jrsdata::Component::Type component_type = jrsdata::Component::Type::CAD;
        jrsdata::ComponentUnit::Type unit_type = jrsdata::ComponentUnit::Type::BODY;
        std::string current_layer;
        std::string current_id;                                 ///< 当前编辑图形id
        std::string window_name;                                //! 当前图形框名称
        std::string algo_name;                                //! 当前图形框添加的算法名称(只有是算法框的时候才有对应的算法名称) by zhangyuyu 2025.1.7
        std::string model_name;                                 ///< 当前编辑模型名称
        std::string defect_name;                                ///< 缺陷名称
        std::string unit_group_name;
        std::string part_name;                                  ///< 当前编辑元件料号
        int         sub_win_type = 0;                           ///< 子检测框类型
        RenderSelectParam select_param;
        RenderSelectObject select_object;

        // 清空所有字段
        void Clear()
        {
            //type = UpdateOperatorType::UPDATE_OPERATOR;  // 或者设置为你需要的默认值
            //component_type = jrsdata::Component::Type::CAD;
            //unit_type = jrsdata::ComponentUnit::Type::BODY;
            current_layer.clear();
            current_id.clear();
            window_name.clear();
            algo_name.clear();
            model_name.clear();
            defect_name.clear();
            unit_group_name.clear();
            part_name.clear();
            sub_win_type = 0;
            select_param.Clear();
        }

    };

    // /**
    //  * @brief  图形编辑参数.
    //  *
    //  * @date   2024.09.25
    //  * <AUTHOR>
    //  */
    // struct RenderEditParam
    // {
    //     /**
    //      * 层次依次往下
    //      */
    //     std::weak_ptr<GraphicsAbstract> g_current_mark;        ///< 当前编辑基准点
    //     std::weak_ptr<GraphicsAbstract> g_current_barcode;     ///< 当前编辑条码
    //     std::weak_ptr<GraphicsAbstract> g_current_subboard;    ///< 当前编辑子板
    //     std::weak_ptr<GraphicsAbstract> g_current_cad;         ///< 当前编辑元件
    //     std::weak_ptr<GraphicsAbstract> g_current_pad;         ///< 当前编辑焊盘
    //     std::weak_ptr<GraphicsAbstract> g_current_sub_mark;    ///< 当前编辑子板基准点
    //     std::weak_ptr<GraphicsAbstract> g_current_sub_barcode; ///< 当前编辑子板条码
    //     std::weak_ptr<GraphicsAbstract> g_current_region;      ///< 当前编辑区域
    //     std::weak_ptr<GraphicsAbstract> g_current_sub_region;  ///< 当前编辑子区域
    //     std::string region_parent_layer;                       ///< 当前编辑区域父图层
    //     std::string current_algo_name;                         ///< 当前编辑算法名称
    //     GraphicsID current_id;                                 ///< 当前编辑图形id
    // };

    /**
     * @brief  图形创建参数.
     *
     * @date   2024.09.25
     * <AUTHOR>
     */
    struct RenderCreateParam
    {
        std::string create_layer;  ///< 图形层级
        std::weak_ptr<GraphicsAbstract> parent; ///< 图形父级
        std::string region_parent_layer; ///< 当前编辑区域父图层
        std::string parent_name; ///< 图形父级
        std::string create_name;
        std::string part_name;
        bool IsEmpty() { return create_layer.empty(); }
        void Reset()
        {
            create_layer = ""; parent.reset(); region_parent_layer = "", parent_name = ""; create_name = ""; part_name = "";
        }
        std::shared_ptr<GraphicsAbstract> GetParent() { return parent.lock(); }
    };
    /**
     * @brief  cad编辑参数.
     *
     * @date   2024.09.25
     * <AUTHOR>
     */
    struct CADEditParam
    {
        jrsdata::CadEventParam cad_event_param;                      ///< cad事件参数
        GraphicsID current_graphics_id;
        std::vector<GraphicsID> ori_graphics_ids;                    ///< 标记原位置的图形id集合
        std::vector<std::shared_ptr<GraphicsAbstract>> cur_graphics; ///< 标记当前位置的图形,为了避免被界面刷新删除,这里做备份
        std::vector<cv::Point> ori_points; ///< 原位置点集合
        std::unordered_map<GraphicsID/**<graphics id*/, std::tuple<cv::Point2f/**< 原来位置 */, cv::Point2f/**<当前选择位置*/>, GraphicsIDHash, GraphicsIDEqual> graphic_and_positions;
        std::vector<cv::Point> cur_points; ///< 当前位置点集合
        std::string cad_parent_subboard_name; ///< 当前编辑cad所在连板名称
    };

    struct TempRegionEditParam
    {
        // EditMode 为 0 时表示没有操作
        // EditMode 为 1 时表示正在编辑区域
        int edit_mode = 0;
    };

    /**
     * @brief  多联板编辑参数.
     *
     * @date   2024.09.25
     * <AUTHOR>
     */
    struct SubEditParam
    {
        jrsdata::MultiBoardEventParam multi_event_param; ///< 多联板事件参数

        std::string flag_id; ///< 标记多联板克隆模板的标记位置对应图形的id
        std::string flag_cols_id; ///< 标记多联板克隆列的标记位置对应图形的id
        std::string flag_rows_id; ///< 标记多联板克隆行的标记位置对应图形的id
        cv::Point flag; ///< 多联板克隆模板中心位置
        cv::Point flag_cols; ///< 多联板克隆列中心位置
        cv::Point flag_rows; ///< 多联板克隆行中心位置

        std::vector<std::vector<float>> res; ///< 多联板克隆结果

        SubEditParam() :
            multi_event_param(),
            flag_id(""), flag_cols_id(""), flag_rows_id(""),
            flag(cv::Point(0, 0)), flag_cols(cv::Point(0, 0)), flag_rows(cv::Point(0, 0)),
            res({})
        {
        }
    };
    /**
     * @brief  多联板克隆参数.
     *
     * @date   2024.09.25
     * <AUTHOR>
     */
    struct SubBoardCloneParam
    {
        bool is_have_col_flag;
        bool is_have_row_flag;

        int cols;
        int rows;
        int copy_mode;

        std::string subboard_name; ///< 克隆模板名称
        cv::Point subboard_center; ///< 多联板中心
        cv::Point flag_temp;       ///< 多联板克隆原模板中心位置
        cv::Point flag_col;        ///< 多联板克隆列模板中心位置
        cv::Point flag_row;        ///< 多联板克隆行模板中心位置
        std::vector<std::vector<float>> res; ///< 多联板克隆结果


        SubBoardCloneParam() :
            subboard_center(cv::Point(0, 0)),
            flag_temp(cv::Point(0, 0)),
            flag_col(cv::Point(0, 0)), flag_row(cv::Point(0, 0)),
            cols(0), rows(0), copy_mode(0),
            is_have_col_flag(true), is_have_row_flag(true),
            res({})
        {
        }
    };
    struct PadEditParam
    {
        std::shared_ptr<GraphicsAbstract> first_gh;
        std::shared_ptr<GraphicsAbstract> end_gh;
        jrsdata::PadEventParam pad_event_param;
        RenderCreateParam render_create_param;
    };
    struct Render2dControllerParam
    {
        bool is_update_entirty_board_images; //如果工程图更新，单个FOV图片不更新
        Render2dControllerParam()
            :is_update_entirty_board_images(false)
        {

        }
    };
    struct Render2dEventParam
    {
        RenderCreateParam render_create_param; ///< 创建参数
        // RenderEditParam render_edit_param;     ///< 编辑参数
        UpdateOperatorParam update_operator_param; ///< 操作参数
        SubEditParam sub_edit_param;
        CADEditParam cad_edit_param;
        TempRegionEditParam temp_region_edit_param;   /**< 临时模板*/
        TempRegionEditParam show_3d_region_edit_param;  /**< 3d显示*/

        TempRegionEditParam multiple_board_region_edit_param;  /**< 多联板临时区域*/

        jrsdata::AlgoEventParamPtr algo_event_param;
        PadEditParam pad_edit_param;
        Render2dControllerParam render_2d_controller_param;
    };
}

#endif // RENDER2DEVENTPARAM_HPP