#include "rulerwidget.h"
#include <QPaintEvent>
#include <QPainter>
/**
 * @brief 计算卡尺刻度位置
 * @param min
 * @param max
 * @param minScaleDistance
 * @param scale_factors
 * @return
 */
std::vector<double> CalculateScalePositions(double min, double max, double minScaleDistance, const std::vector<double>& scale_factors)
{
    std::vector<double> scalePositions;

    // 选择适合的刻度间距，使得刻度不过于密集或稀疏
    double scaleDistance = minScaleDistance;

    for (const double& factor : scale_factors)
    {
        if (scaleDistance <= factor)
        {
            scaleDistance = factor;
            break;
        }
    }

    // 计算刻度位置 前后多算位置,避免坐标轴上出现空白
    // 往前算一个位置
    int num = (int)std::ceil(min / scaleDistance) - 1;
    double start = num * scaleDistance;
    // 往后算两个位置,一个位置在刚刚好的时候还是会出现空白
    double end = max + scaleDistance * 2;
    for (double pos = start; pos < end; pos += scaleDistance)
    {
        scalePositions.push_back(pos);
    }

    return scalePositions;
}
/**
 * @brief 将数值映射到指定范围内
 */
double Mapvalue(double value, double a, double b, double a1, double b1)
{
    return a1 + (value - a) * ((b1 - a1) / (b - a));
}

/**
 * @brief 刻度尺比率因子
 */
const std::vector<double> RulerWidget::scale_factors{ 0.05,0.1,0.25,0.5, 1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000 };

RulerWidget::RulerWidget(Qt::Orientations orientation_, QWidget* parent)
    : QWidget(parent)
    , cursor_position(0), ruler_width(20), precision(0), orientation(orientation_)
    , use_scale(false), tick_min_value(0), tick_max_value(100), tick_step_short(1), tick_step_long(10), tick_scale(1.0)
    , text_alignment(RulerTextAlignment::ABOVE)
    , font({ "微软雅黑", static_cast<int>(ruler_width * 0.5 - 1) })
    , color_background("#FFFFFF")
    , color_text_and_tick("#606060"), color_cursor_line("#00BFFF")
{
    orientation == Qt::Horizontal ? setFixedHeight(ruler_width) : setFixedWidth(ruler_width);
    color_cursor_line.setAlphaF(0.7);
}

void RulerWidget::SetCursorPosition(int pos)
{
    cursor_position = pos;
    update();
}

void RulerWidget::SetRange(double minValue_, double maxValue_)
{
    if (minValue_ == maxValue_)
        return;
    SetMinValue(minValue_);
    SetMaxValue(maxValue_);
    update();
}

void RulerWidget::SetRange(int minValue_, int maxValue_)
{
    SetRange((double)minValue_, (double)maxValue_);
}

void RulerWidget::SetScale(double scale)
{
    if (scale == 0)
        return;
    this->tick_scale = scale;
    SetUseScale(true);
}

void RulerWidget::SetUseScale(bool state)
{
    this->use_scale = state;
}

void RulerWidget::ChangeUseScale()
{
    this->use_scale = !this->use_scale;
    update();
}

void RulerWidget::SetMinValue(double minValue_)
{
    this->tick_min_value = minValue_;
}

void RulerWidget::SetMaxValue(double maxValue_)
{
    this->tick_max_value = maxValue_;
}

void RulerWidget::SetPrecision(int precision_)
{
    if (precision_ < 0)
    {
        return;
    }

    this->precision = precision_;
}

void RulerWidget::SetRulerTextAlignment(RulerTextAlignment alignment)
{
    this->text_alignment = alignment;
}

void RulerWidget::SetBackgroundColor(QColor color)
{
    color_background = color;
    update();
}

void RulerWidget::SetTextAndLineColor(QColor color)
{
    color_text_and_tick = color;
    update();
}

void RulerWidget::SetSlidingLineColor(QColor color)
{
    color_cursor_line = color;
    update();
}

void RulerWidget::paintEvent(QPaintEvent* event)
{
    tick_step_long = 10;
    tick_step_short = 1;

    QPainter painter(this);
    bool isHorizontal = orientation == Qt::Horizontal;

    auto rect = event->rect();
    painter.setRenderHint(QPainter::Antialiasing);
    painter.fillRect(rect, color_background);
    if (!isHorizontal)
    {
        painter.translate(rect.width(), 0);
        painter.rotate(90);
    }
    auto draw_height = (isHorizontal ? rect.height() : rect.width());
    auto draw_width = (isHorizontal ? rect.width() : rect.height());

    painter.setFont(font);

    // 绘制标尺
    switch (text_alignment)
    {
    case RulerTextAlignment::ABOVE:
    {
        DrawRuler(&painter, draw_width, draw_height, true);
        // DrawRulerTop(&painter, draw_width, draw_height);
    }
    break;
    case RulerTextAlignment::BELOW:
    {
        DrawRuler(&painter, draw_width, draw_height, false);
        // DrawRulerBottom(&painter, draw_width, draw_height);
    }
    break;
    }
    // 绘制标记
    DrawCursorLine(&painter, 3, draw_width, draw_height);

    QWidget::paintEvent(event);
}

void RulerWidget::mouseMoveEvent(QMouseEvent* event)
{
    SetCursorPosition(orientation == Qt::Horizontal ? event->pos().x() : event->pos().y());
}

void RulerWidget::DrawCursorLine(QPainter* painter, int slide_width, int width, int height)
{
    painter->save();
    // painter->setOpacity(0.5);
    painter->setPen(Qt::transparent);
    painter->setBrush(color_cursor_line);
    {
        painter->drawRect(cursor_position, 0, slide_width, height);
    }

    double current_scale = use_scale ? tick_scale : 1.0;
    double current_tick_min_value = tick_min_value * current_scale;
    double current_tick_max_value = tick_max_value * current_scale;

    {
        auto fontmetrics = QFontMetrics(font);
        const auto& value = Mapvalue(cursor_position, 0, width, current_tick_min_value, current_tick_max_value);

        QString strValue = QString("%1").arg(value, 0, 'f', 1);
        QSize textsize = fontmetrics.size(Qt::TextSingleLine, strValue);
        double textWidth = textsize.width();
        double textHeight = textsize.height() - painter->pen().width();

        double initY = 0;
        QPointF move;

        switch (text_alignment)
        {
        case RulerTextAlignment::ABOVE:
        {
            initY = textHeight;
            move = QPointF(0, textHeight * 0.5);
        }
        break;
        case RulerTextAlignment::BELOW:
        {
            initY = height - textHeight * 0.5;
            move = QPointF(0, textHeight);
        }
        break;
        }

        QPointF textPot = QPointF(cursor_position - textWidth * 0.5, initY);
        QRectF textRect = QRectF(textPot - move, textsize);
        painter->drawRect(textRect);

        painter->setPen(color_text_and_tick);
        painter->drawText(textPot, strValue);
    }
    painter->restore();
}

void RulerWidget::DrawRuler(QPainter* painter, int width, int height, bool isTop)
{
    painter->save();
    painter->setPen(color_text_and_tick);
    auto fontmetrics = QFontMetrics(font);

    double current_scale = use_scale ? tick_scale : 1.0;
    double current_tick_min_value = tick_min_value * current_scale;
    double current_tick_max_value = tick_max_value * current_scale;
    int current_precision = use_scale ? precision : 0;

    // 初始化标尺基线位置
    double initY = isTop ? 0 : height;  // 顶部 or 底部
    QPointF lineLeftPot = QPointF(0, initY);
    QPointF lineRightPot = QPointF(width, initY);
    painter->drawLine(lineLeftPot, lineRightPot);

    // 定义刻度线长度
    double length = width;
    int longLineLen = std::min(15, (int)(height * 0.35));
    int middleLineLen = std::min(10, (int)(height * 0.3));
    int shortLineLen = std::min(6, (int)(height * 0.2));

    // 计算刻度步长
    int minTextStep = 1;
    {
        QSize minValueSize = fontmetrics.size(Qt::TextSingleLine, QString::number(current_tick_min_value, 'f', current_precision));
        QSize maxValueSize = fontmetrics.size(Qt::TextSingleLine, QString::number(current_tick_max_value, 'f', current_precision));
        minTextStep = std::max(1, std::max(minValueSize.width(), maxValueSize.width()));
    }
    double ratio = static_cast<double>(current_tick_max_value - current_tick_min_value) / length;
    double minValueStep = minTextStep * ratio * 1.5;  // 避免标尺数值过于密集
    auto positions = CalculateScalePositions(current_tick_min_value, current_tick_max_value, minValueStep, scale_factors);
    if (positions.empty())
    {
        painter->restore();
        return;
    }

    // 绘制刻度线与文本
    const int subSteps = 10;
    int tickStepMiddle = tick_step_long / 2;

    for (size_t j = 0; j < positions.size() - 1; ++j)
    {
        double currentValue = Mapvalue(positions[j], current_tick_min_value, current_tick_max_value, 0, length);
        double nextValue = Mapvalue(positions[j + 1], current_tick_min_value, current_tick_max_value, 0, length);
        double interval = (nextValue - currentValue) / subSteps;

        // 绘制刻度文本
        QString strValue = QString::number(positions[j], 'f', current_precision);
        QSize textSize = fontmetrics.size(Qt::TextSingleLine, strValue);
        double textWidth = textSize.width();
        double textHeight = textSize.height();

        QPointF textPot = isTop
            ? QPointF(currentValue - textWidth * 0.5, initY + textHeight)
            : QPointF(currentValue - textWidth * 0.5, initY - textHeight * 0.5);
        painter->drawText(textPot, strValue);

        // 绘制刻度线
        for (int i = 0; i < subSteps; ++i)
        {
            double tickPos = currentValue + i * interval;
            QPointF topPot = QPointF(tickPos, initY);
            QPointF bottomPot;

            if (i % tick_step_long == 0)
                bottomPot = isTop ? QPointF(tickPos, initY + longLineLen) : QPointF(tickPos, initY - longLineLen);
            else if (i % tickStepMiddle == 0)
                bottomPot = isTop ? QPointF(tickPos, initY + middleLineLen) : QPointF(tickPos, initY - middleLineLen);
            else if (i % tick_step_short == 0)
                bottomPot = isTop ? QPointF(tickPos, initY + shortLineLen) : QPointF(tickPos, initY - shortLineLen);
            else
                continue;

            painter->drawLine(topPot, bottomPot);
        }
    }

    painter->restore();
}

void RulerWidget::DrawRulerTop(QPainter* painter, int width, int height)
{
    painter->save();
    painter->setPen(color_text_and_tick);
    auto fontmetrics = QFontMetrics(font);

    double initX = 0;

    // 绘制横向标尺上部分底部线
    double initTopY = 0;
    QPointF lineTopLeftPot = QPointF(initX, initTopY);
    QPointF lineTopRightPot = QPointF(width - initX, initTopY);
    painter->drawLine(lineTopLeftPot, lineTopRightPot);

    double length = width;
    int longLineLen = std::min(15, (int)(height * 0.35));
    int middleLineLen = std::min(10, (int)(height * 0.3));
    int shortLineLen = std::min(6, (int)(height * 0.2));

    int mintextstep = 1;
    {
        QSize minvaluetextsize = fontmetrics.size(Qt::TextSingleLine, QString("%1").arg((double)tick_min_value, 0, 'f', precision));
        QSize maxvaluetextsize = fontmetrics.size(Qt::TextSingleLine, QString("%1").arg((double)tick_max_value, 0, 'f', precision));
        mintextstep = std::max(1, std::max(minvaluetextsize.width(), maxvaluetextsize.width()));
    }
    double ratio = (double)(tick_max_value - tick_min_value) / (length);
    double minvaluestep = (double)mintextstep * ratio + 5; // 适当扩展,避免在小区域范围内太过紧凑
    auto positions = CalculateScalePositions(tick_min_value, tick_max_value, minvaluestep, scale_factors);
    if (positions.empty())
    {
        return;
    }
    // 绘制
    const int step = 10;
    int tick_step_middle = tick_step_long / 2;
    for (size_t j = 0; j < positions.size() - 1; ++j)
    {
        const auto& value = Mapvalue(positions[j], tick_min_value, tick_max_value, 0, length);
        const auto& nextvalue = Mapvalue(positions[j + 1], tick_min_value, tick_max_value, 0, length);
        const auto& interval = (nextvalue - value) / step;

        QString strValue = QString("%1").arg((double)positions[j] * tick_scale, 0, 'f', precision);
        QSize textsize = fontmetrics.size(Qt::TextSingleLine, strValue);
        double textWidth = textsize.width();
        double textHeight = textsize.height();

        QPointF textPot = QPointF(value - textWidth * 0.5, initTopY + textHeight);
        painter->drawText(textPot, strValue);
        for (int i = 0; i < step; ++i)
        {
            initX = value + i * interval;
            if (i % tick_step_long == 0)
            {
                QPointF topPot = QPointF(initX, initTopY);
                QPointF bottomPot = QPointF(initX, initTopY + longLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else if (i % tick_step_middle == 0)
            {
                QPointF topPot = QPointF(initX, initTopY);
                QPointF bottomPot = QPointF(initX, initTopY + middleLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else if (i % tick_step_short == 0)
            {
                QPointF topPot = QPointF(initX, initTopY);
                QPointF bottomPot = QPointF(initX, initTopY + shortLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else
            {
                continue;
            }
        }
    }

    painter->restore();
}

void RulerWidget::DrawRulerBottom(QPainter* painter, int width, int height)
{
    painter->save();
    painter->setPen(color_text_and_tick);
    auto fontmetrics = QFontMetrics(font);

    double initX = 0;

    // 绘制横向标尺下部分底部线
    double initBottomY = height;
    QPointF lineBottomLeftPot = QPointF(initX, initBottomY);
    QPointF lineBottomRightPot = QPointF(width - initX, initBottomY);
    painter->drawLine(lineBottomLeftPot, lineBottomRightPot);

    double length = width;
    // 长线条短线条长度
    int longLineLen = std::min(15, (int)(height * 0.35));
    int middleLineLen = std::min(10, (int)(height * 0.3));
    int shortLineLen = std::min(6, (int)(height * 0.2));

    // 计算需要绘制的刻度数量及位置
    int mintextstep = 1;
    {
        QSize minvaluetextsize = fontmetrics.size(Qt::TextSingleLine, QString("%1").arg((double)tick_min_value, 0, 'f', precision));
        QSize maxvaluetextsize = fontmetrics.size(Qt::TextSingleLine, QString("%1").arg((double)tick_max_value, 0, 'f', precision));
        mintextstep = std::max(1, std::max(minvaluetextsize.width(), maxvaluetextsize.width()));
    }
    double ratio = (double)(tick_max_value - tick_min_value) / (length);
    double minvaluestep = (double)mintextstep * ratio;
    auto positions = CalculateScalePositions(tick_min_value, tick_max_value, minvaluestep, scale_factors);
    if (positions.empty())
    {
        return;
    }
    // 绘制
    const int step = 10;  // 固定10个小刻度
    int tick_step_middle = tick_step_long / 2;
    for (size_t j = 0; j < positions.size() - 1; ++j)
    {
        const auto& value = Mapvalue(positions[j], tick_min_value, tick_max_value, 0, length);
        const auto& nextvalue = Mapvalue(positions[j + 1], tick_min_value, tick_max_value, 0, length);
        const auto& interval = (nextvalue - value) / step;

        QString strValue = QString("%1").arg((double)positions[j] * tick_scale, 0, 'f', precision);
        QSize textsize = fontmetrics.size(Qt::TextSingleLine, strValue);
        double textWidth = textsize.width();
        double textHeight = textsize.height();

        QPointF textPot = QPointF(value - textWidth * 0.5, initBottomY - textHeight * 0.5);
        painter->drawText(textPot, strValue);

        for (int i = 0; i < step; ++i)
        {
            initX = value + i * interval;
            if (i % tick_step_long == 0)
            {
                QPointF topPot = QPointF(initX, initBottomY);
                QPointF bottomPot = QPointF(initX, initBottomY - longLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else if (i % tick_step_middle == 0)
            {
                QPointF topPot = QPointF(initX, initBottomY);
                QPointF bottomPot = QPointF(initX, initBottomY - middleLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else if (i % tick_step_short == 0)
            {
                QPointF topPot = QPointF(initX, initBottomY);
                QPointF bottomPot = QPointF(initX, initBottomY - shortLineLen);
                painter->drawLine(topPot, bottomPot);
            }
            else
            {
                continue;
            }
        }
    }
    painter->restore();
}