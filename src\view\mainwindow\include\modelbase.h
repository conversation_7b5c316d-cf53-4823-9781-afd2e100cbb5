/*****************************************************************//**
 * @file   modelbase.h
 * @brief  界面model基类
 * @details
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __MODELBASE_H__
#define __MODELBASE_H__
 // prebuild
#include "pch.h"
//#include <iostream>
//#include "viewparam.hpp"
#include "viewdefine.h"
#include "coreapplication.h"
#include "paramoperator.h"
//QT
#include <QObject>

namespace jrsaoi
{
    class ModelBase : public QObject
    {
        Q_OBJECT
    public:

        virtual ~ModelBase() = default;
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) = 0;
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) = 0;
    protected:
        explicit ModelBase(const std::string& name);
        jrsaoi::ParamOperator& project_param_instance;
    private:
        std::string type_name;


    };
    using ModelBasePtr = std::shared_ptr<ModelBase>;

}

#endif // !__MODELBASE_H__

