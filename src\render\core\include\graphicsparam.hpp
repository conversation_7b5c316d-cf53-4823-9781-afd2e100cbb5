﻿#pragma once 
/*****************************************************************
 * @file   graphicsparam.hpp
 * @brief   graphics 使用的结构体
 * @details  用于接口优化
 * <AUTHOR>
 * @date 2025.3.20
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.3.20          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
 //Third
#pragma warning(push, 3)
#include <opencv2/opencv.hpp>
#pragma warning(pop)
#include <QRegion>
//namespace renderparam
//{
struct GraphicsShape
{
    enum Type
    {
        None,
        Rect,
        Circle,
        Line,
        Text,
        Image,
        Region,
        Polygon,  /**< 额外添加 Polygon 以支持多边形 */
    };

    Type type;              /**< 图形类型 */
    cv::Scalar color;       /**< 颜色 (BGR 格式) */
    cv::Point2f center_position; /**< 位置（用于 Rect、Circle、Text） */
    cv::Size size;         /**< 尺寸（用于 Rect、Image） */
    cv::Mat image;         /**< 图像（用于 Image） */
    std::pair<cv::Point2f/**< 起始点 */, cv::Point2f/**<结束点*/> line_points; /**< 用于线段数据结构点（用于 Line） */
    float angle;            /**< 角度  顺时针角度*/
    int radius;            /**< 半径（用于 Circle） */
    int thickness;         /**< 线宽（用于 Line、Circle、Polygon 边框） */
    std::string text;      /**< 文字内容（用于 Text） */
    int font_face;         /**< 字体（用于 Text） */
    double font_scale;     /**< 字体大小（用于 Text） */

    QRegion region;        /**< 用于裁剪区域（仅 Region 类型使用） */
    std::vector<cv::Point2f> polygon_points; /**< 用于存储多边形顶点（用于 Polygon） */
    GraphicsShape()
        : type(Rect), color(cv::Scalar(0, 0, 0)), center_position(0.f, 0.f),
        size(0, 0), angle(0.0f), radius(0), thickness(1),
        font_face(cv::FONT_HERSHEY_SIMPLEX), font_scale(1.0)
    {
    }
    GraphicsShape(Type type_, cv::Scalar color_, cv::Point2f center_position_,
        cv::Size size_ = cv::Size(), float angle_ = 0.f, int radius_ = 0, int thickness_ = 1,
        std::string text_ = "", int font_face_ = cv::FONT_HERSHEY_SIMPLEX,
        double font_scale_ = 1.0, QRegion region_ = {},
        std::vector<cv::Point2f> polygon_points_ = {})
        : type(type_), color(color_), center_position(center_position_),
        size(size_), angle(angle_), radius(radius_), thickness(thickness_), text(std::move(text_)),
        font_face(font_face_), font_scale(font_scale_), region(region_),
        polygon_points(std::move(polygon_points_))
    {
    }
};

struct GraphicsImage
{
    uint8_t set_key;/**< 添加到那个集合*/
    std::unordered_map<int, cv::Mat> key_and_imgs;/**<图像key值与image 进行绑定*/

    cv::Point2f center_point; /**<图像显示的中心点*/

    int z;               /**<当前图像显示层级*/
    float angle;         /**<绘制角度*/
    bool is_draw_center; /**<是否中心绘制*/
    int current_show_img;/**<当前要显示的图像*/
    bool is_resize_canvas;/**<是否重新限制画布大小*/
    bool is_move_camera;/**<是否移动相机*/
    GraphicsImage()
        :set_key(0),
        key_and_imgs({}),
        center_point({}),
        z(0),
        angle(0.f),
        is_draw_center(false),
        current_show_img(0),
        is_resize_canvas(true),
        is_move_camera(true)
    {

    }
};

//}


