﻿#include "commandobject.h"
#include "graphicsabstract.hpp"
#include "graphicsmanager.h"
#include "log.h"

CommandCreateGraphics::CommandCreateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_)
    : CommandAbstract(), gm(gm_)
{
    for (auto& gh : ghs)
    {
        if (!gh)
            continue;
        //auto tg = gh->Clone();
        //ghs_clone.emplace_back(tg);
        ghs_clone.emplace_back(gh);
    }
}

CommandCreateGraphics::~CommandCreateGraphics()
{
    for (auto& gh : ghs_clone)
    {
        if (!gh)
            continue;

        gh.reset();
    }
}

void CommandCreateGraphics::excute()
{
    assert(gm);
    gm->UpdateGraphics(ghs_clone);
}

void CommandCreateGraphics::revoke()
{
    assert(gm);

    std::vector<GraphicsID> ids;
    for (auto& gh : ghs_clone)
    {
        if (!gh)
            continue;

        ids.emplace_back(gh->GetId());
    }
    gm->DeleteGraphics(ids);
}

CommandDeleteGraphics::CommandDeleteGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_)
    : CommandAbstract(), gm(gm_)
{
    for (auto& gh : ghs)
    {
        if (!gh)
            continue;
        //auto tg = gh->Clone();
        //ghs_clone.emplace_back(tg);
        ghs_clone.emplace_back(gh);
    }
}

CommandDeleteGraphics::~CommandDeleteGraphics()
{
    for (auto& gh : ghs_clone)
    {
        if (!gh)
            continue;

        gh.reset();
    }
}

void CommandDeleteGraphics::excute()
{
    assert(gm);

    std::vector<GraphicsID> ids;
    for (auto& gh : ghs_clone)
    {
        if (!gh)
            continue;

        ids.emplace_back(gh->GetId());
    }
    gm->DeleteGraphics(ids);
}

void CommandDeleteGraphics::revoke()
{
    assert(gm);
    gm->UpdateGraphics(ghs_clone);
}

CommandEditGraphics::CommandEditGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, GraphicsManager* gm_)
    : CommandAbstract(), gm(gm_)
{
    for (auto& gh : ghs)
    {
        if (!gh)
        {
            continue;
        }
        auto clone_gh = gh->Clone();
        m_input_graphics.emplace_back(clone_gh);
    }
}

CommandEditGraphics::~CommandEditGraphics()
{
    for (auto& gh : m_input_graphics)
    {
        if (!gh)
            continue;

        gh.reset();
    }

    for (auto& gh : m_revoke_graphics)
    {
        if (!gh)
            continue;

        gh.reset();
    }
}

void CommandEditGraphics::excute()
{
    assert(gm);

    if (m_revoke_graphics.empty())
    {
        return;
    }

    gm->UpdateGraphics(m_revoke_graphics);

    std::vector<std::shared_ptr<GraphicsAbstract>>().swap(m_revoke_graphics); // 用完后直接重置,避免二次调用时错误
}

void CommandEditGraphics::revoke()
{
    assert(gm);

    printInfo("");

    // 记录当前状态，以便恢复
    std::vector<GraphicsPtr> revoke_graphics;
    for (auto& gh : m_input_graphics)
    {
        if (!gh) continue;
        GraphicsPtr find_gh;
        gm->ReadGraphics(find_gh, gh->GetId());
        if (!find_gh)
        {
            return;
        }
        auto copy_tg = find_gh->Clone();
        revoke_graphics.emplace_back(copy_tg);
    }
    gm->UpdateGraphics(m_input_graphics);

    revoke_graphics.swap(m_revoke_graphics);
}
