﻿#ifndef _PAD_OPERATOR_H__
#define _PAD_OPERATOR_H__
/*****************************************************************
 * @file   padoperator.hpp
 * @brief   对pad的操作，镜像 复制  旋转等操作， TODO 添加也需要移动到这里面
 * @details
 * <AUTHOR>
 * @date 2025.1.2
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.1.2          <td>V2.0              <td>HaoJiangchun      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
#include <iostream>
#include <unordered_map>
#include <future>
//custom
#include "projectparam.hpp"

namespace jrsdata
{

    struct RenderEventParam;
}
namespace jrsparam
{
    class ProjectDataProcess;
}
namespace jrsaoi
{
    struct Render2dEventParam;

    using Function = std::function<int(const std::shared_ptr<jrsdata::RenderEventParam>& param_)>;
    class ParamOperator;
    /** <pad 操作类型 */
    enum class PadOperateType {
        TOP_BOTTON_MIRROR, /**< 根据元件中心上下镜像 */
        LEFT_RIGHT_MIRROR, /**< 根据元件中心左右镜像 */
        Rotate_90_DEGREE,  /**< 旋转九十度*/
    };
    class PadOperator
    {

    public:
        PadOperator();
        ~PadOperator();
        /**
         * @fun Update
         * @brief  更新pad接口
         * @param param_
         * @param current_event_param render2d 响应的事件信息
         * @return
         * <AUTHOR>
         * @date 2025.1.2
         */
        int Update(const std::shared_ptr<jrsdata::RenderEventParam>& param_, const jrsaoi::Render2dEventParam& current_event_param);

        //int DuplicatePad(const jrsdata::Render2dEventParam& event_params_, const std::string& pad_event_name_);
    private:

        int TopBottomReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_);

        int LeftRightReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_);

        int Rotate90DegreeReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_);

        int Rotate180DegreeReplication(const std::shared_ptr<jrsdata::RenderEventParam>& param_);

        jrsdata::ComponentUnit::Direction GetNextDirection(jrsdata::ComponentUnit::Direction dir);
        jrsdata::ComponentUnit::Direction GetMirrorDirection(jrsdata::ComponentUnit::Direction dir);

        void Init();
        void InitMember();
        bool IsValidParam(const std::shared_ptr<jrsdata::RenderEventParam>& param_);
        bool InvokeFun(const std::shared_ptr<jrsdata::RenderEventParam>& param_);
        int PadEventHandler(const std::shared_ptr<jrsdata::RenderEventParam>& param_);

        std::unordered_map<std::string, Function> _pad_func_map;                            /**< 对事件和pad操作的绑 定*/
        std::shared_ptr<jrsparam::ProjectDataProcess> _project_param_process_instance;      /**< 工程文件获取          */
        std::shared_ptr<jrsaoi::Render2dEventParam> _render_2d_event_param;                 /**< render2d 内的操作事件 */
    };
    using PadOperatorPtr = std::shared_ptr<PadOperator>;
}

#endif
