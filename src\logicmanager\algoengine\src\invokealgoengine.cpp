
//Custom
#include "invokealgoengine.h"
#include "algorithmenginemanager.h"

namespace jrslogic 
{
    struct InvokeDataImpl
    {
        jrsalgo::AlgorithmEngineManagerPtr algo_engine_manager_ptr;
    };
    InvokeAlgoEngine::InvokeAlgoEngine ()
        :invoke_data_impl(new InvokeDataImpl())
    {
        InitMember ();
    }
    InvokeAlgoEngine::~InvokeAlgoEngine ()
    {
        if (invoke_data_impl)
        {
            delete invoke_data_impl;
            invoke_data_impl = nullptr;
        }
    }
    void InvokeAlgoEngine::InitMember ()
    {
        invoke_data_impl->algo_engine_manager_ptr = std::make_shared<jrsalgo::AlgorithmEngineManager> ();
        
    }
    const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& InvokeAlgoEngine::GetAlgoEngineManager ()
    {
        
        return invoke_data_impl->algo_engine_manager_ptr;
    }
}
