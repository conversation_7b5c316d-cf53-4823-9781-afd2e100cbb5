/********************************************************************
 * @brief  防抖类，限制函数在指定时间内只能调用一次.
 *
 * @file   debouncer.hpp
 *
 * @date   2024.06.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef DEBOUNCER_H
#define DEBOUNCER_H
#include <chrono>
#include <functional> //function
#include <mutex>
#include <thread>

class Debouncer
{
public:
    /**
     * @brief 构造函数
     * @param debounceTime 防抖时间，单位为毫秒
     * @param immediate 是否立即执行
     */
    Debouncer(int debounceTime, bool immediate)
        : debounceTime(debounceTime), immediate(immediate),
        lastCallTime(std::chrono::steady_clock::now() - std::chrono::milliseconds(debounceTime)), active(false)
    {
    }

    /**
     * @brief 调用需要防抖的函数
     * @param func 需要防抖的函数
     */
    void call(std::function<void()> func)
    {
        std::lock_guard<std::mutex> lock(mutex);
        auto now = std::chrono::steady_clock::now();
        if (immediate)
        {
            if (!active || (now - lastCallTime) >= std::chrono::milliseconds(debounceTime))
            {
                func();
                lastCallTime = now;
                active = true;
            }
        }
        else
        {
            lastFunc = func;
            if (!active)
            {
                active = true;
                std::thread([this, now]()
                    {
                        std::this_thread::sleep_for(std::chrono::milliseconds(debounceTime));
                        std::lock_guard<std::mutex> lock(this->mutex);
                        if ((std::chrono::steady_clock::now() - this->lastCallTime) >=
                            std::chrono::milliseconds(this->debounceTime))
                        {
                            if (this->lastFunc)
                            {
                                this->lastFunc();
                                this->lastCallTime = std::chrono::steady_clock::now();
                            }
                        }
                        this->active = false; })
                    .detach();
            }
        }
    }

private:
    int debounceTime;                                   ///< 防抖时间，单位为毫秒
    bool immediate;                                     ///< 是否立即执行
    std::chrono::steady_clock::time_point lastCallTime; ///< 上次调用时间
    std::function<void()> lastFunc;                     ///< 上次调用的函数
    bool active;                                        ///< 是否处于活跃状态
    std::mutex mutex;                                   ///< 互斥锁
    std::condition_variable cv;                         ///< 条件变量
};
#endif //! DEBOUNCER_H