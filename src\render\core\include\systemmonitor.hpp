/*********************************************************************
 * @brief  获取系统信息.
 *
 * @file   systemmonitor.hpp
 *
 * @date   2024.06.13
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef SYSTEMMONITOR_HPP
#define SYSTEMMONITOR_HPP
#include <pdh.h> // 包含 PDH
#undef min
#undef max

 /**
  * @func  GetCpuUsage
  * @brief 获取CPU占用率
  *
  * @note TODO 多次采样取平均值
  * @return double CPU占用率
  */
inline double GetCpuUsage()
{
    static PDH_HQUERY cpuQuery;
    static PDH_HCOUNTER cpuTotal;
    static bool init = false;

    if (!init)
    {
        PdhOpenQuery(NULL, NULL, &cpuQuery);
        PdhAddCounter(cpuQuery, L"\\Processor(_Total)\\% Processor Time", NULL, &cpuTotal);
        PdhCollectQueryData(cpuQuery);
        init = true;
    }

    PDH_FMT_COUNTERVALUE counterVal;

    PdhCollectQueryData(cpuQuery);
    PdhGetFormattedCounterValue(cpuTotal, PDH_FMT_DOUBLE, NULL, &counterVal);
    return counterVal.doubleValue;
}

/**
 * @func  GetMemoryUsage
 * @brief 获取内存占用率
 *
 * @return double 内存占用率
 */
inline double GetMemoryUsage()
{
    MEMORYSTATUSEX statex;
    statex.dwLength = sizeof(statex);
    GlobalMemoryStatusEx(&statex);

    double memoryUsage = (statex.ullTotalPhys - statex.ullAvailPhys) * 100.0 / statex.ullTotalPhys;
    return memoryUsage;
}

/**
 * @func  GetKeyDown
 * @brief 获取指定按键是否按下
 * @param vk_code 按键代码 来自winuser.h
 * @return int 1表示按下，0表示未按下
 */
inline int GetKeyDown(int vk_code)
{
    return ((GetAsyncKeyState(vk_code) & 0x8000) ? 1 : 0);
}

/**
 * @func  GetKeyUp
 * @brief 获取指定按键是否抬起
 * @param vk_code 按键代码 来自winuser.h
 * @return int 1表示抬起，0表示未抬起
 */
inline int GetKeyUp(int vk_code)
{
    return ((GetAsyncKeyState(vk_code) & 0x8000) ? 1 : 0);
}

/**
 * @func  ImportCursor
 * @brief 导入ani格式的鼠标指针
 * @note  qt转换的部分放进qtextras模块中了
 */
inline bool ImportCursor(HCURSOR& h_cursor, const char* filename)
{
    h_cursor = LoadCursorFromFileA(filename);
    return h_cursor != NULL;
}

inline bool ReadCursorHot(int& x, int& y, const char* filename)
{
    HCURSOR h_cursor;
    ImportCursor(h_cursor, filename);
    if (h_cursor == NULL)
    {
        return false;
    }
    ICONINFO iconInfo;
    if (!GetIconInfo(h_cursor, &iconInfo))
    {
        return false;
    }
    x = iconInfo.xHotspot;
    y = iconInfo.yHotspot;
    return true;
}

#endif // !SYSTEMMONITOR_HPP