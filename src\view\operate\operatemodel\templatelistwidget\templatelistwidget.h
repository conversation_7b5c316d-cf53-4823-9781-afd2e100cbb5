#ifndef TEMPLATELISTWIDGET_H
#define TEMPLATELISTWIDGET_H

#pragma warning(disable: 4127)

#include <QWidget>
#include <QListWidget>
#include <opencv2/opencv.hpp>



struct TemplateItemValue
{
    QPixmap img_show;
    cv::Mat img_src;
    int     id;
    int     light_type;
    QString color_param;
};


class TemplateListWidget : public QListWidget
{
    Q_OBJECT
public:
    explicit TemplateListWidget(QWidget *parent = nullptr);

private:
    //ImageTableDelegate* item_delegate = nullptr;

public:
    int UpdateAllItems(const QList<TemplateItemValue>& item_vals);
    int AddItem(const TemplateItemValue& item_val);
    int GetItemTemplateData(int index, TemplateItemValue& item_val);
    int UpdateItemTemplateData(int index, const TemplateItemValue& item_val);
    std::vector<int> DeleteSelectItem();

signals:
    void SigTemplateSelectionChanged(const TemplateItemValue& item_val);
    void SigTemplateDoubleClicked(const TemplateItemValue& item_val);

public slots:
    void SoltItemSelectionChanged();
    void SoltItemDoubleClicked(QListWidgetItem* item);
};

#endif // TEMPLATELISTWIDGET_H
