/*****************************************************************//**
 * @file   detectview.h
 * @brief  检测界面
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef DETECTVIEW_H
#define DETECTVIEW_H
 //QT
#pragma warning(push,3)
#include <QWidget>
#include <QButtonGroup>
#include "ui_detectview.h"
#pragma warning(pop)
// CUSTOM
//#include "trackstatus.h"
#include "detectprocessed.h"
#include "onedetectview.h"

namespace Ui {
    class DetectView;
}

class DetectView : public QWidget
{
    Q_OBJECT
public:
    explicit DetectView(QWidget* parent = nullptr);
    ~DetectView();

    void UpdateView(const jrsdata::OperateViewParamPtr ptr);

private:
    /**
     * @fun InitConnect
     * @brief 初始信号链接
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitConnect();
signals:
    /**
     * @fun SigUpdateView
     * @brief 运控消息推送信号
     * #param ptr
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigUpdateView(const jrsdata::OperateViewParamPtr ptr);
    /**
     * @fun SigDetectViewChangeTrigger
     * @brief 清除检测历史记录
     * #param ptr
     * @date 2025.2.18
     * <AUTHOR>
     */
    void SigDetectViewChangeTrigger(const jrsdata::ViewParamBasePtr& param_);
private:
    Ui::DetectView* ui;                                                                         /// 界面
    std::map<jrsdata::TRACK_NUMBER/* track_id */, OneDetectView*> map_one_detect_view;          /// track_id 从1开始，1表示轨道1
    std::vector<jrsdata::TRACK_NUMBER> vec_track_numbers;                                       /// 轨道ID的集合
};
#endif // DETECTVIEW_H
