﻿
// Custom
#include "projectmanager.h"
#include "coreapplication.h"
#include "viewdefine.h"
#include "coordinatetransform.hpp"

namespace jrsproject
{
    struct ProjectDataImpl
    {
        EventInvokeMap event_invoke_fun;                     /**< 事件响应函数map  */
        ViewEventInvokeMap view_event_invoke_fun;            /**< 事件响应函数map  */
        jrsdata::InvokeProjectFun callback_project;          /**< 外部响应回调函数 */
        jrsdata::InvokeViewParamBaseFun callback_view;       /**< 外部响应回调函数 */
        jrsdata::ProjectEventParamPtr project_param;  /**< 工程事件参数 */
        jrscore::CoordinateTransformPtr coor_transform_ptr;  /**< 坐标转换工具 */
    };
    ProjectManager::ProjectManager()
        : project_data_impl(new ProjectDataImpl())
    {
        Init();
    }
    ProjectManager::~ProjectManager()
    {
        if (project_data_impl)
        {
            delete project_data_impl;
            project_data_impl = nullptr;
        }
    }


    void ProjectManager::EventHandler(const jrsdata::ViewParamBasePtr& param_)
    {

        if (!IsValidParam(param_))
        {
            return;
        }
        if (auto param = std::dynamic_pointer_cast<jrsdata::CADEventParam>(param_); param != nullptr)
        {
            project_data_impl->project_param->event_name = param->event_name;
            project_data_impl->project_param->topic_name = param->topic_name;
            project_data_impl->project_param->sub_name = param->sub_name;
            project_data_impl->project_param->module_name = param->module_name;
            project_data_impl->project_param->project_param = param->project_param;
        }
        if (!InvokeFun(param_))
        {
            return;
        }
    }

    void ProjectManager::SetCallBack(jrsdata::InvokeProjectFun callback_)
    {

        if (callback_)
        {
            project_data_impl->callback_project = callback_;
        }
    }

    void ProjectManager::SetTransformCoordinatePtr(const std::shared_ptr<jrscore::CoordinateTransform>& trans_ptr_)
    {
        project_data_impl->coor_transform_ptr = trans_ptr_;
    }

    void ProjectManager::Init()
    {
        InitMember();
        InitInvokeFun();
    }

    void ProjectManager::InitMember()
    {
        //project_data_impl = std::make_shared<jrsdata::ProjectParam>();
        // project_data_impl = std::make_shared<jrsdata::ProjectEventParam>();
        project_data_impl->project_param = std::make_shared<jrsdata::ProjectEventParam>();
        project_data_impl->project_param->project_param = std::make_shared<jrsdata::ProjectParam>();
    }

    void ProjectManager::InitInvokeFun()
    {
        project_data_impl->view_event_invoke_fun =
        {
            {jrsaoi::IMPORT_CAD_EVENT_NAME, std::bind(&ProjectManager::ParseCadInfo, this, std::placeholders::_1)}
        };

    }

    bool ProjectManager::IsValidParam(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            Log_ERROR("param_为空");
            return false;
        }

        if (param_->event_name.empty())
        {
            Log_ERROR("event_name is empty");
            return false;
        }
        return true;
    }


    bool ProjectManager::InvokeFun(const jrsdata::ViewParamBasePtr& param_)
    {
        auto it = project_data_impl->view_event_invoke_fun.find(param_->event_name);
        if (it == project_data_impl->view_event_invoke_fun.end())
        {
            Log_ERROR("ProjectManager::EventHandler() event_name:", param_->event_name, " not found");
            return false;
        }
        if (!it->second)
        {
            Log_ERROR("ProjectManager::EventHandler() event_name:", param_->event_name, " has invalid function pointer");
            return false;
        }
        it->second(param_); // 执行函数
        return true;
    }

    int ProjectManager::ParseCadInfo(const jrsdata::ViewParamBasePtr& param_)
    {
        auto param = std::dynamic_pointer_cast<jrsdata::CADEventParam>(param_);
        if (!param)
        {
            return 1;
        }

        project_data_impl->project_param->project_param->board_info.sub_board.clear();//添加清除，下一次导入之前清除上一次导入的结果

        if (!project_data_impl->coor_transform_ptr)
        {
            return 1;
        }

        Log_INFO("cad 信息映射到工程文件中的元件坐标信息成功！", param->event_name);

        std::map<int, jrsdata::SubBoard> map_subboard;
        std::unordered_map<std::string, jrsdata::PNDetectInfo> map_pn;
        for (auto& value : param->cad_info)
        {
            std::string subboard_name;
            if (map_subboard.count(value.m_cad_sub_id) == 0)
            {
                jrsdata::SubBoard temp_sub_board_info;
                temp_sub_board_info.subboard_name = /*"subboard_"*/
                    project_data_impl->project_param->project_param->project_name + "_" + std::to_string(value.m_cad_sub_id);
                temp_sub_board_info.id = value.m_cad_sub_id;
                temp_sub_board_info.row = 0;//临时
                temp_sub_board_info.col = value.m_cad_sub_id;//临时
                map_subboard[value.m_cad_sub_id] = temp_sub_board_info;
            }
            subboard_name = map_subboard[value.m_cad_sub_id].subboard_name;
            //! 将像物理坐标转换成像素坐标
            int pixel_x = 0, pixel_y = 0;
            project_data_impl->coor_transform_ptr->PhysicalToPixel(value.m_cad_x, value.m_cad_y, pixel_x, pixel_y);
            jrsdata::Component temp_component_info;
            temp_component_info.component_name = value.m_cad_name + "_" + std::to_string(value.m_cad_sub_id);/// 2025/3/25 元件名添加子板ID
            temp_component_info.x = (float)pixel_x;
            temp_component_info.y = (float)pixel_y;
            temp_component_info.angle = value.m_cad_angle;
            temp_component_info.component_part_number = value.m_cad_part_no;
            // temp_component_info.component_type = value.m_cad_type;
            temp_component_info.component_type = jrsdata::Component::Type::CAD;
            temp_component_info.subboard_name = subboard_name;
            //! TODO:目前默认100宽高，后期要跟元件库绑定
            //temp_component_info.width = 100;
            //temp_component_info.height = 100;
            if (map_pn.find(temp_component_info.component_part_number) == map_pn.end())
            {
                jrsdata::PNDetectInfo pn;
                pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, 100, 100, 1, "body", "body",
                    jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
                pn.detect_models.insert({ "body",jrsdata::DetectModel() }); 
                pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, 100, 100, 1, "body", "location",
                    jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
                pn.detect_models.insert({ "body",jrsdata::DetectModel() });
                pn.detect_models.insert({ "location",jrsdata::DetectModel() });
                pn.part_name = value.m_cad_part_no;
                map_pn.emplace(temp_component_info.component_part_number, pn);
            }
            map_subboard[value.m_cad_sub_id].component_info.emplace_back(temp_component_info);
        }
        project_data_impl->project_param->project_param->board_info.part_nums_and_detect_regions = map_pn;


        for (auto& subboard_pair : map_subboard)
        {
            auto& subboard = subboard_pair.second;
            //subboard.UpdatePosition();
            project_data_impl->project_param->project_param->board_info.sub_board.emplace_back(subboard);
        }
        if (project_data_impl->callback_project)
        {
            project_data_impl->callback_project(project_data_impl->project_param);
        }


        return jrscore::AOI_OK;
    }


}
