#ifndef __DBTABLEBASE_HPP__
#define __DBTABLEBASE_HPP__


#include <iostream>
#include <map>
/** 数据库 */
#include "coreapplication.h"
#include "dbparam.h"
namespace jrsdatabase {
    class DBTableBase
    {
    public:
        virtual ~DBTableBase() = default;
        virtual int Create(const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Drop(const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
        virtual int Show(jrstable::TableParamBasePtr& db_, const std::shared_ptr<DB_Mysql>& conn_ptr_) = 0;
    protected:
        bool IsConnPtrEmpty(const std::shared_ptr<DB_Mysql>& conn_ptr_);
        explicit DBTableBase(const std::string& table_name_/*表的名称*/);
        std::vector<std::string> _index_container;
        std::vector<std::string> _trigger_container;
        std::string _table_name;
    };
};

#endif //!__DBTABLEBASE_HPP__