﻿/*****************************************************************//**
 * @file   operateview.h
 * @brief  主操作界面
 * @details  目前主要包括流程编辑，算法编辑，检测编辑，参数设置，流程调试等操作界面
 * <AUTHOR>
 * @date 2024.1.29
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.29         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef OPERATEVIEW_H
#define OPERATEVIEW_H
#pragma warning(push, 3)
#include "ui_operateview.h"
#pragma warning(pop)
 // CUSTOM
#include "pch.h"
//#include "motiondebugview.h"
#include "parametersettings.h"
#include "detectview.h"
#include "projectview.h"
#include "caddefine.hpp"
#include "editdetectmodelview.h"
#include "addcomponentview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class OperateView; };
QT_END_NAMESPACE
using OperateUpdateFunc = std::function<int(const jrsdata::OperateViewParamPtr& param_)>;

namespace jrsaoi
{
    class OperateView : public ViewBase
    {
        Q_OBJECT
    public:
        OperateView(const std::string& name, QWidget* parent = nullptr);
        ~OperateView();
        /**
         * @fun Init
         * @brief 初始化
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Init() override;
        /**
         * @fun UpdateView
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        Q_INVOKABLE virtual int UpdateView(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save
         * @brief
         * @param param_
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_) override;

        QWidget* GetCustomWidget();

    public slots:
        /**
        * @fun SlotDebugView
        * @brief  运控调试界面响应槽函数
        * @param  操作界面槽函数
        * <AUTHOR>
        * @date 2024.8.15
        */
        void SlotDebugView(const jrsdata::OperateViewParamPtr& operateparam);
        /**
        * @fun SlotSaveSetting
        * @brief  保存页面参数槽函数
        * @param
        * <AUTHOR>
        * @date 2024.8.25
        */
        void SlotSaveSetting(const jrsdata::OperateViewParamPtr& operateparam);
  
        /**
         * @fun SlotSendMotionParamToWorkFlow 
         * @brief 初始化时发送运控参数到自动运行模块
         * @param operateparam  [IN] 运控参数
         * <AUTHOR>
         * @date 2025.6.9
         */
        void SlotSendMotionParamToWorkFlow(const jrsdata::OperateViewParamPtr& operateparam);

        void SlotMachineSetting(const jrsdata::MachineParam& machine_param_);
        /**
        * @fun    SlotAlgoView
        * @brief  响应算法事件分发
        * @param
        * <AUTHOR>
        * @date   2024.8.25
        */
        void SlotAlgoView(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun  GetCurSelectAlgoName
         * @brief 获取当前选择的算法名.
         * @param part_name 算法所属料号
         * @param model_name 算法所属模型
         * @param det_win_name 算法所属检测框
         * @param algo_name 算法名
         * @data 2024.10.22
         * <AUTHOR>
         */
         // void GetCurSelectAlgoName(std::string& part_name, std::string& model_name, std::string& det_win_name, std::string& algo_name) const;

        int SetEditAlgorithmViewDefaultParam(const EditAlgorithmViewDefaultParam& _param);


        int  SetAlgorithmAndDefectList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_name_list, const std::vector<std::string>& _defect_list);


        void SlotUpdateProductWidth();

        void SlotSaveRepairData(const jrsdata::RepairData& repair_data);
    signals:
        /**
         * 转发用信号,用于将内部的信号转发给外部
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         * 注意注意注意注意注意注意注意注意注意注意
         *
         * 因为使用的是基类指针,可以接收所有的信号,不要再定义别的信号了
         * 转发给外面的一个信号就够了!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
         * 接收外部信号发给内部的可以有很多
         */
        void SigUpdateOperator(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SigUpdateView
         * @brief 运控消息推送信号
         * @param param_
         * <AUTHOR>
         * @date 2024.8.12
         */
        void SigUpdateView(const jrsdata::OperateViewParamPtr param_);
        /**
         * @fun SigUpdateBoardWidth
         * @brief 跟新板宽信号
         * @param param_
         * <AUTHOR>
         * @date 2024.8.18
         */
        void SigUpdateBoardWidth(const jrsdata::OperateViewParamPtr param_);

        /**
         * @brief 更新ProjectView界面信号
         */
        void SignalUpdateProjectView(const jrsdata::ViewParamBasePtr param_);
        /**
        * @fun SigViewEvent
        * @brief
        * @param param_
        * @date 2024.9.24
        * <AUTHOR>
        */
        void SigViewEvent(const jrsdata::RenderEventParamPtr param_);

        /**
         * @brief 更新EditDetectModelView界面信号
         */
        void SignalUpdateEditDetectModelView(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun SigAlgoEvent
         * @brief
         * @param param_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SigAlgoEvent(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun  SignalUpdateAlgoView
         * @brief 更新算法界面.
         * @param algo_view 算法界面指针
         * @data 2024.10.30
         * <AUTHOR>
         */
        void SignalUpdateAlgoView(QWidget* algo_view);

        /**
         * @fun  SignalUpdateTemplateListView
         * @brief  更新模板列表.
         * @param template_list 模板数据
         * @data 2024.10.30
         * <AUTHOR>
         */
        void SignalUpdateTemplateListView(std::vector<jrsdata::Template>& template_list);

        void SigSaveProject();

        void SigGetBoardWith();

        void SigUpdateNewFileGetProductWidth(double track_width1, double track_width2);


    private:
        /**
         * @fun InitMember
         * @brief
         * <AUTHOR>
         * @date 2024.8.16
         */
        void InitMember();
        /**
         * @fun InitView
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数链接
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
        /**
         * @fun UpdateDataView
         * @brief 更新数据界面
         * @param param_
         * <AUTHOR>
         * @date 2024.11.4
         */
        int UpdateDataView(const jrsdata::OperateViewParamPtr& param_);
        int UpdateFlowView(const jrsdata::OperateViewParamPtr& param_);
        //int UpdateMotionView(const jrsdata::OperateViewParamPtr& param_);
        int UpdateMotionSettingParam(const jrsdata::OperateViewParamPtr& param_);

        int UpdateDetectStatisticsResultParam(const jrsdata::OperateViewParamPtr& param_);
        /**
         * @fun UpdateAddComponentParam
         * @brief 更新增加元件
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.4.2
         */
        int UpdateAddComponentParam(const jrsdata::OperateViewParamPtr& param_);

    private:
        Ui::OperateView* ui;
        // 运控调试界面
        //MotiondebugView* motionview = nullptr;
        // 参数配置页面
        ParameterSettings* param_setting_view = nullptr;
        // 检测页面
        DetectView* detect_view = nullptr;
        ProjectView* project_view = nullptr;  //TODO删除了主页面上的流程页面
        EditDetectModelView* edit_detect_model_view = nullptr;
        AddComponentView* add_component_view = nullptr;
        std::unordered_map<std::string, OperateUpdateFunc> _operate_update_map;

        jrsdata::ProjectEventParamPtr _project_param;
        jrsdata::OperateViewParamPtr operate_view_param_ptr;
        jrsdata::SettingViewParamPtr _setting_view_param;
    };
}
#endif // !__OPERATEVIEW_H