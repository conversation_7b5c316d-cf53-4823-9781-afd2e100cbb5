#include "imageprocessalgo.h"
#include "imagepreprocessalgo.h"
#include "imagebinaryalgo.h"

namespace colorwheel
{
	//// 定义全局变量
	//bool res_image_is_binary = false;


	// 实现函数
	cv::Mat GetPreProcessResult(const cv::Mat& input_image, ColorParams& params, cv::Mat& output_img)
	{
		ImageProcessor image_processor;
		BinaryAlgo binary_processor;
		ColorParams current_params = params;
		cv::Mat enhance_image = cv::Mat();
		if (input_image.empty()) return enhance_image;
		//ImageLabel image_group;
		//image_group.mat = input_image;
		image_processor.SetProcessImage(input_image);
		image_processor.SetProcessParams(current_params.pre_process_params);
		image_processor.GetProcessImage(output_img);
		enhance_image = output_img.clone();
		binary_processor.GetBinaryImage(output_img, current_params.binary_params, output_img);
		auto res_image_is_binary = binary_processor.GetImageIsBinary();
		auto enhance_res = image_processor.GetEnhanceImageStatus();
		if (enhance_res)
			params.deal_type = ColorWheelDealType::ENHANCE;
        if (res_image_is_binary)
			params.deal_type = ColorWheelDealType::BINARY;
		else
			output_img = cv::Mat();
		return enhance_image;
	}

	cv::Mat ApplyColorParam(ColorParams& color_param, const cv::Mat& image)
	{
		if (image.empty()) return cv::Mat();
		cv::Mat result;
		GetPreProcessResult(image, color_param, result);
		return result;
	}

	cv::Mat MaskImageByBinary(const cv::Mat &source_image, const cv::Mat &binary_mask)
	{
		if (source_image.empty() || binary_mask.empty())
		{
			return cv::Mat();
		}
		if (source_image.rows != binary_mask.rows || source_image.cols != binary_mask.cols)
		{
			return cv::Mat();
		}
		if (binary_mask.channels() != 1)
		{
			return cv::Mat();
		}
		cv::Mat masked_result = cv::Mat::zeros(source_image.size(), source_image.type());
		if (source_image.channels() == 1) 
		{
			for (int row = 0; row < source_image.rows; ++row) 
			{
				const uchar* mask_ptr = binary_mask.ptr<uchar>(row);
				const uchar* src_ptr = source_image.ptr<uchar>(row);
				uchar* dst_ptr = masked_result.ptr<uchar>(row);		
				for (int col = 0; col < source_image.cols; ++col) 
				{
					if (mask_ptr[col] == 255) 
					{
						dst_ptr[col] = 255;
					}
					else
					{
						dst_ptr[col] = src_ptr[col];
					}
				}
			}
		} 
		else 
		{  
			for (int row = 0; row < source_image.rows; ++row)
			{
				const uchar* mask_ptr = binary_mask.ptr<uchar>(row);
				const cv::Vec3b* src_ptr = source_image.ptr<cv::Vec3b>(row);
				cv::Vec3b* dst_ptr = masked_result.ptr<cv::Vec3b>(row);		
				for (int col = 0; col < source_image.cols; ++col) 
				{
					if (mask_ptr[col] == 255) 
					{
						//dst_ptr[col] = src_ptr[col];
						dst_ptr[col] = (src_ptr[col] / 2.0 + cv::Vec3b(0, 255, 255) / 2.0) ;
					}
					else
					{
						dst_ptr[col] = src_ptr[col];
					}
				}
			}
		}
		return masked_result;
	}
	int GetProprecessResult(const ColorParams& color_param,
		const cv::Mat& input_image, cv::Mat& output_img)
	{
		int process_status = -1;
		ImageProcessor image_processor;
		BinaryAlgo binary_processor;
		ColorParams cur_colr_param = color_param;
		if(color_param.deal_type == ColorWheelDealType::NONE)
		{
			output_img = input_image;
			process_status = static_cast<int>(ColorWheelDealType::NONE);
		}
		if (color_param.deal_type == ColorWheelDealType::ENHANCE)
		{
			image_processor.SetProcessImage(input_image);
			image_processor.SetProcessParams(color_param.pre_process_params);
			image_processor.GetProcessImage(output_img);
			process_status = static_cast<int>(ColorWheelDealType::ENHANCE);
		}
		if (color_param.deal_type == ColorWheelDealType::BINARY)
		{
			image_processor.SetProcessImage(input_image);
			image_processor.SetProcessParams(color_param.pre_process_params);
			image_processor.GetProcessImage(output_img);
			binary_processor.GetBinaryImage(output_img, cur_colr_param.binary_params, output_img);
			process_status = static_cast<int>(ColorWheelDealType::BINARY);
		}	
		return process_status;
	}
}