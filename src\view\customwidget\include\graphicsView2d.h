/*****************************************************************
 * @file   graphicsView2d.h
 * @brief  
 * @details
 * <AUTHOR>
 * @date 2025.5.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.5.5          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/
//STD
//Custom
//Third
#pragma once

#pragma warning(disable: 4127)

#include <vector>
#include <qgraphicsview.h>
#include <qpushbutton.h>
#include <opencv2/opencv.hpp>


class GraphicsView2D : public QGraphicsView
{
	Q_OBJECT
public:
	GraphicsView2D();
	void FitWin();
	void SetImageSize(const QSize size);
	void SetClickGrab(const bool& status);
	void SetDrawFlag(const bool& status);

signals:
	void SendSecenPosToGetGrayVal(const QPointF& pos);
	void SendSecenPosMoveCamera(const QPointF& pos);
	void SigShowPreImage();
	void SigShowNextImage();
	void SigSetCurDrawRect(const QRectF& rect);
	void SigEndDraw();
	void SigLeftTopPosChanged(const QPointF& pos);

private:
	//QPushButton* m_btn_pre_image = nullptr;
	//QPushButton* m_btn_next_image = nullptr;

	QSize  m_img_size;
	QPoint m_prev_pan;
	bool   m_is_draw = false;
	bool   m_is_pan = false;
	bool   m_is_click_grab = false;

	QPointF m_down_start_pos;

	void Translate(const QPoint& panTo);
	void TogglePan(bool pan, const QPoint& startPos = QPoint());
	void Zoom(QPoint factor);
	void CreateViewButton();

	void OnShowPreImage(bool checked = false);
	void OnShowNextImage(bool checked = false);

protected:
	void mouseMoveEvent(QMouseEvent* event) override;
	void mouseDoubleClickEvent(QMouseEvent* event) override;
	void mousePressEvent(QMouseEvent* event) override;
	void mouseReleaseEvent(QMouseEvent* event) override;
	void wheelEvent(QWheelEvent* event) override;
	void keyPressEvent(QKeyEvent* event) override;
	void keyReleaseEvent(QKeyEvent* event) override;
	void resizeEvent(QResizeEvent* event) override;
};
