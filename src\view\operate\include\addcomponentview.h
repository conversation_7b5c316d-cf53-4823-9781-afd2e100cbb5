﻿#pragma once
/*****************************************************************
 * @file   addcomponentview.h
 * @brief
 * @details
 * <AUTHOR>
 * @date 2025.4.2
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.4.2          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
#include "viewparam.hpp"
 //Third
#include <QWidget>


namespace jrsaoi
{
    struct ImplData;
    class AddComponentView :public QWidget
    {
        Q_OBJECT
    public:
        explicit AddComponentView(QWidget* parent = nullptr);
        ~AddComponentView();
        /**
         * @fun UpdateView
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.4.2
         */
        int UpdateView(const jrsdata::OperateViewParamPtr& param_);
    signals:
        void SigUpdateView(const jrsdata::ViewParamBasePtr& param_);
    private:
        void Init();
        void InitView();
        void InitConnect();
        void ToggleLayoutVisibility(QLayout* layout, bool is_visible = true);
        /**
         * @fun AddComponentAction
         * @brief 增加元件操作
         * <AUTHOR>
         * @date 2025.4.7
         */
        void AddComponentAction();
    private slots:
        void OnButtonToggled(QAbstractButton* button, bool checked);
        void OnPushButton();
    private:
        std::unique_ptr<ImplData> _param;
    };
}
