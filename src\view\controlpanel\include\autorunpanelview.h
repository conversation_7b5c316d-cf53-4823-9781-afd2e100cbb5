/*****************************************************************//**
 * @file   controlconsoleview.h
 * @brief  主界面上控制面板view类
 * @details   主界面上软件运行停止/运行状态显示/打开硬件/信息/切换用户面板的view类
 * <AUTHOR>
 * @date 2024.1.17
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.17         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __AUTORUNPANELEVIEW_H__
#define __AUTORUNPANELEVIEW_H__
// prebuild
#include "pch.h"
//custom
//#include "viewparam.hpp"
//qt
//#include <QWidget>
#include <QObject>
QT_BEGIN_NAMESPACE
namespace Ui { class autorunpanelview; };
class QPushButton;
QT_END_NAMESPACE

namespace jrsaoi
{

    struct ImplData;
    class AutoRunPanelView : public QWidget
    {
        Q_OBJECT

    public:
        AutoRunPanelView(QWidget* parent = nullptr);
        ~AutoRunPanelView();
        int UpdateView(const jrsdata::ViewParamBasePtr& param_);

    signals:
        void SigUpdateAutoRunPanelView(const jrsdata::ViewParamBasePtr& param_);
    private slots:
        void SlotFlowSwitch();
    private:
        //Member
        ImplData* _impl_data;
        //Fun
        int Init();
        void InitView();
        void InitMember();
        void InitConnect();
    };
}

#endif // !__AUTORUNPANELEVIEW_H__
