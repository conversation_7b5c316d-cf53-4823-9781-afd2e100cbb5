/*********************************************************************
 * @brief  管理自定义鼠标指针枚举.
 *
 * @file   customcursortype.hpp
 * @date   2024.11.13
 * <AUTHOR>
**********************************************************************/
#ifndef CUSTOMCURSORTYPE_HPP
#define CUSTOMCURSORTYPE_HPP

/**
 * @enum CustomCursorType
 * @brief 枚举类型，用于指定鼠标指针样式。
 */
enum class CustomCursorType : __int8
{
    Reset = 0,               /**< 重置指针 */
    Default,                 /**< 默认指针 */
    Normal,                  /**< 正常指针 */
    Move,                    /**< 移动指针 */
    MoveAll,                 /**< 移动全部指针 */
    Pickup,                  /**< 提取指针 */
    SelectAlternate,         /**< 备用选择指针 */
    SelectNormal,            /**< 正常选择指针 */
    SelectPosition,          /**< 位置选择指针 */
    SelectPrecision,         /**< 精准选择指针 */
    SelectText,              /**< 文本选择指针 */
    SelectLink,              /**< 链接选择指针 */
    SelectHelp,              /**< 帮助选择指针 */
    SelectMove,              /**< 移动选择指针 */
    ResizeVertical,          /**< 垂直缩放指针 */
    ResizeHorizontal,        /**< 水平缩放指针 */
    ResizeDiagonal1,         /**< 对角线缩放指针1 */
    ResizeDiagonal2,         /**< 对角线缩放指针2 */
    HandWriting,             /**< 手写指针 */
    HandOpen,                /**< 手张开指针 */
    HandClose,               /**< 手合拢指针 */
    Unavailable,             /**< 不可用指针 */
    Rotate,                  /**< 旋转指针 */
    Busy,                    /**< 忙碌指针 */
    WorkingInBackground      /**< 后台工作指针 */
};

#endif //!CUSTOMCURSORTYPE_HPP