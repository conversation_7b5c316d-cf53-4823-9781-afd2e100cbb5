﻿/*****************************************************************//**
 * @file   sysparacontroller.h
 * @brief  系统显示视图controller类
 * @details
 * <AUTHOR>
 * @date 2024.4.16
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.4.16         <td>V1.0              <td>zhaokunlong      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRS_SETTINGCONTROLLER_H__
#define __JRS_SETTINGCONTROLLER_H__
 //Custom
#include "controllerbase.h"

namespace jrsaoi
{
    class SettingView;
    class SettingModel;
    class SettingController :public ControllerBase
    {
        Q_OBJECT
    public:
        SettingController(const std::string& name_);
        ~SettingController();
        /**
         * @fun Update 
         * @brief
         * @param param_
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        int Update(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save 
         * @brief
         * @param param_
         * @return 
         * @date 2024.9.24
         * <AUTHOR>
         */
        int Save(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun SetView 
         * @brief
         * @param view_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SetView(ViewBase* view_param) override;
        /**
         * @fun SetModel 
         * @brief
         * @param model_param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SetModel(ModelBasePtr model_param) override;
        /**
         * @fun InitConnect
         * @brief 初始化信号槽连接
         * @date 2024.9.9
         * <AUTHOR>
         */
        void InitConnect();
    public slots:
        /**
         * @fun SetModel
         * @brief 来自view的更新参数信号处理(数据保存到数据库)
         * @param data_
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotSave(const jrsdata::ViewParamBasePtr& data_);
    signals:
        /**
         * @fun SigSettingParam
         * @brief 初始化信号槽连接
         * @param param
         * @date 2024.9.9
         * <AUTHOR>
         */
        void SigSettingParam(const jrsdata::ViewParamBasePtr& param);
    private:
        SettingView* _view;
        std::shared_ptr<SettingModel> _model;
    };
    using SettingControllerPtr = std::shared_ptr<SettingController>;
}
#endif // !__JRS_STANDARDCONTROLLER_H__
