﻿
#include "fileoperation.h"
#include "timeutility.h"
#include "stringoperation.h"

#include <iostream>
#include <fstream>
#include <filesystem>
#include <string>
#include <cassert>
using namespace jtools;
namespace fs = std::filesystem;

void test_file_copy()
{
    // 测试用例1：源文件存在，目标文件不存在
    std::string source_file1 = "test.txt";
    std::string destination_file1 = "test_copy.txt";
    std::ofstream(source_file1) << "This is a test file.";
    assert(FileOperation::CopyFileOrDirectory(source_file1, destination_file1) == true);
    assert(fs::exists(destination_file1) == true);
    fs::remove(source_file1);
    fs::remove(destination_file1);

    // 测试用例2：源文件不存在，目标文件不存在
    std::string source_file2 = "nonexistent.txt";
    std::string destination_file2 = "test_copy.txt";
    assert(FileOperation::CopyFileOrDirectory(source_file2, destination_file2) == false);
    assert(fs::exists(destination_file2) == false);

    // 测试用例3：源文件存在，目标文件存在
    std::ofstream(source_file1) << "This is a test file.";
    std::ofstream(destination_file1) << "This is a test file.";
    assert(FileOperation::CopyFileOrDirectory(source_file1, destination_file1) == true);
    assert(fs::exists(destination_file1) == true);
    fs::remove(source_file1);
    fs::remove(destination_file1);

    // 测试用例4：源目录存在，目标目录不存在
    std::string source_dir4 = "test_dir";
    std::string destination_dir4 = "test_dir_copy";
    fs::create_directory(source_dir4);
    fs::create_directory(source_dir4 + "/subdir");
    std::ofstream(source_dir4 + "/test.txt") << "This is a test file.";
    std::ofstream(source_dir4 + "/subdir/test.txt") << "This is a test file.";
    assert(FileOperation::CopyFileOrDirectory(source_dir4, destination_dir4) == true);
    assert(fs::exists(destination_dir4) == true);
    assert(fs::exists(destination_dir4 + "/test.txt") == true);
    assert(fs::exists(destination_dir4 + "/subdir/test.txt") == true);
    fs::remove_all(source_dir4);
    fs::remove_all(destination_dir4);

    // 测试用例5：源目录不存在，目标目录不存在
    std::string source_dir5 = "nonexistent_dir";
    std::string destination_dir5 = "test_dir_copy";
    assert(FileOperation::CopyFileOrDirectory(source_dir5, destination_dir5) == false);
    assert(fs::exists(destination_dir5) == false);

    // 测试用例6：源目录存在，目标目录存在
    fs::create_directory(source_dir4);
    fs::create_directory(source_dir4 + "/subdir");
    std::ofstream(source_dir4 + "/test.txt") << "This is a test file.";
    std::ofstream(source_dir4 + "/subdir/test.txt") << "This is a test file.";
    fs::create_directory(destination_dir4);
    fs::create_directory(destination_dir4 + "/subdir");
    std::ofstream(destination_dir4 + "/test.txt") << "This is a test file.";
    std::ofstream(destination_dir4 + "/subdir/test.txt") << "This is a test file.";
    assert(FileOperation::CopyFileOrDirectory(source_dir4, destination_dir4) == true);
    assert(fs::exists(destination_dir4) == true);
    assert(fs::exists(destination_dir4 + "/test.txt") == true);
    assert(fs::exists(destination_dir4 + "/subdir/test.txt") == true);
    fs::remove_all(source_dir4);
    fs::remove_all(destination_dir4);

    printf("All test file_copy cases pass");
}

void test_file_exist()
{
    // const char *path1 = "test.txt";
    // std::ofstream file("test.txt");
    // file.close();
    // assert(FileOperation::IsPathExist(path1) == true);
    // assert(FileOperation::IsPathExist("nonexistent.txt") == false);
    // assert(FileOperation::IsPathExist("") == false);
    assert(FileOperation::IsPathExist(".") == false);
    assert(FileOperation::IsPathExist("/") == false);
    assert(FileOperation::IsPathExist("/dev/null") == false); // 假设/dev/null不存在文件
}
void test_IsValidPath()
{

    // Test case 1: Path is a directory
    std::string path1 = "/path/to/directory";
    assert(FileOperation::IsValidPath(path1) == true);

    // Test case 2: Path is a file
    std::string path2 = "/path/to/file.txt";
    std::ofstream(path2) << "This is a test file.";
    assert(FileOperation::IsValidPath(path2) == true);
    fs::remove(path2);

    // Test case 3: Path does not exist
    std::string path3 = "/path/to/nonexistent";
    assert(FileOperation::IsValidPath(path3) == true);

    // Test case 4: Path is an empty string
    std::string path4 = "";
    assert(FileOperation::IsValidPath(path4) == false);

    printf("All test_is_dir cases pass");
}
void test_fileextensionsupport()
{
    // 测试用例1：文件扩展名在支持列表中
    assert(FileOperation::IsFileExtensionSupport("test.jpg", ".jpg .png .gif") == true);

    // 测试用例2：文件扩展名不在支持列表中
    assert(FileOperation::IsFileExtensionSupport("test.txt", ".jpg .png .gif") == false);

    // 测试用例3：文件扩展名为空
    assert(FileOperation::IsFileExtensionSupport("test", ".jpg .png .gif") == false);

    // 测试用例4：支持列表为空
    assert(FileOperation::IsFileExtensionSupport("test.jpg", "") == false);

    // 测试用例5：文件扩展名和支持列表都为空
    assert(FileOperation::IsFileExtensionSupport("", "") == false);

    // 测试用例6：支持列表包含通配符
    assert(FileOperation::IsFileExtensionSupport("test.png", "*.jpg *.png *.gif") == true);

    // 测试用例7：支持列表包含通配符，但文件扩展名不在支持列表中
    assert(FileOperation::IsFileExtensionSupport("test.txt", "*.jpg *.png *.gif") == false);

    printf("All fileextensionsupport cases pass");
}
void test_deleteall()
{
    // Test case 1: Directory with multiple files
    fs::create_directory("test_dir1");
    std::ofstream("test_dir1/file1.txt");
    std::ofstream("test_dir1/file2.txt");
    assert(FileOperation::DeleteAllInDirectory("test_dir1") == true);
    assert(!fs::exists("test_dir1/file1.txt"));
    assert(!fs::exists("test_dir1/file2.txt"));
    fs::remove_all("test_dir1");

    // Test case 2: Directory with no files
    fs::create_directory("test_dir2");
    assert(FileOperation::DeleteAllInDirectory("test_dir2") == true);
    assert(fs::exists("test_dir2"));
    fs::remove_all("test_dir2");

    // Test case 3: Directory with subdirectories
    fs::create_directory("test_dir3");
    fs::create_directory("test_dir3/subdir1");
    std::ofstream("test_dir3/subdir1/file1.txt");
    assert(FileOperation::DeleteAllInDirectory("test_dir3") == true);
    assert(!fs::exists("test_dir3/subdir1/file1.txt"));
    fs::remove_all("test_dir3");

    // Test case 4: Directory with files and subdirectories
    fs::create_directory("test_dir4");
    std::ofstream("test_dir4/file1.txt");
    fs::create_directory("test_dir4/subdir1");
    std::ofstream("test_dir4/subdir1/file2.txt");
    assert(FileOperation::DeleteAllInDirectory("test_dir4") == true);
    assert(!fs::exists("test_dir4/file1.txt"));
    assert(!fs::exists("test_dir4/subdir1/file2.txt"));
    fs::remove_all("test_dir4");

    // Test case 5: Non-existent directory
    assert(FileOperation::DeleteAllInDirectory("non_existent_dir") == false);

    printf("All test_deleteall cases pass");
}

void test_GetFileNameFromPath()
{

    // // 测试用例1：文件路径包含扩展名
    // std::string filePath1 = "C:\\Users\\<USER>\\Documents\\file.txt";
    // assert(jtools::FileOperation::GetFileNameFromPath(filePath1, true) == "file.txt");

    // 测试用例2：文件路径不包含扩展名
    std::string filePath2 = "C:\\Users\\<USER>\\Documents\\file";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath2, true) == "file");

    // 测试用例3：文件路径为空
    std::string filePath3 = "";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath3, true) == "");

    // 测试用例4：文件路径只包含文件名
    std::string filePath4 = "file.txt";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath4, true) == "file.txt");

    // 测试用例5：文件路径包含多个路径分隔符
    std::string filePath5 = "C:\\Users\\<USER>\\Documents\\folder\\file.txt";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath5, true) == "file.txt");

    // 测试用例6：文件路径包含多个点号分隔符
    std::string filePath6 = "C:\\Users\\<USER>\\Documents\\file.tar.gz";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath6, true) == "file.tar.gz");

    // 测试用例7：文件路径包含特殊字符
    std::string filePath7 = "C:\\Users\\<USER>\\Documents\\file@.txt";
    assert(jtools::FileOperation::GetFileNameFromPath(filePath7, true) == "file@.txt");

    printf("All test_GetFileNameFromPath cases pass");
}

void test_IsVaildFileName()
{
    // 测试用例1：文件名包含非法字符
    assert(!jtools::FileOperation::IsVaildFileName("test?.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test*.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test<.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test>.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test|.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test/.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test\\txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test:txt"));
    assert(!jtools::FileOperation::IsVaildFileName("test\".txt"));

    // 测试用例2：文件名为空
    assert(!jtools::FileOperation::IsVaildFileName(""));

    // 测试用例3：文件名为Windows保留名称
    assert(!jtools::FileOperation::IsVaildFileName("CON.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("PRN.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("AUX.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("NUL.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM1.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM2.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM3.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM4.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM5.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM6.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM7.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM8.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("COM9.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT1.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT2.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT3.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT4.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT5.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT6.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT7.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT8.txt"));
    assert(!jtools::FileOperation::IsVaildFileName("LPT9.txt"));

    // 测试用例4：文件名合法
    assert(jtools::FileOperation::IsVaildFileName("test.txt"));
    assert(jtools::FileOperation::IsVaildFileName("test123.txt"));
    assert(jtools::FileOperation::IsVaildFileName("test_123.txt"));
    assert(jtools::FileOperation::IsVaildFileName("test.123.txt"));
    assert(jtools::FileOperation::IsVaildFileName("test-123.txt"));

    printf("All test_IsVaildFileName cases pass");
}

void test_RenameFileOrDirectory()
{
    // Test case 1: Rename a file
    std::string old_file_path = "test.txt";
    std::string new_file_path = "new_test.txt";
    fs::path old_file_path_obj(old_file_path);
    fs::path new_file_path_obj(new_file_path);
    if (!fs::exists(old_file_path_obj))
    {
        std::ofstream(old_file_path_obj) << "This is a test file.";
    }
    assert(FileOperation::RenameFileOrDirectory(old_file_path, new_file_path) == true);
    assert(fs::exists(new_file_path_obj) == true);
    assert(fs::exists(old_file_path_obj) == false);

    // Test case 2: Rename a directory
    std::string old_dir_path = "test_dir";
    std::string new_dir_path = "new_test_dir";
    fs::path old_dir_path_obj(old_dir_path);
    fs::path new_dir_path_obj(new_dir_path);
    if (!fs::exists(old_dir_path_obj))
    {
        fs::create_directory(old_dir_path_obj);
    }
    assert(FileOperation::RenameFileOrDirectory(old_dir_path, new_dir_path) == true);
    assert(fs::exists(new_dir_path_obj) == true);
    assert(fs::exists(old_dir_path_obj) == false);

    // Test case 3: Rename a file to an existing file
    std::string existing_file_path = "existing.txt";
    fs::path existing_file_path_obj(existing_file_path);
    if (!fs::exists(new_file_path))
    {
        std::ofstream(new_file_path) << "This is a test file.";
    }
    if (!fs::exists(existing_file_path))
    {
        std::ofstream(existing_file_path) << "This is a test file.";
    }
    assert(FileOperation::RenameFileOrDirectory(new_file_path, existing_file_path) == false);
    assert(fs::exists(existing_file_path_obj) == true);

    // Test case 4: Rename a directory to an existing directory
    std::string existing_dir_path = "existing_dir";
    fs::path existing_dir_path_obj(existing_dir_path);
    if (!fs::exists(existing_dir_path_obj))
    {
        fs::create_directory(existing_dir_path_obj);
    }
    if (!fs::exists(new_dir_path))
    {
        fs::create_directory(new_dir_path);
    }
    assert(FileOperation::RenameFileOrDirectory(new_dir_path, existing_dir_path) == false);
    assert(fs::exists(existing_dir_path_obj) == true);
    assert(fs::exists(new_dir_path_obj) == true);
    fs::remove(new_file_path_obj);
    fs::remove(existing_dir_path_obj);
    fs::remove(new_dir_path);

    // Test case 5: Rename a non-existing file
    std::string non_existing_file_path = "non_existing.txt";
    fs::path non_existing_file_path_obj(non_existing_file_path);
    assert(FileOperation::RenameFileOrDirectory(non_existing_file_path, new_file_path) == false);
    assert(fs::exists(new_file_path_obj) == false);
    fs::remove(new_file_path_obj);

    // Test case 6: Rename a non-existing directory
    std::string non_existing_dir_path = "non_existing_dir";
    fs::path non_existing_dir_path_obj(non_existing_dir_path);
    assert(FileOperation::RenameFileOrDirectory(non_existing_dir_path, new_dir_path) == false);
    assert(fs::exists(new_dir_path_obj) == false);
    fs::remove(new_file_path_obj);

    // Clean up
    fs::remove(new_file_path_obj);
    fs::remove(existing_file_path_obj);
    fs::remove(new_dir_path_obj);
    fs::remove(existing_dir_path_obj);

    printf("All test_RenameFileOrDirectory cases pass");
}
void test_GetCurrentTimeStringWithUS()
{
    // 测试用例1：默认格式
    std::string result1 = TimeUtility::GetCurrentTimeStringWithUS();
    std::cout << "r " << result1 << std::endl;
    assert(result1.length() > 0);

    // 测试用例2：自定义格式
    std::string result2 = TimeUtility::GetCurrentTimeStringWithUS("%Y/%m/%d %H:%M:%S");
    std::cout << "r " << result2 << std::endl;
    assert(result2.length() > 0);

    // 测试用例3：检查微秒部分
    std::string result3 = TimeUtility::GetCurrentTimeStringWithUS();
    result3 = result3.substr(result3.length() - 6);
    std::cout << "r " << result3 << std::endl;
    assert(result3.length() == 6);

    // 测试用例4：检查时间格式
    std::string result4 = TimeUtility::GetCurrentTimeStringWithUS("%Ya%ma%d");
    std::cout << "r " << result4 << std::endl;
    assert(result4.length() == 17);

    // 测试用例5：检查时间格式
    std::string result5 = TimeUtility::GetCurrentTimeStringWithUS("%H->%M->%S");
    std::cout << "r " << result5 << std::endl;
    assert(result5.length() == 17);

    printf("All test_GetCurrentTimeStringWithUS cases pass");
}

void test_timeTO()
{
    // Test case 1: Convert a specific time point to timestamp
    jtools::TimeUtility::ClockType::time_point timePoint1 = jtools::TimeUtility::ClockType::now();
    std::time_t timeStamp1 = jtools::TimeUtility::TimePointToTimeStamp(timePoint1);
    assert(timeStamp1 == std::time(nullptr));

    // Test case 4: Convert a timestamp to time point and back to timestamp
    std::time_t timeStamp4 = std::time(nullptr);
    jtools::TimeUtility::ClockType::time_point timePoint5 = jtools::TimeUtility::TimeStampToTimePoint(timeStamp4);
    std::time_t timeStamp5 = jtools::TimeUtility::TimePointToTimeStamp(timePoint5);
    assert(timeStamp5 == timeStamp4);
    printf("All test_timeTO cases pass");
}

void test_ReplaceString()
{
    // 测试用例1：替换字符串中的字符
    assert(jtools::StringOperation::ReplaceString("hello world", "o", "a") == "hella warld");

    // 测试用例2：替换字符串中的字符，字符不存在
    assert(jtools::StringOperation::ReplaceString("hello world", "x", "y") == "hello world");

    // 测试用例3：替换字符串中的字符，被替换字符为空
    assert(jtools::StringOperation::ReplaceString("hello world", "", "y") == "hello world");

    // 测试用例4：替换字符串中的字符，替换字符为空
    assert(jtools::StringOperation::ReplaceString("hello world", "o", "") == "hell wrld");

    // 测试用例5：替换字符串中的字符，字符串为空
    assert(jtools::StringOperation::ReplaceString("", "o", "a") == "");

    // 测试用例6：替换字符串中的字符，替换字符和被替换字符相同
    assert(jtools::StringOperation::ReplaceString("hello world", "o", "o") == "hello world");

    // 测试用例7：替换字符串中的字符，替换字符和被替换字符相同，被替换字符为空
    assert(jtools::StringOperation::ReplaceString("hello world", "", "") == "hello world");

    // 测试用例8：替换字符串中的字符，替换字符长度大于1
    assert(jtools::StringOperation::ReplaceString("hello world", "o", "xy") == "hellxy wxyrld");

    // 测试用例9：替换字符串中的字符，被替换字符长度大于1
    assert(jtools::StringOperation::ReplaceString("hello world", "lo", "xy") == "helxy world");

    // 测试用例10：替换字符串中的字符，被替换字符和替换字符长度都大于1且长度不相等
    assert(jtools::StringOperation::ReplaceString("hello world new world", "world", "universe") == "hello universe new universe");

    // 测试用例11：替换字符串中的字符，被替换字符是空格
    assert(jtools::StringOperation::ReplaceString("hello world", " ", "x") == "helloxworld");

    // 测试用例12：替换字符串中的字符，被替换字符是换行符
    assert(jtools::StringOperation::ReplaceString("hello\nworld", "\n", " ") == "hello world");

    // 测试用例13：替换字符串中的字符，被替换字符和替换字符都是转义字符
    assert(jtools::StringOperation::ReplaceString("hello\tworld", "\t", "\\t") == "hello\\tworld");
    assert(jtools::StringOperation::ReplaceString("hello\tworld", "\t", "\n") == "hello\nworld");

    // 测试用例14：替换字符串中的字符，被替换字符是特殊字符
    assert(jtools::StringOperation::ReplaceString("hello world!", "!", "?") == "hello world?");

    // 测试用例15：替换字符串中的字符，替换字符的长度超过字符长度
    assert(jtools::StringOperation::ReplaceString("hello world", "hello", "hello hello hello hello") == "hello hello hello hello world");
    printf("All test_ReplaceString cases pass");
}

int main()
{
    // test_file_copy();
    // test_file_exist();
    // test_fileextensionsupport();
    // test_deleteall();
    // test_GetFileNameFromPath();
    // test_IsVaildFileName();
    // test_timeTO();
    test_ReplaceString();
    // test_GetCurrentTimeStringWithUS();
    // test_RenameFileOrDirectory();
    // test_IsValidPath();
    return 0;
}