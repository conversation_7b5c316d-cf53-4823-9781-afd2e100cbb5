﻿#pragma warning(push, 1)
#include "ui_addbarcodeview.h"
#include "ui_addmarkview.h"

#pragma warning(pop)
#include "addcomponentview.h"
#include "componentvaluedialog.h"

#include <QButtonGroup>
#include <QDialog>

namespace jrsaoi
{
    struct ImplData
    {
        QDialog* add_barcode_view;
        QDialog* add_mark_view;
        Ui::AddBarcodeView* add_barcode_ui;
        Ui::AddMarkView* add_mark_ui;
        jrsdata::AddComponentViewParam add_component_view_param;

        QButtonGroup* group_ownership;
        QButtonGroup* group_type;
        QButtonGroup* group_param;
        QButtonGroup* group_barcode;
    };

    jrsaoi::AddComponentView::AddComponentView(QWidget* parent)
        :_param(std::make_unique<ImplData>())
        , QWidget(parent)
    {
        Init();
    }

    AddComponentView::~AddComponentView()
    {
        if (_param->add_barcode_ui)
        {
            delete  _param->add_barcode_ui;
            _param->add_barcode_ui = nullptr;
        }
        if (_param->add_mark_ui)
        {
            delete  _param->add_mark_ui;
            _param->add_mark_ui = nullptr;
        }

        //if (_param->add_barcode_view)
        //{
        //    _param->add_barcode_view->close();
        //    _param->add_barcode_view->deleteLater();
        //}
        //if (_param->add_mark_view)
        //{
        //    _param->add_mark_view->close();
        //    _param->add_mark_view->deleteLater();
        //}
    }

    int AddComponentView::UpdateView(const jrsdata::OperateViewParamPtr& param_)
    {
        if (param_->event_name == jrsaoi::SHORTCUT_ACT_CREATE_BARCODE_EVENT_NAME)
        {
            auto check_button = _param->group_barcode->checkedButton();
            OnButtonToggled(check_button, check_button->isChecked());
            _param->add_barcode_view->show();
            _param->add_barcode_view->raise();  // 提升到顶层
            _param->add_barcode_view->activateWindow();  // 激活窗口，使其获得焦点

        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_CREATE_MARK_EVENT_NAME)
        {
            /** <为了刷新事件，响应所以要重置一下 */
            auto check_button = _param->group_ownership->checkedButton();
            OnButtonToggled(check_button, check_button->isChecked());
            auto check_button_type = _param->group_type->checkedButton();
            OnButtonToggled(check_button_type, check_button_type->isChecked());
            _param->add_mark_view->show();
            _param->add_mark_view->raise();  // 提升到顶层
            _param->add_mark_view->activateWindow();  // 激活窗口，使其获得焦点


        }
        else if (param_->event_name == jrsaoi::SHORTCUT_ACT_ADD_COMPONENT)
        {
            AddComponentAction();
        }
        return 0;
    }

    void AddComponentView::Init()
    {
        InitView();
        InitConnect();
    }

    void AddComponentView::InitView()
    {
        _param->add_barcode_view = new QDialog(this);  // 创建 QWidget
        _param->add_mark_view = new QDialog(this);

        _param->add_barcode_ui = new Ui::AddBarcodeView();
        _param->add_mark_ui = new Ui::AddMarkView();

        _param->add_barcode_ui->setupUi(_param->add_barcode_view);  // 绑定 UI
        _param->add_mark_ui->setupUi(_param->add_mark_view);

        _param->add_barcode_view->setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);
        _param->add_mark_view->setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);

        // 创建按钮组
        _param->group_ownership = new QButtonGroup(this);  // "所属" 组
        _param->group_ownership->addButton(_param->add_mark_ui->radio_btn_entirety_mark);
        _param->group_ownership->addButton(_param->add_mark_ui->radio_btn_subboard_mark);

        _param->group_type = new QButtonGroup(this);  // "类型" 组
        _param->group_type->addButton(_param->add_mark_ui->radio_btn_shape);
        _param->group_type->addButton(_param->add_mark_ui->radio_btn_convert_mark);
        _param->group_type->addButton(_param->add_mark_ui->radio_btn_image);

        _param->group_param = new QButtonGroup(this);  // "参数" 组
        _param->group_param->addButton(_param->add_mark_ui->radio_btn_light);
        _param->group_param->addButton(_param->add_mark_ui->radio_btn_dark);

        _param->group_barcode = new QButtonGroup(this);  // "条码" 组
        _param->group_barcode->addButton(_param->add_barcode_ui->radio_btn_entirety_barcode);
        _param->group_barcode->addButton(_param->add_barcode_ui->radio_btn_subboard_barcode);
        _param->group_barcode->addButton(_param->add_barcode_ui->radio_btn_vehicle_barcode);
        _param->group_barcode->addButton(_param->add_barcode_ui->radio_btn_cover_plate_barcode);
        _param->add_mark_ui->radio_btn_image->setChecked(true);
        _param->add_mark_ui->radio_btn_entirety_mark->setChecked(true);
        _param->add_barcode_ui->radio_btn_entirety_barcode->setChecked(true);


        /** <ＴＯＤＯ：带实现功能*/
        _param->add_mark_ui->radio_btn_convert_mark->setDisabled(true);
        _param->add_mark_ui->radio_btn_shape->setDisabled(true);
        _param->add_barcode_ui->radio_btn_vehicle_barcode->setDisabled(true);
        _param->add_barcode_ui->radio_btn_cover_plate_barcode->setDisabled(true);


    }

    void AddComponentView::InitConnect()
    {
        // 连接信号到槽函数
        connect(_param->group_ownership, static_cast<void(QButtonGroup::*)(QAbstractButton*, bool)>(&QButtonGroup::buttonToggled), this, &AddComponentView::OnButtonToggled);
        connect(_param->group_type, static_cast<void(QButtonGroup::*)(QAbstractButton*, bool)>(&QButtonGroup::buttonToggled), this, &AddComponentView::OnButtonToggled);
        connect(_param->group_param, static_cast<void(QButtonGroup::*)(QAbstractButton*, bool)>(&QButtonGroup::buttonToggled), this, &AddComponentView::OnButtonToggled);
        connect(_param->group_barcode, static_cast<void(QButtonGroup::*)(QAbstractButton*, bool)>(&QButtonGroup::buttonToggled), this, &AddComponentView::OnButtonToggled);
        connect(_param->add_mark_ui->btn_frame_select, &QPushButton::clicked, this, &AddComponentView::OnPushButton);
        connect(_param->add_barcode_ui->btn_frame_select, &QPushButton::clicked, this, &AddComponentView::OnPushButton);
        connect(_param->add_mark_ui->btn_identify, &QPushButton::clicked, this, &AddComponentView::OnPushButton);
    }

    void AddComponentView::ToggleLayoutVisibility(QLayout* layout, bool is_visible)
    {
        if (!layout)
            return;

        for (int i = 0; i < layout->count(); ++i)
        {
            QLayoutItem* item = layout->itemAt(i);
            if (item && item->widget())
            {
                item->widget()->setHidden(!is_visible);
            }
        }
    }
    void AddComponentView::OnPushButton()
    {
        jrsdata::OperateViewParamPtr operate_view_param_ptr = std::make_shared<jrsdata::OperateViewParam>();
        operate_view_param_ptr->add_component_view_param = _param->add_component_view_param;

        operate_view_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        operate_view_param_ptr->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        operate_view_param_ptr->event_name = jrsaoi::OPERATE_REQUEST_RENDER_FRAME_SELECT_REGION_EVENT_NAME;
        operate_view_param_ptr->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        operate_view_param_ptr->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;

        if (sender() == _param->add_mark_ui->btn_frame_select)
        {
            _param->add_mark_view->close();
        }
        else if (sender() == _param->add_barcode_ui->btn_frame_select)
        {
            _param->add_barcode_view->close();
        }
        else if (sender() == _param->add_mark_ui->btn_identify)
        {
            // mark 识别
        }
        emit SigUpdateView(operate_view_param_ptr);
    }
    void AddComponentView::OnButtonToggled(QAbstractButton* button, bool checked)
    {
        if (!checked) return;

        static const std::map<QAbstractButton*, jrsdata::Component::Type> component_map = {
            {_param->add_mark_ui->radio_btn_entirety_mark, jrsdata::Component::Type::MARK},
            {_param->add_mark_ui->radio_btn_subboard_mark, jrsdata::Component::Type::SUB_MARK},
            {_param->add_barcode_ui->radio_btn_cover_plate_barcode, jrsdata::Component::Type::COVERPLATE_BARCODE},
            {_param->add_barcode_ui->radio_btn_entirety_barcode, jrsdata::Component::Type::BARCODE},
            {_param->add_barcode_ui->radio_btn_subboard_barcode, jrsdata::Component::Type::SUB_BARCODE},
            {_param->add_barcode_ui->radio_btn_vehicle_barcode, jrsdata::Component::Type::CARRIER_BARCODE},
        };

        auto it = component_map.find(button);
        if (it != component_map.end())
        {
            _param->add_component_view_param.component_type = it->second;
            return;
        }

        // 处理 Mark 相关逻辑
        static const std::map<QAbstractButton*, jrsdata::AddComponentViewParam::AddMarkType> mark_type_map = {
            {_param->add_mark_ui->radio_btn_shape, jrsdata::AddComponentViewParam::AddMarkType::MarkAlgorithm},
            {_param->add_mark_ui->radio_btn_convert_mark, jrsdata::AddComponentViewParam::AddMarkType::ConvertMark},
            {_param->add_mark_ui->radio_btn_image, jrsdata::AddComponentViewParam::AddMarkType::DirectCreate}
        };

        auto mark_it = mark_type_map.find(button);
        if (mark_it != mark_type_map.end())
        {
            _param->add_component_view_param.add_mark_type = mark_it->second;
            bool is_hidden = !(mark_it->first == _param->add_mark_ui->radio_btn_shape);
            _param->add_mark_ui->shape_frame->setHidden(is_hidden);
            _param->add_mark_ui->btn_identify->setHidden(is_hidden);
            //ToggleLayoutVisibility(_param->add_mark_ui->verticalLayout_shape, );
            return;
        }

        // 处理 Light / Dark 逻辑（如果后续有赋值逻辑，可在这里扩展）
        if (button == _param->add_mark_ui->radio_btn_light || button == _param->add_mark_ui->radio_btn_dark)
        {
            // 赋值 mark  相关参数 
            return;
        }
    }
    void AddComponentView::AddComponentAction()
    {
        jrsdata::RenderEventParamPtr render_param = std::make_shared<jrsdata::RenderEventParam>();  /**<结构体待统一，目前无法统一*/
        render_param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        render_param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
        render_param->module_name = jrsaoi::RENDER2D_MODULE_NAME;
        render_param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;

        auto dialog = new ComponentDialog(this);
        if (dialog->exec() == QDialog::Accepted)
        {
            render_param->event_name = jrsaoi::COMPONENT_EDIT_EVENT_NAME;
            jrsdata::CadEventParam param;
            param.component_name = dialog->GetComponentName().toLocal8Bit().toStdString();
            param.part_name = dialog->GetComponentPart().toLocal8Bit().toStdString();
            param.step = jrsdata::CadEventParam::Step::MANUAL_ADD_CAD;
            render_param->cad_param = param;
            emit SigUpdateView(render_param);
        }
        else
        {
            // 如果用户取消对话框，不触发任何事件
            render_param->event_name.clear();
        }
    }
}