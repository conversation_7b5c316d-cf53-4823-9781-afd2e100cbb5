#include "subboardclone.h"
#include "viewparam.hpp"

namespace jrsaoi
{
    int MutliCopy::CreateSub(SubBoardCloneParam& param)
    {
        if (param.rows * param.cols <= 1)
            return 2;
        auto& center = param.subboard_center;
        param.res.clear();
        switch (jrsdata::MultiBoardEventParam::MultiCopyType(param.copy_mode))
        {
        case jrsdata::MultiBoardEventParam::MultiCopyType::ARRAY:
        {
            cv::Point col_offset = param.flag_col - param.flag_temp;
            cv::Point row_offset = param.flag_row - param.flag_temp;

            for (int i = 0; i < param.cols; ++i)
            {
                for (int j = 0; j < param.rows; ++j)
                {
                    if (i == 0 && j == 0)
                        continue;
                    cv::Point2f tp = center + i * col_offset + j * row_offset;
                    param.res.emplace_back(std::vector<float>{tp.x, tp.y, 0});
                }
            }
        }
        break;
        case jrsdata::MultiBoardEventParam::MultiCopyType::ROTATE:
        {
            cv::Point2f rotated_center;
            double angle = 180;
            if (param.rows > 1)
            {
                rotated_center = param.flag_temp + (param.flag_row - param.flag_temp) / 2.0f;
            }
            else if (param.cols > 1)
            {
                rotated_center = param.flag_temp + (param.flag_col - param.flag_temp) / 2.0f;
            }
            else
            {
                return 2;
            }
            // cv::Point center(sub->x(), sub->y());
            cv::Mat rotationMatrix = cv::getRotationMatrix2D(rotated_center, angle, 1.0);
            std::vector<cv::Point2f> originalPoints = { center };
            cv::transform(originalPoints, originalPoints, rotationMatrix);
            param.res.emplace_back(std::vector<float>{originalPoints[0].x, originalPoints[0].y, 180});
        }
        break;
        default:
            return 1;
        }
        return 0;
    }
}