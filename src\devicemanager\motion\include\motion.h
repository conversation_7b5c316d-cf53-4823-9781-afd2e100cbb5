
/*****************************************************************//**
 * @file   motion.h
 * @brief  运动控制封装对外接口，AOI给运控发送指令只能通过此部分进行
 * @details
 * <AUTHOR>
 * @date 2024.8.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.5         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __MOTION_H__
#define __MOTION_H__

// STD
#include <iostream>
#include <thread>
#include <mutex>
#include <map>
#include <functional>
#include <fstream>
#include <future>
//Custom
#include "pluginexport.hpp"
#include "projectparam.hpp"
// ThirdParty
#include "nlohmann/json.hpp"

using JSON = nlohmann::json;

namespace jrsdevice 
{
	// 卡类型
	enum class CardType
	{
		MotionCard = 0,
		IOCard
	};

	// 轴索引
	enum class Axis
	{
		None = 0,
		Axis01,
		Axis02,
		Axis03,
		Axis04,
		Axis05,
		Axis06,
		Axis07,
		Axis08,
		Axis09,
		Axis10,
		Axis11,
		Axis12,
		Axis13,
		Axis14,
		Axis15,
		Axis16,
		Axis17,
		Axis18,
		Axis19,
		Axis20,
		Axis21,
		Axis22,
		Axis23,
		Axis24,
		Axis25,
		Axis26,
		Axis27,
		Axis28,
		Axis29,
		Axis30,
		Axis31,
		Axis32
	};

	// 输入索引
	enum class InputIndex
	{
		None = 0,
		Input01,
		Input02,
		Input03,
		Input04,
		Input05,
		Input06,
		Input07,
		Input08,
		Input09,
		Input10,
		Input11,
		Input12,
		Input13,
		Input14,
		Input15,
		Input16,
		Input17,
		Input18,
		Input19,
		Input20,
		Input21,
		Input22,
		Input23,
		Input24,
		Input25,
		Input26,
		Input27,
		Input28,
		Input29,
		Input30,
		Input31,
		Input32
	};

	// 输出索引
	enum class OutputIndex
	{
		None = 0,
		Output01,
		Output02,
		Output03,
		Output04,
		Output05,
		Output06,
		Output07,
		Output08,
		Output09,
		Output10,
		Output11,
		Output12,
		Output13,
		Output14,
		Output15,
		Output16,
		Output17,
		Output18,
		Output19,
		Output20,
		Output21,
		Output22,
		Output23,
		Output24,
		Output25,
		Output26,
		Output27,
		Output28,
		Output29,
		Output30,
		Output31,
		Output32
	};

	// G代码类型
	enum class GType
	{
		G00,
		G01
	};
	// 轨道索引
	enum class TrackIndex
	{
		Track01 = 1,
		Track02
	};

	// 工位上下料状态
    enum class TrackLoadState
    {
        Loading,      // 上料中
        LoadSuccess,  // 上料完成
        UnLoading,    // 下料中
        UnLoadSuccess // 下料完成
    };

    // 工位拍照状态
    enum class PhotoState
    {
        NotStart,       // 未开始
        PhotoInprocess, // 拍照中
        PhotoSuccess    // 拍照完成
    };

    // 拍照工位
    enum class WorkStation
    {
		Unknown,
        Station1,      // 拍照工位1
        Station2,      // 拍照工位2
        Station3,      // 拍照工位3
        Station4       // 拍照工位4
    };

	// DeviceType
	enum class DeviceType
	{
		AOI3D,
		V2000,
		V1000
	};

    // 轨道工位拍照流程
    struct TrackProcess
    {
        // 公共参数
        TrackLoadState track_load_state; // 工位上下料状态
        PhotoState track_photo_state;    // 工位拍照状态
        WorkStation track_station;       // 拍照工位

        // 3D AOI专用参数
        long long cycle_count; // 老化次数
        bool track_trans_mode; // 传输模式
        int track_mode;        // 轨道模式

        TrackProcess()
        {
            track_load_state = TrackLoadState::UnLoadSuccess;
            track_photo_state = PhotoState::NotStart;
            track_station = WorkStation::Station1;

            cycle_count = 0;
            track_trans_mode = false;
            track_mode = 0;
        }
    };

	// 测试结果
	enum class TestResult
	{
		Undetected = 0,
		OK,
		NG
	};

	// 配方索引
	enum class RecipeIndex
	{
		Slow = 1,
		Normal,
		Faster
	};

	// 指示灯模式
	enum class LightMode
	{
		StandByMode = 0,		/* 待机模式*/
		RunningMode,			/* 运行模式*/
		WarningMode,			/* 警告模式*/
		SoftWareError			/* 软件故障*/
	};


	// 超时时间
	constexpr auto TIMEOUT = 25000;

	// 推送消息回调
	using PublishMsgCallBack = std::function<void(std::string cmd, std::string msg)>;

	class MotionProtocal;
	class MotionWorker;
	class JRS_AOI_PLUGIN_API Motion
	{
		public:
			Motion(const std::string& ip /*= "127.0.0.1"*/, const int port /*= 88888*/);
			~Motion();
			Motion() = delete;

			/**
			 * @fun SetPublishMsgCallBack
			 * @brief 注册推送消息回调
			 * @param callback	消息回调
			 * @return
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			void SetPublishMsgCallBack(const PublishMsgCallBack &callback);

		public:
			// 设置设备型号
			void SetDeviceType(DeviceType type);
			/**************************************************************************
											流程指令
			**************************************************************************/
			/**
			 * @fun AskInitial
			 * @brief 流程初始化(回零)
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int AskInitial(const int timeout = TIMEOUT);

			/**
			 * @fun AskStop
			 * @brief 停止流程
			 * @param timeout	超时时间
			 * @param proIndex	流程索引(-1表示停止所有流程)
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int AskStop(const int timeout = 2000,const int proIndex = -1);

			/**
			 * @fun AskPause
			 * @brief 暂停流程
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int AskPause(const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun OffSet
			 * @brief 点位偏移，用于统一向以发送拍照点位添加固定偏移
			 * @param x	X偏移
			 * @param y	Y偏移
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int OffSet(const double x, const double y, const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun SendNC
			 * @brief 发送NC程序
			 * @param content	NC程序内容
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int SendNc(const std::string content, const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun ChangeRecipe
			 * @brief 切换配方
			 * @param recipename	配方name
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.8.6
			 * <AUTHOR>
			 */
			int ChangeRecipe(const std::string recipename = "recipe1", const int timeout = 2000);

			/**
			 * @fun ChangeLightMode
			 * @brief 切换指示灯模式
			 * @param mode	模式
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2025.5.15
			 * <AUTHOR>
			 */
			int ChangeLightMode(LightMode mode = LightMode::StandByMode, const int timeout = 2000);

			/**
			 * @fun AskStart
			 * @brief 启动流程
			 * @param index	程序从第index行开始执行
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int AskStart(const int index = 0, const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun PhotoTook
			 * @brief PhotoTook信号(可以去往下一个位置拍照) 备注:非自动流程,index才起作用,对于自动流程根据CreateNC指定的索引
			 * @param index	闪频板触发IO索引(10ms后自动复位IO，无需额外复位)
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int PhotoTook(OutputIndex index,const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun RePhtoTo
			 * @brief 自动流程中在等待PHTOOK信号过程中，发送RePhtoTo，则重新触发一次拍照
			 * @param timeout	超时时间
			 * @param proIndex	流程索引
			 * @return 执行结果
			 * @date 2024.8.30
			 * <AUTHOR>
			 */
			int RePhtoTo(const int timeout = 2000, const int proIndex = 1);

			/**
			 * @fun SendResult
			 * @brief 发送测试结果（在检测流程结束，拿到算法结果时发送）
			 * @param result	result为OK或者NG
			 * @param index	轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int SendResult(const TestResult result, const TrackIndex index, const int timeout = 2000);

			/**
			 * @fun CreateNC
			 * @brief 生成NC程序
			 * @param poslist	点位JSON数组字符串，结构[{"xAxis":-100.2345,"yAxis":100.3411},...]
			 * @param type	G代码类型
			 * @param axis1	轴1索引
			 * @param axis2	轴2索引
			 * @param axis3	轴3索引
			 * @param index	相机拍照IO索引
			 * @return 生成的NC程序，直接调用SendNC发送程序
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string CreateNC(const std::string poslist, GType type = GType::G00, Axis axis1 = Axis::Axis01, Axis axis2 = Axis::Axis02, Axis axis3 = Axis::None, OutputIndex index = OutputIndex::Output01);

			/**
			 * @fun CreateNC
			 * @brief 生成NC程序
			 * @param poslist	CaptureFovPosList结构体
			 * @param type	G代码类型
			 * @param axis1	轴1索引
			 * @param axis2	轴2索引
			 * @param axis3	轴3索引
			 * @param index	相机拍照IO索引
			 * @return 生成的NC程序，直接调用SendNC发送程序
			 * @date 2024.8.11
			 * <AUTHOR>
			 */
			std::string CreateNC(jrsdata::CaptureFovPosList poslist, GType type = GType::G00, Axis axis1 = Axis::Axis01, Axis axis2 = Axis::Axis02, Axis axis3 = Axis::Axis03, OutputIndex index = OutputIndex::Output01);

			/**
			 * @fun ReadNc
			 * @brief 直接读取NC文件获取程序
			 * @param filePath	NC文件地址(.NC)
			 * @return 生成的NC程序，直接调用SendNC发送程序
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string ReadNc(const std::string filePath);

			/**************************************************************************
										脚本流程处理指令(非自动检测流程)
			**************************************************************************/
			/**
			 * @fun InterruptProcess
			 * @brief 打断流程
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.30
			 * <AUTHOR>
			 */
			int InterruptProcess(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun InitialCheck
			 * @brief 初始化检查
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.30
			 * <AUTHOR>
			 */
			int InitialCheck(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun TransportMode
			 * @brief 设置轨道为传输模式
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.30
			 * <AUTHOR>
			 */
			int TransportMode(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun TransToImport
			 * @brief 回到入料口
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.11.20
			 * <AUTHOR>
			 */
			int TransToImport(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun TransToExport
			 * @brief 回到出料口
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.11.20
			 * <AUTHOR>
			 */
			int TransToExport(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun Load
			 * @brief 上料
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.8.18
			 * <AUTHOR>
			 */
			int Load(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun Load2
			 * @brief 拍照工位2上料(从工位1位置到工位2位置)		V2000用
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.8.18
			 * <AUTHOR>
			 */
			int Load2(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun DirectLoad2
			 * @brief 拍照工位2上料(直接上料到工位2)		V2000用
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.8.18
			 * <AUTHOR>
			 */
			int DirectLoad2(const TrackIndex index, const int timeout = TIMEOUT);

			/**
			 * @fun UnLoad
			 * @brief 下料
			 * @param index 轨道索引
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.8.18
			 * <AUTHOR>
			 */
			int UnLoad(const TrackIndex index, const int timeout = TIMEOUT);

			/**************************************************************************
											控制指令
			**************************************************************************/
			/**
			 * @fun Mov
			 * @brief 相对移动
			 * @param axis	轴
			 * @param distance	要运动的相对距离
			 * @param speed	运动速度
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int Mov(const Axis axis, const double distance, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun MOVA
			 * @brief 绝对移动
			 * @param axis	轴
			 * @param position	要运动的绝对距离
			 * @param speed	运动速度
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int Mova(const Axis axis, const double position, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun LopTo
			 * @brief 快速定位(X,Y)
			 * @param xpos	X轴绝对位置
			 * @param ypos	Y轴绝对位置
			 * @param speed	运动速度(如果速度小于0,则使用原本速度)
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int LopTo(const double xpos, const double ypos, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun Interpolation
			 * @brief 插补定位(X,Y)
			 * @param xpos	X轴绝对位置
			 * @param ypos	Y轴绝对位置
			 * @param speed	运动速度(如果速度小于0,则使用原本速度)
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int Interpolation(const double xpos, const double ypos, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun LOPToXYZ
			 * @brief 快速定位(X,Y,Z)
			 * @param xpos	X轴绝对位置
			 * @param ypos	Y轴绝对位置
			 * @param zpos	Z轴绝对位置
			 * @param speed	运动速度
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int LopToXYZ(const double xpos, const double ypos, const double zpos, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun InterpolationXYZ
			 * @brief 插补定位(X,Y,Z)
			 * @param xpos	X轴绝对位置
			 * @param ypos	Y轴绝对位置
			 * @param zpos	Z轴绝对位置
			 * @param speed	运动速度
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int InterpolationXYZ(const double xpos, const double ypos, const double zpos, const double speed = 30, const int timeout = TIMEOUT);

			/**
			 * @fun OUTPUT
			 * @brief 控制输出
			 * @param cardType	控制卡类型(运动控制卡或IO卡)
			 * @param cardID	控制卡ID
			 * @param index	输出索引
			 * @param flag	true或false
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int OutPut(const CardType card_type, const int card_id, const OutputIndex index, const bool flag, const int timeout = 2000);

			/**
			 * @fun ClearAlarm
			 * @brief 清除报警
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int ClearAlarm(const int timeout = 2000);

			/**
			 * @fun JOG
			 * @brief 启动JOG/停止JOG
			 * @param axis	轴
			 * @param flag	true为启动，false为停止
			 * @param direction	1为正方向，-1为负方向
			 * @param speed	运动速度
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			int Jog(const Axis axis, const bool flag, const int direction, const double speed, const int timeout = 2000);

			/**
			 * @fun Home
			 * @brief 轴回零
			 * @param axis	轴
			 * @param timeout	超时时间
			 * @return 执行结果
			 * @date 2025.2.4
			 * <AUTHOR>
			 */
			int Home(const Axis axis, const int timeout = TIMEOUT);

			/**
			 * @fun GroupHome
			 * @brief 轴组回零
			 * @param groupIndex	轴组索引
			 * @param timeout		超时时间
			 * @return 执行结果
			 * @date 2025.2.17
			 * <AUTHOR>
			 */
			int GroupHome(const int groupIndex = 0, const int timeout = TIMEOUT);

			/**************************************************************************
											查询指令
			**************************************************************************/
			/**
			 * @fun POS
			 * @brief 获取位置
			 * @param timeout	超时时间
			 * @return JSON数组字符串
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string Pos(const int timeout = 2000);

			/**
			 * @fun POS
			 * @brief 获取指定轴位置
			 * @param axis	轴
			 * @param timeout	超时时间
			 * @return 指定轴位置
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string Pos(const Axis axis, const int timeout = 2000);

			/**
			 * @fun InputStatus
			 * @brief 获取输入
			 * @param cardType	控制卡类型(运动控制卡或IO卡)
			 * @param cardID	控制卡ID
			 * @param timeout	超时时间
			 * @return JSON数组字符串
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string InputStatus(const CardType cardType, const int cardID, const int timeout = 2000);

			/**
			 * @fun InputStatus
			 * @brief 获取输入
			 * @param cardType	控制卡类型(运动控制卡或IO卡)
			 * @param cardID	控制卡ID
			 * @param index	输入索引
			 * @param timeout	超时时间
			 * @return 指定输入
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string InputStatus(const CardType cardType, const int cardID, const InputIndex index, const int timeout = 2000);

			/**
			 * @fun OutputStatus
			 * @brief 获取输出
			 * @param cardType	控制卡类型(运动控制卡或IO卡)
			 * @param cardID	控制卡ID
			 * @param timeout	超时时间
			 * @return JSON数组字符串
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string OutputStatus(const CardType cardType, const int cardID, const int timeout = 2000);

			/**
			 * @fun OutputStatus
			 * @brief 获取输出
			 * @param cardType	控制卡类型(运动控制卡或IO卡)
			 * @param cardID	控制卡ID
			 * @param index	输出索引
			 * @param timeout	超时时间
			 * @return 指定输出
			 * @date 2024.7.22
			 * <AUTHOR>
			 */
			std::string OutputStatus(const CardType cardType, const int cardID, const OutputIndex index, const int timeout = 2000);


			/**
			 * @fun AxisLimit
			 * @brief 获取轴限位
			 * @param timeout	超时时间
			 * @return xleft#yleft,.....
			 * @date 2024.8.20
			 * <AUTHOR>
			 */
			std::string AxisLimit(const int timeout = 2000);

			/**
			 * @fun AxisLimit
			 * @brief 获取指定轴限位
			 * @param axis	轴
			 * @param timeout	超时时间
			 * @return xleft#yleft
			 * @date 2024.8.20
			 * <AUTHOR>
			 */
			std::string AxisLimit(const Axis axis, const int timeout = 2000);

			/**
			 * @fun ProcessStatus
			 * @brief 获取流程状态和进度
			 * @param timeout	超时时间
			 * @return state1#process1#duration1#estimate1,.....	state为流程状态(0:停止、1:运行、2:暂停)，process为进度(%)，duration为流程执行时间(s)，estimate为预计耗时(s)。目前10个流程
			 * @date 2025.2.12
			 * <AUTHOR>
			 */
			std::string ProcessStatus(const int timeout = 2000);

			/**************************************************************************
											配置相关
			**************************************************************************/
			/**
			 * @fun GetDeviceTrack
			 * @brief 获取轨道配置信息
			 * @param timeout	超时时间
			 * @return 轨道配置信息,JSON数组字符串,具体格式参考配置文件
			 * @date 2024.8.7
			 * <AUTHOR>
			 */
			std::string GetDeviceTrack(const int timeout = 2000);

			/**
			 * @fun GetUserSetTrack
			 * @brief 获取轨道设置信息(左进右进等)
			 * @param timeout	超时时间
			 * @return 轨道设置信息,JSON数组字符串,具体格式参考配置文件
			 * @date 2024.10.5
			 * <AUTHOR>
			 */
			std::string GetUserSetTrack(const int timeout = 2000);

			int UpdateUserSetTrack(std::string userset_tack,const int timeout = 2000);

			// 获取错误信息
			std::string GetLastError();

			/**
			 * @fun GetScriptErrorList
			 * @brief 获取脚本执行失败的错误信息列表
			 * @param timeout	超时时间
			 * @return 脚本执行失败的错误信息列表
			 * @date 2024.11.21
			 * <AUTHOR>
			 */
			std::vector<std::string> GetScriptErrorList(const int timeout = 2000);


		private:
			// 脚本协议指令处理
			int ScriptFunction(const int mode, const int timeout = TIMEOUT);

			// 初始化轴映射
			void InitialAxisMap();

			// 运行线程处理函数
			void RunningMsgDealer();

			// 运控消息处理
			void MotionMsgRecvDealer();

			// 校验机构返回的消息,是否符合消息对的规则
			JSON CompareCmdAndMotionCmd(const JSON msg);

			// 构建发送
			std::string ConductSend(std::string sendMsg, const std::string check, const int timeout);

			// 检查是否发过(之前发过，如果还没收到回复，则忽略此次发送)
			bool CheckSend(std::string sendMsg, const std::string check, const int timeout);

			// 添加检查包
			void AddCheckerPack(const std::string sendMsg, const std::string check, const int timeout);

			// 有推送消息
			void NewPublishMsg(const std::string cmd, const std::string msg);

			// 读取文件
			std::string ReadFile(const std::string filePath);

			// 发送并获取结果
			int SendAndResult(std::string sendMsg, std::string check, int timeout);

			/**
			* @fun GetSpeficJsonValue
			* @brief 获取指定路径的json值
			* @param json_data [IN] json数据
			* @param path [IN] 所要获取指定字段内容的路径，例如:std::string path = "/input/input_param/2/input_img"
			* @return  成功返回获取的内容，失败则返回空
			* <AUTHOR>
			* @date 2024.10.11
			*/
			JSON GetSpeficJsonValue(const nlohmann::json& json_data, const std::string& path);

		private:
			// 工作线程
			std::thread motion;
			bool exit_;

			// 运控协议智能指针
			std::shared_ptr<MotionProtocal> protocal_ptr;

			// TCP客户端智能指针
			std::shared_ptr<MotionWorker> worker_ptr;

			// 发送消息锁
			std::mutex send_mutex;

			// 发送给运控消息的包集合
			std::vector<JSON> g_cmd_packlist;

			// 接收消息集合锁
			std::mutex receive_mutex;

			// 接收到来自运控消息的包集合<cmd,std::string>
			std::map<std::string, std::string> g_receive_packlist;

			// 轴映射
			std::map<Axis, std::string> axis_map;

			// 推送消息回调
			jrsdevice::PublishMsgCallBack publish_msg_callback;

			// 错误信息
			std::string last_error;
			
			// 设备型号
			DeviceType device_type;

			// 接收消息条件变量
			std::condition_variable receive_cv;
	};

}
#endif
