﻿#include "commonparamview.h"

//QT
#include <QComboBox>
//custom
#include "coreapplication.h"

jrsaoi::CommonParamView::CommonParamView(QWidget* parent)
	:QWidget(parent)
	, ui(new Ui::commonparamviewClass())
	, ui_need_hide_controller({})
{
	ui->setupUi(this);
	InitView();
	InitMember();
	InitConnect();
}

jrsaoi::CommonParamView::~CommonParamView()
{
}

void jrsaoi::CommonParamView::UpdateView(const jrsdata::SettingParamMap& common_params_)
{
	if (common_params_.empty())
	{
		Log_ERROR("参数为空 请检查!");
		return;
	}
	_common_params = common_params_;

	Log_INFO("从获取数据更新到普通参数界面！");


	UpdateStaticParam();

	SetHideController(common_params_);
}

jrsdata::SettingParamMap jrsaoi::CommonParamView::GetParam()
{
	return _common_params;
}

void jrsaoi::CommonParamView::SlotSaveParam()
{
	SaveParam();
	emit SigSaveParam(_common_params);
}

void jrsaoi::CommonParamView::SlotChooseDirPath()
{
	QString folder_path = QFileDialog::getExistingDirectory(this, "Select Folder");
	if (folder_path.isEmpty()) {
		return;
	}
	QToolButton* button = qobject_cast<QToolButton*>(sender());
	if (button) {
		QAction* action = button->defaultAction();
		 if (action) {
			 if (action->text() == "选择工程默认路径") {
				 ui->line_edit_project_path->setText(folder_path);
			 }
			 else if (action->text() == "选择标准元件路径") {
				 //ui->lineEdit_standard_device_path->setText(folder_path);
			 }
			 else
			 {
			 }
		 }
	}
}



void jrsaoi::CommonParamView::InitMember()
{
	ui_map_value =
	{
		{jrssettingparam::jrscommonparam::COMMON_PARAM_DEMO,ui->line_edit_demo},
		{jrssettingparam::jrscommonparam::COMMON_PROJECT_PATH,ui->line_edit_project_path},
		{jrssettingparam::jrscommonparam::COMMON_DB_IP,ui->db_ip_line_edit},
		{jrssettingparam::jrscommonparam::COMMON_DB_NAME,ui->db_name_line_edit},
		{jrssettingparam::jrscommonparam::COMMON_DB_USER,ui->db_user_line_edit},
		{jrssettingparam::jrscommonparam::COMMON_DB_PASSWORD,ui->db_pwd_line_edit},
		{jrssettingparam::jrscommonparam::COMMON_DB_CONNECT_NUM,ui->db_connect_num_spin_box},

	};
}


void jrsaoi::CommonParamView::InitView()
{
	QIcon icon(":/image/save.svg");
	int size_icon = 15;
	QPixmap pixmap = icon.pixmap(size_icon, size_icon).scaledToWidth(size_icon, Qt::SmoothTransformation);
	ui->btn_save_param->setIcon(QIcon(pixmap));
	ui->btn_save_param->setIconSize(QSize(size_icon, size_icon));
	ui->btn_save_param->setFlat(true);
	size_icon = 25;
	ui->btn_refresh_param->setIcon(QIcon(":/image/refresh.png").pixmap(size_icon, size_icon).scaledToWidth(size_icon, Qt::SmoothTransformation));
	ui->btn_refresh_param->setIconSize(QSize(size_icon, size_icon));
	ui->btn_refresh_param->setFlat(true);

	/** 选择工程默认路径 */
	QIcon chose_folder_icon(":/image/choose_folder.png");  // 确保这个路径有效
	QAction* action_choose_board_path = new QAction("选择工程默认路径", this);
	action_choose_board_path->setIcon(chose_folder_icon);
	ui->btn_choose_project_path->setIconSize(QSize(size_icon, size_icon));
	ui->btn_choose_project_path->setDefaultAction(action_choose_board_path);
	ui->btn_choose_project_path->setToolButtonStyle(Qt::ToolButtonIconOnly);

	ui->line_edit_project_path->setReadOnly(true);
}

void jrsaoi::CommonParamView::InitConnect()
{
	connect(ui->btn_save_param, &QPushButton::clicked, this, &CommonParamView::SlotSaveParam);
	connect(ui->btn_refresh_param, &QPushButton::clicked, this, &CommonParamView::SigUpdateParam);
	connect(ui->btn_choose_project_path, &QPushButton::clicked, this, &CommonParamView::SlotChooseDirPath);
 //connect(ui->tbtn_choose_standard_deivce_path, &QToolButton::clicked, this, &SystemParamView::SlotChooseDirPath);
}


void jrsaoi::CommonParamView::SaveParam()
{
	Log_INFO("系统参数界面保存参数！");
	for (const auto& value : ui_map_value)
	{
		jrsdata::SettingParam temp_param;
		const auto& ui_control = value.second;

		if (auto* lineEdit = dynamic_cast<QLineEdit*>(ui_control))
		{

			temp_param.param_name = value.first;
			temp_param.param_type = "string";
			temp_param.param_value = lineEdit->text().toStdString();

		}
		else if (auto* checkBox = dynamic_cast<QCheckBox*>(ui_control))
		{
			temp_param.param_name = value.first;
			temp_param.param_type = "bool";
			temp_param.param_value = std::to_string(checkBox->isChecked());
		}
		else if (auto* spinBox = dynamic_cast<QSpinBox*>(ui_control))
		{
			temp_param.param_name = value.first;
			temp_param.param_type = "int";
			temp_param.param_value = std::to_string(spinBox->value());

		}
		else if (auto* radioBtn = dynamic_cast<QRadioButton*>(ui_control)) {
			temp_param.param_name = value.first;
			temp_param.param_type = "bool";
			temp_param.param_value = std::to_string(radioBtn->isChecked());
		}
		else if (auto* comboBox = dynamic_cast<QComboBox*>(ui_control)) {
			temp_param.param_name = value.first;
			temp_param.param_type = "int";
			temp_param.param_value = std::to_string(comboBox->currentIndex());
		}
		_common_params[value.first] = temp_param;
	}
}

void jrsaoi::CommonParamView::UpdateStaticParam()
{
	Log_INFO("更新静态参数界面！");

	for (const auto& param : _common_params)
	{
		auto it = ui_map_value.find(param.first);
		if (it != ui_map_value.end())
		{
			auto value_param = param.second.param_value;
			QWidget* ui_control = it->second;
			if (auto* lineEdit = dynamic_cast<QLineEdit*>(ui_control))
			{
				lineEdit->setText(QString::fromStdString(value_param));
			}
			else if (auto* checkBox = dynamic_cast<QCheckBox*>(ui_control))
			{
				checkBox->setChecked((QString::fromStdString(value_param)).toInt());
			}
			else if (auto* spinBox = dynamic_cast<QSpinBox*>(ui_control))
			{
				spinBox->setValue((QString::fromStdString(value_param)).toInt());
			}
			else if (auto* radioBtn = dynamic_cast<QRadioButton*>(ui_control)) {
				radioBtn->setChecked((QString::fromStdString(value_param)).toInt());
			}
			else if (auto* comboBox = dynamic_cast<QComboBox*>(ui_control)) {
				comboBox->setCurrentIndex((QString::fromStdString(value_param)).toInt());
			}
		}
		else
		{
			Log_ERROR(param.first, " 系统参数中不存在系统参数数据，请检查！");
		}
	}

}

void jrsaoi::CommonParamView::SetHideController(const jrsdata::SettingParamMap& common_params_)
{
	if (!common_params_.empty())
	{

	}
}
