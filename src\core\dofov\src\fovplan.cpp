#include "calcfov.h"
#include "fovplan.h"
#include <iostream>
#include <fstream>

// 测试图片颜色
const std::vector<cv::Scalar> vecColor{
    cv::Scalar(123, 234, 45),  // 淡绿色
    cv::Scalar(200, 100, 150), // 浅紫色
    cv::Scalar(50, 100, 200),  // 深蓝色
    cv::Scalar(255, 0, 0),     // 蓝色
    cv::Scalar(0, 255, 0),     // 绿色
    cv::Scalar(0, 0, 255),     // 红色 
    cv::Scalar(255, 255, 0),   // 黄色
    cv::Scalar(0, 255, 255),   // 青色
    cv::Scalar(255, 0, 255),   // 品红色
    cv::Scalar(128, 128, 128), // 灰色
    cv::Scalar(128, 0, 0),     // 深蓝色
    cv::Scalar(0, 128, 0),     // 深绿色
    cv::Scalar(0, 0, 128),     // 深红色
    cv::Scalar(128, 128, 0),   // 橄榄色
    cv::Scalar(0, 128, 128),   // 深青色
    cv::Scalar(128, 0, 128),   // 深品红色
    cv::Scalar(192, 192, 192), // 银色
    cv::Scalar(255, 165, 0),   // 橙色
    cv::Scalar(255, 20, 147),  // 粉红色
    cv::Scalar(75, 0, 130),    // 靛蓝色
    cv::Scalar(173, 255, 47),  // 黄绿色
    cv::Scalar(220, 20, 60),   // 猩红色
    cv::Scalar(30, 144, 255),  // 道奇蓝
    cv::Scalar(255, 105, 180), // 热粉红色
    cv::Scalar(72, 209, 204),  // 适中的青色
    cv::Scalar(128, 0, 0),     // 栗色
    cv::Scalar(0, 255, 127),   // 春绿色
    cv::Scalar(210, 105, 30),  // 巧克力色
    cv::Scalar(139, 69, 19),   // 马鞍棕色
    cv::Scalar(255, 69, 0)     // 橙红色
};

int FovPlan::DoFovPlan(FovPlanOutput& fov_out, const FovPlanParam& param)
{
    /*位置排序*/
    if (0 != PositionPlan(fov_out, param))
    {
        PrintInfo("位置规划异常");
        return 1;
    }
    PrintInfo("位置规划完成:共" + std::to_string(fov_out.fovs.size()) + "个FVO");

    /*路径排序*/
    if (0 != PathPlan(fov_out.fovs, param))
    {
        PrintInfo("路径规划异常");
        return 2;
    }
    PrintInfo("路径规划完成");

    fov_out.fov_path.resize(fov_out.fovs.size());//! TODO: 后期修改，丢弃
    fov_out.fov_path_with_id.resize(fov_out.fovs.size());
    for (int i = 0; i < fov_out.fovs.size(); ++i)
    {
        auto& fov = fov_out.fovs[i];
        fov_out.fov_path[i] = fov.center;

        CalcFov::SeparateFovPath fov_with_id_temp;
        fov_with_id_temp.center = fov.center;
        fov_with_id_temp.fovid = fov.fovid;
        fov_out.fov_path_with_id[i] = fov_with_id_temp;
    }
    return 0;
}

int FovPlan::TestFovPlan(const FovPlanParam& param)
{
    //const auto& rrs = param.rrs;
    //const auto& x = param.region_x;
    //const auto& y = param.region_y;
    const auto& w = param.region_w;
    const auto& h = param.region_h;
    const auto& fov_w = param.fov_w;
    const auto& fov_h = param.fov_h;
    //const auto& gap_w = param.gap_w;
    //const auto& gap_h = param.gap_h;
    //const auto& mode_position = param.mode_position;
    FovPlanOutput fov_out;
    DoFovPlan(fov_out, param);
    auto& new_centers = fov_out.fov_path;
    auto& fovs = fov_out.fovs;

    /*输出*/
    const float half_fov_w = fov_w * 0.5f;
    const float half_fov_h = fov_h * 0.5f;
    cv::Mat tfm = cv::Mat::zeros({ w, h }, CV_8UC3);

    for (int i = 0; i < fovs.size(); ++i)
    {
        const auto& point = fovs[i].center;
        const auto& sColor = vecColor[(i + 1) % vecColor.size()];
        cv::Rect rfov((int)(point.x - half_fov_w), (int)(point.y - half_fov_h), fov_w, fov_h);
        /*绘制覆盖矩形*/
        CalcFov::drawRect(tfm, rfov, sColor, 43);
        /*绘制包含矩形*/
        for (const auto& cr : fovs[i].coveredRectangles)
        {
            for (auto& obj : param.rrs)
            {
                if (obj.id == cr.id)
                {
                    CalcFov::drawRect(tfm, obj.rr.boundingRect(), sColor, -1);
                }
            }
        }
    }
    /*绘制路径*/
    for (int i = 0; i < new_centers.size(); ++i)
    {
        cv::circle(tfm, new_centers[i], 65, { 0, 255, 0 }, -1);
        if (i == 0)
            continue;
        cv::line(tfm, new_centers[i], new_centers[i - 1], { 0, 255, 0 }, 55);
    }
    CalcFov::resizeImagePreserveAspectRatio(tfm, tfm, 5000, 5000);
    if (!tfm.empty())
    {
        cv::imwrite("__CalcFov_test.jpg", tfm);
    }
    PrintInfo("FOV测试图片输出完成");


    return 0;
}

int FovPlan::CreateDebugFile(const FovPlanParam& param)
{
    const auto& rrs = param.rrs;
    //const auto& x = param.region_x;
    //const auto& y = param.region_y;
    const auto& w = param.region_w;
    const auto& h = param.region_h;
    //const auto& fov_w = param.fov_w;
    //const auto& fov_h = param.fov_h;
    //const auto& gap_w = param.gap_w;
    //const auto& gap_h = param.gap_h;
    //const auto& pStart = param.start;


    std::vector<cv::Rect> vecR(rrs.size());
    for (int i = 0; i < rrs.size(); ++i)
    {
        auto& rr = rrs[i];
        vecR[i] = (rr.rr.boundingRect());
    }
    std::string filename = std::string("__CalcFov_debug.txt");
    std::ofstream ofs(filename);

    if (!ofs.is_open())
    {
        std::cerr << "Error opening file for writing: " << filename << std::endl;
        return 1;
    }
    ofs << h << " " << w << "\n";
    for (const auto& rect : vecR)
    {
        ofs << rect.x << " " << rect.y << " " << rect.width << " " << rect.height << "\n";
    }

    ofs.close();
    PrintInfo("FOV调试文件输出完成");
    return 0;
}

void FovPlan::PrintInfo(const std::string& info)
{
    std::cout << "info:" << info << "\n";
}

int FovPlan::PositionPlan(FovPlanOutput& fov_out, const FovPlanParam& param)
{
    const auto& rrs = param.rrs;
    const auto& x = param.region_x;
    const auto& y = param.region_y;
    const auto& w = param.region_w;
    const auto& h = param.region_h;
    const auto& fov_w = param.fov_w;
    const auto& fov_h = param.fov_h;
    const auto& gap_w = param.gap_w;
    const auto& gap_h = param.gap_h;
    const auto& mode_position = param.mode_position;
    cv::Rect region(x, y, w, h);
    switch (mode_position)
    {
    case ROW_MAJOR_MBR:
    {
        CalcFov::FindMinRectRecoverAll0(fov_out.fovs, fov_out.objects, rrs, region, fov_w - gap_w, fov_h - gap_h);
        // fov_out.fov_path.reserve(fov_out.fovs.size());
        // for (const auto& tf : fov_out.fovs)
        // {
        //     fov_out.fov_path.emplace_back(tf.center);
        // }
    }
    break;
    case GRID:
    {
        std::vector<cv::Point2f> path;
        CalcFov::FindRectRecoverAllGrid(path, region, fov_w, fov_h, gap_w, gap_h);

        std::vector<CalcFov::Fov> fovs;
        fovs.reserve(path.size());

        std::unordered_map<std::string, bool> bool_visit;
        for (auto& p : path)
        {
            CalcFov::Fov f;
            f.fovid = (int)fovs.size();
            f.center = p;

            fovs.emplace_back(f);
        }
        if (!rrs.empty())
        {
            for (auto& fov : fovs)
            {
                cv::Rect fovrect((int)(fov.center.x - fov_w * 0.5f), (int)(fov.center.y - fov_h * 0.5f), fov_w, fov_h);
                for (const auto& rr : rrs)
                {
                    if (bool_visit.find(rr.id) != bool_visit.end())
                        continue;
                    if (CalcFov::isRectInRect(rr.rr.boundingRect(), fovrect))
                    {
                        CalcFov::ObjectOutput oo;
                        oo.id = rr.id;
                        oo.fovid = fov.fovid;
                        fov.coveredRectangles.emplace_back(oo);
                        bool_visit[rr.id] = true;
                    }
                }
            }
        }
        fov_out.fovs.swap(fovs);
    }
    break;
    default:
        return 1;
    }
    return 0;
}

int FovPlan::PathPlan(std::vector<CalcFov::Fov>& fovs, const FovPlanParam& param)
{
    switch (PathPlanMode(param.mode_path))
    {
    case SNAKE_TOP_TO_BOTTOM:
    case SNAKE_LEFT_TO_RIGHT:
    case SNAKE_BOTTOM_TO_TOP:
    case SNAKE_RIGHT_TO_LEFT:
    {
        const auto& fov_w = param.fov_w;
        const auto& fov_h = param.fov_h;

        CalcFov::SortFovs(fovs, param.mode_path, (int)std::min(fov_w * 0.4, fov_h * 0.4));
    }
    break;
    default:
        return 1;
    }
    return 0;
}
