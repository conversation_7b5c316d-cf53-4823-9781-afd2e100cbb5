﻿#pragma once
#ifndef READ_CAD_FILE_HPP
#define READ_CAD_FILE_HPP
#include <QFile>
#include <QTextStream>
#include <QTextCodec>
#include <QDebug>
#include <vector>
#include <string>
#include "uchardet.h"

class ReadCadFile
{
public:
    ReadCadFile() {};
    ~ReadCadFile() {};

public:
    /**
     * @fun LoadAndUseUchardet 
     * @brief 根据输出的datas判断编码格式，通过encode输出
     * @param datas 输入数据
     * @param encode 编码格式
     * @return 成功判断
     * @date 2025.4.22
     * <AUTHOR>
     */
    bool LoadAndUseUchardet(std::string datas, std::string& encode)
    {
        encode = "";
        
        // 使用 uchardet 函数
        auto detector = uchardet_new();
        if (!detector) {
            qDebug() << "Failed to create uchardet detector";
            return false;
        }
        uchardet_handle_data(detector, datas.c_str(), int(datas.length()));
        uchardet_data_end(detector);
        encode = uchardet_get_charset(detector);
        uchardet_delete(detector);
        return true;
    }
    /**
     * @fun CanEncode 
     * @brief 编码格式是否支持读取
     * @param encode 编码格式
     * @return  支持读取
     * @date 2025.4.22
     * <AUTHOR>
     */
    bool CanEncode(std::string& encode)
    {
        bool encode_enable = false;
        if (QString::compare(encode.c_str(), "ASCII", Qt::CaseInsensitive) == 0)
        {
            encode = "UTF-8";
            encode_enable = true;
        }
        else if (QString::compare(encode.c_str(), "UTF-8", Qt::CaseInsensitive) == 0)
        {
            encode_enable = true;
        }
        else if (QString::compare(encode.c_str(), "UTF-16", Qt::CaseInsensitive) == 0)
        {
            encode_enable = true;
        }
        if (!encode_enable)
        {
            std::cerr << "无法转换的文件格式: " << encode << std::endl;
            return false;
        }
        return true;
    }
    /**
     * @fun ReadFileDatas
     * @brief  根据编码格式读取文本，转换成UTF-8格式
     * @param file_name 文件名
     * @param encode 编码格式
     * @param file_datas 读取到的数据输出
     * @return 成功读取并非空
     * @date 2025.4.22
     * <AUTHOR>
     */
    bool ReadFileDatas(const std::string& file_name, std::string& encode, std::vector<std::string>& file_datas)
    {
        file_datas.clear();
        if (!CanEncode(encode))
        {
            return false;
        }
        QFile file(file_name.c_str());
        if (!file.open(QIODevice::ReadOnly)) {
            std::cerr << "无法打开文件: " << file_name.c_str() << std::endl;
            return false;
        }

        QByteArray file_data = file.readAll();
        file.close();

        // 检查 file_data 是否为空
        if (file_data.isEmpty()) {
            qDebug() << "File is empty or read error occurred.";
            return false;
        }

        QTextCodec* codec = QTextCodec::codecForName(encode.c_str());
        if (!codec) {
            qDebug() << "Failed to find codec for detected charset:" << encode.c_str();
            return false;
        }

        QString text = codec->toUnicode(file_data);
        QStringList data_list;
        if (text.contains(" \r\n"))
        {
            text.remove(' ');
        }
        else if (text.contains(" \n"))
        {
            text.remove(' ');
        }
        if (text.contains("\r\n"))
        {
            data_list = text.split("\r\n");
        }
        else if (text.contains("\n"))
        {
            data_list = text.split("\n");
        }
        for (auto one_data : data_list)
        {
            if (!one_data.isEmpty())
            {
                file_datas.push_back(one_data.toUtf8().constData());
            }
        }
        if (file_datas.size() < 1)
        {
            return false;
        }
        return true;
    }
};
#endif