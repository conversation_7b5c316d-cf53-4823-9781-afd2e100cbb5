///*****************************************************************
// * @file   executeflow.h
// * @brief  执行运行流程 (丢弃使用2024.12.1) 
// * @details
// * <AUTHOR>
// * @date 2024.10.24
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.10.24          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
// * </table>
// * @copyright 版权 CopyRight (C), 2024-2025.
// *********************************************************************/
//
//#ifndef __JRSEXECUTEFLOW_H__
//#define __JRSEXECUTEFLOW_H__
// //STD
//#include <iostream>
//#include <functional>
//#include <thread>
//#include <mutex>
//#include <condition_variable>
////Custom
//#include "updownmaterial.h"
//#include "fovplan.h"
//
////Third
//namespace jrslogic
//{
//    class JrsAoiImgsManager;
//}
//
//namespace jrsdata
//{
//    struct JrsImageBuffer;
//    struct ProjectParam;
//}
//namespace jrsalgo
//{
//    class AlgorithmEngineManager;
//}
//namespace jrsworkflow
//{
//
//    class IExecuteFlow
//    {
//    public:
//        virtual ~IExecuteFlow() = default;
//
//        /**
//         * @fun StartWorkFlow
//         * @brief 启动执行流程
//         * @param project_param_ [IN] 工程参数
//         * @return 是否执行成功
//         * <AUTHOR>
//         * @date 2024.11.1
//         */
//        virtual int StartWorkFlow(const std::shared_ptr<jrsdata::ProjectParam>& project_param_) = 0;
//
//        /**
//         * @fun StopWorkFlow
//         * @brief 停止工作流程
//         * @return  AOI_OK成功，否则失败
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        virtual int StopWorkFlow() = 0;
//
//
//        /**
//         * @fun AddBuffer
//         * @brief 添加单个FOV的图片
//         * @param imgs [IN] 单个FOV的图片
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        virtual void AddBuffer(const jrsdata::JrsImageBuffer& imgs) = 0;
//
//        virtual void SetLogicInvokeFun(std::function<void(const std::vector<CalcFov::SeparateFovPath>&)> logic_invoke_) = 0;
//
//    };
//
//    class ExecuteFlow :public IExecuteFlow
//    {
//    public:
//        ExecuteFlow(const jrsdevice::DeviceManagerPtr& device_ptr_, const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_);
//        ~ExecuteFlow();
//        int StartWorkFlow(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)override;
//        int StopWorkFlow()override;
//        void AddBuffer(const jrsdata::JrsImageBuffer& imgs)override;
//
//        /**
//        * @fun SetLogicInvokeFun
//        * @brief 外部逻辑层处理回调函数
//        * @param logic_invoke_ [IN] 外部回调函数
//        * <AUTHOR>
//        * @date 2024.11.3
//        */
//        void SetLogicInvokeFun(std::function<void(const std::vector<CalcFov::SeparateFovPath>&)> logic_invoke_) override;
//    private:
//        //Fun
//        void InitMember();
//
//        /**
//         * @fun Execute
//         * @brief 执行流程
//         * @param project_param_ [IN] 工程参数
//         * @return
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        int Execute(const std::shared_ptr<jrsdata::ProjectParam>& project_param_);
//
//        /**
//         * @fun MarkCorrection
//         * @brief MARK 位置矫正
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        void MarkCorrection();
//
//        /**
//         * @fun Detect
//         * @brief 检测元件
//         * @param imgs [IN] 单个FOV图片
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        void Detect(const jrsdata::JrsImageBuffer& imgs);
//
//        /**
//         * @fun AssignComponentToFov
//         * @brief 将元件分配到指定的 FOV中
//         * @param fov_centers
//         * @param board
//         * @param fov_w
//         * @param fov_h
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        void AssignComponentToFov(std::vector<CalcFov::SeparateFovPath>& fov_centers, jrsdata::Board& board, const int& fov_w, const int& fov_h);
//
//        /**
//         * @fun CalcComponentBoundingbox
//         * @brief 计算板子上每个元件的所有算法检测框的最小外接矩形
//         * @param x [OUT] 计算出的最小外接矩形的X坐标
//         * @param y [OUT] 计算出的最小外接矩形的Y坐标
//         * @param width [OUT] 计算出的最小外接矩形的宽
//         * @param height[OUT] 计算出的最小外接矩形的高
//         * @param component [IN] 当前的元件信息
//         * @param part [IN] 当前的元件元件所在料号信息
//         * <AUTHOR>
//         * @date 2024.11.4
//         */
//        void CalcComponentBoundingbox(int& x, int& y, int& width, int& height, const jrsdata::Component& component, const jrsdata::PNDetectInfo& part);
//
//        /**
//         * @fun ReadPartRef
//         * @brief 读取料号信息
//         * @param board [IN] 板子信息
//         * @param part_name  [料号名称]
//         * @return
//         * <AUTHOR>
//         * @date 2024.11.3
//         */
//        std::optional<std::reference_wrapper<const jrsdata::PNDetectInfo>> ReadPartRef(const jrsdata::Board& board, const std::string& part_name);
//
//        private:
//            //Member
//            std::function<void( const std::vector<CalcFov::SeparateFovPath>& )> logic_invoke;
//            UpDownMaterialPtr up_down_material; /**< 上下料实例*/
//            jrsdevice::DeviceManagerPtr device_ptr; /**< 设备实例*/
//            std::shared_ptr<jrsalgo::AlgorithmEngineManager> algo_engine_manager_ptr; /**< 算法引擎实例*/
//            std::shared_ptr<jrsdata::ProjectParam> project_param_ptr;/**< 工程参数实例*/
//            std::thread work_flow_thread; /**< 整体流程线程*/
//            std::mutex work_flow_mtx;     /**< 整体流程锁*/
//            std::mutex detect_mtx;     /**< 检测锁*/
//            std::condition_variable work_flow_cv; /**<整体流程条件变量*/
//            std::atomic<bool> is_wating_up; /**< 是否在等待上料*/
//            std::atomic<bool> is_wating_down;/**<是否在等待下料*/
//            std::atomic<bool> is_scanning;/**< 是否在扫图流程中 */
//            std::atomic<bool> is_running;/**< 是否在流程中*/
//
//            std::unordered_map<int/*FOV的ID*/ , std::vector<jrsdata::Component>/*当前FOV中的所有元件*/> assign_component_res;/**< 元件在哪个FOV中的分配结果*/
//            FovPlan::FovPlanOutput fov_out; /**< fov 规划的结果*/
//        };
//        using ExecuteFlowPtr = std::shared_ptr<ExecuteFlow>;
//        using IExecuteFlowPtr = std::shared_ptr<IExecuteFlow>;
//
//}
//
//
//#endif // !__JRSEXECUTEFLOW_H__
//
