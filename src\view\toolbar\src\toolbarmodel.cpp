#include "toolbarmodel.h"

namespace jrsaoi
{

    ToolBarModel::ToolBarModel (const std::string& name) :ModelBase (name)
    {

    }
    ToolBarModel::~ToolBarModel ()
    {

    }
    int ToolBarModel::Save (const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    int ToolBarModel::Update (const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
 
}
