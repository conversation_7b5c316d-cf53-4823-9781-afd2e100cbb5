﻿#include "addpadview.h"
#include "ui_addpadview.h"

// Custom
#include "datadefine.hpp"
#include "padgroup.h"

namespace jrsaoi
{
    AddPadView::AddPadView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::AddPadView)
    {
        ui->setupUi(this);
        Init();
    }
    AddPadView::~AddPadView()
    {
    }
    void  AddPadView::SlotUpdateAddView(std::shared_ptr<GraphicsAbstract> graphics_)
    {
        auto pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(graphics_);
        if (pad_group->GetGroupType() == PadGraphicsGroup::PadGroupType::ARRAY)
        {
            ui->tabWidget->removeTab(1);
        }
        else if (pad_group->GetGroupType() == PadGraphicsGroup::PadGroupType::MATRIX)
        {
            ui->tabWidget->removeTab(0);
        }
        else
        {
            JRSMessageBox_INFO("提示", "只有单排和阵列才能编辑", jrscore::MessageButton::Ok);
            ui->tabWidget->setEnabled(false);
            return;
        }
        ui->tabWidget->setEnabled(true);
        if (pad_group)
        {
            auto sub_pads = pad_group->_sub_graphics;
            ui->pad_number->setValue((int)sub_pads.size());
        }

    }

    int AddPadView::Init()
    {
        InitView();
        InitConnect();
        return 0;
    }
    int AddPadView::InitConnect()
    {
        connect(ui->btn_pad_preview, &QPushButton::clicked, this, [&]() {
            emit SigPadNumber(ui->pad_number->value());
            });
        connect(ui->btn_pad_generate, &QPushButton::clicked, this, [&]() {
            ui->btn_pad_preview->click();
            this->close();
            });
        return 0;
    }
    int AddPadView::InitView()
    {
        this->setWindowFlags(this->windowFlags() | Qt::Tool | Qt::WindowStaysOnTopHint);

        ui->pad_number->setRange(2, 100);
        return 0;
    }
}
