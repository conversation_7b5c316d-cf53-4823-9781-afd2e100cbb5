﻿/*****************************************************************
 * @file   messagemanager.h
 * @brief  提示框
 * @details
 * <AUTHOR>
 * @date 2024.12.4
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.12.4          <td>V2.0              <td>HJC      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
 //Third
#ifndef MESSAGE_MANAGER_H
#define MESSAGE_MANAGER_H
//custom
#include "jrslogtypedefines.h"
#include "parambase.hpp"
#include "pluginexport.hpp"
//std
#include <iostream>
#include <future>
namespace jrscore
{
    /** 提示框 */
    enum MessageButton
    {
        NoButton = 0x00000000,
        Ok = 0x00000400,
        Save = 0x00000800,
        Yes = 0x00004000,
        No = 0x00010000,
        Retry = 0x00080000,
        Ignore = 0x00100000,
        Close = 0x00200000,
        Cancel = 0x00400000,
        Discard = 0x00800000,
        Apply = 0x02000000,
        Reset = 0x04000000,
    };
    using MessageCallBack = std::function<jrscore::MessageButton(const jrscore::LogLevel& level_, const std::string& title_, const std::string& msg_, const int& message_btn_)>;

    class JRS_AOI_PLUGIN_API MessageManager
    {
    public:
        MessageManager();
        ~MessageManager();
        /**
         * @fun ShowMessageBox
         * @brief 显示 提示框信息
         * @param level_
         * @param title_
         * @param msg_
         * @param message_btn_ 使用 jrscore::MessageButton::OK|jrscore::MessageButton::Cancel; 在弹框中显示多个按钮
         * @return
         * <AUTHOR>
         * @date 2024.12.4
         */
        jrscore::MessageButton ShowMessageBox(const jrscore::LogLevel& level_, const std::string& title_, const std::string& msg_, const int& message_btn_);
        /**
         * @fun SetMessageCallBack
         * @brief  设置messageBox 回调
         * @param callback_
         * <AUTHOR>
         * @date 2024.12.4
         */
        void SetMessageCallBack(MessageCallBack callback_);
    private:
        MessageCallBack _callback;
    };
    using MessageManagerPtr = std::shared_ptr<MessageManager>;

}



#endif //MESSAGE_MANAGER_H
