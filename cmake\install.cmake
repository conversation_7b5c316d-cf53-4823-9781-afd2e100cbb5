# 设置安装目录为构建目录下的一个子目录，而不是源代码目录
set(CMAKE_INSTALL_PREFIX ${CMAKE_BINARY_DIR}/bin/publish)
# 设置版本号和安装路径
set(VERSION_DIR "${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}.${VERSION_BUILD}")
set(CMAKE_INSTALL_PREFIX "${DIR_PROJECT_CURRENT}bin/publish/files")

# 安装装生成的dll
install(TARGETS JRSAOI 
        RUNTIME DESTINATION .
        LIBRARY DESTINATION .
)

# 打包dll
install(DIRECTORY "${DIR_PROJECT_CURRENT}/bin/$<CONFIG>/"
        DESTINATION .
        FILES_MATCHING
        PATTERN "*.dll"
        PATTERN "opencv_world481d.dll" EXCLUDE
        PATTERN "opencv_world4100d.dll" EXCLUDE
        PATTERN "opencv_world481.dll" EXCLUDE
        PATTERN "opencv_world4100.dll" EXCLUDE
        PATTERN "*////" EXCLUDE
)
# 打包pdb文件
install(DIRECTORY "${DIR_PROJECT_CURRENT}/bin/$<CONFIG>/"
        DESTINATION .
        FILES_MATCHING
        PATTERN "*.pdb"
)
# # 拷贝算法文件
install(DIRECTORY ${DIR_PROJECT_CURRENT}/bin/$<CONFIG>/config/algo/algoplugin/
    DESTINATION config/algo/algoplugin/
    PATTERN ".git" EXCLUDE
    PATTERN ".vscode" EXCLUDE
)
# # 拷贝算法权重文件
install(DIRECTORY ${DIR_PROJECT_CURRENT}/bin/$<CONFIG>/weights/
    DESTINATION weights/
)
#######Cpack################
set(CPACK_PROJECT_NAME "ARP2.0")
set(CPACK_INCLUDE_REQUIRED_LIBRARIES OFF)
set(CPACK_INSTALL_REQUIRED_SYSTEM_LIBRARIES_DESTINATION "${CMAKE_INSTALL_PREFIX}")
include(InstallRequiredSystemLibraries)

# 设置 CPack 相关变量
set(CPACK_PACKAGE_NAME "${CPACK_PROJECT_NAME}_${VERSION_DIR}")
set(CPACK_PACKAGE_VERSION "${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_PATCH}.${VERSION_BUILD}")
set(CPACK_GENERATOR "NSIS")
# 设置安装包图标
set(CPACK_NSIS_MUI_ICON "${DIR_PROJECT_CURRENT}/jrsresource/icon/image/JRS.ico")
set(CPACK_NSIS_INSTALLED_ICON_NAME "JRS.ico")
set(CPACK_NSIS_MODIFY_PATH ON)  # 允许添加到系统 PATH
set(CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL ON)  # 安装前自动卸载旧版本
set(CPACK_PACKAGE_DESCRIPTION "JRS Company JRSAOI")
set(CPACK_PACKAGE_VENDOR "${COMPANY_NAME}")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")
set(CPACK_PACKAGE_FILE_NAME "${CPACK_PACKAGE_NAME}") 
set(CPACK_NSIS_INSTALL_ROOT "D:\\\\JRS")
set(CPACK_PACKAGE_INSTALL_DIRECTORY "${CPACK_PROJECT_NAME}")
set(CPACK_PACKAGE_DIRECTORY "${DIR_PROJECT_CURRENT}bin/publish/installpackage")

# 安装项目配置
set(CPACK_INSTALL_CMAKE_PROJECTS
    "${CMAKE_BINARY_DIR};${CPACK_PROJECT_NAME};ALL;/"
)

# 启用创建桌面快捷方式
set(CPACK_NSIS_CREATE_DESKTOP_SHORTCUT "ON")
set(CPACK_NSIS_DESKTOP_SHORTCUT_NAME "JRSAOI")
set(CPACK_NSIS_DESKTOP_SHORTCUT_TARGET "$INSTDIR\\\\JRSAOI.exe")
set(CPACK_NSIS_MENU_LINKS "JRSAOI.exe;ARP2.0")
set(CPACK_NSIS_SHORTCUTS "ARP2.0;$INSTDIR\\\\JRSAOI.exe")
set(CPACK_NSIS_EXTRA_INSTALL_COMMANDS "
CreateShortCut \$DESKTOP\\\\ARP2.0.lnk \$INSTDIR\\\\JRSAOI.exe
")
# 包含动态库和可执行文件
set(CPACK_INCLUDE_TOPLEVEL_DIRECTORY OFF)
# 安装程序版本
set(CPACK_PACKAGE_VERSION_MAJOR ${VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${VERSION_PATCH})

# 包含系统库
include(CPack)



