#include "filehandle.h"
#pragma warning(push, 2)
#include "csv.h"
#pragma warning(pop)

jrsfiledata::FileHandle::FileHandle()
{
}

jrsfiledata::FileHandle::~FileHandle()
{
}

int jrsfiledata::FileHandle::Save(const cv::Mat& img_, const std::string& file_path_)
{
    if (img_.empty()) {
        Log_Error_Stack("传入数据为空");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    std::filesystem::path filePath(file_path_);
    if (!std::filesystem::exists(filePath.parent_path())) {
        std::filesystem::create_directories(filePath.parent_path());
    }

    try
    {
        if (!cv::imwrite(file_path_, img_))
        {
            Log_Error_Stack("Failed to save image to ", file_path_);
            return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
        }
    }
    catch (const std::exception&)
    {
        return jrscore::DataManagerError::E_AOI_DB_CREATE_DIR_FAILURE;
    }


    return jrscore::AOI_OK;
}

int jrsfiledata::FileHandle::Read(cv::Mat& img_, const std::string& file_path_)
{
    if (!std::filesystem::exists(file_path_)) {
        Log_Error_Stack("图片路径", file_path_, "不存在");
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }

    if (file_path_.find(".tif") != std::string::npos)
    {
        try
        {

            img_ = cv::imread(file_path_, cv::IMREAD_UNCHANGED);
        }
        catch (const std::exception& e)
        {
            Log_Error_Stack("未成功加载图片，请检查图片路径，或图片是否损坏:", e.what());
            return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
        }
        return jrscore::AOI_OK;
    }
    try
    {
        img_ = cv::imread(file_path_);

    }
    catch (const std::exception& e)
    {
        Log_Error_Stack("未成功加载图片，请检查图片路径，或图片是否损坏:", e.what());
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    if (img_.empty()) {
        Log_Error_Stack("未成功加载图片，请检查图片路径，或图片是否损坏", file_path_);
        return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
    }
    return jrscore::AOI_OK;
}

int jrsfiledata::FileHandle::WriteMatToBinary(const cv::Mat& mat, const std::string& filename)
{
    try {
        if (mat.empty() || mat.data == nullptr)
        {
            Log_ERROR("输入图片为空或未初始化", filename);
            return -1;
        }


        std::filesystem::path filePath(filename);
        if (!std::filesystem::exists(filePath.parent_path()))
        {
            std::filesystem::create_directories(filePath.parent_path());
            Log_INFO("创建目录", filePath.parent_path().string());
        }

        std::ofstream ofs(filename, std::ios::binary);
        if (!ofs.is_open())
        {
            Log_ERROR("打开", filename, "文件失败");
            return -1;
        }

        // 写入矩阵的尺寸（行、列）和类型（深度和通道数）
        int rows = mat.rows;
        int cols = mat.cols;
        int type = mat.type();

        ofs.write(reinterpret_cast<char*>(&rows), sizeof(rows));
        ofs.write(reinterpret_cast<char*>(&cols), sizeof(cols));
        ofs.write(reinterpret_cast<char*>(&type), sizeof(type)); // 保存类型信息

        // 确保数据连续
        cv::Mat save_mat;
        if (!mat.isContinuous())
        {
            save_mat = mat.clone();
            Log_INFO("输入图像非连续，已强制转换为连续图像");
        }
        else
        {
            save_mat = mat;
        }

        //写入数据
        size_t mat_size = save_mat.total() * save_mat.elemSize();
        ofs.write(reinterpret_cast<const char*>(save_mat.data), mat_size);
        ofs.close();
        Log_INFO("图片保存成功, 文件大小：", std::to_string(mat_size), "文件名称：", filename);
        return jrscore::AOI_OK;
    }
    catch (const std::exception& ex)
    {
        Log_ERROR("异常发生: " + std::string(ex.what()), filename);
        return -1;
    }
}

int jrsfiledata::FileHandle::ReadMatFromBinary(cv::Mat& mat, const std::string& filename)
{
    try
    {
        std::ifstream ifs(filename, std::ios::binary);
        if (!ifs.is_open())
        {
            Log_ERROR("打开", filename, "文件失败");
            return -1;
        }

        int rows, cols, type;
        ifs.read(reinterpret_cast<char*>(&rows), sizeof(rows));
        ifs.read(reinterpret_cast<char*>(&cols), sizeof(cols));
        ifs.read(reinterpret_cast<char*>(&type), sizeof(type));

        if (rows <= 0 || cols <= 0 || type < 0)
        {
            Log_ERROR("读取到无效的尺寸或类型", filename);
            return -1;
        }

        mat.create(rows, cols, type);
        size_t mat_size = mat.total() * mat.elemSize();
        ifs.read(reinterpret_cast<char*>(mat.data), mat_size);

        if (!ifs)
        {
            Log_ERROR("读取数据不完整", filename);
            return -1;
        }

        ifs.close();
        Log_INFO("图片读取成功", filename);
        return jrscore::AOI_OK;
    }
    catch (const std::exception& ex)
    {
        Log_ERROR("异常发生: " + std::string(ex.what()), filename);
        return -1;
    }
}

int jrsfiledata::FileHandle::SaveString(const std::string& path_, const std::string& content_)
{
    return WriteFile(path_, content_);
}

bool jrsfiledata::FileHandle::WriteFile(const std::string& path_, const std::string& content_)
{
    {
        std::filesystem::path filePath(path_);
        if (!std::filesystem::exists(filePath.parent_path())) {
            std::filesystem::create_directories(filePath.parent_path());
        }
        std::ofstream outFile(filePath, std::ios::binary);
        if (outFile) {
            outFile << content_;
            outFile.close();
            //Log_INFO("参数保存成功。");
            return true;
        }
        else {
            Log_INFO("创建或打开文件失败,请检查。", filePath);
            return false;
        }
    }
}

bool jrsfiledata::FileHandle::ReadFile(const std::string& path, std::string& content)
{
    // 检查文件是否存在
    if (!std::filesystem::exists(path)) {
        std::cerr << "File does not exist: " << path << std::endl;
        return false;
    }

    // 打开文件并读取内容
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << path << std::endl;
        return false;
    }
    content.assign((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    return true;
}

