﻿/*********************************************************************
 * @brief  常用随机数函数.
 *
 * @file   randomgenerator.h
 *
 * @date   2024.08.01
 * <AUTHOR>
 *********************************************************************/
#pragma once
#ifndef JTOOLS_RANDOMGENERATOR_H
#define JTOOLS_RANDOMGENERATOR_H
#include "jtoolsapi.hpp"

#include <random>
#include <vector>

namespace jtools
{
    class JT_EXPORTS RandomGenerator
    {
    public:
        /**
         * @fun    GetRandomEngine
         * @brief  获取随机数引擎.
         * @return 随机数引擎
         * @date   2024.01.12
         * <AUTHOR>
         */
        static std::mt19937 &GetRandomEngine();

        /**
         * @fun    GetRandomInt
         * @brief  获取一个指定范围内的随机整数.
         * @param  min 最小值（包含）
         * @param  max 最大值（包含）
         * @return 随机整数
         * @date   2024.01.12
         * <AUTHOR>
         */
        static int GetRandomInt(int min, int max);

        /**
         * @fun    GetRandomFloat
         * @brief  获取一个指定范围内的随机浮点数.
         * @param  min 最小值（包含）
         * @param  max 最大值（包含）
         * @return 随机浮点数
         * @date   2024.01.12
         * <AUTHOR>
         */
        static double GetRandomFloat(double min, double max);

        /**
         * @fun    GetRandomBool
         * @brief  根据概率获取随机布尔值.
         * @param  probability 概率，取值范围为 [0, 1]，表示 true 出现的概率
         * @return 随机布尔值
         *
         * @note   输入[0,1]以外的值时,<0 指定为0,>1 指定为1
         *
         * @date   2024.01.12
         * <AUTHOR>
         */
        static bool GetRandomBool(float probability);

        /**
         * @fun    GetRandomString
         * @brief  获取一个随机字符串，由给定字符串的随机排列组成.
         * @param  str 原始字符串
         * @return 随机字符串
         * @date   2024.01.12
         * <AUTHOR>
         */
        static std::string GetRandomString(const std::string &str);

        /**
         * @fun    GetRandomPointInCircle
         * @brief  在给定圆形区域内获取一个随机点.
         * @param  center_x 圆心 x 坐标
         * @param  center_y 圆心 y 坐标
         * @param  radius   圆半径
         * @return 随机点的坐标 (x, y)
         * @date   2024.01.12
         * <AUTHOR>
         */
        static std::pair<float, float> GetRandomPointInCircle(float center_x, float center_y, float radius);

        /**
         * @fun    GetRandomPointInRect
         * @brief  在给定矩形区域内获取一个随机点.
         * @param  left   矩形左上角 x 坐标
         * @param  top    矩形左上角 y 坐标
         * @param  right  矩形右下角 x 坐标
         * @param  bottom 矩形右下角 y 坐标
         * @return 随机点的坐标 (x, y)
         * @date   2024.01.12
         * <AUTHOR>
         */
        static std::pair<float, float> GetRandomPointInRect(float left, float top, float right, float bottom);

        /**
         * @fun    GetRandomColor
         * @brief  获取一个随机颜色字符串，表示颜色的十六进制值.
         * @return 随机颜色字符串
         * @date   2024.01.12
         * <AUTHOR>
         */
        static std::string GetRandomColor();
    };
}

#endif // JTOOLS_RANDOMGENERATOR_H