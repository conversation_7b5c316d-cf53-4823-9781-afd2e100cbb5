﻿/*********************************************************************
 * @brief  简单的向量.
 *
 * @file   rvec.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef RMATH_H
#define RMATH_H
#include <math.h>

#if defined(__STDC__) || defined(__cplusplus) || defined(_MSC_VER) || defined(__BORLANDC__) || defined(__TURBOC__)
#include <climits>
#if defined(UCHAR_MAX) && (UCHAR_MAX == 0xFF)
typedef signed char int8;
typedef unsigned char uint8;
#endif

#if defined(USHRT_MAX) && (USHRT_MAX == 0xFFFF)
typedef signed short int16;
typedef unsigned short uint16;
#elif defined(UINT_MAX) && (UINT_MAX == 0xFFFF)
typedef signed int int16;
typedef unsigned int uint16;
#endif

#if defined(UINT_MAX) && (UINT_MAX == 0xFFFFFFFFUL)
typedef signed int int32;
typedef unsigned int uint32;
#elif defined(ULONG_MAX) && (ULONG_MAX == 0xFFFFFFFFUL)
typedef signed long int32;
typedef unsigned long uint32;
#endif
#endif

/**
 * @brief  2维向量.
 */
class Vec2
{
public:
    float x;
    float y;

public:
    Vec2() : x(0), y(0) {}
    Vec2(float nx, float ny) : x(nx), y(ny) {}
    Vec2(int nx, int ny) : x((float)nx), y((float)ny) {}

    bool isnull() const
    {
        return x == 0 && y == 0;
    }

    void set(float nx, float ny)
    {
        x = nx;
        y = ny;
    }

    float dot(const Vec2& v) const
    {
        return x * v.x + y * v.y;
    }

    float length() const
    {
        return sqrtf(x * x + y * y);
    }

    const Vec2& normalize()
    {
        if (this->length() == 0)
            return *this;

        float n = 1 / this->length();
        x *= n;
        y *= n;
        return *this;
    }
};

typedef Vec2 Size;

/* + */
inline Vec2 operator+(const Vec2& v1, const Vec2& v2)
{
    return Vec2(v1.x + v2.x, v1.y + v2.y);
}
inline Vec2 operator+(float s, const Vec2& v)
{
    return Vec2(v.x + s, v.y + s);
}
inline Vec2 operator+(const Vec2& v, float s)
{
    return Vec2(v.x + s, v.y + s);
}

/* - */
inline Vec2 operator-(const Vec2& v1, const Vec2& v2)
{
    return Vec2(v1.x - v2.x, v1.y - v2.y);
}
inline Vec2 operator-(float s, const Vec2& v)
{
    return Vec2(s - v.x, s - v.y);
}
inline Vec2 operator-(const Vec2& v, float s)
{
    return Vec2(v.x - s, v.y - s);
}

/* * */
inline Vec2 operator*(const Vec2& v1, const Vec2& v2)
{
    return Vec2(v1.x * v2.x, v1.y * v2.y);
}
inline Vec2 operator*(float s, const Vec2& v)
{
    return Vec2(v.x * s, v.y * s);
}
inline Vec2 operator*(const Vec2& v, float s)
{
    return Vec2(v.x * s, v.y * s);
}

/* / */
inline Vec2 operator/(const Vec2& v1, const Vec2& v2)
{
    return Vec2(v1.x / v2.x, v1.y / v2.y);
}
inline Vec2 operator/(float s, const Vec2& v)
{
    return Vec2(s / v.x, s / v.y);
}
inline Vec2 operator/(const Vec2& v, float s)
{
    return Vec2(v.x / s, v.y / s);
}

/**
 * @brief 3维向量.
 */
class Vec3
{
public:
    float x, y, z;

public:
    Vec3() : x(0), y(0), z(0) {}

    Vec3(float nx, float ny, float nz) : x(nx), y(ny), z(nz) {}

    Vec3(int nx, int ny, int nz) : x((float)nx), y((float)ny), z((float)nz) {}

    Vec3(Vec2& v) : x(v.x), y(v.y), z(0) {}

    Vec3& operator=(const Vec2& v)
    {
        x = v.x;
        y = v.y;
        z = 0;
        return *this;
    }

    void set(float nx, float ny, float nz)
    {
        x = nx;
        y = ny;
        z = nz;
    }

    bool isnull() const
    {
        return x == 0 && y == 0 && z == 0;
    }

    float length() const
    {
        return sqrtf(x * x + y * y + z * z);
    }

    static Vec3 normalize(Vec3& v)
    {
        float len = 1.0f / v.length();
        return Vec3(v.x * len, v.y * len, v.z * len);
    }

    const Vec3& normalize()
    {
        *this = Vec3::normalize(*this);
        return *this;
    }

    static float dot(const Vec3& v1, const Vec3& v2)
    {
        return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
    }

    static Vec3 cross(const Vec3& v1, const Vec3& v2)
    {
        return Vec3(v1.y * v2.z - v1.z * v2.y, v1.z * v2.x - v1.x * v2.z, v1.x * v2.y - v1.y * v2.x);
    }
};

/* + */
inline Vec3 operator+(const Vec3& v1, const Vec3& v2)
{
    return Vec3(v1.x + v2.x, v1.y + v2.y, v1.z + v2.z);
}
inline Vec3 operator+(float s, const Vec3& v)
{
    return Vec3(v.x + s, v.y + s, v.z + s);
}
inline Vec3 operator+(const Vec3& v, float s)
{
    return Vec3(v.x + s, v.y + s, v.z + s);
}

/* - */
inline Vec3 operator-(const Vec3& v1, const Vec3& v2)
{
    return Vec3(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z);
}
inline Vec3 operator-(float s, const Vec3& v)
{
    return Vec3(s - v.x, s - v.y, s - v.z);
}
inline Vec3 operator-(const Vec3& v, float s)
{
    return Vec3(v.x - s, v.y - s, v.z - s);
}

/* * */
inline Vec3 operator*(const Vec3& v1, const Vec3& v2)
{
    return Vec3(v1.x * v2.x, v1.y * v2.y, v1.z * v2.z);
}
inline Vec3 operator*(float s, const Vec3& v)
{
    return Vec3(v.x * s, v.y * s, v.z * s);
}
inline Vec3 operator*(const Vec3& v, float s)
{
    return Vec3(v.x * s, v.y * s, v.z * s);
}

/* / */
inline Vec3 operator/(const Vec3& v1, const Vec3& v2)
{
    return Vec3(v1.x / v2.x, v1.y / v2.y, v1.z / v2.z);
}
inline Vec3 operator/(float s, const Vec3& v)
{
    return Vec3(s / v.x, s / v.y, s / v.z);
}
inline Vec3 operator/(const Vec3& v, float s)
{
    return Vec3(v.x / s, v.y / s, v.z / s);
}

//------------------------------------------------------------------------
// Rect
//------------------------------------------------------------------------
class Rect2
{
public:
    float x, y, w, h;

public:
    Rect2() : x(0), y(0), w(0), h(0)
    {
    }

    void set(float nx, float ny, float nw, float nh)
    {
        x = nx;
        y = ny;
        w = nw;
        h = nh;
    }

    void setPos(const Vec2& pos)
    {
        x = pos.x;
        y = pos.y;
    }
    void setSize(const Size& size)
    {
        w = size.x;
        h = size.y;
    }

    Vec2 getPos()
    {
        return Vec2(x, y);
    }
    Size getSize()
    {
        return Size(w, h);
    }
};

class Color
{
public:
    float r, g, b, a;

public:
    Color() : r(0), g(0), b(0), a(0) {}
    Color(float nr, float ng, float nb, float na = 1.0f) : r(nr), g(ng), b(nb), a(na) {}
    Color(double nr, double ng, double nb, double na = 1.0) : r((float)nr), g((float)ng), b((float)nb), a((float)na) {}
    Color(int nr, int ng, int nb, int na = 255) : r(nr / 255.0f), g(ng / 255.0f), b(nb / 255.0f), a(na / 255.0f) {}

    void set(float nr, float ng, float nb, float na)
    {
        r = nr;
        g = ng;
        b = nb;
        a = na;
    }
    Color GetInv()
    {
        return Color(1 - r, 1 - g, 1 - b, a);
    }
};

static const Color ZERO(0, 0, 0, 0);
static const Color color_black(0.0f, 0.0f, 0.0f);
static const Color color_white(1.0f, 1.0f, 1.0f);
static const Color color_silver(205, 205, 205);
static const Color color_tea_green(200, 237, 204);
static const Color color_reseda_green(110, 123, 108);
static const Color color_anti_flash_white(234, 234, 239);

inline Color operator+(const Color& color, const Color& o)
{
    return Color(color.r + o.r, color.g + o.g, color.b + o.b, color.a + o.a);
}
inline Color operator+(const Color& color, float f)
{
    return Color(color.r + f, color.g + f, color.b + f, color.a + f);
}
inline Color operator-(const Color& color, const Color& o)
{
    return Color(color.r - o.r, color.g - o.g, color.b - o.b, color.a - o.a);
}
inline Color operator-(const Color& color, float f)
{
    return Color(color.r - f, color.g - f, color.b - f, color.a - f);
}
inline Color operator*(const Color& color, const Color& o)
{
    return Color(color.r * o.r, color.g * o.g, color.b * o.b, color.a * o.a);
}
inline Color operator*(const Color& color, float f)
{
    return Color(color.r * f, color.g * f, color.b * f, color.a * f);
}
inline Color operator/(const Color& color, const Color& o)
{
    return Color(color.r / o.r, color.g / o.g, color.b / o.b, color.a / o.a);
}
inline Color operator/(const Color& color, float f)
{
    return Color(color.r / f, color.g / f, color.b / f, color.a / f);
}

#endif //! RMATH_H