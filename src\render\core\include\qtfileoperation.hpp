/*********************************************************************
 * @brief  包含一些与qt文件处理有关的常用函数.
 *
 * @file   qtfileoperation.hpp
 *
 * @date   2024.01.20
 * <AUTHOR>
 *********************************************************************/
#pragma once

#include <QFileDialog>

 // 打开多个文件
inline QStringList openMultipleFiles(const QString& title = "Open Files", const QString& filter = "All Files (*)", const QString& defaultPath = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setFileMode(QFileDialog::ExistingFiles);
    dialog.setNameFilter(filter);
    dialog.setDirectory(defaultPath);

    if (dialog.exec())
    {
        return dialog.selectedFiles();
    }
    else
    {
        return QStringList();
    }
}

// 打开单个文件
inline QString openSingleFile(const QString& title = "Open File", const QString& filter = "All Files (*)", const QString& defaultPath = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setFileMode(QFileDialog::ExistingFile);
    dialog.setNameFilter(filter);
    dialog.setDirectory(defaultPath);

    if (dialog.exec())
    {
        return dialog.selectedFiles().first();
    }
    else
    {
        return QString();
    }
}

// 保存单个文件
inline QString saveSingleFile(const QString& title = "Save File", const QString& filter = "All Files (*)", const QString& defaultPath = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setAcceptMode(QFileDialog::AcceptSave);
    dialog.setNameFilter(filter);
    dialog.setDirectory(defaultPath);

    if (dialog.exec())
    {
        return dialog.selectedFiles().first();
    }
    else
    {
        return QString();
    }
}

// 打开文件夹
inline QString openFolder(const QString& title = "Open Folder", const QString& defaultPath = "")
{
    QFileDialog dialog;
    dialog.setWindowTitle(title);
    dialog.setFileMode(QFileDialog::Directory);
    dialog.setOption(QFileDialog::ShowDirsOnly);
    dialog.setDirectory(defaultPath);

    if (dialog.exec())
    {
        return dialog.selectedFiles().first();
    }
    else
    {
        return QString();
    }
}