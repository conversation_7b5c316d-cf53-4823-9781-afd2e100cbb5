﻿/*****************************************************************//**
 * @file   customtitleview.h
 * @brief  自定义标题栏样式
 * @details
 * <AUTHOR>
 * @date 2024.1.15
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __CUSTOMTITLEVIEW_H__
#define __CUSTOMTITLEVIEW_H__
 // prebuild
#include "pch.h"
//QT
#pragma warning(push, 3)
#include <QWidget>
#include <QSpinBox>
#include <QList>
#include <QAction>
#pragma warning(pop)
//Custom
//#include "viewparam.hpp"
//Third
#include "SARibbonMainWindow.h"
#include "SARibbonLineEdit.h"
#include "SARibbonButtonGroupWidget.h"

class SARibbonCategory;
class QDoubleSpinBox;
class SARibbonComboBox;
class SARibbonLineEdit;

namespace jrsaoi
{
    using InvokeFun = std::function< void() >;
    class CustomTitleView :public SARibbonMainWindow
    {
        Q_OBJECT
    public:
        explicit CustomTitleView(SARibbonMainWindow* parent = nullptr);
        ~CustomTitleView();

    private slots:

        /**
         * @fun SlotActionRenderInvokeFun
         * @brief 处理渲染相关的动作触发事件。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SlotActionRenderInvokeFun();
        /**
         * @fun HandleTransformActions
         * @brief 处理与变换相关的动作。
         * @param action_name 动作名称。
         * @param render_param 渲染参数指针。
         * @return bool 是否处理成功。
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool HandleTransformActions(const std::string& action_name, jrsdata::RenderEventParamPtr render_param);

        /**
         * @fun HandleComponentActions
         * @brief 处理与元件相关的动作。
         * @param action_name 动作名称。
         * @param render_param 渲染参数指针。
         * @date 2025.02.25
         * <AUTHOR>
         */
        void HandleComponentActions(const std::string& action_name, jrsdata::RenderEventParamPtr render_param);
        ///**
        //* @fun HandleAddComponentAction
        //* @brief 处理添加元件的动作。
        //* @param render_param 渲染参数指针。
        //* @date 2025.02.25
        //* <AUTHOR>
        //*/
        //void HandleAddComponentAction(jrsdata::RenderEventParamPtr render_param);

        /**
         * @fun SlotActionProjectInvokeFun
         * @brief 工程模块响应部分
         * <AUTHOR>
         * @date 2024.7.23
         */
        void SlotActionProjectInvokeFun();
        /**
         * @fun SlotActionOperateInvokeFun
         * @brief 工程运行模块快捷按钮响应
         * <AUTHOR>
         * @date 2024.7.23
         */
        void SlotActionOperateInvokeFun();
        /**
         * @fun SlotActionOnlineDebugInvokeFun
         * @brief 在线调试快捷按钮响应槽函数
         * <AUTHOR>
         * @date 2025.3.10
         */
        void SlotActionOnlineDebugInvokeFun();
        /**
         * @fun SlotActionDeviceInvokeFun
         * @brief
         * <AUTHOR>
         * @date 2024.7.23
         */
        void SlotActionDeviceInvokeFun();
        /**
         * @fun SlotActionDeviceInvokeFun
         * @brief 权限管理快捷按钮响应函数
         * <AUTHOR>
         * @date 2024.7.23
         */
        void SlotActionPermissionInvokeFun();
        /**
         * @fun SlotActionMotionInvokeFun
         * @brief 运控模块快捷按钮响应函数
         * <AUTHOR>
         * @date 2024.7.23
         */
        void SlotActionMotionInvokeFun();
        /**
         * @fun SlotActionAxisMoveFun
         * @brief 处理轨道控制动作的槽函数
         * <AUTHOR>
         * @date 2024.8.22
         */
        void SlotActionAxisMoveFun();
        /**
         * @fun ParseActionName
         * @brief 解析动作名称并设置轨道参数。
         * @param action_name 动作名称。
         * @param track_param 轨道控制参数。
         * @return bool 是否解析成功。
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool ParseActionName(const QString& action_name, jrsdata::TrackControlParam& track_param);
        /**
         * @fun CreateTrackControlViewPtr
         * @brief 创建轨道控制视图参数指针。
         * @param track_param 轨道控制参数。
         * @param action_name 动作名称。
         * @return std::shared_ptr<jrsdata::OperateViewParam> 轨道控制视图参数指针。
         * @date 2025.02.25
         * <AUTHOR>
         */
        std::shared_ptr<jrsdata::OperateViewParam> CreateTrackControlViewPtr(const jrsdata::TrackControlParam& track_param, const QString& action_name);
        /**
         * @fun SlotComboboxImageTypeChange
         * @brief 大图显示的颜色切换
         * @param  显示图片颜色名称
         * <AUTHOR>
         * @date 2024.9.25
         */
        void SlotComboboxImageTypeChange(const QString&);
        /**
         * @fun SlotActionLogicInvokeFun
         * @brief 处理逻辑模块的动作触发事件。
         * @details 当用户触发与逻辑模块相关的快捷键或按钮时，此函数将创建一个操作参数对象，
         *          并通过信号 SigActionTrigger 发送该事件到逻辑模块进行处理。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SlotActionLogicInvokeFun();
        /**
         * @fun SlotActionSettingViewParamFun
         * @brief 处理设置模块的动作触发事件。
         * @details 当用户触发与设置模块相关的动作时，此函数会创建一个设置参数对象，
         *          并通过信号 SigActionTrigger 将事件发送到设置模块进行处理。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SlotActionSettingViewParamFun();
        /**
         * @fun SlotGraphicUpdateFun
         * @brief 更新图形界面的显示信息。
         * @details 此函数接收一个视图参数对象，将其转换为渲染视图参数，并更新界面中的设备位置信息。
         *          使用 `setters` 映射来调用相应的设置函数，更新界面控件的值。
         * @param item_info 视图参数对象，包含渲染信息。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void SlotGraphicUpdateFun(const jrsdata::ViewParamBasePtr& item_info);

        /**
         * @fun SlotActionComponentInvokeFun
         * @brief  增加元件快捷建操作响应函数接口
         * @return
         * <AUTHOR>
         * @date 2024.11.29
         */
        void SlotActionComponentInvokeFun();

    public slots:
        /**
         * .元件框信息被更新
         * 丢弃
         */
         //void SlotGraphicUpdateFun(const jrsdata::ViewParamBasePtr& item_info);
    signals:
        void SigActionTrigger(const jrsdata::ViewParamBasePtr&);
    private:
        /**
         * @fun Init
         * @brief 初始化
         * <AUTHOR>
         * @date 2024.7.23
         */
        void Init();
        /**
         * @fun InitView
         * @brief 初始化view部分
         * <AUTHOR>
         * @date 2024.7.23
         */
        void InitView();
        /**
         * @fun InitMember
         * @brief 初始化变量
         * <AUTHOR>
         * @date 2024.7.23
         */
        void InitMember();
        /**
         * @fun CreateRibbonApplicationButton
         * @brief 创建应用快捷按钮
         * <AUTHOR>
         * @date 2024.7.23
         */
        void CreateRibbonApplicationButton();
        /**
         * @fun CreateMainPanel
         * @brief 主操作快捷面板
         * @param page
         * @date 2024.5.24
         * <AUTHOR>
         */
        void CreateMainPanel(SARibbonCategory* page);
        /**
         * @fun CreateRebackPanel
         * @brief 撤销操作快捷面板
         * @param page
         * @date 2024.5.24
         * <AUTHOR>
         */
        void CreateRebackPanel(SARibbonCategory* page);
        /**
        * @fun CreateButtonGroup
        * @brief 创建并配置 SARibbonButtonGroupWidget。
        * @param parent 父级组件。
        * @param actions 动作列表。
        * @param item_height 按钮高度。
        * @param min_width 最小宽度。
        * @return SARibbonButtonGroupWidget* 创建的按钮组。
        * @date 2025.02.25
        * <AUTHOR>
        */
        SARibbonButtonGroupWidget* CreateButtonGroup(QWidget* parent, const QList<QAction*>& actions, int item_height, int min_width);
        /**
         * @fun CreateImagePanel
         * @brief 创建图片面板，包含图像显示和缩放操作。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateImagePanel(SARibbonCategory* page);
        /**
         * @fun CreateDetectPanel
         * @brief 创建检测框面板，包含镜像和旋转操作。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateDetectPanel(SARibbonCategory* page);

        /**
        * @fun CreateAIPanel
        * @brief 创建AI操作按钮
        * @param page SARibbonCategory 对象。
        * @return void
         * @date 2025.05.216
        * <AUTHOR>
        */
        void CreateAIPanel(SARibbonCategory* page);

        /**
         * @fun CreateCADPanel
         * @brief 创建 CAD 面板，包含镜像、旋转和移动操作。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateCADPanel(SARibbonCategory* page);
        /**
         * @fun CreateDeviceControlPanel
         * @brief 创建元件控制面板，包含新增、删除和旋转操作。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateDeviceControlPanel(SARibbonCategory* page);
        /**
         * @fun CreateTestPanel
         * @brief 创建测试面板，包含检测框、元件和料号测试操作。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateTestPanel(SARibbonCategory* page);
        /**
         * @fun CreateControlPanel
         * @brief 创建控制面板，包含实时拍照、当前位置拍照并保存图片、轴控制和显示索引图的操作。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateControlPanel(SARibbonCategory* page);
        /**
         * @fun Create3DPanel
         * @brief 创建3D面板，包含选中元件3D图、框选区域3D图和整板大图3D图的操作。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void Create3DPanel(SARibbonCategory* page);
        /**
         * @fun CreateDeviceShowPanel
         * @brief 创建元件显示面板，包含本体框、显示焊盘和显示检测框的操作。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateDeviceShowPanel(SARibbonCategory* page);
        /**
         * @fun CreateDetectTestShowPanel
         * @brief 创建检测框测试面板，包含显示轮廓、显示填充和显示数值的操作。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateDetectTestShowPanel(SARibbonCategory* page);
        /**
          * @fun CreateDetectResultSignalPanel
          * @brief 创建检测结果标识面板。
          * @param page SARibbonCategory 对象。
          * @return void
          * @date 2025.02.25
          * <AUTHOR>
          */
        void CreateDetectResultSignalPanel(SARibbonCategory* page);
        /**
         * @fun CreateMotionControlPanel
         * @brief 创建上下料控制面板
         * @param page SARibbonCategory 对象。
         * @param index 轨道索引。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateMotionControlPanel(SARibbonCategory* page, int index);
        /**
         * @fun CreateModelPanel
         * @brief 创建模板编辑面板。
         * @param page SARibbonCategory 对象。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateModelPanel(SARibbonCategory* page);
        void CreateMarkShowPanel(SARibbonCategory* page);
        /**
         * @fun CreateLayerShowPanel
         * @brief 创建图层显示面板，包含图像颜色选择下拉框。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateLayerShowPanel(SARibbonCategory* page);
        /**
         * @fun CreateTimeShowPanel
         * @brief 创建时间显示面板，包含一个动态更新的当前时间标签。
         * @param page SARibbonCategory 对象，用于添加面板。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateTimeShowPanel(SARibbonCategory* page);
        /**
         * @fun CreateAction
         * @brief 创建action控件
         * @param text 控件显示名称
         * @param iconurl icon路径
         * @param obj_name 属性名称
         * @return  控件实例
         * @date 2024.5.24
         * <AUTHOR>
         */
        QAction* CreateAction(const QString& text, const QString& iconurl, const QString& obj_name, InvokeFun invoke_fun_);
        /**
         * @fun CreateAction
         * @brief 创建一个 QAction，并设置其文本、图标和对象名称。
         * @param text QAction 的显示文本。
         * @param iconurl 图标的资源路径。
         * @param obj_name QAction 的对象名称，用于后续识别。
         * @return QAction* 创建的 QAction 对象。
         * @date 2025.02.25
         * <AUTHOR>
         */
        QAction* CreateAction(const QString& text, const QString& iconurl, const QString& obj_name);
        /**
         * @fun CreateMenu
         * @brief 创建一个 QMenu，并设置其标题和图标。
         * @param text 菜单的显示文本。
         * @param iconurl 菜单图标的资源路径。
         * @return QMenu* 创建的 QMenu 对象。
         * @date 2025.02.25
         * <AUTHOR>
         */
        QMenu* CreateMenu(const QString& text, const QString& iconurl);
        /**
         * @fun CreateQuickAccessBar
         * @brief 创建快速访问工具栏，并添加设置按钮。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        void CreateQuickAccessBar();
        /**
         * @fun CreateRightButtonGroup
         * @brief 创建最右侧快捷键
         * @date 2024.5.24
         * <AUTHOR>
         */
        void CreateRightButtonGroup();
        SARibbonComboBox* image_color_select = nullptr; /**< 图层显示ComboBox */
        QButtonGroup* subboard_board_group_btn = nullptr;/***< 子板整版切换**/
        /**<属性 */
        InvokeFun invoke_fun_render;                    /**< 渲染界面响应槽函数 */
        InvokeFun invoke_fun_project;                   /**< 工程模块快捷按钮响应槽函数 */
        InvokeFun invoke_fun_operate;                   /**< 流程运行模块快捷按钮响应槽函数 */
        InvokeFun invoke_fun_permission;                /**< 权限管理模块快捷按钮响应槽函数 */
        InvokeFun invoke_fun_motion;                    /**< 运动模块快捷按钮响应槽函数 */
        InvokeFun invoke_fun_axis_move;                 /**< 轴运动快捷按钮响应槽函数 */
        InvokeFun invoke_fun_logic;                     /**< Logic快捷键响应<拍照保存快捷键> */
        InvokeFun invoke_fun_setting_param;             /**< 设置系统参数响应 */
        InvokeFun invoke_fun_onlinedebug;              /**< 动态调试模块快捷按钮响应槽函数 */
        SARibbonMainWindow* parent_view;                /**< 菜单快捷按钮块的父类 */
        QMenu* menu_application_btn;                    /**< 菜单上面的新建打开图像的目录 */
        std::unordered_map<std::string, QDoubleSpinBox*> device_pos_info_spinbox;
        std::unordered_map<std::string, std::function<void(QDoubleSpinBox*, const jrsdata::GraphicsInfo&)>> setters = {
          {"X:", [](QDoubleSpinBox* spin_box, const jrsdata::GraphicsInfo& itemInfo) { spin_box->setValue(itemInfo.x_coordinate); }},
          {"Y:", [](QDoubleSpinBox* spin_box, const jrsdata::GraphicsInfo& itemInfo) { spin_box->setValue(itemInfo.y_coordinate); }},
          {"Width:", [](QDoubleSpinBox* spin_box, const jrsdata::GraphicsInfo& itemInfo) { spin_box->setValue(itemInfo.width); }},
          {"Height:", [](QDoubleSpinBox* spin_box, const jrsdata::GraphicsInfo& itemInfo) { spin_box->setValue(itemInfo.height); }},
          {"Angle:", [](QDoubleSpinBox* spin_box, const jrsdata::GraphicsInfo& itemInfo) { spin_box->setValue(itemInfo.angle); }},
        };
    };
}
#endif // !__CUSTOMTITLEVIEW_H__
