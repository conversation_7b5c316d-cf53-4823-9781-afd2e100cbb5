/*********************************************************************
 * @brief  渲染对象基类.
 *
 * @file   renderabstract.hpp
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef RENDERABSTRACT_H
#define RENDERABSTRACT_H

#include <memory>

class Renderer;
/**
 * @class RenderAbstract
 * @brief 渲染对象基类.
 */
class RenderAbstract
{
public:
    explicit RenderAbstract(RenderAbstract* parent_ = nullptr)
        : renderer(), parent(parent_) {}

    virtual ~RenderAbstract() {}

    /**
     * @brief  渲染.
     * @fun    Render
     *
     * @date   2024.07.05
     * <AUTHOR>
     */
    virtual void Render() = 0;

    /**
     * @brief  资源销毁.
     * @fun    Destroy
     *
     * @date   2024.07.05
     * <AUTHOR>
     */
    virtual void Destroy() = 0;

    void SetRenderer(Renderer* r) { renderer = r; }
    inline bool IsHaveRenerer() { return renderer != nullptr; }
    Renderer* GetRenderer() { return renderer; }

    void SetParent(RenderAbstract* p) { parent = p; }
    inline bool IsHaveParent() { return parent != nullptr; }
    RenderAbstract* GetParent() { return parent; }

    inline bool IsRendered() { return rendered; }
    void SetRendered(bool state) { rendered = state; }

protected:
    Renderer* renderer;     ///< 绑定渲染器
    RenderAbstract* parent; ///< 父对象，还没想到有什么用

private:
    bool rendered = true; ///< 是否渲染
};

using RenderAbstractPtr = std::shared_ptr<RenderAbstract>;

#endif //! RENDERABSTRACT_H