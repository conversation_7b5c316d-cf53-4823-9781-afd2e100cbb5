﻿//QT
#include <QSettings>
#include <QJsonObject>
#include <QJsonDocument>

//CUSTOM
#include "ui_dataview.h"
#include "dataview.h"
#include "checkwidget.h"
#include "paramoperator.h"

DataView::DataView(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::DataView())
    , _is_enable_save_param(false)
{
    ui->setupUi(this);
    if (map_directory.size() < 9)
    {
        _param_name_map = {
            {QStringLiteral("工程路径："),   jrssettingparam::jrsmachineparam::MACHINE_PARAM_PROJECT_PATH},
            {QStringLiteral("整板图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ENTIRTY_IMAGE_PATH},
            {QStringLiteral("元件库路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_COMPONENTS_PATH},
            {QStringLiteral("维修站路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_REPAIR_PATH},
            {QStringLiteral("测试大图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_TEST_BIG_IMAGE_PATH},
            {QStringLiteral("定位点图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_LOCATION_POINT_IMAGE_PATH},
            {QStringLiteral("算法结果路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ALGORITHM_RESULT_PATH},
            {QStringLiteral("实时采图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_REAL_TIME_TAKE_IMAGE_PATH},
            {QStringLiteral("原图路径："), jrssettingparam::jrsmachineparam::MACHINE_PARAM_ORIGINAL_IMAGE_PATH},
        };
        for (auto& map : _param_name_map)
        {
            SettingData setting;
            setting.label = map.first;
            setting.directory = "";
            setting.enable = false;
            map_directory.insert(setting.label, setting);
        }
        SaveSettingFile();
    }
    UpdateView();
    ConnectSlots();
    InitConnect();
}

DataView::~DataView()
{
}

void DataView::SaveSettingFile()
{
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();
    if (!_is_enable_save_param)
    {
        return; //开机时读取配置文件 拒绝保存
    }
    auto keys = map_directory.keys();
    for (auto& map : map_directory.toStdMap())
    {
        auto map_it = _param_name_map.find(map.first);
        if (map_it != _param_name_map.end())
        {
            std::string value_str = SettingDataToJson(map.second);
            param_operate->ReplaceSettingParamValueByName(jrsdata::ParamLevel::MACHINE, map_it->second, value_str);
        };
    }
    _machine_param.event_name = jrsaoi::MACHINE_PARAM_SAVE_EVENT;
    emit SigUpdateMachineParam(_machine_param);
}

void DataView::UpdateView(const jrsdata::MachineParam& machine_param_)
{
    (void)machine_param_;
    auto& param_operate = jrsaoi::ParamOperator::GetInstance().GetParameterProcessInstance();

    auto func = [&](const std::string& name_) -> decltype(auto) {
        return param_operate->GetSettingParamValueByName<std::string>(jrsdata::ParamLevel::MACHINE, name_);
        };

    for (auto& name_temp : _param_name_map)
    {
        auto value = func(name_temp.second);
        auto setting_data = JsonToSettingData(value);
        map_directory[name_temp.first] = setting_data;
    }

    UpdateView();
    if (!_is_enable_save_param)
    {
        _is_enable_save_param = true;
    }
}

void DataView::UpdateView()
{
    int count = ui->vector_check_widget->count();
    int key_count = map_directory.keys().count();
    if (count == key_count)//只是更新数据
    {
        DisconnectSlots();
        for (int i = 0; i < count; i++)
        {
            auto widget = static_cast<CheckWidget*>(ui->vector_check_widget->itemAt(i)->widget());
            if (widget != nullptr)
            {
                widget->SetSetting(map_directory[widget->GetLabel()].enable);
                widget->SetDirectory(map_directory[widget->GetLabel()].directory);
            }
        }
        ConnectSlots();
    }
    else//删除重新添加
    {
        for (int i = 0; i < count; i++)//删除原来的
        {
            auto widget = static_cast<CheckWidget*>(ui->vector_check_widget->itemAt(i)->widget());
            if (widget != nullptr)
            {
                ui->vector_check_widget->removeItem(ui->vector_check_widget->itemAt(i));
                delete widget;
            }
        }
        auto keys = map_directory.keys();
        for (int i = 0; i < keys.size(); i++)
        {
            CheckWidget* one_widget = new CheckWidget(map_directory[keys.at(i)]);
            ui->vector_check_widget->addWidget(one_widget);
        }
    }
}

void DataView::ConnectSlots()
{
    connect(ui->btn_clean, &QPushButton::clicked, this, &DataView::SlotClean);
    connect(ui->btn_read, &QPushButton::clicked, this, &DataView::SlotRead);
    int count = ui->vector_check_widget->count();
    for (int i = 0; i < count; i++)
    {
        auto widget = static_cast<CheckWidget*>(ui->vector_check_widget->itemAt(i)->widget());
        if (widget != nullptr)
        {
            connect(widget, &CheckWidget::SignalDataChange, this, &DataView::SlotDataChange);
        }
    }
}

void DataView::DisconnectSlots()
{
    disconnect(ui->btn_clean, &QPushButton::clicked, this, &DataView::SlotClean);
    disconnect(ui->btn_read, &QPushButton::clicked, this, &DataView::SlotRead);
    int count = ui->vector_check_widget->count();
    for (int i = 0; i < count; i++)
    {
        auto widget = static_cast<CheckWidget*>(ui->vector_check_widget->itemAt(i)->widget());
        if (widget != nullptr)
        {
            disconnect(widget, &CheckWidget::SignalDataChange, this, &DataView::SlotDataChange);
        }
    }
}

void DataView::SlotClean()
{
    DisconnectSlots();
    auto keys = map_directory.keys();
    for (int i = 0; i < keys.size(); i++)
    {
        map_directory[keys.at(i)].directory = "";
    }
    UpdateView();
    ConnectSlots();
}

void DataView::SlotRead()
{
    _machine_param.event_name = jrsaoi::MACHINE_PARAM_UPDATE_EVENT;
    emit SigUpdateMachineParam(_machine_param);
}

void DataView::SlotRadioChecked(QAbstractButton*)
{
    SaveSettingFile();
}

void DataView::InitConnect()
{
}

std::string DataView::SettingDataToJson(const SettingData& setting_)
{
    QJsonObject json;
    json["label"] = setting_.label;
    json["directory"] = setting_.directory;
    json["enable"] = setting_.enable;
    QJsonDocument jsonDoc(json);
    QString json_string = jsonDoc.toJson(QJsonDocument::Compact);
    return json_string.toStdString();
}

SettingData DataView::JsonToSettingData(const std::string& setting_)
{
    QString json_string = QString::fromStdString(setting_);
    QJsonDocument jsonDoc = QJsonDocument::fromJson(json_string.toUtf8());
    QJsonObject json = jsonDoc.object();
    SettingData settingData;
    settingData.label = json["label"].toString();
    settingData.directory = json["directory"].toString();
    settingData.enable = json["enable"].toBool();
    return settingData;
}

void DataView::SlotDataChange(QString label, SettingData setting_data)
{
    DisconnectSlots();
    map_directory[label] = setting_data;
    SaveSettingFile();
    ConnectSlots();
}

