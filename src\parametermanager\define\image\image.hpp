/*****************************************************************//**
 * @file   image.hpp
 * @brief
 * @details
 * <AUTHOR>
 * @date 2024.7.31
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.7.31         <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSIMAGE_H__
#define __JRSIMAGE_H__

 //STD
#include <iostream>
#include <map>

//Third
#pragma warning(push, 3)
#include <opencv2/opencv.hpp>
#pragma warning(pop)
namespace jrsdata
{
    enum class TriggerModeCapture
    {
        DLP1_TRIGGER = 1 << 14,
        DLP2_TRIGGER = 1 << 13,
        DLP3_TRIGGER = 1 << 12,
        DLP4_TRIGGER = 1 << 11,
        DLPS_TRIGGER = 15 << 11,
        RGB_TRIGGER = 7 << 8,
        WHT_TRIGGER = 7 << 5,
        LOW_TRIGGER = 1 << 4,
        SLE_TRIGGER = 1 << 1,
        LGTS_TRIGGER = (7 << 8) + (7 << 5) + (1 << 4),
        LGTS_DLPS_TRIGGER = (15 << 11) + (7 << 8) + (7 << 5) + (1 << 4),
        LGTS_HEIG_TRIGGER = (7 << 8) + (7 << 5) + (1 << 4) + 1,
        NO_TRIGGER = 0
    };

    //! 图像类型
    enum class LightImageType : int
    {
        RGB = 0,
        WHITE,
        LOWWHITE,
        HEIGHT,
        OTHERS,
        COUNT//用于获取数量
    };

    inline constexpr std::array<jrsdata::LightImageType, static_cast<std::size_t>(jrsdata::LightImageType::COUNT)> all_img_types =
    {
        jrsdata::LightImageType::RGB,
        jrsdata::LightImageType::WHITE,
        jrsdata::LightImageType::LOWWHITE,
        jrsdata::LightImageType::HEIGHT,
        jrsdata::LightImageType::OTHERS
    };

    //! 单Fov图像
    struct OneFovImgs
    {
        std::map<LightImageType, cv::Mat> imgs;
        cv::Point2f           pos;
        int                   fov_id;
        OneFovImgs()
            : fov_id{}
        {
        }
    };

    struct BoarderImgs
    {
        std::map<LightImageType, cv::Mat> imgs;
    };

    //! AOI端图像结构体
    struct JrsImageBuffer
    {
        OneFovImgs  one_fov_imgs; /**< 单张FOV图像*/
        BoarderImgs boarder_imgs; /**< 整板图像*/
    };

    using Grab2DImgCallBack = std::function<void(const int& error_code)>;
    using CaptureCallBack = std::function<void(const OneFovImgs& imgs, const int& erro_code)>;

    using JrsImageBufferCallBack = std::function<void(const JrsImageBuffer& imgs)>;
}
#endif // !__JRSIMAGE_H__