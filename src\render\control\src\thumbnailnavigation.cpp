#include "thumbnailnavigation.h"

#include "log.h"

#pragma warning(push, 1)
#include "opencv2/imgproc.hpp" // cv::resize
#pragma warning(pop)


ThumbnailNavigation::ThumbnailNavigation(int w, int h)
    : m_w(w), m_h(h), m_scale(1.0f)
{
}


bool ThumbnailNavigation::SetImage(const cv::Mat& image, int image_w_, int image_h_)
{
    if (image.empty() || image_w_ <= 0 || image_h_ <= 0)
        return false;

    float scale = std::min<float>(m_w / static_cast<float>(image.cols), m_h / static_cast<float>(image.rows));
    if (scale <= 0)
        return false;

    cv::resize(image, m_thumbnail, cv::Size(), scale, scale, cv::INTER_AREA);
    m_qimage = Mat2QImage(m_thumbnail);

    SetRegion(image_w_, image_h_);
    return true;
}

void ThumbnailNavigation::ClearImage()
{
    m_thumbnail.release();
    m_qimage = QImage();
}

bool ThumbnailNavigation::SetRegion(int region_w, int region_h)
{
    if (region_w <= 0 || region_h <= 0)
    {
        return false;
    }

    image_w = region_w;
    image_h = region_h;
    CreateScale();

    float current_w = image_w * m_scale;
    float current_h = image_h * m_scale;
    SetRenderRegionLimit((m_w - current_w) * 0.5f, (m_h - current_h) * 0.5f,
        (m_w + current_w) * 0.5f, (m_h + current_h) * 0.5f);
    return true;
}

void ThumbnailNavigation::SetWindowSize(int w, int h)
{
    if (w <= 0 || h <= 0)
    {
        return;
    }

    m_w = w;
    m_h = h;
    SetImage(m_thumbnail, image_w, image_h);
}

void ThumbnailNavigation::SetViewport(float left, float top, float right, float bottom)
{
    if (right - left <= 0 || bottom - top <= 0)
    {
        return;
    }
    m_viewport_x = left;
    m_viewport_y = top;
    m_viewport_w = right - left;
    m_viewport_h = bottom - top;

    m_region_x = m_viewport_x;
    m_region_y = m_viewport_y;
    m_region_w = m_viewport_w;
    m_region_h = m_viewport_h;
    ImageRegionToViewportRegion(m_region_x, m_region_y, m_region_w, m_region_h);
}

void ThumbnailNavigation::GetViewport(float& x, float& y, float& w, float& h)
{
    x = m_viewport_x;
    y = m_viewport_y;
    w = m_viewport_w;
    h = m_viewport_h;
}

QImage ThumbnailNavigation::Mat2QImage(const cv::Mat& mat)
{
    // 参考 https ://blog.csdn.net/gongjianbo1992/article/details/126595522
    QImage image;
    switch (mat.type())
    {
    case CV_8UC1:
        // QImage构造：数据，宽度，高度，每行多少字节，存储结构
        image = QImage((const unsigned char*)mat.data, mat.cols, mat.rows, static_cast<int>(mat.step), QImage::Format_Grayscale8);
        break;
    case CV_8UC3:
        image = QImage((const unsigned char*)mat.data, mat.cols, mat.rows, static_cast<int>(mat.step), QImage::Format_RGB888).rgbSwapped();
        break;
    case CV_8UC4:
        image = QImage((const unsigned char*)mat.data, mat.cols, mat.rows, static_cast<int>(mat.step), QImage::Format_ARGB32);
        break;
    }
    return image;
}

void ThumbnailNavigation::CreateScale()
{
    m_scale = std::min<float>(m_w / static_cast<float>(image_w), m_h / static_cast<float>(image_h));
}

void ThumbnailNavigation::GetRenderRegionLimit(float& min_x, float& min_y, float& max_x, float& max_y)
{
    min_x = m_region_min_x;
    min_y = m_region_min_y;
    max_x = m_region_max_x;
    max_y = m_region_max_y;
}

void ThumbnailNavigation::SetRenderRegionLimit(float min_x, float min_y, float max_x, float max_y)
{
    m_region_min_x = min_x;
    m_region_min_y = min_y;
    m_region_max_x = max_x;
    m_region_max_y = max_y;
}

void ThumbnailNavigation::SetThumbnailViewport(float x, float y)
{
    m_region_x = qBound(m_region_min_x, x, m_region_max_x);
    m_region_y = qBound(m_region_min_y, y, m_region_max_y);

    // printInfo(std::stringstream() << "region: x " << m_region_x << ",y " << m_region_y);

    m_viewport_x = m_region_x;
    m_viewport_y = m_region_y;
    m_viewport_w = m_region_w;
    m_viewport_h = m_region_h;
    ViewportRegionToImageRegion(m_viewport_x, m_viewport_y, m_viewport_w, m_viewport_h);
}

void ThumbnailNavigation::GetThumbnailViewport(float& x, float& y, float& w, float& h)
{
    x = m_region_x;
    y = m_region_y;
    w = m_region_w;
    h = m_region_h;
}

void ThumbnailNavigation::ImageRegionToViewportRegion(float& x, float& y, float& w, float& h)
{
    x = static_cast<float>(x * m_scale);
    y = static_cast<float>(y * m_scale);
    w = static_cast<float>(w * m_scale);
    h = static_cast<float>(h * m_scale);
    x += m_region_min_x;
    y += m_region_min_y;
    w = qBound(0.0f, w, m_region_max_x - m_region_min_x);
    h = qBound(0.0f, h, m_region_max_y - m_region_min_y);
    x = qBound(m_region_min_x, x, m_region_max_x - w);
    y = qBound(m_region_min_y, y, m_region_max_y - h);
}

void ThumbnailNavigation::ViewportRegionToImageRegion(float& x, float& y, float& w, float& h)
{
    x -= m_region_min_x;
    y -= m_region_min_y;
    x = static_cast<float>(x / m_scale);
    y = static_cast<float>(y / m_scale);
    w = static_cast<float>(w / m_scale);
    h = static_cast<float>(h / m_scale);

    // x = qBound(0.0f, x, image_w - w);
    // y = qBound(0.0f, y, image_h - h);
}