#include "logshowmodel.h"
namespace jrsaoi
{

    LogShowModel::LogShowModel(const std::string& name) :ModelBase(name)
    {

    }
    LogShowModel::~LogShowModel()
    {

    }
    int LogShowModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    int LogShowModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }

}
