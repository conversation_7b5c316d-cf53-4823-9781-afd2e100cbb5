#include "checkwidget.h"

#include <QFileDialog>
#include <QApplication>

CheckWidget::CheckWidget(QString value, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.title->setText(value);
    ui.title->setEnabled(false);
    ui.directory->setEnabled(false);
    ui.setting->setEnabled(false);
    connect(ui.used, &QCheckBox::toggled, this, &CheckWidget::UsedChanged);
    connect(ui.setting, &QPushButton::clicked, this, &CheckWidget::SlotSettingOpen);
}

CheckWidget::CheckWidget(QString value, QString directory, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.title->setText(value);
    ui.directory->setText(directory);
    ui.title->setEnabled(false);
    ui.directory->setEnabled(false);
    ui.setting->setEnabled(false);
    connect(ui.used, &QCheckBox::toggled, this, &CheckWidget::UsedChanged);
    connect(ui.setting, &QPushButton::clicked, this, &CheckWidget::SlotSettingOpen);
}

CheckWidget::CheckWidget(QString value, QString directory, bool enable, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.title->setText(value);
    ui.directory->setText(directory);
    ui.title->setEnabled(enable);
    ui.directory->setEnabled(enable);
    ui.setting->setEnabled(enable);
    connect(ui.used, &QCheckBox::toggled, this, &CheckWidget::UsedChanged);
    connect(ui.setting, &QPushButton::clicked, this, &CheckWidget::SlotSettingOpen);
}

CheckWidget::CheckWidget(SettingData setting_data, QWidget* parent)
    :QWidget(parent)
{
    ui.setupUi(this);
    ui.title->setText(setting_data.label);
    ui.directory->setText(setting_data.directory);
    ui.setting->setEnabled(setting_data.enable);
    ui.title->setEnabled(setting_data.enable);
    ui.directory->setEnabled(setting_data.enable);
    ui.used->setChecked(setting_data.enable);
    connect(ui.used, &QCheckBox::toggled, this, &CheckWidget::UsedChanged);
    connect(ui.setting, &QPushButton::clicked, this, &CheckWidget::SlotSettingOpen);
    connect(ui.directory, &QLineEdit::editingFinished, this, &CheckWidget::SlotEditFinished);
}

CheckWidget::CheckWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.title->setEnabled(false);
    ui.directory->setEnabled(false);
    ui.setting->setEnabled(false);
    connect(ui.used, &QCheckBox::toggled, this, &CheckWidget::UsedChanged);
    connect(ui.setting, &QPushButton::clicked, this, &CheckWidget::SlotSettingOpen);
}

CheckWidget::~CheckWidget()
{
}

void CheckWidget::SetDirectory(QString dir)
{
    ui.directory->setText(dir);
}

void CheckWidget::SetSetting(bool is_enable_)
{
    ui.used->setChecked(is_enable_);
}

QString CheckWidget::GetLabel()
{
    return ui.title->text();
}

void CheckWidget::SlotEditFinished()
{
    SettingData setting_data;
    setting_data.label = ui.title->text();
    setting_data.directory = ui.directory->text();
    setting_data.enable = ui.used->isChecked();
    emit SignalDataChange(ui.title->text(), setting_data);
}

void CheckWidget::SlotSettingOpen()
{
    QString old_directory = ui.directory->text();
    if (old_directory.isEmpty())
    {
        old_directory = QApplication::applicationDirPath();
    }
    QString directory = QFileDialog::getExistingDirectory(this, "choose directory", old_directory);
    ui.directory->setText(directory);
    SettingData setting_data;
    setting_data.label = ui.title->text();
    setting_data.directory = ui.directory->text();
    setting_data.enable = ui.used->isChecked();
    emit SignalDataChange(ui.title->text(), setting_data);
}

void CheckWidget::UsedChanged(bool enable)
{
    if (enable)
    {
        ui.title->setEnabled(true);
        ui.directory->setEnabled(true);
        ui.setting->setEnabled(true);
    }
    else
    {
        ui.title->setEnabled(false);
        ui.directory->setEnabled(false);
        ui.setting->setEnabled(false);
    }

    SettingData setting_data;
    setting_data.label = ui.title->text();
    setting_data.directory = ui.directory->text();
    setting_data.enable = ui.used->isChecked();
    emit SignalDataChange(ui.title->text(), setting_data);
}
