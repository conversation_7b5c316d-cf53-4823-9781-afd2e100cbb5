﻿/*****************************************************************
 * @file   importcadview.h
 * @brief  导入CAD界面
 * @details 主要功能有导入CAD功能，TODO:自动通过AI添加CAD框
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef IMPORTCADVIEW_H
#define IMPORTCADVIEW_H
 //QT
#include <QWidget>
namespace Ui {
    class ImportCadView;
}
namespace jrsdata {
    struct CadStruct;
    struct CadEventParam;
}
namespace jrsaoi
{
    class AddCadView;
    class ImportCadView : public QWidget
    {
        Q_OBJECT
    public:
        ImportCadView(QWidget* parent = Q_NULLPTR);
        ~ImportCadView();

        /**
         * @fun SelectSubName
         * @brief
         * @param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SelectSubName(const QString&);
        /**
        * @fun SlotManualAddCad
        * @brief
        * @date 2024.9.24
        * <AUTHOR>
        */
        //void SlotManualAddCad();
        /**
         * @fun SlotAIAddCad
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotAIAddCad(int current_index);
        /**
         * @fun SlotAddPositionComponent
         * @brief  添加定位元件
         * @param component_name_
         * <AUTHOR>
         * @date 2024.12.18
         */
        void SlotAddPositionComponent(const QString& component_name_);
        /**
         * @fun SlotClearPositionComponents
         * @brief 清除点位的槽函数
         * @date 2025.2.26
         * <AUTHOR>
         */
        void SlotClearPositionComponents();
    signals:
        void SigImportCadCompleted(const std::vector<jrsdata::CadStruct>& param_); ///< 导入CAD完成信号
        void SignalCadEditStep(int state);  ///< CAD编辑信号
        void SignalCadEditParam(const jrsdata::CadEventParam& param_); ///< CAD编辑参数传递信号
        void SignalSelectSubMode(int state); ///< 子板选择信号

    private slots:
        /**
         * @fun SlotAddCad
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotAddCad();

        /**
         * @fun SlotDeleteCad
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
         //void SlotDeleteCad();
         /**
          * @fun SlotSelectComponentTrue
          * @brief
          * @date 2024.9.24
          * <AUTHOR>
          */
        void SlotSelectComponentTrue();
        /**
         * @fun SlotSelectComponentClear
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotSelectComponentClear();
        /**
         * @fun SlotConfirmComponentTrue
         * @brief 确认定位
         * @date 2025.2.26
         * <AUTHOR>
         */
        void SlotConfirmComponentTrue();
        /**
         * @fun SlotSelectComponentPositioned
         * @brief 点位表行点击槽函数
         * @param row
         * @param column
         * @date 2025.2.26
         * <AUTHOR>
         */
        void SlotSelectComponentPositioned(int row, int column);

        /**
         * @fun SlotRectSubBoard
         * @brief  矩形子板
         * @date 2024.11.26
         * <AUTHOR>
         */
        void SlotRectSubBoard();

        /**
         * @fun SlotPolyonSubBoard
         * @brief  多边形子板
         * @date 2024.11.26
         * <AUTHOR>
         */
        void SlotPolyonSubBoard();

        /**
         * @fun SlotSelectSubBoard
         * @brief  选择子板
         * @date 2024.11.26
         * <AUTHOR>
         */
        void SlotSelectSubBoard();

    private:
        /**
         * @fun Init
         * @brief 初始化
         * @date 2024.9.24
         * <AUTHOR>
         */
        void Init();
        /**
         * @fun InitMemeber
         * @brief 初始化变量
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitMemeber();
        /**
         * @fun InitConnect
         * @brief 初始信号链接
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitConnect();
        /**
         * @fun InitView
         * @brief 初始化界面
         * @date 2024.12.13
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun Disconnect
         * @brief 断开信号链接
         * @date 2024.9.26
         * <AUTHOR>
         */
        void Disconnect();

    private:
        Ui::ImportCadView* ui;
        AddCadView* add_cad_view; /**< 添加cad界面*/
    };
}
#endif