﻿2.1.4.fa0474b2
1. 开软件报错
2. 确认扫图bug优化
3. 远端位置设置错误解决
4. A/B点位置获取异常修复

2.1.4.e7fd56e3
1. 更新修正后的算法集成调用方式，目前集成mark和position

2.1.4.2ba3747
1：导入CAD操作修改框选位置和确认定位操作
2：导入CAD添加矩形子板操作
3：快捷键操作添加撤销和恢复
4：图片操作：100%显示、全屏显示、图形居中、放大、缩小
5：CAD快捷键操作：左右镜像、上下镜像、整体旋转90°、整体移动 顺时针逆时针暂无
6：元件操作：坏板标记、元件复制、元件粘贴、元件添加、元件删除
2.1.4.e91b4caf
1. 集成2.1.4.2ba3747版本功能
2. 修改算法运行bug
2.1.4.fa42d0bd
1. 修复无法修改本体框大小bug
2. 修复无法添加算法检测框
3. 修复编辑界面无法切换元件

2.1.4.1227
1. pad框添加bug优化
2. 3D数据显示
3. 高度基面校准
4. 模板和颜色参数绑定
2.1.9.103
1. 集成算法新版本：3D定位、 高度测量、二维码、ocr、ocv、定位、图形mark、锡桥、匹配mark
2. 扩展多连板、pad添加
3. 自动运行数据保存，界面NG/PASS结果更新 
2.1.10.106
1. 条码/锡桥算法更新优化
2. 切换元件崩溃
3. 算法执行时异常捕获，防止崩溃
2.1.10.1062
1.修复pad检测框截图错误问题
2.更新pad bug
3.更新规则多联板bug
4.更新区块算法
5.更新锡桥算法
6.更新高度算法、3D定位算法
2.1.10.1071
1. 更新3D定位、 高度测量
2. 更新复制元件崩溃
2.1.10.1081
1. 检测NG时保存NG数据
2. 图形切换界面功能
3. 带角度元件添加检测框和pad阵列等
2.1.10.1091
1. 2D、3D算法更新
2. 更新颜色调配界面
3. 优化MARK矫正
4. 优化CAD导入bug
5. 标准图片保存
6. 算法结果NG图片保存
2.1.10.1101
1. 更新3D显示界面
2. 更新调色界面
3. 3D 定位偏移对象改为检测框
4. 算法规格界面更新
5. 高度量测负值 bug 解决
2.1.11.11311
1. 更新3D定位 高度测量算法找不到基准面导致异常更新
2. 多连扳，选中定位bug修复
3. 缩略图保存功能(模板数据暂未保存)
4. 自动流程检测框，元件信息数据保存(模板数据暂未保存)
2.1.11.1141
1. 区块算法和焊锡算法崩溃更新
2. 缩略图和所有元件信息保存
2.1.11.1142
1. 更新缩略图缩放下限10
2. 保存的缩略图片格式和大图不影响
2.1.11.1151
1. 2D算法更新，解决旋转框异常问题
2. 修改调色未保存参数bug 
3. 修复调色v通道崩溃bug
4. 优化自动流程闪退卡死
5. 优化自动流程停止异常
2.1.11.1161
1. 修复鼠标释放会偏移一个像素bug
2. 取消中心十字线
3. 取消图片拖动区域限制  
4. 修改子板名称错误
5. 添加整板定位点不限制子板区域内添加
6. 保存工程时已经有默认图片，要提示是否覆盖
7. 添加检测框后默认搜索范围0.5
8. 缩略大图100%、10%时图片保存不下来
9. 维修站图片没有放到指定路径，里面的图片和名称也不对
10. 定位点未识别到还继续测试
11. 算法路径和维修站路径设置开关没有作用，默认一直保存数据
12. 模板调色界面闪退修复
2.1.11.1171
1. 修复列表显示
2. 修复添加检测框后需要拖动一下元件才显示
3. 切换元件默认选中第一个检测框
4. 添加delete删除元件防呆
5. 实时显示时添加十字线
6. 检测NG给维修站结果数据
7. 2D所有算法更新
2.1.12.1201
1. 3D 算法更新
2. solder算法更新
3. 修改模板界面不截取搜索区域问题
4. 修复列表显示
5. 修复添加检测框后需要拖动一下元件才显示
6. 切换元件默认选中第一个检测框
7. 添加delete删除元件防呆
8. 实时显示时添加十字线
9. 检测结果列表更新
10.算法检测结果渲染界面绘制ok ng框
11. 保存工程时提示是否保存果图片，NO则只保存工程数据，YES则保存工程数据和图片
2.1.12.1221
1. 自动流程检测时不贴大图，只贴检测框结果
2. 手动建模时，算法检测框结果更新
3. 2D算法更新
4. solder lead算法更新
5. 3D算法更新
6. 自动运行卡死崩溃更新
2.1.13.2042
1. 优化自动流程运行时内存异常
2. 优化料号检测时没有检测异常
3. 自动流程数据保存开启关闭勾选生效
2.1.15.2111
1. 区块算法更新
2. OCV，OCR，模板匹配算法更新
2.1.16.2181
1. mark扫描失败NG显示并继续执行
2. 高度测量更新，解决单个子检测框不出结果问题
2.1.16.2191
1. 自动成检测项NG时缺陷名称保存
2. 模板调试界面固定
2.1.17.2241
1. 检测界面信息补全
2. 2d/3D 算法更新
2.1.17.2281
1. 更新检测界面显示
2. 优化检测结果缺陷项值保存数据库
3. 时间二维码保存格式更改
2.1.18.3041
1. 算法执行信息保存
2. 更新检测界面显示
3. 优化添加算法时缺陷项选择异常bug
2.1.18.3061
1. 更新结构光版本
2. 更新PAD添加
2.18.3062
1. PAD添加功能优化
2.1.18.3072
1. 模板截图功能优化
2. 导入工程图片功能异常优化
2.1.19.3101
1. 检测界面信息显示问题修复
2. 颜色算法更新
3. 元件运行按钮保存算法信息异常修复
2.1.19.3111
1. 修复快捷按钮响应事件
2. 修复showlist显示问题
3. 显示界面按照轨道数量显示
2.1.19.3131
1. PAD检测框建模优化
2.1.19.3142
1. 机种统计直方图更新，元件NG横向图去掉其他，数量和标签对齐，统计错误类型和数量横向图，工单号修改刷新 
2. 更新CAD 角度时 同步到其他子板的相同元件
3. 修复pad 并排坐标无法更新到工程的问题
4. 修复pad模板截图反向的问题
5. 修复搜索框大小 
6. 锡桥算法更新优化
7. OCR算法更新优化
2.1.20.3181
1. ocv,锡桥算法更新
2. PAD操作优化
3. 元件显示界面功能
4. 算法名称列表更新
2.1.20.3182
1. 元件定位后检测
2. 3D定位更新：自己找基面时把基面优化稳定了。
3. 高度量测更新：解决表里问题。
2.1.20.3201
1. 颜色算法更i性能
2. lead算法更新
3. 结构光更新
4. PAD操作优化---画PAD只保留对称和单排
5. 修复点击切换元件没有立刻刷新数值，需要点检测框才有效
6. 角度方位是0~360，不等于360，实际显示有0和360同时存在修复
7. 大图拼图优化
8. 统一显示颜色
2.1.20.3211
1. 算法列表检测结果刷新修复
2. 元件列表选中蓝底去除
3. 渲染界面选中优化
4. 定位后检测更改成元件测试按钮
2.1.20.3222
1. 基面矫正算法和高度测量算法联合检测任意位置高度差
2. 解决算法结果检测框绘制方向异常
3. 选择pad直接默认刷新选择元件
4. OCV修复分数异常
5. 元件定位算法图形修复
6. 锡桥算法更新
7. mark算法更新
8. 成像速度优化、检测流程速度优化
9. 增加CAD导入子板编号
10. 检测界面选中板子数据刷新
11. 检测界面元件数据不累加，只显示当前板子
2.1.21.3241
1. CAD导入bug修复
2. 多个板子原件定位，跟随移动
3. 修复读取图片崩溃bug
2.1.21.3251
1. 移动检测框元件取消选中修复
2. 增加Pad,元件，检测框测试不测试勾选生效功能
3. 点击软件崩溃问题
4. PAD宽高对调问题
5. 多个板子无法移动对称pad问题
6. 大元件路径规划问题修复
7. 带角度元件定位算法拉偏检测框问题修复
8. 增加软件已经运行时提示窗口
2.1.21.3271
1. 2D算法更新锡桥，定位算法
2. 更新引脚检测结果选择哪个显示哪个
3. 优化规划路径
4. 操作崩溃bug修复
3.1.21.3281
1. 选中元件删除时，弹出的提示框即使点击否，元件列表还是会删除当前元件，但在图片上元件还是存在。只有重新加载工程元件列表才能恢复显示；
2. 删除元件，图片上已经删除，但是元件列表没有完全删除
3. 添加条码的名称错误，
4. 检测框通过检测框信息修改坐标或尺寸后，检测框状态跳到不测。
5. 添加MARK点，没有指定到0号整板，MARK名称同一级下面不能有重复名称。
6. 多连板扩展会在下面生成一个新的料号，没有料号名称，元件名称是机种名。无法选到。
7. 元件列表切换到定位点，检测框列表没有任何显示，状态是未选中，点击定位点也无法选中。
8. 跨群组无法通过点击检测框列表切换，
9. 检测框和PAD的坐标和尺寸依旧相反，0度元件
10. 热动现场添加PAD异常问题修复
2.1.21.3282
1. 更新添加mark，barcode异常修复
2.1.21.3283
1. 更新切换同料号元件无法添加模板，无法检测问题修复
2.1.21.3284
1. 更新mark多模板bug
2. 更新mark切换异常
2.1.22.3301
1. 一组下面同一个检测框做多个测试没有保存信息 
2. 元件测试不会刷新所有检测框状态 
3. 通过检测框信息修改尺寸后，检测框状态切换到不测试 
4. 删除检测框后，已经删除的检测框状态还会保存，没有删除
5. 条码和坏板标记 
6. 算法测试有角度旋转时渲染绘制方向反了的问题修复
2.1.22.4011
1. 所有算法更新
2. 优化检测框乱跳问题
3. 优化删除检测框CAD框消失问题
4. 检测框结果只显示一个结果问题修复
2.1.22.4012
1. 所有算法更新，解决乱飞问题
2. 子板条码存储
3. 元件库集成
4. 解决pad并排生成聚到一起问题
5. 解决拍完mark不运行问题
2.1.22.4031
1. 锡桥算法/定位(图形)二维码算法更新
2. 高度测量算法更新
3. 元件库更新
4. 检测框删除NG不删除修正
2.1.23.4051
1. 自动运行时截图异常修复
2. 切换元件检测框软件崩溃修复
3. FOV路径规划优化
2.1.23.4061
1. 修复拍mark点异常
2. 增加mark失败时图片保存
2.1.23.4071
1. 引脚检测添加高度检测框渲染
2. 3D定位优化部分大元件抓不准
3. 锡桥索引屏蔽bug更新
4. 算法参数互相影响优化
2.1.23.4081
1. 优化算法参数调整异常
2. 增加元件检测时角度的保存
2.1.23.4082
1. 高度测量：解决纬亚产品相对计算-9999问题；
2. 引脚检测：增加自动找基准后测量引脚高度；
3. 引脚检测：更改引脚高度测量方式为取中间60%数据做高度计算
4. 3D定位结果更新优化
2.1.23.4091
1. 料号检测时结果框绘制bug修复
2. 子板整板操作优化，badmark添加
3. 子检测框的检测框跟随bug优化
4. 3D/高度/引脚算法更新
5. 图形/形状增加角度测量屏蔽功能选项
2.1.23.4092
1. 增加mark点结果保存功能，和元件保存一样，勾选则保存，不够选不保存
2. 形状mark更新
3. lead算法更新引脚高度检测框位置
2.1.23.4101
1. MARK形状算法更新
2. 切换图片通道崩溃问题优化
3. 更新模板异常错乱问题
4. 更新mark元件检测异常修复
2.1.23.4102
1. FPN添加新的
2. 定位算法和OCV算法更新调色后崩溃
3. 高度算法更新
2.1.23.4111
1. ocv、ocr算法更新
2. 建模卡顿问题优化
3. 元件旋转后不测选项被勾选优化
4. 删除的检测框结果还存在问题优化
2.1.24.4161
1. 在线调试基本功能实现
2. 算法更新
2.1.24.4171
1. 动态调试卡顿优化
2. 主界面固定
3. 元件点选切换优化
4. 2D算法更新
5. 自动流程崩溃
2.1.25.4191
1. 锡桥手动屏蔽失效bug修复
2. ocv,ocr，图像极反元件检测和检测框检测不一致bug修复
3. 动态调试下定位拉正跑偏修复
2.1.25.4211
1. 自动添加算法检测框功能
2. 料号定位后拉正元件框
3. 元件检测，检测框检测输入数据保持一致
4. 添加算法界面修改
5. ocr算法更新
2.1.25.4221
1. 基面矫正算法更新
2. 添加元件基面矫正功能(目前只有元件基面矫正，其他矫正方式都舍弃)
2.1.25.4222
1. 开放元件角度/pad带角度绘制优化
2. 按照操作规范完善操作
2.1.25.4231
1. 界面固定
2. 显示列表宽度保存
3. 单板扩展多连板后，生成的子板没办法旋转角度
4. 导入多连扳CAD之后无法整体旋转
5. 右下角索引图增加开关按钮
6. 退出动态调试后图片和元件对应不上，无法滚轮缩放图片，无法整体移动，无法单独移动
7. 重新打开工程和图片后还是不能缩放并且元件偏移，
8. 试后列表没有刷新结果、调试OK后没有刷新检测结果也没有显示绿色和红色标识、子板号显示的是子板名称
9. MARK点无法单独保存结果
10. 定位点的搜索范围上限调整为3000像素
2.1.25.4241
1. 软件关闭无法正常退出修复
2. 更新没有整个工程旋转，只有单个CAD在原地旋转 
3. 检测框列表分组名称按照：元件、本体、焊盘1、焊盘2、排列
4. 修复pad对称ID为0
5. 修复点击图像处理，调整左右功能区之后大图会自动缩放到全图显示
6. 检测框赋值工复制功能
7. 打开工程按照时间排序
8. 导入CAD异常符号导致失败修复
9. solder算法更新
2.1.25.4251
1. 手动pad下画框长宽颠倒修复
2. pad下面检测ng没有更新渲染界面修复
3. 动态调试不能拖动修复
4. 自动添加框和元件库添加框后需要手动切换才显示修复
5. 调色空间显示上一个模板数据修复
6. 调色控件崩溃修复
7. 检测框列表选中检测框，但是点击图像还是会切换到其他检测框修复
8. 删除元件的快捷按钮不要把同料号内其它元件检测框删除，只删除选中元件修复
9. mark算法更新
10. 修改角度顺时针逆时针倒置快捷键功能
2.1.25.4252
1. 动态调试偏移优化
2. location组不更新优化
3. 速度优化，减少线程数量使用
2.1.26.4261
1. 算法界面固定
2. 单个元件检测完 根据算法结果修改tab显示颜色
3. 整板 镜像、移动、旋转、元件角度颠倒、元件角度旋转
4. 子板：镜像、旋转、元件角度颠倒
2.1.26.4281
1. 不规则子板功多联板能添加
2. 子板角度更改同步到其他子板的为偏差值，而不是直接修改其他子板角度
3. 检测结束后直接下板，而不是等到过出来再下板
4. 检测框列表tab显示优化
5. 拍照时间和检测时间显示优化
2.1.26.4301
1. 在线调试功能优化(一个元件NG不显示图片/停机/不停机调试/子板号显示/上下键按钮/搜索功能等)
2. 渲染界面鼠标单击不灵敏优化
3. 修复单个子板无法定位问题
4. OCV/MARK形状算法更新
5. 锡焊算法更新
2.1.26.4301
1. pad测试不测试功能修复
2. 修复删除检测框后检测状态更新问题
3. mark识别不到闪退修复
4. 检测的时间统一
5. 在线调试时，检测按钮导致居中优化
6. 形状匹配算法更新
7. MARK形状算法更新
8. ocr算法更新
9. 焊锡算法更新
2.1.28.5058
1. pad检测框结果的颜色和pad ID 不一致问题
2. 元件库pad的检测框显示调整方向
3. 鼠标左键拖拽bug修复
4. 子检测框调整位置
5. 解决渲染界面需要缩放一下才会显示一致的bug
6. 新增所有检测框旋转90度
7. bad mark,子板barcode 功能实现
8. 3D定位精度优化
9. 锡焊已解决，在开发本体识别
10.轮廓定位算法已提交
11. ocr，ocv,元件形状和图像算法更新
2.1.29.5101
1. 输出csv报表数据，在report路径下
2. 优化操作，alt只能更改元件本体位置，ctrl更改目标检测框位置和大小
3. mark失败板子显示skip
4. mark形状算法优化
2.1.29.5141
1. 自动流程检测结束后直接出结果不用等待下料完成
2. mark识别失败后显示mark图片
3. mark算法更新
4. 3D算法更新
2.1.29.5151
1. 规格管控功能添加
2. 维修站数据保存修改为一个按钮
3. good ng 数据名称统一
4. 磁盘存储时容量不够提醒
5. 3D算法更新
2.1.29.5152
1. MARK算法更新
2. 焊盘定位算法更新(可测试，需要建pad)
3. 检测框列表选中检测框，但是点击图像还是会切换到其他检测框(修复)
4. 自动规格设置2D 算法已经完全更新支持，3D算法未更新
5. 元件组下算法测试ok，状态ng修复
2.1.30.5171
1. solder算法更新支持本体检测功能
2. 工程文件版本兼容
3. 更新检测框信息保存维修站显示绘制，需要和维修站配合测试
4. 3D定位算增加对称高度测量模式
2.1.30.5172
1. 优化切换元件时，选中的算法被切换优化
2. 检测框修改时，模板中的检测框位置没有跟随修改优化
3. 3D算法增加模板匹配功能
2.1.30.5191
1. 更新mark显示
2.1.30.5201
1. 精简算法信息保存路径
2. 复制出来的工程导入进来名称没有变化优化
3. 算法分母自动设置
4. mark显示优化
2.1.30.5211
1. 修复mark显示崩溃问题
2. 更新ocv算法，优化效果
3. 3D定位算法优化
2.1.30.5221
1. badmark优化，可以正常使用
2. 上午的崩溃优化，由于检测框异常导致
3. 2D算法更新：1.边缘检测崩溃优化2.锡桥算法支持元件检测3.ocv崩溃优化4.shape和轮廓算法优化
4. 3D显示控件更新：1. 颜色尺度参数本地化2. 新增数据镜像模式3. 新增自动旋转功能4. 新增颜自适应功能5. 新增界面曲线显示高度自适应功能
5. 自动添加检测框列表bug修复
2.1.31.5281
1. 更新镍片定位算法
2. 坏板元件不计入统计
3. 焊锡检测算法轮廓显示功能
2.1.31.5291
1. 增加检测时扫图方向选择
2. 超大图拖拽优化
3. 回零超时时间设置为1分钟
2.1.32.6101
1. 更新mark形状算法
2. 增加多工程添加功能
3. 增加AB板二维码关联功能
4. 结构光功能更新，优化触发流程提高效率
5. 酸价计算轮廓结果显示功能
6. 拍照起点根据最后一个mark点位置自动确定
7. 孝感超大图读取问题修复
2.1.32.6111
1. 导入元件库死锁问题
2. AB板子条码关联功能测试ok
3. 保存报表功能修复
2.1.33.6161
1. 导入工程后，执行检测框软件闪退
2. 运行定位点软件闪退
3. 报表导出优化
4. 3D定位优化，减少3D误判
5. 结构光库更新，需要重新烧入固件
2.1.33.6191
1. 集成最新的拼图功能
2.1.34.6241
1. 拍图时移动到位置后，增加30ms的延时
2. 优化了参数设置界面，将其按照类别分开设置
3. 集成了mark图像匹配优化识别效果
4. 增加自动子板排序功能，手动排序未实现
5. showlisht 选中子板 渲染界面同步选中子板
6. 编辑页面角度 精度调整为0.1
7. 修复偶发点击元件崩溃问题