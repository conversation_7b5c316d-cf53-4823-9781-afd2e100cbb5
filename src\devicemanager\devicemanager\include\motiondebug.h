/*****************************************************************//**
 * @file   motiondebug.h
 * @brief  运控调试相关函数
 * @details 
 * <AUTHOR>
 * @date 2024.9.2
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.9.2         <td>V2.0              <td>zhaokunlong      <td>                      <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

#ifndef __JRSMOTIONMOVE_H__
#define __JRSMOTIONMOVE_H__

 //STD
#include <iostream>
#include <sstream>
#include <iomanip>
#include <map>

 //Custom
#include "pluginexport.hpp"
#include "deviceparam.hpp"
#include "motion.h"
#include "coreapplication.h"
#include "viewparam.hpp"
// Third
#include "nlohmann/json.hpp"

using JSON = nlohmann::json;


namespace jrsdevice
{
    using Function = std::function<void(const jrsdata::DeviceParamPtr& param_)>;
    class JRS_AOI_PLUGIN_API MotionDebug
    {
        public:
            MotionDebug(std::shared_ptr<Motion>& motion,bool& running);
            ~MotionDebug();

            /**
             * @fun SetDeviceTrack
             * @brief 设置运控轨道配置
             * @param track 轨道配置
             * <AUTHOR>
             * @date 2024.9.2
             */
            void SetDeviceTrack(JSON track);

            /**
             * @fun SetMotionSetting
             * @brief 运控轨道设置信息(进料方向、出料方向、模式等)
             * @param track 轨道配置
             * <AUTHOR>
             * @date 2024.9.2
             */
            void SetMotionSetting(jrsdata::MotionSetting motion);

            /**
             * @fun ExcuteCommond
             * @brief 执行绑定的函数
             * @param device_param_ 
             * <AUTHOR>
             * @date 2024.9.2
             */
            void ExcuteCommond(const jrsdata::DeviceParamPtr& device_param_);
        private:

            // 运控处理函数绑定
            void MotionFunctionBind();

            // 报警清除
            void ClearAlarm(const jrsdata::DeviceParamPtr& param_);

            // 停止
            void AskStop(const jrsdata::DeviceParamPtr& param_);

            // 初始化
            void AskInitial(const jrsdata::DeviceParamPtr& param_);

            // 单轴相对移动
            void SingleMove(const jrsdata::DeviceParamPtr& param_);

            // 单轴绝对移动
            void SingleMovea(const jrsdata::DeviceParamPtr& param_);

            // 双轴绝对移动
            void LopTo(const jrsdata::DeviceParamPtr& param_);

            // Jog
            void Jog(const jrsdata::DeviceParamPtr& param_);

            // 控制通用输出
            void Output(const jrsdata::DeviceParamPtr& param_);

            // 控制轨道顶升气缸或挡板气缸
            void CylinderOutput(const jrsdata::DeviceParamPtr& param_);

            // 上下料
            void LoadAndUnLoad(const jrsdata::DeviceParamPtr& param_);

            // 轨道能否执行上下料判断
            bool TrackAvailable(jrsdevice::TrackIndex index);

            // 停板位置
            void BoardStop(const jrsdata::DeviceParamPtr& param_);

            // 远端位置
            void BoardRemote(const jrsdata::DeviceParamPtr& param_);

            // 回零
            void Home(const jrsdata::DeviceParamPtr& param_);

            // 根据name获取对象
            JSON GetObjFromName(JSON Obj, std::string name);

            // 气缸控制
            void CylinderControl(JSON Obj, const jrsdata::DeviceParamPtr& param_);
           
        private:  
            std::shared_ptr<Motion> motion_ptr; /**< 运控实例*/
            std::map<std::string, Function> motion_func_map;                       /**< 运控函数绑定map */
            std::mutex motion_mutex;                                               /**< 运动锁 */
            bool process_running;                                                  /**< 流程运行中 */

            std::map<jrsdevice::TrackIndex, bool> track_running_state;             /**< 轨道上下料执行状态 */

            bool exit_;                                                            /**< 线程运行标志 */
            std::thread motion_thread;                                             /**< 运控状态数据获取线程 */
            JSON track_setting;                                                    /**< 轨道配置 */
            jrsdata::MotionSetting motion_setting;                                 /**< 运控轨道设置信息 */

    };
}

#endif // !__JRSMOTIONMOVE_H__
