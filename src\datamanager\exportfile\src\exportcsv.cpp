#include "exportcsv.h"
#include "fileoperation.h"

namespace jrsdata
{
    ExportCsv::ExportCsv()
    {
    }

    ExportCsv::~ExportCsv()
    {
    }

    bool ExportCsv::ExportToFile(const std::string& file_path, const std::vector<std::string>& header, const std::vector<std::vector<std::string>>& data_)
    {

        auto save_file_name = file_path+".csv";
        auto dir_name = std::filesystem::path(save_file_name).parent_path().string();
        bool res = jtools::FileOperation::JRSCreateDirectory(dir_name);
        if (!res)
        {
            return false;
        }
        std::ofstream ofs(save_file_name);
        if (!ofs.is_open()) return false;
        ofs << "\xEF\xBB\xBF"; //! 写入 UTF-8 BOM，防止中文乱码

        //if (!header.empty()) 
        //{
        //    for (const auto& row : data_) 
        //    {
        //        if (row.size() != header.size()) 
        //        {
        //            return false;  // 表头列数与某行数据列数不一致
        //        }
        //    }
        //}

        //! 写表头
        for (size_t i = 0; i < header.size(); ++i) 
        {
            ofs << EscapeStr(header[i]);
            if (i + 1 < header.size()) ofs << ",";
        }
        ofs << "\n";
        //! 写内容
        for (const auto& row : data_) 
        {
            for (size_t i = 0; i < row.size(); ++i) 
            {
                ofs << EscapeStr(row[i]);
                if (i + 1 < row.size()) ofs << ",";
            }
            ofs << "\n";
        }

        return true;
    }

    std::string ExportCsv::EscapeStr(const std::string& field)
    {
        if (field.find_first_of(",\"\n") != std::string::npos) {
            std::string escaped = "\"";
            for (char c : field) {
                if (c == '"') escaped += "\"\""; // 引号转义为双引号
                else escaped += c;
            }
            escaped += "\"";
            return escaped;
        }
        return field;
    }

}
