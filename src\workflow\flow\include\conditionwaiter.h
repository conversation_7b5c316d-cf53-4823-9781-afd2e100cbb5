#ifndef AB9D29E1_5486_400E_86F0_8BD0C76F8FF5
#define AB9D29E1_5486_400E_86F0_8BD0C76F8FF5
#include <condition_variable>
#include <functional>
#include <mutex>
#include <vector>

namespace jrsworkflow
{
    
    class ConditionWaiter 
    {
        public:
           
            ConditionWaiter() = default;
            ~ConditionWaiter() = default;
            void AddCondition(std::function<bool()> condition);
            void Wait();
            void Notify();
            
        private:
            std::mutex mutex_;
            std::condition_variable cond_var_;
            std::vector<std::function<bool()>> conditions_;
        };
    using ConditionWaiterPtr = std::unique_ptr<ConditionWaiter>;
}
#endif /* AB9D29E1_5486_400E_86F0_8BD0C76F8FF5 */
