project(unittest)
#字符集unicode
Add_Definitions(-DUNICODE -D_UNICODE)
# Find includes in the build directories
set(CMAKE_INCLUDE_CURRENT_DIR ON)
#导出宏
add_compile_definitions(JRS_AOI_PLUGIN_EXPORTS)

set(core_test_srcs
    coretest/logtest.cpp
    )

    
set(data_test_srcs
datatest/datatest.cpp
)

add_executable(${PROJECT_NAME}

    ${core_test_srcs}
    ${data_test_srcs}

)

target_include_directories(${PROJECT_NAME}
                           PUBLIC ${DIR_PROJECT_CURRENT}/src/core/common/include
                           )


target_link_libraries(${PROJECT_NAME}
                    datamanager
                    parametermanager
                    gtest
                    gtest_main
)


set_target_properties(${PROJECT_NAME} PROPERTIES
  LINK_FLAGS /SUBSYSTEM:CONSOLE
)


set(EXECUTABLE_OUTPUT_PATH ${DIR_PROJECT_CURRENT}bin)



# target_compile_options(${PROJECT_NAME} PRIVATE
#     # 设置编译器警告等级
#     $<$<CXX_COMPILER_ID:MSVC>:/W1>
#     $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra>
#     $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra>
# )


