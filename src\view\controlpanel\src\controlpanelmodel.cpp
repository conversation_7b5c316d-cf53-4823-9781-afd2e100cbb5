#include "controlpanelmodel.h"

namespace jrsaoi
{
    ControlPanelModel::ControlPanelModel(const std::string& name) :ModelBase(name)
        , auto_run_panel_param_ptr(std::make_shared<jrsdata::ControlPanelViewParam>())
    {
        InitMember();
    }
    ControlPanelModel::~ControlPanelModel()
    {
    }
    int ControlPanelModel::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;
    }
    const jrsdata::ViewParamBasePtr ControlPanelModel::GetAutoRunParam()
    {
        auto data_process = project_param_instance.GetProjectDataProcessInstance();
        auto param_process = project_param_instance.GetParameterProcessInstance();

        auto_run_panel_param_ptr->current_project_param = data_process->GetProjectParam();

        if (!auto_run_panel_param_ptr->current_project_param)
            return auto_run_panel_param_ptr;

        auto_run_panel_param_ptr->setting_param_ptr = param_process->GetSettingParams();
        auto_run_panel_param_ptr->multi_project_param_map = data_process->GetMultiProjectMap();

        const auto& link_project_name_opt = auto_run_panel_param_ptr->current_project_param->link_project_name;
        if (link_project_name_opt && !link_project_name_opt->empty())
        {
            auto_run_panel_param_ptr->is_load_link_project = data_process->IsLoadLinkProject(*link_project_name_opt);
        }

        return auto_run_panel_param_ptr;
    }

    const jrsdata::ControlPanelViewParamPtr& ControlPanelModel::GetModelData()
    {
        return auto_run_panel_param_ptr;
    }

    int ControlPanelModel::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        if (!param_)
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        if (param_->event_name == jrsaoi::UPDATE_SYSTEM_STATE_EVENT)/**<系统状态更新*/
        {
            auto system_state_param_ptr = std::dynamic_pointer_cast<jrsdata::SystemStateParam>(param_);
            UpdateSystemState(system_state_param_ptr);
            return jrscore::AOI_OK;
        }

        auto_run_panel_param_ptr = std::dynamic_pointer_cast<jrsdata::ControlPanelViewParam>(param_);
        if (!auto_run_panel_param_ptr)
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
        }
        return jrscore::AOI_OK;
    }

    void ControlPanelModel::InitMember()
    {
        auto_run_panel_param_ptr->machine_state_param = std::make_shared<jrsdata::MachineStateViewParam>();
    }

    void ControlPanelModel::UpdateSystemState(const jrsdata::SystemStateParamPtr& system_state_ptr_)
    {
        auto_run_panel_param_ptr->machine_state_param->current_stop_state;
        auto motion_track_state = system_state_ptr_->current_system_states[jrsdata::SystemStateParam::SystemItem::MOTION_TRACK_STATE].level;
        auto motion_axis_state = system_state_ptr_->current_system_states[jrsdata::SystemStateParam::SystemItem::MOTION_AXIS_STATE].level;
        if (motion_track_state != jrsdata::SystemStateParam::StateLevel::OK || motion_axis_state != jrsdata::SystemStateParam::StateLevel::OK)
        {
            auto_run_panel_param_ptr->machine_state_param->current_stop_state = jrsdata::MachineStateViewParam::StopState::ALARM;
        }
        auto_run_panel_param_ptr->event_name = jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME;
    }


}
