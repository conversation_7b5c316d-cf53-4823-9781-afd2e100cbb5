/*********************************************************************
 * @file   thumbnailnavigation.h
 * @brief  缩略图导航类，用于在界面上显示缩略图，并支持视野区域控制。
 *         支持图片显示和渲染区域设置，提供视野转换工具以保证缩略图和
 *         实际图像之间的比例一致
 *
 * @date   2024.03.11
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef THUMBNAIL_NAVIGATION_H
#define THUMBNAIL_NAVIGATION_H

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "opencv2/core/mat.hpp"
#include <qimage.h>
#pragma warning(pop)

class ThumbnailNavigation
{
public:
    /**
     * @brief 构造函数，初始化缩略图窗口的宽度和高度。
     * @param w 缩略图窗口宽度
     * @param h 缩略图窗口高度
     */
    ThumbnailNavigation(int w, int h);
    virtual ~ThumbnailNavigation() {}

    /**
     * @brief 更新缩略图显示,由ui类实现具体更新逻辑。
     */
    virtual void Update() = 0;
    /**
     * @brief  设置显示图片.
     * @param  image   显示图片,可以是缩略图,也可以是大图,需要保持比例
     * @param  image_w 图片实际尺寸 宽
     * @param  image_h 图片实际尺寸 高
     */
    bool SetImage(const cv::Mat& image, int image_w, int image_h);

    /**
     * @brief 清除当前显示的图像。
     * @note 仅清除显示的图像内容，不影响视野范围设定。
     */
    void ClearImage();
    /**
     * @brief 设置渲染窗口的区域大小，用于视图显示。
     * @param region_w 渲染窗口区域的宽度
     * @param region_h 渲染窗口区域的高度
     * @return 设置成功返回 `true`，失败返回 `false`
     */
    bool SetRegion(int region_w, int region_h);
    /**
     * @brief 设置缩略图窗口的尺寸。
     * @param w 缩略图窗口的宽度
     * @param h 缩略图窗口的高度
     */
    void SetWindowSize(int w, int h);
    /**
     * @brief 设置视野范围。
     * @param left 视野范围左边界
     * @param top 视野范围上边界
     * @param right 视野范围右边界
     * @param bottom 视野范围下边界
     */
    void SetViewport(float left, float top, float right, float bottom);
    /**
     * @brief 获取当前视野的参数。
     * @param[out] x 左上角X坐标
     * @param[out] y 左上角Y坐标
     * @param[out] w 视野宽度
     * @param[out] h 视野高度
     */
    void GetViewport(float& x, float& y, float& w, float& h);
    /**
     * @brief  cvMat转QImage,通用接口.
     */
    static QImage Mat2QImage(const cv::Mat& mat);

protected:
    void CreateScale();

    void GetRenderRegionLimit(float& min_x, float& min_y, float& max_x, float& max_y);
    void SetRenderRegionLimit(float min_x, float min_y, float max_x, float max_y);

    void SetThumbnailViewport(float x, float y);
    void GetThumbnailViewport(float& x, float& y, float& w, float& h);

    void ImageRegionToViewportRegion(float& x, float& y, float& w, float& h);
    void ViewportRegionToImageRegion(float& x, float& y, float& w, float& h);

protected:
    QImage m_qimage; ///< 缩略图转格式方便渲染

private:
    int m_w;        ///< 缩略图界面宽度-UI的
    int m_h;        ///< 缩略图界面高度-UI的

    int image_w = 0; ///< 图片实际尺寸 宽
    int image_h = 0; ///< 图片实际尺寸 高

    float m_scale;          ///< 缩放比例,缩略图和实际图片的比例

    float m_viewport_x = 0; ///< 图片视野左上角x坐标
    float m_viewport_y = 0; ///< 图片视野左上角y坐标
    float m_viewport_w = 0; ///< 图片视野宽
    float m_viewport_h = 0; ///< 图片视野高

    float m_region_min_x = 0; ///< 渲染区域限制，控制等比例图片居中渲染
    float m_region_min_y = 0;
    float m_region_max_x = 0;
    float m_region_max_y = 0;

    float m_region_x = 0;  ///< 缩略图上表示视野区域的矩形框
    float m_region_y = 0;
    float m_region_w = 0;
    float m_region_h = 0;

    cv::Mat m_thumbnail; ///< 缩略图

};

#endif // !THUMBNAIL_NAVIGATION_H
