﻿#ifndef __BARCODEDEVICE_H__
#define __BARCODEDEVICE_H__
//STL
#include <vector>
#include <string>
#include <functional>
namespace jrsdevice
{
    using BarcodeCallback = std::function<void(const std::string&)>;
    class BarcodeDevice
    {
        public:
            BarcodeDevice();
            virtual ~BarcodeDevice();

        public:
            /**
             * @fun EnumDeviceLists 
             * @brief 获取设备SerialNumber
             * @param serial_numbers 返回设备的SerialNumber列表
             * @return 无设备返回false
             * @date 2025.5.28
             * <AUTHOR>
             */
            virtual bool EnumDeviceLists(std::vector<std::string>& serial_numbers) = 0;
            /**
             * @fun OpenDeviceBySerialNumber 
             * @brief 根据SerialNumber打开设备
             * @param serial_numbers
             * @return 返回成功状态
             * @date 2025.5.28
             * <AUTHOR>
             */
            virtual bool OpenDeviceBySerialNumber(std::string& serial_numbers) = 0;
            /**
             * @fun OpenDevice
             * @brief 打开第一个设备
             * @return 返回成功状态
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool OpenDevice() = 0;
            /**
             * @fun CloseDevice 
             * @brief 关闭设备
             * @return 返回成功状态
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool CloseDevice() = 0;
            /**
             * @fun StartGrabing 
             * @brief 开始采集二维码
             * @return 返回成功状态
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool StartGrabing() = 0;
            /**
             * @fun StopGrabing 
             * @brief 结束采集二维码
             * @return 返回成功状态
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool StopGrabing() = 0;
            /**
             * @fun SetBarcodeCallback 
             * @brief 设置获取二维码回调函数
             * @param callback 回调函数
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual void SetBarcodeCallback(BarcodeCallback callback) = 0;
            /**
             * @fun SetEnableSaveImage 
             * @brief 设置是否保存二维码图片
             * @param save_image 是否保存图片
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual void SetEnableSaveImage(bool save_image) = 0;
            /**
             * @fun SetBarcodeImagePath 
             * @brief 设置二维码保存图片路径
             * @param image_path 二维码图片路径
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual void SetBarcodeImagePath(std::string image_path) = 0;

            /**
             * @fun IsOpenDevice
             * @brief 返回打开设备状态
             * @return true if the device is open;
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool IsOpenDevice() = 0;

            /**
             * @fun IsGrabing
             * @brief 返回采集设备状态
             * @return true if it is grabbing;
             * @date 2025.5.26
             * <AUTHOR>
             */
            virtual bool IsGrabing() = 0;
    };
}
#endif