﻿#pragma once
/*****************************************************************
 * @file   padmanager.h
 * @brief   pad组管理、 实现组间复制依然在同一组内、Pad组的管理
 * @details   TODO: 根据每个元件 分配group_name,否则显示多个元件的pad组时时会出现问题 !!!!!2025/3/6
 * <AUTHOR>
 * @date 2025.2.24
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2025.2.24          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //Custom
#include "graphicsabstract.hpp"
class GraphicsManager;

class PadManager
{
public:
    PadManager(GraphicsManager* const graphics_manager_);
    ~PadManager();
    /**
     * @fun GetPadGraoupName
     * @brief  获取pad组名称
     * @return
     * <AUTHOR>
     * @date 2025.2.24
     */
    std::string GetNewPadGroupName(const GraphicsID& graphics_id_);

    /**
     * @fun UpdatePadGroupName
     * @brief  更新到pad 指定组内
     * @param group_name_
     * @param gh
     * @return
     * <AUTHOR>
     * @date 2025.3.4
     */
    int UpdatePadGroupName(const std::string& group_name_, std::shared_ptr<GraphicsAbstract> gh);

    /**
     * @fun GetPadGroupId
     * @brief  根据group_name 创建Group ID 组内唯一
     * @param group_name_
     * @return
     * <AUTHOR>
     * @date 2025.2.25
     */
    int GetNewPadGroupID(const GraphicsID& graphics_id_, const std::string& group_name_);
    /**
     * @fun AddPadGroup
     * @brief  增加pad组,同时添加到graphics_manager中  如果没有自身没有分组，则自己创建一个分组
     * @param group_name_
     * @param graphics
     * @return
     * <AUTHOR>
     * @date 2025.2.24
     */
    int AddPadGroup(std::shared_ptr<GraphicsAbstract> graphics_, const std::string& pad_group_ = "");

    /**
     * 获取同组的其他组
     */
    std::vector < std::weak_ptr<GraphicsAbstract>> GetPadGroups(const GraphicsID& graphics_id_, const std::string& group_name_);
    /**
     * @fun AddPadGroup
     * @brief  删除pad组，同时删除graphics_manager中的pad group
     * @param group_name_
     * @param graphics
     * @return
     * <AUTHOR>
     * @date 2025.2.24
     */
    int DeletePadGroup(std::shared_ptr<GraphicsAbstract> graphics);

    /**
     * @fun GetGraphicsManager
     * @brief  获取图形管理指针
     * @return
     * <AUTHOR>
     */
    GraphicsManager* const GetGraphicsManager();
    /**
     * @fun CleareAllPadGroup
     * @brief 清除所有pad组
     * @return
     * <AUTHOR>
     * @date 2025.3.6
     */
    int CleareAllPadGroup();

private:
    std::string JoinPadGroupName(int pad_group_id_);
    int SplitPadGroupName(const std::string& pad_group_name_);
    GraphicsManager* const _graphics_manager;
    std::unordered_map<GraphicsID, std::unordered_map<int/*<group_id*/, std::vector<std::weak_ptr<GraphicsAbstract>>>,
        GraphicsIDHash, GraphicsIDEqual> _component_pad_group_map; /**< 每个原件下唯一的原件ID*/
    //std::unordered_map<int, std::vector<std::weak_ptr<GraphicsAbstract>>>  _name_map_pad_groups; /**< 组映射到pads */
};

