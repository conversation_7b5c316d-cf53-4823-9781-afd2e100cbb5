#include "topic.h"
#include "coreapplication.h"
namespace jrscore
{
    Topic::Topic (const std::string& topic_name_param) :topic_name (topic_name_param)
    {
    }
    Topic::~Topic ()
    {
    }
    int Topic::AddCallback (SubscribeCallbackHelperPtr& cb_)
    {
       std::lock_guard<std::recursive_mutex> lock (call_back_mutex);
       auto iter = std::find_if (topic_callback_v.begin (), topic_callback_v.end (), [cb_](const jrscore::SubscribeCallbackHelperPtr& rhs)->bool
                                 {
                                     return cb_->GetSubName () == rhs->GetSubName ();
                                 });
       if (iter == topic_callback_v.end ())
       {
           topic_callback_v.emplace_back (cb_);
       }
       else
       {
           std::cout << "subscriber has subscribed this topic" << std::endl;
           return -1;
       }
       return 0;
    }
    int Topic::RemoveCallback (SubscribeCallbackHelperPtr& cb_)
    {
        std::lock_guard<std::recursive_mutex> lock (call_back_mutex);

        auto sub_name_to_find = cb_->GetSubName ();

        auto iter = std::find_if (topic_callback_v.begin (), topic_callback_v.end (), [sub_name_to_find](const jrscore::SubscribeCallbackHelperPtr& rhs)->bool
                                  {
                                      return sub_name_to_find == rhs->GetSubName ();
                                  });
        if (iter != topic_callback_v.end ())
        {
            topic_callback_v.erase (iter);
        }
        else
        {
            std::cout << "not exit this subname!" << std::endl;

            return -1;
        }
        return 0;
    }
    int Topic::NotifyOne (const std::string& sub_name, const std::vector<std::any>& args)
    {
       //std::lock_guard<std::recursive_mutex> lock(call_back_mutex);

       for (auto& value : topic_callback_v)
       {
           if (value->GetSubName () == sub_name)
           {
               return value->CallBack (args);
           }

       }
       return jrscore::CoreError::E_AOI_CORE_NO_SUBSCRIBER;
    }
    int Topic::NotifyAll (const std::vector<std::any>& args)
    {
       //std::lock_guard<std::recursive_mutex> lock(call_back_mutex);

       for (auto& value : topic_callback_v)
       {

           value->CallBack (args);
       }
       return 0;
    }
}
