/*****************************************************************//**
 * @file   logmanager.h
 * @brief  日志管理器
 * @details
 * <AUTHOR>
 * @date 2024.1.24
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.1.24         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __LOGMANAGER_H__
#define __LOGMANAGER_H__

 //STD
#include <memory>
#include <unordered_map>
#include <sstream>

//Custom
#include "abstractlogger.h"
#include "errorhandler.h"
#include "pluginexport.hpp"
namespace jrscore
{

    struct LogManagerImplData;



    class JRS_AOI_PLUGIN_API LogManager
    {

    public:
        LogManager();
        ~LogManager();


        /**
         * @fun SetLogFolderPath
         * @brief 设置日志保存文件夹路径
         * @param path_ [IN] 文件夹路径
         * @date 2024.1.24
         * <AUTHOR>
         */
        void SetLogFolderPath(const std::string& path_);

        /**
         * @fun GetFolderPath
         * @brief 获取日志保存文件夹路径
         * @return 返回日志文件夹保存路径
         * @date 2024.1.24
         * <AUTHOR>
         */
        const std::string& GetFolderPath() const;

        /**
         * @fun SetLogPosition
         * @brief 设置日志输出位置
         * @param pos_ [IN] 日志保存位置
         * @date 2024.1.24
         * <AUTHOR>
         */
        void SetLogPosition(const LogPosition pos_);

        /**
         * @fun SetLogMode
         * @brief 设置日志输出模式 同步/异步
         * @param mode_[IN] 输出模式
         * @date 2024.1.24
         * <AUTHOR>
         */
        void SetLogMode(const LogMode mode_);

        /**
         * @fun SetLogOutputLevel
         * @brief 设置日志输出等级
         * @param level_ [IN] 日志等级
         * @date 2024.1.24
         * <AUTHOR>
         */
        void SetLogOutputLevel(const LogLevel level_);


        /**
         * @fun SetLogCallBack
         * @brief 设置日志处理回调函数
         * @param cb_ [IN] 处理回调函数
         * @date 2024.1.24
         * <AUTHOR>
         */
        void SetLogCallBack(AbstractLogger::LogCallBack cb_);

        /**
         * @fun GetLogPosition
         * @brief 获取日志输出位置
         * @return 日志输出位置
         * @date 2024.1.24
         * <AUTHOR>
         */
        const LogPosition GetLogPosition()const;

        /**
         * @fun GetLogMode
         * @brief 获取日志输出模式
         * @return 同步/异步
         * @date 2024.1.24
         * <AUTHOR>
         */
        const LogMode GetLogMode()const;

        /**
         * @fun GetLogOutputLevel
         * @brief 获取日志输出等级
         * @return 日志输出等级
         * @date 2024.1.24
         * <AUTHOR>
         */
        LogLevel GetLogOutputLevel() const;

        /**
         * @fun CreateLogger
         * @brief 创建日志
         * @param logger_name_ [IN] 日志名称
         * @return 是否成功状态码
         * @date 2024.1.24
         * <AUTHOR>
         */
        AOIErrorCode CreateLogger(const std::string& logger_name_);

        /**
         * @fun CreateDefaultLogger
         * @brief 创建日志
         * @detail 软件启动创建默认日志，用于写入不带日志名称的日志
         * @return  AOI_OK 创建成功，其他状态码为失败
         * @date 2024.1.24
         * <AUTHOR>
         */
        AOIErrorCode CreateDefaultLogger();

        /**
         * @fun LogWithName
         * @brief 调用指定日志写入日志
         * @param loggerName [IN] 调用的日志名称
         * @param level_ 日志输出级别
         * @param ...args 输出的日志内容
         * @date 2024.1.24
         * <AUTHOR>
         */
        template<typename... Args>
        inline void LogWithName(const std::string& loggerName, LogLevel level_, const Args& ...args)
        {
            std::stringstream ss;
            ((ss << args), ...);
            std::string msg = ss.str();
            return LogCustom(loggerName, level_, msg);
        }

        /**
         * @fun Log
         * @brief 调用默认日志器写入日志
         * @param level_ [IN] 日志等级
         * @param ...args 日志内容
         * @date 2024.1.24
         * <AUTHOR>
         */
        template<typename... Args>
        inline void Log(const LogLevel level_, const Args& ...args)
        {
            std::stringstream ss;
            ((ss << args), ...);
            std::string msg = ss.str();
            return LogDefault(level_, msg);
        }
    protected:

        /**
         * @fun LogDefault
         * @brief 调用默认日志器写日志
         * @param level_ [IN] 写的日志等级
         * @param msg_ [IN] 写入的日志内容
         * @date 2024.1.24
         * <AUTHOR>
         */
        void LogDefault(const LogLevel level_, const std::string& msg_);

        /**
         * @fun LogCustom
         * @brief 调用自定义的日志器写入日志
         * @param log_name_ [IN] 自定义的日志名称
         * @param level_ [IN] 日志等级
         * @param msg_ [IN] 日志内容
         * @date 2024.1.24
         * <AUTHOR>
         */
        void LogCustom(const std::string& log_name_, const LogLevel level_, const std::string& msg_);

        LogManagerImplData* p_data;
    };

    using LogManagerPtr = std::shared_ptr<LogManager>;
}
#endif // !__LOGMANAGER_H__