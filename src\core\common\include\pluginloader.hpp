
/*****************************************************************//**
 * @file   pluginloader.hpp
 * @brief  插件导入工具
 * @details  用于将外部插件导入到系统中使用
 * <AUTHOR>
 * @date 2024.2.19
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.2.19         <td>V1.0              <td>zhangyuyu      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __PLUGINLOADER_HPP__
#define __PLUGINLOADER_HPP__

//OS Specific
#ifdef _WIN32
#include <Windows.h>
#include <Psapi.h>
#else
#include <dlfcn.h>
#endif

//STD
#include <string>
#include <map>
#include <memory>
#include <cassert>

//Custom
#include "utf8convert.hpp"
#include "pluginfactory.hpp"

namespace jrscore 
{

    template<typename TPlugin>
    class PluginLoader
    {
        public:
            explicit PluginLoader (const std::string& path):
                                            plugin_path(path),
                                            plugin(nullptr),
                                            handle_lib(nullptr)   
            {
            }

            ~PluginLoader ()
            {
                Unload();
            }
            const std::string& GetPluginPath()const
            {
                return plugin_path;
            }
            bool Load()
            {
                if(IsLoad())
                {
                    Unload();
                }
                if(plugin_path.empty())
                {
                    return false;
                }
                return LoadPluginLibrary();

            }
            bool IsLoad()
            {
                return handle_lib!= nullptr;
            }
            bool Unload()
            {
                bool result = true;
                if(IsLoad())
                {
                    if(plugin)
                    {
                        Call<void>(PLUGIN_FACTORY_DESTROY);
                        plugin = nullptr;
                    }
                    result = UnLoadPluginLibrary();
                    if(result)
                    {
                        handle_lib = nullptr;
                    }
                }
                return result;
            }
            TPlugin* GetPluginInstance()
            {
                if(!IsLoad())
                {
                    return nullptr;
                }
                if(!plugin)
                {
                    plugin = Call<TPlugin*>(PLUGIN_FACTORY_CREATE);
                }
                return plugin;


            }
        private:
            #ifdef _WIN32
                typedef HMODULE handle_plugin;
                typedef FARPROC plugin_function_ptr;
                bool LoadPluginLibrary ()
                {
                    std::wstring wpath = Utf8Conv::Utf8ToUtf16(plugin_path);
                     handle_lib = LoadLibraryW(wpath.c_str());
                     return handle_lib!= 0 ;

                }
                bool UnLoadPluginLibrary ()
                {
                    return FreeLibrary(handle_lib) != 0;
                }

                plugin_function_ptr GetPluginFunction (const char* func_name)
                {
                    return GetProcAddress(handle_lib, func_name);
                }
            #else
                typedef void* handle_plugin;
                typedef void* plugin_function_ptr;
            #endif

            template<class Res>
            Res Call( const char* func_name)
            {
                Res(*func)();
                func = reinterpret_cast<Res(*)()>(GetPluginFunction(func_name));
                assert(func);
                return (*func)();
            }
        private:
            std::string plugin_path;
            TPlugin* plugin;
            handle_plugin handle_lib;
    };
    template<class TPlugin>
    using PluginLoaderPtr = std::shared_ptr<PluginLoader<TPlugin>>;

}

#endif// __PLUGINLOADER_HPP__