#ifndef DIRECTEDGRAPH_H
#define DIRECTEDGRAPH_H

#pragma once

#include <iostream>
#include <unordered_map>
#include <vector>
#include <string>
#include <unordered_set>
#include <algorithm>

namespace jtools
{
    struct Node
    {
        std::string name;
        std::string parent_name;
        Node(const std::string& name, const std::string& parent_name) : name(name), parent_name(parent_name) {};
    };

    class DirectedGraph 
    {
    public:
        // 添加节点
        void AddNode(const Node& node)
        {
            if (!node.parent_name.empty())
            {
                adj_list[node.parent_name].push_back(node.name);
            }
            if (adj_list.find(node.name) == adj_list.end())
            {
                adj_list[node.name] = {}; // 确保所有顶点都在图中
            }
            if (node.parent_name.empty())
            {
                root_nodes.push_back(node.name); // 保存起点节点
            }
        }

        // 遍历图，并输出路径，同时检测环
        void TraverseAndDetectCycle(std::vector<std::vector<std::string>>& all_paths, std::vector<std::vector<std::string>>& cycles)
        {
            std::unordered_set<std::string> visited;
            std::vector<std::string> rec_stack; // 当前递归路径

            // 遍历每个起点
            for (const auto& root : root_nodes)
            {
                if (visited.find(root) == visited.end()) 
                {
                    std::vector<std::string> path; // 当前起点的路径
                    dfs(root, visited, rec_stack, path, cycles);
                    all_paths.push_back(path);
                }
            }

            // 输出所有路径
            std::cout << "Traversal Paths:" << std::endl;
            for (const auto& path : all_paths)
            {
                for (size_t i = 0; i < path.size(); ++i)
                {
                    std::cout << path[i];
                    if (i != path.size() - 1)
                    {
                        std::cout << " -> ";
                    }
                }
                std::cout << std::endl;
            }

            // 输出所有环
            if (!cycles.empty())
            {
                std::cout << "Graph contains the following cycles:" << std::endl;
                for (const auto& cycle : cycles)
                {
                    for (size_t i = 0; i < cycle.size(); ++i)
                    {
                        std::cout << cycle[i];
                        if (i != cycle.size() - 1)
                        {
                            std::cout << " -> ";
                        }
                    }
                    std::cout << std::endl;
                }
            }
            else
            {
                std::cout << "Graph does not contain a cycle." << std::endl;
            }
        }

    private:
        std::unordered_map<std::string, std::vector<std::string>> adj_list; // 邻接表
        std::vector<std::string> root_nodes; // 存储起点节点

        // 深度优先遍历，同时检测环
        bool dfs(const std::string& node, std::unordered_set<std::string>& visited,
            std::vector<std::string>& rec_stack, std::vector<std::string>& path,
            std::vector<std::vector<std::string>>& cycles) 
        {
            visited.insert(node);       // 标记当前节点为已访问
            rec_stack.push_back(node);   // 将节点加入当前递归栈
            path.push_back(node);       // 将节点加入路径

            for (const std::string& neighbor : adj_list[node]) 
            {
                if (std::find(rec_stack.begin(), rec_stack.end(), neighbor) != rec_stack.end()) 
                {
                    // 如果邻接节点在递归栈中，说明存在环
                    cycles.push_back(getCyclePath(neighbor, rec_stack));
                }
                else if (visited.find(neighbor) == visited.end()) 
                {
                    // 如果邻接节点未访问，继续递归
                    dfs(neighbor, visited, rec_stack, path, cycles);
                }
            }
            rec_stack.pop_back(); // 当前节点递归结束，从递归栈中移除
            return false;
        }

        // 提取环的路径
        std::vector<std::string> getCyclePath(const std::string& start, const std::vector<std::string>& rec_stack) 
        {
            auto it = std::find(rec_stack.begin(), rec_stack.end(), start);
            std::vector<std::string> cycle(it, rec_stack.end());
            cycle.push_back(start); // 闭环
            return cycle;
        }
    };
}

#endif