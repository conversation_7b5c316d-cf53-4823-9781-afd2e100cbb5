/*****************************************************************//**
 * @file   imagepreprocessaglo.h
 * @brief  图像预处理算法
 * @details    
 * <AUTHOR>
 * @date  2024.08.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.08.18         <td>V1.0              <td>Xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __JRSIMAGEPREPROCESSALGO_H__
#define __JRSIMAGEPREPROCESSALGO_H__
#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
// custom
#include "colorparams.h"
// opencv
#include <opencv2/opencv.hpp>
#pragma warning(pop)

using cv::Mat;
using std::vector;
using Mats3 = vector<Mat>;

class ImageProcessor
{
public:
	/**
    * @fun  SetProcessImage
    * @brief  设置预处理图像
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetProcessImage(const cv::Mat& image_group_);
	/**
    * @fun  SetProcessParams
    * @brief  设置预处理图像参数
    * @date   2024.08.18
    * <AUTHOR>
    */
	int SetProcessParams(const PreProcessParams& params);
	/**
    * @fun  GetProcessImage
    * @brief  获取图像预处理结果
    * @date   2024.08.18
    * <AUTHOR>
    */
	int GetProcessImage(cv::Mat& precess_image);

    bool GetEnhanceImageStatus() const;
public:
	/**
    * @fun  GetRgbChannel
    * @brief  通道分离
    * @date   2024.08.18
    * <AUTHOR>
    */
	Mats3 GetRgbChannel(const Mat& rgb_image);
	/**
    * @fun  GetHsvChannel
    * @brief  HSV通道分离
    * @date   2024.08.18
    * <AUTHOR>
    */
	Mats3 GetHsvChannel(const Mat& rgb_image);
	ImageProcessor();
	~ImageProcessor();
private:
	/**
    * @fun  AdjustContrast
    * @brief  对比度
    * @date   2024.08.18
    * <AUTHOR>
    */
	void AdjustContrast(double value, Mat& image);
	/**
    * @fun  AdjustBrightness
    * @brief  亮度
    * @date   2024.08.18
    * <AUTHOR>
    */
	void AdjustBrightness(double value, Mat& image);
	/**
    * @fun  AdjustHue
    * @brief  颜色
    * @date   2024.08.18
    * <AUTHOR>
    */
	void AdjustHue(double value, Mat& image);
	/**
    * @fun  AdjustSaturation
    * @brief  饱和度
    * @date   2024.08.18
    * <AUTHOR>
    */
	void AdjustSaturation(double value, Mat& image);
	/**
    * @fun  AdjustGamma
    * @brief  伽马
    * @date   2024.08.18
    * <AUTHOR>
    */
	void AdjustGamma(double value, Mat& image);
	/**
    * @fun  MapDoubleToByteRange
    * @brief  将数字从(-1,1)映射到(-255,255)
    * @date   2024.08.18
    * <AUTHOR>
    */
	int MapDoubleToByteRange(double value);

private:
	double contrast_value_ = 1.0;
	double brightness_value_ = 1.0;
	double hue_value_ = 0;
	double saturation_value_ = 1.0;
	double gamma_value_ = 1.0;
	bool is_reverse_ = false;
	int id_;
    bool enhance_image = false;
	//std::map<int, Mat> image_map_;
	Mat image_;
};
#endif