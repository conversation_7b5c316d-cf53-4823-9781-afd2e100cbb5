/*****************************************************************//**
 * @file   ShowListTools.h
 * @brief  工具
 *
 * <AUTHOR>
 * @date   2024.8.22
 *********************************************************************/
#ifndef __SHOWLISTTOOLS_H__
#define __SHOWLISTTOOLS_H__

#include <string>

#include <QStringList>

namespace jrsaoi
{
    class ShowListTools
    {
        public:
            /**
             * @fun ConvertStringToList 
             * @brief 转换std::vector<std::string> 为 QStringList
             * @param m_pad_group_items
             * @return 
             * @date 2024.9.24
             * <AUTHOR>
             */
            static QStringList ConvertStringToList(std::vector<std::string> m_pad_group_items);
    };
}
#endif // !__SHOWLISTTOOLS_H__
