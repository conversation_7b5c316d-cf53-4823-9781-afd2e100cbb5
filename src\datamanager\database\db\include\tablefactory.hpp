/*****************************************************************//**
 * @file   tablefactory.h
 * @brief  数据表
 * @details 用于MVC的生产
 * <AUTHOR>
 * @date 2024.8.8
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                  <th> Desctiption
 * <tr><td>2024.1.15         <td>V1.0              <td>zhangyuyu      <td>                       <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef __TABLEFACTORY_H__
#define __TABLEFACTORY_H__

 //STD
#include <map>
#include <string>
#include <iostream>
//

namespace jrsdatabase
{
    enum class TableTYPE :int
    {
        KCONTROLPANEL,
    };
    class TableFactory
    {
    public:
        TableFactory() = delete;
        template<class TempTable>
        static std::shared_ptr<TempTable> CreateTable(const std::string& name_)
        {
            return std::make_shared<TempTable>(name_);
        }
    };
}
#endif // !__TABLEFACTORY_H__