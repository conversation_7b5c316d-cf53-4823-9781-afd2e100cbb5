/**********************************************************************
 * @brief  控制点基类.
 *
 * @file   controlpointabstract.h
 *
 * @date   2024.07.08
 * <AUTHOR>
**********************************************************************/

#ifndef CONTROLPOINTABSTRACT_H
#define CONTROLPOINTABSTRACT_H

#include "controlpointconstants.hpp" // ControlAttributes ControlPointDraw
#include "graphicsabstract.hpp" // GraphicsPtr

class ResponseEventParam;

constexpr float CONTROL_POINT_RESPONSE_MAX_LIMIT = 10.0f;  ///< 控制点响应最大距离

class ControlPointAbstract
{
public:
    ControlPointAbstract() {}
    virtual ~ControlPointAbstract() {}
    /**
     * @brief  设置控制点属性
     */
    void SetAttributes(const ControlAttributes& attributes_);
    /**
     * @brief  设置控制点绘制属性
     */
    void SetDraw(const ControlPointDraw& cpd_);
    /**
     * @brief  设置控制点位置
     */
    void SetPos(const float& x, const float& y);
    /**
     * @brief  绘制
     */
    virtual void Draw(Renderer* r, Painter* p, const LayerConfig* config) const;
    /**
     * @brief  尝试响应
     * @param  min_dis 最小响应距离
     * @return 响应距离,越近越先响应
     * @note   负值表示未响应
     */
    virtual double TryResponse(const float& x, const float& y, const float& min_dis) const;
    /**
     * @brief  响应
     */
    virtual void Response(const ResponseEventParam& param, GraphicsAbstract* const graphics_) = 0;

    ControlAttributes attributes;
    ControlPointDraw cpd;
};
#endif //! CONTROLPOINTABSTRACT_H