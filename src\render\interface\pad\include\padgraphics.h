﻿#pragma once
/*****************************************************************
 * @file   padgraphics.h
 * @brief  存放pad的坐标，样式等信息
 * @details
 * <AUTHOR>
 * @date 2025.2.24
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                       <th> Desctiption
 * <tr><td>2025.2.24         <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
 //STD
 //Custom
#include "graphicsobject.h"
#include "padgroup.h" // PadGraphicsGroup
#include "tools.hpp"

class GRAPHICS_API PadGraphics : public GraphicsAbstract, public TransientObjectControl<PadGraphics>
{
public:
    PadGraphics(float x, float y, float width, float height, float angle, int pad_id_, std::weak_ptr<PadGraphicsGroup> parent_);
    PadGraphics(int pad_id_, std::weak_ptr<PadGraphicsGroup> parent_);

    PadGraphics& operator=(const PadGraphics& other);

    GraphicsFlag GetFlag() const override;

    void Draw(Renderer* r, const LayerConfig* config) override;

    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    void Update() override;
    void UpdateDrawBuffer() override;
    void UpdateControlPoint()  override;
    void DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config) override;
    int TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>&, const TryResponseEventParam&) override;
    int ResponseControlPoint(const ResponseEventParam&) override;
    int ResponseEvent(const MouseEventValue&) override;

    std::shared_ptr<GraphicsAbstract> Clone() const override;
    std::string GetSerializedData() override;

    /**  */
    //int SetDirection(const PadDirection& pad_type_);

    int GetDirection();
    int GetGroupType();


private:
    void DrawInfo(Renderer* r, PadGraphics* obj);
    AUTO_PROPERTY(int, pad_id, PadID);
    AUTO_PROPERTY(std::weak_ptr<PadGraphicsGroup>, pad_group, PadGroupPtr);

private:
    std::vector<Vec2> paths;  ///< 顶点缓存
    std::vector<Vec2> path_flags;  ///< 顶点缓存(标记点)
};


