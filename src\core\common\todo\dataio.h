﻿#pragma once

#include "jtoolsapi.hpp"

#include <string>

namespace jtools
{
    class DataIO
    {
    public:
        // 将字符串储存到文件
        static void SaveStringToFile(const std::string &str, const std::string &filePath);
        // 从文件读取字符串
        static std::string LoadStringFromFile(const std::string &filePath);
        // 储存3D矩阵数据到文件
        static void SaveMatrixToFile(const float *data, int width, int height, const std::string &filePath);
        // 从文件读取3D矩阵数据
        static void LoadMatrixFromFile(float *data, int width, int height, const std::string &filePath);
    };
}
