/*****************************************************************//**
 * @file   scanprocess.h
 * @brief  扫图逻辑，用于调用运控输入坐标进行图片采集
 * @details
 * <AUTHOR>
 * @date 2024.8.5
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.5          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/

//TODO: 采图逻辑和点拍逻辑需要优化重构 by zhangyuyu 2024.11.21
//STD
#include <iostream>
#include <thread>
#include <mutex>
#include <condition_variable>
//Custom
#include "projectparam.hpp"
#include "image.hpp"
//Third
namespace jrsdevice 
{
    class DeviceManager;
}

namespace jrslogic
{
    class JrsAoiImgsManager;  
    class CaptureImage;
    class ScanProcess
    {
        public:
            ScanProcess ( const std::shared_ptr<jrsdevice::DeviceManager>& device_manager_ptr_ );

            ~ScanProcess ();

            void StartCapture (bool is_auto_run = false );

            void PauseCapture ();

            void ResumeCapture ();

            void StopCapture ();

            void SetJrsAoiImgsManager ( const std::shared_ptr<jrslogic::JrsAoiImgsManager>& _aoi_imgs_manager_ptr );

            /**
             * @fun SetCapturePosList 
             * @brief 设置拍照的位置
             * @param pos_lists_
             * <AUTHOR>
             * @date 2024.8.11
             */
            void SetCapturePosList ( const jrsdata::CaptureFovPosList&  pos_lists_);

            void SetRenderCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback);
            void SetWorkFlowCallback(jrsdata::JrsImageBufferCallBack img_buffer_callback_);
             

        private:

            /**
             * @fun Init 
             * @brief 初始化
             * <AUTHOR>
             * @date 2024.8.12
             */
            void Init ();

            /**
             * @fun Run 
             * @brief 运行
             * <AUTHOR>
             * @date 2024.8.12
             */
            void Run ();

            /**
             * @fun TriggerDone 
             * @brief 触发完成
             * @param result_
             * <AUTHOR>
             * @date 2024.8.12
             */
            void TriggerDone ( const int& result_ );

            /**
             * @fun MergeDone 
             * @brief 合成图像完成
             * @param result_
             * <AUTHOR>
             * @date 2024.8.12
             */
            void MergeDone (const jrsdata::OneFovImgs& imgs, const int& result_);


            int fov_num = 0;    /**< 扫描的fov总个数 */
            int fov_count = 0;  /**< 已扫描的fov个数 */

            jrsdata::CaptureFovPosList capture_fov_pos_list; /**< 采集fov的位置数据 */
            std::shared_ptr<jrsdevice::DeviceManager>    device_manager_ptr; /**< 设备管理实例*/
            std::shared_ptr<jrslogic::JrsAoiImgsManager> aoi_imgs_manager_ptr; /**< 图像管理实例*/
            std::thread scan_thread; /**< 扫图线程 */
            std::mutex mtx_scan; /**< 扫图线程互斥锁 */
            std::condition_variable cv_scan;/**< 扫图线程条件变量 */
            std::atomic<bool> is_running; /**< 扫图线程运行状态 */
            std::atomic<bool> is_paused;  /**< 扫图线程暂停状态 */
            std::atomic<bool> is_stopped; /**< 扫图线程停止状态 */
            std::atomic<bool> waittting; /**< 等待当前fov拍照完成 */    
            std::atomic<bool> is_auto_running; /**< 是否是自动扫图流程 */    

            jrsdata::JrsImageBufferCallBack              render_imgs_callback;                    /**< 图像渲染回调 */
            jrsdata::JrsImageBufferCallBack              work_flow_imgs_callback;                 /**<工程运行图像回调 */
    };
}
