﻿/*****************************************************************//**
 * @file   showlistmodel.h
 * @brief  产品列表Model
 * 20241203 新增MARK和二维码显示
 * <AUTHOR>
 * @date   2024.9.25
 *********************************************************************/
#ifndef __SHOWLISTMODEL_H__
#define __SHOWLISTMODEL_H__
#include "datadefine.hpp"
#include "modelbase.h"
#include "projectparam.hpp"
#include <vector>
#include <QStringList>
#include <string>
using namespace jrsdata;

namespace jrsaoi
{
    class  ShowListModel :public ModelBase
    {
    public:
        ShowListModel(const std::string& name);
        ~ShowListModel();
        /**
         * @fun Update
         * @brief 刷新模板,被showlistcontroller的UpdateView调用
         * @param param_ 消息参数
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual int Update(const jrsdata::ViewParamBasePtr& param_) override;
        /**
         * @fun Save
         * @brief 保存参数,重写ModelBase的Save
         * @param param_ 消息参数
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        virtual int Save(const jrsdata::ViewParamBasePtr& param_)override;
        /**
         * @fun GetModelData
         * @brief 获取接收到的CAD数据结构
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        const jrsdata::ViewParamBasePtr& GetModelData();
        /**
         * @fun Query
         * @brief 筛选元件名/料号的实现
         * @param param 查询参数
         * @date 2024.9.19
         * <AUTHOR>
         */
        void Query(const jrsdata::QueryListViewParam& param);
        void UpdateDeviceShowState(DeviceDataStructPtr& device_data, SHOW_TYPE show_type);
        void ShowListModel::QueryCommon(QString& query, SHOW_TYPE show_type,
            std::function<bool(PartNumberDataStructPtr&, DeviceDataStructPtr& device_data, QString&)> query_part_number_or_device);
        /**
         * @fun QueryPartNumber
         * @brief 筛选料号的实现
         * @param sub_board_data_struct 子板结构
         * @param part_number 输入料号
         * @date 2024.9.19
         * <AUTHOR>
         */
        void QueryPartNumber(QString part_number, SHOW_TYPE show_type);
        /**
         * @fun QueryPartNumberFollow
         * @brief 筛选跟随料号的实现
         * @param sub_board_data_struct 子板结构
         * @param part_number 输入料号
         * @date 2024.9.19
         * <AUTHOR>
         */
        void QueryPartNumberFollow(QString part_number, SHOW_TYPE show_type);
        /**
         * @fun QueryDeviceName
         * @brief 从所有子板中查找元件名称
         * @param sub_board_data_struct
         * @param device_name
         * @date 2025.4.9
         * <AUTHOR>
         */
        void QueryDeviceName(QString device_name, SHOW_TYPE show_type);
        /**
         * @fun FindGraphicsIDFromSubboard
         * @brief 从子板中查找元件名对应的元件
         * @param graphics_id 从Render发过来的元件名
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool FindGraphicsIDFromSubboard(std::string graphics_id);
        /**
         * @fun FindGraphicsIDFromPartNumber
         * @brief 从料号中查找元件名对应的元件
         * @param part_numbers 输入料号
         * @param graphics_id 从Render发过来的元件名
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool FindGraphicsIDFromPartNumber(std::vector<PartNumberDataStructPtr> part_numbers, std::string graphics_id);
        bool FindGraphicsIDFromSubboardStruct(SubBoardDataStructPtr subboad_data, std::string graphics_id, int board_id);
        /**
         * @fun FindGraphicsIDFromDevice
         * @brief 从元件中查找元件名对应的元件
         * @param devices  元件
         * @param graphics_id 从Render发过来的元件名
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool FindGraphicsIDFromDevice(std::vector<DeviceDataStructPtr> devices, std::string graphics_id);
        /**
         * @fun ProjectUpdate
         * @brief 解析CAD数据刷新里元件列表
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool ProjectUpdate();
        /**
         * @fun UpdateProjectData
         * @brief 处理项目更新的核心逻辑
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateProjectData();
        /**
         * @fun ResetShowListParams
         * @brief 重置显示列表参数
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool ResetShowListParams();
        /**
         * @fun GraphicsSelect
         * @brief 实现图片选中元件后,刷新元件列表
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool GraphicsSelect();
        /**
         * @fun ProcessGraphicsMap
         * @brief 处理图形映射，查找有效的图形 ID 并更新显示列表参数。
         * @param graphics_params 图形更新事件参数。
         * @return 如果处理成功返回 true，否则返回 false。
         * @date 2025.05.05
         * <AUTHOR>
         */
        bool ProcessGraphicsMap(const std::shared_ptr<jrsdata::GraphicsUpdateProjectEventParam>& graphics_params);
        /**
         * @fun ProcessGraphicsAndSelectUnits
         * @brief 处理图形和选择单元，查找有效的图形 ID 并更新显示列表参数。
         * @param graphics_params 图形更新事件参数。
         * @return 如果处理成功返回 true，否则返回 false。
         * @date 2025.05.05
         * <AUTHOR>
         */
        bool ProcessGraphicsAndSelectUnits(const std::shared_ptr<jrsdata::GraphicsUpdateProjectEventParam>& graphics_params);

        /**
         * @fun GraphicsUpdate
         * @brief 实现图片选中元件的数据更新
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool GraphicsUpdate();
        /**
         * @fun UpdateDevicePosition
         * @brief 更新元件位置
         * @param graphics_params
         * @param graphics_id
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDevicePosition(const jrsdata::GraphicsUpdateProjectEventParamPtr& graphics_params, const std::string& graphics_id);
        /**
         * @fun UpdateDeviceData
         * @brief 更新元件数据
         * @param list_param
         * @param component
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceData(const ShowListViewParam& list_param, const jrsdata::Component& component);
        /**
         * @fun UpdateDeviceSize
         * @brief 更新元件尺寸
         * @param device_data
         * @param component_part_number
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceSize(DeviceDataStructPtr device_data, const std::string& component_part_number);
        /**
         * @fun GraphicsCreate
         * @brief 根据项目参数创建图形界面。
         * @details 此函数从 `show_list_param` 中提取图形更新事件参数，解析项目参数，并根据图形 ID 列表更新图形参数。
         *          如果图形 ID 列表不为空，将调用 `UpdateGraphicsParams` 函数更新图形参数。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool GraphicsCreate();

        /**
         * @fun GraphicsDelete
         * @brief 实现图片中删除的元件的数据更新
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool GraphicsDelete();
        /**
         * @fun DeleteDevice
         * @brief 删除元件
         * @param device_names
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool DeleteDevice(const std::vector<std::string>& device_names);
        /**
         * @fun DeleteDeviceFromPartNumber
         * @brief 从指定的料号中删除匹配的元件。
         * @details 遍历料号中的所有元件数据，根据提供的元件名称、原始元件名称和键值检查是否匹配。
         *          如果匹配，则从元件列表中删除该元件，并更新显示参数和元件 ID。
         * @param delete_device_name 要删除的元件名称。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool DeleteDeviceFromPartNumber(const std::string& delete_device_name, PartNumberDataStructPtr part_number);

        /**
         * @fun UpdateDeviceIds
         * @brief 更新料号中所有元件的 ID。
         * @details 遍历料号中的所有元件，并重新分配元件 ID。
         * @param part_number 料号数据结构。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool UpdateDeviceIds(PartNumberDataStructPtr part_number);
        /**
         * @fun DeleteSubboard
         * @brief 删除指定名称的子板。
         * @details 根据子板名称删除子板
         * @param subboard_names 子板名称列表。
         * @return void
         * @date 2025.02.25
         * <AUTHOR>
         */
        bool DeleteSubboard(const std::vector<std::string>& subboard_names);

        /**
         * @fun UpdateBoardResult
         * @brief 更新board_names子板名对应的测试结果
         * @param board_names 子板名称
         * @param status 结果
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool UpdateBoardResult(std::vector<std::string> board_names, std::vector<RESULT_STATE> status);
        /**
         * @fun UpdatePartNumberResult
         * @brief 更新part_number_names料号名对应的测试结果
         * @param part_number_names 料号名称
         * @param status 结果
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool UpdatePartNumberResult(std::vector<std::string> part_number_names, std::vector<RESULT_STATE> status);
        /**
         * @fun UpdateDeviceResult
         * @brief 更新device_names元件名对应的测试结果
         * @param device_names 元件名称
         * @param status 结果
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool UpdateDeviceResult(const std::vector<std::string>& device_names, const std::vector<RESULT_STATE>& status);
        /**
         * @fun UpdateDeviceStatus
         * @brief 更新单个元件的状态
         * @param device_name
         * @param status
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceStatus(const std::string& device_name, RESULT_STATE status);
        /**
         * @fun UpdateDeviceStatus
         * @brief 在子板中更新元件状态
         * @param subboard
         * @param device_name
         * @param status
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceStatus(const SubBoardDataStructPtr& subboard, const std::string& device_name, RESULT_STATE status);
        /**
         * @fun UpdateDeviceStatus
         * @brief 在部件编号中更新元件状态
         * @param part_number
         * @param device_name
         * @param status
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceStatus(const PartNumberDataStructPtr& part_number, const std::string& device_name, RESULT_STATE status);

        /**
         * @fun GetShowListViewParam
         * @brief 获取子板号、料号、元件号
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        ShowListViewParam GetShowListViewParam();
        /**
         * @fun GetGraphicsParam
         * @brief 获取所有元件数据
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        jrsdata::GraphicsUpdateProjectEventParamPtr GetGraphicsParam();
        /**
         * @fun CleanDatas
         * @brief 清除数据
         * @date 2024.9.23
         * <AUTHOR>
         */
        bool CleanDatas();
        /**
         * @fun UpdateShowListView
         * @brief 同步更新model和view的显示参数
         * @param show_list_view_param
         * @date 2025.2.7
         * <AUTHOR>
         */
        void UpdateShowListView(ShowListViewParam show_list_view_param);
        /**
         * @fun GetEditViewData
         * @brief 获取编辑界面数据
         * @return
         * <AUTHOR>
         * @date 2025.3.16
         */
        ShowTableParamBasePtr GetEditViewData();
        /**
         * @fun UpdateEditViewDataToProject
         * @brief  将编辑数据更新到工程
         * @param param_
         * <AUTHOR>
         * @date 2025.3.17
         */
        bool UpdateEditViewDataToProject(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SetSelectComponentUpdateData
         * @brief 切换一个元件导致又往回发，形成闭环，把元件名记录下来，外部发的消息中过滤掉这个元件名一次，打破闭环
         * @param device_name 元件名
         * @date 2025.3.24
         * <AUTHOR>
         */
        void SetSelectComponentUpdateData(std::string device_name);
    private:
        /**
         * @fun ResolveBoardData
         * @brief 解析元件数据
         * @param project_event_param_ 工程数据结构
         * @date 2024.9.19
         * <AUTHOR>
         */
        bool ResolveBoardData(const jrsdata::ProjectParamPtr project_event_param);
        /**
         * @fun ClearSubboardData
         * @brief 清空子板数据
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool ClearSubboardData();
        /**
         * @fun CreateSubboardData
         * @brief 创建子板数据
         * @param sub_boards
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool CreateSubboardData(const std::vector<SubBoard>& sub_boards);
        /**
         * @fun InsertCombinedSubboard
         * @brief 插入合并后的子板数据
         * @param board_info
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool InsertCombinedSubboard(const jrsdata::Board& board_info);

        /**
         * @fun MakeSubboardDataStruct
         * @brief 生成子板数据
         * @param board_id 子板ID
         * @param boards 输入 子板
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        SubBoardDataStructPtr MakeSubboardDataStruct(int board_id, jrsdata::SubBoard sub_board);
        /**
         * @fun MakePartNumberDataStruct
         * @brief 生成料号数据
         * @param board_id 输入板子ID
         * @param part_no_id 输入料号ID
         * @param part_number 输入料号
         * @param vec_component 输入元件集合
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        PartNumberDataStructPtr MakePartNumberDataStruct(int board_id, std::string board_name, int part_no_id, std::string part_number, std::vector<jrsdata::Component> vec_component);
        /**
         * @fun MakeDeviceDataStruct
         * @brief 生成元件数据
         * @param board_id 输入板子ID
         * @param part_no_id 输入料号ID
         * @param device_id 输入元件号
         * @param component  输入元件
         * @return
         * @date 2024.9.19
         * <AUTHOR>
         */
        DeviceDataStructPtr MakeDeviceDataStruct(int board_id, int device_id, jrsdata::Component component);
        /**
         * @fun MakePartNumberFromComponent
         * @brief   把输入的元件集合转换key料号的map
         * @param vec_component 输入元件集合
         * @return key料号 value元件的 map
         * @date 2024.12.2
         * <AUTHOR>
         */
        std::map<std::string, std::vector<Component>> MakePartNumberFromComponent(std::vector<jrsdata::Component> vec_component);
        /**
         * @fun MakePartNumberDataStructVector
         * @brief 把输入元件集合转换成料号集合，子板区隔，不合并子板的同料号元件
         * @param part_number_datas 输出料号集合
         * @param board_id 输入板子ID
         * @param vec_component 输入元件集合
         * @return 默认true
         * @date 2024.12.2
         * <AUTHOR>
         */
        bool MakePartNumberDataStructVector(std::vector<PartNumberDataStructPtr>& part_number_datas, int board_id, std::string board_name,
            std::vector<jrsdata::Component> vec_component);
        /**
         * @fun UpdateComponentDetectResultStatus
         * @brief 更新检测结果
         * @param param_
         * @date 2025.2.7
         * <AUTHOR>
         */
        bool UpdateComponentDetectResultStatus(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun HandleInvalidComponentStatus
         * @brief 处理无效参数的情况
         * @date 2025.2.20
         * <AUTHOR>
         */
        void HandleInvalidComponentStatus();
        /**
         * @fun UpdateDeviceResults
         * @brief 更新元件检测结果
         * @param component_result_map
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceResults(const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& component_result_map);
        /**
         * @fun UpdateDeviceResult
         * @brief 更新单个元件的结果
         * @param device
         * @param component_result_map
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateDeviceResult(const DeviceDataStructPtr& device, const std::unordered_map<std::string, jrsdata::ComponentDetectResult>& component_result_map);
        /**
         * @fun UpdateSubboardStates
         * @brief 更新子板状态
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool UpdateSubboardStates();
        /**
         * @fun MarkShowListParamsAsUpdated
         * @brief 标记显示列表参数为已更新
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool MarkShowListParamsAsUpdated();

        /**
         * @fun ConvertAllSubboardToOne
         * @brief 把所有子板的元件放到一起，合并成全部子板，放到产品列表的第一行
         * @param subboard_datas
         * @return
         * @date 2025.2.4
         * <AUTHOR>
         */
        SubBoardDataStructPtr ConvertAllSubboardToOne(const std::vector<SubBoardDataStructPtr>& subboard_datas, const jrsdata::Board& board);
        /**
         * @fun CreateCombinedSubboard
         * @brief 初始化合并后的子板数据结构
         * @return
         * @date 2025.2.20
         * <AUTHOR>
         */
        SubBoardDataStructPtr CreateCombinedSubboard();
        /**
         * @fun CollectBadMarksAndBarcodes
         * @brief 收集所有子板的坏标记和条码
         * @param boards
         * @return
         * @date 2025.2.20
         * <AUTHOR>
         */
        std::vector<jrsdata::Component> CollectSubboardTopValues(const std::vector<SubBoard>& sub_boards);
        /**
         * @fun AddComponentsToCombinedSubboard
         * @brief 将组件添加到合并后的子板中
         * @param combined_subboard
         * @param components
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool AddComponentsToCombinedSubboard(SubBoardDataStructPtr combined_subboard, const std::vector<jrsdata::Component>& components);
        /**
         * @fun MergeBoardTopFromMultipleSources
         * @brief 逐个处理每个组件列表
         * @param combined_subboard
         * @param component_sources
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool MergeBoardTopFromMultipleSources(SubBoardDataStructPtr combined_subboard, const std::vector<std::vector<jrsdata::Component>>& component_sources);
        /**
         * @fun MergeSubboardPartNumbers
         * @brief 合并子板的部件编号
         * @param combined_subboard
         * @param subboard_datas
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool MergeSubboardPartNumbers(SubBoardDataStructPtr combined_subboard, const std::vector<SubBoardDataStructPtr>& subboard_datas);
        /**
         * @fun MergePartNumber
         * @brief 合并单个部件编号
         * @param combined_subboard
         * @param part
         * @date 2025.2.20
         * <AUTHOR>
         */
        bool MergePartNumber(SubBoardDataStructPtr combined_subboard, const PartNumberDataStructPtr& part);

        /**
         * @fun UpdateSubboardState
         * @brief 根据元件检测结果计算板子检测结果
         * @param subboard
         * @return
         * @date 2025.2.7
         * <AUTHOR>
         */
        RESULT_STATE UpdateSubboardState(SubBoardDataStructPtr subboard);
        /**
         * @fun UpdatePartNumberState
         * @brief 根据元件检测结果计算料号检测结果
         * @param part_number
         * @return
         * @date 2025.2.7
         * <AUTHOR>
         */
        RESULT_STATE UpdatePartNumberState(PartNumberDataStructPtr part_number);
        /**
         * @fun UpdateEditViewData
         * @brief  更新编辑界面数据
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.3.16
         */
        int UpdateEditViewData(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun UpdateComponentData
         * @brief 更新元件编辑数据
         * @param data_
         * <AUTHOR>
         * @date 2025.3.17
         */
        bool UpdateComponentData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process_, const jrsdata::SelectUnitsName& data_);
        /**
         * @fun UpdatePadEditData
         * @brief 更新pad 编辑数据
         * @param project_process
         * @param data_
         * <AUTHOR>
         * @date 2025.3.17
         */
        bool UpdatePadEditData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process, const jrsdata::SelectUnitsName& data_);
        /**
         * @fun UpdateRegionEditData
         * @brief 更新检测区域编辑数据
         * @param project_process
         * @param data_
         * <AUTHOR>
         * @date 2025.3.17
         */
        bool UpdateRegionEditData(const std::shared_ptr<jrsparam::ProjectDataProcess>& project_process, const jrsdata::SelectUnitsName& data_);

        /**
        * @fun UpdateSubboardDeviceData
        * @brief 更新指定子板和设备的数据。
        * @param subboard_name 子板名称。
        * @param device_name 设备名称。
        * @param type 组件类型。
        * @return 无
        * @date 2025.05.05
        * <AUTHOR>
        */
        void UpdateSubboardDeviceData(std::string subboard_name, std::string device_name, jrsdata::Component::Type type);
        /**
         * @fun GetComponentRef
         * @brief 从项目数据中获取组件引用。
         * @param device_name 设备名称。
         * @param subboard_name 子板名称。
         * @param type 组件类型。
         * @return 组件引用（可选）。
         * @date 2025.05.05
         * <AUTHOR>
         */
        std::optional<jrsdata::Component> GetComponentRef(std::string device_name, std::string subboard_name, jrsdata::Component::Type type);
        /**
         * @fun GetComponentUnitRef
         * @brief 从项目数据中获取组件单元引用。
         * @param part_number 组件的部件编号。
         * @param unit_name 单元名称。
         * @return 组件单元引用（可选）。
         * @date 2025.05.05
         * <AUTHOR>
         */
        std::optional<jrsdata::ComponentUnit> GetComponentUnitRef(std::string part_number, std::string unit_name);
        /**
         * @fun UpdateDeviceData
         * @brief 在图形参数中更新设备数据。
         * @param subboard_name 子板名称。
         * @param device_name 设备名称。
         * @param component 组件引用。
         * @param unit 组件单元引用。
         * @return 无
         * @date 2025.05.05
         * <AUTHOR>
         */
        void UpdateDeviceData(std::string subboard_name, std::string device_name, const jrsdata::Component& component, const jrsdata::ComponentUnit& unit);
        /**
         * @fun UpdateDeviceProperties
         * @brief 根据组件和单元引用更新设备的属性。
         * @param device 需要更新的设备数据。
         * @param component 组件引用。
         * @param unit 组件单元引用。
         * @return 无
         * @date 2025.05.05
         * <AUTHOR>
         */
        void UpdateDeviceProperties(DeviceDataStructPtr& device, const jrsdata::Component& component, const jrsdata::ComponentUnit& unit);

    private:
        /// 是否发送信号到渲染模块
        bool m_send_signal_to_render;
        /// 当前显示列表的参数
        jrsdata::ViewParamBasePtr show_list_param;
        /// 图形更新项目的参数
        jrsdata::GraphicsUpdateProjectEventParamPtr m_graphics_params;
        /// 项目参数操作器的引用
        ParamOperator& _project_operater;

        jrsdata::EditViewDataPtr _edit_view_data_ptr;       /**< 编辑界面参数 */

        jrsdata::SelectUnitsName _select_units;             /**< 当前选择的组件 */
        std::string m_select_component_name;    /// 记录发送出去的元件名
        bool b_select_component_update = false; /// 记录发送出去的状态
    };
    using ShowListModelPtr = std::shared_ptr<ShowListModel>;
}
#endif // !__SHOWLISTMODEL_H__
