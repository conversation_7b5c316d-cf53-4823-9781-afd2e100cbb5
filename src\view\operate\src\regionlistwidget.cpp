#include <QComboBox>
#include <QSignalMapper>

#include "directedgraph.h"
#include "regionlistwidget.h"
#include "regionwidget.h"
#include "algoselectwidget.h"


DetectWindowListWidget::DetectWindowListWidget(const QString& _model_name, const QString& list_widget_property, QWidget* parent)
    : QListWidget(parent)
{
    item_delegate = new ImageTableDelegate(this);
    this->setItemDelegate(item_delegate);

    this->setSelectionMode(QAbstractItemView::ExtendedSelection);
    this->setProperty("TYPE", list_widget_property);
    show_index_map.insert({ "",0 });
    connect(this, &QListWidget::itemSelectionChanged, this, &DetectWindowListWidget::SlotSelectedChanged);
    model_name = _model_name;
}

void DetectWindowListWidget::CreateDetectWindowItem(const DetectWindowItemValue& value, const bool& set_selected)
{
    std::vector<AlgoSelectWidget::AlgoName> algo_name_ui_list;
    for (auto& name : algo_list)
    {
        algo_name_ui_list.push_back(AlgoSelectWidget::AlgoName(name.first, name.second));
    }

    show_index_map.insert({ value.window_name.toLocal8Bit().toStdString(), count() + 1 });

    auto algo = new AlgoSelectWidget(this, algo_name_ui_list, defect_type_list);
    QListWidgetItem* item = new QListWidgetItem();
    item->setSizeHint(algo->sizeHint());
    this->addItem(item);
    this->setItemWidget(item, algo);

    SetDetectWindowItemValue(value, item);

    this->clearSelection();
    if (set_selected)
    {
        this->setItemSelected(item, true);
    }

    for (int i = 0; i < count(); i++)
    {
        auto w = (AlgoSelectWidget*)this->itemWidget(this->item(i));
        if (w)
        {
            w->UpdateParentWinIndexMap(show_index_map);
        }
    }

    connect(algo, &AlgoSelectWidget::SignalValueChanged, this, [this](DetectWindowItemValue& value)
        {

            emit SignalDetectWinValueChanged(value);

            /*AlgoSelectWidget* algo = nullptr;
            for (int i = 0; i < this->count(); ++i)
            {
                QListWidgetItem* item = this->item(i);
                if (item->data(Qt::UserRole).toString() == value.window_name)
                {
                    QWidget* widget = this->itemWidget(item);
                    if (widget)
                    {
                        algo = dynamic_cast<AlgoSelectWidget*>(widget);
                    }
                    item->setData(Qt::UserRole + 6, value.is_enable);
                }
            }


            if (cur_select_name != value.window_name.toLocal8Bit().toStdString())
            {
                algo->UndoChange();
            }
            else
            {
                algo->UpdateCurValueFromUi();
                emit SignalDetectWinValueChanged(value);
            }*/

        });

    connect(algo, &AlgoSelectWidget::SignalDelete, this, [this](const QString& region_name_)
        {
            DeleteDetectWindowItem(region_name_);
            emit SignalRegionDelete(region_name_);
        });
}

int DetectWindowListWidget::SetDetectWindowItemValue(const DetectWindowItemValue& value, QListWidgetItem* item)
{
    QWidget* widget = this->itemWidget(item);
    auto algo = dynamic_cast<AlgoSelectWidget*>(widget);

    if (!algo)
    {
        return -1;
    }
    algo->SetValue(value);

    item->setData(Qt::UserRole, value.window_name);
    item->setData(Qt::UserRole + 1, value.model_name);
    item->setData(Qt::UserRole + 2, value.parent_window_name);
    item->setData(Qt::UserRole + 3, value.group_name);
    item->setData(Qt::UserRole + 4, value.defect_type_name);
    item->setData(Qt::UserRole + 5, value.algo_name);
    item->setData(Qt::UserRole + 6, value.is_enable);
    item->setData(Qt::UserRole + 7, value.excute_state);
    item->setData(Qt::UserRole + 8, value.serach_size);

    return 0;
}

int DetectWindowListWidget::GetDetectWindowItemValue(const QListWidgetItem* item, DetectWindowItemValue& value)
{
    value.window_name = item->data(Qt::UserRole).value<QString>();
    value.model_name = item->data(Qt::UserRole + 1).value<QString>();
    value.parent_window_name = item->data(Qt::UserRole + 2).value<QString>();
    value.group_name = item->data(Qt::UserRole + 3).value<QString>();
    value.defect_type_name = item->data(Qt::UserRole + 4).value<QString>();
    value.algo_name = item->data(Qt::UserRole + 5).value<QString>();
    value.is_enable = item->data(Qt::UserRole + 6).value<bool>();
    value.excute_state = item->data(Qt::UserRole + 7).value<int>();
    return 0;
}

std::vector<DetectWindowItemValue> DetectWindowListWidget::GetAllDetectWindowItemValue()
{
    std::vector<DetectWindowItemValue> value_list;
    for (int i = 0; i < this->count(); ++i)
    {
        QListWidgetItem* item = this->item(i);
        DetectWindowItemValue value;
        GetDetectWindowItemValue(item, value);
        value_list.push_back(value);
    }
    return value_list;
}

void DetectWindowListWidget::UpdateDetectWindowItem(const QString& window_name, const DetectWindowItemValue& value)
{
    auto item = ReadDetectWindowItem(window_name);
    if (!item)
    {
        CreateDetectWindowItem(value);
        return;
    }
    SetDetectWindowItemValue(value, item);
    //algo->SetValue(value);
}

void DetectWindowListWidget::DeleteDetectWindowItem(const QString& window_name)
{
    for (int i = this->count() - 1; i >= 0; --i)
    {
        QListWidgetItem* item = this->item(i);
        if (item->data(Qt::UserRole).toString() == window_name)
        {
            delete this->takeItem(this->row(item));
        }
    }

    show_index_map.clear();
    show_index_map.insert({ "", 0 });
    for (int i = 0; i < this->count(); ++i)
    {
        show_index_map.insert({ this->item(i)->data(Qt::UserRole).toString().toLocal8Bit().data(), i + 1 });
    }

    for (int i = 0; i < this->count(); i++)
    {
        auto w = (AlgoSelectWidget*)this->itemWidget(this->item(i));
        if (w)
        {
            w->UpdateParentWinIndexMap(show_index_map);
        }
    }
}

QListWidgetItem* DetectWindowListWidget::ReadDetectWindowItem(const QString& window_name)
{
    for (int i = this->count() - 1; i >= 0; --i)
    {
        QListWidgetItem* item = this->item(i);
        if (item->data(Qt::UserRole).toString() == window_name)
        {
            return item;
        }
    }
    return nullptr;

}

void DetectWindowListWidget::BindSelectedDetectWindowItems(const QString& group_name)
{
    auto items = this->selectedItems();
    std::vector<QWidget*> sws;
    for (auto& item : items)
    {
        auto w = this->itemWidget(item);
        if (w)
        {
            sws.push_back(w);
        }
    }

    if (sws.size() < 2)
    {
        return;
    }
    for (auto& sw : sws)
    {
        auto algo = dynamic_cast<AlgoSelectWidget*>(sw);
        if (!algo) continue;

        algo->SetGroup(group_name);
    }
}

void DetectWindowListWidget::UnBindSelectedDetectWindowItems()
{
    auto items = this->selectedItems();
    std::vector<QWidget*> sws;
    for (auto& item : items)
    {
        auto w = this->itemWidget(item);
        if (w)
        {
            sws.push_back(w);
        }
    }

    for (auto& sw : sws)
    {
        auto algo = dynamic_cast<AlgoSelectWidget*>(sw);
        if (!algo) continue;

        algo->SetGroup("X");
    }
}

void DetectWindowListWidget::SetAlgoList(const std::vector<std::pair<std::string, std::string>> _algo_list)
{
    algo_list = _algo_list;
}

void DetectWindowListWidget::SetDefectTypeList(const std::vector<std::string> _defect_type_list)
{
    defect_type_list = _defect_type_list;
}

QString DetectWindowListWidget::GetModelName()
{
    return model_name;
}

void DetectWindowListWidget::DeleteSelectedWidget(std::vector<std::string>& delete_list)
{
    delete_list.clear();
    for (QListWidgetItem* item : this->selectedItems())
    {
        delete_list.push_back(item->data(Qt::UserRole).toString().toLocal8Bit().data());
        delete this->takeItem(this->row(item));
    }

    show_index_map.clear();
    show_index_map.insert({ "", 0 });
    for (int i = 0; i < this->count(); ++i)
    {
        show_index_map.insert({ this->item(i)->data(Qt::UserRole).toString().toLocal8Bit().data(), i + 1 });
    }

    for (int i = 0; i < this->count(); i++)
    {
        auto w = (AlgoSelectWidget*)this->itemWidget(this->item(i));
        if (w)
        {
            w->UpdateParentWinIndexMap(show_index_map);
        }
    }
}

void DetectWindowListWidget::SelectDetectWin(const QString& window_name)
{
    for (int i = 0; i < this->count(); ++i)
    {
        QListWidgetItem* item = this->item(i);
        if (item->data(Qt::UserRole).toString() == window_name)
        {
            this->clearSelection();
            this->setItemSelected(item, true);
            break;
        }
    }
}

std::string DetectWindowListWidget::GetCurSelectName() const
{
    return cur_select_name;
}

// bool DetectWindowListWidget::SetValue(QWidget* widget, const DetectWindowItemValue& value)
//{
//    if (!widget)
//    {
//        return false;
//    }
//    auto algo = dynamic_cast<AlgoSelectWidget*>(widget);
//    if (!algo)
//    {
//        return false;
//    }
//    algo->SetValue(value);
//    return true;
//}

void DetectWindowListWidget::SelectCreatedRegion(const QString& window_name)
{
    (void)window_name;
    //ClearSelected();
    //SetSelected(window_name, true);
}

void DetectWindowListWidget::UpdateEditStatus()
{
    for (int row = 0; row < this->count(); ++row)
    {
        QListWidgetItem* item = this->item(row);
        auto w = this->itemWidget(item);
        auto algo = dynamic_cast<AlgoSelectWidget*>(w);
        if (algo)
        {
            if (this->isItemSelected(item))
            {
                algo->SetEditEnable(true);
            }
            else
            {
                algo->SetEditEnable(false);
            }
        }
    }
}

void DetectWindowListWidget::SlotSelectedChanged()
{
    UpdateEditStatus();

    auto items = this->selectedItems();

    std::vector<QWidget*> sws;
    for (auto& item : items)
    {
        auto w = this->itemWidget(item);
        if (w)
        {
            sws.push_back(w);
        }
    }

    if (!sws.empty())
    {
        auto algo = dynamic_cast<AlgoSelectWidget*>(sws[0]);
        if (algo)
        {
            DetectWindowItemValue value;
            algo->GetCurValueFromUi(value);

            //if (cur_select_name != value.window_name.toLocal8Bit().toStdString())
            {
                value.model_name = model_name;
                cur_select_name = value.window_name.toLocal8Bit().toStdString();
                emit SignalSelectedDetectWinChanged(value);
            }
        }
        algo->SetEditEnable(true);
    }
    else
    {
        cur_select_name = "";
    }
}
