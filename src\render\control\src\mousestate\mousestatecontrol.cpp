﻿#include "mousestatecontrol.hpp"
#include "eventparam.hpp"
#include "graphicsmanager.h"

namespace jrsrender
{
    struct ImplData
    {
        std::shared_ptr<GraphicsManager> graphics_manager_ptr;
    };

    void MouseStateControl::InitMember()
    {
        _impl_data = new ImplData();
    }

    jrsrender::MouseStateControl::MouseStateControl(std::shared_ptr<GraphicsManager> graphics_manager_ptr_)
    {
        InitMember();
        _impl_data->graphics_manager_ptr = graphics_manager_ptr_;
    }

    jrsrender::MouseStateControl::~MouseStateControl()
    {
        if (_impl_data)
        {
            delete _impl_data;
            _impl_data = nullptr;
        }
    }

    void  jrsrender::MouseStateControl::HandlerMouseGraphicsResponse(const MouseEventValue& value)
    {

        _impl_data->graphics_manager_ptr->ResponseEvent(value);
    }

    //void  jrsrender::MouseStateControl::HandlerMouseGraphicsAdd(const MouseEventValue& value)
    //{
    //    switch (m_createmode)
    //    {
    //    case CreateGraphicsMode::RECT:
    //        AddRect(value);
    //        break;
    //    case CreateGraphicsMode::CIRCLE:
    //        AddCircle(value);
    //        break;
    //    case CreateGraphicsMode::POLYGON:
    //        AddPolygon(value);
    //        break;
    //    case CreateGraphicsMode::BEZIER:
    //        AddBezier(value);
    //        break;
    //    case CreateGraphicsMode::SG:
    //        AddSG(value);
    //        break;
    //    case CreateGraphicsMode::MULTI_REGION:
    //        AddMR(value);
    //        break;
    //    case CreateGraphicsMode::PAD:
    //        AddPad(value);
    //        break;
    //    case CreateGraphicsMode::NONE:
    //        return;
    //    default:
    //        printInfo((std::stringstream() << "unknown graphics add"));
    //    }
    //    Update();
    //}

    //void  jrsrender::MouseStateControl::AddRect(const MouseEventValue& value)
    //{
    //    [[maybe_unused]] auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    auto& px = value.px;
    //    auto& py = value.py;
    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        std::shared_ptr<GraphicsAbstract> gh = std::make_shared<RectGraphics>();
    //        _impl_data->graphics_manager_ptr->BeginCreate(gh);
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh)
    //        {
    //            _impl_data->graphics_manager_ptr->UpdateGraphicsValue(gh, px, py, cx, cy, _impl_data->graphics_manager_ptr->GetDrawAngle());
    //            // gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
    //            // gh->SetWH(abs(px - cx), abs(py - cy));
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        _impl_data->graphics_manager_ptr->CommitCreate();
    //        ResetState();
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddCircle(const MouseEventValue& value)
    //{
    //    [[maybe_unused]] auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    auto& px = value.px;
    //    auto& py = value.py;
    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        std::shared_ptr<GraphicsAbstract> gh = std::make_shared<CircleGraphics>();
    //        // gh->SetValue(0, 0, 0, 0, _impl_data->graphics_manager_ptr ->GetDrawAngle());
    //        _impl_data->graphics_manager_ptr->BeginCreate(gh);
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh)
    //        {
    //            gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
    //            gh->SetWH(abs(px - cx), abs(py - cy));
    //            // 新增按住shift时，绘制正圆
    //            if (GetKeyDown(VK_SHIFT))
    //            {
    //                auto r = std::max(abs(px - cx), abs(py - cy));
    //                gh->SetWH(r, r);
    //            }
    //            // _impl_data->graphics_manager_ptr ->Draw();
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        _impl_data->graphics_manager_ptr->CommitCreate();
    //        ResetState();
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddPolygon(const MouseEventValue& value)
    //{
    //    const auto should_type = GraphicsFlag::polygon;
    //    auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    [[maybe_unused]] auto& px = value.px;
    //    [[maybe_unused]] auto& py = value.py;
    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //        }
    //        else
    //        {
    //            std::shared_ptr<GraphicsAbstract> tg =
    //                std::make_shared<PolygonGraphics>();
    //            _impl_data->graphics_manager_ptr->BeginCreate(tg);
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
    //            if (!(type & Qt::MouseButton::LeftButton) && tg->contours.size() > 1)
    //            {
    //                tg->DeletePoint();
    //            }

    //            tg->AddPoint(cx, cy);
    //            // _impl_data->graphics_manager_ptr ->Draw();

    //            printInfo((std::stringstream() << "temp point " << tg->contours.size())
    //                .str()
    //                .c_str());
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            // _impl_data->graphics_manager_ptr ->CommitCreate();
    //            ResetState();
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (!gh || gh->GetFlag() != should_type)
    //        {
    //            break;
    //        }

    //        auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            tg->DeletePoint();
    //            tg->ComfirmPoint();
    //            _impl_data->graphics_manager_ptr->CommitCreate();
    //            ResetState();
    //        }
    //        else
    //        {
    //            tg->AddPoint(cx, cy);

    //            printInfo(
    //                (std::stringstream() << &tg << "add point:" << tg->contours.size())
    //                .str()
    //                .c_str());
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddBezier(const MouseEventValue& value)
    //{
    //    const auto should_type = GraphicsFlag::Bezier;
    //    auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    auto& px = value.px;
    //    auto& py = value.py;
    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            // _impl_data->graphics_manager_ptr ->CancelCreate();
    //        }
    //        else
    //        {
    //            std::shared_ptr<BezierGraphics> tg = std::make_shared<BezierGraphics>();
    //            // auto ttg = std::dynamic_pointer_cast<BezierGraphics>(gh);
    //            tg->SetStart(px, py);
    //            _impl_data->graphics_manager_ptr->BeginCreate(tg);
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
    //            if (!(type & Qt::MouseButton::LeftButton))
    //            {
    //                // tg->DeletePoint();
    //                // tg->AddPoint(cx, cy);
    //            }
    //            else
    //            {
    //                tg->SetEnd(cx, cy, false);
    //            }

    //            // _impl_data->graphics_manager_ptr ->Draw();

    //            printInfo((std::stringstream()
    //                << "control point size:" << tg->controls.size())
    //                .str()
    //                .c_str());
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            // _impl_data->graphics_manager_ptr ->CommitCreate();
    //            ResetState();
    //        }
    //        else if (type & Qt::MouseButton::LeftButton)
    //        {
    //            auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //            if (gh && gh->GetFlag() == should_type)
    //            {
    //                auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
    //                tg->SetEnd(cx, cy, true);
    //            }
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (!gh || gh->GetFlag() != should_type)
    //        {
    //            break;
    //        }

    //        auto tg = std::dynamic_pointer_cast<BezierGraphics>(gh);
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            tg->ComfirmPoint();
    //            _impl_data->graphics_manager_ptr->CommitCreate();
    //            ResetState();
    //        }
    //        else
    //        {
    //            tg->AddPoint(cx, cy);

    //            // _impl_data->graphics_manager_ptr ->Draw();
    //            // printInfo(
    //            //     (std::stringstream() << &tg << "add point:" <<
    //            //     tg->contour.size()) .str() .c_str());
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddSG(const MouseEventValue& value)
    //{
    //    using GTYPE = SGGraphics;
    //    const auto should_type = GraphicsFlag::SG;
    //    auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    auto& px = value.px;
    //    auto& py = value.py;

    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            // _impl_data->graphics_manager_ptr ->CancelCreate();
    //        }
    //        else
    //        {
    //            auto tg = std::make_shared<GTYPE>();
    //            tg->SetStart(px, py);
    //            _impl_data->graphics_manager_ptr->BeginCreate(tg);
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        if (type & Qt::MouseButton::LeftButton)
    //        {
    //            auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //            if (gh && gh->GetFlag() == should_type)
    //            {
    //                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
    //                tg->SetEnd(cx, cy);

    //                printInfo((std::stringstream()
    //                    << "control point size:" << tg->GetPoint().size()));
    //            }
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            ResetState();
    //        }
    //        else if (type & Qt::MouseButton::LeftButton)
    //        {
    //            auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //            if (gh && gh->GetFlag() == should_type)
    //            {
    //                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
    //                tg->SetEnd(cx, cy);
    //                tg->EndComfirm();
    //            }
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (!gh || gh->GetFlag() != should_type)
    //        {
    //            break;
    //        }

    //        auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
    //        if (type & Qt::MouseButton::RightButton)
    //        {
    //            tg->ComfirmPoint();
    //            // tg->ComfirmPoint(tg.get());
    //            _impl_data->graphics_manager_ptr->CommitCreate();
    //            ResetState();
    //        }
    //        else
    //        {
    //            tg->AddPoint(cx, cy, false);
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddMR(const MouseEventValue& value)
    //{
    //    using GTYPE = MultiRegionGraphics;
    //    const auto should_type = GraphicsFlag::multiregion;
    //    auto& type = value.type;
    //    auto& cx = value.cx;
    //    auto& cy = value.cy;
    //    auto& px = value.px;
    //    auto& py = value.py;

    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            // _impl_data->graphics_manager_ptr ->CancelCreate();
    //        }
    //        else
    //        {
    //            auto tg = std::make_shared<GTYPE>();
    //            tg->region_num = 1;
    //            _impl_data->graphics_manager_ptr->BeginCreate(tg);
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::move:
    //    {
    //        auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //        if (gh && gh->GetFlag() == should_type)
    //        {
    //            gh->SetXY((cx + px) * 0.5f, (cy + py) * 0.5f);
    //            gh->SetWH(abs(px - cx), abs(py - cy));
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::release:
    //    {
    //        if (type & Qt::MouseButton::LeftButton)
    //        {
    //            auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //            if (gh && gh->GetFlag() == should_type)
    //            {
    //                auto tg = std::dynamic_pointer_cast<GTYPE>(gh);
    //                tg->region_cx = -tg->w() * 0.25f;
    //                tg->region_cy = 0;
    //                tg->region_w = tg->w() * 0.25f;
    //                tg->region_h = tg->h() * 0.5f;
    //                tg->region_angle = gh->a();
    //                tg->region_num = 2;
    //                tg->region_type = 1;
    //                _impl_data->graphics_manager_ptr->CommitCreate();
    //                ResetState();
    //            }
    //        }
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::AddPad(const MouseEventValue& value)
    //{
    //    (void)value;
    //}

    //void  jrsrender::MouseStateControl::AddSelectBatchPolygon(const MouseEventValue& value)
    //{
    //    printInfo("");
    //    const auto should_type = GraphicsFlag::polygon;
    //    switch (static_cast<MouseEventValue::MouseState>(value.state))
    //    {
    //    case MouseEventValue::MouseState::press:
    //    case MouseEventValue::MouseState::move:
    //    case MouseEventValue::MouseState::release:
    //    {
    //        AddPolygon(value);
    //        return;
    //    }
    //    break;
    //    case MouseEventValue::MouseState::clicked:
    //    {
    //        if (value.type & Qt::MouseButton::RightButton)
    //        {

    //            auto gh = _impl_data->graphics_manager_ptr->GetCreate();
    //            if (gh && gh->GetFlag() == should_type)
    //            {
    //                auto tg = std::dynamic_pointer_cast<PolygonGraphics>(gh);
    //                _impl_data->graphics_manager_ptr->CancelCreate();
    //                _impl_data->graphics_manager_ptr->TrySelectGraphics(tg->contours, false);
    //            }
    //        }
    //        else
    //        {
    //            AddPolygon(value);
    //            return;
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::HandlerRendermouseclicked(int type, int x, int y)
    //{
    //    printInfo((std::stringstream() << GetState() << "," << x << "," << y));

    //    auto r = _impl_data->graphics_manager_ptr->GetRenderer();
    //    float cx = x;
    //    float cy = y;
    //    r->MouseToWorld(cx, cy);

    //    switch (GetState())
    //    {
    //    case VisionMode::SELECT_GRAPHICS:
    //    {
    //        //_impl_data->graphics_manager_ptr ->ClearSelected();
    //        auto state = _impl_data->graphics_manager_ptr->TrySelectGraphics(cx, cy, false);
    //        if (state)
    //        {
    //            SetState(VisionMode::RESPONSE_GRAPHICS);
    //        }
    //        else
    //        {
    //            //By: HJC  点选空白区域不取消选择框
    //            //_impl_data->graphics_manager_ptr ->ClearSelected();
    //            ResetState();
    //        }
    //    }
    //    break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH:
    //    {
    //        auto state = _impl_data->graphics_manager_ptr->TrySelectGraphics(cx, cy, false);
    //        if (state)
    //        {
    //            SetState(VisionMode::RESPONSE_GRAPHICS);
    //        }
    //        else
    //        {
    //            _impl_data->graphics_manager_ptr->ClearSelected();
    //            ResetState();
    //        }
    //    }
    //    break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    //    {
    //        AddSelectBatchPolygon(
    //            MouseEventValue(type, cx, cy, cx, cy,
    //                MouseEventValue::MouseState::clicked));
    //    }
    //    break;
    //    case VisionMode::CREATE_GRAPHICS:
    //    {
    //        if (m_createmode != CreateGraphicsMode::NONE)
    //        {
    //            HandlerMouseGraphicsAdd(
    //                MouseEventValue(type, cx, cy, cx, cy,
    //                    MouseEventValue::MouseState::clicked));
    //        }
    //    }
    //    break;
    //    case VisionMode::MOVE_ALL_GRAPHICS:
    //    case VisionMode::MOVE_CAMERA:
    //        ResetState();
    //        break;
    //    case VisionMode::EDIT_GRAPHICS:
    //        SetState(VisionMode::RESPONSE_GRAPHICS);
    //        break;
    //    case VisionMode::RESPONSE_GRAPHICS:
    //        break;

    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::HandlerRendermousepress(int type, int x, int y)
    //{
    //    printInfo((std::stringstream() << GetState() << "," << x << "," << y));

    //    auto r = _impl_data->graphics_manager_ptr->GetRenderer();
    //    float cx = x;
    //    float cy = y;
    //    r->MouseToWorld(cx, cy);

    //    switch (GetState())
    //    {
    //    case VisionMode::RESPONSE_GRAPHICS:
    //    {
    //        if (GetKeyDown(VK_CONTROL))
    //        {
    //            SetState(VisionMode::SELECT_GRAPHICS_BATCH);
    //            return;
    //        }
    //        if (!GetKeyDown(VK_MENU))
    //        {
    //            SetState(VisionMode::SELECT_GRAPHICS);
    //            return;
    //        }
    //        auto state = _impl_data->graphics_manager_ptr->TryResponseGraphics(cx, cy, false);
    //        if (state == 0)
    //        {
    //            SetState(VisionMode::EDIT_GRAPHICS);
    //        }
    //        else
    //        {
    //            SetState(VisionMode::SELECT_GRAPHICS);
    //        }
    //    }
    //    break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    //    {
    //        AddSelectBatchPolygon(
    //            MouseEventValue(type, cx, cy, cx, cy,
    //                MouseEventValue::MouseState::press));
    //    }
    //    break;
    //    case VisionMode::HOVER:
    //    {
    //        /*在这里处理一部分按键响应*/
    //        if (type == Qt::LeftButton)
    //        {
    //            if (GetKeyDown(VK_CONTROL))
    //            {
    //                SetState(VisionMode::SELECT_GRAPHICS_BATCH);
    //            }
    //            else if (GetKeyDown(VK_SHIFT))
    //            {
    //                SetState(VisionMode::SELECT_GRAPHICS_BATCH_POLYGON);
    //            }
    //            // else if (_impl_data->graphics_manager_ptr ->TrySelectGraphics(cx, cy, false))
    //            // {
    //            //     // SetState(VisionMode::RESPONSE_GRAPHICS);
    //            // }
    //            else
    //            {
    //                SetState(VisionMode::SELECT_GRAPHICS);
    //                if (_impl_data->graphics_manager_ptr->TrySelectGraphics(cx, cy, false))
    //                {
    //                    SetState(VisionMode::RESPONSE_GRAPHICS);
    //                }
    //                if (_impl_data->graphics_manager_ptr->TryResponseGraphics(cx, cy, false) == 0)
    //                {
    //                    SetState(VisionMode::EDIT_GRAPHICS);
    //                }
    //            }
    //        }
    //    }
    //    break;
    //    case VisionMode::CREATE_GRAPHICS:
    //    {
    //        if (m_createmode != CreateGraphicsMode::NONE)
    //        {
    //            HandlerMouseGraphicsAdd(
    //                MouseEventValue(type, cx, cy, cx, cy,
    //                    MouseEventValue::MouseState::press));
    //        }
    //    }
    //    break;
    //    case VisionMode::MOVE_ALL_GRAPHICS:
    //    {

    //    }
    //    case VisionMode::MANUAL_CREATE_GRAPHICS:
    //    {
    //        /*在这里处理一部分按键响应*/
    //        if (type == Qt::LeftButton)
    //        {
    //            /*SetState(VisionMode::SELECT_GRAPHICS);
    //            if (_impl_data->graphics_manager_ptr ->TrySelectGraphics(cx, cy, false))
    //            {
    //                SetState(VisionMode::RESPONSE_GRAPHICS);
    //            }*/
    //            //if (_impl_data->graphics_manager_ptr ->TryResponseGraphics(cx, cy, false) == 0)
    //            //{
    //            //    SetState(VisionMode::EDIT_GRAPHICS);
    //            //}
    //            HandlerCursorchange(static_cast<int>(CustomCursorType::SelectPrecision));
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::HandlerRendermousemove(int type,
    //    int icx, int icy, int ilx, int ily, int ipx, int ipy)
    //{
    //    if (type == Qt::RightButton
    //        && GetState() != VisionMode::MOVE_CAMERA
    //        && GetState() != VisionMode::VIEW_ALL)
    //    {
    //        SetState(VisionMode::MOVE_CAMERA);
    //        return;
    //    }

    //    auto r = _impl_data->graphics_manager_ptr->GetRenderer();

    //    float lx = ilx;
    //    float ly = ily;
    //    r->MouseToWorld(lx, ly);
    //    float cx = icx;
    //    float cy = icy;
    //    r->MouseToWorld(cx, cy);
    //    float px = ipx;
    //    float py = ipy;
    //    r->MouseToWorld(px, py);

    //    // printInfo((std::stringstream()
    //    //            << GetState() << "," << "type:" << type << ","
    //    //            << "ilx " << ilx << "," << "ily " << ily << ","
    //    //            << "icx " << icx << "," << "icy " << icy << ","
    //    //            << "ipx " << ipx << "," << "ipy " << ipy << ",")
    //    //               .str()
    //    //               .c_str());
    //    // printInfo((std::stringstream()
    //    //     << GetState() << "," << "type:" << type << ","
    //    //     << "lx " << lx << "," << "ly " << ly << ","
    //    //     << "cx " << cx << "," << "cy " << cy << ","
    //    //     << "px " << px << "," << "py " << py << ","));

    //    switch (GetState())
    //    {
    //    case VisionMode::HOVER:
    //    {
    //        GetRenderer2DManager()->HoverColor(icx, icy);
    //        auto state = _impl_data->graphics_manager_ptr->TrySelectGraphics(cx, cy, true);
    //        if (!state)
    //        {
    //            GetCustomCursorManager()->ResetCursor();
    //            // HandlerCursorchange(static_cast<int>(CustomCursorType::Default));
    //        }
    //        // if (state == 0)
    //        // {
    //        // GetCustomCursorManager()->ResetCursor();
    //        // }
    //        // else if (state == 1)
    //        // {
    //        //     HandlerCursorchange(static_cast<int>(CustomCursorType::SelectAlternate));
    //        // }
    //        // else if (state == 2)
    //        // {
    //        //     HandlerCursorchange(static_cast<int>(CustomCursorType::SelectMove));
    //        // }
    //    }
    //    break;
    //    case VisionMode::MOVE_CAMERA:
    //        MoveCamera(lx - cx, ly - cy);
    //        break;
    //    case VisionMode::SELECT_GRAPHICS:
    //        _impl_data->graphics_manager_ptr->ClearTemporary();
    //        _impl_data->graphics_manager_ptr->UpdateTemporary(px, py, cx, cy, true);
    //        break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH:
    //        _impl_data->graphics_manager_ptr->ClearTemporary();
    //        _impl_data->graphics_manager_ptr->UpdateTemporary(px, py, cx, cy, true);
    //        _impl_data->graphics_manager_ptr->TrySelectGraphics(px, py, cx, cy, true);
    //        break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    //    {
    //        AddSelectBatchPolygon(
    //            MouseEventValue(type, cx, cy, cx, cy,
    //                MouseEventValue::MouseState::move));
    //    }
    //    break;
    //    case VisionMode::RESPONSE_GRAPHICS:
    //        if (!GetKeyDown(VK_MENU))
    //        {
    //            GetCustomCursorManager()->ResetCursor();
    //            return;
    //        }
    //        _impl_data->graphics_manager_ptr->TryResponseGraphics(cx, cy, true);
    //        break;
    //    case VisionMode::EDIT_GRAPHICS:
    //        if (!GetKeyDown(VK_MENU))
    //        {
    //            return;
    //        }
    //        _impl_data->graphics_manager_ptr->ResponseGraphics(px, py, cx, cy, true);
    //        break;
    //    case VisionMode::CREATE_GRAPHICS:
    //    {
    //        HandlerMouseGraphicsAdd(
    //            MouseEventValue(type, cx, cy, px, py,
    //                MouseEventValue::MouseState::move));

    //    }
    //    break;
    //    case VisionMode::MOVE_ALL_GRAPHICS:
    //    {
    //        _impl_data->graphics_manager_ptr->UpdateGraphicsMoveAll(cx - lx, cy - ly, false);
    //    }
    //    break;
    //    case VisionMode::MANUAL_CREATE_GRAPHICS:
    //    {
    //        if (type == Qt::LeftButton)
    //        {
    //            _impl_data->graphics_manager_ptr->ClearTemporary();
    //            _impl_data->graphics_manager_ptr->UpdateTemporary(px, py, cx, cy, true);
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::HandlerRendermouserelease(int type, int icx, int icy,
    //    int ipx, int ipy)
    //{
    //    auto r = _impl_data->graphics_manager_ptr->GetRenderer();

    //    float cx = icx;
    //    float cy = icy;
    //    r->MouseToWorld(cx, cy);
    //    float px = ipx;
    //    float py = ipy;
    //    r->MouseToWorld(px, py);

    //    printInfo((std::stringstream()
    //        << GetState() << "," << "type:" << type << ","
    //        << "cx " << cx << ",cy " << cy << "," << "px " << px << ",py "
    //        << py << ","));

    //    switch (GetState())
    //    {
    //    case VisionMode::MOVE_CAMERA:
    //        ResetState();
    //        break;
    //    case VisionMode::SELECT_GRAPHICS:
    //    {
    //        callback_regionselected(std::min(px, cx), std::min(py, cy), abs(cx - px), abs(cy - py));
    //        if (_impl_data->graphics_manager_ptr->IsHaveSelected())
    //        {
    //            SetState(VisionMode::RESPONSE_GRAPHICS);
    //        }
    //        else
    //        {
    //            _impl_data->graphics_manager_ptr->ClearSelected();
    //            ResetState();
    //        }
    //    }
    //    break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH:
    //    {
    //        callback_regionselected(std::min(px, cx), std::min(py, cy), abs(cx - px), abs(cy - py));
    //        auto state = _impl_data->graphics_manager_ptr->TrySelectGraphics(px, py, cx, cy, false);
    //        if (state)
    //        {
    //            SetState(VisionMode::RESPONSE_GRAPHICS);
    //        }
    //        else
    //        {
    //            _impl_data->graphics_manager_ptr->ClearSelected();
    //            ResetState();
    //        }
    //    }
    //    break;
    //    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
    //    {
    //        AddSelectBatchPolygon(
    //            MouseEventValue(type, cx, cy, cx, cy,
    //                MouseEventValue::MouseState::release));
    //    }
    //    break;
    //    case VisionMode::EDIT_GRAPHICS:
    //        _impl_data->graphics_manager_ptr->ResponseGraphics(0, 0, 0, 0, false);
    //        SetState(VisionMode::RESPONSE_GRAPHICS);
    //        break;
    //    case VisionMode::CREATE_GRAPHICS:
    //    {
    //        HandlerMouseGraphicsAdd(
    //            MouseEventValue(type, cx, cy, px, py,
    //                MouseEventValue::MouseState::release));
    //    }
    //    break;
    //    case VisionMode::MOVE_ALL_GRAPHICS:
    //    {
    //        _impl_data->graphics_manager_ptr->UpdateGraphicsMoveAll(0, 0, true);
    //        ResetState();
    //    }
    //    break;
    //    // case VisionMode::RESPONSE_GRAPHICS:
    //    //     break;

    //    case VisionMode::MANUAL_CREATE_GRAPHICS:
    //    {
    //        callback_regionselected(std::min(px, cx), std::min(py, cy), abs(cx - px), abs(cy - py));
    //    }
    //    break;

    //    default:
    //        break;
    //    }
    //}

    //void  jrsrender::MouseStateControl::HandlerRenderwheeldelta(int delta, int x, int y)
    //{
    //    // 滚轮模式
    //    enum class WheelType { ZoomIn = 1, ZoomOut = -1, None = 0 };
    //    WheelType wheel_type = (delta >= 120) ? WheelType::ZoomIn
    //        : (delta <= -120) ? WheelType::ZoomOut
    //        : WheelType::None;

    //    // 特殊处理逻辑 TODO
    //    if (IsAddGraphicsState(GetState()))
    //    {
    //        HandlerMouseGraphicsAdd(
    //            MouseEventValue(static_cast<int>(wheel_type), x, y, x, y,
    //                MouseEventValue::MouseState::wheel));
    //        return;
    //    }
    //    else if (GetState() == VisionMode::RESPONSE_GRAPHICS
    //        && GetKeyDown(VK_CONTROL))
    //    {
    //        HandlerMouseGraphicsResponse(
    //            MouseEventValue(static_cast<int>(wheel_type), x, y, x, y,
    //                MouseEventValue::MouseState::wheel));
    //        return;
    //    }


    //    auto r = _impl_data->graphics_manager_ptr->GetRenderer();

    //    // 按住ctrl 中心缩放
    //    bool is_zoom_center = GetKeyDown(VK_CONTROL);

    //    float origin_x = static_cast<float>(x);
    //    float origin_y = static_cast<float>(y);

    //    // 如果是中心缩放模式，转换鼠标到世界坐标系
    //    if (is_zoom_center && !r->MouseToWorld(origin_x, origin_y))
    //        return;

    //    switch (wheel_type)
    //    {
    //    case WheelType::ZoomIn:
    //        MoveCamera(static_cast<int>(CameraDirection::Rear));
    //        break;
    //    case WheelType::ZoomOut:
    //        MoveCamera(static_cast<int>(CameraDirection::Front));
    //        break;
    //    case WheelType::None:
    //        return;
    //    }

    //    // 如果在中心缩放模式，根据鼠标坐标的变化调整摄像机位置
    //    if (is_zoom_center)
    //    {
    //        float result_x = static_cast<float>(x);
    //        float result_y = static_cast<float>(y);

    //        // 转换鼠标到世界坐标系
    //        if (!r->MouseToWorld(result_x, result_y))
    //            return;

    //        // 调整摄像机位置以实现平移效果
    //        MoveCamera(origin_x - result_x, origin_y - result_y);
    //    }
    //}


}



