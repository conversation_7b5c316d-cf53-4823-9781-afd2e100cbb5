﻿#include "EditView.h"
#include "render2deventparam.hpp"

#pragma warning(push,3)
#include "ui_EditView.h"
#pragma warning(pop)
#include <QButtonGroup>
using namespace jrsdata;
namespace jrsaoi
{
    EditView::EditView(QWidget* parent)
        : QWidget(parent)
        , ui(new Ui::EditView)
    {
        ui->setupUi(this);
        this->setWindowFlags(Qt::Tool | Qt::WindowStaysOnTopHint);
        Init();
    }

    EditView::~EditView()
    {
        if (ui)
        {
            delete ui;
            ui = nullptr;
        }
    }

    int EditView::UpdateView(const ShowTableParamBasePtr& param_)
    {
        auto edit_view_ptr = std::dynamic_pointer_cast<jrsdata::EditViewData>(param_);

        if (!edit_view_ptr)
        {
            Log_ERROR("指针转换失败，数据更新失败");
            return -1;
        }

        switch (edit_view_ptr->update_widget)
        {
        case EditViewData::UpdateWidgets::Component:
        {

            UpdateComponentWidgets(edit_view_ptr);
            SetTabState(0, true, true, false);
            SetEnabledPadWidget(false);
        }
        break;

        case EditViewData::UpdateWidgets::PAD:
            UpdatePadWidgets(edit_view_ptr);
            SetTabState(1, true, true, false);
            SetEnabledPadWidget(false);  //暂时屏蔽pad的编辑功能
            ui->pad_is_detect->setEnabled(true);  //默认打开


            break;

        case EditViewData::UpdateWidgets::DETECT_WINDOW:
            UpdateDetectWindowWidgets(edit_view_ptr);

            break;

        default:
            break;
        }

        _edit_veiw_data_ptr = edit_view_ptr;
        return 0;
    }
    void EditView::SetTabState(int currentIndex, bool tab0, bool tab1, bool tab2)
    {
        ui->tabWidget->setTabEnabled(0, tab0);
        ui->tabWidget->setTabEnabled(1, tab1);
        ui->tabWidget->setTabEnabled(2, tab2);
        ui->tabWidget->setCurrentIndex(currentIndex);
    }

    void EditView::SetEnabledPadWidget(bool enable_)
    {
        auto& pad_widgets = _widget_and_widgets[jrsdata::EditViewData::UpdateWidgets::PAD];
        for (auto& [key, widget] : pad_widgets)
        {
            if (widget)
            {
                widget->setEnabled(enable_);
            }
        }

    }

    void EditView::SetRadioButtonChecked(QWidget* widget)
    {
        if (widget)
        {
            QRadioButton* radioButton = qobject_cast<QRadioButton*>(widget);
            if (radioButton)
            {
                radioButton->setChecked(true);
            }
        }
    }
    void EditView::SetComboBoxCurrentIndex(int index)
    {
        if (index != -1)
        {
            ui->combo_add_pad_type->setCurrentIndex(index);
        }
    }
    void EditView::UpdateComponentWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr)
    {
        UpdateWidgets(edit_view_ptr->component_edit_data, jrsdata::EditViewData::UpdateWidgets::Component);
    }
    void EditView::UpdatePadWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr)
    {
        UpdateWidgets(edit_view_ptr->pad_edit_data.common, jrsdata::EditViewData::UpdateWidgets::PAD);
        SetRadioButtonChecked(_detect_window_direction_radios[edit_view_ptr->pad_edit_data.direction]);
        SetComboBoxCurrentIndex(ui->combo_add_pad_type->findData(static_cast<int>(edit_view_ptr->pad_edit_data.pad_type)));
    }

    void EditView::UpdateDetectWindowWidgets(const jrsdata::EditViewDataPtr& edit_view_ptr)
    {
        UpdateWidgets(edit_view_ptr->detect_window_edit_data.second, edit_view_ptr->update_widget);
        if (edit_view_ptr->detect_window_edit_data.first == jrsdata::ComponentUnit::Type::PAD)
        {
            UpdatePadWidgets(edit_view_ptr);
            SetEnabledPadWidget(false);
            ui->pad_is_detect->setEnabled(true);  //默认打开
        }
        else
        {
            UpdateComponentWidgets(edit_view_ptr);
            SetEnabledPadWidget(false);
        }
        SetTabState(2, true, true, true);

    }

    void EditView::SlotAddPadTypeChange(int current_index)
    {
        int key = ui->combo_add_pad_type->itemData(current_index).toInt();  // 获取对应的 key 值
        pad_event_param.create_type = static_cast<ComponentUnit::PadType>(key);
    }

    void EditView::SlotUpdateEditData(double value)
    {
        (void)value;
        if (!_edit_veiw_data_ptr)
        {
            return;
        }

        QObject* sender_obj = sender();
        jrsdata::CommonEditData* edit_data = nullptr;

        // 遍历 _widget_and_widgets 中的控件，找到触发事件的控件，并更新相应的 jrsdata::CommonEditData 结构体
        for (const auto& [widget_type, widgets] : _widget_and_widgets)
        {
            for (const auto& [edit_widget, widget] : widgets)
            {
                if (widget == sender_obj)
                {
                    switch (widget_type)
                    {
                    case EditViewData::UpdateWidgets::Component:
                        _edit_veiw_data_ptr->update_widget = EditViewData::UpdateWidgets::Component;
                        edit_data = &_edit_veiw_data_ptr->component_edit_data;
                        break;
                    case EditViewData::UpdateWidgets::PAD:
                        _edit_veiw_data_ptr->update_widget = EditViewData::UpdateWidgets::PAD;
                        edit_data = &_edit_veiw_data_ptr->pad_edit_data.common;
                        break;
                    case EditViewData::UpdateWidgets::DETECT_WINDOW:
                        _edit_veiw_data_ptr->update_widget = EditViewData::UpdateWidgets::DETECT_WINDOW;
                        edit_data = &_edit_veiw_data_ptr->detect_window_edit_data.second;
                        break;
                    default:
                        return;
                    }

                    if (edit_data)
                    {
                        switch (edit_widget)
                        {
                        case EditWidget::X:
                            edit_data->cx = dynamic_cast<QDoubleSpinBox*>(widget)->value();
                            break;
                        case EditWidget::Y:
                            edit_data->cy = dynamic_cast<QDoubleSpinBox*>(widget)->value();
                            break;
                        case EditWidget::Width:
                            edit_data->width = dynamic_cast<QDoubleSpinBox*>(widget)->value();
                            break;
                        case EditWidget::Height:
                            edit_data->height = dynamic_cast<QDoubleSpinBox*>(widget)->value();
                            break;
                        case EditWidget::Angle:
                            edit_data->angle = dynamic_cast<QDoubleSpinBox*>(widget)->value();
                            break;
                        case EditWidget::Is_Detect:
                            edit_data->is_detect = !dynamic_cast<QCheckBox*>(widget)->isChecked();
                            break;
                        default:
                            break;
                        }
                    }
                    SlotUpdateToRender();
                    return; // 找到并更新后直接返回
                }
            }
        }
    }
    void EditView::EmitRenderEvent(const std::string& event_name, const jrsdata::RenderEventParamPtr& param)
    {
        param->event_name = event_name;
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME;
        param->sub_name = jrsaoi::SHOWLIST_CHANGE_SUB_NAME;
        emit SigUpdateView(param);
    }

    void EditView::LockTabPage(int start_index_)
    {
        for (int index = 0; index < start_index_; ++index)
        {
            ui->tabWidget->setTabEnabled(index, false);  // 禁用其他 Tab
        }

        for (int i = start_index_; i < ui->tabWidget->count(); ++i)
        {
            ui->tabWidget->setTabEnabled(i, false);  // 禁用其他 Tab
        }

    }

    void EditView::SlotUpdateToRender()
    {
        auto param = std::make_shared<jrsdata::RenderEventParam>();
        param->edit_data_update_ptr = std::make_shared<EditViewData>(*_edit_veiw_data_ptr);
        EmitRenderEvent(jrsaoi::EDIT_UPDATE_RENDER_EVENT_NAME, param);
    }

    void EditView::SlotHandlePad()
    {
        QObject* obj = sender();  // 获取信号的发送者

        auto param = std::make_shared<jrsdata::RenderEventParam>();
        param->pad_param = pad_event_param;

        if (obj == ui->pb_add_pad_manual)  // 例如，btn_add_pad 是添加按钮
        {
            param->pad_param.step = jrsdata::PadEventParam::Step::CREATE;
        }
        else if (obj == ui->btn_pad_alter)  // 例如，btn_alter_pad 是修改按钮
        {
            param->pad_param.step = jrsdata::PadEventParam::Step::ALTER;
        }
        else
        {
            return;  // 如果发送者不匹配，不执行
        }

        EmitRenderEvent(jrsaoi::CREATE_PAD_EVENT_NAME, param);
    }

    void EditView::SlotUpdateDetectWindowValue()
    {
        QObject* obj = sender();
        if (!_edit_veiw_data_ptr)
        {
            return;
        }
        auto father_type = _edit_veiw_data_ptr->detect_window_edit_data.first;
        jrsdata::CommonEditData common;
        if (father_type == jrsdata::ComponentUnit::Type::BODY)
        {
            common = _edit_veiw_data_ptr->component_edit_data;
        }
        else
        {
            common = _edit_veiw_data_ptr->pad_edit_data.common;
        }
        if (obj == ui->equal_btn_x)
        {
            ui->detect_window_x->setValue(0);
            Q_EMIT ui->detect_window_x->editingFinished();
        }
        else if (obj == ui->equal_btn_y)
        {
            ui->detect_window_y->setValue(0);
            Q_EMIT ui->detect_window_y->editingFinished();
        }
        else if (obj == ui->equal_btn_w)
        {
            ui->detect_window_width->setValue(common.width);
            Q_EMIT ui->detect_window_width->editingFinished();
        }
        else if (obj == ui->equal_btn_h)
        {
            ui->detect_window_height->setValue(common.height);
            Q_EMIT ui->detect_window_height->editingFinished();
        }

    }

    void EditView::SlotUpdateComponentDegree()
    {
        double angle = ui->body_angle_value->value() + 90;
        jrscore::AOITools::NormalizeAngle(angle);
        ui->body_angle_value->setValue(angle);
        ui->body_angle_value->editingFinished();

    }

    void EditView::Init()
    {
        InitView();

        InitConnect();
        pad_event_param.direction = jrsdata::ComponentUnit::Direction::UNKNOWN;

    }
    void EditView::InitView()
    {

        _widget_and_widgets = {
          {EditViewData::UpdateWidgets::Component,{
            {EditWidget::X,ui->body_x},
            {EditWidget::Y,ui->body_y},
            {EditWidget::Width,ui->body_width},
            {EditWidget::Height,ui->body_height},
            {EditWidget::Angle,ui->body_angle_value},
            {EditWidget::Is_Detect,ui->component_is_detect}}
          },
          {EditViewData::UpdateWidgets::PAD,{
                //{EditWidget::Cols,ui->pad_col},
                //{EditWidget::Rows,ui->pad_row},
                {EditWidget::X,ui->pad_x},
                {EditWidget::Y,ui->pad_y},
                {EditWidget::Width,ui->pad_width},
                {EditWidget::Height,ui->pad_height},
                {EditWidget::Is_Detect,ui->pad_is_detect},
                  }
                },
                {EditViewData::UpdateWidgets::DETECT_WINDOW,{
                  {EditWidget::X,ui->detect_window_x},
                  {EditWidget::Y,ui->detect_window_y},
                  {EditWidget::Width,ui->detect_window_width},
                  {EditWidget::Height,ui->detect_window_height},
                  {EditWidget::Is_Detect,ui->detect_window_is_detect},
                  {EditWidget::Angle,ui->detect_window_angle_value},
                  }
                },
        };

        _detect_window_direction_radios =
        {
          {jrsdata::ComponentUnit::Direction::UNKNOWN, ui->radio_btn_pad_direct_none },
           {jrsdata::ComponentUnit::Direction::UP, ui->radio_btn_pad_direct_up},
          {jrsdata::ComponentUnit::Direction::DOWN,ui->radio_btn_pad_direct_down},
          {jrsdata::ComponentUnit::Direction::LEFT,ui->radio_btn_pad_direct_left},
          {jrsdata::ComponentUnit::Direction::RIGHT,ui->radio_btn_pad_direct_right},
          {jrsdata::ComponentUnit::Direction::INSIDE,ui->radio_btn_pad_direct_center},
        };

        auto button_group = new QButtonGroup(this);
        for (auto& [_index, radio_ui] : _detect_window_direction_radios)
        {
            (void)_index;
            button_group->addButton(static_cast<QAbstractButton*>(radio_ui));
            radio_ui->setEnabled(false);
        }

        /** < 控件值的初始化 */
        for (auto& [key, value] : PadEventParamPadCreateTypeMap)
        {
            ui->combo_add_pad_type->insertItem(static_cast<int>(key), value.c_str(), static_cast<int>(key));
        }
        ui->combo_add_pad_type->setCurrentIndex(0);

        /**< 坐标等这些控件的初始化 */
        std::vector<QDoubleSpinBox*> xy_widgets = {
          ui->body_x, ui->body_y, ui->pad_x, ui->pad_y, ui->detect_window_x, ui->detect_window_y
        };
        std::vector<QDoubleSpinBox*> wh_widgets =
        {
          ui->body_width, ui->body_height, ui->pad_width, ui->pad_height, ui->detect_window_width, ui->detect_window_height
        };
        std::vector<QDoubleSpinBox*> angle_widgets = {
          ui->body_angle_value, ui->detect_window_angle_value
        };

        for (auto* widget : xy_widgets)
        {
            if (widget)
            {
                widget->setRange(std::numeric_limits<double>::lowest(), std::numeric_limits<double>::max());
            }
        }

        for (auto* widget : wh_widgets)
        {
            if (widget)
            {
                widget->setRange(0, std::numeric_limits<double>::max());
            }
        }

        for (auto* widget : angle_widgets)
        {
            if (widget)
            {
                widget->setDecimals(1);       // 设置小数点后精度为 2 位
                widget->setSingleStep(0.1);
                widget->setRange(0, 359.9);
                widget->setWrapping(true);
                //widget->setReadOnly(true);
            }
        }
        InitHideView();
    }
    void EditView::InitHideView()
    {
        /**< 隐藏 widget */
        std::vector<QWidget*> widgets_to_hide = {
          ui->detect_window_angle_value,
          ui->pad_col,
          ui->pad_row,
          ui->label_38,
          ui->label_28,
          ui->label_29,
          ui->label_39,
          ui->detect_window_add_90_degrees,
          ui->detect_window_is_detect
        };

        for (auto& widget : widgets_to_hide)
        {
            widget->setHidden(true);
        }
        /** < 临时删除 角度 和检测框不测*/
        RemoveLayout(ui->horizontalLayout);
        RemoveLayout(ui->horizontalLayout_2);
    }
    void EditView::RemoveLayout(QLayout* layout)
    {
        if (layout)
        {
            QLayout* parentLayout = layout->parentWidget()->layout();
            if (parentLayout)
            {
                parentLayout->removeItem(layout);
            }
            delete layout;
            layout = nullptr;
        }
    }
    void EditView::InitConnect()
    {
        connect(ui->pb_add_pad_manual, &QPushButton::clicked, this, &EditView::SlotHandlePad);
        connect(ui->combo_add_pad_type, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &EditView::SlotAddPadTypeChange);
        //当前数据
        connect(ui->btn_pad_alter, &QPushButton::clicked, this, &EditView::SlotHandlePad);

        // 遍历 _widget_and_widgets 中的控件，并将它们的 editingFinished 信号连接到 SlotUpdateEditData 槽函数上
        for (const auto& [widget_type, widgets] : _widget_and_widgets)
        {

            for (const auto& [edit_widget, widget] : widgets)
            {
                if (auto* double_spin_box = dynamic_cast<QDoubleSpinBox*>(widget))
                {
                    double_spin_box->setKeyboardTracking(false);  // 关闭实时触发
                    connect(double_spin_box, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &EditView::SlotUpdateEditData);
                }
                else if (auto* check_box = dynamic_cast<QCheckBox*>(widget))
                {
                    connect(check_box, &QCheckBox::stateChanged, this, &EditView::SlotUpdateEditData);
                }
                //if (auto* spin_box = dynamic_cast<QDoubleSpinBox*>(widget))
                //{
                //    spin_box->setKeyboardTracking(false);  // 关闭实时触发
                //    connect(spin_box, QOverload<int>::of(&QSpinBox::valueChanged), this, &EditView::SlotUpdateEditData);
                //}
            }
        }
        connect(ui->equal_btn_x, &QPushButton::clicked, this, &EditView::SlotUpdateDetectWindowValue);
        connect(ui->equal_btn_y, &QPushButton::clicked, this, &EditView::SlotUpdateDetectWindowValue);
        connect(ui->equal_btn_w, &QPushButton::clicked, this, &EditView::SlotUpdateDetectWindowValue);
        connect(ui->equal_btn_h, &QPushButton::clicked, this, &EditView::SlotUpdateDetectWindowValue);
        connect(ui->body_add_90_degrees, &QPushButton::clicked, this, &EditView::SlotUpdateComponentDegree);



    }
    void EditView::UpdateWidgets(const jrsdata::CommonEditData& data_, jrsdata::EditViewData::UpdateWidgets widget_type_)
    {
        const auto& widgets = _widget_and_widgets[widget_type_];
        for (const auto& [edit_widget, widget] : widgets)
        {
            widget->blockSignals(true);
            switch (edit_widget)
            {
            case EditWidget::X:
                dynamic_cast<QDoubleSpinBox*>(widget)->setValue(data_.cx);
                break;
            case EditWidget::Y:
                dynamic_cast<QDoubleSpinBox*>(widget)->setValue(data_.cy);
                break;
            case EditWidget::Width:
                dynamic_cast<QDoubleSpinBox*>(widget)->setValue(data_.width);
                break;
            case EditWidget::Height:
                dynamic_cast<QDoubleSpinBox*>(widget)->setValue(data_.height);
                break;
            case EditWidget::Angle:
                if (auto* angle_widget = dynamic_cast<QDoubleSpinBox*>(widget))
                {
                    double angle = data_.angle;
                    jrscore::AOITools::NormalizeAngle(angle);
                    angle_widget->setValue(angle);
                }
                break;
            case EditWidget::Is_Detect:
                dynamic_cast<QCheckBox*>(widget)->setChecked(!data_.is_detect);
                break;
            default:
                break;
            }
            widget->blockSignals(false);
        }
    }

}
