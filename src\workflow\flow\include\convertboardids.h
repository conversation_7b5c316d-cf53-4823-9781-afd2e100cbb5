﻿#pragma once

#include <map>
#include <string>
#include <vector>

enum class ConvertDirection:int
{
    Y_AXIS_FLIP = 0,
    X_AXIS_FLIP
};
struct BoardIDCoors
{
    int id;                                 ///< 子板id
    float x;                                ///< 子板中心x坐标 整图中的像素坐标
    float y;                                ///< 子板中心y坐标 整图中的像素坐标
    BoardIDCoors()
        : id(-1), x(0.0), y(0.0) {}
    BoardIDCoors(int id, float x, float y)
        : id(id), x(x), y(y) {}
};

class ConvertBoardIDs
{
public:
    ConvertBoardIDs();
    ~ConvertBoardIDs();

public:
    /**
     * @fun ConvertBoardIdMapping
     * @brief 映射子板号A/B面 内部的
     * @param [IN] rows 子板行数
     * @param [IN] cols 子板列数
     * @date 2025.5.26
     * @return rows或cols为0返回false
     * <AUTHOR>
     */
    bool ConvertBoardIdMapping(int rows, int cols);
    /**
     * @fun GetMappingBoardIds
     * @brief 获取A板ID和B板ID的映射关系
     * @param [IN] vec_board_id_coors_finished 已经检测完成的工程子板号ID和子板坐标值
     * @param [IN] vec_board_id_coors_current 当前检测工程的子板号ID和子板坐标值
     * @param [IN] convert_direction 翻转的方向 X轴翻转或者Y轴翻转
     * @param [OUT] map_subboard_id_mapping_flip current板ID和finished板的映射map
     * @date 2025.5.27
     * @return vec_board_id_coors_a或vec_board_id_coors_b为0返回false
     * <AUTHOR>
     */
    bool GetMappingBoardIds(const std::vector<BoardIDCoors>& vec_board_id_coors_finished, const std::vector<BoardIDCoors>& vec_board_id_coors_current,
        const ConvertDirection& convert_direction, std::map<int/*B面真实ID*/, int/*A面真实ID*/>& map_subboard_id_mapping_flip/*翻转映射表*/);

private:

    /**
     * @fun MakeBoardIdMappingHorizontalFlip
     * @brief 获取板子B面到A面的映射关系 横向翻转
     * @param convert_board_ids_map B面和A面的映射表 横向翻转
     * @param board_rows 板子行数
     * @param board_columns 板子列数
     * @return 行列为0返回false
     */
    bool MakeBoardIdMappingHorizontalFlip(std::map<int, int>& convert_board_ids_map, int board_rows, int board_columns);//横向翻转

    /**
     * @fun MakeBoardIdMappingVerticalFlip
     * @brief 获取板子B面到A面的映射关系 竖向翻转
     * @param convert_board_ids_map B面和A面的映射表 竖向翻转
     * @param board_rows 板子行数
     * @param board_columns 板子列数
     * @return 行列为0返回false
     */
    bool MakeBoardIdMappingVerticalFlip(std::map<int, int>& convert_board_ids_map, int board_rows, int board_columns);//竖向翻转

private:


    std::map<int/*B面ID*/, int/*A面ID*/> m_board_id_mapping_horizontal_flip;          //横向翻转
    std::map<int/*B面ID*/, int/*A面ID*/> m_board_id_mapping_vertical_flip;            //竖向翻转

};
