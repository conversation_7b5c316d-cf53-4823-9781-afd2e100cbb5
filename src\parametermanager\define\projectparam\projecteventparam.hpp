///*****************************************************************//**
// * @file   projecteventparam.hpp
// * @brief  用于工程相关的事件参数传递
// * @details
// * <AUTHOR>
// * @date 2024.8.14
// * <table>
// * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
// * <tr><td>2024.8.14          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
// * </table>
// * @copyright 版权 CopyRight (C), 2023-2024.
// *********************************************************************/
//#ifndef __JRSPROJECTEVENTPARM_HPP__
//#define __JRSPROJECTEVENTPARM_HPP__
//
//
////STD
//#include <iostream>
//#include <functional>
////Custom
////Third
//
//
//namespace jrsdata
//{
//    //! 工程事件参数
//    struct ProjectEventParam
//    {
//        std::string event_name;
//
//        ProjectEventParam ()
//            :event_name ( {""})
//        {
//
//        }
//    };
//    using ProjectEventParamPtr = std::shared_ptr<ProjectEventParam>;
//
//    using InvokeProjectFun = std::function<int ( const ProjectEventParamPtr& )>;
//
//}
//#endif // !__JRSPROJECTEVENTPARM_HPP__