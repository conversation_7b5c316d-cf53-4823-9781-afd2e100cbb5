﻿/*****************************************************************
 * @file   saveopenimage.h
 * @brief  用于保存打开图片
 * @details
 * <AUTHOR>
 * @date 2024.11.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.18          <td>V2000              <td>baron      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/


#ifndef SAVEOPENIMAGEVIEW_H
#define SAVEOPENIMAGEVIEW_H
// prebuild
#include "pch.h"
 //STD
#include <string>

//Qt
#include <QWidget>
#include <QString>
#include <QFileDialog>
#include <QTableView>
#include <QStandardItemModel>
#include <QCompleter>
#include <QStringList>
#include <QDateTime>
#include <QStringListModel>

//Custom
#include "customstyle.hpp"
//#include "viewparam.hpp"

//Third

using namespace jrscore;

namespace Ui
{
    class SaveOpenImageView;
}
namespace jrsaoi
{
    struct SelectImageGroupInfo;

    class SaveOpenImageView : public QWidget
    {
        Q_OBJECT

    public:
        explicit SaveOpenImageView(QWidget* parent = nullptr);
        ~SaveOpenImageView();
        void SetCurrentProjectPathAndName(const std::string& project_path_, const std::string& project_name_);

    signals:
        /**
         * @fun SignalOpenImageGroup
         * @brief 发送信号到打开图片组的路径
         * @param SignalOpenImageGroup
         * <AUTHOR>
         * @date 2024.11.21
         */
        void SignalOpenImageGroup(const std::string open_group_path);
        /**
         * @fun SignalSaveImageGroup
         * @brief 发送信号到保存图片组的路径
         * @param save_group_path
         * <AUTHOR>
         * @date 2024.11.21
         */
        void SignalSaveImageGroup(const std::string save_group_path);

    private:
        /**
         * @fun Init 进行界面初始化工作
         * @brief
         * <AUTHOR>
         * @date 2024.11.18
         */
        void Init();

        /**
         * @fun InitListView
         * @brief 初始化list界面
         * <AUTHOR>
         * @date 2024.11.18
         */
        void InitListView();

        /**
         * @fun InitMember
         * @brief 初始化属性
         * <AUTHOR>
         * @date 2024.11.18
         */
        void InitMember();

        /**
         * @fun InitShowListHeader
         * @brief 初始化添加list的头
         * <AUTHOR>
         * @date 2024.11.18
         */
        void InitShowListHeader();

        /**
         * @fun InitTabeViewColumnWidth
         * @brief
         * @param table_view
         * @param column_widths
         * <AUTHOR>
         * @date 2024.11.18
         */
        void InitTableViewColumnWidth(QTableView* table_view, std::vector<int> column_widths);

        /**
         * @fun InitConnect
         * @brief 初始化槽函数连接
         * <AUTHOR>
         * @date 2024.11.18
         */
        void InitConnect();

    public slots:

        /**
         * @fun SlotSelectImageFile
         * @brief 选择图片文件路径
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotOpenImageDirectory();

        /**
         * @fun SlotSelectImageFile
         * @brief 选择图片文件
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotSelectImageFile();

        /**
         * @fun SlotCancelSelectImageFile
         * @brief 取消选择图片文件
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotCancelSelectImageFile();

        /**
         * @fun SlotOpenImageGroup
         * @brief 打开图片文件
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotOpenImageGroup();

        /**
         * @fun SlotSaveImageProjectNameChanged
         * @brief 保存图片项目名称变化
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotSaveImageProjectNameChanged(int index_);

        /**
         * @fun SlotFilterSaveImageProject
         * @brief 过滤保存图片项目名称，并获取下面分组的名称
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotFilterSaveImageProject();

        /**
         * @fun SlotUpdateImageGroupList
         * @brief 更新图片分组列表
         * <AUTHOR>
         * @date 2024.11.19
         */
        void SlotUpdateImageGroupList();
        /**
         * @fun SlotListImageGroupTableRowClicked
         * @brief
         * @param index_
         * <AUTHOR>
         * @date 2024.11.28
         */
        void SlotListImageGroupTableRowClicked(const QModelIndex& index_);



    private:
        QFileInfoList GetAllFolderNamesByCurrentPath(const std::string& project_path_);
        void UpdateProjectFolderNames(const std::string& project_path_);

        Ui::SaveOpenImageView* ui;
        QStandardItemModel list_image_group_model;
        QStringList header_list;//标题栏数据
        QStringList project_group_names; //工程文件图片组名
        QStringList project_folder_names; /**< 工程名称*/
        QList<SelectImageGroupInfo> image_group_info_list;
        int select_image_index = -1;
        QCompleter* project_folder_name_completer;		// 创建 QCompleter
        QCompleter* project_group_image_name_completer;		// 创建 QCompleter
        QStringListModel* project_folder_name_model;
        QStringListModel* project_group_image_name_model;


    };
}

#endif // SAVEOPENIMAGEVIEW_H
