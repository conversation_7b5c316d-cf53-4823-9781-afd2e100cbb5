﻿#include "tools.hpp"
#include <string>
#include <sstream>
#include <chrono>
#include <ctime>
#include <iomanip>

std::vector<std::string> render::Tools::SplitString(const std::string& s, char delimiter)
{
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream token_stream(s);
    while (std::getline(token_stream, token, delimiter))
    {
        tokens.push_back(token);
    }
    return tokens;
}

std::string render::Tools::JoinString(const std::vector<std::string> str_vec, char connector)
{
    if (str_vec.empty())
    {
        return "";
    }
    std::string res_str = str_vec[0];
    for (size_t i = 1; i < str_vec.size(); ++i)
    {
        res_str += connector;
        res_str += str_vec[i];
    }
    return res_str;
}

double render::Tools::NormalizeAngle(const double& angle)
{
    double normalize_angle = angle;
    // 确保角度在 0 到 360 之间
    if (normalize_angle < 0)
    {
        normalize_angle = fmod(normalize_angle, 360.0); // 取模运算
        if (normalize_angle < 0)
        {
            normalize_angle += 360.0; // 如果结果为负数，加上 360
        }
    }
    else if (normalize_angle >= 360)
    {
        normalize_angle = fmod(normalize_angle, 360.0); // 如果角度大于等于 360，取模运算
    }
    return normalize_angle;
}

std::string render::Tools::GetCurrentTimeString(bool include_date /*= true*/, bool include_time /*= true*/, bool include_microseconds /*= false*/, const std::string& date_sep /*= "-"*/, const std::string& time_sep /*= ":"*/, const std::string& date_time_sep /*= " "*/)
{
    using namespace std::chrono;

    auto now = system_clock::now();
    auto duration = now.time_since_epoch();
    auto sec = duration_cast<seconds>(duration);
    auto microsec = duration_cast<microseconds>(duration - sec).count();

    std::time_t time = sec.count();
    std::tm tm;
#ifdef _MSC_VER
    localtime_s(&tm, &time);
#else
    localtime_r(&time, &tm);
#endif

    std::ostringstream oss;

    if (include_date)
    {
        oss << std::setfill('0')
            << std::setw(4) << (tm.tm_year + 1900) << date_sep
            << std::setw(2) << (tm.tm_mon + 1) << date_sep
            << std::setw(2) << tm.tm_mday;

        if (include_time)
            oss << date_time_sep;
    }

    if (include_time)
    {
        oss << std::setfill('0')
            << std::setw(2) << tm.tm_hour << time_sep
            << std::setw(2) << tm.tm_min << time_sep
            << std::setw(2) << tm.tm_sec;

        if (include_microseconds)
        {
            oss << "." << std::setw(6) << microsec;
        }
    }

    return oss.str();
}
std::string render::Tools::GetPrefixString(const std::string& ori_str_, const char& mark_char_)
{
    std::string prefix_string = ori_str_;
    size_t pos = ori_str_.find_last_of(mark_char_);
    if (pos != std::string::npos)
    {
        prefix_string = ori_str_.substr(0, pos);
    }
    return prefix_string;
}
