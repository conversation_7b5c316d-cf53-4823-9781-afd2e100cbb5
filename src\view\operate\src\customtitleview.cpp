//STD
#include <iostream>
//QT
#pragma warning(push, 3)
#include <QTextEdit>
#include <QStatusBar>
#include <QShortcut>
#include <QMessageBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QGridLayout>
#include <QLabel>
#include <QApplication>
#pragma warning(pop)
//Custom
#include "customtitleview.h"
#include "coreapplication.h"
#include "viewdefine.h"
#include "viewtool.hpp"
#include "image.hpp"
//Third
#include "SARibbonMainWindow.h"
#include "SAFramelessHelper.h"
#include "SARibbonApplicationButton.h"
#include "SARibbonBar.h"
#include "SARibbonButtonGroupWidget.h"
#include "SARibbonCategory.h"
#include "SARibbonCheckBox.h"
#include "SARibbonColorToolButton.h"
#include "SARibbonComboBox.h"
#include "SARibbonCustomizeDialog.h"
#include "SARibbonCustomizeWidget.h"
#include "SARibbonGallery.h"
#include "SARibbonLineEdit.h"
#include "SARibbonMenu.h"
#include "SARibbonPannel.h"
#include "SARibbonQuickAccessBar.h"
#include "SARibbonToolButton.h"
#include "SARibbonComboBox.h"
namespace jrsaoi
{
    CustomTitleView::CustomTitleView(SARibbonMainWindow* parent)
        :parent_view(parent)
        , menu_application_btn(nullptr)
    {
        Init();
        //this->setAttribute(Qt::WA_DeleteOnClose);  // 窗口关闭后居然不会进入析构函数,手动设置一下属性 jerx
    }
    CustomTitleView::~CustomTitleView()
    {
        // 设置了属性还是进不来
    }
    void CustomTitleView::SlotActionRenderInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::RenderViewParamPtr render_param = std::make_shared<jrsdata::RenderViewParam> ();
        render_param->module_name = jrsaoi::VIEW_MODULE_NAME;
        render_param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        Log_INFO ( action_name , "按钮触发" );
        if (action_name == "act_load_image")
        {
        
            render_param->sub_name = jrsaoi::SHORTCUT_RENDER_SUB_NAME;
            render_param->event_name = action_name;
            QString dir = QApplication::applicationDirPath () + "/";
            QStringList filter = { "Image(*.tif *.tiff *.gif *.bmp *.jpg *.jpeg *.jp2 *.png)" };
            auto file_name_str = OpenSingleFile ( "" , filter , dir ).toStdString ();
            // TODO 目前默认加载真彩图
            render_param->read_local_img_path_hash.emplace ( static_cast< int >( jrsdata::LightImageType::RGB ) , file_name_str );
        }
        else
        {
            render_param->sub_name = jrsaoi::SHORTCUT_RENDER_SUB_NAME;
            render_param->event_name = action_name;
            render_param->add_graphic_name = "Device";
        }
        emit SigActionTrigger ( render_param );
    }
    void CustomTitleView::SlotActionProjectInvokeFun()
    {
        auto param = std::make_shared<jrsdata::ProjectEventParam>();
        //param->project_param->file_param.file_type = jrsdata::FileType::BIN;
        //param->project_param->data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;

        param->event_name = jrsaoi::PROJECT_SAVE_EVENT_NAME;
        param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_RENDER_SUB_NAME;
        Log_INFO(sender()->objectName().toStdString(), "按钮触发");
        emit SigActionTrigger(param);
    }
    void CustomTitleView::SlotActionOperateInvokeFun()
    {
        //TODO:
        auto action_name = sender()->objectName().toStdString();
        jrsdata::OperateViewParamPtr motiondebug_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        motiondebug_view_ptr->module_name = OPERATE_MODULE_NAME;
        motiondebug_view_ptr->sub_name = OPERATE_UPDATE_SUB_NAME;
        motiondebug_view_ptr->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        motiondebug_view_ptr->event_name = action_name;
        emit SigActionTrigger(motiondebug_view_ptr);
    }
    void CustomTitleView::SlotActionDeviceInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        qDebug() << __FUNCTION__ << __LINE__ << " action_name " << action_name.c_str();
    }
    void CustomTitleView::SlotActionPermissionInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        qDebug() << __FUNCTION__ << __LINE__ << " action_name " << action_name.c_str();
    }
    void CustomTitleView::SlotActionMotionInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        jrsdata::OperateViewParamPtr motiondebug_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        motiondebug_view_ptr->module_name = VIEW_MODULE_NAME;
        motiondebug_view_ptr->sub_name =jrsaoi::SHORTCUT_OPERATER_SUB_NAME ;
        motiondebug_view_ptr->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        motiondebug_view_ptr->event_name = action_name;
        emit SigActionTrigger(motiondebug_view_ptr);
    }
    void CustomTitleView::SlotActionAxisMoveFun()
    {
         auto action_name = sender()->objectName();
        qDebug() << __FUNCTION__ << __LINE__ << " action_name " << action_name;
        jrsdata::TrackControlParam track_param;
        QString input_name = "act_input_board_";
        QString output_name = "act_output_board_";
        QString reset_name = "act_reset_track_";
        QString infeed_name = "act_product_return_infeed_sensor_";
        if (action_name.startsWith(input_name))	//	进板
        {
            Log_INFO(action_name.toStdString(), "进板");
            QString index_id = action_name.right(action_name.length() - input_name.length());
            track_param.track_index = index_id.toInt();
            track_param.track_type = jrsdata::TrackControlType::Load;
        }
        else if (action_name.startsWith(output_name))	//	出板
        {
            Log_INFO(action_name.toStdString(), "出板");
            QString index_id = action_name.right(action_name.length() - output_name.length());
            track_param.track_index = index_id.toInt();
            track_param.track_type = jrsdata::TrackControlType::UnLoad;
        }
        else if (action_name.startsWith(reset_name))	//	复位轨道
        {
            Log_INFO(action_name.toStdString(), "复位轨道");
            QString index_id = action_name.right(action_name.length() - reset_name.length());
            track_param.track_index = index_id.toInt();
            track_param.track_type = jrsdata::TrackControlType::InitTrack;
        }
        else if (action_name.startsWith(infeed_name))	//	产品回到进板感应器
        {
            Log_INFO(action_name.toStdString(), "产品回到进板感应器");
            QString index_id = action_name.right(action_name.length() - infeed_name.length());
            track_param.track_index = index_id.toInt();
            track_param.track_type = jrsdata::TrackControlType::RebackInputSignal;
        }
        jrsdata::OperateViewParamPtr track_control_view_ptr = std::make_shared<jrsdata::OperateViewParam>();
        track_control_view_ptr->module_name = jrsaoi::VIEW_MODULE_NAME;
        track_control_view_ptr->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        track_control_view_ptr->sub_name = jrsaoi::SHORTCUT_LOGIC_SUB_NAME;
        track_control_view_ptr->invoke_module_name = jrsaoi::DEVICE_MODULE_NAME;
        track_control_view_ptr->device_param.motion_param.track_param = track_param;
        track_control_view_ptr->device_param.event_name = "LoadAndUnLoad";
        track_control_view_ptr->device_param.device_type = jrsdata::DeviceType::Motion;
        track_control_view_ptr->event_name = action_name.toStdString();

        emit SigActionTrigger(track_control_view_ptr);
    }
    void CustomTitleView::SlotComboboxImageTypeChange(const QString& current_text)
    {
        auto param = std::make_shared<jrsdata::RenderEventParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::SHORTCUT_TRIGGER_TOPIC_NAME;
        //param->sub_name = jrsaoi::SHORTCUT_RENDER_SUB_NAME;
        param->sub_name = "all";
        param->event_name = jrsaoi::CHANGE_RENDER_SHOW_IMG_TYPE;
        param->select_param.show_img_type = current_text.toStdString();
        param->select_param.light_type = (jrsdata::LightImageType)image_color_select->currentData().toInt();
        emit SigActionTrigger(param);
    }
    void CustomTitleView::SlotActionLogicInvokeFun()
    {
        auto action_name = sender()->objectName().toStdString();
        qDebug() << __FUNCTION__ << __LINE__ << " action_name " << action_name.c_str();
        jrsdata::OperateViewParamPtr param = std::make_shared<jrsdata::OperateViewParam>();
        param->module_name = VIEW_MODULE_NAME;
        param->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        param->sub_name = SHORTCUT_LOGIC_SUB_NAME;
        param->invoke_module_name = LOGIC_MODULE_NAME;
        param->event_name = action_name;
        emit SigActionTrigger(param);
    }
    void CustomTitleView::SlotActionSettingViewParamFun()
    {
        auto action_name = sender()->objectName().toStdString();
        auto setting_ptr = std::make_shared<jrsdata::SettingViewParam>();
        setting_ptr->module_name = VIEW_MODULE_NAME;
        setting_ptr->sub_name = SETTING_SUB_NAME;
        setting_ptr->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
        setting_ptr->event_name = action_name;
        emit SigActionTrigger(setting_ptr);
    }
    void CustomTitleView::SlotGraphicUpdateFun(const jrsdata::ViewParamBasePtr& item_info)
    {
        auto item_info_render = std::static_pointer_cast<jrsdata::RenderViewParam>(item_info);
        if (!item_info_render)
        {
            return;
        }
        for (auto& value : device_pos_info_spinbox)
        {
            auto it = setters.find(value.first);
            if (it != setters.end())
            {
                it->second(value.second, item_info_render->selected_item_info);
            }
        }
    }
    void CustomTitleView::Init()
    {
        InitMember();
        InitView();
        QSize iconSize(32, 32);
    }
    void CustomTitleView::InitView()
    {
        setIconSize(QSize(32, 32));
        SARibbonBar* ribbon = parent_view->ribbonBar();
        ribbon->setTabBarHeight(50);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyleTwoRow);//两行模式
        ribbon->setContentsMargins(0, 0, 0, 0);
        CreateRibbonApplicationButton();
        CreateQuickAccessBar();
        CreateRightButtonGroup();
        /** 主面板 */
        SARibbonCategory* main_panel = ribbon->addCategoryPage(tr("主面板"));
        main_panel->setContentsMargins(0, 0, 0, 0);
        main_panel->setObjectName(("main_panel"));
        CreateMainPanel(main_panel);
        CreateRebackPanel(main_panel);
        CreateMotionControlPanel(main_panel, 0);
        CreateMotionControlPanel(main_panel, 1);
        CreateControlPanel(main_panel);
        CreateTestPanel(main_panel);
        CreateImagePanel(main_panel);
        CreateDetectPanel(main_panel);
        CreateCADPanel(main_panel);
        CreateDeviceControlPanel(main_panel);
        CreateDeviceShowPanel(main_panel);
        CreateDetectTestShowPanel(main_panel);
        CreateDetectResultSignalPanel(main_panel);
        Create3DPanel(main_panel);
        CreateModelPanel(main_panel);
        CreateLayerShowPanel(main_panel);
    }
    void CustomTitleView::InitMember()
    {
        invoke_fun_render = std::bind(&CustomTitleView::SlotActionRenderInvokeFun, this);
        invoke_fun_project = std::bind(&CustomTitleView::SlotActionProjectInvokeFun, this);
        invoke_fun_operate = std::bind(&CustomTitleView::SlotActionOperateInvokeFun, this);
        invoke_fun_device_manager = std::bind(&CustomTitleView::SlotActionDeviceInvokeFun, this);
        invoke_fun_motion = std::bind(&CustomTitleView::SlotActionMotionInvokeFun, this);
        invoke_fun_permission = std::bind(&CustomTitleView::SlotActionPermissionInvokeFun, this);
        invoke_fun_axis_move = std::bind(&CustomTitleView::SlotActionAxisMoveFun, this);
        invoke_fun_logic = std::bind(&CustomTitleView::SlotActionLogicInvokeFun, this);
        invoke_fun_setting_param = std::bind(&CustomTitleView::SlotActionSettingViewParamFun, this);
    }
    void CustomTitleView::CreateRibbonApplicationButton()
    {
        SARibbonBar* ribbon = parent_view->ribbonBar();
        if (!ribbon)
        {
            return;
        }
        ribbon->setContentsMargins(0, 0, 0, 0);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyleTwoRow);//两行模式
        QAbstractButton* btn = ribbon->applicationButton();
        if (!btn)
        {
            btn = new SARibbonApplicationButton(this);
            ribbon->setApplicationButton(btn);
        }
        ribbon->applicationButton()->setText(("  &AOI  "));
        if (!menu_application_btn)
        {
            menu_application_btn = new SARibbonMenu(this);
            //menu_application_btn->addAction(CreateAction("    新建", ":/image/newfile.png", "act_new_file", invoke_fun_project));
            auto menu_open = CreateMenu("打开", ":/image/open.png");
            //menu_open->addAction(CreateAction("程序", ":/image/project.png", "act_open_project", invoke_fun_project));
            menu_open->addAction(CreateAction("图像", ":/image/image.png", "act_load_image", invoke_fun_render));
            menu_application_btn->addMenu(menu_open);
        }
        SARibbonApplicationButton* app_btn = qobject_cast<SARibbonApplicationButton*>(btn);
        if (!app_btn)
        {
            return;
        }
        app_btn->setMenu(menu_application_btn);
    }
    QAction* CustomTitleView::CreateAction(const QString& text, const QString& iconurl, const QString& obj_name, InvokeFun invoke_fun_)
    {
        QAction* act = new QAction(this);
        act->setText(text);
        act->setIcon(QIcon(iconurl));
        act->setObjectName(obj_name);
        connect(act, &QAction::triggered, this, invoke_fun_);
        return act;
    }
    QAction* CustomTitleView::CreateAction(const QString& text, const QString& iconurl, const QString& obj_name)
    {
        QAction* act = new QAction(this);
        act->setText(text);
        act->setIcon(QIcon(iconurl));
        act->setObjectName(obj_name);
        return act;
    }
    QMenu* CustomTitleView::CreateMenu(const QString& text, const QString& iconurl)
    {
        QMenu* menu = new QMenu("    " + text + "    ", this);
        menu->setIcon(QIcon(iconurl));
        return menu;
    }
    void CustomTitleView::CreateQuickAccessBar()
    {
        SARibbonQuickAccessBar* quickAccessBar = parent_view->ribbonBar()->quickAccessBar();
        quickAccessBar->addAction(CreateAction("设置", ":/image/setting.png", SHOW_SETTING_VIEW_EVENT_NAME, invoke_fun_setting_param));
        //quickAccessBar->addAction(CreateAction("撤销", ":/image/revoke.png", "act_revoke", invoke_fun_render));
        //quickAccessBar->addAction(CreateAction("恢复", ":/image/recover.png", "act_recover", invoke_fun_render));
        quickAccessBar->addSeparator();
    }

    void CustomTitleView::CreateRightButtonGroup()
    {
        auto action_help_fun = [&]()
            {
                QMessageBox::information(this,
                    tr("infomation"),
                    tr("\n ==============="
                        "\n AOI version:V2.0"
                        "\n Author:JRS"
                        "\n Email:<EMAIL>"
                        "\n ==============="));
            };
        SARibbonBar* ribbon = parent_view->ribbonBar();
        if (!ribbon)
        {
            return;
        }
        ribbon->setContentsMargins(0, 0, 0, 0);
        ribbon->setRibbonStyle(SARibbonBar::RibbonStyle::WpsLiteStyleTwoRow);//两行模式
        SARibbonButtonGroupWidget* rightBar = ribbon->rightButtonGroup();
        QAction* action_help = CreateAction(tr("help"), ":/image/help.png", "act_help", action_help_fun);
        rightBar->addAction(action_help);
    }
    void CustomTitleView::CreateMainPanel(SARibbonCategory* page)
    {
        SARibbonPannel* main_pannel_style = page->addPannel(("主面板"));
        //main_pannel_style->setFixedHeight(40);
        //QAction* act_run = CreateAction(tr("Run"), ":/image/run.png", "act_run", invoke_fun_operate);
        //QAction* act_setting_manager = CreateAction(tr("系统设置"), ":/image/setting.png", SHOW_SETTING_VIEW_EVENT_NAME, invoke_fun_setting_param);
        //QAction* act_device_manager = CreateAction(tr("设备管理"), ":/image/devicemanager.png", "act_device_manager", invoke_fun_device_manager);
        //QAction* act_permission_manager = CreateAction(tr("权限管理"), ":/image/permissionsmanager.png", "act_permission_manager", invoke_fun_permission);
        //QAction* act_zero = CreateAction(tr("回零"), ":/image/zero.png", "act_AskInitial", invoke_fun_motion);
        //QAction* act_axis_move = CreateAction(tr("轴移动"), ":/image/axismove.png", "act_ShowAxisMoveDialog", invoke_fun_axis_move);
        ////加入模板
        ////main_pannel_style->setStyleSheet("QWidget{padding:0;margin:0;}");
        //main_pannel_style->addAction(act_run, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        //main_pannel_style->addAction(act_setting_manager, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        //main_pannel_style->addAction(act_device_manager, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        //main_pannel_style->addAction(act_permission_manager, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        //main_pannel_style->addAction(act_zero, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        //main_pannel_style->addAction(act_axis_move, QToolButton::InstantPopup, SARibbonPannelItem::Large);

        QAction* act_1 = CreateAction(tr("新建"), ":/image/newproj.png", jrsaoi::PROJECT_CREATE_EVENT_NAME, invoke_fun_operate);
        QAction* act_2 = CreateAction(tr("打开"), ":/image/openproj.png", jrsaoi::PROJECT_READ_EVENT_NAME, invoke_fun_operate);
        QAction* act_3 = CreateAction(tr("保存"), ":/image/save.png", jrsaoi::PROJECT_SAVE_EVENT_NAME, invoke_fun_operate);
        QAction* act_4 = CreateAction(tr("另存为"), ":/image/saveas.png", jrsaoi::PROJECT_SAVE_AS_EVENT_NAME, invoke_fun_operate);
        main_pannel_style->addAction(act_1, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(act_2, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(act_3, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(act_4, QToolButton::InstantPopup, SARibbonPannelItem::Large);
    }
    void CustomTitleView::CreateRebackPanel(SARibbonCategory* page)
    {
        SARibbonPannel* main_pannel_style = page->addPannel(("撤销"));
        QAction* act_1 = CreateAction(tr("撤销"), ":/image/undoaction.png", "act_revoke", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("恢复"), ":/image/antiundoaction.png", "act_recover", invoke_fun_render);
        main_pannel_style->addAction(act_1, QToolButton::InstantPopup, SARibbonPannelItem::Large);
        main_pannel_style->addAction(act_2, QToolButton::InstantPopup, SARibbonPannelItem::Large);
    }
    void CustomTitleView::CreateImagePanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("图片"));
        QAction* act_1 = CreateAction(tr("100%显示"), ":/image/img_1_1.png", "act_100%", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("全屏显示"), ":/image/img_max.png", "act_panorama", invoke_fun_render);
        QAction* act_selected_item_center = CreateAction(tr("图形居中"), ":/image/center_view.png", "act_selected_item_center", invoke_fun_render);
        QAction* act_4 = CreateAction(tr("放大"), ":/image/img_up_size.png", "act_zoomin", invoke_fun_render);
        QAction* act_5 = CreateAction(tr("缩小"), ":/image/img_down_size.png", "act_zoomout", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(94);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        btnGroup1->addAction(act_selected_item_center);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_4);
        btnGroup2->addAction(act_5);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDetectPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("检测框"));
        QAction* act_left_right_mirror = CreateAction(tr("左右镜像复制"), ":/image/cad_left_right_mirror.png", "act_copy_left_right_mirror", invoke_fun_render);
        QAction* act_top_bottom_mirror = CreateAction(tr("上下镜像复制"), ":/image/cad_top_bottom_mirror.png", "act_copy_top_bottom_mirror", invoke_fun_render);
        QAction* act_rotate_180 = CreateAction(tr("180度旋转复制"), ":/image/rotate-180.png", "act_copy_rotate_180", invoke_fun_render);
        QAction* act_rotate_90 = CreateAction(tr("90度旋转复制"), ":/image/rotate90.png", "act_copy_rotate_90", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_left_right_mirror);
        btnGroup1->addAction(act_top_bottom_mirror);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_rotate_180);
        btnGroup2->addAction(act_rotate_90);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateCADPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("CAD"));
        QAction* act_1 = CreateAction(tr("整体旋转90度"), ":/image/rotate90_3.png", "act_cad_overall_rotation_90", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("左右镜像"), ":/image/copy1.png", "act_cad_left_right_mirror", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("逆时针顺时针转换"), ":/image/reset1.png", "act_cad_counterclockwise_clockwise_conversion", invoke_fun_render);
        QAction* act_4 = CreateAction(tr("所有元件旋转90度"), ":/image/cad_angle+90.png", "act_cad_all_elements_rotated_90_degrees", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        btnGroup2->addAction(act_4);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDeviceControlPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("元件"));
        QAction* act_1 = CreateAction(tr("元件旋转90度"), ":/image/rotate90_2.png", "act_device_rotated_90_degrees", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("复制元件"), ":/image/copy.png", "act_device_copy", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("粘贴元件+流水号"), ":/image/paste.png", "act_device_paste", invoke_fun_render);
        QAction* act_4 = CreateAction(tr("添加元件"), ":/image/adddevice.png", "act_device_add", invoke_fun_render);
        QAction* act_5 = CreateAction(tr("删除元件"), ":/image/deletedevice.png", "act_device_delete", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(94);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        btnGroup1->addAction(act_3);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_4);
        btnGroup2->addAction(act_5);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateTestPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("测试"));
        QAction* act_1 = CreateAction(tr("检测框测试"), ":/image/run_1.png", "act_test_detects", invoke_fun_operate);
        QAction* act_2 = CreateAction(tr("元件测试"), ":/image/run_2.png", "act_test_devices", invoke_fun_operate);
        QAction* act_3 = CreateAction(tr("料号测试"), ":/image/run_3.png", "act_test_packages", invoke_fun_operate);
        QAction* act_4 = CreateAction(tr("元件测试+位置"), ":/image/shot_1.png", "act_test_devices_location", invoke_fun_operate);
        QAction* act_5 = CreateAction(tr("料号检测+位置"), ":/image/shot_2.png", "act_test_detects_location", invoke_fun_operate);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(94);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        btnGroup1->addAction(act_3);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_4);
        btnGroup2->addAction(act_5);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }

    void CustomTitleView::CreateControlPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("控制"));
        QAction* act_2 = CreateAction(tr("当前位置拍照并保存图片"), ":/image/photograph.png", "act_take_picture_and_save", invoke_fun_logic);
        QAction* act_3 = CreateAction(tr("轴控制"), ":/image/motion.png", jrsaoi::SHORTCUT_ACT_MOTION_CONTROL_EVENT_NAME, invoke_fun_motion);
        QAction* act_4 = CreateAction(tr("显示索引图"), ":/image/index.png", "act_show_index_picture", invoke_fun_render);
        QAction* take_photo = CreateAction(tr("实时拍照"), ":/image/realtimeing.png", jrsaoi::START_CONTINUE_GRAB_EVENT_NAME);
        connect(take_photo, &QAction::triggered, [=]()
            {
                jrsdata::OperateViewParamPtr param = std::make_shared<jrsdata::OperateViewParam>();
                param->module_name = VIEW_MODULE_NAME;
                param->sub_name = SHORTCUT_LOGIC_SUB_NAME;
                param->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
                param->invoke_module_name = LOGIC_MODULE_NAME;
                auto name = take_photo->objectName();
                if (name == jrsaoi::START_CONTINUE_GRAB_EVENT_NAME)
                {
                    take_photo->setObjectName(jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME);
                    take_photo->setText(tr("停止实时"));
                    take_photo->setIcon(QIcon(":/image/realtime.png"));
                    param->event_name = START_CONTINUE_GRAB_EVENT_NAME;
                    Log_INFO(__FUNCTION__, " 实时拍照");
                    emit SigActionTrigger(param);
                }
                else if (name == jrsaoi::STOP_CONTINUE_GRAB_EVENT_NAME)
                {
                    take_photo->setObjectName(jrsaoi::START_CONTINUE_GRAB_EVENT_NAME);
                    take_photo->setText(tr("实时拍照"));
                    take_photo->setIcon(QIcon(":/image/realtimeing.png"));
                    param->event_name = STOP_CONTINUE_GRAB_EVENT_NAME;
                    Log_INFO(__FUNCTION__, " 停止实时");
                    emit SigActionTrigger(param);
                }
            });
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(take_photo);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(64);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        btnGroup2->addAction(act_4);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::Create3DPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("3D"));
        QAction* act_1 = CreateAction(tr("选中元件3D图"), ":/image/3D_1.png", "act_selected_device_3d", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("框选区域3D图"), ":/image/3D_2.png", "act_select_area_3d", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("整板大图3D图"), ":/image/3D_3.png", "act_whole_board_3d", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(34);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDeviceShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("元件显示"));
        QAction* act_1 = CreateAction(tr("本体框"), ":/image/device_1.png", "act_body_device_show", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("显示焊盘"), ":/image/device_2.png", "act_pad_show", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("显示检测框"), ":/image/device_3.png", "act_detect_show", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(34);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDetectTestShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("检测框测试显示"));
        QAction* act_1 = CreateAction(tr("显示轮廓"), ":/image/detect_1.png", "act_display_rectangle", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("显示填充"), ":/image/detect_2.png", "act_display_padding", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("显示数值"), ":/image/detect_3.png", "act_display_number_value", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(34);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateDetectResultSignalPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("检测结果标识"));
        QAction* act_1 = CreateAction(tr("轮廓框选元件"), ":/image/detect_rect.png", "act_rectangle_select_device", invoke_fun_render);
        QAction* act_2 = CreateAction(tr("箭头指示元件"), ":/image/detect_right_short.png", "act_arrow_indicates_device", invoke_fun_render);
        QAction* act_3 = CreateAction(tr("标签指示元件"), ":/image/detect_right.png", "act_label_indicates_device", invoke_fun_render);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(34);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateMotionControlPanel(SARibbonCategory* page, int index)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("输送控制"));
        QString input_name = QString("进板 %1").arg(index + 1);
        QString output_name = QString("出板 %1").arg(index + 1);
        QString reset_name = QString("复位轨道 %1").arg(index + 1);
        QString feedback_name = QString("产品回到进板感应器 %1").arg(index + 1);
        QAction* act_1 = CreateAction(input_name, ":/image/inputboard.png", QString("act_input_board_%1").arg(index), invoke_fun_axis_move);
        QAction* act_2 = CreateAction(output_name, ":/image/return.png", QString("act_output_board_%1").arg(index), invoke_fun_axis_move);
        QAction* act_3 = CreateAction(reset_name, ":/image/refresh_3.png", QString("act_reset_track_%1").arg(index), invoke_fun_axis_move);
        QAction* act_4 = CreateAction(feedback_name, ":/image/return.png", QString("act_product_return_infeed_sensor_%1").arg(index), invoke_fun_axis_move);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(64);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_1);
        btnGroup1->addAction(act_2);
        SARibbonButtonGroupWidget* btnGroup2 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup2->setItemMargin(0);
        btnGroup2->setMinimumHeight(30);
        btnGroup2->setMinimumWidth(34);
        btnGroup2->setItemHeight(28);
        btnGroup2->addAction(act_3);
        btnGroup2->addAction(act_4);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
        ribbon_pannel->addWidget(btnGroup2, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateModelPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("模板编辑"));
        QAction* act_model_edit = CreateAction(tr("模板编辑"), ":/image/model.png", jrsaoi::SHORTCUT_ACT_MODEL_EDIT_EVENT_NAME, invoke_fun_operate);
        SARibbonButtonGroupWidget* btnGroup1 = new SARibbonButtonGroupWidget(ribbon_pannel);
        btnGroup1->setItemMargin(0);
        btnGroup1->setMinimumHeight(30);
        btnGroup1->setMinimumWidth(34);
        btnGroup1->setItemHeight(28);
        btnGroup1->addAction(act_model_edit);
        ribbon_pannel->addWidget(btnGroup1, SARibbonPannelItem::Medium);
    }
    void CustomTitleView::CreateLayerShowPanel(SARibbonCategory* page)
    {
        SARibbonPannel* ribbon_pannel = page->addPannel(("图层显示"));
        //!图像颜色选择：目前有原图，真彩图，低角度图，高度图共4种
        image_color_select = new SARibbonComboBox(this);
        image_color_select->setObjectName("image_color_select");
        image_color_select->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
        image_color_select->addItem("     原图   ", QVariant::fromValue((int)jrsdata::LightImageType::RGB));
        image_color_select->addItem("    真彩图      ", QVariant::fromValue((int)jrsdata::LightImageType::WHITE));
        image_color_select->addItem("   低角度光图  ", QVariant::fromValue((int)jrsdata::LightImageType::LOWWHITE));
        image_color_select->addItem("    高度图  ", QVariant::fromValue((int)jrsdata::LightImageType::HEIGHT));

        image_color_select->setFixedHeight(30);
        connect(image_color_select, QOverload<const QString&>::of(&SARibbonComboBox::textActivated), this, &CustomTitleView::SlotComboboxImageTypeChange);
        ribbon_pannel->addSmallWidget(image_color_select);
    }
}
