﻿
// Custom
#include "render2dmodel.h"
#include "graphicsobject.h" // graphicsabstract
#include "padgraphics.h" // PadGraphics
#include "padgroup.h" // PadGraphicsGroup
#include "viewparam.hpp" // MultiBoardEventParam
#include "render2deventparam.hpp" // UpdateOperatorParam
#include "layerconverter.hpp"
#pragma warning(push,1)
#pragma warning(disable : 4996 4127)
#include "coordinatetransformationtool.h"
#include "coordinatetransform.hpp" // CoordinateTransform
#include "cvtools.h"
#pragma warning(pop)


std::string JoinString(const std::vector<std::string>& names)
{
	std::ostringstream result;
	for (size_t i = 0; i < names.size(); ++i)
	{
		result << names[i];
		if (i != names.size() - 1)
		{
			result << ";"; // 添加分隔符
		}
	}
	return result.str();
}

std::vector<std::string> ParseString(const std::string& input)
{
	std::vector<std::string> result;
	std::istringstream stream(input);
	std::string segment;

	// 按分隔符 ';' 分割字符串
	while (std::getline(stream, segment, ';'))
	{
		result.push_back(segment);
	}

	return result;
}

using namespace jrsdata;

namespace jrsaoi
{
	Render2dModel::Render2dModel(const std::string& name)
		: ModelBase(name)
		, _is_delete(true)
		//, _is_clockwise_rotate(true)
		// , render_view_param(std::make_shared<jrsdata::RenderViewParam>())
	{
	}

	Render2dModel::~Render2dModel()
	{
	}

	int Render2dModel::Update(const jrsdata::ViewParamBasePtr& param_)
	{
		if (!param_)
		{
			RETURN_ERROR(jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER, "Render model更新数据时，传入的数据指针为空");
		}
		if (param_->event_name == PROJECT_READ_EVENT_NAME)
		{
			auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
			if (!param)
			{
				RETURN_ERROR(jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER, "Render model更新数据时，传入的数据指针为空");
			}
			ProjectUpdateGraphics();
		}
		else if (param_->event_name == IMPORT_CAD_EVENT_NAME)
		{
			auto param = std::dynamic_pointer_cast<jrsdata::ProjectEventParam>(param_);
			if (!param)
			{
				RETURN_ERROR(jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER, "Render model更新数据时，传入的数据指针为空");
			}
			int width, height;
			project_param_instance.GetProjectDataProcessInstance()->ReadBoardSizePixel(width, height);
			if (width < 0 || height < 0)//临时 xixi
			{
				RETURN_ERROR(jrscore::ViewError::E_AOI_VIEW_BOARD_SIZE_INVAILD, "未设置板子物理尺寸");
			}
			auto& boards = project_param_instance.GetProjectDataProcessInstance()->GetProjectParam()->board_info.sub_board;
			for (auto& board : boards)
			{
				//TODO 修改位置
				project_param_instance.GetProjectDataProcessInstance()->UpdateSubBoardSize(board);
			}
			ProjectUpdateGraphics(width, height, true);
		}
		else if (param_->event_name == "show_scan_image")
		{
			//! 扫图结束后将
			auto param = std::dynamic_pointer_cast<jrsdata::RenderViewParam>(param_);
			if (!param)
			{
				Log_ERROR("保存工程图片时，参数指针转换失败，请检查！");
				return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER;
			}
			for (auto& [key, val] : param->image_buffer.boarder_imgs.imgs)
			{
				project_param_instance.GetProjectDataProcessInstance()->UpdateImage(static_cast<int>(key), val);
			}
		}
		else if (param_->event_name == jrsaoi::CLEAR_ENTIRETY_BOARD_IMAGE_EVENT_NAME)
		{
			project_param_instance.GetProjectDataProcessInstance()->ClearEntiretyBoardImages();
		}
		else if (param_->event_name == jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
		{
			_is_debugging = false;
		}
		else if (param_->event_name == jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME)
		{
			_is_debugging = true;
		}
		else if (param_->event_name == jrsaoi::ONLINEDEBUG_CHANGE_DEBUG_COMPONENT_EVENT_NAME)
		{
			auto online_debug_param = std::static_pointer_cast<jrsdata::OnlineDebugViewParam>(param_);
			_current_debug_info = online_debug_param->current_debug_info;
			ProjectUpdateGraphics();
		}
		else if (param_->event_name == jrsaoi::SHORTCUT_ACT_CAD_CONVERSION)
		{
			//_is_clockwise_rotate = !_is_clockwise_rotate;
		}
		return jrscore::AOI_OK;
	}

	int Render2dModel::Save(const jrsdata::ViewParamBasePtr& param_)
	{
		(void)param_;
		return jrscore::AOI_OK;
	}

	jrsaoi::ParamOperator& Render2dModel::GetProjectParamInstance()
	{
		return project_param_instance;
	}

	const jrsdata::ProjectParamPtr& Render2dModel::GetProjectParam()
	{
		return project_param_instance.GetProjectDataProcessInstance()->GetProjectParam();
	}

	std::optional<const std::unordered_map<int, cv::Mat>*> Render2dModel::GetProjectImage()
	{
		return project_param_instance.GetProjectDataProcessInstance()->ReadImage();
	}

	std::optional<cv::Mat> Render2dModel::GetProjectImageWithIndex(int image_index)
	{
		return project_param_instance.GetProjectDataProcessInstance()->ReadImage(image_index);
	}

	std::vector<jrsdata::SubBoard> Render2dModel::GetSubBoards()
	{
		return project_param_instance.GetProjectDataProcessInstance()->GetProjectParam()->board_info.sub_board;
	}

	std::optional<jrsdata::SubBoard> Render2dModel::GetSubBoard(const std::string& subboardname)
	{
		return project_param_instance.GetProjectDataProcessInstance()->ReadSubBoard(subboardname);
	}

	std::string Render2dModel::GetDefaultSubboardName()
	{
		auto board = project_param_instance.GetProjectDataProcessInstance()->ReadBoard();
		if (!board.has_value())
		{
			return "";
		}
		auto& subboards = board->get().sub_board;
		if (subboards.empty())
		{
			return "";
		}
		return subboards.front().subboard_name;
	}

	//std::optional<jrsdata::Component> Render2dModel::GetComponent(const std::string& subBoardName, const std::string& componentName)
	//{
	//    return project_param_instance.GetProjectDataProcessInstance()->ReadComponent(subBoardName, componentName);
	//}

	int Render2dModel::GetBoardSize(int& width_, int& height_)
	{
		return project_param_instance.GetProjectDataProcessInstance()->ReadBoardSizePixel(width_, height_);
	}

	void Render2dModel::ProjectUpdateGraphics(int canvas_width, int canvas_height, bool update_subboard_position)
	{
		//先清除数据，再进行更新
		_callback_clear_graphics("pad_group", false);

		auto& project_param = project_param_instance.GetProjectDataProcessInstance()->GetProjectParam();
		if (!project_param)
			return;
		if (project_param->board_info.sub_board.empty())
			return;

		auto& board = project_param->board_info;

		// 矫正子板位置,避免子板出现在有效范围外
		if (update_subboard_position)
		{
			double xoffset = 0, yoffset = 0;
			long long count = 0;
			for (auto& sub : board.sub_board)
			{
				for (auto& value_compent : sub.component_info)
				{
					xoffset += value_compent.x;
					yoffset += value_compent.y;
					count++;
				}
			}
			if (count > 0)
			{
				xoffset /= count;
				yoffset /= count;

				xoffset = canvas_width * 0.5 - xoffset;
				yoffset = canvas_height * 0.5 - yoffset;
				// 生成平移矩阵
				cv::Mat transform = cv::Mat::eye(2, 3, CV_32F);
				transform.at<float>(0, 2) = xoffset;
				transform.at<float>(1, 2) = yoffset;

				AlignmentBoard(board, transform);
			}
		}

		// 工程生成图形
		std::vector<std::shared_ptr<GraphicsAbstract>> ghs;
		{
			CADCreateGraphics(ghs, board.marks);
			CADCreateGraphics(ghs, board.barcodes);

			CreateSubboardGraphics(ghs, board.sub_board, false);
		}

		TriggerCallBackProjectUpdateGraphics(ghs);
	}

	GraphicsPtrVec Render2dModel::GraphicsUpdateProject(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs, const UpdateOperatorParam& param_operator_)
	{
		UpdateOperatorParam param_operator = param_operator_;
		GraphicsPtrVec updated_ghs;
		for (auto& gh : ghs)
		{
			if (param_operator.type == UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR
				|| param_operator.type == UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR)
			{
				if (!SelectGraphicsUpdateOperator(param_operator, gh))
				{
					continue;
				}
			}
			auto& layer = gh->settings.GetLayer();
			switch (LayerConverter::FromString(layer))
			{
			case Layer::subboard:
				GraphicsUpdateSubBoard(gh, param_operator);
				break;
			case Layer::component:
			case Layer::mark:
			case Layer::barcode:
			case Layer::submark:
			case Layer::badmark:
			case Layer::subbarcode:
				updated_ghs = GraphicsUpdateComponent(gh, param_operator);
				break;
			case Layer::pad:
				GraphicsUpdateUnit(gh, param_operator);
				// GraphicsUpdatePAD(gh, param_operator);
				break;
			case Layer::region:
				GraphicsUpdateDetectWindow(gh, param_operator);
				break;
			case Layer::subregion:
				GraphicsUpdateSubDetectWindow(gh, param_operator);
				break;
				// GraphicsUpdateMark(gh, param_operator);
				// break;
				// GraphicsUpdateBarcode(gh, param_operator);
				// break;
				// GraphicsUpdateSubMark(gh, param_operator);
				// break;

			}
			//if (param_operator.type == UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR)
			//{
			//    SelectGraphicsUpdateOperator(param_operator, gh);//创建的时候更新一下字段，保持一致
			//}
		}
		return updated_ghs;
	}

	bool Render2dModel::SelectGraphicsUpdateOperator(UpdateOperatorParam& param_operator, const std::shared_ptr<GraphicsAbstract>& gh)
	{
		auto& layer = gh->settings.GetLayer();
		if (layer.empty())
		{
			return false;
		}
		else if (layer != LayerConverter::ToString(Layer::temp_mark))
		{
			param_operator.Clear();
		}
		//param_operator.type = UpdateOperatorParam::UpdateOperatorType::SELECT_OPERATOR;
		param_operator.current_id = gh->GetId().GetString();
		param_operator.current_layer = gh->settings.GetLayer();

		auto& select_param = param_operator.select_param;
		auto& select_object = param_operator.select_object;

		/*分成两块,用于减少重复代码*/
		switch (LayerConverter::FromString(layer))
		{
		case Layer::component:
		{
			param_operator.component_type = jrsdata::Component::Type::CAD;
		}
		break;
		case Layer::mark:
		{
			param_operator.component_type = jrsdata::Component::Type::MARK;
		}
		break;
		case Layer::barcode:
		{
			param_operator.component_type = jrsdata::Component::Type::BARCODE;
		}
		break;
		case Layer::submark:
		{
			param_operator.component_type = jrsdata::Component::Type::SUB_MARK;
		}
		break;
		case Layer::subbarcode:
		{
			param_operator.component_type = jrsdata::Component::Type::SUB_BARCODE;
		}
		break;
		case Layer::badmark:
		{
			param_operator.component_type = jrsdata::Component::Type::SUB_BADMARK;
		}
		break;
		case Layer::pad:
		{
			param_operator.unit_type = jrsdata::ComponentUnit::Type::PAD;
		}
		break;
		}

		// 更新上层,重置下层,重置省略了
		switch (LayerConverter::FromString(layer))
		{
		case Layer::subboard:
		{
			select_object.subboard_graphics = gh;
			select_param.subboard_name = gh->GetId().GetString();
		}
		break;
		case Layer::region:
		{
			auto names = ParseString(gh->GetId().GetString());
			if (names.size() != 5)
			{
				return false;
			}
			select_object.window_graphics = gh;

			select_param.subboard_name = names[0];
			select_param.component_name = names[1];
			select_param.unit_name = names[2];
			param_operator.model_name = names[3];
			select_param.window_name = names[4];
			param_operator.window_name = select_param.window_name;

			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!component.has_value())
			{
				return false;
			}
			param_operator.part_name = component->get().component_part_number;

			if (select_param.unit_name.find("body") != std::string::npos)
			{
				param_operator.unit_type = jrsdata::ComponentUnit::Type::BODY;
			}
			else if (select_param.unit_name.find("pad") != std::string::npos)
			{
				param_operator.unit_type = jrsdata::ComponentUnit::Type::PAD;
			}
		}
		break;
		case Layer::subregion:
		{
			auto names = ParseString(gh->GetId().GetString());
			if (names.size() != 7)
			{
				return false;
			}
			select_object.subwindow_graphics = gh;

			select_param.subboard_name = names[0];
			select_param.component_name = names[1];
			select_param.unit_name = names[2];
			param_operator.model_name = names[3];
			select_param.window_name = names[4];
			param_operator.algo_name = names[5];
			select_param.subwindow_name = names[6];

			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!component.has_value())
			{
				return false;
			}
			param_operator.part_name = component->get().component_part_number;

			if (select_param.unit_name.find("body") != std::string::npos)
			{
				param_operator.unit_type = jrsdata::ComponentUnit::Type::BODY;
			}
			else if (select_param.unit_name.find("pad") != std::string::npos)
			{
				param_operator.unit_type = jrsdata::ComponentUnit::Type::PAD;
			}


		}
		break;
		case Layer::pad:
		{
			auto names = ParseString(gh->GetId().GetString());
			if (names.size() != 3)
			{
				return false;
			}
			select_object.unit_graphics = gh;

			select_param.subboard_name = names[0];
			select_param.component_name = names[1];
			select_param.unit_name = names[2];
			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadCADRef(select_param.subboard_name, select_param.component_name);
			if (!component.has_value())
			{
				return false;
			}

			/**< 元件类型 */
			param_operator.component_type = component->get().component_type;
			param_operator.part_name = component->get().component_part_number;
			auto unit = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnitRef(component->get().component_part_number, select_param.unit_name);
			if (!unit.has_value())
			{
				return false;
			}
			param_operator.unit_group_name = unit->get().unit_group_name;
			param_operator.model_name = unit->get().unit_group_name;
		}
		break;
		case Layer::component:
		case Layer::mark:
		case Layer::barcode:
		case Layer::submark:
		case Layer::subbarcode:
		case Layer::badmark:
		{
			auto names = ParseString(gh->GetId().GetString());
			if (names.size() != 2)
			{
				return false;
			}
			select_object.component_graphics = gh;

			select_param.subboard_name = names[0];
			select_param.component_name = names[1];

			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!component.has_value())
			{
				return false;
			}
			select_param.unit_name = "body"; // 选中元件时也选中本体
			param_operator.part_name = component->get().component_part_number;
			param_operator.model_name = "body";
			param_operator.unit_type = jrsdata::ComponentUnit::Type::BODY;
		}
		break;
		}
		return true;
	}

	std::string Render2dModel::GetGraphicsActualName(const std::shared_ptr<GraphicsAbstract>& gh)
	{
		auto& layer = gh->settings.GetLayer();
		return GetGraphicsActualName(layer, gh->GetId().GetString());
	}

	std::string Render2dModel::GetGraphicsActualName(const std::string& layer, const std::string& id)
	{
		switch (LayerConverter::FromString(layer))
		{
		case Layer::subboard:
		{
			return id;
		}
		break;
		case Layer::region:
		{
			auto names = ParseString(id);
			if (names.size() != 5)
			{
				return "";
			}
			//select_param.subboard_name = names[0];
			//select_param.component_name = names[1];
			//select_param.unit_name = names[2];
			//param_operator.model_name = names[3];
			return names[4];
		}
		break;
		case Layer::subregion:
		{
			auto names = ParseString(id);
			if (names.size() != 6)
			{
				return "";
			}
			//select_param.subboard_name = names[0];
			//select_param.component_name = names[1];
			//select_param.unit_name = names[2];
			//param_operator.model_name = names[3];
			//select_param.window_name = names[4];
			return names[5];
		}
		break;
		case Layer::pad:
		{
			auto names = ParseString(id);
			if (names.size() != 3)
			{
				return "";
			}
			//select_param.subboard_name = names[0];
			//select_param.component_name = names[1];
			return names[2];
		}
		break;
		case Layer::component:
		case Layer::mark:
		case Layer::barcode:
		case Layer::submark:
		case Layer::subbarcode:
		case Layer::badmark:
		{
			auto names = ParseString(id);
			if (names.size() != 2)
			{
				return "";
			}
			//select_param.subboard_name = names[0];
			//select_param.component_name = names[1];
			return names[1];
		}
		break;
		}
		return "";
	}


	std::vector<std::string> Render2dModel::GetComponnetUnitNamesByOperateParam(const UpdateOperatorParam& param_)
	{
		auto group_units = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnitsBySameGroupName(param_.select_param.component_name, param_.select_param.subboard_name, param_.select_param.unit_name, param_.component_type);
		if (!group_units.has_value())
		{
			Log_ERROR("获取相同组的Units失败");
			return {};
		}
		std::vector<std::string> unit_names;
		for (auto& unit : group_units.value())
		{
			unit_names.push_back(unit.unit_name);
		}
		return unit_names;
	}

	std::string Render2dModel::GetNameInRender(const std::string& subboard_name, const std::string& component_name)
	{
		return JoinString({ subboard_name,component_name });
	}

	std::string Render2dModel::GetNameInRender(const std::string& subboard_name, const std::string& component_name, const std::string& unit_name)
	{
		return JoinString({ subboard_name,component_name,unit_name });
	}

	std::string Render2dModel::GetNameInRender(const std::string& subboard_name, const std::string& component_name, const std::string& unit_name, const std::string& model_name, const std::string& window_name)
	{
		return JoinString({ subboard_name,component_name,unit_name,model_name,window_name });
	}

	std::string Render2dModel::GetNameInRender(const std::string& subboard_name, const std::string& component_name, const std::string& unit_name, const std::string& model_name, const std::string& window_name, const std::string& subwindow_name)
	{
		return JoinString({ subboard_name,component_name,unit_name,model_name,window_name,subwindow_name });
	}

	void Render2dModel::UpdateImageToProject(int type_, const cv::Mat& img_)
	{
		project_param_instance.GetProjectDataProcessInstance()->UpdateImage(type_, img_);
	}

	// void Render2dModel::UpdateImageToProject(const std::unordered_map<int, std::string>& img_path_hash)
	// {
	//     project_param_instance.GetProjectDataProcessInstance()->UpdateImage(type_, img_);
	// }


	void Render2dModel::EntiretyBoardTransform(const cv::Mat& affine_transform_matrix_)
	{
		if (project_param_instance.GetProjectDataProcessInstance()->IsParamPtrEmpty())
			return;
		auto board = project_param_instance.GetProjectDataProcessInstance()->ReadBoard();
		AlignmentBoard(board->get(), affine_transform_matrix_);
	}

	void Render2dModel::AlignmentBoard(jrsdata::Board& board, const cv::Mat& matrix)
	{
		project_param_instance.GetProjectDataProcessInstance()->AlignmentBoard(board, matrix);
	}

	void Render2dModel::AlignmentSubboard(jrsdata::SubBoard& subboard, const cv::Mat& matrix)
	{
		project_param_instance.GetProjectDataProcessInstance()->AlignmentSubBoard(subboard, matrix);
	}

	void Render2dModel::AlignmentSubboard(const std::string& subboardname, const cv::Mat& matrix)
	{
		project_param_instance.GetProjectDataProcessInstance()->AlignmentSubBoard(subboardname, matrix);
	}

	void Render2dModel::AlignmentComponent(jrsdata::Component& component, const cv::Mat& matrix)
	{
		project_param_instance.GetProjectDataProcessInstance()->AlignmentComponent(component, matrix);
	}

	void Render2dModel::AlignmentComponents(std::vector<jrsdata::Component*>& components, const cv::Mat& matrix)
	{
		project_param_instance.GetProjectDataProcessInstance()->AlignmentComponents(components, matrix);
	}

	cv::RotatedRect Render2dModel::GetCurrentPositionFromGraphics(const std::shared_ptr<GraphicsAbstract>& gh)
	{
		if (!gh)
		{
			return  cv::RotatedRect();
		}
		return gh->GetBoundingbox();
	}

	void Render2dModel::GetBoardShow3DImage(cv::Mat& height_image, std::vector<cv::Mat>& texture_images)
	{
		auto board_imgs = project_param_instance.GetProjectDataProcessInstance()->ReadImage();
		if (!board_imgs.has_value())
		{
			return;
		}

		for (auto& img : *board_imgs.value())
		{
			if (img.first == (int)jrsdata::LightImageType::HEIGHT)
			{
				height_image = img.second;
			}
			else
			{
				texture_images.emplace_back(img.second);
			}
		}
	}

	void Render2dModel::GetSelectShow3DImages(const cv::Rect& crop_rect, cv::Mat& height_image, std::vector<cv::Mat>& texture_images)
	{
		for (int i = 0; i < static_cast<int>(jrsdata::LightImageType::COUNT) - 1; i++)
		{
			cv::Mat crop_image;
			auto light_type = static_cast<jrsdata::LightImageType>(i);
			const auto& run_mode_info = GetExecuteModeInfo();

			// 根据截图源左上角坐标调整截个区域
			cv::Rect crop_rect_copy = crop_rect;
			crop_rect_copy.x -= run_mode_info.fov_left_top_x;
			crop_rect_copy.y -= run_mode_info.fov_left_top_y;


			project_param_instance.GetProjectDataProcessInstance()->RotateCropImageFromProject(light_type, crop_rect_copy, crop_image, cv::Point2f(-1.0, -1.0), 0, nullptr, run_mode_info);
			if (light_type == jrsdata::LightImageType::HEIGHT)
			{
				height_image = crop_image;
			}
			else
			{
				texture_images.emplace_back(crop_image);
			}
		}
	}

	void Render2dModel::GetComponentDetectRegionSmallestRect(const std::string& sub_board_name, const std::string& comp_name, cv::Rect& rect)
	{
		auto component = project_param_instance.GetProjectDataProcessInstance()->ReadCADRef(sub_board_name, comp_name);
		if (component.has_value())
		{
			std::map<std::string, jrsparam::AlgoExcuteRectsParam> algo_excute_rects_params;
			cv::RotatedRect crop_image_rect;
			if (project_param_instance.GetProjectDataProcessInstance()->GetSignelComponentExcuteRectsParam(component->get(), algo_excute_rects_params, crop_image_rect) != 0)
			{
				return;
			}
			jcvtools::JrsHomMat2D mat_2d;
			mat_2d.AddHomMat2dRotate(component->get().angle, component->get().x, component->get().y);
			rect = mat_2d.AffineTransRotatedRect(crop_image_rect).boundingRect();
		}
	}

	GraphicsPtr Render2dModel::CreateSearchWindow(const std::string& subboard_name_, const std::string& component_name_,
		const std::string& unit_name_, const std::string& window_name_, GraphicsPtr detect_window_graphics_ptr_, float search_size_)
	{
		auto search_region_name = JoinString({ subboard_name_, component_name_,
					  unit_name_, window_name_, "search_region" });
		auto search_region_gh = std::make_shared<RectGraphics>();/**<搜索范围*/
		search_region_gh->settings.SetLayer(LayerConverter::ToString(Layer::search_region));
		search_region_gh->SetId(search_region_name);
		search_region_gh->SetParent(detect_window_graphics_ptr_);
		auto search_region_size = project_param_instance.GetProjectDataProcessInstance()->GetSearchRegionSize({ detect_window_graphics_ptr_->w(), detect_window_graphics_ptr_->h() },
			search_size_);
		search_region_gh->SetValue(0.0f, 0.0f,
			search_region_size.width, search_region_size.height, 0.f);
		return search_region_gh;
	}

	std::optional<jrsdata::DetectWindow> Render2dModel::GetDetctWindowByCurrentSelect(const UpdateOperatorParam& update_operator_param_)
	{
		auto project_process = project_param_instance.GetProjectDataProcessInstance();
		auto detect_window = project_process->ReadDetectWindow(update_operator_param_.model_name, update_operator_param_.part_name, update_operator_param_.select_param.window_name);
		return detect_window;
	}

	std::string Render2dModel::GetUnitNameByModelName(const std::string& part_number_name, const std::string& model_name_)
	{
		if (model_name_.empty())
		{
			Log_ERROR("model名为空，请检查");
			return "";
		}
		auto project_process = project_param_instance.GetProjectDataProcessInstance();
		auto partnumber_struct = project_process->ReadPNDetectInfoRef(part_number_name);
		if (!partnumber_struct.has_value())
		{
			Log_ERROR("未查询到该料号请检查");
			return "";
		}
		for (const auto& unit : partnumber_struct->get().units)
		{
			if (unit.unit_group_name == model_name_)
			{
				return unit.unit_name;
			}
		}
		return std::string();
	}

	bool Render2dModel::IsDelete()
	{
		bool temp = _is_delete;/**< 返回当前状态*/
		_is_delete = true;  /**< 状态恢复*/
		return temp;
	}

	void Render2dModel::DealCADShortcutOperate(const jrsdata::RenderEventParamPtr& param, const RenderSelectParam& select_param_)
	{
		if (!param || !param->multi_param_ptr)
		{
			return;
		}
		const auto& operate_param = param->multi_param_ptr->operate_param;
		using OperateParam = jrsdata::MultiBoardEventParam::OperateParam;
		if (operate_param->operate_object == OperateParam::OperateObject::ENTIRETY_BOARD)
		{
			//整版处理逻辑
			this->EntiretyBoardTransform(operate_param.value());

		}
		else if (operate_param->operate_object == OperateParam::OperateObject::SUBBOARD)
		{
			/**< 子板处理逻辑*/
			this->SubBoardTransform(select_param_.subboard_name, operate_param.value());
		}
		else
		{
			Log_ERROR("事件复制失败，请检查");
		}
	}

	std::vector<cv::Point2f> Render2dModel::MarkImageToContours(const cv::Mat img_, const cv::Point2f& origin_point_)
	{
		auto contours = jcvtools::JrsCVTools::ConvertMarkToContours(img_);
		std::vector<cv::Point2f> object_contour;
		for (const auto& contour : contours)
		{
			for (const auto& point : contour)
			{
				object_contour.push_back({ point.x + origin_point_.x, origin_point_.y + point.y });
			}
		}
		return object_contour;
	}

	void Render2dModel::UpdateTemporalGraphicsBySubboards(const std::vector<jrsdata::SubBoard>& subboards_)
	{
		std::vector<GraphicsPtr> graphics_vec;
		CreateSubboardGraphics(graphics_vec, subboards_, true);
		TriggerCallBackProjectUpdateGraphics(graphics_vec);

	}

	void Render2dModel::CADCreateGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const std::vector<jrsdata::Component>& components)
	{
		for (const auto& c : components)
		{

			if (_is_debugging && _current_debug_info.current_component_name != c.component_name)
			{
				continue;
			}
			auto part = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(c.component_part_number);
			if (!part.has_value())
				continue;

			const auto& units = part->get().units;
			auto it = std::find_if(units.begin(), units.end(), [](const jrsdata::ComponentUnit& unit) { return unit.unit_type == jrsdata::ComponentUnit::Type::BODY; });
			if (it == units.end())
				continue;

			const auto& body_unit = *it;
			std::shared_ptr<GraphicsAbstract> body_gh;

			if (body_unit.unit_shape == jrsdata::ComponentUnit::Shape::RECT)
			{
				body_gh = std::make_shared<RectGraphics>(c.x, c.y, body_unit.width, body_unit.height, c.angle);
			}
			// 设置id
			{
				auto gh_name = JoinString({ c.subboard_name, c.component_name });
				body_gh->SetId(gh_name);

				switch (c.component_type)
				{
				case jrsdata::Component::Type::CAD:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::component));
					break;
				case jrsdata::Component::Type::MARK:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::mark));
					break;
				case jrsdata::Component::Type::BARCODE:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::barcode));
					break;
				case jrsdata::Component::Type::SUB_MARK:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::submark));
					break;
				case jrsdata::Component::Type::SUB_BARCODE:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::subbarcode));
					break;
				case jrsdata::Component::Type::SUB_BADMARK:
					body_gh->settings.SetLayer(LayerConverter::ToString(Layer::badmark));
					break;
				}

				body_gh->settings.SetIsFlag(true);
				body_gh->settings.SetIsCopy(true); /**< 元件允许Copy*/
				container.push_back(body_gh);
				PNCreateGraphics(container, part->get(), c, body_gh);
			}
		}
	}

	void Render2dModel::PNCreateGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container,
		const jrsdata::PNDetectInfo& pn, const jrsdata::Component& component, const std::shared_ptr<GraphicsAbstract>& parent)
	{
		auto project_ptr = project_param_instance.GetProjectDataProcessInstance();
		// 如果跟随料号有效,使用跟随料号结构体
		// 否则使用当前料号结构体
		const jrsdata::PNDetectInfo* effective_pn = [&]() -> const jrsdata::PNDetectInfo*
			{
				if (pn.bind_part_name.empty())
				{
					return &pn;
				}
				auto bind_pn = project_ptr->ReadPNDetectInfoRef(pn.bind_part_name);
				if (!bind_pn.has_value())
				{
					return &pn;
				}
				return &(bind_pn->get());
			}();

		if (!effective_pn)
		{
			return;
		}

		//// 储存pad对齐工具
		std::unordered_map<std::string, std::vector<std::shared_ptr<PadGraphicsGroup>>> pad_group_map;
		UpdateOperatorParam current_param;
		if (callback_current_select_param)
		{
			callback_current_select_param(current_param);
		}

		for (const auto& unit : effective_pn->units)
		{
			auto& group_name = unit.unit_group_name;
			auto it = effective_pn->detect_models.find(group_name);
			if (it == effective_pn->detect_models.end())
			{
				continue;
			}

			// 生成组件
			std::shared_ptr<GraphicsAbstract> unit_gh;
			if (unit.unit_type == jrsdata::ComponentUnit::Type::BODY)
			{
				unit_gh = parent;
			}
			else if (unit.unit_type == jrsdata::ComponentUnit::Type::PAD)
			{
				/**< 多个子板组名是相同的，导致对称的也放在同一组内。导致问题，需要结合复制及其需求综合处理。By:HJC 2025/3/24 */
				std::shared_ptr<PadGraphicsGroup> pad_group;
				auto pad_it = pad_group_map.find(group_name);
				if (pad_it != pad_group_map.end())
				{
					if (unit.pad_type == jrsdata::ComponentUnit::PadType::MIRROR)
					{
						for (auto& group : pad_it->second)
						{
							if (group->_sub_graphics.empty())
							{
								pad_group = group;
								break;
							}
						}
					}
					else
					{
						pad_group = pad_it->second.back();
					}
				}
				else
				{
					std::shared_ptr<GraphicsAbstract> temp_gh;
					TriggerCallBackCreateGraphics(temp_gh, static_cast<int>(GraphicsFlag::pad_group), LayerConverter::ToString(Layer::pad), group_name, parent);
					pad_group = std::dynamic_pointer_cast<PadGraphicsGroup>(temp_gh);
					pad_group->SetId(parent->GetId().GetString() + "_" + pad_group->GetPadGroupName() + "_" + std::to_string(pad_group->GetPadGroupID()));
					//pad_group->SetParentGraphicsPtr(parent);
					pad_group->SetDirection(static_cast<PadGraphicsGroup::PadDirection>(unit.direction));
					pad_group->SetGroupType(static_cast<PadGraphicsGroup::PadGroupType>(unit.pad_type));
					pad_group->settings.SetIsEditAble(false);
					pad_group_map[group_name].push_back(pad_group);
					container.emplace_back(pad_group);
					/**< 镜像对称Pad 需要特殊处理！TODO: PAD INFO HJC */
					if (unit.pad_type == jrsdata::ComponentUnit::PadType::MIRROR)
					{
						auto pad_group_clone = pad_group->Clone();
						pad_group_map[group_name].emplace_back(std::static_pointer_cast<PadGraphicsGroup>(pad_group_clone));
						container.emplace_back(pad_group_clone);
					}
				}
				unit_gh = std::make_shared<PadGraphics>(unit.id, pad_group);
				unit_gh->settings.SetLayer(LayerConverter::ToString(Layer::pad));
				auto gh_name = JoinString({ component.subboard_name, component.component_name, unit.unit_name });
				unit_gh->SetId(gh_name);
				unit_gh->SetParent(parent);
				//Log_INFO("gh create unit info:", unit.unit_name,
				//    " center point:[", unit.x, ",", unit.y, "]; size:[", unit.width, ",", unit.height, "];");
				unit_gh->SetValue(unit.x, unit.y, unit.width, unit.height, 0.f);
				pad_group->AddSubGraphics(unit_gh);
				container.emplace_back(unit_gh);
			}

			if (!unit_gh)
			{
				continue;
			}
			if (component.component_name == current_param.select_param.component_name /*&& unit.unit_name == current_param.select_param.unit_name*/)//选中显示所有检测框
			{
				const auto& windows = it->second.detect_model;
				for (const auto& window_value : windows)
				{
					auto window = std::make_shared<RectGraphics>();
					window->settings.SetLayer(LayerConverter::ToString(Layer::region));
					auto gh_name = JoinString({ component.subboard_name, component.component_name, unit.unit_name,
					  group_name, window_value.name });
					window->SetId(gh_name);
					window->SetParent(unit_gh);

					if (unit.unit_type == jrsdata::ComponentUnit::Type::PAD)
					{
						cv::Point2f component_center(component.x, component.y);
						cv::Point2f unit_center(unit.x, unit.y);
						cv::Point2f detect_center(window_value.cx, window_value.cy);
						cv::RotatedRect detect_window_rotated(detect_center,
							cv::Size2f(window_value.width, window_value.height), 0);

						auto new_rect = project_ptr->GetRotatedRectByPadDirection(
							component_center,
							unit_center,
							detect_window_rotated,
							jrsdata::ComponentUnit::Direction::UP,
							unit.direction
						);

						window->SetValue(new_rect.center.x, new_rect.center.y,
							new_rect.size.width, new_rect.size.height, 0.f);
					}
					else
					{
						window->SetValue(window_value.cx, window_value.cy,
							window_value.width, window_value.height, 0.f);
					}
					container.emplace_back(window);

					for (const auto& algorithm_value : window_value.algorithms)
					{
						for (const auto& subwindow_value : algorithm_value.algorithm_detect_windows)
						{
							auto subwindow = std::make_shared<RectGraphics>();
							subwindow->settings.SetLayer(LayerConverter::ToString(Layer::subregion));
							auto subwindow_gh_name = JoinString({ component.subboard_name, component.component_name, unit.unit_name,
							  group_name, window_value.name, algorithm_value.detect_algorithm_name, subwindow_value.name });
							subwindow->SetId(subwindow_gh_name);
							subwindow->SetParent(window);
							subwindow->SetValue(subwindow_value.cx, subwindow_value.cy,
								subwindow_value.width, subwindow_value.height, 0.f);
							container.emplace_back(subwindow);
						}
					}
					/**< windows 搜索区域显示  */
					if (window_value.search_size != 0 && current_param.select_param.window_name == window_value.name)
					{
						auto search_region_gh = CreateSearchWindow(component.subboard_name, component.component_name, unit.unit_name, window_value.name, window, window_value.search_size);
						search_region_gh->settings.SetIsEditAble(false);
						container.emplace_back(search_region_gh);
					}
				}
			}
		}
	}

	void Render2dModel::CreateSubboardIdGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& container, const std::vector<jrsdata::SubBoard>& subboards_)
	{
		for (const auto& subboard : subboards_)
		{
			float x = subboard.x - subboard.width / 2;
			float y = subboard.y - subboard.height / 2;
			if (subboard.width <= 0 || subboard.height <= 0)
			{
				Log_ERROR("SubBoard width or height is zero, cannot create ID graphics.");
				continue;
			}
			float width = 50.f;
			float height = 50.f;
			auto text_graphics = std::make_shared<TextGraphics>(x, y, width, height, subboard.angle, std::to_string(subboard.id));
			text_graphics->settings.SetIsSelected(false);
			text_graphics->settings.SetIsPrivateStyle(true);
			text_graphics->settings.SetIsEditAble(false);
			text_graphics->settings.SetIsCopy(false);
			RenderConfig render_config = { 128,128, 128,255,1 };
			text_graphics->settings.SetPrivateStyle(render_config, render_config);
			container.emplace_back(text_graphics);

		}
	}

	GraphicsPtrVec Render2dModel::GraphicsUpdateComponent(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& param_operator)
	{
		// auto id = gh->GetId().GetString();
		// if (id.empty())
		//     return;
		// auto parent = gh->GetParent();
		// if (!parent)
		//     return;
		auto project_data_process = project_param_instance.GetProjectDataProcessInstance();
		auto& select_param = param_operator.select_param;
		switch (param_operator.type)
		{
		case UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR:
		{
			auto component = project_data_process->ReadComponentRef(select_param.component_name,
				select_param.subboard_name, param_operator.component_type);
			if (!component)
				return{};
			project_data_process->UpdateComponentUnitPositionAndSize(component->get().component_part_number,
				select_param.unit_name, { gh->x() ,gh->y() },
				{ gh->w() , gh->h() });
			//auto unit = project_data_process->ReadComponentUnitRef(component->get().component_part_number, select_param.unit_name);
			//if (!unit)
			//	return{};

			std::vector<GraphicsPtr> temp_ghs;
			if (param_operator.unit_type == jrsdata::ComponentUnit::Type::BODY)
			{

				//auto updated_components = project_data_process->UpdateComponentAnglesOfDifferentSubboards(select_param.component_name, select_param.subboard_name, gh->a());
				component->get().x = gh->x();
				component->get().y = gh->y();
				component->get().angle = gh->a();

				/*unit->get().x = 0;
				unit->get().y = 0;*/
				//std::vector<GraphicsID> graphics_ids;
				///** <更新元件信息To 外部 */
				//for (auto& updated_component : updated_components)
				//{
				//    GraphicsID graphics_id;
				//    graphics_id.SetString(GetNameInRender(updated_component.first, updated_component.second));
				//    graphics_ids.push_back(graphics_id);
				//}
				//_callback_get_graphics(temp_ghs, graphics_ids);

			}
			return temp_ghs;
			/*	else
			  {
				unit->get().x = gh->x();
				unit->get().y = gh->y();
			  }

			  unit->get().width = gh->w();
			  unit->get().height = gh->h();*/


		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR:
		{
			Component component;
			component.x = gh->x();
			component.y = gh->y();
			component.component_part_number = param_operator.part_name;
			component.component_type = param_operator.component_type;

			const auto& comp_type = param_operator.component_type;

			// 仅在特定类型时处理 SubBoard 逻辑
			if (!(comp_type == jrsdata::Component::Type::MARK ||
				comp_type == jrsdata::Component::Type::BADMARK ||
				comp_type == jrsdata::Component::Type::BARCODE))
			{
				auto subboard = project_data_process->ReadSubBoardRef(select_param.subboard_name);
				if (!subboard.has_value())
				{
					//TODO: 修改删除添加提示 by baron zhang 2025-07-10
				    JRSMessageBox_WARN("警告",
						"添加元件失败: 未找到子板 [" + select_param.subboard_name + "]！请先选择一个子板！",
						jrscore::MessageButton::Ok);
					return {};
					//TODO END
				}

				cv::Rect component_rect(gh->x() - gh->w() / 2, gh->y() - gh->h() / 2, gh->w(), gh->h());
				cv::Rect subboard_rect(subboard->get().x - subboard->get().width / 2, subboard->get().y - subboard->get().height / 2, subboard->get().width, subboard->get().height);

				// 检查元件是否在子板范围内
				if (comp_type == jrsdata::Component::Type::CAD ||
					comp_type == jrsdata::Component::Type::SUB_BADMARK ||
					comp_type == jrsdata::Component::Type::SUB_BARCODE ||
					comp_type == jrsdata::Component::Type::SUB_MARK)
				{
					//TODO: 修改删除添加提示 by baron zhang 2025-07-10
				/*    if (!subboard_rect.contains(cv::Point(component_rect.x, component_rect.y)) ||
						!subboard_rect.contains(cv::Point(component_rect.x + component_rect.width, component_rect.y + component_rect.height)))
					{
						JRSMessageBox_WARN("警告",
							"添加元件失败: 组件  超出子板 [" + select_param.subboard_name + "] 范围！",
							jrscore::MessageButton::Ok);
						return {};
					}*/
					//TODO END
				}
				component.subboard_name = select_param.subboard_name;
			}

			// CAD 类型组件处理逻辑
			if (comp_type == jrsdata::Component::Type::CAD)
			{
				auto parse_strs = ParseString(gh->GetId().GetString());
				if (parse_strs.size() == 2)
					component.component_name = parse_strs.at(1);  // 复制元件
				else
				{
					component.component_name = parse_strs.at(0);  // 元件名由外界设置
					project_data_process->GenerateComponentName(component);
				}
			}

			// 创建元件
			if (project_data_process->CreateComponent(component) != 0)
			{
				JRSMessageBox_INFO("警告",
					"创建元件失败: [" + component.component_name + "]",
					jrscore::MessageButton::Ok);
				return {};
			}

			/** < 创建 PartNumber */
			{
				auto part = project_data_process->ReadPNDetectInfoRef(param_operator.part_name);
				if (!part.has_value())
				{
					jrsdata::PNDetectInfo pn;
					pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, gh->w(), gh->h(), 1, "body", "body",
						jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
					pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, gh->w(), gh->h(), 2, "body", "location",
						jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
					pn.detect_models["location"] = jrsdata::DetectModel();
					pn.detect_models["body"] = jrsdata::DetectModel();
					project_param_instance.GetProjectDataProcessInstance()->CreatePNDetectInfo(param_operator.part_name, pn);
				}
			}
			auto gh_name = JoinString({ component.subboard_name, component.component_name });
			gh->SetId(gh_name);
		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR:
		{
			auto buttn_res = JRSMessageBox_INFO("警告", "是否删除CAD", jrscore::MessageButton::Ok | jrscore::MessageButton::No);
			if (buttn_res == jrscore::MessageButton::Ok)
			{
				auto state = project_param_instance.GetProjectDataProcessInstance()->DeleteComponent(select_param.component_name,
					select_param.subboard_name, param_operator.component_type);
				if (state != 0)
				{
					_is_delete = false;
					return{};
				}
				_is_delete = true;
			}
			else
			{
				_is_delete = false;
			}
		}
		break;
		}
		return {};
	}

	void Render2dModel::GraphicsUpdateUnit(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& param_operator)
	{
		// auto id = gh->GetId().GetString();
		// if (id.empty())
		//     return;
		// auto parent = gh->GetParent();
		// if (!parent)
		//     return;
		auto& select_param = param_operator.select_param;
		switch (param_operator.type)
		{
		case UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR:
		{
			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!component.has_value())
				return;
			auto unit = project_param_instance.GetProjectDataProcessInstance()->ReadComponentUnit(component->get().component_part_number, select_param.unit_name);
			if (!unit.has_value())
				return;
			unit->x = gh->LocalX();
			unit->y = gh->LocalY();
			unit->width = gh->w();
			unit->height = gh->h();


			auto pad_temp_ptr = std::dynamic_pointer_cast<PadGraphics>(gh);
			unit->direction = static_cast<jrsdata::ComponentUnit::Direction>(pad_temp_ptr->GetDirection());  //By: HJC 2025/3/4 Pad方向更新
			unit->show_id = pad_temp_ptr->GetPadID();
			//Log_INFO("gh update unit info:", unit->unit_name, " center point:[", unit->x, ",", unit->y, "]; size:[", unit->width, ",", unit->height, "];");

			project_param_instance.GetProjectDataProcessInstance()->UpdateComponentUnit(*unit, component->get().component_part_number);

		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR:
		{
			auto subboard = project_param_instance.GetProjectDataProcessInstance()->ReadSubBoardRef(select_param.subboard_name);
			if (!subboard.has_value())
				return;
			auto part = project_param_instance.GetProjectDataProcessInstance()->ReadPNDetectInfoRef(param_operator.part_name);
			if (!part.has_value())
				return;
			auto componet = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!componet.has_value())
				return;
			std::string group_name = "";

			auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(gh);
			if (pad_gh)
			{
				auto pad_group_ptr = pad_gh->GetPadGroupPtr().lock();
				group_name = pad_group_ptr->GetPadGroupName();   //获取唯一的组名
			}
			else
			{
				return;
			}

			auto unit = jrsdata::ComponentUnit(
				gh->LocalX(),
				gh->LocalY(),
				gh->w(),
				gh->h(),
				1, "", group_name, // id,name 在接口内设置
				param_operator.unit_type,
				jrsdata::ComponentUnit::Shape::RECT,
				static_cast<jrsdata::ComponentUnit::Direction>(pad_gh->GetDirection()),
				static_cast<jrsdata::ComponentUnit::PadType>(pad_gh->GetGroupType())
			);
			/**< pad 单排需要外部赋值显示id*/
			if (unit.pad_type == jrsdata::ComponentUnit::PadType::ARRAY)
			{
				unit.show_id = pad_gh->GetPadID();
			}


			if (componet->get().component_type == jrsdata::Component::Type::CAD)
			{
				if (auto state = project_param_instance.GetProjectDataProcessInstance()->CreateComponentUnit(unit, param_operator.part_name); state != 0)
				{
					Log_ERROR("Pad信息更新到工程失败，请检查");
					return;
				}
				auto gh_name = JoinString({ select_param.subboard_name, select_param.component_name, unit.unit_name });
				gh->SetId(gh_name);
			}
			else
			{
				Log_ERROR("只能再CAD上添加pad，无法在", componet->get().component_name, "添加Pad信息");
				return;
			}

		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR:
		{
			auto component = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(select_param.component_name, select_param.subboard_name, param_operator.component_type);
			if (!component.has_value())
				return;
			if (auto state = project_param_instance.GetProjectDataProcessInstance()->DeleteComponentUnit(component->get().component_part_number, select_param.unit_name); state != 0)
			{
				return;
			}
		}
		break;
		}
	}


	void Render2dModel::GraphicsUpdateDetectWindow(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& param_operator)
	{
		auto& select_param = param_operator.select_param;
		auto project_ptr = project_param_instance.GetProjectDataProcessInstance();
		if (!project_ptr)
		{
			Log_ERROR("获取工程指针为空，请检查");
			return;
		}

		auto ProcessMirrorPad = [&](jrsdata::DetectWindow& detectwindow) {
			auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(gh->GetParent());
			if (!pad_gh)
			{
				return;
			}

			auto component_gh = pad_gh->GetParent();
			if (!component_gh)
			{
				Log_ERROR("获取元件指针失败，请检查");
				return;
			}

			cv::Point2f component_center(component_gh->LocalX(), component_gh->LocalY());
			cv::Point2f unit_center(pad_gh->LocalX(), pad_gh->LocalY());
			cv::RotatedRect detect_window_rotated(
				{ detectwindow.cx, detectwindow.cy },
				{ detectwindow.width, detectwindow.height },
				0
			);

			auto new_rect = project_ptr->GetRotatedRectByPadDirection(
				component_center, unit_center, detect_window_rotated,
				static_cast<jrsdata::ComponentUnit::Direction>(pad_gh->GetDirection())
			);

			detectwindow.cx = new_rect.center.x;
			detectwindow.cy = new_rect.center.y;
			detectwindow.width = new_rect.size.width;
			detectwindow.height = new_rect.size.height;
			};

		switch (param_operator.type)
		{
		case UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR:
		{
			auto detectwindow_opt = project_ptr->ReadDetectWindowRef(param_operator.part_name, select_param.window_name);
			if (!detectwindow_opt.has_value()) return;

			auto& detectwindow = detectwindow_opt->get();
			detectwindow.cx = gh->LocalX();
			detectwindow.cy = gh->LocalY();
			detectwindow.width = gh->w();
			detectwindow.height = gh->h();

			ProcessMirrorPad(detectwindow);
			break;
		}
		case UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR:
		{
			jrsdata::DetectWindow detectwindow;

			detectwindow.cx = gh->LocalX();
			detectwindow.cy = gh->LocalY();
			detectwindow.width = gh->w();
			detectwindow.height = gh->h();
			detectwindow.model_name = param_operator.model_name;
			detectwindow.defect_name = param_operator.defect_name;

			ProcessMirrorPad(detectwindow);

			// 2024.12.18 绘制完检测框后添加算法 by wangzhengkai
			DetectAlgorithm detect_algo;
			detect_algo.detect_algorithm_name = param_operator.algo_name;
			CreateSubDetectWindow(detectwindow, param_operator.sub_win_type, detect_algo.algorithm_detect_windows);
			// TODO 根据sub_win_type绘制子检测框添加到算法中
			detectwindow.algorithms.push_back(detect_algo);


			if (project_ptr->CreateDetectWindow(param_operator.model_name, param_operator.part_name, detectwindow) != 0)
				return;

			auto gh_name = JoinString({ select_param.subboard_name, select_param.component_name, select_param.unit_name,
						  param_operator.model_name, detectwindow.name });
			gh->SetId(gh_name);
			break;
		}
		case UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR:
		{
			if (project_ptr->DeleteDetectWindowAndTemplate(param_operator.part_name, select_param.window_name) != 0)
				return;
			break;
		}
		}

	}

	void Render2dModel::GraphicsUpdateSubDetectWindow(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& param_operator)
	{
		auto& select_param = param_operator.select_param;
		switch (param_operator.type)
		{
		case UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR:
		{
			auto sub_detectwindow = project_param_instance.GetProjectDataProcessInstance()->ReadSubDetectWindowRef(param_operator.part_name, param_operator.model_name, select_param.window_name, param_operator.algo_name, select_param.subwindow_name);
			if (!sub_detectwindow.has_value())
				return;
			sub_detectwindow->get().cx = gh->LocalX();
			sub_detectwindow->get().cy = gh->LocalY();
			sub_detectwindow->get().width = gh->w();
			sub_detectwindow->get().height = gh->h();
			// project_param_instance.GetProjectDataProcessInstance()->UpdateSubDetectWindow(param_operator.part_name, param_operator.model_name, parent_id, param_operator.algo_name, *sub_detectwindow);
		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR:
		{
			jrsdata::SubDetectWindow sub_detectwindow;
			sub_detectwindow.cx = gh->LocalX();
			sub_detectwindow.cy = gh->LocalY();
			sub_detectwindow.width = gh->w();
			sub_detectwindow.height = gh->h();
			project_param_instance.GetProjectDataProcessInstance()->CreateSubDetectWindow(param_operator.part_name, param_operator.model_name, select_param.window_name, param_operator.algo_name, sub_detectwindow);
			auto gh_name = JoinString({ select_param.subboard_name, select_param.component_name, select_param.unit_name,
			param_operator.model_name,select_param.window_name, param_operator.window_name, sub_detectwindow.name });
			gh->SetId(gh_name);
		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR:
		{
			if (auto state = project_param_instance.GetProjectDataProcessInstance()->DeleteSubDetectWindow(param_operator.part_name, param_operator.model_name, select_param.window_name, param_operator.algo_name, select_param.subwindow_name);
				state != 0)
			{
				return;
			}
		}
		break;
		}
	}

	void Render2dModel::GraphicsUpdateSubBoard(const std::shared_ptr<GraphicsAbstract>& gh, const UpdateOperatorParam& param_operator)
	{
		auto id = gh->GetId().GetString();
		if (id.empty())
			return;

		switch (param_operator.type)
		{
		case UpdateOperatorParam::UpdateOperatorType::UPDATE_OPERATOR:
		{
			auto subboard = project_param_instance.GetProjectDataProcessInstance()->ReadSubBoard(id);
			if (!subboard.has_value())
			{
				project_param_instance.GetProjectDataProcessInstance()->CreateSubBoard(gh->x(), gh->y(), gh->w(), gh->h());
				return;
			}

			auto cx_offset = gh->x() - subboard->x;
			auto cy_offset = gh->y() - subboard->y;
			subboard->x = gh->x();
			subboard->y = gh->y();
			subboard->width = gh->w();
			subboard->height = gh->h();
			/** 更新指定子板 */
			project_param_instance.GetProjectDataProcessInstance()->UpdateSubBoard(*subboard);
			/**< 更新全部子板 */
			project_param_instance.GetProjectDataProcessInstance()->UpdateSubboard(subboard->subboard_name, cx_offset, cy_offset, gh->w(), gh->h());
			std::vector<std::shared_ptr<GraphicsAbstract>> children;
			for (auto& child : gh->GetChildren())
			{
				auto child_attr_share = child.lock();
				if (!child_attr_share)
					continue;
				auto child_share = std::dynamic_pointer_cast<GraphicsAbstract>(child_attr_share);
				if (!child_share)
					continue;
				children.emplace_back(child_share);
			}
			GraphicsUpdateProject(children, param_operator);
		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::CREATE_OPERATOR:
		{
			jrsdata::SubBoard subboard;
			subboard.x = gh->x();
			subboard.y = gh->y();
			subboard.width = gh->w();
			subboard.height = gh->h();
			project_param_instance.GetProjectDataProcessInstance()->CreateSubBoard(subboard);
			gh->SetId(subboard.subboard_name);
		}
		break;
		case UpdateOperatorParam::UpdateOperatorType::DELETE_OPERATOR:
		{
			project_param_instance.GetProjectDataProcessInstance()->DeleteSubBoard(id);
		}
		break;
		}
	}

	void Render2dModel::CreateSubboardGraphics(std::vector<std::shared_ptr<GraphicsAbstract>>& graphics_,
		const std::vector<jrsdata::SubBoard>& subboards_, bool is_temp_ /*= true*/)
	{
		for (auto& subboard_value : subboards_)
		{
			if (_is_debugging && subboard_value.subboard_name != _current_debug_info.current_subboard_name)
			{//在线调试过程中，如果子板名称不同，则跳过不刷新
				continue;
			}
			if (!_is_debugging)
			{
				auto subboard = std::make_shared<RectGraphics>(subboard_value.x, subboard_value.y,
					subboard_value.width, subboard_value.height, 0.f);
				if (is_temp_)  //临时图形不允许更改
				{
					subboard->settings.SetIsCopy(false);
					subboard->settings.SetIsEditAble(false);
					subboard->settings.SetIsSelected(false);
				}
				subboard->settings.SetLayer(LayerConverter::ToString(Layer::subboard));
				subboard->SetId(subboard_value.subboard_name);
				graphics_.emplace_back(subboard);
			}
			if (!is_temp_)  //临时显示不刷新子图形
			{
				CADCreateGraphics(graphics_, subboard_value.component_info);
				CADCreateGraphics(graphics_, subboard_value.sub_mark);
				CADCreateGraphics(graphics_, { subboard_value.bad_mark,subboard_value.barcode });
			}
		}
		CreateSubboardIdGraphics(graphics_, subboards_);
	}

	void Render2dModel::TriggerCallBackProjectUpdateGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& ghs)
	{
		if (!callback_project_update_graphics)
			return;
		callback_project_update_graphics(ghs);
	}

	void Render2dModel::TriggerCallBackCreateGraphics(std::shared_ptr<GraphicsAbstract>& gh, int graphics_type,
		const std::string& layer, const std::string& group_name_, const std::shared_ptr<GraphicsAbstract>& father_graphics_ptr_)
	{
		if (!callback_create_graphics)
			return;
		callback_create_graphics(gh, graphics_type, layer, group_name_, father_graphics_ptr_);
	}
	jrsparam::ExecuteModeInfo Render2dModel::GetExecuteModeInfo()
	{
		jrsparam::ExecuteModeInfo run_mode_info;
		if (_is_debugging)
		{
			run_mode_info.execute_mode = jrsparam::ExecuteMode::AutoMode;
			run_mode_info.fov_left_top_x = _current_debug_info.current_component_fov_img.pos.x;
			run_mode_info.fov_left_top_y = _current_debug_info.current_component_fov_img.pos.y;
			run_mode_info.src_img = _current_debug_info.current_component_fov_img.imgs;

		}
		else
		{
			run_mode_info.execute_mode = jrsparam::ExecuteMode::ManualMode;
		}
		return run_mode_info;
	}

	void Render2dModel::ApplyTransformToComponent(jrsdata::Component& component,
		const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type,
		float center_x, float center_y)
	{
		switch (type)
		{
		case MultiBoardEventParam::OperateParam::TransformType::VERTICAL_MIRROR:
			jrscore::CoordinateTransform::VerticalMirror(component.x, component.y, component.x, component.y, center_x, center_y);
			break;
		case MultiBoardEventParam::OperateParam::TransformType::HORIZONTAL_MIRROR:
			jrscore::CoordinateTransform::HorizontalMirror(component.x, component.y, component.x, component.y, center_x, center_y);
			break;
		case MultiBoardEventParam::OperateParam::TransformType::ROTATE_90:
		{
			jrscore::CoordinateTransform::Rotate(component.x, component.y, component.x, component.y, center_x, center_y, 90);
			double angle = component.angle + 90;
			jrscore::AOITools::NormalizeAngle(angle);
			component.angle = angle;
		}
		break;
		case MultiBoardEventParam::OperateParam::TransformType::CONVERT:
		{
			double angle = -component.angle;
			jrscore::AOITools::NormalizeAngle(angle);
			component.angle = angle;
		}
		break;
		default:
			break;
		}
	}

	void Render2dModel::ApplyTransformToSubBoard(jrsdata::SubBoard& subboard,
		const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type,
		float center_x, float center_y)
	{
		for (auto& c : subboard.component_info)
		{
			ApplyTransformToComponent(c, type, center_x, center_y);
		}
		switch (type)
		{
		case MultiBoardEventParam::OperateParam::TransformType::VERTICAL_MIRROR:
			jrscore::CoordinateTransform::VerticalMirror(subboard.x, subboard.y, subboard.x, subboard.y, center_x, center_y);
			break;
		case MultiBoardEventParam::OperateParam::TransformType::HORIZONTAL_MIRROR:
			jrscore::CoordinateTransform::HorizontalMirror(subboard.x, subboard.y, subboard.x, subboard.y, center_x, center_y);
			break;
		case MultiBoardEventParam::OperateParam::TransformType::ROTATE_90:
		{
			jrscore::CoordinateTransform::Rotate(subboard.x, subboard.y, subboard.x, subboard.y, center_x, center_y, 90);
			double angle = subboard.angle + 90;
			jrscore::AOITools::NormalizeAngle(angle);
			subboard.angle = angle;
		}
		break;
		default:
			break;
		}
	}

	void Render2dModel::SwapWidthHeightIfRotate(jrsdata::SubBoard& subboard, const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type)
	{
		if (type == MultiBoardEventParam::OperateParam::TransformType::ROTATE_90)
		{
			std::swap(subboard.width, subboard.height);
		}
	}

	void Render2dModel::ApplyRotateOrConvertToComponent(jrsdata::Component& component_, const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type_)
	{
		double current_angle = component_.angle;

		if (type_ == jrsdata::MultiBoardEventParam::OperateParam::TransformType::ROTATE_90)
		{
			current_angle += 90;
		}
		else if (type_ == jrsdata::MultiBoardEventParam::OperateParam::TransformType::CONVERT)
		{
			current_angle = -current_angle;
		}

		jrscore::AOITools::NormalizeAngle(current_angle);

		component_.angle = current_angle;
	}

	void Render2dModel::AllCADComponentRotate(const jrsdata::MultiBoardEventParam::OperateParam::TransformType& type_)
	{
		auto& project_instance_process = project_param_instance.GetProjectDataProcessInstance();
		auto& subboards = project_instance_process->GetProjectParam()->board_info.sub_board;
		for (auto& subboard : subboards)
		{
			for (auto& component : subboard.component_info)
			{
				ApplyRotateOrConvertToComponent(component, type_);
			}
		}

	}

	void Render2dModel::EntiretyBoardTransform(const jrsdata::MultiBoardEventParam::OperateParam& operate_param_)
	{
		if (project_param_instance.GetProjectDataProcessInstance()->IsParamPtrEmpty())
			return;

		auto board = project_param_instance.GetProjectDataProcessInstance()->ReadBoard();
		if (!board.has_value())
			return;

		using OperateParam = jrsdata::MultiBoardEventParam::OperateParam;

		if (operate_param_.operate_type == OperateParam::OperateType::BOARD)
		{
			auto& subboards = board->get().sub_board;

			std::vector<cv::RotatedRect> rects;
			for (const auto& subboard : subboards)
			{
				rects.emplace_back(cv::Point2f(subboard.x, subboard.y),
					cv::Size2f(subboard.width, subboard.height), 0);
			}

			auto min_rect = jrscore::AOITools::GetMinimumExternalRoatedRect(rects);

			for (auto& subboard : subboards)
			{
				ApplyTransformToSubBoard(subboard, operate_param_.transform_type, min_rect.center.x, min_rect.center.y);
				SwapWidthHeightIfRotate(subboard, operate_param_.transform_type);
			}
		}
		else if (operate_param_.operate_type == OperateParam::OperateType::COMPONENT)
		{
			AllCADComponentRotate(operate_param_.transform_type);
		}

		ProjectUpdateGraphics();
	}

	void Render2dModel::SubBoardTransform(const std::string& subboard_name_,
		const jrsdata::MultiBoardEventParam::OperateParam& operate_param_)
	{

		if (subboard_name_.empty())
		{
			JRSMessageBox_INFO("子板为空", "请先选择一个子板", jrscore::MessageButton::Ok);
			return;
		}
		if (project_param_instance.GetProjectDataProcessInstance()->IsParamPtrEmpty())
			return;

		auto subboard = project_param_instance.GetProjectDataProcessInstance()->ReadSubBoard(subboard_name_);
		if (!subboard.has_value() || subboard->component_info.empty())
		{
			JRSMessageBox_INFO("读取子板失败", "读取子板:" + subboard_name_ + "信息失败！", jrscore::MessageButton::Ok);
			return;
		}


		if (operate_param_.operate_type == jrsdata::MultiBoardEventParam::OperateParam::OperateType::BOARD)
		{
			ApplyTransformToSubBoard(*subboard, operate_param_.transform_type, subboard->x, subboard->y);
			SwapWidthHeightIfRotate(*subboard, operate_param_.transform_type);
		}
		else if (operate_param_.operate_type == jrsdata::MultiBoardEventParam::OperateParam::OperateType::COMPONENT)
		{
			for (auto& component : subboard->component_info)
			{
				ApplyRotateOrConvertToComponent(component, operate_param_.transform_type);
			}
		}
		project_param_instance.GetProjectDataProcessInstance()->UpdateSubBoard(*subboard);
		ProjectUpdateGraphics();
	}

	float Render2dModel::GetComponentDegree(const std::string& commponent_name_, const std::string& subboard_name_)
	{
		auto commpoent = project_param_instance.GetProjectDataProcessInstance()->ReadComponentRef(commponent_name_, subboard_name_, jrsdata::Component::Type::CAD);
		if (commpoent.has_value())
		{
			return commpoent->get().angle;
		}
		return 0.0f;
	}
	int Render2dModel::CreateSubDetectWindow(jrsdata::DetectWindow& detect_window, const int& sub_win_type, std::vector<jrsdata::SubDetectWindow>& sub_detect_windows)
	{
		(void)detect_window;
		(void)sub_win_type;
		(void)sub_detect_windows;

		int win_width = (int)std::round(detect_window.width / 4.0);
		int win_height = (int)std::round(detect_window.height / 4.0);

		switch (sub_win_type)
		{
		case 1: // ONE_WINDOW               
		{
			win_width = win_width > 100 ? win_width : 100;
			win_height = win_height > 100 ? win_height : 100;

			SubDetectWindow sub_detect_window;
			sub_detect_window.cx = 0;
			sub_detect_window.cy = 0;
			sub_detect_window.width = win_width;
			sub_detect_window.height = win_height;
			sub_detect_window.name = "sub_detect_window_1";
			sub_detect_windows.push_back(sub_detect_window);
			break;
		}
		case 2: //CROSS_WINDOWS
		{
			SubDetectWindow sub_detect_window1;
			sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
			sub_detect_window1.cy = 0;
			sub_detect_window1.width = win_width;
			sub_detect_window1.height = win_height;
			sub_detect_window1.name = "sub_detect_window_1";

			SubDetectWindow sub_detect_window2;
			sub_detect_window2.cx = (win_width * 3 / 2.0 - 1);
			sub_detect_window2.cy = 0;
			sub_detect_window2.width = win_width;
			sub_detect_window2.height = win_height;
			sub_detect_window2.name = "sub_detect_window_2";

			SubDetectWindow sub_detect_window3;
			sub_detect_window3.cx = 0;
			sub_detect_window3.cy = -(win_height * 3 / 2.0 + 1);
			sub_detect_window3.width = win_width;
			sub_detect_window3.height = win_height;
			sub_detect_window3.name = "sub_detect_window_3";

			SubDetectWindow sub_detect_window4;
			sub_detect_window4.cx = 0;
			sub_detect_window4.cy = (win_height * 3 / 2.0 - 1);
			sub_detect_window4.width = win_width;
			sub_detect_window4.height = win_height;
			sub_detect_window4.name = "sub_detect_window_4";

			sub_detect_windows.push_back(sub_detect_window1);
			sub_detect_windows.push_back(sub_detect_window2);
			sub_detect_windows.push_back(sub_detect_window3);
			sub_detect_windows.push_back(sub_detect_window4);

			break;
		}
		case 3: //FOUR_CORNERS_WINDOWS
		{
			SubDetectWindow sub_detect_window1;
			sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
			sub_detect_window1.cy = -(win_height * 3 / 2.0 + 1);
			sub_detect_window1.width = win_width;
			sub_detect_window1.height = win_height;
			sub_detect_window1.name = "sub_detect_window_1";

			SubDetectWindow sub_detect_window2;
			sub_detect_window2.cx = (win_width * 3 / 2.0 + 1);
			sub_detect_window2.cy = -(win_height * 3 / 2.0 + 1);
			sub_detect_window2.width = win_width;
			sub_detect_window2.height = win_height;
			sub_detect_window2.name = "sub_detect_window_2";

			SubDetectWindow sub_detect_window3;
			sub_detect_window3.cx = -(win_width * 3 / 2.0 + 1);
			sub_detect_window3.cy = (win_height * 3 / 2.0 + 1);
			sub_detect_window3.width = win_width;
			sub_detect_window3.height = win_height;
			sub_detect_window3.name = "sub_detect_window_3";

			SubDetectWindow sub_detect_window4;
			sub_detect_window4.cx = (win_width * 3 / 2.0 + 1);
			sub_detect_window4.cy = (win_height * 3 / 2.0 + 1);
			sub_detect_window4.width = win_width;
			sub_detect_window4.height = win_height;
			sub_detect_window4.name = "sub_detect_window_4";
			sub_detect_windows.push_back(sub_detect_window1);
			sub_detect_windows.push_back(sub_detect_window2);
			sub_detect_windows.push_back(sub_detect_window3);
			sub_detect_windows.push_back(sub_detect_window4);
			break;
		}
		case 4: //HORIZONTAL_SYMMETRY_WINDOWS
		{
			SubDetectWindow sub_detect_window1;
			sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
			sub_detect_window1.cy = 0;
			sub_detect_window1.width = win_width;
			sub_detect_window1.height = win_height;
			sub_detect_window1.name = "sub_detect_window_1";

			SubDetectWindow sub_detect_window2;
			sub_detect_window2.cx = (win_width * 3 / 2.0 - 1);
			sub_detect_window2.cy = 0;
			sub_detect_window2.width = win_width;
			sub_detect_window2.height = win_height;
			sub_detect_window2.name = "sub_detect_window_2";

			sub_detect_windows.push_back(sub_detect_window1);
			sub_detect_windows.push_back(sub_detect_window2);

			break;
		}
		case 5: //VERTICAL_SYMMETRY_WINDOWS
		{
			SubDetectWindow sub_detect_window1;
			sub_detect_window1.cx = 0;
			sub_detect_window1.cy = -(win_height * 3 / 2.0 + 1);
			sub_detect_window1.width = win_width;
			sub_detect_window1.height = win_height;
			sub_detect_window1.name = "sub_detect_window_1";

			SubDetectWindow sub_detect_window2;
			sub_detect_window2.cx = 0;
			sub_detect_window2.cy = (win_height * 3 / 2.0 - 1);
			sub_detect_window2.width = win_width;
			sub_detect_window2.height = win_height;
			sub_detect_window2.name = "sub_detect_window_2";

			sub_detect_windows.push_back(sub_detect_window1);
			sub_detect_windows.push_back(sub_detect_window2);

			break;
		}
		default:
		{
			break;
		}
		}
		return 0;
	}
}
