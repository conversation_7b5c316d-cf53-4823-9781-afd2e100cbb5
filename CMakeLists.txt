#     _____  _______     ______        _       _______     _______    _____       ____    
#    |_   _||_   __ \  .' ____ \      / \     |_   __ \   |_   __ \  / ___ `.   .'    '.  
#      | |    | |__) | | (___ \_|    / _ \      | |__) |    | |__) ||_/___) |  |  .--.  | 
#  _   | |    |  __ /   _.____`.    / ___ \     |  __ /     |  ___/  .'____.'  | |    | | 
# | |__' |   _| |  \ \_| \____) | _/ /   \ \_  _| |  \ \_  _| |_    / /_____  _|  `--'  | 
# `.____.'  |____| |___|\______.'|____| |____||____| |___||_____|   |_______|(_)'.____.'  
                                                                                                                                                        
                                                                      
cmake_minimum_required(VERSION 3.12)
project(jrsaoi)
set_property(GLOBAL PROPERTY USE_FOLDERS On)
set_property(GLOBAL PROPERTY PREDEFINED_TARGETS_FOLDER "CMakeTargets")
 
#设置CMake最低版本
cmake_minimum_required(VERSION 3.12)
#设置C++版本
set (CMAKE_CXX_STANDARD 17)
set (CMAKE_CXX_STANDARD_REQUIRED True)
# 指定生成vs2022 
set(CMAKE_GENERATOR "Visual Studio 17 2022")
#设置release可以调试 临时 TODO:delete 
set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
# 确保生成 PDB 文件
set(CMAKE_DEBUG_POSTFIX "" CACHE STRING "No debug postfix")
set(CMAKE_RELEASE_POSTFIX "" CACHE STRING "No release postfix")
#设置release可以调试 临时 TODO:delete

if (MSVC)

    # 设置全局编译器警告等级
    add_compile_options(
        $<$<CXX_COMPILER_ID:MSVC>:/W4>
    )
    #将警告视为错误
    add_compile_options(
        $<$<CXX_COMPILER_ID:MSVC>:/WX>
    )
    add_compile_options(/wd4828)
    add_compile_options(/wd4251)
    #无法访问的代码
    add_compile_options(/wd4702)
    #被声明为已否决
    add_compile_options(/wd4996) 

   
endif()

if(WIN32)
  set(CMAKE_WIN32_EXECUTABLE ON) 
endif()
 
add_definitions(-DROOT="${PROJECT_SOURCE_DIR}")
######################使用多处理器编译########################
IF (WIN32)
    if(MSVC) 
        OPTION(USE_MP "use multiple" ON)
        OPTION(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP 
          "Set The Global Option COMPILE_FLAGS /MP to target." ON)
        if(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP OR USE_MP)
          set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /MP")
          set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP")
        endif()
        set(VS_STARTUP_PROJECT ${PROJECT_NAME})
    ENDIF(MSVC)  
ENDIF()    
#设置编码格式
add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")

include(cmake/versions.cmake)
include(cmake/options.cmake)
include(cmake/findopencv.cmake) 
include(cmake/findeasyid.cmake) 
include(cmake/findqt.cmake)
include(cmake/printinfo.cmake) 
include(cmake/subrepository.cmake)
include(cmake/copyfile.cmake)  
     
#YLT
add_subdirectory(thirdparty/ylt)

#mysql 
add_definitions(-DORMPP_ENABLE_MYSQL) 
add_definitions(-DJRSCOR_ENABLE_MYSQL) 
add_subdirectory(thirdparty/ormpp)

# gtest库
#  1. 链接CRT动态版 
#  2. 隐藏内部符号
set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
set(gtest_hide_internal_symbols ON CACHE BOOL "" FORCE)
add_subdirectory(thirdparty/googletest EXCLUDE_FROM_ALL)#不参与install
set_target_properties(gtest          PROPERTIES FOLDER "thirdparty/googletest") 
set_target_properties(gtest_main     PROPERTIES FOLDER "thirdparty/googletest") 
set_target_properties(gmock          PROPERTIES FOLDER "thirdparty/googletest") 
set_target_properties(gmock_main     PROPERTIES FOLDER "thirdparty/googletest") 



#添加子目录 
####################View层#############################
#主界面
add_subdirectory(src/view)
set_target_properties(JRSAOI    PROPERTIES FOLDER "view")
#渲染#
add_subdirectory(src/render) 
set_target_properties(render    PROPERTIES FOLDER "view")
####################逻辑层#############################
add_subdirectory(src/logicmanager)

add_subdirectory(src/project)
add_subdirectory(src/workflow) 
 
set_target_properties(logicmanager PROPERTIES FOLDER "logic")
set_target_properties(project    PROPERTIES FOLDER "logic")
set_target_properties(workflow    PROPERTIES FOLDER "logic")
 

 
####################插件层##############################
add_subdirectory(src/algorithmengine)
add_subdirectory(src/devicemanager)
set_target_properties(algorithmengine    PROPERTIES FOLDER "plugin")
set_target_properties(devicemanager    PROPERTIES FOLDER "plugin")
 
 
####################核心库###########################################
add_subdirectory(src/core)
add_subdirectory(src/parametermanager)
set_target_properties(core    PROPERTIES FOLDER "core")
set_target_properties(parametermanager    PROPERTIES FOLDER "core")

####################算法demo TODO:Delete#############################
# add_subdirectory(operatorlibrary/demo)
# set_target_properties(operatordemo    PROPERTIES FOLDER "operator")

####################数据层############################# 
add_subdirectory(src/datamanager)
set_target_properties(datamanager    PROPERTIES FOLDER "data") 

####################单元测试#############################
add_subdirectory(unittest)
set_target_properties(unittest                PROPERTIES FOLDER "unittest")

#####################打包#############################
include(cmake/install.cmake)
