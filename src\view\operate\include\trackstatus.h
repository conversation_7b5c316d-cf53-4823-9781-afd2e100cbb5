/*****************************************************************//**
 * @file   trackstatus.h
 * @brief 检测框界面显示轨道状态
 *
 * <AUTHOR>
 * @date   2024.9.24
 *********************************************************************/
#ifndef TRACKSTATUS_H
#define TRACKSTATUS_H
 // prebuild
#include "pch.h"
#pragma warning(push, 3)
//#include "ui_trackstatus.h"
#include <QWidget>
#include <QLabel>
#pragma warning(pop)

namespace Ui
{
    class TrackStatus;
}

class TrackStatus : public QWidget
{
    Q_OBJECT
public:
    explicit TrackStatus(QWidget* parent = nullptr);
    ~TrackStatus();
    /**
     * @fun SetTrackName
     * @brief 设置轨道名称
     * @param name
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SetTrackName(QString name);
    /**
     * @fun SetTrackIndex
     * @brief 设置轨道索引
     * @param index
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SetTrackIndex(jrsdata::TRACK_NUMBER index);
    /**
     * @fun TrackStatus::CheckTrackStatus
     * @brief 检查轨道状态是否存在。
     * @param ptr 指向操作视图参数的智能指针。
     * @return bool 是否存在轨道状态。
     * @date 2025.02.25
     * <AUTHOR>
     */
    bool CheckTrackStatus(const jrsdata::OperateViewParamPtr ptr) const;
    /**
     * @fun TrackStatus::UpdateTrackMode
     * @brief 更新轨道模式。
     * @param status 轨道状态。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateTrackMode(const jrsdata::Track& status);
    /**
     * @fun TrackStatus::UpdateTrackDirection
     * @brief 更新轨道进出板方向。
     * @param status 轨道状态。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateTrackDirection(const jrsdata::Track& status);
    /**
     * @fun TrackStatus::UpdateBoardStatus
     * @brief 更新轨道板状态。
     * @param input 轨道输入状态。
     * @param output 轨道输出状态。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateBoardStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output);
    /**
     * @fun TrackStatus::UpdateCylinderStatus
     * @brief 更新顶升气缸状态。
     * @param input 轨道输入状态。
     * @param output 轨道输出状态。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateCylinderStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output);
    /**
     * @fun TrackStatus::UpdateShieldStatus
     * @brief 更新挡板气缸状态。
     * @param input 轨道输入状态。
     * @param output 轨道输出状态。
     * @param status 轨道状态。
     * @return void
     * @date 2025.02.25
     * @ chenxixi
     */
    void UpdateShieldStatus(const jrsdata::TrackInput& input, const jrsdata::TrackOutput& output, const jrsdata::Track& status);
    /**
     * @fun TrackStatus::UpdateShieldLabels
     * @brief 更新挡板气缸标签。
     * @param status 挡板气缸状态。
     * @param enterDirection 进板方向。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateShieldLabels(const std::string& status, int enterDirection);
    /**
     * @fun TrackStatus::UpdateBoardWidth
     * @brief 更新板宽。
     * @param status 轨道状态。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateBoardWidth(const jrsdata::Track& status);

public slots:
    /**
     * @fun TrackStatus::UpdateView
     * @brief 更新轨道状态视图。
     * @param ptr 指向操作视图参数的智能指针。
     * @return void
     * @date 2025.02.25
     * <AUTHOR>
     */
    void UpdateView(const jrsdata::OperateViewParamPtr ptr);
private:
    /**
     * @fun UpdateLabelBackGroundColor
     * @brief
     * @param label
     * @param state
     * @param style1
     * @param style2
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateLabelBackGroundColor(QLabel* label, std::string state, QString style1 = "transparent", QString style2 = "background-color: rgb(85, 255, 0);");
private:
    /// 用户界面指针，指向 TrackStatus 的 UI 组件。
    Ui::TrackStatus* ui;

    /// 指向轨道状态视图参数的智能指针，用于存储和传递轨道状态相关数据。
    jrsdata::OperateViewParamPtr trackstatus_view_ptr;

    /// 轨道索引，标识当前操作的轨道编号。
    jrsdata::TRACK_NUMBER track_index;

    /// 透明背景样式，用于设置 QLabel 的透明背景。
    QString color_transparent_style = "transparent";

    /// 浅蓝色背景样式，用于设置 QLabel 的背景颜色为浅蓝色。
    QString color_lightblue_style = "background-color: rgb(71, 167, 255);";

    /// 浅绿色背景样式，用于设置 QLabel 的背景颜色为浅绿色。
    QString color_lightgreen_style = "background-color: rgb(85, 255, 0);";

    /// 浅灰色背景样式，用于设置 QLabel 的背景颜色为浅灰色。
    QString color_lightgray_style = "background-color: rgb(194, 194, 194);";

    /// 浅蓝色边框样式，用于设置 QLabel 的蓝色虚线边框。
    QString color_lightblue_border_style = "QLabel { border: 1px dashed blue; }";
};
#endif // TRACKSTATUS_H
