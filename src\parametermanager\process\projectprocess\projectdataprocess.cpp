//Stl
#include <unordered_set>
#include <algorithm>
//Custom
#include "projectdataprocess.h"
#include "generalimagetool.h"
#include "coordinatetransformationtool.h"
#include "coordinatetransform.hpp"
#include "operatorparambase.h"
#include "directedgraph.h"
#include "viewparam.hpp"
#include "image.hpp"
#include "cvtools.h"
#include "timeutility.h"
#include "stringoperation.h"
#include "jsonoperator.hpp"
#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 4244 26495)
#pragma warning(pop)
using namespace jrsdata;
namespace jrsparam
{


    void ProjectDataProcess::SetProjectParam(const jrsdata::ProjectParamPtr& project_param)
    {
        std::unique_lock<std::shared_mutex> lck(mutex);
        _param = project_param;
        // TODO 移动到其他地方
        {
            // 不存在子板时生成一个默认子板
            auto& board = _param->board_info;
            if (board.sub_board.empty())
            {
                auto sub = jrsdata::SubBoard();
                sub.id = 1;
                sub.x = 0;
                sub.y = 0;
                sub.width = 500;
                sub.height = 500;
                /**<子板名称与工程保持一致 By:HJC 2025/1/15 */
                sub.subboard_name = project_param->project_name + "_" + std::to_string(1);
                board.sub_board.emplace_back(std::move(sub));
            }
        }
    }

    void ProjectDataProcess::UpdateProjectBoardInfo(const jrsdata::Board& board_info_)
    {
        if (board_info_.sub_board.empty())
        {
            return;
        }
        _param->board_info.sub_board = board_info_.sub_board;
        _param->board_info.marks = board_info_.marks;
        _param->board_info.barcodes = board_info_.barcodes;
        _param->board_info.carrier_barcodes = board_info_.carrier_barcodes;
        _param->board_info.cover_plate_barcodes = board_info_.cover_plate_barcodes;

    }

    const jrsdata::ProjectParamPtr& ProjectDataProcess::GetProjectParam()
    {
        return _param;
    }

    std::string ProjectDataProcess::GetProjectName()
    {
        if (IsParamPtrEmpty())
        {
            return ""; //指针为空，请配置参数
        }
        return _param->project_name;
    }



    std::vector<std::vector<jrsdata::SubBoard>> ProjectDataProcess::CalculateRowsAndCols(const ProjectParamPtr& project_param_ptr_)
    {
        if (!project_param_ptr_ || project_param_ptr_->board_info.sub_board.empty())
            return {};

        auto subboards = project_param_ptr_->board_info.sub_board;

        std::sort(subboards.begin(), subboards.end(), [](const auto& a, const auto& b)
            {
                return a.y < b.y;
            });

        std::vector<std::vector<jrsdata::SubBoard>> rows_group;
        for (const auto& subboard : subboards)
        {
            bool found_row = false;
            for (auto& row : rows_group)
            {
                float ref_height = std::min(row.front().height, subboard.height);
                float y_threshold = ref_height * 0.3f;
                if (std::abs(row.front().y - subboard.y) < y_threshold)
                {
                    row.push_back(subboard);
                    found_row = true;
                    break;
                }
            }
            if (!found_row)
            {
                rows_group.push_back({ subboard });
            }
        }

        int rows = static_cast<int>(rows_group.size());
        int cols = 0;
        for (const auto& row : rows_group)
        {
            cols = std::max(cols, static_cast<int>(row.size()));
        }

        project_param_ptr_->board_info.rows = rows;
        project_param_ptr_->board_info.cols = cols;
        return rows_group;
    }


    int ProjectDataProcess::CreateComponent(jrsdata::Component& component)
    {
        switch (component.component_type)
        {
        case Component::Type::CAD:
        {
            return CreateCAD(component);
        }
        case Component::Type::SUB_MARK:
        {
            /** <子板mark 需要单独处理 */
            return CreateSubMark(component);
        }
        case Component::Type::SUB_BARCODE:
        {
            auto res = ReplaceSubBoardBarcode(component);
            if (res != jrscore::AOI_OK)
            {
                return res;
            }
            return SynchronousOtherSameSubboard(component);
        }
        case Component::Type::SUB_BADMARK:
        {
            auto res = ReplaceSubBadMark(component);
            if (res != jrscore::AOI_OK)
            {
                return res;
            }
            return SynchronousOtherSameSubboard(component);
        }
        break;
        case Component::Type::MARK:
        {
            return CreateMark(component);
        }
        case Component::Type::BARCODE:
        {
            return CreateBarcode(component);
        }
        case Component::Type::BADMARK:
        {
            //return CreateBadMark(component);
        }
        break;
        case Component::Type::CARRIER_BARCODE:
        {
            // return CreateCarrierBarcode(component);
        }
        break;
        case Component::Type::COVERPLATE_BARCODE:
        {
            // return CreateCoverPlateBarcode(component);
        }
        break;
        break;
        }
        return 1;
    }

    int ProjectDataProcess::UpdateComponent(jrsdata::Component& component)
    {
        switch (component.component_type)
        {
        case Component::Type::CAD:
        {
            return UpdateCAD(component);
        }
        case Component::Type::SUB_MARK:
        {
            return UpdateSubMark(component);
        }
        case Component::Type::SUB_BARCODE:
        {
            return ReplaceSubBoardBarcode(component);
        }
        case Component::Type::SUB_BADMARK:
        {
            return ReplaceSubBadMark(component);
        }
        break;
        case Component::Type::MARK:
        {
            return UpdateMark(component);
        }
        case Component::Type::BARCODE:
        {
            return UpdateBarcode(component);
        }
        case Component::Type::BADMARK:
        {
            //return UpdateBadMark(component);
        }
        break;
        case Component::Type::CARRIER_BARCODE:
        {
            //return UpdateCarrierBarcode(component);
        }
        break;
        case Component::Type::COVERPLATE_BARCODE:
        {
            //return UpdateCoverPlateBarcode(component);
        }
        break;
        }
        return 1;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>>
        ProjectDataProcess::ReadComponentRef(const std::string& component_name, const std::string& subboard_name, jrsdata::Component::Type type)
    {
        if (component_name.empty())
        {
            return std::nullopt;
        }

        switch (type)
        {
        case Component::Type::CAD:
        {
            return ReadCADRef(subboard_name, component_name);
        }
        break;
        case Component::Type::SUB_MARK:
        {
            return ReadSubMarkRef(subboard_name, component_name);
        }
        break;
        case Component::Type::SUB_BARCODE:
        {
            return ReadSubBoardBarcodeRef(subboard_name, component_name);
        }
        break;
        case Component::Type::SUB_BADMARK:
        {
            return ReadSubBadMarkRef(component_name);
        }
        break;
        case Component::Type::MARK:
        case Component::Type::BARCODE:
        case Component::Type::BADMARK:
        case Component::Type::CARRIER_BARCODE:
        case Component::Type::COVERPLATE_BARCODE:
        {
            return ReadComponentRef(component_name, type);
        }
        break;
        }
        return std::nullopt;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>>
        ProjectDataProcess::ReadComponentRef(const std::string& componentName, jrsdata::Component::Type type)
    {
        if (componentName.empty())
        {
            return std::nullopt;
        }

        switch (type)
        {
        case Component::Type::CAD:
        case Component::Type::SUB_MARK:
        case Component::Type::SUB_BARCODE:
        case Component::Type::SUB_BADMARK:
        {
            return std::nullopt; // 需要子板名
        }
        break;
        case Component::Type::MARK:
        {
            return ReadMarkRef(componentName);
        }
        case Component::Type::BARCODE:
        {
            return ReadBarcodeRef(componentName);
        }
        case Component::Type::BADMARK:
        case Component::Type::CARRIER_BARCODE:
        case Component::Type::COVERPLATE_BARCODE:
        {
            return std::nullopt; // 不支持
        }
        break;
        }
        return std::nullopt;
    }

    std::optional<std::vector<jrsdata::Component>> ProjectDataProcess::ReadComponents(const std::string& component_part_number_)
    {
        std::unordered_map<std::string, std::vector<jrsdata::Component>> component_index;

        // 直接在查询时构建哈希索引
        for (const auto& subboard : _param->board_info.sub_board) {
            for (const auto& component : subboard.component_info) {
                component_index[component.component_part_number].emplace_back(component);
            }
        }

        // 使用哈希表查找，提高查询效率
        auto it = component_index.find(component_part_number_);
        if (it != component_index.end()) {
            return it->second;
        }
        return std::nullopt;
    }

    bool ProjectDataProcess::IsExistComponentInComponentPartNumber(const std::string& component_part_number_, const jrsdata::Component::Type& type_)
    {
        if (type_ == jrsdata::Component::Type::CAD)
        {
            for (const auto& subboard : _param->board_info.sub_board)
            {
                auto it = std::find_if(subboard.component_info.begin(), subboard.component_info.end(),
                    [&](const auto& component)
                    {
                        return component.component_part_number == component_part_number_;
                    });
                if (it != subboard.component_info.end())
                {
                    return true;
                }
            }
        }
        else if (type_ == jrsdata::Component::Type::BARCODE)
        {
            //检索 MARK
            auto it = std::find_if(_param->board_info.barcodes.begin(), _param->board_info.barcodes.end(),
                [&](const auto& component)
                {
                    return component.component_part_number == component_part_number_;
                });
            if (it != _param->board_info.barcodes.end())
            {
                return true;
            }
        }
        else if (type_ == jrsdata::Component::Type::MARK)
        {
            //检索 MARK
            auto it = std::find_if(_param->board_info.marks.begin(), _param->board_info.marks.end(),
                [&](const auto& component)
                {
                    return component.component_part_number == component_part_number_;
                });
            if (it != _param->board_info.marks.end())
            {
                return true;
            }
        }
        else if (type_ == jrsdata::Component::Type::SUB_MARK)
        {
            //检索 sub_MARK
            for (const auto& subboard : _param->board_info.sub_board)
            {
                auto it = std::find_if(subboard.sub_mark.begin(), subboard.sub_mark.end(),
                    [&](const auto& component)
                    {
                        return component.component_part_number == component_part_number_;
                    });
                if (it != subboard.sub_mark.end())
                {
                    return true;
                }
            }
        }
        else if (type_ == jrsdata::Component::Type::SUB_BARCODE)
        {
            auto it = std::find_if(_param->board_info.sub_board.begin(), _param->board_info.sub_board.end(),
                [&](const auto& subboard)
                {
                    return subboard.barcode.component_part_number == component_part_number_;
                });
            if (it != _param->board_info.sub_board.end())
            {
                return true;
            }
        }
        else if (type_ == jrsdata::Component::Type::SUB_BADMARK)
        {
            auto it = std::find_if(_param->board_info.sub_board.begin(), _param->board_info.sub_board.end(),
                [&](const auto& subboard)
                {
                    return subboard.bad_mark.component_part_number == component_part_number_;
                });
            if (it != _param->board_info.sub_board.end())
            {
                return true;
            }
        }


        return false;
    }

    std::vector<jrsdata::BriefComponentInfo> ProjectDataProcess::ReadAllBriefComponentInfo()
    {
        std::vector<jrsdata::BriefComponentInfo> brief_components;
        if (IsParamPtrEmpty())
        {
            return brief_components; //指针为空，请配置参数
        }
        auto components = ReadAllComponent();

        for (auto& component : components)
        {
            jrsdata::BriefComponentInfo brief_component;
            auto component_unit = ReadComponentBodyRef(component.get().component_part_number);
            if (!component_unit.has_value())
            {
                continue;
            }
            brief_component.component_name = component.get().component_name;
            brief_component.x = component.get().x;
            brief_component.y = component.get().y;
            brief_component.angle = component.get().angle;
            brief_component.width = component_unit->get().width;
            brief_component.height = component_unit->get().height;
            brief_components.emplace_back(brief_component);
        }
        return brief_components;
    }

    std::vector<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadAllComponent()
    {

        std::vector<std::reference_wrapper<jrsdata::Component>> components;
        if (IsParamPtrEmpty())
        {
            return components; //指针为空，请配置参数
        }
        components.insert(components.end(), _param->board_info.barcodes.begin(), _param->board_info.barcodes.end());
        components.insert(components.end(), _param->board_info.marks.begin(), _param->board_info.marks.end());
        components.insert(components.end(), _param->board_info.carrier_barcodes.begin(), _param->board_info.carrier_barcodes.end());
        components.insert(components.end(), _param->board_info.cover_plate_barcodes.begin(), _param->board_info.cover_plate_barcodes.end());
        for (auto& subboard_value : _param->board_info.sub_board)
        {
            components.insert(components.end(), subboard_value.component_info.begin(), subboard_value.component_info.end());
            components.insert(components.end(), subboard_value.sub_mark.begin(), subboard_value.sub_mark.end());
            components.emplace_back(subboard_value.bad_mark);
            components.emplace_back(subboard_value.barcode);
        }
        return components;
    }

    int ProjectDataProcess::DeleteComponent(const std::string& component_name, const std::string& subboard_name, jrsdata::Component::Type type)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::CoreError::E_AOI_CORE_PARAM_NULL;
        }
        if (component_name.empty())
        {
            return 1;
        }

        switch (type)
        {
        case Component::Type::CAD:
        {
            return DeleteCAD(subboard_name, component_name);
        }
        break;
        case Component::Type::SUB_MARK:
        {
            return DeleteSubMark(subboard_name, component_name);
        }
        break;
        case Component::Type::SUB_BARCODE:
        {
            return DeleteSubBoardBarcode(subboard_name);
        }
        break;
        case Component::Type::SUB_BADMARK:
        {
            return DeleteSubBadMark(subboard_name);
        }
        break;
        case Component::Type::MARK:
        case Component::Type::BARCODE:
        case Component::Type::BADMARK:
        case Component::Type::CARRIER_BARCODE:
        case Component::Type::COVERPLATE_BARCODE:
        {
            return DeleteComponent(component_name, type);
        }
        break;
        }

        return 2;
    }

    int ProjectDataProcess::DeleteComponent(const std::string& component_name, jrsdata::Component::Type type)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::CoreError::E_AOI_CORE_PARAM_NULL;
        }
        switch (type)
        {
        case Component::Type::CAD:
        case Component::Type::SUB_MARK:
        case Component::Type::SUB_BARCODE:
        case Component::Type::SUB_BADMARK:
        {
            return 1;
        }
        break;
        case Component::Type::MARK:
        {
            return DeleteMark(component_name);
        }
        break;
        case Component::Type::BARCODE:
        {
            return DeleteBarcode(component_name);
        }
        break;
        case Component::Type::BADMARK:
        case Component::Type::CARRIER_BARCODE:
        case Component::Type::COVERPLATE_BARCODE:
        {
            return 3;
        }
        break;
        }
        return 2;
    }


    int ProjectDataProcess::CreateComponentUnit(jrsdata::ComponentUnit& unit, const std::string& part_number_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 1;
        }
        if (unit.unit_group_name.empty())
        {
            return 2;
        }

        auto& units = part->get().units;
        if (unit.unit_type == jrsdata::ComponentUnit::Type::BODY)
        {
            unit.unit_name = "body"; // 默认名称
            auto it = std::find_if(units.begin(), units.end(),
                [&](const jrsdata::ComponentUnit& elem) { return elem.unit_type == unit.unit_type; });
            if (it == units.end())
            {
                units.insert(units.begin(), unit); // 不存在则插入首位
            }
            else
            {
                *it = unit; // 否则替换
            }
        }
        else
        {
            std::vector<int> used_ids;  // 存储已使用的id
            for (const auto& elem : units)
            {
                if (elem.unit_type == unit.unit_type && elem.unit_group_name == unit.unit_group_name)
                {
                    used_ids.push_back(elem.id);
                }
            }

            // 找到当前最大id
            int max_id = 0;
            if (!used_ids.empty())
            {
                max_id = *std::max_element(used_ids.begin(), used_ids.end());
            }

            // 新的id从max_id + 1开始
            unit.id = max_id + 1;

            /**<镜像pad使用 unit 的原始 id*/
            if (unit.pad_type == jrsdata::ComponentUnit::PadType::MIRROR)
            {
                unit.show_id = unit.id;
            }

            unit.unit_name = std::to_string(static_cast<int>(unit.unit_type))  //unit_type_unitgroup_name_id;
                + "_" + unit.unit_group_name + "_" + std::to_string(unit.id);
            units.push_back(unit);
        }

        auto& models = part->get().detect_models;
        models.try_emplace(unit.unit_group_name, jrsdata::DetectModel()); // 创建默认模型
        return 0;
    }

    int ProjectDataProcess::UpdateComponentUnit(jrsdata::ComponentUnit& unit, const std::string& part_number_name)
    {

        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 1;
        }
        {
            auto& units = part->get().units;
            std::shared_lock<std::shared_mutex> lck(mutex);
            auto it = std::find_if(units.begin(), units.end(),
                [&](const jrsdata::ComponentUnit& elem) { return elem.unit_name == unit.unit_name; });
            if (it != units.end())
            {
                *it = unit;
            }
            else if (!unit.unit_name.empty())
            {
                units.emplace_back(unit);
            }

        }
        return 0;
    }

    std::optional<std::reference_wrapper<jrsdata::ComponentUnit>>
        ProjectDataProcess::ReadComponentUnitRef(const std::string& part_number_name, const std::string& unit_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return std::nullopt;
        }
        auto& units = part->get().units;
        std::shared_lock<std::shared_mutex> lck(mutex);
        auto it = std::find_if(units.begin(), units.end(),
            [&](const jrsdata::ComponentUnit& elem) { return elem.unit_name == unit_name; });
        if (it == units.end())
        {
            return std::nullopt;
        }
        return std::ref(*it);
    }


    std::optional<jrsdata::ComponentUnit> ProjectDataProcess::ReadComponentUnitByShowID(const std::string& part_number_name_, const std::string& model_name_, const int& show_id_)
    {
        auto part = ReadPNDetectInfoRef(part_number_name_);
        if (!part.has_value())
        {
            return std::nullopt;
        }
        auto& units = part->get().units;
        std::shared_lock<std::shared_mutex> lck(mutex);
        auto it = std::find_if(units.begin(), units.end(),
            [&](const jrsdata::ComponentUnit& elem) { return ((elem.show_id == show_id_) && (elem.unit_group_name == model_name_)); });
        if (it == units.end())
        {
            return std::nullopt;
        }
        return *it;
    }

    int ProjectDataProcess::UpdateComponentUnitPositionAndSize(const std::string& part_number_name, const std::string& unit_name, const cv::Point2f& center_point_, const cv::Size2f& unint_size_)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            Log_ERROR("未找到料号：", part_number_name, "更新unit失败");
            return -1;
        }
        auto& units = part->get().units;
        for (auto& unit : units)
        {
            if (unit.unit_name == unit_name)
            {
                if (unit.unit_type == jrsdata::ComponentUnit::Type::BODY)
                {
                    unit.width = unint_size_.width;
                    unit.height = unint_size_.height;
                }
                else
                {
                    unit.x = center_point_.x;
                    unit.y = center_point_.y;
                    unit.width = unint_size_.width;
                    unit.height = unint_size_.height;
                }

            }
        }
        return jrscore::AOI_OK;
    }

    std::optional<std::vector<jrsdata::ComponentUnit>> ProjectDataProcess::ReadComponentUnitsBySameGroupName(
        const std::string& component_name,
        const std::string& subboard_name,
        const std::string& unit_name,
        jrsdata::Component::Type type)
    {
        std::vector<jrsdata::ComponentUnit> component_units_;

        // 获取组件引用
        auto component_ref_ = ReadComponentRef(component_name, subboard_name, type);
        if (!component_ref_)
        {
            return std::nullopt;
        }

        const std::string& part_number_ = component_ref_->get().component_part_number;

        // 获取组件单元信息
        auto unit_ = ReadComponentUnit(part_number_, unit_name);
        if (!unit_)
        {
            return std::nullopt;
        }

        // 获取检测信息
        auto part_ = ReadPNDetectInfoRef(part_number_);
        if (!part_)
        {
            return std::nullopt;
        }

        component_units_.emplace_back(*unit_);
        const auto& units_ = part_->get().units;

        {
            std::shared_lock<std::shared_mutex> lock_(mutex);
            for (const auto& unit_temp_ : units_)
            {
                if (unit_temp_.unit_group_name == unit_->unit_group_name &&
                    unit_temp_.unit_name != unit_->unit_name)
                {
                    component_units_.emplace_back(unit_temp_);
                }
            }
        }

        return component_units_;
    }

    std::optional<jrsdata::ComponentUnit> ProjectDataProcess::ReadComponentUnit(const std::string& part_number_name, const std::string& unit_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return std::nullopt;
        }
        auto& units = part->get().units;
        std::shared_lock<std::shared_mutex> lck(mutex);
        auto it = std::find_if(units.begin(), units.end(),
            [&](const jrsdata::ComponentUnit& elem) { return elem.unit_name == unit_name; });
        if (it == units.end())
        {
            return std::nullopt;
        }
        return *it;
    }
    std::optional<std::reference_wrapper<jrsdata::ComponentUnit>> ProjectDataProcess::ReadComponentBodyRef(const std::string& part_number_name)
    {
        return ReadComponentUnitRef(part_number_name, "body");
    }

    std::optional<jrsdata::ComponentUnit> ProjectDataProcess::ReadComponentUnit(const std::string& component_name,
        const std::string& subboard_name, const std::string& unit_name, jrsdata::Component::Type type)
    {
        auto component_ref = ReadComponentRef(component_name, subboard_name, type);
        if (component_ref.has_value())
        {
            return ReadComponentUnit(component_ref.value().get().component_part_number, unit_name);
        }
        return std::optional<jrsdata::ComponentUnit>();
    }

    std::vector<jrsdata::ComponentUnit> ProjectDataProcess::ReadComponentUnit(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return {};
        }
        auto& units = part->get().units;
        std::shared_lock<std::shared_mutex> lck(mutex);
        std::vector<jrsdata::ComponentUnit> find_units;
        for (const auto& unit : units)
        {
            if (unit.unit_type == type)
            {
                find_units.push_back(unit);
            }
        }
        return find_units;
    }

    int ProjectDataProcess::DeleteComponentUnit(const std::string& part_number_name, const std::string& unit_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 1;
        }
        auto& units = part->get().units;
        {
            // 删除所有 unit_name 相同的元素
            auto it = std::remove_if(units.begin(), units.end(),
                [&](const jrsdata::ComponentUnit& elem) { return elem.unit_name == unit_name; });

            // 如果有符合条件的元素，执行删除
            if (it != units.end())
            {
                units.erase(it, units.end());
            }
            else
            {
                return 2;
            }
        }
        // 删除无效的model
        auto& map = part->get().detect_models;
        for (auto it = map.begin(); it != map.end(); )
        {
            auto it_unit = std::find_if(units.begin(), units.end(),
                [&](const jrsdata::ComponentUnit& elem) { return elem.unit_group_name == it->first; });

            if (it_unit == units.end())
            {
                it = map.erase(it);  // 删除并更新迭代器
            }
            else
            {
                ++it;  // 向前移动迭代器
            }
        }
        return 0;
    }

    int ProjectDataProcess::DeleteComponentUnit(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 1;
        }
        auto& units = part->get().units;
        units.erase(std::remove_if(units.begin(), units.end(), [&](const jrsdata::ComponentUnit& elem)
            {
                return elem.unit_type == type;
            }), units.end());

        // 删除无效的model
        auto& map = part->get().detect_models;
        for (auto it = map.begin(); it != map.end(); )
        {
            auto it_unit = std::find_if(units.begin(), units.end(),
                [&](const jrsdata::ComponentUnit& elem) { return elem.unit_group_name == it->first; });

            if (it_unit == units.end())
            {
                it = map.erase(it);  // 删除并更新迭代器
            }
            else
            {
                ++it;  // 向前移动迭代器
            }
        }
        return 0;
    }




    std::vector<jrsdata::ComponentUnit> ProjectDataProcess::ReadComponentUnitGroup(const std::string& part_number_name, const std::string& unit_group_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return {};
        }
        auto& units = part->get().units;
        std::shared_lock<std::shared_mutex> lck(mutex);
        std::vector<jrsdata::ComponentUnit> find_units;
        for (const auto& unit : units)
        {
            if (unit.unit_group_name == unit_group_name)
            {
                find_units.push_back(unit);
            }
        }
        return find_units;
    }

    int ProjectDataProcess::DeleteComponentUnitGroup(const std::string& part_number_name, const std::string& unit_group_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 1;
        }
        auto& units = part->get().units;
        units.erase(std::remove_if(units.begin(), units.end(), [&](const jrsdata::ComponentUnit& elem)
            {
                return elem.unit_group_name == unit_group_name;
            }), units.end());
        return 0;
    }

    int ProjectDataProcess::GetComponentUnitMaxId(const std::string& part_number_name)
    {
        auto part = ReadPNDetectInfoRef(part_number_name);
        if (!part.has_value())
        {
            return 0;
        }
        const auto& units = part->get().units;
        if (units.empty())
        {
            return 0;
        }
        auto max_unit = std::max_element(units.begin(), units.end(),
            [](const jrsdata::ComponentUnit& a, const jrsdata::ComponentUnit& b) { return a.id < b.id; });
        return max_unit->id;
    }

    std::string ProjectDataProcess::GetComponentUnitNewGroupName(const std::string& part_number_name, const jrsdata::ComponentUnit::Type& type)
    {
        auto component_pad_units = ReadComponentUnit(part_number_name, type);
        std::string prefix_str = "";
        int group_max = 0;
        for (auto component_pad_unit : component_pad_units)
        {
            auto prefix_number = jtools::StringOperation::GetPrefixAndNumber(component_pad_unit.unit_group_name);
            if (prefix_str.empty())
            {
                prefix_str = prefix_number.first;
            }
            if (group_max < prefix_number.second)
            {
                group_max = prefix_number.second;
            }
        }
        return prefix_str + std::to_string(group_max + 1);
    }

    int ProjectDataProcess::GetComponentNameFromPartNumber(const std::string& part_number_name, std::string& component_name, std::string& subboard_name)
    {
        const auto& sub_boards = _param->board_info.sub_board;
        for (const auto& sub_board : sub_boards)
        {
            for (const auto& component : sub_board.component_info)
            {
                if (component.component_part_number == part_number_name)
                {
                    component_name = component.component_name;
                    subboard_name = component.subboard_name;
                    return 0;  // 找到直接返回
                }
            }
        }
        return -1;  // 没找到返回错误码
    }


    int ProjectDataProcess::GetSubboardMaxId()
    {
        if (IsParamPtrEmpty())
        {
            return INT_MIN; //指针为空，请配置参数
        }
        int max_id = 0;
        for (auto subboard : _param->board_info.sub_board)
        {
            if (subboard.id > max_id)
            {
                max_id = subboard.id;
            }
        }
        return max_id;
    }



    int ProjectDataProcess::GetSubboardUnusedID()
    {
        if (IsParamPtrEmpty())
        {
            return INT_MIN;
        }
        std::set<int> used_ids;
        for (const auto& subboard : _param->board_info.sub_board)
        {
            used_ids.insert(subboard.id);
        }
        // 从1开始找最小未使用的ID
        int id = 1;
        while (used_ids.count(id))
        {
            ++id;
        }
        return id;
    }

    std::optional<std::reference_wrapper<jrsdata::Board>> ProjectDataProcess::ReadBoard()
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        return std::ref(board);

    }

    int ProjectDataProcess::CreateSubBoard(const float& cx_, const float& cy_, const float& width_, const float& height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        jrsdata::SubBoard subboard;
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        subboard.x = cx_;
        subboard.y = cy_;
        subboard.width = width_;
        subboard.height = height_;
        subboard.id = GetSubboardUnusedID(); // 设置新的id
        subboard.subboard_name = this->_param->project_name + "_" + std::to_string(subboard.id); // 强制设置新的名称,保证不会冲突
        board.sub_board.push_back(subboard);
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::CreateSubBoard(jrsdata::SubBoard& subBoard)
    {

        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;

        subBoard.id = GetSubboardUnusedID(); // 设置新的id
        subBoard.subboard_name = this->_param->project_name + "_" + std::to_string(subBoard.id); // 强制设置新的名称,保证不会冲突
        board.sub_board.push_back(subBoard);

        return jrscore::AOI_OK;
    }

    std::optional<jrsdata::SubBoard> ProjectDataProcess::ReadSubBoard(int subBoardId)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;

        auto it = std::find_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& ele) { return ele.id == subBoardId; });
        if (it != board.sub_board.end())
        {
            return *it;
        }

        return std::nullopt; // 未找到
    }

    std::optional<std::reference_wrapper<jrsdata::SubBoard>> ProjectDataProcess::ReadSubBoardRef(const int& subBoardId)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;

        auto it = std::find_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& ele) { return ele.id == subBoardId; });
        if (it != board.sub_board.end())
        {
            return std::ref(*it);
        }

        return std::nullopt; // 未找到
    }

    std::optional<jrsdata::SubBoard> ProjectDataProcess::ReadSubBoard(const std::string& name)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        //std::shared_lock<std::shared_mutex> lck(mutex);
        auto& sub_board = _param->board_info.sub_board;
        auto it = std::find_if(sub_board.begin(), sub_board.end(),
            [&](const jrsdata::SubBoard& ele) { return ele.subboard_name == name; });
        if (it != sub_board.end())
        {
            return *it;
        }

        return std::nullopt; // 未找到
    }

    std::optional<std::reference_wrapper<jrsdata::SubBoard>> ProjectDataProcess::ReadSubBoardRef(const std::string& name)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        //std::shared_lock<std::shared_mutex> lck(mutex);
        auto& sub_board = _param->board_info.sub_board;
        auto it = std::find_if(sub_board.begin(), sub_board.end(),
            [&](const jrsdata::SubBoard& ele) { return ele.subboard_name == name; });
        if (it != sub_board.end())
        {
            return std::ref(*it);
        }

        return std::nullopt; // 未找到
    }

    int ProjectDataProcess::UpdateSubBoard(const jrsdata::SubBoard& new_subboard_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; // 指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lock(mutex);

        auto& subboards = _param->board_info.sub_board;
        for (auto& sub : subboards)
        {
            if (sub.id == new_subboard_.id)
            {
                sub = new_subboard_;
                return jrscore::AOI_OK;
            }
        }

        return -1; // 建议定义这个错误码
    }
    jrsdata::SubBoard ProjectDataProcess::CopySubboard(const jrsdata::SubBoard& subboard_, const cv::Point2d& subboard_center_point_offset_)
    {
        jrsdata::SubBoard new_sub(subboard_);
        int current_subboard_id = this->GetSubboardMaxId();
        new_sub.id = ++current_subboard_id;
        new_sub.subboard_name = this->GetProjectName() + "_" + std::to_string(current_subboard_id);
        new_sub.x += subboard_center_point_offset_.x;
        new_sub.y += subboard_center_point_offset_.y;
        //!创建临时图形！
        auto new_sub_id_str = std::to_string(new_sub.id);

        float xoffset = subboard_.x - new_sub.x;
        float yoffset = subboard_.y - new_sub.y;

        // 元件拷贝
        for (auto& c : new_sub.component_info)
        {
            c.subboard_name = new_sub.subboard_name;
            c.x -= xoffset;
            c.y -= yoffset;

            auto& name = c.component_name; // 元件id需要外部生成
            name = jrscore::AOITools::GetPrefixString(name) + "_" + new_sub_id_str;
        }
        /** <拷贝mark信息 */
        for (auto& mark : new_sub.sub_mark)
        {
            mark.subboard_name = new_sub.subboard_name;
            this->GenerateComponentNameBySubboardID(mark, new_sub.id);
            mark.x -= xoffset;
            mark.y -= yoffset;
        }
        /** <拷贝barcode信息 */
        new_sub.barcode.x -= xoffset;
        new_sub.barcode.y -= yoffset;
        new_sub.barcode.subboard_name = new_sub.subboard_name;
        this->GenerateComponentNameBySubboardID(new_sub.barcode, new_sub.id);

        /** <拷贝badmark */
        new_sub.bad_mark.x -= xoffset;
        new_sub.bad_mark.y -= yoffset;
        new_sub.bad_mark.subboard_name = new_sub.subboard_name;
        this->GenerateComponentNameBySubboardID(new_sub.bad_mark, new_sub.id);
        return new_sub;
    }

    std::unordered_map<std::string, std::string >  ProjectDataProcess::UpdateComponentAnglesAndEnablesOfDifferentSubboards(const std::string& src_component_name_,
        const std::string& src_subboard_name_, const jrsdata::Component::Type& type_,
        float angle, bool enable)
    {
        std::unordered_map<std::string, std::string > updated_subboards_and_components;
        for (auto& subboard : _param->board_info.sub_board)
        {
            if (subboard.subboard_name == src_subboard_name_)/***<相似不同子板的原件信息同步 */
            {
                continue;
            }
            std::string component_name = jrscore::AOITools::GetPrefixString(src_component_name_)
                + "_" + std::to_string(subboard.id);
            auto component = ReadComponentRef(component_name, subboard.subboard_name, type_);
            if (!component.has_value())
            {
                continue;
            }
            double angle_val = component->get().angle + angle;
            jrscore::AOITools::NormalizeAngle(angle_val);
            component->get().angle = angle_val;  //更新角度信息
            component->get().enable = enable;/**< 更新是否检测*/
            updated_subboards_and_components[subboard.subboard_name] = component_name;
        }
        return updated_subboards_and_components;
    }

    int ProjectDataProcess::UpdateSubboard(const std::string& subboard_name_, float cx_offset_, float cy_offset_, float width_, float height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::CoreError::E_AOI_CORE_PARAM_NULL;
        }
        std::string origin_subboard_name = jrscore::AOITools::GetPrefixString(subboard_name_);
        for (auto& subboard : _param->board_info.sub_board)
        {
            if (subboard_name_ == subboard.subboard_name)/**< 更新相同子板区域位置*/
            {
                continue;
            }
            if (origin_subboard_name + "_" + std::to_string(subboard.id) == subboard.subboard_name)
            {
                subboard.x += cx_offset_;
                subboard.y += cy_offset_;
                subboard.width = width_;
                subboard.height = height_;
            }
        }
        return 0;
    }

    int ProjectDataProcess::ReplaceSubboard(const jrsdata::SubBoard& new_subboard_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::CoreError::E_AOI_CORE_PARAM_NULL;
        }
        if (UpdateSubBoard(new_subboard_) == jrscore::AOI_OK)
        {
            return jrscore::AOI_OK;
        }
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        board.sub_board.push_back(new_subboard_);
        board.num_sub_board = (int)board.sub_board.size();
        return 0;
    }

    int ProjectDataProcess::UpdateSubBoardSize(jrsdata::SubBoard& subboard)
    {
        if (subboard.component_info.empty()) {
            subboard.x = 0;
            subboard.y = 0;
            subboard.width = 0;
            subboard.height = 0;
            return 0;
        }

        float xmin = FLT_MAX, xmax = -FLT_MAX, ymin = FLT_MAX, ymax = -FLT_MAX;

        for (const auto& c : subboard.component_info) {
            // 获取组件的PN信息
            auto pn = ReadPNDetectInfoRef(c.component_part_number);
            if (!pn.has_value()) {
                return 1;
            }
            auto& units = pn->get().units;

            // 计算当前元件的最大外接矩形
            float xmin_c = FLT_MAX, xmax_c = -FLT_MAX, ymin_c = FLT_MAX, ymax_c = -FLT_MAX;
            for (const auto& unit : units)
            {
                float half_width = unit.width / 2.0f;
                float half_height = unit.height / 2.0f;

                float left = unit.x - half_width;
                float right = unit.x + half_width;
                float bottom = unit.y - half_height;
                float top = unit.y + half_height;

                xmin_c = std::min(xmin_c, left);
                xmax_c = std::max(xmax_c, right);
                ymin_c = std::min(ymin_c, bottom);
                ymax_c = std::max(ymax_c, top);
            }

            auto center_x = (xmin_c + xmax_c) / 2.f;
            auto center_y = (ymin_c + ymax_c) / 2.f;
            auto w = xmax_c - xmin_c;
            auto h = ymax_c - ymin_c;

            // 计算当前元件外接矩形的旋转包围框
            auto rect = cv::RotatedRect(
                cv::Point2f(static_cast<float>(c.x) + center_x, static_cast<float>(c.y) + center_y),
                cv::Size2f(static_cast<float>(w), static_cast<float>(h)), c.angle).boundingRect2f();

            // 更新总的最小/最大边界
            xmin = std::min(xmin, rect.x);
            xmax = std::max(xmax, rect.x + rect.width);
            ymin = std::min(ymin, rect.y);
            ymax = std::max(ymax, rect.y + rect.height);
        }

        // 计算最终的bounding box中心和尺寸
        float center_x = (xmax + xmin) / 2.f;
        float center_y = (ymax + ymin) / 2.f;
        float w = xmax - xmin;
        float h = ymax - ymin;

        // 更新subboard的位置和尺寸
        subboard.x = static_cast<int>(center_x);
        subboard.y = static_cast<int>(center_y);
        subboard.width = static_cast<int>(w);
        subboard.height = static_cast<int>(h);

        return 0;
    }

    int ProjectDataProcess::DeleteSubBoard(int subBoardId)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        auto it = std::remove_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& sub) { return sub.id == subBoardId; });

        if (it != board.sub_board.end())
        {
            board.sub_board.erase(it, board.sub_board.end());
            return jrscore::AOI_OK;
        }
        return 1;
    }

    int ProjectDataProcess::DeleteSubBoard(const std::string& subBoardName)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        auto it = std::remove_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& sub) { return sub.subboard_name == subBoardName; });

        if (it != board.sub_board.end() && board.sub_board.size() > 1)
        {
            board.sub_board.erase(it, board.sub_board.end());
            return jrscore::AOI_OK;
        }
        return 1;
    }

    int ProjectDataProcess::ClearSubBoard(int subBoardId)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        auto it = std::remove_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& sub) { return sub.id == subBoardId; });

        if (it != board.sub_board.end())
        {
            // 清空元件信息
            it->component_info.clear();
            return jrscore::AOI_OK;
        }
        return 1;
    }

    int ProjectDataProcess::ClearSubBoard(const std::string& subBoardName)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        auto it = std::remove_if(board.sub_board.begin(), board.sub_board.end(),
            [&](const jrsdata::SubBoard& sub) { return sub.subboard_name == subBoardName; });

        if (it != board.sub_board.end() && board.sub_board.size() > 1)
        {
            // 清空元件信息
            it->component_info.clear();
            return jrscore::AOI_OK;
        }
        return 1;
    }

    std::optional<std::unordered_map<std::string, jrsdata::PNDetectInfo>> ProjectDataProcess::GetPartNumberInfo()
    {
        if (IsParamPtrEmpty())
        {
            return std::optional<std::unordered_map<std::string, PNDetectInfo>>();
        }
        return _param->board_info.part_nums_and_detect_regions;
    }

    int ProjectDataProcess::GetSubBoardIdBySubBoardName(const std::string& sub_board_name)
    {
        for (auto& sub_info : _param->board_info.sub_board)
        {
            if (sub_info.subboard_name == sub_board_name)
            {
                return sub_info.id;
            }
        }
        Log_ERROR("子板名称不存在，未获取到对应子板ID");
        return -1;
    }

    int ProjectDataProcess::CreatePNDetectInfo(const std::string& part_number_name, jrsdata::PNDetectInfo& pn)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& parts = _param->board_info.part_nums_and_detect_regions;
        if (parts.find(part_number_name) != parts.end())
            return 2;
        pn.part_name = part_number_name;
        parts.emplace(part_number_name, pn);
        return jrscore::AOI_OK;
    }

    std::optional<jrsdata::PNDetectInfo> ProjectDataProcess::ReadPNDetectInfo(const std::string& part_number_name)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& parts = _param->board_info.part_nums_and_detect_regions;
        auto it = parts.find(part_number_name);
        if (it == parts.end())
        {
            return std::nullopt;
        }
        return it->second;
    }

    std::optional<std::reference_wrapper<jrsdata::PNDetectInfo>> ProjectDataProcess::ReadPNDetectInfoRef(const std::string& part_number_name)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& parts = _param->board_info.part_nums_and_detect_regions;
        auto it = parts.find(part_number_name);
        if (it != parts.end())
        {
            return std::ref(it->second);
        }
        return std::nullopt;
    }

    int ProjectDataProcess::UpdatePNDetectInfo(const std::string& part_number_name, jrsdata::PNDetectInfo& spec_detect_region)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; // 指针为空，请配置参数
        }
        {

            std::unique_lock<std::shared_mutex> lck(mutex);
            auto& parts = _param->board_info.part_nums_and_detect_regions;
            auto it = parts.find(part_number_name);
            if (it != parts.end())
            {
                it->second = spec_detect_region;
                return jrscore::AOI_OK; // 更新成功
            }
        }
        //Log_WARN("未找到相应的料号：", part_number_name);
        //return -1;
        return CreatePNDetectInfo(part_number_name, spec_detect_region);
    }

    int ProjectDataProcess::UpdatePNDetectModel(const std::string& part_number_name, jrsdata::PNDetectInfo& spec_detect_region)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; // 指针为空，请配置参数
        }
        {
            std::unique_lock<std::shared_mutex> lck(mutex);
            auto& parts = _param->board_info.part_nums_and_detect_regions;
            auto it = parts.find(part_number_name);
            if (it != parts.end())
            {
                // 只更新DetectModel和ComponentUnit
                it->second.detect_models = spec_detect_region.detect_models;
                it->second.units = spec_detect_region.units;
                return jrscore::AOI_OK; // 更新成功
            }
        }

        return CreatePNDetectInfo(part_number_name, spec_detect_region);
    }

    int ProjectDataProcess::DeleteSurplusPNDetectInfo(const std::string& part_number_, const jrsdata::Component::Type& type_)
    {
        if (!IsExistComponentInComponentPartNumber(part_number_, type_))
        {
            //料号下没有相同的元件信息了，料号直接删除
            if (DeletePNDetectInfo(part_number_) != jrscore::AOI_OK)
            {
                Log_WARN("删除料号失败，请检查。");
                return -1;
            };
        }
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::DeletePNDetectInfo(const std::string& part_number_name)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        auto& parts = _param->board_info.part_nums_and_detect_regions;

        auto it = parts.find(part_number_name);
        if (it != parts.end())
        {
            parts.erase(it);
            return jrscore::AOI_OK; //移除成功
        }
        return 1;
    }

    int ProjectDataProcess::UpdateCompoentInfo(const std::string& part_number, jrsdata::PNDetectInfo& info, std::vector<jrsdata::Template>& temp)
    {
        // 收集所有算法指针并初始化模板 ID 替换映射
        std::unordered_map<int, int>temp_id_replace_map;
        std::vector<DetectAlgorithm*> algos;
        for (auto& det_model : info.detect_models)
        {
            for (auto& det_win : det_model.second.detect_model)
            {
                for (auto& algo : det_win.algorithms)
                {
                    algos.push_back(&algo);
                    for (auto& id : algo.template_image_ids)
                    {
                        temp_id_replace_map.insert({ id, id });
                    }
                }
            }
        }

        // 创建模板并更新 ID 映射
        const int invalid_id = -1;
        for (auto& template_item : temp)
        {
            int old_id = template_item.id;
            jrsdata::Template temp_created;

            if (CreateTemplate(template_item.GetMatImage(), template_item.color_params, template_item.light_image_id, temp_created) == 0)
            {
                temp_id_replace_map[old_id] = temp_created.id; // 更新为新 ID
            }
            else
            {
                temp_id_replace_map[old_id] = invalid_id; // 标记为无效 ID
            }
        }

        // 更新算法中的模板 ID
        for (auto& algo : algos)
        {
            for (auto& id : algo->template_image_ids)
            {
                auto it = temp_id_replace_map.find(id);
                if (it != temp_id_replace_map.end())
                {
                    id = it->second;
                }
            }
            // 去除无效 ID
            algo->template_image_ids.erase(
                std::remove_if(
                    algo->template_image_ids.begin(),
                    algo->template_image_ids.end(),
                    [&](int id) { return id == invalid_id; }
                ),
                algo->template_image_ids.end()
            );
        }

        UpdatePNDetectModel(part_number, info);
        return 0;
    }

    std::string ProjectDataProcess::GetModelNameByPartNumberAndDetectWindowName(const std::string& part_number_, const std::string& detect_window_name_)
    {
        auto part_optional = ReadPNDetectInfo(part_number_);
        if (!part_optional.has_value())
            return "";

        for (const auto& [model_name, model_info] : part_optional->detect_models)
        {
            auto it = std::find_if(
                model_info.detect_model.begin(),
                model_info.detect_model.end(),
                [&](const auto& detect_model) {
                    return detect_model.name == detect_window_name_;
                }
            );

            if (it != model_info.detect_model.end())
            {
                return model_name;
            }
        }

        return "";
    }

    int ProjectDataProcess::CreateSpec(const std::string& part_number_name, jrsdata::DetectSpec& spec)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;

        auto& specs = part_optional->specs;
        if (spec.spec_name.empty()) // 生成新名字
        {
            int id = (int)specs.size() + 1;
            spec.spec_name = part_number_name + "_spec_" + std::to_string(id);
        }
        else
        {
            auto it = std::find_if(specs.begin(), specs.end(), [spec](const jrsdata::DetectSpec& elem)
                {
                    return elem.spec_name == spec.spec_name;
                });
            if (it != specs.end())
                return 1;
        }
        specs.push_back(spec);
        return UpdatePNDetectInfo(part_number_name, *part_optional);
    }

    std::optional<jrsdata::DetectSpec> ProjectDataProcess::ReadSpec(const std::string& part_number_name, const std::string& spec_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return std::nullopt;
        if (spec_name.empty())
            return std::nullopt;

        auto& specs = part_optional->specs;

        for (auto& spec : specs)
        {
            if (spec.spec_name == spec_name)
            {
                return spec;
            }
        }
        return std::nullopt;
    }

    int ProjectDataProcess::UpdateSpec(const std::string& part_number_name, jrsdata::DetectSpec& update_spec)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;
        if (update_spec.spec_name.empty())
            return 2;

        for (auto& spec : part_optional->specs)
        {
            if (spec.spec_name == update_spec.spec_name)
            {
                spec = update_spec;
                return 0;
            }
        }
        return CreateSpec(part_number_name, update_spec);
    }

    int ProjectDataProcess::DeleteSpec(const std::string& part_number_name, const std::string& spec_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;
        auto& specs = part_optional->specs;
        auto it = std::remove_if(specs.begin(), specs.end(),
            [&](const jrsdata::DetectSpec& spec) { return spec.spec_name == spec_name; });

        if (it != specs.end())
        {
            specs.erase(it, specs.end());
            return UpdatePNDetectInfo(part_number_name, *part_optional);
        }
        return 0;
    }

    int ProjectDataProcess::CreateDetectWindow(
        const std::string& model_name,
        const std::string& part_number_name,
        jrsdata::DetectWindow& detect_window_)
    {
        // 获取零件信息
        auto part_info_opt = ReadPNDetectInfoRef(part_number_name);
        if (!part_info_opt)
        {
            Log_ERROR("Part number ", part_number_name, " not found.");
            return 2;
        }

        auto& detect_models = part_info_opt->get().detect_models;

        // 查找或创建检测模型
        auto [model_iter, inserted] = detect_models.try_emplace(model_name, jrsdata::DetectModel());

        auto& models = model_iter->second.detect_model;

        // 如果检测窗口名称为空，则自动生成
        if (detect_window_.name.empty())
        {

            int new_id = 1;
            if (!models.empty())
            {
                auto max_it = std::max_element(models.begin(), models.end(),
                    [](const jrsdata::DetectWindow& a, const jrsdata::DetectWindow& b) {
                        return a.id < b.id;
                    });
                new_id = max_it->id + 1;
            }
            detect_window_.id = new_id;
            detect_window_.name = part_number_name + "_" + model_name + "_window_" + std::to_string(new_id);
        }
        else
        {
            // 确保名称唯一
            auto it_ = std::find_if(models.begin(), models.end(),
                [&](const jrsdata::DetectWindow& elem_) { return elem_.name == detect_window_.name; });

            if (it_ != models.end())
            {
                Log_ERROR("Detect window ", detect_window_.name, " already exists.");
                return 1;
            }
        }

        // 插入新的检测窗口
        models.emplace_back(std::move(detect_window_));

        return 0;
    }

    std::optional<jrsdata::DetectWindow> ProjectDataProcess::ReadDetectWindow(const std::string& model_name, const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return std::nullopt;
        auto& models = part_optional->detect_models;
        auto model_it = models.find(model_name);
        if (model_it == models.end())
            return std::nullopt;
        if (window_name.empty())
            return std::nullopt;
        auto& model = model_it->second.detect_model;
        auto it = std::find_if(model.begin(), model.end(), [window_name](const jrsdata::DetectWindow& elem)
            {
                return elem.name == window_name;
            });
        if (it == model.end())
            return std::nullopt;
        return *it;
    }

    int ProjectDataProcess::UpdateDetectWindow(const std::string& model_name, const std::string& part_number_name, jrsdata::DetectWindow& det_window)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;
        auto& models = part_optional->detect_models;
        auto model_it = models.find(model_name);
        if (model_it == models.end())
            return 2;
        auto& window_name = det_window.name;
        if (window_name.empty())
            return CreateDetectWindow(model_name, part_number_name, det_window);

        auto& model = model_it->second.detect_model;
        auto it = std::find_if(model.begin(), model.end(), [window_name](const jrsdata::DetectWindow& elem)
            {
                return elem.name == window_name;
            });
        if (it == model.end())
            return CreateDetectWindow(model_name, part_number_name, det_window);
        *it = det_window;
        return UpdatePNDetectInfo(part_number_name, *part_optional);
    }

    int ProjectDataProcess::DeleteDetectWindow(const std::string& model_name, const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;
        auto& models = part_optional->detect_models;
        auto model_it = models.find(model_name);
        if (model_it == models.end())
            return 2;
        auto& model = model_it->second.detect_model;

        auto it = std::remove_if(model.begin(), model.end(),
            [&](const jrsdata::DetectWindow& elem) { return elem.name == window_name; });

        if (it != model.end())
        {
            model.erase(it, model.end());
            return UpdatePNDetectInfo(part_number_name, *part_optional);
        }
        return 0;
    }

    int ProjectDataProcess::DeleteDetectWindowAndTemplate(const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return 2;
        if (window_name.empty())
            return 2;
        auto& models = part_optional->detect_models;

        std::set<int> template_ids;
        for (auto& iter : models)
        {
            auto& model = iter.second.detect_model;

            DeleteDisableDetectWindow(model); //删除无效detectwindow

            auto it = std::remove_if(model.begin(), model.end(),
                [&](const jrsdata::DetectWindow& elem)
                {
                    if (elem.name == window_name)
                    {
                        for (const auto& algo : elem.algorithms)
                        {
                            template_ids.insert(algo.template_image_ids.begin(), algo.template_image_ids.end());
                        }
                        return true;
                    }
                    return false;
                });

            if (it != model.end())
            {
                model.erase(it, model.end());
                for (auto id : template_ids)
                {
                    DeleteTemplate(id);
                }
                return UpdatePNDetectInfo(part_number_name, *part_optional);
            }
        }
        return 1;
    }

    int ProjectDataProcess::DeleteDisableDetectWindow(std::vector<jrsdata::DetectWindow>& detect_windows_)
    {
        detect_windows_.erase(
            std::remove_if(detect_windows_.begin(), detect_windows_.end(),
                [](const jrsdata::DetectWindow& win) {
                    return(win.width == 0 && win.height == 0 &&
                        win.algorithms.empty());
                }),
            detect_windows_.end());
        return jrscore::AOI_OK;
    }

    std::optional<jrsdata::DetectWindow> ProjectDataProcess::ReadDetectWindow(const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfo(part_number_name);
        if (!part_optional.has_value())
            return std::nullopt;
        if (window_name.empty())
            return std::nullopt;
        auto& models = part_optional->detect_models;

        for (auto& iter : models)
        {
            for (auto& elem : iter.second.detect_model)
            {
                if (elem.name == window_name)
                {
                    return elem;
                }
            }
        }
        return std::nullopt;
    }

    std::optional<std::reference_wrapper<jrsdata::DetectWindow>> ProjectDataProcess::ReadDetectWindowRef(const std::string& model_name, const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfoRef(part_number_name);
        if (!part_optional.has_value())
            return std::nullopt;
        auto& models = part_optional->get().detect_models;
        auto model_it = models.find(model_name);
        if (model_it == models.end())
            return std::nullopt;
        if (window_name.empty())
            return std::nullopt;
        auto& model = model_it->second.detect_model;
        auto it = std::find_if(model.begin(), model.end(), [window_name](const jrsdata::DetectWindow& elem)
            {
                return elem.name == window_name;
            });
        if (it == model.end())
            return std::nullopt;
        return std::ref(*it);
    }

    std::optional<std::reference_wrapper<jrsdata::DetectWindow>> ProjectDataProcess::ReadDetectWindowRef(const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfoRef(part_number_name);
        if (!part_optional.has_value())
            return std::nullopt;
        if (window_name.empty())
            return std::nullopt;
        auto& models = part_optional->get().detect_models;

        for (auto& iter : models)
        {
            for (auto& elem : iter.second.detect_model)
            {
                if (elem.name == window_name)
                {
                    return std::ref(elem);
                }
            }
        }
        return std::nullopt;
    }

    int ProjectDataProcess::UpdateDetectWindow(const std::string& part_number_name, jrsdata::DetectWindow& det_window)
    {
        auto part_optional = ReadPNDetectInfoRef(part_number_name);
        if (!part_optional.has_value())
            return 2;
        auto& window_name = det_window.name;
        if (window_name.empty())
            return 2;
        auto& models = part_optional->get().detect_models;

        for (auto& iter : models)
        {
            for (auto& elem : iter.second.detect_model)
            {
                if (elem.name == window_name)
                {
                    elem = det_window;
                    return 0;
                    // return UpdatePNDetectInfo(part_number_name, *part_optional);
                }
            }
        }
        return 1;
    }

    int ProjectDataProcess::DeleteDetectWindow(const std::string& part_number_name, const std::string& window_name)
    {
        auto part_optional = ReadPNDetectInfoRef(part_number_name);
        if (!part_optional.has_value())
            return 2;
        if (window_name.empty())
            return 2;
        auto& models = part_optional->get().detect_models;

        for (auto& iter : models)
        {
            auto& model = iter.second.detect_model;
            auto it = std::remove_if(model.begin(), model.end(),
                [&](const jrsdata::DetectWindow& elem) { return elem.name == window_name; });

            if (it != model.end())
            {
                model.erase(it, model.end());
                return 0;
                // return UpdatePNDetectInfo(part_number_name, *part_optional);
            }
        }
        return 1;
    }

    int ProjectDataProcess::CreateMultiDetectWindows(jrsdata::ComponentEntity& entity, const jrsdata::MultiAlgoParam& multi_algo_param, const std::map<std::string, std::string>& algo_default_param)
    {
        auto ProcessMirrorPad = [&](jrsdata::DetectWindow& detectwindow, jrsdata::ComponentUnit::Direction direction) {
            if (!detectwindow.model_name._Starts_with("pad"))
            {
                return;
            }

            cv::Point2f component_center(0, 0);
            cv::Point2f unit_center(detectwindow.cx, detectwindow.cy);
            cv::RotatedRect detect_window_rotated(
                { detectwindow.cx, detectwindow.cy },
                { detectwindow.width, detectwindow.height },
                0
            );

            auto new_rect = GetRotatedRectByPadDirection(component_center, unit_center, detect_window_rotated, direction);

            detectwindow.cx = new_rect.center.x;
            detectwindow.cy = new_rect.center.y;
            detectwindow.width = new_rect.size.width;
            detectwindow.height = new_rect.size.height;
            };

        auto& detect_models = entity.detect_info.detect_models;
        for (auto& item : detect_models)
        {
            std::vector<std::string> algo_names;
            if (item.first == "body")
            {
                algo_names = multi_algo_param.body_algo_names;
            }
            else if (item.first == "location")
            {
                algo_names = multi_algo_param.location_algo_names;
            }
            else if (item.first._Starts_with("pad"))
            {
                algo_names = multi_algo_param.pad_algo_names;
            }

            for (size_t i = 0; i < algo_names.size(); i++)
            {
                jrsdata::DetectWindow detectwindow;
                detectwindow.width = entity.detect_info.GetUnitWidth(item.first);
                detectwindow.height = entity.detect_info.GetUnitHeight(item.first);
                detectwindow.model_name = item.first;
                detectwindow.defect_name = "缺料";
                detectwindow.group_name = "None";
                detectwindow.enable = true;

                if (item.first._Starts_with("pad"))
                {
                    ProcessMirrorPad(detectwindow, (jrsdata::ComponentUnit::Direction)entity.detect_info.GetUnitDirection(item.first));
                }

                // 算法默认参数添加
                DetectAlgorithm detect_algo;
                detect_algo.detect_algorithm_name = algo_names[i];
                detectwindow.algorithms.push_back(detect_algo);
                detectwindow.algorithms[0].light_image_id = 0;
                ColorParams color_param;
                detectwindow.algorithms[0].color_param = color_param.ToJson();

                auto it = algo_default_param.find(algo_names[i]);
                if (it != algo_default_param.end())
                {
                    detectwindow.algorithms[0].param = algo_default_param.at(algo_names[i]);

                    // 创建子检测框
                    CreateSubDetectWindow(detectwindow, 0, detect_algo.algorithm_detect_windows);

                    // 创建检测框
                    CreateDetectWindow(item.first, entity.part_name, detectwindow);
                }
            }
        }

        return 0;
    }

    int ProjectDataProcess::DeleteAllDetectWindows(jrsdata::ComponentEntity& entity)
    {
        auto& detect_models = entity.detect_info.detect_models;
        for (auto& item : detect_models)
        {
            auto& detect_model = item.second.detect_model;
            for (size_t i = 0; i < detect_model.size(); i++)
            {
                DeleteDetectWindowAndTemplate(entity.part_name, detect_model[i].name);
            }
        }
        return 0;
    }

    int ProjectDataProcess::CreateAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, jrsdata::DetectAlgorithm& algorithm)
    {
        auto window_optional = ReadDetectWindowRef(model_name, part_number_name, window_name);
        if (!window_optional.has_value())
            return 2;
        auto& algorithms = window_optional->get().algorithms;
        if (algorithm.detect_algorithm_name.empty())
        {
            int id = (int)algorithms.size() + 1;
            algorithm.detect_algorithm_name = part_number_name + "_" + window_name + "_algo_" + std::to_string(id);
        }
        else
        {
            auto it = std::find_if(algorithms.begin(), algorithms.end(), [algorithm](const jrsdata::DetectAlgorithm& elem)
                {
                    return elem.detect_algorithm_name == algorithm.detect_algorithm_name;
                });
            if (it != algorithms.end())
                return 1;
        }
        algorithms.emplace_back(algorithm);
        return 0;
        //return CreateDetectWindow(model_name, part_number_name, *window_optional);
    }

    std::optional<jrsdata::DetectAlgorithm> ProjectDataProcess::ReadAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name)
    {
        auto window_optional = ReadDetectWindowRef(model_name, part_number_name, window_name);
        if (!window_optional.has_value())
            return std::nullopt;
        auto& algorithms = window_optional->get().algorithms;

        auto it = std::find_if(algorithms.begin(), algorithms.end(), [algorithm_name](const jrsdata::DetectAlgorithm& elem)
            {
                return elem.detect_algorithm_name == algorithm_name;
            });
        if (it == algorithms.end())
            return std::nullopt;

        return *it;
    }

    std::optional<std::reference_wrapper<jrsdata::DetectAlgorithm>> ProjectDataProcess::ReadAlgorithmRef(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name)
    {
        auto window_optional = ReadDetectWindowRef(model_name, part_number_name, window_name);
        if (!window_optional.has_value())
            return std::nullopt;
        auto& algorithms = window_optional->get().algorithms;

        auto it = std::find_if(algorithms.begin(), algorithms.end(), [algorithm_name](const jrsdata::DetectAlgorithm& elem)
            {
                return elem.detect_algorithm_name == algorithm_name;
            });
        if (it == algorithms.end())
            return std::nullopt;

        return std::ref(*it);
    }

    int ProjectDataProcess::UpdateAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, jrsdata::DetectAlgorithm& algorithm)
    {
        auto window_optional = ReadDetectWindowRef(part_number_name, window_name);
        if (!window_optional.has_value())
            return 2;
        auto& algorithm_name = algorithm.detect_algorithm_name;
        if (algorithm_name.empty())
            return CreateAlgorithm(model_name, part_number_name, window_name, algorithm);
        auto& algorithms = window_optional->get().algorithms;
        auto it = std::find_if(algorithms.begin(), algorithms.end(), [algorithm_name](const jrsdata::DetectAlgorithm& elem)
            {
                return elem.detect_algorithm_name == algorithm_name;
            });
        if (it == algorithms.end())
            return CreateAlgorithm(model_name, part_number_name, window_name, algorithm);
        *it = algorithm;
        return 0;
        //return UpdateDetectWindow(model_name, part_number_name, *window_optional);
    }

    int ProjectDataProcess::DeleteAlgorithm(const std::string& model_name, const std::string& part_number_name, const std::string& window_name, const std::string& algorithm_name)
    {
        auto window_optional = ReadDetectWindowRef(model_name, part_number_name, window_name);
        if (!window_optional.has_value())
            return 2;
        if (algorithm_name.empty())
            return 2;
        auto& algorithms = window_optional->get().algorithms;
        auto it = std::remove_if(algorithms.begin(), algorithms.end(),
            [&](const jrsdata::DetectAlgorithm& elem) { return elem.detect_algorithm_name == algorithm_name; });

        if (it != algorithms.end())
        {
            algorithms.erase(it, algorithms.end());
            return jrscore::AOI_OK;
        }
        return 0;
        //return UpdateDetectWindow(model_name, part_number_name, *window_optional);
    }

    int ProjectDataProcess::CreateSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, jrsdata::SubDetectWindow& subwindow)
    {
        auto algo_optional = ReadAlgorithmRef(model_name, part_number_name, window_name, algorithm_name);
        if (!algo_optional.has_value())
            return 2;
        auto& subwindows = algo_optional->get().algorithm_detect_windows;
        auto& sub_window_name = subwindow.name;
        if (sub_window_name.empty())
        {
            const auto& max_subwindow = std::max_element(subwindows.begin(), subwindows.end(), [](const jrsdata::SubDetectWindow& a, const jrsdata::SubDetectWindow& b) { return a.id < b.id; });
            subwindow.id = (max_subwindow == subwindows.end()) ? 1 : max_subwindow->id + 1;
            subwindow.name = part_number_name + "_" + model_name + "_" + window_name + "_" + algorithm_name + "_sub_" + std::to_string(subwindow.id);
        }
        else
        {
            auto it = std::find_if(subwindows.begin(), subwindows.end(), [sub_window_name](const jrsdata::SubDetectWindow& elem)
                {
                    return elem.name == sub_window_name;
                });
            if (it != subwindows.end())
                return 1;
        }
        subwindows.emplace_back(subwindow);
        return 0;
        //return UpdateAlgorithm(model_name, part_number_name, window_name, *algo_optional);
    }

    int ProjectDataProcess::CreateSubDetectWindow(jrsdata::DetectWindow& detect_window, const int& sub_win_type, std::vector<jrsdata::SubDetectWindow>& sub_detect_windows)
    {
        int win_width = (int)std::round(detect_window.width / 4.0);
        int win_height = (int)std::round(detect_window.height / 4.0);

        switch (sub_win_type)
        {
        case 1: // ONE_WINDOW               
        {
            win_width = win_width > 100 ? win_width : 100;
            win_height = win_height > 100 ? win_height : 100;

            SubDetectWindow sub_detect_window;
            sub_detect_window.cx = 0;
            sub_detect_window.cy = 0;
            sub_detect_window.width = win_width;
            sub_detect_window.height = win_height;
            sub_detect_window.name = "sub_detect_window_1";
            sub_detect_windows.push_back(sub_detect_window);
            break;
        }
        case 2: //CROSS_WINDOWS
        {
            SubDetectWindow sub_detect_window1;
            sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
            sub_detect_window1.cy = 0;
            sub_detect_window1.width = win_width;
            sub_detect_window1.height = win_height;
            sub_detect_window1.name = "sub_detect_window_1";

            SubDetectWindow sub_detect_window2;
            sub_detect_window2.cx = (win_width * 3 / 2.0 - 1);
            sub_detect_window2.cy = 0;
            sub_detect_window2.width = win_width;
            sub_detect_window2.height = win_height;
            sub_detect_window2.name = "sub_detect_window_2";

            SubDetectWindow sub_detect_window3;
            sub_detect_window3.cx = 0;
            sub_detect_window3.cy = -(win_height * 3 / 2.0 + 1);
            sub_detect_window3.width = win_width;
            sub_detect_window3.height = win_height;
            sub_detect_window3.name = "sub_detect_window_3";

            SubDetectWindow sub_detect_window4;
            sub_detect_window4.cx = 0;
            sub_detect_window4.cy = (win_height * 3 / 2.0 - 1);
            sub_detect_window4.width = win_width;
            sub_detect_window4.height = win_height;
            sub_detect_window4.name = "sub_detect_window_4";

            sub_detect_windows.push_back(sub_detect_window1);
            sub_detect_windows.push_back(sub_detect_window2);
            sub_detect_windows.push_back(sub_detect_window3);
            sub_detect_windows.push_back(sub_detect_window4);

            break;
        }
        case 3: //FOUR_CORNERS_WINDOWS
        {
            SubDetectWindow sub_detect_window1;
            sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
            sub_detect_window1.cy = -(win_height * 3 / 2.0 + 1);
            sub_detect_window1.width = win_width;
            sub_detect_window1.height = win_height;
            sub_detect_window1.name = "sub_detect_window_1";

            SubDetectWindow sub_detect_window2;
            sub_detect_window2.cx = (win_width * 3 / 2.0 + 1);
            sub_detect_window2.cy = -(win_height * 3 / 2.0 + 1);
            sub_detect_window2.width = win_width;
            sub_detect_window2.height = win_height;
            sub_detect_window2.name = "sub_detect_window_2";

            SubDetectWindow sub_detect_window3;
            sub_detect_window3.cx = -(win_width * 3 / 2.0 + 1);
            sub_detect_window3.cy = (win_height * 3 / 2.0 + 1);
            sub_detect_window3.width = win_width;
            sub_detect_window3.height = win_height;
            sub_detect_window3.name = "sub_detect_window_3";

            SubDetectWindow sub_detect_window4;
            sub_detect_window4.cx = (win_width * 3 / 2.0 + 1);
            sub_detect_window4.cy = (win_height * 3 / 2.0 + 1);
            sub_detect_window4.width = win_width;
            sub_detect_window4.height = win_height;
            sub_detect_window4.name = "sub_detect_window_4";
            sub_detect_windows.push_back(sub_detect_window1);
            sub_detect_windows.push_back(sub_detect_window2);
            sub_detect_windows.push_back(sub_detect_window3);
            sub_detect_windows.push_back(sub_detect_window4);
            break;
        }
        case 4: //HORIZONTAL_SYMMETRY_WINDOWS
        {
            SubDetectWindow sub_detect_window1;
            sub_detect_window1.cx = -(win_width * 3 / 2.0 + 1);
            sub_detect_window1.cy = 0;
            sub_detect_window1.width = win_width;
            sub_detect_window1.height = win_height;
            sub_detect_window1.name = "sub_detect_window_1";

            SubDetectWindow sub_detect_window2;
            sub_detect_window2.cx = (win_width * 3 / 2.0 - 1);
            sub_detect_window2.cy = 0;
            sub_detect_window2.width = win_width;
            sub_detect_window2.height = win_height;
            sub_detect_window2.name = "sub_detect_window_2";

            sub_detect_windows.push_back(sub_detect_window1);
            sub_detect_windows.push_back(sub_detect_window2);

            break;
        }
        case 5: //VERTICAL_SYMMETRY_WINDOWS
        {
            SubDetectWindow sub_detect_window1;
            sub_detect_window1.cx = 0;
            sub_detect_window1.cy = -(win_height * 3 / 2.0 + 1);
            sub_detect_window1.width = win_width;
            sub_detect_window1.height = win_height;
            sub_detect_window1.name = "sub_detect_window_1";

            SubDetectWindow sub_detect_window2;
            sub_detect_window2.cx = 0;
            sub_detect_window2.cy = (win_height * 3 / 2.0 - 1);
            sub_detect_window2.width = win_width;
            sub_detect_window2.height = win_height;
            sub_detect_window2.name = "sub_detect_window_2";

            sub_detect_windows.push_back(sub_detect_window1);
            sub_detect_windows.push_back(sub_detect_window2);

            break;
        }
        default:
        {
            break;
        }
        }
        return 0;
    }

    std::optional<jrsdata::SubDetectWindow> ProjectDataProcess::ReadSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name)
    {
        auto algo = ReadAlgorithmRef(model_name, part_number_name, window_name, algorithm_name);
        if (!algo.has_value())
            return std::nullopt;

        auto& subwindows = algo->get().algorithm_detect_windows;

        auto it = std::find_if(subwindows.begin(), subwindows.end(), [sub_window_name](const jrsdata::SubDetectWindow& elem)
            {
                return elem.name == sub_window_name;
            });
        if (it == subwindows.end())
            return std::nullopt;
        return *it;
    }

    std::optional<std::reference_wrapper<jrsdata::SubDetectWindow>> ProjectDataProcess::ReadSubDetectWindowRef(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name)
    {
        auto algo = ReadAlgorithmRef(model_name, part_number_name, window_name, algorithm_name);
        if (!algo.has_value())
            return std::nullopt;

        auto& subwindows = algo->get().algorithm_detect_windows;

        auto it = std::find_if(subwindows.begin(), subwindows.end(), [sub_window_name](const jrsdata::SubDetectWindow& elem)
            {
                return elem.name == sub_window_name;
            });
        if (it == subwindows.end())
            return std::nullopt;
        return std::ref(*it);
    }

    int ProjectDataProcess::UpdateSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, jrsdata::SubDetectWindow& subwindow)
    {
        auto algo = ReadAlgorithmRef(model_name, part_number_name, window_name, algorithm_name);
        if (!algo.has_value())
            return 2;
        auto& sub_window_name = subwindow.name;
        if (sub_window_name.empty())
            return CreateSubDetectWindow(part_number_name, model_name, window_name, algorithm_name, subwindow);

        auto& subwindows = algo->get().algorithm_detect_windows;
        auto it = std::find_if(subwindows.begin(), subwindows.end(), [sub_window_name](const jrsdata::SubDetectWindow& elem)
            {
                return elem.name == sub_window_name;
            });
        if (it == subwindows.end())
            return CreateSubDetectWindow(part_number_name, model_name, window_name, algorithm_name, subwindow);
        *it = subwindow;
        return 0;
        // return UpdateAlgorithm(model_name, part_number_name, window_name, *algo_optional);
    }

    int ProjectDataProcess::DeleteSubDetectWindow(const std::string& part_number_name, const std::string& model_name, const std::string& window_name, const std::string& algorithm_name, const std::string& sub_window_name)
    {
        auto algo = ReadAlgorithmRef(model_name, part_number_name, window_name, algorithm_name);
        if (!algo.has_value())
            return 2;
        if (sub_window_name.empty())
            return 2;

        auto& subwindows = algo->get().algorithm_detect_windows;
        auto it = std::remove_if(subwindows.begin(), subwindows.end(),
            [&](const jrsdata::SubDetectWindow& elem) { return elem.name == sub_window_name; });

        if (it != subwindows.end())
        {
            subwindows.erase(it, subwindows.end());
            // return jrscore::AOI_OK;
        }
        return 0;
        // return UpdateAlgorithm(model_name, part_number_name, window_name, *algo);
    }

    int ProjectDataProcess::CreateCAD(jrsdata::Component& cad, const std::string& subboard_name)
    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        if (cad.component_name.empty())
            return 2;

        auto subBoard = ReadSubBoardRef(subboard_name);
        if (!subBoard.has_value())
        {
            return 1;
        }


        auto& components = subBoard->get().component_info;
        auto it_c = std::find_if(components.begin(), components.end(),
            [&](const jrsdata::Component& elem) { return elem.component_name == cad.component_name; });
        if (it_c != components.end())
        {
            return 3;
        }
        cad.component_id = GetNewComponentID(components);
        components.push_back(cad);
        return 0;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadCADRef(const std::string& subboard_name, const std::string& cad_name)
    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        auto subBoard = ReadSubBoardRef(subboard_name);
        if (!subBoard.has_value())
        {
            return std::nullopt;
        }

        auto& components = subBoard->get().component_info;
        auto it_c = std::find_if(components.begin(), components.end(),
            [&](const jrsdata::Component& elem) { return elem.component_name == cad_name; });
        if (it_c == components.end())
        {
            return std::nullopt;
        }
        return std::ref(*it_c);
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadCADRef(const std::string& cad_name)
    {
        std::shared_lock<std::shared_mutex> lck(mutex);
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }
        for (auto& subboard : _param->board_info.sub_board)
        {
            auto& components = subboard.component_info;
            auto it_c = std::find_if(components.begin(), components.end(),
                [&](const jrsdata::Component& elem) { return elem.component_name == cad_name; });
            if (it_c != components.end())
            {
                return std::ref(*it_c);
            }
        }
        return std::nullopt;
    }

    int ProjectDataProcess::UpdateCAD(jrsdata::Component& cad)
    {
        std::shared_lock<std::shared_mutex> lck(mutex);

        auto subBoard = ReadSubBoardRef(cad.subboard_name);
        if (!subBoard.has_value())
        {
            return 1;
        }
        auto& components = subBoard->get().component_info;
        auto it_c = std::find_if(components.begin(), components.end(),
            [&](const jrsdata::Component& elem) { return elem.component_name == cad.component_name; });
        if (it_c == components.end())
        {
            Log_ERROR("更新CAD失败");
            return -1;
        }
        *it_c = cad;
        return 0;
    }

    int ProjectDataProcess::DeleteCAD(const std::string& subboard_name, const std::string& cad_name)
    {
        //std::unique_lock<std::shared_mutex> lck(mutex); // 改为独占锁

        auto subBoard = ReadSubBoardRef(subboard_name);
        if (!subBoard.has_value())
        {
            return 1;
        }

        auto& components = subBoard->get().component_info;
        auto it_c = std::find_if(components.begin(), components.end(),
            [&](const jrsdata::Component& elem) { return elem.component_name == cad_name; });

        if (it_c == components.end())
        {
            return 2;
        }

        jrsdata::Component component = *it_c; // 复制一份，避免 erase 影响
        SynchronousDeleteOtherSameSubboard(component);
        components.erase(it_c); // 删除 CAD 组件
        DeleteSurplusPNDetectInfo(component.component_part_number);
        return jrscore::AOI_OK;
    }
    int ProjectDataProcess::CreateCAD(jrsdata::Component& cad)
    {
        if (cad.component_name.empty())
            return 2;
        auto state = CreateCAD(cad, cad.subboard_name);
        if (state != 0)
        {
            return state;
        }
        return SynchronousOtherSameSubboard(cad);
    }


    int ProjectDataProcess::DeleteCAD(const int& cad_id)
    {
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& subboards = _param->board_info.sub_board;
        for (auto& sub : subboards)
        {
            auto& components = sub.component_info;
            auto it_c = std::find_if(components.begin(), components.end(),
                [&](const jrsdata::Component& elem) { return elem.component_id == cad_id; });
            if (it_c != components.end())
            {
                components.erase(it_c);
            }
        }
        return 0;
    }

    int ProjectDataProcess::GenerateComponentName(jrsdata::Component& component_)
    {
        /** <查询SubboardID 在工程内的 */
        auto subboard = ReadSubBoard(component_.subboard_name);
        if (!subboard.has_value() && (
            component_.component_type == jrsdata::Component::Type::CAD ||
            component_.component_type == jrsdata::Component::Type::SUB_BARCODE ||
            component_.component_type == jrsdata::Component::Type::SUB_MARK ||
            component_.component_type == jrsdata::Component::Type::SUB_BADMARK)) {
            return -1;
        }

        int subboard_id = subboard.has_value() ? subboard->id : -1;
        return GenerateComponentNameBySubboardID(component_, subboard_id);
    }

    int ProjectDataProcess::GenerateComponentNameBySubboardID(jrsdata::Component& component_, int subboard_id)
    {
        int component_id = component_.component_id;

        static const std::unordered_map<jrsdata::Component::Type, std::string> component_id_types = {
          { jrsdata::Component::Type::BARCODE, "barcode_" },
          { jrsdata::Component::Type::CARRIER_BARCODE, "carrier_barcode_" },
          { jrsdata::Component::Type::COVERPLATE_BARCODE, "coverplate_barcode_" },
          { jrsdata::Component::Type::MARK, "mark_" },
          { jrsdata::Component::Type::AI_REGION, "ai_region_" }
        };

        auto it = component_id_types.find(component_.component_type);
        if (it != component_id_types.end()) {
            component_.component_name = it->second + std::to_string(component_id);
            return jrscore::AOI_OK;
        }

        switch (component_.component_type) {
        case jrsdata::Component::Type::CAD:
            component_.component_name += "_" + std::to_string(subboard_id);
            break;

        case jrsdata::Component::Type::SUB_BARCODE:
            component_.component_name = "sub_barcode_" + std::to_string(subboard_id);
            break;

        case jrsdata::Component::Type::SUB_MARK:
            component_.component_name = "sub_mark_" + std::to_string(subboard_id) + "_" + std::to_string(component_id);
            break;

        case jrsdata::Component::Type::SUB_BADMARK:
            component_.component_name = "bad_mark_" + std::to_string(subboard_id);
            break;

        case jrsdata::Component::Type::AI_REGION:
            component_.component_name = "ai_region_" + std::to_string(subboard_id);
            break;

        default:
            std::cerr << "Warning: Unhandled component type in GenerateComponentNameBySubboardID: "
                << static_cast<int>(component_.component_type) << std::endl;
            break;
        }

        return 0;
    }

    int ProjectDataProcess::CreateMark(jrsdata::Component& mark)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        if (mark.component_part_number.empty())
        {
            return 2;
        }

        {

            auto& marks = _param->board_info.marks;
            int mark_id = GetNewComponentID(marks);

            mark.component_type = Component::Type::MARK;
            mark.component_id = mark_id;
            GenerateComponentName(mark);
            std::unique_lock<std::shared_mutex> lck(mutex);
            marks.push_back(mark);
        }
        //auto part = ReadPNDetectInfo(mark.component_part_number);
        //if (!part.has_value())
        //{
        //    jrsdata::PNDetectInfo spec_and_detect_window;
        //    CreatePNDetectInfo(mark.component_part_number, spec_and_detect_window);
        //}
        return jrscore::AOI_OK;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadMarkRef(const std::string& markName)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& vec = _param->board_info.marks;
        auto it = std::find_if(vec.begin(), vec.end(),
            [&](const jrsdata::Component& ele) { return ele.component_name == markName; });
        if (it != vec.end())
        {
            return std::ref(*it);
        }
        return std::nullopt;
    }

    int ProjectDataProcess::CreateAIComponent(jrsdata::Component& ai_area, float width, float height)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        // 创建或修改PNDetectInfo
        ai_area.component_part_number = "ai_region";
        ai_area.component_type = Component::Type::AI_REGION;
        auto part = ReadPNDetectInfoRef(ai_area.component_part_number);
        if (!part.has_value())
        {
            jrsdata::PNDetectInfo pn;
            pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, width, height, 1, "body", "body",
                jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
            pn.units.emplace_back(jrsdata::ComponentUnit(0, 0, width, height, 2, "body", "location",
                jrsdata::ComponentUnit::Type::BODY, jrsdata::ComponentUnit::Shape::RECT));
            pn.detect_models["location"] = jrsdata::DetectModel();
            pn.detect_models["body"] = jrsdata::DetectModel();
            CreatePNDetectInfo(ai_area.component_part_number, pn);
        }
        else
        {
            auto& units = part.value().get().units;
            for (size_t i = 0; i < units.size(); i++)
            {
                units[i].width = width;
                units[i].height = height;
            }
        }

        GenerateComponentName(ai_area);
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::CreateSubboardComponent(jrsdata::Component& ai_area)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        auto& sub_board = _param->board_info.sub_board;
        if (!sub_board.empty())
        {
            ai_area.x = sub_board[0].x;
            ai_area.y = sub_board[0].y;
            ai_area.subboard_name = sub_board[0].subboard_name;

            CreateAIComponent(ai_area, sub_board[0].width, sub_board[0].height);
            return jrscore::AOI_OK;
        }
        return -1;
    }

    std::optional<std::reference_wrapper<std::vector<jrsdata::Component>>> ProjectDataProcess::ReadAllBoardMarkRef()
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        return std::ref(_param->board_info.marks);
    }

    int ProjectDataProcess::UpdateMark(jrsdata::Component& mark)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& vec = _param->board_info.marks;
        auto it = std::find_if(vec.begin(), vec.end(),
            [&](const jrsdata::Component& ele) { return ele.component_name == mark.component_name; });
        if (it == vec.end())
        {
            return CreateMark(mark);
        }
        *it = mark;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::DeleteMark(const std::string& markName)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        std::string component_part_number = "";
        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& vec = _param->board_info.marks;
        auto it = std::find_if(vec.begin(), vec.end(),
            [&](const jrsdata::Component& ele)
            {
                return ele.component_name == markName;
            });
        if (it != vec.end())
        {
            component_part_number = it->component_part_number;
            vec.erase(it);
        }
        DeleteSurplusPNDetectInfo(component_part_number, jrsdata::Component::Type::MARK);
        return jrscore::AOI_OK;
    }


    int ProjectDataProcess::GetSubMarkMaxId(const jrsdata::SubBoard& subboard)
    {
        int id = 1;
        for (auto sub_mark : subboard.sub_mark)
        {
            if (sub_mark.component_id > id)
            {
                id = sub_mark.component_id;
            }
        }
        return id;
    }

    //int ProjectDataProcess::GetSubMarkMaxId()
    //{
    //    return 0;
    //}

    int ProjectDataProcess::CreateSubMark(jrsdata::Component& mark)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        {
            auto subboard_ref = ReadSubBoardRef(mark.subboard_name);
            if (!subboard_ref.has_value())
            {
                Log_ERROR("查找子板：", mark.subboard_name, "失败");
                return 1;
            }
            mark.component_id = GetNewComponentID(subboard_ref->get().sub_mark);
            GenerateComponentName(mark);
            subboard_ref->get().sub_mark.emplace_back(mark);
        }
        //auto part = ReadPNDetectInfo(mark.component_part_number);
        //if (!part.has_value())
        //{
        //    jrsdata::PNDetectInfo spec_and_detect_window;
        //    CreatePNDetectInfo(mark.component_part_number, spec_and_detect_window);
        //}

        /** <同步到其他相同子板！TODO: HJC */
        return SynchronousOtherSameSubboard(mark);
    }



    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadSubMarkRef(const std::string& subBoardName, const std::string& markName)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        auto subboard = ReadSubBoardRef(subBoardName);
        if (!subboard.has_value())
        {
            return std::nullopt;
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& sub_mark = subboard->get().sub_mark;
        auto it = std::find_if(sub_mark.begin(), sub_mark.end(),
            [&](const jrsdata::Component& ele) { return ele.component_name == markName; });
        if (it == sub_mark.end())
        {
            return std::nullopt;
        }

        return std::ref(*it);
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadSubMarkRef(const std::string& component_name_)
    {

        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }
        for (auto& subboard : _param->board_info.sub_board)
        {
            auto it = std::find_if(subboard.sub_mark.begin(), subboard.sub_mark.end(),
                [&](const jrsdata::Component& ele) { return ele.component_name == component_name_; });
            if (it != subboard.sub_mark.end())
            {
                return std::ref(*it);
            }
        }
        return std::nullopt;
    }



    int ProjectDataProcess::UpdateSubMark(jrsdata::Component& submark)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        auto subboard = ReadSubBoardRef(submark.subboard_name);
        if (!subboard.has_value())
        {
            return 2;
        }

        auto& sub_marks = subboard->get().sub_mark;
        auto it = std::find_if(sub_marks.begin(), sub_marks.end(),
            [&](const jrsdata::Component& ele) { return ele.component_name == submark.component_name; });
        if (it == sub_marks.end())
        {
            return CreateSubMark(submark);/*< 没有则创建*/
        }
        *it = submark;
        return jrscore::AOI_OK;
    }


    int ProjectDataProcess::DeleteSubMark(const std::string& subBoardName, const std::string& markName)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; // 指针为空，请配置参数
        }

        auto subboard = ReadSubBoardRef(subBoardName);
        if (!subboard.has_value())
        {
            return 2;
        }

        auto& sub_marks = subboard->get().sub_mark;

        // 查找目标元件
        auto it = std::find_if(sub_marks.begin(), sub_marks.end(),
            [&](const jrsdata::Component& ele) { return ele.component_name == markName; });

        if (it == sub_marks.end())
        {
            return 3;
        }

        std::string component_part_number = it->component_part_number;
        auto sub_mark_temp = *it;
        {
            // **仅在删除时加锁，最小化锁的影响范围**
            std::unique_lock<std::shared_mutex> lck(mutex);
            sub_marks.erase(it);
        }
        SynchronousDeleteOtherSameSubboard(sub_mark_temp);
        // 元件删除后检查是否需要删除该 component_part_number
        DeleteSurplusPNDetectInfo(component_part_number, jrsdata::Component::Type::SUB_MARK);

        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::ReplaceSubBadMark(jrsdata::Component& mark)
    {
        auto& subboard_name = mark.subboard_name;
        auto subboard = ReadSubBoardRef(subboard_name);
        if (!subboard.has_value())
        {
            return 2;
        }
        GenerateComponentName(mark);
        subboard->get().bad_mark = mark;
        return jrscore::AOI_OK;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadSubBadMarkRef(const std::string& component_name_)
    {
        for (auto& subboard : _param->board_info.sub_board)
        {
            if (subboard.bad_mark.component_name == component_name_)
            {
                return std::ref(subboard.bad_mark);
            }
        }
        return std::nullopt;
    }

    int ProjectDataProcess::DeleteSubBadMark(const std::string& subboard_name)
    {
        auto subboard = ReadSubBoardRef(subboard_name);
        if (!subboard.has_value())
        {
            return 2;
        }
        std::string component_part_number = subboard->get().bad_mark.component_part_number;
        auto bad_mark_temp = subboard->get().bad_mark;
        subboard->get().bad_mark = jrsdata::Component();

        /**<清除其他子板的bad_mark */
        SynchronousDeleteOtherSameSubboard(bad_mark_temp);
        return DeleteSurplusPNDetectInfo(component_part_number, jrsdata::Component::Type::SUB_BADMARK);
    }


    int ProjectDataProcess::CreateBarcode(jrsdata::Component& barcode)
    {
        auto& barcodes = _param->board_info.barcodes;
        auto& barcode_name = barcode.component_name;
        barcode.component_id = GetNewComponentID(barcodes);
        if (barcode_name.empty())
        {
            GenerateComponentName(barcode);
        }
        else
        {
            auto it = std::find_if(barcodes.begin(), barcodes.end(), [barcode_name](const jrsdata::Component& elem)
                {
                    return elem.component_name == barcode_name;
                });
            if (it != barcodes.end())
            {
                Log_ERROR("定位点元件名称重复，添加失败，请检查");
                return 1;
            }
        }
        barcodes.emplace_back(barcode);
        return 0;
    }

    std::optional<jrsdata::Component> ProjectDataProcess::ReadBarcode(const std::string& barcode_name)
    {
        if (barcode_name.empty())
            return std::nullopt;
        auto& barcodes = _param->board_info.barcodes;
        auto it = std::find_if(barcodes.begin(), barcodes.end(), [barcode_name](const jrsdata::Component& elem)
            {
                return elem.component_name == barcode_name;
            });
        if (it != barcodes.end())
            return *it;
        return std::nullopt;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadBarcodeRef(const std::string& barcode_name)
    {
        if (barcode_name.empty())
            return std::nullopt;
        auto& barcodes = _param->board_info.barcodes;
        auto it = std::find_if(barcodes.begin(), barcodes.end(), [barcode_name](const jrsdata::Component& elem)
            {
                return elem.component_name == barcode_name;
            });
        if (it != barcodes.end())
            return std::ref(*it);
        return std::nullopt;
    }

    int ProjectDataProcess::UpdateBarcode(jrsdata::Component& barcode)
    {
        auto& barcodes = _param->board_info.barcodes;
        auto& barcode_name = barcode.component_name;
        auto it = std::find_if(barcodes.begin(), barcodes.end(), [barcode_name](const jrsdata::Component& elem)
            {
                return elem.component_name == barcode_name;
            });
        if (it == barcodes.end())
            return CreateBarcode(barcode);
        *it = barcode;
        return 0;
    }

    int ProjectDataProcess::DeleteBarcode(const std::string& barcode_name)
    {
        if (barcode_name.empty())
            return 2;

        auto& barcodes = _param->board_info.barcodes;
        auto it = std::remove_if(barcodes.begin(), barcodes.end(),
            [&](const jrsdata::Component& elem) { return elem.component_name == barcode_name; });

        if (it == barcodes.end())
            return 1;

        std::string part_number = std::prev(it)->component_part_number;
        barcodes.erase(it, barcodes.end());
        DeleteSurplusPNDetectInfo(part_number, jrsdata::Component::Type::BARCODE);
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::ReplaceSubBoardBarcode(jrsdata::Component& barcode)
    {
        auto sub_optional = ReadSubBoardRef(barcode.subboard_name);
        if (!sub_optional.has_value())
            return 2;
        GenerateComponentName(barcode);
        sub_optional->get().barcode = barcode;
        return 0;
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadSubBoardBarcodeRef(const std::string& subboard_name, const std::string& barcode_name)
    {
        if (barcode_name.empty())
            return std::nullopt;
        auto sub_optional = ReadSubBoardRef(subboard_name);
        if (!sub_optional.has_value())
            return std::nullopt;
        return std::ref(sub_optional->get().barcode);
    }

    std::optional<std::reference_wrapper<jrsdata::Component>> ProjectDataProcess::ReadSubBoardBarcodeRef(const std::string& barcode_name)
    {
        auto it = std::find_if(_param->board_info.sub_board.begin(), _param->board_info.sub_board.end(),
            [&](const auto& sub_board) {
                return sub_board.barcode.component_name == barcode_name;
            });

        if (it != _param->board_info.sub_board.end())
        {
            return std::ref(it->barcode);
        }
        return std::nullopt;
    }

    std::optional<jrsdata::Component> ProjectDataProcess::ReadSubBoardBarcode(const std::string& barcode_name)
    {
        auto it = std::find_if(_param->board_info.sub_board.begin(), _param->board_info.sub_board.end(),
            [&](const auto& sub_board) {
                return sub_board.barcode.component_name == barcode_name;
            });

        if (it != _param->board_info.sub_board.end())
        {
            return it->barcode;
        }
        return std::nullopt;
    }

    std::optional<jrsdata::Component> ProjectDataProcess::ReadSubBoardBarcode(const std::string& subboard_name, const std::string& barcode_name)
    {
        if (barcode_name.empty())
            return std::nullopt;
        auto sub_optional = ReadSubBoardRef(subboard_name);
        if (!sub_optional.has_value())
            return std::nullopt;
        return sub_optional->get().barcode;
    }


    int ProjectDataProcess::DeleteSubBoardBarcode(const std::string& subboard_name)
    {
        if (subboard_name.empty())
            return 2;
        auto sub_optional = ReadSubBoardRef(subboard_name);
        if (!sub_optional.has_value())
            return 2;
        std::string component_part_number = sub_optional->get().barcode.component_part_number;
        auto sub_barcode_temp = sub_optional->get().barcode;
        sub_optional->get().barcode = jrsdata::Component(); // 初始化
        /** <删除其他子板相同的barcode */
        SynchronousDeleteOtherSameSubboard(sub_barcode_temp);

        DeleteSurplusPNDetectInfo(component_part_number, jrsdata::Component::Type::SUB_BARCODE);
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::CreateTemplate(const cv::Mat& src_img, const std::string& color_params, int light_image_id_, jrsdata::Template& output_template)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        output_template.SetMatImage(src_img);
        output_template.color_params = color_params;
        output_template.light_image_id = light_image_id_;

        std::unique_lock<std::shared_mutex> lck(mutex);
        const auto& max_id_temp = std::max_element(_param->temps.begin(), _param->temps.end(), [](const jrsdata::Template& a, const jrsdata::Template& b) { return a.id < b.id; });
        output_template.id = (max_id_temp == _param->temps.end()) ? 1 : max_id_temp->id + 1;
        _param->temps.push_back(output_template);

        return jrscore::AOI_OK;
    }

    //int ProjectDataProcess::CreateTemplate(jrsdata::Template& temp)
    //{
    //    if (IsParamPtrEmpty())
    //    {
    //        return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
    //    }

    //    std::unique_lock<std::shared_mutex> lck(mutex);
    //    auto& temps = _param->temps;
    //    auto it = std::find_if(temps.begin(), temps.end(),
    //        [&](const jrsdata::Template& t) { return t.id == temp.id; });

    //    if (it == temps.end())
    //    {
    //        temps.push_back(temp);
    //    }
    //    else
    //    {
    //        *it = temp;
    //    }
    //    // _param->ReplaceTemplate(temp);

    //    return jrscore::AOI_OK;
    //}

    std::optional<jrsdata::Template> ProjectDataProcess::ReadTemplate(int temp_id)
    {

        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        auto& temps = _param->temps;

        auto it = std::find_if(temps.begin(), temps.end(),
            [&](const jrsdata::Template& t) { return t.id == temp_id; });

        if (it != temps.end())
        {
            return *it;
        }

        return std::nullopt;

    }

    int ProjectDataProcess::UpdateTemplate(const jrsdata::Template& temp)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto it = std::find_if(_param->temps.begin(), _param->temps.end(),
            [&](jrsdata::Template& t) { return t.id == temp.id; });

        if (it != _param->temps.end())
        {
            *it = temp;
            return jrscore::AOI_OK;
        }
        return 2; // temp 不存在
    }

    int ProjectDataProcess::DeleteTemplate(int temp_id)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& temps = _param->temps;
        auto it = std::remove_if(temps.begin(), temps.end(),
            [&](const jrsdata::Template& t) { return t.id == temp_id; });

        if (it != temps.end())
        {
            temps.erase(it, temps.end());
            return jrscore::AOI_OK;
        }

        return 2; //删除失败 元件不存在
    }

    int ProjectDataProcess::SetProject(const std::string& project_name_, double width_, double height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        _param->project_name = project_name_;
        auto& board = _param->board_info;
        board.real_width = width_;
        board.real_height = height_;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::GetProject(std::string& project_name_, double& width_, double& height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::shared_lock<std::shared_mutex> lck(mutex);
        project_name_ = _param->project_name;
        auto& board = _param->board_info;
        width_ = board.real_width;
        height_ = board.real_height;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::SetBoardSize(int width_, int height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        board.width = width_;
        board.height = height_;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::ReadBoardSizePixel(int& width_, int& height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        width_ = board.width;
        height_ = board.height;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::SetBoardRealSize(double real_width_, double real_height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        board.real_width = real_width_;
        board.real_height = real_height_;

        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::GetBoardRealSize(double& real_width_, double& real_height_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        double real_width = board.real_width;
        double real_height = board.real_height;
        real_width_ = real_width;
        real_height_ = real_height;
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::SetBoardRowCol(int row, int col)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        board.rows = row;
        board.cols = col;
        return jrscore::AOI_OK;
    }
    int ProjectDataProcess::SetBoardPosition(double left_top_x, double left_top_y, double right_bottom_x, double right_bottom_y)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        std::unique_lock<std::shared_mutex> lck(mutex);
        auto& board = _param->board_info;
        board.left_top_x = left_top_x;
        board.left_top_y = left_top_y;
        board.right_bottom_x = right_bottom_x;
        board.right_bottom_y = right_bottom_y;

        //TODO  更新长度和宽度的值 by baron_zhang 202-11-25
        board.real_width = std::abs(board.left_top_x - board.right_bottom_x);
        board.real_height = std::abs(board.left_top_y - board.right_bottom_y);



        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::UpdateImage(int type_, const cv::Mat& img_)
    {
        if (IsParamPtrEmpty())
        {
            return 1;
        }
        _param->entirety_board_imgs[type_] = img_;
        return 0;
    }

    std::optional<cv::Mat> ProjectDataProcess::ReadImage(int type)
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt;
        }
        auto it = _param->entirety_board_imgs.find(type);
        if (it != _param->entirety_board_imgs.end())
        {
            return it->second;
        }
        return std::nullopt;
    }

    std::optional<const std::unordered_map<int, cv::Mat>*> ProjectDataProcess::ReadImage()
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt;
        }
        return &_param->entirety_board_imgs;
    }

    int ProjectDataProcess::ClearEntiretyBoardImages()
    {
        if (IsParamPtrEmpty())
        {
            return -1;
        }
        for (auto& [_, board_img] : _param->entirety_board_imgs)
        {
            board_img.release();
        }
        _param->entirety_board_imgs.clear();
        return jrscore::AOI_OK;
    }

    std::optional <std::string> ProjectDataProcess::GetCurrentEntiretyGroupName()
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt;
        }
        if (_param->current_group_name.empty())
        {
            return std::nullopt;
        }
        else
        {
            return _param->current_group_name;
        }

    }

    int ProjectDataProcess::SetCurrentEntiretyGroupName(const std::string& current_group_name_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }
        _param->current_group_name = current_group_name_;
        return jrscore::AOI_OK;
    }

    std::optional <std::vector<std::string>> ProjectDataProcess::GetEntiretyGroupNames()
    {
        if (IsParamPtrEmpty())
        {
            return std::nullopt; //指针为空，请配置参数
        }
        return _param->image_group_names;
    }

    int ProjectDataProcess::GetTemplatesByDetectWinName(const std::string& part_numb_name, const std::string& detect_win_name, std::vector<jrsdata::Template>& templates)
    {
        auto detect_win = ReadDetectWindow(part_numb_name, detect_win_name);
        if (detect_win == std::nullopt || detect_win->algorithms.empty())
        {
            return -1;
        }

        auto& template_ids = detect_win->algorithms[0].template_image_ids;
        if (template_ids.empty())
        {
            return -1;
        }
        for (auto id : template_ids)
        {
            auto template_ = ReadTemplate(id);
            if (template_ == std::nullopt)
            {
                continue;
            }
            templates.push_back(*template_);
        }
        return 0;
    }


    int ProjectDataProcess::GetSignelDetectWinExcuteRectsParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit, const jrsdata::DetectWindow& detect_win, \
        jrsparam::AlgoExcuteRectsParam& algo_excute_rects_param, const ExecuteModeInfo& execute_info)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        cv::RotatedRect component_pos;
        //! 获取本体的宽高信息
        const auto& component_unit_body = ReadComponentUnitGroup(component.component_part_number, "body");
        if (component_unit_body.empty())
        {
            return -1;
        }
        switch (execute_info.execute_mode)
        {
        case ExecuteMode::ManualMode:
        {
            // 元件在大图上的位置
            component_pos.angle = component.angle;
            component_pos.center = cv::Point2f(component.x, component.y);
            component_pos.size = cv::Size2f(component_unit_body[0].width, component_unit_body[0].height);
            break;
        }
        case ExecuteMode::AutoMode:
        {

            //!将元件坐标从大图上换到FOV图(FOV图可能是单个FOV，也可能是多个FOV拼成的一个图，简称为：FOV图)上
            /** 当前元件在FOV图中的中心坐标  by zhangyuyu 2024.12.29*/
            int absoulte_window_center_x_in_fov;
            int absoulte_window_center_y_in_fov;
            jrscore::CoordinateTransform::GlobalToLocal((int)component.x, (int)component.y,
                absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov, execute_info.fov_left_top_x,
                execute_info.fov_left_top_y);

            component_pos.angle = component.angle;
            component_pos.center = cv::Point2f(absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov);
            component_pos.size = cv::Size2f(component_unit.width, component_unit.height);

            break;
        }
        default:
            break;
        }


        const std::string& part_name = component.component_part_number;
        std::vector<cv::Point2f> all_points;
        switch (component_unit.unit_type)
        {
        case jrsdata::ComponentUnit::Type::HOLE:
        case jrsdata::ComponentUnit::Type::BODY:
        {
            // 获取检测框和cad区域
            auto ori_algo_detect_win_rect = JrsRect(component_pos.center.x + detect_win.cx, component_pos.center.y + detect_win.cy, detect_win.width, detect_win.height, 0, -1, false);

            algo_excute_rects_param.component_rect = JrsRect::FromCvRotatedRect(component_pos);
            algo_excute_rects_param.component_rect.angle = 0;

            auto [size_x, size_y] = GetSearchRegionSize(detect_win);
            JrsRect search_win_rect = ori_algo_detect_win_rect;
            search_win_rect.width = size_x;
            search_win_rect.height = size_y;
            //! 用算法检测框外扩的区域给算法，算法在输入的图上直接截取算法检测图片 by zhangyuyu 2024.1.8
            algo_excute_rects_param.expand_detect_win_rect = search_win_rect;
            algo_excute_rects_param.original_detect_win_rect = ori_algo_detect_win_rect;

            cv::Point2f vertices[4];
            search_win_rect.ToCvRotatedRect().points(vertices);
            all_points.insert(all_points.end(), vertices, vertices + 4);
            algo_excute_rects_param.component_rect.ToCvRotatedRect().points(vertices);
            all_points.insert(all_points.end(), vertices, vertices + 4);

            //! wangzhengkai 2025.03.10 新增若无子检测框，则将算法检测框作为子检测框
            if (detect_win.algorithms[0].algorithm_detect_windows.size() == 0)
            {
                algo_excute_rects_param.original_sub_win_rects[0].push_back(algo_excute_rects_param.original_detect_win_rect);
            }
            else
            {
                // 获取子检测框区域
                int sub_win_index = 0;
                for (const auto& sub_win : detect_win.algorithms[0].algorithm_detect_windows)
                {
                    JrsRect rect(algo_excute_rects_param.original_detect_win_rect.cx + sub_win.cx, algo_excute_rects_param.original_detect_win_rect.cy + sub_win.cy, sub_win.width, sub_win.height, 0, sub_win_index, false);
                    algo_excute_rects_param.original_sub_win_rects[0].push_back(rect);
                    search_win_rect.ToCvRotatedRect().points(vertices);
                    all_points.insert(all_points.end(), vertices, vertices + 4);
                }
            }
            break;
        }
        case jrsdata::ComponentUnit::Type::PAD:
        {
            // 获取整组pad和pad检测框区域
            algo_excute_rects_param.component_rect = JrsRect::FromCvRotatedRect(component_pos);

            const auto pad_group = ReadComponentUnitGroup(part_name, component_unit.unit_group_name);
            if (pad_group.empty())
            {
                return -1;
            }

            //! 获取pad元件框坐标，虽然pad和component是同一级别，但是pad的坐标是相对于component保存在工程里的
            for (auto& pad : pad_group)
            {
                if (pad.enable)
                {
                    auto dir = (int)pad.direction;

                    JrsRect rect(component_pos.center.x + pad.x, component_pos.center.y + pad.y, pad.width, pad.height, 0, /*pad.id*/pad.show_id, false);
                    algo_excute_rects_param.pad_rects[dir].push_back(rect);

                }
            }

            //! 获取pad元件的算法rect，pad的算法检测框的坐标也是根据pad的相对位置保存的
            for (auto& pad_rects_dir : algo_excute_rects_param.pad_rects)
            {
                int angle = (static_cast<int>(jrsdata::ComponentUnit::Direction::UP) - static_cast<int>(pad_rects_dir.first)) * 90;
                // 将pad转回到上方向，因为检测框位置是以pad在上方向的位置记录的
                jcvtools::JrsHomMat2D rotate_hom_mat;
                rotate_hom_mat.AddHomMat2dRotate(angle, component_pos.center.x, component_pos.center.y);

                // 用于将检测框转到实际位置
                jcvtools::JrsHomMat2D inverted_rotate_hom_mat = rotate_hom_mat.HomMat2dInvert();
                for (auto& pad_rect : pad_rects_dir.second)
                {
                    auto up_pad_center = rotate_hom_mat.AffineTransPoint(cv::Point2f(pad_rect.cx, pad_rect.cy));
                    cv::RotatedRect detect_window;
                    detect_window.center = cv::Point2f(up_pad_center.x + detect_win.cx, up_pad_center.y + detect_win.cy);
                    detect_window.size = cv::Size2f(detect_win.width, detect_win.height);
                    detect_window.angle = 0;

                    detect_window = inverted_rotate_hom_mat.AffineTransRotatedRect(detect_window);

                    JrsRect original_rect = JrsRect::FromCvRotatedRect(detect_window);
                    original_rect.id = pad_rect.id;

                    // JrsRect original_rect(pad_rect.cx + detect_win.cx, pad_rect.cy + detect_win.cy, detect_win.width, detect_win.height, 0, pad_rect.id);
                    auto [search_width, search_height] = GetSearchRegionSize(detect_window.size, detect_win.search_size);
                    //detect_win.GetSearchSize(size_x, size_y);
                    JrsRect search_win_rect = original_rect;
                    search_win_rect.width = search_width;
                    search_win_rect.height = search_height;
                    //! pad的检测框传给算法时，是以子检测框
                    algo_excute_rects_param.expand_sub_win_rects[pad_rects_dir.first].push_back(search_win_rect);
                    algo_excute_rects_param.original_sub_win_rects[pad_rects_dir.first].push_back(original_rect);

                    cv::Point2f vertices[4];
                    search_win_rect.ToCvRotatedRect().points(vertices);
                    all_points.insert(all_points.end(), vertices, vertices + 4);
                }
            }
            break;
        }
        default:
            break;
        }

        float min_x = std::numeric_limits<float>::max();
        float min_y = std::numeric_limits<float>::max();
        float max_x = std::numeric_limits<float>::lowest();
        float max_y = std::numeric_limits<float>::lowest();

        for (const auto& point : all_points)
        {
            min_x = std::min(min_x, point.x);
            min_y = std::min(min_y, point.y);
            max_x = std::max(max_x, point.x);
            max_y = std::max(max_y, point.y);
        }

        //! 传给算法的截图bounding_box
        cv::Rect bounding_box(cv::Point2f(min_x, min_y), cv::Point2f(max_x, max_y));

        //! 2025.04.02 wangzhengkai 增大外扩区域，防止算法截图误差，截取到图像外面崩溃
        //! 2025.01.20 wangzhengkai 外扩单检测框区域（解决单元件检测和单检测框检测截图不一致问题）
        /*bounding_box.x = std::max(bounding_box.x - 5, 0);
        bounding_box.y = std::max(bounding_box.y - 5, 0);*/
        bounding_box.x = bounding_box.x - 5;
        bounding_box.y = bounding_box.y - 5;
        bounding_box.width = bounding_box.width + 10;
        bounding_box.height = bounding_box.height + 10;

        // 将所有框的坐标转换到 bounding_box 坐标系下
        float offset_x = bounding_box.x;
        float offset_y = bounding_box.y;

        algo_excute_rects_param.component_rect.cx -= offset_x;
        algo_excute_rects_param.component_rect.cy -= offset_y;
        algo_excute_rects_param.original_detect_win_rect.cx -= offset_x;
        algo_excute_rects_param.original_detect_win_rect.cy -= offset_y;
        algo_excute_rects_param.expand_detect_win_rect.cx -= offset_x;
        algo_excute_rects_param.expand_detect_win_rect.cy -= offset_y;

        for (auto& sub_win_rects_dir : algo_excute_rects_param.original_sub_win_rects)
        {
            for (auto& sub_win_rect : sub_win_rects_dir.second)
            {
                sub_win_rect.cx = sub_win_rect.cx - offset_x;
                sub_win_rect.cy = sub_win_rect.cy - offset_y;
            }
        }

        for (auto& expand_sub_win_rects_dir : algo_excute_rects_param.expand_sub_win_rects)
        {
            for (auto& expand_sub_win_rect : expand_sub_win_rects_dir.second)
            {
                expand_sub_win_rect.cx = expand_sub_win_rect.cx - offset_x;
                expand_sub_win_rect.cy = expand_sub_win_rect.cy - offset_y;
            }
        }

        for (auto& pad_rects_dir : algo_excute_rects_param.pad_rects)
        {
            for (auto& pad_rect : pad_rects_dir.second)
            {
                pad_rect.cx = pad_rect.cx - offset_x;
                pad_rect.cy = pad_rect.cy - offset_y;
                // pad_rect.center = pad_rect.center - cv::Point2f(offset_x, offset_y);
            }
        }

        // 所有区域的最小外接矩形作为整体的截图区域
        algo_excute_rects_param.crop_img_rect = JrsRect(bounding_box.x + bounding_box.width / 2.0, bounding_box.y + bounding_box.height / 2.0, bounding_box.width, bounding_box.height, 0, -1, false);

        return 0;
    }

    int ProjectDataProcess::GetSignelComponentExcuteRectsParam(const jrsdata::Component& compoent, std::map<std::string, jrsparam::AlgoExcuteRectsParam>& algo_excute_rects_params,
        cv::RotatedRect& crop_image_rect, const ExecuteModeInfo& execute_info)
    {
        if (IsParamPtrEmpty())
        {
            return -1;
        }
        const auto pn_detect_info = ReadPNDetectInfoRef(compoent.component_part_number);
        if (!pn_detect_info.has_value())
        {
            return -2;
        }
        for (auto& detect_model : pn_detect_info->get().detect_models)
        {
            const auto& detect_model_name = detect_model.first;
            for (auto& detect_win : detect_model.second.detect_model)
            {
                const auto& component_unit_group = ReadComponentUnitGroup(compoent.component_part_number, detect_model_name);
                if (component_unit_group.empty())
                {
                    continue;
                }
                jrsparam::AlgoExcuteRectsParam algo_excute_rects_param;
                GetSignelDetectWinExcuteRectsParam(compoent, component_unit_group[0], detect_win, algo_excute_rects_param, execute_info);
                algo_excute_rects_params.insert({ detect_win.name, algo_excute_rects_param });
            }
        }

        //!TODO:zhangyuyu 获取单个元件所有检测框的最大外接矩形
        std::vector<cv::Point2f> all_points;
        for (auto& algo_excute_rects_param : algo_excute_rects_params)
        {
            cv::Point2f vertices[4];
            algo_excute_rects_param.second.crop_img_rect.ToCvRotatedRect().points(vertices);
            all_points.insert(all_points.end(), vertices, vertices + 4);
        }

        // 2025.04.02 wangzhengkai 元件检测默认截图大小为元件本体区域
        auto component_body = ReadComponentBodyRef(compoent.component_part_number);
        if (!component_body.has_value())
        {
            return -3;
        }


        jrsdata::Component component_new;
        switch (execute_info.execute_mode)
        {
        case ExecuteMode::ManualMode:
        {
            // 元件在大图上的位置
            component_new = compoent;
            break;
        }
        case ExecuteMode::AutoMode:
        {

            //!将元件坐标从大图上换到FOV图(FOV图可能是单个FOV，也可能是多个FOV拼成的一个图，简称为：FOV图)上
            /** 当前元件在FOV图中的中心坐标  by zhangyuyu 2024.12.29*/
            int absoulte_window_center_x_in_fov;
            int absoulte_window_center_y_in_fov;
            jrscore::CoordinateTransform::GlobalToLocal((int)compoent.x, (int)compoent.y,
                absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov, execute_info.fov_left_top_x,
                execute_info.fov_left_top_y);

            component_new.x = absoulte_window_center_x_in_fov;
            component_new.y = absoulte_window_center_y_in_fov;
            component_new.angle = compoent.angle;
            break;
        }
        default:
            break;
        }

        float min_x = component_new.x - component_body->get().width / 2.0f;
        float min_y = component_new.y - component_body->get().height / 2.0f;
        float max_x = component_new.x + component_body->get().width / 2.0f;
        float max_y = component_new.y + component_body->get().height / 2.0f;

        for (const auto& point : all_points)
        {
            min_x = std::min(min_x, point.x);
            min_y = std::min(min_y, point.y);
            max_x = std::max(max_x, point.x);
            max_y = std::max(max_y, point.y);
        }

        //! 在求出的最小外接矩形再外扩 用固定值100像素外扩 by zhangyuyu 2025.2.19
        int deltax = 50.0f;
        int deltay = 50.0f;

        cv::Rect bounding_box(cv::Point(min_x - deltax, min_y - deltay), cv::Point(max_x + deltax, max_y + deltay));
        crop_image_rect = cv::RotatedRect(cv::Point2f(bounding_box.x + bounding_box.width / 2.0, bounding_box.y + bounding_box.height / 2.0), cv::Size2f(bounding_box.width, bounding_box.height), 0);

        for (auto& [name, algo_excute_rects_param] : algo_excute_rects_params)
        {
            float offset_x = bounding_box.x - algo_excute_rects_param.crop_img_rect.ToCvRect2f().x;
            float offset_y = bounding_box.y - algo_excute_rects_param.crop_img_rect.ToCvRect2f().y;

            algo_excute_rects_param.component_rect.cx -= offset_x;
            algo_excute_rects_param.component_rect.cy -= offset_y;
            algo_excute_rects_param.original_detect_win_rect.cx -= offset_x;
            algo_excute_rects_param.original_detect_win_rect.cy -= offset_y;
            algo_excute_rects_param.expand_detect_win_rect.cx -= offset_x;
            algo_excute_rects_param.expand_detect_win_rect.cy -= offset_y;
            algo_excute_rects_param.crop_img_rect = JrsRect::FromCvRotatedRect(crop_image_rect);
            for (auto& sub_win_rects_dir : algo_excute_rects_param.original_sub_win_rects)
            {
                for (auto& sub_win_rect : sub_win_rects_dir.second)
                {
                    sub_win_rect.cx -= offset_x;
                    sub_win_rect.cy -= offset_y;
                }
            }
            for (auto& expand_sub_win_rects_dir : algo_excute_rects_param.expand_sub_win_rects)
            {
                for (auto& expand_sub_win_rect : expand_sub_win_rects_dir.second)
                {
                    expand_sub_win_rect.cx -= offset_x;
                    expand_sub_win_rect.cy -= offset_y;
                }
            }

            for (auto& pad_rects_dir : algo_excute_rects_param.pad_rects)
            {
                for (auto& pad_rect : pad_rects_dir.second)
                {
                    pad_rect.cx = pad_rect.cx - offset_x;
                    pad_rect.cy = pad_rect.cy - offset_y;
                }
            }

            algo_excute_rects_param.crop_img_rect = JrsRect::FromCvRotatedRect(crop_image_rect);
        }
        return 0;
    }

    int ProjectDataProcess::SetPadRectToLocationParam(const std::string& component_part_num_, std::map<std::string, ExecuteAlgoParam>& detect_window_execute_param_)
    {

        //!  获取当前指定的料号信息，因为所有的PAD框，子检测框，算法检测框信息等都在料号中存储的 by zhangyuyu 2024.12.27
        auto partnumber_info = ReadPNDetectInfoRef(component_part_num_);

        if (!partnumber_info.has_value())
        {
            return -1;
        }


        std::vector<jrsparam::ExecuteAlgoParam> pad_group_algo_param;
        for (auto& value : detect_window_execute_param_)
        {
            if (value.first.find("pad") != std::string::npos)
            {
                pad_group_algo_param.push_back(value.second);
            }

        }

        //! TODO：后期需要优化成算法支持添加任意个检测框
        for (auto& value : detect_window_execute_param_)
        {
            if (value.first.find("location") != std::string::npos && value.second.algo_name == "PadLocationOperator")
            {

                //! 在料号种获取当前算法的检测框信息
                const auto detect_win = partnumber_info->get().ReadDetectWindowPtr(value.first);
                if (!detect_win)
                {
                    return -1;
                }
                auto size_padding = detect_win->search_size;
                for (auto& value_pad : pad_group_algo_param)
                {
                    value.second.pad_rects.insert(value_pad.pad_rects.begin(), value_pad.pad_rects.end());
                    value.second.original_sub_win_rects.insert(value_pad.pad_rects.begin(), value_pad.pad_rects.end());
                    for (auto& pads_rects : value_pad.pad_rects)
                    {
                        for (auto& value_rect : pads_rects.second)
                        {
                            auto size = GetSearchRegionSize({ value_rect.width,value_rect.height }, size_padding);
                            JrsRect rect_temp;
                            rect_temp = value_rect;
                            rect_temp.width = size.width;
                            rect_temp.height = size.height;
                            value.second.expand_sub_win_rects[pads_rects.first].emplace_back(rect_temp);
                        }
                    }
                }
            }

        }

        return 0;
    }

    int ProjectDataProcess::CropDetectWinImageFromProject(const jrsdata::LightImageType type, const jrsdata::Component& component, const std::string& window_name, const jrsdata::ComponentUnit& component_unit, cv::Mat& img, std::vector<std::pair<int, cv::RotatedRect>>& detect_rects, const ExecuteModeInfo& run_mode_info)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::ViewError::E_AOI_VIEW_EMPTY_POINTER; //指针为空，请配置参数
        }

        const std::string& part_name = component.component_part_number;
        auto pn_detect_info = ReadPNDetectInfoRef(part_name);
        if (!pn_detect_info.has_value())
        {
            return -1;
        }

        auto iter = pn_detect_info->get().detect_models.find(component_unit.unit_group_name);
        if (iter == pn_detect_info->get().detect_models.end())
        {
            return -1;
        }

        //const auto& detect_model_name = iter->first;
        auto detect_win = std::find_if(iter->second.detect_model.begin(), iter->second.detect_model.end(),
            [&window_name](const jrsdata::DetectWindow& detect_win_value) { return detect_win_value.name == window_name; });
        if (detect_win == iter->second.detect_model.end())
        {
            return -1;
        }

        jrsparam::AlgoExcuteRectsParam algo_excute_rects_param;
        GetSignelDetectWinExcuteRectsParam(component, component_unit, *detect_win, algo_excute_rects_param, run_mode_info);

        if (component_unit.unit_type == ComponentUnit::Type::BODY)
        {
            auto rect = algo_excute_rects_param.original_detect_win_rect.ToCvRotatedRect();
            detect_rects.push_back({ -1, {rect} });
        }
        else if (component_unit.unit_type == ComponentUnit::Type::PAD)
        {
            //! 将component_unit的检查框放到map中的第一个位置

            int count = 0;
            int index = 0;
            for (const auto& rects : algo_excute_rects_param.original_sub_win_rects)
            {
                for (const auto& rect : rects.second)
                {
                    detect_rects.push_back({ rects.first, rect.ToCvRotatedRect() });
                    if (rect.id == component_unit.id)
                    {
                        index = count;
                    }
                    count++;
                }
            }
            std::swap(detect_rects[0], detect_rects[index]);
        }
        //!TODO:临时将获取检测框图片加上在动态调试时将截图元件坐标转到fov上 by zhangyuyu 2025.4.16
        int absoulte_window_center_x_in_fov;
        int absoulte_window_center_y_in_fov;
        jrscore::CoordinateTransform::GlobalToLocal((int)component.x, (int)component.y,
            absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov, run_mode_info.fov_left_top_x,
            run_mode_info.fov_left_top_y);
        cv::Point2f rotate_center;
        rotate_center = cv::Point2f(absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov);
        return RotateCropImageFromProject(type, algo_excute_rects_param.crop_img_rect.ToCvRect(), img, rotate_center, component.angle, nullptr, run_mode_info);
    }

    int ProjectDataProcess::RotateCropImageFromProject(const jrsdata::LightImageType type, const cv::Rect& rect, cv::Mat& croped_image,
        const cv::Point2f rotate_center, const double angle, cv::Mat* matrix_to_src_image, const ExecuteModeInfo& run_mode_info)
    {

        if (IsParamPtrEmpty())
        {
            return jrscore::CommonError::E_AOI_POINTER_EMPTY;
        }
        const cv::Mat* src_image = nullptr;
        constexpr double k_angle_epsilon = 1e-5;
        switch (run_mode_info.execute_mode)
        {
        case ExecuteMode::ManualMode:
        {
            // 元件在大图上的位置
            if (_param->entirety_board_imgs.find((int)type) == _param->entirety_board_imgs.end())
            {
                return -1;
            }
            src_image = &_param->entirety_board_imgs.find((int)type)->second;
            break;
        }
        case ExecuteMode::AutoMode:
        {

            // 元件在FOV中的位置
            auto it = run_mode_info.src_img.find(type);
            if (it == run_mode_info.src_img.end())
            {
                return -1;
            }
            src_image = &it->second;
            break;
        }
        default:
            return -1;
        }

        if (src_image == nullptr)
        {
            return -1;
        }

        if (abs(angle) < k_angle_epsilon)
        {
            if (jrstool::CropAndPasteTool::CropImage(*src_image, rect, croped_image) != 0)
            {
                return -2;
            }
            if (matrix_to_src_image)
            {
                *matrix_to_src_image = (cv::Mat_<double>(2, 3) << 1, 0, rect.x + run_mode_info.fov_left_top_x, 0, 1, rect.y + run_mode_info.fov_left_top_y);
            }
        }
        else
        {
            jcvtools::JrsHomMat2D hom_mat;
            hom_mat.AddHomMat2dTranslate(-run_mode_info.fov_left_top_x, -run_mode_info.fov_left_top_y);
            cv::RotatedRect rotated_rect(cv::Point2f(rect.x + rect.width / 2.0f, rect.y + rect.height / 2.0f), rect.size(), 0);
            cv::Point2f vertices[4];
            rotated_rect.points(vertices);

            // 2025.01.23 wangzhengkai 将截图长度转成整形解决元件有角度时检测框截取图片和单元件截取图片不一致的问题
            int max_distance = 0;
            for (int i = 0; i < 4; i++)
            {
                int distance = cv::norm(rotate_center - vertices[i]);
                max_distance = std::ceil(std::max(max_distance, distance));
            }

            cv::Rect crop_temp_rect = cv::Rect(rotate_center.x - max_distance, rotate_center.y - max_distance, max_distance * 2, max_distance * 2);
            cv::Mat crop_temp_image;
            if (jrstool::CropAndPasteTool::CropImage(*src_image, crop_temp_rect, crop_temp_image) != 0)
            {
                return -2;
            }

            hom_mat.AddHomMat2dTranslate(-crop_temp_rect.x, -crop_temp_rect.y);


            cv::Mat rotate_img;
            cv::Size size = crop_temp_image.size();
            cv::Point2f center(size.width / 2.0, size.height / 2.0);
            cv::Mat rot_mat = cv::getRotationMatrix2D(center, angle, 1.0);
            try
            {
                cv::warpAffine(crop_temp_image, rotate_img, rot_mat, size);
            }
            catch (const std::exception& ex)
            {
                std::cout << "cv::warpAffine 执行异常:" << ex.what() << std::endl;
            }

            hom_mat.AddHomMat2dRotate(-angle, center.x, center.y);


            center = rotated_rect.center - (rotate_center - cv::Point2f(size.width / 2.0, size.height / 2.0));
            crop_temp_rect = cv::Rect((center.x - rect.width / 2), (center.y - rect.height / 2.0f), rect.width, rect.height);
            croped_image = rotate_img(crop_temp_rect).clone();

            hom_mat.AddHomMat2dTranslate(-crop_temp_rect.x, -crop_temp_rect.y);


            auto inv_matrix = hom_mat.HomMat2dInvert();
            if (matrix_to_src_image)
            {
                //*matrix_to_src_image = cv::getRotationMatrix2D(rotate_center, angle, 1.0);
                ///  测试代码  
                // 获取旋转矩形的顶点


                // 对矩形顶点应用变换
                *matrix_to_src_image = inv_matrix.toMat();
                // 如果需要，可以重新创建旋转矩形

                // 绘制框

            }
        }
        return 0;
    }

    void ProjectDataProcess::AlignmentBoard(jrsdata::Board& board, const cv::Mat& transform_matrix)
    {
        //! 矫正子板
        /** @brief 轮询整板下所有的子板进行矫正 */
        for (auto& subboard_value : board.sub_board)
        {
            AlignmentSubBoard(subboard_value, transform_matrix);
        }

        //! 矫正整板MARK
        {
            std::vector<Component*> mark_vector;
            //! 查找整板中所有MARK
            for (auto& mark_value : board.marks)
            {
                mark_vector.emplace_back(&mark_value);
            }
            AlignmentComponents(mark_vector, transform_matrix);
        }

        //! 矫正整板的barcode
        {
            std::vector<Component*> barcode_vector;
            for (auto& barcode_value : board.barcodes)
            {
                barcode_vector.emplace_back(&barcode_value);
            }
            AlignmentComponents(barcode_vector, transform_matrix);
        }
    }

    void ProjectDataProcess::AlignmentSubBoard(jrsdata::SubBoard& sub_board, const cv::Mat& transform_matrix)
    {
        //! 矫正元件
        {
            std::vector<Component*> components_vector;
            for (auto& component_value : sub_board.component_info)
            {
                components_vector.emplace_back(&component_value);
            }
            AlignmentComponents(components_vector, transform_matrix);
        }

        //! 矫正子板MARK
        {
            std::vector<Component*> sub_mark_vector;
            for (auto& m : sub_board.sub_mark)
            {
                sub_mark_vector.emplace_back(&m);
            }
            //! 添加坏板MARK
            sub_mark_vector.emplace_back(&sub_board.bad_mark);
            AlignmentComponents(sub_mark_vector, transform_matrix);
        }

        //! 矫正子板barcode
        {
            std::vector<Component*> sub_barcode_vector;
            {
                sub_barcode_vector.emplace_back(&sub_board.barcode);
            }
            AlignmentComponents(sub_barcode_vector, transform_matrix);

        }

        //! 矫正当前子板
        {

            //! 矫正前的子板左上角坐标vector，因为矫正接口接受的是vector
            std::vector<cv::Point> sub_center_coordinate;
            //! 矫正后的子板左上角坐标vector，因为矫正接口接受的是vector
            std::vector<cv::Point> new_sub_center_coordinate;
            sub_center_coordinate.emplace_back(cv::Point(sub_board.x, sub_board.y));

            auto res_warp = jrstool::CoordinatrTransform<int>::WarpAffine(transform_matrix, sub_center_coordinate, new_sub_center_coordinate);

            if (res_warp != jrscore::AOI_OK)
            {
                Log_ERROR("子板坐标旋转平移矩阵矫正失败！");
                return;
            }
            sub_board.x = new_sub_center_coordinate[0].x;
            sub_board.y = new_sub_center_coordinate[0].y;
        }
    }

    void ProjectDataProcess::AlignmentSubBoard(const std::string& sub_board_name, const cv::Mat& transform_matrix)
    {
        auto subboard = ReadSubBoard(sub_board_name);
        if (!subboard.has_value())
            return;
        AlignmentSubBoard(*subboard, transform_matrix);
    }

    void ProjectDataProcess::AlignmentComponent(jrsdata::Component& component, const cv::Mat& transform_matrix)
    {
        std::vector<jrsdata::Component*> components{ &component };
        AlignmentComponents(components, transform_matrix);
    }

    void ProjectDataProcess::AlignmentComponents(std::vector<jrsdata::Component*>& components, const cv::Mat& transform_matrix)
    {

        if (components.empty())
            return;

        //! 元件矫正前中心坐标
        std::vector<cv::Point> component_center;
        //! 元件矫正过后的中心坐标
        std::vector<cv::Point>    component_center_transformed;
        for (auto& component : components)
        {
            if (!component)
            {
                Log_ERROR("元件矫正过程中，元件指针为空！");
                return;
            }
            component_center.emplace_back(cv::Point(component->x, component->y));
        }

        //! 调用矫正接口，使用旋转平移矩阵对元件坐标进行矫正
        auto res_warp = jrstool::CoordinatrTransform<int>::WarpAffine(transform_matrix, component_center, component_center_transformed);

        if (res_warp != jrscore::AOI_OK)
        {
            Log_ERROR("元件坐标旋转平移矩阵矫正失败！");
            return;
        }

        if (components.size() != component_center_transformed.size())
        {
            Log_ERROR("元件坐标矫正过程中，矫正后的坐标数量与原元件数量不对应！");
            return;
        }

        //！用矫正后的坐标替换原本元件坐标
        for (int i = 0; i < components.size() && i < component_center_transformed.size(); ++i)
        {
            components[i]->x = component_center_transformed[i].x;
            components[i]->y = component_center_transformed[i].y;
        }
    }

    int ProjectDataProcess::GetComponentExecuteParam(const jrsdata::Component& component, std::map<std::string, ExecuteAlgoParam>& detect_window_execute_param_,
        cv::Mat& matrix_to_src_image_, const ExecuteModeInfo& execute_mode_)
    {
        jtools::ScopedTimer timer_scoped("获取元件参数");

        //!  当前元件所有的算法的Rects信息 key:算法名称 value:Rect信息
        std::map<std::string, jrsparam::AlgoExcuteRectsParam> algo_execute_rects_params;

        //! 当前元件所有的检测框信息的最大外接矩形 by zhangyuyu 2024.12.27
        cv::RotatedRect crop_image_rect;
        //! 1. 获取当前元件所有的检测框信息和需要裁剪的区域
        GetSignelComponentExcuteRectsParam(component, algo_execute_rects_params, crop_image_rect, execute_mode_);

        //! 2. 获取当前元件的检测图片的4种颜色,本质上就是截图
        std::unordered_map<int, cv::Mat> input_img_map;

        JrsRect rect = JrsRect::FromCvRotatedRect(crop_image_rect);

        GetExecuteImg(matrix_to_src_image_, component, input_img_map, rect.ToCvRect(), execute_mode_);

        //! 3.截完图后将这些元件的参数，转换赋值给算法运行参数

        SetAlgoExecuteParam(component.component_part_number, input_img_map, algo_execute_rects_params, detect_window_execute_param_);
        //! 将pad信息赋值给焊盘定位算法，不管使不使用
        SetPadRectToLocationParam(component.component_part_number, detect_window_execute_param_);
        return jrscore::AOI_OK;
    }

    int ProjectDataProcess::GetComponentImage(const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& img, cv::RotatedRect& crop_image_rect)
    {
        //!  当前元件所有的算法的Rects信息 key:算法名称 value:Rect信息
        std::map<std::string, jrsparam::AlgoExcuteRectsParam> algo_execute_rects_params;

        //! 1. 获取当前元件所有的检测框信息和需要裁剪的区域
        GetSignelComponentExcuteRectsParam(component, algo_execute_rects_params, crop_image_rect);

        JrsRect rect = JrsRect::FromCvRotatedRect(crop_image_rect);

        cv::Mat matrix_to_src_image_;
        GetExecuteImg(matrix_to_src_image_, component, img, rect.ToCvRect());

        return 0;
    }

    int ProjectDataProcess::GetComponentLightImg(const jrsdata::Component& component, std::unordered_map<int, cv::Mat>& component_img_map)
    {
        std::map<std::string, jrsparam::AlgoExcuteRectsParam> algo_execute_rects_params;
        std::map<std::string, ExecuteAlgoParam> detect_window_execute_param_;
        cv::Mat matrix_to_src_image_;
        //! 当前元件所有的检测框信息的最大外接矩形 by zhangyuyu 2024.12.27
        cv::RotatedRect crop_image_rect;
        //! 1. 获取当前元件所有的检测框信息和需要裁剪的区域
        GetSignelComponentExcuteRectsParam(component, algo_execute_rects_params, crop_image_rect);

        //! 2. 获取当前元件的检测图片的4种颜色,本质上就是截图

        auto rect = JrsRect::FromCvRotatedRect(crop_image_rect);

        GetExecuteImg(matrix_to_src_image_, component, component_img_map, rect.ToCvRect());
        return jrscore::AOI_OK;

    }

    int ProjectDataProcess::GetDetectWindowExecuteParam(const jrsdata::Component& component, const jrsdata::ComponentUnit& component_unit,
        const jrsdata::DetectWindow& detect_win, jrsparam::ExecuteAlgoParam& exect_param, cv::Mat& matrix_to_src_image_, const ExecuteModeInfo& execute_mode_)
    {
        jtools::ScopedTimer timer_scoped("获取检测框参数");
        //! 单个算法的检测区域信息
        jrsparam::AlgoExcuteRectsParam algo_excute_rects_param;

        //！获取单个算法的检测区域信息
        GetSignelDetectWinExcuteRectsParam(component, component_unit, detect_win, algo_excute_rects_param, execute_mode_);
        std::unordered_map<int, cv::Mat> input_img_map;
        //! 获取当前算法检测框的图片内容
        GetExecuteImg(matrix_to_src_image_, component, input_img_map, algo_excute_rects_param.crop_img_rect.ToCvRect(), execute_mode_);
        //! 3.截完图后将这些元件的参数，转换赋值给算法运行参数

        SetSingleDetectWindowExecuteParam(component.component_part_number, input_img_map, detect_win.name, algo_excute_rects_param, exect_param);
        return 0;
    }

    int ProjectDataProcess::GetExecuteImg(cv::Mat& matrix_to_src_image_, const jrsdata::Component& component_, std::unordered_map<int, cv::Mat>& dst_img_map_, const cv::Rect& crop_img_rect, const ExecuteModeInfo& execute_mode_info_)
    {
        if (IsParamPtrEmpty())
        {
            return jrscore::CoreError::E_AOI_CORE_PARAM_NULL;
        }
        matrix_to_src_image_ = cv::Mat::eye(3, 3, CV_32F);


        //!将元件坐标从大图上换到FOV图(FOV图可能是单个FOV，也可能是多个FOV拼成的一个图，简称为：FOV图)上
        int absoulte_window_center_x_in_fov;
        int absoulte_window_center_y_in_fov;
        jrscore::CoordinateTransform::GlobalToLocal((int)component_.x, (int)component_.y,
            absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov, execute_mode_info_.fov_left_top_x,
            execute_mode_info_.fov_left_top_y);
        cv::Point2f rotate_center;
        rotate_center = cv::Point2f(absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov);

        for (int i = 0; i < static_cast<int>(jrsdata::LightImageType::COUNT) - 1; i++)
        {
            cv::Mat detect_win_mat;
            auto light_type = static_cast<jrsdata::LightImageType>(i);
            jrsparam::AlgoExcuteRectsParam algo_excute_rects_param;
            RotateCropImageFromProject(light_type, crop_img_rect,
                detect_win_mat, rotate_center, component_.angle, &matrix_to_src_image_, execute_mode_info_);
            if (detect_win_mat.empty())
            {
                Log_ERROR("截图失败！");
            }
            dst_img_map_.insert({ (int)light_type, detect_win_mat });
        }
        return 0;
    }

    void ProjectDataProcess::SetAlgoExecuteParam(const std::string& component_part_num_name_, const std::unordered_map<int, cv::Mat>& input_img_map_, std::map<std::string, jrsparam::AlgoExcuteRectsParam>& algo_execute_rects_param_, std::map<std::string, jrsparam::ExecuteAlgoParam>& detect_win_execute_param_)
    {
        //!  获取当前指定元件的料号信息，因为所有的PAD框，子检测框，算法检测框信息等都在料号中存储的 by zhangyuyu 2024.12.27
        for (auto& [algo_win_name, detect_window_rects_execute_param] : algo_execute_rects_param_)
        {

            SetSingleDetectWindowExecuteParam(component_part_num_name_, input_img_map_, algo_win_name, detect_window_rects_execute_param, detect_win_execute_param_[algo_win_name]);
        }
    }

    void ProjectDataProcess::SetSingleDetectWindowExecuteParam(const std::string& component_part_num_name_, const std::unordered_map<int, cv::Mat>& input_img_map_,
        const std::string& algo_win_name, jrsparam::AlgoExcuteRectsParam& single_algo_execute_rects_, jrsparam::ExecuteAlgoParam& single_algo_execute_param)
    {

        //!  获取当前指定的料号信息，因为所有的PAD框，子检测框，算法检测框信息等都在料号中存储的 by zhangyuyu 2024.12.27
        auto partnumber_info = ReadPNDetectInfoRef(component_part_num_name_);

        if (!partnumber_info.has_value())
        {
            return;
        }
        //! 元件所有算法检测图像灯光图赋值给算法参数
        single_algo_execute_param.input_img = input_img_map_;

        //! 获取当前检测框模板图像
        std::vector<jrsdata::Template> template_infos;
        GetTemplatesByDetectWinName(component_part_num_name_, algo_win_name, template_infos);

        //! 在料号种获取当前算法的检测框信息
        const auto detect_win = partnumber_info->get().ReadDetectWindowPtr(algo_win_name);
        if (!detect_win)
        {
            return;
        }
        if (detect_win->algorithms.empty())
        {
            return;
        }
        //! 将算法检测框信息赋值进算法执行参数中
        single_algo_execute_param.algo_name = detect_win->algorithms[0].detect_algorithm_name;
        single_algo_execute_param.algo_param = detect_win->algorithms[0].param;
        single_algo_execute_param.x_resolution = resolution_x;
        single_algo_execute_param.y_resolution = resolution_y;
        //! 如果该算法没有建模板图，那么模板数据则使用当前算法的建模时的默认颜色参数，灯光图则用建模时的灯光图
        //! (模板数据!=模板图片，模板数据中包含：模板图片，该算法在何种灯光ID下建模，颜色调色参数)
        if (template_infos.empty())
        {
            const auto& algorithm = detect_win->algorithms[0];
            single_algo_execute_param.template_data.push_back({
              static_cast<jrsoperator::LightType>(
                algorithm.light_image_id
                ),
              ColorParams::FromJson(detect_win->algorithms[0].color_param)
                }
            );
        }
        else
        {

            for (auto& template_img_value : template_infos)
            {
                single_algo_execute_param.template_data.emplace_back(
                    ColorParams::FromJson(template_img_value.color_params),
                    template_img_value.GetMatImage().clone(),
                    static_cast<jrsoperator::LightType>(template_img_value.light_image_id)
                );
            }
        }

        SetAlgoExcuteRectsParam2ExecuteAlgoParam(single_algo_execute_rects_, single_algo_execute_param);

    }

    void ProjectDataProcess::SetAlgoExcuteRectsParam2ExecuteAlgoParam(const jrsparam::AlgoExcuteRectsParam& excute_rects_param, jrsparam::ExecuteAlgoParam& execute_algo_param)
    {
        execute_algo_param.component_rect = excute_rects_param.component_rect;
        execute_algo_param.original_detect_rect = excute_rects_param.original_detect_win_rect;
        execute_algo_param.expand_detect_win_rect = excute_rects_param.expand_detect_win_rect;
        execute_algo_param.pad_rects = excute_rects_param.pad_rects;
        execute_algo_param.original_sub_win_rects = excute_rects_param.original_sub_win_rects;
        execute_algo_param.expand_sub_win_rects = excute_rects_param.expand_sub_win_rects;

        execute_algo_param.correction_matrixes.clear();
        execute_algo_param.correction_matrixes.insert({ execute_algo_param.original_detect_rect.id, cv::Mat::eye(2, 3, CV_32F) });
        for (auto& dir_sub_detect_wins : excute_rects_param.original_sub_win_rects)
        {
            for (auto& sub_detect_win : dir_sub_detect_wins.second)
            {
                execute_algo_param.correction_matrixes.insert({ sub_detect_win.id, cv::Mat::eye(2, 3, CV_32F) });
            }
        }
    }

    int ProjectDataProcess::SortDetectWinsByDepedent(const std::vector<jrsdata::DetectWindow>& detect_wins, std::vector<std::vector<jrsdata::DetectWindow>>& sorted_wins)
    {
        jtools::DirectedGraph directed_graph;
        for (auto& win : detect_wins)
        {
            jtools::Node node(win.name, win.parent_win_name);
            directed_graph.AddNode(node);
        }
        std::vector<std::vector<std::string>> all_paths, cycles;
        directed_graph.TraverseAndDetectCycle(all_paths, cycles);
        if (cycles.size() > 0)
        {
            Log_ERROR("检测框之间存在循环依赖关系");
            return -1;
        }

        for (auto& path : all_paths)
        {
            std::vector<jrsdata::DetectWindow> wins;
            for (auto& name : path)
            {
                for (auto& win : detect_wins)
                {
                    if (win.name == name)
                    {
                        wins.emplace_back(win);
                        break;
                    }
                }
            }
            sorted_wins.emplace_back(wins);
        }
        return 0;
    }

    std::pair<int, int> ProjectDataProcess::GetSearchRegionSize(const jrsdata::DetectWindow& detect_window_)
    {
        // 2025.03.07 wangzhengkai 修复检测框尺寸传入错误问题
        auto size = GetSearchRegionSize({ detect_window_.width,detect_window_.height }, detect_window_.search_size);
        return { size.width,size.height };
    }

    cv::Size2f ProjectDataProcess::GetSearchRegionSize(const cv::Size2f& src_size_, const float& search_size_)
    {
        if (search_size_ < 10E-6)
        {
            return src_size_;
        }

        const int min_padding_size = 10;
        const int max_padding_size = 3000;

        int padding_size = (src_size_.width < src_size_.height) ? (int)(search_size_ * src_size_.width) : (int)(search_size_ * src_size_.height);

        if (padding_size < min_padding_size)
        {
            padding_size = min_padding_size;
        }
        else if (padding_size > max_padding_size)
        {
            padding_size = max_padding_size;
        }
        return { src_size_.width + padding_size * 2, src_size_.height + padding_size * 2 };
    }



    cv::RotatedRect ProjectDataProcess::GetRotatedRectByPadDirection(const cv::Point2f& component_center_point_,
        const cv::Point2f& pad_center_point_,
        const cv::RotatedRect& detect_window_rect_,
        const jrsdata::ComponentUnit::Direction& src_direction_,
        const jrsdata::ComponentUnit::Direction& obj_direction_)
    {
        //根据原方向和目标方向的距离  计算角度。
        if (src_direction_ == jrsdata::ComponentUnit::Direction::UNKNOWN || obj_direction_ == jrsdata::ComponentUnit::Direction::UNKNOWN)
            return  cv::RotatedRect();
        cv::Size2f res_rect;
        int angle = (static_cast<int>(obj_direction_) - static_cast<int>(src_direction_)) * 90;

        if (angle < 0)
        {
            angle += 360;
        }

        // 计算 ComponentUnit 的全局中心坐标
        cv::Point2f unit_global_center_point = component_center_point_ + pad_center_point_;

        // 计算 Detectsrc_detect_window_ 的全局中心坐标
        cv::Point2f detect_global_center = unit_global_center_point + detect_window_rect_.center;

        jcvtools::JrsHomMat2D jrs_hom_mat_2d;
        jrs_hom_mat_2d.AddHomMat2dRotate(angle, component_center_point_.x, component_center_point_.y);
        auto rotated_detect_center = jrs_hom_mat_2d.AffineTransPoint(detect_global_center);
        auto rotated_unit_center = jrs_hom_mat_2d.AffineTransPoint(unit_global_center_point);

        // 计算相对于 ComponentUnit 的新坐标
        cv::Point2f relative_rotated_detect_center_point = rotated_detect_center - rotated_unit_center;
        cv::Size2f rotate_size = detect_window_rect_.size;
        if (angle == 90 || angle == 270)
        {
            rotate_size.width = detect_window_rect_.size.height;
            rotate_size.height = detect_window_rect_.size.width;
        }

        return cv::RotatedRect{ relative_rotated_detect_center_point, rotate_size ,0 };
    }

    void ProjectDataProcess::SetResolution(float resolution_x_, float resolution_y_)
    {
        resolution_x = resolution_x_;
        resolution_y = resolution_y_;
    }

    void ProjectDataProcess::GetResolution(float& resolution_x_, float& resolution_y_)
    {
        resolution_x_ = resolution_x;
        resolution_y_ = resolution_y;
    }

    void ProjectDataProcess::UpdateSingleComponentInfo(const jrsdata::Component& new_component_)
    {
        auto component_ref = ReadComponentRef(new_component_.component_name, new_component_.subboard_name, new_component_.component_type);

        if (component_ref.has_value())
        {
            component_ref.value().get().x = new_component_.x;
            component_ref.value().get().y = new_component_.y;
            component_ref.value().get().angle = new_component_.angle;

        }
    }
    std::unordered_map<std::string, std::vector<std::string>>  ProjectDataProcess::GetNotDetectUnitName()
    {
        std::unordered_map<std::string, std::vector<std::string>> not_detect_unit_name;
        for (auto& part_num_value : _param->board_info.part_nums_and_detect_regions)
        {
            for (auto& unit_value : part_num_value.second.units)
            {
                if (!unit_value.enable)
                {
                    not_detect_unit_name[part_num_value.first].emplace_back(unit_value.unit_name);
                }
            }
        }
        return not_detect_unit_name;
    }

    void ProjectDataProcess::AppendProject(const jrsdata::ProjectParamPtr& new_project)
    {
        if (!new_project)
        {
            Log_ERROR("添加新工程失败，项目参数为空！");
            return;
        }
        multi_project_map[new_project->project_name] = new_project;
    }

    const std::map<std::string, jrsdata::ProjectParamPtr>& ProjectDataProcess::GetMultiProjectMap() const
    {
        return multi_project_map;
    }

    void ProjectDataProcess::SetMultiProjectMap(const std::map<std::string, jrsdata::ProjectParamPtr>& multi_project_map_)
    {
        multi_project_map = multi_project_map_;
    }

    void ProjectDataProcess::ClearProjectMap()
    {
        multi_project_map.clear();
    }
    void ProjectDataProcess::SetCurrentProjectLink(const std::string& link_project_name)
    {
        if (IsParamPtrEmpty())
        {
            return; //指针为空，请配置参数
        }
        _param->link_project_name = link_project_name;
    }
    void ProjectDataProcess::CancleCurrentProjectLink()
    {
        if (IsParamPtrEmpty())
        {
            return; //指针为空，请配置参数
        }
        _param->link_project_name = "";
    }
    std::list<std::string> ProjectDataProcess::GetProjectNameLists()
    {
        std::list<std::string> project_lists;
        for (auto& [project_name, project_ptr] : multi_project_map)
        {
            project_lists.emplace_back(project_name);
        }
        return project_lists;
    }
    std::string ProjectDataProcess::GetLinkedProjectName()
    {
        const auto& linked_name = _param->link_project_name;
        if (!linked_name)
        {
            return "";
        }
        return *linked_name;
    }
    bool ProjectDataProcess::IsLoadLinkProject(const std::string& link_project_name_)
    {

        if (multi_project_map.find(link_project_name_) != multi_project_map.end())
        {
            //! 工程存在
            return true;
        }
        else
        {
            //! 工程不存在
            return false;
        }
    }
    ProjectDataProcess::ProjectDataProcess()
        :_param(nullptr)
    {
    }

    ProjectDataProcess::~ProjectDataProcess()
    {
    }

    int ProjectDataProcess::GetNewComponentID(const std::vector<jrsdata::Component>& component_)
    {
        int max_id = 0;
        for (const auto& component : component_)
        {
            if (component.component_id > max_id)
            {
                max_id = component.component_id;
            }
        }
        return max_id + 1;
    }

    int ProjectDataProcess::SynchronousOtherSameSubboard(const jrsdata::Component& component_)
    {
        auto subboard = ReadSubBoardRef(component_.subboard_name);
        if (!subboard.has_value())
            return 1;

        auto x_offset = component_.x - subboard->get().x;
        auto y_offset = component_.y - subboard->get().y;

        for (auto& sub : _param->board_info.sub_board)
        {
            if (sub.subboard_name == component_.subboard_name) //相同子板加在这里！！！
                continue;

            jrsdata::Component new_component = component_;
            new_component.subboard_name = sub.subboard_name;
            new_component.x = sub.x + x_offset;
            new_component.y = sub.y + y_offset;

            float need_angle = sub.angle - subboard->get().angle;
            jrscore::CoordinateTransform::Rotate(new_component.x, new_component.y, new_component.x, new_component.y, sub.x, sub.y, need_angle);

            GenerateComponentName(new_component);

            switch (component_.component_type)
            {
            case jrsdata::Component::Type::CAD:
                new_component.component_name = jrscore::AOITools::GetPrefixString(component_.component_name) + "_" + std::to_string(sub.id);
                sub.component_info.emplace_back(std::move(new_component));
                break;

            case jrsdata::Component::Type::SUB_BADMARK:
                sub.bad_mark = std::move(new_component);
                break;

            case jrsdata::Component::Type::SUB_MARK:
                sub.sub_mark.emplace_back(std::move(new_component));
                break;

            case jrsdata::Component::Type::SUB_BARCODE:
                sub.barcode = std::move(new_component);
                break;
            }
        }
        return 0;

    }
    int ProjectDataProcess::SynchronousDeleteOtherSameSubboard(const jrsdata::Component& component_)
    {
        for (auto& subboard : _param->board_info.sub_board)
        {
            if (subboard.subboard_name == component_.subboard_name)
                continue; // 跳过同名的子板

            switch (component_.component_type)
            {
            case jrsdata::Component::Type::CAD:  /**<更新到其他子板*/
            {
                auto delete_name = jrscore::AOITools::GetPrefixString(component_.component_name) + "_" + std::to_string(subboard.id);
                // 使用 erase-remove 避免迭代器失效
                subboard.component_info.erase(
                    std::remove_if(subboard.component_info.begin(), subboard.component_info.end(),
                        [&](const jrsdata::Component& elem) {
                            return elem.component_name == delete_name;
                        }),
                    subboard.component_info.end());
            }
            break;

            case jrsdata::Component::Type::SUB_BADMARK:
                subboard.bad_mark = jrsdata::Component(); // 清空 bad_mark
                break;

            case jrsdata::Component::Type::SUB_MARK:
            {
                auto del_sub_mark = "sub_mark_" + std::to_string(subboard.id) + "_" + std::to_string(component_.component_id);

                subboard.sub_mark.erase(
                    std::remove_if(subboard.sub_mark.begin(), subboard.sub_mark.end(),
                        [&](const jrsdata::Component& elem) {
                            return elem.component_name == del_sub_mark;
                        }),
                    subboard.sub_mark.end());
            }
            break;

            case jrsdata::Component::Type::SUB_BARCODE:
                subboard.barcode = jrsdata::Component(); // 清空 barcode
                break;

            default:
                break;
            }
        }
        return jrscore::AOI_OK;
    }
    int ProjectDataProcess::SynchronousUpdateOtherSameSubboard(const jrsdata::Component& component_)
    {
        auto subboard = ReadSubBoardRef(component_.subboard_name);
        if (!subboard.has_value())
            return 1;

        auto x_offset = component_.x - subboard->get().x;
        auto y_offset = component_.y - subboard->get().y;

        for (auto& other_subboard : _param->board_info.sub_board) // 避免变量名混淆
        {
            if (other_subboard.subboard_name == component_.subboard_name) // 跳过相同子板
                continue;

            jrsdata::Component update_component = component_;
            update_component.subboard_name = other_subboard.subboard_name;
            update_component.x = other_subboard.x + x_offset;
            update_component.y = other_subboard.y + y_offset;

            // 统一在 switch 之前计算 ComponentName，避免重复调用
            GenerateComponentName(update_component);

            switch (component_.component_type)
            {
            case jrsdata::Component::Type::CAD:
            {
                update_component.component_name = jrscore::AOITools::GetPrefixString(component_.component_name) + "_" + std::to_string(other_subboard.id);
                auto it_component = std::find_if(other_subboard.component_info.begin(), other_subboard.component_info.end(),
                    [&](const jrsdata::Component& elem) { return elem.component_name == update_component.component_name; });

                if (it_component == other_subboard.component_info.end())
                {
                    Log_ERROR("同步更新 CAD 失败: 子板 [{}], 组件 [{}]", other_subboard.subboard_name, update_component.component_name);
                    continue;
                }

                *it_component = update_component;
            }
            break;

            case jrsdata::Component::Type::SUB_BADMARK:
                other_subboard.bad_mark = update_component; // 更新 bad_mark
                break;

            case jrsdata::Component::Type::SUB_MARK:
            {
                auto it_component = std::find_if(other_subboard.sub_mark.begin(), other_subboard.sub_mark.end(),
                    [&](const jrsdata::Component& elem) { return elem.component_name == update_component.component_part_number; });

                if (it_component == other_subboard.sub_mark.end())
                {
                    Log_ERROR("同步更新 SUB_MARK 失败: 子板 [{}], 组件 [{}]", other_subboard.subboard_name, update_component.component_part_number);
                    continue;
                }

                *it_component = update_component;
            }
            break;

            case jrsdata::Component::Type::SUB_BARCODE:
                other_subboard.barcode = update_component;
                break;

            default:
                break;
            }
        }

        return jrscore::AOI_OK;
    }
}

