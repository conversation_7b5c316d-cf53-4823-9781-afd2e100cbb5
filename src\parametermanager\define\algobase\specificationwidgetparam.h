/*****************************************************************//**
 * @file   specificationwidgetparam.h
 * @brief  zh-cn: 定义参数数据结构
 * @details
 * <AUTHOR>
 * @date 2024.10.07
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.07        <td>V1.0              <td>xailor      <td><EMAIL> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef JUDEPARAMDATA_H
#define JUDEPARAMDATA_H
#define CLEARSPECRESSYMBOL 311159
#define INVALID_RESULT 9999;

 // std
#include <map>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>

// custom
//#include "judgeparam.h"

// thirdparty
#include <iguana/iguana.hpp>


/**
* @class  ComponentsType
* @brief  规格组件类型枚举
* @param  NONE 空
* @param  SINGLELIMIT 单阈值判定
* @param  CHAR 字符(文字)结果
* @param  DOULIMIT 双阈值判定
* @param  COMBOXWITHQLINETEXT 下拉框判定
* @param  NUMBERDOULIMIT 数字双阈值判定
* @date 2024.10.07
* <AUTHOR>
*/
enum class ComponentsType
{
    NONE, //<< 空
    SINGLELIMIT, //<< 单阈值规格组件 标准值 + 百分比
    CHAR, //<< 字符规格组件 字符串
    DOULIMIT, //<< 双阈值规格组件 标准值 + 偏移量
    COMBOXWITHQLINETEXT, //<< 字符串 + 标准值
    NUMBERDOULIMIT, // 下限 + 上限
};

/**
* @class  JudgeResult
* @brief  判定结果枚举
* @param  DEFAULT 默认
* @param  TRUE PASS
* @param  NG NG
* @date 2024.11.21
* <AUTHOR>
*/
enum class JudgeResult
{
    DEFAULT, //<< 默认
    PASS, //<< PASS
    NG, //<< NG
};

struct FastComboxSpecificationParam
{
    std::string name; // AI规格组件的名称
    float thre;     //AI规格组件的值
    YLT_REFL(
        FastComboxSpecificationParam,
        name,
        thre
    )
};

struct FastSpecificationParam
{
    // 快速规格管控参数结构体
    std::string name; // 预设规格管控参数名字
    std::unordered_map<std::string, std::pair<double, int>> single_values; // 单限制规格组件的名称和值
    std::unordered_map<std::string, std::pair<double, double>> double_values; // 双限制规格组件的名称和值
    std::unordered_map<std::string, std::pair<double, double>> num_double_values; // 数字双限制规格组件的名称和值
    FastComboxSpecificationParam combox_value; // AI规格组件的名称和值
    YLT_REFL(
        FastSpecificationParam,
        name,
        single_values,
        double_values,
        num_double_values,
        combox_value
    )
};
using VecSpecificationParams = std::vector<FastSpecificationParam>;

/**
* @class SpecificaTransType
* @brief  规格组件转换类型枚举
* @param  DEFAULT 默认 存在上下限规格 除缺陷、类单阈值规格外
* @param  DEFECTRANS 缺陷规格转换
* @param  UPLIMIT 单阈值规格转上限
* @param  DOWNLIMIT 单阈值规格转下限
* @date 2024.11.19
* @note
* <AUTHOR>
*/
enum class SpecificaTransType : int
{
    DEFAULT, //<< { SINGLELIMIT:(-std_value*percent~std_value*percent) }  { DOULIMIT:(std_value-offset~std_value+offset) } { NUMBERDOULIMIT: (min_vlaue,max_value) }
    DEFECTRANS, //<< 缺陷规格 算法需要显示缺陷时使用
    UPLIMIT, //<<  { SINGLELIMIT + UPLIMIT: (std_value*percent,std_value) } { DOULIMIT + UPLIMIT：(std_value, std_value + offset) } { COMBOXWITHQLINETEXT + UPLIMIT: (-&, std_value) }
    DOWNLIMIT //<< { SINGLELIMIT + DOWNLIMIT: (0, std_value*percent } { DOULIMIT + DOWNLIMIT：(std_value-offset, std_value) } { COMBOXWITHQLINETEXT + UPLIMIT: (std_value, +&) }
};

struct SpecificationWidgetParam
{
    // 规格组件参数结构体
    std::string param_name; // 单个规格组件名字
    SpecificaTransType specifica_trans_type = SpecificaTransType::DEFAULT; // 规格组件转换类型
    ComponentsType type; // 单个规格组件的类型
    int spec_current_row = -1; // 当前规格所在的行
    bool is_hide_variabel_section = true; // 是否隐藏规格中可变的部分
};
using SpecificationWidgetParams = std::vector<SpecificationWidgetParam>;

/**
* @class SpecificaParamBase
* @brief  规格组件参数基类
* @param  name 规格组件名称
* @param  std_value 标准值
* @param  algo_res 算法结果
* @param  judge_res 判定结果
* @param  judge_value 判定值
* @date 2024.10.07
* <AUTHOR>
*/
class  SpecificaParamBase
{
public:
    std::string name;
    double std_value = 0.0f;
    double algo_res = 0.0f;
    JudgeResult judge_res = JudgeResult::DEFAULT;
    double judge_value = 0.0f;
    ComponentsType type = ComponentsType::NONE;
    SpecificaTransType specifica_trans_type = SpecificaTransType::DEFAULT;

    double min_value = 0.0; //<< 最小值
    double max_value = 0.0; //<< 最大值

    // 字符规格 brief: 字符串比较 除字母或者数字外的位置默认表示相同!!! 推荐使用*标记
    std::vector<std::string> char_std_values = {}; //<< 字符标准值
    std::vector<std::string> char_cur_values = {}; //<< 字符测量值

    // 缺陷类型 用于算法界面缺陷输出
    std::unordered_map<std::string, bool> defect_and_status = {}; //<< 缺陷及当前的状态

    YLT_REFL(
        SpecificaParamBase,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        /**
        * @fun JudgeParam
        * @brief  用于将规格组件值转换为判定参数
        * @param  std_value 标准值
        * @param  algo_res 算法结果
        * @param  judge_res 判定结果
        * @param  judge_value 判定值
        * @date 2024.10.07
        * <AUTHOR>
        */
        // virtual void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT) = 0;
         /**
         * @fun    Judge
         * @brief  更新判定结果
         * @date   2024.11.19
         * <AUTHOR>
         */
        void Judge();
    /**
     * @fun    Empty
     * @brief  判断规格是否为空
     * @date   2024.11.21
     * <AUTHOR>
     */
    bool Empty();
    /**
     * @fun    CompareStrings
     * @brief  字符串比较 除字母或者数字外的位置默认表示相同!!! 推荐使用*标记
     * @date   2024.11.21
     * <AUTHOR>
     */
    bool CompareStrings(const std::string& str1, const std::string& str2);

    SpecificaParamBase() {}
    virtual ~SpecificaParamBase() = default;
};
using SpecificaParamBasePtr = std::shared_ptr<SpecificaParamBase>;

// std::string 当前组件的名字
using SpecificationComponentDatas = std::unordered_map<std::string, SpecificaParamBasePtr>;

// SINGLELIMIT
/**
* @class SingleSpecificationComponentData
* @brief  单限制规格组件
* @param  percent_value 通过标准值计算判定值的百分比
* @date 2024.10.07
* <AUTHOR>
*/
class SingleSpecificationComponentData : public SpecificaParamBase
{
public:
    int percent_value;
    YLT_REFL(
        SingleSpecificationComponentData,
        percent_value,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::UPLIMIT);
};

// DOUBLELIMIT
/**
* @class  DoubleSpecificationComponentData
* @brief  双限制规格组件
* @param  offset 使用标准值计算上下限的偏移量
* @date   2024.10.07
* <AUTHOR>
*/
class DoubleSpecificationComponentData : public SpecificaParamBase
{
public:
    double offset = 0.0;

    YLT_REFL(
        DoubleSpecificationComponentData,
        offset,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT);
};

// CHAR
/**
* @class CharSpecificationComponentData
* @brief  字符规格组件
* @param  char_std_value 字符规格组件的名称和规格
* @date 2024.10.07
* <AUTHOR>
*/
class CharSpecificationComponentData : public SpecificaParamBase
{
public:
    std::unordered_map<std::string, JudgeResult> char_std_value;
    int combox_current_id = -1; // combox 当前的id
    std::vector<std::string> combox_string; // combox 字符串组
    YLT_REFL(
        CharSpecificationComponentData,
        combox_current_id,
        combox_string,
        char_std_value,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT);
};

// COMBOXWITHQLINETEXT
/**
* @class COMBOXWITHQLINETEXT
* @brief  下拉规格组件
* @param  combox_std_value 下拉规格组件的名称和值
* @date 2024.10.07
* <AUTHOR>
*/
class ComboxSpecificationComponentData : public SpecificaParamBase
{
public:

    std::string current_combox_name;
    std::unordered_map<std::string, double> combox_std_value;
    YLT_REFL(
        ComboxSpecificationComponentData,
        current_combox_name,
        combox_std_value,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT);
};

// COMBOXWITHDEFECT
/**
* @class  NumDoubleSpecificationComponentData
* @brief  数字双限制规格组件
* @param  min 下限
* @param  max 上限
* @date   2024.10.14
* <AUTHOR>
*/
class NumDoubleSpecificationComponentData : public SpecificaParamBase
{
public:
    double min = 0.0;
    double max = 0.0;
    YLT_REFL(
        NumDoubleSpecificationComponentData,
        min,
        max,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT);
};

class ComboxWithDefectsComponentData : public SpecificaParamBase
{
public:
    int current_combox_id;
    std::vector<std::string> combox_string;
    YLT_REFL(
        ComboxWithDefectsComponentData,
        current_combox_id,
        combox_string,
        name,
        std_value,
        algo_res,
        judge_res,
        judge_value,
        min_value,
        max_value,
        char_std_values,
        char_cur_values,
        defect_and_status,
        type
    )
        void GetJudgeParams(SpecificaTransType type = SpecificaTransType::DEFAULT);
};


// Combox规格值结构体
struct ComboxSpecValue
{
    std::string current_combox_name;
    std::unordered_map<std::string, double> combox_std_value;
    YLT_REFL(
        ComboxSpecValue,
        current_combox_name,
        combox_std_value
    )
};

// 字符规格值结构体
struct CharSpecValue
{
    std::unordered_map<std::string, JudgeResult> char_std_value;
    YLT_REFL(
        CharSpecValue,
        char_std_value
    )
};

/**
* @class  ControlSpec
* @brief  规格参数基类 方便用于反射库以及算法封装
* @param  ComponentsType 规格控件类型
* @param  SpecificaTransType 规格控件转判定参数类型
* @param  min 规格下限
* @param  max 上限
* @param  algo_res 算法结果
* @param  judge_res 判定结果
* @param  std_value 标准值
* @param  offset 偏移量
* @param  percent_value 百分比值
* @param  combox_value 下拉框值 ai模型使用 combox+line(num)
* @param  char_value 字符值和状态 显示当前字符规格的值和状态 缺陷以及显示ocr、条码结果
* @param  char_std_values 字符标准值
* @param  char_rec_values 字符检测值
* @date   2024.12.11
* <AUTHOR>
*/
class ControlSpec
{
public:
    ControlSpec() : type(ComponentsType::NONE), trans_type(SpecificaTransType::DEFAULT) {}
    class SpecBuilder;
    static SpecBuilder CreateSpec(ComponentsType type, SpecificaTransType trans_type);

    ComponentsType type; // 规格控件类型
    SpecificaTransType trans_type; // 规格控件转判定参数类型
    std::optional<double> spec_min = std::nullopt; // 规格下限
    std::optional<double> spec_max = std::nullopt; // 规格上限
    std::optional<double> algo_res = std::nullopt; // 算法结果
    std::optional<int> judge_res = std::nullopt;  // 判定结果 0 false 1 true -1 default

    std::optional<double> std_value = std::nullopt; // 控件标准值
    std::optional<double> offset = std::nullopt; // 控件偏移量
    std::optional<int> percent_value = std::nullopt; // 百分比值
    std::optional<ComboxSpecValue> combox_value = std::nullopt; // 下拉框值 ai模型使用 combox+line(num)
    std::optional<CharSpecValue> char_value = std::nullopt; // 字符值和状态 显示当前字符规格的值和状态 缺陷以及显示ocr、条码结果
    std::optional<std::vector<std::string>> char_std_values = std::nullopt; // 用于存储字符串组 可用于ocr等
    std::optional<std::vector<std::string>> char_rec_values = std::nullopt; // 字符检测值 ocr使用
    std::optional<int> spec_row = std::nullopt; // 当前规格所在的行
    std::optional<std::string> spec_name = std::nullopt; // 当前规格的名称
    std::optional<int> combox_current_id = std::nullopt; // combox 当前的id
    std::optional<bool> is_hide_variabel_section = std::nullopt; // combox 当前的id

    /**
    * @fun  Judge
    * @date   2024.12.11
    * <AUTHOR>
    */
    void Judge(bool use_spec_judge = true);
    /**
    * @fun  Judge
    * @brief  更新算法结果和判定结果(数值型) 算法赋值和规格初始化没有共同对象时调用
    * @note 算法赋值和规格初始化没有共同对象时调用
    * @param  det_algo_res 检测时算法结果
    * @date   2024.12.11
    * <AUTHOR>
    */
    void Judge(double det_algo_res);
    /**
    * @fun  Judge
    * @brief  更新算法结果和判定结果(ocr、条码等字符规格)
    * @note 算法赋值和规格初始化没有共同对象时调用
    * @param  rec_char_values 字符识别结果
    * @date   2024.12.11
    * <AUTHOR>
    */
    void Judge(std::vector<std::string> rec_char_values);
    /**
    * @fun  SetDefectStatus
    * @brief  设置缺陷状态 用于算法界面显示缺陷的状态也可用于ocr、条码等字符的状态
    * @note  算法赋值和规格初始化没有共同对象时调用
    * @param  rec_char_values 字符识别结果
    * @date   2024.12.11
    * <AUTHOR>
    */
    void SetDefectStatus(const std::unordered_map<std::string, bool>& defect_status);
    YLT_REFL(
        ControlSpec,
        type,
        trans_type,
        spec_min,
        spec_max,
        algo_res,
        judge_res,
        std_value,
        offset,
        percent_value,
        combox_value,
        char_value,
        char_std_values,
        char_rec_values
    )

private:
    /**
    * @fun  ControlSpec
    * @brief  私有构造函数
    * @date   2024.12.11
    * <AUTHOR>
    */
    ControlSpec(ComponentsType type, SpecificaTransType trans_type)
        : type(type), trans_type(trans_type) {}   
    /**
    * @fun  CompareStrings
    * @brief  比较字符串
    * @date   2024.12.11
    * <AUTHOR>
    */
    bool CompareStrings(const std::string& str1, const std::string& str2);
    /**
    * @fun  JaccardSimilarity
    * @brief  计算两个字符串的jaccard相似度
    * @date   2025.01.13
    * <AUTHOR>
    */
    double JaccardSimilarity(const std::string& str1, const std::string& str2);

    double m_jaccard_similarity = 0.0;
    std::string m_max_similarity_jaccard = "ocr rec error";
    friend class SpecBuilder;
};

/**
* @class  SpecBuilder
* @brief  用于构建和初始化规格ui
* @date   2024.12.11
* <AUTHOR>
*/
class ControlSpec::SpecBuilder
{
public:
    /**
    * @fun  Builder
    * @brief  构造函数
    * @date   2024.12.11
    * <AUTHOR>
    */
    explicit SpecBuilder(ComponentsType type, SpecificaTransType trans_type)
        : m_spec(type, trans_type) {}
    /**
    * @fun  SetMin
    * @brief  设置双数字（直接存在范围值）规格的下限
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetMin(double value);
    /**
    * @fun  SetMax
    * @brief  设置双数字规格（直接存在范围值）的上限
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetMax(double value);
    /**
    * @fun  SetStdValue
    * @brief  设置单限制和双限制(偏移量)的标准值
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetStdValue(double value);
    /**
    * @fun  SetAlgoValue
    * @brief  设置算法结果(一般在创建规格ui时不直接设置)
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetAlgoValue(double value);
    /**
    * @fun  SetOffset
    * @brief  设置双限制（标准值+偏移量）的偏移量
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetOffset(double value);
    /**
   * @fun  SetPercentValue
   * @brief  设置单限制（标准值+百分比）的百分比值
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetPercentValue(int value);
    /**
   * @fun  SetComboxValue
   * @brief  combox+line(num) 规格的下拉框值 一般用于ai模型的调用
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetComboxValue(const std::string& current_name,
        const std::unordered_map<std::string, double>& std_values);
    /**
   * @fun  SetComponentsType
   * @brief  设置规格控件类型
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetComponentsType(ComponentsType type);
    /**
   * @fun  SetSpecificaTransType
   * @brief  设置规格控件转换判定参数类型
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetSpecificaTransType(SpecificaTransType type);
    /**
   * @fun  SetStdCharValue
   * @brief  设置字符规格的标准值
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetStdCharValue(const std::vector<std::string>& char_values);
    /**
   * @fun  SetSpecRow
   * @brief  设置规格所在的行数
   * @date   2024.12.11
   * <AUTHOR>
   */
    SpecBuilder& SetSpecRow(const int row);
    /**
      * @fun  SetRecCharValue
      * @brief  设置字符规格的检测值
      * @date   2024.12.11
      * <AUTHOR>
      */
    SpecBuilder& SetRecCharValue(const std::vector<std::string>& char_values);
    /**
     * @fun  SetDefectStatus
     * @brief  设置缺陷状态 用于ocr、条码等字符规格的缺陷输出
     * @date   2024.12.11
     * <AUTHOR>
     */
    SpecBuilder& SetDefectStatus(const std::unordered_map<std::string, JudgeResult>& judge_values);
    /**
    * @fun  SetDefectStatus
    * @brief  设置缺陷状态 用于ocr、条码等字符规格的缺陷输出
    * @date   2024.12.11
    * <AUTHOR>
    */
    SpecBuilder& SetSpecName(std::string name);
    /**
   * @fun  SetComboxSpecId
   * @brief  设置combox当前的id
   * @date   2025.01.13
   * <AUTHOR>
   */
    SpecBuilder& SetComboxSpecId(int id);
    /**
  * @fun  SetHideVariabelSection
  * @brief  设置是否隐藏规格可变部分
  * @date   2025.01.13
  * <AUTHOR>
  */
    SpecBuilder& SetHideVariabelSection(bool is_hide);
    /**
    * @fun  Build
    * @brief  构建规格ui 在初始化规格ui的值后调用
    * @date   2024.12.11
    * <AUTHOR>
    */
    ControlSpec Build();

private:
    ControlSpec m_spec; // 规格参数结构体 用以存储规格参数
};

// 静态创建方法实现
/**
* @fun  CreateSpec
* @brief  构建规格ui 在创建规格ui的类型(规格类型以及转换类型)时调用
* @date   2024.12.11
* <AUTHOR>
*/
inline ControlSpec::SpecBuilder ControlSpec::CreateSpec(ComponentsType type, SpecificaTransType trans_type)
{
    return SpecBuilder(type, trans_type);
}

#endif // JUDEPARAMDATA_H