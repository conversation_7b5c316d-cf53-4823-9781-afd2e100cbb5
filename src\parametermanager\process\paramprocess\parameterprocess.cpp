#include "parameterprocess.hpp"

#include "jsonoperator.hpp"

int jrsparam::ParameterProcess::SetSettingParams(const jrsdata::SettingViewParamPtr& param_)
{
    if (IsParamPtrEmpty()||!param_)
    {
        Log_ERROR("更新设置参数失败，");
        return -1;
    }
    _param_ptr = param_;
    return jrscore::AOI_OK;
}

int jrsparam::ParameterProcess::SetSystemSettingParams(const jrsdata::SystemParam& param_)
{
    if (IsParamPtrEmpty())
    {
        Log_ERROR("更新设置参数失败，");
        return -1;
    }
    _param_ptr->sys_param = param_;
    return jrscore::AOI_OK;
}

int jrsparam::ParameterProcess::SetMachineSettingParams(const jrsdata::MachineParam& param_)
{
    if (IsParamPtrEmpty())
    {
        Log_ERROR("更新设置参数失败，");
        return -1;
    }
    _param_ptr->machine_param = param_;
    return jrscore::AOI_OK;
}

int jrsparam::ParameterProcess::SetMachineStateParams(const jrsdata::SystemStateMap& param_)
{
    _sys_state_map = param_;
    return jrscore::AOI_OK;
}

//jrsdata::JrsVariant jrsparam::ParameterProcess::GetSettingParamValueByName(const jrsdata::ParamLevel level_, const std::string& item_name_)
//{
//    const jrsdata::SettingParamMap* setting_param_map = nullptr;
//
//    if (level_ == jrsdata::ParamLevel::SYSTEM)
//    {
//        setting_param_map = &_param_ptr->sys_param.sys_params;
//    }
//    else if (level_ == jrsdata::ParamLevel::MACHINE)
//    {
//        setting_param_map = &_param_ptr->machine_param.machine_params_data;
//    }
//
//    if (!setting_param_map)
//    {
//        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 获取参数失败（level无效）！");
//        return {};
//    }
//
//    auto it = setting_param_map->find(item_name_);
//    if (it == setting_param_map->end())
//    {
//        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 获取参数失败（未找到项）！");
//        return {};
//    }
//
//    const auto& param = it->second;
//    try
//    {
//        if (param.param_type == "int")
//        {
//            return std::stoi(param.param_value);
//        }
//        else if (param.param_type == "float")
//        {
//            return std::stof(param.param_value);
//        }
//        else if (param.param_type == "double")
//        {
//            return std::stod(param.param_value);
//        }
//        else if (param.param_type == "string")
//        {
//            return param.param_value;
//        }
//        else if (param.param_type == "bool")
//        {
//            return (param.param_value == "true" || param.param_value == "1");
//        }
//        else if (param.param_type == "vector<int>")
//        {
//            std::vector<int> vec;
//            std::istringstream iss(param.param_value);
//            std::string token;
//            while (std::getline(iss, token, ','))
//            {
//                if (!token.empty())
//                    vec.push_back(std::stoi(token));
//            }
//            return vec;
//        }
//        else
//        {
//            Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 类型不支持: " + param.param_type);
//            return {};
//        }
//    }
//    catch (const std::exception& e)
//    {
//        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, std::string("解析参数失败：") + e.what());
//        return {};
//    }
//}

int jrsparam::ParameterProcess::ReplaceSettingParamValueByName(const jrsdata::ParamLevel level_,
    const std::string& item_name_,
    const jrsdata::JrsVariant& value_, const std::string& explain_)
{

    if (item_name_.empty())
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "设置参数失败：item_name_ 为空！");
        return -2;
    }
    jrsdata::SettingParamMap* setting_param_map = nullptr;
    if (level_ == jrsdata::ParamLevel::SYSTEM)
    {
        setting_param_map = &_param_ptr->sys_param.sys_params;
    }
    else if (level_ == jrsdata::ParamLevel::MACHINE)
    {
        setting_param_map = &_param_ptr->machine_param.machine_params_data;
    }

    if (!setting_param_map)
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 设置参数失败（level无效）！");
        return -1;
    }

    auto& param = (*setting_param_map)[item_name_];

    try
    {
        std::visit([&](auto&& arg) {
            using T = std::decay_t<decltype(arg)>;
            if constexpr (std::is_same_v<T, int>)
            {
                param.param_type = "int";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, float>)
            {
                param.param_type = "float";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, double>)
            {
                param.param_type = "double";
                param.param_value = std::to_string(arg);
            }
            else if constexpr (std::is_same_v<T, std::string>)
            {
                param.param_type = "string";
                param.param_value = arg;
            }
            else if constexpr (std::is_same_v<T, bool>)
            {
                param.param_type = "bool";
                param.param_value = arg ? "true" : "false";
            }
            else if constexpr (std::is_same_v<T, std::vector<int>>)
            {
                param.param_type = "vector<int>";
                std::ostringstream oss;
                for (size_t i = 0; i < arg.size(); ++i)
                {
                    if (i > 0) oss << ",";
                    oss << arg[i];
                }
                param.param_value = oss.str();
            }
            else
            {
                throw std::runtime_error("不支持的类型");
            }
            }, value_);
        param.param_exp = explain_;
        param.param_level = static_cast<int>(level_);
    }
    catch (const std::exception& e)
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, std::string("设置参数异常：") + e.what());
        return -3;
    }
    return jrscore::AOI_OK;

}

jrsdata::MachineCheckParamInfo jrsparam::ParameterProcess::GetMachineStateParam(const std::string& item_name_)
{
    if (item_name_.empty())
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "设置参数失败：item_name_ 为空！");
        return {};
    }
    auto state_param = _sys_state_map.find(item_name_);
    if (state_param == _sys_state_map.end())
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 获取系统参数失败！");
        return  jrsdata::MachineCheckParamInfo{};
    }
    return state_param->second;

}

int jrsparam::ParameterProcess::ReplaceMachineStateParam(const std::string& item_name_, const jrsdata::MachineCheckParamInfo& machine_check_param_info_)
{
    if (item_name_.empty())
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "设置参数失败：item_name_ 为空！");
        return -1;
    }
    _sys_state_map[item_name_] = machine_check_param_info_;
    return jrscore::AOI_OK;
}



jrsparam::ParameterProcess::ParameterProcess()
    :_param_ptr(std::make_shared<jrsdata::SettingViewParam>())
{
    //_param_ptr->event_name = jrsaoi::ALL_PARAM_UPDATE_EVENT_NAME;
    //_param_ptr->module_name = jrsaoi::OPERATE_MODULE_NAME;
    //_param_ptr->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    //_param_ptr->sub_name = jrsaoi::OPERATE_LOGIC_SUB_NAME;
    _param_ptr->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
}

jrsparam::ParameterProcess::~ParameterProcess()
{

}

std::shared_ptr<jrsdata::SettingViewParam>& jrsparam::ParameterProcess::GetSettingParams()
{
    return _param_ptr;
}

std::string jrsparam::ParameterProcess::GetSettingParamExplanByName(const jrsdata::ParamLevel level_, const std::string& item_name_)
{
    const jrsdata::SettingParamMap* setting_param_map = nullptr;
    if (level_ == jrsdata::ParamLevel::SYSTEM)
    {
        setting_param_map = &_param_ptr->sys_param.sys_params;
    }
    else if (level_ == jrsdata::ParamLevel::MACHINE)
    {
        setting_param_map = &_param_ptr->machine_param.machine_params_data;
    }

    if (!setting_param_map)
    {
        Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "参数获取失败（无效Level）！");
        return "";
    }
    auto iter = setting_param_map->find(item_name_);
    if (iter != setting_param_map->end())
    {
        return iter->second.param_exp;
    }
    return "";
}

std::pair<bool, std::string> jrsparam::ParameterProcess::GetMachinePathAndEnable(const std::string& path_jason_)
{
    auto res_path_str = jrscore::ParseJson(path_jason_, "directory");
    //if (res_path_str.empty())
    //{
    //    Log_ERROR("获取指定参数路径异常！");
    //    return {};
    //}
    auto enable_str = jrscore::ParseJson(path_jason_, "enable");

    const bool is_enabled = (enable_str == "true");

    return { is_enabled ,res_path_str };
}

