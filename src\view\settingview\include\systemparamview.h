#ifndef __JRSSYSTEMPARAMVIEW_H__
#define __JRSSYSTEMPARAMVIEW_H__

//prebuild
#include "pch.h"
//STD
#include <any>
//QT
//#include <QWidget>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QAction>
#include <QFileDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QToolButton>
//Custom
//#include "viewparam.hpp"
#include "ui_systemparamview.h"

QT_BEGIN_NAMESPACE
namespace Ui { class systemparamviewClass; };
QT_END_NAMESPACE

namespace jrsaoi
{
    using ControlMap = std::variant<QLineEdit*, QCheckBox*>;
    class SystemParamView : public QWidget
    {
        Q_OBJECT
    public:
        SystemParamView(QWidget* parent = nullptr);
        ~SystemParamView();
        /**
         * @fun UpdateView
         * @brief 从数据库获取数据更新界面
         * @date 2024.8.25
         * <AUTHOR>
         */
        void UpdateView(const jrsdata::SettingParamMap& system_params_);
        /**
         * @fun GetSystemParam
         * @brief 获取当前系统参数
         * @return
         * @date 2024.8.25
         * <AUTHOR>
         */
        jrsdata::SettingParamMap GetSystemParam();
    private slots:
        /**
         * @fun SlotSaveSystem
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotSaveSystem();
        /**
         * @fun SlotChooseDirPath 
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void SlotChooseDirPath();
    signals:
        /**
         * @fun SigSaveSystem
         * @brief 隐藏控件
         * @param system_params_
         * @date 2024.8.25
         * <AUTHOR>
         */
        void SigSaveSystem(const jrsdata::SettingParamMap& system_params_);
    private:
        /**
         * @fun InitView 
         * @brief
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun InitMember
         * @brief 初始化成员属性
         * @date 2024.8.25
         * <AUTHOR>
         */
        void InitMember();
        /**
         * @fun InitConnect
         * @brief 初始化槽函数连接
         * @date 2024.8.25
         * <AUTHOR>
         */
        void InitConnect();
        /**
         * @fun SaveRemoteParam
         * @brief 保存数据
         * @date 2024.8.25
         * <AUTHOR>
         */
        void SaveSystemParam();
        /**
         * @fun UpdateStaticParam
         * @brief 更新静态参数界面
         * @date 2024.8.25
         * <AUTHOR>
         */
        void UpdateStaticParam();
        /**
         * @fun SetHideController
         * @brief 隐藏控件
         * @param system_param_
         * @date 2024.8.25
         * <AUTHOR>
         */
        void SetHideController(const jrsdata::SettingParamMap& system_param_);
    private:
        std::map<std::string, QWidget*> ui_map_value; /**< 系统参数到控件按钮的映射 系统参数名称---对应的UI上控件*/
        std::map<std::string, QLineEdit*> map_shared_path;
        std::vector<QWidget*> ui_need_hide_controller;/**< 需要隐藏的控件 */
        Ui::systemparamviewClass* ui;
        jrsdata::SettingParamMap _system_params;
    };
}
#endif // !__JRSSYSTEMPARAMVIEW_H__