﻿//STD
#include <filesystem>
//Custom
#include "coreapplication.h"

//Thirdparty
#pragma warning(push, 1)
#include "gtest/gtest.h"
#pragma warning(pop)

#ifdef _WIN32
#include <Windows.h>
#endif // _WIN32

/**
 * @fun GetExecutableDirectory 
 * @brief 获取当前exe所运行的目录
 * @return 
 * @date 2024.3.15
 * <AUTHOR>
 */
std::string GetExecutableDirectory()
{
    std::filesystem::path currentPath = std::filesystem::current_path();
    auto log_path = currentPath.string() + "//log";
   
    
    if (!std::filesystem::exists (log_path))
    {
        std::filesystem::create_directories (log_path);
    }
    return log_path;

    
}

class LogUnitTest :public::testing::TestWithParam<std::string>
{
    protected:
        void SetUp() override
        {
            

        }
        void TearDown() override
        {

        }
                
      
};

TEST_F(LogUnitTest, set_log_path)
{
    std::string path = GetExecutableDirectory();
    AOICoreApp->GetLogManager()->SetLogFolderPath(path);
    AOICoreApp->GetLogManager()->CreateDefaultLogger();
    
    EXPECT_EQ(AOICoreApp->GetLogManager()->GetFolderPath(), path);
    ASSERT_NE(AOICoreApp->GetLogManager(), nullptr);
}

TEST_F(LogUnitTest, test_create_logger)
{
    AOICoreApp->GetLogManager()->SetLogOutputLevel(jrscore::LogLevel::LEVEL_INFO);
    AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_INFO,"hello unit test!");
    auto thread_test = std::thread([&]()
                                  {
                                      AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_TRACE, "Hello2");
                                        
                                      AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_INFO, "Hello3");
                                      AOICoreApp->GetLogManager()->SetLogOutputLevel(jrscore::LogLevel::LEVEL_TRACE);
                                      AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_TRACE, "Hello4");
                                  });

    thread_test.join();
    
}
TEST_P(LogUnitTest, LogMessage)
{
    std::string message = GetParam();
    AOICoreApp->GetLogManager()->Log(jrscore::LogLevel::LEVEL_INFO, message);
   
}
INSTANTIATE_TEST_SUITE_P(LogMessage, LogUnitTest, testing::Values(

    "param 1",
    "param 2"
));
