/*********************************************************************
 * @brief  包含图形派生类.
 *
 * @file   graphicsobject.h
 *
 * @date   2024.03.01
 * <AUTHOR>
**********************************************************************/
#pragma once

#ifndef GRAPHICS_OBJECT_H
#define GRAPHICS_OBJECT_H

#include "graphicsapi.hpp"
#include "graphicsabstract.hpp"
#include "transientobjectcontrol.hpp"
#include "rvec.hpp"

/**
 * @brief 矩形
 */
class GRAPHICS_API RectGraphics : public GraphicsAbstract, public TransientObjectControl<RectGraphics>
{
public:
    RectGraphics() : GraphicsAbstract() {}
    RectGraphics(const std::string& id_) : GraphicsAbstract(id_) {}
    RectGraphics(float x, float y, float width, float height, float angle)
        : GraphicsAbstract(x, y, width, height, angle)
    {
    }
    RectGraphics& operator=(const RectGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        return *this;
    }

    GraphicsFlag GetFlag() const override { return GraphicsFlag::rect; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    void Update() override;
    void UpdateDrawBuffer() override;
    void UpdateControlPoint()  override;
    void DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam&) override;
    int ResponseControlPoint(const ResponseEventParam&) override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;
    int ResponseEvent(const MouseEventValue&) override;
    std::shared_ptr<GraphicsAbstract> Clone() const override;
    std::string GetSerializedData() override;

private:
    void DrawInfo();

private:
    std::vector<Vec2> paths;  ///< 顶点缓存
    std::vector<Vec2> path_flags;  ///< 顶点缓存(标记点)
};

/**
 * @brief 圆形/椭圆
 */
class GRAPHICS_API CircleGraphics : public GraphicsAbstract, public TransientObjectControl<CircleGraphics>
{
public:
    CircleGraphics() : GraphicsAbstract() {}
    CircleGraphics(const std::string& id_) : GraphicsAbstract(id_) {}

    CircleGraphics(float x, float y, float width, float height, float angle)
        : GraphicsAbstract(x, y, width, height, angle)
    {
    }

    CircleGraphics& operator=(const CircleGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<CircleGraphics>(*this);
    }
    GraphicsFlag GetFlag() const override { return GraphicsFlag::circle; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;

private:
    std::vector<Vec2> paths; ///< 路径点
};

/**
 * @brief 文本
 */
class GRAPHICS_API TextGraphics : public GraphicsAbstract, public TransientObjectControl<TextGraphics>
{
public:
    TextGraphics() : GraphicsAbstract() {}
    TextGraphics(const std::string& id_) : GraphicsAbstract(id_) {}
    TextGraphics(float x, float y, float width, float height, float angle, const std::string& text_)
        : GraphicsAbstract(x, y, width, height, angle), text(text_)
    {
    }

    TextGraphics& operator=(const TextGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        text = other.text;
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<TextGraphics>(*this);
    }
    GraphicsFlag GetFlag() const override { return GraphicsFlag::text; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;

    std::string text;

private:
};

/**
 * @brief 直线
 */
class GRAPHICS_API LineGraphics : public GraphicsAbstract, public TransientObjectControl<LineGraphics>
{
public:
    LineGraphics() : GraphicsAbstract() {}
    LineGraphics(const std::string& id_) : GraphicsAbstract(id_) {}
    LineGraphics(float x, float y, float width, float height, float angle, const Vec2& start_, const Vec2& end_)
        : GraphicsAbstract(x, y, width, height, angle), start(start_), end(end_)
    {
    }
    LineGraphics& operator=(const LineGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        start = other.start;
        end = other.end;
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<LineGraphics>(*this);
    }

    GraphicsFlag GetFlag() const override { return GraphicsFlag::line; }
    void Draw(Renderer* r, const LayerConfig* config) override { (void)r;(void)config; }
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override { (void)r;(void)p;(void)config; };
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override { return {}; }
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override
    {
        (void)xstart;(void)ystart;(void)xend;(void)yend;(void)istemp;(void)attr;
        return -1;
    };

    Vec2 start;
    Vec2 end;

private:
    /*生成外接矩形*/
    void CreateBoundingBox(LineGraphics* obj);
    /*更新绘制路径*/
    void UpdateDrawPath(LineGraphics* obj);
};

/**
 * @brief 多边形
 */
class GRAPHICS_API PolygonGraphics : public GraphicsAbstract, public TransientObjectControl<PolygonGraphics>
{
public:
    PolygonGraphics() : GraphicsAbstract() {}
    PolygonGraphics(const std::string& id_) : GraphicsAbstract(id_) {}
    PolygonGraphics(float x, float y, float width, float height, float angle, const std::vector<Vec2>& contour_)
        : GraphicsAbstract(x, y, width, height, angle), contours(contour_)
    {
    }
    PolygonGraphics& operator=(const PolygonGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        contours = other.contours;
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<PolygonGraphics>(*this);
    }

    GraphicsFlag GetFlag() const override { return GraphicsFlag::polygon; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;
    // int ResponseControlPoint(const int &type, const float &xstart, const float &ystart, const float &xend, const float &yend, bool istemp) override;

    /*确认点 - 设置时点坐标为绝对坐标,这里将其转换成相对外接矩形中心的相对坐标*/
    void ComfirmPoint();
    /*设置点*/
    void SetPoint(const std::vector<Vec2>&, bool is_local = false);
    /*在末尾添加一个点*/
    void AddPoint(float x, float y, bool is_local = false);
    /*删除末尾点*/
    void DeletePoint();
    /*多边形是否闭合,将第一个点和最后一个点连接*/
    void SetClosed(bool state) { is_close = state; }

    std::vector<Vec2> contours; ///< 轮廓点

private:
    /*生成外接矩形*/
    void CreateBoundingBox(PolygonGraphics* obj);
    /*更新绘制路径*/
    void UpdateDrawPath(PolygonGraphics* obj);

    bool is_close = true;
    std::vector<Vec2> paths;
};

/**
 * @brief 贝塞尔曲线
 */
class GRAPHICS_API BezierGraphics : public GraphicsAbstract, public TransientObjectControl<BezierGraphics>
{
public:
    BezierGraphics() : GraphicsAbstract() {}
    BezierGraphics(const std::string& id_) : GraphicsAbstract(id_) {}

    BezierGraphics(float x, float y, float width, float height, float angle,
        Vec2 start_, Vec2 end_, const std::vector<Vec2>& controls_)
        : GraphicsAbstract(x, y, width, height, angle),
        start(start_), end(end_), controls(controls_), iscomfirmend(true)
    {
    }

    BezierGraphics& operator=(const BezierGraphics& other)
    {
        GraphicsAbstract::operator=(other);
        this->start = other.start;
        this->end = other.end;
        this->controls = other.controls;
        return *this;
    }

    std::shared_ptr<GraphicsAbstract> Clone() const override
    {
        return std::make_shared<BezierGraphics>(*this);
    }
    GraphicsFlag GetFlag() const override { return GraphicsFlag::Bezier; }
    void Draw(Renderer* r, const LayerConfig* config) override;
    void Draw(Renderer* r, Painter* p, const LayerConfig* config) override;
    std::string GetSerializedData() override;
    std::vector<ControlPoint> CreateControlPoint() override;
    int ResponseControlPoint(const float& xstart, const float& ystart,
        const float& xend, const float& yend,
        const bool& istemp,
        const ControlAttributes& attr) override;

    /*确认点 - 设置时点坐标为绝对坐标,这里将其转换成相对外接矩形中心的相对坐标*/
    void ComfirmPoint();
    /*设置起点*/
    void SetStart(float x, float y);
    /*设置终点*/
    void SetEnd(float x, float y, bool iscomfirm);
    /*设置曲线控制点*/
    void SetPoint(std::vector<Vec2>&, bool is_local = false);
    /*在末尾添加一个曲线控制点*/
    void AddPoint(float x, float y);
    /*删除末尾曲线控制点*/
    void DeletePoint();

    bool iscomfirmend = false;
    Vec2 start;
    Vec2 end;
    std::vector<Vec2> controls;

private:
    /*生成外接矩形*/
    void CreateBoundingBox();
    /*更新绘制路径*/
    void UpdateDrawPath(BezierGraphics* obj);

    std::vector<Vec2> select_bounding_contours;
    std::vector<Vec2> paths;
};

#endif //! GRAPHICS_OBJECT_H
