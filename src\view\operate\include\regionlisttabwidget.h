﻿/*********************************************************************
 * @brief  按模型管理检测框列表.
 *
 * @file   regionlisttabwidget.h
 *
 * @date   2024.09.18
 * <AUTHOR>
 *********************************************************************/
#ifndef REGIONLISTTABWIDGET_H
#define REGIONLISTTABWIDGET_H

#include "pch.h"
 //#include <QWidget>
#include <QTabWidget>
//#include "projectparam.hpp"
#include "regionlistwidget.h"
#include "customtabbar.h"

 //class DetectWindowListWidget;
class DetectModelWidget : public QTabWidget
{
    Q_OBJECT
public:
    explicit DetectModelWidget(QWidget* parent = nullptr);

    DetectWindowListWidget* CreateModel(const QString& model_name, const QString& type_name);
    void DeleteModel(const QString& model_name);

    /**
     * @fun UpdateView
     * @brief 更新当前选中元件的算法列表，即界面上算法列表显示区域
     * @param model_name [IN] 模型组名称，即界面上tab的名称，有body，pad名称等
     * @param det_win_val [IN] 每个算法显示的算法检测框信息，如算法名，外扩区域，跟随等信息
     * <AUTHOR>
     * @date 2025.3.17
     */
    void UpdateView(const std::string& model_name, const QString& type_name, const DetectWindowItemValue& det_win_val);
    /**
     * @fun MoveTabToFirst
     * @brief 将指定名称的tab页，移动到第一行
     * @param tab_name [IN] tab页名称
     * <AUTHOR>
     * @date 2025.3.28
     */
    void MoveTabToFirst(const std::string& tab_name);
    /**
     * @fun SortTab
     * @brief 将tab 进行排序
     * <AUTHOR>
     * @date 2025.3.28
     */
    void SortTabsByCustomOrder();

    void SetCurSelectedDetectWinItemByWinName(const std::string& det_win_name, const std::string& model_name);
    void SetDetectWinItemVal(const DetectWindowItemValue& det_win_val, const std::string& model_name = "");
    void SetCurSelectedTable(const QString& table_name);
    void AddDetectWinItem(const DetectWindowItemValue& det_win_val);
    void SetAlgoList(const std::map<std::string, std::vector<std::pair<std::string, std::string>>>& _algo_list);
    void SetDefectList(const std::vector<std::string>& _defect_list);
    void GetModelTableNames(std::vector<std::string>& table_names);

    /**
     * @fun  SetFirstDetectWinSeleceted
     * @brief 设置默认选中第一个检测框.
     * @data 2024.12.27
     * <AUTHOR>
     */
    void SetFirstDetectWinSeleceted(const std::string& model_name_);

    void FindItemByWinName(const std::string& win_name, QListWidgetItem*& item, DetectWindowListWidget*& list, const std::string& model_name = "");
    DetectWindowListWidget* ReadModel(const QString& model_name);
    DetectWindowListWidget* GetCurrentModel();
    /**
     * @fun  Clear
     * @brief 清楚所有item，是否阻塞清除
     * @data 2025/4/16
     * <AUTHOR>
     */
    void Clear(bool is_block_signal = false);

    // 依据算法结果更新tab颜色
    void UpdateTabColorByAlgoResult(const std::string& model_name);

private:

    /**
    * @fun  SetTabBackgroundByName
    * @brief 根据tab的名字设置其背景色
    * @data 2025/4/24
    * <AUTHOR>
    */
    void SetTabBackgroundByName(const QString& tab_name, const QVariant& color);

    /**
     * @fun  SetTabBackgroundByIndex
     * @brief 根据tab的索引设置其背景色
     * @data 2025/4/24
     * <AUTHOR>
     */
    void SetTabBackgroundByIndex(int index, const QVariant& color);

    /**
     * @fun  GetDetectWindowListWidgets
     * @brief 获取指定tab名下的所有检测框widget
     * @param model_name [IN] tab名
     * @param lists std::vector<DetectWindowListWidget*> [OUT] 获取到的widget
     * @data 2025/4/25
     * <AUTHOR>
     */
    void GetDetectWindowListWidgets(const std::string& model_name, std::vector<DetectWindowListWidget*>& lists);


protected:
    void resizeEvent(QResizeEvent* event) override;

signals:
    void SignalModelItemValChanged(DetectWindowItemValue& det_win_info);
    void SignalModelItemDelete(const QString& model_name, const QString& window_name); ///< 检测框删除
    void SignalModelItemSelectedChanged(DetectWindowItemValue& det_win_info); ///< 改变选中的检测框
    void SigUpdateAllDetectStatus(const std::vector<DetectWindowItemValue>& all_det_win_info);
    //void SignalDetectModelSelectedChanged()

    /**
    * @fun SigDetectModelChange
    * @brief 检测模型组切换
    * @param detect_model_name [IN] 模型组名称
    * <AUTHOR>
    * @date 2025.3.18
    */
    void SigDetectModelChange(const QString& detect_model_name);
private slots:
    void SlotOnCurrentChanged(int index);
private:
    /**
    * @fun ModelNameBindCustomStr
    * @brief
    * @param detect_model_name [IN] 模型组名称
    * <AUTHOR>
    * @date 2025.3.18
    */
    std::string ModelNameBindCustomStr(const QString& detect_model_name);
    void InitConnect();
    std::vector<std::string> defect_list;
    std::map<std::string, std::vector<std::pair<std::string, std::string>>> algo_list;
    std::unordered_map<std::string, std::string> _model_name_mapping_custom_name;
    //void ClearAllTabSelected();

    CustomTabBar* m_custom_tabbar;
};

#endif // REGIONLISTTABWIDGET_H