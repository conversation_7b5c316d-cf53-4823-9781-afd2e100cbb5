
@startuml data层流程图
title data层流程图

start
:初始化事件管理器;
:初始化数据库;
:事件管理器;
if (是否已注册事件) then (是)
    :调用响应事件函数;
    if(是否已绑定回调)then(是)
        :响应回调函数;
    else (否)
        :提示：回调未绑定;
    endif
else (否)
    :提示：未注册响应事件;
endif
stop
@enduml


@startuml 检测结果输出
title 检测结果

start
:检测完成后，将检测数据填写到EntiretyBoard结构体内;
' while (检测FOV) is (是)
'     :检测当前FOV所有的元件;
'     :将元件检测结果信息存入EntiretyBoard内;
'     :发送结果保存事件到data层;
'     :data层进行数据新增;
' endwhile (否)
:发送结果保存事件到data层;
:data层进行board表数据更新并写入数据库;
stop
@enduml


@startuml data层概要类图
title data层概要类图
class FileHandle 
{
    +int Read(T& data_);
    +int Save(T& data_);
    -bool WriteFile(const std::string& path_,const std::string& content_);
    -bool ReadFile(const std::string& path,std::string& content_);
}

class DBManagers
{
    +static int InitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_);
	+static int ReinitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_);

    +int Insert(const T& data_);
    +int Insert(const std::vector<T>& datas_);
    
    +int Update(const T& data_);
    +int Update(const std::vector<T>& datas_);
    
    +int Replace(const T& data_);
    +int Replace(const std::vector<T>& datas_);
    
    +int Select(jrsselect::SelectorParamBasePtr& selector_ptr_);
}

class DataManager
{
    +int EventHandler(const jrsdata::ViewParamBasePtr& param_);         /**< 调用接口      */
    +void SetMotionConfigCallBack(MotionConfigCallBack callback_);      /**< 电机回调  */
    +void SetSettingParamsCallBack(MotionConfigCallBack callback_);     /**< 设置回调  */
    +void SetProjectCallBack(MotionConfigCallBack callback_);           /**< 工程回调  */
    +void SetLogicCallBack(jrsdata::InvokeViewParamBaseFun callback_);  /**< logic 回调   */
}
DataManager "1" *-- "n" DBManagers
DataManager "1" *-- "n" FileHandle

' start
' :检测完成后，将检测数据填写到EntiretyBoard结构体内;
' ' while (检测FOV) is (是)
' '     :检测当前FOV所有的元件;
' '     :将元件检测结果信息存入EntiretyBoard内;
' '     :发送结果保存事件到data层;
' '     :data层进行数据新增;
' ' endwhile (否)
' :发送结果保存事件到data层;
' :data层进行board表数据更新并写入数据库;
' stop
@enduml


' fork
' :拍摄的FOV图片传入检测队列;
' fork again

@startuml Data层工作流程
title Data 层工作流程

actor "事件中心\n(EventCenter)" as EC
participant "数据模块\n(DataManager)" as DM
participant "数据库管理模块\n(DBManager)" as DBM
participant "文件管理模块\n(FileManger)" as FM

== 初始化 ==
DM -> DBM : 初始化数据库
DBM -> DBM : 建立数据库，\n 创建数据表
DBM --> DM : 数据库初始化完成
DM ->DM:初始化事件管理、回调
== 发送工程保存事件 ==
EC -> DM : 发送保存事件
DM -> DM : 事件管理解析事件
DM -> FM : 数据保存到文件
FM -> FM : 解析保存数据格式
FM --> DM :响应保存结果
DM-->EC:  响应回调
EC -> EC :将回调事件\n转发到其他模块
@enduml

@startuml Data层检测结果保存工作流程
title 检测结果保存流程

actor "事件中心\n(EventCenter)" as EC
participant "数据模块\n(DataManager)" as DM
participant "数据库管理模块\n(DBManager)" as DBM
participant "文件管理模块\n(FileManger)" as FM

EC -> DM : 发送结果保存事件
DM -> DM : 事件管理器解析事件
DM -> DBM : 查询最新的board_id
DBM -> DBM : 查询board_id
DBM --> DM :查询结果返回
DM -> DM : 修改board_id
DM -> DBM : 各表数据进行批量存储
DBM -> DBM : 存储数据
DBM-->DM :存储结果返回（AOI_OK | Error_Code）
' DM-->EC:  响应回调
' EC -> EC :将回调事件\n转发到其他模块
@enduml

@startuml data层概要图
title Data Manager 功能概要
rectangle DataManager {
    
    package 文件管理{
        rectangle csv文件输出<<未实现>>
        rectangle json格式输出
        rectangle bin格式输出
        rectangle bin格式读入
        rectangle json格式读入  
    }
    package 数据库管理{
        rectangle 数据库初始化
        package 数据表管理{
            rectangle 增加
            rectangle 删除 <<未实现>>
            rectangle 更新
            rectangle 查找
        }
    }
}


@enduml
