#include "module.h"

namespace jrscore
{
    Module::Module(const std::string& module_name_param) :module_name(module_name_param)
    {
    }
    Module::~Module()
    {
    }
    int Module::AddAdvertise(const std::string& topic_name)
    {
        std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto [iter, inserted] = topic_events_map.emplace(topic_name, std::make_shared<Topic>(topic_name));
        if (!inserted)
        {
            std::cout << topic_name << "topic has existed!" << std::endl;
            return -1;

        }

        return 0;
    }
    int Module::RemoveAdvertise(const std::string& topic_name)
    {
        std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto iter = topic_events_map.find(topic_name);

        if (iter != topic_events_map.end())
        {
            topic_events_map.erase(topic_name);
        }
        else
        {
            std::cout << "topic name  not exit" << topic_name;
            return -1;
        }
        return 0;
    }
    int Module::AddSubscriber(const std::string& topic_name_, SubscribeCallbackHelperPtr& call_)
    {
        std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto iter = topic_events_map.find(topic_name_);

        if (iter != topic_events_map.end())
        {
            if (call_)
            {
                iter->second->AddCallback(call_);

            }
        }
        else
        {
            std::cout << "topic name  not exit" << topic_name_;
            return -1;
        }
        return 0;
    }
    int Module::RemoveSubscriber(const std::string& topic_name_, SubscribeCallbackHelperPtr& call_)
    {
        std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto iter = topic_events_map.find(topic_name_);

        if (iter != topic_events_map.end())
        {
            return iter->second->RemoveCallback(call_);

        }
        else
        {
            std::cout << "topic name not exit" << topic_name_;
            return -1;
        }
    }
    int Module::NotifyOne(const std::string& topic_name_, const std::string& sub_name_, const std::vector<std::any>& args)
    {
        //std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto iter = topic_events_map.find(topic_name_);

        if (iter != topic_events_map.end())
        {
            return iter->second->NotifyOne(sub_name_, args);
        }
        else
        {
            std::cout << "topic name   not exit:" << topic_name_ << std::endl;
            return -1;
        }
    }
    int Module::NotifyAll(const std::string& topic_name_, const std::vector<std::any>& args)
    {
        //std::lock_guard<std::recursive_mutex> lock(topic_events_map_mutex);

        auto iter = topic_events_map.find(topic_name_);

        if (iter != topic_events_map.end())
        {
            return iter->second->NotifyAll(args);
        }
        else
        {
            std::cout << "topic name   not exit:" << topic_name_ << std::endl;
            return -1;
        }
    }

    using ModulePtr = std::shared_ptr<Module>;

}
