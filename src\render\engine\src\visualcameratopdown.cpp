﻿#include "visualcameratopdown.h"
#include "algodefine.hpp"
#include "log.h"

VisualCameraTopDown::VisualCameraTopDown(QVector3D default_position_)
    : VisualCameraAbstract(default_position_)
    , canvas_width(0), canvas_height(0)
    , is_limit_view_by_canvas_area(/*true*/false)
    , _is_adjust_zoom(true)
{
}

VisualCameraTopDown::~VisualCameraTopDown() {}

void VisualCameraTopDown::Reset()
{
    VisualCameraAbstract::Reset();
    SetResetMode(CameraResetMode::AlignCenter);
}

void VisualCameraTopDown::Move(const CameraDirection& d)
{
    VisualCameraAbstract::Move(d);
    // 执行限制
    LimitView();
}

void VisualCameraTopDown::SetScaleMode(const CameraScaleMode& state)
{
    // switch (static_cast<CameraScaleMode>(state))
    switch (state)
    {
    case CameraScaleMode::DEFAULT_SCALE:
    {
        Reset();
    }
    break;
    case CameraScaleMode::AUTO_SCALE:
    {
        double vw = 0, vh = 0;
        GetViewport(vw, vh);
        double scale = CalcScaleFactor(vw, vh, canvas_width, canvas_height);
        if (scale <= 0)
        {
            Reset();
        }
        else
        {
            if (_is_adjust_zoom)
            {
                SetParallelScale(scale * 0.5);
            }
            else
            {
                SetIsAdjustZoom(true);
            }
        }
    }
    break;
    case CameraScaleMode::TRUE_SCALE:
    {
        SetParallelScale(0.5);
    }
    break;
    case CameraScaleMode::MAX_SCALE:
    {
        SetParallelScale(9999);
    }
    break;
    case CameraScaleMode::MIN_SCALE:
    {
        SetParallelScale(-9999);
    }
    break;
    }
}

void VisualCameraTopDown::SetResetMode(const CameraResetMode& state)
{
    // switch (static_cast<CameraResetMode>(state))
    switch (state)
    {
    case CameraResetMode::AlignCenter:
    {
        SetCameraPosition(canvas_width * 0.5, canvas_height * -0.5);
    }
    break;
    case CameraResetMode::AlignTopLeft:
    {
        SetCameraPosition(0, 0);
    }
    break;
    case CameraResetMode::TopLeftToViewTopLeft:
    {
        double left, top, right, bottom;
        CalcViewPosition(left, top, right, bottom);
        SetCameraPosition(left, top);
    }
    break;
    case CameraResetMode::TopRightToViewTopRight:
    {
        double left, top, right, bottom;
        CalcViewPosition(left, top, right, bottom);
        SetCameraPosition(right, top);
    }
    break;
    case CameraResetMode::BottomLeftToViewBottomLeft:
    {
        double left, top, right, bottom;
        CalcViewPosition(left, top, right, bottom);
        SetCameraPosition(left, bottom);
    }
    break;
    case CameraResetMode::BottomRightToViewBottomRight:
    {
        double left, top, right, bottom;
        CalcViewPosition(left, top, right, bottom);
        SetCameraPosition(right, bottom);
    }
    break;
    }
}

void VisualCameraTopDown::SetCameraPosition(double x, double y)
{
    if (IsCanvasValid())
    {
        /*将画布限制在视野内*/
        if (is_limit_view_by_canvas_area)
        {
            LimitViewByCanvasArea(x, y);
        }
        // TODO 添加新模式
        {
            /*将视野限制在画布内*/
            // double left, top, right, bottom;
            // CalcViewPosition(left, top, right, bottom);
            // x = A_CLAMP(std::min(left, right), x, std::max(left, right));
            // y = A_CLAMP(std::min(top, bottom), y, std::max(top, bottom));
        }
        {
            /*将视野中心限制在画布内*/
            // x = A_CLAMP(0.0, x, (double)canvas_width);
            // y = A_CLAMP(-(double)canvas_height, y, 0.0);
        }
    }
    VisualCameraAbstract::SetCameraPosition(x, y);
}

void VisualCameraTopDown::SetViewport(double width, double height)
{

    SetIsAdjustZoom(false);

    VisualCameraAbstract::SetViewport(width, height);
    if (!IsCanvasValid())
        return;

    if (!LimitParallelScale(width, height))
        return;

    //PrintInfoDebug();
}

bool VisualCameraTopDown::SetCanvas(int width, int height)
{
    if (width <= 0 || height <= 0)
        return false;
    if (width == canvas_width && height == canvas_height)
        return false;

    canvas_width = width;
    canvas_height = height;
    SetResetMode(CameraResetMode::AlignCenter);
    // 限制缩放范围
    {
        double vw = 0, vh = 0;
        GetViewport(vw, vh);
        LimitParallelScale(vw, vh); // 限制失败也没有问题,因为viewport设置可能会延后,然后在viewport被设置时重新限制
    }
    return true;
}

void VisualCameraTopDown::GetCanvas(int& width, int& height) const
{
    width = canvas_width;
    height = canvas_height;
}

void VisualCameraTopDown::SetLimitViewByCanvas(bool state)
{
    is_limit_view_by_canvas_area = state;

    // 执行限制
    LimitView();
}

bool VisualCameraTopDown::GetLimitViewByCanvas()
{
    return is_limit_view_by_canvas_area;
}

bool VisualCameraTopDown::IsCanvasValid() const
{
    return canvas_width > 0 && canvas_height > 0;
}

double VisualCameraTopDown::CalcScaleFactor(const double& viewport_width_, const double& viewport_height_, const double& canvas_width_, const double& canvas_height_)
{
    if (viewport_width_ <= 0 || viewport_height_ <= 0 || canvas_width_ <= 0 || canvas_height_ <= 0)
    {
        return 0;
    }

    double w_scale = canvas_width_ / viewport_width_;
    double h_scale = canvas_height_ / viewport_height_;
    double scale = w_scale > h_scale ? w_scale : h_scale;
    return scale;
}

void VisualCameraTopDown::CalcViewTopLeftPosition(double& left, double& top, const double& viewport_width_, const double& viewport_height_)
{
    // double vw = 0, vh = 0;
    // GetViewport(vw, vh);
    double ratio = GetInnerZoom(); // 获取内部倍率
    left = viewport_width_ * ratio;
    top = -viewport_height_ * ratio;
}

void VisualCameraTopDown::CalcViewBottomRightPosition(double& right, double& bottom, const double& viewport_width_, const double& viewport_height_)
{
    // double vw = 0, vh = 0;
    // GetViewport(vw, vh);
    double ratio = GetInnerZoom(); // 获取内部倍率
    right = canvas_width - viewport_width_ * ratio;
    bottom = -(canvas_height - viewport_height_ * ratio);
}

void VisualCameraTopDown::CalcViewPosition(double& left, double& top, double& right, double& bottom, double viewport_width_, double viewport_height_)
{
    if (viewport_width_ == 0 && viewport_height_ == 0)
    {
        GetViewport(viewport_width_, viewport_height_);
    }
    CalcViewTopLeftPosition(left, top, viewport_width_, viewport_height_);
    CalcViewBottomRightPosition(right, bottom, viewport_width_, viewport_height_);
}

void VisualCameraTopDown::LimitViewByCanvasArea(double& x, double& y)
{
    /*将画布限制在视野内*/
    double vw = 0, vh = 0;
    GetViewport(vw, vh);
    double ratio = GetInnerZoom(); // 获取内部倍率
    double half_canvas_width = canvas_width * 0.5;
    double half_canvas_height = canvas_height * 0.5;
    double offset_w = std::max(0.0, half_canvas_width - vw * ratio);
    double offset_h = std::max(0.0, half_canvas_height - vh * ratio);
    x = A_CLAMP(half_canvas_width - offset_w, x, half_canvas_width + offset_w);
    y = A_CLAMP(-(half_canvas_height + offset_h), y, -(half_canvas_height - offset_h));
}

bool VisualCameraTopDown::LimitParallelScale(const double& viewport_width_, const double& viewport_height_)
{
    //PrintInfoDebug();
    const double max_ration = 1.25; // 最大视野放大倍率
    double max_scale = CalcScaleFactor(viewport_width_, viewport_height_, canvas_width * max_ration, canvas_height * max_ration);
    if (max_scale > 0)
    {
        double min_scale = CalcScaleFactor(viewport_width_, viewport_height_, 300, 300); // 最小视野应能显示300个像素
        if (min_scale > max_scale)
            min_scale = max_scale;
        SetParallelScaleMinMax(min_scale * 0.5, max_scale * 0.5); // 倍率计算时比较的是视图的一半和渲染尺寸一半的比例,所以需要乘0.5
        SetScaleMode(CameraScaleMode::AUTO_SCALE);
        return true;
    }
    //PrintInfoDebug();
    return false;
}

void VisualCameraTopDown::LimitView()
{
    if (is_limit_view_by_canvas_area)
    {
        double x, y;
        GetCameraPosition(x, y);
        SetCameraPosition(x, y);
    }
}

void VisualCameraTopDown::PrintInfoDebug() const
{
    printInfo((std::stringstream() << "canvas_width: " << canvas_width << " canvas_height:" << canvas_height
        << "is_limit_view_by_canvas_area：" << is_limit_view_by_canvas_area));
}
