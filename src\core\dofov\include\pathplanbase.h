#ifndef __PATHPLANBASE_H__
#define __PATHPLANBASE_H__
#include <iostream>
#include <vector>
#include <string>
#include <vector>

#include "pluginexport.hpp"
#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "opencv2/opencv.hpp"
#pragma warning(pop)

// 定义一个基类，用于路径规划
class JRS_AOI_PLUGIN_API PathPlanningBase
{
public:
    // 方向
    enum Direction
    {
        Left = 0,
        Right,
        Top,
        Bottom
    };

    // 拍照先走哪个轴
    enum PhotoAxisFirst
    {
        XAxis = 0, ///< 先走X轴
        YAxis,     ///< 先走Y轴
        ZAxis      ///< 先走Z轴
    };

    // 拍照起点位置
    enum PhotoStartPosition
    {
        LeftTop = 0, ///< 左上角
        RightTop,    ///< 右上角
        LeftBottom,  ///< 左下角
        RightBottom  ///< 右下角
    };

    // 位置规划模式
    enum PositionPlanMode
    {
        GRID = 0,      ///< 网格划分
        ROW_MAJOR_MBR, ///< 矩形覆盖
    };

    // 路径规划模式
    enum PathPlanMode
    {
        SNAKE_MODE = 0, ///< 蛇形
        Z_MODE,         ///< Z字形
    };

    // 轨迹模式
    enum PathPattern
    {
        XAxis_SNAKE = 0,    ///< X轴优先-S型
        YAxis_SNAKE,        ///< Y轴优先-S型
        XAxis_Z,            ///< X轴优先-Z型
        YAxis_Z             ///< Y轴优先-Z型
    };

    // 输入参数的结构体,供子类重写
    struct InputParamsBase
    {
        virtual ~InputParamsBase() {}
    };

    // 输出参数的结构体,供子类重写
    struct OutputParamsBase
    {
        virtual ~OutputParamsBase() {}
    };

    // 路径规划虚函数，供子类重写
    virtual std::shared_ptr<OutputParamsBase> PathPlan(const InputParamsBase &input) = 0;

    virtual ~PathPlanningBase() = default; // 虚析构函数
};

#endif //!__PATHPLANBASE_H__