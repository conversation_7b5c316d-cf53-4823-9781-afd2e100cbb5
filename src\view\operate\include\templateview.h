/*****************************************************************//**
 * @file   templateview.h
 * @brief 模板编辑界面
 * @details
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef TemplateView__H
#define TemplateView__H
 // prebuild
#include "pch.h"
//QT
#pragma warning(push, 3)
#include <QDialog>
#include <QWidget>
#include <QPixmap>
#include <QImage>
#include <QMutex>
#include "ui_templateview.h"
#pragma warning(pop)
//Custom
#include "caddefine.hpp"
//#include "viewparam.hpp"
#include "colorparams.h"
#include "colorwidget.h"
//#include "imagetabledelegate.h"
//#include "imagetablemodel.hpp"
#include "templatelistwidget.h"
#include "dlgcreatetemplate.h"
#include "dlgtemplatelistedit.h"
#include <opencv2/opencv.hpp>

using namespace jrsoperator;
using namespace jrsdata;

class ColorModule;

namespace jrsaoi
{
    class TemplateView :public QDialog
    {
        Q_OBJECT
    public:
        TemplateView(QWidget* parent = Q_NULLPTR);
        ~TemplateView();

        /**
         * @fun  SetIPEParamChangedCallback
         * @brief 设置图像预处理模块参数改变回调函数.
         * @param func 回调函数指针.
         * @data 2024.10.21
         * <AUTHOR>
         */
        void SetIPEParamChangedCallback(ColorParamsChangedFunc func);

        /**
         * @fun  AddTemplateModelToList
         * @brief 添加模板到列表中.
         * @param template_data 模板数据
         * @data 2024.12.04
         * <AUTHOR>
         */
        void AddTemplateModelToList(jrsdata::Template& template_data, const double component_angle);

        /**
         * @fun  Update
         * @brief 更新整个模板界面（切换检测框时会调用）.
         * @param param 算法界面参数
         * @data 2024.12.04
         * <AUTHOR>
         */
        void Update(const AlgoEventParamPtr& param);

        /**
         * @fun  ClearAllData
         * @brief 清空模板界面数据.
         * @data 2024.12.04
         * <AUTHOR>
         */
        void ClearAllData();

        /**
         * @fun  GetInputImage
         * @brief 获取模板界面的当前图像.
         * @param input_image
         * @data 2024.12.04
         * <AUTHOR>
         */
        void GetInputImage(cv::Mat& input_image);

        /**
         * @fun  GetCurIPEParam
         * @brief 获取当前图像预处理参数.
         * @param param 图像预处理参数
         * @data 2024.12.04
         * @return 错误码
         * <AUTHOR>
         */
        int GetCurIPEParam(std::string& param);

        /**
         * @fun  UpdateInputImageInfo
         * @brief 更新模板编辑信息，图像预处理图片.
         * @param param 算法界面参数
         * @data 2024.12.26
         * @return
         * <AUTHOR>
         */
        int UpdateInputImageInfo(const AlgoEventParamPtr& param);



        int UpdataInputImage(const cv::Mat& input_image, const LightImageType light_type);

    private:
        /**
         * @fun InitView
         * @brief 初始化界面
         * @date 2024.9.24
         * <AUTHOR>
         */
        void InitView();
        /**
         * @fun ConnectSlots
         * @brief 链接信号槽
         * @date 2024.9.24
         * <AUTHOR>
         */
        void ConnectSlots();
        /**
         * @fun DisconnectSlots
         * @brief 断开信号槽
         * @date 2024.9.24
         * <AUTHOR>
         */
        void DisconnectSlots();
        /**
         * @fun RotatedPixmap
         * @brief 旋转图片角度
         * @param pixmap
         * @param rotationAngle
         * @return
         * @date 2024.9.24
         * <AUTHOR>
         */
        QPixmap RotatedPixmap(const QPixmap& pixmap, qreal rotationAngle);

        /**
         * @fun AddTestImageToIPE
         * @brief 添加cv::Mat图片和参数到图片处理接口
         * @param color_data
         * @param params
         * @date 2024.9.23
         * <AUTHOR>
         */
        void AddTestImageToIPE(cv::Mat color_data, std::string params);

        /**
         * @fun  UpdateTempalteModelList
         * @brief 更新模版列表.
         * @param param
         * @data 2024.10.30
         * <AUTHOR>
         */
        void UpdateTempalteModelList(std::vector<jrsdata::Template>& templates, const double component_angle);

    protected:
        /**
         * @fun showEvent
         * @brief 显示界面showEvent函数
         * @param
         * @date 2024.9.24
         * <AUTHOR>
         */
        void showEvent(QShowEvent*) override;

    protected slots:
        /**
         * @fun AddModelSlot
         * @brief 添加模板按钮点击
         * @date 2024.9.24
         * <AUTHOR>
         */
        void CropTemplateSlot();
        /**
         * @fun DeleteModelSlot
         * @brief 删除模板按钮点击
         * @date 2024.9.24
         * <AUTHOR>
         */
        void DeleteModelSlot();

        void SoltTemplateSelectChanged(const TemplateItemValue& item_val);

        void SoltTemplateItemDoubleClicked(const TemplateItemValue& item_val);

        void SlotCreateTemplate(const cv::RotatedRect& rect, const int& direction, const std::string& ipe_params);

        void SlotUpdateSelectedTemplate(const std::string& ipe_params);

        QPixmap MatToQPixmap(const cv::Mat& mat);

    signals:
        void SignalUpdateTemplate(jrsdata::ViewParamBasePtr template_param);
        void SignalIPEParamChanged(const QPixmap& image, const std::string& params);

    private:
        Ui::TemplateView* ui = nullptr;
        ColorModule* ipe_module = nullptr;                      //! 图像预处理模块
        QTabWidget* m_template_tab = nullptr;                   //! 模板显示Tab
        TemplateListWidget* m_template_list_table = nullptr;    //! 模板列表
        DlgCreateTemplate* m_create_template_dlg = nullptr;     //! 编辑模板显示界面
        DlgTemplateListEdit* m_edit_template_dlg = nullptr;     //! 编辑模板列表界面
        QMutex              m_mutex;                            //! 更新图片的锁
        cv::Mat             m_current_src_image;                //! 当前处理图片
        cv::Mat             m_current_show_image;               //! 当前显示图片
        //cv::Rect            m_current_rect;                     //! 当前检测框位置
        LightImageType      m_currect_light_type;               //! 当前光照类型
        int                 m_detect_win_direction = 0;              //! 当前检测方向

        int IpeMoudleImage2QPixmap(const cv::Mat& enhance_image, const cv::Mat& mask_image, QPixmap& show_image);

        int Template2TemplateItemValue(jrsdata::Template& template_data, TemplateItemValue& item_val, const double component_angle);

        int TemplateItemValue2Template(const TemplateItemValue& item_val, jrsdata::Template& template_data);
    };
}
#endif // !TemplateView__H
