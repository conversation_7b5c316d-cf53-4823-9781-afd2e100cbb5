/*********************************************************************
 * @brief  缩略图导航UI.
 *
 * @file   thumbnailnavigationwidget.h
 *
 * @date   2024.05.05
 * <AUTHOR>
 *********************************************************************/
#pragma once

#include "thumbnailnavigation.h"
#include <QWidget>

namespace cv { class Mat; }

class QMouseEvent;
// class QKeyEvent;
class QPaintEvent;
class QResizeEvent;

class ThumbnailNavigationWidget
    : public QWidget
    , public ThumbnailNavigation
{
    Q_OBJECT

public:
    ThumbnailNavigationWidget(int scene_w = 200, int scene_h = 200, QWidget* parent = nullptr);
    ~ThumbnailNavigationWidget() {};

    void Update() override { update(); }

signals:
    void SignalViewportCenterChange(float x, float y);
    void SignalMouseEnter();
    void SignalMouseLeave();

protected:
    void resizeEvent(QResizeEvent* event) override;
    void paintEvent(QPaintEvent*) override;

    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

    void enterEvent(QEvent*) override;
    void leaveEvent(QEvent*) override;

private:
    /**
     * @brief  将选中框移动到鼠标中心位置
     */
    void MoveViewportToMouseCenter(const QPoint& mousepos);

private:
    QColor color_background;      ///< 背景颜色
    QColor color_valid_region;    ///< 有效区域颜色
    QColor color_select_rect;     ///< 视野框颜色
};
