﻿#include "renderbackground.h"
#include "painter.h"
#include "renderer.h"
#include "log.h"

RenderBackground::RenderBackground()
    : RenderAbstract()
{
}

RenderBackground::~RenderBackground()
{
}

void RenderBackground::Render()
{
    if (!IsRendered())
        return;
    if (!IsHaveRenerer())
        return;

    Painter p(renderer);
    /*绘制画布区域棋盘格*/
    if (false)
    {
        int regionWidth, regionHeight;
        auto state = renderer->GetCanvasSize(regionWidth, regionHeight);
        if (state)
        {
            const int blockSize = 40;

            for (int y = 0; y < regionHeight; y += blockSize)
            {
                for (int x = 0; x < regionWidth; x += blockSize)
                {
                    // 计算方块的宽度和高度，确保不超出区域边界
                    int currentBlockWidth = (x + blockSize <= regionWidth) ? blockSize : regionWidth - x;
                    int currentBlockHeight = (y + blockSize <= regionHeight) ? blockSize : regionHeight - y;

                    // 确定方块颜色
                    if ((x / blockSize + y / blockSize) % 2 == 0)
                    {
                        p.DrawRect(Vec2(x, y), Vec2(x + currentBlockWidth, y + currentBlockHeight), color_reseda_green, 0, -1);
                    }
                    // else
                    // {
                    //     p.DrawRect(Vec2(x, y), Vec2(x + currentBlockWidth, y + currentBlockHeight), Color(0.0f, 0.0f, 0.0f, 1.0f), 0, -1);
                    // }
                }
            }
            // p.DrawRect(Vec2(0, 0), Vec2(width, height), Color(1.0f, 1.0f, 1.0f, 1.0f), true, -1);
        }
    }
    /*绘制画布区域网格*/
    if (false)
    {
        std::vector<Vec2> paths;
        int regionWidth, regionHeight;
        auto state = renderer->GetCanvasSize(regionWidth, regionHeight);
        auto zoom = 1.5 / renderer->GetZoom() * 3;
        int ratio = static_cast<int>(std::pow(10, static_cast<int>(std::log10(zoom))));
        int ratio_limit = std::max(1, std::min(100, ratio));
        if (state)
        {
            const int minor_grid_block_size = 10 * ratio_limit;
            const int grid_block_size = 100 * ratio_limit;

            p.DrawGrid(minor_grid_block_size, regionWidth, regionHeight, Color(205, 205, 205, 125), 1); // 次网格
            p.DrawGrid(grid_block_size, regionWidth, regionHeight, Color(110, 123, 108), 1); // 主网格
        }
        // p.DrawPolyline(paths, color_reseda_green, true);
    }
}

void RenderBackground::Destroy()
{
}
