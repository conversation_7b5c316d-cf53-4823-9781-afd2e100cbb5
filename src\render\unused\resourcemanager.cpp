#include "resourcemanager.h"

// #include "openglrenderer2d.h"

#pragma warning(push, 1)
#pragma warning(disable : 4127 6201 6294 26451 26495 26812 26819)
#include "opencv2/core/types.hpp"
#pragma warning(pop)

void ResourceManager::SetPel(Pel &&pel)
{
    m_pel.SetResource(std::forward<Pel>(pel));
    // SetLines(pel.lines);
    // SetRects(pel.rectangles);
    // SetMessages(pel.messages);
    // SetPolygons(pel.polygons);
}

void ResourceManager::ClearPel()
{
    // ClearLine();
    ClearMessage();
    ClearRect();
    ClearPolygon();
}

ResourceOut<ResourceManager::Pel, std::shared_mutex> ResourceManager::GetPel()
{
    return m_pel.GetReource();
    // return ResourceOut<Pel, std::recursive_mutex>();
}

void ResourceManager::GetPolygonsAndDoSomething(std::function<void(const std::vector<Polygoncvp2f> &)> function) const
{
    m_polygons.UseResourcesDoSomething(function);
}

void ResourceManager::SetPolygons(std::vector<Polygoncvp2f> &values)
{
    m_polygons.SetResource(values);
}

void ResourceManager::AddPolygons(const std::vector<Polygoncvp2f> &values)
{
    m_polygons.AddResource(values);
}

void ResourceManager::ClearPolygon()
{
    m_polygons.ClearResource();
}

// void ResourceManager::GetLinesAndDoSomething(std::function<void(const std::vector<Linecvp2f>&)> function) const
//{
//     m_lines.UseResourcesDoSomething(function);
// }
//
// void ResourceManager::SetLines(std::vector<Linecvp2f>& lines)
//{
//     m_lines.SetResource(lines);
// }
//
// void ResourceManager::AddLines(const std::vector<Linecvp2f>& lines)
//{
//     m_lines.AddResource(lines);
// }
//
// void ResourceManager::ClearLine()
//{
//     m_lines.ClearResource();
// }

void ResourceManager::GetRectsAndDoSomething(std::function<void(const std::vector<Rect> &)> function) const
{
    m_rectangles.UseResourcesDoSomething(function);
}

void ResourceManager::SetRects(std::vector<Rect> &rects)
{
    m_rectangles.SetResource(rects);
}

void ResourceManager::AddRect(const Rect &rect)
{
    m_rectangles.AddResource(rect);
}

void ResourceManager::ClearRect()
{
    m_rectangles.ClearResource();
}

void ResourceManager::GetMessagesAndDoSomething(std::function<void(const std::vector<Message> &)> function) const
{
    m_messages.UseResourcesDoSomething(function);
}

void ResourceManager::SetMessages(std::vector<Message> &messages)
{
    m_messages.SetResource(messages);
}

void ResourceManager::AddMessage(const Message &message)
{
    m_messages.AddResource(message);
}

void ResourceManager::ClearMessage()
{
    m_messages.ClearResource();
}