#ifndef D15D17D4_A710_4561_B0EA_DF11876E5B73
#define D15D17D4_A710_4561_B0EA_DF11876E5B73

/*****************************************************************
 * @file   jrsthreadpool.hpp
 * @brief 线程池类，用于管理线程
 * @details 目前线程池主要功能有：添加任务，等待所有任务完成，停止所有任务
 * <AUTHOR>
 * @date 2024.9.9
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.9.9          <td>V2.0              <td>YYZhang      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/




#include <iostream>
#include <vector>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <functional>
#include <queue>
#include <future>
#include <atomic>

 //跨平台支持
#ifdef _WIN32
#include <windows.h>
#elif defined(__linux__)||defined(__APPLE__)
#include <unistd.h>
#include <pthread.h>
#include <sched.h>
#endif


static void SetAffinity(int cpu_index)
{

    #ifdef _WIN32

        DWORD_PTR mask = 1ULL << cpu_index;
        HANDLE thread = GetCurrentThread();
        if (SetThreadAffinityMask(thread, mask) == 0)
        {
            std::cerr << "Failed to set affinity\n";
        }
        else 
        {
            std::cout << "Thread bound to CPU " << cpu_index << "\n";
        }

    #elif defined(__linux__) || defined(__APPLE__)

        //!linux暂未实现
        
    #endif
}


class JrsThreadPool
{
    public:
    explicit JrsThreadPool(size_t thread_count)
        : stop_all(false), active_tasks(0)
    {
        // 自动选择可用核心（排除指定核心）
        std::vector<unsigned int> excluded_cores{0,1,2,3}; //! 排除的核心
        std::vector<unsigned int> available_cores; //! 可用的核心
        for (unsigned int i = 0; i < std::thread::hardware_concurrency(); ++i) {
            if (std::find(excluded_cores.begin(),
                excluded_cores.end(), i) == excluded_cores.end()) {
                available_cores.push_back(i);
            }
        }
        for (size_t i = 0; i < thread_count; ++i)
        {
            int core = available_cores[i % available_cores.size()];

            workers.emplace_back([this,core]()
                {
                    SetAffinity(core);

                    //SetThreadLowPriority();
                    while (true)
                    {
                        std::function<void()> task;
                        {
                            std::unique_lock<std::mutex> lock(task_mutex);
                            condition_task.wait(lock, [this]()
                                {
                                    return stop_all || !tasks.empty();
                                });

                            if (stop_all) {
                                return;
                            }

                            if (!tasks.empty())
                            {
                                task = std::move(tasks.front());
                                tasks.pop();
                            }
                            else
                            {
                                continue;
                            }

                            {
                                std::lock_guard<std::mutex> state_lock(status_mutex);
                                ++active_tasks;
                            }
                        }

                        try
                        {
                            task();

                        }
                        catch (...)
                        {
                            std::cerr << "[Error] Task threw an exception.\n";
                        }

                        {
                            std::lock_guard<std::mutex> state_lock(status_mutex);
                            --active_tasks;
                            if (active_tasks == 0 && tasks.empty())
                            {
                                finished_condition.notify_all();
                            }
                        }
                    }
                });
        }
    }

    ~JrsThreadPool()
    {
        {
            std::unique_lock<std::mutex> lock(task_mutex);
            stop_all = true;

        }
        condition_task.notify_all();
        for (std::thread& worker : workers)
        {
            if (worker.joinable())
            {
                worker.join();
            }
        }
    }
    template <typename F, typename... Args>
    auto Enqueue(F&& f, Args&&... args)
        -> std::future<typename std::invoke_result<F, Args...>::type>
    {
        using return_type = typename std::invoke_result<F, Args...>::type;

        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );

        std::future<return_type> result = task->get_future();
        {
            std::unique_lock<std::mutex> lock(task_mutex);
            if (stop_all)
            {
                return result;
            }

            tasks.emplace([task]()
                {
                    (*task)();
                });
        }
        condition_task.notify_one();
        return result;
    }
    inline void SetThreadLowPriority()
    {
        #ifdef _WIN32
        HANDLE thread = GetCurrentThread();
        SetThreadPriority(thread, THREAD_PRIORITY_BELOW_NORMAL);
        #elif defined(__linux__) || defined(__APPLE__)
        pthread_t thread = pthread_self();
        sched_param sch_params{};
        sch_params.sched_priority = 0;
        pthread_setschedparam(thread, SCHED_OTHER, &sch_params);
        #endif
    }
    void WaitForAllTasks()
    {
        std::unique_lock<std::mutex> lock(status_mutex);
        finished_condition.wait(lock, [this]()
            {
                return tasks.empty() && active_tasks == 0;
            });
    }

    void StopAllTasks()
    {
        {
            std::unique_lock<std::mutex> lock(task_mutex);
            std::queue<std::function<void()>> empty;
            std::swap(tasks, empty); // 清空任务
        }
        condition_task.notify_all();
    }
    void StartThreadPool()
    {
        {
            std::unique_lock<std::mutex> lock(task_mutex);
            stop_all = false;
            std::queue<std::function<void()>> empty;
            std::swap(tasks, empty); // 清空任务
        }
        //condition_task.notify_all();
    }

    private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    std::mutex task_mutex;                    // 控制 tasks 访问
    std::mutex status_mutex;                  // 控制 active_tasks 和 finished_condition
    std::condition_variable condition_task;
    std::condition_variable finished_condition;
    bool stop_all;                            // 析构时退出
    std::atomic<size_t> active_tasks;         // 当前正在运行的任务数
};

using JrsThreadPoolPtr = std::shared_ptr<JrsThreadPool>;

#endif /* D15D17D4_A710_4561_B0EA_DF11876E5B73 */
