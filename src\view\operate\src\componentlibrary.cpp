#include <componentlibrary.h>

namespace jrsaoi
{
    ComponentLibrary::ComponentLibrary(const std::string path)
    {
        component_file_path = path;
        cur_type = jrsaoi::AddType::Default;
    }

    ComponentLibrary::~ComponentLibrary()
    {
    }

    void ComponentLibrary::SetComponentName(const std::string name)
    {
        component_name = name;
        if (component_file_path.back() != '/')
        {
            component_file_path = component_file_path + "/";
        }
        LoadComponents(component_file_path + component_name);
    }

    void ComponentLibrary::AddComponent(std::vector<jrsdata::ComponentEntity> components, AddType type, bool savefile)
    {
        cur_type = type;
        for (const auto& component : components)
        {
            if (component.part_name != "")
            {
                AddComponent(component, cur_type, savefile);
            }
            else
            {
                // 弹框提示
                JRSMessageBox_INFO("提示", "元件库元件名称为空", jrscore::MessageButton::Ok);
            }
        }
    }

    void ComponentLibrary::RemoveComponent(const std::string partnumber)
    {
        auto param = std::make_shared<jrsdata::OperateViewParam>();
        param->event_name = OPERATE_COMPONENT_REMOVE_COMPONENT_EVENT_NAME;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_LOGIC_SUB_NAME;
        param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
        jrsdata::ComponentEntitys entitys;
        jrsdata::ComponentEntity entity;
        entity.part_name = partnumber;
        entitys.components_param.push_back(entity);
        entitys.component_lib_path = component_file_path + component_name;
        param->entitys = entitys;

        emit SigComponent(param);

        component_entitys.erase(
            std::remove_if(component_entitys.begin(), component_entitys.end(),
                [partnumber](const jrsdata::ComponentEntity& entity)
                {
                    return entity.part_name == partnumber;
                }),
            component_entitys.end());
    }

    void ComponentLibrary::ClearComponents(bool deletefiles)
    {
        if (deletefiles)
        {
            auto param = std::make_shared<jrsdata::OperateViewParam>();
            param->event_name = OPERATE_COMPONENT_REMOVE_COMPONENT_EVENT_NAME;
            param->module_name = OPERATE_MODULE_NAME;
            param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
            param->sub_name = OPERATE_LOGIC_SUB_NAME;
            param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
            jrsdata::ComponentEntitys entitys;
            for (size_t i = 0; i < component_entitys.size(); i++)
            {
                // 只拷贝料号
                jrsdata::ComponentEntity entity;
                entity.part_name = component_entitys[i].part_name;
                entitys.components_param.push_back(entity);
            }
            entitys.component_lib_path = component_file_path + component_name;
            param->entitys = entitys;

            emit SigComponent(param);
        }
        component_entitys.clear();
    }

    void ComponentLibrary::QueryComponent(jrsdata::ComponentEntity& component, const std::string partnumber)
    {
        HasComponentWithBindPartName(partnumber, component);
    }

    void ComponentLibrary::QueryComponents(std::vector<jrsdata::ComponentEntity>& components)
    {
        components = component_entitys;
    }

    void ComponentLibrary::QueryComponents(std::vector<jrsdata::ComponentEntity>& components, MultiConditions conditions)
    {
        components.clear();

        // 计算宽高范围（如果条件不为0）
        const int width_min = conditions.width > 0 ? conditions.width * 0.9 : 0;
        const int width_max = conditions.width > 0 ? conditions.width * 1.1 : INT_MAX;
        const int height_min = conditions.height > 0 ? conditions.height * 0.9 : 0;
        const int height_max = conditions.height > 0 ? conditions.height * 1.1 : INT_MAX;

        for (const auto& item : component_entitys)
        {
            // 检查PAD数量条件
            if (conditions.pad_count != 0 && GetPADCount(item) != conditions.pad_count) {
                continue;
            }

            // 检查料号名称条件
            if (!conditions.part_name.empty() && item.part_name.find(conditions.part_name) == std::string::npos)
            {
                continue;
            }

            // 检查宽高条件
            if (conditions.width != 0 && (item.width <= width_min || item.width >= width_max))
            {
                continue;
            }
            if (conditions.height != 0 && (item.height <= height_min || item.height >= height_max))
            {
                continue;
            }

            // 所有条件都满足
            components.push_back(item);
        }
    }

    void ComponentLibrary::MergeComponentLibrary(const std::string name)
    {
        if (component_file_path.back() != '/')
        {
            component_file_path = component_file_path + "/";
        }

        auto param = std::make_shared<jrsdata::OperateViewParam>();
        param->event_name = OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_LOGIC_SUB_NAME;
        param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager
        emit SigComponent(param);
    }

    std::vector<std::string> ComponentLibrary::GetComponentFolders()
    {
        std::vector<std::string> folders;

        try
        {
            // 检查路径是否存在以及是否为目录
            if (std::filesystem::exists(component_file_path) && std::filesystem::is_directory(component_file_path))
            {
                for (const auto& entry : std::filesystem::directory_iterator(component_file_path))
                {
                    // 检查是否为目录
                    if (entry.is_directory())
                    {
                        folders.push_back(entry.path().filename().string());
                    }
                }
            }
            else
            {
                std::cerr << "Provided path is not a directory or does not exist." << std::endl;
            }
        }
        catch (const std::filesystem::filesystem_error& e)
        {
            std::cerr << "Filesystem error: " << e.what() << std::endl;
        }

        return folders;
    }

    std::string ComponentLibrary::GetComponentLibPath()
    {
        if (component_file_path.back() != '/')
        {
            component_file_path = component_file_path + "/";
        }
        return component_file_path + component_name + "/";
    }

    std::string ComponentLibrary::GetComponentLibFolderPath()
    {
        return component_file_path;
    }

    void ComponentLibrary::SetComponentLibFolderPath(const std::string path)
    {
        component_file_path = path;
    }

    std::string ComponentLibrary::GetComponentName()
    {
        return component_name;
    }

    int ComponentLibrary::GetPADCount(const jrsdata::ComponentEntity& component)
    {
        std::vector<jrsdata::ComponentUnit> units = component.detect_info.units;
        int pad_count = 0;
        for (int i = 0; i < units.size(); i++)
        {
            if (units[i].unit_type == jrsdata::ComponentUnit::Type::PAD) {
                pad_count++;
            }
        }
        return pad_count;
    }

    void ComponentLibrary::AddComponent(jrsdata::ComponentEntity component, AddType type, bool savefile)
    {
        cur_type = type;
        std::string bind_part_name = GetBindPartName(component);

        jrsdata::ComponentEntity existing_component;
        if (HasComponentWithBindPartName(bind_part_name, existing_component))
        {
            HandleExistingComponent(component, existing_component, savefile);
        }
        else
        {
            // 直接添加
            component_entitys.push_back(component);
            SaveComponent(component, savefile);
        }
    }

    void ComponentLibrary::RemoveComponent(const std::vector<std::string> partnumbers)
    {
        for (int i = 0; i < partnumbers.size(); i++)
        {
            RemoveComponent(partnumbers[i]);
        }
    }

    bool ComponentLibrary::HasComponentWithBindPartName(const std::string partname, jrsdata::ComponentEntity& component)
    {
        if (partname == "")
        {
            return false;
        }
        for (const auto& item : component_entitys)
        {
            std::string str = item.bind_part_name;
            if (str == "")
            {
                str = item.part_name;
            }
            if (str == partname)
            {
                component = item;
                return true;
            }
        }
        return false;
    }

    void ComponentLibrary::SaveComponent(jrsdata::ComponentEntity& component, bool savefile)
    {
        auto param = std::make_shared<jrsdata::OperateViewParam>();
        param->event_name = OPERATE_COMPONENT_SAVE_COMPONENT_EVENT_NAME;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_LOGIC_SUB_NAME;
        param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager

        //component.file_param.file_type = jrsdata::FileType::JSON;
        component.file_param.file_path = GetComponentLibPath();
        component.file_param.file_name = component.part_name + ".component";
        //component.data_save_mode = jrsdata::DataSaveMode::SAVE_FILE;
        param->entitys.components_param.push_back(component);

        if (savefile)
        {
            emit SigComponent(param);
        }
    }

    void ComponentLibrary::LoadComponents(const std::string path)
    {
        auto param = std::make_shared<jrsdata::OperateViewParam>();
        param->event_name = OPERATE_COMPONENT_READ_COMPONENT_EVENT_NAME;
        param->module_name = OPERATE_MODULE_NAME;
        param->topic_name = OPERATE_TRIGGER_TOPIC_NAME;
        param->sub_name = OPERATE_LOGIC_SUB_NAME;
        param->invoke_module_name = jrsaoi::DATA_MODULE_NAME; // 传到datamanager

        param->entitys.component_lib_path = path;
        emit SigComponent(param);
    }

    void ComponentLibrary::ShowSaveDialog(const jrsdata::ComponentEntity& new_component, jrsdata::ComponentEntity& existing_component, bool savefile)
    {
        SaveComponentDialog save_component_dialog;
        auto cover_save_lambda = [this, savefile, new_component, &existing_component, &save_component_dialog]()
            {
                AddComponent(new_component, AddType::Overwrite, savefile);
                save_component_dialog.close();
            };

        auto part_num_first_save_lambda = [this, savefile, new_component, &existing_component, &save_component_dialog]()
            {
                AddComponent(new_component, AddType::PartNumFirst, savefile);
                save_component_dialog.close();
            };

        connect(&save_component_dialog, &SaveComponentDialog::SigCoverSave, this, cover_save_lambda);
        connect(&save_component_dialog, &SaveComponentDialog::SigPartNumFirstSave, this, part_num_first_save_lambda);
        save_component_dialog.exec();
    }

    void ComponentLibrary::OverwriteComponent(const jrsdata::ComponentEntity& newComponent, jrsdata::ComponentEntity& existingComponent, bool savefile)
    {
        // 保留原始的文件路径信息
        std::string originalPath = existingComponent.file_param.file_path;
        std::string originalName = existingComponent.file_param.file_name;

        // 完全替换元件数据
        existingComponent = newComponent;

        // 恢复文件路径信息
        existingComponent.file_param.file_path = originalPath;
        existingComponent.file_param.file_name = originalName;

        // 更新内存列表中的对应元件
        for (auto& item : component_entitys)
        {
            std::string currentPartName = item.bind_part_name.empty() ?
                item.part_name : item.bind_part_name;

            if (currentPartName == newComponent.detect_info.part_name) {
                item = existingComponent;  // 同步更新列表中的元件
                break;
            }
        }

        // 如果需要保存到文件
        if (savefile)
        {
            SaveComponent(existingComponent, true);
        }
    }

    void ComponentLibrary::AddAsNewComponent(const jrsdata::ComponentEntity& component, bool savefile)
    {
        jrsdata::ComponentEntity newEntity = component;

        // 确保使用料号作为元件名称
        newEntity.part_name = component.detect_info.part_name;

        // 添加到内存列表
        component_entitys.push_back(newEntity);

        // 如果需要保存到文件
        if (savefile)
        {
            SaveComponent(newEntity, true);
        }
    }

    void ComponentLibrary::HandleExistingComponent(const jrsdata::ComponentEntity& component, jrsdata::ComponentEntity& existing_component, bool savefile)
    {
        switch (cur_type)
        {
        case AddType::Default:
            ShowSaveDialog(component, existing_component, savefile);
            break;
        case AddType::Overwrite:
            OverwriteComponent(component, existing_component, savefile);
            break;
        case AddType::PartNumFirst:
            AddAsNewComponent(component, savefile);
            break;
        }
    }

    std::string ComponentLibrary::GetBindPartName(const jrsdata::ComponentEntity& component)
    {
        return component.detect_info.bind_part_name.empty() ?
            component.detect_info.part_name :
            component.detect_info.bind_part_name;
    }

}