/*********************************************************************
 * @brief  背景渲染.
 *
 * @file   renderbackground.h
 *
 * @date   2024.07.08
 * <AUTHOR>
 *********************************************************************/
#pragma once
#include "renderabstract.hpp" // RenderAbstractPtr

class RenderBackground : public RenderAbstract
{
public:
    RenderBackground();
    ~RenderBackground();

    void Render() override;
    void Destroy() override;

private:
};
