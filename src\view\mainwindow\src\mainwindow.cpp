//STD
#include <iostream>
#include <filesystem>
//QT
#pragma warning(push, 3)

#include <QApplication>
#include <QDockWidget>
#include <QGridLayout>
#include <QLabel>
#include <QSplitter>
#include <QStatusBar>
#include <QScreen>
#include <QTimer>
#pragma warning(pop)

//Custom
#include "viewbase.h"
#include "mainwindow.h" 
#include "customtitleview.h"
#include "statusbarview.h"
#include "viewdefine.h"
#include "coreapplication.h"
#include "tools.h"
#include "custommessagebox.h"

//Thirdy
#pragma warning(push, 3)
//TODO
#include "SARibbonBar.h"

#include "opencv2/opencv.hpp"
#pragma warning(pop)

using namespace jrsaoi;

MainWindow::MainWindow(QWidget* parent /*= nullptr*/)
    :SARibbonMainWindow(parent)
{
    Init();
}

MainWindow::~MainWindow()
{
}

jrsaoi::ViewBase* MainWindow::GetInitView()
{
    return view_main_window.view_manager_ptr->GetView(jrsaoi::SYSTEM_STATE_MODULE_NAME);
}

void MainWindow::HideLeftDockWidget()
{

    if (is_left_hide)
    {
        is_left_hide = false;
        //view_main_window.left_hide_btn->setIcon(QIcon(":/image/fold.png"));

    }
    else
    {
        is_left_hide = true;
        //view_main_window.left_hide_btn->setIcon(QIcon(":/image/unfold.png"));

    }
    view_main_window.dock_left->setVisible(!is_left_hide);
}

void MainWindow::HideRightDockWidget()
{

    if (is_right_hide)
    {
        is_right_hide = false;
        view_main_window.right_hide_btn->setIcon(QIcon(":/image/unfold.png"));

    }
    else
    {
        is_right_hide = true;
        view_main_window.right_hide_btn->setIcon(QIcon(":/image/fold.png"));

    }
    view_main_window.dock_right->setVisible(!is_right_hide);
}

void MainWindow::Init()
{
    try
    {
        //标题栏区域
        QMainWindow::setProperty("MainWindow", 1);
        app_name = "JRS AOI";
        setWindowTitle(app_name);
        setWindowIcon(QIcon(":/image/JRS.ico"));
        setWindowFlags(Qt::Window | Qt::FramelessWindowHint | Qt::WindowSystemMenuHint); // 属性设置自上而下,子控件越多越慢,放到前面 jerx
        setWindowState(Qt::WindowMaximized); // 设置窗口最大化
        setAttribute(Qt::WA_DeleteOnClose);  // 窗口关闭后居然不会进入析构函数,手动设置一下属性 jerx

        InitLog();
        Log_INFO("JrsAOI init ....");
        InitError();
        Log_INFO("JrsAOI init member ....");
        InitMember();
        Log_INFO("JrsAOI init title ....");
        InitTitleView();
        Log_INFO("JrsAOI init view ....");
        InitView();
        Log_INFO("JrsAOI init layout ....");
        InitLayout();
        Log_INFO("JrsAOI init connect ....");
        InitConnect();
        Log_INFO("JrsAOI init message....");
        InitMessage();
        // ShowInitSystemView();
        Log_INFO("JrsAOI init finished!");
    }
    catch (std::exception& e)
    {
        Log_INFO("JrsAOI init catch!  ", e.what());
    }
}

void MainWindow::InitView()
{
    view_main_window.main_show_widget = new QWidget(this);
    //整体布局
    view_main_window.dock_splitter = new QSplitter(Qt::Horizontal, view_main_window.main_show_widget);
    //view_main_window.dock_splitter->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_splitter->setHandleWidth(0);
    view_main_window.dock_splitter->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    //左侧区域
    view_main_window.dock_left = new QDockWidget("左侧区域", view_main_window.dock_splitter);
    view_main_window.dock_left->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_left->setFeatures(QDockWidget::NoDockWidgetFeatures);
    view_main_window.dock_left->setTitleBarWidget(new QWidget());
    //view_main_window.dock_left->setStyleSheet("background:yellow;");
    //左隐藏按钮
    //QWidget* left_hide_widget = new QWidget(view_main_window.dock_splitter);
    ////left_hide_widget->setStyleSheet("background:green;");
    //QVBoxLayout* left_hide_layout = new QVBoxLayout(left_hide_widget);
    //view_main_window.left_hide_btn = new QPushButton(left_hide_widget);
    //left_hide_widget->setContentsMargins(0, 0, 0, 0);
    //left_hide_layout->setContentsMargins(0, 0, 0, 0);
    //left_hide_widget->setMaximumWidth(20);

    //view_main_window.left_hide_btn->setContentsMargins(0, 0, 0, 0);
    //view_main_window.left_hide_btn->setIcon(QIcon(":/image/fold.png"));
    //view_main_window.left_hide_btn->setFixedSize(20, 30);
    //view_main_window.left_hide_btn->setMinimumSize(20, 30);
    //view_main_window.left_hide_btn->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    //left_hide_layout->addStretch();
    //left_hide_layout->addWidget(view_main_window.left_hide_btn);
    //left_hide_layout->addStretch();

    //中间区域
    view_main_window.dock_center = new QDockWidget("中间区域", view_main_window.dock_splitter);
    //view_main_window.dock_center->setStyleSheet("background:blue;");
    view_main_window.dock_center->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_center->setFeatures(QDockWidget::NoDockWidgetFeatures);
    view_main_window.dock_center->setTitleBarWidget(new QWidget());

    //右隐藏按钮
    QWidget* right_hide_widget = new QWidget(view_main_window.dock_splitter);
    //right_hide_widget->setStyleSheet("background:lightgray;");
    QVBoxLayout* right_hide_layout = new QVBoxLayout(right_hide_widget);
    view_main_window.right_hide_btn = new QPushButton(right_hide_widget);
    right_hide_widget->setContentsMargins(0, 0, 0, 0);
    right_hide_layout->setContentsMargins(0, 0, 0, 0);
    right_hide_widget->setMaximumWidth(10);
    view_main_window.right_hide_btn->setFlat(true);
    view_main_window.right_hide_btn->setContentsMargins(0, 0, 0, 0);
    view_main_window.right_hide_btn->setIcon(QIcon(":/image/unfold.png"));
    view_main_window.right_hide_btn->setFixedSize(10, 30);
    view_main_window.right_hide_btn->setMinimumSize(10, 30);
    view_main_window.right_hide_btn->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

    right_hide_layout->addStretch();
    right_hide_layout->addWidget(view_main_window.right_hide_btn);
    right_hide_layout->addStretch();
    //右侧区域
    view_main_window.dock_right = new QDockWidget("右侧区域", view_main_window.dock_splitter);
    //view_main_window.dock_right->setStyleSheet("background:lightblue;");
    view_main_window.dock_right->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_right->setFeatures(QDockWidget::NoDockWidgetFeatures);
    view_main_window.dock_right->setTitleBarWidget(new QWidget());
    view_main_window.dock_left->setFixedWidth(400);
    view_main_window.dock_right->setFixedWidth(350);
    for (int i = 0; i < view_main_window.dock_splitter->count(); i++)//禁用QSplitter的拖动功能
    {
        QSplitterHandle* handle = view_main_window.dock_splitter->handle(i);
        if (handle != nullptr)
        {
            handle->setEnabled(false);
            handle->setCursor(Qt::ArrowCursor);
        }
    }

}

void MainWindow::InitTitleView()
{
    setStatusBar(new QStatusBar());
    SARibbonBar* ribbon = ribbonBar();
    ribbon->setContentsMargins(0, 0, 0, 0);
    //标题栏区域
    view_main_window.title_widget = new jrsaoi::CustomTitleView(this);

    /*占用初始化47.6%耗时 注释方便离线调试 jerx*/
    QTimer::singleShot(0, this, [this]() { this->setRibbonTheme(SARibbonMainWindow::RibbonThemeWindows7); });
}

void MainWindow::InitMember()
{
    view_main_window.view_manager_ptr = std::make_shared<jrsaoi::ViewManager>();
    event_center = std::make_shared<jrsaoi::EventCenter>(view_main_window.view_manager_ptr);
    view_main_window.message_box_ptr = new jrsaoi::CustomMessageBox(this);
}

void MainWindow::InitLog()
{
    //Log初始化
    std::filesystem::path currentPath = std::filesystem::current_path();
    auto log_path = currentPath.string() + "//log";
    jrscore::AOITools::EnsureDirectoryExists(log_path);
    AOICoreApp->GetLogManager()->SetLogFolderPath(log_path);
    AOICoreApp->GetLogManager()->SetLogOutputLevel(jrscore::LogLevel::LEVEL_TRACE);
    AOICoreApp->GetLogManager()->SetLogMode(jrscore::LogMode::ASYNC);
#ifdef _DEBUG
    AOICoreApp->GetLogManager()->SetLogPosition(jrscore::LogPosition::CONSOLE_AND_FILE);
#else
    AOICoreApp->GetLogManager()->SetLogPosition(jrscore::LogPosition::FILELOG);
#endif // DEBUG
    AOICoreApp->GetLogManager()->CreateDefaultLogger();
}

void MainWindow::InitError()
{
    Log_INFO("初始化错误码");
    auto flush_call_back_lambda = [=](jrscore::ErrorInfo error_info_)
        {
            Log_Error_Stack(error_info_.err_string, " ", error_info_.module_name, " ", error_info_.what, " ", error_info_.err_description);
        };
    AOICoreApp->GetErrorHandler()->SetErrorGenerateCallBack(flush_call_back_lambda);
}

void MainWindow::InitLayout()
{
    QWidget* center_widget_temp = takeCentralWidget();
    if (center_widget_temp)
    {
        delete center_widget_temp;
        center_widget_temp = nullptr;
    }
    QList<int> sizes;
    // 设置各个区域的比列
    sizes << 12000/*左侧操作区域*/ << 2000/**/ << 50000/*中间显示渲染区域*/ << 2000/**/ << 10000/*右侧显示列表区域*/;
    view_main_window.dock_splitter->setSizes(sizes);
    QVBoxLayout* layout = new QVBoxLayout(view_main_window.main_show_widget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(view_main_window.dock_splitter);
    setCentralWidget(view_main_window.main_show_widget);

    InitLayoutLeftDock();
    InitLayoutCenterDock();
    InitLayoutRightDock();

}

void MainWindow::InitLayoutLeftDock()
{
    QWidget* left_widget_container = new QWidget(view_main_window.dock_left);
    left_widget_container->setContentsMargins(0, 0, 0, 0);
    QVBoxLayout* left_widget_layout_v = new QVBoxLayout(left_widget_container);
    left_widget_layout_v->setContentsMargins(0, 0, 0, 0);
    left_widget_layout_v->setSpacing(0);
    left_widget_layout_v->setMargin(0);

    auto autorunpanel = view_main_window.view_manager_ptr->GetView(jrsaoi::CONTROL_PANEL_MODULE_NAME);
    left_widget_layout_v->addWidget(autorunpanel);
    //控制面板区域
    auto operate = view_main_window.view_manager_ptr->GetView(jrsaoi::OPERATE_MODULE_NAME);
    left_widget_layout_v->addWidget(operate);

    view_main_window.dock_left->setWidget(left_widget_container);

    /** 设置左侧操作区域固定无法拖动大小 */
    //int fixed_width = operate->width();
    //view_main_window.dock_left->setMinimumWidth(fixed_width - 300);
    //view_main_window.dock_left->setMaximumWidth(fixed_width - 300);
}

void MainWindow::InitLayoutCenterDock()
{
    QWidget* center_widget_container = new QWidget(view_main_window.dock_center);
    center_widget_container->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_center->setWidget(center_widget_container);
    QVBoxLayout* center_widget_layout_v = new QVBoxLayout(center_widget_container);
    center_widget_layout_v->setContentsMargins(0, 0, 0, 0);
    center_widget_layout_v->setSpacing(0);
    center_widget_layout_v->setMargin(0);

    view_main_window.center_splitter = new QSplitter(Qt::Vertical, view_main_window.dock_center);
    view_main_window.center_splitter->setContentsMargins(0, 0, 0, 0);
    view_main_window.center_splitter->setHandleWidth(0);

    ////渲染区域
    auto render2d_view = view_main_window.view_manager_ptr->GetView(jrsaoi::RENDER2D_MODULE_NAME);
    render2d_view->setContentsMargins(0, 0, 0, 0);
    view_main_window.center_splitter->addWidget(render2d_view);
    ////模板编辑区域
    auto operate_view = view_main_window.view_manager_ptr->GetView(jrsaoi::OPERATE_MODULE_NAME);
    auto template_view = operate_view->GetCustomWidget();
    template_view->setContentsMargins(0, 0, 0, 0);
    view_main_window.center_splitter->addWidget(template_view);
    view_main_window.center_splitter->setSizes({ 700, 300 }); // 70% 和 30% 的比例


    ////日志显示区域
    //auto log_show = view_main_window.view_manager_ptr->GetView(jrsaoi::LOGSHOW_MODULE_NAME);
    //log_show->setContentsMargins(0, 0, 0, 0);
    //view_main_window.center_splitter->addWidget(log_show);


    center_widget_layout_v->addWidget(view_main_window.center_splitter);

    view_main_window.dock_center->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);


}

void MainWindow::InitLayoutRightDock()
{
    QWidget* right_widget_container = new QWidget(view_main_window.dock_right);
    right_widget_container->setContentsMargins(0, 0, 0, 0);
    view_main_window.dock_right->setWidget(right_widget_container);
    QVBoxLayout* right_widget_layout_v = new QVBoxLayout(right_widget_container);
    right_widget_layout_v->setContentsMargins(0, 0, 0, 0);
    right_widget_layout_v->setSpacing(0);
    right_widget_layout_v->setMargin(0);
    auto show_list = view_main_window.view_manager_ptr->GetView(jrsaoi::SHOWLIST_MODULE_NAME);
    auto online_debug_view = view_main_window.view_manager_ptr->GetView(jrsaoi::ONLINEDEBUG_MODULE_NAME);
    right_widget_layout_v->addWidget(show_list);
    right_widget_layout_v->addWidget(online_debug_view);
}

void MainWindow::InitConnect()
{
    //connect(view_main_window.left_hide_btn, &QPushButton::clicked, this, &MainWindow::HideLeftDockWidget);
    connect(view_main_window.right_hide_btn, &QPushButton::clicked, this, &MainWindow::HideRightDockWidget);

    connect(view_main_window.title_widget, &jrsaoi::CustomTitleView::SigActionTrigger, this, [&](const jrsdata::ViewParamBasePtr& param_)
        {
            event_center->Notify(param_);// 转发到EventCenter
        });

    connect(this, &MainWindow::SigCloseAllDevice, this, [&](const jrsdata::ViewParamBasePtr& param_)
        {
            event_center->Notify(param_);
        });

    // 渲染界面的框变化导致title界面需要更新
    //connect(event_center.get(), &jrsaoi::EventCenter::SigUpdateItem, view_main_window.title_widget, &jrsaoi::CustomTitleView::SlotGraphicUpdateFun);
}

void MainWindow::InitMessage()
{
    jrscore::MessageCallBack callback = std::bind(&MainWindow::ShowMessageCallBack, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4);
    AOICoreApp->GetMessageManager()->SetMessageCallBack(callback);
}

jrscore::MessageButton MainWindow::ShowMessageCallBack(const jrscore::LogLevel& level_, const std::string& title_, const std::string& msg_, const int& message_btn_)
{
    jrsaoi::MessageBase::Message msg(jrscore::AOITools::GetCurrentDataTime("%Y%m%d%H%M%S", true), title_, level_, msg_, message_btn_);
    return view_main_window.message_box_ptr->ShowMessage(msg);
}


void MainWindow::closeEvent(QCloseEvent* event)
{

    auto res = JRSMessageBox_WARN("警告", "确认关闭软件？", jrscore::MessageButton::Yes | jrscore::MessageButton::Cancel);

    if (res == jrscore::MessageButton::Yes)
    {
        event->accept();
    }
    else 
    {
        event->ignore();
        return;
    }
    jrsdata::OperateViewParamPtr param = std::make_shared<jrsdata::OperateViewParam>();
    param->module_name = VIEW_MODULE_NAME;
    param->topic_name = SHORTCUT_TRIGGER_TOPIC_NAME;
    param->sub_name = SHORTCUT_LOGIC_SUB_NAME;
    param->invoke_module_name = LOGIC_MODULE_NAME;
    param->event_name = "close_all_device";
    SigCloseAllDevice(param);
    event->accept();
}

