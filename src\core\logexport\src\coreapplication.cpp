#include "coreapplication.h"
#include <filesystem>

namespace jrscore
{
    struct CoreAppImplData
    {
        ErrorHandlerPtr p_handler;
        LogManagerPtr p_log_manager;
    };
    CoreApplication::CoreApplication ():p_data(new CoreAppImplData)
    {
        p_data->p_handler = std::make_shared<ErrorHandler>();
        p_data->p_log_manager = std::make_shared<LogManager> ();
        //Log初始化
        std::filesystem::path currentPath = std::filesystem::current_path();
        auto log_path = currentPath.string() + "//log//Alog3DLog";
        EnsureDirectoryExists(log_path);
        p_data->p_log_manager->SetLogFolderPath(log_path);
        p_data->p_log_manager->SetLogOutputLevel(jrscore::LogLevel::LEVEL_TRACE);
        p_data->p_log_manager->SetLogMode(jrscore::LogMode::ASYNC);
        p_data->p_log_manager->SetLogPosition(jrscore::LogPosition::FILELOG);
        p_data->p_log_manager->CreateDefaultLogger();


        Log_INFO("初始化错误码");
        auto flush_call_back_lambda = [=](jrscore::ErrorInfo error_info_)
        {
            Log_Error_Stack(error_info_.err_string, " ", error_info_.module_name, " ", error_info_.what, " ", error_info_.err_description);
        };
        p_data->p_handler->SetErrorGenerateCallBack(flush_call_back_lambda);

    }
    CoreApplication::~CoreApplication ()
    {
    }
    CoreApplication* CoreApplication::GetInstance ()
    {
        static std::shared_ptr<CoreApplication> core_app = std::make_shared<CoreApplication> ();

        return core_app.get ();
    }
    ErrorHandlerPtr CoreApplication::GetErrorHandler ()
    {
       
        return p_data->p_handler;
    }
    LogManagerPtr CoreApplication::GetLogManager ()
    {
    
        return p_data->p_log_manager;
    }


    void CoreApplication::EnsureDirectoryExists (const std::string& directory)
    {
        if (!std::filesystem::exists (directory))
        {
            std::filesystem::create_directories (directory);
        }
    }
    std::string CoreApplication::GetCurrentPath ()
    {
        std::filesystem::path current_path = std::filesystem::current_path ();
        return current_path.string ();
    }
}