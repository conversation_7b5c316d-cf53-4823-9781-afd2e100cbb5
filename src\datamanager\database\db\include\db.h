#ifndef __DBBASE_HPP__
#define __DBBASE_HPP__

//PREBUILD
#include "datapch.h"
#include <iostream>
#include <chrono>
//#include "dbparam.h"

namespace jrsdatabase {
    class DBHandel
    {
    public:
        explicit DBHandel();
        ~DBHandel() = default;

        int Create(const jrsdatabase::DatabaseConnectParam& conn_param_);

        auto GetConnPtr() -> std::shared_ptr<IDatabase<MySqlImp>>;

        int BackDump(const std::string& db_name_);
        template <typename DB>
        auto ReGetConnPtr(std::shared_ptr<DB> conn_)
        {
            auto& conn_pool_ = jrsdatabase::ConnectionPool<jrsdatabase::IDatabase<jrsdatabase::MySqlImp>>::Instance();
            conn_pool_.ReturnBack(conn_);
            return conn_pool_.Get();
        }

    private:

        int InitDBConnectPool(const int& conn_max_,
            const std::string& db_host_,
            const std::string& db_user_name_,
            const std::string& db_pwd_,
            const std::string& db_name_);

        auto GetLastCreateTime();

        std::chrono::system_clock::time_point _latest_tm;
    };
    using DBPtr = std::shared_ptr<DBHandel>;
};

#endif //!__DBBASE_HPP__