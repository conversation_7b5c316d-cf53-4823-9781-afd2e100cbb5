#include "modulehandle.h"

namespace jrscore
{


   /* struct  ModuleImplData
    {
        std::string module_name;
    };*/

    ModuleHandle::ModuleHandle (const std::string& module_name_)//:p_data(new  ModuleImplData)
    {
       module_name = module_name_;
       PubSubManager::GetInstance ().AddModule (module_name);
    }

    ModuleHandle::~ModuleHandle ()
    {
        /*delete p_data;
        p_data = nullptr;*/
    }

    int ModuleHandle::AddAdvertise (const std::string& topic_name_)
    {
      
       return PubSubManager::GetInstance ().AddAdvertise (module_name, topic_name_);

    }

    const std::string& ModuleHandle::GetMoudleName () 
    {
        return module_name;
    }


}