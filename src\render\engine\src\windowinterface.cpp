#include "windowinterface.h"

#include "shaderprogram.h"
#include "renderer.h"
#include "visualcameraabstract.h"
#include "windowsignalemitter.h"
//#include "renderconstants.hpp"
#include "engineconstants.hpp" // RenderType RefreshMode
#include "log.h"

#include <qevent.h>
//#include <QWheelEvent>
//#include <QOffscreenSurface>

//// 指定最低版本
// const QString MIN_VERSION = "5.15.0";
//
// bool checkQtVersion() {
//     QString currentVersion = QString(qVersion());
//     qDebug() << "当前Qt版本:" << currentVersion;
//     return currentVersion >= MIN_VERSION;
// }

WindowInterface::WindowInterface(QObject* parent)
    : m_initialized(false), mouse_pressed(false), mouse_moved(false)
    , main_camera(nullptr)
    , shader_manager(nullptr)
    , refresh_mode(RefreshMode::RM_REAL_TIME) // RefreshMode::RM_EVENT_DRIVEN or RefreshMode::RM_REAL_TIME
    , renderers(static_cast<int>(RenderType::SIZE), nullptr)
    , emitter(new WindowSignalEmitter(parent))

{
    // if (!checkQtVersion()) {
    //     qCritical() << "Qt版本过低。需要Qt版本" << MIN_VERSION << "或更高";
    //     return;  // 版本不符合要求，终止程序
    // }

    for (int i = 0; i < static_cast<int>(RenderType::SIZE); i++)
    {
        renderers[i] = new Renderer();
    }
}

WindowInterface::~WindowInterface()
{
    for (int i = 0; i < static_cast<int>(RenderType::SIZE); i++)
    {
        delete renderers[i];
        renderers[i] = nullptr;
    }
    // if (main_camera)
    // {
    //     delete main_camera;
    //     main_camera = nullptr;
    // }
    if (shader_manager)
    {
        delete shader_manager;
        shader_manager = nullptr;
    }
}

void WindowInterface::Update()
{
    if (!m_initialized)
        return;

    for (int i = 0; i < static_cast<int>(RenderType::SIZE); i++)
    {
        if (renderers[i] == nullptr)
            continue;
        renderers[i]->Update();
    }
}

// void WindowInterface::MoveCamera(int type)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         camera->Move(static_cast<CameraDirection>(type));
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->MoveCamera(type);
//     // }
//     RequestUpdate();
// }

// void WindowInterface::MoveCamera(float xoffset, float yoffset)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         auto p = camera->GetCameraPosition();
//         camera->SetCameraPosition(p.x() + xoffset, p.y() - yoffset);
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->MoveCamera(xoffset, yoffset);
//     // }
//     RequestUpdate();
// }

// void WindowInterface::ResetCamera(int type)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         camera->SetResetMode(static_cast<CameraResetMode>(type));
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->ResetCamera(type);
//     // }
//     RequestUpdate();
// }

// void WindowInterface::MoveCameraTo(float x, float y)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         camera->SetCameraPosition(x, -y);
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->MoveCameraTo(x, y); // 坐标相反
//     // }
//     RequestUpdate();
// }

// void WindowInterface::SetZoom(float zoom)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         camera->SetZoom(zoom);
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->SetZoom(zoom);
//     // }
//     RequestUpdate();
// }

// void WindowInterface::SetZoomState(int state)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         camera->SetScaleMode(static_cast<CameraScaleMode>(state));
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->SetZoomState(state);
//     // }
//     RequestUpdate();
// }

// bool WindowInterface::SetCanvasSize(int width, int height)
// {
//     if (main_camera)
//     {
//         auto& camera = main_camera;
//         auto topdown = dynamic_cast<VisualCameraTopDown*>(camera);
//         if (!topdown)
//             return false;
//         if (!topdown->SetCanvas(width, height))
//             return false;
//     }
//     else
//     {
//         return false;
//     }

//     // for (int i = 0; i < RenderType::SIZE; i++)
//     // {
//     //     if (renderers[i] == nullptr)
//     //         continue;
//     //     renderers[i]->SetCanvasSize(width, height);
//     // }
//     RequestUpdate();
//     return true;
// }

void WindowInterface::SetMainVisualCamera(VisualCameraAbstract* camera)
{
    main_camera = camera;
    if (renderers.size() >= static_cast<int>(RenderType::SIZE))
    {
        renderers[static_cast<int>(RenderType::RT_Background)]->SetCamera(camera);
        renderers[static_cast<int>(RenderType::RT_Graphics)]->SetCamera(camera);
    }
}

void WindowInterface::AddObject(RenderAbstractPtr ro, const RenderType& type)
{
    int index = static_cast<int>(type);
    if (index >= (int)renderers.size() || renderers[index] == nullptr)
        return;
    renderers[index]->AddObject(ro);
}

void WindowInterface::RemoveObject(RenderAbstractPtr ro, const RenderType& type)
{
    int index = static_cast<int>(type);
    if (index >= (int)renderers.size() || renderers[index] == nullptr)
        return;
    renderers[index]->RemoveObject(ro);
}

void WindowInterface::RenderSetProgram(Renderer* render, const ProgramType& type)
{
    if (!render)
        return;
    if (!shader_manager)
        return;
    auto program = shader_manager->GetProgram(type);
    render->SetProgram(program);
}

bool WindowInterface::initialize()
{
    QOpenGLContext* glContext = GetOpenGLContext();
    if (!glContext)
    {
        return false;
    }
    ROpenGLFunctions* glFunc = functions();
    if (!glFunc)
    {
        return false;
    }

    if (!m_initialized)
    {
        if (!glFunc->initializeOpenGLFunctions())
        {
            return false;
        }
        glFunc->glClearColor(color_tea_green.r, color_tea_green.g, color_tea_green.b, color_tea_green.a);
        glFunc->glEnable(GL_TEXTURE_2D);
        /*支持透明纹理绘制*/
        glFunc->glEnable(GL_BLEND);
        glFunc->glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA); // 混合测试函数
        /*支持抗锯齿*/
        // glFunc->glEnable(GL_LINE_SMOOTH);
        // glFunc->glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);
        /*深度测试*/
        // glFunc->glEnable(GL_DEPTH_TEST);
        // glFunc->glDepthFunc(GL_LESS);
        /*线框模式 测试*/
        // glFunc->glPolygonMode(GL_FRONT, GL_LINE); 


        glFunc->glPointSize(12);

        auto context = GetOpenGLContext();
        auto mainSurface = context->surface();

        // auto surface = new QOffscreenSurface();
        // surface->setFormat(context->format());
        // surface->create();
        shader_manager = new ShaderProgramManager();

        for (size_t i = 0; i < static_cast<int>(RenderType::SIZE); ++i)
        {
            if (!renderers[i])
                continue;
            renderers[i]->SetEngine(this);
            renderers[i]->SetContext(mainSurface, context);
            renderers[i]->SetQPaintDevice(GetPaintDevice());
        }

        if (renderers[static_cast<int>(RenderType::RT_Background)])
        {
            renderers[static_cast<int>(RenderType::RT_Background)]->SetProgram(ProgramType::TEXTURE);
        }
        if (renderers[static_cast<int>(RenderType::RT_Foreground)])
        {
            renderers[static_cast<int>(RenderType::RT_Foreground)]->SetProgram(ProgramType::GRAPHICS);
        }
        if (renderers[static_cast<int>(RenderType::RT_Graphics)])
        {
            renderers[static_cast<int>(RenderType::RT_Graphics)]->SetProgram(ProgramType::GRAPHICS);
        }

        QObject::connect(context, &QOpenGLContext::aboutToBeDestroyed, context, [=]
            {
                for (size_t i = 0; i < renderers.size(); ++i)
                {
                    if (!renderers[i])
                        continue;
                    renderers[i]->Destroy();
                }
            });

        m_initialized = true;
    }
    //设置为透明
    glFunc->glClearColor(0, 0, 0, 0);

    return true;
}

void WindowInterface::onResizeGL(int w, int h)
{
    setGLViewport(0, 0, w, h);
}

void WindowInterface::setGLViewport(const QRect& rect)
{
    const int retinaScale = GetDevicePixelRatio();
    m_gl_viewport = QRect(rect.left() * retinaScale, rect.top() * retinaScale,
        rect.width() * retinaScale, rect.height() * retinaScale);

    // if (GetOpenGLContext() && GetOpenGLContext()->isValid())
    {

        if (main_camera)
            main_camera->SetViewport(m_gl_viewport.width(), m_gl_viewport.height());
    }
    // DoMakeCurrent();
    // for (int i = 0; i < RenderType::SIZE; ++i)
    // {
    //     if (!renderers[i])
    //         continue;
    //     renderers[i]->SetViewport(m_gl_viewport.width(), m_gl_viewport.height());
    // }
    // DoDoneCurrent();
    RequestUpdate();

}


void WindowInterface::uninitializeGL()
{
    if (!m_initialized)
        return;

    // DoMakeCurrent();
    // ROpenGLFunctions *glFunc = functions();
    // DoDoneCurrent();

    m_initialized = false;

}

void WindowInterface::doPaintGL()
{
    if (!initPaintGL())
    {
        return;
    }

    if (GetOpenGLContext() && GetOpenGLContext()->isValid())
    {
        DoMakeCurrent();

        // ROpenGLFunctions* glFunc = functions();
        // if (glFunc->glIsEnabled(GL_LINE_SMOOTH)) {
        //     printInfo(std::stringstream() << "glIsEnabled(GL_LINE_SMOOTH) is true");
        // }
        // if (glFunc->glIsEnabled(GL_BLEND)) {
        //     printInfo(std::stringstream() << "glIsEnabled(GL_BLEND) is true");
        // }
        // glFunc->glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        // glFunc->glEnable(GL_DEPTH_TEST);
        // glFunc->glEnable(GL_TEXTURE_2D);

        // glFunc->glViewport(0, 0, m_gl_viewport.width(), m_gl_viewport.height());
        Update();

        /*测试多视角*/
        // glFunc->glViewport(m_gl_viewport.width() - 200, 100, 100, 100);
        // Update();

        // TODO 交换缓冲区,未实现,离屏渲染
        // swapGLBuffers(); 

        // 实时刷新
        if (refresh_mode == RefreshMode::RM_REAL_TIME)
        {
            RequestUpdate();
        }

        // DoDoneCurrent();
    }
}

void WindowInterface::processMousePressEvent(QMouseEvent* event)
{
    mouse_moved = false;
    mouse_pressed = true;

    m_point_mouse_press = event->pos();
    m_point_mouse_last = event->pos();
    emit emitter->signal_mouse_press(event->button(), event->pos().x(), event->pos().y());
}

void WindowInterface::processMouseDoubleClickEvent([[maybe_unused]] QMouseEvent* event)
{
    // TODO 双击处理逻辑
}

void WindowInterface::processMouseMoveEvent(QMouseEvent* event)
{
    if (!mouse_moved && !mouse_pressed)
    {
        m_point_mouse_press = event->pos();
    }
    if (m_point_mouse_last.isNull())
    {
        m_point_mouse_last = event->pos();  //
    }

    emit emitter->signal_mouse_move(event->buttons(),
        event->pos().x(), event->pos().y(),
        m_point_mouse_last.x(), m_point_mouse_last.y(),
        m_point_mouse_press.x(), m_point_mouse_press.y());

    mouse_moved = true;

    m_point_mouse_last = event->pos();
}

void WindowInterface::processMouseReleaseEvent(QMouseEvent* event)
{
    mouse_moved = false;
    mouse_pressed = false;
    /*减少劣质鼠标单击时晃动干扰*/
    if ((event->pos() - m_point_mouse_press).manhattanLength() < 4)
    {
        emit emitter->signal_mouse_clicked(event->button(), event->pos().x(), event->pos().y());
    }
    else
    {
        emit emitter->signal_mouse_release(event->button(), event->pos().x(), event->pos().y(), m_point_mouse_press.x(),
            m_point_mouse_press.y());
    }

    m_point_mouse_press = QPoint();
    m_point_mouse_last = QPoint();
}

void WindowInterface::processWheelEvent(int degree, int x, int y)
{
    emit emitter->signal_wheel_delta(degree, x, y);
}

void WindowInterface::processKeyPressEvent(int key, bool isAutoRepeat)
{
    emit emitter->signal_key_press(key, isAutoRepeat);
}

void WindowInterface::processKeyReleaseEvent(int key, bool isAutoRepeat)
{
    emit emitter->signal_key_release(key, isAutoRepeat);
}

// void WindowInterface::processWheelEvent(QWheelEvent* event)
// {
//     QPoint angle = event->angleDelta();
//     int numDegrees = angle.y();
//     emit emitter->signal_wheel_delta(numDegrees, event->position().x(), event->position().y());
// }

// void WindowInterface::processKeyPressEvent(QKeyEvent* event)
// {
//     emit emitter->signal_key_press(event->key(), event->isAutoRepeat());
// }

// void WindowInterface::processKeyReleaseEvent(QKeyEvent* event)
// {
//     emit emitter->signal_key_release(event->key(), event->isAutoRepeat());
// }

void WindowInterface::processResizeEvent(int w, int h)
{
    setGLViewport(0, 0, w, h);
    emit emitter->signal_window_resize(w, h);
}

void WindowInterface::processEnterEvent()
{
    emit emitter->signal_window_enter();
}

void WindowInterface::processLeaveEvent()
{
    emit emitter->signal_window_leave();
}
