﻿//CUSTOM
#include "showlistview.h"
#include "showlisttools.h"
#include "projectparam.hpp"
#include "inputlineeditdialog.h"
#include "datadefine.hpp"
#include "viewparam.hpp"
#include "nocoverbackgrounddelegate.hpp"
#include "jsonoperator.hpp"
#include "editview.h"
#pragma warning(push,3)
#include "ui_editview.h"
#pragma warning(pop)
//STL
#include <memory>
#include <iostream>
//QT
#include <QItemSelection>
#include <QHeaderView>

using namespace jrscore;
using namespace jrsdata;

namespace jrsaoi
{
    // 构造函数：初始化界面和连接信号槽。
    ShowListView::ShowListView(const std::string& name, QWidget* parent)
        : ViewBase(name, parent)
        , ui(new Ui::ShowListView())
        , _edit_widget(nullptr)
    {
        ui->setupUi(this);  // 加载UI界面
        Init();  // 初始化界面和数据模型
        ConnectSlots();  // 连接信号和槽
    }

    // 析构函数：释放资源。
    ShowListView::~ShowListView()
    {
        delete ui;  // 释放UI资源
    }

    int ShowListView::Init()
    {
        //取消产品列表的显示
        ui->widget->setVisible(false);
        //取消检测项目显示
       ui->widget_3->setVisible(false);

        InitializeModels();
        SetCustomDelegates();
        HideVerticalHeaders();
        InitializeViewParameters();
        return jrscore::AOI_OK;
    }

    void ShowListView::InitializeModels()
    {
        show_board_model = new TableViewModel(nullptr);
        show_part_number_model = new TableViewModel(nullptr);
        show_device_model = new TableViewModel(nullptr);
        ui->list_product->setModel(show_board_model);
        ui->list_part_no->setModel(show_part_number_model);
        ui->list_device->setModel(show_device_model);
    }

    void ShowListView::SetCustomDelegates()
    {
        ui->list_product->setItemDelegate(new NoCoverBackgroundDelegate());
        ui->list_part_no->setItemDelegate(new NoCoverBackgroundDelegate());
        ui->list_device->setItemDelegate(new NoCoverBackgroundDelegate());
    }

    void ShowListView::HideVerticalHeaders()
    {
        ui->list_product->verticalHeader()->setVisible(false);
        ui->list_part_no->verticalHeader()->setVisible(false);
        ui->list_device->verticalHeader()->setVisible(false);
    }

    void ShowListView::InitializeViewParameters()
    {
        m_show_list_params.m_subboard_id = -1;
        m_show_list_params.m_part_number_id = -1;
        m_show_list_params.m_device_id = -1;
        m_show_list_params.m_update_subboard_data = false;
        m_show_list_params.m_update_part_number_data = false;
        m_show_list_params.m_update_device_data = false;
        m_send_signal_to_render = false;
    }

    // 保存视图参数（暂未实现）。
    int ShowListView::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;  // 避免未使用变量的警告
        return jrscore::AOI_OK;
    }

    // 设置焊盘视图的布局。
    void ShowListView::SetWindowView(QWidget* widget)
    {
        auto pad_view_layout = new QVBoxLayout();
        pad_view_layout->setContentsMargins(0, 0, 0, 0);
        pad_view_layout->setSpacing(0);
        pad_view_layout->addWidget(widget);
        ui->frame_window_view->setLayout(pad_view_layout);
        _edit_widget = widget;
    }

    // 根据事件类型更新视图。
    int ShowListView::UpdateView(const jrsdata::ViewParamBasePtr& param_)
    {
        if (param_->event_name.compare(jrsaoi::PROJECT_UPDATE_EVENT_NAME) == 0)
        {
            return ProjectUpdate(param_);
        }
        else if (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_CREATE_EVENT_NAME) == 0)
        {
            return GraphicsCreate(param_);
        }
        else if (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_DELETE_EVENT_NAME) == 0)
        {
            return GraphicsDelete(param_);
        }
        else if (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_SELECT_EVENT_NAME) == 0)
        {
            return GraphicsSelect(param_);
        }
        else if (param_->event_name.compare(jrsaoi::RENDER2D_GRAPHICS_UPDATE_EVENT_NAME) == 0)
        {
            return GraphicsUpdate(param_);
        }
        else if (param_->event_name.compare(jrsaoi::RENDER2D_COMPONENT_SELECT_EVENT_NAME) == 0)
        {
            return CadSelect(param_);
        }
        else if (param_->event_name.compare(jrsaoi::CONTROL_PANEL_ENABLE_ONLINE_DEBUG_SEND_EVENT_NAME) == 0)
        {
            this->setHidden(true);
        }
        else if (param_->event_name.compare(jrsaoi::CONTROL_PANEL_DISABLE_ONLINE_DEBUG_SEND_EVENT_NAME) == 0)
        {
            this->setHidden(false);
        }
        return 1;  // 未知事件类型
    }

    void ShowListView::BodyCircularToggled(bool checked)
    {
        if (checked)
        {
            UpdateVariant(QVariant(0), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_CIRCULAR));
        }
    }

    void ShowListView::BodyRectangleToggled(bool checked)
    {
        if (checked)
        {
            UpdateVariant(QVariant(1), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_RECTANGLE));
        }
    }

    void ShowListView::BodyNoTestedStateChanged(int state)
    {
        UpdateVariant(QVariant(bool(state == Qt::Checked)), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_NOT_TESTED));
    }

    void ShowListView::BodyWidthValueChanged(double value)
    {
        UpdateVariant(QVariant(value), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_WIDTH));
    }

    void ShowListView::BodyHeightValueChanged(double value)
    {
        UpdateVariant(QVariant(value), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_HEIGHT));
    }

    void ShowListView::BodyAngleValueChanged(int value)
    {
        UpdateVariant(QVariant(value), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE));
    }

    void ShowListView::BodyAdd90DegreesClicked()
    {
        UpdateVariant(QVariant(90), int(LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE_ADD));
    }

    // 更新元件字段的值。
    void ShowListView::UpdateVariant(const QVariant& value, int type)
    {
        if (show_device_model == nullptr)
            return;
        auto device_data = GetDeviceData();  // 获取当前选中的元件数据
        if (!device_data)
            return;
        UpdateDeviceField(device_data, type, value);  // 根据字段类型更新元件数据
        SaveDeviceData(device_data);  // 保存更新后的元件数据
        RenderInvokeFun(device_data, type);  // 触发渲染更新
    }

    // 获取当前选中的元件数据
    std::shared_ptr<DeviceDataStruct> ShowListView::GetDeviceData() const
    {
        std::vector<ShowTableParamBasePtr> show_table_datas;
        show_device_model->GetDataModel(show_table_datas);
        std::vector<DeviceDataStructPtr> device_data_vector;
        for (const auto& show : show_table_datas)
        {
            auto device_data = std::dynamic_pointer_cast<DeviceDataStruct>(show);
            if (device_data)
            {
                device_data_vector.push_back(device_data);
            }
        }
        if (m_show_list_params.m_device_id >= 0 && m_show_list_params.m_device_id < device_data_vector.size())
        {
            return device_data_vector[m_show_list_params.m_device_id];
        }
        return nullptr;
    }

    void ShowListView::UpdateDeviceField(DeviceDataStructPtr device_data, int type, const QVariant& value) const
    {
        if (!device_data)
            return;
        switch (LISTVIEW_DATA_DEFINE(type))
        {
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_CIRCULAR:
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_RECTANGLE:
            UpdateBodyShapeType(device_data, value);
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_NOT_TESTED:
            UpdateBodyNotTested(device_data, value);
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_WIDTH:
            UpdateBodyWidth(device_data, value);
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_HEIGHT:
            UpdateBodyHeight(device_data, value);
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE:
            UpdateBodyAngle(device_data, value);
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE_ADD:
            UpdateBodyAngleAdd(device_data, value);
            break;
        default:
            Log_ERROR("Unknown field type: {}", type);
            break;
        }
    }

    // 更新元件的主体形状类型。
    void ShowListView::UpdateBodyShapeType(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_body_shape_type = value.toInt();
    }

    // 更新元件的主体未测试标志。
    void ShowListView::UpdateBodyNotTested(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_body_tested = value.toBool();
    }

    // 更新元件的主体宽度。
    void ShowListView::UpdateBodyWidth(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_width = value.toFloat();
    }

    // 更新元件的主体高度。
    void ShowListView::UpdateBodyHeight(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_height = value.toFloat();
    }

    // 更新元件的主体角度。
    void ShowListView::UpdateBodyAngle(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_angle = value.toFloat();
    }

    // 增加元件的主体角度。
    void ShowListView::UpdateBodyAngleAdd(DeviceDataStructPtr device_data, const QVariant& value) const
    {
        device_data->m_angle += value.toFloat();
    }

    // 将更新后的元件数据保存回模型
    void ShowListView::SaveDeviceData(DeviceDataStructPtr device_data)
    {
        std::vector<ShowTableParamBasePtr> show_table_datas;
        show_device_model->GetDataModel(show_table_datas);
        std::vector<DeviceDataStructPtr> device_data_vector;
        for (const auto& show : show_table_datas)
        {
            auto device_data_ptr = std::dynamic_pointer_cast<DeviceDataStruct>(show);
            if (device_data_ptr)
                device_data_vector.push_back(device_data_ptr);
        }
        if (m_show_list_params.m_device_id >= 0 && m_show_list_params.m_device_id < device_data_vector.size())
        {
            device_data_vector[m_show_list_params.m_device_id] = device_data;
        }
        std::vector<ShowTableParamBasePtr> updated_show_table_datas;
        for (const auto& device_data_temp : device_data_vector)
        {
            updated_show_table_datas.push_back(std::dynamic_pointer_cast<ShowTableParamBase>(device_data_temp));
        }
        show_device_model->SetDataModel(updated_show_table_datas);
    }

    void ShowListView::QueryConditionFocus()
    {
       // ui->query_condition->setFocus();
    }

    // 触发渲染更新函数
    void ShowListView::RenderInvokeFun(const std::shared_ptr<DeviceDataStruct>& data_, int type_)
    {
        // 创建渲染参数对象
        ComponentListViewParamPtr param = std::make_shared<jrsdata::ComponentListViewParam>();
        param->module_name = jrsaoi::VIEW_MODULE_NAME;
        param->topic_name = jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME;
        param->sub_name = jrsaoi::SHOWLIST_CHANGE_SUB_NAME;
        param->event_name = jrsaoi::SHOWLIST_UPDATE_RENDER_EVENT_NAME;
        switch (LISTVIEW_DATA_DEFINE(type_))
        {
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_WIDTH:
            param->update_type = jrsdata::ComponentListViewParam::UpdateType::Width;
            // 设置参数值
            param->body_width = data_->m_width;
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_HEIGHT:
            param->body_height = data_->m_height;
            param->update_type = jrsdata::ComponentListViewParam::UpdateType::Height;
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE_ADD:
            param->update_type = jrsdata::ComponentListViewParam::UpdateType::Angle;
            param->body_angle = data_->m_angle;
            break;
        case LISTVIEW_DATA_DEFINE::LISTVIEW_BODY_ANGLE_VALUE:
            param->update_type = jrsdata::ComponentListViewParam::UpdateType::Angle;
            param->body_angle = data_->m_angle;
            break;
        default:
            Log_ERROR("Unknown field type: {}", type_);
            break;
        }
        param->subboard_name = data_->m_subboard_name;
        param->component_name = data_->m_device_name;
        // 发出渲染触发信号
        emit ShowListRenderTrigger(param);
    }

    void ShowListView::BoardUpdateView(std::vector<SubBoardDataStructPtr> vector_data)
    {
        BlockSignals(true);
        if (show_board_model != nullptr)
        {
            std::vector<ShowTableParamBasePtr> show_datas;
            for (int i = 0; i < vector_data.size(); i++)
            {
                if (vector_data.at(i)->m_show_enable)
                {
                    show_datas.push_back(std::dynamic_pointer_cast<ShowTableParamBase>(vector_data.at(i)));
                }
            }
            show_board_model->setRowCount(int(show_datas.size()));
            show_board_model->SetDataModel(show_datas);
            ResizeBoardColumn();
            LoadLayoutJson(ui->list_product, show_board_model, board_file_name);
            ui->list_product->viewport()->update();
        }
        BlockSignals(false);
    }

    void ShowListView::PartNumberUpdateView(std::vector<PartNumberDataStructPtr> vector_data)
    {
        BlockSignals(true);
        if (show_part_number_model != nullptr)
        {
            std::vector<ShowTableParamBasePtr> show_datas;
            for (int i = 0; i < vector_data.size(); i++)
            {
                if (vector_data.at(i)->m_show_enable)
                {
                    show_datas.push_back(std::dynamic_pointer_cast<ShowTableParamBase>(vector_data.at(i)));
                }
            }
            show_part_number_model->setRowCount(int(show_datas.size()));
            show_part_number_model->SetDataModel(show_datas);
            ResizePartNumberColumn();
            LoadLayoutJson(ui->list_part_no, show_board_model, part_no_file_name);
            ui->list_part_no->viewport()->update();
        }
        BlockSignals(false);
    }

    void ShowListView::DeviceUpdateView(std::vector<DeviceDataStructPtr> vector_data)
    {
        BlockSignals(true);
        if (show_device_model != nullptr)
        {
            std::vector<ShowTableParamBasePtr> show_datas;
            for (int i = 0; i < vector_data.size(); i++)
            {
                if (vector_data.at(i)->m_show_enable)
                {
                    show_datas.push_back(std::dynamic_pointer_cast<ShowTableParamBase>(vector_data.at(i)));
                }
            }
            show_device_model->setRowCount(int(show_datas.size()));
            show_device_model->SetDataModel(show_datas);
            ResizeDeviceColumn();
            LoadLayoutJson(ui->list_device, show_board_model, device_file_name);
            ui->list_device->viewport()->update();
        }
        BlockSignals(false);
    }

    void ShowListView::BoardSelectIndex(int row)
    {
        if (show_board_model != nullptr)
        {
            if (row >= int(show_board_model->rowCount()))
            {
                row = int(show_board_model->rowCount()) - 1;
            }
            if (int(show_board_model->rowCount()) > row && row > -1)
            {
                SelectTableRow(ui->list_product->selectionModel(), show_board_model->index(row, 0), ui->list_product);
                m_show_list_params.m_subboard_id = row;
            }
        }
    }

    void ShowListView::PartNumberSelectIndex(int row)
    {
        if (show_part_number_model != nullptr)
        {
            if (row >= int(show_part_number_model->rowCount()))
            {
                row = int(show_part_number_model->rowCount()) - 1;
            }
            if (int(show_part_number_model->rowCount()) > row && row > -1)
            {
                SelectTableRow(ui->list_part_no->selectionModel(), show_part_number_model->index(row, 0), ui->list_part_no);
                m_show_list_params.m_part_number_id = row;
            }
        }
    }

    void ShowListView::DeviceSelectIndex(int row)
    {
        if (show_device_model != nullptr)
        {
            if (row >= int(show_device_model->rowCount()))
            {
                row = int(show_device_model->rowCount()) - 1;
            }
            if (int(show_device_model->rowCount()) > row && row > -1)
            {
                SelectTableRow(ui->list_device->selectionModel(), show_device_model->index(row, 0), ui->list_device);
                m_show_list_params.m_device_id = row;
            }
        }
    }

    void ShowListView::SelectTableRow(QItemSelectionModel* selectionModel, QModelIndex index, CustomTableView* table_view)
    {
        if (selectionModel == nullptr || table_view == nullptr || !index.isValid())
        {
            return;
        }
        // 设置焦点到 table_view
        table_view->setFocus();
        // 滚动到指定的索引
        table_view->scrollTo(index);
        // 更新 table_view
        table_view->update();
        // 设置当前选中的索引
        table_view->setCurrentIndex(index);
        // 选中行
        table_view->selectRow(index.row());
        // 再次更新 table_view
        table_view->update();
    }

    void ShowListView::DeviceInfoUpdateRow(int row)
    {
        if (show_device_model != nullptr)
        {
            std::vector<ShowTableParamBasePtr> show_table_datas;
            show_device_model->GetDataModel(show_table_datas);
            std::vector<DeviceDataStructPtr> vector_data;
            for (auto show : show_table_datas)
            {
                vector_data.push_back(std::dynamic_pointer_cast<DeviceDataStruct>(show));
            }
            if (row < vector_data.size())
            {
                DeviceDataStructPtr select = vector_data.at(row);
                if (m_send_signal_to_render)
                {
                    m_send_signal_to_render = false;
                    ComponentListViewParamPtr param = std::make_shared<jrsdata::ComponentListViewParam>();
                    param->event_name = jrsaoi::SHOWLIST_SELECT_COMPONENT_UPDATE;
                    param->module_name = jrsaoi::VIEW_MODULE_NAME;
                    param->topic_name = jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME;
                    param->sub_name = "all";
                    param->component_name = select->m_device_name;
                    param->component_part_number = select->m_part_number;
                    emit ShowListRenderTrigger(param);
                    // 发出元件变更信号。
                }
            }
        }
    }

    void ShowListView::AddPartNumberID(int id)
    {
        std::vector<ShowTableParamBasePtr> show_datas;
        if (show_part_number_model != nullptr)
        {
            show_part_number_model->GetDataModel(show_datas);
            std::vector<PartNumberDataStructPtr> vec_part_number;
            for (auto show : show_datas)
            {
                auto one_part_number = std::dynamic_pointer_cast<PartNumberDataStruct>(show);
                if (one_part_number)
                {
                    vec_part_number.push_back(one_part_number);
                }
            }
            int part_number_size = int(vec_part_number.size());
            if (part_number_size > 0)
            {
                m_show_list_params.m_part_number_id += id;
                if (m_show_list_params.m_part_number_id < 0)
                {
                    m_show_list_params.m_part_number_id = part_number_size - 1;
                }
                if (m_show_list_params.m_part_number_id >= part_number_size)
                {
                    m_show_list_params.m_part_number_id = 0;
                }
            }
        }
        m_send_signal_to_render = true;
        m_show_list_params.m_update_device_data = true;
        UpdateAllTableView();
    }

    void ShowListView::AddDeviceID(int id)
    {
        std::vector<ShowTableParamBasePtr> show_table_datas;
        if (show_device_model != nullptr)
        {
            show_device_model->GetDataModel(show_table_datas);
            std::vector<DeviceDataStructPtr> vec_device_data;
            for (const auto& show : show_table_datas)
            {
                auto device_data = std::dynamic_pointer_cast<DeviceDataStruct>(show);
                if (device_data)
                {
                    vec_device_data.push_back(device_data);
                }
            }
            int device_data_size = int(vec_device_data.size());
            if (device_data_size > 0)
            {
                m_show_list_params.m_device_id += id;
                if (m_show_list_params.m_device_id < 0)
                {
                    m_show_list_params.m_device_id = device_data_size - 1;
                }
                if (m_show_list_params.m_device_id >= device_data_size)
                {
                    m_show_list_params.m_device_id = 0;
                }
            }
        }
        m_send_signal_to_render = true;
        UpdateAllTableView();
    }

    void ShowListView::ShowUntested(bool tested)
    {
        ShowEnableDatas(tested, SHOW_TYPE::SHOW_UN_TEST);
    }

    void ShowListView::ShowUnedited(bool edited)
    {
        ShowEnableDatas(edited, SHOW_TYPE::SHOW_UN_EDITED);
    }

    void ShowListView::ShowNg(bool ng)
    {
        ShowEnableDatas(ng, SHOW_TYPE::SHOW_NG);
    }

    void ShowListView::ShowAll(bool all)
    {
        ShowEnableDatas(all, SHOW_TYPE::SHOW_ALL_DEVICE);
    }

    void ShowListView::ShowEnableDatas(bool state, int type)
    {
        for (auto& subboard_data : m_sub_board_datas) {
            for (auto& part_number_data : subboard_data->m_part_number_datas) {
                for (auto& device_data : part_number_data->m_device_datas) {
                    if (IsDeviceVisible(device_data, state, type)) {
                        device_data->m_show_enable = true;
                    }
                    else {
                        device_data->m_show_enable = false;
                    }
                }
            }
        }
        m_show_list_params.m_update_device_data = true;
        UpdateAllTableView();
    }

    bool ShowListView::IsDeviceVisible(DeviceDataStructPtr& device_data, bool state, int type)
    {
        switch (type) {
        case SHOW_TYPE::SHOW_UN_EDITED: // 未编辑
            return device_data->m_unedited == state;
        case SHOW_TYPE::SHOW_UN_TEST: // 未测试
            return device_data->m_body_tested == state;
        case SHOW_TYPE::SHOW_NG: // NG
            return device_data->m_result_state == RESULT_STATE::RESULT_NG;
        case SHOW_TYPE::SHOW_ALL_DEVICE: // 显示所有设备
            return true;
        default:
            return false;
        }
    }
    void ShowListView::ConnectSlots()
    {
        connect(ui->list_product, SIGNAL(CustomLeftClicked(const QModelIndex&)), this, SLOT(BoardLeftClicked(const QModelIndex&)));
        connect(ui->list_part_no, SIGNAL(CustomLeftClicked(const QModelIndex&)), this, SLOT(PartNumberLeftClicked(const QModelIndex&)));
        connect(ui->list_device, SIGNAL(CustomLeftClicked(const QModelIndex&)), this, SLOT(DeviceLeftClicked(const QModelIndex&)));
        //connect(ui->query_condition, SIGNAL(textChanged(QString)), this, SLOT(QueryConditionTextChanged(QString)));
        //connect(ui->query_pre, SIGNAL(clicked()), this, SLOT(QueryPreClicked()));
        //connect(ui->query_post, SIGNAL(clicked()), this, SLOT(QueryPostClicked()));
        //connect(ui->refresh, SIGNAL(clicked()), this, SLOT(QueryRefreshClicked()));
        //connect(ui->enable_all, SIGNAL(stateChanged(int)), this, SLOT(EnableAllStateChanged(int)));
        //connect(ui->enable_unedited, SIGNAL(stateChanged(int)), this, SLOT(EnableUneditedStateChanged(int)));
        //connect(ui->enable_not_tested, SIGNAL(stateChanged(int)), this, SLOT(EnableNoTestedStateChanged(int)));
        //connect(ui->enable_ng, SIGNAL(stateChanged(int)), this, SLOT(EnableNgStateChanged(int)));
        connect(ui->list_product->horizontalHeader(), SIGNAL(sectionResized(int, int, int)), this, SLOT(BoardTableColumnResize(int, int, int)));
        connect(ui->list_part_no->horizontalHeader(), SIGNAL(sectionResized(int, int, int)), this, SLOT(PartNumberTableColumnResize(int, int, int)));
        connect(ui->list_device->horizontalHeader(), SIGNAL(sectionResized(int, int, int)), this, SLOT(DeviceTableColumnResize(int, int, int)));
    }

    void ShowListView::BlockSignals(bool block)
    {
        ui->list_product->blockSignals(block);
        ui->list_part_no->blockSignals(block);
        ui->list_device->blockSignals(block);
        //ui->query_condition->blockSignals(block);
        //ui->query_pre->blockSignals(block);
        //ui->query_post->blockSignals(block);
        //ui->refresh->blockSignals(block);
       // ui->enable_all->blockSignals(block);
       // ui->enable_unedited->blockSignals(block);
       // ui->enable_not_tested->blockSignals(block);
        //ui->enable_ng->blockSignals(block);
        ui->list_product->horizontalHeader()->blockSignals(block);
        ui->list_part_no->horizontalHeader()->blockSignals(block);
        ui->list_device->horizontalHeader()->blockSignals(block);
    }

    void ShowListView::BoardLeftClicked(const QModelIndex& index)
    {
        m_show_list_params.m_update_subboard_data = true;
        m_show_list_params.m_update_part_number_data = true;
        m_show_list_params.m_update_device_data = true;
        m_show_list_params.m_subboard_id = index.row();
        m_send_signal_to_render = true;
        emit ChangeShowListViewParam(m_show_list_params);
        UpdateAllTableView();
    }

    void ShowListView::PartNumberLeftClicked(const QModelIndex& index)
    {
        m_show_list_params.m_update_device_data = true;
        m_show_list_params.m_part_number_id = index.row();
        m_send_signal_to_render = true;
        emit ChangeShowListViewParam(m_show_list_params);
        UpdateAllTableView();
    }

    void ShowListView::DeviceLeftClicked(const QModelIndex& index)
    {
        m_show_list_params.m_device_id = index.row();
        m_send_signal_to_render = true;
        emit ChangeShowListViewParam(m_show_list_params);
        UpdateAllTableView();
    }

    void ShowListView::QueryConditionTextChanged(QString text)
    {
        BlockSignals(true);
        jrsdata::QueryListViewParam query_list_param;
        query_list_param.m_subboard_id = ui->list_product->currentIndex().row();
        query_list_param.m_part_number_id = ui->list_part_no->currentIndex().row();
        query_list_param.m_device_id = ui->list_device->currentIndex().row();
        //query_list_param.m_query_condition = ui->query_condition->text().toStdString();
       // query_list_param.m_type = ui->query_type->currentIndex();
       /* if (ui->enable_all->isChecked())
        {
            query_list_param.m_show_type = SHOW_TYPE::SHOW_ALL_DEVICE;
        }
        else if (ui->enable_unedited->isChecked())
        {
            query_list_param.m_show_type = SHOW_TYPE::SHOW_UN_EDITED;
        }*/
       /* else if (ui->enable_ng->isChecked())
        {
            query_list_param.m_show_type = SHOW_TYPE::SHOW_NG;
        }
        else if (ui->enable_not_tested->isChecked())
        {
            query_list_param.m_show_type = SHOW_TYPE::SHOW_UN_TEST;
        }*/
        emit FindTrigger(query_list_param);
        BlockSignals(false);
    }

    void ShowListView::QueryPreClicked()
    {
       /* if (ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER || ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER_FOLLOW)
        {
            AddPartNumberID(-1);
        }
        else if (ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_DEVICE_NAME)
        {
            AddDeviceID(-1);
        }*/
    }

    void ShowListView::QueryPostClicked()
    {
        /*if (ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER || ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_PART_NUMBER_FOLLOW)
        {
            AddPartNumberID(1);
        }
        else if (ui->query_type->currentIndex() == SEARCH_SHOW_LIST_TYPE::SEARCH_DEVICE_NAME)
        {
            AddDeviceID(1);
        }*/
    }

    void ShowListView::QueryRefreshClicked()
    {
    }

    void ShowListView::EnableAllStateChanged(int state)
    {
        if (state == Qt::Checked)
        {
            /*ui->enable_unedited->setChecked(false);
            ui->enable_not_tested->setChecked(false);
            ui->enable_ng->setChecked(false);*/
            ShowAll(true);
        }
    }

    void ShowListView::EnableUneditedStateChanged(int state)
    {
        if (state == Qt::Checked)
        {
            //ui->enable_all->setChecked(false);
        }
        if (state == Qt::Checked)
        {
            //ShowUnedited(state == Qt::Checked);
        }
        else
        {
            ShowAll(true);
        }
    }

    void ShowListView::EnableNoTestedStateChanged(int state)
    {
        if (state == Qt::Checked)
        {
           // ui->enable_all->setChecked(false);
        }
        if (state == Qt::Checked)
        {
           // ShowUntested(state == Qt::Unchecked);
        }
        else
        {
            ShowAll(true);
        }
    }

    void ShowListView::EnableNgStateChanged(int state)
    {
        if (state == Qt::Checked)
        {
            //ui->enable_all->setChecked(false);
        }
        if (state == Qt::Checked)
        {
            //ShowNg(state == Qt::Checked);
        }
        else
        {
            ShowAll(true);
        }
    }

    // 更新所有表格视图的主函数，负责协调各个子模块的更新和选择状态的恢复。
    void ShowListView::UpdateAllTableView()
    {
        // 阻塞信号，避免更新过程中触发不必要的事件。
        BlockSignals(true);
        // 更新主板视图。
        UpdateBoardView();
        // 更新部件编号视图。
        UpdatePartNumberView();
        // 更新元件视图。
        UpdateDeviceView();
        // 恢复用户之前的选择状态。
        RestoreSelections();
        // 解除信号阻塞。
        BlockSignals(false);
    }

    // 更新主板视图，根据参数决定是否需要更新数据。
    void ShowListView::UpdateBoardView()
    {
        // 如果没有更新标志，则直接返回。
        if (!m_show_list_params.m_update_subboard_data)
            return;
        std::vector<ShowTableParamBasePtr> show_datas;
        show_board_model->GetDataModel(show_datas);
        std::vector<SubBoardDataStructPtr> vector_data;
        for (auto show : show_datas)
        {
            vector_data.push_back(std::dynamic_pointer_cast<SubBoardDataStruct>(show));
        }
        if (vector_data.size() > m_show_list_params.m_subboard_id && m_show_list_params.m_subboard_id > -1)
        {
            if (m_send_signal_to_render)
            {
                m_send_signal_to_render = false;
                ComponentListViewParamPtr param = std::make_shared<jrsdata::ComponentListViewParam>();
                param->event_name = jrsaoi::SHOWLIST_SELECT_SUBBOARD_UPDATE;
                param->module_name = jrsaoi::VIEW_MODULE_NAME;
                param->topic_name = jrsaoi::LISTVIEW_TRIGGER_TOPIC_NAME;
                param->sub_name = "all";
                param->subboard_name = vector_data.at(m_show_list_params.m_subboard_id)->m_subboard_name;
                emit ShowListRenderTrigger(param);
                // 发出子板变更信号。
            }
        }
        // 清除更新标志，避免重复更新。
        m_show_list_params.m_update_subboard_data = false;
        // 调用具体更新函数，刷新主板数据。
        BoardUpdateView(m_sub_board_datas);
    }

    // 更新部件编号视图，根据参数决定是否需要更新数据。
    void ShowListView::UpdatePartNumberView()
    {
        // 如果没有更新标志，则直接返回。
        if (!m_show_list_params.m_update_part_number_data)
            return;
        // 清除更新标志，避免重复更新。
        m_show_list_params.m_update_part_number_data = false;
        // 检查当前选中的主板 ID 是否有效。
        if (m_show_list_params.m_subboard_id >= 0 && m_show_list_params.m_subboard_id < m_sub_board_datas.size())
        {
            // 如果有效，更新部件编号数据。
            PartNumberUpdateView(m_sub_board_datas.at(m_show_list_params.m_subboard_id)->m_part_number_datas);
        }
        else
        {
            // 如果无效，清空部件编号视图。
            PartNumberUpdateView(std::vector<PartNumberDataStructPtr>());
        }
    }

    // 更新元件视图，根据参数决定是否需要更新数据。
    void ShowListView::UpdateDeviceView()
    {
        // 如果没有更新标志，则直接返回。
        if (!m_show_list_params.m_update_device_data)
            return;
        // 清除更新标志，避免重复更新。
        m_show_list_params.m_update_device_data = false;
        // 检查部件编号模型是否有效。
        if (show_part_number_model != nullptr)
        {
            // 获取部件编号数据模型。
            std::vector<ShowTableParamBasePtr> show_table_datas;
            show_part_number_model->GetDataModel(show_table_datas);
            // 将通用数据模型转换为具体部件编号数据。
            std::vector<PartNumberDataStructPtr> vec_data;
            for (const auto& show : show_table_datas)
            {
                auto part_number_data = std::dynamic_pointer_cast<PartNumberDataStruct>(show);
                if (part_number_data)
                    vec_data.push_back(part_number_data);
            }
            // 检查当前选中的部件编号 ID 是否有效。
            if (m_show_list_params.m_part_number_id >= 0 && m_show_list_params.m_part_number_id < vec_data.size())
            {
                // 如果有效，更新元件数据。
                DeviceUpdateView(vec_data.at(m_show_list_params.m_part_number_id)->m_device_datas);
            }
            else
            {
                // 如果无效，清空元件视图。
                DeviceUpdateView(std::vector<DeviceDataStructPtr>());
            }
        }
    }

    // 恢复用户之前的选择状态，包括主板、部件编号和元件的选中项。
    void ShowListView::RestoreSelections()
    {
        BlockSignals(true);
        // 恢复主板选择。
        BoardSelectIndex(m_show_list_params.m_subboard_id);
        // 恢复部件编号选择。
        PartNumberSelectIndex(m_show_list_params.m_part_number_id);
        // 恢复元件选择。
        DeviceSelectIndex(m_show_list_params.m_device_id);
        // 更新元件信息行。
        DeviceInfoUpdateRow(m_show_list_params.m_device_id);
        BlockSignals(false);
    }

    // 更新子模块数据。
    void ShowListView::UpdateSubboardDatas(jrsdata::GraphicsUpdateProjectEventParamPtr params)
    {
        if (!params)
        {
            return;
        }
        m_sub_board_datas = params->m_sub_board_datas;
        m_show_list_params = params->show_list_params;
        UpdateAllTableView();
    }

    // 清理数据。
    void ShowListView::CleanDatas()
    {
        m_sub_board_datas.clear();
        m_show_list_params = ShowListViewParam();
        UpdateAllTableView();
    }

    // 清理元件信息。
    void ShowListView::CleanDeviceInfo()
    {
    }

    void ShowListView::SaveLayoutJson(CustomTableView* table_view, TableViewModel* table_model, std::string file_name)
    {
        std::string path_file_name = file_path + file_name;
        if (table_view == nullptr || table_model == nullptr || path_file_name.empty() || file_path.empty())
        {
            return;
        }
        if (!EnsureDirectoryExists(file_path))
        {
            return;
        }
        // 保存布局
        std::vector<int> column_widths;
        GetColumnWidth(table_view, table_model, column_widths);
        nlohmann::json json_data;
        for (auto json_value : column_widths)
        {
            json_data.push_back(json_value);
        }
        std::string json_string = JsonToString(json_data);
        SaveStringToJson(json_string, path_file_name);
    }
    void ShowListView::LoadLayoutJson(CustomTableView* table_view, TableViewModel* table_model, std::string file_name)
    {
        if (!IsValidInput(table_view, table_model, file_name)) {
            return;
        }

        std::string path_file_name = file_path + file_name;
        auto json_data = ReadJson(path_file_name);
        if (!IsValidJsonData(json_data)) {
            return;
        }

        auto column_widths = ParseColumnWidthsFromJson(json_data);
        SetColumnWidth(table_view, table_model, column_widths);
    }
    bool ShowListView::IsValidInput(CustomTableView* table_view, TableViewModel* table_model, const std::string& file_name) const
    {
        return table_view != nullptr && table_model != nullptr && !file_name.empty() && !file_path.empty();
    }
    bool ShowListView::IsValidJsonData(const nlohmann::json& json_data) const
    {
        return !json_data.is_null() && !json_data.empty();
    }
    std::vector<int> ShowListView::ParseColumnWidthsFromJson(const nlohmann::json& json_data) const
    {
        std::vector<int> column_widths;
        for (const auto& width : json_data)
        {
            if (width.is_number_integer())
            {
                column_widths.push_back(width.get<int>());
            }
        }
        return column_widths;
    }
    void ShowListView::SetColumnWidth(CustomTableView* table_view, TableViewModel* table_model, std::vector<int> column_widths)
    {
        BlockSignals(true);
        if (table_view != nullptr && table_model != nullptr)
        {
            for (int i = 0; i < table_model->columnCount() && i < column_widths.size(); i++)
            {
                table_view->setColumnWidth(i, column_widths.at(i));
            }
        }
        BlockSignals(false);
    }

    void ShowListView::GetColumnWidth(CustomTableView* table_view, TableViewModel* table_model, std::vector<int>& column_widths)
    {
        column_widths.clear();
        if (table_view != nullptr && table_model != nullptr)
        {
            for (int i = 0; i < table_model->columnCount(); i++)
            {
                column_widths.push_back(table_view->columnWidth(i));
            }
        }
    }

    bool ShowListView::EnsureDirectoryExists(std::string& path)
    {
        if (jtools::FileOperation::IsValidPath(path))
        {
            if (!jtools::FileOperation::IsPathExist(path.c_str()))
            {
                return jtools::FileOperation::JRSCreateDirectory(path);
            }
            return true;
        }
        return false;
    }

    // 项目更新。
    int ShowListView::ProjectUpdate(const jrsdata::ViewParamBasePtr& param)
    {
        auto graphics_params = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param);
        if (!graphics_params)
        {
            return jrscore::AOI_OK;
        }
        m_sub_board_datas = graphics_params->m_sub_board_datas;
        m_show_list_params = graphics_params->show_list_params;
        UpdateAllTableView();
        return jrscore::AOI_OK;
    }

    // 图形选择。
    int ShowListView::GraphicsSelect(const jrsdata::ViewParamBasePtr& param)
    {
        auto render_view_param_ = std::dynamic_pointer_cast<jrsdata::GraphicsUpdateProjectEventParam>(param);
        if (!render_view_param_)
        {
            return jrscore::AOI_OK;
        }
        m_show_list_params = render_view_param_->show_list_params;
        UpdateAllTableView();
        return 0;
    }

    int ShowListView::GraphicsUpdate(const jrsdata::ViewParamBasePtr& param)
    {
        Q_UNUSED(param);  // 避免未使用变量的警告
        // TODO: 实现图形更新逻辑
        return 0;  // 返回成功
    }

    int ShowListView::GraphicsCreate(const jrsdata::ViewParamBasePtr& param)
    {
        Q_UNUSED(param);  // 避免未使用变量的警告
        // TODO: 实现图形创建逻辑
        return 0;  // 返回成功
    }

    int ShowListView::GraphicsDelete(const jrsdata::ViewParamBasePtr& param)
    {
        Q_UNUSED(param);  // 避免未使用变量的警告
        // TODO: 实现图形删除逻辑
        return 0;  // 返回成功
    }

    int ShowListView::CadSelect(const jrsdata::ViewParamBasePtr& param)
    {
        Q_UNUSED(param);  // 避免未使用变量的警告
        // TODO: 实现 CAD 选择逻辑
        return 0;  // 返回成功
    }

    void ShowListView::BoardTableColumnResize(int index, int old_size, int new_size)
    {
        (void)index;
        (void)old_size;
        (void)new_size;
        SaveLayoutJson(ui->list_product, show_board_model, board_file_name);
    }

    void ShowListView::PartNumberTableColumnResize(int index, int old_size, int new_size)
    {
        (void)index;
        (void)old_size;
        (void)new_size;
        SaveLayoutJson(ui->list_part_no, show_part_number_model, part_no_file_name);
    }

    void ShowListView::DeviceTableColumnResize(int index, int old_size, int new_size)
    {
        (void)index;
        (void)old_size;
        (void)new_size;
        SaveLayoutJson(ui->list_device, show_device_model, device_file_name);
    }

    void ShowListView::ResizeBoardColumn()
    {
        BlockSignals(true);
        if (show_board_model->columnCount() < 4)
        {
            BlockSignals(false);
            return;
        }
        ui->list_product->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);
        ui->list_product->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Interactive);
        ui->list_product->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Interactive);
        ui->list_product->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Stretch);
        ui->list_product->setColumnWidth(0, 35);
        ui->list_product->setColumnWidth(2, 60);
        BlockSignals(false);
    }

    void ShowListView::ResizePartNumberColumn()
    {
        BlockSignals(true);
        if (show_part_number_model->columnCount() < 4)
        {
            BlockSignals(false);
            return;
        }
        ui->list_part_no->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);
        ui->list_part_no->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Interactive);
        ui->list_part_no->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Interactive);
        ui->list_part_no->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Stretch);
        ui->list_part_no->setColumnWidth(0, 35);
        BlockSignals(false);
    }

    void ShowListView::ResizeDeviceColumn()
    {
        BlockSignals(true);
        if (show_device_model->columnCount() < 6)
        {
            BlockSignals(false);
            return;
        }
        ui->list_device->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);
        ui->list_device->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Interactive);
        ui->list_device->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Interactive);
        ui->list_device->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Interactive);
        ui->list_device->horizontalHeader()->setSectionResizeMode(4, QHeaderView::Interactive);
        ui->list_device->horizontalHeader()->setSectionResizeMode(5, QHeaderView::Stretch);
        ui->list_device->setColumnWidth(0, 35);
        ui->list_device->setColumnWidth(4, 60);
        BlockSignals(false);
    }

}