﻿#include "multipleboardsbase.h"

void jrsaoi::MultipleBoardsBase::SetAddGraphicsCallBack(AddGraphicsCallBack callback_)
{
    _add_graphics_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetProjectUpdateGraphicsCallBack(ProjectUpdateGraphicsCallBack callback_)
{
    _project_update_graphics_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetClearLayerGraphicsCallBack(ClearLayerCallBack callback_)
{
    _clear_layer_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetRenderCurrentLayerCallBack(SetCurrentLayerCallBack callback_)
{
    _set_current_layer_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetRenderVisionModeCallBack(SetVisionModeCallBack callback_)
{
    _set_vision_mode_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetGetGraphicsCallBack(GetGraphicsCallBack callback_)
{
    _get_graphics_callback = callback_;
}

void jrsaoi::MultipleBoardsBase::SetGetCurrentSelectParam(GetCurrentSelectParamCallBack callback_)
{
    _get_current_select_param = callback_;
}

jrsaoi::MultipleBoardsBase::MultipleBoardsBase() :_project_param_instance(jrsaoi::ParamOperator::GetInstance())
{
}
