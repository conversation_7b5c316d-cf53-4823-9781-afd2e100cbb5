<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ComponentLibView</class>
 <widget class="QWidget" name="ComponentLibView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>723</width>
    <height>388</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,2,0">
       <item>
        <widget class="QLabel" name="label">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>元件库:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="componentFolder">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="reload_btn">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>重载</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>宽度:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="component_width">
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>高度:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="component_height">
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>PAD数量:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="component_pad_count">
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>元件料号名称:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="component_part_num"/>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="query_components_btn">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>查询</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTableWidget" name="tableWidget"/>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QPushButton" name="apply_component_btn">
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>关联元件库</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="load_component_detect_btn">
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>加载检测框</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <widget class="QPushButton" name="load_components_btn">
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>加载所有元件</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="save_components_btn">
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(0, 170, 255);</string>
         </property>
         <property name="text">
          <string>保存所有元件</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QFrame" name="frame_show">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
