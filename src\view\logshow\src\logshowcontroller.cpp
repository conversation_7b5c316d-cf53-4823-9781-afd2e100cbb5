#include "logshowcontroller.h"
#include "logshowview.h"
#include "logshowmodel.h"

namespace jrsaoi
{
    LogShowController::LogShowController(const std::string& name) :ControllerBase(name)
    {
    }
    LogShowController::~LogShowController()
    {
    }
    int LogShowController::Update(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    int LogShowController::Save(const jrsdata::ViewParamBasePtr& param_)
    {
        (void)param_;
        return jrscore::AOI_OK;

    }
    void LogShowController::SetView(ViewBase* view_param)
    {
        log_show_view = static_cast<LogShowView*>(view_param);
    }
    void LogShowController::SetModel(ModelBasePtr model_param)
    {
        model = std::dynamic_pointer_cast<LogShowModel>(model_param);
    }
}
