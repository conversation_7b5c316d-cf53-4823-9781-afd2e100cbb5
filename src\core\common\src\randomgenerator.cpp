#include "randomgenerator.h"
#include <sstream>
#include <iomanip>
using namespace jtools;

std::mt19937 &RandomGenerator::GetRandomEngine()
{
    static std::mt19937 gen(static_cast<unsigned int>(std::time(nullptr)));
    return gen;
}

int RandomGenerator::GetRandomInt(int min, int max)
{
    std::uniform_int_distribution<> dis;
    dis.param(std::uniform_int_distribution<>::param_type(min, max));
    return dis(RandomGenerator::GetRandomEngine());
}

double RandomGenerator::GetRandomFloat(double min, double max)
{
    std::uniform_real_distribution<> dis;
    dis.param(std::uniform_real_distribution<>::param_type(min, max));
    return dis(RandomGenerator::GetRandomEngine());
}

bool RandomGenerator::GetRandomBool(float probability)
{
    if (probability <= 0)
        return false;
    if (probability >= 100)
        return true;

    double randomValue = GetRandomFloat(0, 1);
    return randomValue <= probability;
}

std::pair<float, float> jtools::RandomGenerator::GetRandomPointInRect(float left, float top, float right, float bottom)
{
    float x = (float)RandomGenerator::GetRandomFloat(left, right);
    float y = (float)RandomGenerator::GetRandomFloat(top, bottom);

    return std::make_pair(x, y);
}

std::string jtools::RandomGenerator::GetRandomColor()
{
    int value = RandomGenerator::GetRandomInt(0, 0xFFFFFF);

    std::stringstream ss;
    ss << '#' << std::hex << std::setw(6) << std::setfill('0') << value;

    return ss.str();
}
