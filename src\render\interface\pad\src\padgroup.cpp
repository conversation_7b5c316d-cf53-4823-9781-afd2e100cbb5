﻿#include "graphicsmanager.h"
#include "graphicsalgorithm.h"
#include "graphicsserialize.hpp"
#include "graphicseventparam.hpp"

/** < 控制点  */
#include "controlpointabstract.h"
#include "controlpointconstants.hpp"
#include "controlpointfactory.h"

#include "padgraphics.h"

#include "eventparam.hpp"
// #include "customcursortype.hpp"
#include "painter.h"
#include "renderer.h"
#include "log.h"
#include "tools.hpp"
#include "padmanager.h"
// 
#include "cvtools.h"

PadGraphicsGroup::PadGraphicsGroup(PadManager* pad_manager_ptr_, std::weak_ptr<GraphicsAbstract> parent_ /*= {}*/)
    : GraphicsAbstract(),
    _pad_manager_ptr(pad_manager_ptr_),
    _pad_group_id(-1),
    /* gm(gm_),*/
    _parent_graphics(parent_),

    _direction(PadDirection::UNKNOWN),
    _is_align(true)
{
    //this->SetParent(parent_);
}
PadGraphicsGroup& PadGraphicsGroup::operator=(const PadGraphicsGroup& other)
{
    if (this != &other)
    {
        GraphicsAbstract::operator=(other);
        this->_parent_graphics = other._parent_graphics;
        // this->_sub_graphics = other._sub_graphics;
        this->_direction = other.GetDirection();
        this->_pad_group_type = other.GetGroupType();
        this->_pad_group_name = other.GetPadGroupName();
        this->_pad_group_id = other.GetPadGroupID();
    }
    return *this;
}

void PadGraphicsGroup::Draw(Renderer* r, const LayerConfig* config)
{
    if (!r || !config)
        return;

    Painter p(r);
    Draw(r, &p, config);
}

void PadGraphicsGroup::Draw(Renderer* r, Painter* p, const LayerConfig* config)
{
    if (!r || !p || !config)
        return;

    auto const obj = GetTemp(this, true);
    if (!obj)
        return;

    obj->Update(); // 在绘制前再刷新,减少刷新次数


    /** <显示组屏蔽 */
    //if (obj->settings.GetIsSelected()) // 选中才显示
    //{
    //    Color c(config->_display_style.r, config->_display_style.g, config->_display_style.b, config->_display_style.a);

    //    auto& thinkness = config->_display_style.true_line_width;
    //    p->DrawLines(obj->_paths, c, true, thinkness);
    //}
    /* if (update_sub)
     {
         update_sub = false;

         std::vector<std::shared_ptr<GraphicsAbstract>> update_ghs;
         for (auto& sub : obj->_sub_graphics)
         {
             if (sub.expired()) continue;
             update_ghs.emplace_back(sub.lock());
         }
         gm->UpdateGraphics(update_ghs, "", true);

     }*/
}

void PadGraphicsGroup::Update()
{
    UpdateState();
    if (IsNeedUpdate())
    {
        UpdateByTemp(this);
        UpdateBoundingBox();
        UpdateControlPoint();
        UpdateDrawBuffer();
        SetUpdated();
    }
}

void PadGraphicsGroup::UpdateDrawBuffer()
{
    cv::RotatedRect boundingbox = this->GetBoundingbox();
    /*四顶点*/
    cv::Point2f ovp[4];
    boundingbox.points(ovp);

    std::vector<Vec2> tvPaths{ {ovp[0].x, ovp[0].y}, {ovp[1].x, ovp[1].y}, {ovp[2].x, ovp[2].y}, {ovp[3].x, ovp[3].y} };
    tvPaths.swap(this->_paths);
    this->SetUpdated();
}

void PadGraphicsGroup::UpdateControlPoint()
{
    //auto const obj = GetTemp(this, false);

    //std::vector<std::shared_ptr<ControlPointAbstract>> cps;
    ///*创建中心拖拽控制点*/
    //{
    //    auto cp = ControlPointFactory::CreateControlPoint(ControlPointType::MOVE_POINT, obj);
    //    cps.emplace_back(cp);
    //}
    //cps.swap(control_points);

    // for (auto& gh : obj->_sub_graphics)
    // {
    //     gh->UpdateControlPoint();
    // }
}

int PadGraphicsGroup::SetArrayPadsNumber(int number_)
{
    // 缓存 GraphicsManager
    auto graphics_manager = _pad_manager_ptr->GetGraphicsManager();
    if (!_pad_manager_ptr || !graphics_manager || _sub_graphics.empty())
    {
        return -1; // 删除失败
    }

    if (number_ == 0)
    {
        // 删除所有图形
        std::vector<GraphicsID> delete_graphics_ids;
        for (const auto& gh : _sub_graphics)
        {
            if (auto locked_gh = gh.lock())
            {
                delete_graphics_ids.push_back(locked_gh->GetId());
            }
        }
        _sub_graphics.clear();
        graphics_manager->DeleteGraphics(delete_graphics_ids);
        SetRequireUpdate();
    }
    else if (number_ != _sub_graphics.size())
    {
        // 删除或创建图形
        std::vector<GraphicsID> delete_gh_ids;
        std::vector<GraphicsPtr> create_ghs;

        if (number_ > _sub_graphics.size())
        {
            // 需要创建新的图形
            for (size_t i = _sub_graphics.size(); i < number_; ++i)
            {
                if (auto locked_gh = _sub_graphics[0].lock())
                {
                    auto gh = locked_gh->Clone();
                    gh->CreateId(); // 创建ID
                    create_ghs.push_back(gh);
                }
            }
            AddSubGraphics(create_ghs);

            /** 剩余数据更新图形  */
            UpdateProjectExceptGraphicsVec(create_ghs);

            graphics_manager->UpdateGraphics(create_ghs);
            //UpdateProject();
        }
        else
        {
            // 需要删除图形
            size_t i = number_;
            while (i < _sub_graphics.size())
            {
                size_t middle_index = _sub_graphics.size() / 2;
                if (auto delete_gh = _sub_graphics[middle_index].lock())
                {
                    delete_gh_ids.push_back(delete_gh->GetId());
                }
                _sub_graphics.erase(_sub_graphics.begin() + middle_index);
            }
            /** 剩余数据更新到工程  */
            UpdateProjectExceptGraphicsVec();
            graphics_manager->DeleteGraphics(delete_gh_ids);
            //UpdateProject();
        }

        SetRequireUpdate();
    }
    return 0;
}

std::shared_ptr<GraphicsAbstract> PadGraphicsGroup::Clone()
{
    auto clone_gh = std::make_shared<PadGraphicsGroup>(*this);
    clone_gh->CreateId();
    clone_gh->UpdatePadGroupName(_pad_group_name);
    _pad_manager_ptr->GetGraphicsManager()->UpdateGraphics(clone_gh, false);
    return clone_gh;
}

void PadGraphicsGroup::DrawControlPoint(Renderer* r, Painter* p, const LayerConfig* config)
{
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        cp->Draw(r, p, config);
    }

    // for (auto& gh : this->_sub_graphics)
    // {
    //     gh->DrawControlPoint(r, p, config);
    // }
}

int PadGraphicsGroup::TryResponseControlPoint(std::shared_ptr<ControlPointAbstract>& controlpoint, const TryResponseEventParam& param)
{
    if (!settings.GetIsSelected())
        return 2;

    double mindis = param.max_limit;
    std::shared_ptr<ControlPointAbstract> response_cp = nullptr;
    for (auto& cp : control_points)
    {
        if (!cp) continue;
        auto dis = cp->TryResponse(param.x, param.y, (float)param.max_limit);
        if (dis > 0 && dis < mindis)
        {
            // printInfo(std::stringstream() << "响应距离:" << dis);
            mindis = dis;
            response_cp = cp;
        }
    }

    if (!response_cp)
        return 1;
    controlpoint = response_cp;
    // attr = response_cp->attributes;
    return 0;
}

int PadGraphicsGroup::ResponseControlPoint(const ResponseEventParam& param)
{
    auto obj = GetAndUpdateTemp(this, false);
    if (!obj)
        return GraphicsAbstract::GRAPHICS_NULL;
    // for (auto& cp : control_points)
    // {
    //     if (!cp) continue;
    //     if (cp->attributes == param.attr)
    //     {
    //         cp->Response(param, obj);
    //         break;
    //     }
    // }
    // 
    (void)param;
    //更新所有图形
    //for (auto& weak_gh : obj->_sub_graphics)
    //{
    //    auto gh = weak_gh.lock();
    //    gh->ResponseControlPoint(param);
    //}
    return 1;
}

int PadGraphicsGroup::ResponseEvent(const MouseEventValue& value)
{
    switch (value.state)
    {
    case MouseEventValue::MouseState::wheel:
    {
        if (value.type > 0)
        {
            CreateSubGraphics();
        }
        else if (value.type < 0)
        {
            DoAlign();
            DeleteSubGraphics();
        }
    }
    break;
    }
    this->_sub_graphics.clear();
    //DoAlign(true);
    return 0;
}

//void PadGraphicsGroup::SetParentGraphics(std::shared_ptr<GraphicsAbstract> parent_)
//{
//    this->_parent_graphics = parent_;
//}

void PadGraphicsGroup::AddSubGraphics(std::shared_ptr<GraphicsAbstract> sub_)
{
    //PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
    auto it = std::find_if(_sub_graphics.begin(), _sub_graphics.end(), [&sub_](auto& val) { return val.lock() == sub_; });
    if (it == _sub_graphics.end())
    {
        _sub_graphics.emplace_back(sub_);
    }
    SetRequireUpdate();
    PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
}

void PadGraphicsGroup::AddSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_)
{
    //printInfo(std::stringstream() << "");
    //auto shared_from_this_ = shared_from_this();
    for (auto& gh : sub_)
    {
        // gh->SetParent(shared_from_this_);
        AddSubGraphics(gh);
    }

    SetRequireUpdate();
    // UpdateBoundingBox();
}

void PadGraphicsGroup::RemoveSubGraphics(std::shared_ptr<GraphicsAbstract> sub_)
{
    // RemoveChild(sub_);
    auto it = std::find_if(_sub_graphics.begin(), _sub_graphics.end(), [&sub_](auto& val) { return val.lock() == sub_; });
    if (it != _sub_graphics.end())
    {
        _sub_graphics.erase(it);
        // UpdateBoundingBox();
    }
    //_is_align = false;
    SetRequireUpdate();
}

void PadGraphicsGroup::RemoveSubGraphics(const std::vector<std::shared_ptr<GraphicsAbstract>>& sub_)
{
    auto& vec = _sub_graphics;
    vec.erase(std::remove_if(vec.begin(), vec.end(),
        [&sub_](const std::weak_ptr<GraphicsAbstract>& element)
        {
            return std::find(sub_.begin(), sub_.end(), element.lock()) != sub_.end();
        }), vec.end());

    SetRequireUpdate();
}

void PadGraphicsGroup::CreateSubGraphics()
{
    if (_sub_graphics.size() < 1 || !_pad_manager_ptr || !_pad_manager_ptr->GetGraphicsManager())
        return;
    auto gh = _sub_graphics[0].lock()->Clone();
    gh->CreateId(); //创建了ID 并没有赋值
    _pad_manager_ptr->GetGraphicsManager()->UpdateGraphics(gh);  //直接更新到外界 外界再给ID 赋值
    AddSubGraphics(gh);
}

void PadGraphicsGroup::DeleteSubGraphics()
{
    if (_sub_graphics.size() < 1 || !_pad_manager_ptr || !_pad_manager_ptr->GetGraphicsManager())
        return;
    size_t middle_index = _sub_graphics.size() / 2;
    auto delete_gh = _sub_graphics[middle_index].lock();
    //printInfo(std::stringstream() << "delete_gh_id:" << delete_gh->GetId().GetString() << "   " << *delete_gh);
    //gm->DeleteGraphics(gh->GetId());
    _sub_graphics.erase(_sub_graphics.begin() + middle_index);
    for (auto& weak_gh : _sub_graphics)
    {
        auto gh = weak_gh.lock();
        //printInfo(std::stringstream() << "gh_id:" << gh->GetId().GetString() << "   " << *gh);
    }
    _pad_manager_ptr->GetGraphicsManager()->DeleteGraphics(delete_gh->GetId());
    SetRequireUpdate();
}



//TODO: 矫正函数需要移动到外面   12/25 HJC
void PadGraphicsGroup::DoAlign(bool is_append_gh_name_)
{

    if (_parent_graphics.expired())
        return;
    //PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
    render::Tools::RemoveExpiredWeakPtrs(this->_sub_graphics);
    switch (_pad_group_type)
    {
    case PadGraphicsGroup::PadGroupType::UNKNOWN:
        break;
    case PadGraphicsGroup::PadGroupType::SINGLE:
        AutoIdentifyDirection();
        break;
    case PadGraphicsGroup::PadGroupType::MIRROR:
        DoAlignMirrorSymmetry(is_append_gh_name_);
        break;
    case PadGraphicsGroup::PadGroupType::ARRAY:
        DoAlignArray();
        break;
    case PadGraphicsGroup::PadGroupType::MATRIX:
        DoAlignMatrix();
        break;
    default:
        break;
    }
    _is_align = true;
    //PrintCurrentPadGroupInfo(__FUNCTION__, __LINE__);
}

std::string PadGraphicsGroup::GetPadGroupUniqueName()
{
    return _pad_group_name + "&" + std::to_string(_pad_group_id);
}

std::vector<std::shared_ptr<GraphicsAbstract>> PadGraphicsGroup::GetExceptSelectedPads()
{
    PrintCurrentPadGroupInfo(__FUNCDNAME__, __LINE__);
    std::vector<std::shared_ptr<GraphicsAbstract>> temp_ghs;
    DoAlign();
    for (auto& sub_gh_weak : _sub_graphics)
    {
        auto sub_gh = sub_gh_weak.lock();
        if (!sub_gh->settings.GetIsSelected())
        {
            temp_ghs.emplace_back(sub_gh);
        }
    }
    PrintCurrentPadGroupInfo(__FUNCDNAME__, __LINE__);
    return temp_ghs;
}

std::vector<std::shared_ptr<GraphicsAbstract>> PadGraphicsGroup::GetExceptSelectedPadGroups()
{
    std::vector<std::shared_ptr<GraphicsAbstract>> temp_ghs;
    DoAlign();
    auto current_group = FindMatchingPadGroup();
    if (!current_group)
    {
        return temp_ghs;
    }
    for (auto& sub_gh : current_group->_sub_graphics)
    {
        temp_ghs.emplace_back(sub_gh);
    }
    //auto current_name_groups = _pad_manager_ptr->GetPadGroups(_pad_group_name);
    //for (auto& group_weak : current_name_groups)
    //{
    //    auto group_temp_ptr = std::static_pointer_cast<PadGraphicsGroup>(group_weak.lock());
    //    if (group_temp_ptr && group_temp_ptr.get() != this)
    //    {
    //        for (auto& pad : group_temp_ptr->_sub_graphics)
    //        {
    //            temp_ghs.emplace_back(pad);
    //        }
    //    }
    //}
    return temp_ghs;
}

bool PadGraphicsGroup::RemoveSelectSubGraphics()
{
    std::vector<std::shared_ptr<GraphicsAbstract>> delete_ghs;
    DoAlign();
    for (auto& sub_gh_weak : _sub_graphics)
    {
        auto sub_gh = sub_gh_weak.lock();
        if (sub_gh->settings.GetIsSelected())
        {
            delete_ghs.emplace_back(sub_gh);
        }
    }
    this->RemoveSubGraphics(delete_ghs);
    return true;
}

bool PadGraphicsGroup::IsSelectedLastPad()
{
    if (_sub_graphics.empty() && _sub_graphics.back().expired())
    {
        return false;
    }
    return _sub_graphics.back().lock()->settings.GetIsSelected();
}

void PadGraphicsGroup::UpdatePadGroupName(const std::string& group_name_)
{
    if (_pad_manager_ptr)
    {
        std::shared_ptr<PadGraphicsGroup> self = std::dynamic_pointer_cast<PadGraphicsGroup>(shared_from_this());
        if (self)
        {
            _pad_manager_ptr->UpdatePadGroupName(group_name_, self);
        }
        else
        {
            printInfo(std::stringstream() << " 更新pad组名失败，请检查!");
        }
    }
    else
    {
        printInfo(std::stringstream() << " 更新pad组名失败，请检查!");
    }
}

void PadGraphicsGroup::UpdateBoundingBox()
{
    if (!_is_align)
    {
        DoAlign();
    }

    float min_x = std::numeric_limits<float>::max();
    float min_y = std::numeric_limits<float>::max();
    float max_x = std::numeric_limits<float>::lowest();
    float max_y = std::numeric_limits<float>::lowest();

    // if (!this->IsHaveChild())
    if (this->_sub_graphics.empty())
    {
        this->SetWH(0, 0); // 空集合时，默认设置外接矩形
        return;
    }

    for (auto& weak_gh : this->_sub_graphics)
    {
        auto gh = weak_gh.lock();
        auto box = gh->GetBoundingbox().boundingRect2f();
        min_x = std::min(min_x, box.x);
        min_y = std::min(min_y, box.y);
        max_x = std::max(max_x, box.x + box.width);
        max_y = std::max(max_y, box.y + box.height);
    }
    float w = max_x - min_x + 50;
    float h = max_y - min_y + 50;
    this->SetXY((min_x + max_x) * 0.5f, (min_y + max_y) * 0.5f);
    this->SetWH(w, h);
}

void PadGraphicsGroup::UpdateState()
{
    render::Tools::RemoveExpiredWeakPtrs(this->_sub_graphics);

    bool select_sub = false;

    for (auto& gh : this->_sub_graphics)
    {

        if (!gh.expired() && gh.lock()->settings.GetIsSelected())
        {
            //printInfo(std::stringstream() << "选中的gh:" << *gh.lock());
            select_sub = true;
            break;
        }
    }
    this->settings.SetIsSelected(select_sub);

    for (auto& weak_gh : this->_sub_graphics)
    {
        auto gh = weak_gh.lock();
        if (gh->IsNeedUpdate())
        {
            _is_align = false;
            this->SetRequireUpdate();
            break;
        }
    }
}

void PadGraphicsGroup::PrintCurrentPadGroupInfo(const std::string& function_name_, int line_)
{
    (void)function_name_;
    (void)line_;
    //render::Tools::RemoveExpiredWeakPtrs(this->_sub_graphics);
    //printInfo(std::stringstream() << "[" << function_name_ << "][" << line_ << "] address:" << this << " subboard_size:" << _sub_graphics.size());
    //for (auto sub_gh : _sub_graphics)
    //{
    //    auto pd_gh = std::dynamic_pointer_cast<PadGraphics>(sub_gh.lock());
    //    if (!pd_gh)
    //        continue;
    //    //printInfo(std::stringstream()
    //    //    << " graphics_id:" << pd_gh->GetId().GetString()
    //    //    << " pad_group:" << pd_gh->GetPadGroupPtr().lock().get()
    //    //    << " pad_id:" << pd_gh->GetPadID() << "\n"
    //    //    //<< " parent position:[" << pd_gh->GetParent()->x() << "," << pd_gh->GetParent()->y() << "]"
    //    //    << " pad_center_position:[" << pd_gh->x() << "," << pd_gh->y() << "]"
    //    //    << " pad_location_center_position:[" << pd_gh->LocalX() << "," << pd_gh->LocalX() << "]");
    //    //<< " pad_parent_distance:[" << pd_gh->x() - pd_gh->GetParent()->x() << "," << pd_gh->y() - pd_gh->GetParent()->y() << "]");

    //}
}
std::shared_ptr<PadGraphicsGroup> PadGraphicsGroup::FindMatchingPadGroup(bool is_append_gh_name_)
{
    if (_parent_graphics.expired())
    {
        /**<父类指针为空*/
        return nullptr;
    }

    auto pad_groups = _pad_manager_ptr->GetPadGroups(_parent_graphics.lock()->GetId(), _pad_group_name);
    std::shared_ptr<PadGraphicsGroup> gh_group_temp = nullptr;
    std::string subboard_name;
    std::string component_name;

    // 获取当前 PadGraphicsGroup 的第一个子元素
    if (is_append_gh_name_)
    {
        auto src_gh = (!_sub_graphics.empty()) ? _sub_graphics.front().lock() : nullptr;
        if (!src_gh) return nullptr;

        auto gh_names = render::Tools::SplitString(src_gh->GetId().GetString(), ';');
        if (gh_names.size() < 3)
        {
            return nullptr;
        }

        subboard_name = gh_names[0];
        component_name = gh_names[1];
    }

    for (const auto& pad_group_gh : pad_groups)
    {
        auto temp_pad_group = std::static_pointer_cast<PadGraphicsGroup>(pad_group_gh.lock());
        if (!temp_pad_group || temp_pad_group.get() == this || temp_pad_group->_sub_graphics.empty())
            continue;

        auto first_graphic = temp_pad_group->_sub_graphics.front().lock();
        if (!first_graphic) continue;
        /** 使用子板名称与元件名称去检索另一个Pad信息 */
        if ((is_append_gh_name_ && first_graphic->GetId().GetString().find(subboard_name + ";" + component_name) != std::string::npos) ||
            !is_append_gh_name_)
        {
            gh_group_temp = temp_pad_group;
            break;
        }
    }

    return gh_group_temp;
}

PadGraphicsGroup::PadDirection PadGraphicsGroup::RotateClockwise(PadDirection direction_, float angle_)
{
    if (direction_ == PadDirection::INSIDE || direction_ == PadDirection::UNKNOWN)
    {
        return direction_;
    }
    int dir_int = static_cast<int>(direction_);
    dir_int = int(((dir_int - 1) + render::Tools::NormalizeAngle(angle_) / 90)) % 4;
    return static_cast<PadDirection>(dir_int + 1);
}

void PadGraphicsGroup::UpdateProjectExceptGraphicsVec(const GraphicsPtrVec& except_ghs_)
{
    this->DoAlign();
    GraphicsPtrVec graphics_ptrs;

    // 使用 unordered_set 提高查找效率
    std::unordered_set<GraphicsPtr> except_set(except_ghs_.begin(), except_ghs_.end());

    for (auto& weak_gh : _sub_graphics)
    {
        auto gh = weak_gh.lock();
        if (gh && except_set.find(gh) == except_set.end()) // 仅添加不在 except_ghs_ 内的元素
        {
            graphics_ptrs.push_back(gh);
        }
    }

    _pad_manager_ptr->GetGraphicsManager()->UpdateGraphics(graphics_ptrs, false);

}
void PadGraphicsGroup::DoAlignArray()
{

    if (_direction == PadDirection::UNKNOWN)
    {
        AutoIdentifyDirection();
    }

    if (_sub_graphics.size() < 2)
    {
        return;
    }

    //auto  current_direction = RotateClockwise(_direction, _parent_graphics.lock()->a());

    switch (_direction)
    {
    case PadDirection::UP:
    case PadDirection::DOWN:
    {
        std::sort(_sub_graphics.begin(), _sub_graphics.end(),
            [](const std::weak_ptr<GraphicsAbstract>& lhs, const std::weak_ptr<GraphicsAbstract>& rhs)
            {
                return lhs.lock()->LocalX() < rhs.lock()->LocalX();
            });

        auto front_graphics = _sub_graphics.front().lock();
        auto back_graphics = _sub_graphics.back().lock();
        auto w = front_graphics->w();
        auto h = front_graphics->h();
        auto first_x = front_graphics->LocalX();;
        auto end_x = back_graphics->LocalX();
        auto first_y = front_graphics->LocalY();
        //auto end_y = back_graphics->LocalY();
        auto sub_gh_num = _sub_graphics.size();
        auto gap = (end_x - first_x) / (sub_gh_num - 1);
        first_x += gap;
        for (int i = 1; i < sub_gh_num; ++i) {
            auto pad = _sub_graphics[i].lock();
            if (i == sub_gh_num - 1)
            {
                pad->SetLocalXY(end_x, first_y);//SetX(end_x);
            }
            else
            {
                pad->SetLocalXY(first_x, first_y);
            }
            first_x += gap;
            pad->SetWH(w, h);

            auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(pad);
            if (pad_gh)
            {
                pad_gh->SetPadID(i + 1);
            }
        }
        auto temp_pad_gh = std::dynamic_pointer_cast<PadGraphics>(front_graphics);
        if (temp_pad_gh)
        {
            temp_pad_gh->SetPadID(1);
        }
    }
    break;
    case PadDirection::RIGHT:
    case PadDirection::LEFT:
    {
        std::sort(_sub_graphics.begin(), _sub_graphics.end(),
            [](const std::weak_ptr<GraphicsAbstract>& lhs, const std::weak_ptr<GraphicsAbstract>& rhs)
            {
                return lhs.lock()->LocalY() < rhs.lock()->LocalY();
            });
        auto front_graphics = _sub_graphics.front().lock();
        auto back_graphics = _sub_graphics.back().lock();
        auto w = front_graphics->w();
        auto h = front_graphics->h();
        auto first_x = front_graphics->LocalX();;
        auto end_y = back_graphics->LocalY();
        auto first_y = front_graphics->LocalY();
        auto sub_gh_num = _sub_graphics.size();

        auto gap = (end_y - first_y) / (sub_gh_num - 1);
        first_y += gap;
        for (int i = 1; i < sub_gh_num; ++i) {
            auto pad = _sub_graphics[i].lock();
            if (i == sub_gh_num - 1)
            {
                pad->SetLocalXY(first_x, end_y);
            }
            else
            {
                pad->SetLocalXY(first_x, first_y);
            }
            first_y += gap;
            pad->SetWH(w, h);

            auto pad_gh = std::dynamic_pointer_cast<PadGraphics>(pad);
            if (pad_gh)
            {
                pad_gh->SetPadID(i + 1);
            }
        }
        auto temp_pad_gh = std::dynamic_pointer_cast<PadGraphics>(front_graphics);
        if (temp_pad_gh)
        {
            temp_pad_gh->SetPadID(1);
        }
    }
    break;
    default:
        return;
    }

}
/** < 镜像对称 */
void PadGraphicsGroup::DoAlignMirrorSymmetry(bool is_append_gh_name_)
{

    AutoIdentifyDirection();
    std::shared_ptr<PadGraphicsGroup> gh_group_temp = FindMatchingPadGroup(is_append_gh_name_);
    // 获取当前图形
    auto src_gh = (!_sub_graphics.empty()) ? _sub_graphics.front().lock() : nullptr;
    if (!src_gh)
        return;
    // 进行镜像对齐
    if (gh_group_temp && !gh_group_temp->_sub_graphics.empty())
    {
        auto obj_gh = gh_group_temp->_sub_graphics.front().lock();
        if (!obj_gh) return;

        obj_gh->SetLocalXY(-src_gh->LocalX(), -src_gh->LocalY());
        obj_gh->SetWH(src_gh->w(), src_gh->h());

        int dir_int = static_cast<int>(_direction);
        dir_int = ((dir_int - 1) + 2) % 4;
        gh_group_temp->SetDirection(static_cast<PadDirection>(dir_int + 1));
    }
}
/** < 矩阵排列 */
void PadGraphicsGroup::DoAlignMatrix()
{

}

void PadGraphicsGroup::AutoIdentifyDirection()
{
    // 2025.01.08 wangzhengkai 将方向计算修改用相对坐标进行计算
    float xmin = FLT_MAX, xmax = -FLT_MAX, ymin = FLT_MAX, ymax = -FLT_MAX;
    float width_temp = 0.f; float height_temp = 0.f;
    for (auto& weak_gh : _sub_graphics)
    {
        auto gh = weak_gh.lock();
        xmin = std::min(xmin, gh->LocalX());
        xmax = std::max(xmax, gh->LocalX());
        ymin = std::min(ymin, gh->LocalY());
        ymax = std::max(ymax, gh->LocalY());
        //width_temp = std::max(width_temp, gh->w());
        //height_temp = std::max(height_temp, gh->h());
    }
    if (!this->_parent_graphics.expired())
    {
        width_temp = this->_parent_graphics.lock()->w();
        height_temp = this->_parent_graphics.lock()->h();
    }
    float center_x = (xmax + xmin) / 2;
    float center_y = (ymax + ymin) / 2;
    std::pair<float, float> parent_vector = { 0.0f, 0.0f };
    std::pair<float, float> direction_vector = { center_x, -center_y };

    auto direction_angle = GetVectorA2BAngle(parent_vector.first, parent_vector.second, direction_vector.first, direction_vector.second);
    //printInfo(std::stringstream() << "parent_vector:[" << parent_vector.first << "," << parent_vector.second << "];\n"
    //    "direction_vector:[" << direction_vector.first << "," << direction_vector.second << "];\n"
    //    "size:[" << width_temp << "," << height_temp << "];\n"
    //    << "direction_angle" << direction_angle);

    _direction = GetPadDirection(direction_angle, width_temp, height_temp);
}

void PadGraphicsGroup::ParentAngleToZeroAngle()
{
    if (_parent_graphics.expired())
    {
        return;
    }
    auto parent_ptr = _parent_graphics.lock();
    if (!parent_ptr)
    {
        return;
    }


    //jcvtools::JrsHomMat2D jrs_hom_mat_2d;
    //auto angle = parent_ptr->a();
    //jrs_hom_mat_2d.AddHomMat2dRotate(-angle, parent_ptr->x(), parent_ptr->y());//旋转到复角度 还原
    //auto rotated_detect_center = jrs_hom_mat_2d.AffineTransRect(detect_global_center);
    //auto rotated_unit_center = jrs_hom_mat_2d.AffineTransPoint(unit_global_center_point);

}

void PadGraphicsGroup::ZeroAngleToParentAngle()
{

}

PadGraphicsGroup::PadDirection PadGraphicsGroup::GetPadDirection(double angle_, double width_, double height_)
{
    auto alpha = GetVectorAngle(width_, height_);
    //printInfo(std::stringstream() << "alpha:" << alpha);
    if (angle_ >= alpha && angle_ < 180 - alpha)
    {
        return PadGraphicsGroup::PadDirection::UP; // 上    
    }
    else if (angle_ >= -alpha && angle_ < alpha)
    {
        return PadGraphicsGroup::PadDirection::RIGHT; // 右      
    }
    else if (angle_ >= -180 + alpha && angle_ < -alpha)
    {
        return PadGraphicsGroup::PadDirection::DOWN; // 下       [-135°, -45°)
    }
    else
    {
        return PadGraphicsGroup::PadDirection::LEFT; // 左       [135°, 180°] 和 [-180°, -135°)
    }

}
