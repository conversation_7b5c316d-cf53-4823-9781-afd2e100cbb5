﻿//QT
#include <QGraphicsPixmapItem>
#include <QGraphicsScene>
#include <QImage>
#include <QPixmap>
//custom
#include "expansionmultipanel.h"
#include "ui_expansionmultipanel.h"
#include "controlconstants.hpp"
#include "viewparam.hpp"
#include "subboardsortview.h"
#include "qtools.hpp"

ExpansionMultiPanel::ExpansionMultiPanel(QWidget* parent)
    : QWidget(parent), ui(new Ui::ExpansionMultiPanel)
    , _subboard_sort_view(new SubboardSortView())
{
    ui->setupUi(this);
    InitMember();
    InitView();
    InitConnect();
}

ExpansionMultiPanel::~ExpansionMultiPanel()
{
    if (_subboard_sort_view)
    {
        delete _subboard_sort_view;
        _subboard_sort_view = nullptr;
    }
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }

}

int ExpansionMultiPanel::UpdateView(jrsdata::MultiBoardEventParamPtr multiple_baords_param_)
{
    if (multiple_baords_param_->regular_multiple_board_param)
    {
        if (!multiple_baords_param_->regular_multiple_board_param->component_and_images.first.component_name.empty())
        {
            //更新选择元件的信息
            ui->label_rule_component_name->setText(QString::fromStdString(multiple_baords_param_->regular_multiple_board_param->component_and_images.first.component_name));
        }
        /** <更新图像信息 */
        if (!multiple_baords_param_->regular_multiple_board_param->component_and_images.second.empty())
        {
            auto image_map = multiple_baords_param_->regular_multiple_board_param->component_and_images.second;
            QImage image = MatToQImage(image_map[jrsdata::LightImageType::RGB]);
            if (image.isNull()) {
                return -1;
            }
            QGraphicsPixmapItem* pixmap_item = new QGraphicsPixmapItem(QPixmap::fromImage(image));
            _graphic_scene->addItem(pixmap_item);

            QSize view_size = ui->component_graphics_view->size();
            QSize image_size = image.size();
            qreal scale_x = (qreal)view_size.width() / image_size.width();
            qreal scale_y = (qreal)view_size.height() / image_size.height();
            qreal scale = qMin(scale_x, scale_y);
            pixmap_item->setScale(scale);
        }
    }
    return jrscore::AOI_OK;
}
void ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow()
{
    auto sender_btn = qobject_cast<QPushButton*>(sender()); // 获取发送信号的按钮
    _multiple_board_param->create_type = jrsdata::MultiBoardEventParam::CreateType::REGULAR;
    _multiple_board_param->event = jrsdata::MultiBoardEventParam::Event::MULTIPLE_BOARD;
    if (!_multiple_board_param->regular_multiple_board_param.has_value())
    {
        jrsdata::MultiBoardEventParam::RegularParam regular_param;
        _multiple_board_param->regular_multiple_board_param = regular_param;
    }
    if (!sender_btn)
    {
        JRSMessageBox_INFO("提示", "多联板按钮事件匹配失败，请检查！", jrscore::MessageButton::Ok);
        return;
    }
    QString btn_text = sender_btn->text();

    if (btn_text == "确认元件")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_COMPONENT;
    }
    else if (btn_text == "截图模板")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::MARK_COMPONENT_AS_TEMPLATE;
    }
    else if (btn_text == "X框选识别")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_COL_FLAG;
    }
    else if (btn_text == "Y框选识别")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::SELECT_ROW_FLAG;
    }
    else if (btn_text == "预览")
    {
        _multiple_board_param->regular_multiple_board_param->cols = ui->spin_x->value();
        _multiple_board_param->regular_multiple_board_param->rows = ui->spin_y->value();
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::IDENTIFY_REMOTE_SUBBOARD;

    }
    else if (btn_text == "确认生成")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::GENERATE_MULTIPLE_BOARD;
    }
    else if (btn_text == "取消生成")
    {
        _multiple_board_param->regular_multiple_board_param->step = jrsdata::MultiBoardEventParam::RegularParam::Step::CANCEL_GENERATE_MULTIPLE_BOARD;
    }
    emit SigUpdateMultipleBoards(_multiple_board_param);
}

void ExpansionMultiPanel::SlotRuleMultileSub()
{
    _subboard_sort_view->close();
    ui->stackedWidget->setVisible(true);
    ui->stackedWidget->setCurrentIndex(0);
}

void ExpansionMultiPanel::SlotSubstandardMultileSub()
{
    _subboard_sort_view->close();
    ui->stackedWidget->setVisible(true);
    ui->stackedWidget->setCurrentIndex(1);
}

void ExpansionMultiPanel::SlotIrregularMultipleBoardsCreateFlow()
{
    auto sender_btn = qobject_cast<QPushButton*>(sender()); // 获取发送信号的按钮
    _multiple_board_param->create_type = jrsdata::MultiBoardEventParam::CreateType::IREGULAR;/**<生成不规则多连板*/
    if (!sender_btn)
    {
        JRSMessageBox_INFO("提示", "多联板按钮事件匹配失败，请检查！", jrscore::MessageButton::Ok);
        return;
    }
    _multiple_board_param->event = jrsdata::MultiBoardEventParam::Event::MULTIPLE_BOARD;
    QString btn_text = sender_btn->text();
    if (btn_text == "生成多连板")
    {
        if (_multiple_board_param->irregular_multiple_board_param.has_value())
        {
            _multiple_board_param->irregular_multiple_board_param->step = jrsdata::MultiBoardEventParam::IrregularParam::Step::GENERATE_MULTI_BOARD;
        }
        else
        {
            jrsdata::MultiBoardEventParam::IrregularParam iregular_param;
            iregular_param.step = jrsdata::MultiBoardEventParam::IrregularParam::Step::GENERATE_MULTI_BOARD;
            _multiple_board_param->irregular_multiple_board_param = iregular_param;
        }
        _multiple_board_param->irregular_multiple_board_param->cols = ui->spin_x->value();
        _multiple_board_param->irregular_multiple_board_param->rows = ui->spin_y->value();
    }
    else if (btn_text == "确认元件")
    {
        _multiple_board_param->irregular_multiple_board_param->step = jrsdata::MultiBoardEventParam::IrregularParam::Step::CONFIRM_SUBBOARD_COMPONENT;
    }
    else if (btn_text == "框选位置")
    {
        if (_multiple_board_param->irregular_multiple_board_param.has_value())
        {
            _multiple_board_param->irregular_multiple_board_param->step = jrsdata::MultiBoardEventParam::IrregularParam::Step::SELECT_SUBBOARD_LOCATION;
        }
        else
        {
            jrsdata::MultiBoardEventParam::IrregularParam iregular_param;
            iregular_param.step = jrsdata::MultiBoardEventParam::IrregularParam::Step::GENERATE_MULTI_BOARD;
            _multiple_board_param->irregular_multiple_board_param = iregular_param;
        }
    }
    else if (btn_text == "确认位置")
    {
        if (!_multiple_board_param->irregular_multiple_board_param.has_value())
        {
            JRSMessageBox_INFO("越级执行", "请先选择元件和框选位置", jrscore::MessageButton::Ok);
        }
        _multiple_board_param->irregular_multiple_board_param->step = jrsdata::MultiBoardEventParam::IrregularParam::Step::CONFIRM_SUBBOARD_LOCATION;
    }
    emit SigUpdateMultipleBoards(_multiple_board_param);
}
void ExpansionMultiPanel::SlotRequestCreateSub()
{
    // qtools::QTools::TogglePopupWidget(ui->pushbutton_confirm_sort, _subboard_sort_view);
}


void ExpansionMultiPanel::InitView()
{
    ui->spin_x->setValue(1);
    ui->spin_y->setValue(1);
    ui->stackedWidget->setVisible(false);  // 隐藏整个 QTabWidget
    ui->component_graphics_view->setScene(_graphic_scene);

    ui->component_graphics_view->setAlignment(Qt::AlignCenter);
    ui->component_graphics_view->fitInView(_graphic_scene->sceneRect(), Qt::KeepAspectRatio);  // 保持图像的宽高比
    //for (auto& [key, value] : jrsdata::MultiBoardEventParamMultiCopyTypeMap)
    //{
    //    ui->combo_copy_mode->insertItem(static_cast<int>(key), value.c_str(), static_cast<int>(key));
    //}
    ////ui->combo_copy_mode->addItems(QStringList() << "阵列" << "旋转");
    //ui->combo_copy_mode->setCurrentIndex(-1);

    //connect(ui->combo_copy_mode, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [=](int index)
    //    {
    //        ui->spin_x->setValue(1);
    //        ui->spin_y->setValue(1);
    //        ui->spin_x->setEnabled(false);
    //        ui->spin_y->setEnabled(false);
    //        ui->btn_select_sub_copy_flag->setEnabled(index >= 0);
    //    });
    //connect(ui->spin_x, QOverload<int>::of(&QSpinBox::valueChanged), this, [=](int value)
    //    {
    //        bool state = value > 1;
    //        ui->btn_select_sub_copy_flag_x->setEnabled(state);
    //        auto copy_mode = ui->combo_copy_mode->currentData().toInt();
    //        if (jrsdata::MultiBoardEventParam::MultiCopyType(copy_mode) == jrsdata::MultiBoardEventParam::MultiCopyType::ROTATE)
    //        {
    //            if (state)
    //            {
    //                ui->spin_y->setValue(1);
    //            }
    //        }
    //        int num = ui->spin_x->value() * ui->spin_y->value();
    //        ui->btn_create_sub->setEnabled(num > 1);
    //    });
    //connect(ui->spin_y, QOverload<int>::of(&QSpinBox::valueChanged), this, [=](int value)
    //    {
    //        bool state = value > 1;
    //        ui->btn_select_sub_copy_flag_y->setEnabled(state);
    //        auto copy_mode = ui->combo_copy_mode->currentData().toInt();
    //        if (jrsdata::MultiBoardEventParam::MultiCopyType(copy_mode) == jrsdata::MultiBoardEventParam::MultiCopyType::ROTATE)
    //        {
    //            if (state)
    //            {
    //                ui->spin_x->setValue(1);
    //            }
    //        }
    //        int num = ui->spin_x->value() * ui->spin_y->value();
    //        ui->btn_create_sub->setEnabled(num > 1);
    //    });
    //
    ui->pushbutton_rule_multile_board_cancel->setHidden(true);
    ui->line->setHidden(true);

}

void ExpansionMultiPanel::InitMember()
{
    _multiple_board_param = std::make_shared<jrsdata::MultiBoardEventParam>();
    _graphic_scene = new QGraphicsScene();
    //auto param = std::make_shared<jrsdata::RenderEventParam>();
    //param->multi_param.step = jrsdata::MultiBoardEventParam::Step(state);
    //param->event_name = jrsaoi::MULTI_BOARD_SELECT_FLAG_EVENT_NAME;
    //param->topic_name = jrsaoi::OPERATE_TRIGGER_TOPIC_NAME;
    //param->sub_name = jrsaoi::OPERATE_RENDER_SUB_NAME;
    //param->invoke_module_name = jrsaoi::RENDER2D_MODULE_NAME;

}

void ExpansionMultiPanel::InitConnect()
{
    // TODO:  增加新版本的界面的设置扩展多连扳的功能
   // connect(ui->btn_select_sub_board, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotSelectSubBoard);
    connect(ui->pushbutton_rule_multile_boards, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRuleMultileSub);
    connect(ui->pushbutton_substandard_multile_boards, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotSubstandardMultileSub);
    connect(ui->pushbutton_rule_sort_boards, &QPushButton::clicked, this, [=]() {
        ui->stackedWidget->setVisible(false);
        qtools::QTools::ShowPopupAtWidget(ui->pushbutton_confirm_sort, _subboard_sort_view);
        });
    //connect(ui->pushbutton_irregular_sort_boards, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRequestCreateSub);
    connect(ui->pushbutton_confirm_sort, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRequestCreateSub);
    //connect(ui->delete_sub_board, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRequestCreateSub);

    //规格多连扳的按钮操作
    connect(ui->pushbutton_rule_multile_board_confirm_component, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_template, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_X_detect, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_Y_detect, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_confirm, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_gen, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_rule_multile_board_cancel, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotRegularMultipleBoardsCreateFlow);

    //不规则多连扳的按钮操作
    connect(ui->pushbutton_generate_irregular_multile_board, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotIrregularMultipleBoardsCreateFlow);
    //connect(ui->pushbutton_substandard_multile_board_confirm_subboard_component, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotIrregularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_substandard_multile_board_select_location, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotIrregularMultipleBoardsCreateFlow);
    connect(ui->pushbutton_substandard_multile_board_confirm_position, &QPushButton::clicked, this, &ExpansionMultiPanel::SlotIrregularMultipleBoardsCreateFlow);

    connect(_subboard_sort_view, &SubboardSortView::SigUpdateSubboardSort, this, [=](const jrsdata::SubboardSortParam& param_) {
        _multiple_board_param->subboard_sort_param = param_;
        _multiple_board_param->event = jrsdata::MultiBoardEventParam::Event::BOARD_SORT;
        emit SigUpdateMultipleBoards(_multiple_board_param);
        });

}
QImage ExpansionMultiPanel::MatToQImage(const cv::Mat& mat)
{
    if (mat.empty()) {
        return QImage();
    }

    // 根据通道类型进行转换
    switch (mat.type()) {
    case CV_8UC1: { // 灰度图
        return QImage(mat.data, mat.cols, mat.rows, (int)mat.step, QImage::Format_Grayscale8);
    }
    case CV_8UC3: { // 彩色图 (BGR)
        //cv::Mat rgbMat;
        //image =         cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB); // 转换为 RGB 格式
        //return QImage(rgbMat.data, rgbMat.cols, rgbMat.rows, (int)rgbMat.step, QImage::Format_RGB888);
        return QImage((const unsigned char*)mat.data, mat.cols, mat.rows, (int)mat.step, QImage::Format_RGB888);
    }
    case CV_8UC4: { // 带透明通道 (BGRA)
        return QImage(mat.data, mat.cols, mat.rows, (int)mat.step, QImage::Format_ARGB32);
    }
    default:
        return QImage();
    }
}
