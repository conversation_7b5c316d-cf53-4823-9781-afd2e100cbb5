/*****************************************************************
 * @file   algorithmenginemanager.h
 * @brief  算法引擎管理类
 * @details 主要功能用于算法引擎对外部调用的交互，属于对外门面
 * <AUTHOR>
 * @date 2024.10.9
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.10.9          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __ALGORITHMENGINEMANAGER_H__
#define __ALGORITHMENGINEMANAGER_H__

//STD
#include <iostream>
#include <map>
#include <vector>
//Custom
#include "pluginexport.hpp"
//Third
namespace jrsoperator 
{
    class OperatorViewBase;
    struct OperatorParamBase;

}
namespace jrsparam 
{
    struct ExecuteAlgoParam;
    struct AlgoExecuteResultParam;
}


namespace jrsalgo
{
    struct AlgoEngineDataImpl;
    class JRS_AOI_PLUGIN_API AlgorithmEngineManager
    {
        public:
            AlgorithmEngineManager ();
            ~AlgorithmEngineManager ();

            /**
             * @fun GetSpecificAlgoView 
             * @brief 获取指定的算法界面
             * @param algo_name_ [IN] 算法名称
             * @return 返回获取的算法界面指针
             * <AUTHOR>
             * @date 2024.10.17
             */
            jrsoperator::OperatorViewBase* GetSpecificAlgoView ( const std::string& algo_name_ );

            //TODO:使用统一的接口替换这个临时实现方式 by zhangyuyu 2024.10.15
            std::shared_ptr<jrsoperator::OperatorParamBase>GetSpecificAlgoParamPtr(const std::string& algo_name);
            /**
            * @fun GetAlgoNameLists
            * @brief 获取导入算法的所有名称列表
            * @return 返回名称列表
            * <AUTHOR>
            * @date 2024.10.9
            */
            const std::map<std::string,std::string>& GetAlgoNameMap();

            /**
             * @fun ExecuteSpecificAlgoDrive 
             * @brief 执行指定算法
             * @param execute_algo_param_ [IN] 算法执行参数
             * @return  返回算法执行结果
             * <AUTHOR>
             * @date 2024.11.25
             */
            std::shared_ptr<jrsoperator::OperatorParamBase> ExecuteSpecificAlgoDrive(const jrsparam::ExecuteAlgoParam& execute_algo_param_);
            
            /**
             * @fun GetAlgoExecuteRectInfo 
             * @brief 获取算法执行时的矩形信息
             * @param param_ptr_[IN] 算法执行参数
             * @return 返回矩形信息string
             * <AUTHOR>
             * @date 2025.1.11
             */
            std::string GetAlgoExecuteRectInfo(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_);
            
            /**
             * @fun SetAlgoDynamicOutParamValue
             * @brief  动态给算法输出结果赋值，因为需要将算法的输出结果保存
             * @param execute_algo_result_param_ [IN]
             * @param param_ptr_ [OUT]
             * @param json_data_ [IN]
             * @return  成功返回AOI_OK,否则返回错误码
             * <AUTHOR>
             * @date 2025.6.11
             */
            int SetAlgoDynamicOutParamValue(const jrsparam::AlgoExecuteResultParam& execute_algo_result_param_,
                const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_, const std::string& algo_name_);


            /**
             * @fun GetAlgoSpecMap 
             * @brief 获取算法中规格和软件中规格的映射
             * @return  返回算法中规格和软件中规格的映射表
             * <AUTHOR>
             * @date 2025.5.14
             */
            const std::map<std::string/*算法名称*/, std::map<std::string/*算法中规格名称*/, std::vector<std::string>/*软件中规格名称*/>>& GetAlgoSpecMap();
            /**
             * @fun GetAlgoExecuteResultParam 
             * @brief 解析算法结果指针，根据算法配置文件，获取子项的结果值
             * @param param_ptr_ [IN] 算法检测的结果指针
             * @param algo_name_ [IN] 算法名称
             * @return 返回解析后得到的算法执行结果值
             * <AUTHOR>
             * @date 2025.5.7
             */
            jrsparam::AlgoExecuteResultParam GetAlgoExecuteResultParam(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_,const std::string& algo_name_);
        
            /**
             * @fun GetAlgoExecuteResultStatus
             * @brief 获取算法执行结果的状态,仅有 true或false的状态结果
             * @param param_ptr_ [IN] 算法执行结果指针
             * @return ok返回true，ng返回false
             * <AUTHOR>
             * @date 2025.5.7
             */
            bool GetAlgoExecuteResultStatus(const std::shared_ptr<jrsoperator::OperatorParamBase>& param_ptr_);
        private:
            //Fun
            void InitMember ();
            //Member
            AlgoEngineDataImpl* algo_engine_data_impl; /**< 算法引擎实现参数*/
            
    
    };

    using AlgorithmEngineManagerPtr = std::shared_ptr<AlgorithmEngineManager>;

}
#endif // !__ALGORITHMENGINEMANAGER_H__
