# JRSAOI 核心数据模型ER图

```mermaid
erDiagram
    %% 项目相关实体
    PROJECT {
        string project_name PK
        string current_group_name
        string project_path
        string version
        bool is_save
        datetime create_time
        datetime update_time
    }

    BOARD {
        int board_id PK
        string project_name FK
        int width
        int height
        int cols
        int rows
        int num_sub_board
        int layout
        int material
        double real_width
        double real_height
        double left_top_x
        double left_top_y
        double right_bottom_x
        double right_bottom_y
    }

    SUB_BOARD {
        int sub_board_id PK
        int board_id FK
        string sub_board_name
        double offset_x
        double offset_y
        double width
        double height
        bool enable
        int sort_order
    }

    %% 元件相关实体
    COMPONENT {
        int component_id PK
        int sub_board_id FK
        string component_name
        string component_part_number
        double cx
        double cy
        double width
        double height
        double angle
        bool enable
        string component_type
    }

    PART_NUMBER {
        string part_number_name PK
        string description
        string component_type
        double default_width
        double default_height
        bool is_active
    }

    COMPONENT_MODULE {
        int module_id PK
        int component_id FK
        string module_type
        string unit_group_name
        string unit_name
        double offset_x
        double offset_y
        json module_params
    }

    %% 检测相关实体
    DETECT_WINDOW {
        int detect_window_id PK
        string part_number_name FK
        string model_name
        string window_name
        string defect_name
        string group_name
        int cx
        int cy
        int width
        int height
        int level
        int detect_state
        bool enable
        float search_size
    }

    DETECT_ALGORITHM {
        int algorithm_id PK
        int detect_window_id FK
        string algorithm_name
        string algorithm_type
        json algorithm_params
        int execution_order
        bool enable
    }

    TEMPLATE {
        int template_id PK
        int algorithm_id FK
        int cols
        int rows
        int light_image_id
        blob image_data
        json color_params
        datetime create_time
    }

    %% Mark点和条码
    MARK {
        int mark_id PK
        int board_id FK
        string mark_name
        double cx
        double cy
        double width
        double height
        string mark_type
        bool is_reference
    }

    BARCODE {
        int barcode_id PK
        int board_id FK
        string barcode_name
        double cx
        double cy
        double width
        double height
        string barcode_type
        string decode_format
    }

    %% 检测结果相关实体
    DETECT_RESULT {
        int result_id PK
        string project_name FK
        string board_serial_number
        datetime detect_time
        bool overall_result
        int total_components
        int ok_components
        int ng_components
        string operator_name
        string machine_id
    }

    COMPONENT_RESULT {
        int component_result_id PK
        int result_id FK
        int component_id FK
        bool detect_result
        string defect_type
        string defect_description
        double confidence_score
        json result_details
    }

    ALGORITHM_RESULT {
        int algo_result_id PK
        int component_result_id FK
        int algorithm_id FK
        bool algorithm_result
        double execution_time
        json algorithm_output
        string error_message
    }

    %% 系统配置相关实体
    USER {
        int user_id PK
        string username
        string password_hash
        string email
        string role
        bool is_active
        datetime last_login
        datetime create_time
    }

    MACHINE_CONFIG {
        int config_id PK
        string machine_id
        string config_name
        string config_type
        json config_value
        datetime update_time
        string updated_by
    }

    SYSTEM_PARAM {
        int param_id PK
        string param_name
        string param_type
        string param_value
        string param_description
        string param_group
        bool is_editable
    }

    %% 设备相关实体
    DEVICE {
        int device_id PK
        string device_name
        string device_type
        string ip_address
        int port
        bool is_connected
        json device_config
        datetime last_heartbeat
    }

    CAPTURE_FOV {
        int fov_id PK
        int component_id FK
        double fov_x
        double fov_y
        double fov_width
        double fov_height
        int capture_order
        string light_config
    }

    %% 关系定义
    PROJECT ||--|| BOARD : "has"
    BOARD ||--o{ SUB_BOARD : "contains"
    SUB_BOARD ||--o{ COMPONENT : "contains"
    COMPONENT }o--|| PART_NUMBER : "belongs_to"
    COMPONENT ||--o{ COMPONENT_MODULE : "has"
    COMPONENT ||--o{ CAPTURE_FOV : "requires"
    
    PART_NUMBER ||--o{ DETECT_WINDOW : "defines"
    DETECT_WINDOW ||--o{ DETECT_ALGORITHM : "contains"
    DETECT_ALGORITHM ||--o{ TEMPLATE : "uses"
    
    BOARD ||--o{ MARK : "has"
    BOARD ||--o{ BARCODE : "has"
    
    PROJECT ||--o{ DETECT_RESULT : "generates"
    DETECT_RESULT ||--o{ COMPONENT_RESULT : "contains"
    COMPONENT_RESULT }o--|| COMPONENT : "tests"
    COMPONENT_RESULT ||--o{ ALGORITHM_RESULT : "produces"
    ALGORITHM_RESULT }o--|| DETECT_ALGORITHM : "executes"
    
    USER ||--o{ DETECT_RESULT : "operates"
    USER ||--o{ MACHINE_CONFIG : "configures"
```

## 数据模型说明

### 核心实体关系

#### 1. 项目层次结构
- **PROJECT** → **BOARD** → **SUB_BOARD** → **COMPONENT**
- 项目包含板子，板子包含子板，子板包含元件
- 支持多层次的项目组织结构

#### 2. 元件和料号关系
- **COMPONENT** 属于 **PART_NUMBER**（料号）
- 料号定义了元件的通用属性和检测配置
- 元件是料号的具体实例，包含位置和角度信息

#### 3. 检测配置体系
- **PART_NUMBER** → **DETECT_WINDOW** → **DETECT_ALGORITHM** → **TEMPLATE**
- 料号定义检测框，检测框包含算法，算法使用模板
- 支持多级检测配置的灵活组合

#### 4. 检测结果体系
- **PROJECT** → **DETECT_RESULT** → **COMPONENT_RESULT** → **ALGORITHM_RESULT**
- 项目产生检测结果，包含元件结果，进一步包含算法结果
- 完整的结果追溯链条

### 关键字段说明

#### PROJECT表
- `project_name`: 项目唯一标识
- `current_group_name`: 当前激活的图像组
- `is_save`: 项目保存状态标记

#### BOARD表
- `real_width/real_height`: 板子的物理尺寸（毫米）
- `left_top_x/y, right_bottom_x/y`: 板子在坐标系中的位置

#### COMPONENT表
- `cx, cy`: 元件中心坐标
- `angle`: 元件旋转角度
- `component_part_number`: 关联的料号

#### DETECT_WINDOW表
- `cx, cy, width, height`: 检测框的位置和尺寸
- `level`: 检测等级（严格程度）
- `search_size`: 搜索范围

#### DETECT_ALGORITHM表
- `algorithm_type`: 算法类型（OCR、OCV、位置检测等）
- `algorithm_params`: JSON格式的算法参数
- `execution_order`: 算法执行顺序

#### DETECT_RESULT表
- `overall_result`: 整板检测结果（OK/NG）
- `total_components/ok_components/ng_components`: 统计信息

### 数据完整性约束

#### 外键约束
- 所有FK字段都有对应的外键约束
- 确保数据引用的完整性

#### 业务约束
- 元件必须属于有效的料号
- 检测结果必须关联到具体的元件
- 算法结果必须关联到具体的算法

### 索引建议

#### 主要查询索引
- `PROJECT.project_name`
- `COMPONENT.component_part_number`
- `DETECT_RESULT.detect_time`
- `COMPONENT_RESULT.detect_result`

#### 复合索引
- `(project_name, detect_time)` - 按项目查询检测历史
- `(component_part_number, detect_result)` - 按料号统计检测结果
