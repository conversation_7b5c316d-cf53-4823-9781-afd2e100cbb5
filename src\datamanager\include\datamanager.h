﻿#pragma once

#include <iostream>
#include <functional>

//custom
#include "coreapplication.h"
#include "nlohmann/json.hpp"
#include "viewparam.hpp"
#include "projectparam.hpp"
#include "viewdefine.h"

#pragma warning(push, 1)
#pragma warning(disable : 4996 4127)
#include "opencv2/opencv.hpp"
#pragma warning(pop)

namespace jrsdatabase {
    struct DatabaseConnectParam;
}
namespace jrsdata
{
    struct ImplData;
    struct ComponentSaveInfo;
    struct DetectResultParam;
    using JSON = nlohmann::json;
    using Function = std::function<int(const jrsdata::ViewParamBasePtr& param_)>;
    using MotionConfigCallBack = std::function<int(const jrsdata::OperateViewParamPtr& param_)>;

    class JRS_AOI_PLUGIN_API DataManager
    {
    public:
        DataManager();
        ~DataManager();
        /**
        * @fun EventHandler
        * @brief 数据处理
        * @param param_
        * <AUTHOR>
        * @date 2024.8.26
        */
        int EventHandler(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SetSettingParamsCallBack
         * @brief 设置参数回调
         * @param callback_
         * <AUTHOR>
         * @date 2024.9.4
         */
        void SetSettingParamsCallBack(jrsdata::InvokeSettingViewParamFun callback_);

        /**
         * @fun SetProjectCallBack
         * @brief 设置工程回调
         * @param callback_
         * <AUTHOR>
         * @date 2024.9.4
         */
        void SetProjectCallBack(jrsdata::InvokeProjectFun callback_);
        /**
         * @fun SetLogicCallBack
         * @brief  设置 logic 进行数据回调
         * @param callback_
         * <AUTHOR>
         * @date 2024.9.5
         */
        void SetLogicCallBack(jrsdata::InvokeViewParamBaseFun callback_);
        /**
         * @fun SetSystemStateCallBack
         * @brief  系统状态回调
         * @param callback_
         * <AUTHOR>
         * @date 2025.1.13
         */
        void SetSystemStateCallBack(jrsdata::InvokeSystemStateParamFun callback_);
        /**
         * @fun SetDetectStatisticsCallBack
         * @brief  统计信息回调
         * @param callback_
         * <AUTHOR>
         * @date 2025.1.13
         */
        void SetDetectStatisticsCallBack(jrsdata::InvokeOperateViewParamFun callback_);
        /**
         * @fun SetComponentDetectResultCallBack
         * @brief 元件检测结果回调
         * @param callback_
         * <AUTHOR>
         * @date 2025.1.22
         */
        void SetComponentDetectResultCallBack(jrsdata::InvokeAlgoEventParamFun callback_);

        /**
         * @fun SetOnlieDebugInfoCallBack
         * @brief 在线调试信息传递给上层回调
         * @param callback_ [IN] 回调函数
         * <AUTHOR>
         * @date 2025.4.14
         */
        void SetOnlieDebugInfoCallBack(const jrsdata::InvokeOnlineDebugViewParamFun& callback_);
    private:
        /**
         * @fun SavePorjectParams
         * @brief 工程保存
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int SavePorjectParams(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun GetProjectParams
         * @brief 工程读取
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int GetProjectParams(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun GetEntiretyBoardImages
         * @brief 工程大图读取
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int GetEntiretyBoardImages(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SaveEntiretyBoardImages
         * @brief 工程大图保存
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int SaveEntiretyBoardImages(const jrsdata::ViewParamBasePtr& param_);

        /**
        * @fun SaveComponentsImages
        * @brief 多个元件图片保存
        * @param param_ 元件图片信息
        * @return    成功:AOI_OK, 失败：错误码
        * <AUTHOR>
        * @date 2025.1.9
        */
        //int SaveComponentsImages(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SaveComponentImages
         * @brief 单个元件图片保存
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int SaveComponentImages(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun ReadComponentImages
         * @brief  原件图片读取
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.30
         */
        int ReadComponentImages(const jrsdata::ViewParamBasePtr& param_);
        /**
        * @fun HandleMotionParam
        * @brief 处理运控配置参数的读写
        * <AUTHOR>
        * @date 2024.9.5
        */
        int HandleMotionParam(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun GetSettingParams
         * @brief 获取系统参数
         * @param setting_param
         * <AUTHOR>
         * @date 2024.9.4
         */
        int GetSettingParams(const jrsdata::ViewParamBasePtr& param_);
        int GetReadSystemParams(jrsdata::SystemParam& sys_param_);
        int GetReadMachineParams(jrsdata::MachineParam& machine_param_);
        bool CheckAndInitDatabase(jrsdata::SettingParamMap& machine_param_);
        bool IniteMachinePathParam(jrsdata::SettingParamMap& machine_param_, const std::string& key_,
            const std::string& label_, const std::string& default_directory_);
        /**
         * @fun InitMachineParam
         * @brief 初始化机台参数
         * @param machine_param_
         * @param item_name_
         * @param value_
         * @param explain_
         * @return
         * <AUTHOR>
         * @date 2025.5.8
         */
        int InitMachineParam(jrsdata::SettingParamMap& machine_param_, const std::string& item_name_,
            const jrsdata::JrsVariant& value_, const std::string& explain_ = "");
        bool CheckAndInitMachinePathParams(jrsdata::SettingParamMap& machine_param_);
        /**
         * @fun InitMachineParams
         * @brief  如果没有机台参数 默认初始化机台参数
         * @param machine_param_
         * @return
         * <AUTHOR>
         * @date 2025.5.8
         */
        bool InitMachineParams(jrsdata::SettingParamMap& machine_param_);
        int SaveSettingParams(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun SaveDetectResultParam
         * @brief
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.10.8
         */
        int SaveDetectResultParam(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun GetSystemParam
         * @brief  获取系统参数信息,有则返回，没有则新增
         * @param config_name_  配置文件名称
         * @param field_name_   json字段名
         * @param default_value_ 默认值
         * @return
         * @date 2024.8.19
         * <AUTHOR>
         */
        JSON GetSystemParam(const std::string& config_name_, const std::string& field_name_, const  std::any& default_value_);

        /**
         * @fun ConnectDatabase
         * @brief
         * @return
         * <AUTHOR>
         * @date 2024.11.15
         */
        int ConnectDatabase();
        /**
         * @fun ReinitDatabase
         * @brief 重新初始化数据库
         * @param db_conn_param_
         * @return 成功:AOI_OK, 失败：错误码
         * <AUTHOR>
         * @date 2024.10.8
         */
        int ReinitDatabase(const jrsdatabase::DatabaseConnectParam& db_conn_param_);
        /**
         * @fun InitSystem
         * @brief 系统初始化
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.10.24
         */
        int InitSystem(const jrsdata::ViewParamBasePtr& param_);
        void InitSettingParam();

        /**
         * @fun ChangeSystemState
         * @brief 系统状态更新
         * @param check_name_
         * @param state_
         * @param code_and_info_
         * @param event_name_
         * <AUTHOR>
         * @date 2024.11.14
         */
        void ChangeSystemState(const std::string& check_name_, const jrsdata::MachineCheckParamInfo::CheckState& state_,
            const std::tuple<int, std::string, std::string>& code_and_info_ = std::tuple(jrscore::AOI_OK, "", ""),
            const std::string& event_name_ = jrsaoi::SYSTEM_STATE_DEVICE_RESET_EVENT);
        /**
         * @fun SaveDetectResult
         * @brief 保存测试结果
         * @param param_
         * <AUTHOR>
         * @date 2024.11.14
         */
        int SaveDetectResult(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun GetDetectResultStatistics
         * @brief 从数据库中获取测试结果
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2024.11.19
         */
        int GetDetectResultStatistics(const  std::optional<jrsdata::DetectStatisticsViewParam>& param_);

        /**
         * @fun ComponentDetectResultToRender
         * @brief 元件检测结果渲染到Render界面
         * @param component_detect_results_ [IN] 所有元件检测结果状态
         * @return  成功返回AOI_OK
         * <AUTHOR>
         * @date 2025.4.14
         */
        int ComponentDetectResultToRender(const std::vector<jrsdata::ComponentDetectResult> component_detect_results_);

        /**
         * @fun OnlineDebugInfoToOnlineView
         * @brief 将在线调试的信息传递给上层在线调试界面模块
         * @param online_debug_parm_ptr_ [IN] 在线调试信息
         * @return  成功返回AOI_OK
         * <AUTHOR>
         * @date 2025.4.14
         */
        int OnlineDebugInfoToOnlineView(const jrsdata::OnlineDebugParmPtr& online_debug_parm_ptr_);
        // 处理函数绑定
        void InitInvokeFun();
        bool IsValidParam(const jrsdata::ViewParamBasePtr& param_);
        bool InvokeFun(const jrsdata::ViewParamBasePtr& param_);
        int DataEventHandler(const jrsdata::ViewParamBasePtr& param_);

        /**
        * @fun SaveComponentExecuteInfo
        * @brief 保存元件执行时算法的信息
        * @param param_ 元件信息参数
        * @return 成功:AOI_OK, 失败：错误码
        * <AUTHOR>
        * @date 2025.1.9
        */
        int SaveComponentExecuteInfo(const jrsdata::ViewParamBasePtr param_);
        int SaveAlgoReport(const std::shared_ptr<DetectResultParam>& result_data_ptr_, const std::string& report_path);
        std::string GenerateSaveComponentExecuteInfoPath(const std::string& report_path, const std::shared_ptr<ComponentSaveInfo>& component_save_info_);
        /**
        * @fun SaveImage
        * @brief 保存图片
        * @param file_path_ 图片路径
        * @param input_img_ 图片数据
        * @param name_prefix_ 图片名称前缀
        * @param type_ 图片格式
        * @return 成功:AOI_OK, 失败：错误码
        * <AUTHOR>
        * @date 2025.1.9
        * @note 保存图片到指定路径
        * 保存图片到指定路径，如果路径不存在，则创建路径
        * 保存图片数据到指定路径，如果路径不存在，则创建路径
        * 保存图片数据到指定路径，如果路径存在，则覆盖原文件
        */
        int SaveImage(const std::string& file_path_, const std::unordered_map<int, cv::Mat>& input_img_,
            const std::string& name_prefix_, const std::string& type_ = ".png");
        /**
         * @fun InitMember
         * @brief
         * <AUTHOR>
         * @date 2024.11.15
         */
        void InitMember();
        /**
        * @fun SaveCompressEntiretyBoardImages
        * @brief 保存维修站数据 压缩整版图
        * @param param_[IN] 维修站数据信息
        * @return 成功:AOI_OK, 失败：错误码
        * <AUTHOR>
        * @date 2025.1.11
        */
        int SaveRepairCompressEntiretyBoardImages(const jrsdata::ViewParamBasePtr param_);

        /**
         * @fun SaveRepairBriefComponentInfo
         * @brief 保存维修站数据 元件简要信息
         * @param param_ [IN] 维修站数据信息
         * @return 成功:AOI_OK, 失败：错误码
         * <AUTHOR>
         * @date 2025.1.14
         */
        int SaveRepairBriefComponentInfo(const jrsdata::ViewParamBasePtr param_);

        /**
         * @fun GetMachineSpecificParam
         * @brief 获取机台指定参数的值,这个值存放的是字符串内容，具体内容需要解析
         * @note 简单的参数直接获取得到的字符串转换，复杂的是存放一个json字符串进来的，所以需要解析，不过目前只有
         * 各个保存路径是json字符串，其他都为单个字符串
         * @param key 指定机台参数名称
         * @param param_name 指定机台参数说明，用于日志打印
         * @return  返回指定参数内容
         * <AUTHOR>
         * @date 2025.1.16
         */
        std::string GetMachineSpecificParam(const std::string& key, const std::string& param_name);

        /**
         * @fun GetMachineSpecificSavePathParam
         * @brief 获取机台一系列的保存路径如：元件库路径，原图路径等。。。
         * @param specific_path_name[IN] 具体要获取哪一个路径名称
         * @param res_path_str[IN] 获取的路径结果
         * @return 返回bool，表示该路径是否启用，如果没有启用，则无需保存这些内容
         * <AUTHOR>
         * @date 2025.1.16
         */
        bool GetMachineSpecificSavePathParam(const std::string& specific_path_name, std::string& res_path_str);

        int UpdateWorkNumber(const jrsdata::ViewParamBasePtr param_);
        /**
         * @fun ClearStatisticsData
         * @brief  清除板子统计数据
         * @return
         * <AUTHOR>
         * @date 2025.2.20
         */
        int ClearStatisticsData(const jrsdata::ViewParamBasePtr param_);
        /**
         * @fun ClearDevicesData
         * @brief  清除元件统计数据
         * @param project_name_
         * @return
         * @date 2025.3.10
         * <AUTHOR>
         */
        int ClearDevicesData(const jrsdata::ViewParamBasePtr param_);

        /**
         * @fun SaveLibComponent
         * @brief 保存元件库元件为二进制文件
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.3.20
         */
        int SaveLibComponent(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun ReadLibComponent
         * @brief 获取元件库地址下所有元件
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.3.20
         */
        int ReadLibComponent(const jrsdata::ViewParamBasePtr& param_);

        /**
         * @fun RemoveLibComponent
         * @brief 移除元件库地址下的元件
         * @param param_
         * @return
         * <AUTHOR>
         * @date 2025.3.25
         */
        int RemoveLibComponent(const jrsdata::ViewParamBasePtr& param_);
        /**
         * @fun QuerySubboardBarcodesBySubboardBarcode
         * @brief 根据子板条码 查询整个板子的子板条码和子板id
         * @param subboard_barcode_
         * @return
         * <AUTHOR>
         * @date 2025.6.8
         */
        int QuerySubboardBarcodesBySubboardBarcode(const jrsdata::ViewParamBasePtr& param_);
        /**
             * @fun UpdateOnlineRejudicationToDatabase
             * @brief  将在线复判数据更新到数据库
             * @param param_
             * @return
             * <AUTHOR>
             * @date 2025.6.16
             */
        int UpdateOnlineRejudicationToDatabase(const jrsdata::ViewParamBasePtr& param_);

    private:

        void SaveThumbnailImage(const cv::Mat& src_img_, const std::string& save_path_, const std::string& save_img_name_ = "light_img_thumbnail.png",
            const cv::Size& thumbnail_size_ = { 500,500 });

        /**
         * @fun CheckDiskCapacity
         * @brief  检查磁盘容量 充足 true 不足 false
         * @param path_ 磁盘路径
         * <AUTHOR>
         * @date 2025.5.14
         */
        bool CheckDiskCapacity(const std::string& path_, const std::string& action_name_);

        /**
         * @fun ReadDataFromDatabase
         * @brief
         * @param table_select_ptr_
         * @return
         * <AUTHOR>
         * @date 2025.6.20
         */
        int ReadDataFromDatabase(const std::shared_ptr<jrsdatabase::jrsselect::SelectorParamBase>& table_select_ptr_);
        /**
         * @fun DBTableDataOperator
         * @brief
         * @param table_data_operator_
         * @return
         * <AUTHOR>
         * @date 2025.6.20
         */
        int DBTableDataOperator(const std::shared_ptr < jrsdatabase::TablesDataOperator>& table_data_operator_);

        jrsdata::ImplData* _impl_data;
        std::map<std::string, Function> _data_func_map;
        jrsdata::InvokeSettingViewParamFun _setting_param_callback;    /**< 设置参数回调 */
        jrsdata::InvokeProjectFun _project_param_callback;			   /**<设置工程回调*/
        jrsdata::InvokeViewParamBaseFun _logic_param_callback;		   /**< 将参数返回到logic模块 */
        jrsdata::InvokeSystemStateParamFun sys_state_callback;          /**< 初始化系统回调 */
        jrsdata::InvokeOperateViewParamFun _operate_callback;           /**< operate回调*/
        jrsdata::InvokeAlgoEventParamFun _detect_result_callback;       /**< 检测结果统计回调 */
        jrsdata::InvokeOnlineDebugViewParamFun onlie_debug_info_callback; /**< 在线调试结果信息回调 */
        std::mutex _mtxsys_state_param;
    };

    using DataManagerPtr = std::shared_ptr<DataManager>;
};