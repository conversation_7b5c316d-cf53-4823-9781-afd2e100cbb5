﻿#include "projectoperator.h"
namespace jrsaoi
{

    ParamOperator::ParamOperator()
        : project_data_process_ptr(std::make_shared<jrsparam::ProjectDataProcess>())
        , param_process_ptr(std::make_shared<jrsparam::ParameterProcess>())
    {

    }

    ParamOperator::~ParamOperator()
    {
    }
    const std::shared_ptr<jrsparam::ProjectDataProcess>& ParamOperator::GetProjectDataProcessInstance()
    {
        return project_data_process_ptr;
    }
    const std::shared_ptr<jrsparam::ParameterProcess>& ParamOperator::GetParameterProcessInstance()
    {
        return param_process_ptr;
    }




}
