#ifndef __PARAMBASE_HPP__
#define __PARAMBASE_HPP__
//std
#include <iostream>
#include <variant>
#include <vector>
namespace jrsdata
{
    using JrsVariant = std::variant<std::string, int, float, double,bool,std::vector<int>>;
    struct ViewParamBase
    {
        std::string module_name; /**< 模块名称 */
        std::string topic_name;  /**< 发布主题名称 */
        std::string sub_name;    /**< 订阅者名称*/
        std::string invoke_module_name; /**< 事件响应的模块名称，主要是为了跨层的时候使用的*/
        std::string event_name;  /**< 事件名称 */
        ViewParamBase()
            : module_name{}
            , topic_name()
            , sub_name("all") // 默认通知全部
            , event_name{}
        {
        }
        virtual ~ViewParamBase() = default;
    };
    using ViewParamBasePtr = std::shared_ptr<ViewParamBase>;
    /**< 文件保存类型 */
    enum class FileType
    {
        JSON,                 /**< Json 格式*/
        XML,                  /**< XML  格式*/
        YAML,                 /**< YAML  格式*/
        CSV,                  /**< CSV  格式*/
        BIN,                  /**< BIN 二进制格式*/
    };
    /** 数据保存方式 */
    enum class DataSaveMode
    {
        SAVE_FILE,              /**<保存到文件 */
        SAVE_DATABASE,          /**<保存到数据库 */
        SAVE_FILE_AND_DATABASE  /**<保存到文件及数据库 */
    };
    /**< 文件信息 */
    struct FileParam
    {
        std::string file_path; /**<文件存储路径*/
        std::string file_name; /**<文件存储名称*/
        FileType file_type;    /**<文件存储类型*/
    };

    /**< 数据基类 */
    struct DataBase
    {
        DataBase() = default;
        virtual ~DataBase() = default;
        DataSaveMode data_save_mode{};  /**< 数据存储方式 */
        FileParam file_param{};         /**< 文件参数 */
    };
    using DataBasePtr = std::shared_ptr<DataBase>;

}
#endif //!__PARAMBASE_HPP__