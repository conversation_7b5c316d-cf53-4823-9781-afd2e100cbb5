#ifndef __JRSGLOBALTABLE_H__
#define __JRSGLOBALTABLE_H__

// STD
#include <cstring>
#include <iostream>
#include <fstream>
#include <any>
#include <mutex>
#include <map>

#include <nlohmann/json.hpp>

using JSON = nlohmann::json;

namespace jrsfiledata
{
    class JrsGlobalTable
    {
    public:
        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间2024/08/16
        * @brief: 获取指定名称的模块对象，没有则自动创建
        * @param {string} objName: 对象名称
        * @return {JrsGlobalTable*}:模块对象
        * @see:
        * @attention:
        **************************************************************************/
        static JrsGlobalTable* JrsGetObj(std::string objName);

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间2024/08/16
        * @brief: 删除指定名称的模块对象
        * @param {string} objName: 对象名称
        * @return {void}:
        * @see:
        * @attention:
        **************************************************************************/
        static void JrsDelObj(std::string objName);

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间2024/08/16
        * @brief: 构造对象
        * @param {string} objName: 对象名
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        JrsGlobalTable(std::string objName = "");

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间08/16
        * @brief: 析构对象
        * @param:
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        virtual ~JrsGlobalTable();

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间2024/08/16
        * @brief: 设置table的json字符串以及变量名
        * @param {string} jsonString: json字符串
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        void SetGlobalTable(std::string jsonString);

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间08/16
        * @brief: 获取整个Document的json字符串
        * @return: json字符串
        * @see:
        * @attention:
        **************************************************************************/
        std::string GetJsonString();

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间08/16
        * @brief: 获取指定key对应的value
        * @param keyPath: 举例："0.HomeParam.timeout"，0表示数组下标，HomeParam和timeout是分量名
        * @param defaultValue：如果没找到则返回默认值
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        JSON GetValue(std::string keyPath, std::any defaultValue);

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间08/16
        * @brief: 更新表
        * @param jsonStr: 要更新的键值对，若键不存在，则新建
        * @param basePath：举例："Cards.0.Axis"，0表示数组下标，代表Cards的第1个元素的Axis分量
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        void UpdateValue(const std::string& jsonStr, const std::string& basePath);

        /**************************************************************************
        * <AUTHOR>
        * @date 完成时间08/16
        * @brief: 将配置保存为文件
        * @param filePath: 保存文件地址
        * @return:
        * @see:
        * @attention:
        **************************************************************************/
        void SaveSetting(const std::string filePath);

        std::string ReadSetting(const std::string filePath);

    private:

        // 字符串分割
        std::vector<std::string> SplitString(const std::string& s, char delimiter);

        // 判断字符串能否转为整数
        bool IsInteger(const std::string& s);

        // 辅助函数，用于根据给定的键路径获取或创建JSON对象中的元素
        JSON& GetElementByPath(JSON& root, const std::string& path);

    private:
        static std::map<std::string, JrsGlobalTable*>  s_objMap;    // 静态对象-名称映射表
        static std::mutex s_objMapMutex;   	                        // 映射表锁
        std::string m_objName;       	                            // 对象名
        JSON    m_globalTable;
        std::mutex  m_mutex;
    };
    using JrsGlobalTablePtr = std::shared_ptr<JrsGlobalTable>;
}

#endif
