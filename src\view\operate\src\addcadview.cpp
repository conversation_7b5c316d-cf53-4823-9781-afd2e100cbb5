#include <QFile>
#include <QStringList>
#pragma warning(push, 1)
#include "ui_addcadview.h"
#pragma warning(pop)
#include "addcadview.h"
#include "addcadviewmodel.h"
#include "fileoperation.h"
#include "viewtool.hpp"
#include "tools.h"
#include "readcadfile.hpp"

using namespace jtools;
using namespace jrsdata;

namespace jrsaoi
{
    AddCadView::AddCadView(QWidget* parent)
        : QDialog(parent)
        , ui(new Ui::AddCadView())
    {
        ui->setupUi(this);
        InitView();
        ConnectSlots();
    }
    AddCadView::~AddCadView()
    {
        delete ui;
    }
    void AddCadView::InitView()
    {
        setWindowFlags(Qt::WindowCloseButtonHint | Qt::Window | Qt::WindowStaysOnTopHint);
        m_add_cad_model = new AddCadViewModel(nullptr);
        ui->tw_result->setModel(m_add_cad_model);
        ui->line_start->setEnabled(false);
        ui->line_end->setEnabled(false);
    }

    void AddCadView::ReadCADFile()
    {
        QString dir = QApplication::applicationDirPath() + "/";
        QStringList filters = { "CAD(*.TXT *.CSV)","EXCEL(*.xlsx *.xls);" };
        QString file_name = OpenSingleFile("", filters, dir);
        ui->edit_file->setText(file_name);
        ResolveCADFile(file_name.toStdString());
    }

    void AddCadView::ConnectSlots()
    {
        connect(ui->btn_select_file, SIGNAL(clicked()), this, SLOT(ReadCADFile()));
        connect(ui->btn_report, SIGNAL(clicked()), this, SLOT(ImportCADData()));
        connect(ui->btn_select_start, SIGNAL(clicked()), this, SLOT(SelectStartLine()));
        connect(ui->btn_select_end, SIGNAL(clicked()), this, SLOT(SelectEndLine()));
    }

    void AddCadView::ImportCADData()
    {        
        m_cad_structs.clear();
        if (m_add_cad_model != nullptr)
        {
            std::vector<std::vector<std::string>> vector_data;
            m_add_cad_model->GetDataModel(vector_data);
            if (!ConvertCADData(vector_data))
            {
                //JRSMessageBox_ERR("AddCadView", "解析CAD导入失败！", jrscore::MessageButton::Ok);
                //报警一下
                return;
            }
        }
        this->hide();
        GetCADCompleted(m_cad_structs);
    }

    void AddCadView::SetComboList(QStringList items)
    {
        ui->cb_name->clear();
        ui->cb_name->addItems(items);
        ui->cb_name->setCurrentIndex(0);
        ui->cb_x->clear();
        ui->cb_x->addItems(items);
        ui->cb_x->setCurrentIndex(1);
        ui->cb_y->clear();
        ui->cb_y->addItems(items);
        ui->cb_y->setCurrentIndex(2);
        ui->cb_angle->clear();
        ui->cb_angle->addItems(items);
        ui->cb_angle->setCurrentIndex(3);
        ui->cb_part_no->clear();
        ui->cb_part_no->addItems(items);
        ui->cb_part_no->setCurrentIndex(4);
        ui->cb_subboard_id->clear();
        ui->cb_subboard_id->addItem(QString::fromWCharArray(L"默认"));
        ui->cb_subboard_id->addItems(items);
        ui->cb_subboard_id->setCurrentIndex(0);
        ui->cb_component_id->clear();
        ui->cb_component_id->addItem(QString::fromWCharArray(L"默认"));
        ui->cb_component_id->addItems(items);
        ui->cb_component_id->setCurrentIndex(0);
    }

    bool AddCadView::ResolveCADFile(std::string file_name)
    {
        if (!FileOperation::IsFile(file_name)) // 检查文件是否存在
        {
            JRSMessageBox_ERR("AddCadView", "文件不存在！", jrscore::MessageButton::Ok);
            return false;
        }
        std::string file_datas;
        if (!FileOperation::ReadFileDatas(file_name, file_datas))
        {
            JRSMessageBox_ERR("AddCadView", "读取文件失败！", jrscore::MessageButton::Ok);
            return false;
        }
        ReadCadFile read_cad_file;
        std::string encode;
        if (!read_cad_file.LoadAndUseUchardet(file_datas, encode))
        {
            JRSMessageBox_ERR("AddCadView", "文件格式不正常，仅支持(ASCII/UTF-8/UTF-16)！", jrscore::MessageButton::Ok);
            return false;
        }
        std::vector<std::string> real_data_list;
        if (!read_cad_file.ReadFileDatas(file_name, encode, real_data_list))
        {
            JRSMessageBox_ERR("AddCadView", "文件读取失败！", jrscore::MessageButton::Ok);
            return false;
        }
        UpdateTableDatas(real_data_list);
        return true;
    }

    bool AddCadView::UpdateTableDatas(std::vector<std::string> real_data_list)
    {
        int column_count = 0;
        bool set_combo_text = true;
        std::vector<std::vector<std::string>> vec_line_lists;
        for (const auto& one_line_data : real_data_list)
        {
            std::vector<std::string> one_line_list = jrscore::AOITools::SplitString(one_line_data, '\t');
            if (set_combo_text) // 初始化下拉菜单选项
            {
                set_combo_text = false;
                QStringList string_list_data;
                for (const auto& item : one_line_list)
                {
                    string_list_data.push_back(QString::fromStdString(item));
                }
                SetComboList(string_list_data);
            }
            if (column_count < static_cast<int>(one_line_list.size())) // 更新最大列数
            {
                column_count = static_cast<int>(one_line_list.size());
            }
            vec_line_lists.push_back(one_line_list);
        }
        // 更新表格模型
        m_add_cad_model->setRowCount(static_cast<int>(vec_line_lists.size()));
        m_add_cad_model->SetDataModel(vec_line_lists, column_count);
        ui->tw_result->viewport()->update();
        // 设置起始和结束行
        ui->line_start->setValue(1);
        ui->line_end->setValue(static_cast<int>(real_data_list.size()));
        return true;
    }


    bool AddCadView::GetStringData(std::vector<std::string>& data_vector, int column_index, std::string& output_string)
    {
        if (data_vector.size() < 1)
        {
            JRSMessageBox_ERR("AddCadView", "请排除数据空行！", jrscore::MessageButton::Ok);
            return false;
        }
        if (column_index > -1 && column_index < static_cast<int>(data_vector.size()))
        {
            output_string = data_vector.at(column_index);
            if (!output_string.empty())
            {
                return true;
            }
            JRSMessageBox_ERR("AddCadView", "字符为空！", jrscore::MessageButton::Ok);
        }
        JRSMessageBox_ERR("AddCadView", "字符不正常！", jrscore::MessageButton::Ok);
        return false;
    }

    bool AddCadView::GetFloatData(std::string& input_data, float& output_data)
    {
        try {
            size_t pos;
            output_data = float(std::stod(input_data, &pos)); // 使用stod避免float溢出
            if (pos != input_data.size())
            {
                QString error_msg = QString("\"%1\" 输入包含非法字符").arg(input_data.c_str());
                JRSMessageBox_ERR("AddCadView", error_msg.toStdString(), jrscore::MessageButton::Ok);
            }
            return pos == input_data.size(); // 确保整个字符串都被转换
        }
        catch (const std::invalid_argument& e) {
            JRSMessageBox_ERR("AddCadView", "不是浮点数！", jrscore::MessageButton::Ok);
            (void)e;
            return false; // 不是浮点数
        }
        catch (const std::out_of_range& e) {
            JRSMessageBox_ERR("AddCadView", "浮点数超出范围！", jrscore::MessageButton::Ok);
            (void)e;
            return false; // 超出范围
        }
        return false;
    }

    bool AddCadView::GetIntData(std::string& input_data, int& output_data)
    {
        try {
            size_t pos;
            output_data = int(std::stol(input_data, &pos)); // 使用stol避免int溢出
            if (pos != input_data.size())
            {
                QString error_msg = QString("\"%1\" 输入包含非法字符").arg(input_data.c_str());
                JRSMessageBox_ERR("AddCadView", error_msg.toStdString(), jrscore::MessageButton::Ok);
            }
            return pos == input_data.size(); // 确保整个字符串都被转换
        }
        catch (const std::invalid_argument& e) {
            JRSMessageBox_ERR("AddCadView", "不是整数！", jrscore::MessageButton::Ok);
            (void)e;
            return false; // 不是整数
        }
        catch (const std::out_of_range& e) {
            (void)e;
            JRSMessageBox_ERR("AddCadView", "整数超出范围！", jrscore::MessageButton::Ok);
            return false; // 超出范围
        }
        return false;
    }

    bool AddCadView::GetFloatData(std::vector<std::string>& data_vector, int column_index, float& output_data)
    {
        std::string data_to_convert;
        if (!GetStringData(data_vector, column_index, data_to_convert))
        {
            return false;
        }
        return GetFloatData(data_to_convert, output_data);
    }

    bool AddCadView::GetIntData(std::vector<std::string>& data_vector, int column_index, int& output_data)
    {
        std::string data_to_convert;
        if (!GetStringData(data_vector, column_index, data_to_convert))
        {
            return false;
        }
        return GetIntData(data_to_convert, output_data);
    }

    bool AddCadView::GetComponentData(std::vector<std::string>& data_vector, int column_index, std::string& output_string, std::string part_no)
    {
        if (column_index == 0)
        {
            output_string = part_no;
            return true;
        }
        return GetStringData(data_vector, column_index - 1, output_string);
    }


    bool AddCadView::ValidateInputRange(int start_index, int end_index, size_t data_size)
    {
        return start_index >= 1 && start_index <= static_cast<int>(data_size) && end_index >= start_index && end_index >= 1 && end_index <= static_cast<int>(data_size);
    }

    bool AddCadView::GetSubboardId(std::vector<std::string>& cad_data_row, int column_index, int& output_value)
    {
        if (column_index == 0)
        {
            output_value = 1;
            return true;
        }
        std::string output_string;
        if (!GetStringData(cad_data_row, column_index - 1, output_string))
        {
            return false;
        }
        return GetIntData(output_string, output_value);
    }

    int AddCadView::NormalizeAngle(int angle)
    {
        // 将角度值转换为0到360度之间的等效角度
        angle = angle % 360;
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }

    std::string AddCadView::ReplaceString(std::string name)
    {
        QString result = name.c_str();
        // 使用正则表达式匹配所有无效字符
        QRegExp invalidChars("[\\\\/:*?\"<>|]");
        // 将匹配到的无效字符替换为单横线'-'
        result.replace(invalidChars, "-");
        return result.toStdString();
    }


    bool AddCadView::ProcessSingleCadData(std::vector<std::string>& cad_data_row, double unit_conversion_factor, jrsdata::CadStruct& one_cad)
    {
        if (!GetFloatData(cad_data_row, ui->cb_x->currentIndex(), one_cad.m_cad_x))
        {
            return false;
        }
        if (!GetFloatData(cad_data_row, ui->cb_y->currentIndex(), one_cad.m_cad_y))
        {
            return false;
        }
        if (!GetFloatData(cad_data_row, ui->cb_angle->currentIndex(), one_cad.m_cad_angle))
        {
            return false;
        }
        one_cad.m_cad_x *= unit_conversion_factor;
        one_cad.m_cad_y *= unit_conversion_factor;

        if (!GetStringData(cad_data_row, ui->cb_part_no->currentIndex(), one_cad.m_cad_part_no))
        {
            return false;
        }
        if (!GetComponentData(cad_data_row, ui->cb_component_id->currentIndex(), one_cad.m_cad_basic_part_no, one_cad.m_cad_part_no))
        {
            return false;
        }
        if (!GetStringData(cad_data_row, ui->cb_name->currentIndex(), one_cad.m_cad_name))
        {
            return false;
        }
        if (!GetSubboardId(cad_data_row, ui->cb_subboard_id->currentIndex(), one_cad.m_cad_sub_id))
        {
            return false;
        }

        one_cad.m_cad_name = ReplaceString(one_cad.m_cad_name);
        one_cad.m_cad_part_no = ReplaceString(one_cad.m_cad_part_no);
        one_cad.m_cad_angle = NormalizeAngle(one_cad.m_cad_angle);
        return true;
    }

    bool AddCadView::ConvertCADData(std::vector<std::vector<std::string>>& vec_cad_datas)
    {
        int start_index = ui->line_start->value();
        int end_index = ui->line_end->value();

        if (!ValidateInputRange(start_index, end_index, vec_cad_datas.size()))
        {
            JRSMessageBox_ERR("AddCadView", "开始行和结束行出错！", jrscore::MessageButton::Ok);
            return false;
        }

        double unit_conversion_factor = (ui->cbSizeUnit->currentIndex() == 0 ? 1 : 0.001);

        for (int i = start_index - 1; i < end_index && i < static_cast<int>(vec_cad_datas.size()); i++)
        {
            jrsdata::CadStruct one_cad;
            if (!ProcessSingleCadData(vec_cad_datas[i], unit_conversion_factor, one_cad))
            {
                return false;
            }
            m_cad_structs.push_back(one_cad);
        }
        for (auto& cad : m_cad_structs)
        {
            if (cad.m_cad_sub_id <= 0)
            {
                JRSMessageBox_ERR("AddCadView", QString("子板%1 ID小于等于0！").arg(cad.m_cad_name.c_str()).toStdString(), jrscore::MessageButton::Ok);
                return false;
            }
        }
        return true;
    }

    void AddCadView::SelectStartLine()
    {
        if (m_add_cad_model != nullptr)
        {
            std::vector<std::vector<std::string>> vec_data;
            m_add_cad_model->GetDataModel(vec_data);
            int current_row = ui->tw_result->currentIndex().row();
            if (current_row > -1 && current_row < vec_data.size())
            {
                ui->line_start->setValue(current_row + 1);
                std::vector<std::string> one_vec_data = vec_data.at(current_row);
                QStringList string_list_data;
                for (auto one_vec_data_data : one_vec_data)
                {
                    string_list_data.push_back(QString::fromStdString(one_vec_data_data));
                }
                SetComboList(string_list_data);
            }
        }
    }
    void AddCadView::SelectEndLine()
    {
        if (m_add_cad_model != nullptr)
        {
            std::vector<std::vector<std::string>> vec_data;
            m_add_cad_model->GetDataModel(vec_data);
            int current_row = ui->tw_result->currentIndex().row();
            if (current_row > -1 && current_row < vec_data.size())
            {
                ui->line_end->setValue(current_row + 1);
            }
        }
    }
}