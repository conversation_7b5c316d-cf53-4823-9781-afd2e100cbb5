/*****************************************************************
 * @file   resultstorageflow.h
 * @brief   结果保存流程
 * @details 以整板为单位进行结果保存，主要功能是调用数据层的功能将结果数据保存到数据库中
 * <AUTHOR>
 * @date 2024.11.21
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.11.21          <td>V2.0              <td>zhangyuyu      <td><EMAIL> <td> 3D AOI V2.0
 * </table>
 * @copyright 版权 CopyRight (C), 2024-2025.
 *********************************************************************/

#ifndef __JRSRESULTSTORAGEFLOW_H__
#define __JRSRESULTSTORAGEFLOW_H__

//PREBUILD
#include "workflowpch.h"
//STD
//Custom
//#include "workflowinterfaces.h"
#include "datamanager.h"
//Third
namespace jrsworkflow 
{
    class ResultStorageFlow :public IResultStorageFlow
    {
        public :
            ResultStorageFlow (const jrsdata::DataManagerPtr& data_manager_ptr_);
            ~ResultStorageFlow ();
            int SaveResult ( const InspectionResultBasePtr& insp_result_ptr_) override;
            int StopSaveResult ()override;
            int GetDataFromDataBase(jrsdata::QueryDatabaseResult& query_data) override;

        private:
            void Init ();

            jrsdata::DataManagerPtr data_manager_ptr;
    };

}

#endif // !__JRSRESULTSTORAGEFLOW_H__
