#ifndef DLGTEMPLATEANGLECORRECT_H
#define DLGTEMPLATEANGLECORRECT_H

#include <QDialog>
#include <QLabel>
#include <QGraphicsSceneWheelEvent>
#include <qgraphicsview.h>
#include <qgraphicsscene.h>
#include <qgraphicsitem.h>

#include <opencv2/opencv.hpp>

#include "graphicsView2d.h"

class ComGraphicsView : public QGraphicsView {
public:
    ComGraphicsView(QGraphicsScene* scene) : QGraphicsView(scene) 
    {

    }
protected:
    void showEvent(QShowEvent* event) override 
    {
        QGraphicsView::showEvent(event);
        this->fitInView(this->scene()->sceneRect(), Qt::KeepAspectRatio);
    }
};

class ComGraphicsRectItem : public QObject, public QGraphicsRectItem
{
    Q_OBJECT
public:
    ComGraphicsRectItem(QRectF rect, QGraphicsItem* parent = nullptr) : QGraphicsRectItem(rect, parent) 
    {
        setFlag(QGraphicsItem::ItemIsSelectable);
        setFlag(QGraphicsItem::ItemIsMovable);
        setTransformOriginPoint(boundingRect().center());
    }
    ComGraphicsRectItem(QGraphicsItem* parent = nullptr) : QGraphicsRectItem(parent) 
    {
        setFlag(QGraphicsItem::ItemIsSelectable);
        setFlag(QGraphicsItem::ItemIsMovable);
        setTransformOriginPoint(boundingRect().center());
    }

    void GetRect(QRectF& rect, float& angle) const;

signals:
    void angleChanged(const float angle);

protected:
    void paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget = nullptr) override 
    {
        painter->setPen(QPen(Qt::green, 1));
        painter->drawRect(this->rect());
        painter->setRenderHint(QPainter::Antialiasing, true);
        QGraphicsRectItem::paint(painter, option, widget);

    }

    void mousePressEvent(QGraphicsSceneMouseEvent* event) override 
    {
        setCursor(Qt::ClosedHandCursor);
        QGraphicsRectItem::mousePressEvent(event);
    }

    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override 
    {
        setCursor(Qt::ArrowCursor);
        QGraphicsRectItem::mouseReleaseEvent(event);
    }
};

namespace Ui 
{
class DlgCreateTemplate;
}

struct ColorParams;


class DlgCreateTemplate : public QDialog
{
    Q_OBJECT

public:
    explicit DlgCreateTemplate(QWidget *parent = nullptr);
    ~DlgCreateTemplate();

    void SetCreateTemplateData(const  std::vector<std::pair<int, cv::RotatedRect>>& rects, const QPixmap& image, const std::string& ipe_params);

    void SetExistedTemplate(const QPixmap& image, const std::string& ipe_params);
    void UpdataShowImage(const QPixmap& image);
    void resizeEvent(QResizeEvent* event) override;

signals:
    void SigCreateTemplate(const cv::RotatedRect& rect, const int& direction, const std::string& ipe_params);
    void SigUpdateTemplate(const std::string& ipe_params);

protected:
    void OnApply();
    void OnCreate();

private:
    Ui::DlgCreateTemplate *ui;

    std::string m_ipe_params;
    QRect       m_src_rect;
    GraphicsView2D*      m_graphicsView = nullptr;
    QGraphicsScene*      m_scene = nullptr;
    QGraphicsPixmapItem*              m_src_image = nullptr;
    std::vector<ComGraphicsRectItem*> m_rect_items;
    ComGraphicsRectItem*              m_selected_item = nullptr;

    void AngleValueChanged(double value);
    void UpdateEnable();
    void UpdateRectItem(const  std::vector<std::pair<int, cv::RotatedRect>>& rects);

public slots:
    void ImageChanged(const QPixmap& image, const std::string& params);

private slots:
    void AngleValueChangedSlot(const float angle);
    void SelecRectItem();

};

#endif // DLGTEMPLATEANGLECORRECT_H
