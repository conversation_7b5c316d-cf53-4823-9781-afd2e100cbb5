#include "rendertexture.h"
#include "painter.h"
#include "renderer.h"

#pragma warning(push, 1)
#pragma warning(disable : 4514 4365 4244 4800 4820 26495)
#include "opencv2/core/types.hpp"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
#pragma warning(pop)

#include <qopengl.h> // GL_RED

void ConvertDepthToGray(cv::Mat& grayImage, const cv::Mat& depthImage)
{
    // 检查输入深度图是否为 CV_32FC1 类型
    if (depthImage.type() != CV_32FC1) {
        return;
    }

    // Step 1: 归一化，将深度图的值归一化到 [0, 255] 的范围
    cv::Mat normalizedDepthImage;
    cv::normalize(depthImage, normalizedDepthImage, 0, 255, cv::NORM_MINMAX);

    // Step 2: 将归一化后的浮点型图像转换为 8 位图像
    normalizedDepthImage.convertTo(grayImage, CV_8UC1);
}

RenderTexture::RenderTexture()
    : RenderAbstract()
{
}

RenderTexture::~RenderTexture()
{
    // 正常情况下,纹理资源会在上下文销毁时关联事件一同销毁
    // 如果上下文被异常销毁就无法释放资源
    // 所以这里不做任何操作
}

void RenderTexture::Render()
{
    if (!IsRendered())
        return;
    if (!IsHaveRenerer())
        return;
    if (textures.empty())
        return;

    Painter p(renderer);

    SortTextures();
    // for (int j = 0; j < 10000; ++j)
    for (size_t i = 0; i < textures.size(); ++i)
    {
        p.DrawTexture(textures[i]);
        /*测试纹理旋转*/
        // textures[i]->angle -= (i + 1) * 1;
        // if (textures[i]->angle < 0)
        //     textures[i]->angle += 360;
    }

}

void RenderTexture::Destroy()
{
    Clear();
}

void RenderTexture::Clear()
{
    for (auto& texture : textures)
    {
        DeleteTexture(texture->id);
        delete texture;
        texture = nullptr;
    }
    textures.clear();
}

void RenderTexture::DeleteTexture(Texture2D* ptr)
{
    auto& vec = textures;
    vec.erase(std::remove_if(vec.begin(), vec.end(),
        [ptr, this](const Texture2D* obj)
        {
            if (obj == ptr)
            {
                DeleteTexture(obj->id);
                delete obj;
                obj = nullptr;
                return true;
            }
            return false;
        }),
        vec.end());
}

void RenderTexture::ClearTexture(int z)
{
    auto& vec = textures;
    vec.erase(std::remove_if(vec.begin(), vec.end(),
        [z, this](const Texture2D* obj)
        {
            if (obj->z == z)
            {
                DeleteTexture(obj->id);
                delete obj;
                obj = nullptr;
                return true;
            }
            return false;
        }),
        vec.end());
}

void RenderTexture::DeleteTexture(unsigned int texId)
{
    renderer->DeleteTexture(texId);
}

unsigned int RenderTexture::CreateTextureWithCVMat(const cv::Mat& mat, int x, int y, int z, float angle, bool is_draw_center)
{
    if (z < 0 || z>100)
        return 0;
    if (isnan(angle))
        return 0;

    unsigned int format = 0;
    cv::Mat tm;
    if (auto state = PrepareCvMatForTexture(format, tm, mat);!state)
        return 0;

    const auto& data = tm.data;
    int width = tm.cols;
    int height = tm.rows;

    unsigned int texture_id = 0;
    auto state = renderer->CreateTexture(texture_id, data, width, height, format);
    if (!state || texture_id == 0)
        return 0;
    if (!AddTexture(texture_id, format, width, height, x, y, z, angle, is_draw_center))
        return 0;

    return texture_id;
}

int RenderTexture::SetTextureZ(int z, const std::vector<unsigned int>& ids)
{
    for (auto& id : ids)
    {
        Texture2D* t2d = nullptr;
        for (size_t i = 0;i < textures.size();++i)
        {
            auto& t = textures[i];
            if (t && t->id == id)
            {
                t2d = t;
                break;
            }
        }
        if (!t2d)
            return 1;
        t2d->z = z;
    }
    return 0;
}

int RenderTexture::FindTextureZ(int& z, unsigned int id)
{
    for (size_t i = 0;i < textures.size();++i)
    {
        auto& t = textures[i];
        if (t && t->id == id)
        {
            z = t->z;
            return 0;
        }
    }
    return 1;
}

int RenderTexture::FindZTexture(std::vector<unsigned int>& v, int z)
{
    std::vector<unsigned int> tv;
    for (size_t i = 0;i < textures.size();++i)
    {
        auto& t = textures[i];
        if (t && t->z == z)
            tv.push_back(t->id);
    }
    tv.swap(v);

    return v.empty() ? 1 : 0;
}

bool RenderTexture::PrepareCvMatForTexture(unsigned int& format, cv::Mat& src, const cv::Mat& dst)
{
    if (!IsHaveRenerer())
        return false;
    if (dst.empty())
        return false;
    if (!dst.isContinuous())
        return false;

    src = dst.clone();
    switch (dst.type())
    {
    case CV_8UC3:
    {
        cv::cvtColor(dst, src, cv::COLOR_BGR2RGB);
    }
    break;
    case CV_8UC4:
    {
        cv::cvtColor(dst, src, cv::COLOR_BGRA2RGBA);
    }
    break;
    case CV_32FC1:
    {
        ConvertDepthToGray(src, dst);
    }
    break;
    case CV_64FC1:
    {
        ConvertDepthToGray(src, dst);
    }
    break;
    }

    format = 0;
    int channel = src.channels();
    switch (channel)
    {
    case 1:
        format = GL_RED;
        break;
    case 2:
        format = GL_RG;
        break;
    case 3:
        format = GL_RGB;
        break;
    case 4:
        format = GL_RGBA;
        break;
    default:
        return false;
    }
    return true;
}

bool RenderTexture::AddTexture(unsigned int id, unsigned int format, int width, int height, int x, int y, int z, float angle, bool is_draw_center)
{
    Texture2D* t2d = new Texture2D();
    t2d->width = width;
    t2d->height = height;
    t2d->id = id;
    t2d->format = format;
    t2d->x = x;
    t2d->y = y;
    t2d->z = z;
    t2d->angle = angle;
    t2d->is_draw_center = is_draw_center;

    t2d->uv[0].set(0, 0); // 左上角
    t2d->uv[1].set(0, 1);
    t2d->uv[2].set(1, 1);
    t2d->uv[3].set(1, 0);

    textures.emplace_back(t2d);
    return true;
}

bool RenderTexture::TextureCompareByZ(Texture2D* a, Texture2D* b)
{
    return a->z < b->z;
}

void RenderTexture::SortTextures()
{
    std::sort(textures.begin(), textures.end(), &RenderTexture::TextureCompareByZ);
}
