#include "systemparamview.h"
#include "coreapplication.h"
namespace jrsaoi
{
    SystemParamView::SystemParamView(QWidget *parent)
        : QWidget(parent)
        , ui(new Ui::systemparamviewClass())
        , ui_need_hide_controller({})
    {
        ui->setupUi(this);
        InitView();
        InitMember();
        InitConnect();
    }

    SystemParamView::~SystemParamView()
    {
        delete ui;
    }
    void SystemParamView::InitMember()
    {
        ui_map_value = 
        {
            {jrssettingparam::jrssysparam::SYSTEM_PARAM_DEMO,ui->line_edit_demo},
            {jrssettingparam::jrssysparam::SYSTEM_MOTION_PATH,ui->motion_setting_path},
        };
    }


    void SystemParamView::InitConnect()
    {
       connect(ui->btn_save_param, &QPushButton::clicked, this, &SystemParamView::SlotSaveSystem);
       /*  connect(ui->tbtn_choose_board_path, &QToolButton::clicked, this, &SystemParamView::SlotChooseDirPath);*/
        //connect(ui->tbtn_choose_standard_deivce_path, &QToolButton::clicked, this, &SystemParamView::SlotChooseDirPath);
       connect(ui->motion_setting_load, &QPushButton::clicked, this, [this]() {
           QString fileName = QFileDialog::getOpenFileName(this, "Open File", "", "JSON Files (*.json);;All Files (*.*)");
           if (!fileName.isEmpty()) {
               ui->motion_setting_path->setText(fileName);
           }
           });
    }
    void SystemParamView::SaveSystemParam()
    {
        Log_INFO("系统参数界面保存参数！");
        jrsdata::SettingParamMap system_params;

        for (const auto& value : ui_map_value)
        {
            jrsdata::SettingParam temp_param;
            const auto& ui_control = value.second;

            if (auto* lineEdit = dynamic_cast<QLineEdit*>(ui_control))
            {

				temp_param.param_name = value.first;
				temp_param.param_type = "string";
				temp_param.param_value = lineEdit->text().toStdString();

            }
            else if (auto* checkBox = dynamic_cast<QCheckBox*>(ui_control))
            {
				temp_param.param_name = value.first;
				temp_param.param_type = "bool";
				temp_param.param_value = std::to_string(checkBox->isChecked());
            }
            else if (auto* spinBox = dynamic_cast<QSpinBox*>(ui_control))
            {
				temp_param.param_name = value.first;
				temp_param.param_type = "int";
				temp_param.param_value = std::to_string(spinBox->value());
                
            }
            else if (auto* radioBtn = dynamic_cast<QRadioButton*>(ui_control)){
				temp_param.param_name = value.first;
				temp_param.param_type = "bool";
				temp_param.param_value = std::to_string(radioBtn->isChecked());
            }
            else if (auto* comboBox = dynamic_cast<QComboBox*>(ui_control)) {
				temp_param.param_name = value.first;
				temp_param.param_type = "int";
				temp_param.param_value = std::to_string(comboBox->currentIndex());
            }
            _system_params[value.first]=temp_param;
        }
        //_system_params = system_params;
    }

    void SystemParamView::UpdateView(const jrsdata::SettingParamMap& system_params_)
    {
		if (system_params_.empty())
		{
			Log_ERROR("参数为空 请检查!");
			return;
		}
        _system_params = system_params_;

        Log_INFO("从获取数据更新系统参数界面！");

      
        UpdateStaticParam();

        SetHideController(system_params_);

       
       
    }

    jrsdata::SettingParamMap SystemParamView::GetSystemParam()
    {
        return _system_params;
    }

    void SystemParamView::SlotChooseDirPath()
    {
        QString folder_path = QFileDialog::getExistingDirectory(this, "Select Folder");
        if (folder_path.isEmpty()) {
            return;
        }
        QToolButton* button = qobject_cast<QToolButton*>(sender());
        if (button) {
           /* QAction* action = button->defaultAction();
            if (action) {
                if (action->text() == "选择大图路径") {
                    ui->lineEdit_board_img_path->setText(folder_path);
                }
                else if (action->text() == "选择标准元件路径") {
                    //ui->lineEdit_standard_device_path->setText(folder_path);
                }
                else
                {
                }
            }*/
        }
    }

    void SystemParamView::InitView()
    {
        QIcon icon(":/image/save.svg");
        int size_icon = 15;
        QPixmap pixmap = icon.pixmap(size_icon, size_icon).scaledToWidth(size_icon, Qt::SmoothTransformation);
        ui->btn_save_param->setIcon(QIcon(pixmap));
        ui->btn_save_param->setIconSize(QSize(size_icon, size_icon));
        ui->btn_save_param->setFlat(true);
    }

   

    void SystemParamView::UpdateStaticParam()
    {
        Log_INFO("更新静态参数界面！");
     
        for (const auto& sys_param : _system_params)
        {
            auto it = ui_map_value.find(sys_param.first);
            if (it != ui_map_value.end())
            {
                auto& param_value = sys_param.second.param_value;
                QWidget* ui_control = it->second;
                if (auto* lineEdit = dynamic_cast<QLineEdit*>(ui_control))
                {
                    lineEdit->setText(QString::fromStdString(param_value));
                }
                else if (auto* checkBox = dynamic_cast<QCheckBox*>(ui_control))
                {
                    checkBox->setChecked((QString::fromStdString(param_value)).toInt());
                }
                else if (auto* spinBox = dynamic_cast<QSpinBox*>(ui_control))
                {
                    spinBox->setValue((QString::fromStdString(param_value)).toInt());
                }
                else if (auto* radioBtn = dynamic_cast<QRadioButton*>(ui_control)) {
                    radioBtn->setChecked((QString::fromStdString(param_value)).toInt());
                }
                else if (auto* comboBox = dynamic_cast<QComboBox*>(ui_control)) {
                    comboBox->setCurrentIndex((QString::fromStdString(param_value)).toInt());
                }
            }
            else
            {
                Log_ERROR(sys_param.first," 系统参数中不存在系统参数数据，请检查！");
            }
        }
       
    }

    void SystemParamView::SetHideController(const jrsdata::SettingParamMap& system_param_)
    {
        if (system_param_.empty())
        {

        }
      /*  
        jrsdata::MessModel model_temp = jrsdata::MessModel::GB;
        for(auto param: system_param_->system_params)
        {
            if (param.param_name == jrssystemparam::MESS_MODEL)
            {
                model_temp = static_cast<jrsdata::MessModel>(std::stoi(param.param_value));
            }
        }
        if(model_temp== jrsdata::MessModel::IHT || model_temp ==jrsdata::MessModel::OZT)
        {
          ui_need_hide_controller.push_back(ui->groupBox_user_check);                
          ui_need_hide_controller.push_back(ui->label_user_web_address);
          ui_need_hide_controller.push_back(ui->lineEdit_user_confirm_web);
          if(model_temp == jrsdata::MessModel::IHT)
          {
              ui->label_2->setText("Mess信息保存路径:");

              ui->lineEdit_mes_web->setText("D:/MES");

              ui->lineEdit_mes_web->setReadOnly(true);
            
          }
        }
        for(auto ui_controller: ui_need_hide_controller)
        {
            ui_controller->setHidden(true);
        }
        */
    }

    void SystemParamView::SlotSaveSystem()
    {
        SaveSystemParam();
        emit SigSaveSystem(_system_params);
    }

}
