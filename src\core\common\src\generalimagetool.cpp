#include"generalimagetool.h"

using cv::Mat;
using cv::Rect;
using cv::Point;
using cv::Point2f;
using std::vector;
using cv::Scalar;
using cv::RotatedRect;
namespace jrstool
{
	int CropAndPasteTool::CropImage(const Mat& input,
		const RotatedRect& rotate,
		const vector<Point>& contour,
		Mat& rect_mat,
		Mat& rect_mat_with_label)
	{
		if (input.empty()) return -1;
		if (rotate.size.area() == 0) return -1;
		cv::Point2f vertices[4];
		rotate.points(vertices);
		auto row_max = input.rows;
		auto col_max = input.cols;
		for (auto& point : vertices)
		{
			if (JudgePointOutImage(col_max, row_max, point)) return -1;
		}
		cv::Rect rotate_bounding_rect = rotate.boundingRect();
		cv::Mat big_to_small_mat = input(rotate_bounding_rect).clone();
		cv::RotatedRect big_to_small_rotate = rotate;
		big_to_small_rotate.center.x -= rotate_bounding_rect.x;
		big_to_small_rotate.center.y -= rotate_bounding_rect.y;
		auto current_contour = contour;
		for (auto& point : current_contour)
		{
			point.x -= rotate_bounding_rect.x;
			point.y -= rotate_bounding_rect.y;
		}
		GetRotateRigion(big_to_small_rotate, big_to_small_mat, rect_mat);
		if (!current_contour.empty())
		{
			vector<vector<Point>> label_pt = { current_contour };
			cv::Mat rect_mat_copy = cv::Mat::zeros(big_to_small_mat.size(), big_to_small_mat.type());
			cv::drawContours(rect_mat_copy, label_pt, -1, cv::Scalar(1, 1, 1), -1);
			GetRotateRigion(big_to_small_rotate, rect_mat_copy, rect_mat_with_label);
		}
		else
		{
			cv::Mat rect_mat_copy = cv::Mat::ones(rect_mat.size(), rect_mat.type());
			GetRotateRigion(big_to_small_rotate, rect_mat_copy, rect_mat_with_label);
		}
		return 0;
	}
	int CropAndPasteTool::CropImage(CropParams& params)
	{
		if (params.input.empty()) return -1;
		if (params.rotate.size.area() == 0) return -1;
		cv::Point2f vertices[4];
		params.rotate.points(vertices);
		auto row_max = params.input.rows;
		auto col_max = params.input.cols;
		for (auto& point : vertices)
		{
			if (JudgePointOutImage(col_max, row_max, point)) return -1;
		}
		cv::Rect rotate_bounding_rect = params.rotate.boundingRect();
		cv::Mat big_to_small_mat = params.input(rotate_bounding_rect).clone();
		cv::RotatedRect big_to_small_rotate = params.rotate;
		big_to_small_rotate.center.x -= rotate_bounding_rect.x;
		big_to_small_rotate.center.y -= rotate_bounding_rect.y;
		auto current_contour = params.contour;
		for (auto& point : current_contour)
		{
			point.x -= rotate_bounding_rect.x;
			point.y -= rotate_bounding_rect.y;
		}
		GetRotateRigion(big_to_small_rotate, big_to_small_mat, params.rect_mat);
		if (!current_contour.empty())
		{
			vector<vector<Point>> label_pt = { current_contour };
			cv::Mat rect_mat_copy = cv::Mat::zeros(big_to_small_mat.size(), big_to_small_mat.type());
			cv::drawContours(rect_mat_copy, label_pt, -1, cv::Scalar(1, 1, 1), -1);
			GetRotateRigion(big_to_small_rotate, rect_mat_copy, params.rect_mat_with_label);
		}
		else
		{
			cv::Mat rect_mat_copy = cv::Mat::ones(params.rect_mat.size(), params.rect_mat.type());
			GetRotateRigion(big_to_small_rotate, rect_mat_copy, params.rect_mat_with_label);
		}
		return 0;
	}
	int CropAndPasteTool::CropImage(const Mat& input,
		const Rect& rect, Mat& rect_mat)
	{
		if (input.empty()) return -1;

		rect_mat = Mat(rect.height, rect.width, input.type());
		cv::Scalar nan_value;
		if (input.channels() == 3)
		{
			nan_value = Scalar(std::numeric_limits<float>::quiet_NaN(),
				std::numeric_limits<float>::quiet_NaN(),
				std::numeric_limits<float>::quiet_NaN());
		}
		else
		{
			nan_value = Scalar(std::numeric_limits<float>::quiet_NaN());
		}
		rect_mat.setTo(nan_value);

		cv::Rect image_bounds(0, 0, input.cols, input.rows);
		cv::Rect valid_rect = rect & image_bounds;
		if (!valid_rect.empty())
		{
			cv::Point offset(valid_rect.x - rect.x, valid_rect.y - rect.y);
			input(valid_rect).copyTo(rect_mat(cv::Rect(offset.x, offset.y, valid_rect.width, valid_rect.height)));
		}
		return 0;
		/*
		if (input.empty() || rect.empty()) return -1;
		if (JudgePointOutImage(input.cols, input.rows, rect.br())) return -1;
		if (JudgePointOutImage(input.cols, input.rows, rect.tl())) return -1;

		rect_mat = input(rect).clone();
		return 0;*/
	}
	int CropAndPasteTool::CropImage(const Mat& input, const RotatedRect& rotate, Mat& rect_mat)
	{
		if (input.empty()) return -1;
		if (rotate.size.area() == 0) return -1;
		cv::Point2f vertices[4];
		rotate.points(vertices);
		auto row_max = input.rows;
		auto col_max = input.cols;
		for (auto& point : vertices)
		{
			if (JudgePointOutImage(col_max, row_max, point)) return -1;
		}
		cv::Rect rotate_bounding_rect = rotate.boundingRect();
		cv::Mat big_to_small_mat = input(rotate_bounding_rect).clone();
		cv::RotatedRect big_to_small_rotate = rotate;
		big_to_small_rotate.center.x -= rotate_bounding_rect.x;
		big_to_small_rotate.center.y -= rotate_bounding_rect.y;
		GetRotateRigion(rotate, input, rect_mat);
		return 0;
	}
	int CropAndPasteTool::PasteImage(const Mat& input_small,
		const RotatedRect& rotate, Mat& out_big)
	{
		if (input_small.empty() || out_big.empty() || rotate.size.area() == 0)
		{
			return -1;
		}
		cv::Rect rotate_bounding_rect = rotate.boundingRect();
		cv::Mat big_to_small_mat = out_big(rotate_bounding_rect).clone();

		Point2f vertices[4];
		rotate.points(vertices);
		for (int i = 0; i < 4; ++i)
		{
			vertices[i].x -= rotate_bounding_rect.x;
			vertices[i].y -= rotate_bounding_rect.y;
		}
		Point2f src_pts[3] = { Point2f(0, 0), Point2f(float(input_small.cols - 1), 0), Point2f(0, float(input_small.rows - 1)) };
		Point2f dst_pts[3] = { vertices[0], vertices[1], vertices[3] };
		Mat affine_matrix = getAffineTransform(src_pts, dst_pts);
		Mat transformed_small(big_to_small_mat.size(), big_to_small_mat.type(), Scalar::all(0));
		warpAffine(input_small, transformed_small, affine_matrix, transformed_small.size(), cv::INTER_LINEAR, cv::BORDER_TRANSPARENT);
		Mat mask(big_to_small_mat.size(), CV_8U, Scalar(0));
		vector<Point> contour;
		contour.push_back(vertices[0]);
		contour.push_back(vertices[1]);
		contour.push_back(vertices[2]);
		contour.push_back(vertices[3]);
		fillConvexPoly(mask, contour, Scalar(255));
		transformed_small.copyTo(big_to_small_mat, mask);

		big_to_small_mat.copyTo(out_big(rotate_bounding_rect));
		return 0;
	}
	int CropAndPasteTool::PasteImage(const Mat& input_small, const Rect& rect,
		Mat& out_big)
	{
		if (input_small.empty() || out_big.empty() || rect.empty()) return -1;
		{
			return -1;
		}
		input_small.copyTo(out_big(rect));
		return 0;
	}
	cv::Mat CropAndPasteTool::GenerateLowResolutionImage(const cv::Mat& input_image, float scale_factor)
	{
		if (scale_factor <= 0.0 || scale_factor > 1.0)
		{
			std::cerr << "scale factor should be in (0,1)" << std::endl;
			return cv::Mat();
		}

		if (input_image.empty()) 
		{
			std::cerr<<"input image is empty"<<std::endl;
			return cv::Mat();
		}

		// 计算新的尺寸
		int new_width = static_cast<int>(input_image.cols * scale_factor);
		int new_height = static_cast<int>(input_image.rows * scale_factor);

		// 调整图像大小
		cv::Mat resized_image;
		cv::resize(input_image, resized_image, cv::Size(new_width, new_height), 0, 0, cv::INTER_AREA);

		return resized_image;
	}
	CropAndPasteTool::CropAndPasteTool()
	{
	}
	CropAndPasteTool::~CropAndPasteTool()
	{
	}
	int CropAndPasteTool::GetRotateRigion(const RotatedRect& rotate,
		const Mat& input_image, Mat& output_image)
	{
		cv::Point2f vertices[4];
		rotate.points(vertices);
		int width = static_cast<int>(rotate.size.width);
		int height = static_cast<int>(rotate.size.height);
		width = MIN(width, input_image.cols);
		height = MIN(height, input_image.rows);

		cv::Point2f src_vertices[3] =
		{
			vertices[0],
			vertices[1],
			vertices[2]
		};
		cv::Point2f dst_vertices[3] =
		{
			cv::Point2f(0.0f, static_cast<float>(height)),
			cv::Point2f(0.0f, 0.0f),
			cv::Point2f(static_cast<float>(width), 0.0f)
		};
		cv::Mat M = cv::getAffineTransform(src_vertices, dst_vertices);

		cv::warpAffine(input_image, output_image, M, cv::Size(width, height), cv::INTER_LINEAR);
		return 0;
	}
	bool CropAndPasteTool::JudgePointOutImage(int max_x, int max_y,
		const cv::Point2f& test_point)
	{
		bool pt_is_out_image = false;
		auto pt_x = test_point.x;
		auto pt_y = test_point.y;
		if (pt_x<0 || pt_y<0 || pt_x>max_x || pt_y>max_y)
		{
			pt_is_out_image = true;
		}
		return pt_is_out_image;
	}

}