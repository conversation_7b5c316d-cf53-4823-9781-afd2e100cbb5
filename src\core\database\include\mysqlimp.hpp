﻿/*****************************************************************//**
 * @file   mysqlImp.h
 * @brief  实现操作MySQL类，并且定义了一些必要的结构体。
 * @details
 * <AUTHOR>
 * @date   April 2024
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>April 2024         <td>         <td>HJC                       <td><EMAIL> <td>
 *   @copyright 版权 CopyRight (C), 2024-2025.
 * *********************************************************************/
#ifndef _MYSQLIMP_H_
#define _MYSQLIMP_H_
 //std
#include <iostream>
#include <any>

//custom
#include "coreapplication.h"
#include "bdatabase.h"
#include "tools.h"
#pragma warning (push,0)
#include "dbng.hpp"
#include "type_mapping.hpp"
#include "mysql.hpp"
#pragma warning(pop)


#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

// 将多个字段名变成字符串："field1,field2,field3"
#define JRS_MAKE_NAMES(...) JRS_MAKE_NAMES_IMPL(__VA_ARGS__)
#define JRS_MAKE_NAMES_IMPL(...) #__VA_ARGS__

namespace jrsdatabase
{
    //    /**
    //    * @fun JRSREFLECTIONWITHNAME
    //    * @brief 注册结构体、表明、及字段信息
    //    * @param STRUCT_NAME 结构体名称
    //    * @param TABLE_NAME 表名称
    //    * @param ... 结构体中的所有字段名
    //    * @date 2024.4.8
    //    * <AUTHOR>
    //    */
    //#define JRSREFLECTIONWITHNAME(STRUCT_NAME, TABLE_NAME, ...)            \
    //REFLECTION_WITH_NAME(STRUCT_NAME, TABLE_NAME, __VA_ARGS__)
        /**
        * @fun JRSREGISTER_AUTO_KEY
        * @brief 注册自增关键字段
        * @param STRUCT_NAME 结构体名称
        * @param KEY 关键字字段名
        * @date 2024.4.8
        * <AUTHOR>
        */
#define JRSREGISTER_AUTO_KEY(STRUCT_NAME,KEY)\
	  REGISTER_AUTO_KEY(STRUCT_NAME, KEY)
        /**
        * @fun JRSREFLECTION
        * @brief 注册结构体及其字段名，表名默认为结构体名称
        * @param STRUCT_NAME 结构体名称
        * @param ... 结构体中的所有字段名
        * @date 2024.4.8
        * <AUTHOR>
        */
#define JRSREFLECTION(STRUCT_NAME, ...)	\
    YLT_REFL(STRUCT_NAME,__VA_ARGS__)
        /**
        * @fun JRSREGISTER_CONFLICT_KEY
        * @brief 注册冲突主键，update 根据该字段进行更新
        * @param STRUCT_NAME 结构体名称
        * @param ... 结构体中的所有字段名
        * @date 2024.6.12
        * <AUTHOR>
        */
#define JRSREGISTER_CONFLICT_KEY(STRUCT_NAME, ...) \
    REGISTER_CONFLICT_KEY(STRUCT_NAME, __VA_ARGS__)


    inline std::unordered_map<std::string_view, std::string_view>
        _table_key_fields;

    inline int add_key_field(std::string_view key_, std::string_view values_)
    {
        _table_key_fields.emplace(key_, values_);
        return 0;
    }


#define JRSREFLECTION_KEY(STRUCT_NAME, ...) \
    static const int _dummy_##STRUCT_NAME = add_key_field(#STRUCT_NAME, JRS_MAKE_NAMES(__VA_ARGS__))




    /** @brief 更改数据库字符集 */
    struct MySqlAlterDatabase {
        std::string db_name;  /**< 数据库名称 */
        std::string character_set; /**< 数据库字符集 */
        std::string collate;/**< 数据库字符集 */
    };

    /** @brief 查询表字段属性 */
    using TableFields = std::vector<std::tuple<std::string, std::string, std::string, std::string, std::string, std::string>>;
    /** @brief sql 索引类型 */
    enum class MySQLIndexType {
        FULLTEXT,
        UNIQUE,
        INDEX,
        PRIMARY_KEY
    };
    static std::unordered_map<MySQLIndexType, std::string> index_type_strings = {
        {MySQLIndexType::FULLTEXT, "FULLTEXT"},
        {MySQLIndexType::UNIQUE, "UNIQUE"},
        {MySQLIndexType::INDEX, "INDEX"},
        {MySQLIndexType::PRIMARY_KEY, "PRIMARY KEY"}
    };
    /** @brief MySQL字段数据类型 */
    enum class MySQLDataType {
        INT,
        VARCHAR,
        CHAR,
        TEXT,
        LONG,
        TINYINT,
        BLOB,
        DOUBLE,
        LONGBLOB,
        DATETIME
    };

    /** @brief 更改字段数据类型参数 */
    struct MySQLAlterTableFieldType {
        MySQLDataType field_type_name; /**< 目标数据类型 */
        int length; /**< 用于 VARCHAR 和 CHAR 类型的长度 */
        MySQLAlterTableFieldType() :field_type_name(MySQLDataType::INT), length(0) {};
    };

    /** @brief 设置表的字符集参数 */
    struct MySQLAlterTableCharacterSet {
        std::string character_set; /**< 字符集 */
        std::string collate; /**< 校对规则 */
        MySQLAlterTableCharacterSet() : character_set(""), collate("") {};
    };


    /** @brief 实现控制MySql*/
    class MySqlImp :public AbsDatabase<ormpp::dbng<ormpp::mysql>>
    {
    public:
        MySqlImp()
        {
            m_connPtr_ = std::make_shared< ormpp::dbng<ormpp::mysql>>();
        }
        ~MySqlImp()
        {

        }
        /** < 检查数据库是否存在，如果不存在则创建数据库 */
        bool CreateDatabase(const std::string& db_host, const std::string& user_hame, const std::string& password, const std::string& db_name) {
            MYSQL* conn = mysql_init(nullptr);

            if (conn == nullptr) {
                std::cerr << "mysql_init() failed." << std::endl;
                return false;
            }

            if (mysql_real_connect(conn, db_host.c_str(), user_hame.c_str(), password.c_str(), nullptr, 0, nullptr, 0) == nullptr) {
                std::cerr << "mysql_real_connect() failed: " << mysql_error(conn) << std::endl;
                mysql_close(conn);
                return false;
            }

            std::string create_database_query = "CREATE DATABASE IF NOT EXISTS " + db_name;
            if (mysql_query(conn, create_database_query.c_str())) {
                std::cerr << "CREATE DATABASE failed: " << mysql_error(conn) << std::endl;
                mysql_close(conn);
                return false;
            }

            mysql_close(conn);
            return true;
        }


        /**
         * @fun CreateDatabase
         * @brief 创建数据库
         * @param db_name 数据库名称
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int CreateDatabase(const std::string& db_name) override
        {
            if (db_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "CREATE DATABASE IF NOT EXISTS " + db_name;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun DeleteDatabase
         * @brief 删除数据库
         * @param db_name 数据库名称
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int DeleteDatabase(const std::string& db_name) override
        {
            if (db_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "DROP DATABASE IF EXISTS " + db_name;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun ShowDatabases
         * @brief 显示所有数据库
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int ShowDatabases(std::vector<std::tuple<std::string>>& res) override
        {
            std::string sql = "SHOW DATABASES";
            res = m_connPtr_->query<std::tuple<std::string>>(sql);
            return (res.size() > 0) ? jrscore::AOI_OK : CheckError();
        }

        template<typename T>
        int AlterTablePrimaryKey()
        {
            std::string sql = "ALTER TABLE ";
            auto table_name = ormpp::get_struct_name<T>();
            sql += table_name;
            sql.append(" ADD PRIMARY KEY (");

            auto struct_name = typeid(T).name();
            auto str_vec = jrscore::AOITools::SplitString(struct_name, ':');
            if (str_vec.empty())
            {

                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }
            auto key_fields_map = _table_key_fields.find(str_vec.back());

            if (key_fields_map != _table_key_fields.end()) {
                const auto& fields = key_fields_map->second;
                sql.append(fields);
                sql.append(");");

                if (m_connPtr_->execute(sql))
                {
                    return jrscore::AOI_OK;
                }
                else
                {
                    //Log_ERROR("create multiple keys error:", m_connPtr_->get_last_error());
                    return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
                }
            }
            else
            {
                //logError("未注册主键，请检查，如果没有请忽略");
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;
            }

        }


        /**
         * @fun AlterTableName
         * @brief 更改表名称
         * @param t_name 旧表名
         * @param t_new_name 新表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int AlterTableName(const std::string& t_name, const std::string& t_new_name) override
        {
            if (t_name == "" || t_new_name == "") {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;  //传入参数错误
            }
            // ALTER TABLE <旧表名> RENAME TO <新表名>;
            std::string sql = "ALTER TABLE " + t_name + " RENAME TO " + t_new_name;
            if (m_connPtr_->execute(sql)) {
                return jrscore::AOI_OK;
            }
            else {
                return CheckError();
            }

        }
        /**
        * @fun AlterTableFieldName
        * @brief 更改表中字段名称
        * @param t_name 表名
        * @param f_name 旧字段名
        * @param f_new_name 新字段名
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int AlterTableFieldName(const std::string& t_name, const std::string& f_name, const std::string& f_new_name) override
        {
            if (t_name == "" || f_name == "") {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;  //chuan
            }
            std::string sql = "ALTER TABLE " + t_name + " CHANGE " + f_name + " " + f_new_name;

            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }

        /**
        * @fun DeleteTableField
        * @brief 删除表中某个字段
        * @param t_name 表名
        * @param f_name 字段名
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int DeleteTableField(const std::string& t_name, const std::string& f_name) override
        {
            if (t_name == "" || f_name == "") {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "ALTER TABLE " + t_name + " DROP " + f_name;

            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
        * @fun QueryCountDataInTable
        * @brief 查询表中已有数据总数
        * @param t_name 表名
        * @param total 查询总数
        * @return 成功:jrscore::AOI_OK，失败:-1;
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int QueryCountDataInTable(const std::string& t_name, uint64_t& total) override
        {
            if (t_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            total = std::stoull(std::get<0>(m_connPtr_->query<std::tuple<std::string>>("SELECT COUNT(*) FROM `" + t_name + "`")[0]));
            return (total > 0) ? jrscore::AOI_OK : CheckError();

        }
        /**
         * @fun UpdateCustomFieldsData
         * @brief  获取自定义字段数据的sql语句
         * @param table_name
         * @param fields_and_new_value_map
         * @param condition
         * @return
         * <AUTHOR>
         * @date 2025.2.11
         */
        virtual std::string  GetUpdateCustomFieldsDataSQL(const std::string& table_name,
            const std::map<std::string, std::string>& fields_and_new_value_map,
            const std::string& condition)override
        {

            std::string sql = "UPDATE " + table_name + " SET ";
            // 构建 UPDATE 语句部分
            for (auto fieldIt = fields_and_new_value_map.begin(); fieldIt != fields_and_new_value_map.end(); ++fieldIt) {
                sql += fieldIt->first + " = '" + fieldIt->second + "', ";
            }
            sql = sql.substr(0, sql.size() - 2); // 移除最后一个逗号和空格
            //sql += ";";
            // 添加 WHERE 语句部分
            sql += " WHERE " + condition + ";";
            return sql;

        }

        virtual int UpdateCustomFieldsData(const std::string& table_name, const std::map<std::string, std::string>& fields_and_new_value_map, const std::string& condition) override
        {
            {
                auto sql = GetUpdateCustomFieldsDataSQL(table_name, fields_and_new_value_map, condition);

                // 执行 SQL 更新
                return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
            }
        }
        /**
         * @fun AddIndexImp
         * @brief 实现添加索引
         * @param table_name 表名
         * @param ptr  索引类型
         * @param index_name 索引名称
         * @param fields 字段集
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int AddIndexImp(const std::string& table_name, const std::shared_ptr<void> ptr, const std::string& index_name, const std::vector<std::string>& fields) override {
            MySQLIndexType index_type = *std::static_pointer_cast<MySQLIndexType>(ptr);
            std::string sql = "ALTER TABLE `" + table_name + "` ADD " + index_type_strings[index_type] + " index_" + index_name + " (";

            // 遍历字段列表
            for (auto it = fields.begin(); it != fields.end(); ++it) {
                sql += "`" + *it + "`";
                if (std::next(it) != fields.end()) {
                    sql += ", ";
                }
            }
            sql += ");";

            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }

        /**
         * @fun DeleteDataTable
         * @brief 删除表
         * @param table_name 表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int DeleteDataTable(const std::string& table_name) override
        {
            if (table_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "DROP TABLE IF EXISTS " + table_name;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun ReflashDataTable
         * @brief 刷新数据表
         * @param table_name 表名
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int ReflashDataTable(const std::string& table_name) override
        {
            if (table_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "FLUSH TABLES " + table_name;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun ShowTables
         * @brief 显示数据库中所有数据表
         * @return 当前数据库中所有表名
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int ShowTables(std::vector<std::tuple<std::string>>& res) override
        {
            std::string sql = "SHOW TABLES";
            res = m_connPtr_->query<std::tuple<std::string>>(sql);
            return (res.size() > 0) ? jrscore::AOI_OK : CheckError();
        }




    private:

        /**
         * @fun GetFieldType
         * @brief 获取字段类型
         * @param field_type 字段类型数据
         * @return 相应的字符串
         * @date 2024.4.3
         * <AUTHOR>
         */
        std::string GetFieldType(const MySQLAlterTableFieldType& field_type)
        {

            std::string res = "";
            // 打印列的类型和长度
            switch (field_type.field_type_name) {
            case MySQLDataType::INT:
                res = "INT";
                break;
            case MySQLDataType::VARCHAR:
                res = " VARCHAR(" + std::to_string(field_type.length) + ")";
                break;
            case MySQLDataType::CHAR:
                res = " CHAR(" + std::to_string(field_type.length) + ")";
                break;
            case MySQLDataType::TEXT:
                res = "TEXT";
                break;
            case MySQLDataType::LONG:
                res = "LONG";
                break;
            case MySQLDataType::TINYINT:
                res = "TINYINT";
                break;
            case MySQLDataType::BLOB:
                res = "BLOB";
                break;
            case MySQLDataType::DOUBLE:
                res = "DOUBLE";
                break;
            case MySQLDataType::LONGBLOB:
                res = "LONGBLOB";
                break;
            case MySQLDataType::DATETIME:
                res = "DATETIME";
                break;
            default:
                res = "";
            }
            return res;
        }
        /**
         * @fun AlterDatabaseImpl
         * @brief 实现更改数据库字符集
         * @param value 字符集数据
         * @return 成功:jrscore::AOI_OK，失败:-1
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int AlterDatabaseImpl(const std::shared_ptr<void> value) override
        {
            if (value == nullptr) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            MySqlAlterDatabase alterDatabase = *std::static_pointer_cast<MySqlAlterDatabase>(value);
            std::string sql = "ALTER DATABASE " + alterDatabase.db_name
                + " DEFAULT CHARACTER SET " + alterDatabase.character_set
                + " DEFAULT COLLATE " + alterDatabase.collate;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }
        /**
         * @fun QueryTableFieldsImpl
         * @brief 实现查询表字段
         * @param table_name 表名
         * @return 表字段数据
         * @date 2024.4.3
         * <AUTHOR>
         */
        virtual int QueryTableFieldsImpl(const std::string& table_name, std::any& res) override
        {
            if (table_name.empty()) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            std::string sql = "SHOW COLUMNS FROM " + table_name;
            res = m_connPtr_->query<std::tuple< std::string, std::string, std::string, std::string, std::string, std::string>>(sql);
            return (std::any_cast<std::vector<std::tuple< std::string, std::string, std::string, std::string, std::string, std::string>>>(res).size() > 0) ? jrscore::AOI_OK : CheckError();
        }

        /**
        * @fun AlterTableFieldTypeImp
        * @brief 实现更改表中某字段数据类型
        * @param t_name 表名
        * @param f_name 字段名
        * @param type 数据类型
        * @return  成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int AlterTableFieldTypeImp(const std::string& t_name, const std::string& f_name, const std::shared_ptr<void> ptr) override
        {

            if (t_name == "" || f_name == "" || ptr == nullptr) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            MySQLAlterTableFieldType dt = *std::static_pointer_cast<MySQLAlterTableFieldType>(ptr);
            std::string sql = "ALTER TABLE " + t_name + " MODIFY " + f_name + " " + GetFieldType(dt);

            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();

        }
        /**
        * @fun AddTableField
        * @brief 在表中添加新字段
        * @param t_name 表名
        * @param f_new_name 新字段名
        * @param type 新字段数据类型
        * @param is_first 默认表头添加，否则添加到f_name字段之后
        * @param f_name 在那个字段后添加表名
        * @return 成功:jrscore::AOI_OK，失败:-1
        * @date 2024.4.3
        * <AUTHOR>
        */
        virtual int AddTableFieldImp(const std::string& t_name, const std::string& f_new_name, const std::shared_ptr<void> ptr, const bool is_first, const std::string& f_name)  override
        {
            if (t_name == "" || f_new_name == "" || ptr == nullptr) {
                return jrscore::DataManagerError::E_AOI_DB_PARAM_EMPTY;
            }
            MySQLAlterTableFieldType dt = *std::static_pointer_cast<MySQLAlterTableFieldType>(ptr);
            // ALTER TABLE <表名> ADD <新字段名><数据类型>

            std::string sql = "ALTER TABLE " + t_name + " ADD " + f_new_name + " " + GetFieldType(dt);
            if (is_first)
                sql += " FIRST";
            else {
                if (f_name == "")return -1; //暂时先返回-1~~~~~~~~~~~~~~
                sql += " AFTER " + f_name;
            }
            std::cout << sql << std::endl;
            return m_connPtr_->execute(sql) ? jrscore::AOI_OK : CheckError();
        }

        /**
        * @fun CheckError
        * @brief 返回指定错误码
        * @return
        * @date 2024.4.15
        * <AUTHOR>
        */
        virtual int CheckError() override
        {
            std::string str_err = m_connPtr_->get_last_error();
            if (m_connPtr_->has_error() || !str_err.empty()) {
                for (const auto& [error_str, error_code] : error_map) {
                    if (str_err.find(error_str) != std::string::npos) {
                        return error_code;
                    }
                }
                Log_ERROR("对数据库操作出现错误，错误类型未知:", str_err);
                return jrscore::DataManagerError::E_AOI_DATA_UNKNOWN;/**< 未知错误  */
            }
            // 没有查询到任何数据  sql语句可能有误！
            return jrscore::AOI_OK;
        }

        /* 定义错误信息和对应错误码的映射*/
        const std::unordered_map<std::string, int> error_map = {
            {"Access denied for user", jrscore::DataManagerError::E_AOI_DB_UER_OR_PASSWORD_ERROR},
            {"Can't connect to MySQL server on", jrscore::DataManagerError::E_AOI_DB_CONNECT_OUT},
            {"Lost connection to MySQL server",jrscore::DataManagerError::E_AOI_DB_DISCONNECT},
            {"Got a packet bigger than 'max_allowed_packet' bytes", jrscore::DataManagerError::E_AOI_DB_MAX_PACKET},
            {"You have an error in your SQL syntax", jrscore::DataManagerError::E_AOI_DB_MYSQL_CODE},
            {"Unknown database", jrscore::DataManagerError::E_AOI_DB_UNFIND_DB},
            {"Table", jrscore::DataManagerError::E_AOI_DB_UNFIND_TABLE},
            {"Unknown column",jrscore::DataManagerError::E_AOI_DB_UNFIND_COLUMN},
            {"Duplicate key name",jrscore::DataManagerError::E_AOI_DB_DUPLICATE_KEY_NAME},
            {"Multiple primary key defined",jrscore::DataManagerError::E_AOI_DB_DUPLICATE_KEY_NAME},
            {"Event",jrscore::DataManagerError::E_AOI_DB_EVENT_ALREADY_EXISTS}
        };



    };
    using MYSQLImpPtr = std::unique_ptr<MySqlImp>;


};

#endif //！_MYSQLOPERATOR_H_