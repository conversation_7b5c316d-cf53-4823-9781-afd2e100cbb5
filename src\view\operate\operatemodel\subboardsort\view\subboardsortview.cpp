//Custom
#include "subboardsortview.h"
#include "ui_subboardsort.h"
#include "subboardsortbase.h"
//QT
#include <QToolButton>
SubboardSortView::SubboardSortView(QWidget* parent /*= Q_NULLPTR*/)
    : QWidget(parent),
    ui(new Ui::SubboardSort)
{
    Init();
}

SubboardSortView::~SubboardSortView()
{

}

void SubboardSortView::Init()
{
    InitMenber();
    InitView();
    InitConnect();
}

void SubboardSortView::InitView()
{
    // 设置窗口标志
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);

    // 初始化 UI 组件
    ui->setupUi(this);

    SetToolButtonIcon(ui->btn_left_top_to_right_bottom_s, ":image/left_top_to_right_bottom_s.png");
    SetToolButtonIcon(ui->btn_right_top_to_left_bottom_s, ":image/right_top_to_left_bottom_s.png");
    SetToolButtonIcon(ui->btn_left_bottom_to_right_top_s, ":image/left_bottom_to_right_top_s.png");
    SetToolButtonIcon(ui->btn_right_bottom_to_left_top_s, ":image/right_bottom_to_left_top_s.png");

    SetToolButtonIcon(ui->btn_left_top_to_right_bottom_z, ":image/left_top_to_right_bottom_z.png");
    SetToolButtonIcon(ui->btn_right_top_to_left_bottom_z, ":image/right_top_to_left_bottom_z.png");
    SetToolButtonIcon(ui->btn_left_bottom_to_right_top_z, ":image/left_bottom_to_right_top_z.png");
    SetToolButtonIcon(ui->btn_right_bottom_to_left_top_z, ":image/right_bottom_to_left_top_z.png");


    SetToolButtonIcon(ui->btn_left_top_to_right_bottom_vertical_s, ":image/left_top_to_right_bottom_vertical_s.png");
    SetToolButtonIcon(ui->btn_right_top_to_left_bottom_vertical_s, ":image/right_top_to_left_bottom_vertical_s.png");
    SetToolButtonIcon(ui->btn_left_bottom_to_right_top_vertical_s, ":image/left_bottom_to_right_top_vertical_s.png");
    SetToolButtonIcon(ui->btn_right_bottom_to_left_top_vertical_s, ":image/right_bottom_to_left_top_vertical_s.png");

    SetToolButtonIcon(ui->btn_left_top_to_right_bottom_vertical_z, ":image/left_top_to_right_bottom_vertical_z.png");
    SetToolButtonIcon(ui->btn_right_top_to_left_bottom_vertical_z, ":image/right_top_to_left_bottom_vertical_z.png");
    SetToolButtonIcon(ui->btn_left_bottom_to_right_top_vertical_z, ":image/left_bottom_to_right_top_vertical_z.png");
    SetToolButtonIcon(ui->btn_right_bottom_to_left_top_vertical_z, ":image/right_bottom_to_left_top_vertical_z.png");

    //ui->btn_confirm->setHidden(true);
}

void SubboardSortView::InitMenber()
{
    _subboard_sort_param = std::make_shared<jrsdata::SubboardSortParam>();
}

void SubboardSortView::InitConnect()
{
    using Direction = jrsdata::SubboardSortParam::SubBoardDirection;
    using Shape = jrsdata::SubboardSortParam::SortShape;

    auto ConnectButton = [this](QToolButton* button, std::optional<Direction> direction, std::optional<Shape> shape) {
        connect(button, &QToolButton::clicked, this, [=]() {
            if (direction.has_value())
                _subboard_sort_param->subboard_direction = direction.value();
            if (shape.has_value())
                _subboard_sort_param->sort_shape = shape.value();
            _subboard_sort_param->event = jrsdata::SubboardSortParam::Event::REGULAR_SORT;
            emit SigUpdateSubboardSort(*_subboard_sort_param.get());
            });
        };

    ConnectButton(ui->btn_left_top_to_right_bottom_s, Direction::LeftTopToRightBottom, Shape::S);
    ConnectButton(ui->btn_right_top_to_left_bottom_s, Direction::RightTopToLeftBottom, Shape::S);
    ConnectButton(ui->btn_left_bottom_to_right_top_s, Direction::LeftBottomToRightTop, Shape::S);
    ConnectButton(ui->btn_right_bottom_to_left_top_s, Direction::RightBottomToLeftTop, Shape::S);

    ConnectButton(ui->btn_left_top_to_right_bottom_z, Direction::LeftTopToRightBottom, Shape::Z);
    ConnectButton(ui->btn_right_top_to_left_bottom_z, Direction::RightTopToLeftBottom, Shape::Z);
    ConnectButton(ui->btn_left_bottom_to_right_top_z, Direction::LeftBottomToRightTop, Shape::Z);
    ConnectButton(ui->btn_right_bottom_to_left_top_z, Direction::RightBottomToLeftTop, Shape::Z);

    ConnectButton(ui->btn_left_top_to_right_bottom_vertical_s, Direction::LeftTopToRightBottom, Shape::Vertical_S);
    ConnectButton(ui->btn_right_top_to_left_bottom_vertical_s, Direction::RightTopToLeftBottom, Shape::Vertical_S);
    ConnectButton(ui->btn_left_bottom_to_right_top_vertical_s, Direction::LeftBottomToRightTop, Shape::Vertical_S);
    ConnectButton(ui->btn_right_bottom_to_left_top_vertical_s, Direction::RightBottomToLeftTop, Shape::Vertical_S);

    ConnectButton(ui->btn_left_top_to_right_bottom_vertical_z, Direction::LeftTopToRightBottom, Shape::Vertical_Z);
    ConnectButton(ui->btn_right_top_to_left_bottom_vertical_z, Direction::RightTopToLeftBottom, Shape::Vertical_Z);
    ConnectButton(ui->btn_left_bottom_to_right_top_vertical_z, Direction::LeftBottomToRightTop, Shape::Vertical_Z);
    ConnectButton(ui->btn_right_bottom_to_left_top_vertical_z, Direction::RightBottomToLeftTop, Shape::Vertical_Z);

    connect(ui->btn_cancel, &QPushButton::clicked, this, [=]() {
        _subboard_sort_param->event = jrsdata::SubboardSortParam::Event::CANCEL_SORT;
        this->close();
        emit SigUpdateSubboardSort(*_subboard_sort_param.get());
        });
    connect(ui->btn_confirm, &QPushButton::clicked, this, [=]() {
        _subboard_sort_param->event = jrsdata::SubboardSortParam::Event::CONFIRM_SORT;
        emit SigUpdateSubboardSort(*_subboard_sort_param.get());
        });
}

void SubboardSortView::SetToolButtonIcon(QToolButton* button_, const QString& icon_path_, const QSize& icon_size_ /*= QSize(24, 24)*/, const QString& tool_tip_ /*= ""*/)
{
    if (!button_) return;

    button_->setIcon(QIcon(icon_path_));
    button_->setIconSize(icon_size_);
    button_->setToolButtonStyle(Qt::ToolButtonIconOnly);  // 只显示图标
    if (!tool_tip_.isEmpty()) {
        button_->setToolTip(tool_tip_);
    }

    // 可选：设置按钮大小为图标大小（带边距）
    button_->setFixedSize(icon_size_.width() + 6, icon_size_.height() + 6);

}

