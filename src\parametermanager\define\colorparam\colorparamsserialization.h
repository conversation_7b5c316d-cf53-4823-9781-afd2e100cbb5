#ifndef __COLORPARAMS_SERIALIZATION_H__
#define __COLORPARAMS_SERIALIZATION_H__

#include "colorparams.h"
#include <cereal/cereal.hpp>
#include <cereal/archives/json.hpp>
#include <cereal/types/array.hpp>
#include <cereal/types/utility.hpp> 
#include <cereal/types/string.hpp>
#include <cereal/types/vector.hpp>
#include <sstream>

namespace cereal {

// ColorWheelThreshVal 序列化
template<class Archive>
void serialize(Archive& archive, ColorWheelThreshVal& val)
{
    archive(make_nvp("v_low", val.v_low),
           make_nvp("v_high", val.v_high),
           make_nvp("hs_table", val.hs_table_one_dim),
           make_nvp("pts", val.pts),
           make_nvp("type", val.type));
}

// PreProcessParams 序列化
template<class Archive>
void serialize(Archive& archive, PreProcessParams& val)
{
    archive(make_nvp("contrast_value", val.contrast_value),
           make_nvp("brightness_value", val.brightness_value),
           make_nvp("hue_value", val.hue_value),
           make_nvp("saturation_value", val.saturation_value),
           make_nvp("gamma_value", val.gamma_value),
           make_nvp("is_reverse", val.is_reverse),
           make_nvp("image_id", val.image_id));
}

// BinProcessParams 序列化
template<class Archive>
void serialize(Archive& archive, BinProcessParams& val)
{
    archive(make_nvp("binary_type_index", val.binary_type_index),
           make_nvp("r_thre_min", val.r_thre_min),
           make_nvp("r_thre_max", val.r_thre_max),
           make_nvp("g_thre_min", val.g_thre_min),
           make_nvp("g_thre_max", val.g_thre_max),
           make_nvp("b_thre_min", val.b_thre_min),
           make_nvp("b_thre_max", val.b_thre_max),
           make_nvp("gray_thre_min", val.gray_thre_min),
           make_nvp("gray_thre_max", val.gray_thre_max),
           make_nvp("hsv_paramas", val.hsv_paramas),
           make_nvp("height_bin_nums", val.height_bin_nums),
           make_nvp("min_hei", val.min_hei),
           make_nvp("max_hei", val.max_hei),
           make_nvp("height_thre_min", val.height_thre_min),
           make_nvp("height_thre_max", val.height_thre_max));
}

// ColorParams 序列化
template<class Archive>
void serialize(Archive& archive, ColorParams& val)
{
    archive(
        make_nvp("deal_type", val.deal_type),
        make_nvp("pre_process_params", val.pre_process_params),
        make_nvp("binary_params", val.binary_params)
    );

}

} // namespace cereal

// 序列化辅助函数
namespace serialization {

template<typename T>
std::string ToJson(const T& val, const char* name) {
    std::ostringstream oss;
    {
        cereal::JSONOutputArchive archive(oss);
        archive(cereal::make_nvp(name, val));
    }
    return oss.str();
}

template<typename T>
T FromJson(const std::string& json_str, const char* name) {
    T val;
    std::istringstream iss(json_str);
    cereal::JSONInputArchive archive(iss);
    archive(cereal::make_nvp(name, val));
    return val;
}

} // namespace serialization

#endif // __COLORPARAMS_SERIALIZATION_H__