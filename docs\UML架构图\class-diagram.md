# JRSAOI 2.0 整体架构UML类图

```mermaid
classDiagram
    %% 视图层 (View Layer)
    class MainWindow {
        -ViewManager* view_manager
        -CustomTitleView* title_view
        -StatusBarView* status_bar
        +MainWindow(parent)
        +GetInitView() ViewBase*
        +HideLeftDockWidget()
        +HideRightDockWidget()
    }

    class ViewManager {
        -map~string, tuple~ mvc_container
        +RegisterComponent()
        +GetController(name) ControllerBasePtr
        +InitMember()
    }

    class ViewBase {
        <<abstract>>
        #string name
        +Init() int
        +UpdateView(param) int
        +Save(param) int
    }

    class OperateView {
        -Ui::OperateView* ui
        -MotiondebugView* motionview
        -DetectView* detect_view
        -ProjectView* project_view
        -EditDetectModelView* edit_detect_model_view
        +UpdateView(param) int
        +GetCustomWidget() QWidget*
    }

    class Render2DView {
        -Renderer2DWidget* render_widget
        -GraphicsManager* graphics_manager
        +UpdateGraphics(param)
        +HandleMouseEvent(event)
    }

    %% 控制器层 (Controller Layer)
    class ControllerBase {
        <<abstract>>
        #string name
        #ModelBasePtr model
        +Update(param) int
        +Save(param) int
        +SetView(view) void
        +SetModel(model) void
    }

    class OperateController {
        -OperateView* operate_view
        -AlgorithmEngineManager* algo_engine_ptr
        +SlotViewOperator(param)
        +SlotModelOperator(param)
        +ExecuteSingleComponent(component, pn_detect_info, is_save_algo_info, is_location)
        +ExecuteDetections(sorted_detect_wins, detect_win_exec_params, ...)
        +CurSelectedComponentRun(param, is_save_algo_info, is_location)
    }

    class Render2DController {
        -Render2DView* render_view
        -Render2DModel* render_model
        +HandleGraphicsUpdate(param)
        +ProcessCoordinateTransform(param)
    }

    %% 模型层 (Model Layer)
    class ModelBase {
        <<abstract>>
        #string name
        +Update(param) int
        +Save(param) int
    }

    class OperateModel {
        -Component* cur_selected_component
        -DetectWindow* cur_selected_detect_win
        -PNDetectInfo* cur_selected_spec_region
        -map~string, vector~ComponentAlgoResult~~ component_algo_results
        -SubboardSortManager* subboard_sort_manager_ptr
        +GetProjectParam() ProjectParamPtr
        +UpdateSelectedDetectWin(win_name)
        +UpdateSelectedComponent(param, component_changed, part_number_changed)
        +CreateOneDetectWindow(detect_operate_type, detect_window)
        +EraseSpeficComponentResult(component_name)
    }

    class Render2DModel {
        -vector~GraphicsObject~ graphics_objects
        -CoordinateTransform* coord_transform
        +AddGraphicsObject(obj)
        +RemoveGraphicsObject(id)
        +UpdateGraphicsData(param)
    }

    %% 逻辑层 (Logic Layer)
    class LogicManager {
        -DeviceManager* device_manager_ptr
        -ScanProcess* scan_process_ptr
        -JrsAoiImgsManager* imgs_manager_ptr
        -WorkFlowManager* work_flow_manager_ptr
        -DataManager* data_ptr
        -ProjectManager* project_manager_ptr
        +EventHandler(param) int
        +LogicEventHandler(param)
        +InitMember()
    }

    class ProjectManager {
        -ProjectParam* current_project
        +CreateProject(param) int
        +SaveProject(param) int
        +LoadProject(param) int
        +EventHandler(param) int
    }

    class WorkFlowManager {
        -vector~WorkFlowStep~ workflow_steps
        +WorkFlowEventHandler(param)
        +StartWorkFlow()
        +StopWorkFlow()
        +ExecuteStep(step)
    }

    %% 插件层 (Plugin Layer)
    class AlgorithmEngineManager {
        -AlgoFactory* algo_factory_ptr
        -AlgoExecute* algo_execute_ptr
        -LoadAlgoManager* load_algo_manager_ptr
        -nlohmann::json algo_param_config
        +ExecuteSpecificAlgoDrive(execute_algo_param) OperatorParamBasePtr
        +GetAlgoNameMap() map~string, string~
        +LoadAlgorithms()
    }

    class DeviceManager {
        -StructLight* struct_light_ptr
        -Motion* motion_ptr
        -BarcodeDevice* barcode_device_ptr
        -MotionDebug* motion_debug
        -MotionStatus* motion_status
        +EventHandler(param)
        +GetStructLightInstance() StructLightPtr
        +GetMotionInstance() MotionPtr
        +InitDevices()
    }

    %% 核心层 (Core Layer)
    class CoreApplication {
        <<singleton>>
        -ErrorHandler* error_handler
        -LogManager* log_manager
        +GetInstance() CoreApplication*
        +GetErrorHandler() ErrorHandlerPtr
        +GetLogManager() LogManagerPtr
    }

    class CoordinateTransform {
        -cv::Mat transform_matrix
        +PixelToPhysical(pixel_coord) PhysicalCoord
        +PhysicalToPixel(physical_coord) PixelCoord
        +SetTransformMatrix(matrix)
        +ApplyTransform(points) vector~Point~
    }

    %% 数据层 (Data Layer)
    class DataManager {
        -TableManager* table_manager_ptr
        -ParamConvertor* param_convertor_ptr
        -ProjectParam* project_param_ptr
        -AllSettingParamMap all_setting_param_map
        +EventHandler(param) int
        +SaveDetectResult(result_data)
        +LoadProjectData(project_name) ProjectParamPtr
        +ComponentDetectResultToRender(result)
    }

    class TableManager {
        -BoardTable* board_table
        -ProjectTable* project_table
        -DetectWindowTable* detect_window_table
        -UserTable* user_table
        +Save~T~(data) int
        +Query~T~(condition) vector~T~
        +Delete~T~(condition) int
    }

    %% 参数定义类
    class ProjectParam {
        +string project_name
        +string current_group_name
        +string project_path
        +Board board_info
        +vector~Template~ temps
        +unordered_map~int, cv::Mat~ entirety_board_imgs
        +bool is_save
    }

    class Board {
        +int width, height
        +int cols, rows
        +double real_width, real_height
        +vector~Mark~ marks
        +vector~Barcode~ barcodes
        +vector~SubBoard~ sub_board
        +unordered_map~string, PNDetectInfo~ part_nums_and_detect_regions
    }

    class Component {
        +string component_name
        +string component_part_number
        +double cx, cy
        +double width, height
        +double angle
        +bool enable
        +vector~ComponentModule~ modules
    }

    class DetectWindow {
        +string model_name
        +string name
        +string defect_name
        +int id
        +int cx, cy
        +int width, height
        +int level
        +bool enable
        +vector~DetectAlgorithm~ algorithms
    }

    %% 设备相关类
    class StructLight {
        -bool is_connected
        +Connect() bool
        +Disconnect()
        +CaptureImage() cv::Mat
        +SetExposure(exposure)
    }

    class Motion {
        -string ip_address
        -int port
        -bool is_connected
        +Connect() bool
        +MoveTo(x, y, z) bool
        +GetCurrentPosition() Position
        +SetSpeed(speed)
    }

    %% 渲染相关类
    class Renderer2D {
        -vector~GraphicsObject~ objects
        -Camera2D camera
        +Render()
        +AddObject(obj)
        +RemoveObject(id)
        +SetViewport(viewport)
    }

    class GraphicsObject {
        +int id
        +ObjectType type
        +vector~Point2D~ points
        +Color color
        +float line_width
        +Draw(renderer)
        +IsPointInside(point) bool
    }

    %% 继承关系
    ViewBase <|-- OperateView
    ViewBase <|-- Render2DView
    ControllerBase <|-- OperateController
    ControllerBase <|-- Render2DController
    ModelBase <|-- OperateModel
    ModelBase <|-- Render2DModel

    %% 组合关系
    MainWindow *-- ViewManager
    ViewManager *-- OperateController
    ViewManager *-- Render2DController
    OperateController *-- OperateView
    OperateController *-- OperateModel
    OperateController *-- AlgorithmEngineManager
    Render2DController *-- Render2DView
    Render2DController *-- Render2DModel

    LogicManager *-- DeviceManager
    LogicManager *-- ProjectManager
    LogicManager *-- WorkFlowManager
    LogicManager *-- DataManager

    DeviceManager *-- StructLight
    DeviceManager *-- Motion
    DataManager *-- TableManager
    DataManager *-- ProjectParam

    ProjectParam *-- Board
    Board *-- Component
    Component *-- DetectWindow

    Render2DView *-- Renderer2D
    Renderer2D *-- GraphicsObject

    %% 依赖关系
    OperateController ..> CoreApplication
    DataManager ..> CoreApplication
    AlgorithmEngineManager ..> CoordinateTransform
    OperateModel ..> ProjectParam
```
