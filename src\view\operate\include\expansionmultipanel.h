﻿/**********************************************************************
 * @file   expansionmultipanel.h
 * @brief  多联板编辑ui
 *
 * <AUTHOR>
 * @date   2024.8.13
 *********************************************************************/
#ifndef EXPANSIONMULTIPANEL_H
#define EXPANSIONMULTIPANEL_H
 // prebuild
#include "pch.h"
#include <QWidget>

 //custom
//#include "viewparam.hpp"
QT_BEGIN_NAMESPACE
namespace Ui
{
    class ExpansionMultiPanel;
};
QT_END_NAMESPACE

class SubboardSortView;
class ExpansionMultiPanel : public QWidget
{
    Q_OBJECT
public:
    explicit ExpansionMultiPanel(QWidget* parent = Q_NULLPTR);
    ~ExpansionMultiPanel();
    int UpdateView(jrsdata::MultiBoardEventParamPtr multiple_baords_param_);
public:
    ///**
    // * @fun SelectSubName
    // * @brief
    // * @param
    // * @date 2024.9.24
    // * <AUTHOR>
    // */
    //void SelectSubName(const QString&);

Q_SIGNALS:
    void SigUpdateMultipleBoards(const jrsdata::MultiBoardEventParamPtr& param_);

protected Q_SLOTS:

    /**
    * @fun SlotIrregularMultipleBoardsCreateFlow
    * @brief  规则多联板创建流程
    * <AUTHOR>
    * @date 2024.12.31
    */
    void SlotRegularMultipleBoardsCreateFlow();
    /**
     * @fun SlotIrregularMultipleBoardsCreateFlow
     * @brief  不规则多联板创建流程
     * <AUTHOR>
     * @date 2024.12.31
     */
    void SlotIrregularMultipleBoardsCreateFlow();

    /**
     * @fun SlotRequestCreateSub
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SlotRequestCreateSub();
    ///**
    // * @fun SlotSelectSubBoard
    // * @brief
    // * <AUTHOR>
    // * @date 2025.1.3
    // */
    //void SlotSelectSubBoard();

    /**
     * @fun SlotRuleMultileSub
     * @brief 规格匹配多联板
     * <AUTHOR>
     * @date 2024.12.18
     */
    void SlotRuleMultileSub();

    /**
     * @fun SlotSubstandardMultileSub
     * @brief 不规格多连扳
     * <AUTHOR>
     * @date 2024.12.18
     */
    void SlotSubstandardMultileSub();

private:
    /**
     * @fun InitView
     * @brief 初始化界面
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitView();
    void InitMember();
    void InitConnect();
    /**
    * @fun MatToQImage
    * @brief 图像转换
    * @date 2024.12.31
    * <AUTHOR>
    */
    QImage MatToQImage(const cv::Mat& mat);

private:
    QGraphicsScene* _graphic_scene;

    jrsdata::MultiBoardEventParamPtr _multiple_board_param;
    SubboardSortView* _subboard_sort_view;
    Ui::ExpansionMultiPanel* ui;
};
#endif