//STD
#include <thread>
#include <future>

//Custom
#include "devicemanager.h"
#include "coreapplication.h"
#include "structlight.h"
#include <fileoperation.h>
#include "motiondebug.h"

namespace jrsdevice 
{
    MotionDebug::MotionDebug(std::shared_ptr<Motion>& motion, bool& running)
    :motion_ptr(motion)
    ,process_running(running)
    {
    MotionFunctionBind();
    }
    MotionDebug::~MotionDebug()
    {
    }
    void MotionDebug::SetDeviceTrack(JSON track)
    {
        track_setting = track;
    }

    void MotionDebug::SetMotionSetting(jrsdata::MotionSetting motion)
    {
        motion_setting = motion;
    }

	void MotionDebug::ExcuteCommond(const jrsdata::DeviceParamPtr& device_param_)
	{
		std::thread td([=]()
			{
				if (motion_func_map.find(device_param_->event_name) != motion_func_map.end())
				{
					motion_func_map[device_param_->event_name](device_param_);
				} });
		td.detach();
	}
	void MotionDebug::MotionFunctionBind()
	{
		motion_func_map["ClearAlarm"] = std::bind(&MotionDebug::ClearAlarm, this, std::placeholders::_1);
		motion_func_map["Stop"] = std::bind(&MotionDebug::AskStop, this, std::placeholders::_1);
		motion_func_map["Initial"] = std::bind(&MotionDebug::AskInitial, this, std::placeholders::_1);
		motion_func_map["SingleMove"] = std::bind(&MotionDebug::SingleMove, this, std::placeholders::_1);
		motion_func_map["SingleMovea"] = std::bind(&MotionDebug::SingleMovea, this, std::placeholders::_1);
		motion_func_map["LopTo"] = std::bind(&MotionDebug::LopTo, this, std::placeholders::_1);
		motion_func_map["Jog"] = std::bind(&MotionDebug::Jog, this, std::placeholders::_1);
		motion_func_map["Output"] = std::bind(&MotionDebug::Output, this, std::placeholders::_1);
		motion_func_map["CylinderOutput"] = std::bind(&MotionDebug::CylinderOutput, this, std::placeholders::_1);
		motion_func_map["LoadAndUnLoad"] = std::bind(&MotionDebug::LoadAndUnLoad, this, std::placeholders::_1);
		motion_func_map["BoardStop"] = std::bind(&MotionDebug::BoardStop, this, std::placeholders::_1);
		motion_func_map["BoardRemote"] = std::bind(&MotionDebug::BoardRemote, this, std::placeholders::_1);
		motion_func_map["Home"] = std::bind(&MotionDebug::Home, this, std::placeholders::_1);
	}
	void MotionDebug::ClearAlarm(const jrsdata::DeviceParamPtr& param_)
	{
		(void)param_;
		int res = motion_ptr->ClearAlarm();
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}
	void MotionDebug::AskStop(const jrsdata::DeviceParamPtr& param_)
	{
		process_running = false;
		(void)param_;
		int res = motion_ptr->AskStop();
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}

		for (auto it = track_running_state.begin(); it != track_running_state.end(); it++)
		{
			it->second = false;
		}
	}

	void MotionDebug::AskInitial(const jrsdata::DeviceParamPtr& param_)
	{
		(void)param_;
		if (process_running)
		{
			Log_ERROR("程序正在运行中，请先停止流程 ");
			return;
		}
		int res = motion_ptr->AskInitial();
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}

	void MotionDebug::SingleMove(const jrsdata::DeviceParamPtr& param_)
	{
		// 自动运行状态，点击调试不起作用
		if (process_running)
		{
			Log_ERROR("程序正在运行中，请先停止流程 ");
			return;
		}
		int axis = param_->motion_param.motion_move.axis_index;
		double distance = param_->motion_param.motion_move.axis_diatance;
		double speed = param_->motion_param.motion_move.axis_speed;
		int direction = param_->motion_param.motion_move.x_direction;
		if (axis == 2)
		{
			direction = param_->motion_param.motion_move.y_direction;
		}
		else if (axis == 3)
		{
			direction = param_->motion_param.motion_move.z_direction;
		}

		std::lock_guard<std::mutex> motion_guard(motion_mutex);
		int res = motion_ptr->Mov((jrsdevice::Axis)axis, distance * direction, speed);
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}
	void MotionDebug::SingleMovea(const jrsdata::DeviceParamPtr& param_)
	{
		// 自动运行状态，点击调试不起作用
		if (process_running)
		{
			Log_ERROR("程序正在运行中，请先停止流程 ");
			return;
		}
		int axis = param_->motion_param.motion_move.axis_index;
		double distance = param_->motion_param.motion_move.axis_diatance;
		double speed = param_->motion_param.motion_move.axis_speed;

		std::lock_guard<std::mutex> motion_guard(motion_mutex);
		int res = motion_ptr->Mova((jrsdevice::Axis)axis, distance, speed);
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}
	void MotionDebug::LopTo(const jrsdata::DeviceParamPtr& param_)
	{
		// 自动运行状态，点击调试不起作用
		if (process_running)
		{
			Log_ERROR("程序正在运行中，请先停止流程 ");
			return;
		}

		std::lock_guard<std::mutex> motion_guard(motion_mutex);
		int res = motion_ptr->LopTo(param_->motion_param.motion_move.axis_xpos, param_->motion_param.motion_move.axis_ypos, param_->motion_param.motion_move.axis_speed);
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}
	void MotionDebug::Jog(const jrsdata::DeviceParamPtr& param_)
	{
		// 自动运行状态，点击调试不起作用
		if (process_running)
		{
			Log_ERROR("程序正在运行中，请先停止流程 ");
			return;
		}
		int axis = param_->motion_param.motion_move.axis_index;
		bool flag = param_->motion_param.motion_move.axis_jog;
		int direction = param_->motion_param.motion_move.axis_jog_direction;
		double speed = param_->motion_param.motion_move.axis_speed;

		// 上下料过程中JOG不启作用
		for (auto it = track_running_state.begin(); it != track_running_state.end(); it++)
		{
			if ((int)it->first + 6 == axis && it->second)
			{
				return;
			}
		}
		int res = motion_ptr->Jog((jrsdevice::Axis)axis, flag, direction, speed);
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());
		}
	}
	void MotionDebug::Output(const jrsdata::DeviceParamPtr& param_)
	{
		(void)param_;
	}
	void MotionDebug::CylinderOutput(const jrsdata::DeviceParamPtr& param_)
	{
		int track_index = param_->motion_param.track_param.track_index;
		if (!track_setting.empty() && track_setting.is_array() && track_setting.size() > track_index)
		{
			auto cur_track = track_setting[track_index]["TrackOutput"];
			if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::Cylinder)
			{
				auto cylinder1 = GetObjFromName(cur_track, "cylinder1");
				CylinderControl(cylinder1, param_);

            auto cylinder2 = GetObjFromName(cur_track, "cylinder2");
            CylinderControl(cylinder2, param_);
            }
            if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::Shield)
            {
            // 根据进料方向决定是左挡板还是右挡板
            int enterDirection = motion_setting.enterDirection;
            if(enterDirection == 0)
            {
                auto shield1 = GetObjFromName(cur_track, "right_shield_up");
                CylinderControl(shield1, param_);

                auto shield2 = GetObjFromName(cur_track, "right_shield_down");
                CylinderControl(shield2, param_);
            }
            else if(enterDirection == 1)
            {
                auto shield1 = GetObjFromName(cur_track, "left_shield_up");
                CylinderControl(shield1, param_);

                auto shield2 = GetObjFromName(cur_track, "left_shield_down");
                CylinderControl(shield2, param_);
            }
            
            }
        }
    }
    void MotionDebug::LoadAndUnLoad(const jrsdata::DeviceParamPtr& param_)
    {
        // 自动运行状态，点击调试不起作用
        if (process_running)
        {
            Log_ERROR("程序正在运行中，请先停止流程 ");
            return;
        }

        int track_index = param_->motion_param.track_param.track_index;
        jrsdevice::TrackIndex index = (jrsdevice::TrackIndex)(track_index + 1);
        if (!TrackAvailable(index))
        {
            return;
        }
        motion_ptr->Jog((jrsdevice::Axis)(track_index + 7), false, 1, 100);

        // 更新轨道状态
        track_running_state[index] = true;

		int res = 0;
		if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::Load)
		{
			std::cout << __FUNCTION__ << " 进料" << std::endl;
			res = motion_ptr->Load(index);
		}
		else if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::UnLoad)
		{
			std::cout << __FUNCTION__ << " 出料" << std::endl;
			res = motion_ptr->UnLoad(index);
		}
		else if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::LoadAndUnLoad)
		{
			res = motion_ptr->Load(index);
			if (res == 0)
			{
				res = motion_ptr->UnLoad(index);
			}
		}
		else if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::InitTrack)
		{
			std::cout << __FUNCTION__ << " 轨道初始化检查" << std::endl;
			res = motion_ptr->InitialCheck(index);
		}
		else if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::RebackInputSignal)
		{
			std::cout << __FUNCTION__ << " 产品回到入料口" << std::endl;
			res = motion_ptr->TransToImport(index);
		}
		else if (param_->motion_param.track_param.track_type == jrsdata::TrackControlType::RebackOutputSignal)
		{
			std::cout << __FUNCTION__ << " 产品回到出料口" << std::endl;
			res = motion_ptr->TransToExport(index);
		}
		if (res != 0)
		{
			Log_ERROR(motion_ptr->GetLastError());

			// 脚本执行错误的消息
			std::vector<std::string> scriptErr = motion_ptr->GetScriptErrorList();
			for (int i = 0; i < scriptErr.size(); i++)
			{
				Log_ERROR(scriptErr.at(i));
			}
		}
		track_running_state[index] = false;
	}

	bool MotionDebug::TrackAvailable(jrsdevice::TrackIndex index)
	{
		auto it = track_running_state.find(index);
		if (it != track_running_state.end())
		{
			return !it->second;
		}

        track_running_state[index] = false;
        return true;
    }
    void MotionDebug::BoardStop(const jrsdata::DeviceParamPtr& param_)
    {
    (void)param_;
        int enterDirection = motion_setting.enterDirection;	//! 进板方向
        auto board_stop = motion_setting.board_stop;

        int track_index = 0;	//! 目前先考虑轨道1
        if ((int)board_stop.size() > track_index)
        {
            jrsdata::BoardStopPosition stop = board_stop[track_index];
            jrsdata::Point point = stop.right; // 默认左进
            if (enterDirection != 0)
            {
            point = stop.left;
            }

            // 运动
            motion_ptr->LopTo(point.x, point.y, 100);
        }
    }
    void MotionDebug::BoardRemote(const jrsdata::DeviceParamPtr& param_)
    {
        int enterDirection = motion_setting.enterDirection;	//! 进板方向
        auto board_stop = motion_setting.board_stop;

        int track_index = 0;	//! 目前先考虑轨道1
        if ((int)board_stop.size() > track_index)
        {
            jrsdata::BoardStopPosition stop = board_stop[track_index];
            jrsdata::Point point = stop.right; // 默认左进
            if (enterDirection != 0)
            {
            point = stop.left;
            }

            // 板尺寸
            double board_width = param_->extra_param.product_width;
            double board_height = param_->extra_param.product_height;

            // 运动
            motion_ptr->LopTo(point.x - board_width * (enterDirection == 0?1: -1), point.y - board_height, 100);
        }
    }
    
	void MotionDebug::Home(const jrsdata::DeviceParamPtr& param_)
	{
		int axis = param_->motion_param.motion_move.axis_index;
		motion_ptr->Home((jrsdevice::Axis)axis);
	}
	
	JSON MotionDebug::GetObjFromName(JSON Obj, std::string name)
    {
        if (Obj.is_array())
        {
            for (int i = 0; i < (int)Obj.size(); i++)
            {
            if (Obj[i]["name"].get<std::string>() == name)
            {
                return Obj[i];
            }
            }
        }
        return JSON();
    }
    void MotionDebug::CylinderControl(JSON Obj, const jrsdata::DeviceParamPtr& param_)
    {
        if (Obj != nullptr)
        {
            int CardsIndex = Obj["CardsIndex"].get<int>();
            int index = Obj["index"].get<int>();
            int value = Obj["value"].get<int>();
            bool state = value > 0;
            if (index > 0)
            {
            bool flag = param_->motion_param.track_param.track_state ? state : !state;
            motion_ptr->OutPut(CardsIndex == 0 ? jrsdevice::CardType::MotionCard : jrsdevice::CardType::IOCard, 0, (jrsdevice::OutputIndex)index, flag);
            }
        }
    }
}
