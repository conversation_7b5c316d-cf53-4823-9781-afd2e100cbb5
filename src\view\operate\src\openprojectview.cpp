﻿//Custom
#include "openprojectview.h"
#include "ui_openprojectview.h"

namespace jrsaoi
{
    struct SelectProjectInfo
    {
        QString project_path;
        QString project_name;
        QString project_time;

        SelectProjectInfo(QString path, QString name, QString time)
        {
            project_path = path;
            project_name = name;
            project_time = time;
        }
    };

    OpenProjectView::OpenProjectView(QWidget* parent)
        : QWidget(parent), ui(new Ui::OpenProjectView)
    {
        setWindowFlags(Qt::WindowStaysOnTopHint);  // 设置窗体总在最上层
        setAttribute(Qt::WA_TransparentForMouseEvents);  // 允许点击后面的窗体
        ui->setupUi(this);
        Init();
    }

    OpenProjectView::~OpenProjectView()
    {
        delete ui;
    }

    void OpenProjectView::Init()
    {
        InitShowListHeader();
        InitListView();
        InitMember();
        InitConnect();
    }

    void OpenProjectView::InitListView()
    {
        ui->list_project->verticalHeader()->setVisible(false);
        ui->list_project->setStyle(new CustomStyle());
        std::vector<int> column_width_product = { 80,50 };
        list_prject_model.setRowCount(1);
        list_prject_model.setColumnCount(2);
        list_prject_model.setHorizontalHeaderLabels(header_list);
        ui->list_project->setModel(&list_prject_model);
        ui->list_project->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
        ui->list_project->setSelectionBehavior(QAbstractItemView::SelectRows);
        list_prject_model.removeRow(0);
        InitTableViewColumnWidth(ui->list_project, column_width_product);
        ui->list_project->setEditTriggers(QAbstractItemView::NoEditTriggers);

        ui->pushbutton_select_path->setHidden(true);
        ui->qline_project_path->setReadOnly(true);

        this->setWindowFlags(this->windowFlags() | Qt::WindowStaysOnTopHint);
    }

    void OpenProjectView::InitMember()
    {

    }

    void OpenProjectView::InitConnect()
    {
        connect(ui->pushbutton_select_path, &QPushButton::clicked, this, &OpenProjectView::SlotOpenProjectDirectory);
        connect(ui->list_project, &QTableView::clicked, this, &OpenProjectView::SlotListViewSelectProjectFile);
        connect(ui->pushbutton_confirm, &QPushButton::clicked, this, &OpenProjectView::SlotSelectProjectFile);
        connect(ui->pushbutton_cancel, &QPushButton::clicked, this, &OpenProjectView::SlotCancelSelectProjectFile);
    }

    void OpenProjectView::GetProjectListByDirPath(const std::string& dir_)
    {
        project_info_list.clear();
        QDir dir(QString::fromStdString(dir_));
        //获取文件夹下所有指定后缀的文件
        QFileInfoList fileList = dir.entryInfoList(QStringList("*.jrspro"), QDir::Files, QDir::Time);
        foreach(const QFileInfo & info, fileList)
        {
            QString path = info.path();
            QString fileName = info.fileName();
            QString createTime = info.lastModified().toString("yyyy-MM-dd HH:mm");
            //添加数据到project_info_list中
            project_info_list.append(SelectProjectInfo(path, fileName, createTime));
        }
        SlotUpdateProjectList();
    }

    void OpenProjectView::InitShowListHeader()
    {
        header_list.append(QString::fromWCharArray(L"名称"));
        header_list.append(QString::fromWCharArray(L"时间"));
    }

    void OpenProjectView::InitTableViewColumnWidth(QTableView* table_view, std::vector<int> column_widths)
    {
        if (table_view == nullptr)
        {
            return;
        }
        for (int i = 0; i < table_view->model()->columnCount() && i < column_widths.size(); i++)
        {
            table_view->setColumnWidth(i, column_widths.at(i));
        }
    }
    void OpenProjectView::SlotUpdateProjectPath(const std::string& project_path_)
    {
        ui->qline_project_path->setText(QString::fromStdString(project_path_));
        GetProjectListByDirPath(project_path_);
    }
    void OpenProjectView::SlotUpdateProjectList()
    {
        Log_INFO(__FUNCTION__, " 更新工程列表");
        list_prject_model.clear();
        list_prject_model.setColumnCount(2);
        list_prject_model.setHorizontalHeaderLabels(header_list);
        for (int row = 0; row < project_info_list.size(); ++row)
        {
            QList<QStandardItem*> rowItems;
            rowItems << new QStandardItem(project_info_list.at(row).project_name) << new QStandardItem(project_info_list.at(row).project_time);
            list_prject_model.appendRow(rowItems);
        }
        if (list_prject_model.rowCount() > 0)
        {
            ui->list_project->setFocus();
            ui->list_project->setCurrentIndex(list_prject_model.index(0, 0));
            ui->list_project->selectRow(0);
            ui->list_project->update();
            SlotListViewSelectProjectFile(list_prject_model.index(0, 0));
        }
    }

    void OpenProjectView::SlotOpenProjectDirectory()
    {
        Log_INFO(__FUNCTION__, " 打开工程文件夹");
        QString dirPath = QFileDialog::getExistingDirectory(nullptr, "选择工程文件夹", QDir::homePath());
        if (dirPath.isEmpty())
        {
            select_project_index = -1;
            Log_ERROR(__FUNCTION__, " 工程文件夹为空");
            return;
        }
        GetProjectListByDirPath(dirPath.toStdString());
        ui->qline_project_path->setText(dirPath);
        ui->qline_project_select_path->setText("");

    }

    void OpenProjectView::SlotListViewSelectProjectFile(const QModelIndex& index)
    {
        //获取当前选中行的索引
        if (index.row() < 0)
        {
            return;
        }
        select_project_index = index.row();
        ui->qline_project_select_path->setText(project_info_list.at(select_project_index).project_name);
    }

    void OpenProjectView::SlotSelectProjectFile()
    {
        Log_INFO(__FUNCTION__, " 选择工程文件");
        //判断文件是否存在
        if (ui->qline_project_select_path->text().isEmpty())
        {
            Log_ERROR(__FUNCTION__, " 工程文件不存在");
            return;
        }
        else
        {
            if (project_info_list.size() > select_project_index && project_info_list.size() > 0)
            {
                auto info = jrsdata::ProjectEventInfo();
                info.project_name = ui->qline_project_select_path->text().toStdString();
                info.project_path = project_info_list[select_project_index].project_path.toStdString() + "/";
                info.step = jrsdata::ProjectEventInfo::Step::OPEN;
                emit SignalSelectProjectFile(info);
            }
        }
        this->close();
    }

    void jrsaoi::OpenProjectView::SlotCancelSelectProjectFile()
    {
        this->close();
    }
}