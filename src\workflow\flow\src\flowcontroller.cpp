﻿//Custom
#include "flowcontroller.h"
#include "projectparam.hpp"
#include "projectdataprocess.h"
#include "visioninspectionflow.h"
#include "trackflow.h"
#include "devicemanager.h"
#include "barcodedevice.h"
#include "resultstorageflow.h"
#include "conditionwaiter.h"
#include "dataparam.h"
#include "convertboardids.h"
namespace jrsworkflow
{

    FlowController::FlowController(const std::shared_ptr<jrsdevice::DeviceManager>& device_ptr_
        , const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_
        , const std::shared_ptr<jrsdata::DataManager>& data_manager_ptr_
        , LogicFunType logic_invoke_)
        : device_manager_ptr(device_ptr_)
        , algo_engine_manager_ptr(algo_engine_manager_)
        , data_manager_ptr(data_manager_ptr_)
        , condition_waiter_ptr(std::make_unique<ConditionWaiter>())
        , is_running(false)
        , logic_invoke(logic_invoke_)
        , machine_flow_state(std::make_shared<jrsdata::MachineStateViewParam>())

    {
        Init();
    }
    FlowController::~FlowController()
    {
    }
    void FlowController::Start(const std::shared_ptr<jrsdata::DataBase>& project_param_)
    {
        auto res = InitBarcodeDevice();
        if (res != jrscore::AOI_OK)
        {
            return;
        }
        is_running.store(true);
        project_param_ptr = std::dynamic_pointer_cast<jrsdata::ProjectParam> (project_param_);
        work_flow_thread = std::thread(&FlowController::Execute, this, project_param_ptr);
        work_flow_thread.detach();
    }

    void FlowController::Stop()
    {
        LogTo_DEBUG("debug", "停止流程");
        track_controller_ptr->StopTrack();

        vision_inspection_flow_controller_ptr->StopAllTasks();
        is_running.store(false);
        vision_inspection_flow_controller_ptr->StopInspection();
        condition_waiter_ptr->Notify();
        /*  if (work_flow_thread.joinable())
          {
              work_flow_thread.join();
          }*/
        machine_flow_state->current_stop_state = jrsdata::MachineStateViewParam::StopState::IDLE;
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME);

    }
    void FlowController::Pause()
    {

    }
    void FlowController::AddBuffer(const jrsdata::JrsImageBuffer& imgs)
    {
        if (is_running)
        {
            vision_inspection_flow_controller_ptr->AddBuffer(imgs);

        }
    }
    std::string FlowController::GetCurrentStateName()
    {
        return std::string();
    }
    void FlowController::SetWorkFlowParam(const WorkFlowParam& param_)
    {
        work_flow_param = param_;
        vision_inspection_flow_controller_ptr->SetWorkFlowParam(param_);
        condition_waiter_ptr->Notify();


    }
    void FlowController::SetControlPanelCallback(const jrsdata::InvokeControlPanelViewParamFun& cb_)
    {
        control_panel_call_back = cb_;
    }
    void FlowController::Resume()
    {
    }
    void FlowController::Init()
    {
        vision_inspection_flow_controller_ptr = std::make_shared<VisionInspectionFlow>(algo_engine_manager_ptr, logic_invoke);
        track_controller_ptr = std::make_shared<TrackFlow>(device_manager_ptr);
        result_storage_controller_ptr = std::make_shared<ResultStorageFlow>(data_manager_ptr);

        //! 添加是否等待调试完成条件
        //! 增加是否等待调试完成条件 by zhangyuyu 2025-4-29
        condition_waiter_ptr->AddCondition([this]()
            {
                //！开启动态调试并且要停机调试时
                if (work_flow_param.is_enable_online_debug.load() && work_flow_param.is_stop_debug.load())
                {
                    //! 调试完成或者流程停止运行
                    return work_flow_param.finished_online_debug.load() || !is_running.load();
                }
                else
                {
                    return true;
                }
            });

    }
    void FlowController::Execute(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
    {
        (void)project_param_;
        auto up_func = std::bind(&FlowController::LoadFlow, this);
        auto down_func = std::bind(&FlowController::UnLoadFlow, this);
        auto inspection_func = std::bind(&FlowController::InspectionFlow, this);
        auto save_result_func = std::bind(&FlowController::SaveResultFlow, this);
        auto wait_condition_func = std::bind(&FlowController::WaitForCondition, this);
        //首先进行路径规划 
        while (is_running.load())
        {

            //! 1. 上料
            auto res = ExecuteFlowWithInterruptCheck(up_func);
            //! 2. 开始检测
            res = ExecuteFlowWithInterruptCheck(inspection_func);
            //! 3.1. 异步保存结果
            auto save_future = std::async(std::launch::async, [&]()
                {
                    return ExecuteFlowWithInterruptCheck(save_result_func);
                });
            //! 3.2 下料 因为保存结果和下料是同时进行的
            res = ExecuteFlowWithInterruptCheck(down_func);

            //! 3.3 等待保存结果
            res = save_future.get();

            //! 4. 条件等待
            res = ExecuteFlowWithInterruptCheck(wait_condition_func);





        }
    }
    void FlowController::UpdateMachineFlowState(std::string event_name, bool is_running_)
    {
        //return;
        jrsdata::ControlPanelViewParamPtr control_panel_param_ptr = std::make_shared<jrsdata::ControlPanelViewParam>();
        control_panel_param_ptr->machine_state_param = machine_flow_state;
        control_panel_param_ptr->event_name = event_name;
        control_panel_param_ptr->machine_state_param->is_auto_run = is_running_;
        control_panel_call_back(control_panel_param_ptr);
    }
    int FlowController::WaitForCondition()
    {
        condition_waiter_ptr->Wait();
        return jrscore::AOI_OK;
    }
    int FlowController::LoadFlow()
    {
        //! 1. 上料
        machine_flow_state->current_detect_state = std::make_pair(jrsdata::MachineStateViewParam::BoardDetectState::WAITING, jrsdata::MachineStateViewParam::DetectFlowState::ENTER_BOARD);
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME);
        if (0 != track_controller_ptr->LoadMaterial(1))
        {
            machine_flow_state->current_stop_state = jrsdata::MachineStateViewParam::StopState::ALARM;
            UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME, is_running);

            is_running = false;
            auto msg_str = track_controller_ptr->GetTrackErrStr();
            JRSMessageBox_ERR("WorkFlow", "上料失败: "+ msg_str, jrscore::MessageButton::Ok);

            return jrscore::WorkFlowError::E_AOI_WORKFLOW_LOAD_FAILURE;
        }
        return jrscore::AOI_OK;
    }
    int FlowController::UnLoadFlow()
    {
        //! 3. 下料
        machine_flow_state->current_detect_state = std::make_pair(jrsdata::MachineStateViewParam::BoardDetectState::CHECKING, jrsdata::MachineStateViewParam::DetectFlowState::OUT_BOARD);
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME);
        if (0 != track_controller_ptr->UnloadMaterial(1))
        {
            //TODO 增加下料失败主动结束流程处理 by baron_zhang 2024-12-11
            machine_flow_state->current_stop_state = jrsdata::MachineStateViewParam::StopState::ALARM;
            UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME);
            vision_inspection_flow_controller_ptr->StopAllTasks();
            is_running = false;
            return jrscore::WorkFlowError::E_AOI_WORKFLOW_UNLOAD_FAILURE;
        }
        return jrscore::AOI_OK;

    }
    int FlowController::InspectionFlow()
    {
        machine_flow_state->current_detect_state = std::make_pair(jrsdata::MachineStateViewParam::BoardDetectState::CHECKING, jrsdata::MachineStateViewParam::DetectFlowState::DETECT_FOV);
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_START_FLOW_NAME);

        //! 2. 开始检测
        auto res = vision_inspection_flow_controller_ptr->StartInspection(project_param_ptr);
        //vision_inspection_flow_controller_ptr->WaitAllTasks();
        if (res != jrscore::AOI_OK)
        {
            return res;
        }
        LogAutoRun_INFO("检测完成");

        return jrscore::AOI_OK;
    }
    int FlowController::SaveResultFlow()
    {

        //!先等所有的结果检测都结束
        vision_inspection_flow_controller_ptr->WaitAllTasks();
        //! 4. 保存检测结果
        //!获取检测结果
        
        auto insp_res = vision_inspection_flow_controller_ptr->GetInspectionResult();
        //! 检测完成后，将会将调试的信息发送到调试界面，此时将finished_online_debug状态置为false，因为还没有调试完成
        //! 当开启调试时才会改变finished_online_debug状态
        if (work_flow_param.is_enable_online_debug.load() && !insp_res->GetInspectionResultStatus())
        {
            work_flow_param.finished_online_debug.store(false);
        }
        jrsdata::MachineStateViewParam::BoardDetectState detect_result_state;

        if (insp_res->GetInspectionResultStatus())
        {
            detect_result_state = jrsdata::MachineStateViewParam::BoardDetectState::GOOD;
        }
        else
        {
            detect_result_state = jrsdata::MachineStateViewParam::BoardDetectState::NG;
        }

        machine_flow_state->current_detect_state = std::make_pair(detect_result_state, jrsdata::MachineStateViewParam::DetectFlowState::REQUIRE_BOARD);
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_UPDATE_STATE_NAME);

        //! 是否启用硬件条码
        if (work_flow_param.motion_set_param.barcode_mode == static_cast<int>(BarcodeType::HARDWARE))
        {
            SetDeviceBarcode(insp_res);
        }

        LogAutoRun_INFO("检测写入结果开始");
        result_storage_controller_ptr->SaveResult(insp_res);
        LogAutoRun_INFO("检测写入结果完成");

        machine_flow_state->current_detect_state = std::make_pair(jrsdata::MachineStateViewParam::BoardDetectState::WAITING, jrsdata::MachineStateViewParam::DetectFlowState::REQUIRE_BOARD);
        UpdateMachineFlowState(jrsaoi::AUTO_RUN_PANEL_STOP_FLOW_NAME);

        return jrscore::AOI_OK;
    }
    int FlowController::InitBarcodeDevice()
    {
        if (work_flow_param.motion_set_param.barcode_mode != static_cast<int>(BarcodeType::HARDWARE))
        {

            //!未启用扫码枪，直接返回ok
            return jrscore::AOI_OK;
        }
        //！ 设置扫码枪回调
        device_manager_ptr->GetBarcodeInstance()->SetBarcodeCallback(std::bind(&FlowController::BarcodeCallback, this, std::placeholders::_1));
        auto is_open = device_manager_ptr->GetBarcodeInstance()->IsOpenDevice();
        if (!is_open)
        {
            device_manager_ptr->GetBarcodeInstance()->OpenDevice();
        }
        if (device_manager_ptr->GetBarcodeInstance()->IsOpenDevice())
        {
            device_manager_ptr->GetBarcodeInstance()->StartGrabing();
            LogAutoRun_INFO("扫码枪初始化成功");
        }
        else
        {
            
            LogAutoRun_INFO("扫码枪初始化失败");
            JRSMessageBox_ERR("WorkFlow", "扫码枪初始化失败，请检查扫码枪连接状态", jrscore::MessageButton::Ok);
       
            return jrscore::WorkFlowError::E_AOI_WORKFLOW_BARCODE_DEVICE_INIT_FAILURE;
        }
        return jrscore::AOI_OK;
    }
    void FlowController::SetDeviceBarcode(const InspectionResultBasePtr& insp_result_ptr_)
    {
        jrsdata::QueryDatabaseResult query_data_temp; //! 从数据库中查询关联工程的子板二维码和子板id
        ConvertBoardIDs convert_board_id; //! 用于转换当前工程和关联工程的子板id
        jrsparam::ProjectDataProcess project_data_process_temp;//! 用于获取关联工程的子板行列数
        std::vector<BoardIDCoors> vec_board_id_coors_link;//! 关联工程的子板坐标和子板id信息
        std::vector<BoardIDCoors> vec_board_id_coors_current;//！当前工程的子板坐标和子板id信息
        //! 获取扫描枪扫描的条码
        if (barcode_device_str.empty())
        {
            LogAutoRun_INFO("扫码枪未扫描到条码");
            JRSMessageBox_INFO("workflow", "扫码枪未扫描到条码", jrscore::MessageButton::Ok);
            return;
        }
        query_data_temp.query_custom_subboard_barcode.input.subboard_barcode = barcode_device_str;
        
        //! 通过关联工程的一个条码从数据库中将其所有的子板二维码和子板id信息查询出来
        query_data_temp.event_name = jrsaoi::QUERY_DATABASE_SUBBOARD_BARCODES;
        result_storage_controller_ptr->GetDataFromDataBase(query_data_temp);
        
        //! 获取当前工程的子板坐标和子板id信息
        vec_board_id_coors_current = GetSubBoardIdAndCoors(project_param_ptr);
        //! 获取关联工程的子板坐标和子板id信息
        auto multi_project_map = work_flow_param.project_param_map;
        const auto& link_name = project_param_ptr->link_project_name;
        if (link_name)
        {
            auto iter = multi_project_map.find(*link_name);
            if (iter != multi_project_map.end())
            {
                vec_board_id_coors_link = GetSubBoardIdAndCoors(iter->second);
                project_data_process_temp.CalculateRowsAndCols(iter->second);
                convert_board_id.ConvertBoardIdMapping(iter->second->board_info.rows,iter->second->board_info.cols);
            }
            else
            {
                LogAutoRun_INFO("未找到关联工程名称为" + *link_name + "的工程");
                return;
            }


        }
        
        //! 获取当前工程和关联工程的子板id映射结果
        std::map<int/*当前工程子板id*/, int/*映射到关联工程中的子板id*/> map_subboard_id_mapping_flip;
        //! 默认Y轴翻转 TODO:后期需要改成界面设置 by zhangyuyu 2025.6.9
        convert_board_id.GetMappingBoardIds(vec_board_id_coors_link, vec_board_id_coors_current,ConvertDirection::Y_AXIS_FLIP, map_subboard_id_mapping_flip);
    
        //! 设置当前工程从关联工程中获取的子板二维码结果获取
        std::unordered_map<int, std::string> subboard_id_and_barcode;
        for (auto& [current_sub_id, link_sub_id] : map_subboard_id_mapping_flip)
        {
            auto link_sub_barcode_iter = query_data_temp.query_custom_subboard_barcode.output.subboard_id_and_barcode.find(link_sub_id);
            if (link_sub_barcode_iter != query_data_temp.query_custom_subboard_barcode.output.subboard_id_and_barcode.end())
            {
                subboard_id_and_barcode[current_sub_id] = link_sub_barcode_iter->second; //! 设置当前工程的子板id和关联工程的子板二维码
            }
            else
            {
                LogAutoRun_INFO("关联工程的子板id为" + std::to_string(link_sub_id) + "的子板二维码未找到");
            }
            
        }
        //! 设置当前板子的子板二维码
        insp_result_ptr_->SetSpeficParam("sub_barcode", subboard_id_and_barcode);
    }
    void FlowController::BarcodeCallback(const std::string& bar_code)
    {
        barcode_device_str = bar_code;
    }
    std::vector<BoardIDCoors> FlowController::GetSubBoardIdAndCoors(const jrsdata::ProjectParamPtr& project_param_)
    {
        std::vector<BoardIDCoors> vec_board_id_coors{}; //! 当前工程的子板坐标和子板id信息
        if (!project_param_)
        {
            LogAutoRun_INFO("查找子板坐标和ID时，输入的工程未空！");
        }
        for (auto& sub_board_value : project_param_->board_info.sub_board)
        {
            BoardIDCoors board_id_coors_temp;
            board_id_coors_temp.id = sub_board_value.id;
            board_id_coors_temp.x = sub_board_value.x;
            board_id_coors_temp.y = sub_board_value.y;
            vec_board_id_coors.push_back(board_id_coors_temp);
        }
        return vec_board_id_coors;
    }
}
