﻿//QT
#include <QScrollBar>
#include <QImageReader>
#include <QObject>
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QtConcurrent/QtConcurrent>

#include <thread>
#include <mutex>

//Custom
#include "viewergraphicsviewimpl.h"
//#include "errorhandler.h"
//#include "coreapplication.h"
#include "log.h"
ViewerGraphicsViewImpl::ViewerGraphicsViewImpl(QWidget* parent)
    :QGraphicsView(parent)
    , scene(new QGraphicsScene(this))
    , _is_show_entirety_img(false)
    , _current_show_key_img(0)
{
    // 注册 cv::Mat 类型
    qRegisterMetaType<cv::Mat>("cv::Mat");


    this->setScene(scene);
    setDragMode(QGraphicsView::DragMode::NoDrag);

    // 设置棋盘格背景
    QPixmap checkerboard = CreateCheckerboardBackground(20);
    setBackgroundBrush(QBrush(checkerboard));
    setSceneRect(QRectF(-999999, -999999, 1999998, 1999998));
    this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
}

ViewerGraphicsViewImpl::~ViewerGraphicsViewImpl()
{
}

int ViewerGraphicsViewImpl::CreateImagesAsync(const GraphicsImage& graphics_img_)
{
    // 开启子线程做图像转换
    QtConcurrent::run([=]() {

        std::vector<std::pair<int, QPixmap>> pixmaps;

        ConvertMatToPixmap(graphics_img_.key_and_imgs, pixmaps);

        // 在主线程中更新 UI
        QMetaObject::invokeMethod(
            this,
            [=]() {
                for (const auto& [key, pixmap] : pixmaps)
                {
                    CreateImageOnMainThread(graphics_img_.set_key, key, pixmap, graphics_img_.center_point.x, graphics_img_.center_point.y, graphics_img_.z, graphics_img_.angle, graphics_img_.is_draw_center);
                }
                ShowImageByKey(graphics_img_.set_key, graphics_img_.current_show_img); /**<显示设置图像*/
            },
            Qt::QueuedConnection
        );
        });
    return 0;
}

int ViewerGraphicsViewImpl::CreateImageAsync(uint8_t set_key_, int key_, const cv::Mat& image,
    int x, int y, int z, float angle,
    bool is_draw_center, int current_show_image_key_)
{
    // 开启子线程做图像转换
    QtConcurrent::run([=]() {
        QPixmap pixmap = CvMatToQPixmap(image);

        // 在主线程中更新 UI
        QMetaObject::invokeMethod(
            this,
            [=]() {
                CreateImageOnMainThread(set_key_, key_, pixmap, x, y, z, angle, is_draw_center);
                ShowImageByKey(set_key_, current_show_image_key_);
            },
            Qt::QueuedConnection
        );
        });
    return 0;
}
int ViewerGraphicsViewImpl::CreateImage(uint8_t set_key_, int key_, const cv::Mat& image, int x, int y, int z, float angle, bool is_draw_center)
{
    std::lock_guard<std::mutex> lock(_image_mutex);
    auto q_pixel = CvMatToQPixmap(image);
    auto& imgs = _key_and_images[set_key_];

    std::shared_ptr<QGraphicsPixmapItem> item = nullptr;
    auto it = imgs.find(key_);
    if (it != imgs.end())
    {
        item = it->second;
        item->setPixmap(q_pixel);
    }
    else
    {
        item = std::make_shared<QGraphicsPixmapItem>(q_pixel);
        item->setTransformOriginPoint(q_pixel.width() / 2.0, q_pixel.height() / 2.0); // 设置旋转中心
        imgs[key_] = item;
        scene->addItem(item.get());
    }

    item->setZValue(z);
    item->setRotation(angle);

    if (is_draw_center)
    {
        qreal center_x = x - item->pixmap().width() / 2.0;
        qreal center_y = y - item->pixmap().height() / 2.0;
        item->setPos(center_x, center_y);
    }
    else
    {
        item->setPos(x, y);
    }

    centerOn(item->sceneBoundingRect().center()); // 更稳妥的居中方式
    ShowImageByKey(set_key_, key_);
    return 0;
}





int ViewerGraphicsViewImpl::AddGraphicsShapes(const GraphicsShape& graphics_shape_)
{
    if (!scene) {
        return -1;
    }

    QGraphicsItem* item = nullptr;

    switch (graphics_shape_.type) {
    case GraphicsShape::Rect:
        item = scene->addRect(QRectF(graphics_shape_.center_position.x - graphics_shape_.size.width / 2.0,
            graphics_shape_.center_position.y - graphics_shape_.size.height / 2.0,
            graphics_shape_.size.width, graphics_shape_.size.height),
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0])),
            QBrush(Qt::NoBrush));
        // 设置旋转中心为矩形的中心
        item->setTransformOriginPoint(graphics_shape_.center_position.x, graphics_shape_.center_position.y);
        // 顺时针旋转矩形
        item->setRotation(graphics_shape_.angle);
        break;
    case GraphicsShape::Circle:
        item = scene->addEllipse(QRectF(graphics_shape_.center_position.x - graphics_shape_.radius,
            graphics_shape_.center_position.y - graphics_shape_.radius,
            graphics_shape_.radius * 2, graphics_shape_.radius * 2),
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0])),
            QBrush(Qt::NoBrush));
        break;
    case GraphicsShape::Line:
        item = scene->addLine(graphics_shape_.line_points.first.x,
            graphics_shape_.line_points.first.y,
            graphics_shape_.line_points.second.x,
            graphics_shape_.line_points.second.y,
            QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]), graphics_shape_.thickness));
        break;
    case GraphicsShape::Text:
    {
        QGraphicsTextItem* textItem = scene->addText(QString::fromStdString(graphics_shape_.text));
        textItem->setDefaultTextColor(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]));
        textItem->setPos(graphics_shape_.center_position.x, graphics_shape_.center_position.y);
        item = textItem;
    }
    break;
    case GraphicsShape::Region:
    {
        QPainterPath path;
        for (const QRect& rect : graphics_shape_.region.rects())
        {
            path.addRect(rect);
        }
        auto path_item = new QGraphicsPathItem(path);
        path_item->setPen(QPen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]), graphics_shape_.thickness));
        scene->addItem(path_item);
        item = path_item;
        break;
    }
    case GraphicsShape::Polygon:
    {
        QPolygonF polygon;
        for (const auto& pt : graphics_shape_.polygon_points) {
            polygon << QPointF(pt.x, pt.y);
        }
        QPen pen(QColor(graphics_shape_.color[2], graphics_shape_.color[1], graphics_shape_.color[0]),
            graphics_shape_.thickness);
        pen.setStyle(Qt::DashLine);
        item = scene->addPolygon(polygon, pen);
    }
    break;
    case GraphicsShape::Image:
    {
        QPixmap pixmap = CvMatToQPixmap(graphics_shape_.image);
        QGraphicsPixmapItem* imageItem = scene->addPixmap(pixmap);
        imageItem->setPos(graphics_shape_.center_position.x - pixmap.width() / 2.0, graphics_shape_.center_position.y - pixmap.height() / 2.0);
        item = imageItem;
    }
    break;
    default:
        return -1;
    }

    if (item) {
        item->setZValue(graphics_shape_.thickness);
        std::lock_guard<std::mutex> lock(_image_mutex);
        _graphics_shapes.push_back(std::shared_ptr<QGraphicsItem>(item));
    }

    return 0;
}
int ViewerGraphicsViewImpl::ClearGraphicsShapes()
{
    std::lock_guard<std::mutex> lock(_image_mutex);
    for (auto& shape : _graphics_shapes)
    {
        scene->removeItem(shape.get());
    }
    _graphics_shapes.clear();
    return 0;
}
int ViewerGraphicsViewImpl::ClearImage(const int& set_key_, int key_)
{
    std::lock_guard<std::mutex> lock(_image_mutex);

    // 全局清除所有图像项
    if (set_key_ == -1)
    {
        for (auto& [_, key_and_imgs] : _key_and_images)
        {
            for (auto& [_key, item] : key_and_imgs)
            {
                scene->removeItem(item.get());
            }
        }
        _key_and_images.clear();
        //scene->clear(); // 如有网格或辅助线可避免调用
        return 0;
    }

    // 找不到 set_key_ 时返回失败
    auto it_map = _key_and_images.find(set_key_);
    if (it_map == _key_and_images.end())
        return -1;

    auto& img_map = it_map->second;

    // 清除当前 set_key_ 下所有图片
    if (key_ == -1)
    {
        for (auto& [_, item] : img_map)
        {
            scene->removeItem(item.get());
        }
        img_map.clear();
        return 0;
    }

    // 删除某一张特定图片
    auto it = img_map.find(key_);
    if (it != img_map.end())
    {
        scene->removeItem(it->second.get());
        img_map.erase(it);
        return 0;
    }

    return -1;
}


int ViewerGraphicsViewImpl::ShowImageByKey(const uint8_t& set_key_, int key_)
{
    int res = -1;
    bool is_any_image_shown = false;

    for (const auto& [set_key, imgs] : _key_and_images)
    {
        if (set_key_ != set_key)
        {
            for (const auto& [temp_key_, temp_item_] : imgs)
            {
                if (temp_item_->isVisible())  // 只有已显示的图像才隐藏
                {
                    temp_item_->setVisible(false);
                }
            }
        }
        else
        {
            for (const auto& [temp_key_, temp_item_] : imgs)
            {
                if (temp_key_ != key_ && temp_item_->isVisible())  // 只有已显示的图像才隐藏
                {
                    temp_item_->setVisible(false);
                }
                else if (temp_key_ == key_ && !temp_item_->isVisible())
                {
                    temp_item_->setVisible(true);
                    is_any_image_shown = true;
                    res = 0;  // 成功显示图像
                }
            }
        }
    }

    //auto& img_map = _key_and_images[set_key_];
    //// 先隐藏所有图像
    //for (auto& [temp_key_, temp_item_] : img_map)
    //{

    //    if (temp_key_ != key_ && temp_item_->isVisible())  // 只有已显示的图像才隐藏
    //    {
    //        temp_item_->setVisible(false);
    //    }
    //    else if (temp_key_ == key_ && !temp_item_->isVisible())
    //    {
    //        temp_item_->setVisible(true);
    //        is_any_image_shown = true;
    //        res = 0;  // 成功显示图像
    //    }
    //    qDebug() << "Item Key:" << temp_key_ << " visibility" << temp_item_->isVisible();
    //}

    // 如果有图像被显示，触发场景更新
    if (is_any_image_shown)
    {
        scene->update();
    }
    return res;
}

void ViewerGraphicsViewImpl::resizeEvent(QResizeEvent* event)
{
    if (!scene || scene->items().isEmpty())
    {
        QGraphicsView::resizeEvent(event);
        return;
    }

    auto delta_size = event->size() - event->oldSize();
    translate(-delta_size.width(), -delta_size.height());
    //PrintCenterPoint();

}

void ViewerGraphicsViewImpl::SlotMoveCamera(const double& x_, const double& y_)
{
    centerOn(-x_, y_);
    //PrintCenterPoint();
}

void ViewerGraphicsViewImpl::SlotSetZoom(const float& scale_)
{
    //qDebug() << "[" << __FUNCTION__ << "] [" << __LINE__ << "]" << "this scale:" << scale_;
    auto real_scale = scale_ / this->transform().m11();
    scale(real_scale, real_scale);
    //PrintCenterPoint();
}



QPixmap ViewerGraphicsViewImpl::CvMatToQPixmap(const cv::Mat& src)
{
    switch (src.type())
    {
        // 8-bit, 4 channel
    case CV_8UC4:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_ARGB32);

        return QPixmap::fromImage(image);
    }

    // 8-bit, 3 channel
    case CV_8UC3:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_RGB888);

        return QPixmap::fromImage(image.rgbSwapped());
    }

    // 8-bit, 1 channel
    case CV_8UC1:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return QPixmap::fromImage(image);
    }

    // 32-bit, 1 channel
    case CV_32FC1:
    {
        //cv::Mat* imgNormalize = new cv::Mat(src.rows, src.cols, CV_8UC1);
        cv::Mat imgNormalize;
        cv::normalize(src, imgNormalize, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        QImage image(imgNormalize.data,
            imgNormalize.cols, imgNormalize.rows,
            static_cast<int>(imgNormalize.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return QPixmap::fromImage(image);
    }

    default:
        break;
    }

    return QPixmap();
}


QImage ViewerGraphicsViewImpl::CvMatToQimage(const cv::Mat& src)
{
    switch (src.type())
    {
        // 8-bit, 4 channel
    case CV_8UC4:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_ARGB32);

        return image;
    }

    // 8-bit, 3 channel
    case CV_8UC3:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_RGB888);

        return image.rgbSwapped();
    }

    // 8-bit, 1 channel
    case CV_8UC1:
    {
        QImage image(src.data,
            src.cols, src.rows,
            static_cast<int>(src.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return image;
    }

    // 32-bit, 1 channel
    case CV_32FC1:
    {
        //cv::Mat* imgNormalize = new cv::Mat(src.rows, src.cols, CV_8UC1);
        cv::Mat imgNormalize;
        cv::normalize(src, imgNormalize, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        QImage image(imgNormalize.data,
            imgNormalize.cols, imgNormalize.rows,
            static_cast<int>(imgNormalize.step),
            QImage::Format_Grayscale8);//Format_Alpha8 and Format_Grayscale8 were added in Qt 5.5
        return image;
    }

    default:
        break;
    }

    return QImage();
}
QImage ViewerGraphicsViewImpl::CharToQimage(std::vector<char>& src_)
{
    QImage image;
    unsigned char* _data = reinterpret_cast<unsigned char*>(src_.data());
    image.loadFromData(_data, static_cast<uint>(src_.size()));
    if (image.isNull())
    {
        //
        std::cout << "图片为空,请检查";
    }
    return image;
}
QPixmap ViewerGraphicsViewImpl::CreateCheckerboardBackground(int size)
{
    QPixmap pixmap(size * 2, size * 2);
    pixmap.fill(Qt::white);
    QPainter painter(&pixmap);

    QColor color1("#38a8a8");
    QColor color2("#38a8a8");
    //QColor color1(200, 200, 200);  // 浅灰色
    //QColor color2(255, 255, 255);  // 白色

    painter.fillRect(0, 0, size, size, color1);
    painter.fillRect(size, size, size, size, color1);
    painter.fillRect(size, 0, size, size, color2);
    painter.fillRect(0, size, size, size, color2);

    painter.end();
    return pixmap;
}

void ViewerGraphicsViewImpl::ConvertDepthToGray(cv::Mat& gray_image_, const cv::Mat& depth_image_)
{
    // 检查输入深度图是否为 CV_32FC1 类型
    if (depth_image_.type() != CV_32FC1) {
        return;
    }

    // Step 1: 归一化，将深度图的值归一化到 [0, 255] 的范围
    cv::Mat normalizedDepthImage;
    cv::normalize(depth_image_, normalizedDepthImage, 0, 255, cv::NORM_MINMAX);

    // Step 2: 将归一化后的浮点型图像转换为 8 位图像
    normalizedDepthImage.convertTo(gray_image_, CV_8UC1);
}

void ViewerGraphicsViewImpl::PrintCenterPoint()
{
    if (scene->items().size() > 0)
    {
        auto item = scene->items().first();
        QPointF item_center = item->boundingRect().center();  // 获取场景中心点
        QPointF item_to_scene_center = mapFromScene(item_center);  // 将场景中心点转换为视图中的位置
        qDebug() << "[" << __FUNCTION__ << "] [" << __LINE__ << "]" << "image center:" << item_center << " center of item in scene" << item_to_scene_center;
    }
    QPointF scene_center = scene->sceneRect().center();  // 获取场景中心点
    QPointF view_center = mapFromScene(scene_center);  // 将场景中心点转换为视图中的位置
    qDebug() << "[" << __FUNCTION__ << "] [" << __LINE__ << "]" << "scene center:" << scene_center << " center of scene in view" << view_center;
    QPointF viewCenterInScene = mapToScene(viewport()->rect().center());
    qDebug() << "[" << __FUNCTION__ << "] [" << __LINE__ << "]" << "view center:" << viewport()->rect().center() << " center of view in scene" << viewCenterInScene;

    qDebug() << "[" << __FUNCTION__ << "] [" << __LINE__ << "]" << "this window size:" << this->size();

    qreal scale_x = this->transform().m11();  // 水平方向的缩放因子
    qreal scale_y = this->transform().m22();  // 垂直方向的缩放因子

    qDebug() << "当前 Zoom（X方向）:" << scale_x;
    qDebug() << "当前 Zoom（Y方向）:" << scale_y;
}

void ViewerGraphicsViewImpl::CreateImageOnMainThread(uint8_t set_key_, int key_, const QPixmap& q_pixel, int x, int y, int z, float angle, bool is_draw_center)
{
    std::lock_guard<std::mutex> lock(_image_mutex);

    std::shared_ptr<QGraphicsPixmapItem> item = nullptr;
    auto& img_map = _key_and_images[set_key_];
    auto it = img_map.find(key_);
    if (it != img_map.end())
    {
        item = it->second;
        item->setPixmap(q_pixel);
    }
    else
    {
        item = std::make_shared<QGraphicsPixmapItem>(q_pixel);
        img_map[key_] = item;
        scene->addItem(item.get());
    }

    item->setZValue(z);
    item->setRotation(angle);

    if (is_draw_center)
    {
        qreal center_x = x + item->pixmap().width() / 2.0;
        qreal center_y = y + item->pixmap().height() / 2.0;
        item->setPos(center_x, center_y);
    }
    else
    {
        item->setPos(x, y);
    }

    //centerOn(item->boundingRect().center());
    //ShowImageByKey(key_);
}

void ViewerGraphicsViewImpl::ConvertMatToPixmap(const std::unordered_map<int, cv::Mat>& mats_,
    std::vector<std::pair<int, QPixmap>>& pixmaps)
{
    std::mutex pixmap_mutex; // 保护pixmaps容器的mutex
    std::vector<std::thread> threads;

    // 先转换所有图像
    for (const auto& mat : mats_)
    {
        threads.push_back(std::thread([&, mat] {
            // 进行图像转换
            QPixmap pixmap = CvMatToQPixmap(mat.second);

            // 使用mutex来保护共享资源pixmaps
            {
                std::lock_guard<std::mutex> lock(pixmap_mutex);
                pixmaps.push_back({ mat.first, pixmap });
            }
            }));
    }

    // 等待所有线程完成
    for (auto& thread : threads)
    {
        thread.join();
    }
}
