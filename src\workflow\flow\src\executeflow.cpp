//! 丢弃使用 by zhangyuyu 2024.12.1
/// ////STD
//#include <limits>  // std::numeric_limits
//#include <chrono>
//#include <algorithm>
////Custom
//#include "coreapplication.h"
//#include "generalimagetool.h"
//#include "executeflow.h"
//#include "image.hpp"
//#include "coordinatetransform.hpp"
//#include "algorithmenginemanager.h"
//#include "algoexecuteparam.hpp"
////Third
//#include "opencv2/opencv.hpp"
//
//#undef min
//#undef max
//namespace jrsworkflow
//{
//    ExecuteFlow::ExecuteFlow(const jrsdevice::DeviceManagerPtr& device_ptr_, const std::shared_ptr<jrsalgo::AlgorithmEngineManager>& algo_engine_manager_)
//        : device_ptr(device_ptr_)
//        , algo_engine_manager_ptr(algo_engine_manager_)
//        , is_running(false)
//        , is_wating_up(false)
//        , is_wating_down(false)
//        , is_scanning(false)
//
//    {
//        InitMember();
//       
//
//    }
//    ExecuteFlow::~ExecuteFlow()
//    {}
//    void ExecuteFlow::SetLogicInvokeFun(std::function<void(const std::vector<CalcFov::SeparateFovPath>&)> logic_invoke_)
//    {
//        logic_invoke = logic_invoke_;
//    }
//    void ExecuteFlow::AddBuffer(const jrsdata::JrsImageBuffer& imgs)
//    {
//        if (is_running)
//        {
//            std::lock_guard<std::mutex> lock(detect_mtx);
//            Detect(imgs);
//
//        }
//        if (imgs.boarder_imgs.imgs.size() != 0)
//        {
//            is_scanning.store(true);
//            work_flow_cv.notify_one();
//        }
//    }
//    void ExecuteFlow::InitMember()
//    {
//        up_down_material = std::make_shared<UpDownMaterial>(device_ptr);
//
//    }
//    int ExecuteFlow::Execute(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
//    {
//        //检测流程
//        project_param_ptr = project_param_;
//
//
//        while (is_running)
//        {
//            //std::vector<cv::Point2f> fov_centers; // 没地方存,先放这里
//            std::vector<CalcFov::SeparateFovPath> fov_centers;
//            //! 上料
//            up_down_material->UpMaterial(1);
//
//            std::this_thread::sleep_for(std::chrono::milliseconds(500));
//
//            //! MARK矫正
//            MarkCorrection();
//            //! 分配fov
//            AssignComponentToFov(fov_centers, project_param_ptr->board_info, 4096, 3072);
//            //! 拍图 ,拍图过程中接受到 FOV图片后则开始检测
//            logic_invoke(fov_centers);
//
//            //!等待扫图完成
//            std::unique_lock<std::mutex> lock(work_flow_mtx);
//            work_flow_cv.wait(lock, [this]
//                {
//                    return is_scanning.load();
//                });
//            is_scanning.store(false);
//
//            //!下料
//            up_down_material->DownMaterial(1);
//            std::this_thread::sleep_for(std::chrono::milliseconds(100));
//
//
//        }
//        return jrscore::AOI_OK;
//
//    }
//    void ExecuteFlow::MarkCorrection()
//    {
//
//        //offset_martix;
//
//        //project_param_ptr
//    }
//    void ExecuteFlow::Detect(const jrsdata::JrsImageBuffer& imgs)
//    {
//        (void)imgs;
//
//        if (assign_component_res.empty())
//        {
//            Log_ERROR("流程运行异常，分配的原件结果为空！");
//            return;
//        }
//        auto res_components = assign_component_res.at(imgs.one_fov_imgs.fov_id);
//        cv::Point2f fov_center;
//
//        for (auto& value : fov_out.fovs)
//        {
//            if (value.fovid == imgs.one_fov_imgs.fov_id)
//            {
//                fov_center = value.center;
//            }
//        }
//        /** 将fov的中心坐标，转换成左上角坐标 */
//        int fov_left_top_x, fov_left_top_y;
//        jrscore::CoordinateTransform::PixelRectCenterToTopLeft(static_cast<int>(fov_center.x), static_cast<int>(fov_center.y), fov_left_top_x, fov_left_top_y, 4096, 3072);
//        for (auto& component_value : res_components)
//        {
//
//            for (auto& windows_info : project_param_ptr->board_info.part_nums_and_detect_regions[component_value.component_part_number].detect_models)
//            {
//                for (auto& value_window : windows_info.second.detect_model)
//                {
//                    /** 当前算法检测区域在大图中的检测框的中心坐标 ,因为工程中存储的算法检测区域都是相对于元件坐标的相对坐标 */
//                    auto absoulte_window_x_in_big_img = component_value.x + value_window.cx;
//                    auto absoulte_window_y_in_big_img = component_value.y + value_window.cy;
//
//                    /** 当前算法检测区域在FOV中的中心坐标 */
//                    int absoulte_window_center_x_in_fov;
//                    int absoulte_window_center_y_in_fov;
//                    jrscore::CoordinateTransform::GlobalToLocal(absoulte_window_x_in_big_img, absoulte_window_y_in_big_img
//                        , absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov, fov_left_top_x,
//                        fov_left_top_y);
//
//                    int absoulte_window_left_top_x_in_fov;
//                    int absoulte_window_left_top_y_in_fov;
//
//                    jrscore::CoordinateTransform::PixelRectCenterToTopLeft(absoulte_window_center_x_in_fov, absoulte_window_center_y_in_fov,
//                        absoulte_window_left_top_x_in_fov, absoulte_window_left_top_y_in_fov,
//                        value_window.width, value_window.height);
//                    cv::Mat window_mat;
//                    cv::Rect window_rect(absoulte_window_left_top_x_in_fov, absoulte_window_left_top_y_in_fov, value_window.width, value_window.height);
//
//                    for (auto& algo_info : value_window.algorithms)
//                    {
//
//                        if (imgs.one_fov_imgs.imgs.empty())
//                        {
//
//                            return;
//                        }
//                        auto res = jrstool::CropAndPasteTool::CropImage(imgs.one_fov_imgs.imgs.at(static_cast<jrsdata::LightImageType>(algo_info.light_image_id)),
//                            window_rect, window_mat);
//
//                        if (res == jrscore::AOI_OK)
//                        {
//                            Log_ERROR("检测流程中，算法裁图失败，请检查工程检测框参数是否异常");
//                        }
//                        //! 检测算法名称
//
//                        //TODO: 临时测试， 待删除 by zhangyuyu
//                        jrsalgo::ExecuteAlgoParam execute_param;
//                        execute_param.algo_name = algo_info.detect_algorithm_name;
//                        execute_param.algo_param = algo_info.param;
//                        jrsoperator::JrsImage input_img;
//                        execute_param.input_img = input_img.FromMat(window_mat);
//
//                        jrsoperator::JrsImage temp_img_jrs;
//                        cv::Mat tmp_img;
//                        cv::Rect window_rect_temp(50, 50, 10, 10);
//                        jrstool::CropAndPasteTool::CropImage(window_mat,
//                            window_rect_temp, tmp_img);
//                        execute_param.template_img.emplace_back(temp_img_jrs.FromMat(tmp_img));
//                        /********************************************/
//                        //! 调用算法
//                        auto execute_res = algo_engine_manager_ptr->ExecuteSpecificAlgoDrive(execute_param);
//                    }
//                }
//            }
//        }
//
//    }
//
//    void ExecuteFlow::AssignComponentToFov(std::vector<CalcFov::SeparateFovPath>& fov_centers, jrsdata::Board& board, const int& fov_w, const int& fov_h)
//    {
//        //! 工程中所有的元件信息
//        std::vector<jrsdata::Component> components;
//        for (auto& subboard : board.sub_board)
//        {
//            components.insert(components.end(), subboard.component_info.begin(), subboard.component_info.end());
//        }
//
//        //! 计算每个元件的所有检测框的最小外接矩形存入分配FOV的数据结构中
//        FovPlan::FovPlanParam param;
//        for (auto& compinent_value : components)
//        {
//            auto part = ReadPartRef(board, compinent_value.component_part_number);
//            if (!part.has_value())
//                continue;
//            int x, y, width, height;
//            //!计算元件的所有检测框的的最小外接矩形，能够将所有算法检测框包含进去的外接矩形
//            CalcComponentBoundingbox(x, y, width, height, compinent_value, part->get());
//
//            //! 元件分配的时的数据结构赋值
//            CalcFov::ObjectInput object_input;
//            //! 元件的外接矩形
//            object_input.rr = cv::RotatedRect(cv::Point(x + width / 2, y + height / 2), cv::Size(width, height), 0);
//            //!元件名
//            object_input.id = compinent_value.component_name;
//            param.rrs.emplace_back(object_input);
//
//        }
//
//        param.fov_h = fov_h;
//        param.fov_w = fov_w;
//        param.region_h = board.height;
//        param.region_w = board.width;
//        param.region_x = 0;
//        param.region_y = 0;
//        param.mode_path = FovPlan::PathPlanMode::SNAKE_BOTTOM_TO_TOP;
//        param.mode_position = FovPlan::PositionPlanMode::ROW_MAJOR_MBR;
//        FovPlan::TestFovPlan(param);
//        //! 规划FOV位置，这里根据元件的最小外接矩形进行规划
//        FovPlan::DoFovPlan(fov_out, param);
//
//        //! 遍历所有fov
//        assign_component_res.clear();
//        for (const auto& fov : fov_out.fovs)
//        {
//            //！ 遍历当前 FOV 中所有的最小外接矩形
//            for (const auto& covered_rectangle : fov.coveredRectangles)
//            {
//                //！ 查找与当前外接矩形匹配的元件信息，每个外接矩形，对应一个元件，一一对应的
//                auto component_it = std::find_if(components.begin(), components.end(),
//                    [&covered_rectangle](auto& component)
//                    {
//                        return covered_rectangle.id == component.component_name;
//                    });
//
//                if (component_it != components.end())
//                {
//                    //! 更新元件的 FOV ID
//                    //! 如果covered_rectangle.fovid>0说明只在一个FOV中，否则说明当前元件在多个 FOV中
//                    if (covered_rectangle.fovid >= 0)
//                    {
//                        assign_component_res[covered_rectangle.fovid].push_back(*component_it);
//
//                        component_it->component_name;
//                        component_it->fov_ids = { covered_rectangle.fovid };
//                    }
//                    else
//                    {
//                        //TODO:跨FOV元件需要完善 
//                        component_it->fov_ids = covered_rectangle.fovids;
//                    }
//                }
//            }
//        }
//        fov_centers.swap(fov_out.fov_path_with_id);
//    }
//
//    void ExecuteFlow::CalcComponentBoundingbox(int& x, int& y, int& width, int& height, const jrsdata::Component& component, const jrsdata::PNDetectInfo& part)
//    {
//        auto& units = part.units;
//        auto it_body = std::find_if(units.begin(), units.end(),
//            [&](const jrsdata::ComponentUnit& elem) { return elem.unit_type == jrsdata::ComponentUnit::Type::BODY; });
//        if (it_body != units.end())
//        {
//            width = it_body->width;
//            height = it_body->height;
//        }
//
//        // 中心位置
//        x = component.x, y = component.y;
//        //width = component.width, height = component.height;
//        int xmin = std::numeric_limits<int>::max(), xmax = std::numeric_limits<int>::min(),
//            ymin = std::numeric_limits<int>::max(), ymax = std::numeric_limits<int>::min();
//        for (auto& [key, val] : part.detect_models)
//        {
//            auto& windows = val.detect_model;
//
//            for (auto& win : windows)
//            {
//                if (xmin > win.cx)
//                    xmin = win.cx;
//                if (xmax < win.cx)
//                    xmax = win.cx;
//                if (ymin > win.cy)
//                    ymin = win.cy;
//                if (ymax < win.cy)
//                    ymax = win.cy;
//            }
//        }
//        if (xmax > xmin && ymax > ymin)
//        {
//            x = std::min(x, x + xmin);
//            y = std::min(y, y + ymin);
//            width = std::max(width, xmax - xmin);
//            height = std::max(height, ymax - ymin);
//        }
//        // 应用角度
//        auto rr = cv::RotatedRect(cv::Point(x, y), cv::Size(width, height), component.angle);
//        auto rect = rr.boundingRect();
//        // 左上角位置
//        x = rect.x;
//        y = rect.y;
//        width = rect.width;
//        height = rect.height;
//    }
//
//    std::optional<std::reference_wrapper<const jrsdata::PNDetectInfo>> ExecuteFlow::ReadPartRef(const jrsdata::Board& board, const std::string& part_name)
//    {
//        auto& parts = board.part_nums_and_detect_regions;
//        auto it = parts.find(part_name);
//        if (it != parts.end())
//        {
//            return std::ref(it->second);
//        }
//        return std::nullopt;
//    }
//
//    int ExecuteFlow::StartWorkFlow(const std::shared_ptr<jrsdata::ProjectParam>& project_param_)
//    {
//        is_running = true;
//        is_scanning.store(false);
//        work_flow_thread = std::thread(&ExecuteFlow::Execute, this, project_param_);
//
//        return jrscore::AOI_OK;
//    }
//    int ExecuteFlow::StopWorkFlow()
//    {
//        is_running.store(false);
//        is_scanning.store(true);
//        work_flow_cv.notify_one();
//
//        work_flow_thread.join();
//        return 0;
//    }
//}
