﻿/*********************************************************************
 * @brief  简单的日志.
 *
 * @file   log.h
 *
 * @date   2024.04.12
 * <AUTHOR>
 *********************************************************************/
#pragma once

#ifndef LOG_H_20240412
#define LOG_H_20240412

#include "controlconstants.hpp" // VisionMode
#include "graphicsabstract.hpp" // GraphicsAbstract
#include <string>
#include <ostream> //std::ostream
#include <sstream> //std::stringstream

#define printInfo(info) printDebugInfo(info, "", __LINE__, __FUNCTION__)



void printDebugInfo(const char* info, const char* file, int line, const char* function);
void printDebugInfo(const std::stringstream& ss, const char* file, int line, const char* function);

inline std::ostream& operator<<(std::ostream& os, const VisionMode& menum)
{
    switch (menum)
    {
    case VisionMode::HOVER:
        os << "HOVER";
        break;
    case VisionMode::MOVE_CAMERA:
        os << "MOVE_CAMERA";
        break;
    case VisionMode::VIEW_ALL:
        os << "VIEW_ALL";
        break;
    case VisionMode::SELECT_GRAPHICS:
        os << "SELECT_GRAPHICS";
        break;
    case VisionMode::SELECT_GRAPHICS_BATCH:
        os << "SELECT_GRAPHICS_BATCH";
        break;
    case VisionMode::SELECT_GRAPHICS_BATCH_POLYGON:
        os << "SELECT_GRAPHICS_BATCH_POLYGON";
        break;
    case VisionMode::RESPONSE_GRAPHICS:
        os << "RESPONSE_GRAPHICS";
        break;
    case VisionMode::EDIT_GRAPHICS:
        os << "EDIT_GRAPHICS";
        break;
    case VisionMode::CREATE_GRAPHICS:
        os << "CREATE_GRAPHICS";
        break;
    default:
        os << "UNKNOWN VisionMode";
        break;
    }
    return os;
}

inline std::ostream& operator<<(std::ostream& os, const GraphicsAbstract& value)
{
    os << "graphics:" << value.GetId().GetString() << ","
        << "w:" << value.w() << ","
        << "h:" << value.h() << ","
        << "x:" << value.x() << ","
        << "y:" << value.y() << ","
        << "a:" << value.a() << ","
        ;
    return os;
}
#endif //! LOG_H_20240412
