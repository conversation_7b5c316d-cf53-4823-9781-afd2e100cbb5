/*****************************************************************/ /**
* @file   componentlibrary.h
* @brief 历史元件库对外接口库
*
* <AUTHOR>
* @date   2025.3.16
*********************************************************************/
#ifndef COMPONENTLIBRARY_H
#define COMPONENTLIBRARY_H
#include <QObject>

// STD
#include <filesystem>

// Thirdparty
#include "viewparam.hpp"
#include <opencv2/opencv.hpp>

#include "savecomponentdialog.h"
#include "custommessagebox.h"

namespace jrsaoi
{
    // 添加元件时使用的方式
    enum AddType
    {
        Default = 0, /** 默认方式,只有元件库里面不存在才添加,否则不做处理 */
        Overwrite,   /** 覆盖保存 */
        PartNumFirst /** 按照料号保存 */
    };

    // 多条件查询结构体
    struct MultiConditions
    {
        int width;                 /** 元件宽度 */
        int height;                /** 元件高度 */
        int pad_count;             /** 元件PAD数量 */
        std::string part_name;    /** 原料料号名称 */
        MultiConditions()
            :width(0), height(0), pad_count(0), part_name("")
        {

        }
        MultiConditions(int width_, int height_, int padcount, std::string partname)
            :width(width_), height(height_), pad_count(padcount), part_name(partname)
        {

        }
    };
    class ComponentLibrary: public QObject
    {
        Q_OBJECT
    public:
        ComponentLibrary(const std::string path);
        ~ComponentLibrary();

        /**
         * @fun SetComponentName
         * @brief 设置元件库名称(读取对应元件库下所有元件信息保存到component_entitys)
         * @param name 元件库名称
         * <AUTHOR>
         * @date 2025.3.30
         */
        void SetComponentName(const std::string name);

        /**
         * @fun AddComponent
         * @brief 添加多个元件
         * @param components 多个元件信息
         * @param type 添加元件的方式(弹框提醒、覆盖、按照料号保存)
         * @param savefile 是否保存文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void AddComponent(std::vector<jrsdata::ComponentEntity> components, AddType type = AddType::Default, bool savefile = true);

        /**
         * @fun RemoveComponent
         * @brief 根据料号删除多个元件
         * @param partnumbers 料号集合
         * <AUTHOR>
         * @date 2025.3.30
         */
        void RemoveComponent(const std::vector<std::string> partnumbers);

        /**
         * @fun ClearComponents
         * @brief 清空元件库
         * @param deletefiles 是否删除元件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void ClearComponents(bool deletefiles = false);

        /**
         * @fun QueryComponent
         * @brief 查询单个元件(根据料号查询)
         * @param component 查询的结果信息
         * @param partnumber 料号
         * <AUTHOR>
         * @date 2025.3.30
         */
        void QueryComponent(jrsdata::ComponentEntity& component, const std::string partnumber);

        /**
         * @fun QueryComponents
         * @brief 查询所有元件
         * @param components 查询的结果信息
         * <AUTHOR>
         * @date 2025.3.30
         */
        void QueryComponents(std::vector<jrsdata::ComponentEntity>& components);

        /**
         * @fun QueryComponents
         * @brief 多条件查询元件
         * @param components 查询的结果信息
         * @param conditions 查询条件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void QueryComponents(std::vector<jrsdata::ComponentEntity>& components, MultiConditions conditions);

        /**
         * @fun MergeComponentLibrary
         * @brief 根据元件库名称进行元件库合并(TODO:待实现)
         * <AUTHOR>
         * @date 2025.3.30
         */
        void MergeComponentLibrary(const std::string name);

         /**
         * @fun GetComponentFolders
         * @brief 获取元件库里面所有的元件库分类
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::vector<std::string> GetComponentFolders();

        /**
         * @fun GetComponentLibPath
         * @brief 获取当前元件库绝对地址
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::string GetComponentLibPath();

        /**
         * @fun GetComponentLibFolderPath
         * @brief 获取当前元件库文件夹地址
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::string GetComponentLibFolderPath();

        /**
         * @fun SetComponentLibFolderPath
         * @brief 设置当前元件库文件夹地址(用于切换元件库地址切换进行热更新)
         * <AUTHOR>
         * @date 2025.3.30
         */
        void SetComponentLibFolderPath(const std::string path);

        /**
         * @fun GetComponentName
         * @brief 获取当前元件库名称
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::string GetComponentName();

        /**
         * @fun GetPADCount
         * @brief 获取指定元件里面的PAD数量
         * @param component 元件信息
         * <AUTHOR>
         * @date 2025.3.30
         */
        int GetPADCount(const jrsdata::ComponentEntity& component);

    signals:
        void SigComponent(const jrsdata::ViewParamBasePtr& param);

    private:

        /**
         * @fun AddComponent
         * @brief 添加单个元件
         * @param component 元件信息
         * @param type 添加元件的方式(弹框提醒、覆盖、按照料号保存)
         * @param savefile 是否保存文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void AddComponent(jrsdata::ComponentEntity component, AddType type = AddType::Default, bool savefile = true);

        /**
         * @fun RemoveComponent
         * @brief 删除单个元件
         * @param partnumber 料号
         * <AUTHOR>
         * @date 2025.3.30
         */
        void RemoveComponent(const std::string partnumber);

        /**
         * @fun HasComponentWithBindPartName
         * @brief 查找元件库里面跟随料号名称是partname的元件
         * @param partname 料号名称
         * @param component 查找的元件结果
         * <AUTHOR>
         * @date 2025.3.30
         */
        bool HasComponentWithBindPartName(const std::string partname, jrsdata::ComponentEntity& component);

        /**
         * @fun SaveComponent
         * @brief 保存元件(先将component转为二进制数据,再进行文件保存)
         * @param component 元件信息
         * @param savefile 是否保存文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void SaveComponent(jrsdata::ComponentEntity& component, bool savefile = true);

        /**
         * @fun LoadComponents
         * @brief 获取指定路径下的元件库
         * @param path 加载指定地址下的所有元件库文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void LoadComponents(const std::string path);

        /**
         * @fun ShowSaveDialog
         * @brief 元件库保存弹框
         * @param newComponent 新的元件数据
         * @param existingComponent 已存在的元件引用(将被更新)
         * @param savefile 是否保存到文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void ShowSaveDialog(const jrsdata::ComponentEntity& new_component,jrsdata::ComponentEntity& existing_component,bool savefile);

        /**
         * @fun OverwriteComponent
         * @brief 覆盖已存在的元件
         * @param newComponent 新的元件数据
         * @param existingComponent 已存在的元件引用(将被更新)
         * @param savefile 是否保存到文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void OverwriteComponent(const jrsdata::ComponentEntity& newComponent, jrsdata::ComponentEntity& existingComponent, bool savefile);

        /**
         * @fun AddAsNewComponent
         * @brief 将元件作为新元件添加(即使料号相同也新增)
         * @param component 要添加的元件
         * @param savefile 是否保存到文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void AddAsNewComponent(const jrsdata::ComponentEntity& component, bool savefile);

        /**
         * @fun HandleExistingComponent
         * @brief 处理已经存在的元件
         * @param component 要添加的元件
         * @param existing_component 元件库中存在的元件
         * @param savefile 是否保存到文件
         * <AUTHOR>
         * @date 2025.3.30
         */
        void HandleExistingComponent(const jrsdata::ComponentEntity& component,jrsdata::ComponentEntity& existing_component,bool savefile);

        /**
         * @fun GetBindPartName
         * @brief 获取元件料号跟随
         * @param component 元件信息
         * <AUTHOR>
         * @date 2025.3.30
         */
        std::string GetBindPartName(const jrsdata::ComponentEntity& component);

    private:
        std::string component_file_path;                /** 元件库文件夹地址 */
        std::string component_name;                     /** 使用的元件库名称 */
        std::vector<jrsdata::ComponentEntity> component_entitys; /** 元件库集合 */
        jrsaoi::AddType cur_type;
    };

    using ComponentLibraryPtr = std::shared_ptr<ComponentLibrary>;
}

#endif // COMPONENTLIBRARY_H