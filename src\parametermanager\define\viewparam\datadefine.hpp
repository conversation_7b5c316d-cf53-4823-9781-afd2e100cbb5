﻿/*****************************************************************//**
 * @file   datadefine.hpp
 * @brief 数据定义
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef DATADEFINE_HPP
#define DATADEFINE_HPP
#include <vector>
#include <string>
#include <iostream>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <chrono>


static constexpr char T_SHOW_BOARD[] = "t_show_board";              /**< 显示板子表 */
static constexpr char T_SHOW_PART_NUMBER[] = "t_show_part_number";  /**< 显示料号表 */
static constexpr char T_SHOW_DEVICE[] = "t_show_device";            /**< 显示元件表 */
namespace jrsdata {
    enum DetectResult/**<检测结果*/
    {
        SKIP = -1,
        NG = 0,
        GOOD,
        PASS,
    };

    inline std::string DetectResultToString(DetectResult result) {
        switch (result) {
        case GOOD: return "GOOD";
        case PASS: return "PASS";
        case NG:   return "NG";
        case SKIP: return "SKIP";
        default:   return "UNKNOWN";
        }
    }
}
/**
 * @struct CustomRect
 * @brief QRect替换数据定义
 * @date 2024.2.6
 * <AUTHOR>
 */
struct CustomRect {
    int x;      // 矩形的左上角 x 坐标
    int y;      // 矩形的左上角 y 坐标
    int width;  // 矩形的宽度
    int height; // 矩形的高度
    CustomRect(int x, int y, int width, int height)
        : x(x), y(y), width(width), height(height) {
    }
    // 计算矩形的面积
    int area() const {
        return width * height;
    }
    void SetX(int value)
    {
        x = value;
    }
    void SetY(int value)
    {
        y = value;
    }
    void SetWidth(int value)
    {
        width = value;
    }
    void SetHeight(int value)
    {
        height = value;
    }
};
/**
 * @struct RingData
 * @brief 横向柱状图和竖向柱状图的数据元定义
 * @date 2024.2.6
 * <AUTHOR>
 */
struct RingData {
    double m_value;                                 //值
    double m_percent;                               //百分比
    std::string m_label;                            //标签
    std::vector<std::string> m_label_precent;       //包含百分比的标签
    CustomRect* m_rect;                             //柱状图的位置
    int m_color_idx;                                //背景色
    RingData()
    {
        m_color_idx = 0;
        m_percent = 0.0;
        m_label = "";
        m_value = 0.0;
        m_label_precent.clear();
    }
    RingData(std::string label, double value)
    {
        m_color_idx = 0;
        m_percent = 0.0;
        m_label = label;
        m_value = value;
        m_label_precent.clear();
    }
    RingData(std::string label, double value, double percent)
    {
        m_color_idx = 0;
        m_percent = percent;
        m_label = label;
        m_value = value;
        m_label_precent.clear();
    }
};
/**
 * @class DetectResultStruct
 * @brief 缺陷结果数据结构
 * @date 2024.2.6
 * <AUTHOR>
 */
class DetectResultStruct
{
public:
    DetectResultStruct()
    {
        m_id = 0;
        m_track_id = 0;
        m_barcode = "";
        m_result = jrsdata::DetectResult::NG;
        m_detect_time = jrscore::AOITools::GetCurrentDataTime();
    }
    std::vector<std::string> GetShowValue()
    {
        std::vector<std::string> list_variant;
        list_variant.push_back(std::to_string(m_id).c_str());
        list_variant.push_back(m_barcode.c_str());
        list_variant.push_back(m_detect_time.c_str());
        list_variant.push_back(std::to_string(m_track_id).c_str());

        list_variant.push_back(m_result ? "GOOD" : "NG");
        return list_variant;
    }
    std::vector<std::string> GetStringList()
    {
        std::vector<std::string> list;
        list.push_back(std::to_string(m_id));
        list.push_back(m_barcode);
        list.push_back(m_detect_time);
        list.push_back(std::to_string(m_track_id));
        list.push_back(jrsdata::DetectResultToString(m_result));
        return list;
    }
    int m_id;//id
    int m_track_id;
    std::string m_barcode;//二维码
    std::string m_detect_time;//检测时间
    jrsdata::DetectResult m_result;//检测结果
    long m_total_device_count;//总元件
    long m_test_device_count;//测试数
    long m_ng_device_count;//NG元件数
    std::string m_take_phone_time;//拍照时间
    std::string m_circle_time;//循环时间
};
/**
 * @class DetectResultRatioStruct
 * @brief 缺陷结果数据结构
 * @date 2024.2.6
 * <AUTHOR>
 */
class DetectResultRatioStruct
{
public:
    DetectResultRatioStruct()
    {
        m_id = 0;
        m_name = "";
        m_count = 0;
        m_percent = 0.0;
    }
    std::vector<std::string>  GetStringList()
    {
        std::vector<std::string>  list;
        list.push_back(m_name);
        list.push_back(std::to_string(m_count));
        list.push_back(jrscore::AOITools::DoubleToString(m_percent, 2));
        return list;
    }
    int m_id;//id
    std::string m_name;//名称
    int m_count;//数量
    double m_percent;//百分比
};
enum RESULT_STATE
{
    RESULT_NONE = -1,
    RESULT_NG,
    RESULT_OK,
    RESULT_UNTESTED
};
enum SHOW_TYPE
{
    SHOW_UN_EDITED = 1,//显示未编辑
    SHOW_UN_TEST,//显示不测试
    SHOW_NG,//显示NG
    SHOW_ALL_DEVICE//显示所有元件
};
enum SEARCH_SHOW_LIST_TYPE
{
    SEARCH_PART_NUMBER = 0,
    SEARCH_PART_NUMBER_FOLLOW,
    SEARCH_DEVICE_NAME
};
enum SHOW_DETECT_NG_TYPE
{
    SHOW_DEVICE_TYPE = 0,
    SHOW_NG_TYPE,
    SHOW_ANCHOR_TYPE
};
/** 数据表基类 */
struct ShowTableParamBase
{
    ShowTableParamBase() = default;
    virtual ~ShowTableParamBase() = default;
    std::string table_name;
};
using ShowTableParamBasePtr = std::shared_ptr<ShowTableParamBase>;

/**
 * @class DeviceDataStruct
 * @brief 元件数据结构
 * @date 2024.2.6
 * <AUTHOR>
 */
struct DeviceDataStruct : public ShowTableParamBase
{
public:
    DeviceDataStruct()
    {
        this->table_name = T_SHOW_DEVICE;
        m_subboard_name = "";
        m_subboard_id = 0;
        m_id = 0;
        m_part_number = "";
        m_device_name = "";
        m_x = 0.0;
        m_y = 0.0;
        m_width = 0.0;
        m_height = 0.0;
        m_angle = 0.0;
        m_body_shape_type = 1;
        m_body_tested = true;
        m_unedited = false;
        m_pad_number = "";
        m_pad_group_type = 0;
        m_pad_direction = 0;
        m_pad_shape = 0;
        m_pad_width = 0.0;
        m_pad_height = 0.0;
        m_pad_coordinate_x = 0.0;
        m_pad_coordinate_y = 0.0;
        m_pad_shaping_x = 0.0;
        m_pad_shaping_y = 0.0;
        m_enable_masked = false;
        m_enable_masked_skip_numbering = false;
        m_enable_exception = false;
        m_result_state = RESULT_STATE::RESULT_NONE;
        m_show_enable = true;
    };
    DeviceDataStruct(
        int m_id_,//序号
        std::string m_device_name_,//元件名称
        std::string m_old_device_name_,//元件名称
        float m_x_,
        float m_y_,
        float m_width_,
        float m_height_,
        float m_angle_,//角度
        std::string m_subboard_name_,
        int m_subboard_id_,//子板编号
        std::string m_part_number_,//料号
        int m_body_shape_type_,//本体形状
        bool m_body_tested_,//是否测试
        bool m_unedited_,//未编辑
        std::string m_pad_number_,//PAD编号
        std::vector<std::string> m_pad_group_items_,//PAD焊盘组别列表
        int m_pad_group_type_,//PAD组别
        int m_pad_direction_,//PAD方向
        int m_pad_shape_,//PAD形状
        float m_pad_width_,//PAD宽
        float m_pad_height_,//PAD高
        float m_pad_coordinate_x_,//PAD坐标X
        float m_pad_coordinate_y_,//PAD坐标Y
        float m_pad_shaping_x_,//PAD间距X
        float m_pad_shaping_y_,//PAD间距Y
        bool m_enable_masked_,//PAD属性屏蔽
        bool m_enable_masked_skip_numbering_,//PAD属性屏蔽跳过编号
        bool m_enable_exception_,//PAD特例
        RESULT_STATE m_result_state_,//结果
        bool m_show_enable_
    ):
    m_id(m_id_),
        m_device_name(m_device_name_),
        m_old_device_name(m_old_device_name_),
        m_x(m_x_),
        m_y(m_y_),
        m_width(m_width_),
        m_height(m_height_),
        m_angle(m_angle_),
        m_subboard_name(m_subboard_name_),
        m_subboard_id(m_subboard_id_),
        m_part_number(m_part_number_),
        m_body_shape_type(m_body_shape_type_),
        m_body_tested(m_body_tested_),
        m_unedited(m_unedited_),
        m_pad_number(m_pad_number_),
        m_pad_group_items(m_pad_group_items_),
        m_pad_group_type(m_pad_group_type_),
        m_pad_direction(m_pad_direction_),
        m_pad_shape(m_pad_shape_),
        m_pad_width(m_pad_width_),
        m_pad_height(m_pad_height_),
        m_pad_coordinate_x(m_pad_coordinate_x_),
        m_pad_coordinate_y(m_pad_coordinate_y_),
        m_pad_shaping_x(m_pad_shaping_x_),
        m_pad_shaping_y(m_pad_shaping_y_),
        m_enable_masked(m_enable_masked_),
        m_enable_masked_skip_numbering(m_enable_masked_skip_numbering_),
        m_enable_exception(m_enable_exception_),
        m_result_state(m_result_state_),
        m_show_enable(m_show_enable_)
    {
        this->table_name = T_SHOW_DEVICE;
    };
    DeviceDataStruct(const DeviceDataStruct& other) :
        m_id(other.m_id),
        m_device_name(other.m_device_name),
        m_old_device_name(other.m_old_device_name),
        m_x(other.m_x),
        m_y(other.m_y),
        m_width(other.m_width),
        m_height(other.m_height),
        m_angle(other.m_angle),
        m_subboard_name(other.m_subboard_name),
        m_subboard_id(other.m_subboard_id),
        m_part_number(other.m_part_number),
        m_body_shape_type(other.m_body_shape_type),
        m_body_tested(other.m_body_tested),
        m_unedited(other.m_unedited),
        m_pad_number(other.m_pad_number),
        m_pad_group_items(other.m_pad_group_items),
        m_pad_group_type(other.m_pad_group_type),
        m_pad_direction(other.m_pad_direction),
        m_pad_shape(other.m_pad_shape),
        m_pad_width(other.m_pad_width),
        m_pad_height(other.m_pad_height),
        m_pad_coordinate_x(other.m_pad_coordinate_x),
        m_pad_coordinate_y(other.m_pad_coordinate_y),
        m_pad_shaping_x(other.m_pad_shaping_x),
        m_pad_shaping_y(other.m_pad_shaping_y),
        m_enable_masked(other.m_enable_masked),
        m_enable_masked_skip_numbering(other.m_enable_masked_skip_numbering),
        m_enable_exception(other.m_enable_exception),
        m_result_state(other.m_result_state),
        m_show_enable(other.m_show_enable)
    {
        this->table_name = T_SHOW_DEVICE;
    };
    bool operator<(const DeviceDataStruct& other) const
    {
        return m_id < other.m_id;
    }
    std::vector<std::string>  GetStringList()
    {
        std::vector<std::string>  list;
        list.push_back(std::to_string(m_id));
        list.push_back(jrscore::AOITools::GetPrefixString(m_device_name));
        //list.push_back(m_device_name);
        list.push_back(jrscore::AOITools::DoubleToString(m_x, 2));
        list.push_back(jrscore::AOITools::DoubleToString(m_y, 2));
        list.push_back(jrscore::AOITools::DoubleToString(m_angle, 2));
        list.push_back(std::to_string(m_subboard_id));
        return list;
    }
    int m_id;//序号
    std::string m_device_name;//元件名称
    std::string m_old_device_name;//元件名称
    float m_x;
    float m_y;
    float m_width;
    float m_height;
    float m_angle;//角度
    std::string m_subboard_name;//子板名称
    int m_subboard_id;//子板编号
    std::string m_part_number;//料号
    int m_body_shape_type;//本体形状
    bool m_body_tested;//是否测试
    bool m_unedited;//未编辑
    std::string m_pad_number;//PAD编号
    std::vector<std::string> m_pad_group_items;//PAD焊盘组别列表
    int m_pad_group_type;//PAD组别
    int m_pad_direction;//PAD方向
    int m_pad_shape;//PAD形状
    float m_pad_width;//PAD宽
    float m_pad_height;//PAD高
    float m_pad_coordinate_x;//PAD坐标X
    float m_pad_coordinate_y;//PAD坐标Y
    float m_pad_shaping_x;//PAD间距X
    float m_pad_shaping_y;//PAD间距Y
    bool m_enable_masked;//PAD属性屏蔽
    bool m_enable_masked_skip_numbering;//PAD属性屏蔽跳过编号
    bool m_enable_exception;//PAD特例
    RESULT_STATE m_result_state;//结果
    bool m_show_enable;//是否显示 
};
using DeviceDataStructPtr = std::shared_ptr<DeviceDataStruct>;
/**
 * @class PartNumberDataStruct
 * @brief 料号数据结构
 * @date 2024.2.6
 * <AUTHOR>
 */
class PartNumberDataStruct : public ShowTableParamBase
{
public:
    PartNumberDataStruct()
    {
        m_id = 0;
        m_subboard_name = "";
        m_subboard_id = -1;
        m_part_number = "";
        m_old_part_number = "";
        m_part_number_follow = "";
        m_result_state = RESULT_STATE::RESULT_NONE;
        m_device_datas.clear();
        this->table_name = T_SHOW_PART_NUMBER;
        m_show_enable = true;
    }
    PartNumberDataStruct(
        int m_id_,//序号
        std::string m_subboard_name_,//子板名称
        int m_subboard_id_,//子板编号
        std::string m_part_number_,//料号
        std::string m_old_part_number_,//料号
        std::string m_part_number_follow_,//跟随料号
        RESULT_STATE m_result_state_,//结果
        std::vector<DeviceDataStructPtr> m_device_datas_,//相同料号的元件
        bool m_show_enable_
    ) :
        m_id(m_id_),
        m_subboard_name(m_subboard_name_),
        m_subboard_id(m_subboard_id_),
        m_part_number(m_part_number_),
        m_old_part_number(m_old_part_number_),
        m_part_number_follow(m_part_number_follow_),
        m_result_state(m_result_state_),
        m_device_datas(m_device_datas_),
        m_show_enable(m_show_enable_)
    {
        this->table_name = T_SHOW_PART_NUMBER;
    }
    PartNumberDataStruct(const PartNumberDataStruct& other) :
        m_id(other.m_id),
        m_subboard_name(other.m_subboard_name),
        m_subboard_id(other.m_subboard_id),
        m_part_number(other.m_part_number),
        m_old_part_number(other.m_old_part_number),
        m_part_number_follow(other.m_part_number_follow),
        m_result_state(other.m_result_state),
        m_device_datas(other.m_device_datas),
        m_show_enable(other.m_show_enable)
    {
        this->table_name = T_SHOW_PART_NUMBER;
    }
    std::vector<std::string>  GetStringList()
    {
        std::vector<std::string>  list;
        list.push_back(std::to_string(m_id));
        list.push_back(m_part_number);
        list.push_back(m_part_number_follow);
        list.push_back(std::to_string(int(m_device_datas.size())));
        return list;
    }
    PartNumberDataStruct operator = (PartNumberDataStruct& part_number_) /// 自定义深度拷贝 通过 = 号赋值的定义
    {
        m_id = part_number_.m_id;
        m_subboard_name = part_number_.m_subboard_name;
        m_subboard_id = part_number_.m_subboard_id;
        m_part_number = part_number_.m_part_number;
        m_part_number_follow = part_number_.m_part_number_follow;
        m_result_state = part_number_.m_result_state;
        m_device_datas = part_number_.m_device_datas;
        m_show_enable = part_number_.m_show_enable;
        return *this;
    }
    bool operator<(const PartNumberDataStruct& other) const
    {
        return m_id < other.m_id;
    }
    int m_id;//序号
    std::string m_subboard_name;//子板名称
    int m_subboard_id;//子板编号
    std::string m_part_number;//料号
    std::string m_old_part_number;//料号
    std::string m_part_number_follow;//跟随料号
    RESULT_STATE m_result_state;//结果
    std::vector<DeviceDataStructPtr> m_device_datas;//相同料号的元件
    bool m_show_enable;//是否显示 
};
using PartNumberDataStructPtr = std::shared_ptr<PartNumberDataStruct>;
/**
 * @class SubBoardDataStruct
 * @brief 子板数据结构
 * @date 2024.2.6
 * <AUTHOR>
 */
class SubBoardDataStruct : public ShowTableParamBase
{
public:
    SubBoardDataStruct()
    {
        m_id = 0;
        m_subboard_name = "";
        m_angle = 0.0;
        m_subboard_id = 0;
        m_result_state = RESULT_STATE::RESULT_NONE;
        m_part_number_datas.clear();
        m_part_number_datas_top.clear();
        m_part_number_datas_bottom.clear();
        m_part_number_datas_query.clear();
        m_query_enable = false;
        this->table_name = T_SHOW_BOARD;
        m_show_enable = true;
    }
    SubBoardDataStruct(
        int m_id_,//序号
        std::string m_subboard_name_,//子板名称
        float m_angle_,//角度
        int m_subboard_id_,//子板编号
        RESULT_STATE m_result_state_,//结果
        std::vector<PartNumberDataStructPtr> m_part_number_datas_,
        std::vector<PartNumberDataStructPtr> m_part_number_datas_top_,
        std::vector<PartNumberDataStructPtr> m_part_number_datas_bottom_,
        std::vector<PartNumberDataStructPtr> m_part_number_datas_query_,
        bool m_query_enable_,
        bool m_show_enable_
    ) :
        m_id(m_id_),
        m_subboard_name(m_subboard_name_),
        m_angle(m_angle_),
        m_subboard_id(m_subboard_id_),
        m_result_state(m_result_state_),
        m_part_number_datas(m_part_number_datas_),
        m_part_number_datas_top(m_part_number_datas_top_),
        m_part_number_datas_bottom(m_part_number_datas_bottom_),
        m_part_number_datas_query(m_part_number_datas_query_),
        m_query_enable(m_query_enable_),
        m_show_enable(m_show_enable_)
    {
        this->table_name = T_SHOW_BOARD;
    }
    SubBoardDataStruct(const SubBoardDataStruct& other) :
        m_id(other.m_id),
        m_subboard_name(other.m_subboard_name),
        m_angle(other.m_angle),
        m_subboard_id(other.m_subboard_id),
        m_result_state(other.m_result_state),
        m_part_number_datas(other.m_part_number_datas),
        m_part_number_datas_top(other.m_part_number_datas_top),
        m_part_number_datas_bottom(other.m_part_number_datas_bottom),
        m_part_number_datas_query(other.m_part_number_datas_query),
        m_query_enable(other.m_query_enable),
        m_show_enable(other.m_show_enable)
    {
        this->table_name = T_SHOW_BOARD;
    }
    std::vector<std::string>  GetStringList()
    {
        std::vector<std::string>  list;
        list.push_back(std::to_string(m_id));
        //list.push_back(m_subboard_name);
        list.push_back(jrscore::AOITools::GetPrefixString(m_subboard_name));
        list.push_back(jrscore::AOITools::DoubleToString(m_angle, 2));
        list.push_back(std::to_string(m_subboard_id));
        return list;
    }
    bool operator<(const SubBoardDataStruct& other) const
    {
        return m_id < other.m_id;
    }
    int m_id;//序号
    std::string m_subboard_name;//子板名称
    float m_angle;//角度
    int m_subboard_id;//子板编号
    RESULT_STATE m_result_state;//结果
    std::vector<PartNumberDataStructPtr> m_part_number_datas;
    std::vector<PartNumberDataStructPtr> m_part_number_datas_top;
    std::vector<PartNumberDataStructPtr> m_part_number_datas_bottom;
    std::vector<PartNumberDataStructPtr> m_part_number_datas_query;
    bool m_query_enable;
    bool m_show_enable;//是否显示 
};
using SubBoardDataStructPtr = std::shared_ptr<SubBoardDataStruct>;

namespace jrsdata
{
    //EditView Struct
    struct CommonEditData
    {
        float cx; /**< 中心坐标 以pixel为单位*/
        float cy;
        float width;
        float height;
        float angle;
        bool is_detect;/**< 是否检测*/
        CommonEditData()
            :cx(0.f), cy(0.f), width(0.f), height(0.f), angle(0.f), is_detect(true)
        {
        }

        void Clear()
        {
            cx = 0.f;
            cy = 0.f;
            width = 0.f;
            height = 0.f;
            angle = 0.f;
            is_detect = true;
        }
    };

    struct PadEditViewData
    {
        CommonEditData common; /**< 复用公共数据 */
        float col_gap;
        float row_gap;
        jrsdata::ComponentUnit::Direction direction;
        jrsdata::ComponentUnit::PadType pad_type;/**< pad type*/
        PadEditViewData()
            : col_gap(0.f), row_gap(0.f), direction(jrsdata::ComponentUnit::Direction::UNKNOWN)
            , common(), pad_type(jrsdata::ComponentUnit::PadType::UNKNOWN)
        {
        }

        void Clear()
        {
            common.Clear();
            col_gap = 0.f;
            row_gap = 0.f;
            direction = jrsdata::ComponentUnit::Direction::UNKNOWN;
            pad_type = jrsdata::ComponentUnit::PadType::UNKNOWN;
        }
    };

    struct EditViewData : public ShowTableParamBase
    {
        enum class UpdateWidgets
        {
            Component,  /**< 元件编辑界面*/
            PAD,        /**< 焊盘编辑界面*/
            DETECT_WINDOW, /**< 检测框编辑界面*/
        };

        CommonEditData component_edit_data; /**<元件数据*/
        PadEditViewData pad_edit_data;      /**< 焊盘数据*/
        std::pair<jrsdata::ComponentUnit::Type, CommonEditData> detect_window_edit_data;  /**< 检测框数据，<谁的检测框，检测框数据>*/
        UpdateWidgets update_widget;/**< 更新那个界面*/

        void Clear()
        {
            component_edit_data.Clear();
            pad_edit_data.Clear();
            detect_window_edit_data.second.Clear();
            update_widget = UpdateWidgets::Component;
        }
    };
    using EditViewDataPtr = std::shared_ptr<EditViewData>;
}
#endif