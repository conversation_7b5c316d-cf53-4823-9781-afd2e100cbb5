# JRSAOI 2.0 UML架构图文档

本目录包含JRSAOI 2.0项目的完整UML架构图，用于展示系统的设计架构和模块关系。

## 图表说明

### 1. 整体架构UML类图 (class-diagram.md)
- **用途**: 展示项目的核心类结构和继承关系
- **内容**: 
  - 视图层、控制器层、模型层的MVC架构
  - 逻辑层、服务层、数据层的分层设计
  - 核心数据结构和参数定义
  - 设备管理和渲染相关类
- **适用场景**: 代码架构理解、新功能开发参考

### 2. 检测流程时序图 (sequence-diagram.md)
- **用途**: 展示AOI检测的完整业务流程
- **内容**:
  - 项目加载阶段的交互流程
  - 设备初始化的并行处理
  - 检测执行的详细步骤
  - 异常处理机制
- **适用场景**: 业务流程理解、问题排查、流程优化

### 3. 系统分层架构图 (architecture-diagram.md)
- **用途**: 展示完整的7层系统架构
- **内容**:
  - 用户界面层到第三方库层的完整分层
  - 模块间的依赖关系
  - 数据流向和控制流向
  - 第三方库的集成方式
- **适用场景**: 系统架构设计、模块划分、技术选型

### 4. 核心数据模型ER图 (er-diagram.md)
- **用途**: 展示数据库设计和实体关系
- **内容**:
  - 项目、板子、元件的层次结构
  - 检测配置和算法的关联关系
  - 检测结果的存储结构
  - 系统配置和用户管理
- **适用场景**: 数据库设计、数据迁移、报表开发

## 使用方法

### 查看图表
1. 使用支持Mermaid的Markdown编辑器（如Typora、VS Code with Mermaid插件）
2. 在线Mermaid编辑器：https://mermaid.live/
3. GitHub/GitLab等平台可直接渲染Mermaid图表

### 导出图片

#### 快速导出（推荐）
运行提供的导出脚本：
- **Windows**: 双击 `export-images.bat`
- **Linux/Mac**: 运行 `bash export-images.sh`

#### 手动导出方式
1. **在线导出**:
   - 访问 https://mermaid.live/
   - 复制对应的.md文件中的mermaid代码
   - 点击导出为PNG/SVG
   - 详细步骤见：[在线导出图片说明.md](在线导出图片说明.md)

2. **本地导出**:
   - 安装Mermaid CLI: `npm install -g @mermaid-js/mermaid-cli`
   - 运行命令: `mmdc -i class-diagram.md -o class-diagram.png`

3. **VS Code导出**:
   - 安装Mermaid Preview插件
   - 右键选择"Export Mermaid Diagram"

#### 导出的图片文件
所有导出的图片将保存在 `images/` 目录下：
- `class-diagram.png` - 整体架构UML类图
- `sequence-diagram.png` - 检测流程时序图
- `architecture-diagram.png` - 系统分层架构图
- `er-diagram.png` - 核心数据模型ER图

### 编辑图表
1. 修改对应的.md文件中的Mermaid代码
2. 遵循Mermaid语法规范
3. 测试渲染效果后提交更改

## 维护说明

### 更新时机
- 系统架构发生重大变更时
- 新增核心模块或重要功能时
- 数据库结构发生变化时
- 业务流程发生调整时

### 更新原则
- 保持图表的准确性和时效性
- 遵循统一的命名规范
- 保持图表的可读性和美观性
- 及时更新相关文档说明

## 技术说明

### Mermaid语法
- 类图: `classDiagram`
- 时序图: `sequenceDiagram`
- 流程图: `graph TB/LR`
- ER图: `erDiagram`

### 样式定义
- 使用classDef定义统一的颜色方案
- 不同层次使用不同颜色区分
- 保持视觉层次的清晰性

## 相关文档
- [项目文件结构说明](../../项目文件结构说明.md)
- [OperateMVC架构分析](../../OperateMVC架构分析.md)
- [框架设计文档](../框架设计文档/)
- [数据库设计文档](../数据库/)
