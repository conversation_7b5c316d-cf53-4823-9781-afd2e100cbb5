#pragma warning(push, 1)
#pragma warning(disable : 4127 4514 4365 4244 4800 4820 26495)
#include <QtWidgets/qboxlayout.h>
#include <QComboBox>
#include <QCheckBox>
#include "heightwheel.h"
#include <QtWidgets/qpushbutton.h>
#include <QWidget>
#include <QLineEdit>
#include <QPainter>
#include <QMouseEvent>
#include <QHBoxLayout>
#include <vector>
#include <algorithm>

#pragma warning(pop)

HeightWheel::~HeightWheel()
{
}
void HeightWheel::SetHeightHistParams(int bin, float min_hei, 
	float max_hei)
{
	bin_ = bin;
	bin_value->setText(QString::number(bin_));
	m_min_height = min_hei;
	m_max_height = max_hei;
	min_height_value_label->setText(QString::number(m_min_height));
	max_height_value_label->setText(QString::number(m_max_height));
	emit UpdataHeiHistParmas(bin_,
		min_height_value_label->text().toFloat(),
		max_height_value_label->text().toFloat(), m_min_bin, m_max_bin);
}
void HeightWheel::UpataHeightHistParamsSlot()
{
	bin_ = bin_value->text().toInt();
	height_histogramwidget->SetHistBin(bin_);
	auto cur_min_height = use_opposite_min_height_ck->isChecked() ? m_opp_min_height :
		min_height_value_label->text().toFloat();
	emit UpdataHeiHistParmas(bin_,
		cur_min_height,
		max_height_value_label->text().toFloat(), m_min_bin, m_max_bin);
}
void HeightWheel::SetHeightThreSlot(int min_thre, int max_thre)
{
	m_min_bin = min_thre;
	m_max_bin = max_thre;
	auto cur_min_height = use_opposite_min_height_ck->isChecked() ? m_opp_min_height :
		min_height_value_label->text().toFloat();
	emit UpdataHeiHistParmas(bin_,
		cur_min_height,
		max_height_value_label->text().toFloat(), m_min_bin, m_max_bin,true);
}
void HeightWheel::UseOppositeMinHeightSlot()
{
	auto cur_min_height = use_opposite_min_height_ck->isChecked()? m_opp_min_height :
		min_height_value_label->text().toFloat();
	emit UpdataHeiHistParmas(bin_,
		cur_min_height,
		max_height_value_label->text().toFloat(), m_min_bin, m_max_bin);
}
int HeightWheel::FindLeftValley(const std::vector<float>& histogram)
{
	if (histogram.size() < 3) {
		return 0;
	}

	// 1. 找到所有波峰
	struct Peak
	{
		int index;
		float value;
	};
	std::vector<Peak> peaks;

	// 查找局部极大值（波峰）
	for (int i = 1; i < histogram.size() - 1; ++i)
	{
		if (histogram[i] > histogram[i - 1] && histogram[i] > histogram[i + 1])
		{
			peaks.push_back({ i, histogram[i] });
		}
	}

	// 如果波峰少于2个，返回0
	if (peaks.size() < 2)
	{
		return 0;
	}

	// 2. 按值排序找到最大的两个波峰
	std::sort(peaks.begin(), peaks.end(),
		[](const Peak& a, const Peak& b)
		{
			return a.value > b.value;
		});

	// 3. 获取索引较小的波峰
	int peak_index = std::min(peaks[0].index, peaks[1].index);

	// 4. 在该波峰左侧寻找波谷（局部最小值）
	int valley_index = peak_index;
	float min_value = histogram[peak_index];

	const float ZERO_THRESHOLD = 0.00001f;  // 接近零的阈值
	const int RISE_CHECK = 3;  // 连续上升的检查次数
	int rise_count = 0;

	for (int i = peak_index - 1; i >= RISE_CHECK - 1; --i)
	{
		// 如果当前值接近零，直接认为是波谷
		if (histogram[i] <= ZERO_THRESHOLD)
		{
			return i;
		}

		if (histogram[i] <= min_value)
		{
			min_value = histogram[i];
			valley_index = i;
			rise_count = 0;
		}
		else
		{
			// 检查是否连续上升
			bool is_rising = true;
			for (int j = 0; j < RISE_CHECK - 1; ++j)
			{
				if (histogram[i - j] >= histogram[i - j - 1])
				{
					is_rising = false;
					break;
				}
			}

			if (is_rising)
			{
				rise_count++;
				if (rise_count >= RISE_CHECK)
				{
					break;
				}
			}
			else
			{
				rise_count = 0;
			}
		}
	}
	return valley_index;
}
QHBoxLayout* HeightWheel::CreateHeightParamLayout(QWidget* parent)
{
	QHBoxLayout* layout = new QHBoxLayout(parent);

	auto create_line_edit = [&](const QString& text, int maxWidth, bool readOnly = false) {
		QLineEdit* line_edit = new QLineEdit(text, parent);
		line_edit->setMaximumWidth(maxWidth);
		line_edit->setReadOnly(readOnly);
		return line_edit;
	};
	use_opposite_min_height_ck = new QCheckBox("缩小范围");
	bin_name = new QLabel("高度分割层数:");
	bin_value = create_line_edit(QString::number(bin_), 80);
	min_height_name =  new QLabel("高度下限:");
	min_height_value_label = create_line_edit(QString::number(0), 80);
	max_height_name  =  new QLabel("高度上限:");
	max_height_value_label = create_line_edit(QString::number(0), 80);
	
	QHBoxLayout* layout_1 = new QHBoxLayout(parent);
	QHBoxLayout* layout_2 = new QHBoxLayout(parent);
	QHBoxLayout* layout_3 = new QHBoxLayout(parent);


	layout_1->addWidget(bin_name);
	layout_1->addWidget(bin_value);
	layout_1->addStretch(1);
	layout_2->addWidget(min_height_name);
	layout_2->addWidget(min_height_value_label);
	layout_2->addStretch(1);
	layout_3->addWidget(max_height_name);
	layout_3->addWidget(max_height_value_label);
	layout_3->addStretch(1);

	layout->addLayout(layout_1);
	layout->addLayout(layout_2);
	layout->addLayout(layout_3);
	layout->addWidget(use_opposite_min_height_ck);
	return layout;
}
void HeightWheel::SetupLayout(QWidget* parent)
{
	QVBoxLayout* hist_layout = new QVBoxLayout(parent);

	QHBoxLayout* height_params = CreateHeightParamLayout(parent);
	hist_layout->addLayout(height_params);

	height_histogramwidget = new CustomPlotWidget(bin_, Qt::gray);
	height_histogramwidget->xAxis->setRange(-100, 100);

	hist_layout->addWidget(height_histogramwidget);
	
	connect(bin_value, &QLineEdit::returnPressed, this, &HeightWheel::UpataHeightHistParamsSlot);
	connect(min_height_value_label, &QLineEdit::returnPressed, this, &HeightWheel::UpataHeightHistParamsSlot);
	connect(max_height_value_label, &QLineEdit::returnPressed, this, &HeightWheel::UpataHeightHistParamsSlot);	
	connect(height_histogramwidget, &CustomPlotWidget
		::UpdateThre, this, &HeightWheel::SetHeightThreSlot);
	connect(use_opposite_min_height_ck, &QCheckBox::stateChanged, this, &HeightWheel::UseOppositeMinHeightSlot);
}
HeightWheel::HeightWheel(QWidget* parent) 
    : QWidget(parent)
{
	SetupLayout(this);
}
void HeightWheel::SetHeightHistValue(std::vector<float>& gray_hist)
{
	auto opp_min_height_index = FindLeftValley(gray_hist);
		float bin_width = (m_max_height - m_min_height) / 2000;

	m_opp_min_height = m_min_height + opp_min_height_index * bin_width;
	height_histogramwidget->SetHistValue(gray_hist);
}