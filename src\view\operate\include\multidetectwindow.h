﻿#ifndef MULTIDETECTWINDOW_H
#define MULTIDETECTWINDOW_H

#include <QDialog>
#include <QPushButton>
#include <QCheckBox>
#include <QListWidget>
#include <QListWidgetItem>

#include "viewdefine.h"
#include "nlohmann/json.hpp"
using JSON = nlohmann::json;

namespace Ui {
class MultiDetectWindow;
}

class MultiDetectWindow : public QDialog
{
    Q_OBJECT

public:
    explicit MultiDetectWindow(QWidget *parent = nullptr);
    ~MultiDetectWindow();

    // 初始化算法列表
    void IniAlgorithmView(const std::vector<std::pair<std::string, std::string>>& param);

    // 初始化界面
    void InitView(const std::string& param_);

private:
    void InitConnection();

    // 指定参数初始化界面
    void InitViewWithParam(const std::string& param_);

    // 默认参数初始化界面
    void InitViewWithDefaultParam();

    // 获取算法显示名(根据算法名)
    std::string GetAlgoDisplayName(const std::string name);

    // 获取算法名(根据算法显示名)
    std::string GetAlgoName(std::string displayname);

    // 给指定的QListWidget添加checkbox
    void AddCheckBox(QListWidget* list, QString name, Qt::CheckState state = Qt::CheckState::Unchecked);

    // 删除指定的QListWidget添加checkbox
    void DeleteCheckBox(QListWidget* list, QString name);

    // 获取指定的QListWidget所有项中选中的名字
    QStringList GetCheckedNameList(QListWidget* list);

    // 获取指定的QListWidget的算法选择情况(构建一个JSON数组对象)
    JSON GetAlgoSelectPackge(QListWidget* list);

    // 设置指定的QListWidget的check状态
    void SetCheckState(QListWidget* list, Qt::CheckState state);

    // 打包当前算法选中情况(构建一个JSON字符串)
    std::string GetCurSelectParam();

signals:
    void SigUpdate(const jrsdata::ViewParamBasePtr& param);

    // 算法列表状态变化信号
    void SigAlgoListChange(const std::string& list);

private:
    Ui::MultiDetectWindow *ui;
    std::vector<std::pair<std::string, std::string>> param;
};

#endif // MULTIDETECTWINDOW_H
