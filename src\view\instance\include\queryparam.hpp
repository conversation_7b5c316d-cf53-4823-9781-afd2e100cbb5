#ifndef QUERYPARAMETERS_H
#define QUERYPARAMETERS_H

//custom
#include "coreapplication.h"
#include "viewparam.hpp"
//STD
#include <string>
#include <mutex>
namespace jrsdata {
    /** 全局参数查询定义 商议后再添加*/
    constexpr char SYSTEM_PARAM[] = "system_param"; /**< 系统参数 */
    constexpr char MACHINE_PARAM[] = "machine_param"; /**< 机台参数 */
    constexpr char STATE_PARAM[] = " state_param";  /**< 状态参数 */

    class Parameters {
    public:
        // 单例获取实例
        static std::shared_ptr<Parameters> GetInstance() {
            if (!_instance) {
                std::lock_guard<std::mutex> lock(_instanc_mtx);
                if (!_instance) {
                    _instance = std::make_shared<Parameters>();
                }
            }
            return _instance;
        }

        Parameters() = default;
        ~Parameters() = default;

        std::variant<int, float, double, std::string> GetSettingParamValueByName(const std::string& name_)
        {
            auto param = GetParams< jrsdata::SettingParam>(name_);
            if (param.param_type == "int")
            {
                return std::stoi(param.param_value);  // 转换为 int
            }
            else if (param.param_type == "float")
            {
                return std::stof(param.param_value);  // 转换为 float
            }
            else if (param.param_type == "double")
            {
                return std::stod(param.param_value);  // 转换为 double
            }
            else if (param.param_type == "string")
            {
                return param.param_value;  // 返回原始 string
            }
            else
            {
                throw std::invalid_argument("Unsupported type: " + param.param_type);
            }
        }

        /**
         * @fun GetParams
         * @brief
         * @param parm_name_
         * @param item_name_
         * @return
         * <AUTHOR>
         * @date 2024.10.30
         */
        template <typename T>
        T GetParams(const std::string& item_name_)
        {
            //std::lock_guard<std::mutex> lock(mtx);
            if constexpr (std::is_same_v<T, jrsdata::SettingParam>)
            {
                auto setting_param = _params.find(item_name_);
                if (setting_param == _params.end()) {
                    Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 获取系统参数失败！");
                    return jrsdata::SettingParam{};
                }
                return setting_param->second;
            }
            else if constexpr (std::is_same_v<T, jrsdata::MachineCheckParamInfo>)
            {
                auto state_param = _state_params.find(item_name_);
                if (state_param == _state_params.end())
                {
                    Log_WARN(jrscore::CoreError::E_AOI_CORE_INIT_FAIL, "Parameters 获取系统参数失败！");
                    return  jrsdata::MachineCheckParamInfo{};
                }
                return state_param->second;
            }

            return T{};
        }
        template<typename T>
        int SetParams(const std::string& param_name_, const T& item_values_) 
        {
            (void)param_name_;
            std::lock_guard<std::mutex> lock(mtx);
            if constexpr (std::is_same_v<T, jrsdata::SettingParamMap>) {
                for (const auto& value : item_values_) {
                    _params[value.first] = value.second;
                }
            }
            else if constexpr (std::is_same_v<T, jrsdata::SystemStateMap>) {
                for (const auto& value : item_values_) {
                    _state_params[value.first] = value.second;
                }
            }
            else
            {
                static_assert(!std::is_same_v<T, T>, "Unsupported type for GetParams.");
            }
            return jrscore::AOI_OK;
        }

    private:
        // 禁用拷贝构造和拷贝赋值
        Parameters(const Parameters&) = delete;
        Parameters& operator=(const Parameters&) = delete;

        std::mutex mtx;  // 防止多线程
        jrsdata::SettingParamMap _params; // 系统参数和机台参数均存在这里
        jrsdata::SystemStateMap _state_params;/**< 状态参数 */
        inline static std::mutex _instanc_mtx;
        inline static std::shared_ptr<Parameters> _instance;
    };
    using ParametersPtr = std::shared_ptr<Parameters>;
}

#endif // QUERYPARAMETERS_H