/*****************************************************************//**
 * @file   parametersettings.h
 * @brief  参数设置页面
 * @details
 * <AUTHOR>
 * @date 2024.8.18
 * <table>
 * <tr><th>Date              <th>Version           <th>Author       <th>Email                    <th> Desctiption
 * <tr><td>2024.8.18         <td>V1.0              <td>zhaokunlong      <td> <td>
 * </table>
 * @copyright 版权 CopyRight (C), 2023-2024.
 *********************************************************************/
#ifndef PARAMETERSETTINGS_H
#define PARAMETERSETTINGS_H
#pragma warning(push, 3)
 // prebuild
#include "pch.h"
//QT
#include <QWidget>
#include <QButtonGroup>
#include <QComboBox>
// CUSTOM
#include "ui_parametersettings.h"
#include "viewparam.hpp"
#pragma warning(pop)

namespace Ui {
    class ParameterSettings;
}

class ParameterSettings : public QWidget
{
    Q_OBJECT
public:
    explicit ParameterSettings(QWidget* parent = nullptr);
    ~ParameterSettings();
    // 初始化输送模组设置界面
    /**
     * @fun InitTrackView
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    //void InitTrackView();
    void UpdateViewMachineParam(const jrsdata::MachineParam& machine_param_);
private slots:
    void PathPlanningToggled(bool check);               /**< 路径规划RadioButton点击响应 */
    void PathPatternToggled(int index);                 /**< 轨迹模式变化响应 */
    void BarcodeToggled(bool check);                    /**< 条码RadioButton点击响应 */
    void BadBoardMarkToggled(bool check);               /**< 坏板标记RadioButton点击响应 */
    void SubBoardPositioningPointToggled(bool check);   /**< 子板定位点RadioButton点击响应 */
    void InspectionAreaToggled(bool check);             /**< 检测区域RadioButton点击响应 */
    void BadBoardSettingToggled(bool check);            /**< 坏板设置RadioButton点击响应 */
    void EnableRejectionTestToggled(bool check);        /**< 抛料检测CheckBox点击响应 */
    void MinimumHeightFinished();                       /**< 最小高度QDoubleSpinBox编辑结束响应 */
    void MinimumAreaFinished();                         /**< 最小面积QDoubleSpinBox编辑结束响应 */
    void MaskExpansionFinished();                       /**< 遮罩外扩QDoubleSpinBox编辑结束响应 */
    void ProductSwitchCurrentIndexChange(int index);    /**< 产品切换QComboBox行切换响应 */
    void PercentageDynamicShieldingFinished();          /**< 百分比动态屏蔽QDoubleSpinBox编辑结束响应 */
protected:
    void showEvent(QShowEvent* event) override;
private:
    /**
     * @fun InitConnect
     * @brief 初始化槽函数链接
     * @date 2024.9.24
     * <AUTHOR>
     */
    void InitConnect();
    // 轨道2参数显示
    /**
     * @fun ShowTrack2
     * @brief
     * @param flag
     * @date 2024.9.24
     * <AUTHOR>
     */
    void ShowTrack2(bool flag);
    /**
     * @fun MachineParamChangeSave 
     * @brief 机器参数保存
     * @param param_name
     * @param value_
     * @date 2025.5.8
     * <AUTHOR>
     */
    void MachineParamChangeSave(const std::string param_name, const jrsdata::JrsVariant& value_);
public slots:
    // 更新板宽
    /**
     * @fun UpdateBoardWidth
     * @brief
     * @param param_
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateBoardWidth(const jrsdata::OperateViewParamPtr param_);
    // 更新界面参数
    /**
     * @fun UpdateView
     * @brief
     * @param ptr
     * @date 2024.9.24
     * <AUTHOR>
     */
    void UpdateView(const jrsdata::OperateViewParamPtr ptr);
signals:
    void SigUpdateMachineParam(const jrsdata::MachineParam& machine_param_); /**< 发送消息保存参数 */
    // 获取板宽
    /**
     * @fun SigGetBoardWith
     * @brief
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigGetBoardWith();
    // 设置板宽(移动)
    /**
     * @fun SigSetBoardWidth
     * @brief
     * @param param_
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigSetBoardWidth(const jrsdata::OperateViewParamPtr param_);
    // 保存界面参数到配置文件
    /**
     * @fun SigSaveSetting
     * @brief
     * @param param_
     * @date 2024.9.24
     * <AUTHOR>
     */
    void SigSaveSetting(const jrsdata::OperateViewParamPtr param_);

    /**
     * @fun SigSendMotionParamToWorkFlow 
     * @brief 初始化时发送运控的参数给自动运行模块
     * @param param_
     * <AUTHOR>
     * @date 2025.6.9
     */
    void SigSendMotionParamToWorkFlow(const jrsdata::OperateViewParamPtr param_);

    /**
     * @fun SigSaveRepairData
     * @brief 保存维修站数据信号
     * <AUTHOR>
     * @date 2025.1.12
     */
    void SigSaveRepairData(const jrsdata::RepairData& repair_data);

private:
    Ui::ParameterSettings* ui;
    jrsdata::OperateViewParamPtr operateparam;
    std::vector<QButtonGroup> _groups;
    bool config_has_update;
    int stop_board_index;                           // ! 0:轨道1左下按钮单击,1:轨道1右下按钮单击,2:轨道2左上按钮单击,3:轨道2右上按钮单击
};
#endif // PARAMETERSETTINGS_H
