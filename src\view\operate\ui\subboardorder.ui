<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SubboardOrder</class>
 <widget class="QWidget" name="SubboardOrder">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>415</width>
    <height>368</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0">
     <item>
      <widget class="QFrame" name="frame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="0">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>子板规则排序</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="2">
           <widget class="QToolButton" name="toolButton_3">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QToolButton" name="toolButton_4">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QToolButton" name="toolButton_6">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QToolButton" name="toolButton_2">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QToolButton" name="toolButton">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QToolButton" name="toolButton_5">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QToolButton" name="toolButton_7">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QToolButton" name="toolButton_8">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QToolButton" name="toolButton_9">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QToolButton" name="toolButton_10">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QToolButton" name="toolButton_11">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QToolButton" name="toolButton_12">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QToolButton" name="toolButton_13">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QToolButton" name="toolButton_14">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QToolButton" name="toolButton_15">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QToolButton" name="toolButton_16">
            <property name="text">
             <string>...</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_3">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>70</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="1">
         <widget class="QPushButton" name="btn_confirm">
          <property name="text">
           <string>确认</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>70</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="3">
         <widget class="QPushButton" name="btn_cancel">
          <property name="text">
           <string>取消</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>70</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
