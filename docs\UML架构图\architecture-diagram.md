# JRSAOI 系统分层架构图

```mermaid
graph TB
    %% 用户界面层
    subgraph "用户界面层 (Presentation Layer)"
        MainWindow[主窗口<br/>MainWindow]
        OperateView[操作界面<br/>OperateView]
        Render2DView[2D渲染界面<br/>Render2DView]
        ControlPanelView[控制面板<br/>ControlPanelView]
        LogShowView[日志显示<br/>LogShowView]
        SettingView[设置界面<br/>SettingView]
    end

    %% 控制层
    subgraph "控制层 (Controller Layer)"
        ViewManager[视图管理器<br/>ViewManager]
        OperateController[操作控制器<br/>OperateController]
        Render2DController[渲染控制器<br/>Render2DController]
        ControlPanelController[面板控制器<br/>ControlPanelController]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层 (Business Logic Layer)"
        LogicManager[逻辑管理器<br/>LogicManager]
        ProjectManager[项目管理器<br/>ProjectManager]
        WorkFlowManager[工作流管理器<br/>WorkFlowManager]
        
        subgraph "模型层 (Model Layer)"
            OperateModel[操作模型<br/>OperateModel]
            Render2DModel[渲染模型<br/>Render2DModel]
            ControlPanelModel[面板模型<br/>ControlPanelModel]
        end
    end

    %% 服务层
    subgraph "服务层 (Service Layer)"
        subgraph "算法服务"
            AlgorithmEngineManager[算法引擎管理器<br/>AlgorithmEngineManager]
            AlgoExecute[算法执行器<br/>AlgoExecute]
            LoadAlgoManager[算法加载器<br/>LoadAlgoManager]
        end
        
        subgraph "设备服务"
            DeviceManager[设备管理器<br/>DeviceManager]
            StructLight[结构光相机<br/>StructLight]
            Motion[运动控制<br/>Motion]
            BarcodeScanner[条码扫描<br/>BarcodeScanner]
        end
        
        subgraph "渲染服务"
            Renderer2D[2D渲染器<br/>Renderer2D]
            GraphicsManager[图形管理器<br/>GraphicsManager]
            CoordinateTransform[坐标转换<br/>CoordinateTransform]
        end
    end

    %% 数据访问层
    subgraph "数据访问层 (Data Access Layer)"
        DataManager[数据管理器<br/>DataManager]
        TableManager[表管理器<br/>TableManager]
        FileManager[文件管理器<br/>FileManager]
        
        subgraph "数据表"
            BoardTable[板子表<br/>BoardTable]
            ProjectTable[项目表<br/>ProjectTable]
            DetectWindowTable[检测框表<br/>DetectWindowTable]
            UserTable[用户表<br/>UserTable]
        end
    end

    %% 基础设施层
    subgraph "基础设施层 (Infrastructure Layer)"
        subgraph "核心服务"
            CoreApplication[核心应用<br/>CoreApplication]
            ErrorHandler[错误处理器<br/>ErrorHandler]
            LogManager[日志管理器<br/>LogManager]
        end
        
        subgraph "通信服务"
            PubSubManager[发布订阅管理器<br/>PubSubManager]
            ModuleHandle[模块句柄<br/>ModuleHandle]
            EventCenter[事件中心<br/>EventCenter]
        end
        
        subgraph "工具服务"
            JTools[工具库<br/>JTools]
            TimeUtility[时间工具<br/>TimeUtility]
            FileOperation[文件操作<br/>FileOperation]
        end
    end

    %% 数据存储层
    subgraph "数据存储层 (Data Storage Layer)"
        MySQL[(MySQL数据库)]
        FileSystem[(文件系统)]
        ConfigFiles[(配置文件)]
        LogFiles[(日志文件)]
    end

    %% 第三方库层
    subgraph "第三方库层 (Third Party Layer)"
        Qt[Qt Framework]
        OpenCV[OpenCV]
        Eigen3[Eigen3]
        JSON[nlohmann::json]
        SPDLog[spdlog]
        MySQL_Lib[MySQL Connector]
        ORMPP[ORMPP]
        Cereal[Cereal]
    end

    %% 连接关系
    %% 用户界面层到控制层
    MainWindow --> ViewManager
    OperateView --> OperateController
    Render2DView --> Render2DController
    ControlPanelView --> ControlPanelController

    %% 控制层到业务逻辑层
    ViewManager --> LogicManager
    OperateController --> OperateModel
    OperateController --> LogicManager
    Render2DController --> Render2DModel
    ControlPanelController --> ControlPanelModel

    %% 业务逻辑层到服务层
    LogicManager --> AlgorithmEngineManager
    LogicManager --> DeviceManager
    LogicManager --> ProjectManager
    LogicManager --> WorkFlowManager
    
    OperateModel --> AlgorithmEngineManager
    Render2DModel --> Renderer2D
    
    %% 服务层内部连接
    AlgorithmEngineManager --> AlgoExecute
    AlgorithmEngineManager --> LoadAlgoManager
    DeviceManager --> StructLight
    DeviceManager --> Motion
    DeviceManager --> BarcodeScanner
    Renderer2D --> GraphicsManager
    Renderer2D --> CoordinateTransform

    %% 服务层到数据访问层
    LogicManager --> DataManager
    ProjectManager --> DataManager
    DataManager --> TableManager
    DataManager --> FileManager
    TableManager --> BoardTable
    TableManager --> ProjectTable
    TableManager --> DetectWindowTable
    TableManager --> UserTable

    %% 数据访问层到存储层
    TableManager --> MySQL
    FileManager --> FileSystem
    DataManager --> ConfigFiles

    %% 基础设施层连接
    LogicManager --> CoreApplication
    DataManager --> CoreApplication
    AlgorithmEngineManager --> CoreApplication
    DeviceManager --> CoreApplication
    
    CoreApplication --> ErrorHandler
    CoreApplication --> LogManager
    LogManager --> LogFiles
    
    LogicManager --> PubSubManager
    PubSubManager --> ModuleHandle
    
    %% 第三方库依赖
    MainWindow -.-> Qt
    Renderer2D -.-> OpenCV
    CoordinateTransform -.-> Eigen3
    DataManager -.-> JSON
    LogManager -.-> SPDLog
    TableManager -.-> MySQL_Lib
    TableManager -.-> ORMPP
    DataManager -.-> Cereal

    %% 样式定义
    classDef uiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef controlLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef serviceLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef infraLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef storageLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef thirdPartyLayer fill:#fafafa,stroke:#424242,stroke-width:2px

    class MainWindow,OperateView,Render2DView,ControlPanelView,LogShowView,SettingView uiLayer
    class ViewManager,OperateController,Render2DController,ControlPanelController controlLayer
    class LogicManager,ProjectManager,WorkFlowManager,OperateModel,Render2DModel,ControlPanelModel businessLayer
    class AlgorithmEngineManager,AlgoExecute,LoadAlgoManager,DeviceManager,StructLight,Motion,BarcodeScanner,Renderer2D,GraphicsManager,CoordinateTransform serviceLayer
    class DataManager,TableManager,FileManager,BoardTable,ProjectTable,DetectWindowTable,UserTable dataLayer
    class CoreApplication,ErrorHandler,LogManager,PubSubManager,ModuleHandle,EventCenter,JTools,TimeUtility,FileOperation infraLayer
    class MySQL,FileSystem,ConfigFiles,LogFiles storageLayer
    class Qt,OpenCV,Eigen3,JSON,SPDLog,MySQL_Lib,ORMPP,Cereal thirdPartyLayer
```

## 架构层次说明

### 1. 用户界面层 (Presentation Layer)
- **职责**: 提供用户交互界面，处理用户输入和显示输出
- **主要组件**: 各种View组件，基于Qt框架实现
- **特点**: 响应用户操作，通过事件机制与控制层通信

### 2. 控制层 (Controller Layer)
- **职责**: 协调视图和模型，处理用户交互逻辑
- **主要组件**: 各种Controller类，实现MVC模式的控制器
- **特点**: 无状态，纯逻辑处理，连接视图和业务逻辑

### 3. 业务逻辑层 (Business Logic Layer)
- **职责**: 实现核心业务逻辑和数据模型管理
- **主要组件**: LogicManager、各种Manager和Model类
- **特点**: 包含业务规则，管理应用状态

### 4. 服务层 (Service Layer)
- **职责**: 提供可复用的服务组件
- **主要组件**: 算法服务、设备服务、渲染服务
- **特点**: 高内聚低耦合，支持插件化扩展

### 5. 数据访问层 (Data Access Layer)
- **职责**: 封装数据访问逻辑，提供统一的数据接口
- **主要组件**: DataManager、TableManager、各种Table类
- **特点**: 抽象数据存储细节，支持多种数据源

### 6. 基础设施层 (Infrastructure Layer)
- **职责**: 提供系统基础服务和工具
- **主要组件**: 核心服务、通信服务、工具服务
- **特点**: 跨层使用，提供通用功能

### 7. 数据存储层 (Data Storage Layer)
- **职责**: 数据持久化存储
- **主要组件**: MySQL数据库、文件系统、配置文件
- **特点**: 数据的最终存储位置

### 8. 第三方库层 (Third Party Layer)
- **职责**: 提供外部依赖和基础功能
- **主要组件**: Qt、OpenCV、Eigen3等第三方库
- **特点**: 稳定的外部依赖，通过接口层隔离

## 数据流向
- **向下流向**: 用户操作 → 控制层 → 业务逻辑层 → 服务层 → 数据访问层 → 存储层
- **向上流向**: 存储层 → 数据访问层 → 服务层 → 业务逻辑层 → 控制层 → 界面层

## 架构优势
1. **分层清晰**: 职责明确，便于维护和扩展
2. **松耦合**: 层间通过接口通信，降低依赖
3. **可测试**: 每层可独立测试
4. **可扩展**: 支持水平和垂直扩展
