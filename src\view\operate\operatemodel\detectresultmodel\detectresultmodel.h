﻿/*****************************************************************//**
 * @file   DetectResultModel.h
 * @brief 结果TableView的Model
 *
 * <AUTHOR>
 * @date   2024.2.6
 *********************************************************************/
#ifndef DETECTRESULTMODEL_H
#define DETECTRESULTMODEL_H
 //QT
#include <QObject>
#include <QStandardItemModel>
#include <QStringList>
#include <QVariant>
#include <QModelIndex>
//CUSTOM
#include "datadefine.hpp"

class DetectResultModel : public QStandardItemModel
{
    Q_OBJECT
public:
    /**
     * @fun parent
     * @brief 重写QStandardItemModel的parent
     * @param child
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    QModelIndex parent(const QModelIndex& child) const override;
    /**
     * @fun index
     * @brief 重写QStandardItemModel的index
     * @param row
     * @param column
     * @param parent
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    QModelIndex index(int row, int column, const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun rowCount
     * @brief 重写QStandardItemModel的rowCount
     * @param parent
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun columnCount
     * @brief 重写QStandardItemModel的columnCount
     * @param parent
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    /**
     * @fun data
     * @brief 重写QStandardItemModel的data
     * @param index
     * @param role
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    /**
     * @fun headerData
     * @brief 重写QStandardItemModel的headerData
     * @param section
     * @param orientation
     * @param role
     * @return
     * @date 2024.9.24
     * <AUTHOR>
     */
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    /**
     * @fun SetDataModel
     * @brief 设置数据
     * @param vector_data
     * @date 2024.2.6
     * <AUTHOR>
     */
    void SetDataModel(std::vector<DetectResultStruct*> vector_data);
    /**
     * @fun GetDataModel
     * @brief 获取数据
     * @param vector_data
     * @date 2024.2.6
     * <AUTHOR>
     */
    void GetDataModel(std::vector<DetectResultStruct*>& vector_data);
    /**
     * @fun GetHeaderLabels
     * @brief 标题栏
     * @return
     * @date 2024.2.6
     * <AUTHOR>
     */
    QStringList GetHeaderLabels();
public:
    /**
     * @fun DetectResultModel
     * @brief 构造函数，初始化 DetectResultModel
     * @details 初始化自定义的表格项委托，设置父对象
     * @param parent 父对象
     * @date 2025.02.25
     * <AUTHOR>
     */
    DetectResultModel(QObject* parent);
    /**
     * @fun ~DetectResultModel
     * @brief 析构函数
     * @date 2025.02.25
     * <AUTHOR>
     */
    ~DetectResultModel();
private:
    std::vector<DetectResultStruct*> m_vec_data;//检测结果数据
    QStringList m_header_list;//标题栏数据
};
#endif