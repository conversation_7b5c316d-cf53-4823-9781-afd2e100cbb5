#include "spdlogger.h"
namespace jrscore
{
    Spdlogger::Spdlogger (const std::string& log_name_)
        :AbstractLogger()
        ,log_name(log_name_)
        ,p_log_impl(nullptr)
    {
        std::call_once (once_flag, [this]()
                        {
                            spdlog::init_thread_pool (8192, 2);
                            thread_pool_impl = spdlog::thread_pool ();
                        });
    }
    Spdlogger::~Spdlogger ()
    {
        spdlog::drop_all ();
        spdlog::shutdown ();
    }
    AOIErrorCode Spdlogger::Init ()
    {
        assert (!p_log_impl);

        const char* p_format = "[%Y-%m-%d %H:%M:%S.%e] <thread %t> [%^%6l %$] %v";
        if (p_log_impl)
        {
            return CoreError::E_AOI_CORE_REPEAT_INIT;
        }

        std::vector<spdlog::sink_ptr> vec_sink;

        if (LogPosition::CONSOLE == log_sink_pos)
        {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt> ();

            vec_sink.emplace_back (console_sink);
        }
        else if(LogPosition::FILELOG == log_sink_pos)
        {
            const std::string log_path = 
                fmt::format ("{}\\{}.log", GetLogFolderPath (), GetLogName ()+"-%Y-%m-%d" );
            auto file_sink = std::make_shared<spdlog::sinks::daily_file_format_sink_mt> (log_path, 0,0);

            vec_sink.emplace_back (file_sink);
        }
        else if(LogPosition::CONSOLE_AND_FILE == log_sink_pos)
        {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt> ();
            const std::string log_path =
                fmt::format ( "{}\\{}.log" , GetLogFolderPath () , GetLogName () + "-%Y-%m-%d" );
            auto file_sink = std::make_shared<spdlog::sinks::daily_file_format_sink_mt> ( log_path , 0 , 0 );

            vec_sink.emplace_back (console_sink);
            vec_sink.emplace_back (file_sink);
        }
        else
        {
            return CoreError::E_AOI_CORE_UNKNOWN;
        }

        if (LogMode::ASYNC == log_mode)
        {
            p_log_impl = std::make_shared<spdlog::async_logger> (GetLogName (), begin (vec_sink), end (vec_sink), spdlog::thread_pool (), spdlog::async_overflow_policy::block);

        }
        else
        {
            p_log_impl = std::make_shared<spdlog::logger> (GetLogName (), begin (vec_sink), end (vec_sink));
        }

        p_log_impl->set_pattern (p_format);
        p_log_impl->set_level ((spdlog::level::level_enum)log_output_level);
        p_log_impl->flush_on (spdlog::level::err);

        spdlog::flush_every (std::chrono::seconds (3));
        spdlog::register_logger (p_log_impl);

        return AOI_OK;
    }
    void Spdlogger::LogInternal (const LogLevel level_, const std::string& msg_)
    {
        assert (p_log_impl);
        SPDLOG_LOGGER_CALL (p_log_impl, (spdlog::level::level_enum)(level_), msg_);

    }
}