#ifndef __VIEWMANAGER_H__
#define __VIEWMANAGER_H__
//STD
#include <iostream>
#include <map>
//QT
#pragma warning(push, 1)
#include<QObject>

#pragma warning(pop)
//Custom
#include "controllerbase.h"
#include "modulehandle.h"



namespace jrsaoi
{

    class ViewManager;
    struct ManagerImplData;
    class  ModelBase;
    using ModelBasePtr = std::shared_ptr<ModelBase>;
    using ViewManagerPtr = std::shared_ptr<ViewManager>;
    using ComponentMapMVC = std::map<std::string, std::tuple<ModelBasePtr, ViewBase*, ControllerBasePtr>>;
    class ViewManager :public QObject
    {
        Q_OBJECT
    public:
        ViewManager();
        ~ViewManager();


        /**
         * @fun RegisterComponent
         * @brief 注册mvc
         * @param name 界面类别名称
         * @return 错误码0为成功，其他值为失败
         * @date 2024.1.18
         * <AUTHOR>
         */
        template<typename Model, typename View, typename Controller>
        int RegisterComponent(std::string name);
        /**
         * @fun GetMVCComponent
         * @brief 获取MVC的实例
         * @param name 界面类名称
         * @return  返回mvc元组
         * @date 2024.1.18
         * <AUTHOR>
         */
        std::tuple<ModelBasePtr, ViewBase*, ControllerBasePtr> GetMVCComponent(std::string name);
        /**
         * @fun GetView
         * @brief
         * @param name
         * @return
         * @date 2024.1.18
         * <AUTHOR>
         */
        ViewBase* GetView(std::string name);
        ModelBasePtr GetModel(std::string name);
        ControllerBasePtr GetController(std::string name);
    private:
        /**
         * @fun InitMember
         * @brief 初始化
         * @date 2024.1.29
         * <AUTHOR>
         */
        void InitMember();
        /**
         * @fun InitControlPanel
         * @brief 初始化自动运行面板
         * @date 2024.1.29
         * <AUTHOR>
         */
        void InitControlPanel();
        /**
         * @fun InitOperate
         * @brief 初始化操作
         * @date 2024.1.29
         * <AUTHOR>
         */
        void InitOperate();
        /**
         * @fun InitToolBar
         * @brief 初始化快捷菜单栏
         * @date 2024.1.29
         * <AUTHOR>
         */
        void InitToolBar();
        /**
         * @fun InitLogShow
         * @brief 初始化日志显示界面
         * @date 2024.1.29
         * <AUTHOR>
         */
        void InitLogShow();
        /**
         * @fun InitShowList
         * @brief 右侧列表显示区域
         * @date 2024.2.2
         * <AUTHOR>
         */
        void InitShowList();

        /**
         * @fun InitRender2D
         * @brief 初始化2D渲染
         * <AUTHOR>
         * @date 2024.7.17
         */
        void InitRender2D();
        /**
         * @fun InitSetting
         * @brief 设置参数界面
         * <AUTHOR>
         * @date 2024.8.22
         */
        void InitSetting();
        /**
         * @fun InitSystem
         * @brief 系统初始化
         * <AUTHOR>
         * @date 2024.10.21
         */
        void InitSystem();
        /**
         * @fun InitOnLineDebug
         * @brief 在线调界面端初始化
         * <AUTHOR>
         * @date 2025.3.10
         */
        void InitOnLineDebug();

        ComponentMapMVC mvc_container;
        /*
        // 实际内容：
        mvc_container = {
        ["operate_module"]  = <OperateModel*, OperateView*, OperateController*>,
        ["render2d_module"] = <Render2dModel*, Render2dView*, Render2dController*>,
        ["showlist_module"] = <ShowListModel*, ShowListView*, ShowListController*>,
        // ...};
        */

        ManagerImplData* impl_data;

        enum  class MVCTYPE :int
        {
            MODEL = 0,
            VIEW,
            CONTROLLER
        };
    };
}

#endif // !__VIEWMANAGER_H__
